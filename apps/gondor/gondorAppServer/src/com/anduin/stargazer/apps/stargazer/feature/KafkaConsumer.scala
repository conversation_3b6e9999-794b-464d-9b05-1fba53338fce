// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.apps.stargazer.feature

import anduin.id.servicefeature.ServiceFeatureId
import anduin.kafka.KafkaFiber
import com.anduin.stargazer.apps.stargazer.coordinator.ServerCoordinator
import com.anduin.stargazer.module.GondorCommonServiceModule
import zio.ZIO

final case class KafkaConsumer(gondorModule: GondorCommonServiceModule) {

  def getFiber: Seq[ServerCoordinator.DaemonFiber] = {
    if (gondorModule.serviceFeature.hasFeature(ServiceFeatureId.KafkaConsumer)) {
      Seq(KafkaConsumer.Fiber(gondorModule))
    } else {
      Seq.empty
    }
  }

}

object KafkaConsumer {

  final case class Fiber(gondorModule: GondorCommonServiceModule) extends ServerCoordinator.DaemonFiber {

    private val kafkaFibers = Seq[KafkaFiber](
      // Async executor
      gondorModule.emailSenderService,
      gondorModule.fundSubDataLakeIngestionService,
      gondorModule.fundSubWebhookKafkaService,
      gondorModule.fileDownloadService,
      gondorModule.fundSubInvestorGroupService,
      gondorModule.zapierService,
      gondorModule.phoneService,
      gondorModule.dynamicFormKafkaService,
      gondorModule.formKafkaService,
      gondorModule.docusignWebhookHandlerRoute,

      // Consumer
      gondorModule.emailReceiverService,
      gondorModule.fundSubEmailService,
      gondorModule.fundSubSubscriptionDocReviewService,
      gondorModule.fundSubDataInterfaceService,
      gondorModule.fundDataAuditLogService,
      gondorModule.fundDataEmailService,
      gondorModule.fundDataDocumentExpirationNotificationService,
      gondorModule.fundDataAssessmentDueDateNotificationService,
      gondorModule.fundDataRequestConfigService,
      gondorModule.fundDataGreylinSyncService,
      gondorModule.fundDataEventExportService,
      gondorModule.fundDataFundSubSyncService,
      gondorModule.fundDataDataLakeSyncService,
      gondorModule.fundSubDocusignWebhookEventConsumer,
      gondorModule.fundDataRequestService,
      gondorModule.fundDataFirmOrganizationService,
      gondorModule.fundDataInvestmentEntityService,
      gondorModule.fundDataInvestmentEntityDocumentTextractService,
      gondorModule.fundDataWebhookService,
      gondorModule.dataRoomGlobalDatabaseSyncService,
      gondorModule.fundSubGlobalDatabaseSyncService,
      gondorModule.integrationWebhookService,
      gondorModule.integrationServiceRegistry,
      gondorModule.riaEmailService,
      gondorModule.riaEntityFundIntegrationService,
      gondorModule.fundSubOperationDataExtractWebhookEventConsumer,
      gondorModule.fundTransactionSyncService,
      gondorModule.revertOTPAuthenticationService,

      // Digest
      gondorModule.newSupportingDocUploadReportService,
      gondorModule.newSupportingDocReviewReadyReportService,

      // Data Pipeline
      gondorModule.greylinTransformService
    )

    override def name(): String = "kafka-consumer"

    override def start: zio.Task[Unit] = {
      ZIO.collectAllParDiscard(kafkaFibers.map(_.start()))
    }

    override def onInterrupted: zio.Task[Unit] = {
      ZIO.collectAllParDiscard(kafkaFibers.map(_.close()))
    }

  }

}
