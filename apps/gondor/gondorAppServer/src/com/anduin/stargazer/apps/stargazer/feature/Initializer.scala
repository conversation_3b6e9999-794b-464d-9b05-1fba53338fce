// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.apps.stargazer.feature

import zio.implicits.*
import zio.telemetry.opentelemetry.tracing.Tracing
import zio.{RIO, Task, ZEnvironment, ZIO}

import anduin.account.enterprise.EnterpriseStoreProvider
import anduin.account.policy.AccountPolicyStoreProvider
import anduin.account.store.AccountTokenStoreProvider
import anduin.amlkyc.amlcheck.AmlCheckStoreProvider
import anduin.analytics.database.LpFormFieldSourceAnalyticsCacheStoreProvider
import anduin.asyncapiv2.store.AsyncApiStoreProviders
import anduin.bot.StargazerBotUser
import anduin.brienne.store.{ServiceAccountStoreProvider, WebhookEndpointStoreProvider}
import anduin.contact.internal.fdb.{ContactGroupStoreProvider, ContactStoreProvider}
import anduin.cue.storage.table.{CueTableDataStoreProvider, CueTableMetadataStoreProvider}
import anduin.cue.storage.{CueModuleModelStoreProvider, CueModuleVersionStoreProvider}
import anduin.customdomain.{CustomDomainExistException, CustomDomainStoreProvider}
import anduin.dashboard.service.DashboardStoreProvider
import anduin.dataextract.database.{
  DataExtractProjectItemStoreProvider,
  DataExtractProjectModelStoreProvider,
  DataExtractUserDocumentStoreProvider
}
import anduin.dataroom.bot.{DataRoomBotUser, DataRoomIntegrationBotUser}
import anduin.dataroom.email.DataRoomEmailStoreProvider
import anduin.dataroom.flow.*
import anduin.dataroom.group.{DataRoomGroupEventStoreProvider, DataRoomGroupStateStoreProvider}
import anduin.dataroom.homepage.DataRoomHomePageStoreProvider
import anduin.dataroom.integration.fdb.DataRoomIntegrationStoreProvider
import anduin.dataroom.notification.DataRoomNotificationSettingsProvider
import anduin.dataroom.participant.DataRoomParticipantStoreProvider
import anduin.dataroom.whitelabel.DataRoomWhiteLabelStoreProvider
import anduin.digitization.common.DigitizationServiceKeyspace
import anduin.digitization.storage.{
  DigitizationFileModelStoreProvider,
  DigitizationFolderModelStoreProvider,
  DigitizationUserActivityStoreProvider
}
import anduin.disclaimer.DisclaimerStoreProviders
import anduin.dms.file.event.FileEventStoreProvider
import anduin.dms.file.state.FileStateStoreProvider
import anduin.dms.file.version.FileVersionStoreProvider
import anduin.dms.folder.event.FolderEventStoreProvider
import anduin.dms.folder.state.FolderStateStoreProvider
import anduin.dms.shortcut.event.ShortcutEventStoreProvider
import anduin.dms.shortcut.state.ShortcutStateStoreProvider
import anduin.docrequest.recordlayer.{DocRequestStoreProvider, DocSubmissionStoreProvider, FormSubmissionStoreProvider}
import anduin.documentservice.pagecount.FilePageCountStoreProvider
import anduin.documentservice.pdf.PdfFileStorageStoreProvider
import anduin.documentservice.textract.event.TextractEventStoreProvider
import anduin.documentservice.textract.mergetextract.MergeTextractStoreProvider
import anduin.documentservice.textract.state.TextractStateStoreProvider
import anduin.documentservice.watermark.WatermarkFileStorageStoreProvider
import anduin.email.provider.EmailProviderStoreProvider
import anduin.entity.repository.EntityModelStoreProvider
import anduin.entity.repository.whitelabel.EntityWhiteLabelStoreProvider
import anduin.environment.{EnvironmentPolicyStoreProvider, EnvironmentSSOBindingStoreProvider, EnvironmentStoreProvider}
import anduin.fdb.record.FDBRecordDatabase
import anduin.forms.storage.*
import anduin.forms.storage.annotation.{
  AnnotationDocumentModelStoreProvider,
  AnnotationDocumentVersionStoreProvider,
  TextractAnnotationStateStoreProvider
}
import anduin.forms.storage.integration.FormIntegrationStoreProvider
import anduin.funddata.bot.FundDataBotUser
import anduin.funddata.communication.contact.FundDataContactCommunicationStoreProvider
import anduin.funddata.communication.firm.FundDataFirmCommunicationStoreProvider
import anduin.funddata.communication.fundlegalentity.FundDataFundLegalEntityCommunicationStoreProvider
import anduin.funddata.communication.investmententity.FundDataInvestmentEntityCommunicationStoreProvider
import anduin.funddata.contact.store.{
  FundDataFirmContactStoreProvider,
  FundDataInvestmentEntityContactStoreProvider,
  FundDataInvestorContactStoreProvider
}
import anduin.funddata.email.FundDataEmailTemplateStoreProvider
import anduin.funddata.firm.FundDataFirmStoreProvider
import anduin.funddata.fund.FundDataFundV2StoreProvider
import anduin.funddata.fund.fundfamily.FundFamilyStoreProvider
import anduin.funddata.fund.fundlegalentity.FundLegalEntityStoreProvider
import anduin.funddata.fund.transaction.FundTransactionStoreProvider
import anduin.funddata.guest.user.FundDataGuestStoreProvider
import anduin.funddata.investmententity.InvestmentEntityStoreProvider
import anduin.funddata.investmententity.assessment.InvestmentEntityAssessmentStoreProvider
import anduin.funddata.investmententity.contact.InvestmentEntityContactStoreProvider
import anduin.funddata.investmententity.document.InvestmentEntityDocumentStoreProvider
import anduin.funddata.investor.FundDataInvestorStoreProvider
import anduin.funddata.portal.document.distribution.flow.state.DocDistributionStateStoreProvider
import anduin.funddata.portal.file.store.InvestorPortalFileStoreProvider
import anduin.funddata.portal.instance.PortalInstanceStoreProvider
import anduin.funddata.request.FundDataRequestStoreProvider
import anduin.fundsub.activitylog.{ActivityLogProvider, LpActivityStoreProvider}
import anduin.fundsub.comment.database.{
  CommentAssignmentIndexStoreProvider,
  CommentExportTaskStoreProvider,
  CommentMentionIndexStoreProvider,
  CommentThreadIndexStoreProvider
}
import anduin.fundsub.dashboard.LpInfoStoreProvider
import anduin.fundsub.dataextract.database.{
  FundSubDataExtractFormDataStoreProvider,
  FundSubDataExtractMetadataStoreProvider,
  FundSubDataExtractRequestStoreProvider,
  FundSubDataExtractTestProfileStoreProvider
}
import anduin.fundsub.email.digest.log.DigestEmailLogStoreProvider
import anduin.fundsub.email.digest.orderactivity.OrderActivityStoreProvider
import anduin.fundsub.environment.FundSubEnvironmentPolicyStoreProvider
import anduin.fundsub.models.{
  FundAdminNotificationSettingStoreProvider,
  FundSubLpModelStoreProvider,
  FundSubModelStoreProvider,
  SubscriptionSchemaDataStoreProvider
}
import anduin.fundsub.rebac.FundSubOpenFgaMappingStoreProvider
import anduin.fundsub.report.NewLpReportEmailLogProvider
import anduin.fundsub.reviewpackage.{ReviewPackageEmailLogStoreProvider, ReviewPackageStoreProvider}
import anduin.fundsub.ria.group.FundSubRiaGroupStoreProvider
import anduin.fundsub.signature.integration.FundSubSignatureStoreProvider
import anduin.fundsub.submission.version.{SubmissionVersionEventStoreProvider, SubmissionVersionStateStoreProvider}
import anduin.fundsub.subscriptiondoc.review.{
  FundSubSubscriptionDocReviewConfigStoreProvider,
  FundSubSubscriptionDocReviewMappingStoreProvider
}
import anduin.fundsub.supportingdoc.{
  FundSubSupportingDocInfoProvider,
  FundSubSupportingDocReviewStatusStoreProvider,
  SupportingDocReviewConfigStoreProvider
}
import anduin.fundsub.tag.LpTagStoreProvider
import anduin.fundsub.updatelog.InvestorFormUpdateLogStoreProvider
import anduin.fundsub.{FundSubBotUser, LpFormDataStoreProvider}
import anduin.globaldatabase.GlobalDatabase
import anduin.id.servicefeature.ServiceFeatureId
import anduin.idmapping.IdMappingStoreProvider
import anduin.integplatform.store.entity.IntegPlatformEntityStoreProvider
import anduin.integplatform.store.instance.IntegPlatformInstanceStoreProvider
import anduin.link.{OneTimeLinkStoreProvider, ProtectedLinkStoreProvider}
import anduin.multiregion.MultiRegionStoreProvider
import anduin.notification.store.NotificationStoreProvider
import anduin.oauth2.storage.Oauth2IntegrationStoreProvider
import anduin.ontology.service.database.OntologyAsaStoreProvider
import anduin.ontology.storage.{OntologyAsaMappingStoreProvider, OntologySpaceMetadataStoreProvider}
import anduin.portaluser.{PortalUserService, PortalUserStoreProvider}
import anduin.protobuf.customdomain.CustomDomainType
import anduin.psql.TimescaleDatabase
import anduin.rebac.GondorRebacStores
import anduin.review.database.{
  ReviewConfigStoreProvider,
  ReviewFlowStoreProvider,
  ReviewStepConfigStoreProvider,
  ReviewStepEventStoreProvider
}
import anduin.ria.entity.advisor.RiaEntityFundAdvisorStoreProvider
import anduin.ria.entity.emaildomain.RiaEntityEmailDomainRelationStoreProvider
import anduin.ria.entity.fund.RiaFundGroupStoreProvider
import anduin.ria.entity.user.RiaEntityUserRelationStoreProvider
import anduin.ria.order.advisor.RiaAdvisorOrderRelationStoreProvider
import anduin.ria.order.entity.RiaEntityOrderRelationStoreProvider
import anduin.signature.storage.UserSignatureAppStoreProvider
import anduin.tag.v2.objecttags.ObjectTagsStoreProvider
import anduin.team.flow.mainflow.{TeamEventStoreProvider, TeamStateStoreProvider}
import anduin.team.flow.memberflow.{TeamMemberEventStoreProvider, TeamMemberStateStoreProvider}
import anduin.tidb.TiDatabase
import anduin.tyk.store.ApiKeyStoreProvider
import anduin.user.UserModelStoreProvider
import com.anduin.stargazer.module.{GondorCommonServiceModule, GondorCommonWorkflowModule}
import com.anduin.stargazer.service.email.store.InternalEmailStoreProvider
import com.anduin.stargazer.service.file.tracking.DmsStorageTrackingStoreProvider
import com.anduin.stargazer.service.orgbilling.storage.{ChangeDataRoomPlanLogStoreProvider, OrgBillingStoreProvider}
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.service.workflow.GondorWorkflowWorker

final case class Initializer(gondorModule: GondorCommonWorkflowModule) {

  private val gondorConfig = gondorModule.gondorConfig

  def run: zio.Task[Unit] = {
    ZIO
      .when(gondorModule.serviceFeature.hasFeature(ServiceFeatureId.Initializer)) {
        for {
          _ <- startInitServices
          _ <- gondorModule.temporalCronService.start()
        } yield ()
      }
      .unit
      .provideEnvironment(gondorModule.tracingEnvironment.environment)
  }

  private def startInitServices: RIO[Tracing, Unit] = {
    for {
      _ <- ZIO.logInfo("Starting init services")
      _ <- checkTemporalWorker()
      _ <- gondorModule.systemAuditLogService.start
        .catchAllCause { error =>
          ZIO.logErrorCause("initLogStream with failures...", error)
        }
      _ <- gondorModule.rebacStoreManager
        .writeSchema(GondorRebacStores.AllStores)
        .catchAllCause { error =>
          ZIO.logErrorCause("init rebac schema with failures...", error)
        }
      _ <- rebuildAllFdbRecordIndexes
        .catchAllCause { error =>
          ZIO.logErrorCause("rebuildAllFdbRecordIndexes with failures...", error)
        }
      _ <- createSystemUsers(
        gondorModule.portalUserService,
        gondorModule.stargazerBotUser,
        gondorModule.fundSubBotUser,
        gondorModule.dataRoomIntegrationBotUser,
        gondorModule.dataRoomBotUser,
        gondorModule.fundDataBotUser
      )
        .catchAllCause { error =>
          ZIO.logErrorCause("createSystemUsers with failures...", error)
        }
      _ <- migrateSqlDatabase
        .catchAllCause { error =>
          ZIO.logErrorCause("migrateSqlDatabase with failures...", error)
        }
      _ <- initializeSeedData(gondorModule)
        .catchAllCause { error =>
          ZIO.logErrorCause("initializeSeedData with failures...", error)
        }
      _ <- initializeDataRoomHomePageResources(gondorModule)
        .catchAllCause { error =>
          ZIO.logErrorCause("initializeDataRoomHomePageResources with failures...", error)
        }
      _ <- initializeFormBuilderResources(gondorModule)
        .catchAllCause { error =>
          ZIO.logErrorCause("initializeFormBuilderResources with failures...", error)
        }
      _ <- initializeDigitizationResources(gondorModule)
        .catchAllCause { error =>
          ZIO.logErrorCause("initializeDigitizationResources with failures...", error)
        }
      _ <- gondorModule.fundSubDataLakeIngestionService
        .updateDgraphSchema(gondorModule.evendimAdminClient)
        .catchAllCause { error =>
          ZIO.logErrorCause("updateDgraphSchema with failures...", error)
        }
      _ <- initializeSeedCustomDomains(gondorModule).catchAllCause { error =>
        ZIO.logErrorCause("initializeSeedCustomDomains with failures...", error)
      }
      _ <- checkAsaOntologySchemaInitialization(gondorModule)
        .catchAllCause { error =>
          ZIO.logErrorCause("initializeAsaOntologySchema with failures...", error)
        }
      _ <- initializeWebhookEventTypes(gondorModule)
        .catchAllCause { error =>
          ZIO.logErrorCause("initializeWebhookEventTypes with failures...", error)
        }
      _ <- ZIO.logInfo("Initialize successfully!!!")
    } yield ()
  }

  private def createSystemUsers(
    portalUserService: PortalUserService,
    stargazerBotUser: StargazerBotUser,
    fundSubBotUser: FundSubBotUser,
    dataRoomIntegrationBotUser: DataRoomIntegrationBotUser,
    dataRoomBotUser: DataRoomBotUser,
    fundDataBotUser: FundDataBotUser
  ): Task[Unit] = {
    for {
      _ <- stargazerBotUser.initUser.timeout(gondorConfig.timeout)
      _ <- portalUserService.ensureExecutiveAdminCreated.timeout(gondorConfig.timeout)
      _ <- fundSubBotUser.initUser.timeout(gondorConfig.timeout)
      _ <- dataRoomIntegrationBotUser.initUser.timeout(gondorConfig.timeout)
      _ <- dataRoomBotUser.initUser.timeout(gondorConfig.timeout)
      _ <- fundDataBotUser.initUser.timeout(gondorConfig.timeout)
    } yield ()
  }

  private def migrateSqlDatabase = {
    for {
      _ <- ZIO.logInfo("Start migrating SQL databases")
      _ <- TimescaleDatabase
        .ensureLatestDatabaseSchema(gondorConfig.backendConfig.timescaleConfig)
        .catchAllCause(c => ZIO.logInfo(s"Failed to migrate TimescaleDB with error $c"))
      _ <- TiDatabase
        .ensureLatestDatabaseSchema(gondorConfig.backendConfig.tidbConfig)
        .catchAllCause(c => ZIO.logInfo(s"Failed to migrate TiDB with error $c"))
      _ <- ZIO.when(!gondorConfig.commonConfig.multiRegionConfig.isSecondaryRegion) {
        GlobalDatabase
          .ensureLatestDatabaseSchema(gondorConfig.backendConfig.globalDatabaseConfig.writeDataSource)
          .catchAllCause(c => ZIO.logInfo(s"Failed to migrate global database with error $c"))
      }
      _ <- ZIO.logInfo("Done migrating SQL databases")
    } yield ()
  }

  private def rebuildAllFdbRecordIndexes = {
    val buildIndexTask = FDBRecordDatabase.buildIndexes(
      PdfFileStorageStoreProvider.Production,
      DmsStorageTrackingStoreProvider.Production,
      ProtectedLinkStoreProvider.Production,
      LpInfoStoreProvider.Production,
      LpActivityStoreProvider.Production,
      LpTagStoreProvider.Production,
      NewLpReportEmailLogProvider.Production,
      OrgBillingStoreProvider.Production,
      ChangeDataRoomPlanLogStoreProvider.Production,
      TeamEventStoreProvider.Production,
      TeamStateStoreProvider.Production,
      TeamMemberStateStoreProvider.Production,
      TeamMemberEventStoreProvider.Production,
      ContactStoreProvider.Production,
      ContactGroupStoreProvider.Production,
      DataRoomModelStoreProvider.Production,
      DataRoomNotificationSettingsProvider.Production,
      DataRoomHomePageStoreProvider.Production,
      DataRoomEventStoreProvider.Production,
      DataRoomStateStoreProvider.Production,
      FolderEventStoreProvider.Production,
      FolderStateStoreProvider.Production,
      FileEventStoreProvider.Production,
      DataRoomWhiteLabelStoreProvider.Production,
      FileStateStoreProvider.Production,
      FileVersionStoreProvider.Production,
      ShortcutStateStoreProvider.Production,
      ShortcutEventStoreProvider.Production,
      ActivityLogProvider.Production,
      DataRoomIntegrationStoreProvider.Production,
      DataRoomParticipantStoreProvider.Production,
      DataRoomGroupStateStoreProvider.Production,
      DataRoomGroupEventStoreProvider.Production,
      FundAdminNotificationSettingStoreProvider.Production,
      FundSubModelStoreProvider.Production,
      FundSubSupportingDocInfoProvider.Production,
      FundSubLpModelStoreProvider.Production,
      EntityWhiteLabelStoreProvider.Production,
      ReviewPackageStoreProvider.Production,
      ReviewPackageEmailLogStoreProvider.Production,
      FormActivityStoreProvider.Production,
      FormLockStoreProvider.Production,
      InvestorFormUpdateLogStoreProvider.Production,
      WatermarkFileStorageStoreProvider.Production,
      FilePageCountStoreProvider.Production,
      FormTestSuiteStoreProvider.Production,
      FormVersionDataStoreProvider.Production,
      FormDataUserTempStoreProvider.Production,
      FormVersionStoreProvider.Production,
      SubmissionVersionStateStoreProvider.Production,
      SubmissionVersionEventStoreProvider.Production,
      LpFormDataStoreProvider.Production,
      SubscriptionSchemaDataStoreProvider.Production,
      Oauth2IntegrationStoreProvider.Production,
      EnterpriseStoreProvider.Production,
      DocRequestStoreProvider.Production,
      FormSubmissionStoreProvider.Production,
      DocSubmissionStoreProvider.Production,
      DataRoomEmailStoreProvider.Production,
      DashboardStoreProvider.Production,
      UserSignatureAppStoreProvider.Production,
      DataTemplateStoreProvider.Production,
      DataTemplateVersionStoreProvider.Production,
      FormTemplateMappingModelStoreProvider.Production,
      FormTemplateMappingVersionStoreProvider.Production,
      FormTemplateMappingContentStoreProvider.Production,
      ApiKeyStoreProvider.Production,
      ServiceAccountStoreProvider.Production,
      PortalUserStoreProvider.Production,
      WebhookEndpointStoreProvider.Production,
      CustomDomainStoreProvider.Production,
      FormModelStoreProvider.Production,
      FormIntegrationStoreProvider.Production,
      FormFolderModelStoreProvider.Production,
      FundSubSupportingDocReviewStatusStoreProvider.Production,
      SupportingDocReviewConfigStoreProvider.Production,
      ReviewConfigStoreProvider.Production,
      ReviewFlowStoreProvider.Production,
      ReviewStepConfigStoreProvider.Production,
      ReviewStepEventStoreProvider.Production,
      InternalEmailStoreProvider.Production,
      FundSubSubscriptionDocReviewConfigStoreProvider.Production,
      FundSubSubscriptionDocReviewMappingStoreProvider.Production,
      FormTestScriptModelStoreProvider.Production,
      FormTestScriptContentStoreProvider.Production,
      CommentThreadIndexStoreProvider.Production,
      CommentMentionIndexStoreProvider.Production,
      CommentAssignmentIndexStoreProvider.Production,
      CommentExportTaskStoreProvider.Production,
      AnnotationDocumentModelStoreProvider.Production,
      AnnotationDocumentVersionStoreProvider.Production,
      TextractAnnotationStateStoreProvider.Production,
      TextractStateStoreProvider.Production,
      TextractEventStoreProvider.Production,
      MergeTextractStoreProvider.Production,
      OrderActivityStoreProvider.Production,
      DigestEmailLogStoreProvider.Production,
      DigitizationFolderModelStoreProvider.Production,
      DigitizationFileModelStoreProvider.Production,
      DigitizationUserActivityStoreProvider.Production,
      UserModelStoreProvider.Production,
      FundSubSignatureStoreProvider.Production,
      DataExtractProjectModelStoreProvider.Production,
      DataExtractProjectItemStoreProvider.Production,
      DataExtractUserDocumentStoreProvider.Production,
      InvestmentEntityDocumentStoreProvider.Production,
      InvestmentEntityContactStoreProvider.Production,
      MultiRegionStoreProvider.Production,
      OntologyAsaStoreProvider.Production,
      OntologyAsaMappingStoreProvider.Production,
      OntologySpaceMetadataStoreProvider.Production,
      InvestmentEntityStoreProvider.Production,
      InvestmentEntityAssessmentStoreProvider.Production,
      FundDataInvestorStoreProvider.Production,
      ObjectTagsStoreProvider.Production,
      FundDataRequestStoreProvider.Production,
      FundSubOpenFgaMappingStoreProvider.Production,
      FundDataFundV2StoreProvider.Production,
      AmlCheckStoreProvider.Production,
      FundSubDataExtractRequestStoreProvider.Production,
      FundSubDataExtractMetadataStoreProvider.Production,
      FundSubDataExtractFormDataStoreProvider.Production,
      FundSubDataExtractTestProfileStoreProvider.Production,
      FundDataEmailTemplateStoreProvider.Production,
      FundDataFirmStoreProvider.Production,
      FundDataInvestmentEntityContactStoreProvider.Production,
      FundDataFundLegalEntityCommunicationStoreProvider.Production,
      FundDataFirmCommunicationStoreProvider.Production,
      FundDataInvestmentEntityCommunicationStoreProvider.Production,
      FundDataFirmContactStoreProvider.Production,
      FundDataInvestorContactStoreProvider.Production,
      FundDataInvestmentEntityContactStoreProvider.Production,
      FundDataContactCommunicationStoreProvider.Production,
      FundLegalEntityStoreProvider.Production,
      FundFamilyStoreProvider.Production,
      PortalInstanceStoreProvider.Production,
      InvestorPortalFileStoreProvider.Production,
      DocDistributionStateStoreProvider.Production,
      AccountTokenStoreProvider.Production,
      LpFormFieldSourceAnalyticsCacheStoreProvider.Production,
      NotificationStoreProvider.Production,
      EnvironmentStoreProvider.Production,
      EnvironmentSSOBindingStoreProvider.Production,
      EnvironmentPolicyStoreProvider.Production,
      FundSubEnvironmentPolicyStoreProvider.Production,
      RiaEntityFundAdvisorStoreProvider.Production,
      RiaEntityEmailDomainRelationStoreProvider.Production,
      RiaFundGroupStoreProvider.Production,
      RiaEntityUserRelationStoreProvider.Production,
      RiaAdvisorOrderRelationStoreProvider.Production,
      RiaEntityOrderRelationStoreProvider.Production,
      FundSubRiaGroupStoreProvider.Production,
      IntegPlatformInstanceStoreProvider.Production,
      IntegPlatformEntityStoreProvider.Production,
      EntityModelStoreProvider.Production,
      IdMappingStoreProvider.Production,
      CueModuleModelStoreProvider.Production,
      CueModuleVersionStoreProvider.Production,
      CueTableDataStoreProvider.Production,
      CueTableMetadataStoreProvider.Production,
      OneTimeLinkStoreProvider.Production,
      FundDataGuestStoreProvider.Production,
      FundTransactionStoreProvider.Production,
      AccountPolicyStoreProvider.Production,
      EmailProviderStoreProvider.Production
    )
    for {
      _ <- buildIndexTask
      _ <- DisclaimerStoreProviders.rebuildIndex()
      _ <- AsyncApiStoreProviders.rebuildIndexes()
    } yield ()
  }

  private def initializeSeedData(
    gondorServer: GondorCommonServiceModule
  ): RIO[Tracing, Unit] = {
    for {
      seedResults <- gondorServer.seedUsers
        .ensureCreated(gondorServer.userProfileService)
        .tapErrorCause { error =>
          ZIO.logErrorCause("failures when creating seed users", error)
        }
      _ <-
        gondorServer.entityService
          .createSeedEntities(
            gondorServer.gondorBackendConfig.seedEntities,
            gondorServer.seedUsers,
            seedResults
          )
          .catchAllCause { error =>
            ZIO.logErrorCause("failures when creating seed entities", error)
          }
    } yield ()
  }

  private def checkAsaOntologySchemaInitialization(
    gondorModule: GondorCommonServiceModule
  ) = {
    for {
      _ <- ZIO.logInfo("Check to initialize ASA ontology schema as needed...")
      admin <- gondorModule.portalAdmin.userId
      _ <- gondorModule.ontologyService.initializeOntologySchemaIfNeeded(admin)
    } yield ()
  }

  private def initializeWebhookEventTypes(
    gondorModules: GondorCommonServiceModule
  ) = {
    ZIOUtils.when(gondorModules.gondorBackendConfig.svixWebhookConfig.enableWebhook)(
      gondorModules.webhookService.ensureCreatedEventTypes
    )
  }

  private def initializeDataRoomHomePageResources(gondorServer: GondorCommonServiceModule) = {
    gondorServer.dataRoomHomePageService.initializeResources
  }

  private def initializeFormBuilderResources(gondorServer: GondorCommonServiceModule) = {
    gondorServer.formFolderService.initializeResources
  }

  private def initializeDigitizationResources(gondorServer: GondorCommonServiceModule) = {
    gondorServer.digitizationFolderService.initializeResources(
      using DigitizationServiceKeyspace.KeySpace.Production
    )
  }

  private def initializeSeedCustomDomains(gondorServer: GondorCommonServiceModule) = {
    ZIO.foreachDiscard(
      gondorServer.gondorBackendConfig.customDomainConfig.seedCustomDomains.trim
        .split(",")
        .map(_.trim)
        .filter(_.nonEmpty)
    ) { domainName =>
      for {
        portalAdmin <- gondorServer.portalAdmin.userId
        _ <- gondorServer.customDomainService
          .createCustomDomain(domainName, CustomDomainType.System, portalAdmin)
          .catchSome { case _: CustomDomainExistException =>
            ZIO.unit
          }
      } yield ()
    }
  }

  private def checkTemporalWorker() = {
    val task = for {
      // Service worker
      _ <- ZIO.logInfo("Checking service temporal worker...")
      _ <- GondorWorkflowWorker.check(
        GondorCommonWorkflowModule.serviceWorkflows,
        GondorCommonWorkflowModule.serviceActivities(gondorModule)
      )
      // Cron worker
      _ <- ZIO.logInfo("Checking cron temporal worker...")
      _ <- GondorWorkflowWorker.check(
        GondorCommonWorkflowModule.cronWorkflows,
        GondorCommonWorkflowModule.cronActivities(gondorModule)
      )
    } yield ()
    task.provideEnvironment(ZEnvironment(gondorModule))
  }

}
