// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.pantheon.client.form

import java.time.Instant
import scala.util.Success

import com.raquo.airstream.status.{Pending, Resolved}
import com.raquo.laminar.api.L.*

import design.anduin.components.callout.Callout
import design.anduin.components.callout.laminar.CalloutL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.tab.Tab
import design.anduin.components.tab.laminar.TabL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import org.scalajs.dom
import zio.implicits.*
import zio.prelude.NonEmptyList
import zio.{Task, ZIO}

import anduin.annotation.AssetSessionIdUtils.AssetSessionType
import anduin.blueprint.BlueprintMetadata
import anduin.blueprint.endpoint.GetBlueprintVersionCatalaCodeParams
import anduin.component.util.JsDateFormatterUtils
import anduin.cue.model.datalayer.DataLayerDefaultValue
import anduin.digitization.endpoint.GetResourceFileModelParams
import anduin.digitization.model.DigitizationFileModels.DigitizationFile
import anduin.forms.*
import anduin.forms.CueFieldInfo.FieldSchemaInfo
import anduin.forms.asterisk.ContentAsterisksProviderL
import anduin.forms.builder.FormBuilder
import anduin.forms.builder.FormBuilder.{FormBuilderUndoRedoParams, FormBuilderUtilEventStreams}
import anduin.forms.builder.data.ImportDataView
import anduin.forms.builder.document.{BuilderDocument, UpdateAssociatedLinkParams}
import anduin.forms.builder.logic.{LogicBuilder, LogicUtils}
import anduin.forms.builder.navigation.SelectKeyEventData
import anduin.forms.builder.preview.BuilderPreview
import anduin.forms.builder.rule.RuleValidation
import anduin.forms.builder.setting.BuilderSettingPanel.{EditingRule, PropertiesTab, SettingTab}
import anduin.forms.builder.signaldata.*
import anduin.forms.builder.signaldata.FormBuilderDataHelpers.{FieldAddedEvent, RelateKeyModifyEvent}
import anduin.forms.builder.states.FormBuilderSelection.{PdfFieldData, PdfFieldSelectionOrigin}
import anduin.forms.builder.states.{BuilderMode, FormBuilderSelection, UploadedAnnotationData}
import anduin.forms.builder.toc.BuilderTOCHeader.HeaderMode
import anduin.forms.builder.tools.BuilderTestToolsView
import anduin.forms.builder.tools.ontology.{FormOntologyState, OntologyUtils}
import anduin.forms.builder.tools.testscript.FormTestScriptDashboard
import anduin.forms.builder.utils.BuilderUtils
import anduin.forms.client.*
import anduin.forms.embeddednavigation.{EmbeddedDocumentNavigation, WithEmbeddedDocumentNavigation}
import anduin.forms.endpoint.*
import anduin.forms.endpoint.annotation.{ExtractAnnotationParams, GetAnnotationDocumentParams}
import anduin.forms.engine.GaiaEngine.{EngineConfiguration, EngineContext}
import anduin.forms.engine.{GaiaEngine, GaiaState}
import anduin.forms.model.{AssociatedLink, FormFieldMappingTarget, FormModel}
import anduin.forms.model.toolconfig.FormToolConfigProperty
import anduin.forms.rules.FormRule
import anduin.forms.tools.pdf.state.{LoadingState, PageArea, PdfFileProperties}
import anduin.forms.ui.UIKey
import anduin.forms.ui.types.FileId
import anduin.forms.utils.*
import anduin.forms.version.FormVersionModel.FormType
import anduin.forms.version.{FormVersionMetadataModel, FormVersionModel, FormVersionSystemMetadataModel}
import anduin.frontend.AirStreamUtils
import anduin.frontend.AirStreamUtils.Task.{mapTask, splitTask}
import anduin.gaia.engine.utils.GaiaRuntimeLog
import anduin.gaia.engine.utils.GaiaRuntimeLog.RuleExecuteFailed
import anduin.id.annotation.AnnotationDocumentVersionId
import anduin.id.cue.CueModuleId
import anduin.id.digitization.DigitizationFolderId
import anduin.id.form.{FormId, FormTestSuiteId, FormVersionId}
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.FileId as SysFileId
import anduin.ontology.endpoint.{FormAsaMapping, GetFormOntologyDataParams, GetFormVersionAsaMappingsParams}
import anduin.pantheon.client.*
import anduin.pantheon.client.form.PantheonFormBuilderComponent.EditingSessionError.{
  FormLockedByOtherUser,
  FormOutDated,
  SessionTimeout
}
import anduin.pantheon.client.form.PantheonFormBuilderComponent.{EditingSessionError, SaveFormTaskParams}
import anduin.service.GeneralServiceException
import anduin.pantheon.client.form.modal.{AccountUnauthorizedModal, EditFormErrorModal, FormOutdatedModal}
import com.anduin.stargazer.client.services.user.UserUtils
import com.anduin.stargazer.client.utils.ZIOUtils

// TODO: @hiepbui to save draft right after start editing if the draft isn't based on the latest version
// to make sure ontology transfer properly on backend
final case class PantheonFormBuilderComponent(pageSignal: Signal[PantheonFormBuilderPage]) {

  private val formIdSignal = pageSignal.map(_.formId).distinct
  private val versionIdOptSignal = pageSignal.map(_.commonParams.versionId).distinct
  private val tabSignal = pageSignal.map(_.tab).distinct

  private val currentUserVar = Var[Option[UserInfo]](None)
  private val formModelOptVar = Var[Option[FormModel]](None)
  private val draftVersionOptVar = Var[Option[FormVersionModel]](None)
  private val viewingVersionOptVar = Var[Option[FormVersionModel]](None)
  private val standardMappingVersionIdOptVar = Var[Option[FormVersionId]](None)
  private val viewedTabsVar = Var[Set[FormTab]](Set.empty)
  private val settingPanelTabVar = Var[SettingTab](PropertiesTab)
  private val settingPanelEditingRuleVar = Var[Option[EditingRule]](None) // Origin rule & is new created rule
  private val blueprintMetadataVar = Var[BlueprintMetadata](BlueprintMetadata())
  private val logicExtractorControlConfigsOptVar = Var[Option[Seq[FormToolConfigProperty.LogicExtractorControl]]](None)

  private val formDataEventBus = new EventBus[FormData]
  private val formStandardAliasMappingEventBus = new EventBus[Map[String, String]]
  private val formMetadataEventBus = new EventBus[(FormVersionMetadataModel, FormVersionSystemMetadataModel)]
  private val formBuilderDataCommandEventBus = new EventBus[FormBuilderDataCommand]
  private val formBuilderDataStateCommandEventBus = new EventBus[FormBuilderDataStateCommand]

  private val fieldsAddedEventBus = new EventBus[FieldAddedEvent]
  private val fieldsRenameEventBus = new EventBus[String]
  private val fieldsRemovedEventBus = new EventBus[String]
  private val keyModifiedEventBus = new EventBus[Seq[RelateKeyModifyEvent]]
  private val setTabEventBus = new EventBus[FormTab]
  private val editAssociatedLinksEventBus = new EventBus[UpdateAssociatedLinkParams]
  private val initialLoadDoneEventBus = new EventBus[Unit]

  private val sideViewerOpenVar = Var[Boolean](false)
  private val sideViewerOpenSignal = sideViewerOpenVar.signal.distinct
  private val sideViewerOpenWriter = sideViewerOpenVar.writer

  private val sideViewerOpenFlipObserver = Observer[Unit] { _ =>
    sideViewerOpenVar.update(!_)
  }

  private val formBuilderDataHelpers = FormBuilderDataHelpers(
    onFieldAdded = fieldsAddedEventBus.writer,
    onFieldRename = fieldsRenameEventBus.writer,
    onFieldRemoved = fieldsRemovedEventBus.writer,
    onKeyModified = keyModifiedEventBus.writer
  )

  private val formBuilderDataSignal = FormBuilderDataSignal
    .getFormBuilderDataSignal(
      formDataEventBus.events,
      formStandardAliasMappingEventBus.events,
      formMetadataEventBus.events,
      formBuilderDataCommandEventBus.events,
      formBuilderDataStateCommandEventBus.events,
      formBuilderDataHelpers
    )
    .distinct

  private val formDataSignal = formBuilderDataSignal.map(_._1).distinct
  private val formSystemMetadataSignal = formBuilderDataSignal.map(_._6).distinct
  private val selectedSignal = formBuilderDataSignal.map(_._7).distinct

  private val cueDefaultValuesSignal = formDataSignal.map(_.form.cueFieldSchemas)

  private val cueModuleDataSyncEventBus = new EventBus[Unit]

  private val cueDefaultValuesTaskSignal = formSystemMetadataSignal
    .combineWith(formIdSignal, versionIdOptSignal)
    .composeAll(
      changesOperator = changes =>
        EventStream.merge(
          changes.map(Right(_)),
          cueModuleDataSyncEventBus.events.map(Left(_))
        ),
      initialOperator = _.map(Right(_))
    )
    .scanLeft {
      case Right(data) => data
      case Left(_)     => ???
    } { (acc, cur) =>
      cur match {
        case Right(data) => data
        case Left(_)     => acc
      }
    }
    .mapTask { case (systemMetadata, formId, versionIdOpt) =>
      getCueDefaultValuesObj(formId, versionIdOpt, systemMetadata)
    }

  private val cueDefaultValuesUpdateStream = cueDefaultValuesTaskSignal.changes
    .collect { case Resolved(_, Success(defaultValue), _) =>
      DataLayerUtils.dataLayerDefaultValueToFieldSchemaInfos(defaultValue)
    }

  private val cueDefaultValuesLoadingSignal = cueDefaultValuesTaskSignal
    .map {
      case Pending(_) => true
      case _          => false
    }

  private val formSignal = formDataSignal.map(_.form).distinct
  private val uploadedPdfSignal = formDataSignal.map(_.uploadedPdf).distinct
  private val embeddedPdfSignal = formDataSignal.map(_.embeddedPdf).distinct

  private val extractedPdfSignal =
    formDataSignal.map(_.uploadedPdf.map((fileId, extractedFile) => fileId -> extractedFile.name))

  private val associatedLinksSignal =
    formModelOptVar.signal.distinct.map(_.map(_.associatedLinks).getOrElse(Map.empty)).distinct

  private val formTypeSignal = viewingVersionOptVar.signal
    .combineWith(draftVersionOptVar.signal)
    .map { (viewingVersionOpt, draftVersionOpt) =>
      viewingVersionOpt.orElse(draftVersionOpt).fold(FormType.Default)(_.formType)
    }
    .distinct

  private val formStandardAliasMappingSignal = formBuilderDataSignal.map(_._2).distinct
  private val formMetadataSignal = formBuilderDataSignal.map(_._5).distinct
  private val formRuleWarningsSignal = formMetadataSignal.map(_.ruleWarnings).distinct
  private val ignoredInvalidRequireSetupsSignal = formMetadataSignal.map(_.ignoredInvalidRequireSetups).distinct
  private val rejectedAsaSuggestionsSignal = formMetadataSignal.map(_.rejectedAsaSuggestions).distinct
  private val rejectedGeneratedRulesSignal = formMetadataSignal.map(_.rejectedGeneratedRules)
  private val blueprintRefOptSignal = formMetadataSignal.map(_.blueprintRef).distinct

  private val ignoredInvalidPdfNonInputMappingFieldsSignal =
    formMetadataSignal.map(_.ignoredInvalidPdfNonInputMappingFields)

  private val ignoredUnmappedPdfNonInputFieldsSignal = formMetadataSignal.map(_.ignoredUnmappedPdfNonInputFields)

  private val optimizedLogicExtractorControlConfigsSignal =
    logicExtractorControlConfigsOptVar.signal
      .map(_.getOrElse(Seq.empty))
      .map(LogicUtils.optimizeLogicExtractorControlConfigs)

  private val (formRuleExtractResultSignal, formRuleExtractProgressSignal) =
    FormRuleExtractResult.getFormRuleExtractResultSignal(
      formSignal,
      optimizedLogicExtractorControlConfigsSignal
    )

  private val backendFormStandardAliasMappingVar = Var(Map.empty[String, String])
  private val allStandardAliasesVar = Var(List.empty[StandardAlias])

  private val previewDataEventBus = new EventBus[GaiaState]

  private val previewDataSignal = formSignal
    .map(_.defaultState)
    .composeChanges { thisChanges =>
      EventStream.merge(
        thisChanges,
        previewDataEventBus.events
      )
    }

  private val previewAppliedProfileOptVar = Var(Option.empty[FormTestSuiteId])
  private val previewAppliedProfileOptSignal = previewAppliedProfileOptVar.signal.distinct
  private val editModeVar = Var[BuilderMode](BuilderMode.ReadOnly)

  private val saveFormResponseVar = Var[Option[SaveFormResult]](None)

  private val gaiaRuntimeLogVar = Var(List.empty[GaiaRuntimeLog])

  private val createVersionEventBus = new EventBus[Unit]
  private val getVersionEventBus = new EventBus[Option[FormVersionId]]

  private val AutoSaveIntervalMs = 60000
  private val autoSaveEventStream = EventStream.periodic(intervalMs = AutoSaveIntervalMs).drop(1, resetOnStop = true)

  private val AutoRenewLockInternalMs = 60000

  private val autoRenewLockEventStream =
    EventStream.periodic(intervalMs = AutoRenewLockInternalMs).drop(1, resetOnStop = true)

  private val saveFormMappingEventBus = new EventBus[() => Unit]
  private val saveFormEventBus = new EventBus[SaveFormTaskParams]
  private val lastSaveFormParamsVar = Var(Option.empty[SaveFormParams])
  private val lastSaveFormMetadataVar = Var(Option.empty[SaveFormMetadataParams])

  private val editingSectionExpiredAtVar = Var(Option.empty[Instant])
  private val editingSessionErrorVar = Var(Option.empty[EditingSessionError])

  // Use this to detect current form in browser is up-to-date or not.
  private val latestFormCheckUpToDateDataVar = Var(Option.empty[FormCheckUpToDateData])
  private val isFormOutdatedVar = Var(false)

  private val showAccountUnauthorizedModalVar = Var(false)

  private val formIdOptSignal = formModelOptVar.signal.map(_.map(_.formId)).distinct

  private val formNameOptStream = EventStream.merge(
    formModelOptVar.signal.map(_.map(_.name)).changes,
    pageSignal.changes.sample(formModelOptVar.signal.map(_.map(_.name)))
  )

  private val libraryComponentsVar = Var[Option[Seq[LibraryComponent]]](None)

  private val investorAccessAsaValuesVar = Var[Set[String]](Set.empty)

  private val userInfoMapVar = Var(Map.empty[UserId, UserInfo])
  private val userInfoMapSignal = userInfoMapVar.signal

  private val isFullEditModeSignal = editModeVar.signal.map(_ == BuilderMode.EditFull).distinct

  private val hasReferenceVersionSignal = viewingVersionOptVar.signal
    .combineWith(draftVersionOptVar.signal)
    .map { case (viewingVersionOpt, draftVersionOpt) =>
      viewingVersionOpt.nonEmpty || draftVersionOpt.flatMap(_.parentVersionId).nonEmpty
    }
    .distinct

  private val allFileNamesSignal =
    uploadedPdfSignal
      .map(_.values.map(_.name.toLowerCase).toSet)
      .combineWith(embeddedPdfSignal.map(_.keySet.map(_.toLowerCase)))
      .map { case uploadedPdfNames -> embeddedPdfNames =>
        uploadedPdfNames ++ embeddedPdfNames
      }
      .distinct

  private val pdfCutsSignal = formMetadataSignal.map(_.pdfCuts).distinct

  private val containerMaxWidth = "1440px"

  private val digitizationFileOptVar = Var[Option[DigitizationFile]](None)

  private val parentFolderIdSignal: Signal[DigitizationFolderId] = digitizationFileOptVar.signal.distinct.map(
    _.map(_.fileModel.parentFolderId).getOrElse(DigitizationFolderId(DigitizationFolderId.RootFolderId))
  )

  private val backEventBus = new EventBus[Unit]

  private val ruleUpdatedEventBus = new EventBus[FormRule]
  private val validateRulesEventBus = new EventBus[Unit]
  private val reloadDraftEventBus = new EventBus[Unit]

  private lazy val invalidRulesSignal: Signal[List[FormRule]] =
    formDataSignal.combineWith(formRuleExtractResultSignal).map { case (formData, formRuleExtractResult) =>
      RuleValidation
        .validateExtractResult(formData.form.defaultSchema, formRuleExtractResult)
        .filter(_._3.isLeft)
        .map(_._2)
        .toList
    }

  private val uploadedAnnotationDataSignal = uploadedPdfSignal
    .combineWith(formSystemMetadataSignal.map(_.annotationMapping))
    .map { case (uploadedPdf, annotationMapping) =>
      uploadedPdf.flatMap { case (formFileId, extractedPdf) =>
        for {
          sysFileId <- FormDataConverters.fileIdTypeToFileId(formFileId)
        } yield {
          (
            formFileId,
            annotationMapping.getOrElse[SysFileId | AnnotationDocumentVersionId](
              sysFileId,
              extractedPdf.originalFileIdOpt.flatMap(FormDataConverters.fileIdTypeToFileId).getOrElse(sysFileId)
            ) -> extractedPdf.name
          )
        }
      }.toList
    }
    .splitTask(_._1) { (_, data) =>
      data match {
        case (sysFileId: SysFileId, fileName: String) =>
          AnnotationDocumentEndpointClient
            .extractAnnotationData(
              ExtractAnnotationParams(
                sysFileId,
                needStrippedFile = false
              )
            )
            .flatMap(ZIO.fromEither)
            .map { extractResp =>
              UploadedAnnotationData.File(
                pdfFileProperties = PdfFileProperties(
                  fileId = extractResp.baseAnnotationFileId,
                  fileHash = extractResp.annotationData.fileContentHash,
                  fileName = Option.when(fileName.nonEmpty)(fileName).getOrElse(extractResp.annotationData.fileName),
                  totalPages = extractResp.annotationData.totalPages,
                  pdfObjects = extractResp.annotationData.pdfObjects,
                  renderedPageMap = Map.empty[Int, PageArea],
                  metadata = PdfFileProperties.PropertiesMetadata()
                )
              )
            }
        case (versionId: AnnotationDocumentVersionId, fileName: String) =>
          AnnotationDocumentEndpointClient
            .getAnnotationDocument(
              GetAnnotationDocumentParams(versionId.parent, Some(versionId))
            )
            .flatMap(ZIO.fromEither)
            .map { getResp =>
              val annotationData = getResp.annotationData.copy(
                fileName = Option.when(fileName.nonEmpty)(fileName).getOrElse(getResp.annotationData.fileName)
              )
              UploadedAnnotationData.Annotation(getResp.version, annotationData)
            }
      }
    }
    .map(_.map {
      case Pending(input) => input._1 -> LoadingState.Loading()
      case Resolved(input, output, _) =>
        input._1 -> output.fold(
          err => LoadingState.Failed(err.getMessage),
          value => LoadingState.Success(value)
        )
    }.toMap)

  private val syncedUploadedPdfSignal =
    uploadedPdfSignal.combineWith(uploadedAnnotationDataSignal).map { case uploadedPdf -> uploadedAnnotationData =>
      uploadedPdf.map { case formFileId -> extractedPdf =>
        formFileId -> uploadedAnnotationData.get(formFileId).flatMap(_.toOption).fold(extractedPdf) { annotationData =>
          extractedPdf.copy(fields =
            PdfFieldConversionUtils.pdfObjectsToExtractedFields(annotationData.pdfFileProperties.pdfObjects)
          )
        }
      }
    }

  // ontology data
  private val ontologyStateVar: Var[FormOntologyState] = Var(FormOntologyState.empty)
  private val versionAsaMappingVar: Var[Seq[FormAsaMapping]] = Var(Seq.empty)
  private val draftVersionIdOptVar: Var[Option[FormVersionId]] = Var(None)

  private val currentVersionIdOptSignal: Signal[Option[FormVersionId]] = editModeVar.signal
    .combineWith(
      viewingVersionOptVar.signal,
      draftVersionIdOptVar.signal
    )
    .map { case (editMode, viewingVersionOpt, draftVersionIdOpt) =>
      editMode match {
        case BuilderMode.ReadOnly       => viewingVersionOpt.map(_.formVersionId).orElse(draftVersionIdOpt)
        case BuilderMode.EditUI         => viewingVersionOpt.map(_.formVersionId)
        case BuilderMode.EditAsaMapping => viewingVersionOpt.map(_.formVersionId)
        case BuilderMode.EditFull       => draftVersionIdOpt
      }
    }
    .distinct

  // end of ontology data
  private val blueprintVersionCodeStream = blueprintRefOptSignal
    .map(_.map(_.versionId))
    .changes
    .flatMapSwitch {
      case Some(versionId) =>
        AirStreamUtils.taskToStream(
          BlueprintEndpointClient
            .getBlueprintVersionCatalaCode(GetBlueprintVersionCatalaCodeParams(versionId))
            .flatMap(ZIO.fromEither)
            .map(resp => blueprintMetadataVar.set(resp.blueprintInfo.metadata))
        )
      case None =>
        AirStreamUtils.taskToStream(
          ZIO.succeed(blueprintMetadataVar.set(BlueprintMetadata()))
        )
    }

  private val blueprintMetadataSearchTextVar = Var("")

  private val ignoredMismatchedBlueprintMetadataFieldsSignal =
    formMetadataSignal.map(_.ignoredMismatchedBlueprintMetadata)

  private val ignoredNonImportedBlueprintMetadataFieldsSignal =
    formMetadataSignal.map(_.ignoredNonImportedBlueprintMetadata)

  private def handleMakeRequiredFieldsOptional(removeAsterisksData: ContentAsterisksProviderL.RemoveAsterisksData) = {
    formBuilderDataCommandEventBus.emit(
      FormBuilderDataCommand.updateFormWidgetMap { oldMap =>
        oldMap.map { (key, widget) =>
          val allSettings = SettingUtils.getWidgetUIKeys(widget.widgetType)
          val currentOpts = widget.uiOptions

          // Update required setting
          val removedRequired = if (allSettings.contains(UIKey.required)) {
            currentOpts.removed(UIKey.required)
          } else {
            currentOpts
          }
          val addCleanable = if (allSettings.contains(UIKey.clearable)) {
            removedRequired.updated(UIKey.clearable)(true)
          } else {
            removedRequired
          }

          // Update content
          val newContentSettings = if (allSettings.contains(UIKey.formattedText)) {
            removeAsterisksData.updatedContents.get(key).fold(addCleanable) { newContent =>
              addCleanable.updated(UIKey.formattedText)(newContent)
            }
          } else {
            addCleanable
          }
          key -> widget.copy(uiOptions = newContentSettings)
        }
      }
    )
  }

  private val cueModuleIdOptVar = Var[Option[CueModuleId]](None)

  private val headerModeVar = Var[HeaderMode](HeaderMode.DefaultMode)

  def apply(): Node = {
    ContentAsterisksProviderL(
      onRemoveAsterisks = Observer[ContentAsterisksProviderL.RemoveAsterisksData](handleMakeRequiredFieldsOptional)
    )(
      // Navigate to the original document of an embedded cut document
      EmbeddedDocumentNavigation(
        documentCutInfoSignal = pdfCutsSignal,
        extractedPdfSignal = extractedPdfSignal
      )(
        div(
          tw.flex.flexCol.hVh100,
          minWidth := "960px",
          formIdSignal.flatMapSwitch { formId =>
            getDigitizationFolderId(formId)
          } --> Observer.empty,
          formIdSignal
            .withCurrentValueOf(versionIdOptSignal)
            .flatMapSwitch { case (formId, versionIdOpt) =>
              fetchCoreData(
                formId,
                versionIdOpt,
                isInitialLoad = true
              )
            } --> initialLoadDoneEventBus.writer,
          EventStream
            .merge(
              reloadDraftEventBus.stream.mapTo(None),
              getVersionEventBus.events
            )
            .withCurrentValueOf(pageSignal)
            .flatMapSwitch { case (versionIdOpt, page) => fetchCoreData(page.formId, versionIdOpt) } --> Observer.empty,
          viewingVersionOptVar.signal.distinct
            .map(_.map(_.formVersionId))
            .changes
            .withCurrentValueOf(pageSignal)
            .mapN(setVersion) --> Observer.empty,
          standardMappingVersionIdOptVar.signal
            .withCurrentValueOf(versionIdOptSignal.map(_.nonEmpty), formDataSignal)
            .map { case (standardMappingVersionIdOpt, hasViewingVersion, formData) =>
              getFormStandardMappings(
                standardMappingVersionIdOpt = standardMappingVersionIdOpt,
                formData = formData,
                hasViewingVersion = hasViewingVersion
              )
            } --> Observer.empty,
          setTabEventBus.events.withCurrentValueOf(pageSignal).mapN(setTab) --> Observer.empty,
          formNameOptStream --> Observer[Option[String]] { nameOpt =>
            dom.document.title = Seq(nameOpt, Some("Form Builder")).flatten.mkString(" | ")
          },
          autoSaveEventStream.sample(formBuilderDataSignal).map(t => (t._1, t._5, t._6)) --> autoSaveObserver,
          autoRenewLockEventStream.flatMapSwitch(_ => onRenewLock()) --> Observer.empty,
          saveFormEventBus.events.flatMapSwitch(onSaveForm) --> Observer.empty,
          saveFormMappingEventBus.events
            .withCurrentValueOf(
              formStandardAliasMappingSignal,
              formMetadataSignal,
              formSystemMetadataSignal
            )
            .flatMapSwitch { case (cb, mapping, metadata, systemMetadata) =>
              onSaveFormMapping(
                mapping,
                metadata,
                systemMetadata,
                cb
              )
            } --> Observer.empty,
          editingSessionErrorVar.signal.distinct --> Observer[Option[EditingSessionError]] { errorOpt =>
            if (errorOpt.nonEmpty) editModeVar.set(BuilderMode.ReadOnly)
          },
          // When turning on edit mode, always turn off `triggerRuleByProperty` by setting it to false
          // as we want this value for all of the new forms
          editModeVar.signal.distinct.changes
            .filter {
              case BuilderMode.ReadOnly => false
              case _                    => true
            }
            .withCurrentValueOf(formBuilderDataSignal.map(_._1))
            .map(_._2) --> Observer[FormData] { formData =>
            formDataEventBus.emit(formData.turnOffTriggerRuleByProperty)
          },
          editModeVar.signal.distinct.changes.flatMapSwitch { isEditMode =>
            // Only trigger loading library components after start editing
            if (isEditMode == BuilderMode.EditFull && libraryComponentsVar.now().isEmpty) {
              loadLibraryComponents
            } else {
              EventStream.empty
            }
          } --> Observer.empty,
          // Edit associated links
          editAssociatedLinksEventBus.events.withCurrentValueOf(formIdOptSignal, associatedLinksSignal).flatMapSwitch {
            case (updateParams, formIdOpt, currentLinks) =>
              formIdOpt.fold[EventStream[Unit]](EventStream.empty)(
                editAssociatedLinks(
                  _,
                  currentLinks,
                  updateParams
                )
              )
          } --> Observer.empty,
          uploadedAnnotationDataSignal --> Observer.empty,
          blueprintVersionCodeStream --> Observer.empty,
          cueDefaultValuesUpdateStream --> formBuilderDataCommandEventBus.writer.contramap[Seq[FieldSchemaInfo]] {
            fieldInfos =>
              FormBuilderDataCommand.updateFormData { formData =>
                formData.copy(form = formData.form.copy(cueFieldSchemas = fieldInfos))
              }
          },
          initialLoadDoneEventBus.events
            .withCurrentValueOf(logicExtractorControlConfigsOptVar.signal)
            .flatMapSwitch(fetchLogicExtractorControlConfigs) --> Observer.empty,
          // start of ontology data handler
          currentVersionIdOptSignal.flatMapSwitch(_.fold(EventStream.empty)(loadOntologyAsaMappings)) --> Observer.empty,
          formIdOptSignal.flatMapSwitch(_.fold(EventStream.empty)(loadOntologyStatus)) --> Observer.empty,
          ontologyStateVar.signal
            .map(_.graph.isEmpty)
            .distinct
            .combineWith(versionAsaMappingVar.signal)
            --> Observer[(Boolean, Seq[FormAsaMapping])] { case (isGraphEmpty, mappings) =>
              if (!isGraphEmpty) {
                ontologyStateVar.update { state =>
                  state.copy(
                    mappings = mappings.flatMap { mapping =>
                      FormFieldMappingTarget
                        .unapply(mapping.fieldName)
                        .map(_ -> OntologyUtils.convertAsaMappingInfo(state.graph, mapping.asaInfo))
                    }.toMap
                  )
                }
              }
            },
          // end of ontology data handler
          banner,
          header,
          renderLogicErrorMessage,
          tabNode,
          body,
          // Error modals
          EditFormErrorModal(
            errorOptSignal = editingSessionErrorVar.signal.distinct,
            onDone = Observer { _ => finishEditingSession() }
          )(),
          FormOutdatedModal(isOutdatedSignal = isFormOutdatedVar.signal.distinct)(),
          AccountUnauthorizedModal(
            showAccountUnauthorizedModalSignal = showAccountUnauthorizedModalVar.signal.distinct,
            onClose = Observer(_ => showAccountUnauthorizedModalVar.set(false))
          )(),
          WithEmbeddedDocumentNavigation(div()) { renderProps =>
            div(
              renderProps.activateOriginalDocumentSideBySideOptSignal
                --> Observer[Option[EmbeddedDocumentNavigation.ActiveOriginalFile]] { originalFileOpt =>
                  if (originalFileOpt.nonEmpty) {
                    sideViewerOpenVar.set(true)
                  }
                }
            )
          }
        )
      )
    )
  }

  private val banner = {
    child.maybe <-- formSignal
      .map(_.triggerRuleByProperty)
      .map(
        Option.when(_)(
          CalloutL(
            style = Callout.Style.Primary()
          )("This version has trigger rule by used properties switch on. This feature will be turn off for new forms")
        )
      )
  }

  private val header: Node = {
    div(
      ruleUpdatedEventBus.events.withCurrentValueOf(
        formSignal,
        gaiaRuntimeLogVar.signal
      ) --> Observer[(FormRule, Form, List[GaiaRuntimeLog])] { case (formRule, form, runtimeLogs) =>
        val formWithUpdatedRule = form.copy(rules = List(formRule))
        checkInitializingRules(formWithUpdatedRule, runtimeLogs, Some(formRule))
      },
      validateRulesEventBus.events.take(1).withCurrentValueOf(invalidRulesSignal, formSignal) --> Observer[
        (List[FormRule], Form)
      ] { case (invalidRules, form) =>
        val formWithValidRules = form.copy(rules = form.rules.diff(invalidRules))
        checkInitializingRules(formWithValidRules, List.empty, None)
      },
      FormBuilderHeader(
        activeVersionIdOptSignal = currentVersionIdOptSignal,
        formModelOptSignal = formModelOptVar.signal.distinct,
        digitizationFileOptSignal = digitizationFileOptVar.signal.distinct,
        ignoredInvalidRequireSetupsSignal = ignoredInvalidRequireSetupsSignal,
        ignoredInvalidPdfNonInputMappingFieldsSignal = ignoredInvalidPdfNonInputMappingFieldsSignal,
        ignoredUnmappedPdfNonInputFieldsSignal = ignoredUnmappedPdfNonInputFieldsSignal,
        formSignal = formSignal,
        formDataSignal = formDataSignal,
        formMetadataSignal = formMetadataSignal,
        formSystemMetadataSignal = formSystemMetadataSignal,
        formTypeSignal = formTypeSignal,
        formStandardAliasMappingSignal = formStandardAliasMappingSignal,
        formRuleExtractResultSignal = formRuleExtractResultSignal,
        rejectedAsaSuggestionsSignal = rejectedAsaSuggestionsSignal,
        rejectedGeneratedRulesSignal = rejectedGeneratedRulesSignal,
        hasReferenceVersionSignal = hasReferenceVersionSignal,
        allStandardAliasesSignal = allStandardAliasesVar.signal.distinct,
        uploadedPdfSignal = syncedUploadedPdfSignal,
        uploadedAnnotationDataSignal = uploadedAnnotationDataSignal.map(_.flatMap { case (fileId, data) =>
          data match {
            case LoadingState.Success(UploadedAnnotationData.Annotation(version, annotationData)) =>
              Some(fileId -> annotationData)
            case _ => None
          }
        }),
        embeddedPdfSignal = embeddedPdfSignal,
        selectedSignal = selectedSignal,
        investorAccessAsaValuesSignal = investorAccessAsaValuesVar.signal,
        formRuleWarningsSignal = formRuleWarningsSignal,
        draftVersionOptVar = draftVersionOptVar,
        viewingVersionOptVar = viewingVersionOptVar,
        editModeSignal = editModeVar.signal.distinct,
        latestFormCheckUpToDateDataSignal = latestFormCheckUpToDateDataVar.signal.distinct,
        formFolderIdSignal = parentFolderIdSignal,
        formBuilderDataCommandObserver = formBuilderDataCommandEventBus.writer,
        onSaveForm = saveFormEventBus.writer,
        onSaveFormMapping = saveFormMappingEventBus.writer,
        onImportForm = Observer[SaveFormTaskParams] { params =>
          // Always use `triggerRuleByProperty` = false for new forms
          onImportForm(params.params.formData.turnOffTriggerRuleByProperty, params.params.metadataOpt)
          params.afterRefresh()
          saveFormEventBus.emit(params)
        },
        onImportDone = reloadDraftEventBus.writer,
        onDoneImportingAsa = Observer[ImportFormStandardAliasMappingResponse] { res =>
          formBuilderDataCommandEventBus.emit {
            FormBuilderDataCommand.updateFormStandardAliasMapping(_ => res.addedMapping)
          }
          backendFormStandardAliasMappingVar.set(res.addedMapping)
        },
        onVersionCreated = Observer[FormCheckUpToDateData] { checkData =>
          latestFormCheckUpToDateDataVar.set(Some(checkData))
          createVersionEventBus.emit(())
        },
        onStartedEditing = Observer[(Instant, BuilderMode.Editable)] { case (expiredAt, editable) =>
          editingSectionExpiredAtVar.set(Some(expiredAt))
          editModeVar.set(editable)
        },
        onFailToStart = Observer[EditingSessionError] {
          case FormOutDated => isFormOutdatedVar.set(true)
          case error @ _    => editingSessionErrorVar.set(Some(error))
        },
        onFailToSave = Observer[EditFormDataError] { error =>
          editingSessionErrorVar.set(Some(EditingSessionError.fromEditFormDataError(error)))
        },
        onFinishedEditing = Observer { _ => finishEditingSession() },
        onSmartEnableCommenting = formBuilderDataCommandEventBus.writer.contramap[Unit] { _ =>
          FormBuilderDataCommand.updateFormSchema { curFormSchema =>
            BuilderUtils.enableAllCommenting(curFormSchema)
          }
        },
        onSelectKey = formBuilderDataCommandEventBus.writer.contramap[String] { key =>
          setTabEventBus.emit(FormTab.BuildTab)
          FormBuilderDataCommand.updateSelection(_ => FormBuilderSelection.Single(key))
        },
        onSelectKeyWithPDF = formBuilderDataCommandEventBus.writer.contramap[SelectKeyEventData] { data =>
          val pdfFieldData = PdfFieldData(
            NonEmptyList.single((data.fileId, data.alias)),
            PdfFieldSelectionOrigin.Builder
          )
          setTabEventBus.emit(FormTab.BuildTab)
          FormBuilderDataCommand.updateSelection(_ => FormBuilderSelection.SideBySide(data.alias, pdfFieldData))
        },
        onSwitchToDocumentTab = Observer(_ => setTabEventBus.emit(FormTab.DocumentTab)),
        settingPanelTabObserver = settingPanelTabVar.writer,
        settingPanelEditingRuleObserver = settingPanelEditingRuleVar.writer,
        sideViewerOpenVar = sideViewerOpenWriter.contramap[Unit](_ => true),
        backObserver = backEventBus.writer,
        gaiaRuntimeLogSignal = gaiaRuntimeLogVar.signal,
        onRuleChanged = ruleUpdatedEventBus.writer,
        onValidateRules = validateRulesEventBus.writer,
        cueModuleIdSignal = cueModuleIdOptVar.signal,
        cueModuleIdObserver = cueModuleIdOptVar.writer.contramap[CueModuleId](Option.apply),
        cueDefaultValuesLoadingSignal = cueDefaultValuesLoadingSignal,
        cueModuleDataSyncObserver = cueModuleDataSyncEventBus.writer,
        blueprintMetadataSignal = blueprintMetadataVar.signal,
        onHeaderModeChange = headerModeVar.writer,
        blueprintRefOptSignal = blueprintRefOptSignal,
        onChangeBpMetadataSearchText = blueprintMetadataSearchTextVar.writer,
        ignoredMismatchedBlueprintMetadataFieldsSignal = ignoredMismatchedBlueprintMetadataFieldsSignal,
        ignoredNonImportedBlueprintMetadataFieldsSignal = ignoredNonImportedBlueprintMetadataFieldsSignal
      )().amend(
        backEventBus.events
          .sample(parentFolderIdSignal) --> Observer[DigitizationFolderId] { parentFolderId =>
          PantheonRouter.Router.refreshToPage { _ =>
            DigitizationDashboardViewPage(parentFolderId)
          }
        }
      )
    )
  }

  private def checkInitializingRules(
    form: Form,
    currentRuntimeLogs: List[GaiaRuntimeLog],
    updatedFormRuleOpt: Option[FormRule]
  ): Unit = {
    for {
      engine <- GaiaEngine.make(
        form = form,
        config = EngineConfiguration.strictDefault,
        env = EngineContext.default.copy(
          runtimeLogHandlerOpt = Some(logs => {
            val newLogs = currentRuntimeLogs
              .collect { case log: RuleExecuteFailed => log }
              .filterNot(log => updatedFormRuleOpt.exists(_.name == log.ruleName)) ++ logs

            gaiaRuntimeLogVar.set(newLogs)
          })
        )
      )
      _ <- engine.replay(Seq.empty)
    } ()
  }

  private val tabNode = div(
    tw.flex.bgGray0,
    TabL(
      activeTabSignal = tabSignal.map { tab =>
        Some(tab.ordinal)
      },
      alignment = Tab.Alignment.Center,
      panels = List(
        Tab.Panel(title = "Versions"),
        Tab.Panel(title = "Build"),
        Tab.Panel(title = "Logic"),
        Tab.Panel(title = "Preview"),
        Tab.Panel(title = "Documents"),
        Tab.Panel(title = "Profile"),
        Tab.Panel(title = "Test script"),
        Tab.Panel(title = "Import data")
      ),
      style = Tab.Style.Minimal(),
      renderHeader = Option { renderProps =>
        div(
          div(
            tw.grid,
            // The tab titles are always displayed at the center
            styleProp("grid-template-columns") := "1fr repeat(1, auto) 1fr",
            renderProps.renderTitles.amend(
              styleProp("grid-column-start") := "2"
            ),
            div(
              tw.mlAuto.flex.itemsCenter,
              child.maybe <-- saveFormResponseVar.signal.distinct.map { saveFormResponse =>
                saveFormResponse.flatMap(_.draftVersion.createdAt).map { at =>
                  div(
                    tw.textGray6.pr16,
                    "Last saved: ",
                    printInstant(at)
                  )
                }
              }
            )
          ),
          renderProps.renderHeaderBorder
        )
      },
      onClick = Observer[Int] { tabIndex =>
        val tab = tabIndex match {
          case 0 => FormTab.VersionTab
          case 1 => FormTab.BuildTab
          case 2 => FormTab.LogicTab
          case 3 => FormTab.PreviewTab
          case 4 => FormTab.DocumentTab
          case 5 => FormTab.ProfileTab
          case 6 => FormTab.TestScriptTab
          case 7 => FormTab.ImportTab
        }
        setTabEventBus.emit(tab)
      }
    )()
  )

  private def printInstant(at: Instant): String = {
    JsDateFormatterUtils.format(at, JsDateFormatterUtils.JsDateFormat.ShortDateAndTime2)
  }

  private val buildNode: Node = {
    val isVisibleSignal = tabSignal.map(_ == FormTab.BuildTab).distinct
    val keysInFormRulesSignal =
      formRuleExtractResultSignal.map(_.flatMap(_._3.toOption).flatMap(_.allKeys).map(_.key).toSet).distinct

    div(
      tw.hPc100,
      isVisibleSignal.not.cls(tw.hidden),
      backgroundColor := "#FAFCFD",
      child <-- formIdOptSignal
        .combineWith(viewedTabsVar.signal.map(_.contains(FormTab.BuildTab)))
        .distinct
        .map { case (formIdOpt, hasViewedBuilderTab) =>
          formIdOpt
            // Don't render builder tab until it is viewed
            .filter(_ => hasViewedBuilderTab)
            .fold[Node] {
              BlockIndicatorL()()
            } { formId =>
              FormBuilder(
                formId = formId,
                formSignal = formSignal,
                formTypeSignal = formTypeSignal,
                activeVersionIdOptSignal = currentVersionIdOptSignal,
                formFolderIdSignal = parentFolderIdSignal,
                formStandardAliasMappingSignal = formStandardAliasMappingSignal,
                rejectedAsaSuggestionsSignal = rejectedAsaSuggestionsSignal,
                allStandardAliasesVar = allStandardAliasesVar,
                ignoredInvalidRequireSetupsSignal = ignoredInvalidRequireSetupsSignal,
                uploadedPdfSignal = syncedUploadedPdfSignal,
                uploadedAnnotationDataSignal = uploadedAnnotationDataSignal,
                embeddedPdfSignal = embeddedPdfSignal,
                selectedSignal = selectedSignal,
                formRuleWarningsSignal = formRuleWarningsSignal,
                formBuilderDataCommandObserver = formBuilderDataCommandEventBus.writer,
                isVisibleSignal = isVisibleSignal,
                editModeSignal = editModeVar.signal.distinct,
                hasReferenceVersionSignal = hasReferenceVersionSignal,
                formRuleExtractResultSignal = formRuleExtractResultSignal,
                keysInFormRulesSignal = keysInFormRulesSignal,
                libraryComponentsSignal = libraryComponentsVar.signal.map(_.getOrElse(Seq.empty)),
                settingPanelTabSignal = settingPanelTabVar.signal,
                settingPanelEditingRuleSignal = settingPanelEditingRuleVar.signal,
                formBuilderUtilEventStreams = FormBuilderUtilEventStreams(
                  fieldsAddedEventStream = fieldsAddedEventBus.events,
                  fieldRenameEventStream = fieldsRenameEventBus.events,
                  fieldsRemovedEventStream = fieldsRemovedEventBus.events,
                  keyModifiedEventStream = keyModifiedEventBus.events
                ),
                formBuilderUndoRedoParams = FormBuilderUndoRedoParams(
                  canUndoSignal = () => formBuilderDataSignal.map(_._3).distinct,
                  canRedoSignal = () => formBuilderDataSignal.map(_._4).distinct,
                  undoRedoObserver = formBuilderDataStateCommandEventBus.writer
                ),
                sideViewerOpenSignal = sideViewerOpenSignal,
                sideViewerOpenObserver = sideViewerOpenWriter,
                sideViewerOpenFlipObserver = sideViewerOpenFlipObserver,
                settingPanelTabObserver = settingPanelTabVar.writer,
                settingPanelEditingRuleObserver = settingPanelEditingRuleVar.writer,
                pdfCutsSignal = pdfCutsSignal,
                allFileNamesSignal = allFileNamesSignal,
                ontologyStateSignal = ontologyStateVar.signal,
                ontologyStateObserver = Observer { func => ontologyStateVar.update(func) },
                blueprintRefOptSignal = blueprintRefOptSignal,
                blueprintMetadataSignal = blueprintMetadataVar.signal,
                headerModeSignal = headerModeVar.signal.distinct,
                onHeaderModeChange = headerModeVar.writer,
                bpMetadataSearchTextSignal = blueprintMetadataSearchTextVar.signal.distinct,
                onChangeBpMetadataSearchText = blueprintMetadataSearchTextVar.writer
              )().amend(tw.hPc100)
            }
        }
    )
  }

  private val logicNode: Node = {
    div(
      tabSignal.map(_ == FormTab.LogicTab).distinct.not.cls(tw.hidden),
      tw.hPc100.overflowAuto,
      LogicBuilder(
        formSignal = formSignal,
        formRuleExtractResultSignal = formRuleExtractResultSignal,
        formRuleExtractProgressSignal = formRuleExtractProgressSignal,
        formBuilderDataCommandObserver = formBuilderDataCommandEventBus.writer,
        isFullEditModeSignal = isFullEditModeSignal,
        isLogicTabOpenSignal = tabSignal.map(_ == FormTab.LogicTab),
        onRuleChanged = ruleUpdatedEventBus.writer
      )()
    )
  }

  private val previewSettingsVar = Var[BuilderPreview.PreviewSettings](BuilderPreview.PreviewSettings.Default)

  private val renderLogicErrorMessage = {

    // Only show error on view mode only
    val hasErrorSignal = editModeVar.signal
      .map(_ == BuilderMode.ReadOnly)
      .withCurrentValueOf(
        formSignal,
        formRuleExtractResultSignal
      )
      .map { case (isViewOnly, form, formRuleExtractResult) =>
        isViewOnly && RuleValidation.validateExtractResult(form.defaultSchema, formRuleExtractResult).exists(_._3.isLeft)
      }

    div(
      child.maybe <-- hasErrorSignal.map(Option.when(_) {
        CalloutL(
          style = Callout.Style.Danger()
        )("This form version has logic errors and won't run until they are fixed.")
      })
    )
  }

  private val previewNode = div(
    tabSignal.map(_ == FormTab.PreviewTab).distinct.not.cls(tw.hidden),
    tw.mxAuto.hPc100.overflowAuto,
    child.maybe <-- tabSignal.map(_ == FormTab.PreviewTab).map {
      Option.when(_) {
        div(
          tw.hPc100,
          onMountInsert { ctx =>
            child <-- formIdOptSignal
              .combineWith(
                viewingVersionOptVar.signal.map(_.map(_.formVersionId)).distinct,
                formDataSignal,
                formStandardAliasMappingSignal,
                allStandardAliasesVar.signal,
                selectedSignal,
                formRuleExtractResultSignal
              )
              .distinct
              .map {
                case (
                      formIdOpt,
                      versionIdOpt,
                      formData,
                      asaMapping,
                      allStandardAliases,
                      selected,
                      formRuleExtractResult
                    ) =>
                  val builderPreviewNodeOpt = for {
                    formId <- formIdOpt
                  } yield {
                    val selectedKey = selected match {
                      case FormBuilderSelection.Empty                       => ""
                      case FormBuilderSelection.Single(alias)               => alias
                      case FormBuilderSelection.SideBySide(builderAlias, _) => builderAlias
                    }
                    BuilderPreview(
                      formId = formId,
                      versionIdOpt = versionIdOpt,
                      formData = formData,
                      asaMapping = asaMapping,
                      selectedKey = selectedKey,
                      formRuleExtractResult = formRuleExtractResult,
                      initialEventsOrData = Left(
                        previewDataSignal
                          .observe(
                            using ctx.owner
                          )
                          .now()
                          .events
                          .map(_.trigger)
                          .toList
                      ),
                      dataSignal = previewDataSignal,
                      dataObserver = previewDataEventBus.writer,
                      previewSettingsSignal = previewSettingsVar.signal.distinct,
                      previewSettingsObserver = previewSettingsVar.writer,
                      appliedProfileOptSignal = previewAppliedProfileOptSignal,
                      appliedProfileOptObserver = previewAppliedProfileOptVar.writer,
                      allStandardAliases = allStandardAliases
                    )()
                  }
                  builderPreviewNodeOpt.getOrElse(BlockIndicatorL()())
              }
          }
        )
      }
    }
  )

  private val documentNode: Node = {
    div(
      tabSignal.map(_ == FormTab.DocumentTab).distinct.not.cls(tw.hidden),
      tw.hPc100.overflowAuto,
      div(
        tw.mxAuto,
        maxWidth := containerMaxWidth,
        child.maybe <-- formIdOptSignal
          .combineWith(viewedTabsVar.signal.map(_.contains(FormTab.DocumentTab)))
          .distinct
          .map { case (formIdOpt, hasViewed) =>
            formIdOpt.filter(_ => hasViewed).map { formId =>
              BuilderDocument(
                formId = formId,
                formVersionIdOptSignal = currentVersionIdOptSignal,
                formFolderIdSignal = parentFolderIdSignal,
                formSchemaSignal = formSignal.map(_.defaultSchema).distinct,
                isEditModeSignal = isFullEditModeSignal,
                uploadedPdfSignal = syncedUploadedPdfSignal,
                embeddedPdfSignal = embeddedPdfSignal,
                associatedLinksSignal = associatedLinksSignal,
                ignoredMismatchAsaSignal = formMetadataSignal.map(_.ignoredMismatchAsa).distinct,
                allFileNamesSignal = allFileNamesSignal,
                formStandardAliasMappingSignal = formStandardAliasMappingSignal,
                allStandardAliasesSignal = allStandardAliasesVar.signal.distinct,
                pdfCutsSignal = pdfCutsSignal,
                uploadedAnnotationDataSignal = uploadedAnnotationDataSignal,
                formSystemMetadataSignal = formSystemMetadataSignal,
                userInfoMapSignal = userInfoMapSignal,
                blueprintRefOptSignal = blueprintRefOptSignal,
                formBuilderDataCommandObserver = formBuilderDataCommandEventBus.writer,
                onFieldJump = formBuilderDataCommandEventBus.writer.contramap[String] { key =>
                  setTabEventBus.emit(FormTab.BuildTab)
                  FormBuilderDataCommand.updateSelection(_ => FormBuilderSelection.Single(key))
                },
                onUpdateAssociatedLinks = editAssociatedLinksEventBus.writer,
                onOpenLinkedPdf = Observer { versionId =>
                  PantheonRouter.Router.openPageInNewTab { currentPageOpt =>
                    PantheonPdfToolPage(
                      documentId = versionId.parent,
                      commonParams = PdfToolCommonParams(versionId = Some(versionId.value)),
                      fromPage = currentPageOpt
                    )
                  }
                }
              )()
            }
          }
      )
    )
  }

  private val versionNode: Node = {
    val isVisibleSignal = tabSignal.map(_ == FormTab.VersionTab)
    div(
      tw.hPc100.overflowAuto,
      isVisibleSignal.not.cls(tw.hidden),
      div(
        tw.mxAuto,
        maxWidth := containerMaxWidth,
        child.maybe <-- tabSignal.combineWith(formIdOptSignal).distinct.map { case (tab, formIdOpt) =>
          formIdOpt.flatMap { formId =>
            Option.when(tab == FormTab.VersionTab) {
              FormVersionView(
                formId = formId,
                draftVersionOptVar = draftVersionOptVar,
                viewingVersionOptVar = viewingVersionOptVar,
                createVersionEventStream = createVersionEventBus.events,
                isEditModeSignal = editModeVar.signal.map(_ != BuilderMode.ReadOnly),
                userInfoMapSignal = userInfoMapSignal,
                onChangeVersion = Observer { idOpt => getVersionEventBus.emit(idOpt) },
                onOpenMapping = Observer { id =>
                  PantheonRouter.Router.openPageInNewTab { currentPageOpt =>
                    PantheonFormMappingPage(id, fromPage = currentPageOpt)
                  }
                }
              )()
            }
          }
        }
      )
    )
  }

  private val testingNode: Node = {
    div(
      tw.hPc100.overflowAuto,
      tabSignal.map(_ == FormTab.ProfileTab).not.cls(tw.hidden),
      div(
        tw.mxAuto,
        maxWidth := containerMaxWidth,
        child.maybe <-- tabSignal
          .combineWith(formIdOptSignal, viewingVersionOptVar.signal.distinct)
          .distinct
          .map { case (tab, formIdOpt, viewingVersionOpt) =>
            formIdOpt.flatMap { formId =>
              Option.when(tab == FormTab.ProfileTab) {
                BuilderTestToolsView(
                  formId = formId,
                  viewingVersionOpt = viewingVersionOpt,
                  asaMappingSignal = formStandardAliasMappingSignal,
                  allStandardAliasesSignal = allStandardAliasesVar.signal
                )()
              }
            }
          }
      )
    )
  }

  private val testScriptNode: Node = {
    div(
      tabSignal.map(_ == FormTab.TestScriptTab).not.cls(tw.hidden),
      tw.wPc100.hPc100.overflowAuto.mxAuto,
      child.maybe <-- tabSignal
        .combineWith(formIdOptSignal)
        .distinct
        .map { case (tab, formIdOpt) =>
          formIdOpt.flatMap { formId =>
            Option.when(tab == FormTab.TestScriptTab) {
              FormTestScriptDashboard(
                formId = formId
              )()
            }
          }
        }
    )
  }

  private val importNode: Node = {
    div(
      tw.hPc100.overflowAuto,
      tabSignal.map(_ == FormTab.ImportTab).not.cls(tw.hidden),
      div(
        tw.mxAuto,
        maxWidth := containerMaxWidth,
        child.maybe <-- formIdOptSignal
          .combineWith(viewingVersionOptVar.signal.distinct)
          .distinct
          .map { case (formIdOpt, formVersionOpt) =>
            formIdOpt.map(formId =>
              ImportDataView(
                formId = formId,
                formVersionIdOpt = formVersionOpt.map(_.formVersionId),
                onPreviewData = Observer[GaiaState] { state =>
                  previewDataEventBus.emit(state)
                  setTabEventBus.emit(FormTab.PreviewTab)
                }
              )()
            )
          }
      )
    )
  }

  private val body: Node = div(
    tw.flexFill.overflowHidden,
    tabSignal --> Observer[FormTab] { tab =>
      if (!viewedTabsVar.now().contains(tab)) {
        viewedTabsVar.update(_ + tab)
      }
    },
    versionNode,
    buildNode,
    logicNode,
    previewNode,
    documentNode,
    testingNode,
    testScriptNode,
    importNode
  )

  private def getCoreStandardMappings = {
    for {
      (standardAliasList, investorAccessAsaValues) <- FormEndpointClient
        .getAllStandardAliases(())
        .map(_.toOption.getOrElse(List.empty)) <&> FormEndpointClient
        .getAllInvestorAccessAsaValues(())
        .map(_.toOption.map(_.mappings).getOrElse(Set.empty))
    } yield {
      val sortedStandardAliasList =
        standardAliasList.filter(_.forFundSubWorkflowSetup).sortBy(_.alias) ++
          standardAliasList.filterNot(_.forFundSubWorkflowSetup).sortBy(_.alias)

      Var.set(
        allStandardAliasesVar -> sortedStandardAliasList,
        investorAccessAsaValuesVar -> investorAccessAsaValues
      )
    }
  }

  private def fetchLogicExtractorControlConfigs(
    logicExtractorControlConfigsOpt: Option[Seq[FormToolConfigProperty.LogicExtractorControl]]
  ): EventStream[Unit] =
    logicExtractorControlConfigsOpt.fold {
      AirStreamUtils.taskToStream {
        for {
          res <- FormEndpointClient.getFormToolConfigs(
            GetFormToolConfigsParams(
              property = Some(
                FormToolConfigProperty(
                  FormToolConfigProperty.Value.LogicExtractorControl(
                    FormToolConfigProperty.LogicExtractorControl()
                  )
                )
              ),
              active = Some(true)
            )
          )
        } yield res.fold(
          _ => Toast.error(s"Failed to load form tool configs"),
          data => {
            val configs = data.configs.flatMap(_.property.flatMap(_.value.logicExtractorControl))

            logicExtractorControlConfigsOptVar.set(Some(configs))
          }
        )
      }
    }(_ => EventStream.unit())

  private def getFormStandardMappings(
    standardMappingVersionIdOpt: Option[FormVersionId],
    formData: FormData,
    hasViewingVersion: Boolean
  ) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        getAsaMappingResOpt <- standardMappingVersionIdOpt.map { versionId =>
          FormEndpointClient
            .getFormStandardAliasMapping(
              GetFormStandardAliasMappingParams(versionId, checkExistingFormAlias = false)
            )
            .map(_.toOption)
        } getOrElse ZIO.attempt[Option[Map[String, String]]](None)
        _ <- ZIO.logInfo("Done loading form standard mappings")
      } yield {
        getAsaMappingResOpt.map { getAsaMappingRes =>
          val formStandardAliasMapping = if (hasViewingVersion) {
            val existingAliases = BuilderUtils.findAllKeysSeq(formData.form.defaultSchema)

            getAsaMappingRes.view.filterKeys { k =>
              val alias = k.split(MatchedValueCompanion.AliasValueSeparator).headOption.getOrElse("")
              // Handle both alias mapping & value mapping cases
              existingAliases.contains(alias)
            }.toMap
          } else {
            getAsaMappingRes
          }

          EventBus.emit(
            formStandardAliasMappingEventBus -> formStandardAliasMapping
          )

          Var.set(
            backendFormStandardAliasMappingVar -> formStandardAliasMapping
          )
        }
      }
    }
  }

  private def fetchCoreData(
    formId: FormId,
    versionIdOpt: Option[FormVersionId],
    isInitialLoad: Boolean = false
  ) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        _ <- getForm(formId, versionIdOpt)
        _ <- if (isInitialLoad) getCoreStandardMappings else ZIO.unit
        _ <- ZIO.logInfo("Done loading backend core data")
      } yield ()
    }
  }

  private def getForm(
    formId: FormId,
    versionIdOpt: Option[FormVersionId]
  ): ZIO[Any, Throwable, Unit] = {
    for {
      currentUser <- UserUtils.getCurrentUserInfo()
      (getFormEither, getFormCueModuleEither) <- FormEndpointClient
        .getForm(GetFormParams(formId, versionIdOpt))
        .zipPar(
          FormEndpointClient
            .getFormCueModule(
              GetFormCueModuleParams(formId)
            )
        )
      activeVersionIdOpt = getFormEither.toOption.flatMap(res =>
        res.versionOpt
          .map(_.formVersionId)
          // If viewing version is empty, return draft's parent version
          .orElse(
            res.formModel.draftVersion
              .flatMap(draft => draft.parentVersionId)
              // If draft's parent version is also empty, return non-default latest version ID
              .orElse(FormVersionId.getNonDefaultId(res.formModel.latestVersionId))
          )
      )
    } yield {
      currentUserVar.set(currentUser)
      getFormEither.fold(
        error => Toast.error(s"Unable to load form. Error: ${error.getMessage}"),
        resp => {
          val viewingVersionOpt = versionIdOpt.fold {
            if (resp.formModel.draftVersion.isEmpty) {
              resp.versionOpt
            } else {
              None
            }
          } { _ => resp.versionOpt }
          EventBus.emit(
            formDataEventBus -> resp.formData,
            formMetadataEventBus -> (resp.metadata, resp.systemMetadata)
          )

          Var.set(
            viewingVersionOptVar -> viewingVersionOpt,
            standardMappingVersionIdOptVar -> activeVersionIdOpt,
            draftVersionOptVar -> resp.formModel.draftVersion,
            formModelOptVar -> Some(resp.formModel),
            latestFormCheckUpToDateDataVar -> Some(resp.checkData),
            draftVersionIdOptVar -> Some(resp.draftVersionId),
            userInfoMapVar -> resp.userInfoMap,
            cueModuleIdOptVar -> getFormCueModuleEither.toOption.flatMap(_.cueModuleOpt.map(_.id))
          )
        }
      )
    }
  }

  private val autoSaveObserver = Observer[(FormData, FormVersionMetadataModel, FormVersionSystemMetadataModel)] {
    case (formData, formMetadata, formSystemMetadata) =>
      val editMode = editModeVar.now()
      if (editMode != BuilderMode.ReadOnly) {
        saveFormMappingEventBus.emit(() => ())
        // Don't save form on edit ASA mapping mode
        if (editMode != BuilderMode.EditAsaMapping) {
          formModelOptVar.now().foreach { formModel =>
            val params = ClientFormDataUtils.constructSaveDraftParams(
              formModel.formId,
              GaiaSessionStorageUtils.getCurrentSessionId(AssetSessionType.FormBuilder),
              formData,
              formMetadata,
              formSystemMetadata,
              draftVersionOptVar.now(),
              viewingVersionOptVar.now()
            )
            saveFormEventBus.emit(SaveFormTaskParams(params))
          }
        }
      }
  }

  private def onSaveFormMapping(
    formStandardAliasMapping: Map[String, String],
    metadata: FormVersionMetadataModel,
    systemMetadata: FormVersionSystemMetadataModel,
    callback: () => Unit
  ): EventStream[Unit] = {
    AirStreamUtils.taskToStreamDEPRECATED {
      ZIOUtils.when(isEditingSectionValid) {
        val editMode = editModeVar.now()
        val backendFormStandardMapping = backendFormStandardAliasMappingVar.now()
        val stagingFormStandardMapping = formStandardAliasMapping
        // addedMapping should include case of updating previous standard alias mapping
        val addedMapping = stagingFormStandardMapping.toList.filterNot(backendFormStandardMapping.toList.contains).toMap
        val removedMapping = backendFormStandardMapping.keySet -- stagingFormStandardMapping.keySet
        val hasStandardAliasMappingChange = addedMapping.nonEmpty || removedMapping.nonEmpty
        val activeVersionIdOpt = viewingVersionOptVar
          .now()
          .map(_.formVersionId)
          .orElse(
            draftVersionOptVar
              .now()
              .flatMap(draftVer => draftVer.parentVersionId)
              .orElse(
                formModelOptVar.now().flatMap(modelOpt => FormVersionId.getNonDefaultId(modelOpt.latestVersionId))
              )
          )

        ZIOUtils
          .traverseOptionUnit(activeVersionIdOpt) { formVersionId =>
            for {
              // 1. Save ASA mapping
              _ <- ZIOUtils.when(hasStandardAliasMappingChange)(
                for {
                  _ <- ZIOUtils.when(removedMapping.nonEmpty) {
                    FormEndpointClient
                      .removeFormStandardAliasMapping(
                        RemoveFormStandardAliasMappingParams(formVersionId, removedMapping)
                      )
                      .map(_ => ())
                  }
                  _ <- ZIOUtils.when(addedMapping.nonEmpty) {
                    FormEndpointClient
                      .addFormStandardAliasMapping(
                        AddFormStandardAliasMappingParams(formVersionId, addedMapping)
                      )
                      .map(_ => ())
                  }
                  _ <- ZIO.attempt(backendFormStandardAliasMappingVar.set(stagingFormStandardMapping))
                } yield ()
              )
              // 2. Save form metadata if the edit mode is ASA mapping
              _ <- ZIOUtils.when(editMode == BuilderMode.EditAsaMapping)(
                saveFormMetadata(formVersionId, metadata, systemMetadata)
              )
            } yield ()
          }
          .map(_ => callback())
      }
    }
  }

  private def saveFormMetadata(
    formVersionId: FormVersionId,
    metadata: FormVersionMetadataModel,
    systemMetadata: FormVersionSystemMetadataModel
  ) = {
    val params = SaveFormMetadataParams(
      formVersionId,
      GaiaSessionStorageUtils.getCurrentSessionId(AssetSessionType.FormBuilder),
      metadata -> systemMetadata
    )
    // Make sure we don't call this API if the metadata is not changed from last call
    ZIOUtils.when(!lastSaveFormMetadataVar.now().contains(params)) {
      FormEndpointClient.saveFormMetadata(params).map { _ =>
        lastSaveFormMetadataVar.set(Some(params))
        ()
      }
    }
  }

  private def onSaveForm(taskParams: SaveFormTaskParams): EventStream[Unit] = {
    AirStreamUtils.taskToStreamDEPRECATED {
      ZIOUtils.when(isEditingSectionValid) {
        val hasFormChange = !lastSaveFormParamsVar.now().contains(taskParams.params)
        for {
          _ <-
            if (hasFormChange) {
              for {
                either <- FormEndpointClient
                  .saveForm(taskParams.params)
                  .onErrorHandleWith { err =>
                    showAccountUnauthorizedModalVar.set(true)
                    ZIO.attempt(Left[GeneralServiceException, SaveFormResponse](GeneralServiceException(err.getMessage)))
                  }
              } yield {
                either.fold(
                  error => Toast.error(s"Unable to save form. Error: ${error.getMessage}"),
                  _.result.fold(
                    error => {
                      Toast.error(s"Unable to save form. Error: $error")
                      editingSessionErrorVar.set(Some(EditingSessionError.SessionTimeout))
                    },
                    resp => {
                      lastSaveFormParamsVar.set(Some(taskParams.params))
                      draftVersionOptVar.set(Some(resp.draftVersion))
                      saveFormResponseVar.set(Some(resp))
                      viewingVersionOptVar.set(None)
                      latestFormCheckUpToDateDataVar.set(Some(resp.checkData))
                    }
                  )
                )
                taskParams.onDone(either)
              }
            } else {
              ZIO.attempt {
                scribe.info("No change from last save, auto save skipped")
                taskParams.onSkipped()
              }
            }
        } yield ()
      }
    }
  }

  private def onRenewLock(): EventStream[Unit] = {
    formModelOptVar.now().fold[EventStream[Unit]](EventStream.empty) { model =>
      AirStreamUtils.taskToStreamDEPRECATED {
        ZIOUtils.when(editModeVar.now() != BuilderMode.ReadOnly && isEditingSectionValid) {
          for {
            tabId <- ZIO.attempt(GaiaSessionStorageUtils.getCurrentSessionId(AssetSessionType.FormBuilder))
            either <- FormEndpointClient
              .renewFormLock(RenewFormLockParams(model.formId, tabId))
              .onErrorHandleWith { err =>
                showAccountUnauthorizedModalVar.set(true)
                ZIO.attempt(Left[GeneralServiceException, RenewFormLockResponse](GeneralServiceException(err.getMessage)))
              }
          } yield {
            either.fold(
              error => scribe.error(s"Unable to renew form lock, error = $error"),
              _.lockResult match {
                case RenewLockResult.Granted(expiredAt) => editingSectionExpiredAtVar.set(Some(expiredAt))
                case RenewLockResult.AssetLocked(userId, _, userInfo) =>
                  editingSessionErrorVar.set(Some(FormLockedByOtherUser(userId, userInfo)))
                case RenewLockResult.NoLockFound => editingSessionErrorVar.set(Some(SessionTimeout))
              }
            )
          }
        }
      }
    }
  }

  // Should check this before all API to make sure we don't update data when session is expired
  private def isEditingSectionValid = {
    editingSessionErrorVar.now().isEmpty
  }

  private def finishEditingSession(): Unit = {
    editModeVar.set(BuilderMode.ReadOnly)
    editingSessionErrorVar.set(None)
    editingSectionExpiredAtVar.set(None)
  }

  private def onImportForm(
    formData: FormData,
    formMetadataOpt: Option[(FormVersionMetadataModel, FormVersionSystemMetadataModel)]
  ): Unit = {
    formBuilderDataCommandEventBus.emit {
      for {
        _ <- FormBuilderDataCommand.updateFormData(_ => formData)
        // Clear rule warnings
        _ <- FormBuilderDataCommand.updateRuleWarnings(_ => Seq.empty)
        // Try to keep existing widget sources
        _ <- FormBuilderDataCommand.updateWidgetSources { widgetSources =>
          val allKeys = formData.form.defaultSchema.formSchemaSeq.map(_._1).toSet
          widgetSources.view.filterKeys(allKeys.contains).toMap
        }
        _ <- FormBuilderDataCommand.updateFormMetadata { metadata =>
          formMetadataOpt.map(_._1.copy(id = metadata.id)).getOrElse(metadata)
        }
        _ <- FormBuilderDataCommand.updateFormSystemMetadata { systemMetadata =>
          formMetadataOpt.map(_._2.copy(id = systemMetadata.id)).getOrElse(systemMetadata)
        }
      } yield ()
    }
  }

  private def loadLibraryComponents = AirStreamUtils.taskToStreamDEPRECATED {
    FormEndpointClient
      .getAllLibraryComponents(())
      .map(_.foreach { resp =>
        libraryComponentsVar.set(Some(resp.pages.flatMap(_.components)))
      })
  }

  private def setTab(tab: FormTab, curPage: PantheonFormBuilderPage): Unit = {
    if (tab != curPage.tab) {
      val newPage = curPage.copy(tab = tab)
      PantheonRouter.Router.replaceStateToPage(_ => newPage)
    }
  }

  private def setVersion(versionIdOpt: Option[FormVersionId], curPage: PantheonFormBuilderPage): Unit = {
    if (versionIdOpt != curPage.commonParams.versionId) {
      val newPage = curPage.copy(commonParams = curPage.commonParams.copy(versionId = versionIdOpt))
      PantheonRouter.Router.replaceStateToPage(_ => newPage)
    }
  }

  private def editAssociatedLinks(
    formId: FormId,
    currentLinks: Map[String, AssociatedLink],
    updateParams: UpdateAssociatedLinkParams
  ) = {
    val removedLinks = updateParams.oldLinkName.fold(currentLinks)(currentLinks - _)
    val updatedLinks = updateParams.newLink.fold(removedLinks) { newLink =>
      removedLinks + (newLink.name -> AssociatedLink(
        ref = newLink.ref,
        createdBy = UserUtils.getCurrentUserId,
        createdAt = Some(Instant.now())
      ))
    }
    AirStreamUtils.taskToStreamDEPRECATED(
      FormEndpointClient
        .editFormAssociatedLinks(EditFormAssociatedLinksParams(formId, updatedLinks))
        .map(
          _.fold[Unit](
            error => Toast.error(s"Unable to edit associated links. Error: ${error.getMessage}"),
            resp => formModelOptVar.set(Some(resp.formModel))
          )
        )
    )
  }

  private def getDigitizationFolderId(formId: FormId) =
    AirStreamUtils.taskToStreamDEPRECATED(
      task = {
        for {
          response <- DigitizationEndpointClient
            .getResourceFileModel(
              GetResourceFileModelParams(formId)
            )
          _ <- response.fold(
            err =>
              ZIO.attempt {
                Toast.error(s"Unable to get form's parent folder id. Error: ${err.getMessage}")
              },
            res =>
              ZIO.attempt {
                digitizationFileOptVar.set(Some(res.file))
              }
          )
        } yield ()
      }
    )

  private def loadOntologyAsaMappings(formVersionId: FormVersionId) = {
    AirStreamUtils
      .taskToStreamDEPRECATED(
        FormAsaMappingEndpointClient
          .getFormVersionAsaMappings(
            GetFormVersionAsaMappingsParams(
              formId = formVersionId.parent,
              versionIdOpt = Some(formVersionId)
            )
          )
          .map(
            _.fold(
              err => Toast.error(s"Cannot get ASA mappings, error: ${err.getMessage}"),
              resp => versionAsaMappingVar.set(resp.mappings)
            )
          )
      )
  }

  private def loadOntologyStatus(formId: FormId) = {
    AirStreamUtils.taskToStreamDEPRECATED(
      for {
        respEither <- FormAsaMappingEndpointClient.getFormOntologyData(
          GetFormOntologyDataParams(
            formId = formId,
            statusOnly = true
          )
        )
      } yield respEither.fold(
        err => Toast.error(s"Unable to get ontology status, error: ${err.getMessage}"),
        resp => ontologyStateVar.update(_.copy(initializedStatusOpt = Some(resp.status)))
      )
    )
  }

  private def getCueDefaultValuesObj(
    formId: FormId,
    versionIdOpt: Option[FormVersionId],
    systemMetadata: FormVersionSystemMetadataModel
  ): Task[DataLayerDefaultValue] = {
    FormEndpointClient
      .extractDataLayerDefaultValues(
        ExtractDataLayerDefaultValuesParams(
          systemMetadata.cueMappingId match {
            case Some(cueMappingId) => Left(cueMappingId)
            case None               => Right((formId, versionIdOpt))
          }
        )
      )
      .flatMap(ZIO.fromEither)
      .map(_.defaultValue)
  }

}

object PantheonFormBuilderComponent {

  final case class SaveFormTaskParams(
    params: SaveFormParams,
    onDone: Either[GeneralServiceException, SaveFormResponse] => Unit = _ => (),
    onSkipped: () => Unit = () => (),
    afterRefresh: () => Unit = () => ()
  )

  sealed trait EditingSessionError derives CanEqual

  object EditingSessionError {
    case object SessionTimeout extends EditingSessionError
    case object FormOutDated extends EditingSessionError
    final case class FormLockedByOtherUser(userId: UserId, userInfo: UserInfo) extends EditingSessionError

    def fromEditFormDataError(error: EditFormDataError): EditingSessionError = {
      error match {
        case EditFormDataError.NoLockFound => EditingSessionError.SessionTimeout
      }
    }

  }

}
