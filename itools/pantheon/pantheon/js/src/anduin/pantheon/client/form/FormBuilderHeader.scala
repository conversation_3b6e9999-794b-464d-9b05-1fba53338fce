// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.pantheon.client.form

import java.time.Instant
import scala.scalajs.js
import scala.util.{Failure, Success, Try}

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.menu.laminar.{MenuItemL, MenuL}
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterL, ModalFooterWCancelL, ModalL}
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.portal.PortalUtils.IsClosable
import design.anduin.components.portal.laminar.PortalWrapperL
import design.anduin.components.progress.laminar.{BarIndicatorL, BlockIndicatorL}
import design.anduin.components.radio.RadioL
import design.anduin.components.tag.Tag
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import io.circe.syntax.*
import org.scalajs.dom
import org.scalajs.dom.{File, FilePropertyBag, MouseEvent}
import scalapb_circe.JsonFormat
import zio.ZIO

import anduin.annotation.AssetSessionIdUtils.AssetSessionType
import anduin.blueprint.BlueprintMetadata
import anduin.cue.model.CueModuleSharedModels.CueModuleVersion
import anduin.digitization.endpoint.CreateCueModuleFileParams
import anduin.digitization.model.DigitizationFileModels.DigitizationFile
import anduin.file.FileDownloaderUtils
import anduin.forms.Form.GaiaLogicVersion
import anduin.forms.asterisk.WithContentAsterisksL
import anduin.forms.builder.asamapping.ASAMappingPreviewModal
import anduin.forms.builder.checklist.{ChecklistSection, FormBuilderChecklistModal}
import anduin.forms.builder.navigation.SelectKeyEventData
import anduin.forms.builder.setting.BuilderSettingPanel.{EditingRule, SettingTab}
import anduin.forms.builder.signaldata.FormBuilderDataCommand
import anduin.forms.builder.states.FormDataResource.{StandardAliasMappingResource, UIPropertyResource}
import anduin.forms.builder.states.{BuilderMode, FormBuilderSelection}
import anduin.forms.builder.toc.BuilderTOCHeader.HeaderMode
import anduin.forms.builder.utils.WidgetTransformationUtils
import anduin.forms.builder.version.EditVersionModal
import anduin.forms.client.{FormEndpointClient, FormIntegrationEndpointClient}
import anduin.forms.endpoint.*
import anduin.forms.endpoint.integration.GetFormVersionIntegrationConfigParams
import anduin.forms.logic.extractor.ExtractorResult
import anduin.forms.model.FormModel
import anduin.forms.model.annotation.AnnotationDocumentModels.AnnotationDocumentData
import anduin.forms.rules.FormRule
import anduin.forms.tooling.ResizableModal
import anduin.forms.tooling.ResizableModal.{ResizableModalPosition, ResizableModalSizeRestrain}
import anduin.forms.ui.UIKey
import anduin.forms.ui.types.FileId
import anduin.forms.utils.components.CueComponents.ViewCueInput
import anduin.forms.utils.components.{CueComponents, ImportExportResourceMessage}
import anduin.forms.utils.{FormDataConverters, FormDataUtils, GaiaSessionStorageUtils}
import anduin.forms.version.*
import anduin.forms.version.FormVersionMetadataModel.PdfFieldInfo
import anduin.forms.version.FormVersionModel.FormType
import anduin.forms.{ExtractedPdf, Form, FormData}
import anduin.frontend.AirStreamUtils
import anduin.frontend.AirStreamUtils.Task.collectTask
import anduin.gaia.engine.utils.GaiaRuntimeLog
import anduin.id.cue.{CueModuleId, CueModuleVersionId}
import anduin.id.digitization.DigitizationFolderId
import anduin.id.form.{FormId, FormVersionId}
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.model.id.FolderId
import anduin.pantheon.client.common.RegionDisplay
import anduin.pantheon.client.digitization.components.modal.CreateEditDigitizationResourceModal
import anduin.pantheon.client.digitization.components.modal.CreateEditDigitizationResourceModal.DuplicateModeResource.DuplicateFormResource
import anduin.pantheon.client.digitization.resources.form.CreateEditFormModal
import anduin.pantheon.client.form.PantheonFormBuilderComponent.{EditingSessionError, SaveFormTaskParams}
import anduin.pantheon.client.form.modal.*
import anduin.pantheon.client.form.modal.integration.FormIntegrationModal
import anduin.sa.modal.ExportSaMappingModal
import anduin.portal.admin.PortalUserClient
import anduin.scalajs.pluralize.Pluralize
import anduin.tapir.endpoint.CommonParams
import com.anduin.stargazer.client.localstorage.LocalStorageKey
import com.anduin.stargazer.client.services.file.FileJsClient
import com.anduin.stargazer.endpoints.{UploadFileResponse, UploadSystemFileParams}
import com.anduin.stargazer.service.FileServiceEndpoints.GetDownloadUrlParams
import com.anduin.stargazer.service.file.BatchDownloadRequest

final case class FormBuilderHeader(
  activeVersionIdOptSignal: Signal[Option[FormVersionId]],
  formModelOptSignal: Signal[Option[FormModel]],
  digitizationFileOptSignal: Signal[Option[DigitizationFile]],
  formSignal: Signal[Form],
  formDataSignal: Signal[FormData],
  formMetadataSignal: Signal[FormVersionMetadataModel],
  formSystemMetadataSignal: Signal[FormVersionSystemMetadataModel],
  formTypeSignal: Signal[FormType],
  ignoredInvalidRequireSetupsSignal: Signal[Set[String]],
  ignoredInvalidPdfNonInputMappingFieldsSignal: Signal[Set[String]],
  ignoredUnmappedPdfNonInputFieldsSignal: Signal[Set[PdfFieldInfo]],
  formStandardAliasMappingSignal: Signal[Map[String, String]],
  formRuleExtractResultSignal: Signal[List[(Int, FormRule, Either[String, ExtractorResult])]],
  hasReferenceVersionSignal: Signal[Boolean],
  rejectedAsaSuggestionsSignal: Signal[Seq[AsaRejectedSuggestion]],
  rejectedGeneratedRulesSignal: Signal[Seq[GeneratedRule]],
  allStandardAliasesSignal: Signal[List[StandardAlias]],
  uploadedPdfSignal: Signal[Map[FileId, ExtractedPdf]],
  uploadedAnnotationDataSignal: Signal[Map[FileId, AnnotationDocumentData]],
  embeddedPdfSignal: Signal[Map[String, FileId]],
  selectedSignal: Signal[FormBuilderSelection],
  investorAccessAsaValuesSignal: Signal[Set[String]],
  formRuleWarningsSignal: Signal[Seq[FormRuleWarning]],
  draftVersionOptVar: Var[Option[FormVersionModel]],
  viewingVersionOptVar: Var[Option[FormVersionModel]],
  editModeSignal: Signal[BuilderMode],
  latestFormCheckUpToDateDataSignal: Signal[Option[FormCheckUpToDateData]],
  formFolderIdSignal: Signal[DigitizationFolderId],
  formBuilderDataCommandObserver: Observer[FormBuilderDataCommand],
  onSaveForm: Observer[SaveFormTaskParams],
  onSaveFormMapping: Observer[() => Unit],
  onImportForm: Observer[SaveFormTaskParams],
  onImportDone: Observer[Unit],
  onDoneImportingAsa: Observer[ImportFormStandardAliasMappingResponse],
  onVersionCreated: Observer[FormCheckUpToDateData],
  onStartedEditing: Observer[(Instant, BuilderMode.Editable)],
  onFailToStart: Observer[EditingSessionError],
  onFailToSave: Observer[EditFormDataError],
  onFinishedEditing: Observer[Unit],
  onSmartEnableCommenting: Observer[Unit],
  onSelectKey: Observer[String],
  onSelectKeyWithPDF: Observer[SelectKeyEventData],
  onSwitchToDocumentTab: Observer[Unit],
  settingPanelTabObserver: Observer[SettingTab],
  settingPanelEditingRuleObserver: Observer[Option[EditingRule]],
  sideViewerOpenVar: Observer[Unit],
  backObserver: Observer[Unit],
  gaiaRuntimeLogSignal: Signal[List[GaiaRuntimeLog]],
  onRuleChanged: Observer[FormRule] = Observer.empty,
  onValidateRules: Observer[Unit],
  cueModuleIdSignal: Signal[Option[CueModuleId]],
  cueModuleIdObserver: Observer[CueModuleId],
  cueDefaultValuesLoadingSignal: Signal[Boolean],
  cueModuleDataSyncObserver: Observer[Unit],
  blueprintMetadataSignal: Signal[BlueprintMetadata],
  onHeaderModeChange: Observer[HeaderMode],
  blueprintRefOptSignal: Signal[Option[BlueprintRef]],
  onChangeBpMetadataSearchText: Observer[String],
  ignoredMismatchedBlueprintMetadataFieldsSignal: Signal[Set[String]],
  ignoredNonImportedBlueprintMetadataFieldsSignal: Signal[Set[String]]
) {

  private val formIdOptSignal = formModelOptSignal.map(_.map(_.formId))

  private val showChecklistModal = Var(false)

  private val showASAMappingPreviewModal = Var(false)

  private val saveFormBackupEventBus = new EventBus[(FormModel, FormData)]

  private val handleExportCsaMappingReviewEventBus = new EventBus[FormVersionId]
  private val isExportingCsaMappingReviewVar = Var(false)

  private val handleExportFormEventBus =
    new EventBus[(FormData, FormVersionMetadataModel, FormVersionSystemMetadataModel)]

  private val handleOldExportFormEventBus = new EventBus[(FormData, FormVersionMetadataModel)]

  private val isExportingFileVar = Var(false)

  private val enableGenerateCommonRuleModalVar = Var(false)
  private val enableGenerateCommonRuleForStandardOfferingVar = Var(false)
  private val enableHighlightCommonRuleTermModalVar = Var(false)
  private val enableRuleTemplateIdMigrateModalVar = Var(false)

  private val templateIdMigrationModal = TemplateIdMigrationModal(
    formDataSignal = formDataSignal,
    enableRuleTemplateIdMigrateModalSignal = enableRuleTemplateIdMigrateModalVar.signal,
    enableGenerateCommonRuleModalSignal = enableGenerateCommonRuleModalVar.signal,
    enableRuleTemplateIdMigrateModalObserver = enableRuleTemplateIdMigrateModalVar.writer,
    formBuilderDataCommandObserver = formBuilderDataCommandObserver
  )()

  private val allUserEmailMapVar = Var[Map[UserId, EmailAddress]](Map.empty)

  private val expandedChecklistSectionsVar = Var(Set.empty[ChecklistSection])
  private val expandedChecklistModalVar = Var(false)

  private val selectedKeySignal = selectedSignal.map {
    case FormBuilderSelection.Empty                       => ""
    case FormBuilderSelection.Single(alias)               => alias
    case FormBuilderSelection.SideBySide(builderAlias, _) => builderAlias
  }.distinct

  private val exportDataStream = handleExportFormEventBus.events
    .withCurrentValueOf(
      editModeSignal,
      isExportingFileVar,
      formModelOptSignal,
      draftVersionOptVar,
      viewingVersionOptVar.signal,
      activeVersionIdOptSignal
    )
    .map {
      case (
            formData,
            formMetadata,
            formSystemMetadata,
            editMode,
            isExportingFile,
            formModelOpt,
            draftVersionOpt,
            viewingVersionOpt,
            _
          ) =>
        Option
          .unless(isExportingFile) {
            formModelOpt.map { formModel =>
              viewingVersionOpt.fold {
                Left(
                  (formModel, formData, formMetadata, formSystemMetadata, draftVersionOpt, viewingVersionOpt, editMode)
                )
              } { viewingVersion =>
                Right((formModel.formId, viewingVersion.formVersionId))
              }
            }
          }
          .flatten
    }
    .collectSome

  private val versionNameSignal = draftVersionOptVar.signal
    .combineWith(viewingVersionOptVar.signal)
    .map { case (draftVersionOpt, viewingVersionOpt) =>
      viewingVersionOpt
        .map { version =>
          if (version.versionNumber > 0) {
            s"${version.versionNumber}. ${version.name}"
          } else {
            version.name
          }
        }
        .getOrElse {
          draftVersionOpt.map(_.name).getOrElse("")
        }
    }

  private val forceExportDataEventBus = new EventBus[FormId]

  def apply(): Div = div(
    // -- Start subscription
    // Make sure that the signal is up-to-date
    formMetadataSignal --> Observer.empty,
    // -- End subscription
    windowEvents(_.onBeforeUnload).withCurrentValueOf(editModeSignal).map { case (event, editMode) =>
      editMode match {
        case BuilderMode.ReadOnly    => ()
        case _: BuilderMode.Editable => event.returnValue = ""
      }
    } --> Observer.empty,
    tw.bgGray0.flex.itemsCenter.flexNone.justifyBetween.px8,
    height := "53px",
    div(
      tw.flex.itemsCenter,
      child <-- formModelOptSignal.map(backButton),
      div(tw.ml8.mr16.wPx1.bgGray3.hPx20),
      child <-- formModelOptSignal
        .combineWith(draftVersionOptVar.signal, viewingVersionOptVar.signal)
        .map { case (formModelOpt, draftVersionOpt, viewingVersionOpt) =>
          renderTitle(
            formModelOpt,
            draftVersionOpt,
            viewingVersionOpt
          )
        },
      checklistModal,
      child.maybe <-- enableHighlightCommonRuleTermModalVar.signal.map(Option.when(_) {
        commonRuleTermHighlightModal
      }),
      child.maybe <-- showASAMappingPreviewModal.signal.map(Option.when(_) {
        asaMappingPreviewModal()
      }),
      templateIdMigrationModal
    ),
    child <-- editModeSignal.map { editMode =>
      // Do not use this signal for view mode actions
      // Use this to prevent saving backup right after switch to edit mode
      val isInitialBackupTriggerVar = Var(true)
      div(
        formModelOptSignal.map(_.isEmpty).cls(tw.hidden),
        tw.pr8.flex.itemsCenter,
        div(
          tw.pr24.borderRight.borderGray4,
          RegionDisplay()()
        ),
        div(
          tw.mr8.ml24,
          child <-- formDataSignal
            .combineWith(
              formModelOptSignal,
              formMetadataSignal,
              formSystemMetadataSignal,
              formFolderIdSignal,
              formTypeSignal,
              digitizationFileOptSignal,
              viewingVersionOptVar.signal.map(_.map(_.formVersionId)),
              isExportingCsaMappingReviewVar.signal
            )
            .map {
              case (
                    formData,
                    formModelOpt,
                    formMetadata,
                    formSystemMetadata,
                    formFolderId,
                    formType,
                    digitizationFileOpt,
                    viewingFormVersionIdOpt,
                    isExportingCsaMappingReview
                  ) =>
                actionPopover(
                  formData,
                  formMetadata,
                  formSystemMetadata,
                  formModelOpt,
                  formFolderId,
                  formType,
                  digitizationFileOpt,
                  editMode,
                  viewingFormVersionIdOpt,
                  isExportingCsaMappingReview
                )
            }
        ),
        generateRuleModal,
        editMode match {
          case BuilderMode.ReadOnly =>
            Seq(
              div(tw.mr8, asaMappingPreviewButton),
              checklistButton
            )
          case editable: BuilderMode.Editable =>
            children <-- formModelOptSignal.map { formModelOpt =>
              formModelOpt
                .map { formModel =>
                  Seq(
                    div(tw.mr8, asaMappingPreviewButton),
                    div(tw.mr8, checklistButton),
                    div(
                      tw.mr8,
                      formDataSignal --> Observer[FormData] { formData =>
                        if (isInitialBackupTriggerVar.now()) {
                          isInitialBackupTriggerVar.set(false)
                        } else {
                          saveFormBackupEventBus.emit((formModel, formData))
                        }
                      },
                      saveButton(
                        formDataSignal,
                        formModel,
                        editable
                      )
                    ),
                    createVersionButton(
                      formDataSignal,
                      formModel,
                      editable
                    )
                  )
                }
                .getOrElse(Seq.empty)
            }
        },
        // divider
        div(tw.wPx1.bgGray3.mx8.hPx24),
        ToggleEditingButton(
          editMode,
          formIdOptSignal = formIdOptSignal,
          latestFormCheckUpToDateDataSignal = latestFormCheckUpToDateDataSignal,
          formDataSignal,
          formMetadataSignal,
          formSystemMetadataSignal,
          draftVersionOptVar.signal,
          viewingVersionOptVar.signal,
          onStarted = onStartedEditing,
          onFailToStart = onFailToStart,
          onFinished = onFinishedEditing,
          onSaveForm = onSaveForm,
          onSaveFormMapping = onSaveFormMapping
        )()
      )
    },
    saveFormBackupEventBus.events
      .withCurrentValueOf(
        draftVersionOptVar.signal,
        formMetadataSignal,
        formSystemMetadataSignal,
        formTypeSignal
      )
      .flatMapSwitch { case (formModel, formData, versionOpt, metadata, systemMetadata, formType) =>
        FormClientStorageUtils.saveFormBackup(
          formModel = formModel,
          formData = formData,
          parentVersionIdOpt = versionOpt.flatMap(_.parentVersionId),
          metadataOpt = Some(metadata -> systemMetadata),
          formTypeOpt = Some(formType)
        )
      } --> Observer.empty,
    exportDataStream.collect { case Left(draftData) =>
      draftData
    } --> Observer[
      (
        FormModel,
        FormData,
        FormVersionMetadataModel,
        FormVersionSystemMetadataModel,
        Option[FormVersionModel],
        Option[FormVersionModel],
        BuilderMode
      )
    ] { draftData =>
      draftData._7 match {
        case BuilderMode.ReadOnly => {
          isExportingFileVar.set(true)
          forceExportDataEventBus.emit(draftData._1.formId)
        }
        // Don't save form on the edit ASA mapping mode
        case BuilderMode.EditAsaMapping =>
          onSaveFormMapping.onNext(() => {
            isExportingFileVar.set(true)
            forceExportDataEventBus.emit(draftData._1.formId)
          })
        case _ => {
          isExportingFileVar.set(true)
          val params = ClientFormDataUtils.constructSaveDraftParams(
            draftData._1.formId,
            GaiaSessionStorageUtils.getCurrentSessionId(AssetSessionType.FormBuilder),
            draftData._2,
            draftData._3,
            draftData._4,
            draftData._5,
            draftData._6
          )
          onSaveFormMapping.onNext(() => ())
          onSaveForm.onNext(
            SaveFormTaskParams(
              params = params,
              onDone = { resp =>
                resp.fold(
                  error => {
                    Toast.error(s"Cannot save draft, error = ${error.getMessage}")
                    isExportingFileVar.set(false)
                  },
                  _ => forceExportDataEventBus.emit(draftData._1.formId)
                )
                ()
              },
              onSkipped = () => forceExportDataEventBus.emit(draftData._1.formId)
            )
          )
        }
      }
    },
    EventStream
      .merge(
        forceExportDataEventBus.stream.map(_ -> None),
        exportDataStream.collect { case Right(formId -> formVersionId) =>
          formId -> Some(formVersionId)
        }
      ) --> ImportExportResourceMessage.messageEventObserver.contramap[(FormId, Option[FormVersionId])] {
      (formId, formVersionIdOpt) =>
        isExportingFileVar.set(true)
        ImportExportResourceMessage.ExportEvent.FormVersion(
          formId,
          formVersionIdOpt,
          ZIO.attempt(isExportingFileVar.set(false))
        )
    },
    handleExportCsaMappingReviewEventBus.events.flatMapSwitch(handleExportCsaMappingReview) --> Observer.empty,
    handleOldExportFormEventBus.events
      .withCurrentValueOf(
        isExportingFileVar.signal,
        formModelOptSignal,
        draftVersionOptVar,
        viewingVersionOptVar.signal,
        activeVersionIdOptSignal
      )
      .flatMapSwitch {
        case (
              formData,
              formMetadata,
              isExportingFile,
              formModelOpt,
              draftVersionOpt,
              viewingVersionOpt,
              activeVersionIdOpt
            ) =>
          if (isExportingFile) {
            EventStream.empty
          } else {
            handleExportForm(
              formData,
              formMetadata,
              formModelOpt,
              draftVersionOpt,
              viewingVersionOpt,
              activeVersionIdOpt
            )
          }
      } --> Observer.empty
  )

  private def backButton(formModelOpt: Option[FormModel]): Node = {
    TooltipL(
      renderTarget = ButtonL(
        style = ButtonL.Style.Minimal(icon = Some(Icon.Glyph.ArrowLeft)),
        onClick = backObserver.contramap(_ => ()),
        isDisabled = editModeSignal.map(_ != BuilderMode.ReadOnly || formModelOpt.isEmpty)
      )(),
      renderContent = _.amend("Please finish editing first"),
      isDisabled = editModeSignal.map(_ == BuilderMode.ReadOnly),
      position = PortalPosition.BottomLeft,
      targetWrapper = PortalWrapperL.BlockContent
    )()

  }

  private def renderTitle(
    formModelOpt: Option[FormModel],
    draftVersionOpt: Option[FormVersionModel],
    viewingVersionOpt: Option[FormVersionModel]
  ): Node = {
    formModelOpt.fold {
      div(tw.wPx128.textGray6, BarIndicatorL()())
    } { formModel =>
      div(
        div(tw.text15.fontBold, formModel.name),
        div(
          tw.flex.itemsCenter.leading16.hPx20,
          div(tw.textGray7.text11, child.text <-- versionNameSignal),
          child.maybe <-- formTypeSignal.map { formType =>
            Option.when(formType == FormType.Validation) {
              div(TagL(label = Val("OCR mode"), color = Val(Tag.Light.Warning))()).amend(tw.ml8)
            }
          },
          Option.when(viewingVersionOpt.isEmpty) {
            div(
              tw.ml8.flex.itemsCenter,
              TagL(label = Val("draft"))(),
              draftVersionOpt.map { draftVersion =>
                div(tw.ml4, editDraftButton(formModel, draftVersion))
              }
            )
          }
        )
      )
    }
  }

  private def editDraftButton(formModel: FormModel, draftVersion: FormVersionModel): Node = {
    ModalL(
      renderTitle = _ => "Edit draft version",
      renderTarget = open =>
        button(
          tw.p4.hover(tw.bgGray3).active(tw.bgGray4),
          onClick --> open.contramap[MouseEvent](_ => ()),
          IconL(name = Val(Icon.Glyph.Edit), size = Icon.Size.Custom(12))()
        ),
      renderContent = close =>
        EditVersionModal(
          formId = formModel.formId,
          formVersionIdOpt = None,
          version = draftVersion,
          onUpdate = Observer { case (name, note) =>
            draftVersionOptVar.update(_.map(_.copy(name = name, note = note)))
          },
          close = close
        )(),
      isClosable = Some(IsClosable(onEsc = true, onOutsideClick = false))
    )()
  }

  private def renderCueSelectorItem(close: Observer[Unit]): Node = {
    val linkEventBus = new EventBus[Unit]

    CueComponents.CueModuleSelector(
      formModelOptSignal.map(_.map(_.formId)),
      formSystemMetadataSignal.map(_.cueMappingId),
      formModelOptSignal
        .combineWith(formFolderIdSignal)
        .map { case (formModelOpt, formFolderId) =>
          formModelOpt match {
            case Some(formModel) =>
              ViewCueInput.CreateNew(
                CreateCueModuleFileParams(
                  parentId = formModel.formId,
                  name = s"Form Builder project: ${formModel.name}",
                  tags = Set.empty,
                  owners = Seq.empty,
                  parentFolderIdOpt = Some(formFolderId)
                )
              )
            case None =>
              ViewCueInput.Unknown
          }
        },
      editModeSignal.map(_ == BuilderMode.EditFull)
    ) {
      (
        isDisabledSignal,
        selectedCueModuleIdSignal,
        cueModuleVersionIdObserver,
        allCueModuleVersionsResultStream,
        loadObserver,
        createResultStream,
        createObserver
      ) =>
        ModalL(
          renderTitle = _ => "Link Cue Module",
          renderTarget = open =>
            MenuItemL(
              icon = Some(Icon.Glyph.Link),
              onClick = open.contramap(_ => ())
            )("Link cue module"),
          renderContent = close =>
            div(
              ModalBodyL(
                div(tw.mb8, "Please select a cue module version to link"),
                div(
                  tw.overflowYAuto.borderAll.borderGray3.rounded8.p8,
                  height.px := 512,
                  child <-- allCueModuleVersionsResultStream.splitMatchOne
                    .handleCase {
                      case success: Success[Seq[CueModuleVersion]] if success.value.nonEmpty =>
                        success.value
                    } { (_, allVersionsSignal) =>
                      div(
                        tw.flex.flexCol.gapY8,
                        children <-- allVersionsSignal.map(_.sortBy(_.versionIndex)).split(_.id) {
                          (_, _, versionSignal) =>
                            val selectEventBus = new EventBus[Unit]
                            div(
                              tw.flex.itemsCenter.gapX8.p8,
                              RadioL(
                                isChecked = versionSignal.combineWith(selectedCueModuleIdSignal).map {
                                  case (version, selectedIdOpt) => selectedIdOpt.contains(version.id)
                                },
                                onChange = selectEventBus.writer.contramap(_ => ()),
                                isDisabled = isDisabledSignal
                              )(),
                              div(
                                tw.flex.flexCol,
                                children <-- versionSignal
                                  .map(_.isDraft)
                                  .splitBoolean(
                                    whenTrue = _ =>
                                      Seq(
                                        TagL(label = Val("Draft"))(),
                                        span(tw.textGray7.text11, "When save form as new version, it is required to link to a version other than draft")
                                      ),
                                    whenFalse = _ =>
                                      Seq(
                                        div(
                                          child.text <-- versionSignal
                                            .map(version => s"${version.versionIndex + 1}. ${version.name}")
                                        )
                                      )
                                  ),
                                child <-- versionSignal
                                  .map { version =>
                                    val description = version.description
                                    Option.when(description.nonEmpty)(description)
                                  }
                                  .splitOption(
                                    project = (_, descriptionSignal) =>
                                      div(
                                        tw.text11,
                                        child.text <-- descriptionSignal
                                      ),
                                    ifEmpty = emptyNode
                                  ),
                                div(
                                  tw.textGray7.text11,
                                  child.text <-- versionSignal.map(version => s"Tag: ${version.zotVersionTag}")
                                )
                              ),
                              selectEventBus.stream.sample(versionSignal.map(_.id)) --> cueModuleVersionIdObserver
                            )
                        }
                      )
                    }
                    .handleCase {
                      case success: Success[Seq[CueModuleVersion]] if success.value.isEmpty => ()
                    } { (_, _) =>
                      div(
                        span(tw.textGray6.text13, "Module not found, please "),
                        ButtonL(
                          style = ButtonL.Style.Text(color = ButtonL.Color.Primary),
                          onClick = createObserver.contramap(_ => ()),
                          isDisabled = isDisabledSignal
                        )("create a new cue module")
                      )
                    }
                    .handleCase { case Success(_) => () } { (_, _) =>
                      div()
                    }
                    .handleCase { case Failure(_) => () } { (_, errSignal) =>
                      span(tw.textDanger3.text13, child.text <-- errSignal.map("Error: " + _))
                    }
                    .toStream
                    .toSignal(
                      BlockIndicatorL(title = Val(Some("Loading cue modules...")))()
                    )
                )
              ),
              ModalFooterWCancelL(
                cancel = close
              )(
                ButtonL(
                  style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
                  onClick = linkEventBus.writer.contramap(_ => ()),
                  isDisabled = isDisabledSignal
                )("Link")
              ),
              onMountCallback(_ => loadObserver.onNext(())),
              linkEventBus.stream
                .sample(selectedCueModuleIdSignal)
                .collectOpt(identity) --> Observer[CueModuleVersionId] { cueModuleVersionId =>
                formBuilderDataCommandObserver.onNext {
                  FormBuilderDataCommand.updateCueMappingId(_ => Some(cueModuleVersionId))
                }
              },
              createResultStream --> Observer[Try[CueModuleId]] {
                case Success(cueModuleId) =>
                  loadObserver.onNext(())
                  cueModuleIdObserver.onNext(cueModuleId)
                  dom.window.open(s"/pantheon/cue/${cueModuleId.idString}", "_blank")
                  ()
                case Failure(error) =>
                  Toast.error(s"Error: ${error.getMessage}")
              }
            ),
          afterUserClose = close,
          size = ModalL.Size(width = ModalL.Width.Px960)
        )()
    }
  }

  private def renderCueModuleViewer(close: Observer[Unit]) = {
    CueComponents.CueModuleView(
      cueModuleIdSignal
        .combineWith(
          formModelOptSignal,
          formFolderIdSignal
        )
        .map { case (cueModuleId, formModelOpt, formFolderId) =>
          (cueModuleId, formModelOpt) match {
            case (Some(cueModuleId), _) => ViewCueInput.Existing(cueModuleId)
            case (None, Some(formModel)) =>
              ViewCueInput.CreateNew(
                CreateCueModuleFileParams(
                  parentId = formModel.formId,
                  name = s"Form Builder project: ${formModel.name}",
                  tags = Set.empty,
                  owners = Seq.empty,
                  parentFolderIdOpt = Some(formFolderId)
                )
              )
            case (None, None) => ViewCueInput.Unknown
          }
        },
      editModeSignal.map(_ == BuilderMode.EditFull)
    ) { (isDisabledSignal, createResultStream, createObserver) =>
      div(
        child <-- isDisabledSignal.map { isDisabled =>
          MenuItemL(
            icon = None,
            onClick = createObserver.contramap(_ => ()),
            isDisabled = isDisabled
          )(
            div(child.text <-- cueModuleIdSignal.map {
              case Some(_) => s"View cue module"
              case None    => "Create cue module"
            })
          )
        },
        createResultStream --> Observer[Try[CueModuleId]] {
          case Success(cueModuleId) =>
            cueModuleIdObserver.onNext(cueModuleId)
            close.onNext(())
            dom.window.open(s"/pantheon/cue/${cueModuleId.idString}", "_blank")
            ()
          case Failure(error) =>
            Toast.error(s"Error: ${error.getMessage}")
        }
      )
    }
  }

  private def renderCueDefaultGenerator(formModelOpt: Option[FormModel], close: Observer[Unit]): Node = {
    val clickEventBus = new EventBus[Unit]
    val generateEventStream = clickEventBus.events
      .mapTo(formModelOpt)
      .collectTask { case Some(formModel) =>
        FormEndpointClient
          .generateFormModule(GenerateFormCueModuleParams(formModel.formId))
          .flatMap(ZIO.fromEither)
      }

    val isGeneratingSignal = generateEventStream
      .map {
        case Pending(_) => true
        case _          => false
      }
      .toSignal(false)

    val generateCompleteStream = generateEventStream.collect { case Resolved(_, result, _) =>
      result
    }

    div(
      child <-- isGeneratingSignal.combineWith(cueModuleIdSignal).map { case (isGenerating, cueModuleIdOpt) =>
        MenuItemL(
          onClick = clickEventBus.writer.contramap(_ => ()),
          isDisabled = cueModuleIdOpt.isEmpty || isGenerating
        )("Generate default cue content")
      },
      generateCompleteStream.mapToUnit --> close
    )
  }

  private def renderCueDataSyncItem(close: Observer[Unit]): Node = {
    div(
      child <-- cueDefaultValuesLoadingSignal.map { isLoading =>
        MenuItemL(
          onClick = cueModuleDataSyncObserver.contramap(_ => ()),
          isDisabled = isLoading
        )("Sync cue data")
      }
    )
  }

  private def actionPopover(
    formData: FormData,
    formMetadata: FormVersionMetadataModel,
    formSystemMetadata: FormVersionSystemMetadataModel,
    formModelOpt: Option[FormModel],
    formFolderId: DigitizationFolderId,
    formType: FormType,
    digitizationFileOpt: Option[DigitizationFile],
    editMode: BuilderMode,
    viewingFormVersionIdOpt: Option[FormVersionId],
    isExportingCsaMappingReview: Boolean
  ): Node = {
    PopoverL(
      position = PortalPosition.BottomRight,
      renderTarget = (open, isOpen) =>
        ButtonL(
          style = ButtonL.Style.Ghost(icon = Some(Icon.Glyph.EllipsisHorizontal), isSelected = isOpen),
          onClick = open.contramap(_ => ())
        )(),
      renderContent = close =>
        div(
          MenuL(
            Seq(
              recoverFromLocalItem(close, editMode == BuilderMode.EditFull),
              renderFormCommentItem(close, editMode.allowEditResource(UIPropertyResource(UIKey.enabledComment))),
              renderDuplicateItem(formModelOpt, formFolderId, digitizationFileOpt, close),
              importItem(close, editMode == BuilderMode.EditFull),
              importItemOld(close, editMode == BuilderMode.EditFull),
              exportItem(formData, formMetadata, formSystemMetadata),
              exportItemOld(formData, formMetadata),
              importAsaItem(editMode, close),
              exportAsaItem(close),
              exportMappingItem(
                formData,
                formModelOpt,
                close
              ),
              exportCsaMappingReviewItem(viewingFormVersionIdOpt, isExportingCsaMappingReview),
              renderAnalyticsItem(close),
              renderMigrateRuleItem(close, editMode == BuilderMode.EditFull),
              renderGenerateRuleItem(
                close,
                editMode == BuilderMode.EditFull,
                formData.form.gaiaLogicVersion,
                formData.form.rules.isEmpty,
                isStandardOffering = false
              ),
              renderGenerateRuleItem(
                close,
                editMode == BuilderMode.EditFull,
                formData.form.gaiaLogicVersion,
                formData.form.rules.isEmpty,
                isStandardOffering = true
              ),
              renderCustomScriptsItem(close, editMode == BuilderMode.EditFull),
              renderSyncAnnotationItem(close, editMode == BuilderMode.EditFull),
              renderIntegrationModalMenuItem(close),
              renderTurnOffRequireItem(formData.form, close, editMode == BuilderMode.EditFull),
              renderToggleFormTypeItem(formType, close, editMode == BuilderMode.EditFull),
              renderCueSelectorItem(close),
              renderCueModuleViewer(close),
              renderCueDefaultGenerator(formModelOpt, close),
              renderCueDataSyncItem(close)
            )
          )
        )
    )()
  }

  private val generateRuleModal = ModalL(
    size = ModalL.Size(ModalL.Width.Full, ModalL.Height.Full),
    layout = ModalL.LayoutMods(
      overlay = Seq(
        background := "transparent"
      ),
      container = Seq(
        boxShadow := "rgba(32, 57, 77, 0.12) 0px 2px 12px",
        width := "720px",
        marginRight.auto
      )
    ),
    afterUserClose = enableGenerateCommonRuleModalVar.writer.contramap(_ => false),
    renderTitle = _ => "Generate common rules",
    renderContent = close =>
      div(
        tw.hPc100,
        GenerateCommonRuleModal(
          formSignal = formSignal,
          isEditModeSignal = editModeSignal.map(_ == BuilderMode.EditFull),
          isStandardOfferingSignal = enableGenerateCommonRuleForStandardOfferingVar.signal,
          formRuleExtractResultsSignal = formRuleExtractResultSignal,
          rejectedGeneratedRulesSignal = rejectedGeneratedRulesSignal,
          selectedKeySignal = selectedKeySignal,
          onRulesConfirm = Observer { rules =>
            formBuilderDataCommandObserver.onNext {
              for {
                _ <- FormBuilderDataCommand.updateFormRules(_ ++ rules.toList)
                _ <- FormBuilderDataCommand.delay {
                  Toast.info(s"${Pluralize(
                      "rule",
                      rules.size,
                      true
                    )} created!")
                }
              } yield ()
            }
          },
          onRulesReject = Observer { generatedRules =>
            formBuilderDataCommandObserver.onNext {
              for {
                _ <- FormBuilderDataCommand.updateRejectedGeneratedRules(_ ++ generatedRules)
              } yield ()
            }
          },
          close = close,
          onSelectKey = onSelectKey
        )()
      ),
    isOpened = Some(enableGenerateCommonRuleModalVar.signal),
    isClosable = Some(IsClosable(onEsc = true, onOutsideClick = false))
  )()

  private def renderGenerateRuleItem(
    closeMenu: Observer[Unit],
    isEditMode: Boolean,
    gaiaLogicVersion: GaiaLogicVersion,
    isFormRuleEmpty: Boolean,
    isStandardOffering: Boolean
  ) = {
    val enable = (gaiaLogicVersion.supportVersion(GaiaLogicVersion.V2) || isFormRuleEmpty) && isEditMode
    val disableMessage = if (!isEditMode) {
      "Please turn on Edit mode"
    } else {
      "Only available for V2 logic and above"
    }

    val itemName = if (isStandardOffering) {
      "Generate common rules (Essentials)"
    } else {
      "Generate common rules"
    }

    TooltipL(
      renderTarget = MenuItemL(
        icon = Some(Icon.Glyph.FileGenerate),
        onClick = Observer { _ =>
          formBuilderDataCommandObserver.onNext(
            for {
              _ <- FormBuilderDataCommand.delay {
                enableGenerateCommonRuleModalVar.set(true)
                enableGenerateCommonRuleForStandardOfferingVar.set(isStandardOffering)
                closeMenu.onNext(())
              }
              _ <- FormBuilderDataCommand.injectLogicSupport()
            } yield ()
          )
        },
        isDisabled = !enable
      )(itemName),
      renderContent = _.amend(disableMessage),
      isDisabled = Val(enable)
    )()

  }

  private val commonRuleTermHighlightModal = {
    val defaultAsaPreviewDrawerSize = ResizableModal
      .getResizableModalSizeInStorage(LocalStorageKey.BuilderFullLeftModelSizeKey)
      .getOrElse(ResizableModal.getDefaultResizableModalSize)
      .width
    val asaPreviewDrawerSizeVar = Var[Double](defaultAsaPreviewDrawerSize)

    ResizableModal(
      storageKey = LocalStorageKey.BuilderFullLeftModelSizeKey,
      isExpandingSignal = Val(true),
      expandObserver = Observer.empty,
      debugToolSizeRestrain = ResizableModal.ResizableModalSizeRestrain.Default,
      debugToolPosition = ResizableModalPosition.FullLeft,
      showWithSizeDropdown = false,
      onResize = asaPreviewDrawerSizeVar.writer
    )(
      CommonRuleTermHighlightModal(
        formSignal = formSignal,
        selectedKeySignal = selectedKeySignal,
        formRuleExtractResultsSignal = formRuleExtractResultSignal,
        isEditModeSignal = editModeSignal.map(_ == BuilderMode.EditFull),
        asaPreviewDrawerSizeSignal = asaPreviewDrawerSizeVar.signal,
        enableHighlightCommonRuleTermModalSignal = enableHighlightCommonRuleTermModalVar.signal,
        onSelectKey = onSelectKey,
        close = enableHighlightCommonRuleTermModalVar.writer.contramap(_ => false),
        settingPanelTabObserver = settingPanelTabObserver,
        settingPanelEditingRuleObserver = settingPanelEditingRuleObserver,
        formBuilderDataCommandObserver = formBuilderDataCommandObserver
      )()
    )

  }

  private def renderCustomScriptsItem(closeMenu: Observer[Unit], isEditMode: Boolean) = {
    MenuItemL(
      icon = Some(Icon.Glyph.Task),
      hasSubItems = true,
      subItems = () =>
        Seq(
          MenuItemL(
            icon = Some(Icon.Glyph.CodeLine),
            onClick = Observer { _ =>
              enableHighlightCommonRuleTermModalVar.set(true)
              closeMenu.onNext(())
            }
          )("Common Rule Terms Highlight"),
          MenuItemL(
            icon = Some(Icon.Glyph.CodeLine),
            onClick = enableRuleTemplateIdMigrateModalVar.writer.contramap(_ => true),
            isDisabled = !isEditMode
          )("Generate rule's Template ID based on description")
        )
    )("Custom Scripts")
  }

  private def renderSyncAnnotationItem(closeMenu: Observer[Unit], isEditMode: Boolean) = {
    ModalL(
      renderTarget = open =>
        MenuItemL(
          icon = Some(Icon.Glyph.Refresh),
          onClick = open,
          isDisabled = !isEditMode
        )("Sync annotation data"),
      renderContent = close =>
        SyncAnnotationDataModal(
          uploadedPdfSignal = uploadedPdfSignal,
          formSystemMetadataSignal = formSystemMetadataSignal,
          formFolderIdSignal = formFolderIdSignal,
          onSync = formBuilderDataCommandObserver.contramap { data =>
            FormBuilderDataCommand.updateFormSystemMetadata(
              _.addAllAnnotationMapping(data)
            )
          },
          onClose = close
        )(),
      isClosable = None,
      afterUserClose = closeMenu,
      size = ModalL.Size(width = ModalL.Width.Px720, height = ModalL.Height.Content)
    )()
  }

  private def renderTurnOffRequireItem(form: Form, closeMenu: Observer[Unit], isEditMode: Boolean) = {
    val noRequired = form.defaultUiSchema.values.forall(_.uiOptions.get(UIKey.required).isEmpty)
    WithContentAsterisksL { renderAsterisksOptSignal =>
      div(
        child.maybe <-- renderAsterisksOptSignal.map {
          _.map { renderAsterisksProps =>
            ModalL(
              renderTitle = _ => "Toggle required fields to optional",
              renderTarget = open => {
                TooltipL(
                  renderTarget = {
                    MenuItemL(
                      icon = Some(Icon.Glyph.Asterisk),
                      onClick = open.contramap(_ => ()),
                      isDisabled = !isEditMode || noRequired
                    )("Toggle required fields to optional")
                  },
                  renderContent = _.amend("There is no required field"),
                  isDisabled = Val(!noRequired)
                )()
              },
              renderContent = close => {
                div(
                  ModalBodyL(
                    p(
                      "Form Builder will turn any required fields to optional and remove all ",
                      span(color := "#DB3737", "*")
                    ),
                    p("Are you sure you want to continue?")
                  ),
                  ModalFooterWCancelL(
                    cancel = Observer[Any] { _ =>
                      close.onNext(())
                      closeMenu.onNext(())
                    }
                  )(
                    ButtonL(
                      style = ButtonL.Style.Full(
                        color = ButtonL.Color.Primary
                      ),
                      isDisabled = Val(!isEditMode || noRequired),
                      onClick = Observer[Any] { _ =>
                        renderAsterisksProps.onRemoveAsterisks.onNext(())
                        close.onNext(())
                        closeMenu.onNext(())
                      }
                    )("Continue")
                  )
                )
              }
            )()
          }
        }
      )
    }
  }

  private def renderToggleFormTypeItem(formType: FormType, closeMenu: Observer[Unit], isEditMode: Boolean) = {
    WithContentAsterisksL { renderAsterisksOptSignal =>
      div(
        child.maybe <-- renderAsterisksOptSignal.map {
          _.map { renderAsterisksProps =>
            ModalL(
              renderTitle = _ => "Enable OCR mode",
              renderTarget = open => {
                MenuItemL(
                  icon = Some(Icon.Glyph.Draw),
                  onClick = Observer { _ =>
                    formType match {
                      case FormType.Validation =>
                        viewingVersionOptVar.update(_.map(_.withFormType(FormType.Default)))
                        draftVersionOptVar.update(_.map(_.withFormType(FormType.Default)))
                      case _ => open.onNext(())
                    }
                  },
                  isDisabled = !isEditMode
                )(
                  formType match {
                    case FormType.Validation => "Disable OCR mode"
                    case _                   => "Enable OCR mode"
                  }
                )
              },
              renderContent = close => {
                div(
                  ModalBodyL(
                    p(
                      "Enabling OCR mode will:",
                      ul(
                        tw.pl20,
                        li("Remove all logic"),
                        li("Add validations of many to many mappings and validation of form fields unmapped"),
                        li("Convert all signature, dropdown, countries, states fields to textboxes & Convert all radio to checkboxes"),
                        li("Turn all required fields to optional"),
                        li("Remove all file groups")
                      ),
                      "This action can't be undone. Do you want to proceed?"
                    )
                  ),
                  ModalFooterWCancelL(cancel = close)(
                    ButtonL(
                      style = ButtonL.Style.Full(
                        color = ButtonL.Color.Primary
                      ),
                      onClick = Observer { _ =>
                        close.onNext(())
                        formBuilderDataCommandObserver.onNext(
                          WidgetTransformationUtils.convertOcrMode(renderAsterisksProps.onRemoveAsterisks)
                        )
                        viewingVersionOptVar.update(_.map(_.withFormType(FormType.Validation)))
                        draftVersionOptVar.update(_.map(_.withFormType(FormType.Validation)))
                      }
                    )("Confirm")
                  )
                )
              },
              afterUserClose = closeMenu
            )()
          }
        }
      )
    }
  }

  private def renderIntegrationModalMenuItem(closeMenu: Observer[Unit]) = {
    ModalL(
      renderTitle = _ => "Form setup",
      renderTarget = open =>
        MenuItemL(
          icon = Some(Icon.Glyph.TableCog),
          onClick = open
        )("Form integration config"),
      renderContent = close =>
        FormIntegrationModal(
          formIdOptSignal = formIdOptSignal,
          activeFormVersionIdOptSignal = activeVersionIdOptSignal,
          formSignal = formSignal,
          onClose = close
        )(),
      isClosable = None,
      afterUserClose = closeMenu,
      size = ModalL.Size(width = ModalL.Width.Px720, height = ModalL.Height.Content)
    )()
  }

  private def renderMigrateRuleItem(closeMenu: Observer[Unit], isEditMode: Boolean) = {
    val canMigrateToV2Signal = formDataSignal.map(data => data.form.rules.nonEmpty)
    div(
      child <-- canMigrateToV2Signal.map { canMigrate =>
        ModalL(
          renderTitle = _ => "Migrate logic syntax from v1 to v2",
          renderTarget = open =>
            MenuItemL(
              icon = Some(Icon.Glyph.Refresh),
              onClick = open,
              isDisabled = !isEditMode || !canMigrate
            )("Migrate rules"),
          renderContent = close =>
            div(
              child <-- formDataSignal.map { formData =>
                LogicMigrationModal(
                  formData,
                  Observer { rules =>
                    close.onNext(())
                    formBuilderDataCommandObserver.onNext {
                      for {
                        _ <- FormBuilderDataCommand.setFormRules(rules.toList)
                        _ <- FormBuilderDataCommand.setLogicVersion(GaiaLogicVersion.V2)
                      } yield ()
                    }
                  },
                  close
                )()
              }
            ),
          isClosable = Some(IsClosable(onEsc = true, onOutsideClick = false)),
          afterUserClose = closeMenu,
          size = ModalL.Size(width = ModalL.Width.Px720)
        )()
      }
    )
  }

  def renderDuplicateItem(
    formModelOpt: Option[FormModel],
    formFolderId: DigitizationFolderId,
    digitizationFileOpt: Option[DigitizationFile],
    closeMenu: Observer[Unit]
  ): Node = {
    formModelOpt
      .map(formModel =>
        ModalL(
          renderTitle = _ => "Duplicate form",
          size = ModalL.Size(ModalL.Width.Px600),
          afterUserClose = closeMenu,
          renderTarget = open =>
            MenuItemL(
              icon = Some(Icon.Glyph.Duplicate),
              onClick = open
            )("Duplicate form"),
          renderContent = close =>
            div(
              getAllPortalUsers --> Observer.empty,
              CreateEditFormModal(
                mode = CreateEditDigitizationResourceModal.Mode.DuplicateFileMode(DuplicateFormResource(formModel.formId)),
                allOwnersMapSignal = allUserEmailMapVar.signal,
                close = close,
                parentFolderId = formFolderId,
                initialFileName = Some("Copy of " + formModel.name),
                fileOpt = digitizationFileOpt.map(dFile => {
                  DigitizationFile(
                    fileModel = dFile.fileModel.withTags(dFile.fileModel.tags + "Duplicated"),
                    resourceModelOpt = dFile.resourceModelOpt
                  )
                })
              )()
            ),
          isClosable = Some(IsClosable(onEsc = true, onOutsideClick = false))
        )()
      )
      .getOrElse(
        TooltipL(
          renderTarget = MenuItemL(
            icon = Some(Icon.Glyph.Duplicate),
            isDisabled = true
          )("Duplicate form"),
          renderContent = _.amend("Form data is being loaded"),
          isDisabled = Val(true)
        )()
      )
  }

  private def renderFormCommentItem(closeMenu: Observer[Unit], isEditMode: Boolean) = {
    ModalL(
      renderTitle = _ => "Smart enable commenting",
      size = ModalL.Size(ModalL.Width.Px480),
      afterUserClose = closeMenu,
      renderTarget = open =>
        editModeTooltip(
          isEditMode = isEditMode,
          icon = Icon.Glyph.Comment,
          name = "Smart enable commenting",
          onClick = open
        ),
      renderContent = close =>
        div(
          div(
            tw.text13.leading20.textGray8.spaceY8,
            tw.flex.flexCol,
            margin := "20px 28px 24px 28px",
            span(
              "Form Builder will choose appropriate widgets to turn on commenting feature. All the current commenting settings will be override."
            ),
            span(
              "Are you sure you want to continue?"
            )
          ),
          ModalFooterL(
            div(
              tw.flex.itemsCenter.spaceX8.justifyEnd,
              ButtonL(
                onClick = close.contramap(_ => ())
              )("Cancel"),
              ButtonL(
                style = ButtonL.Style.Full(
                  color = ButtonL.Color.Primary
                ),
                onClick = Observer[dom.MouseEvent] { _ =>
                  close.onNext(())
                  onSmartEnableCommenting.onNext(())
                  Toast.success("Commenting settings have been updated successfully!")
                }
              )("Continue")
            )
          )
        ),
      isClosable = Some(IsClosable(onEsc = true, onOutsideClick = false))
    )()
  }

  private def exportItem(
    formData: FormData,
    formMetadata: FormVersionMetadataModel,
    formSystemMetadata: FormVersionSystemMetadataModel
  ): Node = {
    MenuItemL(
      icon = Some(Icon.Glyph.FileExport),
      onClick = Observer { _ => handleExportFormEventBus.emit((formData, formMetadata, formSystemMetadata)) }
    )("Export form")
  }

  private def exportItemOld(
    formData: FormData,
    formMetadata: FormVersionMetadataModel
  ): Node = {
    MenuItemL(
      icon = Some(Icon.Glyph.FileExport),
      onClick = Observer { _ => handleOldExportFormEventBus.emit((formData, formMetadata)) }
    )("Export form (old)")
  }

  private def exportAsaItem(
    closePopover: Observer[Unit]
  ): Node = {
    ModalL(
      renderTitle = _ => "Export ASA mapping",
      size = ModalL.Size(ModalL.Width.Px720),
      afterUserClose = closePopover,
      renderTarget = open =>
        MenuItemL(
          icon = Some(Icon.Glyph.FileExport),
          onClick = open
        )("Export ASA mapping"),
      renderContent = close =>
        ExportSaMappingModal(
          formStandardAliasMappingSignal,
          close,
          customTitleOpt = Some("Form ASA Mapping")
        )(),
      isClosable = Some(IsClosable(onEsc = true, onOutsideClick = false))
    )()
  }

  private def exportCsaMappingReviewItem(
    formVersionIdOpt: Option[FormVersionId],
    isExportingCsaMappingReview: Boolean
  ): Node = {
    val tooltipContentOpt = if (formVersionIdOpt.isEmpty) {
      Some("Only available when viewing a specific form version")
    } else if (isExportingCsaMappingReview) {
      Some("Exporting CSA mapping for review, please wait...")
    } else {
      None
    }

    tooltipContentOpt.fold(
      MenuItemL(
        icon = Some(Icon.Glyph.FileExport),
        onClick = Observer { _ => formVersionIdOpt.fold(())(handleExportCsaMappingReviewEventBus.emit) }
      )("CSA mapping review")
    ) { tooltipContent =>
      TooltipL(
        renderContent = _.amend(tooltipContent, maxWidth.fitContent),
        position = PortalPosition.LeftCenter,
        renderTarget = MenuItemL(
          icon = Some(Icon.Glyph.FileExport),
          onClick = Observer.empty,
          isDisabled = true
        )("CSA mapping review")
      )()
    }
  }

  private def renderAnalyticsItem(
    closePopover: Observer[Unit]
  ): Node = {
    ModalL(
      renderTitle = _ => "Widget Source Analytics",
      size = ModalL.Size(ModalL.Width.Px720),
      afterUserClose = closePopover,
      renderTarget = open =>
        MenuItemL(
          icon = Some(Icon.Glyph.ChartBarVertical),
          onClick = open
        )("Analytics"),
      renderContent = close =>
        FormAnalyticsModal(
          formMetadataSignal,
          close
        )(),
      isClosable = Some(IsClosable(onEsc = true, onOutsideClick = false))
    )()
  }

  private def importAsaItem(editMode: BuilderMode, closePopover: Observer[Unit]): Node = {
    val activeVersionIdOptSignal = viewingVersionOptVar.signal.combineWith(draftVersionOptVar.signal).map {
      case (viewingVersionOpt, draftVersionOpt) =>
        viewingVersionOpt
          .map(_.formVersionId)
          .fold(
            draftVersionOpt.flatMap(_.parentVersionId)
          )(Option(_))
    }

    div(
      child <-- activeVersionIdOptSignal
        .combineWith(formSignal, formModelOptSignal)
        .map { case (activeVersionIdOpt, form, formModelOpt) =>
          val formName = formModelOpt.map(_.name).getOrElse("")
          ModalL(
            renderTitle = _ => "Import ASA mapping",
            size = ModalL.Size(ModalL.Width.Px720),
            afterUserClose = closePopover,
            isClosable = Some(IsClosable(onEsc = true, onOutsideClick = false)),
            renderTarget = open =>
              MenuItemL(
                icon = Some(Icon.Glyph.FileExport),
                isDisabled = activeVersionIdOpt.isEmpty || !editMode.allowEditResource(StandardAliasMappingResource),
                onClick = open
              )("Import ASA mapping"),
            renderContent = close =>
              ImportAsaModal(
                form,
                formIdOptSignal,
                formName,
                activeVersionIdOptSignal,
                onDoneImportingAsa,
                close,
                formBuilderDataCommandObserver
              )()
          )()
        }
    )
  }

  private def exportMappingItem(formData: FormData, formModelOpt: Option[FormModel], closePopover: Observer[Unit])
    : Node = {
    ModalL(
      renderTitle = _ => "Export PDF mapping",
      size = ModalL.Size(ModalL.Width.Px720),
      afterUserClose = closePopover,
      renderTarget = open =>
        MenuItemL(
          icon = Some(Icon.Glyph.FilePdf),
          onClick = open
        )("Export PDF mapping"),
      renderContent = ExportPdfMappingModal(
        formData,
        formModelOpt,
        _
      )(),
      isClosable = Some(IsClosable(onEsc = true, onOutsideClick = false))
    )()
  }

  private def importItem(closePopover: Observer[Unit], isEditMode: Boolean): Node = {
    ModalL(
      renderTitle = _ => "Import form",
      size = ModalL.Size(ModalL.Width.Px720),
      afterUserClose = closePopover,
      isClosable = Some(IsClosable(onEsc = true, onOutsideClick = false)),
      renderTarget = open =>
        editModeTooltip(
          isEditMode,
          Icon.Glyph.FileExport,
          "Import form",
          open
        ),
      renderContent = close =>
        ImportFormView(
          formIdOptSignal,
          activeVersionIdOptSignal,
          onImportDone,
          onImportForm,
          close
        )()
    )()
  }

  private def importItemOld(closePopover: Observer[Unit], isEditMode: Boolean): Node = {
    ModalL(
      renderTitle = _ => "Import form",
      size = ModalL.Size(ModalL.Width.Px720),
      afterUserClose = closePopover,
      isClosable = Some(IsClosable(onEsc = true, onOutsideClick = false)),
      renderTarget = open =>
        editModeTooltip(
          isEditMode,
          Icon.Glyph.FileExport,
          "Import form (old)",
          open
        ),
      renderContent = close =>
        ImportFormView(
          formIdOptSignal,
          activeVersionIdOptSignal,
          onImportDone,
          onImportForm,
          close,
          isOldImport = true
        )()
    )()
  }

  private def recoverFromLocalItem(closePopover: Observer[Unit], isEditMode: Boolean): Node = {
    ModalL(
      renderTitle = _ => "Recover form local",
      size = ModalL.Size(ModalL.Width.Px960),
      afterUserClose = closePopover,
      isClosable = Some(IsClosable(onEsc = true, onOutsideClick = false)),
      renderTarget = open =>
        editModeTooltip(
          isEditMode,
          Icon.Glyph.Database,
          "Recover from local",
          open
        ),
      renderContent = close =>
        RecoverFormFromLocalView(
          formIdOptSignal,
          onImportForm,
          close
        )()
    )()
  }

  private def editModeTooltip(
    isEditMode: Boolean,
    icon: Icon.Name,
    name: String,
    onClick: Observer[Unit]
  ): Node = {
    TooltipL(
      renderTarget = MenuItemL(
        icon = Some(icon),
        onClick = onClick,
        isDisabled = !isEditMode
      )(name),
      renderContent = _.amend("Please turn on edit mode"),
      isDisabled = Val(isEditMode)
    )()
  }

  private def checklistButton: HtmlElement = {
    ButtonL(
      style = ButtonL.Style.Ghost(),
      onClick = Observer[MouseEvent] { _ =>
        showChecklistModal.set(true)
        showASAMappingPreviewModal.set(false)
        onValidateRules.onNext(())
      }
    )("Validation")
  }

  private def checklistModal: Node = {
    ModalL(
      size = ModalL.Size(ModalL.Width.Full, ModalL.Height.Full),
      layout = ModalL.LayoutMods(
        overlay = Seq(
          background := "transparent",
          width := "fit-content",
          boxShadow := "rgba(32, 57, 77, 0.12) 0px 2px 12px"
        ),
        container = Seq(
          width.auto,
          marginRight.auto
        )
      ),
      isOpened = Some(showChecklistModal.signal),
      afterUserClose = Observer(_ => showChecklistModal.set(false)),
      renderContent = close =>
        FormBuilderChecklistModal(
          formIdOptSignal = formIdOptSignal,
          ignoredInvalidRequireSetupsSignal = ignoredInvalidRequireSetupsSignal,
          ignoredInvalidPdfNonInputMappingFieldsSignal = ignoredInvalidPdfNonInputMappingFieldsSignal,
          ignoredUnmappedPdfNonInputFieldsSignal = ignoredUnmappedPdfNonInputFieldsSignal,
          formRuleExtractResultSignal = formRuleExtractResultSignal,
          formSignal = formSignal,
          formTypeSignal = formTypeSignal,
          formStandardAliasMappingSignal = formStandardAliasMappingSignal,
          extractedPdfsSignal = uploadedPdfSignal,
          uploadedAnnotationDataSignal = uploadedAnnotationDataSignal,
          embeddedPdfsSignal = embeddedPdfSignal.map(_.keys.toSeq),
          isEditModeSignal = editModeSignal.map(_ == BuilderMode.EditFull),
          formRuleWarningsSignal = formRuleWarningsSignal,
          selectedKeySignal = selectedKeySignal,
          close = close,
          onRuleChanged = Observer[(FormRule, Int, String)] { case (formRule, index, oldName) =>
            formBuilderDataCommandObserver.onNext {
              for {
                _ <- FormBuilderDataCommand.updateFormRules { oldRules =>
                  oldRules.patch(
                    index,
                    Seq(formRule),
                    1
                  )
                }
                _ <- FormBuilderDataCommand.updateRuleWarnings { ruleWarnings =>
                  ruleWarnings.map(warning =>
                    if (warning.ruleName == oldName) warning.copy(ruleName = formRule.name) else warning
                  )
                }
              } yield ()
            }

            // Check initial error
            onRuleChanged.onNext(formRule)
          },
          formBuilderDataCommandObserver = formBuilderDataCommandObserver,
          onSelectKey = onSelectKey,
          onSelectKeyWithPDF = onSelectKeyWithPDF,
          onSwitchToDocumentTab = onSwitchToDocumentTab,
          sideViewerOpenObserver = sideViewerOpenVar,
          expandedSectionsSignal = expandedChecklistSectionsVar.signal,
          toggleChecklistSectionObserver = Observer[ChecklistSection] { section =>
            expandedChecklistSectionsVar.update { sections =>
              if (sections.contains(section)) {
                sections - section
              } else {
                sections + section
              }
            }
          },
          expandedModalSignal = expandedChecklistModalVar.signal,
          expandedModalObserver = expandedChecklistModalVar.writer,
          gaiaRuntimeLogSignal = gaiaRuntimeLogSignal,
          blueprintMetadataSignal = blueprintMetadataSignal,
          onHeaderModeChange = onHeaderModeChange,
          blueprintRefOptSignal = blueprintRefOptSignal,
          onChangeBpMetadataSearchText = onChangeBpMetadataSearchText,
          ignoredMismatchedBlueprintMetadataFieldsSignal = ignoredMismatchedBlueprintMetadataFieldsSignal,
          ignoredNonImportedBlueprintMetadataFieldsSignal = ignoredNonImportedBlueprintMetadataFieldsSignal
        )(),
      isClosable = Some(IsClosable(onEsc = true, onOutsideClick = false))
    )()
  }

  private def asaMappingPreviewButton: Node = {
    ButtonL(
      style = ButtonL.Style.Ghost(),
      onClick = Observer[MouseEvent] { _ =>
        showASAMappingPreviewModal.set(true)
        showChecklistModal.set(false)
      }
    )("ASA Mapping Preview")
  }

  private def asaMappingPreviewModal(): Node = {
    val defaultAsaPreviewDrawerSize = ResizableModal
      .getResizableModalSizeInStorage(LocalStorageKey.BuilderFullLeftModelSizeKey)
      .getOrElse(ResizableModal.getDefaultResizableModalSize)
      .width
    val asaPreviewDrawerSizeVar = Var[Double](defaultAsaPreviewDrawerSize)

    ResizableModal(
      storageKey = LocalStorageKey.BuilderFullLeftModelSizeKey,
      isExpandingSignal = Val(true),
      expandObserver = Observer.empty,
      debugToolSizeRestrain = ResizableModalSizeRestrain(
        min = Some(480.0),
        max = Some(1200.0)
      ),
      debugToolPosition = ResizableModalPosition.FullLeft,
      showWithSizeDropdown = false,
      onResize = asaPreviewDrawerSizeVar.writer
    )(
      ASAMappingPreviewModal(
        formSignal = formSignal,
        formStandardAliasMappingSignal = formStandardAliasMappingSignal,
        rejectedAsaSuggestionsSignal = rejectedAsaSuggestionsSignal,
        hasReferenceVersionSignal = hasReferenceVersionSignal,
        editModeSignal = editModeSignal,
        allStandardAliasesSignal = allStandardAliasesSignal,
        asaPreviewDrawerSizeSignal = asaPreviewDrawerSizeVar.signal,
        investorAccessAsaValuesSignal = investorAccessAsaValuesSignal,
        close = Observer(_ => showASAMappingPreviewModal.set(false)),
        onSelectKey = onSelectKey,
        formBuilderDataCommandObserver = formBuilderDataCommandObserver
      )()
    )
  }

  private def saveButton(formDataSignal: Signal[FormData], formModel: FormModel, editMode: BuilderMode.Editable): Node = {
    SaveFormButton(
      editMode,
      formModel.formId,
      formDataSignal,
      formMetadataSignal,
      formSystemMetadataSignal,
      draftVersionOptVar.signal,
      viewingVersionOptVar.signal,
      onSaveForm,
      onSaveFormMapping
    )()
  }

  private def createVersionButton(
    formDataSignal: Signal[FormData],
    formModel: FormModel,
    editable: BuilderMode.Editable
  ) = {
    if (editable == BuilderMode.EditAsaMapping) {
      emptyNode
    } else {
      CreateFormVersionModal(
        formModel.formId,
        editable,
        formDataSignal,
        formMetadataSignal,
        formSystemMetadataSignal,
        formTypeSignal,
        viewingVersionOptVar,
        draftVersionOptVar,
        onVersionCreated,
        onFailToSave,
        onSaveFormMapping.contramap(_ => () => ())
      )()
    }
  }

  private def getAllPortalUsers: EventStream[Unit] = {
    AirStreamUtils.taskToStreamDEPRECATED(
      PortalUserClient.getAllPortalUsers(CommonParams.Empty()).map { resp =>
        allUserEmailMapVar.set(
          resp
            .map(_.allPortalUsers)
            .getOrElse(Seq.empty)
            .map { user =>
              user.portalUser.userId ->
                EmailAddress(name = Some(user.userInfo.fullNameString), address = user.userInfo.emailAddressStr)
            }
            .toMap
        )
      }
    )
  }

  private def handleExportCsaMappingReview(formVersionId: FormVersionId) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        _ <- ZIO.attempt(isExportingCsaMappingReviewVar.set(true))
        _ <- FormEndpointClient
          .getNamingRefineTemplate(
            GetNamingRefineTemplateParams(
              formId = formVersionId.parent,
              formVersionIdOpt = Some(formVersionId),
              includeCsaMappingInfo = true
            )
          )
          .map { resp =>
            isExportingCsaMappingReviewVar.set(false)
            resp.fold(
              ex => Toast.error(s"Failed to export CSA mapping for reviewing. Error: ${ex.getMessage}"),
              res => FileDownloaderUtils.download(BatchDownloadRequest(fileIds = Seq(res.fileId)))
            )
          }
      } yield ()
    }
  }

  private def handleExportForm(
    formData: FormData,
    formMetadata: FormVersionMetadataModel,
    fOpt: Option[FormModel],
    dOpt: Option[FormVersionModel],
    vOpt: Option[FormVersionModel],
    activeVersionIdOpt: Option[FormVersionId]
  ): EventStream[Unit] = {
    AirStreamUtils.taskToStreamDEPRECATED {
      lazy val configTaskOpt = activeVersionIdOpt.map { formVersionId =>
        FormIntegrationEndpointClient
          .getFormVersionIntegrationConfig(GetFormVersionIntegrationConfigParams(formVersionId))
          .flatMap(ZIO.fromEither)
          .map(_.config)
          .tapError { throwable =>
            ZIO.succeed(Toast.error(s"Can't load integration config, error: ${throwable.getMessage}"))
          }
      }

      fOpt.fold {
        zio.ZIO.attempt {}
      } { formModel =>
        isExportingFileVar.set(true)
        val sanitizedFormData = FormDataUtils.sanitizeFormFiles(formData)
        val sanitizedUploadedPdf = sanitizedFormData.copy(
          uploadedPdf = sanitizedFormData.uploadedPdf.map { case (fileId, extractedPdf) =>
            fileId -> extractedPdf.copy(originalFileIdOpt = None)
          }
        )
        val jsonFile = new File(
          js.Array(sanitizedUploadedPdf.asJson.toString),
          FormBuilderHeader.formDataFileName,
          js.Dynamic
            .literal(
              `type` = js.defined("application/json")
            )
            .asInstanceOf[FilePropertyBag] // scalafix:ok DisableSyntax.asInstanceOf
        )
        val metadataFile = new File(
          js.Array(JsonFormat.toJson(formMetadata).toString),
          FormBuilderHeader.formMetadataFileName,
          js.Dynamic
            .literal(
              `type` = js.defined("application/json")
            )
            .asInstanceOf[FilePropertyBag] // scalafix:ok DisableSyntax.asInstanceOf
        )
        val params = UploadSystemFileParams(FolderId.channelSystemFolderId(formModel.formId))
        val respTask = configTaskOpt.fold {
          FileJsClient.upload[UploadFileResponse](
            params,
            Seq(jsonFile, metadataFile).map(file => FileJsClient.JsFileUploadData(file, file.name))
          )
        } { configTask =>
          configTask.flatMap { config =>
            val configFile = new File(
              js.Array(JsonFormat.toJson(config).toString),
              FormBuilderHeader.formIntegrationConfigFileName,
              js.Dynamic
                .literal(
                  `type` = js.defined("application/json")
                )
                .asInstanceOf[FilePropertyBag] // scalafix:ok DisableSyntax.asInstanceOf
            )

            FileJsClient.upload[UploadFileResponse](
              params,
              Seq(jsonFile, metadataFile, configFile).map(file => FileJsClient.JsFileUploadData(file, file.name))
            )
          }
        }

        respTask.flatMap(resp =>
          ZIO.attempt {
            resp.files
              .find(_._2 == FormBuilderHeader.formDataFileName)
              .fold {
                Toast.error("Cannot create JSON file for download")
                isExportingFileVar.set(false)
              } { (jsonFileId, _) =>
                val metadataFileIdOpt = resp.files
                  .find(_._2 == FormBuilderHeader.formMetadataFileName)
                  .map(_._1)
                val integrationConfigFileIdOpt = resp.files
                  .find(_._2 == FormBuilderHeader.formIntegrationConfigFileName)
                  .map(_._1)
                val fileIds =
                  formData.uploadedPdf.keys.toSeq ++ formData.embeddedPdf.values ++ Seq(
                    Some(FileId(jsonFileId.idString)),
                    metadataFileIdOpt
                      .map(metadataFileId => FileId(metadataFileId.idString)),
                    integrationConfigFileIdOpt
                      .map(integrationConfigFileId => FileId(integrationConfigFileId.idString))
                  ).flatten

                val formNameSuffix = fOpt.map(formModel => s"(${formModel.name})").getOrElse("")
                val versionName = vOpt.fold {
                  "(Draft) - " + dOpt.map(_.name).getOrElse("")
                } { version =>
                  s"${version.versionNumber}. ${version.name}"
                }
                val zipName = s"$versionName $formNameSuffix".trim
                val downloadRequest = BatchDownloadRequest(
                  zipNameOnMultipleFiles = zipName,
                  fileIds = fileIds.flatMap(FormDataConverters.fileIdTypeToFileId)
                )
                val downloadTask = {
                  for {
                    result <- FileJsClient.getDownloadUrl(GetDownloadUrlParams(downloadRequest)).map(_.map(_.url))
                    _ <- zio.ZIO.attempt {
                      isExportingFileVar.set(false)
                    }
                  } yield result
                }
                FileDownloaderUtils.download(
                  downloadTask,
                  downloadRequest.numFiles,
                  downloadRequest.numFolders
                )
              }
          }
        )
      }
    }
  }

}

object FormBuilderHeader {
  private val formDataFileName = ImportFormView.formDataFileName
  private val formMetadataFileName = ImportFormView.formMetadataFileName
  private val formIntegrationConfigFileName = ImportFormView.formIntegrationConfigFileName
}
