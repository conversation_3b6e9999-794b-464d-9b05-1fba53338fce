// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.sa.sadatatemplate

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.menu.laminar.{MenuItemL, MenuL}
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.progress.CircleIndicator
import design.anduin.components.progress.laminar.CircleIndicatorL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.table.laminar.TableL
import io.circe.Codec
import org.scalajs.dom
import zio.ZIO

import anduin.cue.model.CueModuleSharedModels.CueModule
import anduin.forms.client.SaCueEndpointClient
import anduin.frontend.AirStreamUtils
import anduin.model.codec.ProtoCodecs.{generalMessageDecoder, generalMessageEncoder}
import anduin.portal.sa.common.StringColumn
import anduin.sa.client.SaEndpointClient
import anduin.sa.endpoints.{
  CreateSaDataTemplateCueModuleParams,
  GenerateSaDataTemplateCueModuleParams,
  GetAllSaDataTemplatesParams,
  GetSaDataTemplateCueModuleParams
}
import anduin.sa.model.sadatatemplate.sadatatemplatemessage.SaDataTemplateMessage

final case class SaDataTemplateDashboard() {
  private given Codec[SaDataTemplateMessage] = Codec.from(generalMessageDecoder, generalMessageEncoder)
  private val saDataTemplateListVar = Var(List.empty[SaDataTemplateMessage])
  private val saDataTemplateListSignal = saDataTemplateListVar.signal
  private val isLoadingVar = Var(true)
  private val isLoadingSignal = isLoadingVar.signal.distinct

  private val actionColumn = TableL.Column[SaDataTemplateMessage](
    title = "",
    field = "",
    renderCell = renderProps => {
      val template = renderProps.data
      div(
        onClick.preventDefault.stopPropagation --> Observer.empty,
        PopoverL(
          position = PortalPosition.BottomRight,
          renderTarget = (open, isOpen) =>
            ButtonL(
              style = ButtonL.Style.Minimal(isSelected = isOpen, icon = Some(Icon.Glyph.EllipsisVertical)),
              onClick = open.contramap(_ => ())
            )().amend(tw.m4),
          renderContent = close => {
            val cueModuleOptVar = Var(Option.empty[CueModule])
            val cueModuleOptSignal = cueModuleOptVar.signal
            val isFetchingCueModuleVar = Var(true)
            val isFetchingCueModuleSignal = isFetchingCueModuleVar.signal.distinct
            div(
              MenuL(
                Seq(
                  createOrViewCueModuleMenuItem(
                    template,
                    cueModuleOptSignal,
                    isFetchingCueModuleSignal,
                    cueModuleOptVar.writer,
                    close
                  ),
                  generateCueModuleMenuItem(
                    template,
                    cueModuleOptSignal,
                    isFetchingCueModuleSignal
                  )
                )
              ),
              fetchCueModule(
                renderProps.data,
                cueModuleOptVar.writer,
                isFetchingCueModuleVar.writer
              ) --> Observer.empty
            )
          }
        )()
      )
    },
    width = Some(60)
  )

  def apply(): HtmlElement = {
    div(
      tw.m16,
      fetchTemplateList --> Observer.empty,
      CreateSaDataTemplateModal(
        renderTarget = open =>
          ButtonL(
            style = ButtonL.Style.Full(
              color = ButtonL.Color.Primary,
              icon = Some(Icon.Glyph.Import)
            ),
            onClick = open.contramap(_ => ())
          )("Create new template").amend(tw.mb32),
        onDone = saDataTemplateListVar.updater { case (templateList, newTemplate) => newTemplate +: templateList }
      )(),
      // SA Profiles
      div(
        tw.mb16.text15.fontSemiBold,
        "SA Data Templates"
      ),
      TableL[SaDataTemplateMessage](
        options = TableL.Options(layout = TableL.Layout.FitColumns),
        loading = TableL.Loading(
          criteria = isLoadingSignal,
          render = () =>
            div(
              tw.flex.justifyCenter,
              CircleIndicatorL(size = CircleIndicator.Size.Px48)()
            )
        ),
        columns = List(
          StringColumn[SaDataTemplateMessage](
            colName = "Template ID",
            minWidth = 100,
            getFieldValue = _.id.idString
          )(),
          StringColumn[SaDataTemplateMessage](
            colName = "Template name",
            minWidth = 100,
            getFieldValue = _.name
          )(),
          StringColumn[SaDataTemplateMessage](
            colName = "Template description",
            minWidth = 150,
            getFieldValue = _.description
          )(),
          actionColumn
        ),
        dataSignal = saDataTemplateListSignal
      ).amend(maxHeight.vh := 70)
    )
  }

  private def createOrViewCueModuleMenuItem(
    template: SaDataTemplateMessage,
    cueModuleOptSignal: Signal[Option[CueModule]],
    isFetchingCueModuleSignal: Signal[Boolean],
    cueModuleOptObserver: Observer[Option[CueModule]],
    close: Observer[Unit]
  ) = {
    val isCreatingCueModuleVar = Var(false)
    val isCreatingCueModuleSignal = isCreatingCueModuleVar.signal.distinct
    val createEventBus = new EventBus[Unit]
    div(
      child <-- (isFetchingCueModuleSignal || isCreatingCueModuleSignal)
        .splitBoolean(
          whenTrue = _ =>
            MenuItemL(isDisabled = true)(
              div(
                tw.flex.itemsCenter.spaceX4,
                CircleIndicatorL()(),
                div("Create Cue module")
              )
            ),
          whenFalse = _ =>
            div(
              child <-- cueModuleOptSignal.splitOption(
                project = (_, cueModuleSignal) => {
                  val clickEventBus = new EventBus[Unit]
                  MenuItemL(onClick = clickEventBus.writer)("View Cue module")
                    .amend(clickEventBus.events.sample(cueModuleSignal) --> Observer[CueModule] { cueModule =>
                      dom.window.open(s"/pantheon/cue/${cueModule.id.idString}", "_blank")
                      close.onNext(())
                    })
                },
                ifEmpty = MenuItemL(onClick = createEventBus.writer)("Create Cue module")
              )
            )
        ),
      createEventBus.events.flatMapSwitch { _ =>
        createCueModule(template, cueModuleOptObserver, isCreatingCueModuleVar.writer)
      } --> Observer.empty
    )
  }

  private def generateCueModuleMenuItem(
    template: SaDataTemplateMessage,
    cueModuleOptSignal: Signal[Option[CueModule]],
    isFetchingCueModuleSignal: Signal[Boolean]
  ) = {
    val isGeneratingCueModuleVar = Var(false)
    val isGeneratingCueModuleSignal = isGeneratingCueModuleVar.signal.distinct
    val generateEventBus = new EventBus[Unit]
    div(
      child <-- (isFetchingCueModuleSignal || isGeneratingCueModuleSignal).splitBoolean(
        whenTrue = _ =>
          MenuItemL(isDisabled = true)(
            div(
              tw.flex.itemsCenter.spaceX4,
              CircleIndicatorL()(),
              div("Generate Cue module")
            )
          ),
        whenFalse = _ =>
          div(child <-- cueModuleOptSignal.map(_.nonEmpty).distinct.map { hasCueModule =>
            MenuItemL(onClick = generateEventBus.writer, isDisabled = !hasCueModule)("Generate Cue module")
          })
      ),
      generateEventBus.events.flatMapSwitch { _ =>
        generateCueModule(template, isGeneratingCueModuleVar.writer)
      } --> Observer.empty
    )
  }

  private def fetchTemplateList = {
    AirStreamUtils.taskToStream {
      val task = for {
        _ <- ZIO.attempt(isLoadingVar.set(true))
        respEither <- SaEndpointClient.getAllSaDataTemplates(GetAllSaDataTemplatesParams(ignoreDeleted = false))
        resp <- ZIO.fromEither(respEither)
        _ <- ZIO.attempt(Var.set(saDataTemplateListVar -> resp.templateList, isLoadingVar -> false))
      } yield ()
      task.catchAll { error =>
        ZIO.attempt {
          Toast.error(s"Failed to fetch template list. Error: ${error.getMessage}")
          isLoadingVar.set(false)
        }
      }
    }
  }

  private def fetchCueModule(
    template: SaDataTemplateMessage,
    cueModuleOptObserver: Observer[Option[CueModule]],
    isFetchingObserver: Observer[Boolean]
  ) = {
    AirStreamUtils.taskToStream {
      val task = for {
        _ <- ZIO.attempt(isFetchingObserver.onNext(true))
        respEither <- SaCueEndpointClient.getSaDataTemplateCueModule(
          GetSaDataTemplateCueModuleParams(template.id)
        )
        resp <- ZIO.fromEither(respEither)
        _ <- ZIO.attempt {
          cueModuleOptObserver.onNext(resp.cueModuleOpt)
          isFetchingObserver.onNext(false)
        }
      } yield ()
      task.catchAll { error =>
        ZIO.attempt {
          Toast.error(s"Failed to fetch cue module. Error: ${error.getMessage}")
          isFetchingObserver.onNext(false)
        }
      }
    }
  }

  private def createCueModule(
    template: SaDataTemplateMessage,
    cueModuleOptObserver: Observer[Option[CueModule]],
    isCreatingObserver: Observer[Boolean]
  ) = {
    AirStreamUtils.taskToStream {
      val task = for {
        _ <- ZIO.attempt(isCreatingObserver.onNext(true))
        respEither <- SaCueEndpointClient.createSaDataTemplateCueModule(
          CreateSaDataTemplateCueModuleParams(template.id)
        )
        resp <- ZIO.fromEither(respEither)
        _ <- ZIO.attempt {
          cueModuleOptObserver.onNext(Some(resp.cueModule))
          isCreatingObserver.onNext(false)
        }
      } yield ()
      task.catchAll { error =>
        ZIO.attempt {
          Toast.error(s"Failed to create cue module. Error: ${error.getMessage}")
          isCreatingObserver.onNext(false)
        }
      }
    }
  }

  private def generateCueModule(
    template: SaDataTemplateMessage,
    isGeneratingObserver: Observer[Boolean]
  ) = {
    AirStreamUtils.taskToStream {
      val task = for {
        _ <- ZIO.attempt(isGeneratingObserver.onNext(true))
        respEither <- SaCueEndpointClient.generateSaDataTemplateCueModule(
          GenerateSaDataTemplateCueModuleParams(template.id)
        )
        _ <- ZIO.fromEither(respEither)
        _ <- ZIO.attempt(isGeneratingObserver.onNext(false))
      } yield ()
      task.catchAll { error =>
        ZIO.attempt {
          Toast.error(s"Failed to generate cue module. Error: ${error.getMessage}")
          isGeneratingObserver.onNext(false)
        }
      }
    }
  }

}
