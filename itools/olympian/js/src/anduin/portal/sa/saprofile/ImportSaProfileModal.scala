// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.sa.saprofile

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import org.scalajs.dom
import org.scalajs.dom.File
import zio.ZIO

import anduin.component.laminar.FileUploader
import anduin.frontend.AirStreamUtils
import com.anduin.stargazer.client.services.file.FileJsClient
import com.anduin.stargazer.endpoints.{UploadFileResponse, UploadSystemFileParams}
import com.anduin.stargazer.service.FileServiceEndpoints.CreateUserFolderParams
import com.raquo.laminar.api.L.*
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL, ModalL}
import design.anduin.components.tooltip.laminar.TooltipL

import anduin.model.id.FileId
import anduin.portal.sa.common.TextAreaInput
import anduin.sa.client.SaEndpointClient
import anduin.sa.endpoints.{
  AddMappingDestinationParams,
  AddSaProfileParams,
  ParseSaListFromCsvParams,
  SaProfileBasicInfo,
  UpdateSaProfileParams
}
import anduin.sa.model.{CsvResourceDetails, SaMessage, SaSourceInfo}
import anduin.service.GeneralServiceException
import anduin.util.FilenameUtils
import anduin.sa.SaCommonUtils
import com.anduin.stargazer.client.utils.ZIOUtils

final case class ImportSaProfileModal(
  renderTarget: Observer[Unit] => Node = _ => div(),
  onDone: Observer[Unit] = Observer.empty,
  existingSaProfileOpt: Option[SaProfileBasicInfo] = None
) {
  private val existingProfileNameOpt = existingSaProfileOpt.map(_.profileName)
  private val existingProfileDescriptionOpt = existingSaProfileOpt.map(_.profileDescription)

  private val profileNameVar = Var(existingProfileNameOpt.getOrElse(""))
  private val profileDescriptionVar = Var(existingProfileDescriptionOpt.getOrElse(""))
  private val saAliasIdxVar = Var[Int](1)
  private val saDescriptionIdxVar = Var[Int](2)
  private val saTypeIdxVar = Var[Int](3)
  private val saParentIdxVar = Var[Int](4)
  private val saOntologyAnnotationIdxVar = Var[Int](5)
  private val saAsaIdxVar = Var[Int](6)
  private val startRowIdxVar = Var[Int](2)
  private val idxBaseVar = Var[Int](1) // Use 1-based index (instead of 0-based) which is more intuitive to human

  private val selectedFileOptVar = Var(Option.empty[(String, FileId)])
  private val parsedSaListVar = Var(List.empty[SaMessage])
  private val parsedSaListErrorListVar = Var(List.empty[String])

  private val btnUploadFileBusyVar = Var(false)
  private val btnParseCsvBusyVar = Var(false)
  private val btnCreateOrUpdateProfileBusyVar = Var(false)

  // Cannot parse -> return -1 to ignore that index
  private def parseOptionalIndex(strInput: String) = strInput.toIntOption.getOrElse(-1)

  private def uploadedFileHandler(
    files: Seq[File]
  ): EventStream[Unit] = {
    def notifySendCsvForParsingError = ZIO.attempt {
      Toast.error("Failed to send the selected CSV for parsing")
      btnUploadFileBusyVar.set(false)
    }

    AirStreamUtils.taskToStreamDEPRECATED {
      files.headOption
        .fold(
          ZIO.attempt(Toast.error("No CSV file selected"))
        ) { file =>
          for {
            _ <- ZIO.attempt(btnUploadFileBusyVar.set(true))
            createFolderResEither <- FileJsClient.createUserFolderIfNecessaryWithCache(CreateUserFolderParams())
            _ <- createFolderResEither.fold(
              _ => notifySendCsvForParsingError,
              createFolderRes =>
                for {
                  uploadRes <- FileJsClient.upload[UploadFileResponse](
                    UploadSystemFileParams(createFolderRes.folderId),
                    Seq(FileJsClient.JsFileUploadData(file, file.name))
                  )
                  _ <- uploadRes.files.headOption
                    .map(_._1)
                    .fold(notifySendCsvForParsingError) { inputCsvFileId =>
                      ZIO.attempt {
                        if (existingSaProfileOpt.nonEmpty) {
                          Var.set(
                            selectedFileOptVar -> Some(file.name -> inputCsvFileId),
                            btnUploadFileBusyVar -> false
                          )
                        } else {
                          Var.set(
                            selectedFileOptVar -> Some(file.name -> inputCsvFileId),
                            profileNameVar -> FilenameUtils.getName(file.name),
                            btnUploadFileBusyVar -> false
                          )
                        }
                        onParseCsv(Some(inputCsvFileId))
                      }
                    }
                } yield ()
            )
          } yield ()
        }
    }
  }

  private def onParseCsv(csvFileIdOpt: Option[FileId]): EventStream[Unit] = {
    val idxBase = idxBaseVar.now()

    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        _ <- ZIO.attempt(btnParseCsvBusyVar.set(true))
        _ <- ZIOUtils.traverseOptionUnit(csvFileIdOpt) { csvFileId =>
          SaEndpointClient
            .parseSaListFromCsv(
              ParseSaListFromCsvParams(
                csvFileId = csvFileId,
                aliasIdx = saAliasIdxVar.now() - idxBase,
                descriptionIdx = saDescriptionIdxVar.now() - idxBase,
                optionParentIdx = saParentIdxVar.now() - idxBase,
                saTypeIdx = saTypeIdxVar.now() - idxBase,
                asaIdx = saAsaIdxVar.now() - idxBase,
                ontologyAnnotationIdx = saOntologyAnnotationIdxVar.now() - idxBase,
                startRowIdx = startRowIdxVar.now() - idxBase
              )
            )
            .map(
              _.fold(
                err => {
                  Toast.error(s"Failed to parse SA list from CSV. Error: ${err.getMessage}")
                  resetUiVar()
                },
                res => {
                  Var.set(
                    btnParseCsvBusyVar -> false,
                    parsedSaListVar -> res.saList,
                    parsedSaListErrorListVar -> SaCommonUtils.getErrorMsgOptFromCheckingSaListValidity(res.saList)
                  )
                }
              )
            )
        }
      } yield ()
    }
  }

  private def resetUiVar() = {
    Var.set(
      btnCreateOrUpdateProfileBusyVar -> false,
      btnParseCsvBusyVar -> false,
      profileNameVar -> existingProfileNameOpt.getOrElse(""),
      profileDescriptionVar -> existingProfileDescriptionOpt.getOrElse(""),
      selectedFileOptVar -> None,
      parsedSaListVar -> List.empty,
      parsedSaListErrorListVar -> List.empty
    )
  }

  private def createOrUpdateSaProfile(
    onFinish: Observer[Unit],
    existingSaProfileOpt: Option[SaProfileBasicInfo]
  ): EventStream[Unit] = {
    val saList = parsedSaListVar.now()
    val profileName = profileNameVar.now()
    val profileDescription = profileDescriptionVar.now()

    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        _ <- ZIO.attempt(btnCreateOrUpdateProfileBusyVar.set(true))
        mappingDestIdEither <-
          if (saList.isEmpty) {
            ZIO.succeed(Left(GeneralServiceException("Skip because SA list is empty")))
          } else {
            selectedFileOptVar
              .now()
              .map { case (fileName, _) =>
                SaEndpointClient
                  .addMappingDestination(
                    AddMappingDestinationParams(
                      resourceId = fileName,
                      resourceDescription = fileName,
                      resourceDetails = CsvResourceDetails(fileName)
                    )
                  )
              }
              .getOrElse(ZIO.succeed(Left(GeneralServiceException("Skip because there is no selected file"))))
          }
        resEither <- mappingDestIdEither.fold(
          ex => ZIO.attempt(Left(ex)),
          mappingDestId => {
            val saList = parsedSaListVar
              .now()
              .map(saMsg => saMsg.copy(sourceInfoOpt = Option(SaSourceInfo(mappingDestId, saMsg.alias))))

            existingSaProfileOpt.fold(
              SaEndpointClient.addSaProfile(
                AddSaProfileParams(
                  profileName = profileName,
                  profileDescription = profileDescriptionVar.now(),
                  saList = saList
                )
              )
            ) { existingSaProfile =>
              SaEndpointClient.updateSaProfile(
                UpdateSaProfileParams(
                  saProfileId = existingSaProfile.saProfileId,
                  newProfileNameOpt = Option.when(profileName != existingSaProfile.profileName)(profileName),
                  newProfileDescriptionOpt =
                    Option.when(profileDescription != existingSaProfile.profileDescription)(profileDescription),
                  toAddSaList = saList
                )
              )
            }
          }
        )
      } yield resEither.fold(
        ex => {
          val action = if (existingSaProfileOpt.isEmpty) "create" else "update"
          btnCreateOrUpdateProfileBusyVar.set(false)
          Toast.error(s"Failed to $action SA profile. Error: ${ex.getMessage}")
        },
        _ => {
          val action = if (existingSaProfileOpt.isEmpty) "creating" else "updating"
          Toast.info(s"Done $action SA profile '$profileName'")
          resetUiVar()
          onFinish.onNext(())
        }
      )
    }
  }

  private def renderParseCsvSection() = {
    div(
      tw.my16,
      // Upload *.csv button
      child <-- selectedFileOptVar.signal.distinct.map { fileInfoOpt =>
        val (strDesc, strLabel) = fileInfoOpt.fold(
          "Select *.csv file" -> "Choose file"
        ) { case (fileName, _) =>
          s"Selected file: $fileName" -> "Change file"
        }
        div(
          tw.mb16,
          span(
            tw.mr8.fontSemiBold,
            strDesc
          ),
          FileUploader(
            style = ButtonL.Style.Ghost(
              icon = Some(Icon.Glyph.Upload),
              isBusy = btnUploadFileBusyVar.signal
            ),
            acceptTypes = ".csv",
            isMultiple = false,
            observer = Observer { files =>
              uploadedFileHandler(files)
              ()
            }
          )(strLabel).amend(tw.fontSemiBold)
        )
      },
      div(
        tw.mb16.flex,
        "After uploading the file, map the csv columns to the required data points by providing the column index. Column A is index 1, B is index 2, and so on"
      ),
      div(
        tw.mb16.flex,
        // CSA config to parse
        div(
          tw.mr8,
          div(
            tw.flex.itemsCenter,
            div(tw.mr8, "Stand alias"),
            TooltipL(
              renderTarget = IconL(name = Val(Icon.Glyph.InfoCircleLine))(),
              renderContent = _.amend("The key agreed with the client, can be a field or option level")
            )()
          ),
          TextAreaInput(
            valueSignal = saAliasIdxVar.signal.map(_.toString),
            numRow = 1,
            onInputChange = Observer(str => str.toIntOption.foreach(saAliasIdxVar.set))
          )()
        ),
        div(
          tw.mr8,
          div(
            tw.flex.itemsCenter,
            div(tw.mr8, "Description"),
            TooltipL(
              renderTarget = IconL(name = Val(Icon.Glyph.InfoCircleLine))(),
              renderContent =
                _.amend("Description of what the standard alias present, including the section it belongs to")
            )()
          ),
          TextAreaInput(
            valueSignal = saDescriptionIdxVar.signal.map(_.toString),
            numRow = 1,
            onInputChange = Observer(str => str.toIntOption.foreach(saDescriptionIdxVar.set))
          )()
        ),
        div(
          tw.mr8,
          div(
            tw.flex.itemsCenter,
            div(tw.mr8, "Data type"),
            TooltipL(
              renderTarget = IconL(name = Val(Icon.Glyph.InfoCircleLine))(),
              renderContent = _.amend(
                """Allowed data types
                  |"string" | "text"
                  |"boolean" | "bool"
                  |"integer" | "int"
                  |"float"
                  |"radio"
                  |"checkbox"
                  |""".stripMargin
              )
            )()
          ),
          TextAreaInput(
            valueSignal = saTypeIdxVar.signal.map(_.toString),
            numRow = 1,
            onInputChange = Observer(str => saTypeIdxVar.set(parseOptionalIndex(str)))
          )()
        ),
        div(
          tw.mr8,
          div(
            tw.flex.itemsCenter,
            div(tw.mr8, "Parent alias"),
            TooltipL(
              renderTarget = IconL(name = Val(Icon.Glyph.InfoCircleLine))(),
              renderContent = _.amend("The group that the options belong to")
            )()
          ),
          TextAreaInput(
            valueSignal = saParentIdxVar.signal.map(_.toString),
            numRow = 1,
            onInputChange = Observer(str => saParentIdxVar.set(parseOptionalIndex(str)))
          )()
        ),
        div(
          tw.mr8,
          div(
            tw.flex.itemsCenter,
            div(tw.mr8, "Ontology")
          ),
          TextAreaInput(
            valueSignal = saOntologyAnnotationIdxVar.signal.map(_.toString),
            numRow = 1,
            onInputChange = Observer(str => saOntologyAnnotationIdxVar.set(parseOptionalIndex(str)))
          )()
        ),
        div(
          tw.mr8,
          div(
            tw.flex.itemsCenter,
            div(tw.mr8, "Corresponding ASA")
          ),
          TextAreaInput(
            valueSignal = saAsaIdxVar.signal.map(_.toString),
            numRow = 1,
            onInputChange = Observer(str => saAsaIdxVar.set(parseOptionalIndex(str)))
          )()
        ),
        div(
          tw.mr8,
          div(
            tw.flex.itemsCenter,
            div(tw.mr8, "Starting Row index")
          ),
          TextAreaInput(
            valueSignal = startRowIdxVar.signal.map(_.toString),
            numRow = 1,
            onInputChange = Observer(str => str.toIntOption.foreach(startRowIdxVar.set))
          )()
        )
      ),
      child <-- selectedFileOptVar.signal.distinct.map { _ =>
        div(
          tw.wPc100.mb16,
          ButtonL(
            style = ButtonL.Style.Full(
              isBusy = btnParseCsvBusyVar.signal
            ),
            isDisabled = selectedFileOptVar.signal.map(_.isEmpty),
            onClick = Observer[dom.MouseEvent] { _ =>
              onParseCsv(selectedFileOptVar.now().map(_._2))
              ()
            }
          )("Parse SA list").amend(tw.wPc100)
        )
      },
      // SA Table
      SaTable(parsedSaListVar.signal)().amend(tw.mb16),
      children <-- parsedSaListErrorListVar.signal.distinct.map(
        _.map { errorMsg =>
          div(
            tw.mb16,
            label(
              tw.flex.itemsCenter.text11.leading16,
              div(
                tw.textWarning4,
                IconL(name = Val(Icon.Glyph.Warning), size = Icon.Size.Px16)()
              ),
              span(
                tw.flexFill.ml4.textWarning4,
                errorMsg
              )
            )
          )
        }
      )
    )
  }

  def apply(): Node = {
    ModalL(
      renderTitle = _ =>
        if (existingSaProfileOpt.nonEmpty) {
          div(
            "Append more SAs from CSV",
            div(
              tw.fontNormal.text11,
              "New SAs from CSV will overwrite existing ones in case of conflicted aliases"
            )
          )
        } else {
          div(
            "Import SA profile from CSV"
          )
        },
      isClosable = None,
      size = ModalL.Size(ModalL.Width.Px1160),
      renderTarget = renderTarget,
      renderContent = close =>
        div(
          ModalBodyL(
            div(
              tw.wPc100.hPc100,
              // Profile name
              div(
                tw.mb8,
                "SA profile name",
                TextAreaInput(
                  valueSignal = profileNameVar.signal,
                  numRow = 1,
                  onInputChange = Observer(profileNameVar.set)
                )()
              ),
              // Profile description
              div(
                tw.mb48,
                "SA profile description",
                TextAreaInput(
                  valueSignal = profileDescriptionVar.signal,
                  numRow = 2,
                  onInputChange = Observer(profileDescriptionVar.set)
                )()
              ),
              renderParseCsvSection()
            )
          ),
          ModalFooterWCancelL(cancel = close)(
            ButtonL(
              style = ButtonL.Style.Full(
                color = ButtonL.Color.Primary,
                isBusy = btnCreateOrUpdateProfileBusyVar.signal
              ),
              isDisabled = parsedSaListVar.signal.combineWith(parsedSaListErrorListVar.signal).map {
                case (parsedSaList, parsedSaListErrorList) => parsedSaList.isEmpty || parsedSaListErrorList.nonEmpty
              },
              onClick = Observer[dom.MouseEvent] { _ =>
                createOrUpdateSaProfile(Observer.combine(close, onDone), existingSaProfileOpt)
                ()
              }
            )(
              if (existingSaProfileOpt.nonEmpty) "Append to SA profile" else "Create SA profile"
            )
          )
        )
    )()
  }

}
