// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.sa.saprofile

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.menu.laminar.{MenuItemL, MenuL}
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.style.tw.*
import design.anduin.table.laminar.TableL
import com.raquo.laminar.api.L.*
import design.anduin.components.progress.CircleIndicator
import design.anduin.components.progress.laminar.CircleIndicatorL
import design.anduin.components.toast.Toast
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.id.sa.SaProfileId
import anduin.portal.sa.common.StringColumn
import anduin.sa.endpoints.{ExportProfileSaListToCsvParams, SaProfileBasicInfo}
import anduin.sa.client.SaEndpointClient
import anduin.tapir.endpoint.CommonParams
import anduin.file.FileDownloaderUtils
import com.anduin.stargazer.client.services.file.FileJsClient
import com.anduin.stargazer.service.FileServiceEndpoints.GetDownloadUrlParams
import com.anduin.stargazer.service.file.BatchDownloadRequest

final case class SaProfileDashboard() {

  private val saProfileListVar = Var[List[SaProfileBasicInfo]](List.empty)
  private val isLoadingVar = Var[Boolean](false)

  private def fetchProfileList: EventStream[Unit] = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        _ <- ZIO.attempt(isLoadingVar.set(true))
        _ <- SaEndpointClient
          .getAllSaProfileInfos(CommonParams.Empty())
          .map(
            _.fold(
              err => {
                Var.set(
                  isLoadingVar -> false,
                  saProfileListVar -> List.empty
                )
                Toast.error(s"Failed to get all SA profiles. Error: ${err.getMessage}")
              },
              res => {
                Var.set(
                  isLoadingVar -> false,
                  saProfileListVar -> res.profileInfoList
                )
              }
            )
          )
      } yield ()
    }
  }

  private def exportProfileSaList(
    saProfileId: SaProfileId
  ): EventStream[Unit] = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        _ <- SaEndpointClient
          .exportProfileSaListToCsv(
            ExportProfileSaListToCsvParams(saProfileId = saProfileId)
          )
          .map(
            _.fold(
              ex => Toast.error(s"Failed to export SA list for ${saProfileId.idString}. Error: ${ex.getMessage}"),
              res =>
                res.exportedCsvFileIdOpt.fold(
                  Toast.warn(s"No SA data to export for ${saProfileId.idString}")
                ) { exportedFileId =>
                  val downloadRequest = BatchDownloadRequest(fileIds = Seq(exportedFileId))
                  FileDownloaderUtils.download(
                    FileJsClient.getDownloadUrl(GetDownloadUrlParams(downloadRequest)).map(_.map(_.url)),
                    numFiles = downloadRequest.numFiles
                  )
                }
            )
          )
      } yield ()
    }
  }

  private val actionColumn = TableL.Column[SaProfileBasicInfo](
    title = "",
    field = "actions",
    renderCell = renderProps => {
      div(
        onClick.preventDefault.stopPropagation --> Observer.empty,
        PopoverL(
          position = PortalPosition.BottomRight,
          renderTarget = (open, isOpen) =>
            ButtonL(
              style = ButtonL.Style.Minimal(isSelected = isOpen, icon = Some(Icon.Glyph.EllipsisVertical)),
              onClick = open.contramap(_ => ())
            )().amend(tw.m4),
          renderContent = close =>
            MenuL(
              Seq(
                ViewSaProfileDetailsModal(
                  saProfileName = renderProps.data.profileName,
                  saProfileId = renderProps.data.saProfileId,
                  renderTarget = open =>
                    MenuItemL(
                      icon = Some(Icon.Glyph.Eye),
                      color = MenuItemL.ColorGray,
                      onClick = open
                    )("View SA details"),
                  onClose = close
                )(),
                ImportSaProfileModal(
                  renderTarget = open =>
                    MenuItemL(
                      icon = Some(Icon.Glyph.Import),
                      color = MenuItemL.ColorGray,
                      onClick = open
                    )("Append more SAs from *.csv"),
                  onDone = Observer { _ =>
                    fetchProfileList
                    ()
                  },
                  existingSaProfileOpt = Some(renderProps.data)
                )(),
                MenuItemL(
                  icon = Some(Icon.Glyph.FileExport),
                  color = MenuItemL.ColorGray,
                  onClick = Observer[Unit] { _ =>
                    exportProfileSaList(renderProps.data.saProfileId)
                    close.onNext(())
                  }
                )("Export SA details")
              )
            )
        )()
      )
    },
    width = Some(60)
  )

  def apply(): HtmlElement = {
    div(
      tw.m16,
      fetchProfileList --> Observer.empty,
      ImportSaProfileModal(
        renderTarget = open =>
          ButtonL(
            style = ButtonL.Style.Full(
              color = ButtonL.Color.Primary,
              icon = Some(Icon.Glyph.Import)
            ),
            onClick = open.contramap(_ => ())
          )("Import from *.csv").amend(tw.mb32),
        onDone = Observer { _ =>
          fetchProfileList
          ()
        }
      )(),
      // SA Profiles
      div(
        tw.mb16.text15.fontSemiBold,
        "SA Profiles"
      ),
      TableL[SaProfileBasicInfo](
        options = TableL.Options(layout = TableL.Layout.FitColumns),
        loading = TableL.Loading(
          criteria = isLoadingVar.signal,
          render = () =>
            div(
              tw.flex.justifyCenter,
              CircleIndicatorL(size = CircleIndicator.Size.Px48)()
            )
        ),
        columns = List(
          StringColumn[SaProfileBasicInfo](
            colName = "Profile ID",
            minWidth = 100,
            getFieldValue = _.saProfileId.idString
          )(),
          StringColumn[SaProfileBasicInfo](
            colName = "Profile name",
            minWidth = 100,
            getFieldValue = _.profileName
          )(),
          StringColumn[SaProfileBasicInfo](
            colName = "Profile description",
            minWidth = 150,
            getFieldValue = _.profileDescription
          )(),
          StringColumn[SaProfileBasicInfo](
            colName = "SA count",
            minWidth = 50,
            getFieldValue = _.saCountBySourceList.map(_._2).sum.toString
          )(),
          actionColumn
        ),
        dataSignal = saProfileListVar.signal
      ).amend(maxHeight.vh := 70)
    )
  }

}
