// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.fundsub.dataextract

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.callout.Callout
import design.anduin.components.callout.laminar.CalloutL
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL, ModalL}
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.portal.{PortalPosition, PortalUtils}
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.tag.Tag
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import zio.ZIO

import anduin.component.util.JsDateFormatterUtils
import anduin.forms.data.DataTools.DataToolConfig
import anduin.frontend.AirStreamUtils
import anduin.fundsub.dataextract.FundSubDataExtractSchema.FundSubDataExtractRequestStatus
import anduin.fundsub.dataextract.GetDataExtractRequestFormInfoResp.ViewOnlyModeData
import anduin.fundsub.dataextract.{CloseDataExtractFormViewParams, PreviewExtractedFormDataModal}
import anduin.fundsub.dataextract.PreviewExtractedFormDataModal.{RenderHeaderProps, SaveExtractedDataProps}
import anduin.fundsub.endpoint.operation.FormVersionForFundSetup
import anduin.fundsub.endpoint.operation.dataextract.{
  DataExtractRequestFormInfoForAnduinAdmin,
  FundSubDataExtractInfoForDashboard,
  GetDataExtractFormInfoForAnduinAdminParams,
  LpInfoForDataExtractRequest
}
import anduin.portal.fundsub.operation.FundSubOperationDataExtractClient
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubDataExtractClient

private[dataextract] case class ViewFormDataOfRequestModal(
  closeOuterModal: Observer[Unit],
  lpInfoOpt: Option[LpInfoForDataExtractRequest],
  request: FundSubDataExtractInfoForDashboard,
  onUpdateRequest: Observer[Unit],
  canEdit: Boolean
) {

  private val lpRepresentativeName = lpInfoOpt.fold("") { lpInfo =>
    if (lpInfo.firmName.nonEmpty) {
      lpInfo.firmName
    } else {
      lpInfo.fullName
    }
  }

  private val isMarkingAsReady = Var(false)

  private val markAsReadyEventBus = new EventBus[Unit]

  private val requestId = request.id

  private val isFetchingDataExtractRequestFormInfo: Var[Boolean] = Var(true)

  private val dataExtractRequestFormInfoOptVar: Var[Option[DataExtractRequestFormInfoForAnduinAdmin]] = Var(None)
  private val viewOnlyModeDataOptVar: Var[Option[ViewOnlyModeData]] = Var(None)

  private val dataExtractRequestFormInfoOptSignal: Signal[Option[DataExtractRequestFormInfoForAnduinAdmin]] =
    dataExtractRequestFormInfoOptVar.signal

  private val extractedDataFormVersionInfoSignal: Signal[Option[FormVersionForFundSetup]] =
    dataExtractRequestFormInfoOptSignal.map(_.map(_.formVersionInfo))

  private def isFromDataExtractSignal =
    dataExtractRequestFormInfoOptSignal.map(_.exists(_.extractedFormData.isFromDataExtract))

  private val closeFormViewEventBus = new EventBus[Unit]

  private def renderFormFillingModal(isViewOnly: Boolean) = {
    ModalL(
      renderContent = _ =>
        PreviewExtractedFormDataModal(
          requestId = requestId,
          formDataOptSignal = dataExtractRequestFormInfoOptSignal.map(_.map(_.formData)),
          initialGaiaStateOptSignal = dataExtractRequestFormInfoOptSignal.map(_.map(_.extractedFormData.gaiaState)),
          onCloseFormModal = closeFormViewEventBus.writer,
          submittedDocsSignal = Val(request.submittedDocs),
          dataToolConfigOpt = Some(
            DataToolConfig(
              enablePreviewData = true,
              enablePreviewDocuments = true,
              enableExport = true,
              enableImport = !isViewOnly,
              enableUndo = !isViewOnly,
              enableReset = !isViewOnly
            )
          ),
          renderHeader = renderHeader,
          viewOnlyModeDataOptSignal = viewOnlyModeDataOptVar.signal,
          initialFormDataLastUpdateAtSignal =
            dataExtractRequestFormInfoOptSignal.map(_.flatMap(_.extractedFormData.lastUpdatedAt))
        )(),
      defaultIsOpened = true,
      afterUserClose = closeOuterModal,
      size = ModalL.Size(width = ModalL.Width.Full, height = ModalL.Height.Full),
      isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false))
    )()
  }

  private def renderHeader(renderHeaderProps: PreviewExtractedFormDataModal.RenderHeaderProps) = {
    div(
      div(
        tw.flex.itemsCenter.py12.px20.shadow1.z1,
        ButtonL(
          style = ButtonL.Style.Ghost(isBusy = renderHeaderProps.isClosingAndSaving),
          onClick = renderHeaderProps.onCloseModal.contramap(_ => ())
        )(IconL(Val(Icon.Glyph.Cross))()),
        renderFormTitle(renderHeaderProps),
        child <-- viewOnlyModeDataOptVar.signal.map { viewOnlyDataMode =>
          if (!canEdit || viewOnlyDataMode.nonEmpty) renderViewOnlyTag else renderSavedBlock
        },
        renderActions(renderHeaderProps)
      ),
      Option.when(request.status == FundSubDataExtractRequestStatus.InProgress) {
        CalloutL(style = Callout.Style.Primary())(
          "Form data is automatically synced from the data extraction tool"
        )
      },
      child.maybe <-- viewOnlyModeDataOptVar.signal.map {
        _.map(renderViewOnlyModeCallout)
      }
    )

  }

  private def renderViewOnlyModeCallout(viewOnlyModeData: ViewOnlyModeData): HtmlElement = {
    div(
      tw.flex.itemsCenter.justifyCenter.spaceX8.py8.bgWarning3,
      IconL(name = Val(Icon.Glyph.Info))(),
      div(
        tw.flex.itemsCenter,
        "Extracted data is being reviewed by",
        span(tw.fontSemiBold.mx4, viewOnlyModeData.userName),
        s"(${viewOnlyModeData.userEmail}). Editing will be available when ",
        span(tw.fontSemiBold.mx4, viewOnlyModeData.userName),
        " exits the review view."
      )
    )
  }

  private def renderActions(renderHeaderProps: PreviewExtractedFormDataModal.RenderHeaderProps) = {
    div(
      markAsReadyEventBus.events.flatMapSwitch(_ => onMarkAsReady) --> Observer.empty,
      tw.mlAuto.flex.itemsCenter.spaceX8,
      child.maybe <-- viewOnlyModeDataOptVar.signal.map { viewOnlyModeDataOpt =>
        Option.when(canEdit && viewOnlyModeDataOpt.isEmpty) {
          ButtonL(
            style = ButtonL.Style.Full(isBusy = renderHeaderProps.isClosingAndSaving),
            onClick = Observer { _ =>
              renderHeaderProps.onCloseModal.onNext(())
            }
          )("Save and close")
        }
      },
      Option.when(request.status == FundSubDataExtractRequestStatus.InProgress) {
        ModalL(
          renderTarget = open =>
            ButtonL(
              style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
              onClick = open.contramap(_ => ())
            )("Mark as ready for review"),
          renderContent = cancel => renderMarkAsReadyConfirmationModal(cancel, renderHeaderProps),
          renderTitle = _ => "Mark extracted data as ready for review"
        )()
      }
    )
  }

  private def renderViewOnlyTag = {
    div(
      tw.ml16,
      TagL(
        color = Val(Tag.Bold.Primary),
        icon = Some(Icon.Glyph.Eye),
        label = Val("View only")
      )()
    )
  }

  private def renderFormTitle(renderHeaderProps: RenderHeaderProps) = {
    div(
      tw.ml16,
      div(
        tw.text15.leading20.fontSemiBold,
        s"Data extracted for $lpRepresentativeName"
      ),
      child.maybe <-- renderHeaderProps.formDataLastUpdateAtSignal.map(_.map { lastUpdatedAt =>
        val formattedTime =
          JsDateFormatterUtils.format(lastUpdatedAt, JsDateFormatterUtils.JsDateFormat.MonthDateYearTime3)
        div(
          tw.textGray7,
          s"Last edited: $formattedTime"
        )
      })
    )
  }

  private def renderSavedBlock = {
    div(
      tw.ml16,
      TooltipL(
        renderTarget = span(
          tw.flex.itemsCenter,
          tw.textBody.textGray6,
          IconL(name = Val(Icon.Glyph.Check))(),
          span(tw.ml8.textGray7, "Saved")
        ),
        renderContent = _.amend(
          "All changes save automatically"
        ),
        position = PortalPosition.BottomCenter
      )()
    )
  }

  private def renderMarkAsReadyConfirmationModal(
    cancel: Observer[Unit],
    renderHeaderProps: PreviewExtractedFormDataModal.RenderHeaderProps
  ) = {
    div(
      ModalBodyL(
        div(
          div("After marking this request as ready for review:"),
          ul(
            tw.mt8.spaceY8,
            li(
              tw.pl8,
              "Form data will be exposed to the fund managers"
            ),
            li(
              tw.pl8,
              "Form data will not be synced with the data extraction tool. Any changes must be done using the fundsub admin portal"
            )
          ),
          div(
            tw.my24,
            DividerL()()
          ),
          child.maybe <-- extractedDataFormVersionInfoSignal.map(_.map { formVersionInfo =>
            div(
              s"Form version of extracted data: ",
              span(tw.fontSemiBold, formVersionInfo.nameWithNumber)
            )
          }),
          lpInfoOpt.flatMap(_.currentFormVersionOpt).fold(emptyNode) { formVersionInfo =>
            div(
              tw.mt8,
              s"Form version of investor ",
              span(tw.fontSemiBold, formVersionInfo.nameWithNumber)
            )
          }
        )
      ),
      ModalFooterWCancelL(
        cancel = cancel,
        cancelLabel = "Close"
      )(
        ButtonL(
          style = ButtonL.Style.Full(color = ButtonL.Color.Primary, isBusy = isMarkingAsReady.signal),
          onClick = Observer { _ =>
            renderHeaderProps.onSaveData.onNext(
              SaveExtractedDataProps(
                onSuccess = markAsReadyEventBus.writer,
                isClosingAfterSaving = false
              )
            )
          }
        )("Confirm")
      )
    )
  }

  private def onMarkAsReady: EventStream[Unit] = {
    val task = for {
      _ <- ZIO.succeed(isMarkingAsReady.set(true))
      _ <- FundSubOperationDataExtractClient
        .markDataExtractRequestAsReadyForReview(requestId)
        .map(
          _.fold(
            err => {
              Toast.error(err.getMessage)
              isMarkingAsReady.set(false)
            },
            _ => {
              Toast.success("Saved form data successfully")
              isMarkingAsReady.set(false)
              onUpdateRequest.onNext(())
              closeOuterModal.onNext(())
            }
          )
        )
    } yield ()
    AirStreamUtils.taskToStream(task)
  }

  private def renderHasNoExtractedData = {
    ModalL(
      renderContent = onClose =>
        div(
          ModalBodyL(
            div(
              NonIdealStateL(
                icon = img(
                  src := "/web/gondor/images/404/illustration-404.svg"
                ),
                title = "This data extraction item is empty",
                description = "Please go to the linked project, extract data and import to form for this item"
              )()
            )
          ),
          ModalFooterWCancelL(onClose)()
        ),
      size = ModalL.Size(width = ModalL.Width.Px600),
      defaultIsOpened = true,
      afterUserClose = closeOuterModal
    )()

  }

  def apply(): HtmlElement = {
    val isFormDataEmptySignal = dataExtractRequestFormInfoOptSignal.map(_.isEmpty).distinct
    div(
      child <-- isFetchingDataExtractRequestFormInfo.signal.distinct.map {
        if (_) {
          BlockIndicatorL()()
        } else {
          div(
            child <-- isFormDataEmptySignal.combineWith(viewOnlyModeDataOptVar.signal).map {
              case (isFormDataEmpty, viewOnlyModeDataOpt) =>
                if (isFormDataEmpty) {
                  renderHasNoExtractedData
                } else {
                  val isViewOnly = !canEdit || viewOnlyModeDataOpt.nonEmpty
                  renderFormFillingModal(isViewOnly)
                }
            }
          )
        }
      },
      fetchData --> Observer.empty,
      closeFormViewEventBus.events.sample(isFromDataExtractSignal).flatMapSwitch { isFromDataExtract =>
        if (isFromDataExtract) EventStream.empty else onCloseFormView
      } --> Observer.empty
    )
  }

  private def fetchData: EventStream[Unit] = {
    AirStreamUtils.taskToStream(
      FundSubOperationDataExtractClient
        .anduinAdminGetDataExtractRequestFormInfo(
          GetDataExtractFormInfoForAnduinAdminParams(
            requestId = requestId,
            shouldGetLock = canEdit
          )
        )
        .map(
          _.fold(
            err => {
              isFetchingDataExtractRequestFormInfo.set(false)
              Toast.error(err.getMessage)
            },
            resp =>
              Var.set(
                isFetchingDataExtractRequestFormInfo -> false,
                dataExtractRequestFormInfoOptVar -> resp.formInfoOpt,
                viewOnlyModeDataOptVar -> resp.viewOnlyModeDataOpt
              )
          )
        )
    )
  }

  private def onCloseFormView: EventStream[Unit] = {
    ZIOUtils.toEventStreamUnsafe {
      FundSubDataExtractClient
        .closeDataExtractFormView(CloseDataExtractFormViewParams(requestId))
        .map(_ => closeOuterModal.onNext(()))
    }
  }

}
