// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.fundsub.setup.featureswitch

import com.raquo.laminar.api.L.*
import design.anduin.components.button.Button
import design.anduin.components.field.Field
import design.anduin.components.modal.Modal
import design.anduin.components.switcher.react.SwitcherR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.featureswitch.FundSubFeatureSwitchConstants
import anduin.portal.fundsub.setup.featureswitch.FundSubSetupSwitchHandoff.FundSubFeatureSwitchHandoff
import anduin.protobuf.fundsub.{FeatureSwitch, LpFlowType}

private[fundsub] final case class FundSubSetupFeatureSwitch(
  lpFlowType: LpFlowType,
  featureSwitch: FeatureSwitch,
  onChangeSwitch: FeatureSwitch => Callback,
  isCreatingFund: Boolean
) {
  def apply(): VdomElement = FundSubSetupFeatureSwitch.component(this)

  private val isFlexibleFlow = lpFlowType == LpFlowType.Flexible

}

private[fundsub] object FundSubSetupFeatureSwitch {

  private type Props = FundSubSetupFeatureSwitch

  private final case class State(
    expandAll: Boolean = false
  )

  private case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      Field(
        label = Some("Features"),
        layout = Field.Layout.Hor(Field.Flex.Fill(1), Field.Flex.Fill(4)),
        helper = Field.Helper.BelowLabel(
          SwitcherR(
            isChecked = state.expandAll,
            onChange = checked => scope.modState(_.copy(expandAll = checked))
          )("Show all")
        )
      )(
        <.div(
          standardSwitchSection(props, state),
          enterpriseSwitchSection(props, state),
          renderDownloadMetrics
        )
      )
    }

    private def standardSwitchSection(props: Props, state: State): VdomElement = {
      <.div(
        FundSubSetupSwitch(
          isChecked = !props.featureSwitch.disableInAppAnnouncement,
          label = FundSubFeatureSwitchConstants.enableInAppAnnouncement,
          onChange = checked => props.onChangeSwitch(props.featureSwitch.copy(disableInAppAnnouncement = !checked)),
          shouldHide = !state.expandAll
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableFundPermission,
          FundSubFeatureSwitchConstants.enableFundPermission,
          checked => props.onChangeSwitch(props.featureSwitch.copy(enableFundPermission = checked)),
          shouldHide = !props.isFlexibleFlow,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.enableFundPermission,
            body = <.div(
              <.div(
                <.span(tw.fontBold, "When the switch is turned off"),
                ", admin user only has access to the default 4 groups. They not be able to (1) create a new fund user group, " +
                  "(2) delete any of the default group, (3) apply a custom role to any of the groups, and (4) cannot create new dashboards. " +
                  "They can only create investor group. "
              ),
              <.div(
                <.span(tw.fontBold, "When the switch is turned on"),
                ", admin user will have the ability to do the followings: (1) Create fund user group, " +
                  "(2) delete any fund user group except for super admin group, (3) customize permission, " +
                  "(4) create investor group and assign to custom GP group, and (5) control access to investors data."
              )
            ),
            images = Seq(
              "enableFundPermission_1.png",
              "enableFundPermission_2.png",
              "enableFundPermission_3.png",
              "enableFundPermission_4.png",
              "enableFundPermission_5.png"
            )
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.allowMultiStepInSubscriptionReview,
          FundSubFeatureSwitchConstants.allowMultiStepInSubscriptionReview,
          checked => props.onChangeSwitch(props.featureSwitch.copy(allowMultiStepInSubscriptionReview = checked)),
          shouldHide = !props.isFlexibleFlow,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = "Multi-step review",
            body = "When turn on, the fund administrator can set up a multi-step approval workflow" +
              " for unsigned and signed subscription documents.",
            images = Seq(
              "allowSubscriptionDocMultiStepReview_1.png",
              "allowSubscriptionDocMultiStepReview_2.png",
              "allowSubscriptionDocMultiStepReview_3.png"
            )
          )
        ),
        FundSubSetupSwitch(
          !props.featureSwitch.disableLpUndoSubmission,
          FundSubFeatureSwitchConstants.enableLpUndoSubmission,
          checked => props.onChangeSwitch(props.featureSwitch.copy(disableLpUndoSubmission = !checked)),
          handoffContent = FundSubFeatureSwitchHandoff(
            header = "Edit subscription after submission",
            body =
              "When turn on, the option to edit the submitted subscription document would be display from LP point of view.",
            images = Seq("enableInvestorUndoSubmission.png")
          )
        ),
        FundSubSetupSwitch(
          !props.featureSwitch.disableLpSupportingDoc,
          FundSubFeatureSwitchConstants.enableLpSupportingDoc,
          checked =>
            props.onChangeSwitch(
              props.featureSwitch.copy(
                disableLpSupportingDoc = !checked,
                blockLpSubmissionUntilFinishSupporting =
                  props.featureSwitch.blockLpSubmissionUntilFinishSupporting && checked
              )
            ),
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.enableLpSupportingDoc,
            body = "When turn on, LPs will be able to submit additional documents aside from what required by GPs.",
            images = Seq("enableInvestorSupportingDocSection.png")
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableSupportingDocReview,
          FundSubFeatureSwitchConstants.enableSupportingDocReview,
          checked => props.onChangeSwitch(props.featureSwitch.copy(enableSupportingDocReview = checked)),
          shouldHide = !state.expandAll,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.enableSupportingDocReview,
            body = <.div(
              <.div(
                "When turn on, Fund Admins can set up an approval workflow for supporting documents in the Review and Approval section in the Setting tab."
              ),
              <.div(
                "To turn off the switch in Admin Portal, Fund Admin must clear out all pending approval requests and disable on the GP dashboard first."
              )
            ),
            images = Seq(
              "enableSupportingDocReview1.png",
              "enableSupportingDocReview2.png",
              "enableSupportingDocReview3.png"
            )
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enabledAddAdditionalTaxForms,
          FundSubFeatureSwitchConstants.enabledAddAdditionalTaxForms,
          checked => props.onChangeSwitch(props.featureSwitch.copy(enabledAddAdditionalTaxForms = checked)),
          handoffContent = FundSubFeatureSwitchHandoff(
            header = "Add form to supporting documents",
            body = "When turn on, the \"Add form\" button will be displayed in the Upload AML/KYC" +
              " & other documents section in LP's point of view.",
            images = Seq("enableAddSupportingFormButton.png")
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.formCommentSwitch,
          FundSubFeatureSwitchConstants.formCommentSwitch,
          checked => props.onChangeSwitch(props.featureSwitch.copy(formCommentSwitch = checked)),
          handoffContent = FundSubFeatureSwitchHandoff(
            header = "Form commenting",
            body = "When turn on, GPs and LPs would be able to add comments to subscription document.",
            images = Seq("enableFormCommenting.png")
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableAmlKycCommenting,
          FundSubFeatureSwitchConstants.enableAmlKycCommenting,
          checked => props.onChangeSwitch(props.featureSwitch.copy(enableAmlKycCommenting = checked)),
          shouldHide = !props.featureSwitch.formCommentSwitch,
          shouldDisable = !props.isFlexibleFlow,
          paddingLevel = 1,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = "AML/KYC commenting",
            body = "When turn on, GPs and LPs would be able to add comments to supporting documents.",
            images = Seq("enableAMLKYCCommenting.png")
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableCommentMentioning,
          FundSubFeatureSwitchConstants.enableCommentMentioning,
          checked => props.onChangeSwitch(props.featureSwitch.copy(enableCommentMentioning = checked)),
          shouldHide = !props.featureSwitch.formCommentSwitch,
          shouldDisable = !props.isFlexibleFlow,
          paddingLevel = 1,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = "Comment mentioning",
            body = "When turned on, members in a comment thread can mention other people"
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableCommentAssignment,
          FundSubFeatureSwitchConstants.enableCommentAssignment,
          checked => props.onChangeSwitch(props.featureSwitch.copy(enableCommentAssignment = checked)),
          shouldHide = !props.featureSwitch.formCommentSwitch,
          shouldDisable = !props.isFlexibleFlow,
          paddingLevel = 1,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = "Comment assignment",
            body = "When turned on, GPs can assign other GP users or GP groups to a comment anchor point"
          )
        ),
        FundSubSetupSwitch(
          !props.featureSwitch.disableAbilityOfInvestorToInviteCollaborator,
          FundSubFeatureSwitchConstants.enableAbilityOfInvestorToInviteCollaborator,
          checked =>
            props.onChangeSwitch(props.featureSwitch.copy(disableAbilityOfInvestorToInviteCollaborator = !checked)),
          shouldDisable = !props.isFlexibleFlow
        ),
        FundSubSetupSwitch(
          !props.featureSwitch.disableSubmissionInstruction,
          FundSubFeatureSwitchConstants.enableSubmissionInstruction,
          checked => props.onChangeSwitch(props.featureSwitch.copy(disableSubmissionInstruction = !checked)),
          shouldHide = !state.expandAll || props.isFlexibleFlow,
          handoffContent = FundSubFeatureSwitchHandoff(
            images = Seq("enableSubmissionInstruction_1.jpg", "enableSubmissionInstruction_2.jpg")
          )
        ),
        FundSubSetupSwitch(
          !props.featureSwitch.disableInvestFromAdditionalEntity,
          FundSubFeatureSwitchConstants.enableInvestFromAdditionalEntity,
          checked => props.onChangeSwitch(props.featureSwitch.copy(disableInvestFromAdditionalEntity = !checked)),
          shouldHide = !state.expandAll,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.enableInvestFromAdditionalEntity,
            body = "When turn on, the option to \"Invest from the additional entity\" will be display.",
            images = Seq("disableInvestFromAdditionalEntity_1.png", "disableInvestFromAdditionalEntity_2.png")
          )
        ),
        FundSubSetupSwitch(
          !props.featureSwitch.disableDownloadSubscriptionDocument,
          FundSubFeatureSwitchConstants.enableDownloadSubscriptionDocument,
          checked => props.onChangeSwitch(props.featureSwitch.copy(disableDownloadSubscriptionDocument = !checked)),
          shouldHide = !state.expandAll,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.enableDownloadSubscriptionDocument,
            body = "When turn on, LP will be able to download subscription documents."
          )
        ),
        FundSubSetupSwitch(
          !props.featureSwitch.disabledMarkAsNotApplicable,
          FundSubFeatureSwitchConstants.enableLPMarkAsNotApplicable,
          checked => props.onChangeSwitch(props.featureSwitch.copy(disabledMarkAsNotApplicable = !checked)),
          shouldHide = !state.expandAll,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = "Disable LP marking supporting doc as \"Not applicable\"",
            body = "When turn on, LP will be able to mark a supporting doc as \"Not applicable\".",
            images = Seq("enableMarkSupportingDocAsNotApplicableFeature.png")
          )
        ),
        FundSubSetupSwitch(
          !props.featureSwitch.hideFormFilesAndSigningButtons,
          FundSubFeatureSwitchConstants.showFormFilesAndSigningButtons,
          checked => props.onChangeSwitch(props.featureSwitch.copy(hideFormFilesAndSigningButtons = !checked)),
          shouldHide = !state.expandAll,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = "Hide signature section for LP",
            body = "When turn off, LP won't be able to see the signature section.",
            images = Seq("showSigningButtonsGroup.png")
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.showManualOrdersToInvestors,
          FundSubFeatureSwitchConstants.showManualOrdersToInvestors,
          checked => props.onChangeSwitch(props.featureSwitch.copy(showManualOrdersToInvestors = checked)),
          shouldHide = !state.expandAll,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = "Display offline subscription for investors",
            body =
              "When turn on, investors can view their offline subscriptions in the \"Subscription prepared for you\" section. " +
                "This section is located in Fundsub App Dashboard under the \"Fund you're participating in\" session.",
            images = Seq("showOfflineSubscriptionsToInvestors.png")
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.showOrdersViaLinkToInvitedLpsOnly,
          FundSubFeatureSwitchConstants.showOrdersViaLinkToInvitedLpsOnly,
          checked => props.onChangeSwitch(props.featureSwitch.copy(showOrdersViaLinkToInvitedLpsOnly = checked)),
          shouldHide = !state.expandAll,
          shouldDisable = !props.featureSwitch.showManualOrdersToInvestors,
          paddingLevel = 1,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.showOrdersViaLinkToInvitedLpsOnly,
            body =
              "When turn on, only invited investors with existing online or offline orders can access via shareable link. " +
                "New subscriptions cannot be created via shareable link."
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableFormDiff,
          FundSubFeatureSwitchConstants.enableFormDiff,
          checked => props.onChangeSwitch(props.featureSwitch.copy(enableFormDiff = checked)),
          shouldHide = !state.expandAll || !props.isFlexibleFlow,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.enableFormDiff,
            body = "When turn on, Fund Admin can compare form versions if there are updates. " +
              "Fund Admin can do this by go to Setting and click on Document tab.",
            images = Seq("enableFormDiff.png")
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableLpManualSubmitSubscription,
          FundSubFeatureSwitchConstants.enableLpManualSubmitSubscription,
          checked =>
            props.onChangeSwitch(
              props.featureSwitch.copy(
                enableLpManualSubmitSubscription = checked,
                enableLpReusePreviousSignedVersion = props.featureSwitch.enableLpReusePreviousSignedVersion && checked,
                blockLpSubmissionUntilFinishSupporting =
                  props.featureSwitch.blockLpSubmissionUntilFinishSupporting && checked
              )
            ),
          shouldHide = !state.expandAll || !props.isFlexibleFlow,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.enableLpManualSubmitSubscription,
            body = "When turn on, LP will need to manually submit subscription order when finish so GP can proceed.",
            images = Seq("enableLPManuallySubmitSubscriptionDocuments.png")
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableLpReusePreviousSignedVersion,
          FundSubFeatureSwitchConstants.enableLpReusePreviousSignedVersion,
          checked => props.onChangeSwitch(props.featureSwitch.copy(enableLpReusePreviousSignedVersion = checked)),
          shouldHide = !state.expandAll || !props.isFlexibleFlow,
          shouldDisable =
            !props.featureSwitch.enableLpManualSubmitSubscription || props.featureSwitch.showSwitchAllowFormEditPostSigning,
          paddingLevel = 1,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.enableLpReusePreviousSignedVersion,
            body =
              "When turn on, LPs will be able to reuse signatures from previous version of subscription document " +
                "as long as there is no changes detected to required fields. " +
                "Once submitted, both the newest version (contains latest form data) " +
                "and previous version (contains signatures) will be sent to GP for next step."
          )
        ),
        FundSubSetupSwitch(
          isChecked = props.featureSwitch.showSwitchAllowFormEditPostSigning,
          label = FundSubFeatureSwitchConstants.showSwitchAllowFormEditPostSigning,
          onChange = checked =>
            props.onChangeSwitch(
              props.featureSwitch.copy(
                showSwitchAllowFormEditPostSigning = checked,
                allowFormEditPostSigning = props.featureSwitch.allowFormEditPostSigning && checked
              )
            ),
          shouldHide = !state.expandAll || !props.isFlexibleFlow,
          shouldDisable = props.featureSwitch.enableLpReusePreviousSignedVersion,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.showSwitchAllowFormEditPostSigning,
            body = <.div(
              <.div(
                "When turned on, fund-side users can go to the fund setting and turn on the ability to make edits on subdocs post signing"
              ),
              <.div(
                s"""This feature and "${FundSubFeatureSwitchConstants.enableLpReusePreviousSignedVersion}" cannot be both enabled"""
              )
            )
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.blockLpSubmissionUntilFinishSupporting,
          FundSubFeatureSwitchConstants.blockLpSubmissionUntilFinishSupporting,
          checked => props.onChangeSwitch(props.featureSwitch.copy(blockLpSubmissionUntilFinishSupporting = checked)),
          shouldHide = !state.expandAll || !props.isFlexibleFlow,
          shouldDisable =
            !props.featureSwitch.enableLpManualSubmitSubscription || props.featureSwitch.disableLpSupportingDoc,
          paddingLevel = 1,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = "Allow submission when Sub doc & AML/KYC docs are completed",
            body =
              "When turn on, the submit button will be disable until LP fulfilled all the required AML/KYC documents"
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.exportInvestorDashboardTagsAsColumns,
          FundSubFeatureSwitchConstants.exportInvestorDashboardTagsAsColumns,
          checked => props.onChangeSwitch(props.featureSwitch.copy(exportInvestorDashboardTagsAsColumns = checked)),
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.exportInvestorDashboardTagsAsColumns,
            body =
              "When turn on, exporting file of investor dashboard data from Standard Dashboard will have tags as a column.",
            images = Seq("exportInvestorDashboardTagsAsColumns.png")
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableImportData,
          FundSubFeatureSwitchConstants.enableImportData,
          checked => props.onChangeSwitch(props.featureSwitch.copy(enableImportData = checked)),
          handoffContent = FundSubFeatureSwitchHandoff(
            header = "Import Investor data",
            body =
              "When turn on, GPs can import from spreadsheet. Spreadsheet template is available to download and fill data. " +
                "Note that this feature only available to turn on if fund is using ASA form.",
            images = Seq("enableDataImport.png")
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableAmlKycListAfterRequiredFieldsCompleted,
          FundSubFeatureSwitchConstants.enableAmlKycListAfterRequiredFieldsCompleted,
          checked =>
            props.onChangeSwitch(props.featureSwitch.copy(enableAmlKycListAfterRequiredFieldsCompleted = checked)),
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.enableAmlKycListAfterRequiredFieldsCompleted,
            body =
              "When turn on, the AML/KYC and supporting document list becomes visible once all required fields are filled." +
                "When turn off, both recommended and required fields must be completed to display the list"
          ),
          shouldHide = !state.expandAll || !props.isFlexibleFlow
        ),
        FundSubSetupSwitch(
          props.featureSwitch.showOrderMetadataInDetailView,
          FundSubFeatureSwitchConstants.showOrderMetadataInDetailView,
          checked => props.onChangeSwitch(props.featureSwitch.copy(showOrderMetadataInDetailView = checked)),
          shouldHide = !state.expandAll,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.showOrderMetadataInDetailView,
            body = "When turn on, order metadata fields will be listed in the investor detail view"
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableImportFromFundData,
          FundSubFeatureSwitchConstants.enableImportFromFundData,
          checked => props.onChangeSwitch(props.featureSwitch.copy(enableImportFromFundData = checked)),
          shouldHide = !state.expandAll || !props.isFlexibleFlow,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.enableImportFromFundData,
            body =
              "When turn on, fund managers can invite investors with form data, contacts and provided documents from Investor Data Management"
          )
        ),
        FundSubSetupSwitch(
          !props.featureSwitch.disableAbilityToCountersign,
          FundSubFeatureSwitchConstants.enableAbilityToCounterSign,
          checked => props.onChangeSwitch(props.featureSwitch.copy(disableAbilityToCountersign = !checked)),
          shouldHide = !state.expandAll
        )
      )
    }

    private def hideAllInvitationButtonOnGpSideHandoff = <.div(
      <.div(
        "When turn on all invitation UI on GP side will be hidden. This is used on fund where all invitation should happen via API except LP invite collaborator."
      ),
      <.div(
        tw.mt12,
        "The disabled invitation components are listed below.",
        <.ul(
          tw.ml24,
          <.li(
            "Invitation Button on the Fund Sub dashboard."
          ),
          <.li(
            "Invitation Button on the ",
            <.span(tw.fontSemiBold, "Permission"),
            " tab."
          ),
          <.li(
            "Create duplicate subscription feature (GP side)"
          ),
          <.li(
            "Invest from additional entity feature (Investor side)"
          ),
          <.li(
            "Invite collaborator in investor detail drawer"
          )
        )
      )
    )

    private def enterpriseSwitchSection(props: Props, state: State): VdomElement = {
      <.div(
        <.div(tw.fontSemiBold.mb4, "Enterprise package's features only"),
        FundSubSetupSwitch(
          props.featureSwitch.enableLpProfile,
          FundSubFeatureSwitchConstants.enableLpProfile,
          checked =>
            props.onChangeSwitch(if (checked) {
              props.featureSwitch.copy(enableLpProfile = checked, enableAutoPrefillForLp = false)
            } else {
              props.featureSwitch.copy(enableLpProfile = false)
            }),
          handoffContent = FundSubFeatureSwitchHandoff(
            header = "Enable LP to autofill from profile or past subscription",
            body = "When turn on, LP will have option to autofill form from profile or from past subscriptions.",
            images = Seq("enableBothLPAutofillFromProfile_pastSubscriptions.png")
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableAutoPrefillForLp,
          FundSubFeatureSwitchConstants.enableAutoPrefillForLp,
          checked =>
            props.onChangeSwitch(if (checked) {
              props.featureSwitch.copy(enableAutoPrefillForLp = checked, enableLpProfile = false)
            } else {
              props.featureSwitch.copy(enableAutoPrefillForLp = checked)
            }),
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.enableAutoPrefillForLp,
            body = "When turn on, LP would be able to auto-fill form using past subscriptions.",
            images = Seq("enableLPAutofillFromPastSubscriptionsOnly.png")
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableAutoPrefill,
          FundSubFeatureSwitchConstants.enableAutoPrefill,
          checked => props.onChangeSwitch(props.featureSwitch.copy(enableAutoPrefill = checked)),
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.enableAutoPrefill,
            body = "When turn on, GPs can autofill from past subscription of an investors.",
            images = Seq("enableGPFromPastSubscriptions.png")
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableOntologyFormMatching,
          FundSubFeatureSwitchConstants.enableOntologyFormMatching,
          checked => props.onChangeSwitch(props.featureSwitch.copy(enableOntologyFormMatching = checked)),
          shouldHide = !state.expandAll
        ),
        FundSubSetupSwitch(
          props.featureSwitch.useNewGaiaImportOrder,
          FundSubFeatureSwitchConstants.useNewGaiaImportOrder,
          checked => props.onChangeSwitch(props.featureSwitch.copy(useNewGaiaImportOrder = checked)),
          shouldHide = !state.expandAll
        ),
        FundSubSetupSwitch(
          isChecked = !props.featureSwitch.disableSelfServiceExport,
          label = FundSubFeatureSwitchConstants.enableSelfServiceExport,
          onChange = checked => props.onChangeSwitch(props.featureSwitch.copy(disableSelfServiceExport = !checked)),
          shouldHide = !state.expandAll
        ),
        FundSubSetupSwitch(
          isChecked = props.featureSwitch.hideAllInvitationButtonOnGpSide,
          label = FundSubFeatureSwitchConstants.hideAllInvitationButtonOnGpSide,
          onChange = checked => props.onChangeSwitch(props.featureSwitch.copy(hideAllInvitationButtonOnGpSide = checked)),
          handoffContent = FundSubFeatureSwitchHandoff(
            header = "Hide all invitation buttons on GP side",
            body = hideAllInvitationButtonOnGpSideHandoff
          ),
          shouldHide = !state.expandAll
        ),
        FundSubSetupSwitch(
          isChecked = props.featureSwitch.enableRia,
          label = FundSubFeatureSwitchConstants.enableRia,
          onChange = checked => props.onChangeSwitch(props.featureSwitch.copy(enableRia = checked)),
          shouldHide = !state.expandAll || !props.isFlexibleFlow,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.enableRia,
            body = <.div(
              "Toggle on this switch will allow the fund to invite advisors and manage invited advisor in the fund"
            )
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableSideLetter,
          FundSubFeatureSwitchConstants.enableSideLetter,
          checked => props.onChangeSwitch(props.featureSwitch.copy(enableSideLetter = checked)),
          shouldHide = !state.expandAll || !props.isFlexibleFlow
        ),
        FundSubSetupSwitch(
          props.featureSwitch.forcingFundManagersToReceiveAllEmailNotifications,
          FundSubFeatureSwitchConstants.forcingFundManagersToReceiveAllEmailNotifications,
          checked =>
            props.onChangeSwitch(props.featureSwitch.copy(forcingFundManagersToReceiveAllEmailNotifications = checked)),
          shouldHide = !state.expandAll || !props.isFlexibleFlow,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.forcingFundManagersToReceiveAllEmailNotifications,
            body = <.div(
              "Toggle on this switch will prevent fund manager from unsubscribe any email notification." +
                " They can still change frequency of some."
            )
          )
        ),
        FundSubSetupSwitch(
          props.featureSwitch.enableDataExtractDraftFormLog,
          FundSubFeatureSwitchConstants.enableDataExtractDraftFormLog,
          checked => props.onChangeSwitch(props.featureSwitch.copy(enableDataExtractDraftFormLog = checked)),
          shouldHide = !state.expandAll || !props.isFlexibleFlow,
          handoffContent = FundSubFeatureSwitchHandoff(
            header = FundSubFeatureSwitchConstants.enableDataExtractDraftFormLog,
            body = <.div("Enable log for the draft form")
          )
        )
      )
    }

    private def renderDownloadMetrics = {
      Modal(
        title = "Download switch usage metrics",
        renderTarget = open => Button(style = Button.Style.Text(), onClick = open)("Download usage metrics"),
        renderContent = close => DownloadFeatureSwitchUsageMetricModal(close)()
      )()
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
