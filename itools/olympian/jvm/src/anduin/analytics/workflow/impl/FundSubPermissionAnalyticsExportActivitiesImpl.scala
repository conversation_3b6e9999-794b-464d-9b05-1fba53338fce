// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.analytics.workflow.impl

import io.temporal.failure.ApplicationFailure
import zio.ZIO

import anduin.analytics.fundsub.fund.FundAnalyticExportService
import anduin.analytics.workflow.interf.FundSubPermissionAnalyticsExportActivities
import anduin.analytics.workflow.{EmptyParams, EmptyResponse, ExportSingleFundParams}
import anduin.fdb.record.FDBRecordDatabase
import anduin.fundsub.models.FundSubModelStoreOperations
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.fundsub.dashboard.AllFundIdsResponse

final case class FundSubPermissionAnalyticsExportActivitiesImpl(
  fundAnalyticExportService: FundAnalyticExportService
)(
  using val temporalWorkflowService: TemporalWorkflowService
) extends FundSubPermissionAnalyticsExportActivities {

  override def getAllFundIds(params: EmptyParams): AllFundIdsResponse = {
    val task = FDBRecordDatabase
      .transact(FundSubModelStoreOperations.Production) {
        _.getAllFundSubPublicModel.map(_.collect {
          case fundModel
              if !fundModel.status.isFundSubArchived && fundModel.fundType.isProduction && fundModel.lpFlowType.isFlexible =>
            fundModel.fundSubId
        })
      }
      .map(AllFundIdsResponse(_))
    temporalWorkflowService.executeTask(task, "FundSubPermissionAnalyticsExportActivities#getAllFundIds")
  }

  override def exportFundSubPermissionData(params: ExportSingleFundParams): EmptyResponse = {
    val task = fundAnalyticExportService
      .exportFundSubPermissionDataToS3(
        customS3ExportPathOpt = params.storageExportParams.flatMap(_.customS3Path),
        fundId = params.fundId,
        actor = params.userId
      )
      .as(EmptyResponse())
      .tapError { case err: Throwable =>
        ZIO.fail(ApplicationFailure.newNonRetryableFailure(err.getMessage, err.getClass.getName))
      }

    temporalWorkflowService.executeTask(
      task,
      "FundSubPermissionAnalyticsExportActivities#exportFundSubPermissionData"
    )
  }

}
