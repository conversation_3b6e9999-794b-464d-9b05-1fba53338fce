// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.service

import java.time.ZonedDateTime

import zio.implicits.*
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.customdomain.CustomDomainService
import anduin.dataroom.flow.DataRoomStateStoreOperations
import anduin.dataroom.participant.{DataRoomParticipantRoleOperations, DataRoomParticipantService}
import anduin.dataroom.service.DataRoomServiceUtils
import anduin.entity.model.*
import anduin.entity.repository.{EntityModelStoreOperations, EntityRepository}
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.fundsub.admin.FundSubAdminGeneral
import anduin.fundsub.models.{FundSubLpModelStoreOperations, FundSubModelStoreOperations}
import anduin.fundsub.user.FundSubUserService
import anduin.id.entity.EntityId
import anduin.id.fundsub.{FundSubAdminGeneralId, FundSubAdminRestrictedId}
import anduin.id.offering.OfferingId
import anduin.id.role.portal.PortalSectionId
import anduin.link.LinkGeneratorService
import anduin.model.common.user.UserId
import anduin.model.id.EntityIdFactory
import anduin.model.user.FullName
import anduin.portal.*
import anduin.portal.email.AdminPortalInviteNewUserToEntityEmailGenerate
import anduin.portaluser.PortalUserService
import anduin.service.GeneralServiceException
import anduin.service.entity.common.EntityUtils
import anduin.service.entity.invitation.EntityInvitationService
import anduin.service.entity.{EntityService, EntityServiceUtils}
import anduin.utils.ScalaUtils
import com.anduin.stargazer.service.email.EmailSenderService
import com.anduin.stargazer.service.fundsub.operation.FundSubOperationEntityService
import com.anduin.stargazer.service.orgbilling.OrgBillingService
import com.anduin.stargazer.service.orgbilling.storage.OrgBillingStoreOperations
import com.anduin.stargazer.service.utils.ZIOUtils

final case class PortalService(
  entityService: EntityService,
  entityInvitationService: EntityInvitationService,
  emailSenderService: EmailSenderService,
  orgBillingService: OrgBillingService,
  userProfileService: UserProfileService,
  portalUserService: PortalUserService,
  customDomainService: CustomDomainService,
  dataRoomParticipantService: DataRoomParticipantService,
  fundSubUserService: FundSubUserService,
  fundSubOperationEntityService: FundSubOperationEntityService
)(
  using val linkGeneratorService: LinkGeneratorService
) {

  private given userProfileServiceIm: UserProfileService = userProfileService

  def createEntity(
    params: CreateEntityFromPortalParams
  ): Task[CreateEntityFromPortalResponse] = {
    for {
      userIds <- createZombieUsers(userProfileService, params.members)
      entityId = EntityIdFactory.unsafeRandomId
      _ <- entityService.createEntityInternal(
        name = params.entityName,
        alias = params.entityAlias,
        entityTrackingType = params.entityTrackingType,
        members = for {
          (email, userId) <- userIds
          member <- params.members.find(_.email == email)
        } yield userId -> member.role,
        customerTrackingId = params.customerTrackingId,
        entityId = entityId,
        emailDomains = params.emailDomains,
        skipPermission = true,
        httpContext = None
      )
      _ <- sendInvitationToEntityEmail(
        getUserListToSendEmail(userIds, params.members),
        entityId,
        params.entityAlias,
        params.customizedEmailParams
      )
    } yield CreateEntityFromPortalResponse(entityId)
  }

  def inviteEntityMembers(
    params: InviteEntityMemberParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      userIds <- createZombieUsers(userProfileService, params.members)
      entityRestrictedModel <- EntityRepository.getEntityRestricted(params.entityId)
      newMembers <- ZIO.succeed(userIds.filterNot { case (_, userId) =>
        entityRestrictedModel.membersInfo.keySet.contains(userId)
      })
      _ <- ZIO.logInfo(s"Inviting ${newMembers.keySet} to entity ${params.entityId}")

      entityModel <- EntityServiceUtils.execute(_.getEntityModel(params.entityId))
      _ <- addMemberToEntity(
        params,
        actor,
        newMembers
      )
      _ <- sendInvitationToEntityEmail(
        getUserListToSendEmail(newMembers, params.members),
        params.entityId,
        entityModel.alias,
        params.customizedEmailParams
      )
    } yield ()
  }

  private def addMemberToEntity(
    params: InviteEntityMemberParams,
    actor: UserId,
    newMembers: Map[String, UserId]
  ) = {
    val newEntityMembersInfo = for {
      (email, userId) <- newMembers
      member <- params.members.find(_.email == email)
    } yield {
      userId -> EntityMemberInfo(
        member.role,
        joinedDate = Some(ZonedDateTime.now)
      )
    }
    ZIO.foreachPar(newEntityMembersInfo.toSeq) { case (userId, memberInfo) =>
      entityInvitationService.addEntityMember(
        actor,
        userId,
        memberInfo.role,
        params.entityId,
        skipPermission = true
      )
    }
  }

  def removeEntityMember(
    entityId: EntityId,
    userId: UserId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Removing $userId from $entityId by admin portal")
      _ <- entityInvitationService.removeMember(
        removedUser = userId,
        entityId = entityId,
        actor = actor,
        isAnduinAdmin = true
      )
    } yield ()
  }

  def deleteEntity(entityId: EntityId, actor: UserId): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor is deleting entity $entityId")
      (entityResModelOpt, entitySigOpt) <- FDBRecordDatabase.transact(EntityModelStoreOperations.Production) { ops =>
        for {
          entityResModelOpt <- ops.getOptEntityRestrictedModel(entityId)
          entitySigOpt <- ops.getOptEntitySignatureModel(entityId)
        } yield entityResModelOpt -> entitySigOpt
      }
      fundCount = entityResModelOpt.map(_.fundSubIds.size).getOrElse(0)
      signatureModuleCount = entitySigOpt.map(_.signatureModules.size).getOrElse(0)
      _ <- ZIOUtils.validate(fundCount == 0 && signatureModuleCount == 0) {
        GeneralServiceException(
          s"Entity is not safe to delete: $fundCount fund(s) and $signatureModuleCount signature module(s)"
        )
      }
      _ <- ZIO.foreach(entityResModelOpt.map(_.membersInfo.keys.toSeq).getOrElse(Seq.empty)) { userId =>
        removeEntityMember(
          entityId,
          userId,
          actor
        )
      }
      _ <- FDBRecordDatabase.transact(EntityModelStoreOperations.Production) {
        _.deleteEntity(entityId)
      }
    } yield ()
  }

  private def sendInvitationToEntityEmail(
    invitees: List[UserId],
    entityId: EntityId,
    entityAlias: String,
    customizedEmailParams: CustomizedEntityInvitationEmailParams
  ) = {
    ZIO.foreachPar(invitees) { userId =>
      emailSenderService.enqueue(
        AdminPortalInviteNewUserToEntityEmailGenerate(
          userId,
          entityId,
          entityAlias,
          customizedEmailParams
        ),
        entityId
      )
    }
  }

  private def getUserListToSendEmail(
    userIds: Map[String, UserId],
    members: List[EntityMemberParams]
  ): List[UserId] = {
    members.filter(_.sendInvitationEmail).flatMap(member => userIds.get(member.email))
  }

  // Entities
  def getAllEntityModels: Task[GetAllEntityModelsResponse] = {
    for {
      entities <- entityService.getEntityModels
    } yield GetAllEntityModelsResponse(entities)
  }

  def getAllEntities: Task[GetAllEntitiesResponse] = {
    for {
      entityIds <- entityService.getEntityIds
      entities <- EntityServiceUtils
        .execute { entityOps =>
          RecordIO.parTraverseN(4)(entityIds) { entityId =>
            entityOps.getEntityModels(entityId).map(entityId -> _)
          }
        }
    } yield GetAllEntitiesResponse(entities.map { case (entityId, (entityModel, entityRestrictedModel)) =>
      EntityDisplayData(
        entityId,
        entityModel.name,
        entityModel.alias,
        entityModel.customerTrackingId,
        entityRestrictedModel.fundSubIds.size,
        entityModel.emailDomains,
        entityModel.entityTrackingType
      )
    })
  }

  def queryEntityId(
    entityId: EntityId
  ): Task[QueryEntityIdResponse] = {
    for {
      (entityModel: EntityModel, entityResModel: EntityRestrictedModel) <- entityService.queryEntityId(entityId)
      funds <- fundSubOperationEntityService.getEntityFundsInternal(entityId)
      memberInfos <- userProfileService.batchGetUserInfos(entityResModel.membersInfo.keySet)
    } yield {
      QueryEntityIdResponse(
        entityModel,
        entityResModel,
        funds,
        memberInfos
      )
    }
  }

  def getOrgBillingModel(
    params: GetOrgBillingModelParams
  ): Task[GetOrgBillingModelResponse] = {
    for {
      resp <- orgBillingService.getOrgBillingModel(params.entityId)
    } yield GetOrgBillingModelResponse(resp)
  }

  def changeDataRoomPlan(
    params: ChangeDataRoomPlanParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- orgBillingService.changeDataRoomPlan(
        params.entityId,
        OrgBillingStoreOperations.toDataRoomPackageProto(params.plan),
        actor
      )
    } yield ()
  }

  def getAllEntityAndFundSubOfUser(
    email: String,
    userIdStr: String
  ): Task[GetUserEntityAndFundSubResponse] = {
    for {
      userIdEmailOpt <-
        if (email.nonEmpty) { // get by email
          userProfileService
            .getUserFromEmailAddress(email)
            .map { case (userId, _) =>
              Some(userId -> email)
            }
            .onErrorHandleWith(_ => ZIO.attempt(None))
        } else { // get by userId
          val userId = UserId(userIdStr)
          userProfileService
            .getEmailAddress(userId)
            .map { emailAddress =>
              Some(userId -> emailAddress.address)
            }
            .onErrorHandleWith(_ => ZIO.attempt(None))
        }
      resp <- userIdEmailOpt.fold(
        ZIO.attempt(GetUserEntityAndFundSubResponse(userIdEmailOpt = None))
      ) { case (userId, email) =>
        for {
          // Get all entities of user
          joinedEntityIds <- EntityUtils.getAllEntityIdOfUser(userId)
          joinedEntities <- EntityServiceUtils.execute { entityOps =>
            RecordIO.parTraverseN(12)(joinedEntityIds) { entityId =>
              entityOps.getEntityModel(entityId).map(entityId -> _)
            }
          }
          // Get all fundsubs of user
          fundSubIdsAsAdmin <- fundSubUserService.getUserFundSubAdmin(userId)
          lpIds <- fundSubUserService.getUserFundSubLp(userId)
          fundModels <- ZIO.foreachPar(fundSubIdsAsAdmin ++ lpIds.map(_.parent)) { fundSubId =>
            FDBRecordDatabase.transactC(FundSubModelStoreOperations.Production) { (ctx, ops) =>
              for {
                fundSubModel <- ops.getFundSubPublicModel(fundSubId)
                adminResModel <- ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
                adminGeneralModel <- FundSubAdminGeneral.fundSubAdminGeneralSubspace
                  .getValue(FundSubAdminGeneralId(fundSubId))
                  .recordIO(ctx)
              } yield (fundSubModel, adminResModel, adminGeneralModel)
            }
          }
          lpModels <- ZIO.foreachPar(lpIds) { lpId =>
            FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
              ops.getFundSubLpModel(lpId)
            }
          }
        } yield {
          val allUserFundData = for {
            (fundSubModel, adminResModel, adminGeneralModel) <- fundModels
            fundSubId = fundSubModel.fundSubId
            isFundAdmin = adminGeneralModel.admins.exists(_._1 == userId)
            lps = lpModels.flatMap { lpModel =>
              val lpId = lpModel.fundSubLpId
              val isCurrentFundLp = lpModel.fundSubLpId.parent == fundSubId && adminResModel.lpIds.contains(lpId)
              val isMainLp = lpModel.mainLp == userId
              val isCollaborator = lpModel.collaborators.contains(userId)
              Option.when(isCurrentFundLp && (isMainLp || isCollaborator)) {
                lpId -> isMainLp
              }
            }
          } yield {
            UserFundData(
              fundSubId = fundSubId,
              fundName = fundSubModel.fundName,
              isFundAdmin = isFundAdmin,
              lps = lps.toSeq
            )
          }
          GetUserEntityAndFundSubResponse(
            Some(userId -> email),
            joinedEntities.toMap,
            allUserFundData.toSeq
          )
        }
      }
    } yield resp
  }

  def getUserEntityAndDataRoom(
    params: GetUserEntityAndDataRoomParams
  ): Task[GetUserEntityAndDataRoomResponse] = {
    for {
      joinedEntityIds <- EntityUtils.getAllEntityIdOfUser(params.userId)
      joinedEntities <- EntityServiceUtils.execute { entityOps =>
        RecordIO.parTraverseN(16)(joinedEntityIds) { entityId =>
          entityOps.getEntityModel(entityId).map(entityId -> _)
        }
      }
      joinedDataRoomIds <- dataRoomParticipantService.getParticipatingDataRooms(params.userId)
      dataRoomStates <- FDBRecordDatabase.transact(
        FDBOperations[(DataRoomStateStoreOperations, DataRoomParticipantRoleOperations)].Production
      ) { (stateOps, roleOps) =>
        RecordIO.parTraverseN(16)(joinedDataRoomIds) { dataRoomId =>
          for {
            state <- stateOps.getState(dataRoomId)
            roleMap <- roleOps.getAllParticipantRoleMap(dataRoomId)
          } yield state -> roleMap
        }
      }
      userIds = dataRoomStates.flatMap(_._2.keySet).toSet
      userInfos <- DataRoomServiceUtils.batchGetUserInfos(userIds, None)
    } yield GetUserEntityAndDataRoomResponse(
      userInfoMap = userInfos,
      joinedEntities = joinedEntities.toMap,
      joinedDataRooms = dataRoomStates.map { (createdState, roleMap) =>
        UserDataRoomData(
          createdState.dataRoomWorkflowId,
          createdState.name,
          roleMap
        )
      }
    )
  }

  def updateEntitySetting(
    params: UpdateEntitySettingFromPortalParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- entityService.updateSettingInternal(
        entityId = params.entityId,
        name = params.entityName,
        alias = params.entityAlias,
        customerTrackingId = params.customerTrackingId,
        entityTrackingType = params.entityTrackingType,
        emailDomains = params.emailDomains,
        actor,
        skipPermission = true
      )
    } yield ()
  }

  // Fund sub
  def fetchFundSubs(fetchFundSubMode: FetchFundSubMode, actor: UserId): Task[FetchFundSubResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is fetching all funds")
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      _ <- ZIOUtils.when(ScalaUtils.isMatch[FetchFundSubMode.ByEnvironment](fetchFundSubMode)) {
        portalUserService.validateReadPermission(actor, PortalSectionId.Environment)
      }
      funds <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        for {
          fundSubPublicModels <- {
            fetchFundSubMode match {
              case FetchFundSubMode.All => ops.getAllFundSubPublicModel
              case FetchFundSubMode.ByEnvironment(None) =>
                ops.getAllFundSubPublicModel.map(_.filter(_.environmentIdOpt.isEmpty))
              case FetchFundSubMode.ByEnvironment(someEnvironmentId) => ops.queryFundsByEnvironment(someEnvironmentId)
            }
          }
          funds <- RecordIO.parTraverseN(64)(
            fundSubPublicModels.filterNot(_.status.isFundSubArchived)
          ) { fundSubPublicModel =>
            val fundSubId = fundSubPublicModel.fundSubId
            ops
              .getOptFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
              .map(_.map { adminRestrictedModel =>
                FundSubDisplayData(
                  fundSubId = fundSubId,
                  fundName = fundSubPublicModel.fundName,
                  startDate = fundSubPublicModel.startDate,
                  investorCount = adminRestrictedModel.lpIds.size,
                  fundType = fundSubPublicModel.fundType,
                  useNewForm = adminRestrictedModel.formVersions.nonEmpty,
                  lpFlowType = fundSubPublicModel.lpFlowType,
                  isClosed = fundSubPublicModel.closingConfig.exists(_.isClosed),
                  packageType = fundSubPublicModel.packageType,
                  flowType = fundSubPublicModel.flowType,
                  entityId = fundSubPublicModel.investorEntity,
                  enabledPermission = fundSubPublicModel.featureSwitch.exists(_.enableFundPermission)
                )
              })
          }
        } yield funds.flatten
      }
    } yield FetchFundSubResponse(funds)
  }

  def getFundSubAppUrl: Task[String] = {
    for {
      fundSubUrl <- customDomainService.resolveOfferingCustomDomainPath(
        offeringId = OfferingId.FundSub,
        path = ""
      )
    } yield fundSubUrl
  }

  private def createZombieUsers(
    userProfileService: UserProfileService,
    members: List[EntityMemberParams]
  ): Task[Map[String, UserId]] = {
    ZIO
      .foreach(members) { entityMember =>
        userProfileService
          .maybeCreateZombieUser(
            entityMember.email,
            FullName(entityMember.firstname, entityMember.lastname)
          )
          .map(entityMember.email -> _.userId)
      }
      .map(_.toMap)
  }

  def updateEntityTrackingIds(
    param: UpdateEntityTrackingIdsParams
  ): Task[Unit] = {
    for {
      _ <- entityService.updateTrackingIds(param.trackingIds)
    } yield ()
  }

}
