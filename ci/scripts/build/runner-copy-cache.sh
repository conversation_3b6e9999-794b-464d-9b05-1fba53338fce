#!/bin/env bash

here=`cd $(dirname $BASH_SOURCE); pwd`
root=`readlink -f $here/../../../`
home=`cd ~/; pwd`

if [ -z "$BRANCH_NAME" ]; then
    echo "Missing BRANCH_NAME env var"
    exit 1
fi

S3_DIR_URL="s3://anduin-infra-storage/eks-cicd/stargazer/$BRANCH_NAME"
BASENAME="stargazer.cache.tar.zst"

function pushCache {
  fileName=$BASENAME
  localFilePath=$root/$fileName
  s3FilePath=$S3_DIR_URL/$fileName

  cd $home
  tar -I zstd --posix -cf $localFilePath .cache .yarn
  aws s3 cp --no-progress $localFilePath $s3FilePath
  rm -f $localFilePath
}

function pullCache {
  fileName=$BASENAME
  localFilePath=$root/$fileName
  s3FilePath=$S3_DIR_URL/$fileName

  cd $home
  echo "[INFO] - Start downloading ..."
  aws s3 cp --no-progress $s3FilePath $localFilePath
  echo "[INFO] - Start decompressing ..."
  tar -I zstd --posix -xf $localFilePath
  rm -f $localFilePath
  echo "[INFO] - Finish pull !!!"
}

case $1 in
  push)
    shift
    pushCache "$@"
    ;;
  pull)
    shift
    pullCache "$@"
    ;;
  *)
    echo "unknown command"
    exit 1
esac
