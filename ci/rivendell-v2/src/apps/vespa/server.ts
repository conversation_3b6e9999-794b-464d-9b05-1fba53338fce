import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import { IServicePort } from "kubernetes-models/v1";
import { StatefulSet } from "kubernetes-models/apps/v1";
import { PodDisruptionBudget } from "kubernetes-models/policy/v1";
import * as com from "../common";

import * as vCommon from "./common";
import * as vCfg from "./configs";

export default function (spec: vCommon.VespaClusterSpec) {
  return async function (ctx: Context): Promise<K8sObject[]> {
    const name = spec.name;
    const headlessSvc = vCommon.getHeadlessSvc(name);
    const commonLabels = { app: name };

    const ports: Record<string, number> = {
      container: 8080,
      config: 19071,
    };

    // need to be full domain
    const configServers = vCfg.getConfigServerAddresses(spec.nodes);

    const pdb = new PodDisruptionBudget({
      metadata: { name },
      spec: {
        maxUnavailable: 1,
        selector: {
          matchLabels: commonLabels,
        },
      },
    });

    const sts = new StatefulSet({
      metadata: { name },
      spec: {
        serviceName: headlessSvc,
        replicas: spec.nodes.length,
        updateStrategy: {
          type: "RollingUpdate",
        },
        podManagementPolicy: "OrderedReady",
        selector: {
          matchLabels: commonLabels,
        },
        template: {
          metadata: {
            labels: commonLabels,
          },
          spec: {
            nodeSelector: com.misc.productionGuard(
              ctx,
              com.node.nodeVespa(ctx),
              com.node.nodeDatabase(ctx),
            ),
            imagePullSecrets: com.misc.getPullSecrets(ctx),
            securityContext: {
              fsGroup: 1000, // ref: https://github.com/vespa-engine/docker-image/blob/master/Dockerfile#L54
            },
            containers: [
              {
                name,
                image: ConfigUtils.getDockerPublicImage(ctx.configs, "vespa"),
                imagePullPolicy: "IfNotPresent",
                args: ["configserver,services"],
                env: [
                  k8s.envFromSource(
                    "VESPA_CONFIGSERVERS",
                    configServers.join(","),
                  ),
                  // k8s.envFromValue("TRACE_JVM_STARTUP", "true"),
                  // k8s.envFromValue("DEBUG_JVM_STARTUP", "true"),
                ],
                securityContext: {
                  runAsUser: 1000,
                },
                ports: k8s.containerPortsFromMap(ports),
              },
            ],
            tolerations: [{
              key: "Vespa",
              operator: "Exists",
              effect: "NoSchedule",
            }],
            topologySpreadConstraints: [{
              maxSkew: 1,
              topologyKey: "topology.kubernetes.io/zone",
              whenUnsatisfiable: "ScheduleAnyway",
              labelSelector: {
                matchLabels: commonLabels,
              },
            }, {
              maxSkew: 1,
              topologyKey: "kubernetes.io/hostname",
              whenUnsatisfiable: "ScheduleAnyway",
              labelSelector: {
                matchLabels: commonLabels,
              },
            }],
          },
        },
      },
    });

    if (ConfigUtils.isPersistData(ctx.configs)) {
      com.storage.storageAsClaimTemplate(ctx, sts, {
        name: "logs",
        accessModes: ["ReadWriteOnce"],
        size: "5Gi",
        mounts: [{
          container: name,
          path: "/opt/vespa/logs",
        }],
      });
      com.storage.storageAsClaimTemplate(ctx, sts, {
        name: "var",
        accessModes: ["ReadWriteOnce"],
        size: com.misc.productionGuard(ctx, "50Gi", "5Gi")!,
        mounts: [{
          container: name,
          path: "/opt/vespa/var",
        }],
      });
    }

    const svcPorts: IServicePort[] = k8s.servicePortsFromMap(ports);
    return [
      pdb,
      sts,
      k8s.serviceFromStatefulSet(sts, {
        probeTcp: true,
        ports: svcPorts,
      }),
      k8s.serviceFromStatefulSet(sts, {
        clusterIP: "None",
        ports: svcPorts,
        publishNotReadyAddresses: true,
      }),
    ];
  };
}
