import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as k8s from "@utils/k8s";
import { IServicePort } from "kubernetes-models/v1";
import { StatefulSet } from "kubernetes-models/apps/v1";
import { Job } from "kubernetes-models/batch/v1";

import { tidbSecretRef } from "./common";
import * as com from "../common";
import { ConfigUtils } from "@configutils";

async function fakeJob(ctx: Context, name: string): Promise<K8sObject[]> {
  const job = new Job({
    metadata: { name },
    spec: {
      template: {
        metadata: { name },
        spec: {
          restartPolicy: "Never",
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [{
            name: "setup",
            image: "ubuntu",
            imagePullPolicy: "IfNotPresent",
            args: ["bash", "-c", "exit 0"],
          }],
        },
      },
    },
  });
  return [job];
}

export async function setupJob(ctx: Context): Promise<K8sObject[]> {
  return await fakeJob(ctx, "tidb-setup");
}

export async function setupCdcJob(ctx: Context): Promise<K8sObject[]> {
  return await fakeJob(ctx, "tidb-setup-changefeed");
}

export async function server(ctx: Context): Promise<K8sObject[]> {
  const name = "tidb-fake-mysql";
  const commonLabels = { name };

  const sts = new StatefulSet({
    metadata: { name },
    spec: {
      serviceName: `${name}-headless`,
      replicas: 1,
      selector: {
        matchLabels: commonLabels,
      },
      template: {
        metadata: {
          labels: commonLabels,
        },
        spec: {
          nodeSelector: com.node.nodeDatabase(ctx),
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [{
            name: "mysql",
            image: ConfigUtils.getDockerPublicImage(ctx.configs, "mysql"),
            imagePullPolicy: "IfNotPresent",
            env: [
              k8s.envFromSource("MYSQL_ROOT_PASSWORD", tidbSecretRef("root")),
              com.env.envFromGondorConfig(
                "MYSQL_USER",
                "STARGAZER_SERVICES_TIDB_USER",
              ),
              k8s.envFromSource("MYSQL_PASSWORD", tidbSecretRef("gondor")),
              com.env.envFromGondorConfig(
                "MYSQL_DATABASE",
                "STARGAZER_SERVICES_TIDB_DB",
              ),
            ],
          }],
        },
      },
    },
  });

  if (ConfigUtils.isPersistData(ctx.configs)) {
    com.storage.storageAsClaimTemplate(ctx, sts, {
      name: "data",
      accessModes: ["ReadWriteOnce"],
      size: "10Gi",
      mounts: [{
        container: "mysql",
        path: "/var/lib/mysql",
      }],
    });
  }

  const svcPorts: IServicePort[] = [{
    name: "mysql",
    port: 4000, // matching tidb port
    targetPort: 3306,
    protocol: "TCP",
  }];

  return [
    sts,
    k8s.serviceFromStatefulSet(sts, {
      svcName: "tidb-tidb",
      ports: svcPorts,
    }),
    k8s.serviceFromStatefulSet(sts, {
      clusterIP: "None",
      ports: svcPorts,
    }),
  ];
}
