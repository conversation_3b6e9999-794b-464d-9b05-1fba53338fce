import { Context } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import { IPodSpec } from "kubernetes-models/v1";

function combineNodeSelectors(
  ...overridens: IPodSpec["nodeSelector"][]
): IPodSpec["nodeSelector"] | undefined {
  return (overridens || []).reduce((accum, it) => {
    return Object.assign(accum || {}, it);
  }, undefined);
}

export function nodeTypeSelector(nodeType: string): IPodSpec["nodeSelector"] {
  return {
    "k8s/node-type": nodeType,
  };
}

export function nodeGroupSelector(nodeGroup: string): IPodSpec["nodeSelector"] {
  return {
    "k8s/node-group": nodeGroup,
  };
}

export function nodeEnvSelector(ctx: Context): IPodSpec["nodeSelector"] {
  return {
    "k8s/node-environment": ConfigUtils.getEnvType(ctx.configs),
  };
}

export function nodeDatabase(ctx: Context): IPodSpec["nodeSelector"] {
  if (ConfigUtils.isLocal(ctx.configs)) {
    return undefined;
  }
  return combineNodeSelectors(
    nodeTypeSelector("app"),
    nodeGroupSelector("database"),
    nodeEnvSelector(ctx),
  );
}

export function nodeVespa(ctx: Context): IPodSpec["nodeSelector"] {
  if (ConfigUtils.isLocal(ctx.configs)) {
    return undefined;
  }
  return combineNodeSelectors(
    nodeTypeSelector("app"),
    nodeGroupSelector("vespa"),
    nodeEnvSelector(ctx),
  );
}

export function nodeGondor(ctx: Context): IPodSpec["nodeSelector"] {
  if (ConfigUtils.isLocal(ctx.configs)) {
    return undefined;
  }
  return combineNodeSelectors(
    nodeTypeSelector("app"),
    nodeGroupSelector("service"),
    nodeEnvSelector(ctx),
  );
}
