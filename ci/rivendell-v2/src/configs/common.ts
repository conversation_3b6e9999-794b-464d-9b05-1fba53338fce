export default {
  awsRegion: "",
  awsAccountId: "************",

  stargazerImage: "stargazer-canary",
  stargazerSprintTag: "latest",

  envName: "",
  envType: "",
  envNukable: false,
  kubeCtx: "",
  k8sNamespace: "",
  k8sServiceSuffix: "",

  dockerPullSecrets: [],
  dockerGithubImages: {
    foundationDbStats: "ghcr.io/anduintransaction/fdb-stats:sha-8737769",
    foundationDbStatsLegacy: "ghcr.io/anduintransaction/fdb-stats:sha-08e5e3b",
    zot: "ghcr.io/project-zot/zot-linux-amd64:v2.1.1",
  },
  tidb: {
    mirror: "pingcap",
    version: "v6.5.1",
  },
  dockerPublicImages: {
    nginx: "nginx:1.21.0",
    postgres: "postgres:10.5",
    postgres11: "postgres:11.9",
    postgres14: "postgres:14.4",
    postgres15: "postgres:15.3",
    redis: "redis:4.0.9",
    redis7: "redis:7.2.3",
    memcached: "memcached:1.6.24-alpine",
    rabbitMQ: "rabbitmq:3.7.15",
    cassandra: "cassandra:3.11.19",
    temporalAdminTools: "temporalio/admin-tools:1.27.1-tctl-1.18.2-cli-1.3.0",
    temporalServer: "temporalio/server:1.27.1.0",
    temporalWeb: "temporalio/ui:2.36.0",
    dgraph: "dgraph/dgraph:v24.0.5",
    dgraphRatel: "dgraph/ratel:v21.03.2",
    tykPump: "tykio/tyk-pump-docker-pub:v1.11.0",
    truffleHog: "trufflesecurity/trufflehog:3.10.0",
    nats: "nats:2.10.26-alpine",
    edgedb: "edgedb/edgedb:4.5",
    openFga: "openfga/openfga:v1.5.9",
    svix: "svix/svix-server:v1.66.0",
    grafanaAgent: "grafana/alloy:v1.4.3",
    kafka3: "confluentinc/cp-kafka:7.8.0",
    vespa: "vespaengine/vespa:8.457.32",
    vector: "timberio/vector:0.42.0-distroless-libc",
    mysql: "mysql:9.1.0",
  },
  dockerUseRegionalEcr: true,
  dockerEcrImages: {
    awsCli: "infra/awscli:1.19.0",
    kafka: "infra/confluent-kafka:7.2.0.4",
    zookeeper: "infra/confluent-zookeeper:7.2.0.3",
    postgresWait: "infra/postgres-wait:11.9.2",
    postgresBackup: "infra/postgres-s3backup:15.6.3",
    onlyOfficeDocumentServer: "infra/documentserver:8.2.1.1.1",
    foundationDb: "infra/foundationdb:7.3.27.1",
    foundationDbLegacy: "infra/foundationdb:7.1.27.1",
    foundationDbS3Backup: "infra/foundationdb-s3backup:7.3.27.1",
    foundationDbS3BackupLegacy: "infra/foundationdb-s3backup:7.1.27.1",
    foundationDbWait: "infra/foundationdb-wait:7.3.27.1",
    foundationDbWaitLegacy: "infra/foundationdb-wait:7.1.27.1",
    timescaledb: "infra/timescaledb:2.14.2.3",
    scyllaWait: "infra/scylla-wait:4.4.2.1",
    fuseki: "infra/fuseki:4.4.0",
    graphqlAnalyzer: "infra/graphql-analyzer:1.4.6",
    cockroach: "infra/cockroachdb:v22.1.5.1",
    bifrost: "bifrost:18.0.1-43-03de0b",
    bifrostLocal: "bifrost:1.0.0-local",
    keycloakSidecar: "infra/keycloak-sidecar:1.0.0.1",
    mysqlSetup: "infra/mysql-setup:0.3.1",
    pinot: "infra/pinot:0.11.0.2",
    yugabyte: "infra/yugabyte:2.16.2.0-b41",
    pulsar: "infra/pulsar:2.11.0.1",
    debezium: "infra/debezium-server:1.9.7.Final-4",
    tidbChangefeed: "infra/tidb-changefeed:1.0.0.1",
    sidecarCommon: "infra/sidecar-common:1.3-jammy",
    tyk: "infra/tyk-gateway:v5.7.1.2",
  },
};
