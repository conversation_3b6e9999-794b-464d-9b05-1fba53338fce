import * as merge from "deepmerge";
import commonCfg from "../common";
import regionCfg from "../aws/us";

export default merge.all([commonCfg, regionCfg, {
  envType: "production",
  awsDbBackupBucket: "stargazer-db-backup",
  persistData: true,
  pvClass: "aws-standard",
  local: false,
  keycloakDev: false,
  onlyoffice: true,
  temporalCassandraReplicationFactor: 3,
  temporalEsEnabled: true,
  temporalEsHost: "temporal-es:80",
  enableProfiler: true,
  gondorFeatures: {
    topo: "standard",
    ses: true,
  },
  vault: true,
  vaultAwsRole: "gondor-public",
  vaultKubeRole: "gondor-public-v2",
  vaultCliAwsRole: "cli-gondor-public",
  vaultCliKubeRole: "gondor-public-v2",
  vespa: true,
}]);
