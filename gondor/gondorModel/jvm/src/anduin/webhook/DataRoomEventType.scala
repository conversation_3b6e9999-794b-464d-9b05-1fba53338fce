// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.webhook

import anduin.webhook.DataRoomEventType.DataRoomResource.*
import anduin.webhook.DataRoomEventType.DataRoomAction.*

enum DataRoomEventType(
  val resource: ResourceEnum,
  val action: ActionEnum,
  val description: String
) extends EventTypeEnum {
  override def appName: String = "dataroom"

  case User<PERSON>oin
      extends DataRoomEventType(
        resource = User,
        action = Join,
        description = "A user joins the data room"
      )

  case UserAddToGroup
      extends DataRoomEventType(
        resource = User,
        action = AddToGroup,
        description = "A user is added to a group in the data room"
      )

  case UserRemoveFromGroup
      extends DataRoomEventType(
        resource = User,
        action = RemoveFromGroup,
        description = "A user is removed from a group in the data room"
      )

  case UserRemoved
      extends DataRoomEventType(
        resource = User,
        action = Removed,
        description = "A user is removed from the data room"
      )

  case UserInvited
      extends DataRoomEventType(
        resource = User,
        action = Invited,
        description = "A user is invited to the data room"
      )

  case UserDeclineInvitation
      extends DataRoomEventType(
        resource = User,
        action = DeclineInvitation,
        description = "A user declines an invitation to the data room"
      )

}

object DataRoomEventType extends EventTypeEnumCompanion[DataRoomEventType] {

  private[webhook] enum DataRoomResource(val value: String) extends ResourceEnum {
    case User extends DataRoomResource("user")
  }

  private[webhook] enum DataRoomAction(val value: String) extends ActionEnum {
    case Join extends DataRoomAction("join")
    case Removed extends DataRoomAction("removed")
    case Invited extends DataRoomAction("invited")
    case DeclineInvitation extends DataRoomAction("decline_invitation")
    case AddToGroup extends DataRoomAction("add_to_group")
    case RemoveFromGroup extends DataRoomAction("remove_from_group")
  }

}
