syntax = "proto3";

package anduin.rag.workflow;

import "scalapb/scalapb.proto";
import "anduin/dms/feature.proto";
import "rag/tensors_message.proto";
import "rag/file_index_info.proto";

option (scalapb.options) = {
  single_file: true
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.FileId"
  import: "anduin.dms.DocumentStorageIdMapper.given"
  import: "anduin.model.document.DocumentStorageId"
  import: "anduin.rag.Tensor1D"
  import: "anduin.rag.TensorsMessageTypeMapper.given"
};

message SetTagsParams {
  repeated string tags = 1;
}

message RagIndexDocumentParams {
  dms.DmsFeatureProto dms_feature = 1;
  string file_id = 2 [(scalapb.field).type = "FileId"];
  string actor = 3 [(scalapb.field).type = "UserId"];
  SetTagsParams set_tags_opt = 4;
  bool forced = 5;
  optional rag.FileIndexInfo precomputed_file_index_info = 6;
}

message RagIndexDocumentParamsWithIndexState {
  dms.DmsFeatureProto dms_feature = 1;
  string file_id = 2 [(scalapb.field).type = "FileId"];
  string actor = 3 [(scalapb.field).type = "UserId"];
  SetTagsParams set_tags_opt = 4;
  rag.FileIndexInfo file_index_info = 5 [(scalapb.field).required = true];
}

message RagIndexDocumentResponse {}

message GetFileIndexStateParams {
  dms.DmsFeatureProto dms_feature = 1;
  string file_id = 2 [(scalapb.field).type = "FileId"];
  string actor = 3 [(scalapb.field).type = "UserId"];
}

message GetFileIndexStateResponse {
  rag.FileIndexInfo index_info = 1 [(scalapb.field).required = true];
}

message ConvertToImagesParams {
  dms.DmsFeatureProto dms_feature = 1;
  string file_id = 2 [(scalapb.field).type = "FileId"];
  string actor = 3 [(scalapb.field).type = "UserId"];
}

message ConvertToImagesResponse {
  repeated string image_storage_ids = 1 [(scalapb.field).collection_type = "List", (scalapb.field).type = "DocumentStorageId"];
}

message GetImageFileStorageIdParams {
  dms.DmsFeatureProto dms_feature = 1;
  string file_id = 2 [(scalapb.field).type = "FileId"];
  string actor = 3 [(scalapb.field).type = "UserId"];
}

message GetImageFileStorageIdResponse {
  string storage_id = 1 [(scalapb.field).type = "DocumentStorageId"];
}

message GenerateVisualEmbeddingsParams {
  string actor = 1 [(scalapb.field).type = "UserId"];
  repeated string image_storage_ids = 2 [(scalapb.field).collection_type = "List", (scalapb.field).type = "DocumentStorageId"];
}

message GenerateVisualEmbeddingsResponse {
  repeated Tensor1DMessage embeddings = 1 [(scalapb.field).collection_type = "List", (scalapb.field).type = "Tensor1D"];
}

message StoreVisualEmbeddingsParams {
  rag.FileIndexInfo index_info = 1 [(scalapb.field).required = true];
  repeated Tensor1DMessage visual_embeddings = 2 [(scalapb.field).collection_type = "List", (scalapb.field).type = "Tensor1D"];
}

message StoreVisualEmbeddingsResponse {}

message SyncVespaFileStateParams {
  rag.FileIndexInfo index_info = 1 [(scalapb.field).required = true];
  SetTagsParams set_tags_opt = 2;
}

message SyncVespaFileStateResponse {}

message RagBatchIndexDocumentParams {
  dms.DmsFeatureProto dms_feature = 1;
  repeated rag.FileIndexInfo file_index_infos = 2;
  string actor = 3 [(scalapb.field).type = "UserId"];
  SetTagsParams set_tags_opt = 4;
  bool forced = 5;
}

message RagBatchIndexDocumentResponse {}