// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.model.email

import io.circe.Codec
import scalapb.TypeMapper

import anduin.circe.generic.semiauto.deriveStringEnumCodec
import anduin.enumeration.{StringEnum, StringEnumCompanion}

sealed trait SystemTag derives CanEqual { self: EmailTagKey => }

enum EmailTagKey(val value: String) extends StringEnum {

  case UserTag extends EmailTagKey("user") with SystemTag

  case TransactionTag extends EmailTagKey("transaction") with SystemTag

  case DealBotTag extends EmailTagKey("dealbot") with SystemTag

  case WorkflowTag extends EmailTagKey("workflow") with SystemTag

  case DeploymentTestTag extends EmailTagKey("deptest") with SystemTag

  case UnclassifiedTag extends EmailTagKey("unclassified") with SystemTag

  case UnknownTag extends EmailTagKey("unknown")

}

object EmailTagKey extends StringEnumCompanion[EmailTagKey] {

  given Codec[EmailTagKey] = deriveStringEnumCodec

  def fromString(tagStr: String): EmailTagKey = {
    fromValueOpt(tagStr).getOrElse(UnknownTag)
  }

  given emailTagMapper: TypeMapper[String, EmailTagKey] = TypeMapper[String, EmailTagKey] { tagStr =>
    fromString(tagStr)
  } { emailTag =>
    emailTag.value
  }

}
