// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.cue.model.commands

import io.circe.Codec

import anduin.circe.generic.semiauto.{CirceCodec, deriveStringEnumCodec}
import anduin.enumeration.{StringEnum, StringEnumCompanion}

object CueCommands {

  lazy val defaultCommandPackages: Seq[String] = Seq("commands/commands")

  enum Command(val value: String) extends StringEnum {
    case Eval extends Command("eval")
    case Export extends Command("export")
    case Vet extends Command("vet")
  }

  object Command extends StringEnumCompanion[Command] {
    given Codec[Command] = deriveStringEnumCodec
  }

  final case class CueCommand(
    command: Command,
    packages: Seq[String] = Seq.empty,
    filePaths: Seq[String] = Seq.empty,
    expression: Option[String] = None,
    schema: Option[String] = None,
    path: Option[String] = None
  ) derives CanEqual,
        CirceCodec.WithDefaultsDropNull

  sealed trait CommandsTrait

  sealed trait TableCommandsTrait extends CommandsTrait {
    def exportTableSchemas: CueCommand
    def exportTableValues: CueCommand
    def exportTableSchemasWithValidation: Option[CueCommand]
    def exportTableValuesWithValidation: Option[CueCommand]

    lazy val toTableCommands: TableCommands = TableCommands(
      exportTableSchemas,
      exportTableValues,
      exportTableSchemasWithValidation,
      exportTableValuesWithValidation
    )

  }

  final case class TableCommands(
    exportTableSchemas: CueCommand,
    exportTableValues: CueCommand,
    exportTableSchemasWithValidation: Option[CueCommand] = None,
    exportTableValuesWithValidation: Option[CueCommand] = None
  ) extends TableCommandsTrait derives CanEqual, CirceCodec.WithDefaultsDropNull

  sealed trait TableMappingCommandsTrait extends CommandsTrait {
    def mappings: Map[String, MappingCommands]
    lazy val toTableMappingCommands: TableMappingCommands = TableMappingCommands(mappings)
  }

  final case class TableMappingCommands(mappings: Map[String, MappingCommands]) extends TableMappingCommandsTrait
      derives CanEqual,
        CirceCodec.WithDefaultsDropNull

  final case class MappingCommands(
    transformTableData: CueCommand,
    transformCustomData: Option[CueCommand]
  ) derives CanEqual,
        CirceCodec.WithDefaultsDropNull

  final case class DataExtractCommands(
    exportTableSchemas: CueCommand,
    exportTableValues: CueCommand,
    exportTableSchemasWithValidation: Option[CueCommand] = None,
    exportTableValuesWithValidation: Option[CueCommand] = None,
    exportTableValuesFromOcrData: CueCommand,
    exportTableValuesWithValidationFromOcrData: CueCommand
  ) extends TableCommandsTrait derives CanEqual, CirceCodec.WithDefaultsDropNull

  final case class DataLayerCommands(
    extractDefaultValues: CueCommand
  ) extends CommandsTrait derives CanEqual, CirceCodec.WithDefaultsDropNull

}
