// Copyright (C) 2014-2025 Anduin Transactions Inc.

package design.anduin.htmlForm.portal

import design.anduin.htmlForm.GenerateHtmlTemplate
import scalatags.Text

import design.anduin.email.*
import design.anduin.email.button.*
import design.anduin.email.footer.*
import design.anduin.htmlForm.common.*
import scalatags.Text.all.*

object AdminPortalInviteNewUserToEntityHtmlGenerate extends GenerateHtmlTemplate {

  override protected def subject: String = "{{{subject}}}"

  override protected def title: String = "Entity Invitation"

  override protected def bodyContent: Text.TypedTag[String] = {
    BaseWrapper(
      Layout(
        Illustration(src = "https://static-assets.anduintransact.com/gondor/digest_icons/invite_collaborate.png"),
        TextContent(
          style = TextStyle.H2,
          textAlign = "center"
        )("{{title}}"),
        TextContent()("{{body}}"),
        <PERSON><PERSON>(link = "{{redirectUrl}}")("{{cta}}")
      ),
      AttachmentLink(),
      Footer(
        FooterText("Anduin is the platform that makes deals simpler, smarter and faster."),
        FooterText(raw("Sent via <a href=\"https://anduintransact.com\">Anduin</a>"))
      )
    )
  }

}
