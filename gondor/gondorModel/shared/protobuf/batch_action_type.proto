syntax = "proto3";

package anduin.batchaction;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.batchaction"
  single_file: true
};

enum BatchActionType {
  reserved 14;
  //FundData
  FundDataImportInvestorFromSubscription = 0;
  FundDataImportInvestorsBySpreadsheet = 1;
  FundDataImportInvestmentEntitiesBySpreadsheet = 2;
  FundDataImportContactsBySpreadsheet = 3;
  FundDataComputeProfilesFromSpreadsheet = 4;
  FundDataImportDocumentBySpreadsheet = 5;
  FundDataImportProfilesFromSpreadsheet = 6;
  FundDataImportRiskAssessmentsBySpreadsheet = 7;
  FundDataExportInvestmentEntitiesToSpreadsheet = 8;
  FundDataBatchDocumentRequest = 9;
  FundDataExportContactsToSpreadsheet = 23;
  FundDataMergeInvestors = 10;
  FundDataAssignInvestorsToClientGroup = 11;
  FundDataDeleteClientGroup = 12;
  FundDataInviteMembers = 13;
  FundDataRemoveGuests = 15;
  FundDataCreateOfflineSubscriptions = 16;
  FundDataInviteGuests = 17;
  FundDataNotifyGuests = 18;
  FundDataModifyGuestsAccessToOpportunityPages = 19;
  FundDataCreateOfflineTransactions = 20;
  FundDataImportOrdersToTransactions = 21;
  FundDataExtractInvestmentEntityInfoFromFiles = 22;

  //DataExtract
  reserved 100, 102, 103, 104;
  DataExtractGetUserDocumentsMappingResult = 101;
  DataExtractGenerateAnalyticsReport = 105;
  DataExtractGenerateDebugReport = 106;

  // FundSub
  FundSubOperationBulkAddEnvironment = 200;
  FundSubOperationRemoveEnvironment = 201;
  FundSubInviteLp = 202;
  FundSubInviteFundManager = 203;
  FundSubMoveFundManager = 204;
  FundSubRemoveFundManager = 205;
  FundSubDeleteFundManagerGroup = 206;
  FundSubAssignInvestor = 207;
  FundSubUnassignInvestor = 208;
  FundSubDeleteInvestorGroup = 209;

  // RIA
  RiaCreateOrder = 300;

  // Annotations / PDF Tool
  PdfAnalyzePdfAnnotations = 400;
}
