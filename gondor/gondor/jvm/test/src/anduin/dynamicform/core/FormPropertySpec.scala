// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dynamicform.core

import com.anduin.stargazer.UnitSpec
import com.anduin.stargazer.dynamicform.core.FormProperty
import com.anduin.stargazer.dynamicform.misc.FormSectionAttribute

import anduin.protobuf.dynamicform.*

final class FormPropertySpec extends UnitSpec {

  "FormPropertySpec" - {

    "allAliases should be correct" in {
      FormProperty(
        DynamicFormSection(
          id = "s0_s0",
          alias = "alias1",
          children = Seq(
            DynamicFormSection(id = "s0_s0_s0", alias = "alias2"),
            DynamicFormSection(id = "s0_s0_s1", alias = "alias3")
          )
        ),
        duplicateCounts = Map.empty
      ).allAliases shouldBe Map(
        "alias1" -> "s0_s0",
        "alias2" -> "s0_s0_s0",
        "alias3" -> "s0_s0_s1"
      )

      FormProperty(
        DynamicFormSection(
          id = "s0_s0",
          alias = "alias1",
          children = Seq(
            DynamicFormSection(
              maxDuplicates = 2,
              id = "s0_s0_s0",
              alias = "alias2",
              children = Seq(
                DynamicFormSection(id = "s0_s0_s0_s0", alias = "alias3")
              )
            )
          )
        ),
        duplicateCounts = Map("s0_s0_s0" -> 2)
      ).allAliases shouldBe Map(
        "alias1" -> "s0_s0",
        "alias2" -> "s0_s0_s0",
        "alias2:0" -> "s0_s0_s0_0",
        "alias2:1" -> "s0_s0_s0_1",
        "alias3" -> "s0_s0_s0_s0",
        "alias3:0" -> "s0_s0_s0_0_s0",
        "alias3:1" -> "s0_s0_s0_1_s0"
      )
    }

    "form without alias should be correct" in {
      FormProperty(
        DynamicFormSection(
          id = "s0_s0",
          alias = "alias1",
          children = Seq(
            DynamicFormSection(
              id = "s0_s0_s0",
              alias = "alias2",
              newRules = Seq(
                NewFormRule(
                  condition = BinaryExpr(
                    Variable("alias1"),
                    "==",
                    Value("abc")
                  ),
                  event = Option(NewFormEvent(NewEventType.EventDisable))
                )
              ),
              formDescription = Option(
                NewFormDescription(
                  label = "This is label {{alias1}}",
                  htmlDescription = "<div>This is description {{alias3}}</div>"
                )
              ),
              properties = Set(FormSectionAttribute.Interpolated)
            ),
            DynamicFormSection(
              id = "s0_s0_s1",
              alias = "alias3",
              newRules = Seq(
                NewFormRule(
                  condition = BinaryExpr(
                    Variable("alias1"),
                    "==",
                    Value("abc")
                  ),
                  event = Option(
                    NewFormEvent(NewEventType.EventSetValue, value = UnaryExpr("!", Variable("alias2")))
                  )
                )
              ),
              formDescription = Option(
                NewFormDescription(
                  label = "This is label {{alias1}}",
                  htmlDescription = "<div>This is description {{alias3}}</div>",
                  validations = Seq("required", "validate(alias3 == alias2, should match value)")
                )
              )
            )
          )
        ),
        duplicateCounts = Map("s0_s0_s1" -> 3)
      ).form shouldBe
        DynamicFormSection(
          id = "s0_s0",
          alias = "alias1",
          children = Seq(
            DynamicFormSection(
              id = "s0_s0_s0",
              alias = "alias2",
              newRules = Seq(
                NewFormRule(
                  condition = BinaryExpr(
                    Variable("s0_s0"),
                    "==",
                    Value("abc")
                  ),
                  event = Option(NewFormEvent(NewEventType.EventDisable))
                )
              ),
              formDescription = Option(
                NewFormDescription(
                  label = "This is label {{s0_s0}}",
                  htmlDescription = "<div>This is description {{s0_s0_s1}}</div>"
                )
              ),
              properties = Set(FormSectionAttribute.Interpolated)
            ),
            DynamicFormSection(
              id = "s0_s0_s1",
              alias = "alias3",
              newRules = Seq(
                NewFormRule(
                  condition = BinaryExpr(
                    Variable("s0_s0"),
                    "==",
                    Value("abc")
                  ),
                  event = Option(
                    NewFormEvent(NewEventType.EventSetValue, value = UnaryExpr("!", Variable("s0_s0_s0")))
                  )
                )
              ),
              formDescription = Option(
                NewFormDescription(
                  label = "This is label {{s0_s0}}",
                  htmlDescription = "<div>This is description {{alias3}}</div>",
                  validations = Seq("required", "validate((s0_s0_s1 == s0_s0_s0),should match value)")
                )
              )
            )
          )
        )
    }

    "relatedMap should be correct" in {
      FormProperty(
        DynamicFormSection(
          id = "s0_s0",
          alias = "alias1",
          children = Seq(
            DynamicFormSection(
              maxDuplicates = 2,
              id = "s0_s0_s0",
              alias = "alias2",
              newRules = Seq(
                NewFormRule(
                  condition = BinaryExpr(
                    Variable("alias1"),
                    "==",
                    Value("abc")
                  ),
                  event = Option(NewFormEvent(NewEventType.EventDisable))
                )
              ),
              formDescription = Option(
                NewFormDescription(
                  label = "This is label {{alias1}}",
                  htmlDescription = "<div>This is description {{alias3}}</div>"
                )
              ),
              properties = Set(FormSectionAttribute.Interpolated)
            ),
            DynamicFormSection(
              id = "s0_s0_s1",
              alias = "alias3",
              newRules = Seq(
                NewFormRule(
                  condition = BinaryExpr(
                    Variable("alias1"),
                    "==",
                    Value("abc")
                  ),
                  event = Option(
                    NewFormEvent(NewEventType.EventSetValue, value = UnaryExpr("!", Variable("alias2")))
                  )
                )
              ),
              formDescription = Option(
                NewFormDescription(
                  label = "This is label {{alias1}}",
                  htmlDescription = "<div>This is description {{alias3}}</div>",
                  validations = Seq("required", "validate(alias3 == alias4, should match value)")
                )
              )
            )
          )
        ),
        duplicateCounts = Map("s0_s0_s0" -> 2)
      ).relatedMap shouldBe Map(
        "s0_s0" -> Set(
          "s0_s0_s0",
          "s0_s0_s1",
          "s0_s0_s0_0",
          "s0_s0_s0_1"
        ),
        "s0_s0_s0" -> Set("s0_s0_s1"),
        "s0_s0_s1" -> Set(
          "s0_s0_s0",
          "s0_s0_s0_0",
          "s0_s0_s0_1"
        ),
        "alias4" -> Set("s0_s0_s1")
      )
    }

    "getDefaultValues should be correct" in {
      FormProperty.getDefaultValues(
        DynamicFormSection(
          id = "s0_s0",
          formDescription = Some(NewFormDescription(defaultValue = "a")),
          children = Seq(
            DynamicFormSection(id = "s0_s1", formDescription = Some(NewFormDescription(defaultValue = "b"))),
            DynamicFormSection(id = "s0_s2")
          )
        )
      ) shouldBe Map("s0_s0" -> "a", "s0_s1" -> "b")
    }

    "transform position should be correct" in {
      FormProperty.transformVar(
        DynamicFormSection(
          id = "s0_s0",
          alias = "root",
          formDescription = Some(
            NewFormDescription(
              inputType = InputType.Radio,
              inputOptions = Seq(
                InputWithMapping("option1"),
                InputWithMapping("option2")
              )
            )
          ),
          children = Seq(
            DynamicFormSection(
              id = "s0_s0_s1",
              formDescription = Some(
                NewFormDescription(position = DynamicFormSectionBelowOptionPosition("root", "option1"))
              )
            )
          )
        ),
        varMap = Map("root" -> "s0_s0")
      ) shouldBe DynamicFormSection(
        id = "s0_s0",
        alias = "root",
        formDescription = Some(
          NewFormDescription(
            inputType = InputType.Radio,
            inputOptions = Seq(
              InputWithMapping("option1"),
              InputWithMapping("option2")
            )
          )
        ),
        children = Seq(
          DynamicFormSection(
            id = "s0_s0_s1",
            formDescription = Some(
              NewFormDescription(position = DynamicFormSectionBelowOptionPosition("s0_s0", "option1"))
            )
          )
        )
      )
    }

  }

}
