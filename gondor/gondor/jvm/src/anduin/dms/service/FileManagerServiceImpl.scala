// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dms.service

import java.time.Instant
import java.time.temporal.ChronoUnit
import scala.annotation.tailrec
import zio.{Task, ZIO}
import anduin.account.profile.UserProfileService
import anduin.environment.EnvironmentCheck
import anduin.dataroom.flow.DataRoomValidateOperations.PlanCheck
import anduin.dataroom.service.{DataRoomService, DataRoomTrashService}
import anduin.dataroom.validator.DataRoomValidator
import anduin.dms.DmsFeature
import anduin.dms.file.event.FileEventStoreOperations
import anduin.dms.file.state.FileStateStoreOperations
import anduin.dms.service.FileManagerService.*
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.id.entity.EntityId
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.dms.DmsChannel
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{FileId, FolderId, TeamId}
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.protobuf.service.file.*
import anduin.radix.RadixId
import anduin.service.entity.EntityServiceUtils
import anduin.service.entity.common.EntityUtils
import anduin.stargazer.service.dataroom.validation.DataRoomAssetCheckParams
import com.anduin.stargazer.endpoints.*
import com.anduin.stargazer.service.FileServiceEndpoints.*
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FileManagerServiceImpl(
  fileService: FileService,
  userProfileService: UserProfileService,
  dataRoomService: DataRoomService,
  dataRoomValidator: DataRoomValidator,
  dataRoomTrashService: DataRoomTrashService
) extends FileManagerService {

  private val daysToQueryRecentFiles: Long = 30
  private val maxNumRecentFiles = 1000
  private val parallelism = 8

  private def validateLocationPermission(
    location: FileManagerLocation,
    actor: UserId
  )(
    using DmsFeature
  ): Task[Unit] = {
    ZIOUtils.traverseOptionUnit(location.folderIdOpt) { folderId =>
      folderId.channel match {
        case dataRoomWorkflowId: DataRoomWorkflowId => validateDataRoomPermission(dataRoomWorkflowId, folderId, actor)
        case _                                      => ZIO.unit
      }
    }
  }

  private def validateDataRoomPermission(
    dataRoomId: DataRoomWorkflowId,
    folderId: FolderId,
    actor: UserId
  )(
    using DmsFeature
  ) = {
    dataRoomValidator.checkDataRoomAsset(
      new DataRoomAssetCheckParams {
        override def folderIds = Set(folderId)
        override def fileIds = Set.empty
        override def assetDataRoomIdOpt = Some(dataRoomId)
      },
      actor
    )
  }

  def getFileManagerLocation(
    params: GetFileManagerLocationParams,
    actor: UserId,
    environmentCheck: EnvironmentCheck
  )(
    using DmsFeature
  ): Task[GetFileManagerLocationResponse] = {
    for {
      _ <- validateLocationPermission(params.fileManagerLocation, actor)
      data <- params.fileManagerLocation match {
        case folderView: FileManagerLocation.FolderView =>
          getFolderViewInfo(folderView, actor, params.includeSubItems, environmentCheck)
        case folder: FileManagerLocation.Folder =>
          getFolderInfo(folder, actor, params.includeSubItems)
      }
      path <- getPath(params.fileManagerLocation, actor)
    } yield GetFileManagerLocationResponse(data, path)
  }

  private def getPath(
    location: FileManagerLocation,
    actor: UserId
  )(
    using DmsFeature
  ): Task[Seq[ItemInPath]] = {
    for {
      folderViewKeyList <- ZIO.foreach(location.viewKeyOpt.toSeq) {
        getFolderViewPath
      }
      viewPaths <- ZIOUtils.foreachParN(parallelism)(folderViewKeyList.flatten) { pathViewKey =>
        for {
          name <- getViewName(pathViewKey)
        } yield ItemInPath(FileManagerLocation.FolderView(pathViewKey), name, None)
      }
      folderPaths <- ZIOUtils.foreachParN(parallelism) {
        for {
          rootFolderId <- location.folderIdOpt.toSeq
          pathFolderId <- getFolderIdPath(List.empty, rootFolderId)
        } yield FileManagerLocation.Folder(location.viewKeyOpt, pathFolderId)
      } { folder =>
        for {
          name <- getFolderName(folder.folderId, actor)
          index <- getFolderIndex(folder.folderId, actor)
        } yield ItemInPath(folder, name, index)
      }
    } yield viewPaths ++ folderPaths
  }

  @tailrec
  private def getFolderIdPath(aggregate: List[FolderId], folderId: FolderId): List[FolderId] = {
    folderId.parentFolder match {
      case Some(parentFolder) => getFolderIdPath(folderId :: aggregate, parentFolder)
      case None               => folderId :: aggregate
    }
  }

  private def getFolderViewPath(folderViewKey: FolderViewKey) = {
    ZIOUtils.tailRecM(folderViewKey -> Seq.empty[FolderViewKey]) { case (currentRoot, path) =>
      val nextPath = currentRoot +: path
      currentRoot match {
        case _: RootFolderViewKey | FolderViewKey.Empty =>
          ZIO.attempt(Right(nextPath))
        case _: DataRoomProductFolderViewKey =>
          ZIO.attempt(Left(RootFolderViewKey() -> nextPath))
        case _: InternalDataRoomListFolderViewKey | _: ExternalDataRoomListFolderViewKey =>
          ZIO.attempt(Left(DataRoomProductFolderViewKey() -> nextPath))
      }
    }
  }

  private def getAllUserEntities(actor: UserId) = {
    EntityUtils.getAllEntityIdOfUser(actor)
  }

  private def getAllDataRooms(actor: UserId, environmentCheck: EnvironmentCheck) = {
    dataRoomService.getAllDataRooms(
      actor = actor,
      isArchived = false,
      includeInvited = false,
      includeToaNotAccepted = false,
      includeGroup = false,
      planCheck = PlanCheck.RequirePlan(Set()),
      environmentCheck = environmentCheck
    )
  }

  private def getDataRooms(
    entityIdOpt: Option[EntityId],
    actor: UserId,
    environmentCheck: EnvironmentCheck
  ) = {
    for {
      userEntities <- getAllUserEntities(actor)
      allDataRooms <- getAllDataRooms(actor, environmentCheck)
    } yield {
      allDataRooms.flatMap { res =>
        Option.when {
          entityIdOpt.fold {
            !userEntities.contains(res.createdState.creatorEntityId)
          } { entityId =>
            res.createdState.creatorEntityId == entityId
          }
        } {
          FolderId.channelSystemFolderId(res.workflowId)
        }
      }
    }
  }

  private def getViewName(folderViewKey: FolderViewKey) = {
    folderViewKey match {
      case _: RootFolderViewKey =>
        ZIO.attempt("File manager")
      case _: DataRoomProductFolderViewKey =>
        ZIO.attempt("Data room")
      case InternalDataRoomListFolderViewKey(entityId) =>
        EntityServiceUtils.execute(_.getEntityModel(entityId)).map(_.alias)
      case _: ExternalDataRoomListFolderViewKey =>
        ZIO.attempt("External data rooms")
      case FolderViewKey.Empty =>
        ZIO.attempt("")
    }
  }

  private def getViewsInView(
    folderViewKey: FolderViewKey,
    actor: UserId,
    includeSubItems: Boolean,
    environmentCheck: EnvironmentCheck
  )(
    using DmsFeature
  ): Task[(Seq[FileManagerLocation.FolderView], Option[Seq[FolderViewInfo]])] = {
    for {
      subViewKeys <- folderViewKey match {
        case _: RootFolderViewKey =>
          ZIO.attempt(Seq(DataRoomProductFolderViewKey()))
        case _: DataRoomProductFolderViewKey =>
          for {
            userEntities <- EntityUtils.getAllEntityIdOfUser(actor)
            allDataRooms <- getAllDataRooms(actor, environmentCheck)
            groupedByEntity = allDataRooms.groupBy { dataroom =>
              val entityId = dataroom.createdState.creatorEntityId
              Option.when(userEntities.contains(entityId))(entityId)
            }
          } yield {
            groupedByEntity.keySet.toSeq.map {
              _.fold[FolderViewKey](ExternalDataRoomListFolderViewKey())(InternalDataRoomListFolderViewKey(_))
            }
          }
        case _: InternalDataRoomListFolderViewKey | _: ExternalDataRoomListFolderViewKey | FolderViewKey.Empty =>
          ZIO.attempt(Seq())
      }
      subViews = subViewKeys.map(viewKey => FileManagerLocation.FolderView(viewKey))
      subViewInfos <- ZIO.when(includeSubItems) {
        ZIOUtils.foreachParN(parallelism)(subViews)(viewKey =>
          getFolderViewInfo(
            viewKey,
            actor,
            includeSubItems = false,
            environmentCheck
          )
        )
      }
    } yield subViews -> subViewInfos
  }

  private def filterSubFolders(
    rawFolderIds: Seq[FolderId],
    actor: UserId
  )(
    using DmsFeature
  ) = {
    ZIOUtils
      .foreachParN(parallelism)(rawFolderIds) { folderId =>
        fileService.existFolder(actor)(folderId).map(Option.when(_)(folderId))
      }
      .map(_.flatten)
  }

  private def getFoldersInView(
    folderViewKey: FolderViewKey,
    actor: UserId,
    includeSubItems: Boolean,
    environmentCheck: EnvironmentCheck
  )(
    using DmsFeature
  ): Task[(Seq[FileManagerLocation.Folder], Option[Seq[FolderInfo]])] = {
    for {
      rawFolders <- folderViewKey match {
        case _: RootFolderViewKey | _: DataRoomProductFolderViewKey | FolderViewKey.Empty =>
          ZIO.attempt(Seq())
        case InternalDataRoomListFolderViewKey(entityId) =>
          getDataRooms(Some(entityId), actor, environmentCheck)
        case _: ExternalDataRoomListFolderViewKey =>
          getDataRooms(None, actor, environmentCheck)
      }
      filteredSubFolders <- filterSubFolders(rawFolders, actor)
      subFolders = filteredSubFolders.map { folderId =>
        FileManagerLocation.Folder(Some(folderViewKey), folderId)
      }
      subFolderInfos <- ZIO.when(includeSubItems) {
        ZIOUtils.foreachParN(parallelism)(subFolders)(folder => getFolderInfo(folder, actor, includeSubItems = false))
      }
    } yield subFolders -> subFolderInfos
  }

  private def getFolderViewInfo(
    folderView: FileManagerLocation.FolderView,
    actor: UserId,
    includeSubItems: Boolean,
    environmentCheck: EnvironmentCheck
  )(
    using DmsFeature
  ): Task[FolderViewInfo] = {
    for {
      name <- getViewName(folderView.viewKey)
      subViews <- getViewsInView(folderView.viewKey, actor, includeSubItems, environmentCheck)
      subFolders <- getFoldersInView(folderView.viewKey, actor, includeSubItems, environmentCheck)
    } yield FolderViewInfo(
      folderView,
      name,
      views = subViews._2,
      folders = subFolders._2,
      subViewCount = subViews._1.size,
      subFolderCount = subFolders._1.size
    )
  }

  private def getFolderInfo(
    folder: FileManagerLocation.Folder,
    actor: UserId,
    includeSubItems: Boolean,
    includeAllFileIds: Boolean = false
  )(
    using DmsFeature
  ): Task[FolderInfo] = {
    val folderId = folder.folderId
    for {
      index <- getFolderIndex(folderId, actor)
      name <- getFolderName(folderId, actor)
      lastModifiedTime <- getLastUpdatedAt(folderId, actor)
      createdAt <- getCreatedAt(folderId, actor)
      createdBy <- getCreatedBy(folderId, actor)
      deletedAt <- getDeletedAt(folderId, actor)
      files <- getFiles(folderId, actor, includeSubItems)
      folders <- getSubFolders(folder, actor, includeSubItems, includeAllFileIds)
      permission <- getFolderPermission(folderId, actor)
      allFileIds <- ZIO.when(includeAllFileIds)(getAllFileIds(folderId, actor))
    } yield FolderInfo(
      folder,
      name,
      createdAt,
      lastModifiedTime,
      Some(createdBy._1),
      Some(createdBy._2),
      deletedAt,
      files._2,
      folders._2,
      subFolderCount = folders._1.size,
      fileCount = files._1.size,
      userPermission = permission,
      index = index,
      allFileIds = allFileIds
    )
  }

  private def getFolderIndex(
    folderId: FolderId,
    actor: UserId
  )(
    using DmsFeature
  ): Task[Option[Long]] = {
    getFolderInfoField(
      folderId,
      ZIOUtils
        .traverseOption2(folderId.parentFolder) { parentFolderId =>
          fileService.getFolderOrder(actor, parentFolderId).map(_.get(folderId))
        },
      ZIO.none,
      ZIO.none
    )
  }

  private def getFolderName(
    folderId: FolderId,
    actor: UserId
  )(
    using DmsFeature
  ): Task[String] = {
    getFolderInfoField(
      folderId,
      fileService.getFolderName(actor)(folderId),
      ZIO.succeed("Recent files"),
      ZIO.succeed("Trash")
    )
  }

  private def getLastUpdatedAt(
    folderId: FolderId,
    actor: UserId
  )(
    using DmsFeature
  ): Task[Option[Instant]] = {
    val task = for {
      lastUpdatedAtOpt <- fileService.getFolderLastUpdatedAt(actor)(folderId)
      lastUpdatedAt <- lastUpdatedAtOpt.fold(
        fileService.getFolderCreatedAt(actor)(folderId)
      )(timestamp => ZIO.succeed(Some(timestamp)))
    } yield lastUpdatedAt
    getFolderInfoField(
      folderId,
      task,
      ZIO.none,
      ZIO.none
    )
  }

  private def getCreatedAt(
    folderId: FolderId,
    actor: UserId
  )(
    using DmsFeature
  ): Task[Option[Instant]] = {
    val rootFolderId = folderId.copy(folderParts = folderId.folderParts.copy(tail = List()))
    getFolderInfoField(
      folderId,
      fileService.getFolderCreatedAt(actor)(folderId),
      fileService.getFolderCreatedAt(actor)(rootFolderId),
      fileService.getFolderCreatedAt(actor)(rootFolderId)
    )
  }

  private def getCreatedBy(
    folderId: FolderId,
    actor: UserId
  )(
    using DmsFeature
  ): Task[(UserId, UserInfo)] = {
    val rootFolderId = folderId.copy(folderParts = folderId.folderParts.copy(tail = List()))
    def mainTask(folderId: FolderId): Task[(UserId, UserInfo)] = for {
      userId <- fileService.getFolderCreatedBy(actor)(folderId)
      userInfo <- userProfileService.getUserInfo(userId)
    } yield userId -> userInfo

    getFolderInfoField(
      folderId,
      mainTask(folderId),
      mainTask(rootFolderId),
      mainTask(rootFolderId)
    )
  }

  private def getDeletedAt(
    folderId: FolderId,
    actor: UserId
  )(
    using DmsFeature
  ): Task[Option[Instant]] = {
    getFolderInfoField(
      folderId,
      fileService.getFolderDeletedOpt(actor)(folderId).map(_.flatMap(_.at)),
      ZIO.none,
      ZIO.none
    )
  }

  private def getFiles(
    folderId: FolderId,
    actor: UserId,
    includeSubItems: Boolean
  )(
    using DmsFeature
  ): Task[(Seq[FileId], Option[Seq[FileInfo]])] = {
    val rootFolderId = folderId.copy(folderParts = folderId.folderParts.copy(tail = List()))
    for {
      (fileIds, includeDeleted) <- getFolderInfoField(
        folderId,
        defaultTask = fileService.getFiles(actor)(folderId).map(_ -> false),
        recentFolderTask = getRecentFiles(rootFolderId, actor).map(_ -> false),
        trashFolderTask = getDeletedFiles(actor, folderId.channel).map(_ -> true)
      )
      fileInfos <- ZIO.when(includeSubItems)(
        fileService.batchGetFileInfos(fileIds, actor, includeDeleted)
      )
    } yield fileIds -> fileInfos
  }

  private def getRecentFiles(
    rootFolderId: FolderId,
    actor: UserId
  )(
    using DmsFeature
  ): Task[Seq[FileId]] = {
    val now = Instant.now()
    val queryTimeRange = now.minus(daysToQueryRecentFiles, ChronoUnit.DAYS) -> now
    for {
      fileIds <- FDBRecordDatabase.transact(
        FDBOperations[(FileEventStoreOperations, FileStateStoreOperations)].Production
      ) { case (fileEventOps, fileStateOps) =>
        for {
          createFileEvents <- fileEventOps.getCreateEventsInChannelByTime(
            rootFolderId.channel,
            queryTimeRange,
            maxNumRecentFiles
          )
          filesWithAccess <- RecordIO.collectAll(
            createFileEvents.flatMap { createFileEvent =>
              createFileEvent.fileId.map(fileId =>
                fileStateOps
                  .exist(
                    actor,
                    fileId,
                    requiredPermission = FileFolderPermission.ViewOnly
                  )
                  .map(
                    FileWithAccess(
                      fileId,
                      createFileEvent.timestamp.fold(0L)(_.getEpochSecond()),
                      _
                    )
                  )
              )
            }
          )
        } yield filesWithAccess.filter(_.hasAccess).sortBy(_.createdAt).reverse.map(_.fileId)
      }
    } yield fileIds
  }

  private def getDeletedFiles[K <: RadixId](
    actor: UserId,
    channel: K
  )(
    using dmsFeature: DmsFeature
  ): Task[Seq[FileId]] = {
    dmsFeature
      .getDmsChannel(channel)
      .fold[Task[Seq[FileId]]] {
        ZIO.succeed(Seq.empty)
      } {
        case DmsChannel.DataRoomWorkflowIdCase =>
          channel match {
            case dataRoomWorkflowId: DataRoomWorkflowId =>
              dataRoomTrashService.getTrash(dataRoomWorkflowId, actor).map(_.map(_.fileId))
            case _ => ZIO.succeed(Seq.empty)
          }
        case _ => ZIO.succeed(Seq.empty)
      }
  }

  private def getSubFolders(
    location: FileManagerLocation.Folder,
    actor: UserId,
    includeSubItems: Boolean,
    includeAllFileIds: Boolean
  )(
    using DmsFeature
  ): Task[(Seq[FolderId], Option[Seq[FolderInfo]])] = {
    for {
      folderIds <- getFolderInfoField(
        location.folderId,
        fileService.getSubFolders(actor)(location.folderId),
        ZIO.succeed(Seq.empty),
        ZIO.succeed(Seq.empty)
      )
      folderInfos <- ZIO.when(includeSubItems)(
        fileService.batchGetFolderInfos(folderIds, actor, includeAllFileIds)
      )
    } yield folderIds -> folderInfos
  }

  private def getParentFolderInfoMap(
    actor: UserId
  )(
    folderIds: Set[FolderId],
    includeDeleted: Boolean = false
  )(
    using DmsFeature
  ) = {
    ZIOUtils
      .foreachParN(parallelism)(folderIds.toList)(folderId =>
        getParentFolderInfo(actor)(folderId, includeDeleted).map(folderId -> _)
      )
      .map(_.toMap)
  }

  private def getParentFolderInfo(
    actor: UserId
  )(
    parentFolder: FolderId,
    includeDeleted: Boolean
  )(
    using DmsFeature
  ): Task[ParentFolderInfo] = {
    for {
      name <- fileService.getFolderName(actor, includeDeleted)(parentFolder)
      permission <- fileService
        .getFolderPermission(actor)(parentFolder)
        .either
        .map(_.toOption.flatten)
      order <- fileService.getFolderOrder(actor, parentFolder)
    } yield ParentFolderInfo(
      name,
      permission,
      order
    )
  }

  private def getFolderPermission(
    folderId: FolderId,
    actor: UserId
  )(
    using DmsFeature
  ): Task[Option[FileFolderPermission]] = {
    getFolderInfoField(
      folderId,
      fileService.getFolderPermission(actor)(folderId),
      ZIO.succeed(Some(FileFolderPermission.ViewOnly)),
      ZIO.succeed(Some(FileFolderPermission.Own))
    )
  }

  private def getAllFileIds(
    folderId: FolderId,
    actor: UserId
  )(
    using DmsFeature
  ): Task[Seq[FileId]] = {
    for {
      fileIds <- fileService.getFiles(actor)(folderId)
      subFolders <- fileService.getSubFolders(actor)(folderId)
      subFileIds <- ZIO
        .foreach(subFolders) { subFolderId =>
          getAllFileIds(subFolderId, actor)
        }
        .map(_.flatten)
    } yield fileIds ++ subFileIds
  }

  def getFileInfos(
    fileIds: Seq[FileId],
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    using DmsFeature
  ): Task[Seq[FileInfo]] = {
    for {
      fileInfos <- ZIOUtils.foreachParN(parallelism)(fileIds) { fileId =>
        getFileInfo(
          fileId = fileId,
          actor = actor,
          includeDeleted = includeDeleted,
          // Don't include parent folder info for this API
          parentFolderInfoOpt = None
        )
      }
    } yield fileInfos
  }

  def getFileInfo(
    fileId: FileId,
    actor: UserId,
    includeDeleted: Boolean,
    parentFolderInfoOpt: Option[ParentFolderInfo]
  )(
    using DmsFeature
  ): Task[FileInfo] = {
    for {
      name <- fileService.getFileName(actor, includeDeleted)(fileId)
      lastUpdatedAt <- fileService.getFileLastUpdatedAt(actor, includeDeleted)(fileId).flatMap { lastUpdatedAt =>
        ZIOUtils.optionOrElse(lastUpdatedAt)(fileService.getFileCreatedAt(actor, includeDeleted)(fileId))
      }
      createdBy <- fileService.getFileCreatedBy(actor, includeDeleted)(fileId).flatMap { userId =>
        userProfileService.getUserInfo(userId)
      }
      deletedOpt <- fileService.getFileDeletedOpt(actor)(fileId)
      deletedBy <- ZIOUtils.traverseOption(deletedOpt) { deleteRecord =>
        userProfileService.getUserInfo(deleteRecord.actor)
      }
      deletedAt = deletedOpt.flatMap(_.at)
      fileSize <- fileService.getFileSize(fileId)
      userPermission <- fileService.getFilePermission(actor, includeDeleted)(fileId)
    } yield FileInfo(
      itemId = fileId,
      name = name,
      lastModifiedTime = lastUpdatedAt,
      createdBy = Some(createdBy),
      fileSize = Some(fileSize),
      userPermission = userPermission,
      index = parentFolderInfoOpt.flatMap(_.order.get(fileId)),
      parentFolderName = parentFolderInfoOpt.map(_.name),
      parentFolderPermission = parentFolderInfoOpt.flatMap(_.permission),
      deletedBy = deletedBy,
      deletedAt = deletedAt
    )
  }

  private def getSingleUserPermission(
    folderId: FolderId,
    subFolders: Seq[FolderId],
    subFiles: Seq[FileId],
    userId: UserId
  )(
    using DmsFeature
  ) = {
    for {
      parentPermission <- fileService.getFolderPermission(userId)(folderId)
      folderPermissions <- ZIOUtils.foreachParN(parallelism)(subFolders) { folderId =>
        getFolderPermission(
          folderId,
          userId
        ).map(_.map(folderId -> _))
      }
      filePermissions <- ZIOUtils.foreachParN(parallelism)(subFiles) { fileId =>
        fileService.getFilePermission(userId)(fileId).map(_.map(fileId -> _))
      }
    } yield TargetPermission(
      parentPermission,
      folderPermissions.flatten.toMap,
      filePermissions.flatten.toMap
    )
  }

  private def getSingleTeamPermission(
    folderId: FolderId,
    subFolders: Seq[FolderId],
    subFiles: Seq[FileId],
    actor: UserId,
    teamId: TeamId
  )(
    using DmsFeature
  ) = {
    for {
      parentPermission <- fileService.getFolderTeamPermission(actor, teamId)(folderId)
      folderPermissions <- ZIOUtils
        .foreachParN(parallelism)(subFolders) { folderId =>
          fileService
            .getFolderTeamPermission(
              actor,
              teamId
            )(folderId)
            .map(_.map(folderId -> _))
        }
      filePermissions <- ZIOUtils.foreachParN(parallelism)(subFiles) { fileId =>
        fileService.getFileTeamPermission(actor, teamId)(fileId).map(_.map(fileId -> _))
      }
    } yield TargetPermission(
      parentPermission,
      folderPermissions.flatten.toMap,
      filePermissions.flatten.toMap
    )
  }

  private def getMultipleTeamsPermission(
    folderId: FolderId,
    subFolders: Seq[FolderId],
    subFiles: Seq[FileId],
    actor: UserId,
    teamIds: Set[TeamId]
  )(
    using DmsFeature
  ) = {
    for {
      parentPermission <- fileService.getFolderMaxTeamPermission(actor, teamIds)(folderId)
      folderPermissions <- ZIOUtils
        .foreachParN(parallelism)(subFolders) { folderId =>
          fileService
            .getFolderMaxTeamPermission(
              actor,
              teamIds
            )(folderId)
            .map(_.map(folderId -> _))
        }
      filePermissions <- ZIOUtils.foreachParN(parallelism)(subFiles) { fileId =>
        fileService.getFileMaxTeamPermission(actor, teamIds)(fileId).map(_.map(fileId -> _))
      }
    } yield TargetPermission(
      parentPermission,
      folderPermissions.flatten.toMap,
      filePermissions.flatten.toMap
    )
  }

  def getFileTree(
    params: GetFileTreeParams,
    actor: UserId,
    environmentCheck: EnvironmentCheck
  )(
    using DmsFeature
  ): Task[GetFileTreeResponse] = {
    for {
      _ <- validateLocationPermission(params.location, actor)
      data <- params.location match {
        case folderView: FileManagerLocation.FolderView =>
          getFolderViewInfo(folderView, actor, includeSubItems = true, environmentCheck)
        case folder: FileManagerLocation.Folder =>
          getFolderInfo(
            folder,
            actor,
            includeSubItems = true,
            includeAllFileIds = params.includeAllFileIds
          )
      }
      targetPermissionOpt <- ZIOUtils.traverseOption2(params.location.folderIdOpt) { folderId =>
        for {
          folders <- fileService.getSubFolders(actor)(folderId)
          files <- fileService.getFiles(actor)(folderId)
          targetPermissionOpt <- ZIOUtils.traverseOption(params.permissionTargetOpt) {
            case PermissionTarget.SingleUser(userId) => getSingleUserPermission(folderId, folders, files, userId)
            case PermissionTarget.SingleTeam(teamId) => getSingleTeamPermission(folderId, folders, files, actor, teamId)
            case PermissionTarget.MultipleTeams(teamIds) =>
              getMultipleTeamsPermission(folderId, folders, files, actor, teamIds)
          }
        } yield targetPermissionOpt
      }
    } yield GetFileTreeResponse(
      data,
      targetPermissionOpt
    )
  }

}
