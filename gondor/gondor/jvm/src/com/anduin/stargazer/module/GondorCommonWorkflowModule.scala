// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.module

import com.softwaremill.macwire.wire

import anduin.account.cron.{
  CleanExpiredAccountTokenActivities,
  CleanExpiredAccountTokenActivitiesImpl,
  CleanExpiredAccountTokenWorkflowImpl
}
import anduin.analytics.form.workflow.asa.FormAsaAnalyticActivities
import anduin.analytics.form.workflow.asa.impl.{
  FormAsaAnalyticActivitiesImpl,
  FormAsaAnalyticCronWorkflowImpl,
  FormAsaAnalyticWorkflowImpl
}
import anduin.analytics.user.workflow.UserDataExportActivities
import anduin.analytics.user.workflow.impl.{UserDataExportActivitiesImpl, UserDataExportWorkflowImpl}
import anduin.analytics.workflow.impl.*
import anduin.analytics.workflow.interf.*
import anduin.annotation.service.{
  AnalyzePdfAnnotationsActivitiesImpl,
  AnalyzePdfAnnotationsWorkflowImpl,
  AnalyzePdfAnnotationsWorkflowItemImpl
}
import anduin.autofill.workflow.ComputeFormMatchingActivities
import anduin.autofill.workflow.impl.{ComputeFormMatchingActivitiesImpl, ComputeFormMatchingWorkflowImpl}
import anduin.batchaction.BatchActionActivities
import anduin.brienne.fundsub.workflow.activity.*
import anduin.brienne.fundsub.workflow.impl.*
import anduin.dataextract.batchaction.*
import anduin.dataprotection.workflow.DataRequestActivities
import anduin.dataprotection.workflow.impl.{DataRequestActivitiesImpl, DataRequestWorkflowImpl}
import anduin.dataroom.cron.upload.DataRoomNewUploadNotificationActivities
import anduin.dataroom.cron.upload.impl.{
  DataRoomNewUploadNotificationActivitiesImpl,
  DataRoomNewUploadNotificationWorkflowImpl
}
import anduin.dataroom.integration.workflow.{
  StorageFileSyncActivities,
  StorageFileSyncActivitiesImpl,
  StorageFileSyncWorkflowImpl
}
import anduin.dataroom.workflow.simulator.DataRoomSimulatorPopulateDataActivities
import anduin.dataroom.workflow.simulator.impl.{
  DataRoomSimulatorPopulateDataActivitiesImpl,
  DataRoomSimulatorPopulateDataWorkflowImpl
}
import anduin.dataroom.workflow.upload.DataRoomPostUploadActivities
import anduin.dataroom.workflow.upload.impl.{DataRoomPostUploadActivitiesImpl, DataRoomPostUploadWorkflowImpl}
import anduin.dms.upload.flow.activity.{BatchUploadFlowActivity, BatchUploadFlowActivityImpl}
import anduin.dms.workflow.impl.DmsSearchIndexWorkflowImpl
import anduin.funddata.clientgroup.*
import anduin.funddata.contact.{
  FundDataExportContactsToSpreadsheetActivitiesImpl,
  FundDataExportContactsToSpreadsheetWorkflowImpl,
  FundDataExportContactsToSpreadsheetWorkflowItemImpl
}
import anduin.funddata.document.split.{
  FundDataExtractInvestmentEntityInfoFromFilesActivitiesImpl,
  FundDataExtractInvestmentEntityInfoFromFilesWorkflowImpl,
  FundDataExtractInvestmentEntityInfoFromFilesWorkflowItemImpl
}
import anduin.funddata.email.notification.*
import anduin.funddata.fund.subscription.{
  FundDataCreateOfflineSubscriptionsActivitiesImpl,
  FundDataCreateOfflineSubscriptionsWorkflowImpl,
  FundDataCreateOfflineSubscriptionsWorkflowItemImpl
}
import anduin.funddata.fund.transaction.{
  FundDataCreateOfflineTransactionsActivitiesImpl,
  FundDataCreateOfflineTransactionsWorkflowImpl,
  FundDataCreateOfflineTransactionsWorkflowItemImpl
}
import anduin.funddata.fund.transaction.sync.{
  FundDataImportOrdersToTransactionsActivitiesImpl,
  FundDataImportOrdersToTransactionsWorkflowImpl,
  FundDataImportOrdersToTransactionsWorkflowItemImpl
}
import anduin.funddata.group.{
  FundDataInviteMembersActivitiesImpl,
  FundDataInviteMembersWorkflowImpl,
  FundDataInviteMembersWorkflowItemImpl
}
import anduin.funddata.guest.*
import anduin.funddata.investmententity.*
import anduin.funddata.investmententity.assessment.{
  FundDataImportAssessmentsBySpreadsheetActivitiesImpl,
  FundDataImportRiskAssessmentsBySpreadsheetWorkflowImpl,
  FundDataImportRiskAssessmentsBySpreadsheetWorkflowItemImpl
}
import anduin.funddata.investmententity.contact.{
  FundDataImportContactsBySpreadsheetActivitiesImpl,
  FundDataImportContactsBySpreadsheetWorkflowImpl,
  FundDataImportContactsBySpreadsheetWorkflowItemImpl
}
import anduin.funddata.investmententity.document.*
import anduin.funddata.investmententity.profile.*
import anduin.funddata.investor.*
import anduin.funddata.sync.*
import anduin.fundsub.dataexport.workflow.InvestorDataExportActivities
import anduin.fundsub.dataexport.workflow.impl.{
  InvestorDataExportActivitiesImpl,
  InvestorDataExportCronWorkflowImpl,
  InvestorDataExportWorkflowImpl
}
import anduin.fundsub.group.*
import anduin.fundsub.investorgroup.*
import anduin.fundsub.subscriptiondoc.review.{
  FundSubUpdateSubscriptionDocReviewActivity,
  FundSubUpdateSubscriptionDocReviewActivityImpl,
  FundSubUpdateSubscriptionDocReviewMultipleWorkflowImpl,
  FundSubUpdateSubscriptionDocReviewSingleWorkflowImpl
}
import anduin.greylin.correction.workflow.{
  GreylinCorrectionActivities,
  GreylinCorrectionActivitiesImpl,
  GreylinCorrectionWorkflowImpl
}
import anduin.module.*
import anduin.ontology.workflow.InitOntologySpaceActivities
import anduin.ontology.workflow.impl.{InitOntologySpaceActivitiesImpl, InitOntologySpaceWorkflowImpl}
import anduin.rag.workflow.impl.{
  RagBatchIndexDocumentWorkflowImpl,
  RagIndexDocumentWorkflowImpl,
  RagUpdateIndexWorkflowImpl
}
import anduin.workflow.*
import anduin.workflow.expireddocs.DocumentExpirationNotificationActivities
import anduin.workflow.expireddocs.impl.{
  DocumentExpirationNotificationActivitiesImpl,
  DocumentExpirationNotificationWorkflowImpl
}
import anduin.workflow.fundsub.baitevent.impl.{
  SendBaitSupportingDocReviewReadyEventActivityImpl,
  SendBaitSupportingDocReviewReadyEventWorkflowImpl,
  SendBaitUploadSupportingDocEventActivityImpl,
  SendBaitUploadSupportingDocEventWorkflowImpl
}
import anduin.workflow.fundsub.baitevent.{
  SendBaitSupportingDocReviewReadyEventActivity,
  SendBaitUploadSupportingDocEventActivity
}
import anduin.workflow.fundsub.batchaction.FundSubBatchActionActivities
import anduin.workflow.fundsub.batchaction.impl.{
  FundSubBatchActionActivitiesImpl,
  FundSubBatchActionWorkflowImpl,
  FundSubSingleActionWorkflowImpl
}
import anduin.workflow.fundsub.countersign.FundSubSignCountersignReqActivities
import anduin.workflow.fundsub.countersign.impl.{
  FundSubBatchSignCountersignReqWorkflowImpl,
  FundSubSignCountersignReqActivitiesImpl,
  FundSubSingleCountersignWorkflowImpl
}
import anduin.workflow.fundsub.dashboard.impl.{
  FundSubSyncDashboardActivitiesImpl,
  FundSubSyncDashboardWorkflowImpl,
  SyncSingleFundDashboardActivitiesImpl,
  SyncSingleFundDashboardWorkflowImpl
}
import anduin.workflow.fundsub.dashboard.{FundSubSyncDashboardActivities, SyncSingleFundDashboardActivities}
import anduin.workflow.fundsub.dataexport.FundSubDataExportActivity
import anduin.workflow.fundsub.dataexport.impl.{
  DataExportActivityImpl,
  MultipleDataExportWorkflowImpl,
  SingleDataExportWorkflowImpl
}
import anduin.workflow.fundsub.dataextract.impl.{
  FundSubDataExtractRequestNotificationActivitiesImpl,
  FundSubDataExtractRequestNotificationWorkflowImpl,
  FundSubPrepareDummyDataForDataExtractActivitiesImpl,
  FundSubPrepareDummyDataForDataExtractWorkflowImpl
}
import anduin.workflow.fundsub.dataextract.{
  FundSubDataExtractRequestNotificationActivities,
  FundSubPrepareDummyDataForDataExtractActivities
}
import anduin.workflow.fundsub.dataimport.impl.{MultipleDataImportWorkflowImpl, SingleDataImportWorkflowImpl}
import anduin.workflow.fundsub.formcomment.impl.{
  CommentExportActivitiesImpl,
  CommentExportWorkflowImpl,
  FundSubFormCommentNotificationActivitiesImpl,
  FundSubFormCommentNotificationWorkflowImpl
}
import anduin.workflow.fundsub.formcomment.{CommentExportActivities, FundSubFormCommentNotificationActivities}
import anduin.workflow.fundsub.fundactivity.impl.{
  FundSubDailyFundActivitiesEmailActivitiesImpl,
  FundSubDailyFundActivitiesEmailWorkflowImpl,
  FundSubWeeklyFundActivitiesEmailActivitiesImpl,
  FundSubWeeklyFundActivitiesEmailWorkflowImpl
}
import anduin.workflow.fundsub.fundactivity.{
  FundSubDailyFundActivitiesEmailActivities,
  FundSubWeeklyFundActivitiesEmailActivities
}
import anduin.workflow.fundsub.invitation.impl.{
  FundSubMultipleInvitationWorkflowImpl,
  FundSubSingleInvitationWorkflowImpl
}
import anduin.workflow.fundsub.newinvestor.FundSubNewInvestorReportEmailActivities
import anduin.workflow.fundsub.newinvestor.impl.{
  FundSubNewInvestorReportEmailActivitiesImpl,
  FundSubNewInvestorReportEmailWorkflowImpl
}
import anduin.workflow.fundsub.simulator.FundSubSimulatorSetupDemoOrdersActivities
import anduin.workflow.fundsub.simulator.impl.{
  FundSubSimulatorSetupDemoOrdersActivitiesImpl,
  FundSubSimulatorSetupDemoOrdersWorkflowImpl
}
import anduin.workflow.fundsub.syncinvestoraccess.FundSubAutoSaveSubscriptionDataActivities
import anduin.workflow.fundsub.syncinvestoraccess.impl.{
  FundSubAutoSaveSubscriptionDataActivitiesImpl,
  FundSubAutoSaveSubscriptionDataWorkflowImpl
}
import anduin.workflow.updateprofiletemplate.UpdateProfileTemplateActivities
import anduin.workflow.updateprofiletemplate.impl.{
  UpdateProfileTemplateActivitiesImpl,
  UpdateProfileTemplateWorkflowImpl
}
import com.anduin.stargazer.service.fundsub.operation.*

trait GondorCommonWorkflowModule
    extends GondorCommonServiceModule
    with GondorCoreWorkflowModule
    with FundSubWorkflowModule
    with FundSubDataWorkflowModule
    with DataRoomApiWorkflowModule
    with FundDataWorkflowModule {

  // Analytic
  lazy val fundSubBillingExportActivitiesImpl =
    ActivityImpl[FundSubBillingExportActivities, FundSubBillingExportActivitiesImpl](
      ActivityQueue.FundSubBillingExport,
      wire[FundSubBillingExportActivitiesImpl]
    )

  lazy val auditLogsExportActivitiesImpl =
    ActivityImpl[AuditLogsExportActivities, AuditLogsExportActivitiesImpl](
      ActivityQueue.AuditLogExport,
      wire[AuditLogsExportActivitiesImpl]
    )

  lazy val emailsExportActivitiesImpl = ActivityImpl[EmailsExportActivities, EmailsExportActivitiesImpl](
    ActivityQueue.EmailExport,
    wire[EmailsExportActivitiesImpl]
  )

  lazy val emailsExportForFundsActivitiesImpl =
    ActivityImpl[EmailsExportForFundsActivities, EmailsExportForFundsActivitiesImpl](
      ActivityQueue.EmailExportForFunds,
      wire[EmailsExportForFundsActivitiesImpl]
    )

  lazy val formAsaAnalyticActivitiesImpl =
    ActivityImpl[FormAsaAnalyticActivities, FormAsaAnalyticActivitiesImpl](
      ActivityQueue.FormAsaAnalytic,
      wire[FormAsaAnalyticActivitiesImpl]
    )

  lazy val userDataExportActivitiesImpl =
    ActivityImpl[UserDataExportActivities, UserDataExportActivitiesImpl](
      ActivityQueue.UserDataExport,
      wire[UserDataExportActivitiesImpl]
    )

  lazy val dataRoomInsightsExportActivitiesImpl =
    ActivityImpl[DataRoomInsightsExportActivities, DataRoomInsightsExportActivitiesImpl](
      ActivityQueue.DataRoomExport,
      wire[DataRoomInsightsExportActivitiesImpl]
    )

  lazy val sandboxSetupTimeExportActivitiesImpl =
    ActivityImpl[SandboxSetupTimeExportActivities, SandboxSetupTimeExportActivitiesImpl](
      ActivityQueue.SandboxSetupTimeExport,
      wire[SandboxSetupTimeExportActivitiesImpl]
    )

  lazy val fundSubLpSubmissionExportActivitiesImpl =
    ActivityImpl[FundSubLpSubmissionExportActivities, FundSubLpSubmissionExportActivitiesImpl](
      ActivityQueue.FundSubLpSubmissionExport,
      wire[FundSubLpSubmissionExportActivitiesImpl]
    )

  lazy val investorDataExportActivitiesImpl =
    ActivityImpl[InvestorDataExportActivities, InvestorDataExportActivitiesImpl](
      ActivityQueue.InvestorDataExport,
      wire[InvestorDataExportActivitiesImpl]
    )

  lazy val investorAccessAnalyticExportActivitiesImpl =
    ActivityImpl[InvestorAccessAnalyticExportActivities, InvestorAccessAnalyticExportActivitiesImpl](
      ActivityQueue.InvestorAccessAnalyticExport,
      wire[InvestorAccessAnalyticExportActivitiesImpl]
    )

  lazy val fundSubIAConfigExportActivitiesImpl =
    ActivityImpl[FundSubIAConfigExportActivities, FundSubIAConfigExportActivitiesImpl](
      ActivityQueue.FundSubIAConfigExport,
      wire[FundSubIAConfigExportActivitiesImpl]
    )

  lazy val fundSubCommentAnalyticExportActivitiesImpl =
    ActivityImpl[CommentAnalyticExportActivities, CommentAnalyticExportActivitiesImpl](
      ActivityQueue.CommentAnalyticExportActivities,
      wire[CommentAnalyticExportActivitiesImpl]
    )

  lazy val fundSubFormFieldSourceAnalyticExportActivitiesImpl =
    ActivityImpl[FundFormFieldSourceAnalyticsExportActivities, FundFormFieldSourceAnalyticsExportActivitiesImpl](
      ActivityQueue.FundFormFieldSourceAnalyticsExportActivities,
      wire[FundFormFieldSourceAnalyticsExportActivitiesImpl]
    )

  lazy val fundSubPermissionAnalyticsExportActivitiesImpl =
    ActivityImpl[FundSubPermissionAnalyticsExportActivities, FundSubPermissionAnalyticsExportActivitiesImpl](
      ActivityQueue.FundSubPermissionAnalyticsExportActivities,
      wire[FundSubPermissionAnalyticsExportActivitiesImpl]
    )

  // Simulator
  lazy val dataRoomSimulatorPopulateDataActivitiesImpl =
    ActivityImpl[DataRoomSimulatorPopulateDataActivities, DataRoomSimulatorPopulateDataActivitiesImpl](
      ActivityQueue.DataRoomSimulator,
      wire[DataRoomSimulatorPopulateDataActivitiesImpl]
    )

  lazy val fundSubSimulatorSetupDemoOrdersActivitiesImpl =
    ActivityImpl[FundSubSimulatorSetupDemoOrdersActivities, FundSubSimulatorSetupDemoOrdersActivitiesImpl](
      ActivityQueue.FundSubSimulator,
      wire[FundSubSimulatorSetupDemoOrdersActivitiesImpl]
    )

  // Cron
  lazy val dataRoomNewUploadNotificationActivitiesImpl =
    ActivityImpl[DataRoomNewUploadNotificationActivities, DataRoomNewUploadNotificationActivitiesImpl](
      ActivityQueue.DataRoomNewUploadNotification,
      wire[DataRoomNewUploadNotificationActivitiesImpl]
    )

  lazy val fundSubFormCommentNotificationActivitiesImpl =
    ActivityImpl[FundSubFormCommentNotificationActivities, FundSubFormCommentNotificationActivitiesImpl](
      ActivityQueue.FundSubFormCommentNotification,
      wire[FundSubFormCommentNotificationActivitiesImpl]
    )

  lazy val fundSubSyncDashboardActivitiesImpl =
    ActivityImpl[FundSubSyncDashboardActivities, FundSubSyncDashboardActivitiesImpl](
      ActivityQueue.FundSubSyncDashboardData,
      FundSubSyncDashboardActivitiesImpl()
    )

  lazy val syncSingleFundDashboardActivitiesImpl =
    ActivityImpl[SyncSingleFundDashboardActivities, SyncSingleFundDashboardActivitiesImpl](
      ActivityQueue.SyncSingleFundDashboardData,
      wire[SyncSingleFundDashboardActivitiesImpl]
    )

  lazy val sendBaitUploadSupportingDocEventActivityImpl =
    ActivityImpl[SendBaitUploadSupportingDocEventActivity, SendBaitUploadSupportingDocEventActivityImpl](
      ActivityQueue.FundSubSendBaitUploadSupportingDocEvent,
      wire[SendBaitUploadSupportingDocEventActivityImpl]
    )

  lazy val sendBaitSupportingDocReviewReadyEventActivityImpl =
    ActivityImpl[SendBaitSupportingDocReviewReadyEventActivity, SendBaitSupportingDocReviewReadyEventActivityImpl](
      ActivityQueue.FundSubSendBaitSupportingDocReviewReadyEvent,
      wire[SendBaitSupportingDocReviewReadyEventActivityImpl]
    )

  lazy val fundSubDailyFundActivitiesEmailActivitiesImpl =
    ActivityImpl[FundSubDailyFundActivitiesEmailActivities, FundSubDailyFundActivitiesEmailActivitiesImpl](
      ActivityQueue.FundSubDailyFundActivitiesEmail,
      wire[FundSubDailyFundActivitiesEmailActivitiesImpl]
    )

  lazy val fundSubWeeklyFundActivitiesEmailActivitiesImpl =
    ActivityImpl[FundSubWeeklyFundActivitiesEmailActivities, FundSubWeeklyFundActivitiesEmailActivitiesImpl](
      ActivityQueue.FundSubWeeklyFundActivitiesEmail,
      wire[FundSubWeeklyFundActivitiesEmailActivitiesImpl]
    )

  lazy val fundSubNewInvestorReportEmailActivitiesImpl =
    ActivityImpl[FundSubNewInvestorReportEmailActivities, FundSubNewInvestorReportEmailActivitiesImpl](
      ActivityQueue.FundSubNewInvestorReportEmail,
      wire[FundSubNewInvestorReportEmailActivitiesImpl]
    )

  lazy val commentExportActivitiesImpl = ActivityImpl[CommentExportActivities, CommentExportActivitiesImpl](
    ActivityQueue.CommentExport,
    wire[CommentExportActivitiesImpl]
  )

  lazy val fundSubDataExtractRequestNotificationActivitiesImpl =
    ActivityImpl[FundSubDataExtractRequestNotificationActivities, FundSubDataExtractRequestNotificationActivitiesImpl](
      ActivityQueue.FundSubDataExtractRequestNotification,
      wire[FundSubDataExtractRequestNotificationActivitiesImpl]
    )

  lazy val fundSubPrepareDummyDataForDataExtractActivitiesImpl =
    ActivityImpl
      .derived[FundSubPrepareDummyDataForDataExtractActivities, FundSubPrepareDummyDataForDataExtractActivitiesImpl]

  lazy val documentExpirationNotificationActivitiesImpl =
    ActivityImpl[DocumentExpirationNotificationActivities, DocumentExpirationNotificationActivitiesImpl](
      ActivityQueue.InvestorAccessDocumentExpirationNotification,
      wire[DocumentExpirationNotificationActivitiesImpl]
    )

  lazy val fundDataDocumentExpirationNotificationActivitiesImpl =
    ActivityImpl[FundDataDocumentExpirationNotificationActivities, FundDataDocumentExpirationNotificationActivitiesImpl](
      ActivityQueue.FundDataDocumentExpirationNotification,
      wire[FundDataDocumentExpirationNotificationActivitiesImpl]
    )

  lazy val fundDataAssessmentDueDateNotificationActivitiesImpl =
    ActivityImpl[FundDataAssessmentDueDateNotificationActivities, FundDataAssessmentDueDateNotificationActivitiesImpl](
      ActivityQueue.FundDataAssessmentDueDateNotification,
      wire[FundDataAssessmentDueDateNotificationActivitiesImpl]
    )

  lazy val fundDataBatchDocumentRequestWorkflowActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataBatchDocumentRequestWorkflowActivitiesImpl](
      ActivityQueue.FundDataBatchDocumentRequest,
      wire[FundDataBatchDocumentRequestWorkflowActivitiesImpl]
    )

  lazy val fundDataCreateOfflineSubscriptionsActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataCreateOfflineSubscriptionsActivitiesImpl](
      ActivityQueue.FundDataCreateOfflineSubscriptions,
      wire[FundDataCreateOfflineSubscriptionsActivitiesImpl]
    )

  lazy val fundDataCreateOfflineTransactionsActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataCreateOfflineTransactionsActivitiesImpl](
      ActivityQueue.FundDataCreateOfflineTransactions,
      wire[FundDataCreateOfflineTransactionsActivitiesImpl]
    )

  lazy val fundDataImportOrdersToTransactionActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataImportOrdersToTransactionsActivitiesImpl](
      ActivityQueue.FundDataImportOrdersToTransactions,
      wire[FundDataImportOrdersToTransactionsActivitiesImpl]
    )

  lazy val fundDataExtractInvestmentEntityInfoFromFilesActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataExtractInvestmentEntityInfoFromFilesActivitiesImpl](
      ActivityQueue.FundDataExtractInvestmentEntityInfoFromFiles,
      wire[FundDataExtractInvestmentEntityInfoFromFilesActivitiesImpl]
    )

  lazy val fundDataFundSubProfileConflictSyncNotificationActivitiesImpl = ActivityImpl[
    FundDataFundSubProfileConflictSyncNotificationActivities,
    FundDataFundSubProfileConflictSyncNotificationActivitiesImpl
  ](
    ActivityQueue.FundDataFundSubProfileConflictSyncNotification,
    wire[FundDataFundSubProfileConflictSyncNotificationActivitiesImpl]
  )

  lazy val fundDataFundSubSyncAllFirmsActivitiesImpl =
    ActivityImpl[FundDataFundSubSyncAllFirmsActivities, FundDataFundSubSyncAllFirmsActivitiesImpl](
      ActivityQueue.FundDataFundSubSyncAllFirms,
      wire[FundDataFundSubSyncAllFirmsActivitiesImpl]
    )

  lazy val fundDataFundSubSyncSingleFirmActivitiesImpl =
    ActivityImpl[FundDataFundSubSyncSingleFirmActivities, FundDataFundSubSyncSingleFirmActivitiesImpl](
      ActivityQueue.FundDataFundSubSyncSingleFirm,
      wire[FundDataFundSubSyncSingleFirmActivitiesImpl]
    )

  lazy val fundDataDocumentExpirationWebhookActivitiesImpl =
    ActivityImpl[FundDataDocumentExpirationWebhookActivities, FundDataDocumentExpirationWebhookActivitiesImpl](
      ActivityQueue.FundDataDocumentExpirationWebhook,
      wire[FundDataDocumentExpirationWebhookActivitiesImpl]
    )

  lazy val greylinCorrectionActivitiesImpl =
    ActivityImpl[GreylinCorrectionActivities, GreylinCorrectionActivitiesImpl](
      ActivityQueue.GreylinCorrection,
      wire[GreylinCorrectionActivitiesImpl]
    )

  // Public Api
  lazy val getApiFileDownloadWflActivityImpl =
    ActivityImpl[GetApiFileDownloadWflActivity, GetApiFileDownloadWflActivityImpl](
      ActivityQueue.GetApiFileDownload,
      wire[GetApiFileDownloadWflActivityImpl]
    )

  lazy val getRequestStatusWflActivityImpl = ActivityImpl[GetRequestStatusWflActivity, GetRequestStatusWflActivityImpl](
    ActivityQueue.GetRequestStatus,
    wire[GetRequestStatusWflActivityImpl]
  )

  lazy val bulkCreateOrdersApiWflActivityImpl =
    ActivityImpl[BulkCreateOrdersApiWflActivity, BulkCreateOrdersApiWflActivityImpl](
      ActivityQueue.BulkCreateOrders,
      wire[BulkCreateOrdersApiWflActivityImpl]
    )

  lazy val activateOfflineApiWflActivityImpl =
    ActivityImpl[ActivateOfflineApiWflActivity, ActivateOfflineApiWflActivityImpl](
      ActivityQueue.ActivateOfflineOrders,
      wire[ActivateOfflineApiWflActivityImpl]
    )

  lazy val getFundInvitationLinkApiWflActivityImpl =
    ActivityImpl[GetFundInvitationLinkApiWflActivity, GetFundInvitationLinkApiWflActivityImpl](
      ActivityQueue.GetFundInvitationLink,
      wire[GetFundInvitationLinkApiWflActivityImpl]
    )

  lazy val getOrdersFormDataApiWflActivityImpl =
    ActivityImpl[GetOrdersFormDataApiWflActivity, GetOrdersFormDataApiWflActivityImpl](
      ActivityQueue.GetOrdersFormData,
      wire[GetOrdersFormDataApiWflActivityImpl]
    )

  lazy val createWebhookEndpointWflActivityImpl =
    ActivityImpl[CreateWebhookEndpointWflActivity, CreateWebhookEndpointWflActivityImpl](
      ActivityQueue.CreateWebhookEndpoint,
      wire[CreateWebhookEndpointWflActivityImpl]
    )

  lazy val getWebhookWflActivityImpl = ActivityImpl[GetWebhookWflActivity, GetWebhookWflActivityImpl](
    ActivityQueue.GetWebhookEndpoint,
    wire[GetWebhookWflActivityImpl]
  )

  lazy val getAllFundWebhooksWfActivityImpl =
    ActivityImpl[GetAllFundWebhooksWfActivity, GetAllFundWebhooksWfActivityImpl](
      ActivityQueue.GetAllFundWebhooks,
      wire[GetAllFundWebhooksWfActivityImpl]
    )

  lazy val removeWebhookWflActivityImpl = ActivityImpl[RemoveWebhookWflActivity, RemoveWebhookWflActivityImpl](
    ActivityQueue.RemoveWebhook,
    wire[RemoveWebhookWflActivityImpl]
  )

  lazy val updateWebhookWflActivityImpl = ActivityImpl[UpdateWebhookWflActivity, UpdateWebhookWflActivityImpl](
    ActivityQueue.UpdateWebhook,
    wire[UpdateWebhookWflActivityImpl]
  )

  lazy val getStandardFormFieldsWflActivityImpl =
    ActivityImpl[GetStandardFormFieldsWflActivity, GetStandardFormFieldsWflActivityImpl](
      ActivityQueue.GetStandardFormFields,
      wire[GetStandardFormFieldsWflActivityImpl]
    )

  lazy val getStandardFormFieldWflActivityImpl =
    ActivityImpl[GetStandardFormFieldWflActivity, GetStandardFormFieldWflActivityImpl](
      ActivityQueue.GetStandardFormField,
      wire[GetStandardFormFieldWflActivityImpl]
    )

  lazy val genericJsonApiWfActivityImpl = ActivityImpl[GenericJsonApiWfActivity, GenericJsonApiWfActivityImpl](
    ActivityQueue.GenericJsonApi,
    wire[GenericJsonApiWfActivityImpl]
  )

  lazy val testApiWflActivityImpl = ActivityImpl[TestApiWflActivity, TestApiWflActivityImpl](
    ActivityQueue.TestApi,
    wire[TestApiWflActivityImpl]
  )

  lazy val fundSubDataExportActivityImpl = ActivityImpl[FundSubDataExportActivity, DataExportActivityImpl](
    ActivityQueue.FundSubDataExport,
    wire[DataExportActivityImpl]
  )

  lazy val fundSubUpdateSubscriptionDocReviewActivityImpl =
    ActivityImpl[FundSubUpdateSubscriptionDocReviewActivity, FundSubUpdateSubscriptionDocReviewActivityImpl](
      ActivityQueue.FundSubUpdateSubscriptionDocReview,
      wire[FundSubUpdateSubscriptionDocReviewActivityImpl]
    )

  lazy val fundSubSignCountersignReqActivitiesImpl =
    ActivityImpl[FundSubSignCountersignReqActivities, FundSubSignCountersignReqActivitiesImpl](
      ActivityQueue.FundSubCountersign,
      wire[FundSubSignCountersignReqActivitiesImpl]
    )

  lazy val fundSubBatchActionActivitiesImpl =
    ActivityImpl[FundSubBatchActionActivities, FundSubBatchActionActivitiesImpl](
      ActivityQueue.FundSubBatchAction,
      wire[FundSubBatchActionActivitiesImpl]
    )

  lazy val fundSubAutoSaveSubscriptionDataActivitiesImpl =
    ActivityImpl[FundSubAutoSaveSubscriptionDataActivities, FundSubAutoSaveSubscriptionDataActivitiesImpl](
      ActivityQueue.FundSubAutoSaveSubscriptionData,
      wire[FundSubAutoSaveSubscriptionDataActivitiesImpl]
    )

  lazy val fundSubOperationBulkAddEnvironmentActiviesImpl =
    ActivityImpl[BatchActionActivities, FundSubOperationBulkAddEnvironmentActiviesImpl](
      ActivityQueue.FundSubBulkAddEnvironment,
      wire[FundSubOperationBulkAddEnvironmentActiviesImpl]
    )

  lazy val fundSubOperationRemoveEnvironmentActiviesImpl =
    ActivityImpl[BatchActionActivities, FundSubOperationRemoveEnvironmentActiviesImpl](
      ActivityQueue.FundSubRemoveEnvironment,
      wire[FundSubOperationRemoveEnvironmentActiviesImpl]
    )

  // FundSub
  lazy val fundSubInviteFundManagerActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundSubInviteFundManagerActivitiesImpl](
      ActivityQueue.FundSubInviteFundManager,
      wire[FundSubInviteFundManagerActivitiesImpl]
    )

  lazy val fundSubMoveFundManagerActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundSubMoveFundManagerActivitiesImpl](
      ActivityQueue.FundSubMoveFundManager,
      wire[FundSubMoveFundManagerActivitiesImpl]
    )

  lazy val fundSubRemoveFundManagerActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundSubRemoveFundManagerActivitiesImpl](
      ActivityQueue.FundSubRemoveFundManager,
      wire[FundSubRemoveFundManagerActivitiesImpl]
    )

  lazy val fundSubAssignInvestorActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundSubAssignInvestorActivitiesImpl](
      ActivityQueue.FundSubAssignInvestor,
      wire[FundSubAssignInvestorActivitiesImpl]
    )

  lazy val fundSubUnassignInvestorActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundSubUnassignInvestorActivitiesImpl](
      ActivityQueue.FundSubUnassignInvestor,
      wire[FundSubUnassignInvestorActivitiesImpl]
    )

  lazy val dataRoomPostUploadActivitiesImpl =
    ActivityImpl[DataRoomPostUploadActivities, DataRoomPostUploadActivitiesImpl](
      ActivityQueue.DataRoomPostUpload,
      wire[DataRoomPostUploadActivitiesImpl]
    )

  lazy val storageFileSyncActivitiesImpl =
    ActivityImpl[StorageFileSyncActivities, StorageFileSyncActivitiesImpl](
      ActivityQueue.StorageFileSync,
      wire[StorageFileSyncActivitiesImpl]
    )

  // Investor Access
  lazy val updateProfileTemplateActivitiesImpl =
    ActivityImpl[UpdateProfileTemplateActivities, UpdateProfileTemplateActivitiesImpl](
      ActivityQueue.InvestorAccessUpdateProfileTemplate,
      wire[UpdateProfileTemplateActivitiesImpl]
    )

  // Data Protection
  lazy val dataRequestActivitiesImpl = ActivityImpl[DataRequestActivities, DataRequestActivitiesImpl](
    ActivityQueue.DataProtectionRequest,
    wire[DataRequestActivitiesImpl]
  )

  // Fund Data
  lazy val fundDataImportInvestorsFromSubscriptionActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataImportInvestorsFromSubscriptionActivitiesImpl](
      ActivityQueue.FundDataImportInvestorsFromSubscription,
      wire[FundDataImportInvestorsFromSubscriptionActivitiesImpl]
    )

  lazy val fundDataImportInvestorsBySpreadsheetActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataImportInvestorsBySpreadsheetActivitiesImpl](
      ActivityQueue.FundDataImportInvestorsBySpreadsheet,
      wire[FundDataImportInvestorsBySpreadsheetActivitiesImpl]
    )

  lazy val fundDataImportInvestmentEntitiesBySpreadsheetActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataImportInvestmentEntitiesBySpreadsheetActivitiesImpl](
      ActivityQueue.FundDataImportInvestmentEntitiesBySpreadsheet,
      wire[FundDataImportInvestmentEntitiesBySpreadsheetActivitiesImpl]
    )

  lazy val fundDataExportInvestmentEntitiesToSpreadsheetActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataExportInvestmentEntitiesToSpreadsheetActivitiesImpl](
      ActivityQueue.FundDataExportInvestmentEntitiesToSpreadsheet,
      wire[FundDataExportInvestmentEntitiesToSpreadsheetActivitiesImpl]
    )

  lazy val fundDataImportContactsBySpreadsheetActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataImportContactsBySpreadsheetActivitiesImpl](
      ActivityQueue.FundDataImportContactsBySpreadsheet,
      wire[FundDataImportContactsBySpreadsheetActivitiesImpl]
    )

  lazy val fundDataImportAssessmentsBySpreadsheetActivitiesImpl = ActivityImpl[
    BatchActionActivities,
    FundDataImportAssessmentsBySpreadsheetActivitiesImpl
  ](
    ActivityQueue.FundDataImportRiskAssessmentsBySpreadsheet,
    wire[FundDataImportAssessmentsBySpreadsheetActivitiesImpl]
  )

  lazy val fundDataComputeProfilesFromSpreadsheetActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataComputeProfilesFromSpreadsheetActivitiesImpl](
      ActivityQueue.FundDataComputeProfilesFromSpreadsheet,
      wire[FundDataComputeProfilesFromSpreadsheetActivitiesImpl]
    )

  lazy val fundDataImportDocumentsBySpreadsheetActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataImportDocumentBySpreadsheetActivitiesImpl](
      ActivityQueue.FundDataImportDocumentsBySpreadsheet,
      wire[FundDataImportDocumentBySpreadsheetActivitiesImpl]
    )

  lazy val fundDataImportProfilesFromSpreadsheetActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataImportProfilesFromSpreadsheetActivitiesImpl](
      ActivityQueue.FundDataImportProfilesFromSpreadsheet,
      wire[FundDataImportProfilesFromSpreadsheetActivitiesImpl]
    )

  lazy val fundDataMergeInvestorsActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataMergeInvestorsActivitiesImpl](
      ActivityQueue.FundDataMergeInvestors,
      wire[FundDataMergeInvestorsActivitiesImpl]
    )

  lazy val fundDataAssignInvestorsToClientGroupActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataAssignInvestorsToClientGroupActivitiesImpl](
      ActivityQueue.FundDataAssignInvestorsToClientGroup,
      wire[FundDataAssignInvestorsToClientGroupActivitiesImpl]
    )

  lazy val fundDataDeleteClientGroupActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataDeleteClientGroupActivitiesImpl](
      ActivityQueue.FundDataDeleteClientGroup,
      wire[FundDataDeleteClientGroupActivitiesImpl]
    )

  lazy val fundDataInviteMembersActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataInviteMembersActivitiesImpl](
      ActivityQueue.FundDataInviteMembers,
      wire[FundDataInviteMembersActivitiesImpl]
    )

  lazy val fundDataRemovePortalUsersActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataRemoveGuestsActivitiesImpl](
      ActivityQueue.FundDataRemoveGuests,
      wire[FundDataRemoveGuestsActivitiesImpl]
    )

  lazy val fundDataInviteGuestsActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataInviteGuestsActivitiesImpl](
      ActivityQueue.FundDataInviteGuests,
      wire[FundDataInviteGuestsActivitiesImpl]
    )

  lazy val fundDataNotifyGuestsActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataNotifyGuestsActivitiesImpl](
      ActivityQueue.FundDataNotifyGuests,
      wire[FundDataNotifyGuestsActivitiesImpl]
    )

  lazy val fundDataModifyGuestsAccessToOpportunityPagesActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataModifyGuestsAccessToOpportunityPagesActivitiesImpl](
      ActivityQueue.FundDataModifyGuestsAccessToOpportunityPages,
      wire[FundDataModifyGuestsAccessToOpportunityPagesActivitiesImpl]
    )

  lazy val fundDataExportContactsToSpreadsheetActivitiesImpl =
    ActivityImpl[BatchActionActivities, FundDataExportContactsToSpreadsheetActivitiesImpl](
      ActivityQueue.FundDataExportContactsToSpreadsheet,
      wire[FundDataExportContactsToSpreadsheetActivitiesImpl]
    )

  // Data Extract
  lazy val dataExtractGetSingleFileMappingResultActivityImpl =
    ActivityImpl[BatchActionActivities, DataExtractGetSingleFileMappingResultActivityImpl](
      ActivityQueue.DataExtractGetUserDocumentsMappingResult,
      wire[DataExtractGetSingleFileMappingResultActivityImpl]
    )

  lazy val dataExtractGenerateAnalyticsReportActivitiesImpl =
    ActivityImpl[BatchActionActivities, DataExtractGenerateAnalyticsReportActivitiesImpl](
      ActivityQueue.DataExtractGenerateAnalyticsReport,
      wire[DataExtractGenerateAnalyticsReportActivitiesImpl]
    )

  lazy val dataExtractGenerateDebugReportActivitiesImpl =
    ActivityImpl[BatchActionActivities, DataExtractGenerateDebugReportActivitiesImpl](
      ActivityQueue.DataExtractGenerateDebugReport,
      wire[DataExtractGenerateDebugReportActivitiesImpl]
    )

  // Ontology
  lazy val initOntologySpaceActivitiesImpl =
    ActivityImpl[InitOntologySpaceActivities, InitOntologySpaceActivitiesImpl](
      ActivityQueue.InitOntologySpace,
      wire[InitOntologySpaceActivitiesImpl]
    )

  // Autofill
  lazy val computeFormMatchingActivitiesImpl =
    ActivityImpl[ComputeFormMatchingActivities, ComputeFormMatchingActivitiesImpl](
      ActivityQueue.ComputeFormMatching,
      wire[ComputeFormMatchingActivitiesImpl]
    )

  lazy val cleanExpiredAccountTokenActivitiesImpl =
    ActivityImpl[CleanExpiredAccountTokenActivities, CleanExpiredAccountTokenActivitiesImpl](
      ActivityQueue.AccountToken,
      wire[CleanExpiredAccountTokenActivitiesImpl]
    )

  lazy val batchUploadFlowActivityImpl = ActivityImpl.derived[BatchUploadFlowActivity, BatchUploadFlowActivityImpl]

  // Annotations / PDF Tool
  lazy val analyzePdfAnnotationsActivitiesImpl =
    ActivityImpl[BatchActionActivities, AnalyzePdfAnnotationsActivitiesImpl](
      ActivityQueue.AnalyzePdfAnnotations,
      wire[AnalyzePdfAnnotationsActivitiesImpl]
    )

}

object GondorCommonWorkflowModule {

  private lazy val commonWorkflows: List[WorkflowImpl[?, ?]] = List(
    // Temporal
    GondorCoreWorkflowModule.temporalLockWorkflowImpl,
    GondorCoreWorkflowModule.batchUploadFlowImpl,
    GondorCoreWorkflowModule.batchUploadFlowQueryWorkflowImpl,
    // Analytic
    FundSubBillingExportWorkflowImpl.instance,
    AuditLogsExportWorkflowImpl.instance,
    EmailsExportWorkflowImpl.instance,
    EmailsExportForFundsWorkflowImpl.instance,
    DataRoomInsightsExportWorkflowImpl.instance,
    UserDataExportWorkflowImpl.instance,
    SandboxSetupTimeExportWorkflowImpl.instance,
    FundSubLpSubmissionExportWorkflowImpl.instance,
    InvestorAccessAnalyticExportWorkflowImpl.instance,
    FundSubIAConfigExportWorkflowImpl.instance,
    CommentAnalyticExportWorkflowImpl.instance,
    FundFormFieldSourceAnalyticsExportWorkflowImpl.instance,
    FundSubPermissionAnalyticsExportWorkflowImpl.instance,
    // Simulator
    DataRoomSimulatorPopulateDataWorkflowImpl.instance,
    FundSubSimulatorSetupDemoOrdersWorkflowImpl.instance,
    // Fundsub
    FundSubMultipleInvitationWorkflowImpl.instance,
    FundSubSingleInvitationWorkflowImpl.instance,
    SingleDataImportWorkflowImpl.instance,
    MultipleDataImportWorkflowImpl.instance,
    SingleDataExportWorkflowImpl.instance,
    MultipleDataExportWorkflowImpl.instance,
    FundSubUpdateSubscriptionDocReviewMultipleWorkflowImpl.instance,
    FundSubUpdateSubscriptionDocReviewSingleWorkflowImpl.instance,
    FundSubBatchSignCountersignReqWorkflowImpl.instance,
    FundSubSingleCountersignWorkflowImpl.instance,
    FundSubBatchActionWorkflowImpl.instance,
    FundSubSingleActionWorkflowImpl.instance,
    CommentExportWorkflowImpl.instance,
    FundSubAutoSaveSubscriptionDataWorkflowImpl.instance,
    FundSubOperationBulkAddEnvironmentWorkflowImpl.instance,
    FundSubOperationBulkAddEnvironmentWorkflowItemImpl.instance,
    FundSubOperationRemoveEnvironmentWorkflowImpl.instance,
    FundSubOperationRemoveEnvironmentWorkflowItemImpl.instance,
    FundSubInviteFundManagerWorkflowImpl.instance,
    FundSubInviteFundManagerWorkflowItemImpl.instance,
    FundSubMoveFundManagerWorkflowImpl.instance,
    FundSubMoveFundManagerWorkflowItemImpl.instance,
    FundSubRemoveFundManagerWorkflowImpl.instance,
    FundSubRemoveFundManagerWorkflowItemImpl.instance,
    FundSubAssignInvestorWorkflowImpl.instance,
    FundSubAssignInvestorWorkflowItemImpl.instance,
    FundSubUnassignInvestorWorkflowImpl.instance,
    FundSubUnassignInvestorWorkflowItemImpl.instance,
    // DataRoom
    DmsSearchIndexWorkflowImpl.instance,
    RagIndexDocumentWorkflowImpl.instance,
    RagBatchIndexDocumentWorkflowImpl.instance,
    RagUpdateIndexWorkflowImpl.instance,
    DataRoomPostUploadWorkflowImpl.instance,
    StorageFileSyncWorkflowImpl.instance,
    // Investor Access,
    UpdateProfileTemplateWorkflowImpl.instance,
    // Data Protection
    DataRequestWorkflowImpl.instance,
    // Fund Data
    FundDataImportInvestorsFromSubscriptionWorkflowImpl.instance,
    FundDataImportInvestorsFromSubscriptionWorkflowItemImpl.instance,
    FundDataImportInvestorsBySpreadsheetWorkflowImpl.instance,
    FundDataImportInvestorsBySpreadsheetWorkflowItemImpl.instance,
    FundDataImportInvestmentEntitiesBySpreadsheetWorkflowImpl.instance,
    FundDataImportInvestmentEntitiesBySpreadsheetWorkflowItemImpl.instance,
    FundDataExportInvestmentEntitiesToSpreadsheetWorkflowImpl.instance,
    FundDataExportInvestmentEntitiesToSpreadsheetWorkflowItemImpl.instance,
    FundDataImportContactsBySpreadsheetWorkflowImpl.instance,
    FundDataImportContactsBySpreadsheetWorkflowItemImpl.instance,
    FundDataImportRiskAssessmentsBySpreadsheetWorkflowImpl.instance,
    FundDataImportRiskAssessmentsBySpreadsheetWorkflowItemImpl.instance,
    FundDataComputeProfilesFromSpreadsheetWorkflowImpl.instance,
    FundDataComputeProfilesFromSpreadsheetWorkflowItemImpl.instance,
    FundDataImportProfilesFromSpreadsheetWorkflowImpl.instance,
    FundDataImportProfilesFromSpreadsheetWorkflowItemImpl.instance,
    FundDataImportDocumentsBySpreadsheetWorkflowImpl.instance,
    FundDataImportDocumentBySpreadsheetWorkflowItemImpl.instance,
    FundDataBatchDocumentRequestWorkflowImpl.instance,
    FundDataBatchDocumentRequestWorkflowItemImpl.instance,
    FundDataMergeInvestorsWorkflowImpl.instance,
    FundDataMergeInvestorsWorkflowItemImpl.instance,
    FundDataAssignInvestorsToClientGroupWorkflowImpl.instance,
    FundDataAssignInvestorsToClientGroupWorkflowItemImpl.instance,
    FundDataDeleteClientGroupWorkflowImpl.instance,
    FundDataDeleteClientGroupWorkflowItemImpl.instance,
    FundDataInviteMembersWorkflowImpl.instance,
    FundDataInviteMembersWorkflowItemImpl.instance,
    FundDataRemoveGuestsWorkflowImpl.instance,
    FundDataRemoveGuestsWorkflowItemImpl.instance,
    FundDataInviteGuestsWorkflowImpl.instance,
    FundDataInviteGuestsWorkflowItemImpl.instance,
    FundDataNotifyGuestsWorkflowImpl.instance,
    FundDataNotifyGuestsWorkflowItemImpl.instance,
    FundDataModifyGuestsAccessToOpportunityPagesWorkflowImpl.instance,
    FundDataModifyGuestsAccessToOpportunityPagesWorkflowItemImpl.instance,
    FundDataCreateOfflineSubscriptionsWorkflowImpl.instance,
    FundDataCreateOfflineSubscriptionsWorkflowItemImpl.instance,
    FundDataCreateOfflineTransactionsWorkflowImpl.instance,
    FundDataCreateOfflineTransactionsWorkflowItemImpl.instance,
    FundDataImportOrdersToTransactionsWorkflowImpl.instance,
    FundDataImportOrdersToTransactionsWorkflowItemImpl.instance,
    FundDataExtractInvestmentEntityInfoFromFilesWorkflowImpl.instance,
    FundDataExtractInvestmentEntityInfoFromFilesWorkflowItemImpl.instance,
    FundDataExportContactsToSpreadsheetWorkflowImpl.instance,
    FundDataExportContactsToSpreadsheetWorkflowItemImpl.instance,
    // Data Extract
    DataExtractGetUserDocumentsMappingResultWorkflowImpl.instance,
    DataExtractGetUserDocumentsMappingResultItemWorkflowImpl.instance,
    DataExtractGenerateAnalyticsReportWorkflowImpl.instance,
    DataExtractGenerateAnalyticsReportItemWorkflowImpl.instance,
    DataExtractGenerateDebugReportWorkflowImpl.instance,
    DataExtractGenerateDebugReportItemWorkflowImpl.instance,
    // Ontology
    InitOntologySpaceWorkflowImpl.instance,
    // Autofill
    ComputeFormMatchingWorkflowImpl.instance,
    // Annotations / PDF Tool
    AnalyzePdfAnnotationsWorkflowImpl.instance,
    AnalyzePdfAnnotationsWorkflowItemImpl.instance
  ) ++ FundDataWorkflowModule.allWorkflows

  private def commonActivities(gondorModule: GondorCommonWorkflowModule): List[ActivityImpl[?, ?]] = List(
    gondorModule.temporalLockActivityImpl,
    gondorModule.batchUploadFlowActivityImpl,
    // Analytic
    gondorModule.fundSubBillingExportActivitiesImpl,
    gondorModule.auditLogsExportActivitiesImpl,
    gondorModule.emailsExportActivitiesImpl,
    gondorModule.emailsExportForFundsActivitiesImpl,
    gondorModule.formAsaAnalyticActivitiesImpl,
    gondorModule.userDataExportActivitiesImpl,
    gondorModule.dataRoomInsightsExportActivitiesImpl,
    gondorModule.sandboxSetupTimeExportActivitiesImpl,
    gondorModule.fundSubLpSubmissionExportActivitiesImpl,
    gondorModule.investorDataExportActivitiesImpl,
    gondorModule.investorAccessAnalyticExportActivitiesImpl,
    gondorModule.fundSubIAConfigExportActivitiesImpl,
    gondorModule.fundSubCommentAnalyticExportActivitiesImpl,
    gondorModule.fundSubFormFieldSourceAnalyticExportActivitiesImpl,
    gondorModule.fundSubPermissionAnalyticsExportActivitiesImpl,
    // Simulator
    gondorModule.dataRoomSimulatorPopulateDataActivitiesImpl,
    gondorModule.fundSubSimulatorSetupDemoOrdersActivitiesImpl,
    // Fundsub
    gondorModule.fundSubInvitationActivitiesImpl,
    gondorModule.fundSubDataImportActivityImpl,
    gondorModule.fundSubDataExportActivityImpl,
    gondorModule.fundSubUpdateSubscriptionDocReviewActivityImpl,
    gondorModule.fundSubSignCountersignReqActivitiesImpl,
    gondorModule.fundSubBatchActionActivitiesImpl,
    gondorModule.commentExportActivitiesImpl,
    gondorModule.fundSubAutoSaveSubscriptionDataActivitiesImpl,
    gondorModule.fundSubOperationBulkAddEnvironmentActiviesImpl,
    gondorModule.fundSubOperationRemoveEnvironmentActiviesImpl,
    gondorModule.fundSubInviteFundManagerActivitiesImpl,
    gondorModule.fundSubMoveFundManagerActivitiesImpl,
    gondorModule.fundSubRemoveFundManagerActivitiesImpl,
    gondorModule.fundSubAssignInvestorActivitiesImpl,
    gondorModule.fundSubUnassignInvestorActivitiesImpl,
    // Data room
    gondorModule.dmsSearchIndexActivityImpl,
    gondorModule.ragIndexDocumentActivitiesImpl,
    gondorModule.ragUpdateIndexActivitiesImpl,
    gondorModule.dataRoomPostUploadActivitiesImpl,
    gondorModule.storageFileSyncActivitiesImpl,
    // Investor Access,
    gondorModule.updateProfileTemplateActivitiesImpl,
    // Data Protection
    gondorModule.dataRequestActivitiesImpl,
    // Fund Data
    gondorModule.fundDataImportInvestorsFromSubscriptionActivitiesImpl,
    gondorModule.fundDataImportInvestorsBySpreadsheetActivitiesImpl,
    gondorModule.fundDataImportInvestmentEntitiesBySpreadsheetActivitiesImpl,
    gondorModule.fundDataExportInvestmentEntitiesToSpreadsheetActivitiesImpl,
    gondorModule.fundDataImportContactsBySpreadsheetActivitiesImpl,
    gondorModule.fundDataImportAssessmentsBySpreadsheetActivitiesImpl,
    gondorModule.fundDataComputeProfilesFromSpreadsheetActivitiesImpl,
    gondorModule.fundDataImportDocumentsBySpreadsheetActivitiesImpl,
    gondorModule.fundDataImportProfilesFromSpreadsheetActivitiesImpl,
    gondorModule.fundDataBatchDocumentRequestWorkflowActivitiesImpl,
    gondorModule.fundDataMergeInvestorsActivitiesImpl,
    gondorModule.fundDataAssignInvestorsToClientGroupActivitiesImpl,
    gondorModule.fundDataDeleteClientGroupActivitiesImpl,
    gondorModule.fundDataInviteMembersActivitiesImpl,
    gondorModule.fundDataRemovePortalUsersActivitiesImpl,
    gondorModule.fundDataInviteGuestsActivitiesImpl,
    gondorModule.fundDataNotifyGuestsActivitiesImpl,
    gondorModule.fundDataModifyGuestsAccessToOpportunityPagesActivitiesImpl,
    gondorModule.fundDataCreateOfflineSubscriptionsActivitiesImpl,
    gondorModule.fundDataCreateOfflineTransactionsActivitiesImpl,
    gondorModule.fundDataImportOrdersToTransactionActivitiesImpl,
    gondorModule.fundDataExtractInvestmentEntityInfoFromFilesActivitiesImpl,
    gondorModule.fundDataExportContactsToSpreadsheetActivitiesImpl,
    // Data Extract
    gondorModule.dataExtractGetSingleFileMappingResultActivityImpl,
    gondorModule.dataExtractGenerateAnalyticsReportActivitiesImpl,
    gondorModule.dataExtractGenerateDebugReportActivitiesImpl,
    // Ontology
    gondorModule.initOntologySpaceActivitiesImpl,
    // Autofill
    gondorModule.computeFormMatchingActivitiesImpl,
    // Investor Portal
    gondorModule.investorPortalFlowActivityImpl,
    gondorModule.investorPortalFileActivityImpl,
    gondorModule.investorPortalDocDistributionActivityImpl,
    gondorModule.docDistributionFlowActivityImpl,
    gondorModule.fundDataContactActivityImpl,
    // Annotations / PDF Tool
    gondorModule.analyzePdfAnnotationsActivitiesImpl
  )

  lazy val cronWorkflows: List[WorkflowImpl[?, ?]] = List(
    FormAsaAnalyticCronWorkflowImpl.instance,
    FormAsaAnalyticWorkflowImpl.instance,
    DataRoomNewUploadNotificationWorkflowImpl.instance,
    FundSubFormCommentNotificationWorkflowImpl.instance,
    FundSubSyncDashboardWorkflowImpl.instance,
    SyncSingleFundDashboardWorkflowImpl.instance,
    SendBaitUploadSupportingDocEventWorkflowImpl.instance,
    SendBaitSupportingDocReviewReadyEventWorkflowImpl.instance,
    FundSubDailyFundActivitiesEmailWorkflowImpl.instance,
    FundSubWeeklyFundActivitiesEmailWorkflowImpl.instance,
    FundSubNewInvestorReportEmailWorkflowImpl.instance,
    DocumentExpirationNotificationWorkflowImpl.instance,
    FundDataDocumentExpirationNotificationWorkflowImpl.instance,
    FundDataAssessmentDueDateNotificationWorkflowImpl.instance,
    FundDataFundSubProfileConflictSyncNotificationWorkflowImpl.instance,
    GreylinCorrectionWorkflowImpl.instance,
    InvestorDataExportCronWorkflowImpl.instance,
    InvestorDataExportWorkflowImpl.instance,
    FundDataFundSubSyncAllFirmsWorkflowImpl.instance,
    FundDataFundSubSyncSingleFirmWorkflowImpl.instance,
    FundDataDocumentExpirationWebhookWorkflowImpl.instance,
    FundSubDataExtractRequestNotificationWorkflowImpl.instance,
    FundSubPrepareDummyDataForDataExtractWorkflowImpl.instance,
    CleanExpiredAccountTokenWorkflowImpl.instance
  )

  def cronActivities(gondorModule: GondorCommonWorkflowModule): List[ActivityImpl[?, ?]] = List(
    gondorModule.dataRoomNewUploadNotificationActivitiesImpl,
    gondorModule.fundSubFormCommentNotificationActivitiesImpl,
    gondorModule.fundSubSyncDashboardActivitiesImpl,
    gondorModule.syncSingleFundDashboardActivitiesImpl,
    gondorModule.sendBaitUploadSupportingDocEventActivityImpl,
    gondorModule.sendBaitSupportingDocReviewReadyEventActivityImpl,
    gondorModule.fundSubDailyFundActivitiesEmailActivitiesImpl,
    gondorModule.fundSubWeeklyFundActivitiesEmailActivitiesImpl,
    gondorModule.fundSubNewInvestorReportEmailActivitiesImpl,
    gondorModule.documentExpirationNotificationActivitiesImpl,
    gondorModule.fundDataDocumentExpirationNotificationActivitiesImpl,
    gondorModule.fundDataAssessmentDueDateNotificationActivitiesImpl,
    gondorModule.fundDataFundSubProfileConflictSyncNotificationActivitiesImpl,
    gondorModule.greylinCorrectionActivitiesImpl,
    gondorModule.fundDataFundSubSyncAllFirmsActivitiesImpl,
    gondorModule.fundDataFundSubSyncSingleFirmActivitiesImpl,
    gondorModule.fundDataDocumentExpirationWebhookActivitiesImpl,
    gondorModule.fundSubDataExtractRequestNotificationActivitiesImpl,
    gondorModule.fundSubPrepareDummyDataForDataExtractActivitiesImpl,
    gondorModule.cleanExpiredAccountTokenActivitiesImpl
  )

  def dataIntegrationActivities(gondorModule: GondorCommonWorkflowModule): List[ActivityImpl[?, ?]] = List(
    FundSubDataWorkflowModule.allActivities(gondorModule)
  ).flatten

  def asyncApiActivities(gondorModule: GondorCommonWorkflowModule): List[ActivityImpl[?, ?]] =
    gondorModule.asyncApiActivities

  private lazy val publicApiWorkflows: List[WorkflowImpl[?, ?]] = List(
    GetApiFileDownloadWorkflowImpl.instance,
    GetRequestStatusWorkflowImpl.instance,
    BulkCreateOrdersWorkflowImpl.instance,
    ActivateOfflineOrderApiWorkflowImpl.instance,
    GetFundInvitationLinkApiWorkflowImpl.instance,
    GetOrdersFormDataApiWorkflowImpl.instance,
    CreateWebhookEndpointWorkflowImpl.instance,
    GetWebhookEndpointWorkflowImpl.instance,
    GetAllFundWebhooksWorkflowImpl.instance,
    RemoveWebhookWorkflowImpl.instance,
    UpdateWebhookWorkflowImpl.instance,
    GetStandardFormFieldsWorkflowImpl.instance,
    GetStandardFormFieldWorkflowImpl.instance,
    GenericJsonApiWorkflowImpl.instance,
    TestApiWorkflowImpl.instance
  ) ++ DataRoomApiWorkflowModule.workflows

  private lazy val dataIntegrationWorkflows: List[WorkflowImpl[?, ?]] = List(
    FundSubDataWorkflowModule.allWorkflows
  ).flatten

  private def publicApiActivities(gondorModule: GondorCommonWorkflowModule): List[ActivityImpl[?, ?]] = List(
    gondorModule.getApiFileDownloadWflActivityImpl,
    gondorModule.getRequestStatusWflActivityImpl,
    gondorModule.bulkCreateOrdersApiWflActivityImpl,
    gondorModule.activateOfflineApiWflActivityImpl,
    gondorModule.getFundInvitationLinkApiWflActivityImpl,
    gondorModule.getOrdersFormDataApiWflActivityImpl,
    gondorModule.createWebhookEndpointWflActivityImpl,
    gondorModule.getWebhookWflActivityImpl,
    gondorModule.getAllFundWebhooksWfActivityImpl,
    gondorModule.removeWebhookWflActivityImpl,
    gondorModule.updateWebhookWflActivityImpl,
    gondorModule.getStandardFormFieldsWflActivityImpl,
    gondorModule.getStandardFormFieldWflActivityImpl,
    gondorModule.genericJsonApiWfActivityImpl,
    gondorModule.testApiWflActivityImpl
  ) ++ DataRoomApiWorkflowModule.activities(gondorModule)

  private lazy val asyncApiWorkflows: List[WorkflowImpl[?, ?]] = GondorCoreWorkflowModule.asyncApiWorkflows

  lazy val serviceWorkflows: List[WorkflowImpl[?, ?]] =
    commonWorkflows ++ publicApiWorkflows ++ dataIntegrationWorkflows ++ asyncApiWorkflows

  lazy val allWorkflows: List[WorkflowImpl[?, ?]] = serviceWorkflows ++ cronWorkflows

  def serviceActivities(gondorModule: GondorCommonWorkflowModule): List[ActivityImpl[?, ?]] =
    commonActivities(gondorModule) ++ publicApiActivities(gondorModule) ++ dataIntegrationActivities(
      gondorModule
    ) ++ asyncApiActivities(gondorModule)

  def allActivities(gondorModule: GondorCommonWorkflowModule): List[ActivityImpl[?, ?]] =
    serviceActivities(gondorModule) ++ cronActivities(gondorModule)

}
