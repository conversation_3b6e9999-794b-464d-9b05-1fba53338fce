// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service.cron.account

import zio.Task

import anduin.account.cron.{CleanExpiredAccountTokenParams, CleanExpiredAccountTokenWorkflowImpl}
import anduin.workflow.cron.TemporalScheduleUtils

final case class CleanExpiredAccountTokenCron(
  temporalScheduleUtils: TemporalScheduleUtils
) {

  def start: Task[Unit] = {
    temporalScheduleUtils
      .startDaily(
        "clean-expired-account-token-daily",
        CleanExpiredAccountTokenWorkflowImpl.instance
      )(
        _.cleanExpiredToken(
          CleanExpiredAccountTokenParams()
        )
      )
      .unit
  }

}
