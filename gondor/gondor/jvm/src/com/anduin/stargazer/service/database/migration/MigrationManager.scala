// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service.database.migration

import zio.{Task, ZIO}

import com.anduin.stargazer.module.GondorCommonServiceModule

object MigrationManager {

  /*
   * Put your NORMAL migration scripts here.
   * Every script must have a comment line to show script owner, date added and sprint added.
   * After cutting new release, <PERSON><PERSON><PERSON> will go here and delete old scripts. He will also ask
   * script owner to cleanup.
   * Command to run migration script:
   * ./millw runMigration
   *
   * Remember to backup your db before running the script:
   * ./project/tools/local-deps/backup-tools.sh backup releaseA-db
   * To restore db:
   * ./project/tools/local-deps/backup-tools.sh restore releaseA-db
   */
  def run(
    using gondorModule: GondorCommonServiceModule
  ): Task[Unit] = {
    val _ = gondorModule // Do not remove this, this is needed if migration scripts require it.
    for {
      _ <- ZIO.logInfo("Starting Migration Manager")
    } yield ()
  }

}
