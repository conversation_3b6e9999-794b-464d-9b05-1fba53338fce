// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tflow.test.docshare

import anduin.tflow.TFlowSpec

object ExampleDocShareWorkflowSpec extends TFlowSpec {
  type Command = CoreDocShareCommand.NonEmpty
  type CommandResponse = CoreDocShareResponse

  type Query = CoreDocShareQuery.NonEmpty
  type QueryResponse = CoreDocShareQueryResponse

  type WorkflowEvent = Create
  type WorkflowResponse = Created

  type Reject = CoreDocShareReject.NonEmpty

  type State = DocShareState
  type StoreActivity = ExampleDocShareStoreActivity.type
}
