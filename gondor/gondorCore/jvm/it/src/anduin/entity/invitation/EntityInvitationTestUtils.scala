// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.entity.invitation

import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.entity.model.{EntityRestrictedModel, EntityRole}
import anduin.entity.repository.EntityRepository
import anduin.entity.service.invitation.*
import anduin.id.entity.EntityId
import anduin.model.common.user.UserId
import anduin.model.user.FullName
import anduin.service.entity.invitation.EntityInvitationService
import anduin.team.TeamService
import zio.test.*

object EntityInvitationTestUtils {

  def invite(
    inviter: UserId,
    invitee: UserId,
    entityId: EntityId,
    inviteeRole: EntityRole = EntityRole.Member
  )(
    using invitationService: EntityInvitationService,
    userProfileService: UserProfileService,
    teamService: TeamService
  ): Task[TestResult] = {
    for {
      entityRestrictedModel <- EntityRepository.getEntityRestricted(entityId)
      containInviteeAtFirst = entityRestrictedModel.membersInfo.contains(invitee)

      email <- userProfileService.getEmailAddress(invitee)
      _ <-
        invitationService
          .inviteInternal(
            EntityInviteParams(
              invitees = List(
                EntityInvitationInfo(
                  inviteeEmail = email.address,
                  inviteeName = FullName("First", "Last"),
                  entityId = entityId,
                  entityRole = inviteeRole
                )
              )
            ),
            inviter
          )
          .either

      entityRestricted <- EntityRepository.getEntityRestricted(entityId)
      teamId <- ZIO.succeed(entityRestricted.entityTeamId)
      joinedUsers <- teamService.getJoinedMembers(teamId)

    } yield {
      val memberInfo = entityRestricted.membersInfo.get(invitee)
      assertTrue(
        !containInviteeAtFirst && memberInfo != None &&
          memberInfo.map(_.role).contains(inviteeRole) &&
          joinedUsers.contains(invitee)
      )
    }
  }

  def removeEntityMember(
    actor: UserId,
    removeUser: UserId,
    entityId: EntityId
  )(
    using entityInvitationService: EntityInvitationService,
    teamService: TeamService
  ): Task[TestResult] = {
    for {
      _ <- entityInvitationService
        .removeEntityMember(
          RemoveEntityMemberParams(removeUser, entityId),
          actor
        )
        .either

      entityRestricted <- EntityRepository.getEntityRestricted(entityId)
      teamId <- ZIO.succeed(entityRestricted.entityTeamId)
      joinedUsers <- teamService.getJoinedMembers(teamId)
      invitedUsers <- teamService.getInvitedUsers(teamId)
    } yield {
      assertTrue(
        !joinedUsers.contains(removeUser) &&
          !invitedUsers.contains(removeUser) &&
          !entityRestricted.membersInfo.contains(removeUser)
      )
    }
  }

  def changeEntityMemberRole(
    actor: UserId,
    member: UserId,
    entityId: EntityId,
    newRole: EntityRole
  )(
    using invitationService: EntityInvitationService
  ): Task[TestResult] = {
    for {
      params <- ZIO.attempt(
        ChangeEntityMemberRoleParams(
          entityId,
          member,
          newRole
        )
      )
      _ <- invitationService.changeEntityMemberRole(params, actor).either
      entityRestrictedModel <- EntityRepository.getEntityRestricted(entityId)

    } yield {
      val memberInfo = entityRestrictedModel.membersInfo.get(member)
      assertTrue(memberInfo.get.role == newRole)
    }
  }

}
