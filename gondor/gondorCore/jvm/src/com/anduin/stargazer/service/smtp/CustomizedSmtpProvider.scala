// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service.smtp

import java.util.Properties

import com.anduin.stargazer.service.EmailResponse
import com.anduin.stargazer.service.email.utils.MimeUtils
import com.anduin.stargazer.service.email.{Email, EmailProvider, EmailProviderBuilderConfig}
import jakarta.mail.internet.{InternetAddress, MimeMessage}
import jakarta.mail.{Authenticator, PasswordAuthentication, Session, Transport}

import com.anduin.stargazer.service.api.FileDownloadService
import com.anduin.stargazer.service.email.store.{EmailStoreService, ExternalEmailProvider}
import zio.{Task, ZIO}
import scala.util.Random

import io.opentelemetry.api.trace.SpanKind

import anduin.email.provider.{EmailProviderModel, EmailProviderService}
import anduin.encryption.StreamingEncryptionService
import anduin.model.common.emailaddress.EmailAddress
import anduin.telemetry.TelemetryEnvironment
import com.anduin.stargazer.external.base64.Base64
import com.anduin.stargazer.service.utils.ZIOTelemetryUtils

final case class CustomizedSmtpProvider(
  config: EmailProviderBuilderConfig.CustomizedSmtpBuilderConfig,
  mimeUtils: MimeUtils,
  emailStoreService: EmailStoreService,
  emailProviderService: EmailProviderService,
  encryptionService: StreamingEncryptionService,
  tracingEnvironment: TelemetryEnvironment.Tracing
)(
  using val fileDownloadService: FileDownloadService
) extends EmailProvider {

  private def getProps(provider: EmailProviderModel): Properties = {
    val prop = new Properties()
    prop.put("mail.smtp.host", provider.host)
    prop.put("mail.smtp.port", provider.port.toString)
    prop.put("mail.smtp.auth", "true")
    prop.put("mail.smtp.starttls.enable", provider.tls.toString)
    // connect timeout
    prop.put("mail.smtp.connectiontimeout", "1000")
    prop.put("mail.smtps.connectiontimeout", "1000")
    // read timeout
    prop.put("mail.smtp.timeout", "3000")
    prop.put("mail.smtps.timeout", "3000")
    // write timeout
    prop.put("mail.smtp.writetimeout", "3000")
    prop.put("mail.smtps.writetimeout", "3000")
    prop
  }

  override def sendEmail(email: Email): Task[EmailResponse] = {
    val task = for {
      provider <- emailProviderService.getEmailProvider(config.providerId)
      _ <- ZIOTelemetryUtils.withTracing { tracing =>
        for {
          _ <- tracing.setAttribute("providerId", provider.id.idString)
          _ <- tracing.setAttribute("host", provider.host)
          _ <- tracing.setAttribute("port", provider.port)
          _ <- tracing.setAttribute("tls", provider.tls)
          _ <- tracing.setAttribute("username", provider.userName)
          _ <- tracing.setAttribute("from", provider.fromAddress)
          _ <- tracing.setAttribute("to", email.receivers.map(_.address).mkString(","))
        } yield ()
      }
      prop = getProps(provider)
      decryptedPassword <- encryptionService.decrypt(Base64.toByteArray(provider.encryptedPassword)).map(new String(_))
      session = Session.getInstance(
        prop,
        new Authenticator() {
          override protected def getPasswordAuthentication =
            new PasswordAuthentication(provider.userName, decryptedPassword)
        }
      )
      originalMessage = new MimeMessage(session)
      message <- mimeUtils.generateMimeMessageFromEmail(originalMessage, email)
      from = EmailAddress(name = Some(provider.fromName), address = provider.fromAddress)
      _ = {
        message.setFrom(from.value)
        message.setReplyTo(Array(new InternetAddress(from.value)))
      }
      _ <- ZIO.attempt(Transport.send(message)).timeout(CustomizedSmtpProvider.TIMEOUT)
      messageId = email.messageId.getOrElse(Random.alphanumeric.take(32).mkString)
      _ <- storeMessageIdToInternalEmailIfNeeded(
        email,
        messageId,
        ExternalEmailProvider.SMTP
      )(emailStoreService = emailStoreService)
      _ <- storeMimeMessageToS3IfNeeded(email, message)(emailStoreService = emailStoreService)
        .catchAllCause { err =>
          ZIO.logErrorCause(s"Fail to store mime message to s3 of ${email.internalEmailIdOpt}", err)
        }
    } yield EmailResponse()
    ZIOTelemetryUtils
      .traceWithChildSpan(
        spanName = "service/email/smtp",
        spanKind = SpanKind.CLIENT
      )(task)
      .provideEnvironment(tracingEnvironment.environment)
  }

}

object CustomizedSmtpProvider {
  // TIMEOUT hard timeout on sending email
  val TIMEOUT = zio.Duration.fromSeconds(10) // TODO: @keimoon fix hard-coded timeout
}
