// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service.email

import scala.annotation.unused
import scala.util.Random

import io.circe.Codec
import io.opentelemetry.api.trace.SpanKind
import zio.implicits.*
import zio.{Task, UIO, ZIO}

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.dms.service.FileService
import anduin.email.provider.EmailProviderService
import anduin.encryption.StreamingEncryptionService
import anduin.id.TransactionId
import anduin.kafka.KafkaService.RetryACK
import anduin.kafka.{KafkaAsyncExecutor, KafkaFiber, KafkaService, Topic}
import anduin.model.id.email.sending.EmailSendingTaskId
import anduin.model.id.email.{GeneratedEmailId, InternalEmailId}
import anduin.model.id.{FundSubIdFactory, InternalEmailIdFactory, TransactionIdFactory}
import anduin.radix.RadixId
import anduin.telemetry.TelemetryEnvironment
import com.anduin.stargazer.apps.stargazer.StargazerSettings
import com.anduin.stargazer.service.api.FileDownloadService
import com.anduin.stargazer.service.email.generate.GenerateEmail
import com.anduin.stargazer.service.email.store.EmailStoreService
import com.anduin.stargazer.service.email.utils.MimeUtils
import com.anduin.stargazer.service.ses.SesService
import com.anduin.stargazer.service.utils.{ZIOTelemetryUtils, ZIOUtils}
import com.anduin.stargazer.service.{GondorConfig, ServiceFeature}

sealed trait EmailSenderService extends KafkaFiber {

  def enqueue(
    generateEmail: => GenerateEmail,
    emailSpaceId: RadixId, // Will be used for email scheduling, pass fundSubId, dataRoomId here for example
    emailProviderBuilderConfig: EmailProviderBuilderConfig = EmailProviderBuilderConfig.defaultBuilderConfig,
    storeEmailConfig: EmailSenderService.StoreEmailConfig = EmailSenderService.StoreEmailConfig.defaultConfig
  ): Task[(internalId: Seq[EmailSenderService.EmailWithInternalId], sendingTaskId: Seq[EmailSendingTaskId])]

}

object EmailSenderService {

  final case class Live(
    gondorConfig: GondorConfig,
    fileService: FileService,
    fileDownloadService: FileDownloadService,
    emailStoreService: EmailStoreService,
    sesService: SesService,
    kafkaService: KafkaService,
    mimeUtils: MimeUtils,
    serviceFeature: ServiceFeature,
    emailProviderService: EmailProviderService,
    encryptionService: StreamingEncryptionService,
    tracingEnvironment: TelemetryEnvironment.Tracing
  )(
    using val emailServiceUtils: EmailServiceUtils,
    val userEmailService: UserEmailService
  ) extends EmailSenderService {

    private val emailProviderBuilder = EmailProviderBuilder(
      fileDownloadService,
      emailStoreService,
      sesService,
      mimeUtils,
      emailProviderService,
      encryptionService,
      tracingEnvironment
    )

    private val generation = gondorConfig.backendConfig.email.generation

    private val topic = Topic[String, EmailWithProvider](
      StargazerSettings.gondorConfig.backendConfig.email.sending.kafkaTopic
    )

    private val highEngagementTopic = Topic[String, EmailWithProvider](
      StargazerSettings.gondorConfig.backendConfig.email.sending.highEngagementKafkaTopic
    )

    private val immediatelyTopic = Topic[String, EmailWithProvider](
      StargazerSettings.gondorConfig.backendConfig.email.sending.immediatelyKafkaTopic
    )

    private val emailSendingAsyncExecutor = KafkaAsyncExecutor[String, EmailWithProvider](
      kafkaService = kafkaService,
      topic = topic,
      handler = emailSendingHandler,
      retryOpt = Some(
        KafkaService.RetryOptions(
          when = (_, _, _) => ZIO.attempt(RetryACK.Retry),
          retryMax = gondorConfig.backendConfig.email.sending.retry.maxCount
        )
      ),
      parallelism = 2
    )

    private val highEngagementEmailSendingAsyncExecutor = KafkaAsyncExecutor[String, EmailWithProvider](
      kafkaService = kafkaService,
      topic = highEngagementTopic,
      handler = emailSendingHandler,
      retryOpt = Some(
        KafkaService.RetryOptions(
          when = (_, _, _) => ZIO.attempt(RetryACK.Retry),
          retryMax = gondorConfig.backendConfig.email.sending.retry.maxCount
        )
      ),
      parallelism = 1
    )

    private val immediatelyEmailSendingAsyncExecutor = KafkaAsyncExecutor[String, EmailWithProvider](
      kafkaService = kafkaService,
      topic = immediatelyTopic,
      handler = emailSendingHandler,
      retryOpt = Some(
        KafkaService.RetryOptions(
          when = (_, _, _) => ZIO.attempt(RetryACK.Retry),
          retryMax = gondorConfig.backendConfig.email.sending.retry.maxCount
        )
      ),
      parallelism = 1
    )

    private def emailSendingHandler(
      @unused key: String,
      emailWithProvider: EmailWithProvider
    ): Task[Unit] = {
      for {
        rawEmailOpt <- emailStoreService.getGeneratedEmail(emailWithProvider.generatedEmailId)
        rawEmail <- ZIO.getOrFailWith(
          new RuntimeException(s"Generated email not found: ${emailWithProvider.generatedEmailId}")
        )(rawEmailOpt)
        receivers <- EmailSenderHelper.getSubscribeReceivers(rawEmail)
        unsubscribeUrlOpt <- EmailSenderHelper.getUnsubscribeUrlOpt(rawEmail)
        email = rawEmail.copy(receivers = receivers, unsubscribeUrlOpt = unsubscribeUrlOpt)
        _ <-
          if (email.receivers.isEmpty) {
            ZIO.logWarning("Receiver is empty, maybe all addresses have unsafe domain or unsubscribed")
          } else {
            for {
              emailProvider <- emailProviderBuilder.build(emailWithProvider.emailProviderBuilderConfig)
              _ <- ZIOTelemetryUtils
                .traceWithChildSpan(
                  spanName = "service/email",
                  spanKind = SpanKind.INTERNAL
                )(emailProvider.sendEmail(email))
                .provideEnvironment(tracingEnvironment.environment)
              _ <- ZIO.logInfo(s"Using sender address ${email.sender}")
              _ <- ZIO.logInfo(s"Successfully send email to ${email.receivers.map(_.redacted)}")
            } yield ()
          }
      } yield ()
    }

    private def storeInternalEmailsIfNeeded(
      emails: List[Email],
      storeEmailConfig: EmailSenderService.StoreEmailConfig
    ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
      if (storeEmailConfig.shouldStore) {
        emailStoreService
          .storeEmails(emails, storeEmailConfig.parentId)
          .map(
            _.zip(emails)
              .map { case (id, email) =>
                EmailSenderService.EmailWithInternalId(email.copy(internalEmailIdOpt = Some(id)), Some(id))
              }
          )
      } else {
        ZIO.attempt(emails.map(EmailSenderService.EmailWithInternalId(_, None)))
      }
    }

    override def enqueue(
      generateEmail: => GenerateEmail,
      emailSpaceId: RadixId,
      emailProviderBuilderConfig: EmailProviderBuilderConfig = EmailProviderBuilderConfig.defaultBuilderConfig,
      storeEmailConfig: EmailSenderService.StoreEmailConfig = EmailSenderService.StoreEmailConfig.defaultConfig
    ): Task[(internalId: Seq[EmailSenderService.EmailWithInternalId], sendingTaskId: Seq[EmailSendingTaskId])] = {
      for {
        emails <- generateEmail.genEmailList.timeout(generation.timeout)
        shouldSendEmail <- ZIO.attempt {
          val isEmailSendingDisabled =
            gondorConfig.backendConfig.email.sending.disableSendingEmail || serviceFeature.hasSandbox
          !isEmailSendingDisabled || generateEmail.isWhitelisted
        }
        emailsWithInternalId <- storeInternalEmailsIfNeeded(emails, storeEmailConfig)
        _ <- ZIOUtils.when(shouldSendEmail)(ZIO.foreach(emailsWithInternalId) { emailWithInternalId =>
          val email = emailWithInternalId.email
          for {
            generatedEmailId <- emailStoreService.storeGeneratedEmail(email)
            _ <-
              if (email.priority == EmailPriority.Urgent) {
                immediatelyEmailSendingAsyncExecutor.send(
                  immediatelyTopic
                    .message(
                      email.messageId.getOrElse(Random.alphanumeric.take(32).mkString),
                      EmailWithProvider(generatedEmailId, emailProviderBuilderConfig)
                    )
                )
              } else if (email.priority == EmailPriority.High) {
                highEngagementEmailSendingAsyncExecutor.send(
                  highEngagementTopic
                    .message(
                      email.messageId.getOrElse(Random.alphanumeric.take(32).mkString),
                      EmailWithProvider(generatedEmailId, emailProviderBuilderConfig)
                    )
                )
              } else {
                emailSendingAsyncExecutor.send(
                  topic
                    .message(
                      email.messageId.getOrElse(Random.alphanumeric.take(32).mkString),
                      EmailWithProvider(generatedEmailId, emailProviderBuilderConfig)
                    )
                )
              }
          } yield ()
        })
        _ <- ZIO.logInfo(s"Pushed ${emails.size} emails to queue")
      } yield (
        internalId = emailsWithInternalId,
        sendingTaskId = Seq.empty
      )
    }

    override def start(): zio.Task[Unit] = {
      ZIO.collectAllParDiscard(
        Seq(
          emailSendingAsyncExecutor.start(),
          highEngagementEmailSendingAsyncExecutor.start(),
          immediatelyEmailSendingAsyncExecutor.start()
        )
      )
    }

    override def close(): UIO[Unit] = {
      ZIO.collectAllParDiscard(
        Seq(
          emailSendingAsyncExecutor.close(),
          highEngagementEmailSendingAsyncExecutor.close(),
          immediatelyEmailSendingAsyncExecutor.close()
        )
      )
    }

  }

  final case class Mock(gondorConfig: GondorConfig) extends EmailSenderService {

    override def start(): zio.Task[Unit] = ZIO.unit

    override def close(): UIO[Unit] = ZIO.unit

    override def enqueue(
      generateEmail: => GenerateEmail,
      emailSpaceId: RadixId,
      emailProviderBuilderConfig: EmailProviderBuilderConfig = EmailProviderBuilderConfig.defaultBuilderConfig,
      storeEmailConfig: EmailSenderService.StoreEmailConfig = EmailSenderService.StoreEmailConfig.defaultConfig
    ): Task[(internalId: Seq[EmailSenderService.EmailWithInternalId], sendingTaskId: Seq[EmailSendingTaskId])] = {
      for {
        emails <- generateEmail.genEmailList.timeout(gondorConfig.backendConfig.email.generation.timeout)
        internalId = emails.map(email =>
          EmailWithInternalId(
            email,
            Some(
              InternalEmailIdFactory.unsafeRandomId(FundSubIdFactory.unsafeRandomId(TransactionIdFactory.unsafeRandomId))
            )
          )
        )
      } yield (
        internalId = internalId,
        sendingTaskId = Seq.empty
      )
    }

  }

  final case class EmailWithProvider(
    generatedEmailId: GeneratedEmailId,
    emailProviderBuilderConfig: EmailProviderBuilderConfig
  )

  object EmailWithProvider {
    given Codec.AsObject[EmailWithProvider] = deriveCodecWithDefaults
  }

  final case class EmailWithInternalId(
    email: Email,
    internalIdOpt: Option[InternalEmailId]
  )

  final case class StoreEmailConfig(
    shouldStore: Boolean,
    parentId: RadixId
  )

  object StoreEmailConfig {

    val defaultConfig: StoreEmailConfig = StoreEmailConfig(
      shouldStore = false,
      parentId = TransactionId.defaultValue.get
    )

  }

}
