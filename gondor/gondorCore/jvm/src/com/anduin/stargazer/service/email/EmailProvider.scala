// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service.email

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.model.common.emailaddress.EmailAddress
import com.anduin.stargazer.service.EmailResponse
import com.anduin.stargazer.service.api.FileDownloadService
import com.anduin.stargazer.service.email.store.{EmailStoreService, ExternalEmailProvider}
import com.anduin.stargazer.service.email.utils.MimeUtils
import com.anduin.stargazer.service.ses.SesService
import com.anduin.stargazer.service.smtp.{CustomizedSmtpProvider, DeprecatedSmtpProvider}
import com.anduin.stargazer.service.utils.ZIOUtils
import io.circe.Codec
import jakarta.mail.internet.MimeMessage
import zio.{Task, ZIO}

import anduin.email.provider.EmailProviderService
import anduin.encryption.StreamingEncryptionService
import anduin.model.id.email.provider.EmailProviderId
import anduin.telemetry.TelemetryEnvironment

trait EmailProvider {

  def sendEmail(email: Email): Task[EmailResponse]

  protected def storeMessageIdToInternalEmailIfNeeded(
    email: Email,
    messageId: String,
    emailProvider: ExternalEmailProvider
  )(
    emailStoreService: EmailStoreService
  ): Task[Unit] = {
    ZIOUtils.traverseOptionUnit(email.internalEmailIdOpt) { internalEmailId =>
      for {
        _ <- ZIO.logInfo(s"Storing Email message ID $messageId for email id $internalEmailId")
        _ <- emailStoreService.updateExternalEmailId(
          internalEmailId,
          messageId,
          emailProvider
        )
      } yield ()
    }
  }

  protected def storeMimeMessageToS3IfNeeded(
    email: Email,
    message: MimeMessage
  )(
    emailStoreService: EmailStoreService
  ): Task[Unit] = {
    ZIOUtils.traverseOptionUnit(email.internalEmailIdOpt) { internalEmailId =>
      emailStoreService.storeMimeMessageToS3(
        internalEmailId,
        message
      )
    }
  }

}

sealed trait EmailProviderBuilderConfig derives CanEqual

object EmailProviderBuilderConfig {

  given Codec.AsObject[EmailProviderBuilderConfig] = deriveCodecWithDefaults

  case object SesEmailBuilderConfig extends EmailProviderBuilderConfig

  given Codec.AsObject[SesEmailBuilderConfig.type] = deriveCodecWithDefaults

  // Deprecated, use CustomizedSmtpBuilderConfig instead
  final case class DeprecatedSmtpBuilderConfig(
    from: Option[EmailAddress], // to override `From` header in Email generator
    host: String,
    port: Int,
    username: String,
    password: String,
    tls: Boolean
  ) extends EmailProviderBuilderConfig

  object DeprecatedSmtpBuilderConfig {
    given Codec.AsObject[DeprecatedSmtpBuilderConfig] = deriveCodecWithDefaults
  }

  final case class CustomizedSmtpBuilderConfig(
    providerId: EmailProviderId
  ) extends EmailProviderBuilderConfig

  object CustomizedSmtpBuilderConfig {
    given Codec.AsObject[CustomizedSmtpBuilderConfig] = deriveCodecWithDefaults
  }

  val defaultBuilderConfig: EmailProviderBuilderConfig = SesEmailBuilderConfig
}

final case class EmailProviderBuilder(
  fileDownloadService: FileDownloadService,
  emailStoreService: EmailStoreService,
  sesService: SesService,
  mimeUtils: MimeUtils,
  emailProviderService: EmailProviderService,
  encryptionService: StreamingEncryptionService,
  tracingEnvironment: TelemetryEnvironment.Tracing
) {

  def build(builderConfig: EmailProviderBuilderConfig): Task[EmailProvider] = {
    builderConfig match {
      case EmailProviderBuilderConfig.SesEmailBuilderConfig =>
        ZIO.attempt(
          sesService
        )
      case builderConfig: EmailProviderBuilderConfig.DeprecatedSmtpBuilderConfig =>
        ZIO.attempt(
          DeprecatedSmtpProvider(
            builderConfig,
            mimeUtils,
            emailStoreService,
            tracingEnvironment
          )(
            using fileDownloadService
          )
        )
      case builderConfig: EmailProviderBuilderConfig.CustomizedSmtpBuilderConfig =>
        ZIO.attempt(
          CustomizedSmtpProvider(
            builderConfig,
            mimeUtils,
            emailStoreService,
            emailProviderService,
            encryptionService,
            tracingEnvironment
          )(
            using fileDownloadService
          )
        )
    }
  }

}
