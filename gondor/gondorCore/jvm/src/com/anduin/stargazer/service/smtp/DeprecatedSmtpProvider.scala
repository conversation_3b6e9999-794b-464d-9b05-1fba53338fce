// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service.smtp

import java.util.Properties

import com.anduin.stargazer.service.EmailResponse
import com.anduin.stargazer.service.email.utils.MimeUtils
import com.anduin.stargazer.service.email.{Email, EmailProvider, EmailProviderBuilderConfig}
import jakarta.mail.internet.{InternetAddress, MimeMessage}
import jakarta.mail.{Authenticator, PasswordAuthentication, Session, Transport}

import com.anduin.stargazer.service.api.FileDownloadService
import com.anduin.stargazer.service.email.store.{EmailStoreService, ExternalEmailProvider}
import zio.{Task, ZIO}
import scala.util.Random

import io.opentelemetry.api.trace.SpanKind

import anduin.telemetry.TelemetryEnvironment
import com.anduin.stargazer.service.utils.ZIOTelemetryUtils

final case class DeprecatedSmtpProvider(
  config: EmailProviderBuilderConfig.DeprecatedSmtpBuilderConfig,
  mimeUtils: MimeUtils,
  emailStoreService: EmailStoreService,
  tracingEnvironment: TelemetryEnvironment.Tracing
)(
  using val fileDownloadService: FileDownloadService
) extends EmailProvider {

  private val prop: Properties = {
    val prop = new Properties()
    prop.put("mail.smtp.host", config.host)
    prop.put("mail.smtp.port", config.port.toString)
    prop.put("mail.smtp.auth", "true")
    prop.put("mail.smtp.starttls.enable", config.tls.toString)
    // connect timeout
    prop.put("mail.smtp.connectiontimeout", "1000")
    prop.put("mail.smtps.connectiontimeout", "1000")
    // read timeout
    prop.put("mail.smtp.timeout", "3000")
    prop.put("mail.smtps.timeout", "3000")
    // write timeout
    prop.put("mail.smtp.writetimeout", "3000")
    prop.put("mail.smtps.writetimeout", "3000")
    prop
  }

  override def sendEmail(email: Email): Task[EmailResponse] = {
    val session = Session.getInstance(
      prop,
      new Authenticator() {
        override protected def getPasswordAuthentication = new PasswordAuthentication(config.username, config.password)
      }
    )
    val originalMessage = new MimeMessage(session)

    val task = for {
      _ <- ZIOTelemetryUtils.withTracing { tracing =>
        for {
          _ <- tracing.setAttribute("host", config.host)
          _ <- tracing.setAttribute("port", config.port)
          _ <- tracing.setAttribute("tls", config.tls)
          _ <- tracing.setAttribute("username", config.username)
          _ <- tracing.setAttribute("from", config.from.map(_.address).getOrElse(""))
          _ <- tracing.setAttribute("to", email.receivers.map(_.address).mkString(","))
        } yield ()
      }
      message <- mimeUtils.generateMimeMessageFromEmail(originalMessage, email)
      _ = config.from.foreach { address =>
        message.setFrom(address.value)
        message.setReplyTo(Array(new InternetAddress(address.value)))
      }
      _ <- ZIO.attempt(Transport.send(message)).timeout(DeprecatedSmtpProvider.TIMEOUT)
      messageId = email.messageId.getOrElse(Random.alphanumeric.take(32).mkString)
      _ <- storeMessageIdToInternalEmailIfNeeded(
        email,
        messageId,
        ExternalEmailProvider.SMTP
      )(emailStoreService = emailStoreService)
      _ <- storeMimeMessageToS3IfNeeded(email, message)(emailStoreService = emailStoreService)
        .catchAllCause { err =>
          ZIO.logErrorCause(s"Fail to store mime message to s3 of ${email.internalEmailIdOpt}", err)
        }
    } yield EmailResponse()
    ZIOTelemetryUtils
      .traceWithChildSpan(
        spanName = "service/email/smtp-deprecated",
        spanKind = SpanKind.CLIENT
      )(task)
      .provideEnvironment(tracingEnvironment.environment)

  }

}

object DeprecatedSmtpProvider {
  // TIMEOUT hard timeout on sending email
  val TIMEOUT = zio.Duration.fromSeconds(10) // TODO: @keimoon fix hard-coded timeout
}
