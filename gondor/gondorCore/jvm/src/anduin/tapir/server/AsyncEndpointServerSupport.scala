// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir.server

import scala.concurrent.TimeoutException
import scala.concurrent.duration.FiniteDuration

import io.circe.{Decoder, Encoder}
import sttp.capabilities.zio.ZioStreams
import sttp.model.headers.Cookie
import sttp.model.{Header, <PERSON>ri}
import sttp.tapir.Endpoint
import sttp.tapir.ztapir.*
import zio.ZIO

import anduin.asyncapiv2.AsyncApiService
import anduin.asyncapiv2.execution.{AsyncApiHand<PERSON>, AsyncApiWorkflowType}
import anduin.service.{AuthenticatedRequestContext, ServiceActor}
import anduin.tapir.AnduinEndpointIO
import anduin.tapir.AsyncEndpoint.*
import anduin.tapir.AuthenticatedEndpoints.BearerToken
import com.anduin.stargazer.service.authorization.AuthorizationService
import com.anduin.stargazer.service.utils.ZIOUtils
import io.circe.syntax.given

trait AsyncEndpointServerSupport {

  protected def defaultTimeout: FiniteDuration

  protected def authorizationService: AuthorizationService

  protected def asyncApiService: AsyncApiService

  private def getAuthenticatedResponse: String = {
    UnauthorizedResponse(
      anduinCode = 403,
      error = "Unauthorized"
    ).asJson.noSpaces
  }

  protected def collectContextCreateAsync[I](
    endpoint: Endpoint[BearerToken, I, AsyncEndpointError, CreateAsyncEndpointResponse, Any]
  ): ZPartialServerEndpoint[
    Any,
    BearerToken,
    ServiceActor,
    (I, List[Header], List[Cookie], Uri),
    AsyncEndpointError,
    CreateAsyncEndpointResponse,
    ZioStreams
  ] = {
    endpoint
      .in(headers)
      .in(cookies)
      .in(AnduinEndpointIO.uri)
      .zServerSecurityLogic[Any, ServiceActor] { a =>
        ZIOUtils
          .timeout(defaultTimeout) {
            authorizationService.authenticateSession(a.token).flatMapError { error =>
              ZIO
                .logErrorCause("Authenticate session error: ", error.toCause)
                .as(Unauthorized(getAuthenticatedResponse))
            }
          }
      }
  }

  protected def collectContextRunAsync[I](
    endpoint: Endpoint[BearerToken, I, AsyncEndpointError, RunAsyncEndpointResponse, Any]
  ): ZPartialServerEndpoint[
    Any,
    BearerToken,
    ServiceActor,
    (I, List[Header], List[Cookie], Uri),
    AsyncEndpointError,
    RunAsyncEndpointResponse,
    ZioStreams
  ] = {
    endpoint
      .in(headers)
      .in(cookies)
      .in(AnduinEndpointIO.uri)
      .zServerSecurityLogic[Any, ServiceActor] { a =>
        ZIOUtils
          .timeout(defaultTimeout) {
            authorizationService.authenticateSession(a.token).flatMapError { error =>
              ZIO
                .logErrorCause("Authenticate session error: ", error.toCause)
                .as(Unauthorized(getAuthenticatedResponse))
            }
          }
      }
  }

  protected def collectContextFetch(
    endpoint: Endpoint[
      BearerToken,
      FetchEndpointParams,
      AsyncEndpointError,
      FetchEndpointResponse,
      Any
    ]
  ): ZPartialServerEndpoint[
    Any,
    BearerToken,
    ServiceActor,
    (FetchEndpointParams, List[Header], List[Cookie], Uri),
    AsyncEndpointError,
    FetchEndpointResponse,
    ZioStreams
  ] = {
    endpoint
      .in(headers)
      .in(cookies)
      .in(AnduinEndpointIO.uri)
      .zServerSecurityLogic[Any, ServiceActor] { a =>
        ZIOUtils
          .timeout(defaultTimeout) {
            authorizationService.authenticateSession(a.token).flatMapError { error =>
              ZIO
                .logErrorCause("Authenticate session error: ", error.toCause)
                .as(Unauthorized(getAuthenticatedResponse))
            }
          }
      }
  }

  protected def buildHandlerMap[I: Decoder, E: Encoder, O: Encoder](
    endpoint: AsyncAuthenticatedEndpoint[I, E, O]
  )(
    impl: (I, AuthenticatedRequestContext) => zio.Task[Either[E, O]]
  ): Map[AsyncApiWorkflowType, AsyncApiHandler] = {
    Map(
      AsyncApiWorkflowType(endpoint.endpoint.showPathTemplate()) -> asyncApiService
        .buildHandler[AuthenticatedRequestContext, I, E, O] { (ctx, input) =>
          impl(input, ctx).timeoutFail(new TimeoutException("Implementation timed out"))(
            zio.Duration.fromScala(endpoint.workflowQueue.getTimeout)
          )
        }
    )
  }

}
