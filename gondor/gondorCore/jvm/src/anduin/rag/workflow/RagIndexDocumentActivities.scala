// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.rag.workflow

import zio.temporal.{activityInterface, activityMethod}

import anduin.workflow.ActivityQueue.RagIndexDocument
import anduin.workflow.common.{TemporalActivity, TemporalActivityCompanion}

@activityInterface
trait RagIndexDocumentActivities extends TemporalActivity {

  @activityMethod
  def getFileIndexState(params: GetFileIndexStateParams): GetFileIndexStateResponse

  @activityMethod
  def convertToImages(params: ConvertToImagesParams): ConvertToImagesResponse

  @activityMethod
  def getImageFileStorageId(params: GetImageFileStorageIdParams): GetImageFileStorageIdResponse

  @activityMethod
  def generateVisualEmbeddings(params: GenerateVisualEmbeddingsParams): GenerateVisualEmbeddingsResponse

  @activityMethod
  def storeVisualEmbeddings(params: StoreVisualEmbeddingsParams): StoreVisualEmbeddingsResponse

  @activityMethod
  def syncVespaFileState(params: SyncVespaFileStateParams): SyncVespaFileStateResponse

}

object RagIndexDocumentActivities extends TemporalActivityCompanion[RagIndexDocumentActivities](RagIndexDocument) {

  override val maximumRetryAttempts: Int = 3

  override val startToCloseTimeout: java.time.Duration = java.time.Duration.ofMinutes(10)
}
