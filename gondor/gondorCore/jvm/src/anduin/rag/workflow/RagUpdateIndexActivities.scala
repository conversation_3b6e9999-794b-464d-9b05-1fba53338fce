// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.rag.workflow

import zio.temporal.{activityInterface, activityMethod}

import anduin.workflow.ActivityQueue.RagUpdateIndex
import anduin.workflow.common.{TemporalActivity, TemporalActivityCompanion}

@activityInterface
trait RagUpdateIndexActivities extends TemporalActivity {

  @activityMethod
  def scanFileIndexes(params: RagScanFileIndexesParams): RagScanFileIndexesResponse

  @activityMethod
  def scanFolderIndexes(params: RagScanFolderIndexesParams): RagScanFolderIndexesResponse

  @activityMethod
  def updateFolderIndex(params: RagUpdateFolderIndexParams): RagUpdateFolderIndexResponse

  @activityMethod
  def updateChannelIndexState(params: UpdateChannelIndexStateParams): UpdateChannelIndexStateResponse

}

object RagUpdateIndexActivities extends TemporalActivityCompanion[RagUpdateIndexActivities](RagUpdateIndex) {

  override val maximumRetryAttempts: Int = 3

  override val startToCloseTimeout: java.time.Duration = java.time.Duration.ofMinutes(10)

}
