// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.rag.workflow

import java.time.Duration

import zio.temporal.{workflowInterface, workflowMethod}

import anduin.workflow.WorkflowQueue.RagBatchIndexDocument
import anduin.workflow.common.{TemporalWorkflow, TemporalWorkflowCompanion}

@workflowInterface
trait RagBatchIndexDocumentWorkflow extends TemporalWorkflow[RagBatchIndexDocumentParams, RagBatchIndexDocumentResponse] {

  @workflowMethod
  override def run(input: RagBatchIndexDocumentParams): RagBatchIndexDocumentResponse

}

object RagBatchIndexDocumentWorkflow extends TemporalWorkflowCompanion[RagBatchIndexDocumentWorkflow] {

  override val workflowQueue = RagBatchIndexDocument

  override val workflowTaskTimeout: Option[Duration] = None

  override val workflowRunTimeout: Option[Duration] = None

  override val workflowExecutionTimeout: Option[Duration] = None
}
