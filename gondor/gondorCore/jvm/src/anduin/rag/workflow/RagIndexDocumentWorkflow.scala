// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.rag.workflow

import java.time.Duration

import zio.temporal.{workflowInterface, workflowMethod}

import anduin.workflow.WorkflowQueue.RagIndexDocument
import anduin.workflow.common.{TemporalWorkflow, TemporalWorkflowCompanion}

@workflowInterface
trait RagIndexDocumentWorkflow extends TemporalWorkflow[RagIndexDocumentParams, RagIndexDocumentResponse] {

  @workflowMethod
  override def run(input: RagIndexDocumentParams): RagIndexDocumentResponse

}

object RagIndexDocumentWorkflow extends TemporalWorkflowCompanion[RagIndexDocumentWorkflow] {

  override val workflowQueue = RagIndexDocument

  override val maximumRetryAttempts: Int = 3

  override val workflowTaskTimeout: Option[Duration] = None

  override val workflowRunTimeout: Option[Duration] = None

  override val workflowExecutionTimeout: Option[Duration] = None
}
