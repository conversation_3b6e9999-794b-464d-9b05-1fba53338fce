// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.rag.workflow

import java.time.Duration

import zio.temporal.{workflowInterface, workflowMethod}

import anduin.workflow.WorkflowQueue.RagUpdateIndex
import anduin.workflow.common.{TemporalWorkflow, TemporalWorkflowCompanion}

@workflowInterface
trait RagUpdateIndexWorkflow extends TemporalWorkflow[RagUpdateIndexParams, RagUpdateIndexResponse] {

  @workflowMethod
  override def run(input: RagUpdateIndexParams): RagUpdateIndexResponse

}

object RagUpdateIndexWorkflow extends TemporalWorkflowCompanion[RagUpdateIndexWorkflow] {

  override val workflowQueue = RagUpdateIndex

  override val maximumRetryAttempts: Int = 3

  override val workflowTaskTimeout: Option[Duration] = None

  override val workflowRunTimeout: Option[Duration] = None

  override val workflowExecutionTimeout: Option[Duration] = None
}
