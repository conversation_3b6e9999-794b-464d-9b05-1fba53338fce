// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.rag.workflow.impl

import anduin.rag.IndexState
import anduin.rag.workflow.*
import anduin.util.FilenameUtils
import anduin.workflow.TemporalWorkflowService.newActivityStub
import anduin.workflow.common.TemporalWorkflowImplCompanion
import anduin.workflow.{WorkflowImpl, WorkflowTask}

class RagIndexDocumentWorkflowImpl extends RagIndexDocumentWorkflow {

  private val activities = newActivityStub[RagIndexDocumentActivities]

  private val fileTypeRoutingRules = List(
    (FilenameUtils.isImageFile, imageFileHandler),
    (FilenameUtils.isPdfFile, pdfFileHandler),
    (FilenameUtils.isPdfConvertible, pdfFileHandler)
  )

  private def pdfFileHandler(
    params: RagIndexDocumentParamsWithIndexState
  ): WorkflowTask[RagIndexDocumentResponse] = {
    for {
      imageStorageIds <- WorkflowTask
        .executeActivity(
          activities.convertToImages(
            ConvertToImagesParams(params.dmsFeature, params.fileId, params.actor)
          )
        )
      visualEmbeddings <- WorkflowTask.executeActivity(
        activities.generateVisualEmbeddings(
          GenerateVisualEmbeddingsParams(
            params.actor,
            imageStorageIds.imageStorageIds
          )
        )
      )
      _ <- WorkflowTask.executeActivity(
        activities.storeVisualEmbeddings(
          StoreVisualEmbeddingsParams(
            params.fileIndexInfo,
            visualEmbeddings.embeddings
          )
        )
      )
    } yield RagIndexDocumentResponse()
  }

  private def imageFileHandler(
    params: RagIndexDocumentParamsWithIndexState
  ): WorkflowTask[RagIndexDocumentResponse] = {
    for {
      imageStorageId <- WorkflowTask
        .executeActivity(
          activities.getImageFileStorageId(
            GetImageFileStorageIdParams(params.dmsFeature, params.fileId, params.actor)
          )
        )
        .map(_.storageId)
      visualEmbeddings <- WorkflowTask.executeActivity(
        activities.generateVisualEmbeddings(
          GenerateVisualEmbeddingsParams(
            params.actor,
            List(imageStorageId)
          )
        )
      )
      _ <- WorkflowTask.executeActivity(
        activities.storeVisualEmbeddings(
          StoreVisualEmbeddingsParams(
            params.fileIndexInfo,
            visualEmbeddings.embeddings
          )
        )
      )
    } yield RagIndexDocumentResponse()
  }

  private def runImageFlow(params: RagIndexDocumentParamsWithIndexState): WorkflowTask[RagIndexDocumentResponse] = {
    if (params.fileIndexInfo.state.isSynced) {
      WorkflowTask.succeed(RagIndexDocumentResponse())
    } else {
      val filename = params.fileIndexInfo.dmsInfo.name
      for {
        rule <- WorkflowTask.attempt(fileTypeRoutingRules.find((filter, _) => filter(filename)))
        _ <- rule match {
          case Some(_, handler) => handler(params)
          case None =>
            WorkflowTask.succeed(
              scribe.info(
                s"File ${params.fileId.idString} is not a supported file type for indexing"
              )
            )
        }
      } yield RagIndexDocumentResponse()
    }
  }

  override def runAsync(params: RagIndexDocumentParams): WorkflowTask[RagIndexDocumentResponse] = {
    for {
      _ <- WorkflowTask.succeed(
        scribe.info(
          s"Start rag indexing document workflow for file ${params.fileId.idString}"
        )
      )
      indexInfo <- params.precomputedFileIndexInfo.fold(
        WorkflowTask
          .executeActivity(
            activities.getFileIndexState(
              GetFileIndexStateParams(params.dmsFeature, params.fileId, params.actor)
            )
          )
          .map(_.indexInfo)
          .map(indexInfo =>
            indexInfo
              .copy(state = Option.when(params.forced)(IndexState.Missing).getOrElse(indexInfo.state))
          )
      )(WorkflowTask.succeed)
      _ <- WorkflowTask.when(params.fileId != indexInfo.fileId)(
        WorkflowTask.fail(
          new IllegalArgumentException(
            s"FileId mismatch: expected ${params.fileId.idString}, got ${indexInfo.fileId.idString}"
          )
        )
      )
      paramsWithIndexInfo = RagIndexDocumentParamsWithIndexState(
        dmsFeature = params.dmsFeature,
        fileId = params.fileId,
        actor = params.actor,
        setTagsOpt = params.setTagsOpt,
        fileIndexInfo = indexInfo
      )

//      _ <- runImageFlow(paramsWithIndexInfo)

      _ <- WorkflowTask.executeActivity(
        activities.syncVespaFileState(
          SyncVespaFileStateParams(
            indexInfo = indexInfo,
            setTagsOpt = paramsWithIndexInfo.setTagsOpt
          )
        )
      )

    } yield RagIndexDocumentResponse()
  }

  override def run(params: RagIndexDocumentParams): RagIndexDocumentResponse = {
    runAsync(params).getOrThrow
  }

}

object RagIndexDocumentWorkflowImpl
    extends TemporalWorkflowImplCompanion[RagIndexDocumentWorkflow, RagIndexDocumentWorkflowImpl] {

  val instance = WorkflowImpl.derived[RagIndexDocumentWorkflow, RagIndexDocumentWorkflowImpl]
}
