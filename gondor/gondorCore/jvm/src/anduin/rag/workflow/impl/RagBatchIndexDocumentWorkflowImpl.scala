// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.rag.workflow.impl

import io.temporal.api.enums.v1.ParentClosePolicy

import anduin.rag.workflow.*
import anduin.workflow.TemporalWorkflowService.newChildWorkflowStubWithOptionsCustomizer
import anduin.workflow.common.TemporalWorkflowImplCompanion
import anduin.workflow.{WorkflowImpl, WorkflowTask}

class RagBatchIndexDocumentWorkflowImpl extends RagBatchIndexDocumentWorkflow {

  private def runRagIndexDocumentWorkflow(params: RagIndexDocumentParams): WorkflowTask[Unit] = {
    val stub = newChildWorkflowStubWithOptionsCustomizer[RagIndexDocumentWorkflow](options =>
      options.withParentClosePolicy(ParentClosePolicy.PARENT_CLOSE_POLICY_ABANDON)
    )
    WorkflowTask.executeChildWorkflow(stub.run(params)).unit
  }

  override def runAsync(params: RagBatchIndexDocumentParams): WorkflowTask[RagBatchIndexDocumentResponse] = {
    WorkflowTask
      .foreachPar(params.fileIndexInfos) { info =>
        runRagIndexDocumentWorkflow(
          RagIndexDocumentParams(
            dmsFeature = params.dmsFeature,
            fileId = info.fileId,
            actor = params.actor,
            setTagsOpt = params.setTagsOpt,
            forced = params.forced,
            precomputedFileIndexInfo = Some(info)
          )
        )
      }
      .catchAll(error => WorkflowTask.fail(error))
      .as(RagBatchIndexDocumentResponse())
  }

  override def run(params: RagBatchIndexDocumentParams): RagBatchIndexDocumentResponse = {
    runAsync(params).getOrThrow
  }

}

object RagBatchIndexDocumentWorkflowImpl
    extends TemporalWorkflowImplCompanion[RagBatchIndexDocumentWorkflow, RagBatchIndexDocumentWorkflowImpl] {

  val instance = WorkflowImpl.derived[RagBatchIndexDocumentWorkflow, RagBatchIndexDocumentWorkflowImpl]

}
