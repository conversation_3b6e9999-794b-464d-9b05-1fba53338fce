// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.rag.workflow.impl

import com.apple.foundationdb.record.TupleRange

import anduin.dms.DmsFeature
import anduin.dms.common.FileFolderIdExpressions.fileFolderChannelTupleConverter
import anduin.dms.file.state.{FileState, FileStateStoreProvider}
import anduin.dms.folder.state.FolderStateStoreProvider
import anduin.fdb.record.FDBRecordDatabase
import anduin.model.id.FileId
import anduin.rag.workflow.*
import anduin.rag.workflow.impl.RagUpdateIndexActivitiesImpl.maxBatchCount
import anduin.rag.{IndexState, RagIndexService}
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.TemporalWorkflowService.runActivity
import com.anduin.stargazer.service.GondorBackendConfig

final case class RagUpdateIndexActivitiesImpl(
  backendConfig: GondorBackendConfig,
  ragIndexService: RagIndexService
)(
  using val temporalWorkflowService: TemporalWorkflowService
) extends RagUpdateIndexActivities {

  override def scanFileIndexes(
    params: RagScanFileIndexesParams
  ): RagScanFileIndexesResponse = {
    given dmsFeature: DmsFeature = DmsFeature.fromProto(params.dmsFeature)
    val task = for {
      stream <- FDBRecordDatabase.largeScanStream(
        FileStateStoreProvider.Production,
        FileStateStoreProvider.mapping,
        TupleRange.allOf(fileFolderChannelTupleConverter.toTuple(params.channel)),
        limit = maxBatchCount
      )
      indexes <- stream
        .filter(_._2.deletedOpt.isEmpty)
        .mapChunksZIO(chunk => ragIndexService.batchGetFileIndexState(chunk.map(_._1), params.actor).map(zio.Chunk.apply))
        .filter(_.state != IndexState.Synced)
        .runCollect
        .map(_.toSeq)
    } yield RagScanFileIndexesResponse(indexes)

    task.runActivity
  }

  override def scanFolderIndexes(params: RagScanFolderIndexesParams): RagScanFolderIndexesResponse = {
    given dmsFeature: DmsFeature = DmsFeature.fromProto(params.dmsFeature)
    val task = for {
      stream <- FDBRecordDatabase.largeScanStream(
        FolderStateStoreProvider.Production,
        FolderStateStoreProvider.mapping,
        TupleRange.allOf(fileFolderChannelTupleConverter.toTuple(params.channel)),
        limit = maxBatchCount
      )
      indexes <- stream
        .filter(_._2.deletedOpt.isEmpty)
        .mapChunksZIO(chunk =>
          ragIndexService.batchGetFolderIndexState(chunk.map(_._1), params.actor).map(zio.Chunk.apply)
        )
        .filter(_.state != IndexState.Synced)
        .runCollect
        .map(_.toSeq)
    } yield RagScanFolderIndexesResponse(indexes)

    task.runActivity
  }

  override def updateFolderIndex(params: RagUpdateFolderIndexParams): RagUpdateFolderIndexResponse = {
    val task = ragIndexService
      .batchSyncFolderStateDocument(params.folderIndexes, setTagsOpt = None)
      .map(_ => RagUpdateFolderIndexResponse())

    task.runActivity
  }

  override def updateChannelIndexState(params: UpdateChannelIndexStateParams): UpdateChannelIndexStateResponse = {
    val task = ragIndexService
      .updateChannelIndexState(params.channel, params.state)(
        using DmsFeature.fromProto(params.dmsFeature)
      )
      .as(UpdateChannelIndexStateResponse())

    task.runActivity
  }

}

object RagUpdateIndexActivitiesImpl {
  private val maxBatchCount: Int = 200
}
