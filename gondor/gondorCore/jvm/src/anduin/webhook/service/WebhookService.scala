// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.webhook.service

import java.net.URI
import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration
import scala.jdk.CollectionConverters.*
import scala.util.Random

import com.svix.models.*
import com.svix.{Svix, SvixOptions}
import io.opentelemetry.api.trace.SpanKind
import sttp.client3.emptyRequest
import sttp.model.UriInterpolator
import zio.implicits.*
import zio.{Cause, Task, ZIO}

import anduin.radix.RadixId
import anduin.service.ServiceException
import anduin.telemetry.TelemetryEnvironment
import anduin.utils.{ScalaUtils, UrlValidatorUtils}
import anduin.webhook.EventTypeEnum
import anduin.webhook.model.Webhook
import anduin.webhook.service.WebhookService.WebhookValidationError
import com.anduin.stargazer.service.GondorConfig
import com.anduin.stargazer.service.http.{ArmeriaClient, ArmeriaEnvironment}
import com.anduin.stargazer.service.utils.{ZIOTelemetryUtils, ZIOUtils}
import com.svix.api.{EndpointListOptions, MessageListOptions}

class WebhookService(
  gondorConfig: GondorConfig,
  httpClient: ArmeriaEnvironment.FundSubWebhookClient,
  tracingEnvironment: TelemetryEnvironment.Tracing
) extends UriInterpolator {

  private val svixConfig = gondorConfig.backendConfig.svixWebhookConfig
  private lazy val svixServerUrl = s"http://${svixConfig.svixHost}:${svixConfig.svixPort}"

  private lazy val svixOptions = {
    val r = new SvixOptions()
    r.setServerUrl(svixServerUrl)
    r
  }

  private lazy val svix = new Svix(svixConfig.svixToken, svixOptions)

  private val spanName = "service/svix"

  def ensureCreatedEventTypes: Task[Unit] = {
    for {
      _ <- ZIO.logInfo("Initializing webhook event types")
      _ <- ZIO.foreachDiscard(EventTypeEnum.eventTypeMap.values) { eventType =>
        execute(
          op = "getEventType.update",
          tracingAttributes = Map("eventType" -> eventType.value),
          task = svix.getEventType.update(
            eventType.value,
            new EventTypeUpdate()
              .description(eventType.description)
          )
        )
      }
    } yield ()
  }

  def getOrCreateApp(
    appId: RadixId,
    appName: String
  ): Task[ApplicationOut] = {
    execute(
      op = "getApplication.getOrCreate",
      tracingAttributes = Map("appId" -> appId.idString),
      task = svix.getApplication.getOrCreate(
        new ApplicationIn()
          .name(appName)
          .uid(appId.idString)
      )
    )
  }

  def createApp(appId: RadixId, appName: String): Task[ApplicationOut] = {
    execute(
      op = "getApplication.create",
      tracingAttributes = Map(
        "appId" -> appId.idString,
        "appName" -> appName
      ),
      task = svix.getApplication.create(
        new ApplicationIn()
          .name(appName)
          .uid(appId.idString)
      )
    )
  }

  def getApp(appId: RadixId): Task[ApplicationOut] = {
    execute(
      op = "getApplication.get",
      tracingAttributes = Map("appId" -> appId.idString),
      task = svix.getApplication.get(appId.idString)
    )
  }

  def deleteApp(appId: RadixId): Task[Unit] = {
    execute(
      op = "getApplication.delete",
      tracingAttributes = Map("appId" -> appId.idString),
      task = svix.getApplication.delete(appId.idString)
    )
  }

  def listWebhooks(
    appId: RadixId
  ): Task[List[Webhook]] = {
    execute(
      op = "getEndpoint.list",
      tracingAttributes = Map("appId" -> appId.idString),
      task = svix.getEndpoint
        .list(
          appId.idString,
          new EndpointListOptions()
        )
        .getData
        .asScala
        .toList
        .map(webhookConversion)
    )
  }

  def createWebhook[A <: EventTypeEnum](
    appId: RadixId,
    url: String,
    eventTypes: Set[A],
    rateLimit: Option[Int] = None,
    disabled: Boolean = false,
    secret: Option[String] = None,
    skipVerification: Boolean = false
  ): Task[Webhook] = {
    for {
      _ <- ZIOUtils.validate(verifyUrlFormat(url))(
        WebhookValidationError.UrlFormatError
      )
      _ <- ZIOUtils.unless(skipVerification)(
        ZIOUtils.validate(verifyValidationRequest(url))(WebhookValidationError.WrongValidationKeyError)
      )
      normUrl = url.trim
      eventTypeStrSet = eventTypes.map(_.value)
      webhook <- execute(
        op = "getEndpoint.create",
        tracingAttributes = Map(
          "appId" -> appId.idString,
          "url" -> normUrl,
          "eventTypes" -> eventTypeStrSet.mkString(","),
          "rateLimit" -> rateLimit.getOrElse(-1).toString,
          "disabled" -> disabled.toString,
          "hasSecret" -> secret.isDefined.toString
        ),
        task = svix.getEndpoint.create(
          appId.idString,
          new EndpointIn()
            .url(URI.create(normUrl))
            .filterTypes(eventTypeStrSet.asJava)
            .rateLimit(rateLimit.map(_.toLong).map(long2Long).orNull)
            .disabled(disabled)
            .secret(secret.orNull)
        )
      )
    } yield webhook
  }

  def getWebhook(appId: RadixId, webhookId: String): Task[Webhook] = {
    execute(
      op = "getEndpoint.get",
      tracingAttributes = Map(
        "appId" -> appId.idString,
        "webhookId" -> webhookId
      ),
      task = svix.getEndpoint.get(appId.idString, webhookId)
    )
  }

  def updateWebhook[A <: EventTypeEnum](
    appId: RadixId,
    webhookId: String,
    url: String,
    eventTypes: Set[A],
    rateLimit: Option[Int],
    disabled: Boolean = false,
    skipVerification: Boolean
  ): Task[Webhook] = {
    for {
      _ <- ZIOUtils.validate(verifyUrlFormat(url))(
        WebhookValidationError.UrlFormatError
      )
      _ <- ZIOUtils.unless(skipVerification)(
        ZIOUtils.validate(verifyValidationRequest(url))(WebhookValidationError.WrongValidationKeyError)
      )
      normUrl = url.trim
      eventTypeStrSet = eventTypes.map(_.value)
      webhook <- execute(
        op = "getEndpoint.update",
        tracingAttributes = Map(
          "appId" -> appId.idString,
          "webhookId" -> webhookId,
          "url" -> normUrl,
          "eventTypes" -> eventTypeStrSet.mkString(","),
          "rateLimit" -> rateLimit.getOrElse(-1).toString,
          "disabled" -> disabled.toString
        ),
        task = svix.getEndpoint.update(
          appId.idString,
          webhookId,
          new EndpointUpdate()
            .url(URI.create(normUrl))
            .filterTypes(eventTypeStrSet.asJava)
            .rateLimit(rateLimit.map(_.toLong).map(long2Long).orNull)
            .disabled(disabled)
        )
      )
    } yield webhook
  }

  def deleteWebhook(
    appId: RadixId,
    webhookId: String
  ): Task[Unit] = {
    execute(
      op = "getEndpoint.delete",
      tracingAttributes = Map(
        "appId" -> appId.idString,
        "webhookId" -> webhookId
      ),
      task = svix.getEndpoint.delete(appId.idString, webhookId)
    )
  }

  def getWebhookSecret(appId: RadixId, webhookId: String): Task[String] = {
    execute(
      op = "getEndpoint.getSecret",
      tracingAttributes = Map(
        "appId" -> appId.idString,
        "webhookId" -> webhookId
      ),
      task = svix.getEndpoint.getSecret(appId.idString, webhookId).getKey
    )
  }

  def rotateWebhookSecret(
    appId: RadixId,
    webhookId: String,
    secret: Option[String]
  ): Task[Unit] = {
    execute(
      op = "getEndpoint.rotateSecret",
      tracingAttributes = Map(
        "appId" -> appId.idString,
        "webhookId" -> webhookId
      ),
      task = svix.getEndpoint.rotateSecret(
        appId.idString,
        webhookId,
        new EndpointSecretRotateIn().key(secret.orNull)
      )
    )
  }

  def listMessages[A <: EventTypeEnum](
    appId: RadixId,
    eventTypes: Set[A]
  ): Task[List[MessageOut]] = {
    val option = new MessageListOptions()
    val eventTypeStrSet = eventTypes.map(_.value)
    option.setEventTypes(eventTypeStrSet.asJava)

    execute(
      op = "getMessage.list",
      tracingAttributes = Map(
        "appId" -> appId.idString,
        "eventTypes" -> eventTypeStrSet.mkString(",")
      ),
      task = svix.getMessage
        .list(
          appId.idString,
          option
        )
        .getData
        .asScala
        .toList
    )
  }

  def createMessage(
    appId: RadixId,
    eventType: EventTypeEnum,
    payload: String
  ): Task[MessageOut] = {
    execute(
      op = "getMessage.create",
      tracingAttributes = Map(
        "appId" -> appId.idString,
        "eventType" -> eventType.value
      ),
      task = svix.getMessage.create(
        appId.idString,
        new MessageIn()
          .eventType(eventType.value)
          .payload(payload)
      )
    )
  }

  private def execute[A](
    op: String,
    task: => A,
    tracingAttributes: Map[String, String] = Map.empty
  ): Task[A] = {
    val attributes = tracingAttributes ++ Map("op" -> op)
    val effect = ZIOTelemetryUtils
      .traceWithChildSpan(spanName, SpanKind.CLIENT, attributes)(
        ZIO.attemptBlocking(task).onErrorHandleWith(err => ZIO.fail(err))
      )
    effect.provideEnvironment(tracingEnvironment.environment)
  }

  private def verifyUrlFormat(url: String): Task[Boolean] = {
    ZIO.attempt(
      UrlValidatorUtils.SimpleWebLinkValidator.matches(url.trim.toLowerCase)
    )
  }

  private def verifyValidationRequest(url: String): Task[Boolean] = {
    val task = for {
      key <- ZIO.succeed(generateChallengeKey)
      response <- ArmeriaClient
        .rawRequest(
          emptyRequest
            .get(uri"${url.trim}")
            .headers(
              Map[String, String](svixConfig.challengeHeader -> key)
            )
            .readTimeout(FiniteDuration(15, TimeUnit.SECONDS))
        )
        .provideEnvironment(httpClient.environment ++ tracingEnvironment.environment)
      _ <- ZIOUtils.failUnless(response.code.isSuccess)(
        WebhookValidationError.ValidationRequestError(response.code.code)
      )
      returnedKeyOpt = response.header(svixConfig.challengeHeader)
    } yield returnedKeyOpt.contains(key)

    task.catchAllCause {
      case Cause.Fail(ex, _) if ScalaUtils.isMatch[WebhookValidationError](ex) => ZIO.fail(ex)
      case cause                                                               => ZIO.logErrorCause(cause).as(false)
    }
  }

  protected def generateChallengeKey: String =
    Random.alphanumeric.take(svixConfig.challengeKeyLength).mkString("")

  private given webhookConversion: Conversion[EndpointOut, Webhook] = webhook =>
    Webhook(
      id = webhook.getId,
      url = webhook.getUrl.toString,
      enabledEvents = webhook.getFilterTypes.asScala.toSet,
      rateLimit = Option(webhook.getRateLimit).filter(_ != null).map(_.toInt), // scalafix:ok DisableSyntax.null
      disabled = Option(webhook.getDisabled)
        .filter(_ != null)
        .map(_.booleanValue())
        .orElse(Some(false)), // scalafix:ok DisableSyntax.null
      createdAt = webhook.getCreatedAt.toString,
      updatedAt = webhook.getUpdatedAt.toString
    )

}

object WebhookService {

  sealed trait WebhookValidationError extends ServiceException {
    def message: String

    override def getMessage: String = message
  }

  object WebhookValidationError {

    case object UrlFormatError extends WebhookValidationError {
      override def message: String = "URL has an invalid format"
    }

    case class ValidationRequestError(statusCode: Int) extends WebhookValidationError {

      override def message: String =
        s"URL fails the validation request. You may need to echo the challenge header in the validation request, or whitelist our webhook IP addresses. Status code: $statusCode"

    }

    case object WrongValidationKeyError extends WebhookValidationError {
      override def message: String = s"URL responds wrong validation key in the validation request"
    }

  }

}
