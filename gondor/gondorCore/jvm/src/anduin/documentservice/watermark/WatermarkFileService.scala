// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.documentservice.watermark

import java.time.Instant

import zio.{Task, ZIO}

import anduin.dms.DmsFeature
import anduin.dms.service.FileService
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.documentservice.pdf.PdfFileService
import anduin.documentservice.watermark.WatermarkFileService.{
  WatermarkGenerationFailedDueToPasswordProtected,
  WatermarkGenerationUnknownException,
  WatermarkMetadata
}
import anduin.documentservice.watermark.WatermarkFileStorageStoreProvider.PrimaryKey
import anduin.fdb.record.FDBRecordDatabase
import anduin.model.common.user.UserId
import anduin.model.document.DocumentStorageId
import anduin.model.id.FileId
import anduin.serverless.common.ServerlessModels.watermark.WatermarkGeneratorRequest
import anduin.serverless.functions.WatermarkGeneratorServerless
import anduin.serverless.utils.ServerlessUtils
import anduin.service.AuthenticatedRequestContext
import anduin.storageservice.s3.{S3PreSignService, S3Service}
import anduin.util.FilenameUtils
import com.anduin.stargazer.service.utils.ZIOUtils

final case class WatermarkFileService(
  fileService: FileService,
  pdfFileService: PdfFileService,
  s3Service: S3Service,
  s3PreSignService: S3PreSignService,
  watermarkGeneratorServerless: WatermarkGeneratorServerless
) {

  private def checkValidWatermarkFile(
    originalStorageId: DocumentStorageId,
    watermarkStorageIdOpt: Option[DocumentStorageId],
    watermarkMetadata: WatermarkMetadata
  ): Task[Option[DocumentStorageId]] = {
    watermarkStorageIdOpt.fold[Task[Option[DocumentStorageId]]] {
      ZIO.succeed(Option.empty[DocumentStorageId])
    } { storageId =>
      for {
        metadataOpt <- s3Service.getObjectMetadataOpt(
          bucket = s3Service.s3Config.bucket,
          key = storageId.id
        )
        isValid = metadataOpt.exists(_.contentLength > 0)
        _ <- ZIOUtils.unless(isValid) {
          FDBRecordDatabase.transact(WatermarkFileStorageOperations.Production) {
            _.store
              .delete(
                PrimaryKey(
                  originalStorageId,
                  watermarkMetadata.watermarkText,
                  watermarkMetadata.color,
                  watermarkMetadata.layout,
                  watermarkMetadata.alpha
                )
              )
              .unit
          }
        }
      } yield {
        Option.when(isValid)(storageId)
      }
    }
  }

  /** Caller must guarantee that this file is pdf convertible
    */
  def getWatermarkUrl(
    actor: UserId,
    fileId: FileId,
    password: Option[String],
    watermarkMetadata: WatermarkMetadata,
    purpose: DmsTrackingActivityType,
    httpContext: Option[AuthenticatedRequestContext],
    includeDeleted: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[(String, Option[Instant])] = {
    for {
      (watermarkStorageId, sessionOpt) <- getWatermarkStorageIdOrAddWatermark(
        actor,
        fileId,
        password,
        watermarkMetadata,
        purpose,
        httpContext,
        includeDeleted
      )
      url <- s3PreSignService.getPreSignedLink(watermarkStorageId, useCloudFront = true)
    } yield url -> sessionOpt
  }

  def getWatermarkStorageIdOrAddWatermark(
    actor: UserId,
    fileId: FileId,
    password: Option[String],
    watermarkMetadata: WatermarkMetadata,
    purpose: DmsTrackingActivityType,
    httpContext: Option[AuthenticatedRequestContext],
    includeDeleted: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[(DocumentStorageId, Option[Instant])] = {
    for {
      fileName <- fileService.getFileName(actor)(fileId)
      (storageId, sessionOpt) <-
        fileService
          .getFileStorageIdWithSessionTimestamp(
            actor,
            fileId,
            purpose,
            httpContext,
            includeDeleted
          )
      watermarkedStorageIdOpt <- FDBRecordDatabase.transact(WatermarkFileStorageOperations.Production) {
        _.getWatermarkStorageId(
          PrimaryKey(
            storageId,
            watermarkMetadata.watermarkText,
            watermarkMetadata.color,
            watermarkMetadata.layout,
            watermarkMetadata.alpha
          )
        )
      }
      // Watermarked file generating may fail in last attempt
      watermarkFileStorageIdOpt <- checkValidWatermarkFile(
        storageId,
        watermarkedStorageIdOpt,
        watermarkMetadata
      )
      watermarkFileStorageId <- watermarkFileStorageIdOpt
        .fold {
          createWatermarkFile(
            actor,
            fileId,
            storageId,
            fileName,
            password,
            watermarkMetadata,
            httpContext
          )
        } {
          ZIO.attempt(_)
        }
    } yield watermarkFileStorageId -> sessionOpt
  }

  private def createWatermarkFile(
    actor: UserId,
    fileId: FileId,
    originalStorageId: DocumentStorageId,
    filename: String,
    password: Option[String],
    watermarkMetadata: WatermarkMetadata,
    httpContext: Option[AuthenticatedRequestContext]
  )(
    using dmsFeature: DmsFeature
  ): Task[DocumentStorageId] = {
    val watermarkFilename = FilenameUtils.getWatermarkFileName(filename)
    val watermarkStorageId = s3Service.generateRandomStorageId(Some(fileId), watermarkFilename)
    for {
      _ <- ZIO.logInfo(s"Applying watermark on $fileId")
      pdfStorageId <- pdfFileService
        .getPdfStorageIdOrConvert(
          actor,
          fileId,
          DmsTrackingActivityType.Internal,
          password,
          httpContext
        )
        .map(_._1)
      createWatermarkFileResponse <- watermarkGeneratorServerless.generateWatermark(
        WatermarkGeneratorRequest(
          inputStorageId = pdfStorageId.id,
          outputStorageId = watermarkStorageId.id,
          watermarkText = watermarkMetadata.watermarkText,
          color = watermarkMetadata.color,
          alpha = watermarkMetadata.alpha.toFloat / 100,
          watermarkLayout = watermarkMetadata.layout.name,
          s3Access = ServerlessUtils.getS3Access()
        )
      )
      successfulResponse <- ZIO
        .fromEither(createWatermarkFileResponse)
        .mapError { exception =>
          WatermarkGenerationUnknownException(exception.message)
        }
      _ <- ZIOUtils.failWhen(successfulResponse.isPasswordProtectedFile) {
        WatermarkGenerationFailedDueToPasswordProtected
      }
      _ <- ZIO.logInfo(s"Watermark was applied on $fileId")
      _ <- FDBRecordDatabase.transact(WatermarkFileStorageOperations.Production) {
        _.addWatermarkStorageIdIfNotExist(
          PrimaryKey(
            originalStorageId,
            watermarkMetadata.watermarkText,
            watermarkMetadata.color,
            watermarkMetadata.layout,
            watermarkMetadata.alpha
          ),
          watermarkStorageId
        )
      }
    } yield watermarkStorageId
  }

}

object WatermarkFileService {

  final case class WatermarkMetadata(
    watermarkText: String,
    color: Int,
    layout: WatermarkLayout,
    alpha: Int
  )

  sealed abstract class GenerateWatermarkFailure(message: String) extends Exception {
    override def getMessage: String = message
  }

  case object WatermarkGenerationFailedDueToPasswordProtected
      extends GenerateWatermarkFailure("Unable to generate watermark for password protected file")

  case class WatermarkGenerationUnknownException(message: String) extends GenerateWatermarkFailure(message)

}
