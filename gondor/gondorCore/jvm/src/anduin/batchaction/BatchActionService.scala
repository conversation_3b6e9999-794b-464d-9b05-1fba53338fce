// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.batchaction

import java.time.Instant

import io.circe.Json
import io.github.arainko.ducktape.*
import zio.temporal.ZWorkflowExecution
import zio.{Task, ZIO}

import anduin.batchaction.BatchActionService.given
import anduin.batchaction.config.AutoRemoveFrontendTrackingConfig
import anduin.batchaction.endpoint.*
import anduin.fdb.record.FDBRecordDatabase
import anduin.fdb.record.model.RecordIO
import anduin.id.batchaction.{BatchActionId, BatchActionItemId}
import anduin.model.common.user.UserId
import anduin.model.id.{BatchActionItemIdFactory, LongTaskIdFactory}
import anduin.model.notichannel.UserNotificationChannels
import anduin.radix.RadixId
import anduin.service.GeneralServiceException
import anduin.utils.ScalaUtils
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils

final case class BatchActionService(
  natsNotificationService: NatsNotificationService,
  batchActionTemporalService: BatchActionTemporalService
) {

  def startBatchActionInternal(
    parent: RadixId,
    actor: UserId,
    actionType: BatchActionType,
    batchActionItemsData: List[Json],
    postExecuteDataOpt: Option[Json] = None,
    frontendTracking: BatchActionFrontendTracking,
    startWorkflow: BatchActionWorkflowParam => Task[ZWorkflowExecution]
  ): Task[BatchActionId] = {
    for {
      (batchActionId, batchActionItemIds) <- setupBatchAction(
        parent,
        actor,
        actionType,
        batchActionItemsData,
        postExecuteDataOpt,
        frontendTracking
      )
      workflowExecution <- startWorkflow(
        BatchActionWorkflowParam(
          batchActionId,
          batchActionItemIds
        )
      )
      workflowId = workflowExecution.workflowId
      runId = workflowExecution.runId
      _ <- storeBatchActionWorkflowId(
        batchActionId,
        workflowId
      )
      _ <- ZIO.logInfo(s"Started batch action $batchActionId with workflowId $workflowId and runId $runId")
    } yield batchActionId
  }

  def startMockBatchAction(
    parent: RadixId,
    actor: UserId,
    actionType: BatchActionType,
    batchActionItemsData: List[Json],
    postExecuteDataOpt: Option[Json] = None,
    frontendTracking: BatchActionFrontendTracking
  ): Task[BatchActionId] = {
    setupBatchAction(
      parent,
      actor,
      actionType,
      batchActionItemsData,
      postExecuteDataOpt,
      frontendTracking
    ).map(_._1)
  }

  private def setupBatchAction(
    parent: RadixId,
    actor: UserId,
    actionType: BatchActionType,
    batchActionItemsData: List[Json],
    postExecuteDataOpt: Option[Json],
    frontendTracking: BatchActionFrontendTracking
  ): Task[(BatchActionId, List[BatchActionItemId])] = {
    for {
      longTaskId <- ZIO.attempt(LongTaskIdFactory.unsafeRandomId(parent))
      batchActionId <- ZIO.attempt(BatchActionId(longTaskId))

      batchActionItemIds <- ZIOUtils
        .foreachParN(4)(batchActionItemsData.zipWithIndex.sliding(100, 100).toList) { items =>
          BatchActionStoreOperations.transact { ops =>
            RecordIO.parTraverseN(4)(items) { case (item, order) =>
              val batchActionItemId = BatchActionItemIdFactory.unsafeRandomId(batchActionId)
              ops
                .createItem(
                  BatchActionItemModel(
                    batchActionItemId = batchActionItemId,
                    status = BatchActionItemStatusWaiting(Some(Instant.now)),
                    data = Some(item),
                    workflowId = None,
                    order = order
                  )
                )
                .map(_ => batchActionItemId)
            }
          }
        }
        .map(_.flatten)

      _ <- BatchActionStoreOperations.transact(
        _.create(
          BatchActionModel(
            batchActionId = batchActionId,
            createdBy = actor,
            createdAt = Some(Instant.now),
            workflowId = None,
            frontendTracking = frontendTracking,
            actionType = actionType,
            postExecuteStatus = BatchActionItemStatusWaiting(Some(Instant.now)),
            postExecuteData = postExecuteDataOpt
          )
        )
      )

      _ <- frontendTracking match {
        case BatchActionFrontendTracking.ACTOR_TRACKING =>
          for {
            _ <- FDBRecordDatabase.transact(BatchActionStoreOperations.Production) { op =>
              op.addUserBatchAction(actor, batchActionId)
            }
          } yield ()
        case _ => ZIO.unit
      }
      _ <- notifyFrontendTracking(batchActionId)
    } yield batchActionId -> batchActionItemIds
  }

  private def storeBatchActionWorkflowId(
    batchActionId: BatchActionId,
    workflowId: String
  ): Task[Unit] =
    FDBRecordDatabase
      .transact(BatchActionStoreOperations.Production)(
        _.update(batchActionId)(
          _.copy(workflowId = Some(workflowId))
        )
      )
      .unit

  def updateSingleActionStatusInternal(
    batchActionItemId: BatchActionItemId,
    updateStatusFn: BatchActionItemStatus => BatchActionItemStatus
  ): Task[(BatchActionItemModel, UserId)] = {
    val batchActionId = batchActionItemId.parent
    for {
      (batchActionModel, batchActionItemModel) <- FDBRecordDatabase.transact(
        BatchActionStoreOperations.Production
      ) { ops =>
        for {
          batchActionModel <- ops.get(batchActionId)
          batchActionItemModel <- ops.updateItem(batchActionItemId)(oldModel =>
            oldModel.copy(status = updateStatusFn(oldModel.status))
          )
        } yield batchActionModel -> batchActionItemModel
      }
      _ <- notifyFrontendTracking(batchActionId)
    } yield (batchActionItemModel, batchActionModel.createdBy)
  }

  def updatePostExecuteActionStatus(
    batchActionId: BatchActionId,
    updateStatusFn: BatchActionItemStatus => BatchActionItemStatus
  ): Task[(BatchActionModel, UserId)] = {
    for {
      batchActionModel <- FDBRecordDatabase.transact(
        BatchActionStoreOperations.Production
      ) { ops =>
        for {
          batchActionModel <- ops.update(batchActionId) { oldModel =>
            oldModel.copy(postExecuteStatus = updateStatusFn(oldModel.postExecuteStatus))
          }
        } yield batchActionModel
      }
      _ <- notifyFrontendTracking(batchActionId)
    } yield (batchActionModel, batchActionModel.createdBy)
  }

  def getUserBatchActions(parent: RadixId, actor: UserId): Task[Seq[BatchActionInfo]] = {
    for {
      _ <- ZIO.logInfo(s"$actor get user batch actions in parent $parent")
      userBatchActionIds <- FDBRecordDatabase.transact(BatchActionStoreOperations.Production)(
        _.getUserBatchActions(actor).map(_.filter(_.parent.parent == parent))
      )
      batchActionInfos <- ZIOUtils.foreachParN(4)(userBatchActionIds)(getBatchActionInfo)
      refinedBatchActionInfos <- ZIOUtils.foreachParN(4)(batchActionInfos) { batchActionInfo =>
        if (AutoRemoveFrontendTrackingConfig.shouldAutoRemove(batchActionInfo)) {
          removeBatchActionFrontendTracking(
            batchActionInfo.batchActionId,
            reason = BatchActionRemovedReason.AutoRemove
          ).as(Option.empty[BatchActionInfo])
        } else {
          ZIO.succeed(Some(batchActionInfo))
        }
      }
    } yield refinedBatchActionInfos.flatten
  }

  def getBatchActionInfo(batchActionId: BatchActionId, actor: UserId): Task[BatchActionInfo] = {
    for {
      _ <- ZIO.logInfo(s"$actor get batch action info of $batchActionId")
      batchActionInfo <- getBatchActionInfo(batchActionId)
    } yield batchActionInfo
  }

  def getBatchActionStatus(batchActionId: BatchActionId, actor: UserId): Task[BatchActionStatus] = {
    for {
      _ <- ZIO.logInfo(s"$actor get batch action status of $batchActionId")
      batchActionInfo <- getBatchActionInfo(batchActionId)
    } yield BatchActionStatus(
      batchActionId = batchActionId,
      itemStatus = batchActionInfo.items.map(item => item.batchActionItemId -> item.status).toMap,
      postExecuteStatus = batchActionInfo.postExecuteStatus,
      hasFrontendTracking = batchActionInfo.hasFrontendTracking
    )
  }

  private[batchaction] def getBatchActionInfo(
    batchActionId: BatchActionId
  ): Task[BatchActionInfo] = {
    for {
      _ <- cancelBatchActionIfWorkflowEnded(batchActionId)
      batchActionInfo <- FDBRecordDatabase.transact(BatchActionStoreOperations.Production) { ops =>
        for {
          batchActionModel <- ops.get(batchActionId)
          batchActionItemModels <- ops.getItems(batchActionId)
          hasFrontendTracking <- ops
            .getUserBatchActions(batchActionModel.createdBy)
            .map(
              _.contains(batchActionId)
            )
        } yield batchActionModel
          .into[BatchActionInfo]
          .transform(
            Field.const(_.items, batchActionItemModels.map(_.to[BatchActionItemInfo])),
            Field.const(_.hasFrontendTracking, hasFrontendTracking)
          )
      }
    } yield batchActionInfo
  }

  def cancelBatchAction(
    batchActionId: BatchActionId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor cancel batch action $batchActionId")
      _ <- cancelBatchActionByStatus(
        batchActionId,
        shouldCancelByStatus = ScalaUtils.isMatch[BatchActionItemStatusWaiting],
        reason = BatchActionItemStatusCancelledReason.UserTrigger
      )
      _ <- notifyFrontendTracking(batchActionId)
    } yield ()
  }

  private def cancelBatchActionIfWorkflowEnded(
    batchActionId: BatchActionId
  ): Task[Unit] = {
    for {
      batchActionModel <- FDBRecordDatabase.transact(BatchActionStoreOperations.Production)(
        _.get(batchActionId)
      )
      endedWorkflowOpt <- ZIOUtils.traverseOption2(batchActionModel.workflowId) { workflowId =>
        for {
          (isEnded, workflowStatus) <- batchActionTemporalService.isWorkflowEnded(workflowId)
        } yield Option.when(isEnded)(workflowId -> workflowStatus)
      }
      _ <- ZIOUtils.traverseOptionUnit(endedWorkflowOpt) { case (workflowId, workflowStatus) =>
        for {
          _ <- ZIO.logInfo(
            s"Cancel batch action $batchActionId because workflow $workflowId ended with status $workflowStatus"
          )
          _ <- cancelBatchActionByStatus(
            batchActionId,
            shouldCancelByStatus = status =>
              ScalaUtils.isMatch[BatchActionItemStatusWaiting](status) ||
                ScalaUtils.isMatch[BatchActionItemStatusRunning](status),
            reason = BatchActionItemStatusCancelledReason.WorkflowTerminated
          )
        } yield ()
      }
    } yield ()
  }

  private def cancelBatchActionByStatus(
    batchActionId: BatchActionId,
    shouldCancelByStatus: BatchActionItemStatus => Boolean,
    reason: BatchActionItemStatusCancelledReason
  ): Task[Unit] = {
    FDBRecordDatabase.transact(BatchActionStoreOperations.Production) { ops =>
      for {
        batchActionModel <- ops.get(batchActionId)
        itemModels <- ops.getItems(batchActionId)
        _ <- RecordIO.traverse(itemModels) { itemModel =>
          RecordIO.when(shouldCancelByStatus(itemModel.status))(
            ops.updateItem(itemModel.batchActionItemId)(
              _.copy(status = BatchActionItemStatusCancelled(cancelledAt = Some(Instant.now), reason = reason))
            )
          )
        }
        _ <- RecordIO.when(shouldCancelByStatus(batchActionModel.postExecuteStatus))(
          ops.update(batchActionId) {
            _.copy(
              postExecuteStatus = BatchActionItemStatusCancelled(cancelledAt = Some(Instant.now), reason = reason)
            )
          }
        )
      } yield ()
    }
  }

  def removeBatchActionFrontendTrackingManually(
    params: RemoveBatchActionFrontendTrackingParams,
    actor: UserId
  ): Task[Unit] = {
    val batchActionId = params.batchActionId
    for {
      _ <- ZIO.logInfo(s"$actor remove batch action frontend tracking manually $batchActionId")
      _ <- ZIOUtils.when(params.shouldStopBatchAction) {
        cancelBatchActionByStatus(
          batchActionId,
          shouldCancelByStatus = ScalaUtils.isMatch[BatchActionItemStatusWaiting],
          reason = BatchActionItemStatusCancelledReason.UserTrigger
        )
      }
      _ <- removeBatchActionFrontendTracking(
        batchActionId,
        BatchActionRemovedReason.UserTriggerRemove
      )
    } yield ()
  }

  private def removeBatchActionFrontendTracking(
    batchActionId: BatchActionId,
    reason: BatchActionRemovedReason
  ): Task[Unit] = {
    for {
      batchActionModel <- FDBRecordDatabase.transact(BatchActionStoreOperations.Production)(
        _.update(batchActionId)(
          _.copy(removed = Some(BatchActionRemoved(Some(Instant.now), reason)))
        )
      )
      _ <- batchActionModel.frontendTracking match {
        case BatchActionFrontendTracking.ACTOR_TRACKING =>
          FDBRecordDatabase.transact(BatchActionStoreOperations.Production) {
            _.removeUserBatchAction(batchActionModel.createdBy, batchActionModel.batchActionId)
          }
        case _ => ZIO.unit
      }
    } yield ()
  }

  def notifyFrontendTracking(
    batchActionId: BatchActionId
  ): Task[Unit] = {
    for {
      (createdBy, hasFrontendTracking) <- FDBRecordDatabase.transact(BatchActionStoreOperations.Production) { ops =>
        for {
          createdBy <- ops.get(batchActionId).map(_.createdBy)
          hasFrontendTracking <- ops.getUserBatchActions(createdBy).map(_.contains(batchActionId))
        } yield createdBy -> hasFrontendTracking
      }
      _ <- ZIOUtils.when(hasFrontendTracking) {
        natsNotificationService.publish(batchActionId, UserNotificationChannels.batchAction(createdBy))
      }
    } yield ()
  }

  def validateAccessValidator(
    batchActionId: BatchActionId,
    actor: UserId
  ): Task[Unit] = {
    for {
      batchActionModel <- FDBRecordDatabase.transact(BatchActionStoreOperations.Production)(
        _.get(batchActionId)
      )
      _ <- ZIOUtils.failUnless(actor == batchActionModel.createdBy)(
        GeneralServiceException(s"User $actor do not have permission to access $batchActionId")
      )
    } yield ()
  }

  def getBatchActionItemModelUnsafe(
    batchActionItemId: BatchActionItemId
  ): Task[BatchActionItemModel] = {
    FDBRecordDatabase
      .transact(BatchActionStoreOperations.Production)(
        _.getItem(batchActionItemId)
      )
  }

}

object BatchActionService {

  given nonEmptyStatusTransformer: Transformer[BatchActionItemStatus.NonEmpty, BatchActionItemStatusInfo] =
    Transformer
      .define[BatchActionItemStatus.NonEmpty, BatchActionItemStatusInfo]
      .build(
        Case.computed[BatchActionItemStatusWaiting](_.to[BatchActionItemStatusInfo.Waiting]),
        Case.computed[BatchActionItemStatusRunning](_.to[BatchActionItemStatusInfo.Running]),
        Case.computed[BatchActionItemStatusCancelled](_.to[BatchActionItemStatusInfo.Cancelled]),
        Case.computed[BatchActionItemStatusSucceeded](_.to[BatchActionItemStatusInfo.Succeeded]),
        Case.computed[BatchActionItemStatusFailed](
          _.into[BatchActionItemStatusInfo.Failed]
            .transform(
              Field.computed(
                _.error,
                _.error match {
                  case _: BatchActionItemNoPermissionError => "You do not have permission"
                  case _                                   => "Server error"
                }
              )
            )
        )
      )

  given statusTransformer: Transformer[BatchActionItemStatus, BatchActionItemStatusInfo] =
    Transformer
      .define[BatchActionItemStatus, BatchActionItemStatusInfo]
      .build(
        Case.computed[BatchActionItemStatus.NonEmpty](_.to[BatchActionItemStatusInfo]),
        Case.const[BatchActionItemStatus.Empty.type](BatchActionItemStatusInfo.Waiting(None))
      )

  given cancelledReasonTransformer
    : Transformer[BatchActionItemStatusCancelledReason, BatchActionItemStatusInfo.CancelledReason] =
    Transformer
      .define[BatchActionItemStatusCancelledReason, BatchActionItemStatusInfo.CancelledReason]
      .build(
        Case.computed[BatchActionItemStatusCancelledReason.Recognized](_.to[BatchActionItemStatusInfo.CancelledReason]),
        Case.const[BatchActionItemStatusCancelledReason.Unrecognized](
          BatchActionItemStatusInfo.CancelledReason.WorkflowTerminated
        )
      )

}
