// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.serverless.common

import scala.concurrent.duration.FiniteDuration

import io.circe.generic.semiauto.deriveCodec
import io.circe.{<PERSON><PERSON>, <PERSON><PERSON>, JsonObject}

import anduin.circe.generic.semiauto.{CirceCodec, deriveCodecWithDefaults, deriveEnumCodec, deriveStringEnumCodec}
import anduin.cue.model.commands.CueCommands.Command
import anduin.dataextract.models.{MappingContentAlgorithmVersion, OverlappedContent, PageMatch, PageMatchingResult}
import anduin.enumeration.{StringEnum, StringEnumCompanion}
import anduin.formmatching.FormMatchingMode
import anduin.forms.Form.FormNamespace
import anduin.forms.annotation.*
import anduin.forms.engine.GaiaEngineData.{GaiaEngineError, ImportDataResult}
import anduin.forms.engine.{GaiaEngineData, GaiaState}
import anduin.forms.event.Patches
import anduin.forms.testing.data.RuntimeError
import anduin.forms.testing.engine.RunnerEngine.{EngineCompileError, ErrorReportMode}
import anduin.id.serverless.{ServerlessAction, ServerlessRequestId}
import anduin.model.codec.EitherCodec.given
import anduin.model.id.ServerlessRequestIdFactory
import anduin.model.textract.TextractProcessType
import anduin.serverless.common.ServerlessModels.annotations.PdfFormSchema
import anduin.serverless.common.ServerlessModels.formModule.CueGeneratorInput.GenerateTableMappingModule.TableModule
import anduin.storageservice.zot.models.ModuleInfo
import com.anduin.stargazer.service.{CommonConfig, GondorBackendConfig}

object ServerlessModels {

  final case class S3Access(
    accessKey: String,
    secretKey: String,
    region: String,
    bucket: String,
    publicBucket: String
  )

  object S3Access {
    given Codec.AsObject[S3Access] = deriveCodecWithDefaults

    val Default: S3Access = S3Access("", "", "", "", "")
  }

  final case class LiteLlmAccess(
    apiKey: String,
    model: String,
    baseUrl: String
  ) derives CirceCodec.WithDefaults

  object LiteLlmAccess {

    enum Model(val value: String) extends StringEnum {
      case GPT4o extends Model("openai/gpt-4o")
      case GPT4oMini extends Model("openai/openai/gpt-4o-mini")
      case ClaudeSonnet3_5V1 extends Model("openai/claude-sonnet-3-5-aws-bedrock-us-v1")
      case ClaudeSonnet3_5V2 extends Model("openai/claude-sonnet-3-5-aws-bedrock-us-v2")
      case ClaudeSonnet3_7V1 extends Model("openai/us.anthropic.claude-3-7-sonnet-20250219-v1:0")
      case ClaudeOpus3 extends Model("openai/claude-opus-3-aws-bedrock-us")
      case Gemini2_0FlashExp extends Model("openai/gemini-2.0-flash-exp")
      case Gemini1_5Pro extends Model("openai/gemini-1.5-pro")
    }

    def fromLiteLlmConfig(liteLlmConfig: GondorBackendConfig.LiteLlmConfig, model: Model = Model.ClaudeOpus3)
      : LiteLlmAccess = {
      LiteLlmAccess(
        apiKey = liteLlmConfig.apiKey,
        model = model.value,
        baseUrl = liteLlmConfig.apiBase
      )
    }

  }

  sealed trait ServerlessRequest derives CanEqual {
    def id: ServerlessRequestId
  }

  sealed trait ServerlessResponse derives CanEqual {
    def id: ServerlessRequestId
    def message: String
  }

  final case class ServerlessAsyncRequest[R](
    id: ServerlessRequestId,
    responseQueueUrl: String,
    request: R
  )

  final case class PdfConvertRequest(
    s3Access: S3Access,
    inputKey: String,
    outputKey: String
  )

  object PdfConvertRequest {
    given Codec.AsObject[PdfConvertRequest] = deriveCodecWithDefaults
  }

  private[serverless] final case class PdfConvertRequestWithId(
    id: ServerlessRequestId,
    request: PdfConvertRequest
  ) extends ServerlessRequest

  private[serverless] object PdfConvertRequestWithId {
    given Codec.AsObject[PdfConvertRequestWithId] = deriveCodecWithDefaults
  }

  final case class PdfConvertResponse(
    id: ServerlessRequestId,
    output: String
  ) extends ServerlessResponse {
    val message: String = output
  }

  object PdfConvertResponse {
    given Codec.AsObject[PdfConvertResponse] = deriveCodecWithDefaults
  }

  final case class RedliningRequest(
    s3Access: S3Access,
    originalPath: String,
    revisedPath: String,
    resultDocPath: String,
    resultPdfPath: String
  )

  object RedliningRequest {
    given Codec.AsObject[RedliningRequest] = deriveCodecWithDefaults
  }

  private[serverless] final case class RedliningRequestWithId(
    id: ServerlessRequestId,
    request: RedliningRequest
  ) extends ServerlessRequest

  private[serverless] object RedliningRequestWithId {
    given Codec.AsObject[RedliningRequestWithId] = deriveCodecWithDefaults
  }

  final case class RedliningResponse(
    requestId: ServerlessRequestId,
    message: String
  ) extends ServerlessResponse {
    val id: ServerlessRequestId = requestId
  }

  object RedliningResponse {
    given Codec.AsObject[RedliningResponse] = deriveCodecWithDefaults
  }

  final case class OcrTextExtractRequest(
    s3Access: S3Access,
    inputKey: String,
    // Dpi for parsing purpose.
    // Higher value will produce higher accuracy but come with longer execution time
    dpi: Int = 300
  )

  object OcrTextExtractRequest {
    given Codec.AsObject[OcrTextExtractRequest] = deriveCodecWithDefaults
  }

  private[serverless] final case class OcrTextExtractRequestWithId(
    id: ServerlessRequestId,
    request: OcrTextExtractRequest
  ) extends ServerlessRequest

  private[serverless] object OcrTextExtractRequestWithId {
    given Codec.AsObject[OcrTextExtractRequestWithId] = deriveCodecWithDefaults
  }

  final case class OcrTextExtractResponse(
    message: String,
    id: ServerlessRequestId,
    pages: Seq[String]
  ) extends ServerlessResponse

  object OcrTextExtractResponse {
    given Codec.AsObject[OcrTextExtractResponse] = deriveCodecWithDefaults
  }

  object text {

    final case class TikaExtractRequest(
      s3Access: S3Access,
      inputKey: String,
      mainOnly: Boolean = true,
      metaData: Boolean = false
    )

    object TikaExtractRequest {
      given Codec.AsObject[TikaExtractRequest] = deriveCodecWithDefaults
    }

    private[serverless] final case class TikaExtractRequestWithId(
      id: ServerlessRequestId,
      request: TikaExtractRequest
    ) extends ServerlessRequest

    private[serverless] object TikaExtractRequestWithId {
      given Codec.AsObject[TikaExtractRequestWithId] = deriveCodecWithDefaults
    }

    final case class TikaContent(
      metadata: Option[String],
      content: String,
      isEncrypted: Boolean
    ) {

      lazy val metaJsonObject: Option[JsonObject] = {
        for {
          str <- metadata
          json <- io.circe.parser.parse(str).toOption
          res <- json.asObject
        } yield res
      }

      // This is for pdf only
      lazy val hasMissingUnicode: Boolean = {
        val unmappedUnicodeCharsPerPage = metaJsonObject
          .flatMap(
            _.toMap
              .get("pdf:unmappedUnicodeCharsPerPage")
              .flatMap(_.asArray.map(_.toList))
              .map {
                _.flatMap(_.as[Int].toOption)
              }
          )
          .getOrElse(List.empty)
        unmappedUnicodeCharsPerPage.exists(_ > 0)
      }

    }

    object TikaContent {
      given Codec.AsObject[TikaContent] = deriveCodecWithDefaults
    }

    final case class TikaExtractResponse(
      message: String,
      id: ServerlessRequestId,
      content: TikaContent
    ) extends ServerlessResponse

    object TikaExtractResponse {
      given Codec.AsObject[TikaExtractResponse] = deriveCodecWithDefaults
    }

    final case class TextDiff(
      left_document_content: Option[String] = None,
      right_document_content: Option[String] = None,
      left_document_id: Option[String] = None,
      right_document_id: Option[String] = None,
      timelimit: Float = 0.0f,
      checklines: Boolean = false,
      cleanup_semantic: Boolean = true,
      counts_only: Boolean = true,
      diff_by_word: Boolean = false
    )

    object TextDiff {
      given Codec.AsObject[TextDiff] = deriveCodecWithDefaults
    }

    final case class TextDiffRequest(
      s3Access: S3Access,
      diffs: List[TextDiff]
    )

    object TextDiffRequest {
      given Codec.AsObject[TextDiffRequest] = deriveCodecWithDefaults
    }

    private[serverless] final case class TextDiffRequestWithId(
      id: ServerlessRequestId,
      request: TextDiffRequest
    ) extends ServerlessRequest

    private[serverless] object TextDiffRequestWithId {
      given Codec.AsObject[TextDiffRequestWithId] = deriveCodecWithDefaults
    }

    final case class TextDiffEntry(
      ops: String,
      count: Int,
      changes: Option[String] = None
    ) derives CanEqual

    object TextDiffEntry {
      given Codec.AsObject[TextDiffEntry] = deriveCodecWithDefaults
    }

    final case class TextDiffResponse(
      message: String,
      id: ServerlessRequestId,
      result: List[List[TextDiffEntry]]
    ) extends ServerlessResponse

    object TextDiffResponse {
      given Codec.AsObject[TextDiffResponse] = deriveCodecWithDefaults
    }

  }

  final case class ZipRequestFileInfo(
    path: Seq[String], // Absolute path of the file inside the zip result
    storageId: Option[String] // None means the file is a folder instead of a file
  ) derives CanEqual {
    val isFolder: Boolean = storageId.isEmpty
  }

  object ZipRequestFileInfo {
    given Codec.AsObject[ZipRequestFileInfo] = deriveCodecWithDefaults
  }

  final case class ZipRequestInput(
    s3Access: S3Access,
    files: Seq[ZipRequestFileInfo]
  ) derives CanEqual

  object ZipRequestInput {
    given Codec.AsObject[ZipRequestInput] = deriveCodecWithDefaults
  }

  final case class ZipRequestOutput(
    s3Access: S3Access,
    outputKey: String
  )

  object ZipRequestOutput {
    given Codec.AsObject[ZipRequestOutput] = deriveCodecWithDefaults
  }

  final case class ZipRequest(
    inputs: Seq[ZipRequestInput],
    output: ZipRequestOutput
  )

  object ZipRequest {
    given Codec.AsObject[ZipRequest] = deriveCodecWithDefaults
  }

  private[serverless] final case class ZipRequestWithId(
    id: ServerlessRequestId,
    request: ZipRequest
  ) extends ServerlessRequest

  private[serverless] object ZipRequestWithId {
    given Codec.AsObject[ZipRequestWithId] = deriveCodecWithDefaults
  }

  final case class ZipResponse(
    message: String,
    id: ServerlessRequestId
  ) extends ServerlessResponse

  object ZipResponse {
    given Codec.AsObject[ZipResponse] = deriveCodecWithDefaults
  }

  object htmlToPdf {

    enum PageOrientation(val value: String) extends StringEnum {

      case Portrait extends PageOrientation("Portrait")

      case Landscape extends PageOrientation("Landscape")
    }

    object PageOrientation extends StringEnumCompanion[PageOrientation] {
      given Codec[PageOrientation] = deriveStringEnumCodec
    }

    final case class S3FileInfo(
      storageId: String,
      s3Access: S3Access
    )

    object S3FileInfo {
      given Codec.AsObject[S3FileInfo] = deriveCodecWithDefaults
    }

    // Find all available config at
    // https://github.com/anduintransaction/infrastructure/blob/master/serverless/python/anduin/html_to_pdf/html_to_pdf_converter.py
    final case class HtmlToPdfRequestConfig(
      orientation: PageOrientation = PageOrientation.Portrait,
      pageSize: Option[String] = Some("Letter"),
      footerSpacing: Option[Float] = Some(10),
      marginTop: Option[String] = None,
      marginLeft: Option[String] = None,
      marginRight: Option[String] = None,
      marginBottom: Option[String] = None,
      zoom: Option[Float] = None,
      minimumFontSize: Option[Int] = None,
      footerHtml: Option[String] = None
    )

    object HtmlToPdfRequestConfig {
      given Codec.AsObject[HtmlToPdfRequestConfig] = deriveCodecWithDefaults
    }

    final case class HtmlToPdfRequestInput(
      config: HtmlToPdfRequestConfig,
      content: Option[String] = None,
      url: Option[String] = None,
      fileId: Option[S3FileInfo] = None
    )

    object HtmlToPdfRequestInput {
      given Codec.AsObject[HtmlToPdfRequestInput] = deriveCodecWithDefaults
    }

    final case class HtmlToPdfRequest(
      input: HtmlToPdfRequestInput,
      output: S3FileInfo
    )

    object HtmlToPdfRequest {
      given Codec.AsObject[HtmlToPdfRequest] = deriveCodecWithDefaults
    }

    private[serverless] final case class HtmlToPdfRequestWithId(
      id: ServerlessRequestId,
      request: HtmlToPdfRequest
    ) extends ServerlessRequest

    private[serverless] object HtmlToPdfRequestWithId {
      given Codec.AsObject[HtmlToPdfRequestWithId] = deriveCodecWithDefaults
    }

    final case class HtmlToPdfResponse(
      id: ServerlessRequestId,
      message: String
    ) extends ServerlessResponse

    object HtmlToPdfResponse {
      given Codec.AsObject[HtmlToPdfResponse] = deriveCodecWithDefaults
    }

  }

  object rclone {

    final case class RcloneCopyItem(
      source: List[String], // ["path", "to", "source"]. This can be either file path or directory path
      dest: List[String] // ["path", "to", "dest"]. This can be either file path or directory path
    )

    object RcloneCopyItem {
      given Codec.AsObject[RcloneCopyItem] = deriveCodecWithDefaults
    }

    final case class S3Config(
      accessKey: String,
      secretKey: String,
      region: String,
      acl: Option[String] = None
    )

    object S3Config {
      given Codec.AsObject[S3Config] = deriveCodecWithDefaults
    }

    final case class BoxConfig(
      token: String
    )

    object BoxConfig {
      given Codec.AsObject[BoxConfig] = deriveCodecWithDefaults
    }

    final case class DropboxConfig(
      token: String
    )

    object DropboxConfig {
      given Codec.AsObject[DropboxConfig] = deriveCodecWithDefaults
    }

    final case class GoogleDriveConfig(
      serviceAccountConfig: String,
      sharedWithMe: Boolean = false, // True if use shared with me folder as root folder
      rootFolderId: String = ""
    )

    object GoogleDriveConfig {
      given Codec.AsObject[GoogleDriveConfig] = deriveCodecWithDefaults
    }

    final case class SharePointConfig(
      siteUrl: String,
      user: String,
      password: String
    )

    object SharePointConfig {
      given Codec.AsObject[SharePointConfig] = deriveCodecWithDefaults
    }

    final case class SftpServerConfig(
      host: String,
      user: String,
      port: Int,
      privateKey: String,
      password: Option[String]
    )

    object SftpServerConfig {
      given Codec.AsObject[SftpServerConfig] = deriveCodecWithDefaults
    }

    final case class ResourceConfig(
      s3Config: Option[S3Config] = None,
      driveConfig: Option[GoogleDriveConfig] = None,
      boxConfig: Option[BoxConfig] = None,
      dropboxConfig: Option[DropboxConfig] = None,
      sharePointConfig: Option[SharePointConfig] = None,
      sftpServerConfig: Option[SftpServerConfig] = None
    )

    object ResourceConfig {
      given Codec.AsObject[ResourceConfig] = deriveCodecWithDefaults
    }

    final case class RcloneCopyRequest(
      files: List[RcloneCopyItem],
      source: ResourceConfig,
      dest: ResourceConfig
    )

    object RcloneCopyRequest {
      given Codec.AsObject[RcloneCopyRequest] = deriveCodecWithDefaults
    }

    private[serverless] final case class RcloneCopyRequestWithId(
      id: ServerlessRequestId,
      request: RcloneCopyRequest
    ) extends ServerlessRequest

    private[serverless] object RcloneCopyRequestWithId {
      given Codec.AsObject[RcloneCopyRequestWithId] = deriveCodecWithDefaults
    }

    final case class RcloneCopyResponse(
      id: ServerlessRequestId,
      message: String
    ) extends ServerlessResponse

    object RcloneCopyResponse {
      given Codec.AsObject[RcloneCopyResponse] = deriveCodecWithDefaults
    }

  }

  object muPdf {

    sealed trait MuPDFRequest extends ServerlessRequest

    object MuPDFRequest {
      given Codec.AsObject[MuPDFRequest] = deriveCodecWithDefaults
    }

    final case class ThumbnailRequest(
      s3Access: S3Access,
      pdfStorageId: String,
      thumbnailStorageId: String,
      scale: Float // Need to rename to `quality` both here and in serverless infrastructure.
    )

    object ThumbnailRequest {
      given Codec.AsObject[ThumbnailRequest] = deriveCodecWithDefaults
    }

    final case class ThumbnailRequestWithId(
      id: ServerlessRequestId,
      request: ThumbnailRequest
    ) extends MuPDFRequest

    final case class PdfPageCountRequest(
      s3Access: S3Access,
      pdfStorageId: String,
      passwordOpt: Option[String]
    )

    object PdfPageCountRequest {
      given Codec.AsObject[PdfPageCountRequest] = deriveCodecWithDefaults
    }

    final case class PdfPageCountRequestWithId(
      id: ServerlessRequestId,
      request: PdfPageCountRequest
    ) extends MuPDFRequest

    object PdfPageCountRequestWithId {
      given Codec.AsObject[PdfPageCountRequestWithId] = deriveCodecWithDefaults
    }

    sealed trait MuPDFResponse extends ServerlessResponse

    object MuPDFResponse {
      given Codec.AsObject[MuPDFResponse] = deriveCodecWithDefaults
    }

    final case class ThumbnailResponse(
      id: ServerlessRequestId,
      message: String
    ) extends MuPDFResponse

    final case class PdfPageCountResponse(
      id: ServerlessRequestId,
      message: String,
      pageCount: Int
    ) extends MuPDFResponse

    sealed trait PageRange derives CanEqual

    object PageRange {
      // 1-based index
      case class SinglePage(index: Int) extends PageRange

      // if endIndexOpt is None, select from startIndex to the end of the file
      case class MultiplePages(startIndex: Int, endIndexOpt: Option[Int]) extends PageRange

      case object AllPages extends PageRange

      given Codec.AsObject[PageRange] = deriveCodecWithDefaults

      given Codec.AsObject[AllPages.type] = deriveCodecWithDefaults

      given Codec.AsObject[SinglePage] = deriveCodecWithDefaults

      given Codec.AsObject[MultiplePages] = deriveCodecWithDefaults
    }

    final case class MergePdfFileParam(
      storageId: String,
      pageRanges: Seq[PageRange] = Seq(PageRange.AllPages)
    )

    object MergePdfFileParam {
      given Codec.AsObject[MergePdfFileParam] = deriveCodecWithDefaults
    }

    final case class MergePdfRequest(
      s3Access: S3Access,
      inputPdfs: Seq[MergePdfFileParam],
      outputStorageId: String,
      flattenAcroForm: Option[Boolean]
    )

    object MergePdfRequest {
      given Codec.AsObject[MergePdfRequest] = deriveCodecWithDefaults
    }

    final case class MergePdfRequestWithId(
      id: ServerlessRequestId,
      request: MergePdfRequest
    ) extends MuPDFRequest

    sealed trait MergePdfException derives CanEqual {
      def message: String
    }

    object MergePdfException {
      given Codec.AsObject[MergePdfException] = deriveCodecWithDefaults
    }

    final case class PasswordProtectedFilesException(
      override val message: String,
      storageIds: Seq[String]
    ) extends MergePdfException

    final case class ServerlessException(
      override val message: String
    ) extends MergePdfException

    final case class MergePdfResponse(
      id: ServerlessRequestId,
      message: String,
      exception: Option[MergePdfException]
    ) extends MuPDFResponse

    final case class InsertPdfRequest(
      s3Access: S3Access,
      originalPdfStorageId: String,
      pdfToInsertStorageId: String,
      insertBeforePageIndex: Int,
      outputStorageId: String,
      flattenAcroForm: Option[Boolean] = Option(false)
    )

    object InsertPdfRequest {
      given Codec.AsObject[InsertPdfRequest] = deriveCodecWithDefaults
    }

    final case class InsertPdfRequestWithId(
      id: ServerlessRequestId,
      request: InsertPdfRequest
    ) extends MuPDFRequest

    final case class InsertPdfResponse(
      id: ServerlessRequestId,
      message: String
    ) extends MuPDFResponse

    final case class PageSize(
      width: Double,
      height: Double
    )

    object PageSize {
      given Codec.AsObject[PageSize] = deriveCodecWithDefaults
    }

    final case class PageMediaBoxesRequest(
      s3Access: S3Access,
      pdfStorageId: String
    )

    object PageMediaBoxesRequest {
      given Codec.AsObject[PageMediaBoxesRequest] = deriveCodecWithDefaults
    }

    final case class PageMediaBoxesRequestWithId(
      id: ServerlessRequestId,
      request: PageMediaBoxesRequest
    ) extends MuPDFRequest

    object PageMediaBoxesRequestWithId {
      given Codec.AsObject[PageMediaBoxesRequestWithId] = deriveCodecWithDefaults
    }

    final case class PageMediaBoxesResponse(
      id: ServerlessRequestId,
      message: String,
      // page -> PageSize, page is 1-based indexed
      pageMediaBoxMap: Map[Int, PageSize]
    ) extends MuPDFResponse

    object PageMediaBoxesResponse {
      given Codec.AsObject[PageMediaBoxesResponse] = deriveCodecWithDefaults
    }

    final case class SplitPdfPagesRequest(
      s3Access: S3Access,
      pdfStorageId: String,
      passwordOpt: Option[String],
      outputStoragePrefix: String,
      outputFileName: String
    )

    object SplitPdfPagesRequest {
      given Codec.AsObject[SplitPdfPagesRequest] = deriveCodecWithDefaults
    }

    final case class SplitPdfPagesRequestWithId(
      id: ServerlessRequestId,
      request: SplitPdfPagesRequest
    ) extends MuPDFRequest

    object SplitPdfPagesRequestWithId {
      given Codec.AsObject[SplitPdfPagesRequestWithId] = deriveCodecWithDefaults
    }

    final case class SplitPdfPagesResponse(
      id: ServerlessRequestId,
      message: String,
      outputStorageIds: List[String]
    ) extends MuPDFResponse

    object SplitPdfPagesResponse {
      given Codec.AsObject[SplitPdfPagesRequest] = deriveCodecWithDefaults
    }

    final case class PdfToImagesRequest(
      s3Access: S3Access,
      pdfStorageId: String,
      passwordOpt: Option[String],
      outputStoragePrefix: String,
      outputFileName: String
    )

    object PdfToImagesRequest {
      given Codec.AsObject[PdfToImagesRequest] = deriveCodecWithDefaults
    }

    final case class PdfToImagesRequestWithId(
      id: ServerlessRequestId,
      request: PdfToImagesRequest
    ) extends MuPDFRequest

    object PdfToImagesRequestWithId {
      given Codec.AsObject[SplitPdfPagesRequestWithId] = deriveCodecWithDefaults
    }

    final case class PdfToImagesResponse(
      id: ServerlessRequestId,
      message: String,
      outputStorageIds: List[String],
      error: Option[String]
    ) extends MuPDFResponse

    object PdfToImagesResponse {
      given Codec.AsObject[SplitPdfPagesRequest] = deriveCodecWithDefaults
    }

  }

  object computePageSimilarity {

    final case class ComputePageSimilarityRequestWithId(
      executionId: ServerlessRequestId,
      eventData: ComputePageSimilarityRequest
    ) extends ServerlessRequest {

      override val id: ServerlessRequestId = executionId
    }

    object ComputePageSimilarityRequestWithId {
      given Codec.AsObject[ComputePageSimilarityRequestWithId] = deriveCodecWithDefaults
    }

    final case class ComputePageSimilarityRequest(
      s3Access: S3Access,
      documentReferenceA: DocumentReference,
      documentReferenceB: DocumentReference,
      s3OutputPath: String,
      topK: Option[Int]
    )

    object ComputePageSimilarityRequest {
      given Codec.AsObject[ComputePageSimilarityRequest] = deriveCodecWithDefaults
    }

    final case class DocumentReference(
      textractJobId: String,
      textractProcessType: TextractProcessType
    )

    object DocumentReference {
      given Codec.AsObject[DocumentReference] = deriveCodecWithDefaults
    }

    final case class ComputePageSimilarityResult(
      pageSimilarityScores: List[List[(Int, Float)]],
      documentAJobId: String,
      documentBJobId: String
    )

    object ComputePageSimilarityResult {
      given Codec.AsObject[ComputePageSimilarityResult] = deriveCodecWithDefaults
    }

    final case class ComputePageSimilarityResponse(
      executionId: ServerlessRequestId,
      statusCode: Int,
      body: ComputePageSimilarityResult
    ) extends ServerlessResponse {

      override val id: ServerlessRequestId = executionId
      override def message: String = s"Got ${body.pageSimilarityScores.size} scores"
    }

    object ComputePageSimilarityResponse {
      given Codec.AsObject[ComputePageSimilarityResponse] = deriveCodecWithDefaults
    }

  }

  object signPdf {

    final case class SignPdfRequest(
      s3Access: S3Access,
      inputKey: String,
      outputKey: String,
      field_name: String,
      include_lta: Boolean,
      use_bundle_certs: Boolean
    )

    object SignPdfRequest {
      given Codec.AsObject[SignPdfRequest] = deriveCodecWithDefaults
    }

    final case class SignPdfRequestWithId(
      id: ServerlessRequestId,
      request: SignPdfRequest
    ) extends ServerlessRequest

    object SignPdfRequestWithId {
      given Codec.AsObject[SignPdfRequestWithId] = deriveCodecWithDefaults
    }

    final case class SignPdfResponse(
      id: ServerlessRequestId,
      message: String
    ) extends ServerlessResponse

    object SignPdfResponse {
      given Codec.AsObject[SignPdfResponse] = deriveCodecWithDefaults
    }

  }

  // https://docs.aws.amazon.com/lambda/latest/dg/nodejs-prog-mode-exceptions.html
  private[common] final case class AwsServerlessError(
    id: ServerlessRequestId,
    errorType: String,
    errorMessage: String,
    stackTrace: List[String]
  ) extends Throwable {

    private[common] object AwsServerlessError {
      given Codec.AsObject[AwsServerlessError] = deriveCodecWithDefaults
    }

    override def toString: String = {
      s"request $id: [$errorType}]: $errorMessage.\n Trace: ${stackTrace.mkString("\n")}"
    }

  }

  trait ServerlessError extends Exception {
    def message: String
    override def getMessage: String = s"Serverless request failed, message: $message"
  }

  final case class GeneralServerlessError(message: String) extends ServerlessError

  final case class ServerlessTimeoutError(timeout: FiniteDuration) extends ServerlessError {

    override def message: String =
      s"Request timeout after ${timeout.toMillis} milliseconds"

  }

  object loki {

    sealed trait LokiRequest

    object LokiRequest {
      given Codec.AsObject[LokiRequest] = deriveCodecWithDefaults

      final case class RunScriptRequest(
        formKey: String,
        formS3Access: S3Access,
        scriptContent: String,
        mode: ErrorReportMode
      ) extends LokiRequest

      object RunScriptRequest {
        given Codec.AsObject[RunScriptRequest] = deriveCodecWithDefaults
      }

      final case class GetGaiaVersionRequest() extends LokiRequest

      object GetGaiaVersionRequest {
        given Codec.AsObject[GetGaiaVersionRequest] = deriveCodecWithDefaults
      }

    }

    sealed trait LokiResponse

    object LokiResponse {
      given Codec.AsObject[LokiResponse] = deriveCodecWithDefaults

      final case class RunScriptResponse(
        result: Either[EngineCompileError, (GaiaState, Seq[RuntimeError], Seq[String])]
      ) extends LokiResponse

      object RunScriptResponse {
        given Codec.AsObject[RunScriptResponse] = deriveCodecWithDefaults
      }

      final case class GetGaiaVersionResponse(version: String) extends LokiResponse

      object GetGaiaVersionResponse {
        given Codec.AsObject[GetGaiaVersionResponse] = deriveCodecWithDefaults
      }

    }

    final case class LokiRequestWithId(
      id: ServerlessRequestId,
      request: LokiRequest
    ) extends ServerlessRequest

    object LokiRequestWithId {
      given Codec.AsObject[LokiRequestWithId] = deriveCodecWithDefaults
    }

    final case class LokiResponseWithId(
      id: ServerlessRequestId,
      response: LokiResponse,
      message: String
    ) extends ServerlessResponse

    object LokiResponseWithId {
      given Codec.AsObject[LokiResponseWithId] = deriveCodecWithDefaults
    }

  }

  object ffprobe {

    final case class GetMp4DurationRequest(
      s3Access: S3Access,
      storageId: String
    )

    object GetMp4DurationRequest {
      given Codec.AsObject[GetMp4DurationRequest] = deriveCodecWithDefaults
    }

    final case class GetMp4DurationRequestWithId(
      id: ServerlessRequestId,
      request: GetMp4DurationRequest
    ) extends ServerlessRequest

    object GetMp4DurationRequestWithId {
      given Codec.AsObject[GetMp4DurationRequestWithId] = deriveCodecWithDefaults
    }

    final case class GetMp4DurationResponse(
      id: ServerlessRequestId,
      message: String,
      duration: Int // seconds
    ) extends ServerlessResponse

    object GetMp4DurationResponse {
      given Codec.AsObject[GetMp4DurationResponse] = deriveCodecWithDefaults
    }

  }

  object docusign {

    case class IntegrationConfigs(
      integrationKey: String,
      rsaPrivateKey: String,
      apiUserId: String,
      apiAccountId: String,
      oAuthBasePath: String,
      apiBasePath: String,
      phoneAuthenticationConfigId: String,
      idVerificationWorkflowId: String
    )

    object IntegrationConfigs {
      given Codec.AsObject[IntegrationConfigs] = deriveCodecWithDefaults
    }

    sealed abstract class DocusignRequest(
      integrationConfigs: IntegrationConfigs // scalafix:ok
    )

    object DocusignRequest {

      given Codec.AsObject[DocusignRequest] = deriveCodecWithDefaults

      case class CreateEnvelopeRequest(
        files: Seq[FileInfoWithDocumentId],
        recipientsData: Seq[DocusignRecipientData],
        enabledQes: Boolean,
        webhookUrl: String,
        s3Access: S3Access,
        registeredWebhookEvents: Seq[String],
        integrationConfigs: IntegrationConfigs,
        brandId: String, // Default to use Anduin brand if brandId is an empty string
        metadata: Map[String, String],
        carbonCopyRecipientOpt: Option[CarbonCopyEnvelopeRecipient],
        createAsDraft: Boolean,
        enforceSignerVisibility: Boolean // https://www.docusign.com/blog/developers/the-trenches-deep-dive-document-visibility
      ) extends DocusignRequest(integrationConfigs)

      object CreateEnvelopeRequest {
        given Codec.AsObject[CreateEnvelopeRequest] = deriveCodecWithDefaults
      }

      final case class SendEnvelopeRequest(
        envelopeId: String,
        integrationConfigs: IntegrationConfigs
      ) extends DocusignRequest(integrationConfigs)

      object SendEnvelopeRequest {
        given Codec.AsObject[SendEnvelopeRequest] = deriveCodecWithDefaults
      }

      final case class UpdateEnvelopeDocumentsRequest(
        envelopeId: String,
        files: Seq[FileInfoWithDocumentId],
        recipientTabDataSeq: Seq[RecipientTabData],
        s3Access: S3Access,
        integrationConfigs: IntegrationConfigs
      ) extends DocusignRequest(integrationConfigs)

      object UpdateEnvelopeDocumentsRequest {
        given Codec.AsObject[UpdateEnvelopeDocumentsRequest] = deriveCodecWithDefaults
      }

      case class VoidEnvelopeRequest(
        envelopeId: String,
        integrationConfigs: IntegrationConfigs
      ) extends DocusignRequest(integrationConfigs)

      object VoidEnvelopeRequest {
        given Codec.AsObject[VoidEnvelopeRequest] = deriveCodecWithDefaults
      }

      case class GetEnvelopeDocumentRequest(
        envelopeId: String,
        docusignDocumentId: String,
        s3Access: S3Access,
        outputStorageId: String,
        integrationConfigs: IntegrationConfigs
      ) extends DocusignRequest(integrationConfigs)

      object GetEnvelopeDocumentRequest {
        given Codec.AsObject[GetEnvelopeDocumentRequest] = deriveCodecWithDefaults
      }

      case class GetEnvelopeDocumentsRequest(
        envelopeId: String,
        s3Access: S3Access,
        signedFilesInfo: Seq[UnzippedFileInfo],
        integrationConfigs: IntegrationConfigs
      ) extends DocusignRequest(integrationConfigs)

      object GetEnvelopeDocumentsRequest {
        given Codec.AsObject[GetEnvelopeDocumentsRequest] = deriveCodecWithDefaults
      }

      case class GetSigningUrlRequest(
        envelopeId: String,
        recipientIdentity: DocusignSignerIdentity,
        returnUrl: String,
        integrationConfigs: IntegrationConfigs,
        recipientAuthenticationMethod: String
      ) extends DocusignRequest(integrationConfigs)

      object GetSigningUrlRequest {
        given Codec.AsObject[GetSigningUrlRequest] = deriveCodecWithDefaults
      }

      case class ReassignRecipientRequest(
        envelopeId: String,
        newRecipient: DocusignSignerIdentity,
        integrationConfigs: IntegrationConfigs,
        resendEnvelope: Boolean = false
      ) extends DocusignRequest(integrationConfigs)

      object ReassignRecipientRequest {
        given Codec.AsObject[ReassignRecipientRequest] = deriveCodecWithDefaults
      }

      case class RemoveRecipientsRequest(
        envelopeId: String,
        recipientIds: Seq[String],
        integrationConfigs: IntegrationConfigs
      ) extends DocusignRequest(integrationConfigs)

      object RemoveRecipientsRequest {
        given Codec.AsObject[RemoveRecipientsRequest] = deriveCodecWithDefaults
      }

      case class ResendRecipientRequest(
        envelopeId: String,
        recipientId: String,
        recipientEmail: String,
        recipientName: String,
        integrationConfigs: IntegrationConfigs
      ) extends DocusignRequest(integrationConfigs)

      object ResendRecipientRequest {
        given Codec.AsObject[ResendRecipientRequest] = deriveCodecWithDefaults
      }

    }

    final case class UnzippedFileInfo(
      outputStorageId: String,
      fileName: String
    )

    object UnzippedFileInfo {
      given Codec.AsObject[UnzippedFileInfo] = deriveCodecWithDefaults
    }

    final case class FileInfoWithDocumentId(
      documentStorageId: String,
      fileName: String,
      docusignDocumentId: String,
      prefilledFields: Seq[PrefilledField]
    )

    object FileInfoWithDocumentId {
      given Codec.AsObject[FileInfoWithDocumentId] = deriveCodecWithDefaults
    }

    final case class PrefilledField(
      location: SignatureFieldLocation,
      text: String
    )

    object PrefilledField {
      given Codec.AsObject[PrefilledField] = deriveCodecWithDefaults
    }

    sealed trait DocusignRecipientAuthServerlessData derives CanEqual

    object DocusignRecipientAuthServerlessData {
      given Codec.AsObject[DocusignRecipientAuthServerlessData] = deriveCodecWithDefaults

      case class AccessCode(code: String) extends DocusignRecipientAuthServerlessData

      object AccessCode {
        given Codec.AsObject[AccessCode] = deriveCodecWithDefaults
      }

      case class PhoneOtp(
        dialCode: String,
        phoneNumber: String
      ) extends DocusignRecipientAuthServerlessData

      object PhoneOtp {
        given Codec.AsObject[PhoneOtp] = deriveCodecWithDefaults
      }

      case object IdVerification extends DocusignRecipientAuthServerlessData {
        given Codec.AsObject[IdVerification.type] = deriveCodecWithDefaults
      }

    }

    case class DocusignSignerIdentity(
      fullName: String,
      emailAddress: String,
      userId: String,
      recipientId: String,
      docusignRecipientAuthDataOpt: Option[DocusignRecipientAuthServerlessData]
    )

    object DocusignSignerIdentity {
      given Codec.AsObject[DocusignSignerIdentity] = deriveCodecWithDefaults
    }

    case class GeneralDocusignApiException(message: String) extends Exception {
      override def getMessage: String = message
    }

    object GeneralDocusignApiException {
      given Codec.AsObject[GeneralDocusignApiException] = deriveCodecWithDefaults
    }

    sealed trait DocusignSuccessfulResponse derives CanEqual

    object DocusignSuccessfulResponse {

      given Codec.AsObject[DocusignSuccessfulResponse] = deriveCodecWithDefaults

      case class CreateEnvelopeResponse(envelopeId: String) extends DocusignSuccessfulResponse

      object CreateEnvelopeResponse {
        given Codec.AsObject[CreateEnvelopeResponse] = deriveCodecWithDefaults
      }

      case class SendEnvelopeResponse() extends DocusignSuccessfulResponse

      object SendEnvelopeResponse {
        given Codec.AsObject[SendEnvelopeResponse] = deriveCodecWithDefaults
      }

      case class UpdateEnvelopeDocumentsResponse() extends DocusignSuccessfulResponse

      object UpdateEnvelopeDocumentsResponse {
        given Codec.AsObject[UpdateEnvelopeDocumentsResponse] = deriveCodecWithDefaults
      }

      case class VoidEnvelopeResponse() extends DocusignSuccessfulResponse

      object VoidEnvelopeResponse {
        given Codec.AsObject[VoidEnvelopeResponse] = deriveCodecWithDefaults
      }

      case class GetEnvelopeDocumentResponse() extends DocusignSuccessfulResponse

      object GetEnvelopeDocumentResponse {
        given Codec.AsObject[GetEnvelopeDocumentResponse] = deriveCodecWithDefaults
      }

      case class GetEnvelopeDocumentsResponse(filesInfo: Seq[UnzippedFileInfo]) extends DocusignSuccessfulResponse

      object GetEnvelopeDocumentsResponse {
        given Codec.AsObject[GetEnvelopeDocumentsResponse] = deriveCodecWithDefaults
      }

      case class GetSigningUrlResponse(signingUrl: String) extends DocusignSuccessfulResponse

      object GetSigningUrlResponse {
        given Codec.AsObject[GetSigningUrlResponse] = deriveCodecWithDefaults
      }

      case class ReassignRecipientResponse() extends DocusignSuccessfulResponse

      object ReassignRecipientResponse {
        given Codec.AsObject[ReassignRecipientResponse] = deriveCodecWithDefaults
      }

      case class RemoveRecipientsResponse() extends DocusignSuccessfulResponse

      object RemoveRecipientsResponse {
        given Codec.AsObject[RemoveRecipientsResponse] = deriveCodecWithDefaults
      }

      case class ResendRecipientResponse() extends DocusignSuccessfulResponse

      object ResendRecipientResponse {
        given Codec.AsObject[ResendRecipientResponse] = deriveCodecWithDefaults
      }

    }

    final case class DocusignRequestWithId(
      id: ServerlessRequestId,
      request: DocusignRequest
    ) extends ServerlessRequest

    object DocusignRequestWithId {
      given Codec.AsObject[DocusignRequestWithId] = deriveCodecWithDefaults
    }

    final case class DocusignResponseWithId(
      id: ServerlessRequestId,
      response: Either[GeneralDocusignApiException, DocusignSuccessfulResponse],
      message: String
    ) extends ServerlessResponse

    object DocusignResponseWithId {
      given Codec.AsObject[DocusignResponseWithId] = deriveCodecWithDefaults
    }

    final case class DocusignRecipientData(
      identity: DocusignSignerIdentity,
      preparedDocuments: Seq[PreparedDocuments],
      excludedDocumentsOpt: Option[Seq[String]] // docusign document ID, documents that signer cannot access
    )

    object DocusignRecipientData {
      given Codec.AsObject[DocusignRecipientData] = deriveCodecWithDefaults
    }

    final case class RecipientTabData(
      recipientId: String,
      preparedDocuments: Seq[PreparedDocuments]
    )

    object RecipientTabData {
      given Codec.AsObject[RecipientTabData] = deriveCodecWithDefaults
    }

    final case class PreparedDocuments(
      docusignDocumentId: String,
      preparedFields: Seq[PrepFieldModel]
    )

    object PreparedDocuments {
      given Codec.AsObject[PreparedDocuments] = deriveCodecWithDefaults
    }

    final case class ValidationPattern(
      validationMessage: String, // Customize error message when user's input does not pass the validation pattern
      validationPattern: String // Regex to validate input from users (used in custom date format)
    )

    object ValidationPattern {
      given Codec.AsObject[ValidationPattern] = deriveCodecWithDefaults
    }

    final case class PrepFieldModel(
      fieldType: SignatureFieldType,
      location: SignatureFieldLocation,
      tooltip: Option[String],
      radioData: Seq[RadioData],
      isOptional: Boolean,
      validationPatternOpt: Option[ValidationPattern]
    )

    object PrepFieldModel {
      given Codec.AsObject[PrepFieldModel] = deriveCodecWithDefaults
    }

    final case class RadioData(
      location: SignatureFieldLocation
    )

    object RadioData {
      given Codec.AsObject[RadioData] = deriveCodecWithDefaults
    }

    sealed trait SignatureFieldType derives CanEqual

    object SignatureFieldType {

      given Codec.AsObject[SignatureFieldType] = deriveCodecWithDefaults
      case object Signature extends SignatureFieldType

      case object Name extends SignatureFieldType

      case object Title extends SignatureFieldType

      case object Date extends SignatureFieldType

      case object Company extends SignatureFieldType

      case object Address extends SignatureFieldType

      case object Phone extends SignatureFieldType

      case object Custom extends SignatureFieldType

      case object Checkbox extends SignatureFieldType

      case object Radio extends SignatureFieldType

      given Codec.AsObject[Signature.type] = deriveCodecWithDefaults
      given Codec.AsObject[Name.type] = deriveCodecWithDefaults
      given Codec.AsObject[Title.type] = deriveCodecWithDefaults
      given Codec.AsObject[Date.type] = deriveCodecWithDefaults
      given Codec.AsObject[Company.type] = deriveCodecWithDefaults
      given Codec.AsObject[Address.type] = deriveCodecWithDefaults
      given Codec.AsObject[Phone.type] = deriveCodecWithDefaults
      given Codec.AsObject[Custom.type] = deriveCodecWithDefaults
      given Codec.AsObject[Checkbox.type] = deriveCodecWithDefaults
      given Codec.AsObject[Radio.type] = deriveCodecWithDefaults

    }

    final case class SignatureFieldLocation(
      pageIndex: Int,
      xPos: Float,
      yPos: Float,
      width: Float,
      height: Float
    )

    object SignatureFieldLocation {
      given Codec.AsObject[SignatureFieldLocation] = deriveCodecWithDefaults
    }

    case class CarbonCopyEnvelopeRecipient(
      name: String,
      email: String,
      recipientId: String,
      excludedDocumentsOpt: Seq[String] // docusign document ID
    )

    object CarbonCopyEnvelopeRecipient {
      given Codec.AsObject[CarbonCopyEnvelopeRecipient] = deriveCodec
    }

  }

  object textract {

    sealed trait TextractUtilityRequest derives CanEqual

    object TextractUtilityRequest {
      given Codec.AsObject[TextractUtilityRequest] = deriveCodecWithDefaults
    }

    sealed trait TextractUtilityResponse derives CanEqual

    object TextractUtilityResponse {
      given Codec.AsObject[TextractUtilityResponse] = deriveCodecWithDefaults
    }

    // Process block start
    final case class ProcessBlockPageRequest(
      textractJobId: String,
      processType: TextractProcessType,
      s3Access: S3Access,
      s3Prefix: String
    ) extends TextractUtilityRequest

    object ProcessBlockPageRequest {
      given Codec.AsObject[ProcessBlockPageRequest] = deriveCodecWithDefaults
    }

    final case class ProcessBlockPageResponse(
      pageCount: Int,
      pageMetadata: Map[Int, (Int, Int)]
    ) extends TextractUtilityResponse

    object ProcessBlockPageResponse {
      given Codec.AsObject[ProcessBlockPageResponse] = deriveCodecWithDefaults
    }
    // Process block end

    // Preprocess PdfTool's Textract block start
    final case class ProcessTextractBlockRequest(
      textractJobId: String,
      s3Access: S3Access,
      s3Prefix: String,
      pageRange: (Int, Int),
      pageMetadata: Map[Int, (Int, Int)]
    ) extends TextractUtilityRequest

    object ProcessTextractBlockRequest {
      given Codec.AsObject[ProcessTextractBlockRequest] = deriveCodecWithDefaults
    }

    final case class ProcessTextractBlockResponse(
      blocks: List[TextractAnnotationBlock]
    ) extends TextractUtilityResponse

    object ProcessTextractBlockResponse {
      given Codec.AsObject[ProcessTextractBlockResponse] = deriveCodecWithDefaults
    }

    // Preprocess PdfTool's Textract block end

    final case class GetBestPageMatchingRequest(
      textractS3Access: S3Access,
      dataExtractS3Access: S3Access,
      textractS3Prefix: String,
      dataExtractS3Prefix: String,
      dataExtractCacheVersion: String,
      userDocuments: Seq[DataExtractFile],
      templates: Seq[DataExtractFile]
    ) extends TextractUtilityRequest

    object GetBestPageMatchingRequest {
      given Codec.AsObject[GetBestPageMatchingRequest] = deriveCodecWithDefaults
    }

    final case class GetBestPageMatchingResponse(
      bestPageMatchingResults: Seq[PageMatchingResult]
    ) extends TextractUtilityResponse

    object GetBestPageMatchingResponse {
      given Codec.AsObject[GetBestPageMatchingResponse] = deriveCodecWithDefaults
    }

    final case class GetMappingContentRequest(
      textractS3Access: S3Access,
      dataExtractS3Access: S3Access,
      textractS3Prefix: String,
      dataExtractS3Prefix: String,
      dataExtractCacheVersion: String,
      userDocuments: Seq[DataExtractFile],
      templates: Seq[DataExtractFile],
      pageMatches: Seq[PageMatch],
      algorithmVersion: MappingContentAlgorithmVersion
    ) extends TextractUtilityRequest

    object GetMappingContentRequest {
      given Codec.AsObject[GetMappingContentRequest] = deriveCodecWithDefaults
    }

    final case class GetMappingContentResponse(
      mappingContent: Seq[OverlappedContent],
      transformedTemplateFieldAreas: Seq[(PageMatch, String, Area2D)]
    ) extends TextractUtilityResponse

    object GetMappingContentResponse {
      given Codec.AsObject[GetMappingContentResponse] = deriveCodecWithDefaults
    }

    final case class DataExtractFile(
      fileId: String,
      mergeTextractData: MergeTextractData,
      pageSizeMap: Map[Int, muPdf.PageSize] = Map.empty
    )

    object DataExtractFile {
      given Codec.AsObject[DataExtractFile] = deriveCodecWithDefaults
    }

    final case class MergeTextractData(
      baseInfoOpt: Option[TextractFileInfo],
      items: Seq[MergeTextractItem],
      originalPageToMergeTextractItemIndex: Map[Int, Int]
    )

    object MergeTextractData {
      given Codec.AsObject[MergeTextractData] = deriveCodecWithDefaults
    }

    final case class TextractFileInfo(
      processType: TextractProcessType,
      jobId: String
    )

    object TextractFileInfo {
      given Codec.AsObject[TextractFileInfo] = deriveCodecWithDefaults
    }

    final case class MergeTextractItem(
      info: TextractFileInfo,
      cutPages: Seq[Int]
    )

    object MergeTextractItem {
      given Codec[MergeTextractItem] = deriveCodecWithDefaults
    }

    final case class TextractUtilityRequestWithId(
      id: ServerlessRequestId,
      textractUtilityRequest: TextractUtilityRequest
    ) extends ServerlessRequest

    object TextractUtilityRequestWithId {
      given Codec.AsObject[TextractUtilityRequestWithId] = deriveCodecWithDefaults
    }

    final case class TextractUtilityResponseWithId(
      id: ServerlessRequestId,
      textractUtilityResponse: Either[TextractUtilityGeneralException, TextractUtilityResponse],
      message: String
    ) extends ServerlessResponse

    object TextractUtilityResponseWithId {
      given Codec.AsObject[TextractUtilityResponseWithId] = deriveCodecWithDefaults
    }

    case class TextractUtilityGeneralException(message: String) extends Exception

    object TextractUtilityGeneralException {
      given Codec.AsObject[TextractUtilityGeneralException] = deriveCodecWithDefaults
    }

  }

  object watermark {

    case class WatermarkGeneratorRequest(
      inputStorageId: String,
      outputStorageId: String,
      watermarkText: String,
      color: Int, // rgb
      alpha: Float,
      watermarkLayout: String,
      s3Access: S3Access
    )

    object WatermarkGeneratorRequest {
      given Codec.AsObject[WatermarkGeneratorRequest] = deriveCodec
    }

    case class WatermarkGeneratorSuccessfulResponse(
      isPasswordProtectedFile: Boolean
    )

    object WatermarkGeneratorSuccessfulResponse {
      given Codec.AsObject[WatermarkGeneratorSuccessfulResponse] = deriveCodec
    }

    final case class WatermarkGeneratorServiceException(message: String)

    object WatermarkGeneratorServiceException {
      given Codec.AsObject[WatermarkGeneratorServiceException] = deriveCodec
    }

    final case class WatermarkGeneratorRequestWithId(
      id: ServerlessRequestId,
      request: WatermarkGeneratorRequest
    ) extends ServerlessRequest

    object WatermarkGeneratorRequestWithId {
      given Codec.AsObject[WatermarkGeneratorRequestWithId] = deriveCodec
    }

    final case class WatermarkGeneratorResponseWithId(
      id: ServerlessRequestId,
      responseEither: Either[WatermarkGeneratorServiceException, WatermarkGeneratorSuccessfulResponse],
      message: String
    ) extends ServerlessResponse

    object WatermarkGeneratorResponseWithId {
      given Codec.AsObject[WatermarkGeneratorResponseWithId] = deriveCodec
    }

  }

  case class Sha256ComputeRequest(
    storageId: String,
    s3Access: S3Access
  )

  object Sha256ComputeRequest {
    given Codec.AsObject[Sha256ComputeRequest] = deriveCodec
  }

  case class ExportCsvFilesToExcelRequest(
    exportItems: List[SpreadsheetExportItem],
    outputStorageId: String,
    s3Access: S3Access
  )

  object ExportCsvFilesToExcelRequest {
    given Codec.AsObject[ExportCsvFilesToExcelRequest] = deriveCodec
  }

  case class SpreadsheetExportItem(
    headers: Either[List[List[String]], ExportTemplate],
    rawDataStorageIds: List[String],
    sheetName: String
  )

  object SpreadsheetExportItem {
    given Codec.AsObject[SpreadsheetExportItem] = deriveCodec
  }

  case class ExportTemplate(
    storageId: String,
    startRow: Int = 0,
    startCol: Int = 0
  )

  object ExportTemplate {
    given Codec.AsObject[ExportTemplate] = deriveCodec
  }

  case class ExportCsvFilesToExcelRequestWithId(
    id: ServerlessRequestId,
    request: ExportCsvFilesToExcelRequest
  ) extends ServerlessRequest

  object ExportCsvFilesToExcelRequestWithId {
    given Codec.AsObject[ExportCsvFilesToExcelRequestWithId] = deriveCodec
  }

  case class ExportCsvFilesToExcelRespWithId(
    id: ServerlessRequestId,
    message: String
  ) extends ServerlessResponse

  object ExportCsvFilesToExcelRespWithId {
    given Codec.AsObject[ExportCsvFilesToExcelRespWithId] = deriveCodec
  }

  case class Sha256ComputeRequestWithId(
    id: ServerlessRequestId,
    request: Sha256ComputeRequest
  ) extends ServerlessRequest

  object Sha256ComputeRequestWithId {
    given Codec.AsObject[Sha256ComputeRequestWithId] = deriveCodec
  }

  case class Sha256ComputeResponseWithId(
    id: ServerlessRequestId,
    checksumSha256: String
  ) extends ServerlessResponse {
    override def message: String = checksumSha256
  }

  object Sha256ComputeResponseWithId {
    given Codec.AsObject[Sha256ComputeResponseWithId] = deriveCodec
  }

  object fillpdf {

    enum FlattenOption {
      case FlattenAllFields, FlattenFilledFields, NoFlatten
    }

    object FlattenOption {
      given Codec[FlattenOption] = deriveEnumCodec
    }

    final case class InsertHeaderFooterParams(
      text: String,
      placement: HeaderFooterPlacement
    )

    object InsertHeaderFooterParams {
      given Codec.AsObject[InsertHeaderFooterParams] = deriveCodec
    }

    enum HeaderFooterPlacement {
      case TopLeft, TopRight, BottomLeft, BottomRight
    }

    object HeaderFooterPlacement {
      given Codec[HeaderFooterPlacement] = deriveEnumCodec
    }

    sealed trait FillPdfException derives CanEqual {
      def getMessage: String
    }

    object FillPdfException {

      case class NoValidFontException(fields: Seq[InvalidFieldInfo]) extends FillPdfException {
        override def getMessage: String = toString
      }

      case class ServiceException(message: String) extends FillPdfException {
        override def getMessage: String = message
      }

      given Codec.AsObject[FillPdfException] = deriveCodec

      given Codec.AsObject[NoValidFontException] = deriveCodec

      given Codec.AsObject[ServiceException] = deriveCodec
    }

    case class InvalidFieldInfo(
      fieldName: String,
      value: String
    )

    object InvalidFieldInfo {
      given Codec.AsObject[InvalidFieldInfo] = deriveCodec
    }

    sealed trait PdfFormUtilsRequest derives CanEqual

    sealed trait PdfFormUtilsResponse derives CanEqual

    object PdfFormUtilsRequest {

      given Codec.AsObject[PdfFormUtilsRequest] = deriveCodec

      case class FillPdfRequest(
        documentStorageId: String,
        fields: Map[String, String],
        flattenOption: FlattenOption,
        fontColorOpt: Option[String],
        outputDocumentStorageId: String,
        s3Access: S3Access,
        insertHeaderFooterParamOpt: Option[InsertHeaderFooterParams]
      ) extends PdfFormUtilsRequest

      object FillPdfRequest {
        given Codec.AsObject[FillPdfRequest] = deriveCodec
      }

      case class ExtractPdfFieldValuesRequest(
        documentStorageId: String,
        s3Access: S3Access
      ) extends PdfFormUtilsRequest

      object ExtractPdfFieldValuesRequest {
        given Codec.AsObject[ExtractPdfFieldValuesRequest] = deriveCodec
      }

    }

    object PdfFormUtilsResponse {

      given Codec.AsObject[PdfFormUtilsResponse] = deriveCodec

      case class FillPdfResponse(
        errorOpt: Option[FillPdfException]
      ) extends PdfFormUtilsResponse

      object FillPdfResponse {
        given Codec.AsObject[FillPdfResponse] = deriveCodec
      }

      case class ExtractPdfFieldValuesResponse(
        fields: List[(String, String)]
      ) extends PdfFormUtilsResponse

      object ExtractPdfFieldValuesResponse {
        given Codec.AsObject[ExtractPdfFieldValuesResponse] = deriveCodec
      }

    }

    case class PdfFormUtilsRequestWithId(
      id: ServerlessRequestId,
      request: PdfFormUtilsRequest
    ) extends ServerlessRequest

    object PdfFormUtilsRequestWithId {
      given Codec.AsObject[PdfFormUtilsRequestWithId] = deriveCodec
    }

    case class PdfFormUtilsResponseWithId(
      id: ServerlessRequestId,
      response: PdfFormUtilsResponse,
      message: String
    ) extends ServerlessResponse

    object PdfFormUtilsResponseWithId {
      given Codec.AsObject[PdfFormUtilsResponseWithId] = deriveCodec
    }

  }

  object formdata {

    case class FormDataUtilsRequestWithId(
      id: ServerlessRequestId,
      request: FormDataUtilsRequest
    ) extends ServerlessRequest

    object FormDataUtilsRequestWithId {
      given Codec.AsObject[FormDataUtilsRequestWithId] = deriveCodecWithDefaults
    }

    case class FormDataUtilsResponseWithId(
      id: ServerlessRequestId,
      response: FormDataUtilsResponse,
      message: String
    ) extends ServerlessResponse

    object FormDataUtilsResponseWithId {
      given Codec.AsObject[FormDataUtilsResponseWithId] = deriveCodecWithDefaults
    }

    final case class UpdateGaiaFormOptions(
      gaiaFormUpdateType: GaiaEngineData.UpdateFormDataType = GaiaEngineData.UpdateFormDataType.default,
      skippedEvents: Seq[Int] = Seq.empty // For replay event type
    )

    object UpdateGaiaFormOptions {
      given Codec.AsObject[UpdateGaiaFormOptions] = deriveCodecWithDefaults
    }

    sealed trait FormDataUtilsRequest derives CanEqual

    object FormDataUtilsRequest {
      given Codec.AsObject[FormDataUtilsRequest] = deriveCodecWithDefaults

      case class CompareGaiaFormPdfFieldRequest(
        currentFormState: GaiaState,
        currentFormKey: String,
        newFormKey: String,
        updateGaiaFormOptions: UpdateGaiaFormOptions,
        s3Access: S3Access,
        areFormFieldsExposed: Boolean // Form fields should be hidden in production funds
      ) extends FormDataUtilsRequest

      object CompareGaiaFormPdfFieldRequest {
        given Codec.AsObject[CompareGaiaFormPdfFieldRequest] = deriveCodecWithDefaults
      }

      case class UpdateGaiaFormStateRequest(
        currentFormState: GaiaState,
        newFormKey: String,
        updateGaiaFormOptions: UpdateGaiaFormOptions,
        s3Access: S3Access
      ) extends FormDataUtilsRequest

      object UpdateGaiaFormStateRequest {
        given Codec.AsObject[UpdateGaiaFormStateRequest] = deriveCodecWithDefaults
      }

      case class ReplayEventRequest(
        initialStateOpt: Option[GaiaState],
        formKey: String,
        s3Access: S3Access,
        events: Seq[Patches]
      ) extends FormDataUtilsRequest

      object ReplayEventRequest {
        given Codec.AsObject[ReplayEventRequest] = deriveCodecWithDefaults
      }

      case class ImportDataRequest(
        data: Seq[(FormNamespace, Map[String, Json])], // namespace -> jsonMap
        initialStateOpt: Option[GaiaState] = None,
        s3Access: S3Access,
        formKey: String
      ) extends FormDataUtilsRequest

      object ImportDataRequest {
        given Codec.AsObject[ImportDataRequest] = deriveCodecWithDefaults
      }

      case class AutoFillDataRequest(
        data: Seq[(FormNamespace, Map[String, Json])], // namespace -> jsonMap
        initialStateOpt: Option[GaiaState] = None,
        s3Access: S3Access,
        formKey: String
      ) extends FormDataUtilsRequest

      object AutoFillDataRequest {
        given Codec.AsObject[AutoFillDataRequest] = deriveCodecWithDefaults
      }

      case class ComputeInputSourceRequest(
        gaiaState: GaiaState,
        formKey: String,
        s3Access: S3Access
      ) extends FormDataUtilsRequest

      object ComputeInputSourceRequest {
        given Codec.AsObject[ComputeInputSourceRequest] = deriveCodecWithDefaults
      }

      final case class GetGaiaVersionRequest() extends FormDataUtilsRequest

      object GetGaiaVersionRequest {
        given Codec.AsObject[GetGaiaVersionRequest] = deriveCodecWithDefaults
      }

    }

    sealed trait CompareFormPdfFieldValue derives CanEqual

    object CompareFormPdfFieldValue {

      case object NotExisted extends CompareFormPdfFieldValue

      case object EmptyValue extends CompareFormPdfFieldValue

      case object NonEmptyValue extends CompareFormPdfFieldValue

      case object DifferentNonEmptyValue extends CompareFormPdfFieldValue

      case object SameNonEmptyValue extends CompareFormPdfFieldValue

      case class ExposedValue(valueOpt: Option[String]) extends CompareFormPdfFieldValue

      given Codec.AsObject[CompareFormPdfFieldValue] = deriveCodecWithDefaults
      given Codec.AsObject[NotExisted.type] = deriveCodecWithDefaults
      given Codec.AsObject[EmptyValue.type] = deriveCodecWithDefaults
      given Codec.AsObject[NonEmptyValue.type] = deriveCodecWithDefaults
      given Codec.AsObject[DifferentNonEmptyValue.type] = deriveCodecWithDefaults
      given Codec.AsObject[SameNonEmptyValue.type] = deriveCodecWithDefaults
      given Codec.AsObject[ExposedValue] = deriveCodecWithDefaults

    }

    case class ComparePdfFieldResult(
      fieldName: String,
      current: CompareFormPdfFieldValue,
      latest: CompareFormPdfFieldValue
    ) extends FormDataUtilsResponse

    object ComparePdfFieldResult {
      given Codec.AsObject[ComparePdfFieldResult] = deriveCodecWithDefaults
    }

    sealed trait FormDataUtilsResponse derives CanEqual

    object FormDataUtilsResponse {
      given Codec.AsObject[FormDataUtilsResponse] = deriveCodecWithDefaults

      case class CompareGaiaFormPdfFieldResponse(
        comparePdfFieldsResult: Seq[ComparePdfFieldResult]
      ) extends FormDataUtilsResponse

      object CompareGaiaFormPdfFieldResponse {
        given Codec.AsObject[CompareGaiaFormPdfFieldResponse] = deriveCodecWithDefaults
      }

      case class UpdateGaiaFormStateResponse(
        updatedState: GaiaState,
        updateErrorCount: Int,
        exceptionOpt: Option[String] = None
      ) extends FormDataUtilsResponse

      object UpdateGaiaFormStateResponse {
        given Codec.AsObject[UpdateGaiaFormStateResponse] = deriveCodecWithDefaults
      }

      case class ReplayEventResponse(
        resultEither: Either[String, (GaiaState, Seq[Either[GaiaEngineError, Patches]])]
      ) extends FormDataUtilsResponse

      object ReplayEventResponse {
        given Codec.AsObject[ReplayEventResponse] = deriveCodecWithDefaults
      }

      case class ImportDataResponse(
        resultEither: Either[String, ImportDataResult]
      ) extends FormDataUtilsResponse

      object ImportDataResponse {
        given Codec.AsObject[ImportDataResponse] = deriveCodecWithDefaults
      }

      case class AutoFillDataResponse(
        resultEither: Either[String, ImportDataResult]
      ) extends FormDataUtilsResponse

      object AutoFillDataResponse {
        given Codec.AsObject[AutoFillDataResponse] = deriveCodecWithDefaults
      }

      final case class ComputeInputSourceResponse(
        total: Int,
        imported: Int,
        autoFilled: Int,
        manual: Int,
        logic: Int
      ) extends FormDataUtilsResponse

      object ComputeInputSourceResponse {
        given Codec.AsObject[ComputeInputSourceResponse] = deriveCodecWithDefaults
      }

      final case class GetGaiaVersionResponse(version: String) extends FormDataUtilsResponse

      object GetGaiaVersionResponse {
        given Codec.AsObject[GetGaiaVersionResponse] = deriveCodecWithDefaults
      }

    }

  }

  object formmatching {

    case class FormMatchingUtilsRequestWithId(
      id: ServerlessRequestId,
      request: FormMatchingUtilsRequest
    ) extends ServerlessRequest

    object FormMatchingUtilsRequestWithId {
      given Codec.AsObject[FormMatchingUtilsRequestWithId] = deriveCodecWithDefaults
    }

    case class FormMatchingUtilsResponseWithId(
      id: ServerlessRequestId,
      response: FormMatchingUtilsResponse,
      message: String
    ) extends ServerlessResponse

    object FormMatchingUtilsResponseWithId {
      given Codec.AsObject[FormMatchingUtilsResponseWithId] = deriveCodecWithDefaults
    }

    sealed trait FormMatchingUtilsRequest derives CanEqual

    object FormMatchingUtilsRequest {
      given Codec.AsObject[FormMatchingUtilsRequest] = deriveCodecWithDefaults

      case class ComputeFormMatchingRequest(
        s3Access: S3Access,
        srcFormKey: String,
        destFormKey: String,
        srcFormAsaMapping: Map[String, String],
        destFormAsaMapping: Map[String, String],
        matchingMode: FormMatchingMode
      ) extends FormMatchingUtilsRequest

      object ComputeFormMatchingRequest {
        given Codec.AsObject[ComputeFormMatchingRequest] = deriveCodecWithDefaults
      }

      final case class GetGaiaVersionRequest() extends FormMatchingUtilsRequest

      object GetGaiaVersionRequest {
        given Codec.AsObject[GetGaiaVersionRequest] = deriveCodecWithDefaults
      }

    }

    sealed trait FormMatchingUtilsResponse derives CanEqual

    object FormMatchingUtilsResponse {
      given Codec.AsObject[FormMatchingUtilsResponse] = deriveCodecWithDefaults

      case class ComputeFormMatchingResponse(
        reusabilityScore: Double = 0.0,
        matchingScore: Double = 0.0,
        matchingAliasMap: Map[String, Set[(String, Double)]] = Map.empty,
        matchingValueMap: Map[String, Set[String]] = Map.empty
      ) extends FormMatchingUtilsResponse

      object ComputeFormMatchingResponse {
        given Codec.AsObject[ComputeFormMatchingResponse] = deriveCodecWithDefaults
      }

      final case class GetGaiaVersionResponse(version: String) extends FormMatchingUtilsResponse

      object GetGaiaVersionResponse {
        given Codec.AsObject[GetGaiaVersionResponse] = deriveCodecWithDefaults
      }

    }

  }

  object catala {
    sealed trait CatalaRequest extends ServerlessRequest

    object CatalaRequest {
      given Codec.AsObject[CatalaRequest] = deriveCodecWithDefaults
    }

    case class CatalaCommandRequest(catalaProgram: String, commands: List[String])

    object CatalaCommandRequest {
      given Codec.AsObject[CatalaCommandRequest] = deriveCodecWithDefaults
    }

    case class CatalaCommandRequestWithId(request: CatalaCommandRequest, id: ServerlessRequestId) extends CatalaRequest

    object CatalaCommandRequestWithId {
      given Codec.AsObject[CatalaCommandRequestWithId] = deriveCodecWithDefaults
    }

    sealed trait CatalaResponse extends ServerlessResponse

    object CatalaResponse {
      given Codec.AsObject[CatalaResponse] = deriveCodecWithDefaults
    }

    case class CatalaCommandResponse(
      exitCode: Int,
      out: String,
      error: String
    )

    object CatalaCommandResponse {
      given Codec.AsObject[CatalaCommandResponse] = deriveCodecWithDefaults
    }

    case class CatalaCommandResponseWithId(id: ServerlessRequestId, response: CatalaCommandResponse, message: String)
        extends CatalaResponse

    object CatalaCommandResponseWithId {
      given Codec.AsObject[CatalaCommandResponseWithId] = deriveCodecWithDefaults
    }

  }

  object pdfAnnotationRunner {

    // Requests

    sealed trait PdfAnnotationRequest

    object PdfAnnotationRequest {
      given Codec.AsObject[PdfAnnotationRequest] = deriveCodecWithDefaults
    }

    final case class PdfExtractAnnotationRequest(
      fileName: String,
      inputDocumentStorageId: String,
      inputDocumentS3Access: S3Access
    ) extends PdfAnnotationRequest

    object PdfExtractAnnotationRequest {
      given Codec.AsObject[PdfExtractAnnotationRequest] = deriveCodecWithDefaults
    }

    final case class PdfGenerateDocumentRequest(
      inputDocumentStorageId: String,
      inputDocumentS3Access: S3Access,
      inputAnnotationDataStr: String
    ) extends PdfAnnotationRequest

    object PdfGenerateDocumentRequest {
      given Codec.AsObject[PdfGenerateDocumentRequest] = deriveCodecWithDefaults
    }

    // Responses

    sealed trait PdfAnnotationResponse

    object PdfAnnotationResponse {
      given Codec.AsObject[PdfAnnotationResponse] = deriveCodecWithDefaults
    }

    final case class PdfExtractAnnotationResponse(
      outputDocumentStorageId: String,
      outputAnnotationDataStr: String
    ) extends PdfAnnotationResponse

    object PdfExtractAnnotationResponse {
      given Codec.AsObject[PdfExtractAnnotationResponse] = deriveCodecWithDefaults
    }

    final case class PdfGenerateDocumentResponse(
      outputDocumentStorageId: String
    ) extends PdfAnnotationResponse

    object PdfGenerateDocumentResponse {
      given Codec.AsObject[PdfGenerateDocumentResponse] = deriveCodecWithDefaults
    }

    // Generals

    final case class PdfAnnotationRequestWithId(
      id: ServerlessRequestId,
      request: PdfAnnotationRequest
    ) extends ServerlessRequest

    object PdfAnnotationRequestWithId {
      given Codec.AsObject[PdfAnnotationRequestWithId] = deriveCodecWithDefaults
    }

    final case class PdfAnnotationResponseWithId(
      id: ServerlessRequestId,
      response: PdfAnnotationResponse,
      message: String
    ) extends ServerlessResponse

    object PdfAnnotationResponseWithId {
      given Codec.AsObject[PdfAnnotationResponseWithId] = deriveCodecWithDefaults
    }

  }

  object formModule {
    // COMMON CLASS ------------------------------------------------------------
    sealed trait FormModuleRequest extends ServerlessRequest

    object FormModuleRequest {
      given Codec.AsObject[FormModuleRequest] = deriveCodecWithDefaults
    }

    sealed trait FormModuleResponse extends ServerlessResponse

    object FormModuleResponse {
      given Codec.AsObject[FormModuleResponse] = deriveCodecWithDefaults
    }

    final case class GeneralFormModuleResponse(
      message: String,
      description: String = ""
    )

    object GeneralFormModuleResponse {
      given Codec.AsObject[GeneralFormModuleResponse] = deriveCodecWithDefaults
    }

    final case class GeneralFormModuleResponseWithId(
      id: ServerlessRequestId,
      response: GeneralFormModuleResponse
    ) extends FormModuleResponse {
      override def message: String = response.message
    }

    object GeneralFormModuleResponseWithId {
      given Codec.AsObject[GeneralFormModuleResponseWithId] = deriveCodecWithDefaults
    }

    final case class FormModuleException(
      codeOpt: Option[Int],
      message: String
    )

    object FormModuleException {
      given Codec.AsObject[FormModuleException] = deriveCodecWithDefaults
    }

    // CREATE CLASS ------------------------------------------------------------
    final case class CreateModuleRequest(
      registry: Registry,
      module: ModuleInfo,
      languageVersion: String,
      metadata: Map[String, String] = Map.empty
    )

    object CreateModuleRequest {
      given Codec.AsObject[CreateModuleRequest] = deriveCodecWithDefaults
    }

    final case class CreateModuleRequestWithId(request: CreateModuleRequest, id: ServerlessRequestId)
        extends FormModuleRequest

    object CreateModuleRequestWithId {
      given Codec.AsObject[CreateModuleRequestWithId] = deriveCodecWithDefaults
    }

    final case class CreateModuleResponse(id: ServerlessRequestId, errorOpt: Option[FormModuleException])
        extends FormModuleResponse {
      lazy val message: String = errorOpt.fold("OK") { error => s"Failed to create module. Error: ${error.message}" }
    }

    object CreateModuleResponse {
      given Codec.AsObject[CreateModuleResponse] = deriveCodecWithDefaults
    }

    // MODULE EDIT CLASS -----------------------------------------------------
    final case class UpdateModuleDraftRequest(
      registry: Registry,
      module: ModuleInfo,
      ops: Seq[FileOp],
      metadata: Map[String, String] = Map.empty
    )

    object UpdateModuleDraftRequest {
      given Codec.AsObject[UpdateModuleDraftRequest] = deriveCodecWithDefaults
    }

    final case class UpdateModuleDraftRequestWithId(request: UpdateModuleDraftRequest, id: ServerlessRequestId)
        extends FormModuleRequest

    object UpdateModuleDraftRequestWithId {
      given Codec.AsObject[UpdateModuleDraftRequestWithId] = deriveCodecWithDefaults
    }

    final case class UpdateModuleDraftResponse(id: ServerlessRequestId, message: String) extends FormModuleResponse

    object UpdateModuleDraftResponse {
      given Codec.AsObject[UpdateModuleDraftResponse] = deriveCodecWithDefaults
    }

    // MODULE PUBLISH CLASS ---------------------------------------------------------
    final case class ModulePublishRequest(
      registry: Registry,
      module: ModuleInfo
    )

    object ModulePublishRequest {
      given Codec.AsObject[ModulePublishRequest] = deriveCodecWithDefaults
    }

    final case class ModulePublishRequestWithId(request: ModulePublishRequest, id: ServerlessRequestId)
        extends FormModuleRequest

    object ModulePublishRequestWithId {
      given Codec.AsObject[ModulePublishRequestWithId] = deriveCodecWithDefaults
    }

    final case class ModulePublishResponse(id: ServerlessRequestId, errorOpt: Option[FormModuleException])
        extends FormModuleResponse {
      lazy val message: String = errorOpt.fold("OK") { error => s"Failed to publish module. Error: ${error.message}" }
    }

    object ModulePublishResponse {
      given Codec.AsObject[ModulePublishResponse] = deriveCodecWithDefaults
    }

    // MODULE CHECKOUT CLASS ---------------------------------------------------------
    final case class ModuleCheckoutRequest(
      registry: Registry,
      module: ModuleInfo
    )

    object ModuleCheckoutRequest {
      given Codec.AsObject[ModuleCheckoutRequest] = deriveCodecWithDefaults
    }

    final case class ModuleCheckoutRequestWithId(request: ModuleCheckoutRequest, id: ServerlessRequestId)
        extends FormModuleRequest

    object ModuleCheckoutRequestWithId {
      given Codec.AsObject[ModuleCheckoutRequestWithId] = deriveCodecWithDefaults
    }

    final case class ModuleCheckoutResponse(id: ServerlessRequestId, message: String) extends FormModuleResponse

    object ModuleCheckoutResponse {
      given Codec.AsObject[ModuleCheckoutResponse] = deriveCodecWithDefaults
    }

    // EXEC CUE COMMAND CLASS---------------------------------------------------------
    final case class ExecCueCommandRequest(
      registry: Registry,
      module: ModuleInfo,
      command: Command,
      // .:package
      packages: Seq[String] = Seq.empty,
      filePaths: Seq[String] = Seq.empty,
      // shortcut to upload a json file here, will need to replace by actually uploading file in the future
      inputJsonStringOpt: Option[String] = None,
      // -e
      expressionOpt: Option[String] = None,
      // -d
      schemaOpt: Option[String] = None,
      // -l
      pathOpt: Option[String] = None
    )

    object ExecCueCommandRequest {
      given Codec.AsObject[ExecCueCommandRequest] = deriveCodecWithDefaults
    }

    final case class ExecCueCommandRequestWithId(request: ExecCueCommandRequest, id: ServerlessRequestId)
        extends FormModuleRequest

    object ExecCueCommandRequestWithId {
      given Codec.AsObject[ExecCueCommandRequestWithId] = deriveCodecWithDefaults
    }

    final case class ExecCueCommandResponse(
      id: ServerlessRequestId,
      result: Either[FormModuleException, String]
    ) extends FormModuleResponse {
      lazy val message: String = result.fold(_ => "Exec cue command returns an error", _ => "OK")
    }

    object ExecCueCommandResponse {
      given Codec.AsObject[ExecCueCommandResponse] = deriveCodecWithDefaults
    }

    // GENERATE CUE MODULE CLASS ---------------------------------------------------------
    case class GenerateCueModuleRequest(
      registry: Registry,
      module: ModuleInfo,
      languageVersion: String,
      metadata: Map[String, String] = Map.empty,
      cueModuleConfig: CueModuleConfig,
      cueGeneratorInput: CueGeneratorInput
    )

    object GenerateCueModuleRequest {
      given Codec.AsObject[GenerateCueModuleRequest] = deriveCodecWithDefaults
    }

    case class GenerateCueModuleRequestWithId(request: GenerateCueModuleRequest, id: ServerlessRequestId)
        extends FormModuleRequest

    object GenerateCueModuleRequestWithId {
      given Codec.AsObject[GenerateCueModuleRequestWithId] = deriveCodecWithDefaults
    }

    case class GenerateCueModuleResponse(id: ServerlessRequestId) extends FormModuleResponse {
      lazy val message: String = "OK"
    }

    object GenerateCueModuleResponse {
      given Codec.AsObject[GenerateCueModuleResponse] = deriveCodecWithDefaults
    }

    // CORE CLASS --------------------------------------------------------------------
    final case class Registry(url: String, user: String, password: String)

    object Registry {
      given Codec.AsObject[Registry] = deriveCodecWithDefaults
    }

    sealed trait FileOp

    object FileOp {
      given Codec.AsObject[FileOp] = deriveCodecWithDefaults
    }

    final case class DataFile(storageId: String, s3Access: S3Access)

    object DataFile {
      given Codec.AsObject[DataFile] = deriveCodecWithDefaults
    }

    final case class OverrideFile(path: String, source: DataFile) extends FileOp

    object OverrideFile {
      given Codec.AsObject[OverrideFile] = deriveCodecWithDefaults
    }

    final case class DeleteFile(path: String) extends FileOp

    object DeleteFile {
      given Codec.AsObject[DeleteFile] = deriveCodecWithDefaults
    }

    // CUE GENERATOR CLASS ---------------------------------------------------------
    final case class CueModuleConfig(
      commonDataTypes: ModuleInfo,
      softValidationDataTypes: ModuleInfo,
      tableCommonSchema: ModuleInfo,
      fundsubTypes: ModuleInfo,
      dataextractCommonSchema: ModuleInfo,
      dataLayerCommonSchema: ModuleInfo
    )

    object CueModuleConfig {
      given Codec.AsObject[CueModuleConfig] = deriveCodecWithDefaults

      def fromCommonConfig(commonConfig: CommonConfig.CueModuleConfig): Option[CueModuleConfig] = {
        for {
          commonDataTypes <- ModuleInfo.fromCanonicalString(commonConfig.commonDataTypes.modulePath)
          softValidationTypes <- ModuleInfo.fromCanonicalString(commonConfig.softValidationDataTypes.modulePath)
          tableCommonSchema <- ModuleInfo.fromCanonicalString(commonConfig.tableCommonSchema.modulePath)
          fundsubTypes <- ModuleInfo.fromCanonicalString(commonConfig.fundsubTypes.modulePath)
          dataextractCommonSchema <- ModuleInfo.fromCanonicalString(commonConfig.dataextractCommonSchema.modulePath)
          dataLayerCommonSchema <- ModuleInfo.fromCanonicalString(commonConfig.dataLayerCommonSchema.modulePath)
        } yield CueModuleConfig(
          commonDataTypes,
          softValidationTypes,
          tableCommonSchema,
          fundsubTypes,
          dataextractCommonSchema,
          dataLayerCommonSchema
        )
      }

    }

    sealed trait CueGeneratorInput

    object CueGeneratorInput {
      given Codec.AsObject[CueGeneratorInput] = deriveCodecWithDefaults

      final case class GenerateDataExtractModule(
        annotationDataS3Access: S3Access,
        templatesInfo: Seq[GenerateDataExtractModule.TemplateInfo]
      ) extends CueGeneratorInput

      object GenerateDataExtractModule {
        given Codec.AsObject[GenerateDataExtractModule] = deriveCodecWithDefaults

        final case class TemplateInfo(
          alias: String,
          name: String,
          isRepeatable: Boolean,
          annotationDataS3Key: String
        )

        object TemplateInfo {
          given Codec.AsObject[TemplateInfo] = deriveCodecWithDefaults
        }

      }

      final case class GeneratePdfToolModule(
        fileId: String,
        name: String,
        annotationDataS3Key: String,
        annotationDataS3Access: S3Access
      ) extends CueGeneratorInput

      object GeneratePdfToolModule {
        given Codec.AsObject[GeneratePdfToolModule] = deriveCodecWithDefaults
      }

      final case class GenerateDataLayerModule(
        annotationDataS3Access: S3Access,
        asaInfo: List[(String, Json)],
        csaInfo: List[(String, Json)],
        pdfCueModuleInfos: Seq[GenerateDataLayerModule.PdfCueModuleInfo]
      ) extends CueGeneratorInput

      object GenerateDataLayerModule {
        given Codec.AsObject[GenerateDataLayerModule] = deriveCodecWithDefaults

        final case class PdfCueModuleInfo(
          annotationVersionId: String,
          module: ModuleInfo
        )

        object PdfCueModuleInfo {
          given Codec.AsObject[PdfCueModuleInfo] = deriveCodecWithDefaults
        }

      }

      final case class GenerateSaTemplateModule() extends CueGeneratorInput

      object GenerateSaTemplateModule {
        given Codec.AsObject[GenerateSaTemplateModule] = deriveCodecWithDefaults
      }

      final case class GenerateTableMappingModule(
        sourceModule: TableModule,
        targetModules: List[TableModule]
      ) extends CueGeneratorInput

      object GenerateTableMappingModule {
        given Codec.AsObject[GenerateTableMappingModule] = deriveCodecWithDefaults

        final case class TableModule(
          moduleInfo: ModuleInfo,
          tableSchemaPackage: String,
          valueSchemaPackage: String,
          valuesTransformerPackage: String,
          valuesWithValidationTransformerPackage: String
        )

        object TableModule {
          given Codec.AsObject[TableModule] = deriveCodecWithDefaults
        }

      }

    }

  }

  object mtslApi {

    final case class CertificateSigningRequest(
      id: ServerlessRequestId = ServerlessRequestIdFactory.unsafeRandomId(ServerlessAction.Certificate),
      a: String = "SIGN",
      p: String, // the content of the csr file
      cn: String = "lambda_gen",
      `override`: Json = Json.Null
    ) extends ServerlessRequest

    object CertificateSigningRequest {
      given Codec.AsObject[CertificateSigningRequest] = deriveCodecWithDefaults
    }

    final case class CertificateSigningResponse(
      id: ServerlessRequestId,
      message: String,
      output: String // "cert in PEM format" to indicate the action success or not and to return the cert
    ) extends ServerlessResponse

    object CertificateSigningResponse {
      given Codec.AsObject[CertificateSigningResponse] = deriveCodecWithDefaults
    }

  }

  object IpWhitelist {

    enum IpWhitelistAction(val value: String) extends StringEnum {
      case Update extends IpWhitelistAction("update")
      case Remove extends IpWhitelistAction("remove")
      case Get extends IpWhitelistAction("get")
    }

    object IpWhitelistAction extends StringEnumCompanion[IpWhitelistAction] {
      given Codec[IpWhitelistAction] = deriveStringEnumCodec
    }

    // Request Model
    final case class IpWhitelistRequest(
      id: ServerlessRequestId,
      action: IpWhitelistAction,
      mhash: String,
      ip: Option[List[String]] = None
    ) extends ServerlessRequest

    object IpWhitelistRequest {
      given Codec.AsObject[IpWhitelistRequest] = deriveCodecWithDefaults
    }

    // Response Model
    final case class IpWhitelistResponse(
      id: ServerlessRequestId,
      message: String,
      result: Option[List[String]] = None
    ) extends ServerlessResponse

    object IpWhitelistResponse {
      given Codec.AsObject[IpWhitelistResponse] = deriveCodecWithDefaults
    }

  }

  object docSplit {

    // General models
    final case class PageRange(
      startPage: Int,
      endPage: Int
    ) derives CirceCodec.WithDefaults

    // Request and response traits
    sealed trait DocSplitRequest derives CirceCodec.WithDefaults
    sealed trait DocSplitResponse derives CirceCodec.WithDefaults

    // Request and response with id
    final case class DocSplitRequestWithId(
      id: ServerlessRequestId,
      request: DocSplitRequest
    ) extends ServerlessRequest derives CirceCodec.WithDefaults

    final case class DocSplitResponseWithId(
      id: ServerlessRequestId,
      response: DocSplitResponse,
      message: String
    ) extends ServerlessResponse derives CirceCodec.WithDefaults

    // Get page ranges by text pattern
    final case class GetPageRangesByTextPatternRequest(
      storageId: String,
      s3Access: S3Access,
      textPattern: String
    ) extends DocSplitRequest derives CirceCodec.WithDefaults

    final case class GetPageRangesByTextPatternResponse(
      pageRanges: List[GetPageRangesByTextPatternResponse.PageRangeWithMatchedText]
    ) extends DocSplitResponse derives CirceCodec.WithDefaults

    object GetPageRangesByTextPatternResponse {

      final case class PageRangeWithMatchedText(
        startPage: Int,
        endPage: Int,
        matchedTexts: List[String]
      ) derives CirceCodec.WithDefaults

    }

    // Extract text with page ranges
    final case class ExtractTextWithPageRangesRequest(
      storageId: String,
      s3Access: S3Access,
      pageRanges: List[PageRange],
      pageArea: ExtractTextWithPageRangesRequest.PageArea
    ) extends DocSplitRequest derives CirceCodec.WithDefaults

    object ExtractTextWithPageRangesRequest {

      final case class PageArea(
        pageIndex: Int,
        pageRect: PageRect
      ) derives CirceCodec.WithDefaults

      final case class PageRect(
        x: Double,
        y: Double,
        width: Double,
        height: Double
      ) derives CirceCodec.WithDefaults

    }

    final case class ExtractTextWithPageRangesResponse(
      extractedTexts: List[ExtractTextWithPageRangesResponse.ExtractedText]
    ) extends DocSplitResponse derives CirceCodec.WithDefaults

    object ExtractTextWithPageRangesResponse {

      final case class ExtractedText(
        pageRange: PageRange,
        text: String
      ) derives CirceCodec.WithDefaults

    }

  }

  object bedrock {

    final case class BedrockAccess(
      accessKey: String,
      secretKey: String,
      region: String
    ) derives CirceCodec.WithDefaults

    sealed trait BedrockRequest extends ServerlessRequest derives CirceCodec.WithDefaults

    sealed trait BedrockResponse extends ServerlessResponse derives CirceCodec.WithDefaults

    case class GenTextEmbTitanG1Request(
      bedrockAccess: BedrockAccess,
      text: String,
      embeddingLength: Option[Int]
    ) derives CirceCodec.WithDefaults

    case class GenTextEmbTitanG1RequestWithId(
      id: ServerlessRequestId,
      request: GenTextEmbTitanG1Request
    ) extends BedrockRequest derives CirceCodec.WithDefaults

    case class GenTextEmbTitanG1Response(
      id: ServerlessRequestId,
      message: String,
      embeddings: List[List[Float]],
      error: String
    ) extends BedrockResponse derives CirceCodec.WithDefaults

    object GenTextEmbTitanG1Response {
      given Codec.AsObject[GenTextEmbTitanG1Response] = deriveCodecWithDefaults
    }

    case class GenImageEmbTitanG1Request(
      bedrockAccess: BedrockAccess,
      s3Access: S3Access,
      inputStorageId: String,
      embeddingLength: Option[Int]
    ) derives CirceCodec.WithDefaults

    case class GenImageEmbTitanG1RequestWithId(
      id: ServerlessRequestId,
      request: GenImageEmbTitanG1Request
    ) extends BedrockRequest derives CirceCodec.WithDefaults

    case class GenImageEmbTitanG1Response(
      id: ServerlessRequestId,
      message: String,
      embeddings: List[List[Float]],
      error: String
    ) extends BedrockResponse derives CirceCodec.WithDefaults

  }

  object formDataExtraction {

    final case class Field(
      field_name: String,
      `type`: String,
      label: String,
      description: String,
      question: String
    ) derives CirceCodec.WithDefaults

    final case class FormSchema(
      fields: List[Field]
    ) derives CirceCodec.WithDefaults

    object extractData {

      final case class FieldResult(
        field_name: String,
        value: String
      ) derives CirceCodec.WithDefaults

      // Request and response
      final case class FormDataExtractionRequest(
        formSchema: FormSchema,
        s3Images: List[String],
        s3Access: S3Access,
        liteLlmAccess: LiteLlmAccess
      ) derives CirceCodec.WithDefaults

      final case class FormDataExtractionRequestWithId(
        id: ServerlessRequestId,
        request: FormDataExtractionRequest
      ) extends ServerlessRequest derives CirceCodec.WithDefaults

      final case class FormDataExtractionResponse(
        fields: List[FieldResult]
      ) derives CirceCodec.WithDefaults

      final case class FormDataExtractionResponseWithId(
        id: ServerlessRequestId,
        statusCode: Int,
        body: FormDataExtractionResponse
      ) extends ServerlessResponse derives CirceCodec.WithDefaults {
        override def message: String = s"Response with status code: $statusCode"
      }

    }

    object generateSchema {

      // Request and response
      final case class GenerateSchemaRequest(
        documentType: Option[String],
        s3Images: List[String],
        initialFormSchema: annotations.PdfFormSchema,
        s3Access: S3Access,
        liteLlmAccess: LiteLlmAccess
      ) derives CirceCodec.WithDefaults

      final case class GenerateSchemaRequestWithId(
        id: ServerlessRequestId,
        request: GenerateSchemaRequest
      ) extends ServerlessRequest derives CirceCodec.WithDefaults

      final case class GenerateSchemaResponse(
        document_type: Option[String],
        form_schema: FormSchema
      ) derives CirceCodec.WithDefaults

      final case class GenerateSchemaResponseWithId(
        id: ServerlessRequestId,
        statusCode: Int,
        body: GenerateSchemaResponse
      ) extends ServerlessResponse derives CirceCodec.WithDefaults {
        override def message: String = s"Response with status code: $statusCode"
      }

    }

  }

  object annotations {

    final case class PdfFieldInfo(
      field_name: String,
      bounding_box: List[Float],
      page_number: Int,
      field_label: Option[String] = None,
      field_value: Option[String] = None,
      field_type: Option[String] = None
    ) derives CirceCodec.WithDefaults

    object PdfFieldInfo {

      def fromPdfObjects(pdfObjects: Seq[PdfObject], getLabelFn: PdfObject => Option[String]): Seq[PdfFieldInfo] = {
        pdfObjects.collect { case field: PdfTerminalField =>
          val area = field.area
          PdfFieldInfo(
            field_name = field.name,
            bounding_box = List(
              area.left.floatValue,
              area.top.floatValue,
              area.right.floatValue,
              area.bottom.floatValue
            ),
            page_number = field.pageIndex,
            field_label = getLabelFn(field),
            field_value = None,
            field_type = Some(field match {
              case _: PdfTextField      => "Text"
              case _: PdfCheckboxField  => "CheckBox"
              case _: PdfRadioField     => "RadioButton"
              case _: PdfSignatureField => "Signature"
            })
          )
        }
      }

    }

    final case class PdfField(
      index: Option[Int],
      page_number: Option[Int],
      pdf_field_name: Option[String],
      `type`: Option[String],
      label: Option[String]
    ) derives CirceCodec.WithDefaults

    final case class PdfFormSchema(
      fields: Map[String, PdfField]
    ) derives CirceCodec.WithDefaults

    object analyzePdfAnnotations {

      final case class FieldResult(
        field_name: String,
        `type`: String,
        alias: String,
        asa: List[String]
      ) derives CirceCodec.WithDefaults

      final case class FormSchemaResult(
        fields: List[FieldResult]
      ) derives CirceCodec.WithDefaults

      // Request and response
      final case class AnalyzePdfAnnotationsRequest(
        initialFormSchema: PdfFormSchema,
        s3Images: List[String],
        s3Access: S3Access,
        liteLlmAccess: LiteLlmAccess,
        documentType: Option[String],
        section: Option[String]
      ) derives CirceCodec.WithDefaults

      final case class AnalyzePdfAnnotationsRequestWithId(
        id: ServerlessRequestId,
        request: AnalyzePdfAnnotationsRequest
      ) extends ServerlessRequest derives CirceCodec.WithDefaults

      final case class AnalyzePdfAnnotationsResponse(
        document_type: Option[String],
        form_schema: FormSchemaResult
      ) derives CirceCodec.WithDefaults

      final case class AnalyzePdfAnnotationsResponseWithId(
        id: ServerlessRequestId,
        statusCode: Int,
        body: AnalyzePdfAnnotationsResponse
      ) extends ServerlessResponse derives CirceCodec.WithDefaults {
        override def message: String = s"Response with status code: $statusCode"
      }

    }

    object drawAnnotationBoxes {

      // Request and response
      final case class DrawAnnotationBoxesRequest(
        pdfPath: String, // Must be annotated if `fieldsOpt` is None
        fieldNames: Set[String],
        s3Access: S3Access,
        useCache: Boolean,
        fieldsOpt: Option[List[PdfFieldInfo]]
      ) derives CirceCodec.WithDefaults

      final case class DrawAnnotationBoxesRequestWithId(
        id: ServerlessRequestId,
        request: DrawAnnotationBoxesRequest
      ) extends ServerlessRequest derives CirceCodec.WithDefaults

      final case class DrawAnnotationBoxesResponse(
        pdf_form_schema: PdfFormSchema,
        image_path_by_page: Map[Int, String]
      ) derives CirceCodec.WithDefaults

      final case class DrawAnnotationBoxesResponseWithId(
        id: ServerlessRequestId,
        statusCode: Int,
        body: DrawAnnotationBoxesResponse
      ) extends ServerlessResponse derives CirceCodec.WithDefaults {
        override def message: String = s"Response with status code: $statusCode"
      }

    }

  }

  object extractKeywords {

    final case class ExtractKeywordsRequest(
      text: String
    ) derives CirceCodec.WithDefaults

    final case class ExtractKeywordsRequestWithId(
      executionId: ServerlessRequestId,
      request: ExtractKeywordsRequest
    ) extends ServerlessRequest derives CirceCodec.WithDefaults {
      override val id: ServerlessRequestId = executionId
    }

    final case class ExtractedKeyWord(
      text: String,
      position: Int,
      lemmatized: String
    ) derives CirceCodec.WithDefaults

    final case class ExtractKeywordsResponse(
      statusCode: Int,
      executionId: ServerlessRequestId,
      message: String,
      keywords: List[ExtractedKeyWord]
    ) extends ServerlessResponse derives CirceCodec.WithDefaults {
      override val id: ServerlessRequestId = executionId
    }

  }

}
