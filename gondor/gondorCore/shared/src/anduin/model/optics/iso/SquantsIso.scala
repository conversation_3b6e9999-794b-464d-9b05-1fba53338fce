// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.model.optics.iso

import monocle.Iso
import squants.Dimensionless
import squants.information.Information
import squants.market.Money

import anduin.protobuf.SquantMappers

import anduin.optics.iso.utils.IsoUtils.*
import anduin.protobuf.external.squants.*

trait SquantsIso {

  given moneyIso: Iso[Money, MoneyMessage] = fromTypeMapper(SquantMappers.moneyTypeMapper)

  given dimensionlessIso: Iso[Dimensionless, DimensionlessMessage] =
    fromTypeMapper(SquantMappers.dimensionlessTypeMapper)

  given informationIso: Iso[Information, InformationMessage] =
    fromTypeMapper(SquantMappers.informationTypeMapper)

}

object SquantsIso extends SquantsIso
