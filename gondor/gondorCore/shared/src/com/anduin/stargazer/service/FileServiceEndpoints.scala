// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service

import io.circe.{Codec, JsonObject}
import anduin.circe.generic.semiauto.{CirceCodec, deriveCodecWithDefaults, deriveCodecWithDefaultsAndTypename}
import anduin.dms.{LongRunningTaskError, LongRunningTaskStatus}
import anduin.id.task.ZipFileTaskId
import anduin.model.id.{FileId, FolderId, TeamId}
import anduin.service.GeneralServiceException
import anduin.tapir.{AsyncEndpoint, AuthenticatedEndpoints}
import anduin.tapir.AuthenticatedEndpoints.BaseAuthenticatedEndpoint
import com.anduin.stargazer.service.Service.ServiceError
import com.anduin.stargazer.service.file.BatchDownloadRequest
import sttp.tapir.*
import anduin.asyncapiv2.execution.AsyncApiWorkflowQueue
import anduin.dms.upload.FileUploadData
import anduin.dms.upload.error.FileUploadError
import anduin.file.endpoints.FileUploadParams
import anduin.id.upload.{BatchUploadId, FileUploadId}
import anduin.model.codec.ProtoCodecs.given
import anduin.model.codec.MapCodecs.given
import anduin.model.common.user.UserId
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.tapir.AsyncEndpoint.AsyncAuthenticatedEndpoint
import anduin.tapir.endpoint.EmptyEndpointValidationParams
import com.anduin.stargazer.endpoints.{FileInfo, FileManagerLocation, FileManagerLocationInfo, ItemInPath}

object FileServiceEndpoints extends AuthenticatedEndpoints with AsyncEndpoint {

  private val files = "files"

  val getDownloadUrl: BaseAuthenticatedEndpoint[
    GetDownloadUrlParams,
    FileServiceApiError,
    GetDownloadUrlResponse
  ] = {
    authEndpoint[
      GetDownloadUrlParams,
      FileServiceApiError,
      GetDownloadUrlResponse
    ](files / "getDownloadUrl")
  }

  val getDownloadUrlAsync: BaseAuthenticatedEndpoint[
    GetDownloadUrlAsyncParams,
    FileServiceApiError,
    GetDownloadUrlAsyncResponse
  ] = {
    authEndpoint[
      GetDownloadUrlAsyncParams,
      FileServiceApiError,
      GetDownloadUrlAsyncResponse
    ](files / "getDownloadUrlAsync")
  }

  val getPdfUrl: BaseAuthenticatedEndpoint[
    GetPdfUrlParams,
    FileServiceApiError,
    GetDownloadUrlResponse
  ] = {
    authEndpoint[
      GetPdfUrlParams,
      FileServiceApiError,
      GetDownloadUrlResponse
    ](files / "getPdfUrl")
  }

  val getViewUrl: BaseAuthenticatedEndpoint[
    GetViewUrlParams,
    FileServiceApiError,
    GetViewUrlResp
  ] = {
    authEndpoint[
      GetViewUrlParams,
      FileServiceApiError,
      GetViewUrlResp
    ](files / "getViewUrl")
  }

  val createUserFolderIfNeeded: BaseAuthenticatedEndpoint[
    CreateUserFolderParams,
    FileServiceApiError,
    CreateUserFolderResponse
  ] = {
    authEndpoint[
      CreateUserFolderParams,
      FileServiceApiError,
      CreateUserFolderResponse
    ](files / "createUserFolderIfNeeded")
  }

  val getZipFileTaskStatus: BaseAuthenticatedEndpoint[
    GetZipFileTaskStatusParams,
    GeneralServiceException,
    GetZipFileTaskStatusResponse
  ] = {
    authEndpoint[
      GetZipFileTaskStatusParams,
      GeneralServiceException,
      GetZipFileTaskStatusResponse
    ](files / "getZipFileTaskStatus")
  }

  val createDirectUpload: BaseAuthenticatedEndpoint[
    CreateDirectUploadParams,
    FileUploadError,
    CreateDirectUploadResponse
  ] = {
    authEndpoint[
      CreateDirectUploadParams,
      FileUploadError,
      CreateDirectUploadResponse
    ](files / "createDirectUpload")
  }

  val getDirectUploadUrl: BaseAuthenticatedEndpoint[
    GetDirectUploadUrlParams,
    FileUploadError,
    GetDirectUploadUrlResponse
  ] = {
    authEndpoint[
      GetDirectUploadUrlParams,
      FileUploadError,
      GetDirectUploadUrlResponse
    ](files / "getDirectUploadUrl")
  }

  val completeDirectUpload: AsyncAuthenticatedEndpoint[
    CompleteDirectUploadParams,
    FileUploadError,
    JsonObject
  ] = {
    asyncEndpoint[
      CompleteDirectUploadParams,
      FileUploadError,
      JsonObject
    ](files / "completeDirectUpload", AsyncApiWorkflowQueue.Medium)
  }

  val getFileManagerLocation: BaseAuthenticatedEndpoint[
    GetFileManagerLocationParams,
    GeneralServiceException,
    GetFileManagerLocationResponse
  ] = authEndpoint[
    GetFileManagerLocationParams,
    GeneralServiceException,
    GetFileManagerLocationResponse
  ](files / "getFileManagerLocation")

  val getFileTree: BaseAuthenticatedEndpoint[
    GetFileTreeParams,
    GeneralServiceException,
    GetFileTreeResponse
  ] = authEndpoint[
    GetFileTreeParams,
    GeneralServiceException,
    GetFileTreeResponse
  ](files / "getFileTree")

  val getFileInfos: BaseAuthenticatedEndpoint[
    GetFileInfosParams,
    GeneralServiceException,
    GetFileInfosResponse
  ] = authEndpoint[
    GetFileInfosParams,
    GeneralServiceException,
    GetFileInfosResponse
  ](files / "getFileInfos")

  final case class GetDownloadUrlParams(
    request: BatchDownloadRequest
  )

  object GetDownloadUrlParams {
    given Codec.AsObject[GetDownloadUrlParams] = deriveCodecWithDefaults
  }

  final case class GetPdfUrlParams(
    fileId: FileId,
    password: Option[String]
  )

  object GetPdfUrlParams {
    given Codec.AsObject[GetPdfUrlParams] = deriveCodecWithDefaults
  }

  final case class GetViewUrlParams(
    fileId: FileId,
    password: Option[String]
  )

  object GetViewUrlParams {
    given Codec.AsObject[GetViewUrlParams] = deriveCodecWithDefaults
  }

  final case class GetViewUrlResp(
    url: String,
    extensionOpt: Option[String]
  )

  object GetViewUrlResp {
    given Codec.AsObject[GetViewUrlResp] = deriveCodecWithDefaults
  }

  final case class CreateUserFolderParams()

  object CreateUserFolderParams {
    given Codec.AsObject[CreateUserFolderParams] = deriveCodecWithDefaults
  }

  final case class UploadFilesToUserTemporaryFolderParams(
    purpose: UploadFilesToUserTemporaryFolderParams.Purpose
  ) extends FileUploadParams.WithEncoder[UploadFilesToUserTemporaryFolderParams]

  object UploadFilesToUserTemporaryFolderParams
      extends FileUploadParams.Companion[UploadFilesToUserTemporaryFolderParams](
        "uploadFilesToUserTemporaryFolder"
      ) {
    given Codec.AsObject[UploadFilesToUserTemporaryFolderParams] = deriveCodecWithDefaults

    sealed trait Purpose derives CanEqual

    object Purpose {
      case object CreateDataRoomWithTermsOfAccess extends Purpose
      case object ModifyDataRoomTermsOfAccess extends Purpose
      case object ImportFundDataDocuments extends Purpose

      given Codec.AsObject[Purpose] = deriveCodecWithDefaultsAndTypename
      given Codec.AsObject[CreateDataRoomWithTermsOfAccess.type] = deriveCodecWithDefaults
      given Codec.AsObject[ModifyDataRoomTermsOfAccess.type] = deriveCodecWithDefaults
    }

  }

  //// Responses
  sealed trait FileServiceApiError extends ServiceError

  object FileServiceApiError {
    given Codec.AsObject[FileServiceApiError] = deriveCodecWithDefaults

    final case class FileApiGeneralError(statusText: String, errorMessage: String) extends FileServiceApiError

    object FileApiGeneralError {
      given Codec.AsObject[FileApiGeneralError] = deriveCodecWithDefaults
    }

    final case class FilePasswordError(statusText: String, errorMessage: String) extends FileServiceApiError

    object FilePasswordError {
      given Codec.AsObject[FilePasswordError] = deriveCodecWithDefaults
    }

  }

  final case class GetDownloadUrlResponse(
    url: String,
    numberOfFiles: Int
  )

  object GetDownloadUrlResponse {
    given Codec.AsObject[GetDownloadUrlResponse] = deriveCodecWithDefaults
  }

  final case class GetDownloadUrlAsyncParams(
    request: BatchDownloadRequest
  )

  object GetDownloadUrlAsyncParams {
    given Codec.AsObject[GetDownloadUrlAsyncParams] = deriveCodecWithDefaults
  }

  final case class GetDownloadUrlAsyncResponse(
    taskId: ZipFileTaskId
  )

  object GetDownloadUrlAsyncResponse {
    given Codec.AsObject[GetDownloadUrlAsyncResponse] = deriveCodecWithDefaults
  }

  final case class CreateUserFolderResponse(folderId: FolderId)

  object CreateUserFolderResponse {
    given Codec.AsObject[CreateUserFolderResponse] = deriveCodecWithDefaults
  }

  final case class GetZipFileTaskStatusParams(
    zipFileTaskId: ZipFileTaskId
  )

  object GetZipFileTaskStatusParams {
    given Codec.AsObject[GetZipFileTaskStatusParams] = deriveCodecWithDefaults
  }

  final case class GetZipFileTaskStatusResponse(
    status: LongRunningTaskStatus,
    downloadUrlOpt: Option[String],
    errorOpt: Option[LongRunningTaskError]
  )

  object GetZipFileTaskStatusResponse {
    given Codec.AsObject[GetZipFileTaskStatusResponse] = deriveCodecWithDefaults
  }

  final case class UploadFilesToUserTemporaryFolderResp(
    files: List[FileInfo]
  )

  object UploadFilesToUserTemporaryFolderResp {
    given Codec.AsObject[UploadFilesToUserTemporaryFolderResp] = deriveCodecWithDefaults
  }

  final case class CreateDirectUploadParams(
    apiName: String,
    paramsOpt: Option[String],
    files: Seq[FileUploadData],
    emptyFolders: Seq[String]
  ) extends EmptyEndpointValidationParams

  object CreateDirectUploadParams {
    given Codec.AsObject[CreateDirectUploadParams] = deriveCodecWithDefaults
  }

  final case class CreateDirectUploadResponse(
    batchUploadId: BatchUploadId
  )

  object CreateDirectUploadResponse {
    given Codec.AsObject[CreateDirectUploadResponse] = deriveCodecWithDefaults
  }

  final case class GetDirectUploadUrlParams(
    batchUploadId: BatchUploadId,
    fileUploadId: FileUploadId
  ) extends EmptyEndpointValidationParams

  object GetDirectUploadUrlParams {
    given Codec.AsObject[GetDirectUploadUrlParams] = deriveCodecWithDefaults
  }

  final case class GetDirectUploadUrlResponse(
    uploadUrl: String,
    contentType: String
  )

  object GetDirectUploadUrlResponse {
    given Codec.AsObject[GetDirectUploadUrlResponse] = deriveCodecWithDefaults
  }

  final case class CompleteDirectUploadParams(
    batchUploadId: BatchUploadId
  ) extends EmptyEndpointValidationParams

  object CompleteDirectUploadParams {
    given Codec.AsObject[CompleteDirectUploadParams] = deriveCodecWithDefaults
  }

  final case class GetFileManagerLocationParams(
    fileManagerLocation: FileManagerLocation,
    includeSubItems: Boolean
  ) derives CanEqual

  object GetFileManagerLocationParams {
    given Codec.AsObject[GetFileManagerLocationParams] = deriveCodecWithDefaults
  }

  final case class GetFileManagerLocationResponse(
    data: FileManagerLocationInfo,
    path: Seq[ItemInPath]
  ) derives CanEqual

  object GetFileManagerLocationResponse {
    given Codec.AsObject[GetFileManagerLocationResponse] = deriveCodecWithDefaults
  }

  final case class GetFileTreeParams(
    location: FileManagerLocation,
    includeAllFileIds: Boolean = false,
    permissionTargetOpt: Option[PermissionTarget] = None
  ) derives CanEqual

  object GetFileTreeParams {
    given Codec.AsObject[GetFileTreeParams] = deriveCodecWithDefaults
  }

  sealed trait PermissionTarget derives CanEqual, CirceCodec.WithDefaultsAndTypeName

  object PermissionTarget {
    final case class SingleUser(userId: UserId) extends PermissionTarget
    final case class SingleTeam(teamId: TeamId) extends PermissionTarget
    final case class MultipleTeams(teamIds: Set[TeamId]) extends PermissionTarget
  }

  final case class TargetPermission(
    parentPermission: Option[FileFolderPermission],
    folders: Map[FolderId, FileFolderPermission],
    files: Map[FileId, FileFolderPermission]
  )

  object TargetPermission {

    given Codec.AsObject[TargetPermission] = deriveCodecWithDefaults

    val empty: TargetPermission = TargetPermission(
      None,
      Map(),
      Map()
    )

  }

  final case class GetFileTreeResponse(
    data: FileManagerLocationInfo,
    targetPermissionOpt: Option[TargetPermission]
  ) derives CanEqual

  object GetFileTreeResponse {
    given Codec.AsObject[GetFileTreeResponse] = deriveCodecWithDefaults
  }

  final case class GetFileInfosParams(
    fileIds: Seq[FileId]
  ) derives CanEqual

  object GetFileInfosParams {
    given Codec.AsObject[GetFileInfosParams] = deriveCodecWithDefaults
  }

  final case class GetFileInfosResponse(
    fileInfos: Seq[FileInfo]
  ) derives CanEqual

  object GetFileInfosResponse {
    given Codec.AsObject[GetFileInfosResponse] = deriveCodecWithDefaults
  }

}
