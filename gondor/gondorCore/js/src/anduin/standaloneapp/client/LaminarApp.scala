// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.standaloneapp.client

import com.raquo.laminar.api.L.*
import com.raquo.waypoint.Route
import design.anduin.components.error.ErrorBoundaryProvider
import design.anduin.components.toast.ToastProvider
import org.scalajs.dom

import anduin.component.error.ErrorModalManager
import anduin.graphql.component.QueryScheduler
import anduin.graphql.component.context.GraphqlContextL
import anduin.id.offering.OfferingId
import anduin.routing.LaminarRouter
import anduin.token.{AuthenticationTokenService, TokenRefresher}
import anduin.user.CurrentUserInfoProvider
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.notiCenter.NatsEventStream
import stargazer.model.routing.Page
import stargazer.util.StargazerJsBuildInfo
import stargazer.component.routing.laminar.LaminarRouterProviderL

final case class LaminarApp(
  name: String,
  containerId: String,
  offeringId: OfferingId,
  routes: String => List[Route[? <: Page, ?]],
  views: LaminarRouter => Signal[HtmlElement]
) {

  private def renderApp(
    router: LaminarRouter,
    queryScheduler: QueryScheduler
  ): Unit = {
    val container = dom.document.getElementById(containerId)

    // Reset the background color
    dom.document.body.style.backgroundColor = "var(--color-gray-0)"

    // Remove the initial loading indicator
    Option(dom.document.getElementById("mainLoadingEle")).foreach { loadingNode =>
      loadingNode.classList.add("loading-anim-done")
      while (container.childElementCount > 0) { // scalafix:ok DisableSyntax.while
        container.removeChild(container.lastElementChild)
        ()
      }
    }

    render(
      container = container,
      rootNode = ErrorBoundaryProvider(
        ToastProvider(
          GraphqlContextL(queryScheduler)(
            LaminarRouterProviderL(router)(
              div(
                child <-- views(router)
              )
            )
          )
        )
      )
    )

    ZIOUtils.runAsync(queryScheduler.start())
  }

  private def onError(error: Throwable): Unit = {
    scribe.error("Cannot start app", error)
    AuthenticationTokenService.deleteAllAuthenticationTokens()
    dom.window.localStorage.clear()
  }

  def run(): Unit = {
    StandaloneAppUtils.startServices(browserLog = true)

    NatsEventStream.start()

    TokenRefresher.init()
    TokenRefresher.start()

    ErrorModalManager.initLaminar()

    CurrentUserInfoProvider.init()

    scribe.info(s"$name: build ${StargazerJsBuildInfo.version}")

    val pathTilHash = dom.window.location.href.takeWhile(c => c != '#') + "#"
    val originPath = dom.document.location.origin
    val basePath = pathTilHash.replace(originPath, "")

    val router = LaminarRouter(routes(basePath))

    val task = for {
      queryScheduler <- StandaloneAppUtils.getDefaultQueryScheduler()
      _ <- StandaloneAppUtils.startApp(
        render = () => renderApp(router, queryScheduler),
        onError = onError,
        initSessionManager = true,
        queryScheduler = Option(queryScheduler)
      )

    } yield ()
    ZIOUtils.runAsyncAndForget(task)
  }

}
