// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.file.tree

import scala.annotation.tailrec
import design.anduin.components.button.Button
import design.anduin.components.dropdown.Dropdown
import design.anduin.components.portal.PortalWrapper
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.radio.Radio
import design.anduin.components.responsive.ScreenWidth
import design.anduin.components.responsive.react.WithScreenWidthR
import design.anduin.components.selector.react.SelectorR
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.tree.Tree
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.file.explorer.DmsIcon
import anduin.file.tree.PermissionTree.ItemType
import anduin.file.{FilePermissionExplanationPopover, ReactStream, TooltipOnTruncate}
import anduin.model.id.{FileId, FolderId}
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.endpoints.{AssetPermissionChanges, FileFolderCommon, FileInfo, FolderInfo}
import com.anduin.stargazer.service.FileServiceEndpoints.{PermissionTarget, TargetPermission}
import com.anduin.stargazer.utils.FileFolderPermissionUtils

final case class PermissionTree(
  rootItem: ItemType,
  permissionTargetOpt: Option[PermissionTarget] = None,
  changes: StateSnapshotWithModFn[AssetPermissionChanges],
  sortFn: Option[Ordering[ItemType]] = Some(PermissionTree.defaultOrder),
  externalCanSelectHeaderCheck: Option[FileFolderPermission] => Either[String, Unit] = _ => Right(()),
  externalCanSelectItemCheck: (ItemType, Option[FileFolderPermission]) => Either[String, Unit] = (_, _) => Right(()),
  showIndex: Boolean,
  lowestPermission: FileFolderPermission,
  currentUpperBound: FileFolderPermission = FileFolderPermission.Own,
  showApplyMaxPermissionButton: Option[String] = None,
  disabledReasonOpt: Option[String] = None,
  reactStream: Option[ReactStream] = None,
  fetchOnMount: Boolean = true
) {

  def apply(): VdomElement = PermissionTree.component(this)
}

object PermissionTree {

  private type Props = PermissionTree

  sealed trait ItemType derives CanEqual {
    def data: FileFolderCommon
    def targetUserPermission: Option[FileFolderPermission]
  }

  object ItemType {

    final case class Folder(
      data: FolderInfo,
      targetUserPermissionResult: TargetPermission
    ) extends ItemType {
      def targetUserPermission: Option[FileFolderPermission] = targetUserPermissionResult.parentPermission
    }

    final case class File(
      data: FileInfo,
      targetUserPermission: Option[FileFolderPermission]
    ) extends ItemType

  }

  private val TypedTree = (new Tree[ItemType])()

  private val PermissionDropdown = (new Dropdown[Option[FileFolderPermission]])()

  private val permissionOptions = List(Option.empty[FileFolderPermission]) ++ FileFolderPermission.values.map(Some(_))

  val defaultOrder: Ordering[ItemType] = Ordering.by(_.data.index)

  private def loadingBlock(item: ItemType): VdomElement = {
    BlockIndicatorR(title = Some(s"Loading ${item.data.name}..."))()
  }

  private def renderer(props: Props)(item: ItemType, renderItem: ItemType => VdomElement) = {

    item match {
      case folder: ItemType.Folder =>
        FileTreeLoader.permission(
          folder.data.itemId,
          props.permissionTargetOpt
        )(
          renderFn = { (dataOpt, targetPermission) =>
            dataOpt.fold(loadingBlock(item)) { completeData =>
              renderItem(
                ItemType.Folder(
                  completeData,
                  targetPermission.getOrElse(
                    TargetPermission(
                      None,
                      Map(),
                      Map()
                    )
                  )
                )
              )
            }
          },
          reactStream = props.reactStream,
          fetchOnMount = props.fetchOnMount
        )
      case _: ItemType.File =>
        renderItem(item)
    }
  }

  private def hasChildren(item: ItemType) = {
    item match {
      case folder: ItemType.Folder => folder.data.subFolderCount + folder.data.fileCount > 0
      case _: ItemType.File        => false
    }
  }

  private def loader(props: Props) =
    Tree.Loader.Sync[ItemType](
      loadSync = item => {
        val dataList = item match {
          case folder: ItemType.Folder =>
            folder.data.subItems.map {
              case subFolder: FolderInfo =>
                val targetUserPermission = TargetPermission(
                  parentPermission = folder.targetUserPermissionResult.folders.get(subFolder.itemId),
                  folders = Map(),
                  files = Map()
                )
                ItemType.Folder(subFolder, targetUserPermission)
              case file: FileInfo =>
                val targetUserPermission = folder.targetUserPermissionResult.files.get(file.itemId)
                ItemType.File(file, targetUserPermission)
            }
          case _: ItemType.File =>
            Seq()
        }
        props.sortFn.fold(dataList)(
          dataList.sorted(
            using _
          )
        )
      },
      hasChildrenOpt = Some(hasChildren)
    )

  @tailrec
  private def getLowestRecursiveChange(
    changes: AssetPermissionChanges,
    folderId: FolderId
  ): Option[Option[FileFolderPermission]] = {
    changes.recursivePermissions.get(folderId) -> folderId.parentFolder match {
      case (Some(permissionOpt), _)     => Some(permissionOpt)
      case (None, Some(parentFolderId)) => getLowestRecursiveChange(changes, parentFolderId)
      case (None, None)                 => None
    }
  }

  private def updateSinglePermissions[K](
    key: K,
    changes: AssetPermissionChanges,
    getCurrentSingleMap: AssetPermissionChanges => Map[K, Option[FileFolderPermission]],
    getFolder: K => FolderId,
    currentPermissionOpt: Option[FileFolderPermission],
    optionPermissionOpt: Option[FileFolderPermission]
  ) = {
    if (getLowestRecursiveChange(changes, getFolder(key)).getOrElse(currentPermissionOpt) == optionPermissionOpt) {
      getCurrentSingleMap(changes) - key
    } else {
      getCurrentSingleMap(changes) + (key -> optionPermissionOpt)
    }
  }

  private def updateRecursivePermissions(
    folderId: FolderId,
    changes: AssetPermissionChanges,
    optionPermissionOpt: Option[FileFolderPermission]
  ) = {
    val recursiveChildrenRemoved = changes.recursivePermissions.view.filterKeys(!folderId.isAncestorOf(_)).toMap
    if (folderId.parentFolder.flatMap(getLowestRecursiveChange(changes, _)).contains(optionPermissionOpt)) {
      recursiveChildrenRemoved - folderId
    } else {
      recursiveChildrenRemoved + (folderId -> optionPermissionOpt)
    }
  }

  @tailrec
  private def getNoAccessParents(
    props: Props,
    ancestors: List[ItemType],
    res: List[ItemType]
  ): Option[List[ItemType]] = {
    ancestors.lastOption match {
      case Some(folder: ItemType.Folder) =>
        if (getUpdatedPermission(props, folder).nonEmpty) {
          Some(res)
        } else {
          val remaining = ancestors.dropRight(1)
          val canSelect = getCanSelect(
            props = props,
            node = folder,
            parentNode = remaining.lastOption,
            optionPermissionOpt = Some(props.lowestPermission),
            otherReason = Right(())
          )
          if (canSelect.isRight) {
            getNoAccessParents(
              props,
              remaining,
              folder :: res
            )
          } else {
            None
          }
        }
      case Some(_: ItemType.File) =>
        None
      case None =>
        Some(res)
    }
  }

  private def onSelect(
    props: Props,
    node: ItemType,
    optionPermissionOpt: Option[FileFolderPermission],
    isRecursive: Boolean,
    onFinish: Callback
  ): Callback = {
    (node.data match {
      case folderInfo: FolderInfo =>
        if (isRecursive) {
          props.changes.modState { changes =>
            changes.copy(
              folderPermissions = changes.folderPermissions.view.filterKeys { folderId =>
                folderInfo.itemId != folderId && !folderInfo.itemId.isAncestorOf(folderId)
              }.toMap,
              filePermissions = changes.filePermissions.view.filterKeys { fileId =>
                folderInfo.itemId != fileId.folder && !folderInfo.itemId.isAncestorOf(fileId.folder)
              }.toMap,
              recursivePermissions = updateRecursivePermissions(
                folderId = folderInfo.itemId,
                changes = changes,
                optionPermissionOpt = optionPermissionOpt
              )
            )
          }
        } else {
          props.changes.modState { changes =>
            changes.copy(
              folderPermissions = updateSinglePermissions[FolderId](
                key = folderInfo.itemId,
                changes = changes,
                getCurrentSingleMap = _.folderPermissions,
                getFolder = identity,
                currentPermissionOpt = node.targetUserPermission,
                optionPermissionOpt = optionPermissionOpt
              )
            )
          }
        }
      case fileInfo: FileInfo =>
        props.changes.modState { changes =>
          changes.copy(
            filePermissions = updateSinglePermissions[FileId](
              key = fileInfo.itemId,
              changes = changes,
              getCurrentSingleMap = _.filePermissions,
              getFolder = _.folder,
              currentPermissionOpt = node.targetUserPermission,
              optionPermissionOpt = optionPermissionOpt
            )
          )
        }
    }) >> onFinish
  }

  private def onSelectNode(
    props: Props,
    rProps: Tree.NodeRenderProps[ItemType],
    isRecursive: Boolean
  )(
    optionPermissionOpt: Option[FileFolderPermission]
  ) = {
    onSelect(
      props = props,
      node = rProps.node,
      optionPermissionOpt = optionPermissionOpt,
      isRecursive = isRecursive,
      onFinish = Callback.unless(rProps.isExpanded)(rProps.toggle)
    )
  }

  private def renderRow(
    renderContent: Option[FileFolderPermission] => VdomNode,
    renderPlusChildren: Option[FileFolderPermission] => Option[VdomNode] = _ => None
  ) = {
    val options = permissionOptions.sorted(
      using FileFolderPermissionUtils.optOrdering
    )
    options.toVdomArray(
      using { optionPermissionOpt =>
        <.div(
          ^.key := optionPermissionOpt.toString,
          tw.flexNone.flex.itemsCenter.justifyCenter.wPx128,
          TagMod.when(renderPlusChildren(optionPermissionOpt).nonEmpty)(tw.group),
          <.div(tw.flexNone),
          <.div(
            ComponentUtils.testId(PermissionTree, FileFolderPermissionUtils.getPermissionName(optionPermissionOpt)),
            tw.flexNone,
            renderContent(optionPermissionOpt)
          ),
          <.div(
            tw.wPx1.overflowXVisible,
            renderPlusChildren(optionPermissionOpt).whenDefined(
              using { plusChildren =>
                TagMod(
                  tw.invisible.groupHover(tw.visible),
                  plusChildren
                )
              }
            )
          )
        )
      }
    )
  }

  private def getUpdatedPermission(props: Props, node: ItemType) = {
    val (nearestFolder, updatedPermission) = node.data match {
      case folderInfo: FolderInfo =>
        folderInfo.itemId -> props.changes.value.folderPermissions.get(folderInfo.itemId)
      case fileInfo: FileInfo =>
        fileInfo.itemId.folder -> props.changes.value.filePermissions.get(fileInfo.itemId)
    }
    updatedPermission
      .orElse(getLowestRecursiveChange(props.changes.value, nearestFolder))
      .filter(props.externalCanSelectItemCheck(node, _).isRight)
      .fold {
        FileFolderPermissionUtils.optOrdering.min(node.targetUserPermission, Some(props.currentUpperBound))
      } {
        FileFolderPermissionUtils.optOrdering.min(_, node.data.userPermission)
      }
  }

  private def getCanSelect(
    props: Props,
    node: ItemType,
    parentNode: Option[ItemType],
    optionPermissionOpt: Option[FileFolderPermission],
    otherReason: Either[String, Unit]
  ) = {
    for {
      _ <- props.externalCanSelectItemCheck(node, optionPermissionOpt)
      parentOwnerCheck = parentNode.forall { parentNode =>
        !getUpdatedPermission(props, parentNode).contains(FileFolderPermission.Own)
      }
      _ <- Either.cond(
        parentOwnerCheck,
        (),
        "Owners of the containing folder also own this"
      )
      _ <- FileFolderPermissionUtils.canChangePermission(
        actorPermissionOpt = node.data.userPermission,
        currentPermissionOpt = node.targetUserPermission,
        optionPermissionOpt = optionPermissionOpt
      )
      _ <- otherReason
    } yield ()
  }

  private def renderConfirmation(
    props: Props,
    onChange: Option[FileFolderPermission] => Callback,
    noAccessParentOpt: Option[List[ItemType]]
  )(
    renderTarget: (Option[FileFolderPermission] => Callback) => VdomNode
  ) = {
    val noAccessParents = noAccessParentOpt.getOrElse(List())
    val noAccessFolders = noAccessParents.foldRight(List.empty[DmsGrantAccessConfirmation.Tree]) { (parent, children) =>
      List(DmsGrantAccessConfirmation.Tree(parent.data.name, children))
    }
    if (noAccessFolders.isEmpty) {
      renderTarget(onChange)
    } else {
      DmsGrantAccessConfirmation(
        renderTarget = renderTarget,
        folders = Map("" -> noAccessFolders),
        lowestPermission = props.lowestPermission,
        onSave = onChange(_) >> Callback.traverse(noAccessParents) { item =>
          onSelect(
            props = props,
            node = item,
            optionPermissionOpt = Some(props.lowestPermission),
            isRecursive = false,
            onFinish = Callback.empty
          )
        }
      )()
    }
  }

  private def renderRadioButton(
    props: Props,
    rProps: Tree.NodeRenderProps[ItemType],
    updatedPermissionOpt: Option[FileFolderPermission],
    canSelectMap: Map[Option[FileFolderPermission], Either[String, Unit]],
    noAccessParentOpt: Option[List[ItemType]]
  )(
    optionPermissionOpt: Option[FileFolderPermission]
  ) = {
    val isChecked = updatedPermissionOpt == optionPermissionOpt
    val canSelect = canSelectMap.getOrElse(optionPermissionOpt, Right(()))
    val isDisabled = canSelect.isLeft || canSelectMap.count(_._2.isRight) <= 1
    TooltipR(
      renderTarget = {
        renderConfirmation(
          props = props,
          onChange = onSelectNode(
            props = props,
            rProps = rProps,
            isRecursive = true
          ),
          noAccessParentOpt = noAccessParentOpt
        ) { onChange =>
          <.div(
            TagMod.when(isChecked && !isDisabled)(^.onClick --> onChange(optionPermissionOpt)),
            tw.wPx16.mx8,
            Radio(
              isChecked = isChecked,
              onChange = onChange(optionPermissionOpt),
              isDisabled = isDisabled && !isChecked
            )()
          )
        }
      },
      renderContent = _(canSelect.left.getOrElse[String]("")),
      isDisabled = canSelect.isRight
    )()
  }

  private def renderThisOnlyLabel(
    props: Props,
    rProps: Tree.NodeRenderProps[ItemType],
    updatedPermissionOpt: Option[FileFolderPermission],
    canSelectMap: Map[Option[FileFolderPermission], Either[String, Unit]],
    noAccessParentOpt: Option[List[ItemType]]
  )(
    optionPermissionOpt: Option[FileFolderPermission]
  ) = {
    val canSelect = canSelectMap.getOrElse(optionPermissionOpt, Right(()))
    val isDisabled = canSelect.isLeft || canSelectMap.count(_._2.isRight) <= 1
    val isChecked = optionPermissionOpt == updatedPermissionOpt
    val isOwnOrNoAccess = optionPermissionOpt.forall(_ == FileFolderPermission.Own)

    def shouldFolderHaveTag(folderInfo: FolderInfo) = {
      val isEmpty = folderInfo.subFolderCount + folderInfo.fileCount == 0
      !isDisabled && !isEmpty && !isChecked && !isOwnOrNoAccess
    }

    rProps.node.data match {
      case folderInfo: FolderInfo if shouldFolderHaveTag(folderInfo) =>
        Some(
          renderConfirmation(
            props = props,
            onChange = onSelectNode(
              props = props,
              rProps = rProps,
              isRecursive = false
            ),
            noAccessParentOpt = noAccessParentOpt
          ) { onChange =>
            <.div(
              tw.wFit,
              TooltipR(
                renderTarget = Button(
                  style = Button.Style.Text(),
                  onClick = onChange(optionPermissionOpt)
                )(<.span(tw.whitespaceNowrap, "Only this")),
                renderContent = _("Apply this permission to this folder\nonly, not its content"),
                targetWrapper = PortalWrapper.Inline
              )()
            )
          }
        )
      case _ =>
        None
    }
  }

  private def renderDropdown(
    props: Props,
    rProps: Tree.NodeRenderProps[ItemType],
    updatedPermissionOpt: Option[FileFolderPermission],
    canSelectMap: Map[Option[FileFolderPermission], Either[String, Unit]],
    noAccessParentOpt: Option[List[ItemType]]
  ) = {
    val isDisabled = canSelectMap.count(_._2.isRight) <= 1
    if (isDisabled) {
      <.div(
        tw.flex.itemsCenter.textPrimary5,
        FileFolderPermissionUtils.getPermissionName(updatedPermissionOpt)
      )
    } else {
      renderConfirmation(
        props,
        onChange = onSelectNode(
          props,
          rProps,
          isRecursive = true
        ),
        noAccessParentOpt
      ) { openToggle =>
        <.label(
          tw.flex.itemsCenter.cursorPointer,
          PermissionDropdown(
            value = Some(updatedPermissionOpt),
            valueToString = FileFolderPermissionUtils.getPermissionName,
            onChange = openToggle,
            items = permissionOptions.map { optionPermissionOpt =>
              Dropdown.Item(
                value = optionPermissionOpt,
                isDisabled = canSelectMap.getOrElse(optionPermissionOpt, Right(())).isLeft
              )
            },
            menu = Dropdown.Menu(
              renderItemBody = Option { item =>
                <.div(
                  ^.minHeight := "40px",
                  tw.flex.flexCol.justifyCenter.text13.leading20.pl8,
                  <.div(
                    tw.fontSemiBold,
                    FileFolderPermissionUtils.getPermissionName(item.value)
                  ),
                  <.div(
                    tw.fontNormal,
                    item.value.map(FileFolderPermissionUtils.getPermissionSubtitles)
                  )
                )
              }
            ),
            button = Dropdown.Button(appearance = Dropdown.Appearance.Text()),
            itemWidth = 240
          )()
        )
      }
    }
  }

  private def renderItemRow(
    props: Props,
    rProps: Tree.NodeRenderProps[ItemType],
    isLargeScreen: Boolean
  ) = {
    val updatedPermissionOpt = getUpdatedPermission(props, rProps.node)
    val noAccessParentOpt = getNoAccessParents(
      props,
      rProps.ancestorNodes.toList,
      List()
    )
    val otherReason = for {
      _ <- noAccessParentOpt.map(_ => ()).toRight {
        "This user's permission can't be modified as they don't have access to the parent folder. Please contact an admin if you think this is a mistake"
      }
      _ <- Either.cond(
        props.disabledReasonOpt.isEmpty,
        (),
        props.disabledReasonOpt.getOrElse("")
      )
    } yield ()
    val canSelectMap = permissionOptions.map { optionPermissionOpt =>
      optionPermissionOpt ->
        getCanSelect(
          props = props,
          node = rProps.node,
          parentNode = rProps.ancestorNodes.lastOption,
          optionPermissionOpt = optionPermissionOpt,
          otherReason = otherReason
        )
    }.toMap
    if (isLargeScreen) {
      renderRow(
        renderContent = renderRadioButton(
          props,
          rProps,
          updatedPermissionOpt,
          canSelectMap,
          noAccessParentOpt
        ),
        renderPlusChildren = renderThisOnlyLabel(
          props,
          rProps,
          updatedPermissionOpt,
          canSelectMap,
          noAccessParentOpt
        )
      )
    } else {
      renderDropdown(
        props,
        rProps,
        updatedPermissionOpt,
        canSelectMap,
        noAccessParentOpt
      )
    }
  }

  private def renderHeaderRow(props: Props) = {
    renderRow(
      renderContent = { optionPermissionOpt =>
        renderHeaderItem(
          props = props,
          title = FileFolderPermissionUtils.getPermissionName(optionPermissionOpt),
          optionPermissionOpt = optionPermissionOpt,
          canSelect = props.externalCanSelectHeaderCheck(optionPermissionOpt),
          isMaxPermission = false
        )
      }
    )
  }

  private def getOptionPermissionChanges(
    rootItem: ItemType,
    optionPermissionOpt: Option[FileFolderPermission]
  ) = {
    AssetPermissionChanges(
      recursivePermissions = rootItem match {
        case folder: ItemType.Folder =>
          Map(folder.data.itemId -> optionPermissionOpt)
        case _: ItemType.File =>
          Map()
      }
    )
  }

  private def renderHeaderItem(
    props: Props,
    title: String,
    optionPermissionOpt: Option[FileFolderPermission],
    isMaxPermission: Boolean,
    canSelect: Either[String, Unit]
  ) = {
    val optionPermissionChanges = getOptionPermissionChanges(props.rootItem, optionPermissionOpt)
    val maxPermissionChanges = getOptionPermissionChanges(props.rootItem, Some(props.currentUpperBound))
    TooltipR(
      renderTarget = {
        <.div(
          ^.width := "110px",
          SelectorR(
            isReadOnly = canSelect.isLeft,
            isSelected =
              (props.changes.value == optionPermissionChanges) && (isMaxPermission || optionPermissionChanges != maxPermissionChanges),
            onClick = props.changes.setState(optionPermissionChanges)
          )(
            <.div(
              tw.py8.fontSemiBold,
              title
            )
          )
        )
      },
      renderContent = _(canSelect.left.getOrElse[String]("")),
      isDisabled = canSelect.isRight
    )()
  }

  private def renderIcon(rProps: Tree.NodeRenderProps[ItemType]) = {
    <.div(
      tw.flexNone.px8,
      DmsIcon(
        rProps.node.data match {
          case folderInfo: FolderInfo =>
            DmsIcon.Folder(folderInfo.fileCount + folderInfo.subFolderCount)
          case fileInfo: FileInfo =>
            DmsIcon.File(fileInfo.name)
        }
      )()
    )
  }

  private def renderItem(props: Props, isLargeScreen: Boolean)(rProps: Tree.NodeRenderProps[ItemType]) = {
    val data = rProps.node.data
    val indexes = rProps.ancestorNodes.flatMap(_.data.index) ++ data.index
    Some(
      <.div(
        tw.flex.itemsStretch.py8.hover(tw.bgGray1),
        tw.sticky.top0.bgGray0.z1,
        <.button(
          if (hasChildren(rProps.node)) {
            ^.onClick --> rProps.toggle
          } else {
            ^.cursor.default
          },
          tw.flexFill.flex.itemsCenter,
          renderIcon(rProps),
          <.div(
            ComponentUtils.testId(PermissionTree, "FileName"),
            tw.flexFill.truncate.textLeft,
            TooltipOnTruncate(
              renderTarget = <.div.withRef(_)(
                tw.truncate,
                if (indexes.nonEmpty && props.showIndex) {
                  s"${indexes.mkString(".")}. ${rProps.node.data.name}"
                } else {
                  rProps.node.data.name
                }
              ),
              content = rProps.node.data.name
            )()
          )
        ),
        renderItemRow(
          props,
          rProps,
          isLargeScreen
        )
      )
    )
  }

  private def render(props: Props) = {
    WithScreenWidthR(
      render = screenWidth => {
        val isLargeScreen = screenWidth >= ScreenWidth.Large
        React.Fragment(
          if (isLargeScreen) {
            <.div(
              tw.flex.itemsEnd.itemsCenter.py12.mb8,
              tw.borderBottom.borderGray2.border1,
              tw.sticky.top0.bgGray0.z2,
              <.div(
                tw.flexFill,
                FilePermissionExplanationPopover()
              ),
              props.showApplyMaxPermissionButton.fold(EmptyVdom) { title =>
                <.div(
                  tw.flexNone.flex.itemsCenter.justifyCenter.wPx128,
                  renderHeaderItem(
                    props = props,
                    title = title,
                    optionPermissionOpt = Some(props.currentUpperBound),
                    isMaxPermission = true,
                    canSelect = Right(())
                  )
                )
              },
              renderHeaderRow(props)
            )
          } else {
            EmptyVdom
          },
          <.div(
            tw.overflowYAuto,
            TypedTree(
              node = props.rootItem,
              render = renderItem(props, isLargeScreen),
              getKey = _.data.itemId.idString,
              loader = loader(props),
              shouldExpanded = _.data.itemId == props.rootItem.data.itemId,
              rendererOnExpand = renderer(props),
              appearance = Tree.Appearance.Minimal,
              layout = Tree.Layout(
                container = container =>
                  <.div(
                    ComponentUtils.testId(PermissionTree, "FileRow"),
                    tw.flex.itemsCenter,
                    container
                  ),
                toggle = toggle =>
                  <.div(
                    ComponentUtils.testId(PermissionTree, "Expander"),
                    tw.flexNone,
                    toggle
                  ),
                content = content => <.div(tw.flexFill, content)
              )
            )()
          )
        )
      }
    )()
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

  object AssetPermissionChangesReusability {

    given permission: Reusability[FileFolderPermission] = Reusability.by_==

    given permissionMap: [K] => Reusability[Map[K, Option[FileFolderPermission]]] =
      Reusability.map[K, Option[FileFolderPermission]](
        using Reusability.option[FileFolderPermission]
      )

    given unknownFieldSet: Reusability[scalapb.UnknownFieldSet] = Reusability.always

    given changes: Reusability[AssetPermissionChanges] =
      Reusability.derive

  }

}
