// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.file.tree

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.file.ReactStream
import anduin.model.id.FolderId
import anduin.service.GeneralServiceException
import anduin.stargazer.component.SignalReactor
import anduin.utils.endpoint.{FetchComponent, FetchProps}
import com.anduin.stargazer.client.services.file.FileJsClient
import com.anduin.stargazer.endpoints.*
import com.anduin.stargazer.notiCenter.NotificationCenter
import com.anduin.stargazer.service.FileServiceEndpoints.{
  GetFileTreeParams,
  GetFileTreeResponse,
  PermissionTarget,
  TargetPermission
}
import design.anduin.components.toast.Toast

object FileTreeLoader {

  type RenderFn = (Option[FileManagerLocationInfo], Option[TargetPermission]) => VdomNode

  final case class OuterProps(renderFn: RenderFn, reactStream: Option[ReactStream]) derives CanEqual

  type Props = FetchProps[
    OuterProps,
    GetFileTreeParams,
    GeneralServiceException,
    GetFileTreeResponse
  ]

  private final case class State()

  def permission(
    folderId: FolderId,
    permissionTargetOpt: Option[PermissionTarget]
  )(
    renderFn: (Option[FolderInfo], Option[TargetPermission]) => VdomNode,
    reactStream: Option[ReactStream] = None,
    fetchOnMount: Boolean = true
  ): VdomElement = {
    fetchComponent(
      OuterProps(
        { (fileManagerLocationInfoOpt, targetPermissionOpt) =>
          renderFn(
            fileManagerLocationInfoOpt.collect { case folder: FolderInfo =>
              folder
            },
            targetPermissionOpt
          )
        },
        reactStream
      ),
      GetFileTreeParams(
        FileManagerLocation.Folder(None, folderId),
        permissionTargetOpt = permissionTargetOpt
      ),
      fetchOnMount = fetchOnMount,
      onError = _ => Toast.errorCallback("Failed to get folder permissions")
    )
  }

  def tree(
    location: FileManagerLocation,
    includeAllFileIds: Boolean = false
  )(
    renderFn: Option[FileManagerLocationInfo] => VdomNode,
    reactStream: Option[ReactStream] = None,
    fetchOnMount: Boolean = true
  ): VdomElement = {
    fetchComponent(
      OuterProps(
        { (fileManagerLocationInfoOpt, _) =>
          renderFn(fileManagerLocationInfoOpt)
        },
        reactStream
      ),
      GetFileTreeParams(
        location,
        includeAllFileIds
      ),
      fetchOnMount = fetchOnMount,
      onError = _ => Toast.errorCallback("Failed to get folder tree")
    )
  }

  private final class Backend(scope: BackendScope[Props, State]) {
    val reactor: SignalReactor[NotificationCenter.ReactorData] = NotificationCenter.reactor((_, _) => fetchData)

    def render(props: Props) = {
      props.outerProps.renderFn(
        props.respOpt.map(_.data),
        props.respOpt.flatMap(_.targetPermissionOpt)
      )
    }

    private def fetchData: Callback = {
      for {
        props <- scope.props
        _ <- props.refetch
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidMount { scope =>
      Callback.traverseOption(scope.props.outerProps.reactStream.map(_.channel)) { channel =>
        NotificationCenter.subscribeSingleChannelReactor(
          scope.backend.reactor,
          channel
        )
      }
    }
    .componentWillUnmount(_.backend.reactor.cleanUp())
    .build

  val fetchComponent = FetchComponent(
    component,
    FileJsClient.getFileTree
  )

}
