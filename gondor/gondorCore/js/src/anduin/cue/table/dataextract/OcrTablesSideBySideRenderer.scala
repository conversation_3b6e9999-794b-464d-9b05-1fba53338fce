// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.cue.table.dataextract

import com.raquo.laminar.api.L.*
import design.anduin.components.icon.Icon
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.style.tw.*

import anduin.cue.model.dataextract.DataExtractCommonSchema
import anduin.cue.model.dataextract.DataExtractCommonSchema.OcrFields
import anduin.cue.model.table.TableCommonSchema.*
import anduin.cue.model.table.TableSharedModels.SoftValidationValuesWithValidation
import anduin.cue.table.dataextract.OcrPdfViewer.{OcrBlock, OcrBlockRect}
import anduin.cue.table.dataextract.OcrTablesSideBySideRenderer.*
import anduin.cue.table.softvalidation.models.TableSection
import anduin.cue.table.softvalidation.{SoftValidationTableRenderer, SoftValidationTablesRenderer}
import anduin.dataextract.models.TemplateData
import anduin.model.id.FileId

final case class OcrTablesSideBySideRenderer(
  tableSchemas: Seq[TableSchema],
  initialData: SoftValidationValuesWithValidation = SoftValidationValuesWithValidation.empty,
  ocrDataSignal: Signal[OcrFields],
  templateFileIdMapSignal: Signal[Map[String, FileId]],
  userDocumentFileIdMapSignal: Signal[Map[String, FileId]],
  templateDataMapSignal: Signal[Map[String, TemplateData]],
  dataEvents: EventStream[SoftValidationValuesWithValidation] = EventStream.empty,
  dataObserver: Observer[SoftValidationValuesWithValidation] = Observer.empty
) {
  private val locateTargetVar: Var[LocateTarget] = Var(LocateTarget())
  private val locateTargetSignal = locateTargetVar.signal.distinct

  private val filePagesBlocksMapSignal = ocrDataSignal.map(buildFilePagesBlocksMapFromOcrData).distinct

  def renderLeftSide(): HtmlElement = {
    div(
      tw.hPc100.flex.flexCol.overflowHidden,
      div(
        tw.flex.itemsCenter.justifyBetween.hPx48.px16.flexNone,
        div(
          tw.text17.fontSemiBold.leading28,
          "Extract data"
        )
      ),
      div(
        tw.px16.pb12.flexNone,
        TextBoxL(
          value = Val(""),
          placeholder = "Search for sections, questions, values ...",
          icon = Some(Icon.Glyph.Search)
        )()
      ),
      div(
        tw.px16.flex1.overflowYAuto,
        SoftValidationTablesRenderer(
          tableSchemas = tableSchemas,
          initialData = initialData,
          dataEvents = dataEvents,
          dataObserver = dataObserver,
          tableMode = SoftValidationTableRenderer.TableViewerSideBySideMode(
            onLocateField = locateTargetVar.writer,
            ocrDataSignal = ocrDataSignal
          )
        )()
      )
    )
  }

  def renderRightSide(): HtmlElement = {
    div(
      tw.hPc100.overflowHidden.bgGray1,
      OcrPdfRenderer(
        locateTargetSignal = locateTargetSignal,
        filePagesBlocksMapSignal = filePagesBlocksMapSignal,
        userDocumentFileIdMapSignal = userDocumentFileIdMapSignal
      )()
    )
  }

  def apply(): HtmlElement = {
    div(
      tw.grid.gridCols2.hPc100,
      renderLeftSide(),
      renderRightSide()
    )
  }

}

object OcrTablesSideBySideRenderer {
  case class LocateTarget(blocks: Seq[OcrBlock] = Seq.empty)

  def getMappedBlocks(ocrFields: OcrFields, namespace: String, fieldName: String, repeatableIndex: Option[Int] = None)
    : Seq[OcrBlock] = {
    ocrFields.fieldMap match {
      case Right(map) =>
        map
          .get(namespace)
          .flatMap(_.get(fieldName))
          .flatMap {
            case single: DataExtractCommonSchema.OcrFieldData =>
              val blocks = extractBlocksFromOcrFieldData(
                fieldData = single,
                namespace = namespace,
                fieldName = fieldName,
                repeatableIndex = repeatableIndex
              )
              Some(blocks)
            // repeatable
            case multiple: Seq[DataExtractCommonSchema.OcrFieldData] =>
              repeatableIndex match {
                case Some(index) =>
                  multiple.lift(index).map { fieldData =>
                    extractBlocksFromOcrFieldData(
                      fieldData = fieldData,
                      namespace = namespace,
                      fieldName = fieldName,
                      repeatableIndex = repeatableIndex
                    )
                  }
                case None =>
                  Some(
                    multiple.zipWithIndex.flatMap((fieldData, repeatableIndex) =>
                      extractBlocksFromOcrFieldData(
                        fieldData = fieldData,
                        namespace = namespace,
                        fieldName = fieldName,
                        repeatableIndex = Some(repeatableIndex)
                      )
                    )
                  )
              }
          }
          .getOrElse(Seq.empty)
      case Left(_) => Seq.empty
    }
  }

  def getBlocksForGroup(group: TableSection.Group, ocrFields: OcrFields): Seq[OcrBlock] = {
    group.fields.flatMap {
      case stringField: SoftValidationStringField =>
        (for {
          namespace <- stringField.namespace.toOption.toSeq
          fieldName <- stringField.fieldName.toOption.toSeq
        } yield getMappedBlocks(ocrFields = ocrFields, namespace = namespace, fieldName = fieldName)).flatten

      case compoundField: SoftValidationCompoundField =>
        for {
          namespace <- compoundField.namespace.toOption.toSeq
          subFieldMetadata <- compoundField.subFieldMetadata.toOption.toSeq
          subField <- subFieldMetadata
          block <- getMappedBlocks(
            ocrFields = ocrFields,
            namespace = namespace,
            fieldName = subField.subFieldName
          )
        } yield block

      case checkboxField: SoftValidationMultipleCheckboxField =>
        for {
          namespace <- checkboxField.namespace.toOption.toSeq
          valueSchema <- checkboxField.valueSchema.toOption.toSeq
          allValues <- valueSchema.rawValueIncomplete.flatMap(_.allValues).toSeq
          value <- allValues
          block <- getMappedBlocks(ocrFields = ocrFields, namespace = namespace, fieldName = value.key)
        } yield block
    }
  }

  private def extractBlocksFromOcrFieldData(
    fieldData: DataExtractCommonSchema.OcrFieldData,
    namespace: String,
    fieldName: String,
    repeatableIndex: Option[Int]
  ): Seq[OcrBlock] = {
    fieldData.metadata match {
      case Right(ocrFieldMetadata) =>
        ocrFieldMetadata.mappedBlocks match {
          case Right(mappedBlocks) =>
            mappedBlocks
              .map { mapBlock =>
                (for {
                  fileId <- mapBlock.fileId
                  page <- mapBlock.page
                  boundingBox <- mapBlock.boundingBox
                  boxWidth <- boundingBox.width
                  boxHeight <- boundingBox.height
                  boxTop <- boundingBox.top
                  boxLeft <- boundingBox.left
                  blockInfo <- mapBlock.textractBlockInfo
                  blockId <- blockInfo.blockId
                  blockType <- blockInfo.blockType
                  value <- blockInfo.value
                } yield {
                  OcrBlock(
                    blockId = blockId,
                    blockType = blockType,
                    fileId = fileId,
                    value = value,
                    repeatableIndex = repeatableIndex,
                    namespace = namespace,
                    fieldName = fieldName,
                    pageIndex = page,
                    rect = OcrBlockRect(
                      width = boxWidth,
                      height = boxHeight,
                      top = boxTop,
                      left = boxLeft
                    )
                  )
                }) match {
                  case Left(_)      => None
                  case Right(block) => Some(block)
                }
              }
              .collect { case Some(block) => block }
          case Left(_) => Seq.empty
        }
      case Left(_) => Seq.empty
    }
  }

  private def buildFilePagesBlocksMapFromOcrData(ocrData: OcrFields): Map[String, Map[Int, Seq[OcrBlock]]] = {
    ocrData.fieldMap match {
      case Right(fieldMap) =>
        // Iterate over all namespaces and fields to collect blocks
        fieldMap.foldLeft(Map.empty[String, Map[Int, Seq[OcrBlock]]]) { case (accMap, (namespace, fieldsMap)) =>
          fieldsMap.foldLeft(accMap) { case (innerMap, (fieldName, fieldData)) =>
            // Extract blocks based on whether the field is single or repeatable
            val blocks = fieldData match {
              case single: DataExtractCommonSchema.OcrFieldData =>
                extractBlocksFromOcrFieldData(
                  fieldData = single,
                  namespace = namespace,
                  fieldName = fieldName,
                  repeatableIndex = None
                )
              case multiple: Seq[DataExtractCommonSchema.OcrFieldData] =>
                multiple.zipWithIndex.flatMap((fieldData, repeatableIndex) =>
                  extractBlocksFromOcrFieldData(
                    fieldData = fieldData,
                    namespace = namespace,
                    fieldName = fieldName,
                    repeatableIndex = Some(repeatableIndex)
                  )
                )
            }

            // Add all blocks to the result map
            blocks.foldLeft(innerMap)(OcrTablesSideBySideRenderer.addSingleBlockToMap)
          }
        }
      case Left(_) => Map.empty[String, Map[Int, Seq[OcrBlock]]]
    }
  }

  private def addSingleBlockToMap(
    currentMap: Map[String, Map[Int, Seq[OcrBlock]]],
    newBlock: OcrBlock
  ): Map[String, Map[Int, Seq[OcrBlock]]] = {
    currentMap.get(newBlock.fileId) match {
      case Some(pageMap) =>
        pageMap.get(newBlock.pageIndex) match {
          case Some(blockList) =>
            val newBlockList = (newBlock +: blockList).distinctBy(_.blockId)
            val newPageMap = pageMap + (newBlock.pageIndex -> newBlockList)
            currentMap + (newBlock.fileId -> newPageMap)
          case None =>
            val newPageMap = pageMap + (newBlock.pageIndex -> Seq(newBlock))
            currentMap + (newBlock.fileId -> newPageMap)
        }
      case None =>
        val pageMap = Map(newBlock.pageIndex -> Seq(newBlock))
        currentMap + (newBlock.fileId -> pageMap)
    }
  }

}
