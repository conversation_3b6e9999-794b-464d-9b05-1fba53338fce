// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.cue.table.dataextract

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.clipboard.laminar.CopyToClipboardL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.laminar.PortalWrapperL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.viewer.pdfium.Layout.GridAttribute
import design.anduin.components.viewer.pdfium.{Layout, LightToolbar, ViewerP}
import design.anduin.style.Colors
import design.anduin.style.tw.*

import anduin.cue.model.dataextract.DataExtractCommonSchema.TextractBlockInfo
import anduin.cue.model.dataextract.DataExtractCommonSchema.TextractBlockInfo.BlockType
import anduin.cue.table.dataextract.OcrPdfViewer.OcrBlock

case class OcrPdfViewer(
  url: String,
  initialBlock: OcrBlock,
  mappedBlocksSignal: Signal[Seq[OcrBlock]],
  pageBlocksMapSignal: Signal[Map[Int, Seq[OcrBlock]]]
) {

  private def renderCopy(block: OcrBlock) = {
    PopoverL(
      targetWrapper = PortalWrapperL.Block,
      renderTarget = (onOpen, _) => {
        div(
          tw.inlineBlock.absolute.inset0,
          onClick.mapToUnit --> onOpen
        )
      },
      renderContent = _ => {
        div(
          margin.px(-8),
          tw.p4,
          CopyToClipboardL(
            content = Val(block.value.toString),
            renderTarget = _ => {
              div(
                tw.flex.itemsCenter,
                ButtonL(
                  style = ButtonL.Style.Full(
                    height = ButtonL.Height.Fix24,
                    icon = Some(Icon.Glyph.Clipboard)
                  )
                )("Copy")
              )
            }
          )()
        )
      }
    )().amend(
      tw.sizePc100.relative
    )
  }

  private def renderBlock(block: OcrBlock): HtmlElement = {
    val isLocatedBlockSignal: Signal[Boolean] = mappedBlocksSignal.map(_.exists(_.blockId == block.blockId)).distinct

    div(
      tw.absolute.cursorPointer.borderAll,
      isLocatedBlockSignal.cls(tw.bgWarning4.bgOpacity30, tw.bgPrimary4.bgOpacity10),
      tw.z1 := (block.blockType == TextractBlockInfo.BlockType.Value),
      borderColor <-- isLocatedBlockSignal.map(if (_) Colors.warning4 else Colors.primary4("50%")),
      left := s"${block.rect.left * 100}%",
      top := s"${block.rect.top * 100}%",
      width := s"${block.rect.width * 100}%",
      height := s"${block.rect.height * 100}%",
      dataAttr("block-id") := block.blockId,
      dataAttr("namespace") := block.namespace,
      dataAttr("field-name") := block.fieldName,
      block.repeatableIndex.map { index =>
        dataAttr("repeatable-index") := s"$index"
      },
      block.blockType match {
        case TextractBlockInfo.BlockType.Line     => renderCopy(block)
        case TextractBlockInfo.BlockType.Value    => renderCopy(block)
        case TextractBlockInfo.BlockType.Checkbox => emptyNode
      }
    )

  }

  private def createJumpDestinationFromBlock(block: OcrBlock) = {
    val yOffsetPercentage = (1 - block.rect.top) + 0.25

    val finalYOffsetPercentage = Math.min(1, yOffsetPercentage)

    ViewerP.PageNavigation(
      pageIndex = block.pageIndex,
      yOffsetPercentage = finalYOffsetPercentage,
      xOffsetPercentage = block.rect.left
    )
  }

  def apply(): HtmlElement = {
    div(
      tw.hPc100.wPc100.overflowHidden,
      ViewerP(
        url = url,
        initialPage = Some(createJumpDestinationFromBlock(initialBlock)),
        body = body => {
          Layout.Default.copy(
            container = container => {
              div(
                tw.hPc100.grid,
                GridAttribute.TemplateAreas := "'toolbar' 'main'",
                GridAttribute.TemplateRows := "81px calc(100% - 81px)",
                container
              )
            },
            toolbar = renderToolbar => {
              div(
                GridAttribute.Area := "toolbar",
                tw.borderBottom.border1.borderGray3,
                div(
                  tw.flex.py6.px12.itemsCenter.gap4.hPx40,
                  IconL(
                    name = Val(Icon.Glyph.FileGeneric)
                  )().amend(tw.textGray6.flexNone),
                  TruncateL(
                    target = {
                      div(
                        tw.fontSemiBold,
                        "CLOUD VENTURE Fund IV L.P. - Subscription Booklet.pdf"
                      )
                    }
                  )()
                ),
                div(
                  tw.flexCenter.py4,
                  LightToolbar(
                    renderToolbar = renderToolbar
                  )()
                )
              )
            },
            main = main => {
              div(
                GridAttribute.Area := "main",
                tw.overflowYScroll.bgGray1,
                mappedBlocksSignal.changes.map(_.headOption).collect { case Some(block) =>
                  block
                } --> Observer[OcrBlock] { block =>
                  body.onJumpToPageWithOffset.onNext(
                    createJumpDestinationFromBlock(block)
                  )
                },
                main
              )
            }
          )
        },
        renderPage = Some { renderProps =>
          Seq(renderProps.customLayer)
        },
        renderCustomLayer = Some(renderProps => {
          val page = renderProps.page
          val pageIndex = page.realIndex
          val blocksSignal = pageBlocksMapSignal.map(_.getOrElse(pageIndex, Seq.empty))

          div(
            tw.absolute.wPc100.hPc100.group,
            children <-- blocksSignal.split(_.blockId) { (_, block, _) =>
              renderBlock(block)
            }
          )
        })
      )()
    )
  }

}

object OcrPdfViewer {

  case class OcrBlockRect(
    top: Double,
    left: Double,
    width: Double,
    height: Double
  )

  case class OcrBlock(
    blockId: String,
    blockType: BlockType,
    fileId: String,
    namespace: String,
    fieldName: String,
    repeatableIndex: Option[Int],
    value: String | Boolean,
    pageIndex: Int,
    rect: OcrBlockRect
  )

}
