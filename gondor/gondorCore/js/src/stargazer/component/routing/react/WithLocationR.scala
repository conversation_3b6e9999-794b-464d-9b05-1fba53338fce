// Copyright (C) 2014-2025 Anduin Transactions Inc.

package stargazer.component.routing.react

import scala.annotation.unused

import com.raquo.laminar.api.L.Observer
import design.anduin.components.wrapper.react.AirStreamOwnerSupportR
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

object WithLocationR {

  private type Props = String => VdomNode

  private final case class State(
    location: String
  )

  private class Backend(@unused scope: BackendScope[Props, State]) extends AirStreamOwnerSupportR {

    def render(props: Props, state: State): VdomNode = {
      props(state.location)
    }

    def registerObservable: Callback = {
      withOwner { owner =>
        Callback {
          LocationProviderR.locationSignal.addObserver(Observer[String] { location =>
            scope.modState(_.copy(location = location))
            ()
          })(
            using owner
          )
        }
      }
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State(LocationProviderR.getCurrentLocation))
    .renderBackend[Backend]
    .configure(AirStreamOwnerSupportR.install)
    .componentDidMount(_.backend.registerObservable)
    .build

  def apply(props: Props): VdomElement = component(props)
}
