#!/usr/bin/env bash

# Playground for infra team, if you dont know how to install k8s, please ignore this file.

here=`cd $(dirname $BASH_SOURCE); pwd`
source $here/../../../../ci/vars/common/aws-env.sh
source $here/common.sh

export FAKE_TIDB="true"

local_envs=("$here/../../../../.local.env" "$HOME/.config/stargazer/local.env")
for f in ${local_envs[@]}; do
    if test -f "$f"; then
        echo "Sourcing local env var at $f"
        source $f
    fi
done

function startServices {
    kubectl delete job wait-stargazer --namespace $NAMESPACE --context "$context" --ignore-not-found=true
    kubectl create ns $NAMESPACE || true
    kubectl label ns $NAMESPACE nukable='true' || true
    cat <<EOF | kubectl apply -f -
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: standard
provisioner: rancher.io/local-path
volumeBindingMode: WaitForFirstConsumer
reclaimPolicy: Delete
EOF
    pushd $here/../../../../ci/rivendell-v2
    INFRA_ONLY=true ./node_modules/.bin/ts-node ./src/actions/interops.ts \
                    --env=local \
                    --var=stargazerSprintTag=$sidecarImageTag \
                    --var=k8sNamespace=$NAMESPACE
    popd
}

function start {
        startServices
}

start
