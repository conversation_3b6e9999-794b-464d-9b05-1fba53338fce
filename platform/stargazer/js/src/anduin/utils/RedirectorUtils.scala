// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.utils

import org.scalajs.dom
import org.scalajs.dom.URL

object RedirectorUtils {

  def redirectToUrl(url: String): Unit = {
    if ((url.startsWith("/") || url.startsWith("#")) && !url.startsWith("//")) {
      dom.window.setTimeout(() => dom.window.location.replace(url), 2000)
      ()
    } else {
      ()
    }
  }

  def redirectToAbsoluteUrl(absUrl: String): Unit = {
    val url = new URL(absUrl)
    redirectToUrl(s"${url.pathname}${url.hash}")
  }

  def redirectToUrlImmediately(url: String): Unit = {
    if ((url.startsWith("/") || url.startsWith("#")) && !url.startsWith("//")) {
      dom.window.setTimeout(() => dom.window.location.replace(url), 100)
      ()
    } else {
      ()
    }
  }

  def redirectToUrlUnsafe(url: String, timeout: Int = 100): Unit = {
    dom.window.setTimeout(() => dom.window.location.replace(url), timeout)
    ()
  }

}
