// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.component.text

import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import org.scalajs.dom.KeyCode

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class SearchBox(
  placeholder: String = "Search",
  initialValue: String = "",
  onSearch: String => Callback = (_ => Callback.empty),
  onChange: String => Callback = (_ => Callback.empty),
  width: String = "256px"
) {
  def apply(): VdomElement = SearchBox.component(this)
}

object SearchBox {
  private type Props = SearchBox

  private final case class State(
    text: String
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      <.div(
        tw.relative,
        ^.width := props.width,
        <.div(
          tw.absolute.top0,
          tw.cursorPointer,
          tw.hPc100.wPx32,
          tw.flex.itemsCenter.justifyCenter,
          ^.onClick --> props.onSearch(state.text)
        )(
          <.div(tw.textGray4, IconR(name = Icon.Glyph.Search)())
        ),
        <.input(
          tw.block.wPc100.borderAll.border1,
          tw.textGray8.bgGray0.borderGray4.rounded2,
          tw.trnsS.focus(tw.borderPrimary4.shadowSpread),
          tw.hPx32.leading16.text13.pl32.pr24,
          ^.tpe := "text",
          ^.placeholder := props.placeholder,
          ^.value := state.text,
          ^.onChange ==> { (e: ReactEventFromInput) =>
            val value = e.target.value
            scope.modState(_.copy(text = value), props.onChange(value))
          },
          ^.onKeyDown ==> { (e: ReactKeyboardEventFromInput) =>
            val keyCode = e.keyCode
            Callback.when(keyCode == KeyCode.Enter) {
              props.onSearch(state.text)
            }
          }
        ),
        TagMod.when(state.text.nonEmpty) {
          <.div(
            tw.absolute.top0.right0.bottom0.pr8.z1,
            tw.flex.itemsCenter.justifyCenter,
            <.div(
              tw.roundedFull.bgGray5.wPx16.hPx16.hover(tw.bgGray7),
              tw.flex.itemsCenter.justifyCenter,
              <.button(
                tw.textGray0.borderNone.bgTransparent,
                IconR(name = Icon.Glyph.CrossSmall)(),
                ^.onClick --> scope.modState(_.copy(text = ""), props.onChange("") >> props.onSearch(""))
              )
            )
          )
        }
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps { props =>
      State(props.initialValue)
    }
    .renderBackend[Backend]
    .componentDidUpdate { scope =>
      Callback.when(scope.prevProps.initialValue != scope.currentProps.initialValue) {
        scope.modState(_.copy(text = scope.currentProps.initialValue))
      }
    }
    .build

}
