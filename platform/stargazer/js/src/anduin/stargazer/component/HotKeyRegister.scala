// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.stargazer.component

import design.anduin.style.tw.*
import org.scalajs.dom
import org.scalajs.dom.window

import com.raquo.laminar.api.L.*

final case class HotKeyRegister(
  keyDownHandler: dom.KeyboardEvent => Unit = _ => (),
  keyUpHandler: dom.KeyboardEvent => Unit = _ => ()
) {

  def apply(): Node = div(
    tw.hidden,
    onMountCallback { _ =>
      dom.document.addEventListener("keydown", keyDownHandler)
      dom.document.addEventListener("keyup", keyUpHandler)
    },
    onUnmountCallback { _ =>
      dom.document.removeEventListener("keydown", keyDownHandler)
      dom.document.removeEventListener("keyup", keyUpHandler)
    }
  )

}

object HotKeyRegister {

  lazy val isMac: Boolean = window.navigator.userAgent.matches(".*(Mac|iPod|iPhone|iPad).*")

}
