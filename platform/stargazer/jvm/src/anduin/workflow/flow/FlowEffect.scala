package anduin.workflow.flow

import scala.reflect.TypeTest
import zio.temporal.JavaTypeTag
import zio.temporal.activity.ZActivityStub
import zio.temporal.workflow.{ZChildWorkflowStub, ZSaga, ZWorkflow}

// scalafix:off DisableSyntax.covariant
opaque type FlowEffect[+A] = ZSaga[A]
// scalafix:on DisableSyntax.covariant

object FlowEffect {

  extension [A](underlying: FlowEffect[A]) {

    def get(options: ZSaga.Options = ZSaga.Options.default): Either[Throwable, A] =
      underlying.run(options)

    def getOrThrow(options: ZSaga.Options = ZSaga.Options.default): A = {
      underlying.runOrThrow(options)
    }

    def map[B](f: A => B): FlowEffect[B] = {
      underlying.map(f)
    }

    def as[B](value: => B): FlowEffect[B] =
      map(_ => value)

    def unit: FlowEffect[Unit] =
      as(())

    def flatMap[B](f: A => FlowEffect[B]): FlowEffect[B] = {
      underlying.flatMap(f)
    }

    def catchAll[A0 >: A](f: Throwable => FlowEffect[A0]): FlowEffect[A0] = {
      underlying.catchAll(f)
    }

    def either: FlowEffect[Either[Throwable, A]] = {
      underlying.map(res => Right(res)).catchAll { error =>
        succeed(Left(error))
      }
    }

    def eitherSome[E <: Throwable](
      using TypeTest[Throwable, E]
    ): FlowEffect[Either[E, A]] = {
      underlying.map(res => Right(res)).catchSome { case error: E =>
        succeed(Left(error))
      }
    }

    def catchSome[A0 >: A](pf: PartialFunction[Throwable, ZSaga[A0]]): FlowEffect[A0] = {
      underlying.catchSome(pf)
    }

    def zipWith[B, C](that: => ZSaga[B])(f: (A, B) => C): ZSaga[C] = {
      underlying.zipWith(that)(f)
    }

  }

  def succeed[A](value: => A): FlowEffect[A] = {
    ZSaga.succeed(value)
  }

  val unit: FlowEffect[Unit] = succeed({})

  val none: FlowEffect[Option[Nothing]] = {
    succeed(Option.empty[Nothing])
  }

  def attempt[A](thunk: => A): FlowEffect[A] = {
    ZSaga.attempt(thunk)
  }

  def foreach[A, B](as: Seq[A])(f: A => FlowEffect[B]): FlowEffect[Seq[B]] = {
    ZSaga.foreach(as)(f)
  }

  def fromEither[E <: Throwable, A](either: Either[E, A]): FlowEffect[A] = {
    either.fold[FlowEffect[A]](
      FlowEffect.fail,
      FlowEffect.succeed
    )
  }

  def fromOption[E <: Throwable, A](option: Option[A], error: => E): FlowEffect[A] = {
    option.fold[FlowEffect[A]](FlowEffect.fail(error))(FlowEffect.succeed)
  }

  def fail[E <: Throwable](error: => E): FlowEffect[Nothing] = ZSaga.fail(error)

  def acquireRelease[A, T](acquire: => FlowEffect[A])(use: A => FlowEffect[T])(release: A => FlowEffect[Any])
    : FlowEffect[T] = {
    acquire.flatMap { resource =>
      val task = for {
        res <- use(resource)
        _ <- release(resource)
      } yield res
      task.catchAll[T] { error =>
        release(resource).flatMap { _ =>
          FlowEffect.fail(error)
        }
      }
    }
  }

  def sideEffect[R](
    f: () => R
  )(
    using JavaTypeTag[R]
  ): FlowEffect[R] = ZSaga.attempt {
    ZWorkflow.sideEffect(f)
  }

  inline def executeActivity[R](
    inline f: R
  )(
    using JavaTypeTag[R]
  ): FlowEffect[R] = {
    FlowEffect.attempt(
      ZActivityStub.execute(f)
    )
  }

  inline def executeActivityEither[E <: Throwable, R](
    inline f: Either[E, R]
  )(
    using JavaTypeTag[Either[E, R]]
  ): FlowEffect[R] = {
    for {
      either <- FlowEffect.attempt(
        ZActivityStub.execute(f)
      )
      res <- either.fold[FlowEffect[R]](
        FlowEffect.fail,
        FlowEffect.succeed
      )
    } yield res
  }

  inline def executeChildWorkflow[R](
    inline f: R
  )(
    using JavaTypeTag[R]
  ): FlowEffect[R] = {
    FlowEffect.attempt(
      ZChildWorkflowStub.execute(f)
    )
  }

  def validate(condition: => Boolean)(exception: => Throwable): FlowEffect[Unit] = {
    if (condition) {
      FlowEffect.unit
    } else {
      FlowEffect.fail(exception)
    }
  }

}
