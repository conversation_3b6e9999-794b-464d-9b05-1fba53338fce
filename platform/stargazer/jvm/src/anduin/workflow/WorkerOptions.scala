// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow

import zio.temporal.worker.ZWorkerOptions

import com.anduin.stargazer.apps.stargazer.StargazerSettings

object WorkerOptions {

  lazy val default: ZWorkerOptions = ZWorkerOptions.default
    .withMaxConcurrentWorkflowTaskExecutionSize(20)
    .withMaxConcurrentActivityExecutionSize(100)
    .withMaxConcurrentLocalActivityExecutionSize(100)

  lazy val fast: ZWorkerOptions = default.withMaxConcurrentActivityExecutionSize(100) // Fast activities
  lazy val medium: ZWorkerOptions = default.withMaxConcurrentActivityExecutionSize(50) // Slower activities or IO bound
  lazy val heavy: ZWorkerOptions = default.withMaxConcurrentActivityExecutionSize(10) // CPU bound

  object DataIntegration {

    lazy val workerOptions: ZWorkerOptions = ZWorkerOptions.default
      .withMaxConcurrentWorkflowTaskExecutionSize(50)
      .withMaxConcurrentActivityExecutionSize(100)
      .withMaxConcurrentLocalActivityExecutionSize(100)

  }

  object PublicApi {

    private lazy val publicApiConfig = StargazerSettings.gondorConfig.publicApiConfig
    private lazy val publicApiAsyncConfig = publicApiConfig.asyncWorkflowConfig

    lazy val bulkCreateOrders: ZWorkerOptions = ZWorkerOptions.default
      .withMaxConcurrentWorkflowTaskExecutionSize(publicApiAsyncConfig.maxConcurrentTaskExecutionSize)
      .withMaxConcurrentActivityExecutionSize(publicApiAsyncConfig.maxConcurrentActivityExecutionSize)
      .withMaxConcurrentLocalActivityExecutionSize(publicApiAsyncConfig.maxConcurrentTaskExecutionSize)

  }

  object FundSub {

    private val MultipleInvitationMaxConcurrentActivityExecutionSize = 50
    private val MultipleInvitationMaxConcurrentWorkflowTaskExecutionSize = 10

    lazy val multipleInvitationWorkerOptions: ZWorkerOptions = ZWorkerOptions.default
      .withMaxConcurrentWorkflowTaskExecutionSize(MultipleInvitationMaxConcurrentWorkflowTaskExecutionSize)
      .withMaxConcurrentActivityExecutionSize(MultipleInvitationMaxConcurrentActivityExecutionSize)
      .withMaxConcurrentLocalActivityExecutionSize(MultipleInvitationMaxConcurrentActivityExecutionSize)

    private val MultipleDataImportMaxConcurrentActivityExecutionSize = 100
    private val MultipleDataImportMaxConcurrentTaskExecutionSize = 20

    lazy val multipleDataImportWorkerOptions: ZWorkerOptions = ZWorkerOptions.default
      .withMaxConcurrentWorkflowTaskExecutionSize(MultipleDataImportMaxConcurrentTaskExecutionSize)
      .withMaxConcurrentActivityExecutionSize(MultipleDataImportMaxConcurrentActivityExecutionSize)
      .withMaxConcurrentLocalActivityExecutionSize(MultipleDataImportMaxConcurrentActivityExecutionSize)

    private val CountersignMaxConcurrentActivityExecutionSize = 50
    private val CountersignMaxConcurrentWorkflowTaskExecutionSize = 10

    lazy val counterSignWorkerOptions: ZWorkerOptions = ZWorkerOptions.default
      .withMaxConcurrentWorkflowTaskExecutionSize(CountersignMaxConcurrentWorkflowTaskExecutionSize)
      .withMaxConcurrentActivityExecutionSize(CountersignMaxConcurrentActivityExecutionSize)
      .withMaxConcurrentLocalActivityExecutionSize(CountersignMaxConcurrentActivityExecutionSize)

    private val BatchActionMaxConcurrentActivityExecutionSize = 50
    private val BatchActionMaxConcurrentWorkflowTaskExecutionSize = 10

    lazy val batchActionWorkerOptions: ZWorkerOptions = ZWorkerOptions.default
      .withMaxConcurrentWorkflowTaskExecutionSize(BatchActionMaxConcurrentWorkflowTaskExecutionSize)
      .withMaxConcurrentActivityExecutionSize(BatchActionMaxConcurrentActivityExecutionSize)
      .withMaxConcurrentLocalActivityExecutionSize(BatchActionMaxConcurrentActivityExecutionSize)

  }

  object BatchAction {
    private val MaxConcurrentActivityExecutionSize = 50
    private val MaxConcurrentWorkflowTaskExecutionSize = 10

    lazy val workerOptions: ZWorkerOptions = ZWorkerOptions.default
      .withMaxConcurrentWorkflowTaskExecutionSize(MaxConcurrentWorkflowTaskExecutionSize)
      .withMaxConcurrentActivityExecutionSize(MaxConcurrentActivityExecutionSize)
      .withMaxConcurrentLocalActivityExecutionSize(MaxConcurrentActivityExecutionSize)

  }

  object Rag {
    private val IndexDocumentMaxConcurrentActivityExecutionSize = 50
    private val IndexDocumentMaxConcurrentWorkflowTaskExecutionSize = 10

    lazy val indexDocumentOptions: ZWorkerOptions = ZWorkerOptions.default
      .withMaxConcurrentWorkflowTaskExecutionSize(IndexDocumentMaxConcurrentWorkflowTaskExecutionSize)
      .withMaxConcurrentActivityExecutionSize(IndexDocumentMaxConcurrentActivityExecutionSize)
      .withMaxConcurrentLocalActivityExecutionSize(IndexDocumentMaxConcurrentActivityExecutionSize)

    private val BatchIndexDocumentMaxConcurrentWorkflowTaskExecutionSize = 10

    lazy val batchIndexDocumentOptions: ZWorkerOptions = ZWorkerOptions.default
      .withMaxConcurrentWorkflowTaskExecutionSize(BatchIndexDocumentMaxConcurrentWorkflowTaskExecutionSize)

    private val UpdateIndexMaxConcurrentActivityExecutionSize = 5
    private val UpdateIndexMaxConcurrentWorkflowTaskExecutionSize = 2

    lazy val updateIndexOptions: ZWorkerOptions = ZWorkerOptions.default
      .withMaxConcurrentWorkflowTaskExecutionSize(UpdateIndexMaxConcurrentWorkflowTaskExecutionSize)
      .withMaxConcurrentActivityExecutionSize(UpdateIndexMaxConcurrentActivityExecutionSize)
      .withMaxConcurrentLocalActivityExecutionSize(UpdateIndexMaxConcurrentActivityExecutionSize)

  }

}
