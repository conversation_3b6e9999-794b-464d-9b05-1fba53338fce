// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record.model

import zio.{BuildFrom, Cause, Tag, Trace, ZIO}

import anduin.execution.ZIOExecutor
import anduin.fdb.record.model.RecordZStream
import com.anduin.stargazer.service.utils.ZIOUtils

// scalafix:off DisableSyntax.contravariant, DisableSyntax.covariant
opaque type RecordIO[-R, +E, +A] = ZIO[R, E, A]

object RecordIO {

  extension [R, E, A](underlying: RecordIO[R, E, A]) {

    def map[B](f: A => B): RecordIO[R, E, B] = {
      RecordIO.fromZIO(underlying.toZIO.map(f))
    }

    def as[B](b: B): RecordIO[R, E, B] = {
      map(_ => b)
    }

    def flatMap[R1 <: R, E1 >: E, B](f: A => RecordIO[R1, E1, B]): RecordIO[R1, E1, B] = {
      RecordIO.fromZIO(underlying.toZIO.flatMap(a => f(a)))
    }

    def unit: RecordIO[R, E, Unit] = {
      RecordIO.fromZIO(underlying.toZIO.unit)
    }

    def either: RecordIO[R, E, Either[E, A]] = {
      RecordIO.fromZIO(underlying.toZIO.either)
    }

    def combine[R1 <: R, E1 >: E, B](r: RecordIO[R1, E1, B]): RecordIO[R1, E1, (A, B)] = {
      RecordIO.fromZIO {
        underlying.toZIO.zipPar(r)
      }
    }

    def tapErrorCause[R1 <: R, E1 >: E](f: Cause[E] => RecordIO[R1, E1, Any]): RecordIO[R1, E1, A] = {
      RecordIO.fromZIO { underlying.toZIO.tapErrorCause(f) }
    }

    def catchAll[R1 <: R, E2, A1 >: A](h: E => RecordIO[R1, E2, A1]): RecordIO[R1, E2, A1] = {
      RecordIO.fromZIO { underlying.toZIO.catchAll(e => h(e)) }
    }

    def catchAllCause[R1 <: R, E2, A1 >: A](h: Cause[E] => RecordIO[R1, E2, A1]): RecordIO[R1, E2, A1] = {
      RecordIO.fromZIO { underlying.toZIO.catchAllCause(e => h(e)) }
    }

    def catchSome[R1 <: R, E1 >: E, A1 >: A](pf: PartialFunction[E, RecordIO[R1, E1, A1]]): RecordIO[R1, E1, A1] = {
      RecordIO.fromZIO { underlying.toZIO.catchSome(e => pf(e)) }
    }

    def mapError[E2](f: E => E2): RecordIO[R, E2, A] = {
      RecordIO.fromZIO { underlying.toZIO.mapError(f) }
    }

    def andThen[R1 <: R, E1 >: E, B](that: RecordIO[R1, E1, B]): RecordIO[R1, E1, B] = {
      flatMap(_ => that)
    }

    def *>[R1 <: R, E1 >: E, B](that: RecordIO[R1, E1, B]): RecordIO[R1, E1, B] = {
      andThen(that)
    }

    protected[fdb] def toZIO: ZIO[R, E, A] = underlying

  }

  private val parallelism = 64

  private[fdb] val recordIOExecutor = ZIOExecutor.forkJoin("record-io-forkjoin", parallelism)

  private[fdb] val recordIOScheduler = recordIOExecutor.asJava

  private[fdb] lazy val recordIORuntime = ZIOUtils.unsafeBuildRuntimeFromLayer(
    zio.Runtime.setExecutor(recordIOExecutor) ++
      zio.Runtime.setBlockingExecutor(recordIOExecutor)
  )

  private[fdb] val recordLayerScheduler = ZIOExecutor.forkJoin("record-layer-forkjoin", parallelism).asJava

  private[fdb] def fromZIO[R, E, A](effect: ZIO[R, E, A]): RecordIO[R, E, A] = {
    effect.onExecutor(recordIOExecutor)
  }

  given [X, R, E, A, B] => (conversion: Conversion[A, B])
    => Conversion[X => RecordIO[R, E, A], X => RecordIO[R, E, B]] = f => {
    f.andThen(_.map(conversion))
  }

  def unit: RecordIO[Any, Nothing, Unit] = fromZIO(ZIO.unit)

  def none: RecordIO[Any, Nothing, Option[Nothing]] = fromZIO(ZIO.none)

  def succeed[T](t: => T): RecordIO[Any, Nothing, T] = fromZIO(ZIO.succeed(t))

  def fail[E](error: => E): RecordIO[Any, E, Nothing] = fromZIO(ZIO.fail(error))

  def attempt[T](t: => T): RecordIO[Any, Throwable, T] = fromZIO(ZIO.attempt(t))

  def fromOption[E, T](opt: => Option[T], error: => E): RecordIO[Any, E, T] = {
    fromZIO(ZIO.fromOption(opt).orElseFail(error))
  }

  def when[R, E, T](condition: => Boolean)(task: RecordIO[R, E, T]): RecordIO[R, E, Unit] = {
    if (condition) task.unit else unit
  }

  def unless[R, E, T](condition: => Boolean)(task: RecordIO[R, E, T]): RecordIO[R, E, Unit] = {
    when(!condition)(task)
  }

  def validate[R, E](predicate: => Boolean)(ex: => E): RecordIO[R, E, Unit] = {
    unless(predicate)(fail(ex))
  }

  def validateAll[R, K, E](predicates: => Seq[(K, Boolean)])(ex: K => E): RecordIO[R, E, Unit] = {
    traverse(predicates) { (key, predicate) =>
      validate(predicate)(ex(key))
    }.unit
  }

  def validateNot[R, E](predicate: => Boolean)(ex: => E): RecordIO[R, E, Unit] = {
    when(predicate)(fail(ex))
  }

  def validate[R, E, E1 >: E](predicateZIO: RecordIO[R, E, Boolean])(ex: => E1): RecordIO[R, E1, Unit] = {
    predicateZIO.flatMap(predicate => validate(predicate)(ex))
  }

  def validateNot[R, E, E1 >: E](predicateZIO: RecordIO[R, E, Boolean])(ex: => E1): RecordIO[R, E1, Unit] = {
    predicateZIO.flatMap(predicate => validateNot(predicate)(ex))
  }

  // WARNING: We don't recommend use `collectStream`, please use List variants of FDBRecordStore operation for better performance
  def collectStream[R, E, R1 <: R, E1 >: E, T](
    streamTask: RecordIO[R, E, RecordZStream[R1, E1, T]]
  ): RecordIO[R1, E1, List[T]] = {
    RecordIO.fromZIO {
      for {
        stream <- streamTask
        content <- stream.runCollect
      } yield content.toList
    }
  }

  def traverseOption[R, E, A, B](opt: => Option[A])(task: A => RecordIO[R, E, Option[B]]): RecordIO[R, E, Option[B]] = {
    opt.fold[RecordIO[R, E, Option[B]]](RecordIO.succeed(None))(task)
  }

  def traverseOptionUnit[R, E, A, B](opt: => Option[A])(task: A => RecordIO[R, E, B]): RecordIO[R, E, Unit] = {
    opt.fold[RecordIO[R, E, Unit]](RecordIO.unit)(a => task(a).unit)
  }

  def traverse[R, E, A, B, M[+X] <: Iterable[X]]( // scalafix:ok DisableSyntax.covariant
    in: => M[A]
  )(
    f: A => RecordIO[R, E, B]
  )(
    using bf: BuildFrom[M[A], B, M[B]]
  ): RecordIO[R, E, M[B]] = {
    RecordIO.fromZIO {
      ZIO.foreach(in)(r => f(r))
    }
  }

  def traverse[R, E, A, B](
    in: => Option[A]
  )(
    f: A => RecordIO[R, E, B]
  ): RecordIO[R, E, Option[B]] = {
    RecordIO.fromZIO {
      in.fold[ZIO[R, E, Option[B]]](
        ZIO.succeed(Option.empty)
      ) { v =>
        f(v).map(Option(_))
      }
    }
  }

  def parTraverseN[R, E, A, B](
    parallelism: => Int
  )(
    in: => Iterable[A]
  )(
    f: A => RecordIO[R, E, B]
  ): RecordIO[R, E, List[B]] = {
    RecordIO.fromZIO {
      ZIO.foreachPar(in)(a => f(a)).withParallelism(parallelism).map(_.toList)
    }
  }

  def parTraverseFlattenN[R, E, A, B](
    parallelism: => Int
  )(
    in: => Iterable[A]
  )(
    f: A => RecordIO[R, E, Option[B]]
  ): RecordIO[R, E, List[B]] = {
    RecordIO.fromZIO {
      ZIO.foreachPar(in)(a => f(a)).withParallelism(parallelism).map(_.flatten.toList)
    }
  }

  def parTraverseDiscardN[R, E, A](
    parallelism: => Int
  )(
    in: => Iterable[A]
  )(
    f: A => RecordIO[R, E, Any]
  ): RecordIO[R, E, Unit] = {
    RecordIO.fromZIO {
      ZIO.foreachParDiscard(in)(a => f(a)).withParallelism(parallelism)
    }
  }

  def tailRecM[R, E, A, B](a: => A)(f: A => RecordIO[R, E, Either[A, B]]): RecordIO[R, E, B] = {
    RecordIO.fromZIO {
      ZIOUtils.tailRecM(a)(r => f(r))
    }
  }

  def collectAll[R, E, A](in: Seq[RecordIO[R, E, A]]): RecordIO[R, E, Seq[A]] =
    RecordIO.fromZIO {
      ZIO.collectAll(in)
    }

  def iterate[R, E, S](
    initial: => S
  )(
    cont: S => Boolean
  )(
    body: S => RecordIO[R, E, S]
  ): RecordIO[R, E, S] = {
    RecordIO.fromZIO {
      ZIO.iterate(initial)(cont)(s => body(s))
    }
  }

  def service[R: Tag]: RecordIO[R, Nothing, R] = {
    RecordIO.fromZIO {
      ZIO.service[R]
    }
  }

  def logAnnotate[R, E, A](key: => String, value: => String)(task: RecordIO[R, E, A]): RecordIO[R, E, A] = {
    RecordIO.fromZIO {
      ZIO.logAnnotate(key, value)(task)
    }
  }

  def logSpan[R, E, A](label: => String)(task: RecordIO[R, E, A]): RecordIO[R, E, A] = {
    RecordIO.fromZIO {
      ZIO.logSpan(label)(task)
    }
  }

  def logInfo(
    msg: => String
  )(
    using trace: Trace
  ): RecordIO[Any, Nothing, Unit] = {
    RecordIO.fromZIO {
      ZIO.logInfo(msg)
    }
  }

  def logInfoCause(
    cause: Cause[Any]
  )(
    using trace: Trace
  ): RecordIO[Any, Nothing, Unit] = {
    RecordIO.fromZIO {
      ZIO.logInfoCause(cause)
    }
  }

  def logInfoCause(
    msg: => String,
    cause: Cause[Any]
  )(
    using trace: Trace
  ): RecordIO[Any, Nothing, Unit] = {
    RecordIO.fromZIO {
      ZIO.logInfoCause(msg, cause)
    }
  }

  def logError(
    msg: => String
  )(
    using trace: Trace
  ) = {
    RecordIO.fromZIO {
      ZIO.logError(msg)
    }
  }

  def logErrorCause(
    cause: => Cause[Any]
  )(
    using trace: Trace
  ) = {
    RecordIO.fromZIO {
      ZIO.logErrorCause(cause)
    }
  }

  def logErrorCause(
    msg: => String,
    cause: => Cause[Any]
  )(
    using trace: Trace
  ) = {
    RecordIO.fromZIO {
      ZIO.logErrorCause(msg, cause)
    }
  }

  def logWarning(
    msg: => String
  )(
    using trace: Trace
  ) = {
    RecordIO.fromZIO {
      ZIO.logWarning(msg)
    }
  }

  def logWarningCause(
    cause: => Cause[Any]
  )(
    using trace: Trace
  ) = {
    RecordIO.fromZIO {
      ZIO.logWarningCause(cause)
    }
  }

  def logWarningCause(
    msg: => String,
    cause: => Cause[Any]
  )(
    using trace: Trace
  ) = {
    RecordIO.fromZIO {
      ZIO.logWarningCause(msg, cause)
    }
  }

}

// scalafix:on
