// Copyright (C) 2014-2025 Anduin Transactions Inc.

package build.platform.stargazerBuildInfo

import anduin.mill.buildinfo.BuildInfo
import java.time.format.DateTimeFormatter
import java.time.{ZoneOffset, ZonedDateTime}
import mill.*, scalalib.*
import anduin.mill.*
import anduin.build.*
import anduin.mill.utils.*

object `package` extends AnduinCrossPlatformModule with AnduinBuildEnv {

  trait StargazerBuildInfo extends ScalaModule with BuildInfo {

    override def scalacOptions = super.scalacOptions()

    override def buildInfoPackageName = "anduin.buildinfo"

    override def buildInfoMembers = Seq(
      BuildInfo.Value("version", version()),
      BuildInfo.Value("scalaVersion", scalaVersion()),
      BuildInfo.Value("jsResourceSuffix", jsResourceSuffix()),
      BuildInfo.Value("buildEnv", buildEnv().toString),
      BuildInfo.Value("productionMode", productionBuild().toString),
      BuildInfo.Value("buildTime", ZonedDateTime.now(ZoneOffset.UTC).format(DateTimeFormatter.RFC_1123_DATE_TIME)),
      BuildInfo.Value("buildTimeEpochSecond", ZonedDateTime.now(ZoneOffset.UTC).toEpochSecond.toString),
      BuildInfo.Value("gaiaVersion", build_.build.versions_.AnduinGaia.version)
    )

  }

  object jvm extends JvmModule with StargazerBuildInfo {
    override def buildInfoObjectName = "stargazerJvmBuildInfo"
  }

  object js extends JsModule with StargazerBuildInfo {
    override def buildInfoObjectName = "stargazerJsBuildInfo"

    private def gzipPath = Task {
      mill.define.BuildCtx.withFilesystemCheckerDisabled {
        val path = clientDirectory() / AnduinWebModules.Gondor.value
        os.makeDir.all(path)
        path / s"${buildInfoObjectName}${jsResourceSuffix()}.js.gz"
      }
    }

    def jsBuildInfo = Task {
      mill.define.BuildCtx.withFilesystemCheckerDisabled {
        val result = renderKeys(buildInfoMembers())
        val dest = Task.dest / (buildInfoObjectName + jsResourceSuffix() + ".js")
        os.write.over(dest, result.mkString("\n"))
        IOUtils.gzip()(dest, gzipPath())
        PathRef(dest)
      }
    }

    def renderKeys(infoKeysNameAndValues: Seq[BuildInfo.Value]): Seq[String] = {
      Seq(
        s"""(function (global, factory) {
           |    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
           |    typeof define === 'function' && define.amd ? define(factory) :
           |    global.$buildInfoObjectName = factory()
           |}(this, (function () {
           | var $buildInfoObjectName = {
            """.stripMargin
      ) ++
        infoKeysNameAndValues.map(render) ++
        Seq(s"""
               |}
               |return $buildInfoObjectName
               |})))
            """.stripMargin)
    }

    // scalastyle:off multiple.string.literals
    private def render(info: BuildInfo.Value): String = {
      val key = info.key
      val value = "\"" + info.value
        .replace("\\", "\\\\")
        .replace("\n", "\\n")
        .replace("\b", "\\b")
        .replace("\r", "\\r")
        .replace("\t", "\\t")
        .replace("\'", "\\'")
        .replace("\f", "\\f")
        .replace("\"", "\\\"") + "\""
      s"""$key:$value,"""
    }

  }

}
