// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.utils

import scala.util.Try
import java.time.format.DateTimeFormatter
import java.util.TimeZone

import anduin.protobuf.InstantMessage

import scala.jdk.CollectionConverters.*
import java.time.*
import org.apache.commons.lang3.time.DurationFormatUtils

/** Formatter utils to provide the same formatting of date time objects in both front-end and back-end.
  */
object DateTimeUtils {

  val DefaultDateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("MMM d, yyyy") // Oct 19, 2016
  val MonthDayFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("MMMM dd, yyyy")
  val MonthDayFormatter2: DateTimeFormatter = DateTimeFormatter.ofPattern("MMMM dd")
  val MonthDayFormatter3: DateTimeFormatter = DateTimeFormatter.ofPattern("MMM d yyyy")
  // lowercase z means include timezone name eg. UTC
  val MonthDayTimeTimezoneFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("MMMM dd, yyyy 'at' HH:mm z")
  val MonthDayTimeTimezoneFormatter2: DateTimeFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm z")
  val MonthDayTimeWithYearFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy hh:mm a")
  val MonthDayTimeWithoutYearFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("MMM dd, hh:mm a")
  val TimeWithZone: DateTimeFormatter = DateTimeFormatter.ofPattern("hh:mm a z")

  val MonthDayTimeWithoutYearWithoutLeadingZeroFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern(
    "MMM d, h:mm a"
  )

  val TimeAndDateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("hh:mm a, MMM d")
  val Time24hourFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm")
  val Time12hourFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("h:mm a")
  val DateSlashFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy")
  val DayMonthFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("dd MMMM, yyyy")
  val DayMonthShortFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("d MMM, yyyy")
  val DateAndTimeFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("E, MMM d, yyyy 'at' hh:mm a")
  val DateAndTimeFormatter2: DateTimeFormatter = DateTimeFormatter.ofPattern("MMM d, yyyy 'at' h:mm a")
  val DateAndTimeFormatter3: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm a")
  val DatabaseDatetimeFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
  val DatabaseDateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
  val DayOfWeekFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("E dd/MM")
  val LongLocalDatePattern: DateTimeFormatter = DateTimeFormatter.ofPattern("LLL d, yyyy") // May 1, 2016
  val ShortLocalDatePattern: DateTimeFormatter = DateTimeFormatter.ofPattern("LLL d")

  val DateFormats: List[DateTimeFormatter] = List(
    MonthDayFormatter,
    DefaultDateFormatter,
    DateSlashFormatter,
    DayMonthFormatter,
    DayMonthShortFormatter,
    DateTimeFormatter.ofPattern("dd MMM yyyy"),
    DateTimeFormatter.ofPattern("dd MMMM yyyy"),
    DateTimeFormatter.ofPattern("MMMM d, yyyy"),
    DateTimeFormatter.ofPattern("MMM dd, yyyy")
  )

  val SignatureDefaultDateFormat = "MMM d, yyyy"

  /** Format the given date with the given formatter.
    * @param date
    *   the date to format
    * @param formatter
    *   the formatter to be used to format the `date`
    */
  def formatZonedDateTime(date: ZonedDateTime, formatter: DateTimeFormatter): String = {
    date.format(formatter)
  }

  def formatInstant(
    instant: Instant,
    formatter: DateTimeFormatter
  )(
    using zoneId: ZoneId
  ): String = {
    formatZonedDateTime(instant.atZone(zoneId), formatter)
  }

  def formatLocalDate(
    date: LocalDate,
    formatter: DateTimeFormatter
  )(
    using zoneId: ZoneId
  ): String = {
    date.format(formatter.withZone(zoneId))
  }

  def formatZonedDateTimeToUTC(date: ZonedDateTime): String = {
    LocalDateTime.ofInstant(date.toInstant, utcTimezone).format(DatabaseDatetimeFormatter)
  }

  def formatZonedDateTimeToUTCDate(date: ZonedDateTime): String = {
    LocalDateTime.ofInstant(date.toInstant, utcTimezone).format(DatabaseDateFormatter)
  }

  def formatLocalDateToUTC(date: LocalDate): String = {
    date.format(DatabaseDateFormatter)
  }

  def parseDateString(input: String, formats: List[DateTimeFormatter]): Option[LocalDate] = {
    formats.collectFirst {
      Function.unlift { (format: DateTimeFormatter) =>
        Try(LocalDate.parse(input, format)).toOption
      }
    }
  }

  def parseDateTimeString(input: String, format: DateTimeFormatter): Option[LocalDateTime] = {
    Try(LocalDateTime.parse(input, format)).toOption
  }

  def instantMessageToInstant(input: InstantMessage): Instant = Instant.ofEpochSecond(input.seconds)
  def instantToInstantMessage(input: Instant): InstantMessage = InstantMessage(input.getEpochSecond)

  def formatCalendar(
    instant: Instant,
    timezone: ZoneId
  )(
    today: LocalDate = ZonedDateTime.now(timezone).toLocalDate
  ): String = {
    val yesterday = today.minusDays(1)
    val tomorrow = today.plusDays(1)
    val lastWeek = today.minusWeeks(1)
    val nextWeek = today.plusWeeks(1)
    val moment = instant.atZone(timezone).toLocalDate
    val pattern = if (moment.compareTo(today) == 0) {
      "'Today at 'HH:mm"
    } else if (moment.compareTo(yesterday) == 0) {
      "'Yesterday at 'HH:mm"
    } else if (moment == tomorrow) {
      "'Tomorrow at 'HH:mm"
    } else if (lastWeek.compareTo(moment) < 0 && moment.compareTo(today) < 0) {
      "'Last 'eeee' at 'HH:mm"
    } else if (today.compareTo(moment) < 0 && moment.compareTo(nextWeek) < 0) {
      "eeee' at 'HH:mm"
    } else {
      "MM/dd/yyyy"
    }
    formatInstant(instant, DateTimeFormatter.ofPattern(pattern))(
      using timezone
    )
  }

  /** If the date is within one week from now, then show in relative date, else use absolute date with provided pattern
    */
  def formatDateRelativeInOneWeek(
    instant: Instant,
    timezone: ZoneId,
    includePreposition: Boolean
  )(
    today: LocalDate = LocalDate.now(timezone)
  ): String = {
    val moment = instant.atZone(timezone).toLocalDate
    val differentDays = Duration.between(moment.atStartOfDay(), today.atStartOfDay()).toDays
    if (differentDays == 0) {
      "today"
    } else if (differentDays == 1) {
      "yesterday"
    } else if (differentDays < 7) {
      s"$differentDays days ago"
    } else {
      val differentYear = today.getYear != moment.getYear

      val prepositionOpt = if (includePreposition) {
        "on "
      } else {
        ""
      }
      val dateTimeFormatter = if (differentYear) {
        DateTimeFormatter.ofPattern("MMM d, yyyy")
      } else {
        DateTimeFormatter.ofPattern("MMM d")
      }

      prepositionOpt + formatInstant(instant, dateTimeFormatter)(
        using timezone
      )
    }
  }

  /** If the date is within the same year with now, show in MonthDayFormatter2 format, else in MonthDayFormatter format
    */
  def formatMonthDayWithoutTimeWithSameYearHidden(
    instant: Instant,
    timezone: ZoneId
  )(
    today: LocalDate = LocalDate.now(timezone)
  ): String = {
    val moment = instant.atZone(timezone).toLocalDate
    if (moment.getYear < today.getYear) {
      formatInstant(instant, MonthDayFormatter)(
        using timezone
      )
    } else {
      formatInstant(instant, MonthDayFormatter2)(
        using timezone
      )
    }
  }

  /** If the date is within the same year with now, show in DefaultTimeFormatter format without year, else in
    * DefaultTimeFormatter
    */
  def formatDefaultWithSameYearHidden(
    instant: Instant,
    timezone: ZoneId
  )(
    today: LocalDate = LocalDate.now(timezone)
  ): String = {
    val moment = instant.atZone(timezone).toLocalDate
    if (moment.getYear != today.getYear) {
      formatInstant(instant, DefaultDateFormatter)(
        using timezone
      )
    } else {
      formatInstant(instant, DateTimeFormatter.ofPattern("MMM d"))(
        using timezone
      )
    }
  }

  /** If the date is within the same year with now, show in MonthDayTimeFormatter2 format, else in MonthDayTimeFormatter
    * format
    */
  def formatMonthDayWithSameYearHidden(
    instant: Instant,
    timezone: ZoneId
  )(
    today: LocalDate = LocalDate.now(timezone)
  ): String = {
    val moment = instant.atZone(timezone).toLocalDate
    if (moment.getYear < today.getYear) {
      formatInstant(instant, MonthDayTimeWithYearFormatter)(
        using timezone
      )
    } else {
      formatInstant(instant, MonthDayTimeWithoutYearFormatter)(
        using timezone
      )
    }
  }

  def isYearBeforeThisYear(
    instant: Instant,
    timezone: ZoneId
  )(
    today: LocalDate = LocalDate.now(timezone)
  ): Boolean = {
    instant.atZone(timezone).toLocalDate.getYear < today.getYear
  }

  def formatTimeOnCondition(
    instant: Instant,
    timezone: ZoneId,
    condition: Boolean,
    ifTrueFormatter: DateTimeFormatter,
    ifFalseFormatter: DateTimeFormatter
  ): String = {
    if (condition) {
      formatInstant(instant, ifTrueFormatter)(
        using timezone
      )
    } else {
      formatInstant(instant, ifFalseFormatter)(
        using timezone
      )
    }
  }

  val defaultTimezone: ZoneId = ZoneId.systemDefault()

  val losAngelesTimezone: ZoneId = ZoneId.of("America/Los_Angeles")

  val utcTimezone: ZoneId = ZoneId.of("UTC")

  val estTimezone: ZoneId = ZoneId.of("America/New_York")

  def timezoneByMinuteOffset(timezoneOffsetInMinutes: Int): ZoneId =
    ZoneOffset.ofTotalSeconds(timezoneOffsetInMinutes * 60)

  def getTimezone(timezone: Option[ZoneId], shouldUseSystemDefault: Boolean = true): ZoneId = {
    timezone.getOrElse {
      if (shouldUseSystemDefault) defaultTimezone else losAngelesTimezone
    }
  }

  lazy val availableTimezones: List[ZoneId] = {
    ZoneId.getAvailableZoneIds.asScala.toList
      .filterNot(value => value.contains("Etc/") || value.contains("SystemV/"))
      .map(ZoneId.of)
      .sortBy(timezone => getTimezoneOffset(timezone).getTotalSeconds -> timezone.getId)
  }

  def getTimezoneDisplay(timezone: ZoneId): String = {
    s"(UTC${getTimezoneOffset(timezone).getId}) ${timezone.getId.replace('_', ' ')}"
  }

  def getTimezoneOffset(timezone: ZoneId, instantOpt: Option[Instant] = None): ZoneOffset = {
    timezone.getRules.getOffset(instantOpt.getOrElse(Instant.now))
  }

  def fromLocalDateToInstant(localDate: LocalDate, zoneId: ZoneId = TimeZone.getTimeZone("GMT").toZoneId): Instant =
    localDate.atStartOfDay(zoneId).toInstant

  def fromInstantToLocalDate(instant: Instant, zoneId: ZoneId = TimeZone.getTimeZone("GMT").toZoneId): LocalDate = {
    instant.atZone(zoneId).toLocalDate
  }

  def formatDurationHMS(duration: Duration): String = {
    DurationFormatUtils.formatDurationHMS(duration.toMillis)
  }

}
