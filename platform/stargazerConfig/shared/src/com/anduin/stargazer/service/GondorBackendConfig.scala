// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.model.codec.FiniteDurationCodecs.given
import anduin.model.common.user.UserInfo
import com.anduin.stargazer.service.GondorBackendConfig.*
import com.anduin.stargazer.service.GondorBackendConfig.EmailConfig.EmailDomainType
import io.circe.Codec
import squants.information.Information

import scala.concurrent.duration.{Duration, FiniteDuration}

/** Configurations for backend services.
  */
final case class GondorBackendConfig(
  server: ServerConfig,
  account: AccountConfig,
  botUser: AnduinBotAccountConfig,
  portalAdmin: PortalAdminConfig,
  executiveAdmin: ExecutiveAdminConfig,
  fundSubBot: FundSubBot,
  dataRoomIntegrationBot: BotUser,
  dataRoomBot: DataRoomBot,
  fundDataBot: FundDataBot,
  adminEntity: AdminEntity,
  anduinInternalEntities: String,
  seedEntities: List[SeedEntity],
  seedUsers: List[SeedUserConfig],
  seedFeatureIds: List[String],
  keycloak: KeycloakConfig,
  jwt: JwtConfig,
  ses: SesConfig,
  email: EmailConfig,
  fundSubSupportingDocUploadDigest: FundSubSupportingDocUploadDigest,
  textract: TextractConfig,
  fundSubSupportingReadyForReviewDigest: FundSubSupportingReadyForReviewDigest,
  aws: AwsConfig,
  liteLlmConfig: LiteLlmConfig,
  temporalCron: TemporalCronConfig,
  redirect: RedirectConfig,
  actionToken: ActionTokenConfig,
  remoteRepl: RemoteReplConfig,
  servicesTimeout: ServicesTimeout,
  googleCloudConfig: GoogleCloudConfig,
  dmsConfig: DmsConfig,
  serverlessConfig: ServerlessConfig,
  issueTrackerConfig: IssueTrackerConfig,
  foundationDbConfig: FoundationDbConfig,
  foundationDbReadOnlyConfig: FoundationDbConfig,
  boxConfig: BoxConfig,
  driveConfig: DriveConfig,
  sharePointConfig: SharePointConfig,
  sftpServerConfig: SftpServerConfig,
  dropboxConfig: DropboxConfig,
  notificationConfig: NotificationConfig,
  dealsDashboardConfig: DealsDashboardConfig,
  limiterConfig: LimiterConfig,
  analyticsConfig: AnalyticsConfig,
  customDomainConfig: CustomDomainConfig,
  shortenUrlConfig: ShortenUrlConfig,
  turnstileBackendConfig: TurnstileBackendConfig,
  oauth2Config: Oauth2Config,
  encryptionConfig: EncryptionConfig,
  fundSubEmailConfig: FundSubEmailConfig,
  protectedLinkConfig: ProtectedLinkConfig,
  timescaleConfig: PostgresqlConfig,
  zapierConfig: ZapierConfig,
  temporalConfig: TemporalConfig,
  fundSubSimulatorConfig: FundSubSimulatorConfig,
  dataRoomSimulatorConfig: DataRoomSimulatorConfig,
  formConfig: FormConfig,
  dgraphConfig: DgraphConfig,
  ontologyConfig: OntologyConfig,
  fusekiConfig: FusekiConfig,
  dataLakeConfig: DataLakeConfig,
  fundDataConfig: FundDataConfig,
  taskRequestConfig: TaskRequestConfig,
  enterpriseLoginConfig: EnterpriseLoginConfig,
  otpAuthenticationConfig: OTPAuthenticationConfig,
  kafkaConfig: KafkaConfig,
  pulsarConfig: PulsarConfig,
  twilioConfig: TwilioConfig,
  tykConfig: TykConfig,
  cassandraConfig: CassandraConfig,
  systemAuditLogConfig: SystemAuditLogConfig,
  rebacConfig: RebacConfig,
  fundSubLpInvitedDigestConfig: FundSubLpInvitedDigestConfig,
  edgedbConfig: SqlConfig,
  tidbConfig: SqlConfig,
  debugConfig: DebugConfig,
  investorGroupConfig: InvestorGroupConfig,
  tracingConfig: TracingConfig,
  environmentConfig: EnvironmentConfig,
  globalSignConfig: GlobalSignConfig,
  dataPipelineConfig: DataPipelineConfig,
  svixWebhookConfig: SvixWebhookConfig,
  docusignIntegrationConfig: DocusignIntegrationConfig,
  signatureRequestStatusChangeEventConfig: SignatureRequestStatusChangeEventConfig,
  actionLoggerConfig: ActionLoggerConfig,
  natsConfig: NatsConfig,
  multiRegionConfig: MultiRegionConfig,
  getFeedbackBackendConfig: GetFeedbackBackendConfig,
  memcachedConfig: MemcachedConfig,
  prismaticConfig: PrismaticConfig,
  globalDatabaseConfig: GlobalDatabaseConfig,
  integPlatformConfig: IntegPlatformConfig,
  zotRegistryConfig: ZotRegistryConfig,
  riaEntityConfig: RiaEntityConfig,
  fundSubPrismaticConfig: FundSubPrismaticConfig,
  elasticsearch: ElasticSearchConfig,
  dataExtractStatusChangeEventConfig: DataExtractStatusChangeEventConfig,
  oneTimeLinkConfig: OneTimeLinkConfig,
  vespaConfig: VespaConfig,
  dataRoomSemanticSearchConfig: DataRoomSemanticSearchConfig
)

object GondorBackendConfig {

  final case class ServerConfig(
    baseUrl: String,
    portalUrl: String,
    deployment: String,
    serviceFeatures: String,
    maxNumConnections: Int,
    maxRequestLengthInMbs: Long,
    idleTimeout: FiniteDuration,
    requestTimeout: FiniteDuration,
    graphqlMaxQueryDepth: Int
  )

  object ServerConfig {
    given Codec.AsObject[ServerConfig] = deriveCodecWithDefaults
  }

  final case class AccountConfig(
    token: AccountToken,
    cookie: LoginCookieConfig,
    accountTokenTimeoutConfig: AccountTokenTimeoutConfig,
    shortSessionDuration: FiniteDuration
  )

  final case class AccountToken(
    shouldRefreshBefore: FiniteDuration,
    duration: Duration
  )

  final case class LoginCookieConfig(
    name: String,
    expire: FiniteDuration,
    clientExpire: FiniteDuration
  )

  final case class AccountTokenTimeoutConfig(
    verifyEmailTokenTimeout: FiniteDuration,
    resetPasswordTokenTimeout: FiniteDuration,
    linkAccountTokenTimeout: FiniteDuration
  )

  // TODO: Support retrying with exponential backoff?
  final case class KeycloakClientConfig(
    host: String,
    ssl: Boolean,
    port: Int,
    realm: String = KeycloakConfig.DEFAULT_REALM,
    realmToken: String
  )

  final case class KeycloakConfig(
    admin: KeycloakClientConfig
  )

  object KeycloakConfig {
    val DEFAULT_REALM = "anduin"
  }

  final case class AnduinBotAccountConfig(
    user: String,
    password: String,
    userInfo: UserInfo
  )

  final case class PortalAdminConfig(
    user: String, // email.
    password: String,
    userInfo: UserInfo
  )

  final case class ExecutiveAdminConfig(
    user: String,
    password: String,
    userInfo: UserInfo
  )

  final case class FundSubBot(
    user: String,
    password: String,
    userInfo: UserInfo
  )

  final case class DataRoomBot(
    user: String,
    password: String,
    userInfo: UserInfo
  )

  final case class FundDataBot(
    user: String,
    password: String,
    userInfo: UserInfo
  )

  final case class ActionLoggingBot(
    user: String,
    password: String,
    userInfo: UserInfo
  )

  // Common bot user struct
  final case class BotUser(
    user: String,
    password: String,
    userInfo: UserInfo
  )

  final case class AdminEntity(
    entityId: String
  )

  final case class SeedEntity(
    entityId: String,
    name: String,
    alias: String,
    emailDomains: List[String],
    entityType: String
  )

  final case class SqlUser(
    user: String,
    password: String
  )

  final case class SeedUserConfig(
    email: String,
    password: Option[String],
    userInfo: UserInfo,
    userType: Option[Int],
    entityIds: Seq[String] = Seq.empty
  )

  final case class TemporalCronConfig(
    workflowTaskTimeout: FiniteDuration,
    workflowRunTimeout: FiniteDuration,
    hourlySchedule: String,
    firstMinuteOfEveryHourSchedule: String,
    dailySchedule: String,
    weeklySchedule: String,
    cron10MinSchedule: String
  )

  final case class DigestConfig(
    sendingTime: String,
    gracePeriodMinutes: Long,
    timeout: FiniteDuration
  )

  final case class DataRoomConfig(digest: DigestConfig)

  final case class JwtConfig(
    hmacKey: String,
    rsaKeyMapFile: Option[String],
    defaultKid: String
  )

  final case class SesConfig(
    accessKeyId: String,
    secretAccessKey: String,
    configurationSet: String,
    s3Bucket: String,
    sqsReceiveQueue: String,
    sqsEventQueue: String,
    awsAccountId: String,
    region: String,
    s3CredentialConfig: S3CredentialConfig,
    connectionTimeout: FiniteDuration,
    sqsQueueWaitTime: FiniteDuration,
    sqsMaxBufferSize: Int,
    sqsMaxBatchSize: Int,
    sqsVisibilityTimeout: FiniteDuration
  )

  final case class FundSubSupportingDocUploadDigest(
    kafkaTopic: String,
    kafkaWindowedTopic: String,
    windowLength: FiniteDuration
  )

  final case class FundSubSupportingReadyForReviewDigest(
    kafkaTopic: String,
    kafkaWindowedTopic: String,
    windowLength: FiniteDuration
  )

  final case class EmailConfig(
    private val highEngagementDomain: String,
    private val standardDomain: String,
    generation: EmailConfig.Generation,
    sending: EmailConfig.Sending,
    receiving: EmailConfig.Receiving,
    threadTagging: EmailConfig.ThreadTagging,
    inboxEvent: EmailConfig.InboxEvent
  ) {

    def getDomain(domainType: EmailDomainType): String = {
      domainType match {
        case EmailDomainType.Standard       => standardDomain
        case EmailDomainType.HighEngagement => highEngagementDomain
      }
    }

    def getDomain(customDomain: Either[EmailDomainType, String]): String = {
      customDomain.fold(
        {
          case EmailDomainType.Standard       => standardDomain
          case EmailDomainType.HighEngagement => highEngagementDomain
        },
        identity
      )
    }

    def getAllDomains: Set[String] = EmailDomainType.values.map(getDomain).toSet
  }

  object EmailConfig {

    enum EmailDomainType {
      case Standard, HighEngagement
    }

    final case class Generation(
      timeout: FiniteDuration,
      retry: RetryConfig
    )

    final case class Sending(
      timeout: FiniteDuration,
      retry: RetryConfig,
      kafkaTopic: String,
      highEngagementKafkaTopic: String,
      immediatelyKafkaTopic: String,
      disableSendingEmail: Boolean,
      throttleMessagesPerSecond: Int,
      throttleOneMessagePerDuration: FiniteDuration,
      dynamoDbStatusTable: String,
      statusFetchCacheDuration: FiniteDuration,
      statusFetchMaxDuration: FiniteDuration
    )

    final case class Receiving(
      botName: String,
      receivingEmailKafkaTopic: String,
      taggedEmailKafkaTopic: String,
      emailEventKafkaTopic: String
    )

    final case class ThreadTagging(
      consumerGroup: String,
      deadLetterTopic: String,
      deadLetterConsumerGroup: String
    )

    final case class InboxEvent(
      consumerGroup: String,
      deadLetterTopic: String,
      deadLetterConsumerGroup: String
    )

  }

  final case class AwsConfig(
    S3: AwsS3Config,
    dynamoDbConfig: DynamoDbConfig,
    cloudwatchLogConfig: CloudwatchLogConfig,
    bedrockConfig: BedrockConfig
  )

  final case class AwsS3Config(
    bucket: String,
    batchDownloadBucket: String,
    publicBucket: String,
    formTemplateBucket: String,
    dataExportBucket: String,
    formStorageBucket: String,
    emailStorageBucket: String,
    resourcesBucket: String,
    tempUploadBucket: String,
    webBuilderBucket: String,
    s3CredentialConfig: S3CredentialConfig,
    connectionTimeout: FiniteDuration,
    chunkSize: Information,
    cloudFrontConfig: CloudFrontConfig,
    batchDownloadPrefix: String,
    cacheConfig: CacheConfig,
    annotationStorageVersion: Int
  )

  final case class S3CredentialConfig(
    accessKeyId: String,
    secretAccessKey: String,
    region: String
  )

  final case class TextractConfig(
    isEnabled: Boolean,
    snsConfig: SnsConfig,
    awsAccountId: String,
    sqsEventQueue: String,
    credentialConfig: TextractCredentialConfig,
    outputConfig: TextractOutputConfig,
    kafkaConfig: TextractKafkaConfig,
    useApi: Boolean
  )

  final case class SnsConfig(
    topicArn: String,
    roleArn: String
  )

  final case class TextractCredentialConfig(
    accessKeyId: String,
    secretAccessKey: String,
    region: String
  )

  final case class TextractOutputConfig(
    s3Bucket: Option[String],
    s3Prefix: Option[String]
  )

  final case class TextractKafkaConfig(
    eventTopic: String
  )

  final case class CloudFrontConfig(
    cloudFrontEnabled: Boolean,
    domain: String,
    keyPairId: String,
    privateKeyValue: String
  )

  final case class DynamoDbConfig(
    accessKeyId: String,
    secretAccessKey: String,
    region: String
  )

  final case class CloudwatchLogConfig(
    accessKeyId: String,
    secretAccessKey: String,
    region: String
  )

  final case class BedrockConfig(
    accessKeyId: String,
    secretAccessKey: String,
    region: String
  )

  final case class LiteLlmConfig(
    apiKey: String,
    apiBase: String
  )

  final case class ConfigException(e: String) extends RuntimeException(e)

  final case class RedirectConfig(duration: Duration)

  final case class ActionTokenConfig(
    temporaryTokenDuration: Duration,
    autoLoginTokenDuration: Duration
  )

  final case class RemoteReplConfig(
    isEnabled: Boolean,
    port: Int
  )

  final case class DmsConfig(
    useGraphDB: Boolean,
    downloadKafkaTopic: String
  )

  final case class ServerlessConfig(
    serverlessRegionConfig: ServerlessRegionConfig,
    serverlessSqsConfig: ServerlessSQSConfig,
    serverlessFunctions: ServerlessFunctions
  )

  final case class ServerlessRegionConfig(
    awsAccountId: String,
    accessKeyId: String,
    secretAccessKey: String,
    region: String,
    bucket: String,
    maxConcurrency: Int,
    readTimeout: FiniteDuration
  )

  final case class ServerlessFunctionVersion(
    ap: String,
    us: String,
    eu: String
  )

  final case class ServerlessFunctionConfig(
    parallelism: Int,
    overrideRegion: Option[String] = None,
    version: ServerlessFunctionVersion,
    serverlessFunction: String,
    executionTimeout: FiniteDuration
  )

  final case class ServerlessSQSConfig(
    queueName: String,
    waitTime: FiniteDuration,
    maxBufferSize: Int,
    maxBatchSize: Int,
    visibilityTimeout: FiniteDuration
  )

  final case class ServerlessFunctions(
    libreOffice: ServerlessFunctionConfig,
    tesseract: ServerlessFunctionConfig,
    compressUtils: ServerlessFunctionConfig,
    htmlToPdf: ServerlessFunctionConfig,
    rclone: ServerlessFunctionConfig,
    mupdf: ServerlessFunctionConfig,
    signPdf: ServerlessFunctionConfig,
    loki: ServerlessFunctionConfig,
    ffprobe: ServerlessFunctionConfig,
    docusign: ServerlessFunctionConfig,
    textractUtilities: ServerlessFunctionConfig,
    computePageSimilarity: ServerlessFunctionConfig,
    watermarkGenerator: ServerlessFunctionConfig,
    sha256Compute: ServerlessFunctionConfig,
    exportCsvFilesToExcel: ServerlessFunctionConfig,
    fillPdf: ServerlessFunctionConfig,
    formDataUtils: ServerlessFunctionConfig,
    formMatchingUtils: ServerlessFunctionConfig,
    catalaRunner: ServerlessFunctionConfig,
    pdfAnnotationRunner: ServerlessFunctionConfig,
    formModule: ServerlessFunctionConfig,
    mtlsCA: ServerlessFunctionConfig,
    bedrock: ServerlessFunctionConfig,
    docSplit: ServerlessFunctionConfig,
    formDataExtraction: ServerlessFunctionConfig,
    generateSchema: ServerlessFunctionConfig,
    ipWhitelist: ServerlessFunctionConfig,
    analyzePdfAnnotations: ServerlessFunctionConfig,
    drawAnnotationBoxes: ServerlessFunctionConfig,
    extractKeywords: ServerlessFunctionConfig
  )

  final case class IssueTrackerConfig(
    defaultAccessibleDomains: String,
    permissionIntegrityCheckInterval: FiniteDuration
  )

  final case class FoundationDbConfig(
    host: String,
    port: Int,
    description: String,
    localTempFileName: String,
    localTempFileExtension: String,
    localTempDirectory: String,
    verboseTraceEvent: Boolean,
    traceEventSampleRate: Int
  )

  final case class BoxConfig(accessToken: String)

  final case class DropboxConfig(accessToken: String)

  final case class DriveConfig(key: String)

  final case class SharePointConfig(
    username: String,
    password: String,
    tenant: String,
    clientId: String,
    clientSecret: String,
    scope: String,
    encryptedPass: String // Specific encrypted password for rclone
  )

  final case class SftpServerConfig(
    publicKey: String,
    privateKey: String
  )

  final case class NotificationConfig(kafkaTopic: String)

  final case class DealsDashboardConfig(maxDealsPerPage: Int)

  object DealsDashboardConfig {
    given Codec.AsObject[DealsDashboardConfig] = deriveCodecWithDefaults
  }

  final case class FundSubSimulatorConfig(
    inviteDemoOrdersTopic: String,
    privateToken: String
  )

  final case class DataRoomSimulatorConfig(
    privateToken: String
  )

  final case class OauthConfig(
    baseUrl: String,
    authorizationUrl: String,
    clientId: String,
    clientSecret: String,
    redirectUri: String,
    tokenEndpoint: String,
    userInfoEndpoint: String,
    stateTimeout: FiniteDuration
  )

  final case class LimiterConfig(
    hitCount: Int,
    duration: FiniteDuration,
    maxCount: Int
  )

  final case class AnalyticsConfig(
    s3Bucket: String,
    s3Prefix: String,
    exporterConfig: AnalyticsExporterConfig
  )

  final case class AnalyticsExporterConfig(
    internalQueueSize: Int,
    recordChunkSize: Int
  )

  final case class CustomDomainConfig(
    isEnabled: Boolean,
    seedCustomDomains: String,
    secretTargetHeaderValue: String
  )

  final case class ShortenUrlConfig(
    salt: String,
    offset: Int,
    length: Int
  )

  final case class TurnstileBackendConfig(
    invisibleSecret: String,
    visibleSecret: String
  )

  final case class Oauth2Config(
    stateTimeout: FiniteDuration,
    clients: List[Oauth2ClientConfig],
    enableSkipLinkAccount: Boolean
  )

  final case class Oauth2ClientConfig(
    name: String,
    clientId: String,
    secret: String
  )

  final case class ServicesTimeout(
    default: FiniteDuration,
    createTransaction: FiniteDuration,
    termSheetGeneration: FiniteDuration,
    stage: FiniteDuration,
    fileServiceJvmApi: FiniteDuration,
    fileUploader: FiniteDuration,
    foundationDb: FiniteDuration,
    dataRoomLongTask: FiniteDuration
  )

  final case class GoogleCloudConfig(bigQueryConfig: Option[BigQueryConfig])

  final case class BigQueryConfig(
    credentialFile: String,
    projectId: String,
    datasetId: String
  )

  final case class EncryptionConfig(
    accessKeyId: String,
    secretAccessKey: String,
    // KMS Customer Master Key (CMK)
    masterKeyKMSArn: String,
    // Data Encryption Key (DEK) encrypted by CMK.
    // To generate a new key, use AWS CLI:
    //   $ aws kms generate-data-key --key-id {{CMK-key-id}}
    encryptedDataKeyBlob: String
  )

  final case class FundSubEmailConfig(
    consumerGroup: String,
    deadLetterTopic: String,
    deadLetterConsumerGroup: String
  )

  final case class ProtectedLinkConfig(
    publicPasswordPassedTokenDuration: FiniteDuration
  )

  final case class TemporalWorkerMonitor(
    isEnabled: Boolean,
    checkInterval: FiniteDuration,
    maxAge: FiniteDuration
  )

  final case class TemporalConfig(
    host: String,
    port: Int,
    namespace: String,
    retentionPeriodInDays: Long,
    workerMonitor: TemporalWorkerMonitor
  )

  final case class PostgresqlConfig(
    host: String,
    port: Int,
    username: String,
    password: String,
    databaseName: String,
    concurrentSessions: Int
  )

  final case class ZapierConfig(
    kafkaTopic: String,
    retryMax: Int
  )

  final case class CacheConfig(
    capacity: Int,
    timeToLive: FiniteDuration
  )

  final case class FormConfig(
    lockTimeout: FiniteDuration,
    sharedSecret: String,
    cacheConfig: CacheConfig,
    formDiffV1KafkaTopic: String,
    formDiffV2KafkaTopic: String
  )

  final case class DgraphConfig(
    httpEndpoint: String,
    grpcEndpoint: String
  ) {
    val graphQLEndpoint: String = s"$httpEndpoint/graphql"
    val adminEndpoint: String = s"$httpEndpoint/admin"
  }

  final case class FusekiConfig(
    endpoint: String
  )

  final case class OntologyConfig(
    isEnabled: Boolean
  )

  final case class DataLakeConfig(
    fundSubDataLakeIngestionTopic: String
  )

  final case class FundDataConfig(
    eventTopic: String
  )

  final case class TaskRequestConfig(
    eventTopic: String
  )

  final case class EnterpriseLoginConfig(
    samlConfig: SamlConfig
  )

  final case class SamlConfig(
    entityId: String,
    assertionEncryptionPrivateKey: String,
    assertionEncryptionPublicKey: String
  )

  final case class OTPAuthenticationConfig(
    codeLength: Int,
    maxAttempt: Int,
    loginEmailAddress: String,
    receivingKafkaConsumerGroup: String
  )

  final case class KafkaConfig(
    bootstrapServer: String
  )

  final case class PulsarConfig(
    bootstrapServer: String
  )

  final case class TwilioConfig(
    accountSid: String,
    messageServiceId: String,
    keySid: String,
    keySecret: String,
    kafkaTopic: String
  )

  final case class TykClientConfig(
    host: String,
    ssl: Boolean,
    port: Int,
    secret: String
  )

  final case class TykConfig(
    admin: TykClientConfig,
    http: TykClientConfig
  )

  final case class CassandraConfig(
    host: String,
    port: Int,
    replication: Int
  )

  final case class SystemAuditLogConfig(
    enable: Boolean,
    logGroup: String
  )

  final case class RebacConfig(
    openFgaConfig: OpenFgaConfig,
    openFgaFundConfig: OpenFgaConfig
  )

  final case class OpenFgaConfig(
    host: String,
    port: Int
  )

  final case class SqlConfig(
    host: String,
    port: Int,
    database: String,
    user: SqlUser
  )

  final case class FundSubLpInvitedDigestConfig(
    inputTopic: String,
    outputTopic: String,
    defaultTimeZone: String,
    advanceInterval: FiniteDuration,
    windowLength: FiniteDuration
  )

  final case class DebugConfig(
    token: String
  )

  final case class InvestorGroupConfig(
    investorGroupUpdateEventKafkaTopic: String
  )

  final case class TracingConfig(
    isEnabled: Boolean,
    traceRootSpanOnly: Boolean,
    collectorSchema: String,
    collectorEndpoint: String,
    collectorPort: Int
  )

  final case class EnvironmentConfig(
    fallbackSubdomain: String,
    internalSubdomains: String,
    externalTargetDomain: String
  )

  final case class GlobalSignConfig(
    baseUrl: String,
    apiKey: String,
    apiSecret: String
  )

  final case class DataPipelineConfig(
    enableDataIngest: Boolean,
    enableDataTransform: Boolean,
    enableDataCorrectionCron: Boolean,
    correctEventPerCronExecution: Int
  )

  final case class SvixWebhookConfig(
    enableWebhook: Boolean,
    svixToken: String,
    svixHost: String,
    svixPort: Int,
    challengeHeader: String,
    challengeKeyLength: Int
  )

  final case class DocusignIntegrationConfig(
    webhookHandlerTopic: String,
    webhookBaseUrl: String,
    canUseDocusignProduction: Boolean,
    demoAppConfig: DocusignAppConfig,
    productionAppConfig: DocusignAppConfig
  )

  final case class DocusignAppConfig(
    docusignOAuthBasePath: String,
    docusignApiBasePath: String,
    integrationKey: String,
    rsaPrivateKey: String,
    apiAccountId: String,
    apiUserId: String,
    hmacKey: String,
    phoneOtpConfigId: String,
    cerityApiUserId: String,
    idVerificationWorkflowId: String
  )

  final case class SignatureRequestStatusChangeEventConfig(
    topic: String
  )

  final case class ActionLoggerConfig(
    isEnabled: Boolean,
    flushInterval: Duration,
    bufferCapacity: Int
  )

  final case class NatsConfig(
    rpcEndpoint: String,
    wsEndpoint: String,
    accountSeed: String
  )

  final case class MultiRegionConfig(
    sharedKey: String,
    loginTokenDuration: FiniteDuration
  )

  final case class GetFeedbackBackendConfig(
    cspHash: String
  )

  final case class MemcachedConfig(
    host: String,
    port: Int
  )

  final case class PrismaticConfig(
    organizationId: String,
    privateKey: String, // Private Key is used to generate JWT for embedded Prismatic
    refreshToken: String // Refresh Token is used to generate authorization token for Prismatic GraphQL API
  )

  final case class GlobalDatabaseConfig(
    writeDataSource: PostgresqlConfig,
    readDataSource: PostgresqlConfig
  )

  final case class IntegPlatformConfig(
    messageReceiverHook: String,
    headerApiKey: String
  )

  final case class ZotRegistryConfig(
    host: String,
    username: String,
    password: String,
    repoPrefix: String
  )

  final case class RiaEntityConfig(
    eventTopic: String
  )

  final case class FundSubPrismaticConfig(
    requestDemoEndpoint: String,
    apiKey: String
  )

  final case class ElasticSearchConfig(
    scheme: String,
    host: String,
    port: Int
  )

  final case class DataExtractStatusChangeEventConfig(
    topic: String
  )

  final case class OneTimeLinkConfig(
    duration: FiniteDuration
  )

  final case class VespaConfig(
    scheme: String,
    host: String,
    port: Int,
    namespace: String
  )

  final case class DataRoomSemanticSearchConfig(
    isEnabled: Boolean
  )

}
