# default configuration layer

networkDnsSuffix = ".gondor"
networkDnsSuffix = ${?STARGAZER_K8S_SVC_SUFFIX}

stargazer {

  svcName = "gondor-all-in-one"
  svcName = ${?STARGAZER_SVC_NAME}
  hostname = "0.0.0.0"
  port = 8080
  port = ${?API_PORT}
  internalPort = 4949
  internalPort = ${?INTERNAL_PORT}
  endpointHostname = "localhost"
  endpointHostname = ${?STARGAZER_ENDPOINT_HOSTNAME}
  timeout = 30 seconds
  timeout = ${?STARGAZER_TIMEOUT}
  staticWeb= "target/build/main/web/"
  staticWeb= ${?STARGAZER_STATICWEB}
  enableMetric = true

  # common config for both front-end and back-end
  commonConfig {
    s3Config {
      linkExpiration = 600 seconds
      publicApiLinkExpiration = 1 days
    }

    pageLoginSessionDuration {
      defaultDuration = 24 hours
    }

    captchaCommonConfig {
      isEnabled = false
      isEnabled = ${?STARGAZER_SERVICES_CAPTCHA_ENABLED}
      bypassDuration = 365 days
    }

    gdprCommonConfig {
      cookieConsentCommonConfig {
        name = "stargazer_cookie_consent"
        expires = 365 days
      }
    }

    otpAuthenticationCommonConfig {
      requestDelay = 30 seconds
      codeTimeout = 30 minutes
      enableRevertOTP = false
      enableRevertOTP = ${?STARGAZER_SERVICES_OTP_AUTHENTICATION_ENABLE_REVERT_OTP}
      revertOTPCodeTimeout = 24 hours
    }

    rememberEmailCookieConfig {
      name = "stargazer_remember_email"
      expire = 30 days
    }

    multiTenantConfig {
      tenantSuffix = ""
      tenantSuffix = ${?STARGAZER_SERVICES_TENANT_SUFFIX}
    }

    multiRegionConfig {
      isSecondaryRegion = false
      isSecondaryRegion = ${?STARGAZER_SERVICES_MULTI_REGION_ENABLE_SECONDARY_REGION}
      mainRegionEndpoint = "http://localhost:8080"
      mainRegionEndpoint = ${?STARGAZER_SERVICES_MULTI_REGION_MAIN_REGION_ENDPOINT}
      regionCode = "us"
      regionCode = ${?STARGAZER_SERVICES_MULTI_REGION_REGION_CODE}
      regionName = "US"
      regionName = ${?STARGAZER_SERVICES_MULTI_REGION_REGION_NAME}
    }

    prismaticConfig {
      jwtTimeToLive = 4 hour
      organizationUrl = "https://integration-platform.anduin.app"
      organizationUrl = ${?STARGAZER_PRISMATIC_ORG_URL}
    }

    documentServiceConfig {
      maxFileSizeInMbs = 1024
    }

    fundSubSchwabSignatureIntegrationConfig {
      schwabCarbonCopyEmail = "<EMAIL>"
      schwabCarbonCopyEmail = ${?SCHWAB_CARBON_COPY_EMAIL}
    }

    cueModuleConfig {
      commonDataTypes {
        modulePath = "public.anduintransact.com/acs_root.cue:v24.0.0"
        packagePath = ".:data_types_final"
      }
      softValidationDataTypes {
        modulePath = "public.anduintransact.com/acs_root.cue:v24.0.0"
        packagePath = ".:data_types_with_soft_validation"
      }
      tableCommonSchema {
        modulePath = "public.anduintransact.com/acs_table.cue:v26.0.0"
        packagePath = ".:table_common_schema"
      }
      fundsubTypes {
        modulePath = "public.anduintransact.com/acs_fundsub.cue:v0.0.0"
        packagePath = ".:types"
      }
      dataextractCommonSchema {
        modulePath = "public.anduintransact.com/acs_dataextract.cue:v19.0.0"
        packagePath = ".:data_extract_common_schema"
      }
      dataLayerCommonSchema {
        modulePath = "public.anduintransact.com/acs_datalayer.cue:v0.0.0"
        packagePath = ".:data_layer_common"
      }
    }
  }

  # config for the backend
  backendConfig {

    # gondor server
    gondor {
      gondor-blocking-thread-pool-dispatcher {
        # Dispatcher is the name of the event-based dispatcher
        type = Dispatcher
        # What kind of ExecutionService to use
        executor = "thread-pool-executor"
        # Configuration for the thread pool
        thread-pool-executor {
          # minimum number of threads to cap factor-based core number to
          core-pool-size-min = 2
          # No of core threads ... ceil(available processors * factor)
          core-pool-size-factor = 2.0
          # maximum number of threads to cap factor-based number to
          core-pool-size-max = 10
        }
        # Throughput defines the maximum number of messages to be
        # processed per actor before the thread jumps to the next actor.
        # Set to 1 for as fair as possible.
        throughput = 100
      }
    }

    server {
      baseUrl = ""
      baseUrl = ${?STARGAZER_SERVICES_BASE_URL}
      portalUrl = "http://gondor-local.io:8080"
      portalUrl = ${?STARGAZER_SERVICES_PORTAL_URL}
      deployment = "gondor-local-dev"
      deployment = ${?STARGAZER_SERVICES_DEPLOYMENT}
      serviceFeatures = "web0, adm0, api0, init, kafk, temp, cron"
      # SimulatorApp
//      serviceFeatures = "web0, adm0, api0, init, kafk, temp, drsb, fssb, lpsb, fdsb, cron"
      serviceFeatures = ${?STARGAZER_SERVICES_FEATURES}

      maxNumConnections = 1024
      maxRequestLengthInMbs = 500
      idleTimeout = 360 seconds
      requestTimeout = 360 seconds
      graphqlMaxQueryDepth = 16
    }

    dealsDashboardConfig {
      maxDealsPerPage = 10
    }

    # account service
    account {
      token {
        shouldRefreshBefore = 30 minutes
        duration = 2 hours
      }
      cookie {
        name = "stargazer_cookie"
        expire = 1 days
        expire = ${?STARGAZER_SERVICES_COOKIE_EXPIRE}
        clientExpire = 15 days
      }
      accountTokenTimeoutConfig {
        verifyEmailTokenTimeout = 30 days
        verifyEmailTokenTimeout = ${?STARGAZER_SERVICES_VERIFY_EMAIL_TOKEN_TIMEOUT}
        resetPasswordTokenTimeout = 1 days
        resetPasswordTokenTimeout = ${?STARGAZER_SERVICES_RESET_PASSWORD_TOKEN_TIMEOUT}
        linkAccountTokenTimeout = 1 days
        linkAccountTokenTimeout = ${?STARGAZER_SERVICES_LINK_ACCOUNT_TOKEN_TIMEOUT}
      }
      shortSessionDuration = 1 hour
    }

    botUser {
      user = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Stargazer"
        lastName = "Anduin"
        jobTitle = "Bot"
        company = "Anduin"
        phone = ""
        country = "DigitalWorld"
        state = ""
        city = ""
      }
    }

    portalAdmin {
      user = "<EMAIL>"
      password = "anduin1808"
      password = ${?STARGAZER_SERVICES_PORTAL_ADMIN_PASSWORD}
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Stargazer"
        lastName = "Portal"
        jobTitle = "Admin"
        company = "Anduin"
        phone = ""
        country = "DigitalWorld"
        state = ""
        city = ""
      }
    }

    executiveAdmin {
      user = "<EMAIL>"
      password = "anduin1808"
      password = ${?STARGAZER_SERVICES_EXECUTIVE_ADMIN_PASSWORD}
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Executive"
        lastName = "Admin"
        jobTitle = "Admin"
        company = "Anduin"
        phone = ""
        country = "DigitalWorld"
        state = ""
        city = ""
      }
    }

    fundSubBot {
      user = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "FundSub"
        lastName = "Bot"
        jobTitle = "Bot"
        company = "Anduin"
        phone = ""
        country = "DigitalWorld"
        state = ""
        city = ""
      }
    }

    dataRoomIntegrationBot {
      user = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "DataRoomIntegration"
        lastName = "Bot"
        jobTitle = "Bot"
        company = "Anduin"
        phone = ""
        country = "DigitalWorld"
        state = ""
        city = ""
      }
    }

    dataRoomBot {
      user = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "DataRoom"
        lastName = "Bot"
        jobTitle = "Bot"
        company = "Anduin"
        phone = ""
        country = "DigitalWorld"
        state = ""
        city = ""
      }
    }

    fundDataBot {
      user = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Anduin"
        lastName = "System"
        jobTitle = "Bot"
        company = "Anduin"
        phone = ""
        country = "DigitalWorld"
        state = ""
        city = ""
      }
    }

    adminEntity {
      entityId = "entbbbbbbbbbbbbb",
    }

    anduinInternalEntities = "entaaaaaaaaaaaaa"
    anduinInternalEntities = ${?STARGAZER_SERVICES_ANDUIN_INTERNAL_ENTITIES}

    seedEntities = [
      {
        entityId = "entaaaaaaaaaaaaa",
        name = "Harry and friends",
        alias = "H&f",
        emailDomains = ["anduin.co"],
        entityType = "Investor"
      },
      {
        entityId = "entbbbbbbbbbbbbb",
        name = "Anduin Admin Portal",
        alias = "AAA",
        emailDomains = [],
        entityType = "PortalAdmin"
      },
      {
        entityId = "entccccccccccccc",
        name = "Jonathin and friends",
        alias = "J&f",
        emailDomains = [],
        entityType = "Company"
      },
      {
        entityId = "entddddddddddddd",
        name = "David and friends",
        alias = "D&f",
        emailDomains = [],
        entityType = "LawFirm"
      }
    ]

    seedUsers = ${?seedUsers} [{
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Ada"
        lastName = "Banks"
        jobTitle = "CEO"
      }
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Stargazer"
        lastName = "Portal"
        jobTitle = "Admin"
      }
      entityIds = ["entbbbbbbbbbbbbb"]
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Hary"
        lastName = "Williams"
        jobTitle = "Lawyer"
      }
      entityIds = ["entaaaaaaaaaaaaa"]
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "John"
        lastName = "Mcregor"
        jobTitle = "CFO"
      }
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Logan"
        lastName = "Smith"
        jobTitle = "General Partner"
      }
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        password = "anduin1808"
        firstName = "Vincent"
        lastName = "Harris"
        jobTitle = "CFO"
      }
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Virginia"
        lastName = "Lee"
        jobTitle = "Lawyer"
      }
      entityIds = ["entaaaaaaaaaaaaa"]
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Jenny"
        lastName = "Yoo"
        jobTitle = "Board Member"
      }
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Jonathin"
        lastName = "Sundin"
        jobTitle = "Arrakis Inc"
      }
      entityIds = ["entccccccccccccc"]
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Eric"
        lastName = "Miller"
        jobTitle = "Investor"
      }
      entityIds = ["entccccccccccccc"]
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "David"
        lastName = "Walton"
        jobTitle = "Investor"
      }
      entityIds = ["entddddddddddddd"]
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Daiki"
        lastName = "Rikuto"
        jobTitle = "Shareholder"
      }
      entityIds = ["entddddddddddddd"]
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Angelino"
        lastName = "Sansone"
        jobTitle = "Shareholder"
      }
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Ian"
        lastName = "Cohen"
        jobTitle = "Council"
      }
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Coleen"
        lastName = "Curtis"
        jobTitle = "Council"
      }
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Dennis"
        lastName = "Allen"
        jobTitle = "CFO"
      }
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Mariela"
        lastName = "Orozco"
        jobTitle = "Co-Investor"
      }
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Madison"
        lastName = "Benton"
        jobTitle = "Board Member"
      }
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Johan"
        lastName = "Chen"
        jobTitle = "CFO"
      }
    }, {
      email = "<EMAIL>"
      password = "anduin1808"
      userInfo {
        emailAddressStr = "<EMAIL>"
        firstName = "Chris"
        lastName = "McFarland"
        jobTitle = "Shareholder"
      }
    }]

    seedFeatureIds = ["entaaaaaaaaaaaaa.feature_deal_memo", "entaaaaaaaaaaaaa.feature_template_editing"],

    keycloak {
      admin {
        host = keycloak${?networkDnsSuffix}
        host = ${?STARGAZER_SERVICES_KEYCLOAK_ADMIN_HOST}
        ssl = false
        ssl = ${?STARGAZER_SERVICES_KEYCLOAK_ADMIN_SSL}
        port = 80
        port = ${?STARGAZER_SERVICES_KEYCLOAK_ADMIN_PORT}
        realmToken = "8e19172c36d3e5db797c43b8bc0ad155"
        realmToken = ${?STARGAZER_SERVICES_KEYCLOAK_REALM_TOKEN}
      }
    }

    jwt {
      hmacKey = "3ouJju5cuuTEr1YNDaPuGbz5jBAke4s"
      hmacKey = ${?STARGAZER_SERVICES_JWT_TOKEN_KEY}
      rsaKeyMapFile = ${?STARGAZER_SERVICES_JWT_RSA_KEY_MAP_FILE}
      defaultKid = "anduin-jwt-private-0"
      defaultKid = ${?STARGAZER_SERVICES_JWT_RSA_DEFAULT_KID}
    }

    ses {
      awsAccountId = "************"
      region = "us-west-2"
      region = ${?STARGAZER_DEFAULT_AWS_REGION}
      region = ${?STARGAZER_SES_REGION}
      accessKeyId = ${stargazer.backendConfig.aws.accessKeyId}
      secretAccessKey = ${stargazer.backendConfig.aws.secretAccessKey}

      configurationSet = "ses-dev-v2-01"
      configurationSet = ${?STARGAZER_SES_CONFIGURATION_SET}
      s3Bucket = "gondor-ses-email-dev-v2"
      s3Bucket = ${?STARGAZER_SES_S3_BUCKET}
      sqsReceiveQueue = "ses-receive-dev-v2-01"
      sqsReceiveQueue = ${?STARGAZER_SES_SQS_RECEIVE_QUEUE}
      sqsEventQueue = "ses-event-dev-v2-01"
      sqsEventQueue = ${?STARGAZER_SES_SQS_EVENT_QUEUE}

      s3CredentialConfig = {
        region = "us-west-2"
        region = ${?STARGAZER_DEFAULT_AWS_REGION}
        region = ${?STARGAZER_SES_REGION}
        accessKeyId = ${stargazer.backendConfig.aws.accessKeyId}
        secretAccessKey = ${stargazer.backendConfig.aws.secretAccessKey}
      }

      connectionTimeout = 1 minute
      sqsQueueWaitTime = 20 seconds
      sqsMaxBufferSize = 100
      sqsMaxBatchSize = 10
      sqsVisibilityTimeout = 10 seconds
    }

    textract {
      isEnabled = false
      isEnabled = ${?STARGAZER_SERVICES_TEXTRACT_ENABLED}
      awsAccountId = "************"
      snsConfig = {
        topicArn = "arn:aws:sns:ap-southeast-1:************:AmazonTextract-gondor-dev-20230504075724788800000002"
        topicArn = ${?STARGAZER_SERVICES_TEXTRACT_SNS_ARN}
        roleArn = "arn:aws:iam::************:role/textract-sns-dev"
        roleArn = ${?STARGAZER_SERVICES_TEXTRACT_SNS_ROLE_ARN}
      }
      sqsEventQueue = "AmazonTextract-gondor-dev-20230504075726960700000004"
      sqsEventQueue = ${?STARGAZER_SERVICES_TEXTRACT_SQS_QUEUE}

      credentialConfig = {
        region = "ap-southeast-1"
        region = ${?STARGAZER_DEFAULT_AWS_REGION}
        region = ${?STARGAZER_SERVICES_TEXTRACT_AWS_REGION}
        accessKeyId = ${stargazer.backendConfig.aws.accessKeyId}
        secretAccessKey = ${stargazer.backendConfig.aws.secretAccessKey}
      }
      outputConfig = {
        s3Bucket = "gondor-textract-dev"
        s3Bucket = ${?STARGAZER_SERVICES_TEXTRACT_S3_BUCKET}
        s3Prefix = "textract_output"
        s3Prefix = ${?STARGAZER_SERVICES_TEXTRACT_S3_PREFIX}
      }
      kafkaConfig = {
        eventTopic = "textract-completion-event-topic"
      }
      useApi = false
    }

    fundSubSupportingDocUploadDigest {
      kafkaTopic = "v3-fundSub-supporting-doc-upload"
      kafkaWindowedTopic = "v3-fundSub-supporting-doc-upload-windowed"
      windowLength = 10 minutes
    }

    fundSubSupportingReadyForReviewDigest {
      kafkaTopic = "v3-fundSub-supporting-review-ready"
      kafkaWindowedTopic = "v3-fundSub-supporting-review-ready-windowed"
      windowLength = 1 hour
    }

    fundSubLpInvitedDigestConfig {
      inputTopic = "v2-fundSub-lp-invited-input"
      outputTopic = "v2-fundSub-lp-invited-output"
      defaultTimeZone = "-8" # PST
      windowLength = 24 hours
      advanceInterval = 1 hour
    }

    email {
      highEngagementDomain = "dev.anduintransact.email"
      highEngagementDomain = ${?STARGAZER_EMAIL_HIGH_ENGAGEMENT_DOMAIN}
      standardDomain = "dev.anduintransact.email"
      standardDomain = ${?STARGAZER_EMAIL_STANDARD_DOMAIN}

      generation {
        timeout = 5 minutes
        retry {
          maxCount = 3
          interval = 2 seconds
        }
      }

      sending {
        disableSendingEmail = false
        disableSendingEmail = ${?STARGAZER_EMAIL_SENDING_DISABLE}
        timeout = 5 minutes
        retry {
          maxCount = 3
        }
        kafkaTopic = "v3-sending-emails"
        highEngagementKafkaTopic = "v4-sending-emails-high-engagement"
        immediatelyKafkaTopic = "v4-sending-emails-immediately"
        throttleMessagesPerSecond = 10
        throttleOneMessagePerDuration = 10 seconds
        throttleOneMessagePerDuration = ${?STARGAZER_EMAIL_SENDING_THROTTLE_ONE_MESSAGE}
        dynamoDbStatusTable = "EmailStatusTest"
        dynamoDbStatusTable = ${?STARGAZER_EMAIL_SENDING_DYNAMO_DB_STATUS_TABLE}
        statusFetchCacheDuration = 1 hour
        statusFetchMaxDuration = 15 days
      }

      receiving {
        botName = "dealbot"
        receivingEmailKafkaTopic = "v2-receiving-emails"
        taggedEmailKafkaTopic = "v2-tagged-emails"
        emailEventKafkaTopic = "v2-email-events"
      }

      threadTagging {
        consumerGroup = "v2-thread-tagging-email"
        deadLetterTopic = "v2-thread-tagging-email-dead-letter"
        deadLetterConsumerGroup = "v2-thread-tagging-email-dead-letter"
      }

      inboxEvent {
        consumerGroup = "v2-inbox-event-email"
        deadLetterTopic = "v2-inbox-event-email-dead-letter"
        deadLetterConsumerGroup = "v2-inbox-event-email-dead-letter"
      }
    }

    aws {
      S3 {
        bucket = "gondor-assets-document-dev-v2"
        bucket = ${?STARGAZER_S3_DEFAULT_BUCKET}
        batchDownloadBucket = "gondor-batch-download-dev-v2"
        batchDownloadBucket = ${?STARGAZER_S3_BATCH_DOWNLOAD_BUCKET}
        publicBucket = "gondor-public-document-dev-v2"
        publicBucket = ${?STARGAZER_S3_PUBLIC_BUCKET}
        formTemplateBucket = "gondor-form-template-dev-v2"
        formTemplateBucket = ${?STARGAZER_S3_FORM_TEMPLATE_BUCKET}
        dataExportBucket = "gondor-export-dev-v2"
        dataExportBucket = ${?STARGAZER_S3_EXPORT_BUCKET}
        formStorageBucket = "gondor-form-storage-dev-v2"
        formStorageBucket = ${?STARGAZER_S3_FORM_STORAGE_BUCKET}
        emailStorageBucket = "gondor-email-storage-dev-v2"
        emailStorageBucket = ${?STARGAZER_S3_EMAIL_STORAGE_BUCKET}
        resourcesBucket = "gondor-resources-dev-v2"
        resourcesBucket = ${?STARGAZER_S3_RESOURCES_BUCKET}
        tempUploadBucket = "gondor-temp-upload-dev-v2"
        tempUploadBucket = ${?STARGAZER_S3_TEMP_UPLOAD_BUCKET}
        webBuilderBucket = "gondor-webbuilder-dev"
        webBuilderBucket = ${?STARGAZER_S3_WEB_BUILDER_BUCKET}

        # Remove this after remove alpakka S3
        region = "ap-southeast-1"
        region = ${?STARGAZER_DEFAULT_AWS_REGION}
        region = ${?STARGAZER_S3_REGION}
        accessKeyId = ${stargazer.backendConfig.aws.accessKeyId}
        secretAccessKey = ${stargazer.backendConfig.aws.secretAccessKey}
        s3CredentialConfig = {
          region = "ap-southeast-1"
          region = ${?STARGAZER_DEFAULT_AWS_REGION}
          region = ${?STARGAZER_S3_REGION}
          accessKeyId = ${stargazer.backendConfig.aws.accessKeyId}
          secretAccessKey = ${stargazer.backendConfig.aws.secretAccessKey}
        }

        linkExpiration = 600 seconds
        connectionTimeout = 2 minutes
        chunkSize = 6 MB

        cloudFrontConfig = {
          cloudFrontEnabled = true
          cloudFrontEnabled = ${?STARGAZER_S3_CLOUD_FRONT_ENABLED}
          domain = "document-dev.anduin.app"
          domain = ${?STARGAZER_S3_CLOUD_FRONT_DOMAIN}
          keyPairId = "KZOF12HJ4T98J"
          keyPairId = ${?STARGAZER_S3_CLOUD_FRONT_KEYPAIR_ID}
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
          privateKeyValue = ${?STARGAZER_S3_CLOUD_FRONT_PRIVATE_KEY}
        }

        batchDownloadPrefix = "batch-download",
        cacheConfig = {
          capacity = 128
          timeToLive = 5 minutes
        },
        annotationStorageVersion = 3
      }

      dynamoDbConfig {
        accessKeyId = ${stargazer.backendConfig.aws.accessKeyId}
        secretAccessKey = ${stargazer.backendConfig.aws.secretAccessKey}
        region = "us-east-1"
        region = ${?STARGAZER_DEFAULT_AWS_REGION}
        region = ${?STARGAZER_SERVICE_DYNAMODB_REGION}
      }

      cloudwatchLogConfig {
        accessKeyId = ${stargazer.backendConfig.aws.accessKeyId}
        secretAccessKey = ${stargazer.backendConfig.aws.secretAccessKey}
        region = "us-east-1"
        region = ${?STARGAZER_DEFAULT_AWS_REGION}
        region = ${?STARGAZER_SERVICE_CLOUDWATCH_LOG_REGION}
      }

      bedrockConfig {
        accessKeyId = "********************"
        accessKeyId = ${?BEDROCK_AWS_ACCESS_KEY_ID}
        secretAccessKey = "bvOUDxSvfetRfrrhdezJWqzdB0tDWUIhPz/LsNaA"
        secretAccessKey = ${?BEDROCK_AWS_SECRET_ACCESS_KEY}
        region = "us-east-1"
        region = ${?BEDROCK_DEFAULT_AWS_REGION}
      }

      accessKeyId = "********************"
      accessKeyId = ${?STARGAZER_AWS_ACCESS_KEY_ID}
      secretAccessKey = "JnxKa+dP4MMDpd1LDS73tllL7aZNRjQKVlMggSCe"
      secretAccessKey = ${?STARGAZER_AWS_SECRET_ACCESS_KEY}
    }

    liteLlmConfig {
      apiKey = "sk--I1yhEN2ESwdFj1r62U29g"
      apiKey = ${?LITE_LLM_API_KEY}
      apiBase = "https://litellm.anduin.center"
      apiBase = ${?LITE_LLM_API_BASE}
    }

    temporalCron {
      workflowTaskTimeout = 2 minutes
      workflowRunTimeout = 30 minutes
      hourlySchedule = "42 * * * *"
      firstMinuteOfEveryHourSchedule = "01 * * * *"
      dailySchedule = "05 0 * * *"
      weeklySchedule = "0 1 * * 1"
      cron10MinSchedule = "*/10 * * * *"
    }

    redirect {
      # This is the prefix of redirect url
      duration = 3650 days # redirect by default never expires
    }

    actionToken {
      temporaryTokenDuration = 1 hour
      autoLoginTokenDuration = 365 days
    }

    cronHttpClientConfig {
      clientPoolName = "cron"
      responseHeaderTimeout = 60 seconds
      idleTimeout = 60 seconds
      requestTimeout = 60 seconds
      maxTotalConnections = 8
      threadPoolSize = 8
      maxWaitQueueLimit = 20480
      forkJoinedParallelism = 16
      forkJoinedMaxThreads = 256
    }

    remoteRepl {
      isEnabled = false
      port = 7777
    }

    servicesTimeout {
      default = 30 seconds
      createTransaction = 120 seconds
      termSheetGeneration = 60 seconds
      stage = 40 seconds
      fileServiceJvmApi = 300 seconds
      fileUploader = 600 seconds
      foundationDb = 30 seconds
      dataRoomLongTask = 60 seconds
    }

    googleCloudConfig {
      bigQueryConfig = {
        credentialFile = ""
        credentialFile = ${?STARGAZER_SERVICE_BIG_QUERY_CREDENTIAL_FILE}
        projectId = ""
        projectId = ${?STARGAZER_SERVICE_BIG_QUERY_PROJECT_ID}
        datasetId = ""
        datasetId = ${?STARGAZER_SERVICE_BIG_QUERY_DATASET_ID}
      }
    }

    dmsConfig {
      useGraphDB = false
      downloadKafkaTopic = "v2-dms-download"
    }

    serverlessConfig {

      serverlessRegionConfig {
        awsAccountId = "************"
        accessKeyId = ${stargazer.backendConfig.aws.accessKeyId}
        secretAccessKey = ${stargazer.backendConfig.aws.secretAccessKey}
        region = "us-east-1"
        region = ${?STARGAZER_DEFAULT_AWS_REGION}
        region = ${?STARGAZER_SERVERLESS_AWS_REGION}
        bucket = ${stargazer.backendConfig.aws.S3.bucket}
        maxConcurrency = 100
        readTimeout = 600 seconds
      }

      serverlessSqsConfig {
        queueName = ""
        queueName = ${?STARGAZER_SERVICES_SERVERLESS_ASYNC_SQS_QUEUE}
        waitTime = 20 seconds
        maxBufferSize = 100
        maxBatchSize = 10
        visibilityTimeout = 20 seconds
      }

      serverlessFunctions {
        libreOffice = {
          parallelism = 100
          version = {
            ap = 4
            us = 1
            eu = 1
          }
          serverlessFunction = "anduin-serverless-libreoffice"
          executionTimeout = 120 seconds
        }

        tesseract = {
          parallelism = 30
          version = {
            ap = 6
            us = 1
            eu = 1
          }
          serverlessFunction = "anduin-serverless-tesseract"
          executionTimeout = 600 seconds
        }

        compressUtils = {
          parallelism = 100
          version = {
            ap = 20
            us = 3
            eu = 3
          }
          serverlessFunction = "anduin-serverless-compressUtils"
          executionTimeout = 600 seconds
        }

        htmlToPdf = {
          parallelism = 100
          version = {
            ap = 5
            us = 1
            eu = 1
          }
          serverlessFunction = "anduin-serverless-htmlToPdf"
          executionTimeout = 120 seconds
        }

        rclone = {
          parallelism = 100
          version = {
            ap = 9
            us = 1
            eu = 1
          }
          serverlessFunction = "anduin-serverless-rclone"
          executionTimeout = 120 seconds
        }

        mupdf = {
          parallelism = 100
          version = {
            ap = 10
            us = 7
            eu = 7
          }
          serverlessFunction = "anduin-serverless-muPdf"
          executionTimeout = 300 seconds
        }

        signPdf = {
          parallelism = 100
          version = {
            ap = 16
            us = 6
            eu = 3
          }
          serverlessFunction = "anduin-serverless-signpdf"
          executionTimeout = 120 seconds
        }

        loki = {
          parallelism = 20
          version = {
            ap = 20
            us = 9
            eu = 9
          }
          serverlessFunction = "anduin-serverless-loki"
          executionTimeout = 120 seconds
        }

        ffprobe = {
          parallelism = 20
          version = {
            ap = 14
            us = 1
            eu = 1
          }
          serverlessFunction = "anduin-serverless-ffprobe"
          executionTimeout = 120 seconds
        }

        docusign = {
          parallelism = 100
          version = {
            ap = 25
            us = 14
            eu = 14
          }
          serverlessFunction = "anduin-serverless-docusign"
          executionTimeout = 300 seconds
        }

        textractUtilities = {
          parallelism = 20
          version = {
            ap = 18
            us = 7
            eu = 7
          }
          serverlessFunction = "anduin-serverless-textract"
          executionTimeout = 600 seconds
        }

        watermarkGenerator = {
          parallelism = 100
          version = {
            ap = 21
            us = 13
            eu = 9
          }
          serverlessFunction = "anduin-serverless-watermarkGenerator"
          executionTimeout = 180 seconds
        }

        sha256Compute = {
          parallelism = 20
          version = {
            ap = 16
            us = 4
            eu = 4
          }
          serverlessFunction = "anduin-serverless-sha256Compute"
          executionTimeout = 180 seconds
        }

        fillPdf = {
          parallelism = 100
          version = {
            ap = 21
            us = 7
            eu = 7
          }
          serverlessFunction = "anduin-serverless-fillPdf"
          executionTimeout = 180 seconds
        }

        formDataUtils = {
          parallelism = 100
          version = {
            ap = 20
            us = 12
            eu = 11
          }
          serverlessFunction = "anduin-serverless-formDataUtils"
          executionTimeout = 120 seconds
        }

        formMatchingUtils = {
          parallelism = 100
          version = {
            ap = 23
            us = 10
            eu = 10
          }
          serverlessFunction = "anduin-serverless-formMatchingUtils"
          executionTimeout = 120 seconds
        }

        exportCsvFilesToExcel = {
          parallelism = 100
          version = {
            ap = 11
            us = 6
            eu = 6
          }
          serverlessFunction = "anduin-serverless-csvToExcel"
          executionTimeout = 120 seconds
        }

        catalaRunner = {
          parallelism = 50
          version = {
            ap = 5
            us = 4
            eu = 4
          }
          serverlessFunction = "anduin-serverless-catala"
          executionTimeout = 30 seconds
        }

        pdfAnnotationRunner = {
          parallelism = 100
          version = {
            ap = 11
            us = 8
            eu = 8
          }
          serverlessFunction = "anduin-serverless-pdfAnnotation"
          executionTimeout = 60 seconds
        }

        computePageSimilarity = {
          parallelism = 20
          version = {
            ap = 7
            us = 3
            eu = 3
          }
          serverlessFunction = "anduin-serverless-pageSimilarity"
          executionTimeout = 300 seconds
        }

        formModule = {
          parallelism = 100
          version = {
            ap = 19
            us = 17
            eu = 18
          }
          serverlessFunction = "anduin-serverless-formModule"
          executionTimeout = 60 seconds
        } 

        mtlsCA = {
          parallelism = 4
          overrideRegion = "ap-southeast-1"
          overrideRegion = ${?STARGAZER_SAPI_MTLS_SERVERLESS_REGION}
          version = {
            ap = 2
            us = 2
            eu = 2
          }
          serverlessFunction = "anduin-serverless-mtlsca"
          executionTimeout = 120 seconds
        }

        bedrock = {
          parallelism = 20
          version = {
            ap = 2
            us = 1
            eu = 1
          }
          serverlessFunction = "anduin-serverless-bedrock"
          executionTimeout = 300 seconds
        }

        docSplit = {
          parallelism = 20
          version = {
            ap = "$LATEST"
            us = 1
            eu = 1
          }
          serverlessFunction = "anduin-serverless-docSplit"
          executionTimeout = 300 seconds
        }

        formDataExtraction = {
          parallelism = 20
          version = {
            ap = 2
            us = 1
            eu = 1
          }
          serverlessFunction = "anduin-serverless-extractData"
          executionTimeout = 300 seconds
        }

        generateSchema = {
          parallelism = 20
          version = {
            ap = 1
            us = 1
            eu = 1
          }
          serverlessFunction = "anduin-serverless-generateSchema"
          executionTimeout = 300 seconds
        }

        ipWhitelist {
          serverlessFunction = "anduin-serverless-ipwhitelist"
          overrideRegion = "ap-southeast-1"
          overrideRegion = ${?STARGAZER_SAPI_MTLS_SERVERLESS_REGION}
          executionTimeout = 30s
          parallelism = 4
          version {
            ap = "$LATEST"
            us = 3
            eu = 1
          }
        }

        analyzePdfAnnotations = {
          parallelism = 20
          version = {
            ap = "$LATEST"
            us = 1
            eu = 1
          }
          serverlessFunction = "anduin-serverless-analyzePdfAnnotations"
          executionTimeout = 300 seconds
        }

        drawAnnotationBoxes = {
          parallelism = 20
          version = {
            ap = 2
            us = 2
            eu = 2
          }
          serverlessFunction = "anduin-serverless-drawAnnotationBoxes"
          executionTimeout = 300 seconds
        }

        extractKeywords = {
          parallelism = 100
          version = {
            ap = 2
            us = 1
            eu = 1
          }
          serverlessFunction = "anduin-serverless-extractKeywords"
          executionTimeout = 30 seconds
        }
      }

    }

    issueTrackerConfig {
      defaultAccessibleDomains = "anduintransact.com,cliffordchance.com,foxwood.sg"
      permissionIntegrityCheckInterval = 24 hours
    }

    foundationDbConfig {
      host = foundationdb${?networkDnsSuffix}
      host = ${?STARGAZER_SERVICES_FOUNDATIONDB_HOST}
      port = 4500
      port = ${?STARGAZER_SERVICES_FOUNDATIONDB_PORT}
      description = "fdb:fdb"
      description = ${?STARGAZER_SERVICES_FOUNDATIONDB_DESCRIPTION}
      localTempFileName = "foundationdb"
      localTempFileExtension = ".cluster"
      localTempDirectory = "/tmp"
      verboseTraceEvent = true
      verboseTraceEvent = ${?STARGAZER_SERVICES_FOUNDATIONDB_VERBOSE_TRACE_EVENT}
      traceEventSampleRate = 10
    }

    foundationDbReadOnlyConfig {
      host = foundationdb${?networkDnsSuffix} # By default using the same primary fdb cluster
      host = ${?STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY_HOST}
      port = 4500
      port = ${?STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY_PORT}
      description = "fdb:fdb"
      description = ${?STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY_DESCRIPTION}
      localTempFileName = "foundationdb-readonly"
      localTempFileExtension = ".cluster"
      localTempDirectory = "/tmp"
      verboseTraceEvent = true
      verboseTraceEvent = ${?STARGAZER_SERVICES_FOUNDATIONDB_VERBOSE_TRACE_EVENT}
      traceEventSampleRate = 10
    }

    boxConfig {
      accessToken = "********************************" # This only works on local dev
      accessToken = ${?STARGAZER_SERVICES_BOX_ACCESS_TOKEN}
    }

    dropboxConfig {
      accessToken = "TGWk7eTuJQ4AAAAAAAAAAZEmTybxBq2QztplkXh7r2yMQMYQXGylSYbVJTKksxsg" # This only works on local dev
      accessToken = ${?STARGAZER_SERVICES_DROPBOX_ACCESS_TOKEN}
    }

    driveConfig {
      key = """***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""
      key = ${?STARGAZER_SERVICES_GOOGLE_DRIVE_SA_KEY}
    }

    sharePointConfig {
      username = "<EMAIL>"
      username = ${?STARGAZER_SERVICES_SHAREPOINT_USERNAME}
      password = "bVEiQ4U?NM}GpX*"
      password = ${?STARGAZER_SERVICES_SHAREPOINT_PASSWORD}
      tenant = "da6e6f9c-8c65-4fff-92d5-cc99aa412bd2"
      tenant = ${?STARGAZER_SERVICES_SHAREPOINT_TENANT}
      clientId = "301e81a6-4be4-4ae0-b6e7-672fefdb951c"
      clientId = ${?STARGAZER_SERVICES_SHAREPOINT_CLIENT_ID}
      clientSecret = "**********************************"
      clientSecret = ${?STARGAZER_SERVICES_SHAREPOINT_CLIENT_SECRET}
      scope = "user.read offline_access files.readwrite.all sites.readwrite.all"
      scope = ${?STARGAZER_SERVICES_SHAREPOINT_SCOPE}
      encryptedPass = "yN-Hj2pRoRu5ZVFTeKPsOmEI7s-fqRcPiBvtmwQzUg" # Encrypted password using rclone
      encryptedPass = ${?STARGAZER_SERVICES_SHAREPOINT_ENCRYPTED_PASS}
    }

    sftpServerConfig {
      publicKey = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC62oi7YGl3+BSEGIwLRMhiJ2CECqBgRSdms3be4A3LpgJGv+veqx7wJU9HdEiA/JJBXMfJBkxxH+9b7WMsuLBSDlWUZ9GUNdZRq4HF0xmi/QuorQ3VcFYLY1OhqWorI+JR3mNCms8MCtqFPwMGe5w2ksPwpK3QvISpd9FCf5l4vDCXrXS/HtcHDlMPi+9nbMZyV2KW5uyrkxo1CMleXMcFGnwKOftZki2MlXAeH8FWipRpBHkvh5PoG/MJt3SXMEM6p4k2NMuLBD97wyXb2SibM+8bHKF/23TAXih8xLgxcG914Fznw/wOiEFVcM7YO5TWTu35vWO+pJxuOxGm5SAqwYjGEO4r5P/MltOA1WSme50F4hkWNjovc0GohoJHTUzaae3ifS9O0/z0ZtmpoS5ipbrh3H/ncJjsVDMs+51ZvJGRL70WaHY32ebj8h1O/G6N7NyRFoVrwpndS2TBiaD5uY+qnfTFWz0cuZHFMvmSsry/lwZCLn0iVUs2qRjMtH8= sftp@localhost"
      publicKey = ${?STARGAZER_SERVICES_SFTP_SERVER_PUBLIC_KEY}
      privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----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\ntDQcd6HV0SOA3dAAAAFmtlaW1vb25Aa2VpbW9vbi1mZWRvcmEBAgME\n-----END OPENSSH PRIVATE KEY-----"
      privateKey = ${?STARGAZER_SERVICES_SFTP_SERVER_PRIVATE_KEY}
    }

    analyticsConfig {
      s3Bucket = "stargazer-analytics-dev-v2"
      s3Bucket = ${?STARGAZER_SERVICES_ANALYTICS_S3_BUCKET}
      s3Prefix = ""
      s3Prefix = ${?STARGAZER_SERVICES_ANALYTICS_S3_PREFIX}

      exporterConfig {
        internalQueueSize = 500
        recordChunkSize = 1000
      }
    }

    notificationConfig {
      kafkaTopic = "v2-notifications"
    }


    thirdPartyAppConfig {
    }

    tinyUrlConfig {
    }

    limiterConfig {
      hitCount = 100
      duration = 1 minutes
      maxCount = 200
    }

    fileActivityLoggerConfig {
      kafkaBufferSize = 10000
    }

    customDomainConfig {
      isEnabled = true
      seedCustomDomains = ""
      seedCustomDomains = ${?STARGAZER_SERVICES_SEED_CUSTOM_DOMAINS}
      secretTargetHeaderValue = ""
      secretTargetHeaderValue = ${?STARGAZER_SERVICES_SECRET_TARGET_HEADER}
    }

    shortenUrlConfig {
      salt = "dec357e7926b69f312060901cca8d90a"
      offset: 20
      length = 16
    }

    turnstileBackendConfig {
      invisibleSecret = "1x0000000000000000000000000000000AA"
      invisibleSecret = ${?STARGAZER_SERVICES_TURNSTILE_SECRET_INVISIBLE}
      visibleSecret = "1x0000000000000000000000000000000AA"
      visibleSecret = ${?STARGAZER_SERVICES_TURNSTILE_SECRET_VISIBLE}
    }

    oauth2Config {
      clients = []
      stateTimeout = 30 minutes
      enableSkipLinkAccount = false
      enableSkipLinkAccount = ${?STARGAZER_SERVICES_OAUTH2_ENABLE_SKIP_LINK_ACCOUNT}
    }

    encryptionConfig {
      accessKeyId = ${stargazer.backendConfig.aws.accessKeyId}
      secretAccessKey = ${stargazer.backendConfig.aws.secretAccessKey}
      masterKeyKMSArn = "aws-kms://arn:aws:kms:us-east-1:************:key/2c5300f2-515e-422e-8f99-d6a67b3392a4"
      masterKeyKMSArn = ${?STARGAZER_SERVICES_MASTER_KEY_ARN}

      # Encrypted data key with Base64 encoded
      encryptedDataKeyBlob = "AQICAHhTNgXOanuG36K3ThdTRqWnPeaPvDajkOb2FRdjM7z+iQFls8JO7bXp9MOwmlvtLISkAAAA5TCB4gYJKoZIhvcNAQcGoIHUMIHRAgEAMIHLBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDDAvnTO0LKSVSNwmAgIBEICBnfg/MCxHOKfZ2aHIJx0rpPD5zoKmvoQfa8CqZT52mjb0n51JrflgDxYkbxPhRwVLFn41KF1ayFhGr/CKYx+4rTCJZSBs6DtnQx1XkXR4dDdY8z2nyyzMISmSDfSxp3vwMx7IXkO5pi/J72By+g5PO4HrZ+BgrHGGRmR1hKhKkLOKr0XMT5QWUn6dQkIl3xKTtnGghvPuwYh5YGUVbqY="
      encryptedDataKeyBlob = ${?STARGAZER_SERVICES_ENCRYPTED_DATA_KEY_BLOB}
    }

    fundSubEmailConfig {
      consumerGroup = "v2-fundsub-event-email"
      deadLetterTopic = "v2-fundsub-event-email-dead-letter"
      deadLetterConsumerGroup = "v2-fundsub-event-email-dead-letter"
    }

    fundSubSimulatorConfig {
      inviteDemoOrdersTopic = "v2-fundsub-simulator-invite-demo-orders"
      privateToken = "v2ol7m1qjkw5qzd0on21ej4gwl453xgjkvop0mv7lz534yjd71qmz3dg0xojp5g7ekx1yv2mw1mgk2ylq7xep7vkzl2ewo10m43ywknzl1xq2n134qzevjg5r0kv9do5k41x5ppd8pxykd0o9wml72jog7d9v0ep5qym9x53dg4njz9dv534o0njplow490n3djzqep49y2kl5nvw1woq2gpek9xney729zqn1m0dl9vwk7xy35pmd4g3xzy9pn0"
    }

    dataRoomSimulatorConfig {
      privateToken = "qrodjz89p2oqr3wxm6ye58wpolx9dejlgry6q2w86n8doq2v1rjmgme2dj8o57z3pj37rvml28yonv5g2rq3ewz9jyz8ep5xw79dqnx2p3dlz6gjj8r97w5qdlne4w853gn4y6zo3xmp21gv7v1ym89wroe5gmq62lr13von16x7ydojp8ml5w96enzdgq1xnvx9w1q6rlpyz3yxew7pg9l535p7xn1zmo9vgd1rzjqvn327m56neyl1gv7d62xw"
    }

    protectedLinkConfig {
      publicPasswordPassedTokenDuration = 5 minutes
    }

    timescaleConfig {
      host = timescaledb-v3${?networkDnsSuffix}
      host = ${?STARGAZER_SERVICES_TIMESCALEDB_HOST}
      port = 5432
      port = ${?STARGAZER_SERVICES_TIMESCALEDB_PORT}
      username = "timescaledb"
      username = ${?STARGAZER_SERVICES_TIMESCALEDB_USERNAME}
      password = "timescaledb"
      password = ${?STARGAZER_SERVICES_TIMESCALEDB_PASSWORD}
      databaseName = "timescaledb"
      databaseName = ${?STARGAZER_SERVICES_TIMESCALEDB_DATABASE_NAME}
      concurrentSessions = 32
      concurrentSessions = ${?STARGAZER_SERVICES_TIMESCALEDB_CONCURRENT_SESSIONS}
    }

    temporalConfig {
      host = temporal${?networkDnsSuffix}
      host = ${?STARGAZER_SERVICES_TEMPORAL_HOST}
      port = 7233
      port = ${?STARGAZER_SERVICES_TEMPORAL_PORT}
      namespace = "default"
      namespace = ${?STARGAZER_SERVICES_TEMPORAL_NAMESPACE}
      retentionPeriodInDays = 30

      workerMonitor {
        isEnabled = false
        isEnabled = ${?STARGAZER_SERVICES_TEMPORAL_WORKER_MONITOR}
        checkInterval = 15 minutes
        maxAge = 24 hours
        maxAge = ${?STARGAZER_SERVICES_TEMPORAL_WORKER_MAX_AGE}
      }
    }

    zapierConfig {
      kafkaTopic = "v2-zapier-event"
      retryMax = 3
    }

    formConfig {
      lockTimeout = 5 minutes
      sharedSecret = "ff69ec29054a70725a07bcd9cf758886a578698e5d6291c6770f0fba1faa7c52"
      sharedSecret = ${?STARGAZER_SERVICES_FORM_SHARED_SECRET}
      cacheConfig = {
        capacity = 100
        timeToLive = 10 minutes
      }
      formDiffV1KafkaTopic = "v2-form-diff-v1"
      formDiffV2KafkaTopic = "v2-form-diff-v2"
    }

    dgraphConfig {
      httpEndpoint = "http://dgraph-alpha"${?networkDnsSuffix}":8080"
      httpEndpoint = ${?STARGAZER_SERVICES_DGRAPH_HTTP_ENDPOINT}
      grpcEndpoint = "dgraph-alpha"${?networkDnsSuffix}":9080"
      grpcEndpoint = ${?STARGAZER_SERVICES_DGRAPH_GRPC_ENDPOINT}
    }

    ontologyConfig {
      # Still in local test mode. Enable when ready
      # (may wanna enable by servers for testing too)
      isEnabled = true
    }

    fusekiConfig {
      endpoint = "http://fuseki"${?networkDnsSuffix}":3030"
      endpoint = ${?STARGAZER_SERVICES_FUSEKI_ENDPOINT}
    }

    dataLakeConfig {
      fundSubDataLakeIngestionTopic = "fund-sub-data-lake-update-events"
    }

    fundDataConfig {
      eventTopic = "fund-data-event"
    }

    taskRequestConfig {
      eventTopic = "task-request-event"
    }

    enterpriseLoginConfig {
      samlConfig {
        entityId = "http://id.gondor-local.io"
        entityId = ${?STARGAZER_SERVICES_SAML_ENTITY_ID}
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        assertionEncryptionPrivateKey = ${?STARGAZER_SERVICES_SAML_ASSERTION_ENCRYPTION_PRIVATE_KEY}
        assertionEncryptionPublicKey = "-----BEGIN CERTIFICATE-----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\nHS65TDH2rWpDvMuzToTJKEp565N0e3IwKVnrhAGYtWgxAjE=\n-----END CERTIFICATE-----"
        assertionEncryptionPublicKey = ${?STARGAZER_SERVICES_SAML_ASSERTION_ENCRYPTION_PUBLIC_KEY}
      }
    }

    kafkaConfig {
        bootstrapServer: "kafka-v3"${?networkDnsSuffix}":9092"
        bootstrapServer: ${?STARGAZER_KAFKA_BOOTSTRAP_SERVER}
    }

    otpAuthenticationConfig {
      codeLength = 6
      maxAttempt = 10
      loginEmailAddress = "<EMAIL>"
      loginEmailAddress = ${?STARGAZER_SERVICES_OTP_AUTHENTICATION_LOGIN_EMAIL_ADDRESS}
      receivingKafkaConsumerGroup = "v2-revert-otp-receive-email"
    }

    pulsarConfig {
      bootstrapServer: "pulsar://pulsar"${?networkDnsSuffix}":6650"
      bootstrapServer: ${?STARGAZER_SERVICES_PULSAR_BOOTSTRAP_SERVER}
    }

    twilioConfig {
      accountSid = "AC1c8cc2d420e50342023207eccb536cae"
      accountSid = ${?STARGAZER_SERVICES_TWILIO_ACCOUNT_SID}
      messageServiceId = "MG5ea70445ef177fa7abc9644bc80c5052"
      messageServiceId = ${?STARGAZER_SERVICES_TWILIO_MESSAGE_SERVICE_ID}
      keySid = "**********************************"
      keySid = ${?STARGAZER_SERVICES_TWILIO_KEY_SID}
      keySecret = "BCHSRM1ErDIvqhdd9iaxqMI2Wc13XiPI"
      keySecret = ${?STARGAZER_SERVICES_TWILIO_KEY_SECRET}
      kafkaTopic = "v2-sending-sms"
    }

    cassandraConfig {
      host = cassandradb${?networkDnsSuffix}
      host = ${?STARGAZER_SERVICES_CASSANDRA_HOST}
      port = 9042
      port = ${?STARGAZER_SERVICES_CASSANDRA_PORT}
      replication = 1
    }

    tykConfig {
      admin {
        host = tyk-control${?networkDnsSuffix}
        host = ${?STARGAZER_SERVICES_TYK_ADMIN_HOST}
        ssl = false
        ssl = ${?STARGAZER_SERVICES_TYK_ADMIN_SSL}
        port = 80
        port = ${?STARGAZER_SERVICES_TYK_ADMIN_PORT}
        secret = "Mp8x99cpdzRNzyH87P6zm6CQaS8tR5C7"
        secret = ${?STARGAZER_SERVICES_TYK_ADMIN_SECRET}
      }

      http {
        host = tyk-http${?networkDnsSuffix}
        host = ${?STARGAZER_SERVICES_TYK_HTTP_HOST}
        ssl = false
        ssl = ${?STARGAZER_SERVICES_TYK_HTTP_SSL}
        port = 80
        port = ${?STARGAZER_SERVICES_TYK_HTTP_PORT}
        secret = ""
        secret = ${?STARGAZER_SERVICES_TYK_HTTP_SECRET}
      }
    }

    systemAuditLogConfig {
      enable = false
      logGroup = "gondor-system-audit-test"
      logGroup = ${?STARGAZER_SERVICES_SYSTEM_AUDIT_LOG_GROUP}
    }

    docusignIntegrationConfig {
      webhookHandlerTopic = "docusign-webhook-handler-topic"
      webhookHandlerTopic = ${?STARGAZER_DOCUSIGN_WEBHOOK_TOPIC}
      webhookBaseUrl = "https://docusign-test-1.anduin.center"
      webhookBaseUrl = ${stargazer.backendConfig.server.baseUrl}
      canUseDocusignProduction = false
      canUseDocusignProduction = ${?STARGAZER_DOCUSIGN_CAN_USE_PROD}

      demoAppConfig {
        docusignOAuthBasePath = "account-d.docusign.com"
        docusignApiBasePath = "https://demo.docusign.net/restapi"
        integrationKey = "30cc045d-220c-41c6-a9cf-64ea4dd7dd43"
        integrationKey = ${?STARGAZER_DOCUSIGN_DEMO_INTEGRATION_KEY}
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        rsaPrivateKey = ${?STARGAZER_DOCUSIGN_DEMO_RSA_KEY}
        apiAccountId = "23b0dcd8-8684-49b2-9b94-fe4e522ac7a6"
        apiAccountId = ${?STARGAZER_DOCUSIGN_DEMO_ACCOUNT_ID}
        apiUserId = "646ff222-9d43-43bb-bc55-1277556a5f44"
        apiUserId = ${?STARGAZER_DOCUSIGN_DEMO_USER_ID}
        hmacKey = "5bmPFuJNl0wWV56fYEhcRYoBXVhnSJvzHVX6KUZEzhM="
        hmacKey = ${?STARGAZER_DOCUSIGN_DEMO_HMAC_KEY}
        phoneOtpConfigId = "8e231129-c80a-4fa5-bad5-03d12417b18f"
        cerityApiUserId = "770a0369-38ea-4d1d-8b6a-026644fbc8af"
        cerityApiUserId = ${?STARGAZER_DOCUSIGN_DEMO_CERITY_API_USER_ID}
        idVerificationWorkflowId = "6b2d3c20-6e49-4c91-b1d1-5bd580d827b6"
      }
      productionAppConfig {
        docusignOAuthBasePath = "account.docusign.com"
        docusignOAuthBasePath = ${?STARGAZER_DOCUSIGN_PROD_OAUTH_BASE_PATH}
        docusignApiBasePath = "https://na4.docusign.net/restapi"
        docusignApiBasePath = ${?STARGAZER_DOCUSIGN_PROD_API_BASE_PATH}
        integrationKey = ""
        integrationKey = ${?STARGAZER_DOCUSIGN_PROD_INTEGRATION_KEY}
        rsaPrivateKey = ""
        rsaPrivateKey = ${?STARGAZER_DOCUSIGN_PROD_RSA_KEY}
        apiAccountId = ""
        apiAccountId = ${?STARGAZER_DOCUSIGN_PROD_ACCOUNT_ID}
        apiUserId = ""
        apiUserId = ${?STARGAZER_DOCUSIGN_PROD_USER_ID}
        hmacKey = ""
        hmacKey = ${?STARGAZER_DOCUSIGN_PROD_HMAC_KEY}
        phoneOtpConfigId = ""
        phoneOtpConfigId = ${?STARGAZER_DOCUSIGN_PHONE_OTP_CONFIG_ID}
        cerityApiUserId = ""
        cerityApiUserId = ${?STARGAZER_DOCUSIGN_PROD_CERITY_API_USER_ID}
        idVerificationWorkflowId = ""
        idVerificationWorkflowId = ${?STARGAZER_DOCUSIGN_ID_VERIFICATION_WORKFLOW_ID}
      }
    }

    rebacConfig {

      openFgaConfig {
        host = openfga${?networkDnsSuffix}
        host = ${?STARGAZER_SERVICES_OPENFGA_HOST}
        port = 8080
        port = ${?STARGAZER_SERVICES_OPENFGA_PORT}
      }

      openFgaFundConfig {
        host = openfga-fund${?networkDnsSuffix}
        host = ${?STARGAZER_SERVICES_OPENFGA_FUND_HOST}
        port = 8080
        port = ${?STARGAZER_SERVICES_OPENFGA_FUND_PORT}
      }
    }

    edgedbConfig {
      host = edgedb-v2${?networkDnsSuffix}
      host = ${?STARGAZER_SERVICES_EDGEDB_HOST}
      port = 5656
      port = ${?STARGAZER_SERVICES_EDGEDB_PORT}
      database = "edgedb"
      database = ${?STARGAZER_SERVICES_EDGEDB_DB}

      user = {
        user = "edgedb"
        user = ${?STARGAZER_SERVICES_EDGEDB_USER}
        password = "lDn2EcpbyFdQU72mW4vL"
        password = ${?STARGAZER_SERVICES_EDGEDB_ROOT_PASSWORD}
      }
    }

    tidbConfig {
      host = tidb-tidb${?networkDnsSuffix}
      host = ${?STARGAZER_SERVICES_TIDB_HOST}
      port = 4000
      port = ${?STARGAZER_SERVICES_TIDB_PORT}
      database = "gondor"
      database = ${?STARGAZER_SERVICES_TIDB_DB}

      user = {
        user = "gondor"
        user = ${?STARGAZER_SERVICES_TIDB_USER}
        password = "0NZQ9KGH0jG4K96UClaB"
        password = ${?STARGAZER_SERVICES_TIDB_USER_PASSWORD}
      }
    }

    debugConfig = {
      token = "7af41e6ec86c453a294d47bce26d88b2"
    }

    investorGroupConfig {
      investorGroupUpdateEventKafkaTopic = "investor-group-update-event-topic"
    }

    tracingConfig {
      isEnabled = false
      isEnabled = ${?STARGAZER_TRACING_ENABLED}
      traceRootSpanOnly = false
      traceRootSpanOnly = ${?STARGAZER_TRACE_ROOT_SPAN_ONLY}
      collectorSchema = "http"
      collectorSchema = ${?STARGAZER_TRACING_COLLECTOR_SCHEMA}
      collectorEndpoint = ""
      collectorEndpoint = ${?STARGAZER_TRACING_COLLECTOR_ENDPOINT}
      collectorPort = 6831
      collectorPort = ${?STARGAZER_TRACING_COLLECTOR_PORT}
    }

    environmentConfig {
      fallbackSubdomain = "gondor-local.io"
      fallbackSubdomain = ${?STARGAZER_ENVIRONMENT_FALLBACK_SUBDOMAIN}
      internalSubdomains = "gondor-local.io server"
      internalSubdomains = ${?STARGAZER_ENVIRONMENT_INTERNAL_SUBDOMAINS}
      externalTargetDomain = "target.gondor-local.io"
      externalTargetDomain = ${?STARGAZER_ENVIRONMENT_EXTERNAL_TARGET_DOMAIN}
    }

    globalSignConfig {
      baseUrl = "https://emea.api.dss.globalsign.com:8443/v2"
      baseUrl = ${?STARGAZER_GLOBAL_SIGN_BASE_URL}
      apiKey = "1ea41e631c19584b"
      apiKey = ${?STARGAZER_GLOBAL_SIGN_API_KEY}
      apiSecret = "b138a9269acd6a93935682bb8e9c38a9be642ba4"
      apiSecret = ${?STARGAZER_GLOBAL_SIGN_API_SECRET}
    }

    dataPipelineConfig {
      enableDataIngest = false
      enableDataIngest = ${?STARGAZER_DATAPIPELINE_ENABLED}
      enableDataTransform = false
      enableDataTransform = ${?STARGAZER_DATAPIPELINE_ENABLED}
      enableDataCorrectionCron = false
      enableDataCorrectionCron = ${?STARGAZER_DATAPIPELINE_ENABLED}
      correctEventPerCronExecution = 100
    }

    svixWebhookConfig {
      enableWebhook = false
      enableWebhook = ${?STARGAZER_SVIX_WEBHOOK_ENABLED}
      svixToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3MDY3NjY2MDYsImV4cCI6MjAyMjEyNjYwNiwibmJmIjoxNzA2NzY2NjA2LCJpc3MiOiJzdml4LXNlcnZlciIsInN1YiI6Im9yZ18yM3JiOFlkR3FNVDBxSXpwZ0d3ZFhmSGlyTXUifQ.Fzf1fCiP2tGvVUizkparbhDmdsO3Fh7y4cfNTXs5lDM"
      svixToken = ${?STARGAZER_SVIX_TOKEN}
      svixHost = svix${?networkDnsSuffix}
      svixHost = ${?STARGAZER_SVIX_HOST}
      svixPort = 8071
      svixPort = ${?STARGAZER_SVIX_PORT}
      challengeHeader = "x-anduin-webhook-validation-key"
      challengeKeyLength = 20
    }

    signatureRequestStatusChangeEventConfig {
      topic = "signature-request-status-change-event-topic"
    }

    actionLoggerConfig {
      isEnabled = false
      isEnabled = ${?STARGAZER_ACTIONLOGGER_ENABLED}
      flushInterval = 10 minutes
      flushInterval = ${?STARGAZER_ACTIONLOGGER_FLUSH_INTERVAL}
      bufferCapacity = 300
      bufferCapacity = ${?STARGAZER_ACTIONLOGGER_BUFFER_CAPACTITY}
    }

    natsConfig {
      rpcEndpoint = "nats://nats"${?networkDnsSuffix}":4222"
      rpcEndpoint = ${?STARGAZER_NATS_RPC_ENDPOINT}
      accountSeed = "SAAD57LNV3B4WFRQKHL6MGTPMVTFWWNI73BPUNVOHJJCCP77IQF7CHP6EQ"
      accountSeed = ${?STARGAZER_NATS_ACCOUNT_SEED}
      wsEndpoint = "ws://nats"${?networkDnsSuffix}":5222"
      wsEndpoint = ${?STARGAZER_NATS_WS_ENDPOINT}
    }

    multiRegionConfig {
      sharedKey = "575cdbb7b559764ed1e8697095ac68ad"
      sharedKey = ${?STARGAZER_SERVICES_MULTI_REGION_SHARED_KEY}
      loginTokenDuration = 5 minutes
    }

    getFeedbackBackendConfig {
      cspHash = ""
      cspHash = ${?STARGAZER_SERVICES_GETFEEDBACK_FUNDSUB_BUTTON_HASH}
    }

    memcachedConfig {
      host = "gondor-memcached"${?networkDnsSuffix}
      port = 11211
    }

    prismaticConfig {
      organizationId = "T3JnYW5pemF0aW9uOjVmMzIyNDQ5LTUzMGMtNDM3OC05MDIzLTE3ZDc5MzUzYzk5OQ=="
      organizationId = ${?STARGAZER_PRISMATIC_ORG_ID}
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      privateKey = ${?STARGAZER_PRISMATIC_PRIVATE_KEY}
      refreshToken = "_nXxKpSTud0kt3eOLEByM8xQ0ZUnT1OPEY6zEiVhVStfq"
      refreshToken = ${?STARGAZER_PRISMATIC_REFRESH_TOKEN}
    }

    globalDatabaseConfig {
      writeDataSource {
        host = shared-postgres${?networkDnsSuffix}
        host = ${?STARGAZER_SERVICES_GLOBAL_DATABASE_WRITE_HOST}
        port = 5432
        port = ${?STARGAZER_SERVICES_GLOBAL_DATABASE_WRITE_PORT}
        username = "postgres"
        username = ${?STARGAZER_SERVICES_GLOBAL_DATABASE_WRITE_USERNAME}
        password = "41e7b3ecb226768aab5fbb7e8cec94e0"
        password = ${?STARGAZER_SERVICES_GLOBAL_DATABASE_WRITE_PASSWORD}
        databaseName = "global_db_local"
        databaseName = ${?STARGAZER_SERVICES_GLOBAL_DATABASE_WRITE_DATABASE_NAME}
        concurrentSessions = 8
      }

      readDataSource {
        host = shared-postgres${?networkDnsSuffix}
        host = ${?STARGAZER_SERVICES_GLOBAL_DATABASE_READ_HOST}
        port = 5432
        port = ${?STARGAZER_SERVICES_GLOBAL_DATABASE_READ_PORT}
        username = "postgres"
        username = ${?STARGAZER_SERVICES_GLOBAL_DATABASE_READ_USERNAME}
        password = "41e7b3ecb226768aab5fbb7e8cec94e0"
        password = ${?STARGAZER_SERVICES_GLOBAL_DATABASE_READ_PASSWORD}
        databaseName = "global_db_local"
        databaseName = ${?STARGAZER_SERVICES_GLOBAL_DATABASE_READ_DATABASE_NAME}
        concurrentSessions = 8
      }
    }

    integPlatformConfig {
      messageReceiverHook = "https://hooks.integration-platform.anduin.app/trigger/SW5zdGFuY2VGbG93Q29uZmlnOjdmNzBlNzRkLWFhMGEtNGQ1OC04YmU1LTM0MzVjNzljMWYyZQ=="
      messageReceiverHook = ${?STARGAZER_INTEG_PLATFORM_RECEIVER_HOOK}
      headerApiKey = "a4b78b3bbf959e74b26f678ee1653e02"
      headerApiKey = ${?STARGAZER_INTEG_PLATFORM_API_KEY}
    }
    
    zotRegistryConfig {
      host = "https://zot.ap-southeast-1.anduin.dev"
      host = ${?STARGAZER_SERVICES_ZOT_HOST}
      username = "gondor"
      username = ${?STARGAZER_SERVICES_ZOT_USERNAME}
      password = "6be4ac18e8b6bc70550c63555282afbd"
      password = ${?STARGAZER_SERVICES_ZOT_PASSWORD}
      repoPrefix = "dev"
      repoPrefix = ${?STARGAZER_SERVICES_ZOT_REPO_PREFIX}
    }

    riaEntityConfig {
      eventTopic = "ria-entity-event"
    }

    fundSubPrismaticConfig {
        requestDemoEndpoint = "https://hooks.integration-platform.anduin.app/trigger/SW5zdGFuY2VGbG93Q29uZmlnOjU2NDkzNmUzLWIyZTYtNGZlNi05MjMxLTQ3MmViMzdhZmMxNg=="
        apiKey = "d6e02212c65eea0f716a329ac2f58473"
    }

    elasticsearch {
      scheme = "http"
      scheme = ${?STARGAZER_ELASTICSEARCH_SCHEME}
      host = elasticsearch${?networkDnsSuffix}
      host = ${?STARGAZER_ELASTICSEARCH_HOST}
      port = 9200
      port = ${?STARGAZER_ELASTICSEARCH_PORT}
    }

    dataExtractStatusChangeEventConfig {
      topic = "data-extract-status-change-event-topic"
    }

    oneTimeLinkConfig {
      duration = 1 day
    }

    vespaConfig {
      scheme = "http"
      scheme = ${?STARGAZER_SERVICES_VESPA_SCHEME}
      host = vespa${?networkDnsSuffix}
      host = ${?STARGAZER_SERVICES_VESPA_HOST}
      port = 8080
      port = ${?STARGAZER_SERVICES_VESPA_PORT}
      namespace = "dev"
      namespace = ${?STARGAZER_SERVICES_VESPA_NAMESPACE}
    }

    dataRoomSemanticSearchConfig {
      isEnabled = false
      isEnabled = ${?STARGAZER_SERVICES_DATA_ROOM_SEMANTIC_SEARCH_ENABLED}
    }
  }

  # frontend config
  frontendConfig {

    clientId = "gondor"

    graphql = {
      timeout = 3 minutes
      noLoginTimeout = 5 minutes
    }

    idleConfig = {
      activeDuration = 15 minutes
      activeCheckInterval = 1 minute
      passiveDuration = 4 hours
      passiveCheckInterval = 1 minute
    }

    tokenRefresher = {
      tokenRefreshDuration = 5 minutes
      guardingDuration = 30 seconds
    }

    documentService = {
      maxEntityLogoSizeInMbs = 1
      maxNumberOfUploadFiles = 1000
      defaultTaskTimeout = 30 seconds
      longTaskTimeout = 70 seconds
      zipTaskTimeout = 5 minutes
      uppyTimeout = 300 seconds
    }

    onlyOffice {
      scheme = "http"
      scheme = ${?STARGAZER_ONLYOFFICE_SCHEME}
      host = oo-doc-server${?networkDnsSuffix}
      host = ${?STARGAZER_ONLYOFFICE_HOST}
      publicScheme = "http"
      publicScheme = ${?STARGAZER_ONLYOFFICE_PUBLIC_SCHEME}
      publicHost = oo-doc-server${?networkDnsSuffix}
      publicHost = ${?STARGAZER_ONLYOFFICE_PUBLIC_HOST}
      gondorServer = "http://gondor:8080"
      gondorServer = ${?STARGAZER_ONLYOFFICE_GONDOR_SERVER}
    }

    mail = {
      domain = ${?stargazer.backendConfig.email.standardDomain}
    }

    gtmConfig {
      containerId = ""
      containerId = ${?STARGAZER_SERVICES_GTM_CONTAINER_ID}
    }

    recaptchaFrontendConfig {
      sitekey = "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"
      sitekey = ${?STARGAZER_SERVICES_RECAPTCHA_SITEKEY}
    }

    recaptchaV3FrontendConfig {
      sitekey = "6Le2kdoUAAAAAJ3mXqvnFGkFDBjyQoIHjtlL3T2Y"
      sitekey = ${?STARGAZER_SERVICES_RECAPTCHA_V3_SITEKEY}
    }

    turnstileFrontendConfig {
      invisibleSiteKey = "1x00000000000000000000BB"
      invisibleSiteKey = ${?STARGAZER_SERVICES_TURNSTILE_SITEKEY_INVISIBLE}
      visibleSiteKey = "1x00000000000000000000AA"
      visibleSiteKey = ${?STARGAZER_SERVICES_TURNSTILE_SITEKEY_VISIBLE}
    }

    oauth2FrontendConfig {
      clients = [
      ]
    }

    versionCheckingConfig {
      userActivityThreshold = 1 minutes
      timeOut = 60 minutes
      refreshDuration = 5 minutes
    }

    getFeedbackConfig {
      fundSubButtonId = ""
      fundSubButtonId = ${?STARGAZER_SERVICES_GETFEEDBACK_FUNDSUB_BUTTON_ID}
    }

    cdnConfig {
      isEnabled = false
      isEnabled = ${?STARGAZER_CDN_ENABLED}
      root = ""
      root = ${?STARGAZER_CDN_ROOT}
      # Only allowed static config. No dynamic set via environment
      # Only for local environment when using together with CDN
      overrideVersion = ""
    }

    asyncApiConfig {
      isEnabled = true
      isEnabled = ${?STARGAZER_ASYNC_API_ENABLED}
    }
  }

  # e2e config
  e2eConfig {
    driver = {
      chrome = "chromedriver"
      firefox = "geckodriver"
    }
  }

  customerSupport {
    contact = "<EMAIL>"
    contact = ${?STARGAZER_CUSTOMER_SUPPORT_EMAIL}
  }

  publicApiConfig {
    endpointConfig = {
      defaultTimeout = 60 seconds
      defaultTimeout = ${?STARGAZER_PUBLIC_API_ENDPOINT_DEFAULT_TIMEOUT}
      maxPendingRequests = 200
      maxPendingRequests = ${?STARGAZER_PUBLIC_API_ENDPOINT_MAX_PENDING_REQUESTS}
      commitFileUploadTimeout = 300 seconds
    }

    syncWorkflowConfig = {
      workflowExecutionDefaultTimeout = 60 seconds
      workflowExecutionDefaultTimeout = ${?STARGAZER_PUBLIC_API_SYNC_WORKFLOW_WORKFLOW_EXECUTION_DEFAULT_TIMEOUT}
      workflowRunDefaultTimeout = 30 seconds
      workflowRunDefaultTimeout = ${?STARGAZER_PUBLIC_API_SYNC_WORKFLOW_WORKFLOW_RUN_DEFAULT_TIMEOUT}
      workflowTaskDefaultTimeout = 30 seconds
      workflowTaskDefaultTimeout = ${?STARGAZER_PUBLIC_API_SYNC_WORKFLOW_TASK_DEFAULT_TIMEOUT}
      activityStartToCloseDefaultTimeout = 30 seconds
      activityStartToCloseDefaultTimeout = ${?STARGAZER_PUBLIC_API_SYNC_WORKFLOW_ACTIVITY_START_TO_CLOSE_DEFAULT_TIMEOUT}
    }

    asyncWorkflowConfig = {
      maxConcurrentActivityExecutionSize = 50
      maxConcurrentActivityExecutionSize = ${?STARGAZER_PUBLIC_API_ASYNC_WORKFLOW_MAX_CONCURRENT_ACTIVITY_EXECUTION_SIZE}
      maxConcurrentTaskExecutionSize = 10
      maxConcurrentTaskExecutionSize = ${?STARGAZER_PUBLIC_API_ASYNC_WORKFLOW_MAX_CONCURRENT_TASK_EXECUTION_SIZE}
      workflowExecutionTimeout = 60 minutes
      workflowExecutionTimeout = ${?STARGAZER_PUBLIC_API_ASYNC_WORKFLOW_WORKFLOW_EXECUTION_TIMEOUT}
      workflowRunTimeout = 20 minutes
      workflowRunTimeout = ${?STARGAZER_PUBLIC_API_ASYNC_WORKFLOW_WORKFLOW_RUN_TIMEOUT}
      workflowTaskTimeout = 60 seconds
      workflowTaskTimeout = ${?STARGAZER_PUBLIC_API_ASYNC_WORKFLOW_WORKFLOW_TASK_TIMEOUT}
      activityStartToCloseTimeout = 120 seconds
      activityStartToCloseTimeout = ${?STARGAZER_PUBLIC_API_ASYNC_WORKFLOW_ACTIVITY_START_TO_CLOSE_TIMEOUT}
    }

    webhookConfig = {
      challengeHeader = "x-anduin-webhook-validation-key"
      challengeKeyLength = 20
      requestTimeout = 10 seconds
      brokenCounterLimit = 3
      notificationEmailAddress = "<EMAIL>"
      webhookPayloadKafkaTopic = "webhook-payload-kafka-topic"
      webhookPayloadAndEndpointKafkaTopic = "webhook-payload-and-endpoint-kafka-topic"
      webhookIntegrationMessageKafkaTopic = "webhook-integration-message-kafka-topic"
      dataRoomPayloadKafkaTopic = "data-room-webhook-payload-kafka-topic"
    }

    customIntegrationFlowConfig {
        endpoint: "",
        endpoint = ${?STARGAZER_CUSTOM_INTEGRATION_FLOW_ENDPOINT}
        apiKey: ""
        apiKey: ${?STARGAZER_CUSTOM_INTEGRATION_FLOW_KEY}
    }
  }
}

kamon {
  modules {
    host-metrics {
      enabled = no
    }
  }

  prometheus {
    embedded-server {
      hostname = "0.0.0.0"
      port = 28080
    }
  }
}

# vi: ft=hocon
