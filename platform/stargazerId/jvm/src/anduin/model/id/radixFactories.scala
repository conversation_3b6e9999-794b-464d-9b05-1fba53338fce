// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.model.id

import java.time.Instant
import java.util.UUID
import scala.util.Random

import cats.data.NonEmptyList
import com.github.kolotaev.ride.Id
import sqids.Sqids
import sqids.options.Alphabet

import anduin.id.TransactionId
import anduin.id.account.*
import anduin.id.actionlogger.{ActionEventId, LoggingEventPublishingStateId}
import anduin.id.admin.AdminUserAuditLogId
import anduin.id.amlkyc.AmlCheckId
import anduin.id.annotation.{AnnotationDocumentId, AnnotationDocumentVersionId, TextractAnnotationStateId}
import anduin.id.asyncapiv2.AsyncApiId
import anduin.id.authwhitelabel.AuthenticationWhitelabelId
import anduin.id.autofill.ComputeFormMatchingId
import anduin.id.batchaction.{BatchActionId, BatchActionItemId}
import anduin.id.blueprint.{BlueprintId, BlueprintVersionId}
import anduin.id.comment.CommentThreadId
import anduin.id.common.IdPrefix
import anduin.id.communication.CommunicationTypeId
import anduin.id.contact.{ContactGroupId, ContactId}
import anduin.id.cue.table.CueTableId
import anduin.id.cue.{CueModuleId, CueModuleVersionId}
import anduin.id.customdomain.CustomDomainId
import anduin.id.dataextract.{DataExtractProjectId, DataExtractProjectItemId, DataExtractUserDocumentId}
import anduin.id.dataroom.DataRoomGroupId
import anduin.id.digitization.*
import anduin.id.disclaimer.DisclaimerId
import anduin.id.docdraft.{DocDraftId, DocDraftItemId}
import anduin.id.docrequest.{DocRequestId, FormSubmissionId}
import anduin.id.dynamicform.s3form.*
import anduin.id.dynamicform.testtool.{DynamicFormTestDataSetId, DynamicFormTestId, DynamicFormTestScriptId}
import anduin.id.entity.*
import anduin.id.environment.*
import anduin.id.flow.{TFlowEventId, TFlowId, TFlowQueryId, TFlowType}
import anduin.id.form.*
import anduin.id.funddata.*
import anduin.id.funddata.fund.*
import anduin.id.funddata.note.{FundDataNoteGroupId, FundDataNoteId}
import anduin.id.funddata.portal.PortalInstanceId
import anduin.id.fundsub.*
import anduin.id.fundsub.auditlog.FundSubAuditLogId
import anduin.id.fundsub.dashboard.FilterPresetId
import anduin.id.fundsub.dataextract.{FundSubDataExtractRequestId, FundSubDataExtractTestProfileId}
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.fundsub.rebac.PortalFundSubTeamId
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.id.idmapping.IdMappingGroupId
import anduin.id.integration.IntegrationId
import anduin.id.investmententity.{InvestmentEntityId, InvestorProfileAuditLogId}
import anduin.id.investorportal.InvestorPortalId
import anduin.id.issuetracker.*
import anduin.id.job.DataRequestJobId
import anduin.id.link.{OneTimeLinkId, ProtectedLinkId}
import anduin.id.linkrestriction.{LinkRestrictionId, LinkRestrictionListId}
import anduin.id.lpprofile.{LpProfileDocumentId, LpProfileId}
import anduin.id.notification.NotificationId
import anduin.id.oauth2.{Oauth2IntegrationClientId, Oauth2IntegrationStateId, Oauth2RefreshTokenId}
import anduin.id.offering.*
import anduin.id.ontology.{OntologyAsaId, SharedOntologyAsaId}
import anduin.id.review.{ReviewConfigId, ReviewFlowId, ReviewStepConfigId}
import anduin.id.ria.{RiaEntityId, RiaFundGroupId}
import anduin.id.role.TransactionParticipantRoleId
import anduin.id.role.fundsub.{FundManagerRoleId, FundsubInvestorRoleId}
import anduin.id.role.portal.*
import anduin.id.sa.{MappingDestinationId, SaDataTemplateId, SaProfileId}
import anduin.id.serverless.ServerlessRequestId.valuePrefix
import anduin.id.serverless.{ServerlessAction, ServerlessRequestId}
import anduin.id.signature.{SignatureModuleId, SignatureRequestId, SignatureRequestItemId}
import anduin.id.tag.{TagItemId, TagListId}
import anduin.id.task.{LongRunningTaskId, LongTaskId}
import anduin.id.taskrequest.{TaskRequestId, TaskRequestItemId}
import anduin.id.textract.{MergeTextractProjectId, TextractWorkflowId}
import anduin.id.tinyurl.TinyUrlId
import anduin.id.transaction.TransactionParticipantId
import anduin.id.upload.BatchUploadId
import anduin.id.user.UserRestrictedId
import anduin.id.webcontent.WebContentId
import anduin.id.webhook.{WebhookEndpointId, WebhookEventId}
import anduin.id.zapier.{ZapEventId, ZapId}
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.token.{JwtId, OauthStateId, ShortlinkId, TokenMetaId}
import anduin.model.common.user.UserId
import anduin.model.document.DocumentStorageId
import anduin.model.id.email.provider.EmailProviderId
import anduin.model.id.email.sending.EmailSendingTaskId
import anduin.model.id.email.{GeneratedEmailId, InternalEmailId}
import anduin.model.id.notification.NotificationSpaceId
import anduin.model.id.stage.*
import anduin.radix.RadixId
import anduin.radix.factory.{RadixChildNodeFactory, RadixRootNodeFactory, RadixStringFactory}
import anduin.radix.spec.RadixRootNodeSpec
import anduin.refined.Refined
import anduin.util.Aws3Utils

object UserIdFactory {

  def unsafeRandomId: UserId =
    UserId(
      UserId.valuePrefix.value + RadixStringFactory.Default
        .randomString(UserId.valueLength - UserId.valuePrefix.value.length)
    )

}

object UserSessionIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: UserRestrictedId): UserSessionId =
    unsafeNode[Refined[UserSessionId], UserRestrictedId, UserSessionId](parent)

}

object UserSignupIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: UserSignupId = unsafeNode[Refined[UserSignupId], UserSignupId]
}

object UserResetPasswordIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: UserResetPasswordId = unsafeNode[Refined[UserResetPasswordId], UserResetPasswordId]
}

object UserAccountRecoveryIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: UserAccountRecoveryId = unsafeNode[Refined[UserAccountRecoveryId], UserAccountRecoveryId]
}

object Oauth2StateIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: Oauth2StateId = unsafeNode[Refined[Oauth2StateId], Oauth2StateId]
}

object LinkAccountIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: LinkAccountId = unsafeNode[Refined[LinkAccountId], LinkAccountId]
}

object Oauth2IntegrationClientIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: Oauth2IntegrationClientId =
    unsafeNode[Refined[Oauth2IntegrationClientId], Oauth2IntegrationClientId]

}

object Oauth2IntegrationStateIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: Oauth2IntegrationStateId = unsafeNode[Refined[Oauth2IntegrationStateId], Oauth2IntegrationStateId]
}

object Oauth2RefreshTokenIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: Oauth2RefreshTokenId = unsafeNode[Refined[Oauth2RefreshTokenId], Oauth2RefreshTokenId]
}

object AdminUserAuditLogIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: AdminUserAuditLogId = unsafeNode[Refined[AdminUserAuditLogId], AdminUserAuditLogId]
}

object CookieConsentIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: CookieConsentId = unsafeNode[Refined[CookieConsentId], CookieConsentId]
}

object EnterpriseLoginConfigIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: EnterpriseLoginConfigId = unsafeNode[Refined[EnterpriseLoginConfigId], EnterpriseLoginConfigId]
}

object EnterpriseLoginLinkIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: EnterpriseLoginLinkId = unsafeNode[Refined[EnterpriseLoginLinkId], EnterpriseLoginLinkId]
}

object EnterpriseLoginStateIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: EnterpriseLoginStateId = unsafeNode[Refined[EnterpriseLoginStateId], EnterpriseLoginStateId]
}

object EnterpriseLoginUserLinkIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: EnterpriseLoginUserLinkId =
    unsafeNode[Refined[EnterpriseLoginUserLinkId], EnterpriseLoginUserLinkId]

}

object EnterpriseLoginProxyDataIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: EnterpriseLoginProxyDataId =
    unsafeNode[Refined[EnterpriseLoginProxyDataId], EnterpriseLoginProxyDataId]

}

object JwtIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: JwtId = unsafeNode[Refined[JwtId], JwtId]
}

object ShortlinkIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: ShortlinkId = unsafeNode[Refined[ShortlinkId], ShortlinkId]
}

object OauthStateIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: OauthStateId = unsafeNode[Refined[OauthStateId], OauthStateId]
}

object TokenMetaIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: TokenMetaId = unsafeNode[Refined[TokenMetaId], TokenMetaId]
}

object EntityIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: EntityId = unsafeNode[Refined[EntityId], EntityId]
}

object TransactionIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: TransactionId = unsafeNode[Refined[TransactionId], TransactionId]
}

object ContactIdFactory extends RadixRootNodeFactory {
  def generate(group: ContactGroupId): ContactId = ContactId(group, generatePart)

  def generatePart: Refined[ContactId] = unsafeValue[Refined[ContactId], ContactId]
}

object ContactGroupIdFactory extends RadixRootNodeFactory {
  def generate[I <: RadixId](space: I): ContactGroupId = ContactGroupId(space, generatePart)

  def generatePart: Refined[ContactGroupId] = unsafeValue[Refined[ContactGroupId], ContactGroupId]
}

object NewFlowIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: RadixId): NewFlowId = {
    unsafeNode[Refined[NewFlowId], RadixId, NewFlowId](parent)
  }

}

object NewFlowEventIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: NewFlowId): NewFlowEventId = {
    unsafeNode[Refined[NewFlowEventId], NewFlowId, NewFlowEventId](parent)
  }

}

object FundSubAdminInfoIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): FundSubAdminInfoId =
    unsafeNode[Refined[FundSubAdminInfoId], FundSubId, FundSubAdminInfoId](parent)

}

object FundSubItoolLpLockIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubLpId): FundSubItoolLpLockId =
    unsafeNode[Refined[FundSubItoolLpLockId], FundSubLpId, FundSubItoolLpLockId](parent)

}

object FundSubItoolLpNoteIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubLpId): FundSubItoolLpNoteId =
    unsafeNode[Refined[FundSubItoolLpNoteId], FundSubLpId, FundSubItoolLpNoteId](parent)

}

object FundSubLpTagIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): FundSubLpTagId =
    unsafeNode[Refined[FundSubLpTagId], FundSubId, FundSubLpTagId](parent)

}

object FundSubContactGroupIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): FundSubContactGroupId =
    unsafeNode[Refined[FundSubContactGroupId], FundSubId, FundSubContactGroupId](parent)

}

object ActivityLogIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: RadixId): ActivityLogId = {
    unsafeNode[Refined[ActivityLogId], RadixId, ActivityLogId](parent)
  }

}

object FunndSubAuditLogIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): FundSubAuditLogId =
    unsafeNode[Refined[FundSubAuditLogId], FundSubId, FundSubAuditLogId](parent)

}

object FundSubLpIdFactory extends RadixChildNodeFactory {
  def unsafeRandomId(parent: FundSubId): FundSubLpId = unsafeNode[Refined[FundSubLpId], FundSubId, FundSubLpId](parent)
}

object FundSubEmailTemplateIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): FundSubEmailTemplateId =
    unsafeNode[Refined[FundSubEmailTemplateId], FundSubId, FundSubEmailTemplateId](parent)

}

object FundSubInvestorGroupIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): FundSubInvestorGroupId =
    unsafeNode[Refined[FundSubInvestorGroupId], FundSubId, FundSubInvestorGroupId](parent)

}

object FundSubDashboardIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): FundSubDashboardId =
    unsafeNode[Refined[FundSubDashboardId], FundSubId, FundSubDashboardId](parent)

}

object CustomDataColumnIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): CustomDataColumnId =
    unsafeNode[Refined[CustomDataColumnId], FundSubId, CustomDataColumnId](parent)

}

object FundSubLpActivityIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubLpId): FundSubLpActivityId =
    unsafeNode[Refined[FundSubLpActivityId], FundSubLpId, FundSubLpActivityId](parent)

}

object FundSubLpFormIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: DynamicFormId): FundSubLpFormId =
    unsafeNode[Refined[FundSubLpFormId], DynamicFormId, FundSubLpFormId](parent)

}

object FundSubLpFormVersionIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FormVersionId): FundSubLpFormVersionId =
    unsafeNode[
      Refined[FundSubLpFormVersionId],
      FormVersionId,
      FundSubLpFormVersionId
    ](parent)

}

object InvestorFormUpdateLogIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): InvestorFormUpdateLogId =
    unsafeNode[Refined[InvestorFormUpdateLogId], FundSubId, InvestorFormUpdateLogId](parent)

}

object FundSubCloseIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): FundSubCloseId =
    unsafeNode[Refined[FundSubCloseId], FundSubId, FundSubCloseId](parent)

}

object FundManagerRoleIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): FundManagerRoleId =
    unsafeNode[Refined[FundManagerRoleId], FundSubId, FundManagerRoleId](parent)

}

object FundsubInvestorRoleIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubLpId): FundsubInvestorRoleId =
    unsafeNode[Refined[FundsubInvestorRoleId], FundSubLpId, FundsubInvestorRoleId](parent)

}

object FundSubExportTemplateIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): FundSubExportTemplateId =
    unsafeNode[Refined[FundSubExportTemplateId], FundSubId, FundSubExportTemplateId](parent)

}

object FundSubSupportingDocIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubLpId): FundSubSupportingDocId =
    unsafeNode[Refined[FundSubSupportingDocId], FundSubLpId, FundSubSupportingDocId](parent)

}

object FundSubBatchInvitationItemIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubBatchInvitationId): FundSubBatchInvitationItemId =
    unsafeNode[
      Refined[FundSubBatchInvitationItemId],
      FundSubBatchInvitationId,
      FundSubBatchInvitationItemId
    ](parent)

}

object FundSubDataImportItemIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubDataImportId): FundSubDataImportItemId =
    unsafeNode[
      Refined[FundSubDataImportItemId],
      FundSubDataImportId,
      FundSubDataImportItemId
    ](parent)

}

object FundSubDataExportItemIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubDataExportId): FundSubDataExportItemId =
    unsafeNode[
      Refined[FundSubDataExportItemId],
      FundSubDataExportId,
      FundSubDataExportItemId
    ](parent)

}

object FundSubBatchActionItemIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubBatchActionId): FundSubBatchActionItemId =
    unsafeNode[
      Refined[FundSubBatchActionItemId],
      FundSubBatchActionId,
      FundSubBatchActionItemId
    ](parent)

}

object FundSubSelfServiceExportTemplateIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): FundSubSelfServiceExportTemplateId =
    unsafeNode[
      Refined[FundSubSelfServiceExportTemplateId],
      FundSubId,
      FundSubSelfServiceExportTemplateId
    ](parent)

}

object FundSubRiaGroupIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): FundSubRiaGroupId =
    unsafeNode[
      Refined[FundSubRiaGroupId],
      FundSubId,
      FundSubRiaGroupId
    ](parent)

}

object DynamicFormIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: DynamicFormId = unsafeNode[Refined[DynamicFormId], DynamicFormId]
}

object DynamicFormTestDataSetIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: DynamicFormTestId): DynamicFormTestDataSetId =
    unsafeNode[
      Refined[DynamicFormTestDataSetId],
      DynamicFormTestId,
      DynamicFormTestDataSetId
    ](parent)

}

object DynamicFormTestScriptIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: DynamicFormTestId): DynamicFormTestScriptId =
    unsafeNode[
      Refined[DynamicFormTestScriptId],
      DynamicFormTestId,
      DynamicFormTestScriptId
    ](parent)

}

object FormIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: FormId = unsafeNode[Refined[FormId], FormId]
}

object DigitizationFileIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: DigitizationFileId = unsafeNode[Refined[DigitizationFileId], DigitizationFileId]
}

object DigitizationFileActivityIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(fileId: DigitizationFileId): DigitizationFileActivityId =
    unsafeNode[
      Refined[DigitizationFileActivityId],
      DigitizationFileId,
      DigitizationFileActivityId
    ](fileId)

}

object FormFolderIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: FormFolderId = unsafeNode[Refined[FormFolderId], FormFolderId]
}

object DigitizationFolderIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: DigitizationFolderId = unsafeNode[Refined[DigitizationFolderId], DigitizationFolderId]
}

object DigitizationTagIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: DigitizationTagId = unsafeNode[Refined[DigitizationTagId], DigitizationTagId]
}

object FormVersionIdFactory extends RadixChildNodeFactory {
  def unsafeRandomId(formId: FormId): FormVersionId = unsafeNode[Refined[FormVersionId], FormId, FormVersionId](formId)
}

object FormActivityIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(formId: FormId): FormActivityId =
    unsafeNode[Refined[FormActivityId], FormId, FormActivityId](formId)

}

object FormTestSuiteIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(formVersionId: FormVersionId): FormTestSuiteId =
    unsafeNode[Refined[FormTestSuiteId], FormVersionId, FormTestSuiteId](formVersionId)

}

object FormTestScriptIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(formId: FormId): FormTestScriptId =
    unsafeNode[Refined[FormTestScriptId], FormId, FormTestScriptId](formId)

}

object FormVersionDataIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(formVersionId: FormVersionId): FormVersionDataId =
    unsafeNode[Refined[FormVersionDataId], FormVersionId, FormVersionDataId](formVersionId)

}

object FormTemplateMappingIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(formVersionId: FormVersionId): FormTemplateMappingId =
    unsafeNode[Refined[FormTemplateMappingId], FormVersionId, FormTemplateMappingId](formVersionId)

}

object FormTemplateMappingVersionIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(mappingId: FormTemplateMappingId): FormTemplateMappingVersionId =
    unsafeNode[
      Refined[FormTemplateMappingVersionId],
      FormTemplateMappingId,
      FormTemplateMappingVersionId
    ](mappingId)

}

object AnnotationDocumentIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: AnnotationDocumentId = unsafeNode[Refined[AnnotationDocumentId], AnnotationDocumentId]
}

object TextractAnnotationStateIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: TextractAnnotationStateId =
    unsafeNode[Refined[TextractAnnotationStateId], TextractAnnotationStateId]

  def fromChecksum(checksum: String): TextractAnnotationStateId = {
    val spec: RadixRootNodeSpec[Refined[TextractAnnotationStateId], TextractAnnotationStateId] = implicitly
    spec.buildNode(TextractAnnotationStateId.unsafeMakeValue(valuePrefix.value + checksum))
  }

}

object AnnotationDocumentVersionIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: AnnotationDocumentId): AnnotationDocumentVersionId =
    unsafeNode[
      Refined[AnnotationDocumentVersionId],
      AnnotationDocumentId,
      AnnotationDocumentVersionId
    ](parent)

}

object DataTemplateIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: DataTemplateId = unsafeNode[Refined[DataTemplateId], DataTemplateId]
}

object DataTemplateVersionIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(templateId: DataTemplateId): DataTemplateVersionId =
    unsafeNode[Refined[DataTemplateVersionId], DataTemplateId, DataTemplateVersionId](templateId)

}

object BlueprintIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: BlueprintId = unsafeNode[Refined[BlueprintId], BlueprintId]
}

object BlueprintVersionIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(blueprintId: BlueprintId): BlueprintVersionId =
    unsafeNode[Refined[BlueprintVersionId], BlueprintId, BlueprintVersionId](blueprintId)

}

object FormDiffJobIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: FormDiffJobId = unsafeNode[Refined[FormDiffJobId], FormDiffJobId]
}

object S3FormChangeIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: DynamicFormId): S3FormChangeId =
    unsafeNode[Refined[S3FormChangeId], DynamicFormId, S3FormChangeId](parent)

}

object EmailTemplateIdFactory extends RadixRootNodeFactory {

  def generate(
    emailPath: String
  )(
    using spec: RadixRootNodeSpec[Refined[EmailTemplateId], EmailTemplateId]
  ): EmailTemplateId = {
    val prefix = spec.valuePrefix
    val id = emailPath.toLowerCase().replace("/", "+")
    val nodeValue = spec.unsafeValue(prefix.value + id)
    spec.buildNode(nodeValue)
  }

}

object EmailDomainIdFactory extends RadixRootNodeFactory {

  def generate(
    emailDomain: String
  )(
    using spec: RadixRootNodeSpec[Refined[EmailDomainId], EmailDomainId]
  ): EmailDomainId = {
    val prefix = spec.valuePrefix
    // Replace all . in email domain to + to not mess up Id parser
    val id = emailDomain.toLowerCase().replace(".", "+")
    val nodeValue = spec.unsafeValue(prefix.value + id)
    spec.buildNode(nodeValue)
  }

}

object EmailProviderIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: EmailProviderId = unsafeNode[Refined[EmailProviderId], EmailProviderId]
}

object EmailSendingTaskIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: EmailSendingTaskId = unsafeNode[Refined[EmailSendingTaskId], EmailSendingTaskId]
}

object EmailThreadIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: EmailThreadId = unsafeNode[Refined[EmailThreadId], EmailThreadId]
}

object EmailAttachmentIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: EmailAttachmentId = unsafeNode[Refined[EmailAttachmentId], EmailAttachmentId]
}

object UserEmailThreadIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: EmailThreadId): UserEmailThreadId =
    unsafeNode[Refined[UserEmailThreadId], EmailThreadId, UserEmailThreadId](parent)

}

object TeamIdFactory extends RadixChildNodeFactory {
  def unsafeRandomId(parent: RadixId): TeamId = unsafeNode[NonEmptyList[Refined[TeamId]], RadixId, TeamId](parent)

  def unsafeRandomValue: Refined[TeamId] = unsafeValue[NonEmptyList[Refined[TeamId]], TeamId].head
}

object FundSubIdFactory extends RadixChildNodeFactory {
  def unsafeRandomId(parent: TransactionId): FundSubId = unsafeNode[Refined[FundSubId], TransactionId, FundSubId](parent)
}

object InvestmentFundIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): InvestmentFundId =
    unsafeNode[Refined[InvestmentFundId], FundSubId, InvestmentFundId](parent)

}

object SignatureIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: UserRestrictedId): SignatureId =
    unsafeNode[Refined[SignatureId], UserRestrictedId, SignatureId](parent)

}

object EmailContactIdFactory {

  def generate(parent: TransactionId, emailAddress: String): EmailContactId = {
    EmailContactId(
      parent,
      EmailContactId.spec.unsafeValue(
        EmailContactId.valuePrefix.value + EmailIdUtils.base64Sha256(EmailAddress.normalizeAddress(emailAddress))
      )
    )
  }

}

object InternalEmailIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: RadixId): InternalEmailId =
    unsafeNode[Refined[InternalEmailId], RadixId, InternalEmailId](parent)

}

object DataRoomHomeSectionIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: DataRoomWorkflowId): DataRoomHomeSectionId =
    unsafeNode[
      Refined[DataRoomHomeSectionId],
      DataRoomWorkflowId,
      DataRoomHomeSectionId
    ](parent)

}

object DataRoomWorkflowIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: TransactionId): DataRoomWorkflowId =
    unsafeNode[Refined[DataRoomWorkflowId], TransactionId, DataRoomWorkflowId](parent)

}

object DataRoomGroupIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: DataRoomWorkflowId): DataRoomGroupId =
    unsafeNode[Refined[DataRoomGroupId], DataRoomWorkflowId, DataRoomGroupId](parent)

}

object CommunicationTypeIdFactory extends RadixChildNodeFactory {

  def generate(parent: FundDataFirmId): CommunicationTypeId = CommunicationTypeId(parent, generatePart)

  def generatePart: Refined[CommunicationTypeId] = unsafeValue[Refined[CommunicationTypeId], CommunicationTypeId]

}

object FolderIdFactory extends RadixChildNodeFactory {
  def generate(folder: FolderId): FolderId = folder.addSuffix(generateSuffix)

  def generateSuffix: Refined[FolderId] = unsafeValue[NonEmptyList[Refined[FolderId]], FolderId].head
}

object FileIdFactory extends RadixChildNodeFactory {
  def generate(folder: FolderId): FileId = FileId(folder, generatePart)

  def generatePart: Refined[FileId] = unsafeValue[Refined[FileId], FileId]
}

object ShortcutIdFactory extends RadixChildNodeFactory {
  def generate(folder: FolderId): ShortcutId = ShortcutId(folder, generatePart)

  def generatePart: Refined[ShortcutId] = unsafeValue[Refined[ShortcutId], ShortcutId]
}

object PublicFileIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: PublicFileId = unsafeNode[Refined[PublicFileId], PublicFileId]
}

object CommentThreadIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: CommentThreadId = unsafeNode[Refined[CommentThreadId], CommentThreadId]
}

object DocDraftIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: UserRestrictedId): DocDraftId =
    unsafeNode[Refined[DocDraftId], UserRestrictedId, DocDraftId](parent)

}

object DocDraftItemIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: DocDraftId): DocDraftItemId =
    unsafeNode[Refined[DocDraftItemId], DocDraftId, DocDraftItemId](parent)

}

object ActionEventIdFactory extends RadixRootNodeFactory(RadixStringFactory.timeSensitive()) {
  def unsafeRandomId: ActionEventId = ActionEventId(unsafeValue[Refined[ActionEventId], ActionEventId])
}

object ServerlessRequestIdFactory {

  def unsafeRandomId(action: ServerlessAction): ServerlessRequestId =
    ServerlessRequestId(Refined.unsafeApply(valuePrefix.value + action.value + Id().toString()))

}

object LoggingEventPublishingStateIdFactory extends RadixRootNodeFactory(RadixStringFactory.timeSensitive()) {

  def unsafeRandomId: LoggingEventPublishingStateId = LoggingEventPublishingStateId(
    unsafeValue[Refined[LoggingEventPublishingStateId], LoggingEventPublishingStateId]
  )

}

// NotificationId is prefixed by the current time, so that we can query latest notification by using only the Id
object NotificationIdFactory extends RadixRootNodeFactory(RadixStringFactory.timeSensitive()) {

  def unsafeRandomId(spaceId: NotificationSpaceId): NotificationId =
    NotificationId(
      spaceId,
      unsafeValue[Refined[NotificationId], NotificationId]
    )

}

object GlobalOfferingIdFactory extends RadixRootNodeFactory {

  def generateId(offeringId: OfferingId): GlobalOfferingId =
    GlobalOfferingId(GlobalOfferingId.unsafeMakeValue(GlobalOfferingId.valuePrefix.value + offeringId.value))

}

object NotificationSpaceIdFactory extends RadixChildNodeFactory {

  def generate[FC <: RadixId](space: NotificationSpaceId, id: FC): NotificationSpaceId = {
    space.addSuffix(NotificationSpaceId.generateSuffixFromRadixId(id))
  }

  def generate(space: NotificationSpaceId, ids: RadixId*): NotificationSpaceId = {
    ids.foldLeft(space) { case (space, id) => generate(space, id) }
  }

  def generate(ids: NonEmptyList[RadixId]): NotificationSpaceId = {
    ids.tail.foldLeft(NotificationSpaceId.channelSystemNotificationSpaceId(ids.head)) { case (space, id) =>
      generate(space, id)
    }
  }

}

object CustomDomainIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: CustomDomainId = unsafeNode[Refined[CustomDomainId], CustomDomainId]
}

object TinyUrlIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: TinyUrlId = unsafeNode[Refined[TinyUrlId], TinyUrlId]
}

object SignatureModuleIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: TransactionId): SignatureModuleId =
    unsafeNode[Refined[SignatureModuleId], TransactionId, SignatureModuleId](parent)

}

object SignatureRequestIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: SignatureModuleId): SignatureRequestId =
    unsafeNode[Refined[SignatureRequestId], SignatureModuleId, SignatureRequestId](parent)

}

object SignatureRequestItemIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: SignatureRequestId): SignatureRequestItemId =
    unsafeNode[
      Refined[SignatureRequestItemId],
      SignatureRequestId,
      SignatureRequestItemId
    ](parent)

}

object IssueTrackerModuleIdFactory extends RadixChildNodeFactory {

  def generate(userId: UserId, trxnId: TransactionId): TransactionParticipantOfferingId =
    TransactionParticipantOfferingId(
      TransactionParticipantId(trxnId, UserRestrictedId(userId)),
      OfferingId.IssueTracker
    )

}

object IssueIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(trxnId: TransactionId): IssueId =
    unsafeNode[Refined[IssueId], TransactionOfferingId, IssueId](TransactionOfferingId(trxnId, OfferingId.IssueTracker))

}

object MentionIdFactory extends RadixChildNodeFactory {
  def unsafeRandomId(parent: IssueId): MentionId = unsafeNode[Refined[MentionId], IssueId, MentionId](parent)
}

object IssueListIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(trxnId: TransactionId): IssueListId =
    unsafeNode[Refined[IssueListId], TransactionOfferingId, IssueListId](
      TransactionOfferingId(trxnId, OfferingId.IssueTracker)
    )

}

object TransactionParticipantRoleIdFactory extends RadixChildNodeFactory {

  def generate(userId: UserId, trxnId: TransactionId): TransactionParticipantRoleId =
    TransactionParticipantRoleId(
      TransactionParticipantId(trxnId, UserRestrictedId(userId))
    )

}

object OfferingResourceIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(trxnId: TransactionId, offeringId: OfferingId = OfferingId.IssueTracker): OfferingResourceId =
    unsafeNode[
      Refined[OfferingResourceId],
      TransactionOfferingId,
      OfferingResourceId
    ](
      TransactionOfferingId(trxnId, offeringId)
    )

}

object IssueTrackerCollaborationChannelIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(trxnId: TransactionId): IssueTrackerCollaborationChannelId =
    unsafeNode[
      Refined[IssueTrackerCollaborationChannelId],
      TransactionOfferingId,
      IssueTrackerCollaborationChannelId
    ](
      TransactionOfferingId(trxnId, OfferingId.IssueTracker)
    )

}

object CollaboratorGroupIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(userId: UserId): CollaboratorGroupId =
    unsafeNode[Refined[CollaboratorGroupId], UserRestrictedId, CollaboratorGroupId](UserRestrictedId(userId))

}

object DataRequestJobIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parentId: RadixId): DataRequestJobId = {
    unsafeNode[Refined[DataRequestJobId], RadixId, DataRequestJobId](parentId)
  }

}

object DocRequestIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: RadixId): DocRequestId = {
    unsafeNode[Refined[DocRequestId], RadixId, DocRequestId](parent)
  }

}

object FormSubmissionIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: DocRequestId): FormSubmissionId = {
    unsafeNode[Refined[FormSubmissionId], DocRequestId, FormSubmissionId](parent)
  }

}

object ProtectedLinkIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: ProtectedLinkId = ProtectedLinkId(UUID.randomUUID)
}

object OneTimeLinkIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: OneTimeLinkId = OneTimeLinkId(UUID.randomUUID)
}

object DocumentStorageIdFactory {

  def generate(
    fileId: FileId,
    fileName: String
  ): DocumentStorageId = {
    typeUnsafeApply(fileId, fileName)
  }

  def generate(
    entityId: EntityId,
    fileName: String
  ): DocumentStorageId = {
    typeUnsafeApply(entityId, fileName)
  }

  def generate(
    fundSubId: FundSubId,
    fileName: String
  ): DocumentStorageId = {
    typeUnsafeApply(fundSubId, fileName)
  }

  def generate(
    disclaimerId: DisclaimerId,
    fileName: String
  ): DocumentStorageId = {
    typeUnsafeApply(disclaimerId, fileName)
  }

  def generate(
    dataRoomId: DataRoomWorkflowId,
    fileName: String
  ): DocumentStorageId = {
    typeUnsafeApply(dataRoomId, fileName)
  }

  def generate(
    environmentId: EnvironmentId,
    fileName: String
  ): DocumentStorageId = {
    typeUnsafeApply(environmentId, fileName)
  }

  def generate(
    firmId: FundDataFirmId,
    fileName: String
  ): DocumentStorageId = {
    typeUnsafeApply(firmId, fileName)
  }

  def generate(
    webContentId: WebContentId,
    fileName: String
  ): DocumentStorageId = {
    typeUnsafeApply(webContentId, fileName)
  }

  private def typeUnsafeApply(fileId: RadixId, filename: String): DocumentStorageId = {
    val sanitizedFilename = Aws3Utils.sanitizeFileOrFolderName(filename)
    DocumentStorageId(s"${fileId.idString.replace('.', '/')}/$sanitizedFilename")
  }

}

object LongRunningTaskIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: LongRunningTaskId =
    LongRunningTaskId(Refined.unsafeApply(LongRunningTaskId.valuePrefix.value + Id().toString()))

}

object LongTaskIdFactory extends RadixChildNodeFactory {
  def unsafeRandomId(parent: RadixId): LongTaskId = unsafeNode[Refined[LongTaskId], RadixId, LongTaskId](parent)
}

object PortalSectionRoleIdFactory extends RadixRootNodeFactory {

  def generateId(portalSectionId: PortalSectionId): PortalSectionRoleId =
    PortalSectionRoleId(
      PortalSectionRoleId.unsafeMakeValue(PortalSectionRoleId.valuePrefix.value + portalSectionId.value)
    )

}

object PortalGroupIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: PortalGroupId = unsafeNode[Refined[PortalGroupId], PortalGroupId]
}

object PortalFundSubTeamIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: PortalFundSubTeamId = unsafeNode[Refined[PortalFundSubTeamId], PortalFundSubTeamId]
}

object DisclaimerIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: DisclaimerId = unsafeNode[Refined[DisclaimerId], DisclaimerId]
}

object FormMappingIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: FormMappingId = unsafeNode[Refined[FormMappingId], FormMappingId]

}

object LpProfileIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: LpProfileId = unsafeNode[Refined[LpProfileId], LpProfileId]

}

object ComputeFormMatchingIdFactory extends RadixRootNodeFactory {

  def generate(
    srcFormVersionId: FormVersionId,
    srcAsaMappingLastUpdate: Instant,
    destFormVersionId: FormVersionId,
    destAsaMappingLastUpdate: Instant,
    matchingModeOpt: Option[String] = None
  ): ComputeFormMatchingId = {
    val value = Seq(
      Option(ComputeFormMatchingId.valuePrefix.value),
      Option(srcFormVersionId.idString.replaceAll("\\.", "_")),
      Option(destFormVersionId.idString.replaceAll("\\.", "_")),
      Option(srcAsaMappingLastUpdate.toEpochMilli.toString),
      Option(destAsaMappingLastUpdate.toEpochMilli.toString),
      matchingModeOpt
        .map(mode => Aws3Utils.sanitizeFileOrFolderName(mode).toLowerCase.trim)
        .filter(_.nonEmpty)
    ).flatten.mkString("_")

    ComputeFormMatchingId(ComputeFormMatchingId.spec.unsafeValue(value))
  }

}

object LpProfileDocumentIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: LpProfileId): LpProfileDocumentId =
    unsafeNode[Refined[LpProfileDocumentId], LpProfileId, LpProfileDocumentId](parent)

}

object InvestmentEntityIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: InvestmentEntityId = unsafeNode[Refined[InvestmentEntityId], InvestmentEntityId]

}

object InvestorProfileAuditLogIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: InvestorProfileAuditLogId =
    unsafeNode[Refined[InvestorProfileAuditLogId], InvestorProfileAuditLogId]

}

object AliasIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: AliasId = {
    val randomAlias = RadixStringFactory.Default.randomString(16)
    AliasId.generate(randomAlias)
  }

}

object ZapIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: ZapId = unsafeNode[Refined[ZapId], ZapId]
}

object ZapEventIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: ZapEventId = unsafeNode[Refined[ZapEventId], ZapEventId]
}

object LinkRestrictionIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: LinkRestrictionId = unsafeNode[Refined[LinkRestrictionId], LinkRestrictionId]
}

object LinkRestrictionListIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: LinkRestrictionListId = unsafeNode[Refined[LinkRestrictionListId], LinkRestrictionListId]
}

object OTPAuthenticationRequestIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: OTPAuthenticationRequestId =
    unsafeNode[Refined[OTPAuthenticationRequestId], OTPAuthenticationRequestId]

}

object AuthenticationWhitelabelIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: AuthenticationWhitelabelId =
    unsafeNode[Refined[AuthenticationWhitelabelId], AuthenticationWhitelabelId]

}

object WebhookEndpointIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: RadixId): WebhookEndpointId = {
    unsafeNode[Refined[WebhookEndpointId], RadixId, WebhookEndpointId](parent)
  }

}

object WebhookEventIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: RadixId): WebhookEventId = {
    unsafeNode[Refined[WebhookEventId], RadixId, WebhookEventId](parent)
  }

}

object ReviewConfigIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: RadixId): ReviewConfigId = {
    unsafeNode[Refined[ReviewConfigId], RadixId, ReviewConfigId](parent)
  }

}

object ReviewStepConfigIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: ReviewConfigId): ReviewStepConfigId = {
    unsafeNode[Refined[ReviewStepConfigId], ReviewConfigId, ReviewStepConfigId](parent)
  }

}

object ReviewFlowIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: RadixId): ReviewFlowId = {
    unsafeNode[Refined[ReviewFlowId], RadixId, ReviewFlowId](parent)
  }

}

object EnvironmentIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: EnvironmentId = unsafeNode[Refined[EnvironmentId], EnvironmentId]
}

object EnvironmentSSOBindingIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: EnvironmentId): EnvironmentSSOBindingId = {
    unsafeNode[
      Refined[EnvironmentSSOBindingId],
      EnvironmentId,
      EnvironmentSSOBindingId
    ](parent)
  }

}

object UserEnvironmentSSOBindingIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: UserEnvironmentSSOBindingId =
    unsafeNode[Refined[UserEnvironmentSSOBindingId], UserEnvironmentSSOBindingId]

}

object EmailDomainEnvironmentSSOBindingIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: EmailDomainEnvironmentSSOBindingId =
    unsafeNode[Refined[EmailDomainEnvironmentSSOBindingId], EmailDomainEnvironmentSSOBindingId]

}

object GlobalEmailDomainEnvironmentSSOBindingIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: EnvironmentId): GlobalEmailDomainEnvironmentSSOBindingId = {
    unsafeNode[
      Refined[GlobalEmailDomainEnvironmentSSOBindingId],
      EnvironmentId,
      GlobalEmailDomainEnvironmentSSOBindingId
    ](parent)
  }

}

/** FUND DATA
  */

object FundDataFirmIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: FundDataFirmId = unsafeNode[Refined[FundDataFirmId], FundDataFirmId]
}

object FundDataFirmDefaultSettingIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: FundDataFirmDefaultSettingId =
    unsafeNode[Refined[FundDataFirmDefaultSettingId], FundDataFirmDefaultSettingId]

}

object FundDataFirmOrganizationIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): FundDataFirmOrganizationId =
    unsafeNode[
      Refined[FundDataFirmOrganizationId],
      FundDataFirmId,
      FundDataFirmOrganizationId
    ](parent)

}

object FundDataRoleIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): FundDataRoleId =
    unsafeNode[Refined[FundDataRoleId], FundDataFirmId, FundDataRoleId](parent)

}

object FundDataClientGroupIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): FundDataClientGroupId =
    unsafeNode[Refined[FundDataClientGroupId], FundDataFirmId, FundDataClientGroupId](parent)

}

object FundDataFundIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): FundDataFundId =
    unsafeNode[Refined[FundDataFundId], FundDataFirmId, FundDataFundId](parent)

}

object FundDataDataRoomIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): FundDataDataRoomId =
    unsafeNode[Refined[FundDataDataRoomId], FundDataFirmId, FundDataDataRoomId](parent)

}

object FundDataFundGroupIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): FundDataFundGroupId =
    unsafeNode[Refined[FundDataFundGroupId], FundDataFirmId, FundDataFundGroupId](parent)

}

object FundDataFundSubscriptionIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFundId): FundDataFundSubscriptionId =
    unsafeNode[
      Refined[FundDataFundSubscriptionId],
      FundDataFundId,
      FundDataFundSubscriptionId
    ](parent)

}

object FundDataFundSubscriptionDocumentIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFundSubscriptionId): FundDataFundSubscriptionDocumentId =
    unsafeNode[
      Refined[FundDataFundSubscriptionDocumentId],
      FundDataFundSubscriptionId,
      FundDataFundSubscriptionDocumentId
    ](parent)

}

object FundDataInvestorIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): FundDataInvestorId =
    unsafeNode[Refined[FundDataInvestorId], FundDataFirmId, FundDataInvestorId](parent)

}

object FundDataInvestmentEntityIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataInvestorId): FundDataInvestmentEntityId =
    unsafeNode[
      Refined[FundDataInvestmentEntityId],
      FundDataInvestorId,
      FundDataInvestmentEntityId
    ](parent)

}

object FundDataFundSubSyncConflictIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFundSubscriptionId): FundDataFundSubSyncConflictId = unsafeNode[
    Refined[FundDataFundSubSyncConflictId],
    FundDataFundSubscriptionId,
    FundDataFundSubSyncConflictId
  ](parent)

}

object FundDataProfileConflictIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataInvestmentEntityId): FundDataProfileConflictId = unsafeNode[
    Refined[FundDataProfileConflictId],
    FundDataInvestmentEntityId,
    FundDataProfileConflictId
  ](parent)

}

object FundDataInvestmentEntityContactIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataInvestmentEntityId): FundDataInvestmentEntityContactId = unsafeNode[
    Refined[
      FundDataInvestmentEntityContactId
    ],
    FundDataInvestmentEntityId,
    FundDataInvestmentEntityContactId
  ](parent)

}

object FundDataInvestmentEntityAssessmentIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataInvestmentEntityId): FundDataInvestmentEntityAssessmentId = unsafeNode[
    Refined[
      FundDataInvestmentEntityAssessmentId
    ],
    FundDataInvestmentEntityId,
    FundDataInvestmentEntityAssessmentId
  ](parent)

}

object FundDataInvestmentEntityDocumentIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataInvestmentEntityId): FundDataInvestmentEntityDocumentId = unsafeNode[
    Refined[
      FundDataInvestmentEntityDocumentId
    ],
    FundDataInvestmentEntityId,
    FundDataInvestmentEntityDocumentId
  ](parent)

}

object FundDataInvestmentEntityDocumentTextractIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataInvestmentEntityDocumentId): FundDataInvestmentEntityDocumentTextractId =
    unsafeNode[
      Refined[
        FundDataInvestmentEntityDocumentTextractId
      ],
      FundDataInvestmentEntityDocumentId,
      FundDataInvestmentEntityDocumentTextractId
    ](parent)

}

object FundDataFundSubSyncEventIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): FundDataFundSubSyncEventId = unsafeNode[
    Refined[
      FundDataFundSubSyncEventId
    ],
    FundDataFirmId,
    FundDataFundSubSyncEventId
  ](parent)

}

object FundDataExpirationDateConfigIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): FundDataExpirationDateConfigId = unsafeNode[
    Refined[
      FundDataExpirationDateConfigId
    ],
    FundDataFirmId,
    FundDataExpirationDateConfigId
  ](parent)

}

object FundDataInvestmentEntityProfileHistoryIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): FundDataInvestmentEntityProfileHistoryId = unsafeNode[
    Refined[
      FundDataInvestmentEntityProfileHistoryId
    ],
    FundDataFirmId,
    FundDataInvestmentEntityProfileHistoryId
  ](parent)

}

object FundDataOpportunityPageIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): FundDataOpportunityPageId = unsafeNode[
    Refined[
      FundDataOpportunityPageId
    ],
    FundDataFirmId,
    FundDataOpportunityPageId
  ](parent)

}

object FundDataLandingPageLinkIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): FundDataLandingPageLinkId = unsafeNode[
    Refined[
      FundDataLandingPageLinkId
    ],
    FundDataFirmId,
    FundDataLandingPageLinkId
  ](parent)

}

object FundLegalEntityIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): FundLegalEntityId =
    unsafeNode[Refined[FundLegalEntityId], FundDataFirmId, FundLegalEntityId](parent)

}

object FundFamilyIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): FundFamilyId =
    unsafeNode[Refined[FundFamilyId], FundDataFirmId, FundFamilyId](parent)

}

object FundShareClassIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundLegalEntityId): FundShareClassId =
    unsafeNode[Refined[FundShareClassId], FundLegalEntityId, FundShareClassId](parent)

}

object FundInvestmentIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundLegalEntityId): FundInvestmentId =
    unsafeNode[Refined[FundInvestmentId], FundLegalEntityId, FundInvestmentId](parent)

}

object FundTransactionIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundLegalEntityId): FundTransactionId =
    unsafeNode[Refined[FundTransactionId], FundLegalEntityId, FundTransactionId](parent)

}

object PortalInstanceIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataFirmId): PortalInstanceId =
    unsafeNode[Refined[PortalInstanceId], FundDataFirmId, PortalInstanceId](parent)

}

object FundTransactionDocumentIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundTransactionId): FundTransactionDocumentId =
    unsafeNode[
      Refined[FundTransactionDocumentId],
      FundTransactionId,
      FundTransactionDocumentId
    ](parent)

}

object TaskRequestIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: TaskRequestId = unsafeNode[Refined[TaskRequestId], TaskRequestId]
}

object TaskRequestItemIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: TaskRequestId): TaskRequestItemId =
    unsafeNode[Refined[TaskRequestItemId], TaskRequestId, TaskRequestItemId](parent)

}

object WebContentIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: RadixId): WebContentId =
    unsafeNode[Refined[WebContentId], RadixId, WebContentId](parent)

}

object FundDataNoteGroupIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: RadixId): FundDataNoteGroupId =
    unsafeNode[Refined[FundDataNoteGroupId], RadixId, FundDataNoteGroupId](parent)

}

object FundDataNoteIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundDataNoteGroupId): FundDataNoteId =
    unsafeNode[Refined[FundDataNoteId], FundDataNoteGroupId, FundDataNoteId](parent)

}

object FundSubFilterPresetIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubDashboardId): FilterPresetId =
    unsafeNode[Refined[FilterPresetId], FundSubDashboardId, FilterPresetId](parent)

}

object BatchActionItemIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: BatchActionId): BatchActionItemId =
    unsafeNode[Refined[BatchActionItemId], BatchActionId, BatchActionItemId](parent)

}

object DataExtractProjectIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: DataExtractProjectId = unsafeNode[Refined[DataExtractProjectId], DataExtractProjectId]
}

object DataExtractProjectItemIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: DataExtractProjectId): DataExtractProjectItemId = unsafeNode[
    Refined[DataExtractProjectItemId],
    DataExtractProjectId,
    DataExtractProjectItemId
  ](parent)

}

object DataExtractUserDocumentIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: DataExtractProjectItemId): DataExtractUserDocumentId = unsafeNode[
    Refined[DataExtractUserDocumentId],
    DataExtractProjectItemId,
    DataExtractUserDocumentId
  ](parent)

}

object SaProfileIdFactory extends RadixRootNodeFactory {
  private val AlphaNumericList = "abcdefghijklmnopqrstuvwxyz0123456789".toList

  // The generated ID would have length = 2 * length_of_random_number_list (default ID length = 10)
  // - Choose reasonably short ID length to be user friendly
  // - SaProfileIdNew is used as FDB store key -> conflicts will be detected & handled with retry
  def generate(halfIdLength: Int = 5): SaProfileId = {
    val randNumList = List.fill(halfIdLength)(0).map(_ => Random.nextLong(10))
    Alphabet(Random.shuffle(AlphaNumericList).mkString)
      .flatMap(Sqids.forAlphabet)
      .fold(
        _ => throw new IllegalArgumentException("Invalid AlphaNumericList"), // Should never happens
        sqIds =>
          SaProfileId.spec.buildNode(
            SaProfileId.spec.unsafeValue(
              SaProfileId.valuePrefix.value + sqIds.encodeUnsafeString(randNumList*)
            )
          )
      )
  }

}

object MappingDestinationIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: MappingDestinationId = unsafeNode[Refined[MappingDestinationId], MappingDestinationId]
}

object SaDataTemplateIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: SaDataTemplateId = unsafeNode[Refined[SaDataTemplateId], SaDataTemplateId]
}

object OntologyAsaIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: OntologyAsaId = unsafeNode[Refined[OntologyAsaId], OntologyAsaId]
}

object SharedOntologyAsaIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: SharedOntologyAsaId = unsafeNode[Refined[SharedOntologyAsaId], SharedOntologyAsaId]
}

object TagListIdFactory extends RadixChildNodeFactory {
  def unsafeRandomId(parent: RadixId): TagListId = unsafeNode[Refined[TagListId], RadixId, TagListId](parent)
}

object TagItemIdFactory extends RadixChildNodeFactory {
  def unsafeRandomId(parent: TagListId): TagItemId = unsafeNode[Refined[TagItemId], TagListId, TagItemId](parent)
}

object FormToolConfigIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: FormToolConfigId = unsafeNode[Refined[FormToolConfigId], FormToolConfigId]
}

object TextractWorkflowIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId: TextractWorkflowId =
    unsafeNode[Refined[TextractWorkflowId], TransactionId, TextractWorkflowId](TransactionId.defaultValue.get)

}

object UserFundSubPublicIdFactory extends RadixChildNodeFactory {

  def generate(parent: UserRestrictedId): UserFundSubPublicId =
    unsafeNode[Refined[UserFundSubPublicId], UserRestrictedId, UserFundSubPublicId](parent)

}

object UserFundSubAdminIdFactory extends RadixChildNodeFactory {

  def generate(parent: UserRestrictedId): UserFundSubAdminId =
    unsafeNode[Refined[UserFundSubAdminId], UserRestrictedId, UserFundSubAdminId](parent)

}

object UserFundSubLpIdFactory extends RadixChildNodeFactory {

  def generate(parent: UserRestrictedId): UserFundSubLpId =
    unsafeNode[Refined[UserFundSubLpId], UserRestrictedId, UserFundSubLpId](parent)

}

object BatchUploadIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: BatchUploadId = unsafeNode[Refined[BatchUploadId], BatchUploadId]
}

object AmlCheckIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: AmlCheckId = unsafeNode[Refined[AmlCheckId], AmlCheckId]
}

object FundSubSingleUserInvitationLinkIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: FundSubSingleUserInvitationLinkId =
    unsafeNode[Refined[FundSubSingleUserInvitationLinkId], FundSubSingleUserInvitationLinkId]

}

object IntegrationIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: IntegrationId =
    unsafeNode[Refined[IntegrationId], IntegrationId]

}

object RiaEntityIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: RiaEntityId =
    unsafeNode[Refined[RiaEntityId], RiaEntityId]

}

object RiaFundGroupIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: RiaEntityId): RiaFundGroupId =
    unsafeNode[Refined[RiaFundGroupId], RiaEntityId, RiaFundGroupId](parent)

}

object GeneratedEmailIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: GeneratedEmailId =
    unsafeNode[Refined[GeneratedEmailId], GeneratedEmailId]

}

object MergeTextractProjectIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: MergeTextractProjectId = unsafeNode[Refined[MergeTextractProjectId], MergeTextractProjectId]
}

object IdMappingGroupIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: IdMappingGroupId = unsafeNode[Refined[IdMappingGroupId], IdMappingGroupId]
}

object CueModuleVersionIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: CueModuleId): CueModuleVersionId =
    unsafeNode[Refined[CueModuleVersionId], CueModuleId, CueModuleVersionId](parent)

}

object CueTableIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: CueTableId = unsafeNode[Refined[CueTableId], CueTableId]
}

object TFlowIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parentId: RadixId, flowType: TFlowType): TFlowId = {
    val id = IdPrefix.TFlowIdPrefixValue.value + flowType.value + Id().toString
    TFlowId(parentId, Refined.unsafeApply(id))
  }

}

object TFlowEventIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parentId: TFlowId): TFlowEventId = {
    val id = IdPrefix.TFlowEventIdPrefixValue.value + Id().toString
    TFlowEventId(parentId, Refined.unsafeApply(id))
  }

}

object TFlowQueryIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parentId: TFlowId): TFlowQueryId = {
    val id = IdPrefix.TFlowQueryIdPrefixValue.value + Id().toString
    TFlowQueryId(parentId, Refined.unsafeApply(id))
  }

}

object AsyncApiIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId(userRestrictedId: UserRestrictedId): AsyncApiId = AsyncApiId(userRestrictedId, UUID.randomUUID)
}

object InvestorPortalIdFactory extends RadixRootNodeFactory {
  def unsafeRandomId: InvestorPortalId = unsafeNode[Refined[InvestorPortalId], InvestorPortalId]
}

object FundSubDataExtractTestProfileIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubId): FundSubDataExtractTestProfileId =
    unsafeNode[
      Refined[FundSubDataExtractTestProfileId],
      FundSubId,
      FundSubDataExtractTestProfileId
    ](parent)

}

object FundSubDataExtractRequestIdFactory extends RadixChildNodeFactory {

  def unsafeRandomId(parent: FundSubLpId): FundSubDataExtractRequestId =
    unsafeNode[
      Refined[FundSubDataExtractRequestId],
      FundSubLpId,
      FundSubDataExtractRequestId
    ](parent)

}

object RevertOTPAuthenticationRequestIdFactory extends RadixRootNodeFactory {

  def unsafeRandomId: RevertOTPAuthenticationRequestId =
    unsafeNode[Refined[RevertOTPAuthenticationRequestId], RevertOTPAuthenticationRequestId]

}
