// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.id.common

import io.circe.Codec

import anduin.circe.generic.semiauto.deriveStringEnumCodec
import anduin.enumeration.{StringEnum, StringEnumCompanion}

enum IdPrefix(val value: String) extends StringEnum {
  override def toString: String = value

  // Transactions
  case TransactionIdPrefixValue extends IdPrefix("txn")

  // ReadChannel
  case ReadChannelPublicPrefix extends IdPrefix("pub")
  // TODO: clash with EntityPrivatePrefixValue
  // case ReadChannelPrivatePrefix extends IdPrefix("pri")
  case ReadChannelPrefix extends IdPrefix("rcn")

  // Users
  case UserIdPrefixValue extends IdPrefix("user-")
  case UserRestrictedIdPrefixValue extends IdPrefix("urs")
  case UserSessionIdPrefixValue extends IdPrefix("usi")
  case UserSignupIdPrefixValue extends IdPrefix("usu")
  case UserResetPasswordIdPrefixValue extends IdPrefix("urp")
  case UserAccountRecoveryIdPrefixValue extends IdPrefix("uar")
  case Oauth2StateIdPrefixValue extends IdPrefix("os2")
  case LinkAccountIdPrefixValue extends IdPrefix("lci")
  case AdminUserIdPrefixValue extends IdPrefix("adu")
  case AdminUserAuditLogIdPrefixValue extends IdPrefix("adl")
  case CookieConsentIdPrefixValue extends IdPrefix("ckc")
  case UserSignatureIdPrefixValue extends IdPrefix("usg")
  case UserTrackingIdPrefixValue extends IdPrefix("utr")
  case EnterpriseLoginConfigIdPrefixValue extends IdPrefix("elc")
  case EnterpriseLoginLinkIdPrefixValue extends IdPrefix("ell")
  case EnterpriseLoginStateIdPrefixValue extends IdPrefix("els")
  @deprecated case EnterpriseLoginSamlStateIdPrefixValue extends IdPrefix("ess")
  case EnterpriseLoginUserLinkIdPrefixValue extends IdPrefix("elu")
  case EnterpriseLoginEmailDomainBindingIdPrefixValue extends IdPrefix("eld")
  case EnterpriseLoginProxyDataIdPrefixValue extends IdPrefix("elp")

  // OTP
  case OTPAuthenticationRequestIdPrefixValue extends IdPrefix("oar")
  case OTPAuthenticationCodeIdPrefixValue extends IdPrefix("oac")
  case RevertOTPAuthenticationRequestIdPrefixValue extends IdPrefix("ror")
  case RevertOTPAuthenticationCodeIdPrefixValue extends IdPrefix("roc")

  // Oauth2
  case Oauth2IntegrationClientIdPrefix extends IdPrefix("o2c")
  case Oauth2IntegrationStateIdPrefix extends IdPrefix("o2s")
  case Oauth2RefreshTokenIdPrefix extends IdPrefix("o2r")

  // Pages
  case DashboardPagePrefix extends IdPrefix("das")
  case EntityDashboardPagePrefix extends IdPrefix("ett")
  case EntityRestrictedDashboardPagePrefix extends IdPrefix("ers")

  // Team, role, permission
  case TeamIdPrefixValue extends IdPrefix("ttm")
  case RoleIdPrefixValue extends IdPrefix("rol")
  case UserRoleIdPrefixValue extends IdPrefix("url")
  case TeamRoleIdPrefixValue extends IdPrefix("trl")
  case EntityRoleIdPrefixValue extends IdPrefix("etr")
  case EntityAdminRolePrefixValue extends IdPrefix("ero")

  // Investment entity roles
  case InvestmentEntityRoleIdPrefixValue extends IdPrefix("ier")

  case AccessTypeIdPrefixValue extends IdPrefix("atp")

  // Email
  case EmailIdPrefixValue extends IdPrefix("ema")
  case EmailThreadIdPrefixValue extends IdPrefix("emt")
  case EmailAttachmentIdPrefixValue extends IdPrefix("eat")
  case EmailContactIdPrefixValue extends IdPrefix("emp")
  case UserEmailThreadIdPrefixValue extends IdPrefix("uet")
  @deprecated case EmailReadChannelPrefixValue extends IdPrefix("eml")
  case InternalEmailIdPrefixValue extends IdPrefix("iem")
  case GeneratedEmailIdPrefixValue extends IdPrefix("gee")
  case EmailProviderIdPrefixValue extends IdPrefix("epr")
  case EmailSystemSpaceIdPrefixValue extends IdPrefix("ems")
  case EmailSendingTaskIdPrefixValue extends IdPrefix("est")

  // Flow
  case FlowIdPrefixValue extends IdPrefix("fl2")
  case FlowEventIdPrefixValue extends IdPrefix("fe2")

  // Legaltech
  @deprecated case LegalTermSummaryIdPrefixValue extends IdPrefix("lt5")
  @deprecated case PreprocessedDocIdPrefix extends IdPrefix("lt3pp")
  @deprecated case DeidentifiedDocIdPrefix extends IdPrefix("lt3di")
  @deprecated case HumanAnnotatedDocIdPrefix extends IdPrefix("lt3ha")
  @deprecated case ParsedDocIdPrefix extends IdPrefix("lt3pa")
  @deprecated case DocInfoIdPrefix extends IdPrefix("lt3if")

  // Token and redirect
  case CronSynchronizerIdPrefixValue extends IdPrefix("csc")
  case CouchbaseDistributedLockIdPrefixValue extends IdPrefix("cdl")
  case JwtIdPrefixValue extends IdPrefix("jwt-")
  case ShortlinkIdPrefixValue extends IdPrefix("shl-")
  case OauthStateIdPrefixValue extends IdPrefix("ous")
  case TinyUrlIdPrefixValue extends IdPrefix("tur")
  case ShortenUrlIdPrefixValue extends IdPrefix("shu")
  case TokenMetaIdPrefixValue extends IdPrefix("tmt")

  // SES email
  case SesMessageIdPrefixValue extends IdPrefix("ses")
  case SesReceiveMessageIdPrefixValue extends IdPrefix("ser")

  // File management id prefixes
  case FolderIdPrefixValue extends IdPrefix("fdr")
  case FileIdPrefixValue extends IdPrefix("fil")
  case ShortcutIdPrefixValue extends IdPrefix("shc")
  case PublicFileIdPrefixValue extends IdPrefix("fip")
  case RootFolderSgwIdPrefixValue extends IdPrefix("rfs")
  @deprecated case RootFolderReadChannelPrefixValue extends IdPrefix("rfr")

  // Dynamic forms
  case DynamicFormIdPrefixesValue extends IdPrefix("dfi")
  case S3FormFileDirectoryIdPrefixesValue extends IdPrefix("s3f")
  case S3FormChangeDirectoryIdPrefixesValue extends IdPrefix("s3c")
  case S3FormChangeIdPrefixesValue extends IdPrefix("s3x")
  case DynamicFormTestIdPrefixesValue extends IdPrefix("dft")
  case DynamicFormTestDataSetIdPrefixesValue extends IdPrefix("dfd")
  case DynamicFormTestScriptIdPrefixesValue extends IdPrefix("dfs")
  case FormDiffJobIdPrefixValue extends IdPrefix("fdj")
  case DynamicFormFileIdPrefixesValue extends IdPrefix("dff")

  // New form
  case FormIdPrefixValue extends IdPrefix("for")
  case FormVersionIdPrefixValue extends IdPrefix("fve")
  case FormVersionMetadataIdPrefixValue extends IdPrefix("fvm")
  case FormVersionSystemMetadataIdPrefixValue extends IdPrefix("fvs")
  case FormActivityIdPrefixValue extends IdPrefix("fac")
  case FormTestSuiteIdPrefixValue extends IdPrefix("fts")
  case FormTestScriptIdPrefixValue extends IdPrefix("fte")
  case FormVersionDataIdPrefixValue extends IdPrefix("fvd")
  case FormMappingIdPrefixValue extends IdPrefix("fmp")
  case FormMappingTemplateIdPrefixValue extends IdPrefix("ftm")
  case FormMappingTemplateVersionIdPrefixValue extends IdPrefix("fmv")
  case FormFolderIdPrefixValue extends IdPrefix("ffi")
  case DataTemplateIdPrefixValue extends IdPrefix("dtp")
  case DataTemplateVersionIdPrefixValue extends IdPrefix("dtv")
  case FormToolConfigIdPrefixValue extends IdPrefix("ftc")
  case FormDataUserTempIdPrefixValue extends IdPrefix("fut")

  // Digitization
  case DigitizationFolderIdPrefixValue extends IdPrefix("dgd")
  case DigitizationFileIdPrefixValue extends IdPrefix("dgf")

  case DigitizationTagIdPrefixValue extends IdPrefix("dgt")

  // Annotation
  case AnnotationDocumentIdPrefixValue extends IdPrefix("adi")
  case AnnotationDocumentMetadataIdPrefixValue extends IdPrefix("ami")
  case AnnotationDocumentVersionIdPrefixValue extends IdPrefix("adv")
  case AnnotationDocumentVersionMetadataIdPrefixValue extends IdPrefix("avm")
  case DigitizationFileActivityIdPrefixValue extends IdPrefix("dfa")
  case TextractAnnotationStateIdPrefixValue extends IdPrefix("tas")

  // LP Profile
  case InvestmentEntityIdPrefixValue extends IdPrefix("ive")
  case LpProfileIdPrefixValue extends IdPrefix("prf")

  case InvestorProfileAuditLogIdPrefixValue extends IdPrefix("ipa")

  case LpProfileDocumentIdPrefixValue extends IdPrefix("prd")
  case AsaIdPrefixValue extends IdPrefix("asa_")
  case OntologyAsaIdPrefixValue extends IdPrefix("ota")
  case SharedOntologyAsaIdPrefixValue extends IdPrefix("soa")

  case ComputeFormMatchingIdPrefixValue extends IdPrefix("cfm")

  case SaProfileIdPrefixValue extends IdPrefix("sap_")
  case MappingDestinationIdPrefixValue extends IdPrefix("mdi")
  case SaDataTemplateIdPrefixValue extends IdPrefix("dti")

  // Fundsub
  case IndexIdPrefixValue extends IdPrefix("idx")
  case AliasIdPrefixValue extends IdPrefix("al_")
  case FundSubAdminInfoPrefixValue extends IdPrefix("fad")
  case FundSubRestrictedPrefixValue extends IdPrefix("fsr")
  case FundSubAdminGeneralPrefixValue extends IdPrefix("fag")
  case FundSubLpPrefixValue extends IdPrefix("lpp")
  case FundSubRiaGroupPrefixValue extends IdPrefix("rig")
  case FundSubEmailTemplatePrefixValue extends IdPrefix("ete")
  case FundSubLpRestrictedPrefixValue extends IdPrefix("lpr")
  case FundSubAdminReadChannelPrefixValue extends IdPrefix("adm")
  case FundSubLpFolderTypeIdPrefixValue extends IdPrefix("lft")
  case FundSubLpFormPrefixValue extends IdPrefix("lpf")
  case FundSubLpFormVersionPrefixValue extends IdPrefix("lfv")
  case FundSubLpDashboardUpdateValue extends IdPrefix("fdb")
  case FundSubLpActivityLogUpdateValue extends IdPrefix("lal")
  case FundSubLpActivityValue extends IdPrefix("lpa")
  case FundSubStorageIntegrationPrefixValue extends IdPrefix("fsi")
  case FundSubLpTagIdPrefixValue extends IdPrefix("lpt")
  case FundsubContactGroupIdPrefixValue extends IdPrefix("fcg")
  case FundSubExportTemplateIdPrefixValue extends IdPrefix("fet")
  case FundSubSupportingDocPrefixValue extends IdPrefix("spd")
  case ReviewPackageSettingPrefixValue extends IdPrefix("rps")
  @deprecated case ReviewPackageEmailLogPrefixValue extends IdPrefix("rel")
  @deprecated case ReviewPackagePrefixValue extends IdPrefix("rpk")
  case FundSubSupportingDocTrackingPrefixValue extends IdPrefix("sdt")
  case InvestorFormUpdateLogId extends IdPrefix("ful")
  case FundSubDashboardPrefixValue extends IdPrefix("fdi")
  case FundSubFilterPresetPrefixValue extends IdPrefix("ffp")
  case CustomDataColumnPrefixValue extends IdPrefix("cdc")
  case FundSubLpInternalCommentChannelPrefixValue extends IdPrefix("lic")
  case InvestmentFundIdPrefixValue extends IdPrefix("ivf")
  case FundSubReportingIdPrefixValue extends IdPrefix("frp")
  case UserFundSubPublicIdPrefixValue extends IdPrefix("ufp")
  case UserFundSubAdminIdPrefixValue extends IdPrefix("ufa")
  case UserFundSubLpIdPrefixValue extends IdPrefix("ulp")
  case FundSubItoolLpNoteIdPrefixValue extends IdPrefix("fin")
  case FundSubItoolLpLockIdPrefixValue extends IdPrefix("ill")
  case FundSubSelfServiceExportTemplateIdPrefixValue extends IdPrefix("sse")
  case SubscriptionSchemaDataIdPrefixValue extends IdPrefix("ssd")
  case FundInfoSchemaDataIdPrefixValue extends IdPrefix("fis")

  // Fundsub data extract
  case FundSubDataExtractRequestIdPrefixValue extends IdPrefix("fer")
  case FundSubDataExtractTestProfilePrefixValue extends IdPrefix("fsp")

  // Fundsub group
  case FundSubInvestorGroupIdPrefixValue extends IdPrefix("fig")

  // Fundsub closes
  case FundSubCloseDataIdPrefixValue extends IdPrefix("fcd")
  case FundSubCloseIdPrefixValue extends IdPrefix("fsc")

  // Fundsub roles
  @deprecated case FundSubGroupRoleIdPrefixValue extends IdPrefix("fgr")
  case FundManagerRoleIdPrefixValue extends IdPrefix("fmr")
  case FundsubInvestorRoleIdPrefixValue extends IdPrefix("fiv")

  // Fundsub disclaimer
  case DisclaimerIdPrefixValue extends IdPrefix("dcl")

  // Review
  case ReviewConfigIdPrefixValue extends IdPrefix("rvc")
  case ReviewStepConfigIdPrefixValue extends IdPrefix("rvs")
  case ReviewFlowIdPrefixValue extends IdPrefix("rvf")
  case SupportingDocConfigIdPrefixValue extends IdPrefix("sci")

  // Fundsub audit log
  case FundSubAuditLogIdPrefixValue extends IdPrefix("fal")

  @deprecated case FundSubPredefinedResourceIdPrefixValue extends IdPrefix("fpr")

  // Fundsub environment sso
  case FundSubGpCompositeIdPrefix extends IdPrefix("fgc")
  case FundSubLpCompositeIdPrefix extends IdPrefix("flc")
  case FundSubCollaboratorSSOTombstoneIdPrefix extends IdPrefix("fct")

  // Activity log
  case ActivityLogIdPrefixValue extends IdPrefix("alg")

  // Workflow prefixes
  case FundSubWflIdPrefixValue extends IdPrefix("fsb")
  case DataRoomWflIdPrefixValue extends IdPrefix("dtr")
  case DataRoomTermsOfAccessIdPrefixValue extends IdPrefix("dta")
  case DataRoomContactRoleIdPrefixValue extends IdPrefix("dca")
  case DataRoomSimulatorIdPrefixValue extends IdPrefix("dsb")

  // Entity prefixes
  case EntityIdPrefixValue extends IdPrefix("ent")
  case EntityPrivatePrefixValue extends IdPrefix("pri")
  case EntityRestrictedIdPrefixValue extends IdPrefix("rst")
  @deprecated case EntityReferralIdPrefixValue extends IdPrefix("ref")
  case NaturalPersonEntityPrefixValue extends IdPrefix("npe")
  @deprecated case WhiteLabelEntityPrefixValue extends IdPrefix("wle")
  case EntitySignaturePrefixValue extends IdPrefix("esi")
  @deprecated case EntityPersonalPrefixValue extends IdPrefix("epe")

  case EmailDomainIdPrefixValue extends IdPrefix("emd")

  // Action Logger
  case ActionEventIdPrefixValue extends IdPrefix("ace")
  case LoggingEventPublishingStateIdPrefixValue extends IdPrefix("als")

  // Comment
  case CommentThreadIdPrefixValue extends IdPrefix("thr")

  // Contact
  case ContactGroupIdPrefixValue extends IdPrefix("cgr")
  case ContactIdPrefixValue extends IdPrefix("ctc")

  // Email template
  case EmailTemplateIdPrefixesValue extends IdPrefix("eti")

  // Doc Draft
  case DocDraftIdPrefixValue extends IdPrefix("ddr")
  case DocDraftIdItemPrefixValue extends IdPrefix("ddi")

  // Doc Request
  case DocRequestIdPrefixValue extends IdPrefix("dri")
  case FormSubmissionIdPrefixValue extends IdPrefix("fsm")
  case DocSubmissionIdPrefixValue extends IdPrefix("dsm")

  // Notification
  case NotificationSpaceIdPrefixValue extends IdPrefix("nsp")
  case NotificationIdPrefixValue extends IdPrefix("ntf")

  // Tag
  case TagIdPrefixValue extends IdPrefix("tag")

  // App
  @deprecated case ThirdPartyAppIdPrefixValue extends IdPrefix("app")
  @deprecated case ThirdPartyAppTokenPrefixValue extends IdPrefix("ato")
  @deprecated case ThirdPartyAppTokenSecretPrefixValue extends IdPrefix("ats")

  // Signature
  case SignatureIdPrefixValue extends IdPrefix("sig")
  case SignatureModuleIdPrefixValue extends IdPrefix("smd")
  case SignatureRequestIdPrefixValue extends IdPrefix("srq")
  case SignatureRequestItemIdPrefixValue extends IdPrefix("sri")

  case SignatureModuleRoleIdPrefixValue extends IdPrefix("smr")
  case SignatureRequestRoleIdPrefixValue extends IdPrefix("srr")
  @deprecated case SignatureRequestItemRoleIdPrefixValue extends IdPrefix("sir")

  // serverless
  case ServerlessRequestIdPrefixValue extends IdPrefix("svr")

  // Issue tracker
  case UserIssueTrackerIdPrefixValue extends IdPrefix("uit")
  case IssueIdPrefixValue extends IdPrefix("isu")
  case IssueListIdPrefixValue extends IdPrefix("isl")
  case IssueTrackerCollaborationChannelIdPrefixValue extends IdPrefix("icc")
  case CollaboratorGroupIdPrefixValue extends IdPrefix("cog")
  case ResourceIdPrefixValue extends IdPrefix("rsc")
  case MentionIdPrefixValue extends IdPrefix("men")
  case CommentAssignmentIdPrefixValue extends IdPrefix("asm")
  case IssueLpCompositeIdPrefixValue extends IdPrefix("ilc")

  // Offering
  case OfferingIdPrefixValue extends IdPrefix("off")
  case GlobalOfferingPrefixValue extends IdPrefix("gof")

  // Custom domain
  case CustomDomainIdPrefixValue extends IdPrefix("cdi")
  case CustomDomainOfferingIdPrefixValue extends IdPrefix("cdo")

  // Jobs
  case DataRequestJobIdPrefixValue extends IdPrefix("drj")
  @deprecated case CronJobIdPrefixValue extends IdPrefix("crj")

  // Server config
  case ServerConfigPrefixValue extends IdPrefix("src")

  // Test
  @deprecated case CouchbaseModelTestIdPrefix extends IdPrefix("ttt-")

  // Protected link
  case ProtectedLinkIdPrefixValue extends IdPrefix("lnk")
  case OneTimeLinkPrefixValue extends IdPrefix("otl")

  // Data Room
  case DataRoomIntegrationConfigPrefixValue extends IdPrefix("drc")
  case DataRoomHomeSectionPrefixValue extends IdPrefix("drs")
  case DataRoomHomePageIdPrefixValue extends IdPrefix("dhp")
  case DataRoomGroupIdPrefixValue extends IdPrefix("drg")

  // Long running tasks
  case LongRunningTaskIdPrefixValue extends IdPrefix("lrt")
  case ZipFileTaskIdPrefixValue extends IdPrefix("zip")
  case LongTaskIdPrefixValue extends IdPrefix("lti")
  case FundSubBatchInvitationIdPrefixValue extends IdPrefix("fbi")
  case FundSubSingleInvitationIdPrefixValue extends IdPrefix("fbs")
  case FundSubDataImportIdPrefixValue extends IdPrefix("fsd")
  case FundSubDataExportIdPrefixValue extends IdPrefix("fde")
  case FundSubDataImportItemIdPrefixValue extends IdPrefix("dii")
  case FundSubDataExportItemIdPrefixValue extends IdPrefix("dei")
  case DataImportItemResultIdPrefixValue extends IdPrefix("dis")
  case FundSubBatchActionIdPrefixValue extends IdPrefix("fba")
  case FundSubBatchActionItemIdPrefixValue extends IdPrefix("fsa")
  case FundSubSingleUserInvitationLinkIdPrefixValue extends IdPrefix("sul")

  // FundSub simulators
  case FundSubSimulatorProgressIdPrefixValue extends IdPrefix("fdp")

  // Admin portal
  case PortalProductRoleIdPrefixValue extends IdPrefix("ppr")
  case PortalSectionRoleIdPrefixValue extends IdPrefix("pps")
  case PortalGroupIdPrefixValue extends IdPrefix("ppg")

  // Admin portal fundsub
  case PortalFundSubTeamIdPrefixValue extends IdPrefix("pft")

  // Zapier
  case ZapIdPrefixValue extends IdPrefix("zap")
  case ZapEventIdPrefixValue extends IdPrefix("zpe")

  // Link Restriction
  case LinkRestrictionIdPrefixValue extends IdPrefix("lrr")
  case LinkRestrictionListIdPrefixValue extends IdPrefix("lrl")

  // Link Whitelabel
  case LinkWhitelabelIdPrefixValue extends IdPrefix("lwl")

  // Temporal workflow
  case TemporalWorkflowIdPrefixValue extends IdPrefix("twl")

  // Async job
  @deprecated case AsyncJobIdPrefixValue extends IdPrefix("asj")

  // Webhook
  case WebhookEndpointIdPrefixValue extends IdPrefix("wid")
  case WebhookEventIdPrefixValue extends IdPrefix("wei")

  // Environment
  case EnvironmentIdPrefixValue extends IdPrefix("env")
  case EnvironmentWhitelabelIdPrefixValue extends IdPrefix("enw")
  case EnvironmentPolicyIdPrefixValue extends IdPrefix("enp")
  case EnvironmentReauthenticationPolicyIdPrefixValue extends IdPrefix("erp")
  case EnvironmentEnforcementPolicyIdPrefixValue extends IdPrefix("eep")
  case EnvironmentSSOBindingIdPrefixValue extends IdPrefix("esb")
  case UserEnvironmentSSOBindingIdPrefixValue extends IdPrefix("eub")
  case EmailDomainEnvironmentSSOBindingIdPrefixValue extends IdPrefix("eed")
  case GlobalEmailDomainEnvironmentSSOBindingIdPrefixValue extends IdPrefix("eeg")

  // Fund Data
  case FundDataFirmIdPrefixValue extends IdPrefix("fdf")
  case FundDataGroupRoleIdPrefixValue extends IdPrefix("fdg")
  case FundDataFundIdPrefixValue extends IdPrefix("fdu")
  case FundDataDataRoomIdPrefixValue extends IdPrefix("fdd")
  case FundDataFundGroupIdPrefixValue extends IdPrefix("fda")
  case FundDataFundSubscriptionIdPrefixValue extends IdPrefix("ffs")
  case FundDataFundSubscriptionDocumentIdPrefixValue extends IdPrefix("ffd")
  case FundDataInvestorIdPrefixValue extends IdPrefix("fdv")
  case FundDataClientGroupIdPrefixValue extends IdPrefix("cgi")
  case FundDataInvestmentEntityIdPrefixValue extends IdPrefix("fdm")
  case FundDataInvestmentEntityAssessmentIdPrefixValue extends IdPrefix("fia")
  case FundDataInvestmentEntityDocumentIdPrefixValue extends IdPrefix("fid")
  case FundDataInvestmentEntityContactIdPrefixValue extends IdPrefix("fic")
  case FundDataFirmDefaultSettingIdPrefixValue extends IdPrefix("fds")
  case FundDataFundSubSyncIdPrefixValue extends IdPrefix("fss")
  case FundDataFundSubSyncConflictIdPrefixValue extends IdPrefix("cba")
  case FundDataProfileConflictIdPrefixValue extends IdPrefix("fpc")
  case FundDataFirmOrganizationIdPrefixValue extends IdPrefix("ftp")
  case FundDataFeatureSwitchTrackingIdPrefixValue extends IdPrefix("fdt")
  case FundDataInvestmentEntityDocumentTextractIdPrefixValue extends IdPrefix("dtx")
  case FundDataExpirationDateConfigIdPrefixValue extends IdPrefix("edc")
  case FundDataRoleIdPrefixValue extends IdPrefix("fri")
  case FundDataInvestmentEntityProfileHistoryIdPrefixValue extends IdPrefix("pfh")
  case FundDataOpportunityPageIdPrefixValue extends IdPrefix("fop")
  case FundDataLandingPageLinkIdPrefixValue extends IdPrefix("fll")
  case CommunicationTypeId extends IdPrefix("com")
  case FundDataNoteGroupIdPrefixValue extends IdPrefix("fng")
  case FundDataNoteIdPrefixValue extends IdPrefix("fdn")
  // Task Request
  case TaskRequestIdPrefixValue extends IdPrefix("tar")
  case TaskRequestItemIdPrefixValue extends IdPrefix("tri")
  // Web content
  case WebContentIdPrefixValue extends IdPrefix("wct")
  // Fund
  case FundLegalEntityIdPrefixValue extends IdPrefix("fle")
  case FundFamilyIdPrefixValue extends IdPrefix("ffa")
  case FundShareClassIdPrefixValue extends IdPrefix("fsh")
  case FundInvestmentIdPrefixValue extends IdPrefix("fii")
  case FundTransactionIdPrefixValue extends IdPrefix("ftr")
  case FundTransactionDocumentIdPrefixValue extends IdPrefix("ftd")

  // Textract
  case TextractWorkflowIdPrefixValue extends IdPrefix("tew")
  case MergeTextractProjectIdPrefixValue extends IdPrefix("mte")

  // Batch Action
  case BatchActionIdPrefixValue extends IdPrefix("bac")
  case BatchActionItemIdPrefixValue extends IdPrefix("bai")

  // Data Extract
  case DataExtractProjectIdPrefixValue extends IdPrefix("dep")
  case DataExtractRequestIdPrefixValue extends IdPrefix("der")
  case DataExtractProjectItemIdPrefixValue extends IdPrefix("dpi")
  case DataExtractUserDocumentIdPrefixValue extends IdPrefix("dud")

  // Tag
  case TagListIdPrefixValue extends IdPrefix("ftl")
  case TagItemIdPrefixValue extends IdPrefix("fti")

  // Upload
  @deprecated case FileUploadIdPrefixValue extends IdPrefix("fup")
  case BatchUploadIdPrefixValue extends IdPrefix("bup")

  // Blueprint
  case BlueprintIdPrefixValue extends IdPrefix("bpr")
  case BlueprintVersionIdPrefixValue extends IdPrefix("bpv")

  // amlkyc
  case AmlCheckIdPrefixValue extends IdPrefix("acp")

  // Integration
  case IntegrationIdPrefixValue extends IdPrefix("itg")

  // Prismatic
  @deprecated case PrismaticCustomerIdPrefixValue extends IdPrefix("pci")

  // RIA
  case RiaEntityIdPrefixValue extends IdPrefix("rei")
  case RiaFundGroupIdPrefixValue extends IdPrefix("rfg")

  // Integration Platform
  case IntegPlatformEntityFeatureAccessIdPrefixValue extends IdPrefix("efa")

  // Id Mapping
  case IdMappingGroupIdPrefixValue extends IdPrefix("img")

  // Cue module
  case AnduinCueSystemIdPrefixValue extends IdPrefix("acs")
  case CueModuleIdPrefixValue extends IdPrefix("cue")
  case CueModuleVersionIdPrefixValue extends IdPrefix("cuv")
  case CueMappingIdPrefixValue extends IdPrefix("cmp")

  // Cue Table
  case CueTableIdPrefixValue extends IdPrefix("cti")

  // Temporal Flow
  case TFlowIdPrefixValue extends IdPrefix("tfl")
  case TFlowEventIdPrefixValue extends IdPrefix("tfe")
  case TFlowQueryIdPrefixValue extends IdPrefix("tfq")

  // Async API
  case AsyncApiIdPrefixValue extends IdPrefix("asy")
  case AsyncApiClientIdPrefixValue extends IdPrefix("asc")

  // Investor Portal
  case InvestorPortalIdPrefixValue extends IdPrefix("ipt")
  case PortalInstanceIdPrefixValue extends IdPrefix("pii")

}

object IdPrefix extends StringEnumCompanion[IdPrefix] {
  given Codec[IdPrefix] = deriveStringEnumCodec
}

object DuplicatedIdPrefixes {
  val ReadChannelPrivatePrefix: IdPrefix = IdPrefix.EntityPrivatePrefixValue
}
