//  Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.id.funddata.portal

import anduin.id.RadixChildRefinedId
import anduin.id.common.IdPrefix
import anduin.id.funddata.FundDataFirmId
import anduin.model.DefaultValue
import anduin.model.id.RadixIdDefaultValue
import anduin.radix.RadixChildRefinedCompanion
import anduin.refined.Refined

final case class PortalInstanceId(
  parent: FundDataFirmId,
  value: Refined[PortalInstanceId]
) extends RadixChildRefinedId[PortalInstanceId, FundDataFirmId] {
  override type Parent = FundDataFirmId
  override type Value = Refined[PortalInstanceId]
}

object PortalInstanceId extends RadixChildRefinedCompanion[PortalInstanceId, FundDataFirmId] {
  override val valueLength = 7
  override val valuePrefix: IdPrefix = IdPrefix.PortalInstanceIdPrefixValue

  override val buildNode: (FundDataFirmId, Refined[PortalInstanceId]) => PortalInstanceId = { (parent, value) =>
    PortalInstanceId(parent, value)
  }

  override given defaultValue: DefaultValue[PortalInstanceId] = DefaultValue.instance {
    buildNode(FundDataFirmId.defaultValue.get, RadixIdDefaultValue.unsafeDefaultValue)
  }

}
