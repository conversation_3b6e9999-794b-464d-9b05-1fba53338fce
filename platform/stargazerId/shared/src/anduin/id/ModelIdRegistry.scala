// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.id

import anduin.id.account.*
import anduin.id.actionlogger.{ActionEventId, LoggingEventPublishingStateId}
import anduin.id.admin.{AdminUserAuditLogId, AdminUserId}
import anduin.id.amlkyc.AmlCheckId
import anduin.id.annotation.*
import anduin.id.asyncapiv2.AsyncApiId
import anduin.id.authwhitelabel.AuthenticationWhitelabelId
import anduin.id.autofill.ComputeFormMatchingId
import anduin.id.batchaction.{BatchActionId, BatchActionItemId}
import anduin.id.blueprint.{BlueprintId, BlueprintVersionId}
import anduin.id.comment.CommentThreadId
import anduin.id.communication.CommunicationTypeId
import anduin.id.contact.{ContactGroupId, ContactId}
import anduin.id.cron.CronSynchronizerId
import anduin.id.cue.table.CueTableId
import anduin.id.cue.{AnduinCueSystemId, CueMappingId, CueModuleId, CueModuleVersionId}
import anduin.id.customdomain.{CustomDomainId, CustomDomainOfferingId}
import anduin.id.dataextract.{DataExtractProjectId, DataExtractProjectItemId, DataExtractUserDocumentId}
import anduin.id.dataroom.{DataRoomGroupId, DataRoomIntegrationConfigId, DataRoomSimulatorId}
import anduin.id.digitization.{DigitizationFileId, DigitizationFolderId, DigitizationTagId}
import anduin.id.disclaimer.DisclaimerId
import anduin.id.dlock.CouchbaseDistributedLockId
import anduin.id.docdraft.{DocDraftId, DocDraftItemId}
import anduin.id.docrequest.{DocRequestId, DocSubmissionId, FormSubmissionId}
import anduin.id.dynamicform.formfile.DynamicFormFileId
import anduin.id.dynamicform.s3form.*
import anduin.id.dynamicform.testtool.{DynamicFormTestDataSetId, DynamicFormTestId, DynamicFormTestScriptId}
import anduin.id.entity.*
import anduin.id.environment.*
import anduin.id.flow.{TFlowEventId, TFlowId, TFlowQueryId}
import anduin.id.form.*
import anduin.id.funddata.*
import anduin.id.funddata.fund.*
import anduin.id.funddata.note.{FundDataNoteGroupId, FundDataNoteId}
import anduin.id.funddata.portal.PortalInstanceId
import anduin.id.fundsub.*
import anduin.id.fundsub.auditlog.FundSubAuditLogId
import anduin.id.fundsub.dashboard.FilterPresetId
import anduin.id.fundsub.dataextract.{FundSubDataExtractRequestId, FundSubDataExtractTestProfileId}
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.fundsub.rebac.PortalFundSubTeamId
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.id.idmapping.IdMappingGroupId
import anduin.id.integplatform.IntegPlatformEntityFeatureAccessId
import anduin.id.integration.IntegrationId
import anduin.id.investmententity.{InvestmentEntityId, InvestorProfileAuditLogId}
import anduin.id.investorportal.InvestorPortalId
import anduin.id.issuetracker.*
import anduin.id.job.DataRequestJobId
import anduin.id.link.{OneTimeLinkId, ProtectedLinkId}
import anduin.id.linkrestriction.{LinkRestrictionId, LinkRestrictionListId}
import anduin.id.lpprofile.*
import anduin.id.notification.NotificationId
import anduin.id.oauth2.{Oauth2IntegrationClientId, Oauth2IntegrationStateId, Oauth2RefreshTokenId}
import anduin.id.offering.{GlobalOfferingId, TransactionOfferingId, TransactionParticipantOfferingId}
import anduin.id.ontology.{AsaId, OntologyAsaId, SharedOntologyAsaId}
import anduin.id.permission.AccessTypeId
import anduin.id.review.{ReviewConfigId, ReviewFlowId, ReviewStepConfigId, SupportingDocReviewConfigId}
import anduin.id.ria.{RiaEntityId, RiaFundGroupId}
import anduin.id.role.entity.{EntityAdminRoleId, EntityRoleId}
import anduin.id.role.fundsub.{FundManagerRoleId, FundSubUserPrivateRoleId, FundsubInvestorRoleId}
import anduin.id.role.investmententity.{InvestmentEntityRoleId, UserInvestmentEntityRoleId}
import anduin.id.role.investorprofile.{LpProfileRoleId, LpProfileTemplateRoleId}
import anduin.id.role.issuetracker.{
  IssueListRoleId,
  IssueRoleId,
  IssueTrackerCollaborationChannelRoleId,
  TransactionOfferingRoleId
}
import anduin.id.role.portal.{PortalGroupId, PortalProductRoleId, PortalSectionRoleId}
import anduin.id.role.signature.{SignatureModuleRoleId, SignatureRequestRoleId}
import anduin.id.role.{TeamRoleId, TransactionParticipantRoleId, UserProductRoleId, UserRoleId}
import anduin.id.sa.{MappingDestinationId, SaDataTemplateId, SaProfileId}
import anduin.id.serverless.ServerlessRequestId
import anduin.id.shortenurl.ShortenUrlId
import anduin.id.signature.{SignatureModuleId, SignatureRequestId, SignatureRequestItemId, UserSignatureId}
import anduin.id.tag.*
import anduin.id.task.{LongRunningTaskId, LongTaskId, ZipFileTaskId}
import anduin.id.taskrequest.{TaskRequestId, TaskRequestItemId}
import anduin.id.textract.{MergeTextractProjectId, TextractWorkflowId}
import anduin.id.tinyurl.TinyUrlId
import anduin.id.transaction.TransactionParticipantId
import anduin.id.upload.BatchUploadId
import anduin.id.user.{UserRestrictedId, UserTrackingId}
import anduin.id.webcontent.WebContentId
import anduin.id.webhook.{WebhookEndpointId, WebhookEventId}
import anduin.id.zapier.{ZapEventId, ZapId}
import anduin.model.common.ses.{SesMessageId, SesReceiveMessageId}
import anduin.model.common.token.{JwtId, OauthStateId, ShortlinkId, TokenMetaId}
import anduin.model.id.*
import anduin.model.id.email.provider.EmailProviderId
import anduin.model.id.email.sending.{EmailSystemSpaceId, EmailSendingTaskId}
import anduin.model.id.email.{GeneratedEmailId, InternalEmailId}
import anduin.model.id.notification.NotificationSpaceId
import anduin.model.id.stage.*
import anduin.model.id.tag.TagId
import anduin.radix.{RadixId, RadixParser, RadixRegistry}

object ModelIdRegistry {

  lazy val registry: RadixRegistry[RadixId] = RadixRegistry.build { builder =>
    /// Root for file system
    builder.child(FolderId, FileId)
    builder.child(FolderId, ShortcutId)
    builder.child(FolderId, NewFlowId)
    builder.child(NewFlowId, NewFlowEventId)
    builder.child(NewFlowId, FolderId)
    builder.child(FileId, NewFlowId)
    builder.child(FileId, DynamicFormFileId)
    builder.root(PublicFileId)

    /// Other roots
    builder.root(CronSynchronizerId)
    builder.root(CouchbaseDistributedLockId)
    builder.root(EmailDomainId)
    builder.root(EmailProviderId)
    builder.root(EmailSystemSpaceId)
    builder.root(EmailSendingTaskId)
    builder.root(AccessTypeId)
    builder.root(TemporalWorkflowId)
    builder.root(AsaId)
    builder.root(OntologyAsaId)
    builder.root(SharedOntologyAsaId)
    builder.root(ComputeFormMatchingId)
    builder.root(IdMappingGroupId)
    // Contact
    {
      builder.child(UserRestrictedId, ContactGroupId)
      builder.child(EntityId, ContactGroupId)
      builder.child(TeamId, ContactGroupId)
      builder.child(UserSignatureId, ContactGroupId)
      builder.child(DataRoomWorkflowId, ContactGroupId)
      builder.child(FundDataFirmId, ContactGroupId)
      builder.child(FundDataInvestorId, ContactGroupId)
      builder.child(FundDataInvestmentEntityId, ContactGroupId)
      builder.child(ContactGroupId, ContactId)
    }

    {
      builder.child(DynamicFormId, S3FormChangeDirectoryId)
      builder.child(DynamicFormId, S3FormFileDirectoryId)
      builder.child(DynamicFormId, S3FormChangeId)
      builder.child(DynamicFormId, FundSubLpFormId)
    }

    {
      builder.child(DynamicFormId, DynamicFormTestId)
      builder.child(DynamicFormTestId, DynamicFormTestDataSetId)
      builder.child(DynamicFormTestId, DynamicFormTestScriptId)
    }

    {
      builder.root(DynamicFormId)
      builder.child(DynamicFormId, FolderId)
      builder.root(FormDiffJobId)
    }

    {
      builder.root(FormMappingId)
      builder.root(FormId)
      builder.root(FormFolderId)
      builder.root(DigitizationFolderId)
      builder.root(DigitizationFileId)
      builder.root(DigitizationTagId)
      builder.child(FormId, FolderId)
      builder.child(FormId, FormVersionId)
      builder.child(FormId, FormActivityId)
      builder.child(FormId, FormTestScriptId)
      builder.root(DataTemplateId)
      builder.root(FormToolConfigId)
      builder.child(DataTemplateId, FolderId)
      builder.child(FormVersionId, FormTestSuiteId)
      builder.child(FormVersionId, FormVersionDataId)
      builder.child(FormVersionId, FundSubLpFormVersionId)
      builder.child(DataTemplateId, DataTemplateVersionId)
      builder.child(FormVersionId, FormTemplateMappingId)
      builder.child(FormTemplateMappingId, FormTemplateMappingVersionId)
      builder.child(FormVersionId, FormVersionMetadataId)
      builder.child(FormVersionId, FormVersionSystemMetadataId)
      builder.child(FormVersionId, LpProfileTemplateRoleId)
    }

    {
      builder.root(TextractAnnotationStateId)
      builder.root(AnnotationDocumentId)
      builder.child(AnnotationDocumentId, AnnotationDocumentVersionId)
      builder.child(AnnotationDocumentId, AnnotationDocumentMetadataId)
      builder.child(AnnotationDocumentId, FolderId)
      builder.child(AnnotationDocumentVersionId, AnnotationDocumentVersionMetadataId)
    }

    {
      builder.root(EmailTemplateId)
    }

    {
      builder.root(BlueprintId)
      builder.child(BlueprintId, BlueprintVersionId)
      builder.child(BlueprintId, FolderId)
    }

    {
      builder.root(EmailThreadId)
      builder.child(EmailThreadId, FolderId)
      builder.child(EmailThreadId, EmailId)
      builder.child(EmailId, FolderId)
      builder.child(EmailThreadId, UserEmailThreadId)
    }
    {
      builder.root(EmailAttachmentId)
    }

    /// Entity space root
    {
      builder.root(EntityId)
      builder.child(EntityId, FolderId)
      builder.child(EntityId, EntityRestrictedId)
      builder.child(EntityId, DataRequestJobId)
      builder.child(EntityId, EntityRoleId)
      builder.child(EntityId, EntityAdminRoleId)

      builder.child(EntityId, NewFlowId)
      builder.child(EntityId, NaturalPersonEntityId)
      builder.child(EntityId, TeamId)

      builder.child(EntityId, EntitySignatureId)
    }

    /// User space root
    {
      builder.root(UserRestrictedId)
      builder.child(UserRestrictedId, FolderId)
      builder.child(UserRestrictedId, UserRoleId)
      builder.child(UserRestrictedId, DataRequestJobId)
      builder.child(UserRestrictedId, UserSignatureId)
      builder.child(UserRestrictedId, UserTrackingId)

      {
        builder.child(UserRoleId, UserProductRoleId)
        builder.child(UserRoleId, UserInvestmentEntityRoleId)
      }

      {
        builder.child(UserRestrictedId, DocDraftId)
        builder.child(DocDraftId, NewFlowId)

        builder.child(DocDraftId, DocDraftItemId)
        builder.child(DocDraftItemId, NewFlowId)
      }

      {
        builder.child(UserRestrictedId, SignatureId)
        builder.child(SignatureId, FolderId)
      }

      {
        builder.child(UserRestrictedId, UserSessionId)
      }

      {
        builder.child(UserRestrictedId, AdminUserId)
      }
    }

    /// Account
    {
      builder.root(UserSignupId)
      builder.root(UserResetPasswordId)
      builder.root(UserAccountRecoveryId)
      builder.root(Oauth2StateId)
      builder.root(LinkAccountId)
      builder.root(AdminUserAuditLogId)
      builder.root(CookieConsentId)
      builder.root(Oauth2IntegrationClientId)
      builder.root(Oauth2IntegrationStateId)
      builder.root(Oauth2RefreshTokenId)
      builder.root(EnterpriseLoginConfigId)
      builder.root(EnterpriseLoginLinkId)
      builder.root(EnterpriseLoginStateId)
      builder.root(EnterpriseLoginUserLinkId)
      builder.root(EnterpriseLoginEmailDomainBindingId)
      builder.root(EnterpriseLoginProxyDataId)
    }

    /// Token root
    {
      builder.root(JwtId)
    }

    /// Shortlink root
    {
      builder.root(ShortlinkId)
    }

    /// Oauth state root
    {
      builder.root(OauthStateId)
    }

    /// Token Meta Data Root
    {
      builder.root(TokenMetaId)
    }

    /// Ses Message Id
    {
      builder.root(SesReceiveMessageId)
      builder.root(SesMessageId)
    }

    // TinyURL
    {
      builder.root(TinyUrlId)
      builder.root(ShortenUrlId)
    }

    // Action Logger id
    {
      builder.root(ActionEventId)
      builder.root(LoggingEventPublishingStateId)
    }

    // Custom domain
    {
      builder.root(CustomDomainId)
      builder.child(CustomDomainId, CustomDomainOfferingId)
    }

    // Global offering
    {
      builder.root(GlobalOfferingId)
    }

    // LP Profile ID
    {
      builder.root(LpProfileId)

      {
        builder.child(LpProfileId, TeamId)
        builder.child(LpProfileId, FolderId)
        builder.child(LpProfileId, LpProfileRoleId)
        builder.child(LpProfileId, LpProfileDocumentId)
      }
    }

    // Investment entity ID
    {
      builder.root(InvestmentEntityId)
      builder.root(InvestorProfileAuditLogId)

      {
        builder.child(InvestmentEntityId, TeamId)
        builder.child(InvestmentEntityId, InvestmentEntityRoleId)
        builder.child(InvestmentEntityId, InternalEmailId)
      }
    }

    // Standard Alias Profile (SaProfile) ID
    {
      builder.root(SaProfileId)
      builder.root(MappingDestinationId)
      builder.root(SaDataTemplateId)
    }

    // Fundsub Alias ID
    {
      builder.root(AliasBaseId)

      {
        builder.child(AliasBaseId, AliasId)
      }
    }

    // Review
    {

      {
        builder.child(ReviewConfigId, ReviewStepConfigId)
      }

      {
        builder.child(FundSubId, ReviewConfigId)
        builder.child(FundSubId, SupportingDocReviewConfigId)
      }

      {
        builder.child(FundSubLpId, ReviewFlowId)
      }
    }

    /// Transaction space root
    {
      builder.root(TransactionId)

      {
        builder.child(TransactionId, FolderId)
        builder.child(TransactionId, EmailContactId)
        builder.child(TransactionId, TagId)
        builder.child(TransactionId, DataRequestJobId)

        {
          builder.child(EntityId, NotificationSpaceId)
          builder.child(TransactionId, NotificationSpaceId)
          builder.child(FundSubId, NotificationSpaceId)
          builder.child(DataRoomWorkflowId, NotificationSpaceId)
          builder.child(NotificationSpaceId, NotificationId)
          builder.child(FundSubLpId, CommentAssignmentId)
        }

        {
          builder.child(TransactionId, NewFlowId)
        }

        // Workflow ids
        {
          builder.child(TransactionId, DataRoomWorkflowId)
          builder.child(DataRoomWorkflowId, DataRoomTermsOfAccessId)
          builder.child(DataRoomWorkflowId, DataRoomHomePageId)
          builder.child(DataRoomWorkflowId, DataRoomContactRoleId)
          builder.child(DataRoomWorkflowId, FolderId)
          builder.child(DataRoomWorkflowId, NewFlowId)
          builder.child(DataRoomWorkflowId, DataRoomHomeSectionId)
          builder.child(DataRoomTermsOfAccessId, FolderId)
          builder.child(DataRoomHomePageId, FolderId)
          builder.child(DataRoomWorkflowId, RootFolderSgwId)
          builder.child(DataRoomTermsOfAccessId, RootFolderSgwId)
          builder.child(DataRoomHomePageId, RootFolderSgwId)
          builder.child(FolderId, DataRoomIntegrationConfigId)
          builder.child(DataRoomWorkflowId, DataRoomGroupId)
          builder.child(DataRoomGroupId, TeamId)
          builder.child(TransactionId, TextractWorkflowId)
          builder.child(DataRoomWorkflowId, DataRoomSimulatorId)
        }

        {
          builder.child(TransactionId, TeamId)
          builder.child(TeamId, NewFlowId)
          builder.child(TeamId, FolderId)
          builder.child(TeamId, TeamRoleId)
        }

        {
          builder.child(TransactionId, FundSubId)
          builder.child(FundSubId, NewFlowId)
          builder.child(FundSubId, FolderId)
          builder.child(FundSubId, FundSubUserPrivateRoleId)
          builder.child(FundSubId, FundManagerRoleId)
          builder.child(FundSubId, FundSubExportTemplateId)
          builder.child(FundSubId, ReviewPackageSettingId)
          builder.child(FundSubId, InvestorFormUpdateLogId)
          builder.child(FundSubId, TeamId)
          builder.child(FundSubId, InternalEmailId)
          builder.child(FundSubId, FundSubEmailTemplateId)
          builder.child(FundSubLpId, InternalEmailId)

          // Close
          {
            builder.child(FundSubId, FundSubCloseId)
            builder.child(FundSubId, FundSubCloseDataId)
          }

          // Activity log
          {
            builder.child(FundSubId, ActivityLogId)
          }

          // Ria
          {
            builder.child(FundSubId, FundSubRiaGroupId)
            builder.child(FundSubRiaGroupId, TeamId)
          }

          builder.child(FundSubId, FundSubAuditLogId)

          {
            builder.child(FundSubId, FundSubAdminRestrictedId)
            builder.child(FundSubId, FundSubAdminGeneralId)
            builder.child(FundSubId, LpDashboardUpdateId)
            builder.child(FundSubId, LpActivityLogUpdateId)
            builder.child(FundSubId, FundSubAdminInfoId)
            builder.child(FundSubId, FundSubLpTagId)
            builder.child(FundSubId, FundSubContactGroupId)
            builder.child(FundSubAdminRestrictedId, FolderId)
            builder.child(FundSubId, FundSubDashboardId)
            builder.child(FundSubDashboardId, FilterPresetId)
            builder.child(FundSubId, CustomDataColumnId)
            builder.child(FundSubId, FundSubStorageIntegrationId)
            builder.child(FundSubId, InvestmentFundId)
            builder.child(FundSubId, FundSubReportingId)
            builder.child(FundSubLpId, FundSubItoolLpLockId)
            builder.child(FundSubLpId, FundSubItoolLpNoteId)
          }

          {
            builder.child(FundSubId, FundSubLpId)
            builder.child(FundSubId, FundSubInvestorGroupId)
            builder.child(FundSubLpId, NewFlowId)
            builder.child(FundSubLpId, FolderId)
            builder.child(FundSubId, WebhookEndpointId)
            builder.child(FundSubId, WebhookEventId)
            builder.child(FundSubId, FundSubSelfServiceExportTemplateId)

            {
              builder.child(FundSubLpId, FundSubLpRestrictedId)
              builder.child(FundSubLpId, ActivityLogId)
              builder.child(FundSubLpId, FundSubLpActivityId)
              builder.child(FundSubLpId, FundsubInvestorRoleId)
              builder.child(FundSubLpId, FundSubSupportingDocId)
              builder.child(FundSubLpId, FundSubSupportingDocTrackingId)
              builder.child(FundSubLpRestrictedId, FolderId)
              builder.child(FundSubLpId, ReviewPackageEmailLogId)
              builder.child(FundSubLpId, DocRequestId)
              builder.child(FundSubLpId, FundSubLpInternalCommentChannelId)
              builder.child(FundSubLpId, FundSubLpFolderTypeId)
              builder.child(FundSubLpFolderTypeId, FolderId)
              builder.child(FundSubLpId, SubscriptionSchemaDataId)

              {
                builder.child(DocRequestId, FormSubmissionId)
                builder.child(DocRequestId, DocSubmissionId)
              }

              builder.root(FundSubSingleUserInvitationLinkId)
            }
          }

          {
            builder.child(FundSubId, LongTaskId)
            builder.child(LongTaskId, FundSubBatchInvitationId)
            builder.child(FundSubBatchInvitationId, FundSubBatchInvitationItemId)
            builder.child(LongTaskId, FundSubDataImportId)
            builder.child(LongTaskId, FundSubDataExportId)
            builder.child(FundSubDataImportId, FundSubDataImportItemId)
            builder.child(FundSubDataImportItemId, DataImportItemResultId)
            builder.child(FundSubDataExportId, FundSubDataExportItemId)
            builder.child(LongTaskId, FundSubBatchActionId)
            builder.child(FundSubBatchActionId, FundSubBatchActionItemId)
          }

          {
            builder.child(FundSubId, FundSubSimulatorProgressId)
          }

          {
            builder.child(UserRestrictedId, UserFundSubPublicId)
            builder.child(UserRestrictedId, UserFundSubAdminId)
            builder.child(UserRestrictedId, UserFundSubLpId)
          }

          {
            builder.child(FundSubId, FundSubGpCompositeId)
            builder.child(FundSubLpId, FundSubLpCompositeId)
            builder.child(FundSubLpCompositeId, FundSubCollaboratorSSOTombstoneId)
          }

          {
            builder.child(FundSubLpId, FundSubDataExtractRequestId)
            builder.child(FundSubId, FundSubDataExtractTestProfileId)
          }
        }

        {
          builder.child(TransactionId, SignatureModuleId)
          builder.child(SignatureModuleId, SignatureRequestId)
          builder.child(SignatureModuleId, FolderId)
          builder.child(SignatureModuleId, SignatureModuleRoleId)

          builder.child(SignatureRequestId, SignatureRequestItemId)
          builder.child(SignatureRequestId, SignatureRequestRoleId)
        }

        {
          builder.child(TransactionOfferingId, IssueId)
          builder.child(TransactionOfferingId, IssueListId)
          builder.child(TransactionOfferingId, FolderId)
          builder.child(UserRestrictedId, UserIssueTrackerId)
          builder.child(TransactionOfferingId, OfferingResourceId)
          builder.child(TransactionOfferingId, IssueTrackerCollaborationChannelId)
          builder.child(IssueId, IssueRoleId)
          builder.child(IssueId, MentionId)
          builder.child(IssueListId, IssueListRoleId)
          builder.child(IssueTrackerCollaborationChannelId, IssueTrackerCollaborationChannelRoleId)
          builder.child(TransactionOfferingId, TransactionOfferingRoleId)
          builder.child(UserRestrictedId, CollaboratorGroupId)
        }

        {
          builder.child(TransactionId, TransactionParticipantId)
          builder.child(TransactionId, TransactionOfferingId)
          builder.child(TransactionParticipantId, TransactionParticipantOfferingId)
          builder.child(TransactionParticipantId, TransactionParticipantRoleId)
        }

        {
          builder.child(IssueId, IssueLpCompositeId)
        }
      }
    }

    // Comment space root
    {
      builder.root(CommentThreadId)

      {
        builder.child(CommentThreadId, NewFlowId)
      }
    }

    // Serverless Id
    {
      builder.root(ServerlessRequestId)
    }

    // Protected link Id
    {
      builder.root(ProtectedLinkId)
      builder.root(OneTimeLinkId)
    }

    // Long running task Id
    {
      builder.root(LongRunningTaskId)

      {
        builder.child(LongRunningTaskId, ZipFileTaskId)
      }
    }

    {
      builder.root(PortalProductRoleId)
      builder.root(PortalSectionRoleId)
      builder.root(PortalGroupId)
    }

    {
      builder.root(PortalFundSubTeamId)
    }

    {
      builder.root(DisclaimerId)
    }

    // Zap
    {
      builder.root(ZapId)
      builder.root(ZapEventId)
    }

    // Link restriction
    {
      builder.root(LinkRestrictionId)
      builder.root(LinkRestrictionListId)
    }

    // OTP Authentication
    {
      builder.root(OTPAuthenticationRequestId)
      builder.child(OTPAuthenticationRequestId, OTPAuthenticationCodeId)
      builder.root(RevertOTPAuthenticationRequestId)
      builder.root(RevertOTPAuthenticationCodeId)
    }

    // Authentication whitelabel
    {
      builder.root(AuthenticationWhitelabelId)
    }

    // Data Extract
    {
      builder.root(DataExtractProjectId)
      builder.child(DataExtractProjectId, DataExtractProjectItemId)
      builder.child(DataExtractProjectId, FolderId)
      builder.child(DataExtractProjectId, TeamId)
      builder.child(DataExtractProjectId, LongTaskId)
      builder.child(DataExtractProjectItemId, DataExtractUserDocumentId)
    }

    // Environment
    {
      builder.root(EnvironmentId)
      builder.child(EnvironmentId, EnvironmentWhitelabelId)
      builder.child(EnvironmentId, EnvironmentReauthenticationPolicyId)
      builder.child(EnvironmentId, EnvironmentEnforcementPolicyId)
      builder.child(EnvironmentId, EnvironmentSSOBindingId)

      builder.root(UserEnvironmentSSOBindingId)
      builder.root(EmailDomainEnvironmentSSOBindingId)

      builder.child(EnvironmentId, GlobalEmailDomainEnvironmentSSOBindingId)
      builder.child(EnvironmentId, LongTaskId)
    }

    // Task Request
    {
      builder.root(TaskRequestId)
      builder.child(TaskRequestId, TaskRequestItemId)
      builder.child(TaskRequestId, TeamId)
      builder.child(TaskRequestId, FolderId)
    }

    // Fund Data
    {
      builder.root(FundDataFirmDefaultSettingId)

      builder.root(FundDataFirmId)
      builder.child(FundDataFirmId, TeamId)
      builder.child(FundDataFirmId, FundDataGroupRoleId)
      builder.child(FundDataFirmId, FundDataRoleId)
      builder.child(FundDataFirmId, InternalEmailId)
      builder.child(FundDataFirmId, LongTaskId)
      builder.child(FundDataFirmId, FundDataFundSubSyncEventId)
      builder.child(FundDataFirmId, FundDataFirmOrganizationId)
      builder.child(FundDataFirmId, FundDataFeatureSwitchTrackingId)
      builder.child(FundDataFirmId, FundDataFundGroupId)
      builder.child(FundDataFirmId, FundDataExpirationDateConfigId)
      builder.child(FundDataFirmId, FundDataInvestmentEntityProfileHistoryId)
      builder.child(FundDataFirmId, FundDataOpportunityPageId)
      builder.child(FundDataFirmId, FundDataLandingPageLinkId)
      builder.child(FundDataFirmId, CommunicationTypeId)
      builder.child(CommunicationTypeId, TeamId)

      {
        builder.child(FundDataFirmId, FundFamilyId)
        builder.child(FundDataFirmId, FundLegalEntityId)
        builder.child(FundLegalEntityId, FundShareClassId)
        builder.child(FundLegalEntityId, FundInvestmentId)
        builder.child(FundLegalEntityId, FundTransactionId)
        builder.child(FundLegalEntityId, TeamId)

        {
          builder.child(FundTransactionId, FundTransactionDocumentId)
          builder.child(FundTransactionId, FolderId)
        }
      }

      {
        builder.child(FundDataFirmId, PortalInstanceId)
      }

      {
        builder.child(FundDataFirmId, FundDataFundId)
        builder.child(FundDataFirmId, FundDataDataRoomId)
        builder.child(FundDataFundId, FundDataFundSubscriptionId)
        builder.child(FundDataFundSubscriptionId, FundDataFundSubSyncConflictId)
        builder.child(FundDataFundSubscriptionId, FundDataFundSubscriptionDocumentId)
        builder.child(FundDataFundId, FolderId)
      }

      {
        builder.child(FundDataFirmId, FundDataClientGroupId)
        builder.child(FundDataFirmId, FundDataInvestorId)
        builder.child(FundDataClientGroupId, TeamId)
        builder.child(FundDataInvestorId, FundDataInvestmentEntityId)
        builder.child(FundDataInvestorId, FolderId)
        builder.child(FundDataInvestorId, TeamId)
        builder.child(FundDataInvestmentEntityId, FolderId)
        builder.child(FundDataInvestmentEntityId, FundDataInvestmentEntityAssessmentId)
        builder.child(FundDataInvestmentEntityId, FundDataInvestmentEntityDocumentId)
        builder.child(FundDataInvestmentEntityId, FundDataInvestmentEntityContactId)
        builder.child(FundDataInvestmentEntityId, FundDataProfileConflictId)
        builder.child(FundDataInvestmentEntityId, TeamId)

        {
          builder.child(FundDataInvestmentEntityDocumentId, FundDataInvestmentEntityDocumentTextractId)
        }
      }

    }

    // Batch action
    {
      builder.child(LongTaskId, BatchActionId)
      builder.child(BatchActionId, BatchActionItemId)
    }

    // Tag
    {
      builder.child(FundDataFirmId, TagListId)
      builder.child(FundDataFirmDefaultSettingId, TagListId)
      builder.child(TagListId, TagItemId)
    }

    // Webcontent
    {
      builder.child(FundDataFirmId, WebContentId)
      builder.child(FundDataOpportunityPageId, WebContentId)
      builder.child(WebContentId, FolderId)
    }

    // Note
    {
      builder.child(FundDataFirmId, FundDataNoteGroupId)
      builder.child(FundDataNoteGroupId, FundDataNoteId)
    }

    {
      builder.root(BatchUploadId)
    }

    // AmlKyc
    {
      builder.root(AmlCheckId)
    }

    // Integration
    {
      builder.root(IntegrationId)
    }

    // RIA
    {
      builder.root(RiaEntityId)

      {
        builder.child(RiaEntityId, RiaFundGroupId)
        builder.child(RiaFundGroupId, TeamId)
        builder.child(RiaEntityId, TeamId)
        builder.child(RiaEntityId, InternalEmailId)
      }
    }

    // Email
    {
      builder.root(GeneratedEmailId)
    }

    // Integ Platform
    {
      builder.child(EntityId, IntegPlatformEntityFeatureAccessId)
    }

    // Merge Textract
    {
      builder.root(MergeTextractProjectId)
    }

    // Cue Module
    {
      builder.root(AnduinCueSystemId)
      {
        builder.child(AnduinCueSystemId, CueModuleId)
      }
    }

    {
      builder.child(FormId, CueModuleId)
      builder.child(AnnotationDocumentId, CueModuleId)
      builder.child(DataExtractProjectId, CueModuleId)
      builder.child(SaDataTemplateId, CueModuleId)
      builder.child(CueModuleId, CueModuleVersionId)
      builder.child(CueMappingId, CueModuleId)
      builder.child(FormId, CueMappingId)
      builder.child(DataExtractProjectId, CueMappingId)
    }

    {
      builder.root(CueTableId)
    }

    // Temporal workflow
    {
      builder.child(FolderId, TFlowId)
      builder.child(FileId, TFlowId)
      builder.child(InvestorPortalId, TFlowId)
      builder.child(BatchUploadId, TFlowId)
      builder.child(TFlowId, TFlowEventId)
      builder.child(TFlowId, TFlowQueryId)
    }

    // Async API
    {
      builder.child(UserRestrictedId, AsyncApiId)
    }

    // Investor Portal
    {
      builder.root(InvestorPortalId)
      builder.child(InvestorPortalId, FolderId)
    }
  }

  lazy val parser: RadixParser[RadixId] = new RadixParser(registry, ModelId.Separator)
}
