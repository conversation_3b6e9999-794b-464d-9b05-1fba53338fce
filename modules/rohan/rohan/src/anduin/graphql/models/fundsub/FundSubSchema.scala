// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.graphql.models.fundsub

import sangria.marshalling.FromInput
import sangria.schema.*
import sangria.util.tag.@@

import anduin.autofill.ComputeFormMatchingResult.ComputeFormMatchingStatus
import anduin.fundsub.autoprefill.*
import anduin.fundsub.endpoint.operation.*
import anduin.fundsub.endpoint.whitelabel.FundSubWhiteLabelData
import anduin.fundsub.homepage.FundSubEnvironmentNavigationData
import anduin.fundsub.utils.FundSubWhiteLabelUtils.CustomValue
import anduin.graphql.MapSchema
import anduin.graphql.models.id.*
import anduin.graphql.models.signature.ESignatureSchema
import anduin.graphql.models.squant.MoneyMessageSchema
import anduin.graphql.models.time.InstantSchema
import anduin.graphql.models.utils.GraphqlUtils
import anduin.graphql.query.context.GraphqlServerContext
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus
import anduin.protobuf.fundsub.*
import anduin.protobuf.fundsub.models.FundSubPublicModel.PackageType
import anduin.protobuf.fundsub.models.FundSubSignatureConfig.SignerAuthenticationMethod
import anduin.protobuf.fundsub.models.{AnduinSignSignatureConfig, DocusignSignatureConfig, FundSubSignatureConfig}
import anduin.whitelabel.dashboard.DashboardWhitelabelValue
import anduin.whitelabel.dashboard.EnvironmentDashboardWhitelabelProtocols.DashboardWhitelabelData

object FundSubSchema {

  val FundSubIdArgument: Argument[FundSubId] = Argument(
    name = "id",
    argumentType = FundSubIdSchema.FundSubIdType
  )

  val CurrentFundIdArgument: Argument[FundSubId] = Argument(
    name = "curFundId",
    argumentType = FundSubIdSchema.FundSubIdType
  )

  val FundSubIdListArgument: Argument[Seq[FundSubId @@ FromInput.CoercedScalaResult]] = Argument(
    name = "ids",
    argumentType = ListInputType(FundSubIdSchema.FundSubIdType)
  )

  val FundSubFilterOutIdListArgument: Argument[Seq[FundSubId @@ FromInput.CoercedScalaResult]] = Argument(
    name = "filterOutIds",
    argumentType = ListInputType(FundSubIdSchema.FundSubIdType)
  )

  val FundSubLpIdArgument: Argument[FundSubLpId] = Argument(
    name = "lpId",
    argumentType = FundSubIdSchema.FundSubLpIdType
  )

  val LpStatusType: EnumType[LpStatus] = GraphqlUtils.protobufEnum[LpStatus]

  val FundSubEventType: EnumType[FundSubEvent] =
    GraphqlUtils.protobufEnumWithUnrecognized[FundSubEvent](
      using FundSubEvent,
      Seq(
        10, 24, 75, 30, 31
      ).map(FundSubEvent.Unrecognized.apply)
    )

  val FundSubInvestorRoleType: EnumType[FundSubLpRole] = GraphqlUtils.protobufEnum[FundSubLpRole]
  val LpOrderTypeType: EnumType[LpOrderType] = GraphqlUtils.protobufEnum[LpOrderType]

  val FundSubSupportingContactInfoType: ObjectType[Unit, FundSubSupportingContactInfo] = ObjectType(
    name = "FundSubSupportingContactInfo",
    fields = fields[Unit, FundSubSupportingContactInfo](
      Field(
        "name",
        StringType,
        resolve = _.value.name
      ),
      Field(
        "email",
        StringType,
        resolve = _.value.email
      ),
      Field(
        "phone",
        StringType,
        resolve = _.value.phone
      )
    )
  )

  val FundSubSupportingContactsType: ObjectType[Unit, FundSubSupportingContacts] = ObjectType(
    name = "FundSubSupportingContacts",
    fields = fields[Unit, FundSubSupportingContacts](
      Field(
        "orgName",
        StringType,
        resolve = _.value.orgName
      ),
      Field(
        "contacts",
        ListType(FundSubSupportingContactInfoType),
        resolve = _.value.contacts
      )
    )
  )

  val FeatureSwitchType: ObjectType[Unit, FeatureSwitch] = ObjectType(
    name = "FundSubFeatureSwitchType",
    fields = fields[Unit, FeatureSwitch](
      Field(
        "disableLpUndoSubmission",
        BooleanType,
        resolve = _.value.disableLpUndoSubmission
      ),
      Field(
        "disableLpSupportingDoc",
        BooleanType,
        resolve = _.value.disableLpSupportingDoc
      ),
      Field(
        "enabledCustomLpId",
        BooleanType,
        resolve = _.value.enabledCustomLpId
      ),
      Field(
        "formCommentSwitch",
        BooleanType,
        resolve = _.value.formCommentSwitch
      ),
      Field(
        "disableSubmissionInstruction",
        BooleanType,
        resolve = _.value.disableSubmissionInstruction
      ),
      Field(
        "disableInvestFromAdditionalEntity",
        BooleanType,
        resolve = _.value.disableInvestFromAdditionalEntity
      ),
      Field(
        "disableDownloadSubscriptionDocument",
        BooleanType,
        resolve = _.value.disableDownloadSubscriptionDocument
      ),
      Field(
        "disabledMarkAsNotApplicable",
        BooleanType,
        resolve = _.value.disabledMarkAsNotApplicable
      ),
      Field(
        "enabledAddAdditionalTaxForms",
        BooleanType,
        resolve = _.value.enabledAddAdditionalTaxForms
      ),
      Field(
        "hideFormFilesAndSigningButtons",
        BooleanType,
        resolve = _.value.hideFormFilesAndSigningButtons
      ),
      Field(
        "showManualOrdersToInvestors",
        BooleanType,
        resolve = _.value.showManualOrdersToInvestors
      ),
      Field(
        "disableFundContactInInvestorWorkspace",
        BooleanType,
        resolve = _.value.disableFundContactInInvestorWorkspace
      ),
      Field(
        "enableFormDiff",
        BooleanType,
        resolve = _.value.enableFormDiff
      ),
      Field(
        "enableLpManualSubmitSubscription",
        BooleanType,
        resolve = _.value.enableLpManualSubmitSubscription
      ),
      Field(
        "enableLpReusePreviousSignedVersion",
        BooleanType,
        resolve = _.value.enableLpReusePreviousSignedVersion
      ),
      Field(
        "disableInvestorTaxFormElectronicSignature",
        BooleanType,
        resolve = _.value.disableInvestorTaxFormElectronicSignature
      ),
      Field(
        "exportInvestorDashboardTagsAsColumns",
        BooleanType,
        resolve = _.value.exportInvestorDashboardTagsAsColumns
      ),
      Field(
        "blockLpSubmissionUntilFinishSupporting",
        BooleanType,
        resolve = _.value.blockLpSubmissionUntilFinishSupporting
      ),
      Field(
        "enableAutoPrefill",
        BooleanType,
        resolve = _.value.enableAutoPrefill
      ),
      Field(
        "enableImportData",
        BooleanType,
        resolve = _.value.enableImportData
      ),
      Field(
        "enableFundPermission",
        BooleanType,
        resolve = _.value.enableFundPermission
      ),
      Field(
        "enableAutoPrefillForLp",
        BooleanType,
        resolve = _.value.enableAutoPrefillForLp
      ),
      Field(
        "enableEnforceLpSubmitReviewBeforeSignature",
        BooleanType,
        resolve = _.value.enableEnforceLpSubmitReviewBeforeSignature
      ),
      Field(
        "allowMultiStepInSubscriptionReview",
        BooleanType,
        resolve = _.value.allowMultiStepInSubscriptionReview
      ),
      Field(
        "enableAmlKycCommenting",
        BooleanType,
        resolve = _.value.enableAmlKycCommenting
      ),
      Field(
        "enableLpProfile",
        BooleanType,
        resolve = _.value.enableLpProfile
      ),
      Field(
        "preventLpUploadSupportingDocAfterCountersigned",
        BooleanType,
        resolve = _.value.preventLpUploadSupportingDocAfterCountersigned
      ),
      Field(
        "enableSupportingDocReview",
        BooleanType,
        resolve = _.value.enableSupportingDocReview
      ),
      Field(
        "enableCommentMentioning",
        BooleanType,
        resolve = _.value.enableCommentMentioning
      ),
      Field(
        "enableCommentAssignment",
        BooleanType,
        resolve = _.value.enableCommentAssignment
      ),
      Field(
        "disableAbilityOfInvestorToInviteCollaborator",
        BooleanType,
        resolve = _.value.disableAbilityOfInvestorToInviteCollaborator
      ),
      Field(
        "enableAmlKycListAfterRequiredFieldsCompleted",
        BooleanType,
        resolve = _.value.enableAmlKycListAfterRequiredFieldsCompleted
      ),
      Field(
        "showOrderMetadataInDetailView",
        BooleanType,
        resolve = _.value.showOrderMetadataInDetailView
      ),
      Field(
        "enableImportFromFundData",
        BooleanType,
        resolve = _.value.enableImportFromFundData
      ),
      Field(
        "disableAbilityToCountersign",
        BooleanType,
        resolve = _.value.disableAbilityToCountersign
      ),
      Field(
        "disableLpResolvingComment",
        BooleanType,
        resolve = _.value.disableLpResolvingComment
      ),
      Field(
        "disableSelfServiceExport",
        BooleanType,
        resolve = _.value.disableSelfServiceExport
      ),
      Field(
        "enableOntologyFormMatching",
        BooleanType,
        resolve = _.value.enableOntologyFormMatching
      ),
      Field(
        "disableInAppAnnouncement",
        BooleanType,
        resolve = _.value.disableInAppAnnouncement
      ),
      Field(
        "hideAllInvitationButtonOnGpSide",
        BooleanType,
        resolve = _.value.hideAllInvitationButtonOnGpSide
      ),
      Field(
        "enableRia",
        BooleanType,
        resolve = _.value.enableRia
      ),
      Field(
        "disableRiaBanner",
        BooleanType,
        resolve = _.value.disableRiaBanner
      ),
      Field(
        "enableSideLetter",
        BooleanType,
        resolve = _.value.enableSideLetter
      ),
      Field(
        "forcingFundManagersToReceiveAllEmailNotifications",
        BooleanType,
        resolve = _.value.forcingFundManagersToReceiveAllEmailNotifications
      ),
      Field(
        "enableDataExtractDraftFormLog",
        BooleanType,
        resolve = _.value.enableDataExtractDraftFormLog
      ),
      Field(
        "showSwitchAllowFormEditPostSigning",
        BooleanType,
        resolve = _.value.showSwitchAllowFormEditPostSigning
      ),
      Field(
        "allowFormEditPostSigning",
        BooleanType,
        resolve = _.value.allowFormEditPostSigning
      )
    )
  )

  val EmbedInvestorDataPlacementType: EnumType[EmbedInvestorDataOnSubDocConfig.Placement] =
    GraphqlUtils.protobufEnum[EmbedInvestorDataOnSubDocConfig.Placement]

  val EmbedInvestorDataOnSubDocConfigType: ObjectType[Unit, EmbedInvestorDataOnSubDocConfig] = ObjectType(
    name = "emberInvestorDataOnSubDocConfigType",
    fields = fields[Unit, EmbedInvestorDataOnSubDocConfig](
      Field(
        "placement",
        EmbedInvestorDataPlacementType,
        resolve = _.value.placement
      )
    )
  )

  val LpGeneralConfigType: ObjectType[Unit, FundSubLpGeneralConfig] = ObjectType(
    name = "LpGeneralConfig",
    fields = fields[Unit, FundSubLpGeneralConfig](
      Field(
        "enableImportExportJsonValues",
        BooleanType,
        resolve = _.value.enableImportExportJsonValues
      ),
      Field(
        "enableLivePreviewMode",
        BooleanType,
        resolve = _.value.enableLivePreviewMode
      ),
      Field(
        "enableLpSigningInstruction",
        BooleanType,
        resolve = _.value.enableLpSigningInstruction
      ),
      Field(
        "lpSigningInstructionContent",
        StringType,
        resolve = _.value.lpSigningInstructionContent
      ),
      Field(
        "embedInvestorDataOnSubDocConfig",
        OptionType(EmbedInvestorDataOnSubDocConfigType),
        resolve = _.value.embedInvestorDataOnSubDocConfig
      )
    )
  )

  val SignerAuthenticationMethodType: EnumType[SignerAuthenticationMethod] =
    GraphqlUtils.protobufEnum[SignerAuthenticationMethod]

  private val AnduinSignSignatureConfigType: ObjectType[Unit, AnduinSignSignatureConfig] = ObjectType(
    name = "AnduinSignSignatureConfig",
    fields = fields[Unit, AnduinSignSignatureConfig](
      Field(
        "eSignDisclaimerMessage",
        OptionType(StringType),
        resolve = _.value.eSignDisclaimerMessage
      ),
      Field(
        "enabledFinishSigning2fa",
        BooleanType,
        resolve = _.value.enabledFinishSigning2Fa
      ),
      Field(
        "applicableSignatureTypes",
        ListType(ESignatureSchema.SignatureTypeType),
        resolve = _.value.applicableSignatureTypes.toSeq
      ),
      Field(
        "enabledElectronicSeal",
        BooleanType,
        resolve = _.value.enabledElectronicSeal
      ),
      Field(
        "isEnabled",
        BooleanType,
        resolve = _.value.isEnabled
      )
    )
  )

  private val DocusignSignatureConfigType: ObjectType[Unit, DocusignSignatureConfig] = ObjectType(
    name = "DocusignSignatureConfig",
    fields = fields[Unit, DocusignSignatureConfig](
      Field(
        "enabledQes",
        BooleanType,
        resolve = _.value.enabledQes
      ),
      Field(
        "isEnabled",
        BooleanType,
        resolve = _.value.isEnabled
      ),
      Field(
        "enabledRecipientAuthentication",
        BooleanType,
        resolve = _.value.enabledRecipientAuthentication
      ),
      Field(
        "enforceIdVerification",
        BooleanType,
        resolve = _.value.enforceIdVerification
      )
    )
  )

  val FundSubSignatureConfigType: ObjectType[Unit, FundSubSignatureConfig] = ObjectType(
    name = "FundSubSignatureConfigType",
    fields = fields[Unit, FundSubSignatureConfig](
      Field(
        "signerAuthenticationMethod",
        SignerAuthenticationMethodType,
        resolve = _.value.signerAuthenticationMethod
      ),
      Field(
        "anduinSignSignatureConfig",
        AnduinSignSignatureConfigType,
        resolve = _.value.anduinSignSignatureConfig
      ),
      Field(
        "docusignSignatureConfig",
        DocusignSignatureConfigType,
        resolve = _.value.docusignSignatureConfig
      ),
      Field(
        "dateFormat",
        OptionType(StringType),
        resolve = _.value.dateFormat
      ),
      Field(
        "enableOneEnvelopeExperience",
        BooleanType,
        resolve = _.value.enableOneEnvelopeExperience
      )
    )
  )

  val LpFlowEnumType: EnumType[LpFlowType] = GraphqlUtils.protobufEnum[LpFlowType]
  val PackageEnumType: EnumType[PackageType] = GraphqlUtils.protobufEnum[PackageType]

  val FundSubClosingConfigType: ObjectType[Unit, FundSubClientClosingConfig] = ObjectType(
    name = "fundSubClosingConfigType",
    fields = fields[Unit, FundSubClientClosingConfig](
      Field(
        "isClosed",
        BooleanType,
        resolve = _.value.isClosed
      ),
      Field(
        "updatedBy",
        UserIdSchema.UserIdType,
        resolve = _.value.updatedBy
      ),
      Field(
        "updatedAt",
        OptionType(InstantSchema.InstantType),
        resolve = _.value.updatedAt
      )
    )
  )

  val FundSubDashboardConfigType: ObjectType[Unit, FundSubDashboardConfig] = ObjectType(
    name = "fundSubDashboardConfigType",
    fields = fields[Unit, FundSubDashboardConfig](
      Field(
        "enableStandardDashboard",
        BooleanType,
        resolve = _.value.enableStandardDashboard
      ),
      Field(
        "enableAdvancedDashboard",
        BooleanType,
        resolve = _.value.enableAdvancedDashboard
      )
    )
  )

  private val CustomValueType = EnumType(
    name = "customValue",
    values = CustomValue.values.toList.map { value =>
      EnumValue(
        name = value.value,
        value = value
      )
    }
  )

  private val FundSubWhiteLabelCustomValueMapType = MapSchema.entryType(
    name = "customValuesMap",
    keyType = CustomValueType,
    valueType = StringType
  )

  val FundSubWhiteLabelDataType: ObjectType[GraphqlServerContext, FundSubWhiteLabelData] = ObjectType(
    name = "FundSubWhiteLabelData",
    fields = fields[GraphqlServerContext, FundSubWhiteLabelData](
      Field(
        name = "isEnabled",
        fieldType = BooleanType,
        resolve = _.value.isEnabled
      ),
      Field(
        name = "logoUrl",
        fieldType = OptionType(StringType),
        resolve = _.value.logoUrl
      ),
      Field(
        name = "longLogoUrl",
        fieldType = OptionType(StringType),
        resolve = _.value.longLogoUrl
      ),
      MapSchema.field(
        name = "customValues",
        entryType = FundSubWhiteLabelCustomValueMapType,
        resolve = ctx => ctx.value.customValues
      ),
      MapSchema.field(
        name = "authCustomValues",
        entryType = MapSchema.entryType(
          name = "authCustomValuesMap",
          keyType = StringType,
          valueType = StringType
        ),
        resolve = ctx => ctx.value.authCustomValues
      ),
      Field(
        name = "providerLongLogo",
        fieldType = OptionType(StringType),
        resolve = _.value.providerLongLogo
      ),
      Field(
        name = "providerLogo",
        fieldType = OptionType(StringType),
        resolve = _.value.providerLogo
      ),
      Field(
        name = "providerEntity",
        fieldType = OptionType(EntityIdSchema.EntityIdType),
        resolve = _.value.providerEntity
      ),
      Field(
        name = "enableProviderBranding",
        fieldType = BooleanType,
        resolve = _.value.enableProviderBranding
      ),
      Field(
        name = "enableAuthWhiteLabel",
        fieldType = BooleanType,
        resolve = _.value.enableAuthWhiteLabel
      )
    )
  )

  val DemoInfoType: ObjectType[GraphqlServerContext, DemoInfo] = ObjectType(
    name = "DemoInfoType",
    fields = fields[GraphqlServerContext, DemoInfo](
      Field(
        name = "demoLpId",
        fieldType = FundSubIdSchema.FundSubLpIdType,
        resolve = _.value.demoLpId
      ),
      Field(
        name = "demoFundDataFirmIdOpt",
        fieldType = OptionType(FundSubIdSchema.FundDataFirmIdType),
        resolve = _.value.demoFundDataFirmIdOpt
      ),
      Field(
        name = "mainDemoFundSubId",
        fieldType = FundSubIdSchema.FundSubIdType,
        resolve = _.value.mainDemoFundSubId
      ),
      Field(
        name = "additionalDemoFundSubIds",
        fieldType = ListType(FundSubIdSchema.FundSubIdType),
        resolve = _.value.additionalDemoFundSubIds
      ),
      Field(
        name = "additionalDemoLpIds",
        fieldType = ListType(FundSubIdSchema.FundSubLpIdType),
        resolve = _.value.additionalDemoLpIds
      ),
      Field(
        name = "demoMarketingDataRoomWorkflowIdOpt",
        fieldType = OptionType(DataRoomWorkflowIdSchema.DataRoomWorkflowIdType),
        resolve = _.value.demoMarketingDataRoomWorkflowIdOpt
      ),
      Field(
        name = "demoRiaFundGroupIdOpt",
        fieldType = OptionType(RiaIdSchema.RiaFundGroupIdType),
        resolve = _.value.demoRiaFundGroupIdOpt
      )
    )
  )

  val ComputeFormMatchingStatusType: EnumType[ComputeFormMatchingStatus] =
    GraphqlUtils.protobufEnum[ComputeFormMatchingStatus]

  val FundInfoForAutoPrefillDataType: ObjectType[Unit, FundInfoForAutoPrefill] = ObjectType(
    name = "FundInfoForAutoPrefill",
    fields = fields[Unit, FundInfoForAutoPrefill](
      Field(
        name = "fundSubId",
        fieldType = FundSubIdSchema.FundSubIdType,
        resolve = _.value.fundSubId
      ),
      Field(
        name = "fundName",
        fieldType = StringType,
        resolve = _.value.fundName
      ),
      Field(
        name = "numOfInvestors",
        fieldType = IntType,
        resolve = _.value.numOfInvestors
      ),
      Field(
        name = "currencyOpt",
        fieldType = OptionType(MoneyMessageSchema.CurrencyMessageType),
        resolve = _.value.currencyOpt
      ),
      Field(
        name = "formSimilarity",
        fieldType = FloatType,
        resolve = _.value.formSimilarity
      ),
      Field(
        name = "demoInfoOpt",
        OptionType(DemoInfoType),
        resolve = _.value.demoInfoOpt
      ),
      Field(
        name = "computeSimilarityStatus",
        ComputeFormMatchingStatusType,
        resolve = _.value.computeSimilarityStatus
      ),
      Field(
        name = "similarityStatusUpdatedAt",
        InstantSchema.InstantType,
        resolve = _.value.similarityStatusUpdatedAt
      )
    )
  )

  val AllFundInfoForAutoPrefillDataType: ObjectType[Unit, AllFundInfoForAutoPrefill] = ObjectType(
    name = "AllFundInfoForAutoPrefill",
    fields = fields[Unit, AllFundInfoForAutoPrefill](
      Field(
        name = "currentFundEntityId",
        fieldType = EntityIdSchema.EntityIdType,
        resolve = _.value.currentFundEntityId
      ),
      Field(
        name = "currentFundEntityName",
        fieldType = StringType,
        resolve = _.value.currentFundEntityName
      ),
      Field(
        name = "fundInfoList",
        fieldType = ListType(FundInfoForAutoPrefillDataType),
        resolve = _.value.fundInfoList
      )
    )
  )

  val InvestorUserContactInfoDataType: ObjectType[Unit, InvestorUserContactInfo] = ObjectType(
    name = "investorUserContactInfo",
    fields = fields[Unit, InvestorUserContactInfo](
      Field(
        name = "userId",
        fieldType = UserIdSchema.UserIdType,
        resolve = _.value.userId
      ),
      Field(
        name = "email",
        fieldType = StringType,
        resolve = _.value.email
      ),
      Field(
        name = "firstName",
        fieldType = StringType,
        resolve = _.value.firstName
      ),
      Field(
        name = "lastName",
        fieldType = StringType,
        resolve = _.value.lastName
      )
    )
  )

  val ConflictLpInfoType: ObjectType[Unit, ConflictLpInfo] = ObjectType(
    name = "conflictLpInfo",
    fields = fields[Unit, ConflictLpInfo](
      Field(
        name = "lpId",
        fieldType = FundSubIdSchema.FundSubLpIdType,
        resolve = _.value.lpId
      ),
      Field(
        name = "investmentEntity",
        fieldType = StringType,
        resolve = _.value.investmentEntity
      ),
      Field(
        name = "mainContactEmail",
        fieldType = StringType,
        resolve = _.value.mainContactEmail
      ),
      Field(
        name = "similarityScore",
        fieldType = FloatType,
        resolve = _.value.similarityScore
      )
    )
  )

  val InvestorInfoForAutoPrefillDataType: ObjectType[Unit, InvestorInfoForAutoPrefill] = ObjectType(
    name = "investorInfoForAutoPrefill",
    fields = fields[Unit, InvestorInfoForAutoPrefill](
      Field(
        name = "lpId",
        fieldType = FundSubIdSchema.FundSubLpIdType,
        resolve = _.value.lpId
      ),
      Field(
        name = "investmentEntity",
        fieldType = StringType,
        resolve = _.value.investmentEntity
      ),
      Field(
        name = "mainLpContactInfo",
        fieldType = InvestorUserContactInfoDataType,
        resolve = _.value.mainLpContactInfo
      ),
      Field(
        name = "collaboratorContactInfos",
        fieldType = ListType(InvestorUserContactInfoDataType),
        resolve = _.value.collaboratorContactInfos
      ),
      Field(
        name = "status",
        fieldType = LpStatusType,
        resolve = _.value.status
      ),
      Field(
        name = "commitmentAmount",
        fieldType = OptionType(FloatType),
        resolve = _.value.commitmentAmount
      ),
      Field(
        name = "lastUpdated",
        fieldType = OptionType(InstantSchema.InstantType),
        resolve = _.value.lastUpdated
      ),
      Field(
        name = "customId",
        fieldType = StringType,
        resolve = _.value.customId
      ),
      Field(
        name = "tagNames",
        fieldType = ListType(StringType),
        resolve = _.value.tagNames
      ),
      Field(
        name = "conflictLpOpt",
        fieldType = OptionType(ConflictLpInfoType),
        resolve = _.value.conflictLpInfo
      ),
      Field(
        name = "formFillingProgress",
        fieldType = FloatType,
        resolve = _.value.formFillingProgress
      )
    )
  )

  val AllPastSubscriptionInfoForLpAutoPrefillType: ObjectType[Unit, AllPastSubscriptionInfoForLpAutoPrefill] =
    ObjectType(
      name = "AllPastSubscriptionInfoForLpAutoPrefill",
      fields = fields[Unit, AllPastSubscriptionInfoForLpAutoPrefill](
        Field(
          name = "currentFundEntityId",
          fieldType = EntityIdSchema.EntityIdType,
          resolve = _.value.currentFundEntityId
        ),
        Field(
          name = "currentFundEntityName",
          fieldType = StringType,
          resolve = _.value.currentFundEntityName
        ),
        Field(
          name = "fundInfoList",
          fieldType = ListType(FundInfoForAutoPrefillDataType),
          resolve = _.value.fundInfoList
        ),
        Field(
          name = "investorInfoList",
          fieldType = ListType(InvestorInfoForAutoPrefillDataType),
          resolve = _.value.investorInfoList
        )
      )
    )

  private val EnvironmentWhiteLabelValueType = EnumType(
    name = "environmentWhiteLabelValueType",
    values = DashboardWhitelabelValue.values.toList.map { value =>
      EnumValue(
        name = value.value,
        value = value
      )
    }
  )

  private val EnvironmentWhiteLabelValueMapType = MapSchema.entryType(
    name = "environmentWhiteLabelValueMapType",
    keyType = EnvironmentWhiteLabelValueType,
    valueType = StringType
  )

  private val HomePageEnvironmentWhitelabelDataType: ObjectType[GraphqlServerContext, DashboardWhitelabelData] =
    ObjectType(
      name = "HomePageEnvironmentWhitelabelDataType",
      fields = fields[GraphqlServerContext, DashboardWhitelabelData](
        Field(
          name = "isEnabled",
          fieldType = BooleanType,
          resolve = _.value.isEnabled
        ),
        MapSchema.field(
          name = "customValues",
          entryType = EnvironmentWhiteLabelValueMapType,
          resolve = _.value.customValues
        ),
        Field(
          name = "providerLongLogo",
          fieldType = OptionType(StringType),
          resolve = _.value.providerLongLogo
        )
      )
    )

  val FundSubEnvironmentNavigationDataType: ObjectType[GraphqlServerContext, FundSubEnvironmentNavigationData] =
    ObjectType(
      name = "FundSubEnvironmentNavigationData",
      fields = fields[GraphqlServerContext, FundSubEnvironmentNavigationData](
        Field(
          name = "environmentId",
          fieldType = EnvironmentIdSchema.EnvironmentIdType,
          resolve = _.value.environmentId
        ),
        Field(
          name = "environmentWhitelabelData",
          fieldType = HomePageEnvironmentWhitelabelDataType,
          resolve = _.value.environmentWhitelabelData
        ),
        Field(
          name = "baseUrlWithPrefixPath",
          fieldType = StringType,
          resolve = _.value.baseUrlWithPrefixPath
        ),
        Field(
          name = "environmentName",
          fieldType = StringType,
          resolve = _.value.environmentName
        )
      )
    )

}
