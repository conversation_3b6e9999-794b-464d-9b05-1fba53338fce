fragment FundSubFeatureSwitch on FundSubFeatureSwitchType {
  disableLpUndoSubmission
  disableLpSupportingDoc
  enabledCustomLpId
  formCommentSwitch
  disableSubmissionInstruction
  disableInvestFromAdditionalEntity
  disableDownloadSubscriptionDocument
  disabledMarkAsNotApplicable
  enabledAddAdditionalTaxForms
  hideFormFilesAndSigningButtons
  showManualOrdersToInvestors
  disableFundContactInInvestorWorkspace
  enableFormDiff
  enableLpManualSubmitSubscription
  enableLpReusePreviousSignedVersion
  disableInvestorTaxFormElectronicSignature
  exportInvestorDashboardTagsAsColumns
  blockLpSubmissionUntilFinishSupporting
  enableAutoPrefill
  enableImportData
  enableFundPermission
  enableAutoPrefillForLp
  enableEnforceLpSubmitReviewBeforeSignature
  enableLpProfile
  allowMultiStepInSubscriptionReview
  enableAmlKycCommenting
  preventLpUploadSupportingDocAfterCountersigned
  enableSupportingDocReview
  enableCommentMentioning
  enableCommentAssignment
  disableAbilityOfInvestorToInviteCollaborator
  enableAmlKycListAfterRequiredFieldsCompleted
  showOrderMetadataInDetailView
  enableImportFromFundData
  disableAbilityToCountersign
  disableLpResolvingComment
  disableSelfServiceExport
  enableOntologyFormMatching
  disableInAppAnnouncement
  hideAllInvitationButtonOnGpSide
  enableRia
  enableSideLetter
  disableRiaBanner
  forcingFundManagersToReceiveAllEmailNotifications
  enableDataExtractDraftFormLog
  allowFormEditPostSigning
  showSwitchAllowFormEditPostSigning
}
