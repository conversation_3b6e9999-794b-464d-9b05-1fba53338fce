// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataextract.project

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL}
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.suggest.laminar.MultiSuggestL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import zio.ZIO

import anduin.dataextract.client.DataExtractEndpointClient
import anduin.dataextract.models.DataExtractSharedSchema.DataExtractProjectBasicInfo
import anduin.dataextract.protocols.GetAllSaDataTemplatesParams
import anduin.frontend.AirStreamUtils
import anduin.id.ModelIdRegistry
import anduin.id.sa.SaDataTemplateId
import anduin.sa.model.sadatatemplate.sadatatemplatemessage.SaDataTemplateMessage

final case class SelectSaTemplatesModal(
  projectInfo: DataExtractProjectBasicInfo,
  title: String,
  confirmButtonText: String,
  cancelObserver: Observer[Unit],
  confirmObserver: Observer[Seq[SaDataTemplateId]]
) {
  private val allSaDataTemplatesVar = Var(Seq.empty[SaDataTemplateMessage])
  private val allSaDataTemplatesSignal = allSaDataTemplatesVar.signal
  private val isFetchingAllSaDataTemplatesVar = Var(true)
  private val isFetchingAllSaDataTemplatesSignal = isFetchingAllSaDataTemplatesVar.signal.distinct
  private val selectedTemplatesVar = Var(Seq.empty[SaDataTemplateId])
  private val selectedTemplatesSignal = selectedTemplatesVar.signal
  private val selectedTemplateSetSignal = selectedTemplatesSignal.map(_.toSet)
  private val allTemplateIdSetSignal = allSaDataTemplatesSignal.map(_.map(_.id).toSet)

  private val nonSelectedTemplatesSignal =
    allSaDataTemplatesSignal.combineWith(selectedTemplateSetSignal).map { case allTemplates -> selectedTemplateSet =>
      allTemplates.filterNot { template => selectedTemplateSet.contains(template.id) }.sortBy(_.name)
    }

  def apply(): HtmlElement = {
    div(
      ModalBodyL(
        div(
          tw.spaceY16,
          div(title),
          child <-- isFetchingAllSaDataTemplatesSignal.splitBoolean(
            whenTrue = _ => BlockIndicatorL()(),
            whenFalse = _ => {
              val changeEventBus = new EventBus[Seq[String]]
              MultiSuggestL[SaDataTemplateMessage](
                items = nonSelectedTemplatesSignal.map(_.map(MultiSuggestL.Item(_))),
                value = selectedTemplatesSignal.map(_.map(_.idString)),
                valueToString = _.id.idString,
                content = MultiSuggestL.Content(
                  renderItem = Some(_.defaultItemRender.amend(minWidth.px := 406)),
                  renderItemBody = Some(item => div(s"${item.value.id} - ${item.value.name}"))
                ),
                onChange = changeEventBus.writer
              )().amend(changeEventBus.events.withCurrentValueOf(allTemplateIdSetSignal).map {
                case selectedTemplateIdStrings -> allTemplateIdSet =>
                  selectedTemplateIdStrings.flatMap(
                    ModelIdRegistry.parser.parseAs[SaDataTemplateId](_).filter(allTemplateIdSet.contains)
                  )
              } --> selectedTemplatesVar.writer)
            }
          )
        )
      ),
      ModalFooterWCancelL(cancel = cancelObserver) {
        val clickEventBus = new EventBus[Unit]
        ButtonL(
          style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
          onClick = clickEventBus.writer.contramap { _ => () },
          isDisabled = selectedTemplatesSignal.map(_.isEmpty).distinct
        )(confirmButtonText).amend(clickEventBus.events.sample(selectedTemplatesSignal) --> confirmObserver)
      },
      fetchAllSaDataTemplates --> Observer.empty
    )
  }

  private def fetchAllSaDataTemplates = {
    AirStreamUtils.taskToStream {
      val task = for {
        _ <- ZIO.attempt(isFetchingAllSaDataTemplatesVar.set(true))
        respEither <- DataExtractEndpointClient.getAllSaDataTemplates(GetAllSaDataTemplatesParams(projectInfo.projectId))
        resp <- ZIO.fromEither(respEither)
        _ <- ZIO.attempt(Var.set(allSaDataTemplatesVar -> resp.templateList, isFetchingAllSaDataTemplatesVar -> false))
      } yield ()
      task.catchAll { error =>
        ZIO.attempt {
          Toast.error(s"Failed to fetch all SA data templates. Error: ${error.getMessage}")
          isFetchingAllSaDataTemplatesVar.set(false)
        }
      }
    }
  }

}
