// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataextract.project

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.dropdown.laminar.DropdownL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.switcher.laminar.SwitcherL
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import org.scalajs.dom
import zio.ZIO

import anduin.batchaction.BatchActionProvider
import anduin.batchaction.endpoint.{BatchActionInfo, BatchActionItemStatusInfo}
import anduin.cue.model.CueModuleSharedModels.CueModule
import anduin.dataextract.client.DataExtractEndpointClient
import anduin.dataextract.models.*
import anduin.dataextract.models.DataExtractSharedSchema.{
  DataExtractProjectBasicInfo,
  DataExtractProjectItem,
  DataExtractUserDocument,
  ProjectConfig,
  RunTextractMode
}
import anduin.dataextract.project.DataExtractProjectPageView.ProjectSettings
import anduin.dataextract.project.GetDebugReportInputModal.GetDebugReportInput
import anduin.dataextract.project.GetMappingResultInputModal.GetMappingResultInput
import anduin.dataextract.protocols.*
import anduin.dataextract.utils.DataExtractUtils
import anduin.file.FileDownloaderUtils
import anduin.forms.model.annotation.AnnotationDocumentModels.AnnotationDocumentVersionModel
import anduin.frontend.AirStreamUtils
import anduin.id.annotation.AnnotationDocumentVersionId
import anduin.id.dataextract.DataExtractProjectItemId
import anduin.id.form.{FormTemplateMappingVersionId, FormVersionId}
import anduin.id.sa.SaDataTemplateId
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.FileId
import com.anduin.stargazer.service.file.BatchDownloadRequest

private[dataextract] final case class DataExtractProjectPageView(
  projectInfo: DataExtractProjectBasicInfo
) {
  private val isGeneratingMappingResultVar = Var(false)
  private val isGeneratingDebugReportVar = Var(false)
  private val templatesVar = Var[Seq[TemplateModel]](Seq.empty)
  private val templatesSignal = templatesVar.signal
  private val templateVersionMapVar = Var[Map[AnnotationDocumentVersionId, AnnotationDocumentVersionModel]](Map.empty)
  private val templateVersionMapSignal = templateVersionMapVar.signal
  private val projectItemsVar = Var[Seq[DataExtractProjectItem]](Seq.empty)
  private val projectItemsSignal = projectItemsVar.signal
  private val getMappingResultEventBus = new EventBus[GetMappingResultInput]
  private val getDebugReportEventBus = new EventBus[GetDebugReportInput]
  private val configVar = Var(ProjectConfig())
  private val configSignal = configVar.signal
  private val thresholdsSignal = configSignal.map(_.thresholds)
  private val mappingContentAlgorithmVersionVar = Var(MappingContentAlgorithmVersion.V1)
  private val mappingContentAlgorithmVersionSignal = mappingContentAlgorithmVersionVar.signal.distinct
  private val saveSettingsEventBus = new EventBus[ProjectSettings]

  private val setFormImportInputConfigEventBus =
    new EventBus[(Option[FormVersionId], Option[FormTemplateMappingVersionId])]

  private val debugModeVar = Var(false)
  private val debugModeSignal = debugModeVar.signal.distinct
  private val userInfoMapVar = Var(Map.empty[UserId, UserInfo])
  private val userInfoMapSignal = userInfoMapVar.signal
  private val userDocumentMapVar = Var(Map.empty[DataExtractProjectItemId, Seq[DataExtractUserDocument]])
  private val userDocumentMapSignal = userDocumentMapVar.signal
  private val projectItemsWithUserDocumentsBeingFetchedVar = Var(Set.empty[DataExtractProjectItemId])
  private val projectItemsWithUserDocumentsBeingFetchedSignal = projectItemsWithUserDocumentsBeingFetchedVar.signal
  private val cueModuleOptVar = Var(Option.empty[CueModule])
  private val cueModuleOptSignal = cueModuleOptVar.signal
  private val isFetchingProjectDataVar = Var(true)
  private val isFetchingProjectDataSignal = isFetchingProjectDataVar.signal.distinct
  private val isCreatingCueModuleVar = Var(false)
  private val isCreatingCueModuleSignal = isCreatingCueModuleVar.signal.distinct
  private val generateCueModuleEventBus = new EventBus[Seq[FileId]]
  private val isGeneratingCueModuleVar = Var(false)
  private val isGeneratingCueModuleSignal = isGeneratingCueModuleVar.signal.distinct
  private val cueMappingModuleOptVar = Var(Option.empty[CueModule])
  private val cueMappingModuleOptSignal = cueMappingModuleOptVar.signal
  private val isCreatingCueMappingModuleVar = Var(false)
  private val isCreatingCueMappingModuleSignal = isCreatingCueMappingModuleVar.signal.distinct
  private val generateCueMappingModuleEventBus = new EventBus[Seq[SaDataTemplateId]]
  private val isGeneratingCueMappingModuleVar = Var(false)
  private val isGeneratingCueMappingModuleSignal = isGeneratingCueMappingModuleVar.signal.distinct

  private val fetchMappingResultObserver = Observer[BatchActionInfo] { batchActionInfo =>
    if (batchActionInfo.isCompleted) {
      isGeneratingMappingResultVar.set(false)
      batchActionInfo.postExecuteStatus match {
        case successfulBatch: BatchActionItemStatusInfo.Succeeded =>
          val reportFileIdOpt = for {
            respJson <- successfulBatch.data
            resp <- respJson.as[GenerateMappingResultPostExecuteResponse].toOption
          } yield resp.fileId
          reportFileIdOpt.fold(Toast.error("Failed to generate mapping result. Error: failed to decode response")) {
            fileId => FileDownloaderUtils.download(BatchDownloadRequest(fileIds = List(fileId)))
          }
        case _ =>
          val error =
            if (batchActionInfo.isPostExecuteFailed) "failed"
            else if (batchActionInfo.isPostExecuteCancelled) "was cancelled"
            else "entered an unknown fail state"
          Toast.error(s"Failed to generate mapping result. Error: Post Execute $error")
      }
    }
  }

  private val fetchDebugReportObserver = Observer[BatchActionInfo] { batchActionInfo =>
    if (batchActionInfo.isCompleted) {
      isGeneratingDebugReportVar.set(false)
      batchActionInfo.postExecuteStatus match {
        case successfulBatch: BatchActionItemStatusInfo.Succeeded =>
          val reportFileIdOpt = for {
            respJson <- successfulBatch.data
            resp <- respJson.as[GenerateDebugReportPostExecuteResponse].toOption
          } yield resp.fileId
          reportFileIdOpt.fold(Toast.error("Failed to generate debug report. Error: failed to decode response")) {
            fileId => FileDownloaderUtils.download(BatchDownloadRequest(fileIds = List(fileId)))
          }
        case _ =>
          val error =
            if (batchActionInfo.isPostExecuteFailed) "failed"
            else if (batchActionInfo.isPostExecuteCancelled) "was cancelled"
            else "entered an unknown fail state"
          Toast.error(s"Failed to generate debug report. Error: Post Execute $error")
      }
    }
  }

  private def renderHeader = {
    div(
      tw.flex.itemsCenter.justifyBetween,
      div(
        tw.flex.itemsCenter.spaceX8,
        span(IconL(name = Val(Icon.File.Folder), size = Icon.Size.Px32)()),
        span(
          tw.fontSemiBold.text15.leading24,
          projectInfo.projectName
        )
      ),
      div(
        tw.flex.itemsCenter.spaceX8,
        child.maybe <-- debugModeSignal.map(Option.when(_) {
          div(
            tw.flex.itemsCenter.spaceX8,
            child <-- cueModuleOptSignal.splitOption(
              (_, cueModuleSignal) => {
                val viewCueModuleEventBus = new EventBus[Unit]
                div(
                  tw.flex.itemsCenter.spaceX8,
                  ModalL(
                    renderTarget = open =>
                      ButtonL(
                        style =
                          ButtonL.Style.Full(icon = Some(Icon.Glyph.FileGenerate), isBusy = isGeneratingCueModuleSignal),
                        onClick = open.contramap { _ => () }
                      )("Generate Cue module"),
                    renderContent = close =>
                      SelectTemplatesModal(
                        title =
                          "Select templates to generate Cue module. Warning: this will overwrite the draft of the " +
                            "current Cue module!",
                        confirmButtonText = "Generate",
                        templatesSignal = templatesSignal,
                        templateVersionMapSignal = templateVersionMapSignal,
                        cancelObserver = close,
                        confirmObserver = Observer { selectedTemplateFileIds =>
                          generateCueModuleEventBus.emit(selectedTemplateFileIds.toSeq)
                          close.onNext(())
                        }
                      )()
                  )(),
                  ButtonL(
                    style = ButtonL.Style.Full(icon = Some(Icon.Glyph.OpenNewWindow)),
                    onClick = viewCueModuleEventBus.writer.contramap { _ => () }
                  )("View Cue module"),
                  viewCueModuleEventBus.events.sample(cueModuleSignal) --> Observer[CueModule] { cueModule =>
                    dom.window.open(s"/pantheon/cue/${cueModule.id.idString}", "_blank")
                    ()
                  }
                )
              }, {
                val clickEventBus = new EventBus[Unit]
                ButtonL(
                  style = ButtonL.Style.Full(isBusy = isCreatingCueModuleSignal),
                  onClick = clickEventBus.writer.contramap { _ => () }
                )("Create Cue module").amend(clickEventBus.events.flatMapSwitch { _ =>
                  createCueModule
                } --> Observer.empty)
              }
            ),
            child <-- cueMappingModuleOptSignal.splitOption(
              (_, cueMappingModuleSignal) => {
                val clickEventBus = new EventBus[Unit]
                div(
                  tw.flex.itemsCenter.spaceX8,
                  ModalL(
                    renderTarget = open =>
                      ButtonL(
                        style = ButtonL.Style.Full(
                          icon = Some(Icon.Glyph.FileGenerate),
                          isBusy = isGeneratingCueMappingModuleSignal
                        ),
                        onClick = open.contramap { _ => () }
                      )("Generate Cue mapping module"),
                    renderContent = close =>
                      SelectSaTemplatesModal(
                        projectInfo = projectInfo,
                        title =
                          "Select SA data templates to generate Cue mapping module. Warning: this will overwrite the " +
                            "draft of the current Cue mapping module!",
                        confirmButtonText = "Generate",
                        cancelObserver = close,
                        confirmObserver = Observer { selectedSaDataTemplateIds =>
                          generateCueMappingModuleEventBus.emit(selectedSaDataTemplateIds)
                          close.onNext(())
                        }
                      )()
                  )(),
                  ButtonL(
                    style = ButtonL.Style.Full(icon = Some(Icon.Glyph.OpenNewWindow)),
                    onClick = clickEventBus.writer.contramap { _ => () }
                  )("View Cue mapping module")
                    .amend(clickEventBus.events.sample(cueMappingModuleSignal) --> Observer[CueModule] { cueModule =>
                      dom.window.open(s"/pantheon/cue/${cueModule.id.idString}", "_blank")
                      ()
                    })
                )
              }, {
                val clickEventBus = new EventBus[Unit]
                ButtonL(
                  style = ButtonL.Style.Full(isBusy = isCreatingCueMappingModuleSignal),
                  onClick = clickEventBus.writer.contramap { _ => () }
                )("Create Cue mapping module").amend(clickEventBus.events.flatMapSwitch { _ =>
                  createCueMappingModule
                } --> Observer.empty)
              }
            )
          )
        }),
        PopoverL(
          position = PortalPosition.BottomRight,
          renderTarget = (open, isOpen) =>
            ButtonL(
              style = ButtonL.Style.Full(
                isSelected = isOpen,
                icon = Some(Icon.Glyph.Cog)
              ),
              onClick = open.contramap(_ => ())
            )(),
          renderContent = renderSettingsSection
        )()
      )
    )
  }

  private def renderSettingsSection(onClose: Observer[Unit]) = {
    val config = configVar.now()
    val mappingContentAlgorithmVersion = mappingContentAlgorithmVersionVar.now()
    val debugMode = debugModeVar.now()
    val newSettingsVar = Var(
      ProjectSettings(
        config.thresholds,
        mappingContentAlgorithmVersion,
        debugMode,
        config.runTextractMode
      )
    )
    val newSettingsSignal = newSettingsVar.signal
    val newThresholdsSignal = newSettingsSignal.map(_.thresholds)
    val newMappingContentAlgorithmVersionSignal = newSettingsSignal.map(_.mappingContentAlgorithmVersion).distinct
    val newDebugModeSignal = newSettingsSignal.map(_.debugMode).distinct
    val newRunTextractModeSignal = newSettingsSignal.map(_.runTextractMode).distinct
    val newThresholdsUpdater = newSettingsVar.updater[DataExtractThresholds => DataExtractThresholds] {
      case settings -> updateFunc =>
        settings.copy(thresholds = updateFunc(settings.thresholds))
    }

    div(
      tw.p4.spaceY12,
      div(
        tw.flex.itemsCenter.spaceX8,
        div(
          tw.wPx256.flex.itemsCenter.justifyBetween,
          div("Overlapping threshold"),
          TooltipL(
            renderTarget = IconL(Val(Icon.Glyph.Question))().amend(tw.textGray7),
            renderContent = _.amend("Overlapping between the Textract block and template field")
          )()
        ),
        TextBoxL(
          value = newThresholdsSignal.map(_.overlappingThreshold * 100).distinct.map(_.toString),
          onChange = newThresholdsUpdater.contracollectOpt { strValue =>
            DataExtractUtils.sanitizeNumberStr(strValue).toFloatOption.map { value =>
              _.copy(overlappingThreshold = value / 100)
            }
          },
          tpe = TextBoxL.Tpe.Percentage(decimalLimit = 2)
        )().amend(width := "80px")
      ),
      div(
        tw.flex.itemsCenter.spaceX8,
        div(
          tw.wPx256.flex.itemsCenter.justifyBetween,
          div("Confidence threshold"),
          TooltipL(
            renderTarget = IconL(Val(Icon.Glyph.Question))().amend(tw.textGray7),
            renderContent = _.amend("How confident Textract is about the value they detected")
          )()
        ),
        TextBoxL(
          value = newThresholdsSignal.map(_.confidenceThreshold).distinct.map(_.toString),
          onChange = newThresholdsUpdater.contracollectOpt { strValue =>
            DataExtractUtils.sanitizeNumberStr(strValue).toFloatOption.map { value =>
              _.copy(confidenceThreshold = value)
            }
          },
          tpe = TextBoxL.Tpe.Percentage(decimalLimit = 2)
        )().amend(width := "80px")
      ),
      div(
        tw.flex.itemsCenter.spaceX8,
        div(
          tw.wPx256.flex.itemsCenter.justifyBetween,
          div("Duplication overlapping threshold"),
          TooltipL(
            renderTarget = IconL(Val(Icon.Glyph.Question))().amend(tw.textGray7),
            renderContent = _.amend("Duplication between the Textract blocks value")
          )()
        ),
        TextBoxL(
          value = newThresholdsSignal.map(_.contentOverlapToRemoveThreshold * 100).distinct.map(_.toString),
          onChange = newThresholdsUpdater.contracollectOpt { strValue =>
            DataExtractUtils.sanitizeNumberStr(strValue).toFloatOption.map { value =>
              _.copy(contentOverlapToRemoveThreshold = value / 100)
            }
          },
          tpe = TextBoxL.Tpe.Percentage(decimalLimit = 2)
        )().amend(width := "80px")
      ),
      div(
        tw.flex.itemsCenter.spaceX8,
        div(
          tw.wPx256.flex.itemsCenter.justifyBetween,
          div("Mapping content algorithm"),
          TooltipL(
            renderTarget = IconL(Val(Icon.Glyph.Question))().amend(tw.textGray7),
            renderContent = _.amend("Algorithm version to compute overlapped content")
          )()
        ),
        DropdownL[MappingContentAlgorithmVersion](
          value = newMappingContentAlgorithmVersionSignal.map(Some(_)),
          valueToString = _.value,
          onChange = newSettingsVar.updater { case settings -> mappingContentAlgorithmVersion =>
            settings.copy(mappingContentAlgorithmVersion = mappingContentAlgorithmVersion)
          },
          items = MappingContentAlgorithmVersion.values.map(DropdownL.Item(_)).toSeq
        )()
      ),
      div(
        tw.flex.itemsCenter.spaceX8,
        div(
          tw.wPx256.flex.itemsCenter.justifyBetween,
          "Textract Eco (experimental)",
          TooltipL(
            renderTarget = IconL(Val(Icon.Glyph.Question))().amend(tw.textGray7),
            renderContent = _.amend(
              "Save textract cost by running document text detection, " +
                "only run document analysis on mapped pages with annotations"
            )
          )()
        ),
        SwitcherL(
          isChecked = newRunTextractModeSignal.map(_ == RunTextractMode.Eco),
          onChange = newSettingsVar.updater { case settings -> isEcoMode =>
            settings.copy(runTextractMode = if (isEcoMode) {
              RunTextractMode.Eco
            } else {
              RunTextractMode.Full
            })
          }
        )()
      ),
      div(
        tw.flex.itemsCenter.spaceX8,
        div(tw.wPx256, "Debug mode"),
        SwitcherL(
          isChecked = newDebugModeSignal,
          onChange = newSettingsVar.updater { case settings -> debugMode => settings.copy(debugMode = debugMode) }
        )()
      ),
      div(
        tw.flex.justifyEnd.spaceX8,
        ButtonL(
          style = ButtonL.Style.Full(
            height = ButtonL.Height.Fix24
          ),
          onClick = onClose.contramap(_ => ())
        )("Cancel"),
        ButtonL(
          style = ButtonL.Style.Full(
            color = ButtonL.Color.Primary,
            height = ButtonL.Height.Fix24
          ),
          onClick = Observer { _ =>
            saveSettingsEventBus.emit(newSettingsVar.now())
            onClose.onNext(())
          }
        )("Save")
      )
    )
  }

  def apply(): HtmlElement = {
    div(
      fetchProjectData --> Observer.empty,
      getMappingResultEventBus.events
        .withCurrentValueOf(
          thresholdsSignal,
          mappingContentAlgorithmVersionSignal
        )
        .flatMapSwitch(requestMappingResult)
        .collectSome
        .flatMapSwitch(BatchActionProvider.getBatchActionInfoOptSignal)
        .collectSome
        .distinctBy(info => (info.batchActionId, info.postExecuteStatus)) --> fetchMappingResultObserver,
      getDebugReportEventBus.events
        .withCurrentValueOf(
          thresholdsSignal,
          mappingContentAlgorithmVersionSignal
        )
        .flatMapSwitch(requestDebugReport)
        .collectSome
        .flatMapSwitch(BatchActionProvider.getBatchActionInfoOptSignal)
        .collectSome
        .distinctBy(info => (info.batchActionId, info.postExecuteStatus)) --> fetchDebugReportObserver,
      saveSettingsEventBus.events.withCurrentValueOf(configSignal).flatMapSwitch { case settings -> config =>
        Var.set(
          mappingContentAlgorithmVersionVar -> settings.mappingContentAlgorithmVersion,
          debugModeVar -> settings.debugMode
        )
        saveConfig(config.copy(thresholds = settings.thresholds, runTextractMode = settings.runTextractMode))
      } --> Observer.empty,
      setFormImportInputConfigEventBus.events.withCurrentValueOf(configSignal).flatMapSwitch {
        case (lastSelectedFormVersionIdOpt, lastSelectedTemplateMappingVersionIdOpt, config) =>
          saveConfig(
            config.copy(
              lastSelectedFormVersionIdOpt = lastSelectedFormVersionIdOpt,
              lastSelectedTemplateMappingVersionIdOpt = lastSelectedTemplateMappingVersionIdOpt
            )
          )
      } --> Observer.empty,
      projectItemsSignal
        .map(_.map(_.projectItemId).toSet)
        .distinct
        .changes
        .withCurrentValueOf(userDocumentMapSignal)
        .flatMapSwitch { case projectItemIds -> userDocumentMap =>
          val newProjectItemIds = projectItemIds -- userDocumentMap.keySet
          val filteredUserDocumentMap = userDocumentMap.view.filterKeys(projectItemIds.contains).toMap
          fetchUserDocuments(newProjectItemIds).map(_.groupBy(_.id.parent) ++ filteredUserDocumentMap)
        } --> userDocumentMapVar.writer,
      generateCueModuleEventBus.events.flatMapSwitch(generateCueModule) --> Observer.empty,
      generateCueMappingModuleEventBus.events.flatMapSwitch(generateCueMappingModule) --> Observer.empty,
      tw.py32.flex.justifyCenter,
      child <-- isFetchingProjectDataSignal.map(if (_) {
        BlockIndicatorL()()
      } else {
        div(
          tw.wPc100.spaceY24,
          maxWidth := "1200px",
          renderHeader,
          TemplateFileSection(
            projectId = projectInfo.projectId,
            templatesSignal,
            templateVersionMapSignal,
            templatesVar.updater[Seq[TemplateModel] => Seq[TemplateModel]] { (currentTemplates, updateFn) =>
              updateFn(currentTemplates)
            },
            templateVersionMapVar.updater[Map[AnnotationDocumentVersionId, AnnotationDocumentVersionModel]](_ ++ _)
          )(),
          DataExtractProjectItemsSection(
            projectInfo,
            projectItemsSignal,
            templatesSignal,
            templateVersionMapSignal,
            configSignal,
            mappingContentAlgorithmVersionSignal,
            userInfoMapSignal,
            isGeneratingMappingResultVar.signal,
            isGeneratingDebugReportVar.signal,
            debugModeSignal,
            userDocumentMapSignal,
            projectItemsWithUserDocumentsBeingFetchedSignal,
            cueModuleOptSignal,
            projectItemsVar.updater[Seq[DataExtractProjectItem] => Seq[DataExtractProjectItem]] {
              (currentProjectItems, updateFn) =>
                updateFn(currentProjectItems)
            },
            setFormImportInputConfigEventBus.writer,
            getMappingResultEventBus.writer,
            getDebugReportEventBus.writer,
            userDocumentMapVar.updater { case userDocumentMap -> updateFn => updateFn(userDocumentMap) }
          )()
        )
      })
    )
  }

  private def requestDebugReport(
    input: GetDebugReportInput,
    thresholds: DataExtractThresholds,
    mappingContentAlgorithmVersion: MappingContentAlgorithmVersion
  ) = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.attempt(isGeneratingDebugReportVar.set(true))
        resp <- DataExtractEndpointClient.generateDebugReport(
          GenerateDebugReportParams(
            projectId = projectInfo.projectId,
            projectName = projectInfo.projectName,
            projectItems = input.projectItemsIds,
            includedDataPoints = input.includedDataPoints,
            thresholds = thresholds,
            mappingContentAlgorithmVersion = mappingContentAlgorithmVersion
          )
        )
        batchActionIdOpt <- ZIO.attempt {
          resp.fold(
            error => {
              Toast.error(s"Failed to generate debug report. Error: ${error.getMessage}")
              isGeneratingDebugReportVar.set(false)
              None
            },
            resp => Some(resp.batchActionId)
          )
        }
      } yield batchActionIdOpt
    )
  }

  private def requestMappingResult(
    input: GetMappingResultInput,
    thresholds: DataExtractThresholds,
    mappingContentAlgorithmVersion: MappingContentAlgorithmVersion
  ) = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.attempt(isGeneratingMappingResultVar.set(true))
        resp <- DataExtractEndpointClient
          .getMappingResultFile(
            GetMappingResultWorkbookParams(
              projectId = projectInfo.projectId,
              projectName = projectInfo.projectName,
              dataExtractProjectItemIds = input.projectItemsIds.toSeq,
              overlappingThresholdOpt = Some(thresholds.overlappingThreshold),
              contentOverlapToRemoveThresholdOpt = Some(thresholds.contentOverlapToRemoveThreshold),
              confidenceThresholdOpt = Some(thresholds.confidenceThreshold),
              includedSheetTypes = input.includedSheetTypes,
              includedDataPoints = input.includedDataPoints,
              tags = input.tags,
              mappingContentAlgorithmVersion = mappingContentAlgorithmVersion
            )
          )
        batchActionIdOpt <- ZIO.attempt {
          resp.fold(
            error => {
              Toast.error(s"Failed to generate mapping result. Error: ${error.getMessage}")
              isGeneratingMappingResultVar.set(false)
              None
            },
            resp => Some(resp.batchActionId)
          )
        }
      } yield batchActionIdOpt
    )
  }

  private def fetchProjectData: EventStream[Unit] = {
    AirStreamUtils.taskToStream {
      val task = for {
        _ <- ZIO.attempt(isFetchingProjectDataVar.set(true))
        respEither <- DataExtractEndpointClient
          .getProjectData(GetProjectDataParams(projectId = projectInfo.projectId))
        resp <- ZIO.fromEither(respEither)
        _ <- ZIO.attempt(
          Var.set(
            projectItemsVar -> resp.items,
            templatesVar -> resp.templates,
            templateVersionMapVar -> resp.templateVersionMap,
            configVar -> resp.configOpt.getOrElse(ProjectConfig()),
            userInfoMapVar -> resp.userInfoMap,
            cueModuleOptVar -> resp.cueModuleOpt,
            cueMappingModuleOptVar -> resp.cueMappingModuleOpt,
            isFetchingProjectDataVar -> false
          )
        )
      } yield ()
      task.catchAll { error =>
        ZIO.attempt {
          Toast.error(s"Failed to fetch project data. Error: ${error.getMessage}")
          isFetchingProjectDataVar.set(false)
        }
      }
    }
  }

  private def saveConfig(config: ProjectConfig) = {
    AirStreamUtils.taskToStream {
      DataExtractEndpointClient
        .setProjectConfig(SetProjectConfigParams(projectInfo.projectId, config))
        .map(_.fold(_ => Toast.error("Failed to save config"), _ => configVar.set(config)))
    }
  }

  private def fetchUserDocuments(projectItemIds: Set[DataExtractProjectItemId]) = {
    AirStreamUtils.taskToStream {
      for {
        _ <- ZIO.attempt(projectItemsWithUserDocumentsBeingFetchedVar.update(_ ++ projectItemIds))
        userDocuments <-
          if (projectItemIds.nonEmpty) {
            DataExtractEndpointClient
              .getUserDocuments(GetUserDocumentsParams(projectItemIds.toSeq, includeNotes = true))
              .map(
                _.fold(
                  error => {
                    Toast.error(s"Failed to fetch user documents. Error: $error")
                    Seq.empty
                  },
                  resp => resp.userDocuments
                )
              )
          } else {
            ZIO.succeed(Seq.empty)
          }
        _ <- ZIO.attempt(projectItemsWithUserDocumentsBeingFetchedVar.update(_ -- projectItemIds))
      } yield userDocuments
    }
  }

  private def createCueModule = {
    AirStreamUtils.taskToStream {
      val task = for {
        _ <- ZIO.attempt(isCreatingCueModuleVar.set(true))
        respEither <- DataExtractEndpointClient.createCueModule(CreateCueModuleParams(projectInfo.projectId))
        resp <- ZIO.fromEither(respEither)
        _ <- ZIO.attempt {
          Toast.success("Cue module created successfully")
          Var.set(cueModuleOptVar -> Some(resp.cueModule), isCreatingCueModuleVar -> false)
        }
      } yield ()
      task.catchAll { error =>
        ZIO.attempt {
          Toast.error(s"Failed to create cue module. Error: ${error.getMessage}")
          isCreatingCueModuleVar.set(false)
        }
      }
    }
  }

  private def generateCueModule(selectedTemplateFileIds: Seq[FileId]) = {
    AirStreamUtils.taskToStream {
      val task = for {
        _ <- ZIO.attempt(isGeneratingCueModuleVar.set(true))
        respEither <- DataExtractEndpointClient.generateDataExtractCueModule(
          GenerateDataExtractCueModuleParams(
            projectId = projectInfo.projectId,
            selectedTemplateFileIds = selectedTemplateFileIds
          )
        )
        _ <- ZIO.fromEither(respEither)
        _ <- ZIO.attempt {
          Toast.success("Cue module generated successfully")
          isGeneratingCueModuleVar.set(false)
        }
      } yield ()
      task.catchAll { error =>
        ZIO.attempt {
          Toast.error(s"Failed to generate cue module. Error: ${error.getMessage}")
          isGeneratingCueModuleVar.set(false)
        }
      }
    }
  }

  private def createCueMappingModule = {
    AirStreamUtils.taskToStream {
      val task = for {
        _ <- ZIO.attempt(isCreatingCueMappingModuleVar.set(true))
        respEither <- DataExtractEndpointClient.createCueMappingModule(
          CreateCueMappingModuleParams(projectInfo.projectId)
        )
        resp <- ZIO.fromEither(respEither)
        _ <- ZIO.attempt {
          Toast.success("Cue mapping module created successfully")
          Var.set(cueMappingModuleOptVar -> Some(resp.cueModule), isCreatingCueMappingModuleVar -> false)
        }
      } yield ()
      task.catchAll { error =>
        ZIO.attempt {
          Toast.error(s"Failed to create cue mapping module. Error: ${error.getMessage}")
          isCreatingCueMappingModuleVar.set(false)
        }
      }
    }
  }

  private def generateCueMappingModule(targetSaDataTemplateIds: Seq[SaDataTemplateId]) = {
    AirStreamUtils.taskToStream {
      val task = for {
        _ <- ZIO.attempt(isGeneratingCueMappingModuleVar.set(true))
        respEither <- DataExtractEndpointClient.generateDataExtractCueMappingModule(
          GenerateDataExtractCueMappingModuleParams(
            projectId = projectInfo.projectId,
            targetSaDataTemplateIds = targetSaDataTemplateIds
          )
        )
        _ <- ZIO.fromEither(respEither)
        _ <- ZIO.attempt {
          Toast.success("Cue mapping module generated successfully")
          isGeneratingCueMappingModuleVar.set(false)
        }
      } yield ()
      task.catchAll { error =>
        ZIO.attempt {
          Toast.error(s"Failed to generate cue mapping module. Error: ${error.getMessage}")
          isGeneratingCueMappingModuleVar.set(false)
        }
      }
    }
  }

}

private object DataExtractProjectPageView {

  final case class ProjectSettings(
    thresholds: DataExtractThresholds,
    mappingContentAlgorithmVersion: MappingContentAlgorithmVersion,
    debugMode: Boolean,
    runTextractMode: RunTextractMode
  )

}
