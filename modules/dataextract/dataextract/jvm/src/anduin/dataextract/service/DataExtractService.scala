// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataextract.service

import java.time.Instant

import com.apple.foundationdb.record.TupleRange
import io.circe.Json
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.annotation.service.{AnnotationDocumentService, AnnotationDocumentStorageService}
import anduin.batchaction.*
import anduin.cue.model.commands.CueCommands.DataExtractCommands
import anduin.cue.model.dataextract.DataExtractCommonSchema.OcrFields
import anduin.cue.model.table.TableCommonSchema.SoftValidationValueSchema
import anduin.cue.model.table.TableSharedModels.SoftValidationValuesWithValidation
import anduin.cue.service.CueModuleService
import anduin.cue.service.CueModuleService.CueModuleServiceKeySpace
import anduin.cue.utils.CueGeneratorUtils
import anduin.cue.utils.CueModuleZotUtils.CueModuleZotKeySpace
import anduin.dataextract.database.{
  DataExtractProjectItemStoreOperations,
  DataExtractProjectModelOperations,
  DataExtractProjectModelStoreProvider,
  DataExtractUserDocumentStoreOperations
}
import anduin.dataextract.models.*
import anduin.dataextract.models.DataExtractSharedSchema.*
import anduin.dataextract.protocols.*
import anduin.dataextract.utils.DataExtractMappingUtils
import anduin.digitization.common.DigitizationServiceKeyspace
import anduin.digitization.service.DigitizationFileService
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.documentservice.textract.*
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{DefaultCluster, FDBOperations, FDBRecordDatabase}
import anduin.forms.annotation.*
import anduin.forms.engine.GaiaState
import anduin.forms.service.FormService
import anduin.forms.util.TextUtils
import anduin.id.annotation.AnnotationDocumentVersionId
import anduin.id.cue.CueModuleVersionId
import anduin.id.dataextract.{DataExtractProjectId, DataExtractProjectItemId, DataExtractUserDocumentId}
import anduin.id.digitization.DigitizationFolderId
import anduin.id.form.{FormTemplateMappingVersionId, FormVersionId}
import anduin.id.sa.SaDataTemplateId
import anduin.model.common.user.UserId
import anduin.model.id.*
import anduin.model.notichannel.DataExtractNotificationChannels
import anduin.portaluser.PortalUserService
import anduin.protobuf.dataextract.projectitem.{
  DataExtractProjectItemMessage,
  DataExtractProjectItemStatusMessage,
  ProjectItemUserDocumentsInfoMessage
}
import anduin.protobuf.dataextract.userdocument.{DataExtractUserDocumentMessage, DataExtractUserDocumentStatusMessage}
import anduin.protobuf.dataextract.{
  DataExtractProjectMessage,
  ProjectItemsInfoMessage,
  TemplateDataCacheMessage,
  TemplateMessage
}
import anduin.protobuf.flow.file.{FileFolderPermission, FileFolderPermissionMap}
import anduin.sa.service.SaDataTemplateService
import anduin.serverless.common.ServerlessModels.LiteLlmAccess
import anduin.serverless.common.ServerlessModels.annotations.drawAnnotationBoxes.{
  DrawAnnotationBoxesRequest,
  DrawAnnotationBoxesResponse
}
import anduin.serverless.common.ServerlessModels.annotations.{PdfFieldInfo, PdfFormSchema}
import anduin.serverless.common.ServerlessModels.formDataExtraction.extractData.FormDataExtractionRequest
import anduin.serverless.common.ServerlessModels.formDataExtraction.generateSchema.{
  GenerateSchemaRequest,
  GenerateSchemaResponse
}
import anduin.serverless.common.ServerlessModels.formModule.CueGeneratorInput.GenerateDataExtractModule.TemplateInfo
import anduin.serverless.common.ServerlessModels.formModule.CueGeneratorInput.{
  GenerateDataExtractModule,
  GenerateTableMappingModule
}
import anduin.serverless.common.ServerlessModels.muPdf.PdfToImagesRequest
import anduin.serverless.functions.{
  DrawAnnotationBoxesServerless,
  FormDataExtractionServerless,
  GenerateSchemaServerless,
  MuPDFServerless
}
import anduin.serverless.utils.ServerlessUtils
import anduin.service.GeneralServiceException
import anduin.team.TeamService
import anduin.team.TeamServiceParams.{AddMemberParams, CreateNewTeamParams}
import anduin.textract.{TextractObjectUtils, TextractSdkConverter}
import anduin.utils.ScalaUtils
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.actionlogger.datadump.S3Utils
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils

final case class DataExtractService(
  protected val backendConfig: GondorBackendConfig,
  textractService: TextractService,
  annotationDocumentService: AnnotationDocumentService,
  fileService: FileService,
  formService: FormService,
  teamService: TeamService,
  permissionService: DataExtractPermissionService,
  batchActionService: BatchActionService,
  dataExtractMappingService: DataExtractMappingService,
  dataExtractStorageService: DataExtractStorageService,
  dataExtractProjectItemService: DataExtractProjectItemService,
  dataExtractFormMappingService: DataExtractFormMappingService,
  userProfileService: UserProfileService,
  portalUserService: PortalUserService,
  natsNotificationService: NatsNotificationService,
  mergeTextractService: MergeTextractService,
  cueModuleService: CueModuleService,
  digitizationFileService: DigitizationFileService,
  annotationDocumentStorageService: AnnotationDocumentStorageService,
  dataExtractCueService: DataExtractCueService,
  drawAnnotationBoxesServerless: DrawAnnotationBoxesServerless,
  generateSchemaServerless: GenerateSchemaServerless,
  formDataExtractionServerless: FormDataExtractionServerless,
  muPDFServerless: MuPDFServerless,
  saDataTemplateService: SaDataTemplateService
) {
  private val s3Access = ServerlessUtils.getS3Access()
  private val liteLlmConfig = ServerlessUtils.getLiteLlmConfig

  def createProject(
    actor: UserId,
    params: CreateDataExtractProjectParams
  ): Task[CreateDataExtractProjectResp] = {
    val projectId = params.predefinedProjectIdOpt
      .getOrElse(DataExtractProjectIdFactory.unsafeRandomId)
    val teamId = TeamIdFactory.unsafeRandomId(projectId)
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is creating new project ${params.projectName}")
      _ <- ZIOUtils.traverseOption(params.predefinedProjectIdOpt) { predefinedProjectId =>
        ZIOUtils.validate(
          FDBRecordDatabase.transact(DataExtractProjectModelOperations.Production) { storeOps =>
            storeOps.getOpt(predefinedProjectId).map(_.isEmpty)
          }
        )(
          new GeneralServiceException(s"Project ID ${predefinedProjectId.idString} is taken!")
        )
      }
      _ <- permissionService.validatePortalPermission(actor)
      _ <- teamService.createNewTeam(
        CreateNewTeamParams(
          projectId,
          None,
          teamIdOpt = Some(teamId)
        )
      )
      _ <- teamService.addMember(AddMemberParams(actor, actor, teamId, None, skipPermission = true))
      rootFolderId <- fileService.createSystemFolderForChannel(
        channel = projectId,
        folderName = params.projectName,
        creator = actor,
        permissionOpts = FileFolderPermissionMap(teamPermissions = Map(teamId -> FileFolderPermission.Own))
      )
      project <- FDBRecordDatabase.transact(DataExtractProjectModelOperations.Production) { storeOps =>
        storeOps.create(
          DataExtractProjectMessage(
            id = projectId,
            projectName = params.projectName,
            teamId = teamId,
            creator = actor,
            rootFolderId = rootFolderId,
            createdAt = Option(Instant.now()),
            projectItemsInfo = Some(ProjectItemsInfoMessage())
          )
        )
      }

    } yield CreateDataExtractProjectResp(DataExtractProtoConverter.projectProtoToProjectBasicInfo(project))
  }

  def addNewMemberToProject(
    actor: UserId,
    projectId: DataExtractProjectId,
    newMembers: Set[UserId]
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is adding ${newMembers.size} members to project ${projectId.idString}")
      _ <- permissionService.validateSuperAdminOrProjectOwnerPermission(projectId, actor)
      _ <- permissionService.addUserToProject(
        actor = actor,
        projectId = projectId,
        membersToAdd = newMembers
      )
    } yield ()
  }

  def removeMemberFromProject(
    actor: UserId,
    projectId: DataExtractProjectId,
    membersToRemove: Set[UserId]
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is removing ${membersToRemove.size} members from project ${projectId.idString}"
      )
      _ <- permissionService.validateSuperAdminOrProjectOwnerPermission(projectId, actor)
      _ <- permissionService.removeUserFromProject(
        actor = actor,
        projectId = projectId,
        membersToRemove = membersToRemove
      )
    } yield ()
  }

  def getProjectMembers(projectId: DataExtractProjectId, actor: UserId): Task[GetProjectMembersResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is getting members of project ${projectId.idString}")
      _ <- permissionService.validatePortalPermission(actor)
      memberIds <- permissionService.getProjectUsers(projectId)
      members <- userProfileService.batchGetUserInfos(memberIds, Some(actor))
    } yield GetProjectMembersResponse(members.toSeq)
  }

  private[service] def getProjectTemplates(
    projectId: DataExtractProjectId
  ) = {
    for {
      projectModelOpt <- FDBRecordDatabase.transact(FDBOperations[DataExtractProjectModelOperations].Production) {
        _.getOpt(projectId)
      }
      templateMsgs <- ZIOUtils.optionToTask(
        projectModelOpt.map(_.templates),
        GeneralServiceException(s"No project model for ID ${projectId.idString}")
      )
    } yield templateMsgs.map(DataExtractProtoConverter.templateProtoToModel)
  }

  def addTemplateToProject(
    params: AddTemplateToProjectParams,
    actor: UserId
  )(
    using AnnotationDocumentService.KeySpace
  ): Task[AddTemplateToProjectResponse] = {
    val projectId = params.projectId
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is adding ${params.versions.size} templates to project ${params.projectId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      project <- FDBRecordDatabase.transact(FDBOperations[DataExtractProjectModelOperations].Production)(_.get(projectId))
      existingVersions = project.templates.flatMap(_.annotationVersionIdOpt).toSet
      newVersions = params.versions.filterNot(existingVersions.contains)
      newTemplateMsgs <- ZIO
        .foreachPar(newVersions) { data =>
          extractTemplateData(projectId, project.rootFolderId, data, actor)
        }
        .withParallelism(4)
      finalTemplates <- FDBRecordDatabase.transact(FDBOperations[DataExtractProjectModelOperations].Production) {
        storeOps =>
          for {
            updatedProject <- storeOps.update(projectId)(
              _.copy(templates = project.templates ++ newTemplateMsgs)
            )
          } yield updatedProject.templates.map(DataExtractProtoConverter.templateProtoToModel)
      }
      templateDocumentVersionMap <- getTemplateDocumentVersionMap(newTemplateMsgs.flatMap(_.annotationVersionIdOpt))
    } yield AddTemplateToProjectResponse(finalTemplates, templateDocumentVersionMap)
  }

  private def getTemplateDocumentVersionMap(
    annotationVersionIds: Seq[AnnotationDocumentVersionId]
  )(
    using AnnotationDocumentService.KeySpace
  ) = {
    ZIOUtils
      .foreachParN(4)(annotationVersionIds) { annotationVersionId =>
        for {
          version <- getAnnotationDocumentVersion(annotationVersionId)
        } yield annotationVersionId -> version
      }
      .map(_.toMap)
  }

  private def getAnnotationDocumentVersion(
    versionId: AnnotationDocumentVersionId
  )(
    using AnnotationDocumentService.KeySpace
  ) = {
    for {
      (_, version) <- annotationDocumentService.getAnnotationDocumentModelAndVersion(
        versionId.parent,
        Some(versionId)
      )
    } yield version
  }

  private def extractTemplateData(
    projectId: DataExtractProjectId,
    projectFolderId: FolderId,
    annotationVersionId: AnnotationDocumentVersionId,
    actor: UserId
  )(
    using AnnotationDocumentService.KeySpace
  ) = {
    for {
      (pdfObjects, fileId, fileName, pageCount) <- annotationDocumentService
        .getAnnotationData(
          annotationVersionId.parent,
          Some(annotationVersionId),
          actor
        )
        .zipPar {
          annotationDocumentService
            .getAnnotationDocumentModel(
              annotationVersionId.parent,
              Some(annotationVersionId),
              actor
            )
        }
        .map { case (data, (model, version)) =>
          (
            data.pdfObjects,
            version.baseDocumentFileId,
            model.name,
            data.totalPages
          )
        }
      templatePdfFields = pdfObjects
        .flatMap {
          case terminalField: PdfTerminalField => Some(terminalField)
          case _                               => None
        }
        .flatMap { field =>
          val groupNameOpt = pdfObjects
            .getParent(field.id)
            .flatMap {
              case multipleCheckboxes: PdfMultipleCheckboxesField => Some(multipleCheckboxes.name)
              case radioGroup: PdfRadioGroupField                 => Some(radioGroup.name)
              case _                                              => None
            }
          val fieldType = field match {
            case _: PdfTextField     => TemplatePdfFieldType.Text
            case _: PdfRadioField    => TemplatePdfFieldType.Radio
            case _: PdfCheckboxField => TemplatePdfFieldType.Checkbox
            case _                   => TemplatePdfFieldType.Other
          }
          pdfObjects.getPageIndexArea(field.id).map { (pageIndex, area) =>
            TemplatePdfField(
              field.id.value,
              field.name,
              area,
              pageIndex,
              groupNameOpt,
              fieldType
            )
          }
        }
        .toSeq
      firstGroupFieldAreaByName = templatePdfFields
        .filter(_.groupNameOpt.isDefined)
        .groupBy(_.groupNameOpt.getOrElse(""))
        .flatMap { case groupName -> fields =>
          fields.minByOption(_.area).map(groupName -> _.area)
        }
      templateData = TemplateData(
        templatePdfFields.sortBy { field =>
          val area = field.groupNameOpt.flatMap(firstGroupFieldAreaByName.get).getOrElse(field.area)
          (field.pageIndex, area, field.area)
        }
      )
      copiedFileId <- fileService.copyFileInSameFeature(
        actor,
        fileId,
        projectFolderId,
        None,
        None
      )
      pagesWithAnnotations = templatePdfFields.map(_.pageIndex).toSet
      _ <- dataExtractStorageService.uploadTemplateData(projectId, copiedFileId, templateData, actor)
    } yield TemplateMessage(
      id = copiedFileId,
      name = fileName,
      createdAt = Some(Instant.now),
      annotationVersionIdOpt = Some(annotationVersionId),
      templateDataCache = Some(
        TemplateDataCacheMessage(
          fieldCount = templatePdfFields.size,
          pagesWithAnnotations = pagesWithAnnotations,
          pageCount = pageCount
        )
      )
    )
  }

  def updateTemplatesArchivedStatus(
    params: UpdateTemplatesArchivedStatusParams,
    actor: UserId
  ): Task[UpdateTemplatesArchivedStatusResponse] = {
    val projectId = params.projectId
    val templates = params.templates
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is updating archived status of ${templates.size} templates of project " +
          s"${projectId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      project <- FDBRecordDatabase.transact(FDBOperations[DataExtractProjectModelOperations].Production) { storeOps =>
        storeOps.update(projectId) { project =>
          project.copy(templates = project.templates.map { template =>
            if (templates.contains(template.id)) {
              template.copy(isArchived = params.newArchivedStatus, lastUpdatedAt = Some(Instant.now))
            } else {
              template
            }
          })
        }
      }
    } yield UpdateTemplatesArchivedStatusResponse(
      project.templates.map(DataExtractProtoConverter.templateProtoToModel)
    )
  }

  def createProjectItems(
    params: CreateProjectItemsParams,
    actor: UserId
  ): Task[Seq[DataExtractProjectItem]] = {
    val projectId = params.projectId
    val projectItemInfos = params.projectItemInfos
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is creating ${params.projectItemInfos.size} project items for project " +
          s"${params.projectId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      project <- FDBRecordDatabase.transact(DataExtractProjectModelOperations.Production)(_.get(projectId))
      validProjectItemInfos = projectItemInfos.flatMap { projectItemInfo =>
        val validUserDocumentFileIds = projectItemInfo.userDocumentFileIds.filter(_.folder == project.rootFolderId)
        Option.when(validUserDocumentFileIds.nonEmpty)(
          projectItemInfo.copy(userDocumentFileIds = validUserDocumentFileIds)
        )
      }
      userDocFileNameMap <- fileService
        .getMultipleFileNameAndDeletedStateUnsafe(
          validProjectItemInfos.flatMap(_.userDocumentFileIds.headOption).toList
        )
        .map(_.view.mapValues(_._1).toMap)
      newProjectItems <- ZIOUtils.foreachParN(4)(validProjectItemInfos) { projectItemInfo =>
        val name = projectItemInfo.nameOpt
          .orElse(projectItemInfo.userDocumentFileIds.headOption.flatMap(userDocFileNameMap.get))
          .getOrElse("User document")
        val newProjectItemId = DataExtractProjectItemIdFactory.unsafeRandomId(projectId)
        for {
          _ <- ZIOUtils.foreachParN(4)(projectItemInfo.userDocumentFileIds) {
            dataExtractProjectItemService.createUserDocumentUnsafe(newProjectItemId, _, actor)
          }
          newProjectItem <- FDBRecordDatabase.transact(DataExtractProjectItemStoreOperations.Production)(
            _.create(
              DataExtractProjectItemMessage(
                projectItemId = newProjectItemId,
                createdAt = Some(Instant.now),
                creator = actor,
                status = DataExtractProjectItemStatusMessage.Todo,
                name = name,
                userDocumentsInfo =
                  Some(ProjectItemUserDocumentsInfoMessage(userDocumentCount = projectItemInfo.userDocumentFileIds.size))
              )
            )
          )
        } yield newProjectItem
      }
      _ <- dataExtractProjectItemService.recalculateProjectItemsInfo(projectId)
    } yield newProjectItems.map(dataExtractProjectItemService.buildProjectItem)
  }

  private[service] def getAllItemsInProject(
    projectId: DataExtractProjectId,
    includeDeleted: Boolean = false
  ) = {
    FDBRecordDatabase
      .transact(DataExtractProjectItemStoreOperations.Production)(_.getAllItemsInProject(projectId))
      .map(
        _.filter(itemModel => includeDeleted || !itemModel.isDeleted).map(dataExtractProjectItemService.buildProjectItem)
      )
  }

  def getUserDocuments(projectItemIds: Seq[DataExtractProjectItemId], includeNotes: Boolean, actor: UserId)
    : Task[GetUserDocumentsResponse] = {
    for {
      projectId <- ZIOUtils.uniqueSeqToTask(
        projectItemIds.map(_.parent),
        GeneralServiceException("All project items should be in the same project")
      )
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is getting user documents of ${projectItemIds.size} project items of project " +
          s"${projectId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      projectTemplates <- getProjectTemplates(projectId)
      userDocuments <- dataExtractProjectItemService.getProjectItemUserDocuments(
        projectItemIds,
        includeNotes,
        includeFileNames = true,
        includeRejected = true
      )
      projectTemplateIds = projectTemplates.map(_.fileId).toSet
      correctedUserDocuments = userDocuments.map { userDocument =>
        userDocument.copy(applicableTemplates = userDocument.applicableTemplates.intersect(projectTemplateIds))
      }
    } yield GetUserDocumentsResponse(correctedUserDocuments)
  }

  def getProjectData(
    actor: UserId,
    params: GetProjectDataParams
  )(
    using CueModuleServiceKeySpace,
    AnnotationDocumentService.KeySpace
  ): Task[GetProjectDataResp] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is getting data for project ${params.projectId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(params.projectId, actor)
      items <- getAllItemsInProject(params.projectId, params.includeDeletedProjectItems)
      templates <- getProjectTemplates(params.projectId)
      templateDocumentVersionMap <- getTemplateDocumentVersionMap(templates.flatMap(_.annotationVersionIdOpt))
      configOpt <- FDBRecordDatabase.transact(FDBOperations[DataExtractProjectModelOperations].Production) { storeOps =>
        for {
          projectModelOpt <- storeOps.getOpt(params.projectId)
          configMessageOpt = projectModelOpt.flatMap(_.projectConfigOpt)
          configOpt = configMessageOpt.map(DataExtractProtoConverter.projectConfigProtoToModel)
        } yield configOpt
      }
      userInfoMap <- userProfileService.batchGetUserInfos((actor +: items.map(_.creator)).toSet, Some(actor))
      cueModuleOpt <- cueModuleService.getCueModuleOptUnsafe(params.projectId.cueModuleId)
      cueMappingModuleOpt <- cueModuleService.getCueModuleOptUnsafe(params.projectId.cueMappingModuleId)
    } yield GetProjectDataResp(
      items = items,
      templates = templates,
      templateVersionMap = templateDocumentVersionMap,
      configOpt = configOpt,
      userInfoMap = userInfoMap,
      cueModuleOpt = cueModuleOpt,
      cueMappingModuleOpt = cueMappingModuleOpt
    )
  }

  def removeProjectItems(
    params: RemoveDataExtractProjectItems,
    actor: UserId
  ): Task[Unit] = {
    for {
      projectId <- ZIOUtils.uniqueSeqToTask(
        params.projectItemIds.map(_.parent),
        GeneralServiceException("All project items should be in the same project")
      )
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is removing ${params.projectItemIds.size} project items of project " +
          s"${projectId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      _ <- FDBRecordDatabase.transact(DataExtractProjectItemStoreOperations.Production) { storeOps =>
        RecordIO.parTraverseN(4)(params.projectItemIds) { id =>
          storeOps.update(id)(_.copy(isDeleted = true))
        }
      }
      _ <- dataExtractProjectItemService.recalculateProjectItemsInfo(projectId)
    } yield ()
  }

  private def textractStatusToDataExtractTextractStatus(status: TextractStatus) = {
    status match {
      case _: TextractStatus.Succeeded                                => DataExtractTextractStatus.Succeeded
      case _: (TextractStatus.Failed | TextractStatus.PartialSuccess) => DataExtractTextractStatus.Failed
      case _                                                          => DataExtractTextractStatus.InProgress
    }
  }

  private def runDocumentAnalysisForFile(
    fileId: FileId,
    actor: UserId
  )(
    using TextractServiceEvidence
  ) = {
    for {
      status <- textractService
        .getTextractStatus(
          fileId = fileId,
          processType = TextractProcessType.DocumentAnalysis,
          processData = TextractService.defaultProcessData,
          actor = actor
        )
      _ <- ZIO.when(status match {
        case TextractStatus.NotStarted | TextractStatus.Created |
            _: (TextractStatus.Failed | TextractStatus.PartialSuccess) =>
          true
        case _ => false
      })(
        textractService.startTextract(
          fileId = fileId,
          processType = TextractProcessType.DocumentAnalysis,
          processData = TextractService.defaultProcessData,
          actor = actor
        )
      )
    } yield textractStatusToDataExtractTextractStatus(status)
  }

  private def runEcoTextractForUserDocument(
    userDocument: DataExtractUserDocumentMessage,
    actor: UserId
  )(
    using TextractServiceEvidence
  ) = {
    for {
      documentAnalysisStatus <- textractService.getTextractStatus(
        fileId = userDocument.fileId,
        processType = TextractProcessType.DocumentAnalysis,
        processData = TextractService.defaultProcessData,
        actor = actor
      )
      status <- documentAnalysisStatus match {
        // only run merge textract if file does not already have document analysis
        case TextractStatus.NotStarted | TextractStatus.Created =>
          val mergeTextractProjectId = userDocument.mergeTextractProjectId
          for {
            mergeTextractProjectData <- mergeTextractService.getMergeTextractProjectData(
              projectId = mergeTextractProjectId,
              actor = actor
            )
            statuses = mergeTextractProjectData.baseTextractStatusOpt ++: mergeTextractProjectData.itemStatuses
            _ <- ZIO.when(statuses.exists {
              case TextractStatus.NotStarted | TextractStatus.Created => true
              case _                                                  => false
            })(mergeTextractService.startTextract(projectId = mergeTextractProjectId, actor = actor))
          } yield DataExtractMappingUtils.getCumulatedTextractStatus(
            statuses.map(textractStatusToDataExtractTextractStatus)
          )
        case _ => ZIO.succeed(textractStatusToDataExtractTextractStatus(documentAnalysisStatus))
      }
    } yield status
  }

  private[service] def runTextractForUserDocumentsUnsafe(
    projectId: DataExtractProjectId,
    userDocumentIds: Seq[DataExtractUserDocumentId],
    runTextractMode: RunTextractMode,
    actor: UserId
  )(
    using TextractServiceEvidence
  ) = {
    for {
      templates <- getProjectTemplates(projectId)
      // run textract for templates first
      templateStatuses <- ZIOUtils.foreachParN(4)(templates) { template =>
        runDocumentAnalysisForFile(template.fileId, actor)
      }
      cumulatedTemplateStatus = DataExtractMappingUtils.getCumulatedTextractStatus(templateStatuses)
      statusMap <-
        if (cumulatedTemplateStatus == DataExtractTextractStatus.Succeeded) {
          ZIOUtils
            .foreachParN(4)(userDocumentIds) { userDocumentId =>
              for {
                userDocument <- FDBRecordDatabase.transact(DataExtractUserDocumentStoreOperations.Production)(
                  _.get(userDocumentId)
                )
                status <- runTextractMode match {
                  case RunTextractMode.Full => runDocumentAnalysisForFile(userDocument.fileId, actor)
                  case RunTextractMode.Eco  => runEcoTextractForUserDocument(userDocument, actor)
                }
              } yield userDocument.id -> status
            }
            .map(_.toMap)
        } else {
          ZIO.succeed(userDocumentIds.map(_ -> cumulatedTemplateStatus).toMap)
        }
    } yield statusMap
  }

  def runTextractForUserDocuments(
    firstTextractUserDocumentIds: Seq[DataExtractUserDocumentId],
    secondTextractUserDocumentIds: Seq[DataExtractUserDocumentId],
    runTextractMode: RunTextractMode,
    actor: UserId
  )(
    using TextractServiceEvidence
  ): Task[RunTextractForUserDocumentsResponse] = {
    val userDocumentIds = firstTextractUserDocumentIds ++ secondTextractUserDocumentIds
    for {
      projectId <- ZIOUtils.uniqueSeqToTask(
        userDocumentIds.map(_.parent.parent),
        GeneralServiceException("All user documents should be in the same project")
      )
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is running textract for ${userDocumentIds.size} user documents of project " +
          s"${projectId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      statusMap <- runTextractForUserDocumentsUnsafe(projectId, userDocumentIds, runTextractMode, actor)
      _ <- ZIO.when(secondTextractUserDocumentIds.nonEmpty) {
        val succeededUserDocumentIds = secondTextractUserDocumentIds.filter { userDocumentId =>
          statusMap.get(userDocumentId).contains(DataExtractTextractStatus.Succeeded)
        }
        for {
          _ <- ZIOUtils.foreachParN(4)(succeededUserDocumentIds) { userDocumentId =>
            DataExtractCacheUtils.updateUserFileCache(
              userDocumentId,
              cache => cache.copy(pageMatchingCache = cache.pageMatchingCache.map(_.copy(doneSecondTextract = true)))
            )
          }
          _ <- dataExtractProjectItemService.recalculateUserDocumentStatusesAndPublish(
            projectId,
            succeededUserDocumentIds,
            ignoreRejectedUserDocuments = true
          )
        } yield ()
      }
    } yield RunTextractForUserDocumentsResponse(statusMap)
  }

  def getUserDocumentPageContent(
    userDocumentId: DataExtractUserDocumentId,
    userDocumentPage: Int,
    actor: UserId
  )(
    using TextractServiceEvidence
  ): Task[GetUserDocumentPageContentResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is getting user document page content for user document ${userDocumentId.idString}, " +
          s"page = $userDocumentPage"
      )
      _ <- permissionService.validateProjectOwnerPermission(userDocumentId.parent.parent, actor)
      userDocument <- FDBRecordDatabase.transact(DataExtractUserDocumentStoreOperations.Production)(_.get(userDocumentId))
      blocks <- getTextractBlocks(userDocument, userDocumentPage, actor)
      objectMap = TextractObjectUtils.toObjectMap(blocks)
      extractedContent = DataExtractMappingUtils.getMappingExtractedContent(objectMap)
    } yield GetUserDocumentPageContentResponse(extractedContent)
  }

  private def getTextractBlocks(
    userDocument: DataExtractUserDocumentMessage,
    page: Int,
    actor: UserId
  )(
    using TextractServiceEvidence
  ) = {
    for {
      documentAnalysisStatus <- textractService.getTextractStatus(
        fileId = userDocument.fileId,
        processType = TextractProcessType.DocumentAnalysis,
        processData = TextractService.defaultProcessData,
        actor = actor
      )
      sdkBlocks <-
        if (ScalaUtils.isMatch[TextractStatus.Succeeded](documentAnalysisStatus)) {
          textractService.getTextractResult(
            fileId = userDocument.fileId,
            processType = TextractProcessType.DocumentAnalysis,
            processData = TextractService.defaultProcessData,
            pageRange = (page + 1, page + 1),
            actor = actor
          )
        } else {
          mergeTextractService.getTextractResult(
            projectId = userDocument.mergeTextractProjectId,
            pageRange = (page + 1, page + 1),
            actor = actor
          )
        }
      blocks <- ZIO.attempt(sdkBlocks.flatMap(TextractSdkConverter.convertBlock))
    } yield blocks
  }

  def getReviewedMapping(
    projectItemId: DataExtractProjectItemId,
    templateFileId: FileId,
    actor: UserId
  ): Task[GetReviewedMappingResponse] = {
    val projectId = projectItemId.parent
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is getting reviewed mapping for project item ${projectItemId.idString} and template " +
          s"${templateFileId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      templates <- getProjectTemplates(projectId)
      _ <- ZIOUtils.validate(templates.exists(_.fileId == templateFileId))(
        GeneralServiceException(s"Project does not contain template: ${templateFileId.idString}")
      )
      reviewedMappingOpt <- dataExtractStorageService.getReviewedMappingOpt(
        projectItemId = projectItemId,
        templateFileId = templateFileId
      )
      reviewedMapping = reviewedMappingOpt.getOrElse(ReviewedMapping.default)
    } yield GetReviewedMappingResponse(reviewedMapping)
  }

  def saveReviewedMapping(
    projectItemId: DataExtractProjectItemId,
    templateFileId: FileId,
    reviewedMapping: ReviewedMapping,
    actor: UserId
  ): Task[Unit] = {
    val projectId = projectItemId.parent
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is saving reviewed mapping for project item ${projectItemId.idString} and template " +
          s"${templateFileId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      templates <- getProjectTemplates(projectId)
      _ <- ZIOUtils.validate(templates.exists(_.fileId == templateFileId))(
        GeneralServiceException(s"Project does not contain template: ${templateFileId.idString}")
      )
      _ <- saveReviewedMappingInternal(projectItemId, templateFileId, reviewedMapping, actor)
      _ <- dataExtractProjectItemService.recalculateProjectItemStatusesAndPublish(
        projectId,
        Seq(projectItemId)
      )
    } yield ()
  }

  private def saveReviewedMappingInternal(
    projectItemId: DataExtractProjectItemId,
    templateFileId: FileId,
    reviewedMapping: ReviewedMapping,
    actor: UserId
  ) = {
    for {
      _ <- dataExtractStorageService.uploadReviewedMapping(
        projectItemId = projectItemId,
        templateFileId = templateFileId,
        reviewedMapping = reviewedMapping,
        actor = actor
      )
      _ <- DataExtractCacheUtils.saveReviewedMappingCache(
        projectItemId,
        templateFileId,
        DataExtractMappingUtils.getReviewedFields(reviewedMapping).size
      )
    } yield ()
  }

  def getAllErrorTags(projectId: DataExtractProjectId, actor: UserId): Task[GetAllErrorTagsResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is getting all error tags from project ${projectId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      allErrorTagsOpt <- dataExtractStorageService.getAllErrorTagsOpt
      allErrorTags = allErrorTagsOpt.fold(Set.empty[String])(_.tags)
    } yield GetAllErrorTagsResponse(allErrorTags)
  }

  def addErrorTag(projectId: DataExtractProjectId, tag: String, actor: UserId): Task[AddErrorTagResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is adding an error tag from project ${projectId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      allErrorTagsOpt <- dataExtractStorageService.getAllErrorTagsOpt
      allErrorTags = allErrorTagsOpt.fold(Set.empty[String])(_.tags) + tag
      _ <- dataExtractStorageService.uploadAllErrorTags(allErrorTags = AllErrorTags(allErrorTags), actor = actor)
    } yield AddErrorTagResponse(allErrorTags = allErrorTags)
  }

  def removeErrorTag(projectId: DataExtractProjectId, tag: String, actor: UserId): Task[RemoveErrorTagResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is removing an error tag from project ${projectId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      allErrorTagsOpt <- dataExtractStorageService.getAllErrorTagsOpt
      allErrorTags = allErrorTagsOpt.fold(Set.empty[String])(_.tags) - tag
      _ <- dataExtractStorageService.uploadAllErrorTags(allErrorTags = AllErrorTags(allErrorTags), actor = actor)
    } yield RemoveErrorTagResponse(allErrorTags = allErrorTags)
  }

  def getMappingContent(
    userDocumentIds: Seq[DataExtractUserDocumentId],
    pageMatches: Seq[PageMatch],
    algorithmVersion: MappingContentAlgorithmVersion,
    actor: UserId,
    parallelism: Int = 10
  )(
    using TextractServiceEvidence
  ): Task[GetMappingContentResponse] = {
    for {
      projectId <- ZIOUtils.uniqueSeqToTask(
        userDocumentIds.map(_.parent.parent),
        GeneralServiceException("All user documents should be in the same project")
      )
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is getting mapping content for ${userDocumentIds.size} user documents of project " +
          s"${projectId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      (mappingContent, transformedTemplateFieldAreas) <- ZIOUtils
        .foreachParN(parallelism)(userDocumentIds)(
          dataExtractMappingService.getMappingContent(
            _,
            pageMatches,
            algorithmVersion,
            actor
          )
        )
        .map(_.unzip)
    } yield GetMappingContentResponse(mappingContent.flatten, transformedTemplateFieldAreas.flatten)
  }

  def getTemplateData(
    projectId: DataExtractProjectId,
    templateFileId: FileId,
    actor: UserId
  )(
    using TextractServiceEvidence
  ): Task[GetTemplateDataResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is getting template data for template ${templateFileId.idString} of project " +
          s"${projectId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      templates <- getProjectTemplates(projectId)
      _ <- ZIOUtils.validate(templates.exists(_.fileId == templateFileId))(
        GeneralServiceException(s"Project does not contain template: ${templateFileId.idString}")
      )
      templateData <- dataExtractStorageService.getTemplateData(projectId, templateFileId)
    } yield GetTemplateDataResponse(templateData)
  }

  def renameProject(
    projectId: DataExtractProjectId,
    name: String,
    actor: UserId
  ): Task[RenameProjectResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is renaming project ${projectId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      project <- FDBRecordDatabase.transact(DataExtractProjectModelOperations.Production)(
        _.update(projectId)(_.copy(projectName = name))
      )
    } yield RenameProjectResponse(DataExtractProtoConverter.projectProtoToProjectBasicInfo(project))
  }

  def getAccessibleProjects(
    actorId: UserId,
    joinedProjectsOnly: Boolean = true,
    parallelism: Int = 4
  ): Task[Seq[DataExtractProjectBasicInfo]] = {
    for {
      _ <- ZIO.logInfo(s"User ${actorId.idString} is getting accessible projects")
      _ <- permissionService.validatePortalPermission(actorId)
      projectInfos <-
        if (!joinedProjectsOnly) {
          for {
            stream <- FDBRecordDatabase
              .largeScanStream(
                store = DataExtractProjectModelStoreProvider.Production,
                mapping = DataExtractProjectModelStoreProvider.projectPrimaryMapping,
                tupleRange = TupleRange.ALL,
                limit = 100
              )
            projectInfos <- stream
              .mapZIOPar(parallelism) { case _ -> project =>
                ZIO.succeed(DataExtractProtoConverter.projectProtoToProjectBasicInfo(project))
              }
              .runCollect
          } yield projectInfos
        } else {
          for {
            teamIds <- teamService.getAllTeamsOf(actorId)
            projectInfos <- FDBRecordDatabase
              .transact(DataExtractProjectModelOperations.Production) { ops =>
                RecordIO
                  .parTraverseN(parallelism)(teamIds.toList) { teamId =>
                    ops.getProjectsByTeamId(teamId).map(_.map(DataExtractProtoConverter.projectProtoToProjectBasicInfo))
                  }
                  .map(_.flatten)
              }
          } yield projectInfos
        }
    } yield projectInfos
  }

  def getProjectBasicInfo(
    projectId: DataExtractProjectId,
    actorId: UserId
  ): Task[DataExtractProjectBasicInfo] = {
    for {
      _ <- ZIO.logInfo(s"User ${actorId.idString} is getting basic info of project ${projectId.idString}")
      _ <- permissionService.validatePortalPermission(actorId)
      projectInfo <- getProjectBasicInfoUnsafe(projectId)
    } yield projectInfo
  }

  def getProjectBasicInfoUnsafe(
    projectId: DataExtractProjectId
  ): Task[DataExtractProjectBasicInfo] = {
    FDBRecordDatabase.transact(DataExtractProjectModelOperations.Production)(
      _.get(projectId).map(DataExtractProtoConverter.projectProtoToProjectBasicInfo)
    )
  }

  // This function does not work for unreviewed data, please ensure reviewed mapping is complete before calling it
  def getFormImportData(
    projectItemId: DataExtractProjectItemId,
    formVersionId: FormVersionId,
    actor: UserId
  ): Task[GetFormImportDataResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is getting form import data for project item ${projectItemId.idString} and form " +
          s"version ${formVersionId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectItemId.parent, actor)
      (formData, pdfData, importData) <- dataExtractFormMappingService.getFormImportData(
        projectItemId,
        formVersionId,
        actor
      )
    } yield GetFormImportDataResponse(formData, pdfData, importData)
  }

  // This function does not work for unreviewed data, please ensure reviewed mapping is complete before calling it
  def getFormImportDataWithTemplateMapping(
    projectItemId: DataExtractProjectItemId,
    formVersionId: FormVersionId,
    mappingVersionId: FormTemplateMappingVersionId,
    actor: UserId
  ): Task[GetFormImportDataWithTemplateMappingResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is getting form import data with template mapping for project item " +
          s"${projectItemId.idString}, form version ${formVersionId.idString}, and mapping version " +
          s"${mappingVersionId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectItemId.parent, actor)
      (formData, pdfData, mappingItems, dataTemplateType) <- dataExtractFormMappingService
        .getFormImportDataWithTemplateMapping(
          projectItemId,
          formVersionId,
          mappingVersionId,
          actor
        )
    } yield GetFormImportDataWithTemplateMappingResponse(formData, pdfData, mappingItems, dataTemplateType)
  }

  def getBestPageMatching(
    userDocumentIds: Seq[DataExtractUserDocumentId],
    actor: UserId,
    parallelism: Int = 10
  )(
    using TextractServiceEvidence
  ): Task[GetBestPageMatchingResponse] = {
    for {
      projectId <- ZIOUtils.uniqueSeqToTask(
        userDocumentIds.map(_.parent.parent),
        GeneralServiceException("All user documents should be in the same project")
      )
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is getting best page matching for ${userDocumentIds.size} user documents of project " +
          s"${projectId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      bestPageMatchingResults <- ZIOUtils
        .foreachParN(parallelism)(userDocumentIds) { userDocumentId =>
          for {
            userDocument <- FDBRecordDatabase.transact(DataExtractUserDocumentStoreOperations.Production)(
              _.get(userDocumentId)
            )
            _ <- ZIOUtils.validate(userDocument.applicableTemplates.nonEmpty) {
              GeneralServiceException(
                s"Page matching failed: Applicable templates are empty for user document ${userDocumentId.idString}"
              )
            }
            bestPageMatchingResults <- dataExtractMappingService.getBlockBasedPageMatching(
              userDocumentId,
              userDocument.applicableTemplates,
              actor
            )
          } yield bestPageMatchingResults
        }
        .map(_.flatten)
    } yield GetBestPageMatchingResponse(bestPageMatchingResults)
  }

  def getTopKSimilarPagesInUserDoc(
    userDocumentId: DataExtractUserDocumentId,
    templateFileId: FileId,
    topK: Int,
    actor: UserId
  )(
    using TextractServiceEvidence
  ): Task[GetTopKSimilarPagesInUserDocResponse] = {
    val projectId = userDocumentId.parent.parent
    for {
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      similarityScores <- dataExtractMappingService.getPageSemanticSimilarityScores(
        userDocumentId,
        templateFileId,
        Some(topK),
        actor
      )
    } yield GetTopKSimilarPagesInUserDocResponse(similarityScores)
  }

  def getProjectItemImportedFormData(projectItemId: DataExtractProjectItemId, actor: UserId)
    : Task[GetProjectItemImportedFormDataResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is getting imported form data for project item ${projectItemId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectItemId.parent, actor)
      reviewedMapping <- dataExtractMappingService.getMergedReviewMappingForProjectItemAndTemplates(projectItemId)
      flattenedReviewedMapping = reviewedMapping.getFlattenedReviewedData()
      extractedFormDataOpt <- dataExtractProjectItemService.getExtractedFormDataUnsafe(projectItemId)
      extractedFormData <- ZIOUtils.fromOption(
        extractedFormDataOpt,
        GeneralServiceException(s"Project item ${projectItemId.idString} has no extracted form data")
      )
      formData <- dataExtractFormMappingService.getFormData(extractedFormData.formVersionId, actor)
      status <- dataExtractProjectItemService.getProjectItemUnsafe(projectItemId).map(_.status)
      pdfData = flattenedReviewedMapping.flatMap { case key -> reviewedData => reviewedData.valueOpt.map(key -> _) }
    } yield GetProjectItemImportedFormDataResponse(
      pdfData,
      extractedFormData.formVersionId,
      formData,
      extractedFormData.gaiaState,
      extractedFormData.importData,
      extractedFormData.enableImportMode,
      status == DataExtractProjectItemStatus.Done
    )
  }

  def saveProjectItemImportedFormData(
    projectItemId: DataExtractProjectItemId,
    formVersionId: FormVersionId,
    gaiaState: GaiaState,
    importData: Map[String, Json],
    enableImportMode: Boolean,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is saving imported form data for project item ${projectItemId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectItemId.parent, actor)
      _ <- dataExtractProjectItemService.setExtractedFormData(
        projectItemId,
        Some(
          ExtractedFormData(
            formVersionId = formVersionId,
            gaiaState = gaiaState,
            importData = importData,
            enableImportMode = enableImportMode
          )
        )
      )
      _ <- dataExtractProjectItemService.recalculateProjectItemStatusesAndPublish(
        projectItemId.parent,
        Seq(projectItemId)
      )
    } yield ()
  }

  def clearProjectItemImportedFormData(projectItemId: DataExtractProjectItemId, actor: UserId): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is clearing imported form data for project item ${projectItemId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectItemId.parent, actor)
      _ <- dataExtractProjectItemService.setExtractedFormData(projectItemId, None)
      _ <- dataExtractProjectItemService.recalculateProjectItemStatusesAndPublish(
        projectItemId.parent,
        Seq(projectItemId)
      )
    } yield ()
  }

  def getFormVersionBasicInfo(
    formVersionId: FormVersionId,
    actor: UserId
  ): Task[GetFormVersionBasicInfoResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.idString} is getting basic info for form version with id ${formVersionId.idString}")
      _ <- formService.validateFormPermission(formVersionId.parent, actor)
      (formModel, formVersionOpt, _, _, _, _) <- formService.getFormModelsUnsafe(
        formId = formVersionId.parent,
        versionIdOpt = Some(formVersionId)
      )
      formVersion <- ZIOUtils.fromOption(
        formVersionOpt,
        GeneralServiceException(s"Failed to get form version with id ${formVersionId.idString}")
      )
    } yield GetFormVersionBasicInfoResponse(
      formName = formModel.name,
      formVersionName = formVersion.name,
      formVersionNumber = formVersion.versionNumber
    )
  }

  def getImportedFormVersionBasicInfo(projectItemId: DataExtractProjectItemId, actor: UserId)
    : Task[GetImportedFormVersionBasicInfoResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is getting basic info of imported form version for project item ${projectItemId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectItemId.parent, actor)
      extractedFormDataOpt <- dataExtractProjectItemService.getExtractedFormDataUnsafe(projectItemId)
      extractedFormData <- ZIOUtils.fromOption(
        extractedFormDataOpt,
        GeneralServiceException(s"Project item $projectItemId has no extracted form data")
      )
      formVersionId = extractedFormData.formVersionId
      response <- getFormVersionBasicInfo(formVersionId, actor)
    } yield GetImportedFormVersionBasicInfoResponse(
      formVersionId = formVersionId,
      formName = response.formName,
      formVersionName = response.formVersionName,
      formVersionNumber = response.formVersionNumber
    )
  }

  def setProjectConfig(
    projectId: DataExtractProjectId,
    config: ProjectConfig,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is setting config for project ${projectId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      _ <- FDBRecordDatabase.transact(DataExtractProjectModelOperations.Production)(
        _.update(projectId)(_.copy(projectConfigOpt = Some(DataExtractProtoConverter.projectConfigModelToProto(config))))
      )
    } yield ()
  }

  def getAllUsers(actor: UserId): Task[GetAllUsersResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is getting all portal users")
      _ <- permissionService.validatePortalPermission(actor)
      portalUsers <- portalUserService.getAllPortalUsersSimple.map(_.allPortalUsers)
    } yield GetAllUsersResponse(portalUsers.map { user => user.portalUser.userId -> user.userInfo })
  }

  def setUserDocumentNotes(userDocumentId: DataExtractUserDocumentId, notes: String, actor: UserId): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is setting notes for user document ${userDocumentId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(userDocumentId.parent.parent, actor)
      _ <- FDBRecordDatabase.transact(DataExtractUserDocumentStoreOperations.Production)(
        _.update(userDocumentId)(_.copy(notes = notes))
      )
    } yield ()
  }

  def getUserDocumentStatuses(userDocumentIds: Seq[DataExtractUserDocumentId], actor: UserId, parallelism: Int = 4)
    : Task[GetUserDocumentStatusesResponse] = {
    for {
      projectId <- ZIOUtils.uniqueSeqToTask(
        userDocumentIds.map(_.parent.parent),
        GeneralServiceException("All project items should be in the same project")
      )
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is getting statuses for ${userDocumentIds.size} user documents of project " +
          s"${projectId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      statuses <- FDBRecordDatabase.transact(DataExtractUserDocumentStoreOperations.Production) { ops =>
        RecordIO.parTraverseN(parallelism)(userDocumentIds) { userDocumentId =>
          ops.get(userDocumentId).map { userDocument =>
            userDocumentId -> DataExtractProtoConverter.userDocumentStatusProtoToModel(userDocument.status)
          }
        }
      }
    } yield GetUserDocumentStatusesResponse(statuses)
  }

  def markUserDocumentAsRejected(userDocumentId: DataExtractUserDocumentId, actor: UserId): Task[Unit] = {
    val projectId = userDocumentId.parent.parent
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is marking user document ${userDocumentId.idString} as rejected")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      _ <- FDBRecordDatabase.transact(DataExtractUserDocumentStoreOperations.Production)(
        _.update(userDocumentId)(_.copy(status = DataExtractUserDocumentStatusMessage.Rejected))
      )
      _ <- natsNotificationService.publishMulti(
        Seq(userDocumentId),
        DataExtractNotificationChannels.updateUserDocumentStatus(projectId)
      )
      _ <- dataExtractProjectItemService.recalculateUserDocumentStatusesAndPublish(
        projectId,
        Seq(userDocumentId),
        ignoreRejectedUserDocuments = true
      )
    } yield ()
  }

  def resetUserDocumentStatus(userDocumentId: DataExtractUserDocumentId, actor: UserId): Task[Unit] = {
    val projectId = userDocumentId.parent.parent
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is resetting status of user document ${userDocumentId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      _ <- dataExtractProjectItemService.recalculateUserDocumentStatusesAndPublish(
        projectId,
        Seq(userDocumentId),
        ignoreRejectedUserDocuments = false
      )
    } yield ()
  }

  def getProjectItemApplicableTemplates(projectItemId: DataExtractProjectItemId, actor: UserId)
    : Task[GetProjectItemApplicableTemplatesResponse] = {
    val projectId = projectItemId.parent
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is getting applicable templates of project item ${projectItemId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      applicableTemplates <- dataExtractProjectItemService.getProjectItemApplicableTemplates(projectItemId)
    } yield GetProjectItemApplicableTemplatesResponse(applicableTemplates)
  }

  def setProjectItemsApplicableTemplates(
    projectItemsApplicableTemplates: Seq[(DataExtractProjectItemId, Set[FileId])],
    actor: UserId
  ): Task[Unit] = {
    for {
      projectId <- ZIOUtils.uniqueSeqToTask(
        projectItemsApplicableTemplates.map(_._1.parent),
        GeneralServiceException("All project items should be in the same project")
      )
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is setting applicable templates for ${projectItemsApplicableTemplates.size} project " +
          s"items of project ${projectId.idString}"
      )
      _ <- ZIOUtils.when(projectItemsApplicableTemplates.exists(_._2.isEmpty)) {
        ZIO.fail(GeneralServiceException(s"Applicable templates must not be empty"))
      }
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      _ <- ZIOUtils.foreachParN(4)(projectItemsApplicableTemplates) { case projectItemId -> applicableTemplates =>
        for {
          userDocuments <- dataExtractProjectItemService.getProjectItemUserDocuments(Seq(projectItemId))
          applicableTemplatesByUserDocument = userDocuments.map(_.id -> applicableTemplates).toMap
          _ <- dataExtractProjectItemService.setUserDocumentsApplicableTemplates(
            applicableTemplatesByUserDocument
          )
        } yield ()
      }
      _ <- dataExtractProjectItemService.recalculateProjectItemStatusesAndPublish(
        projectId,
        projectItemsApplicableTemplates.map(_._1)
      )
    } yield ()
  }

  def setUserDocumentsApplicableTemplates(
    applicableTemplatesByUserDocument: Map[DataExtractUserDocumentId, Set[FileId]],
    userDocumentsToReject: Set[DataExtractUserDocumentId],
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIOUtils.when(applicableTemplatesByUserDocument.values.exists(_.isEmpty)) {
        ZIO.fail(GeneralServiceException(s"Applicable templates must not be empty"))
      }
      projectItemIds = (applicableTemplatesByUserDocument.keySet ++ userDocumentsToReject).map(_.parent)
      projectId <- ZIOUtils.uniqueSeqToTask(
        projectItemIds.map(_.parent),
        GeneralServiceException("All user documents should be in the same project")
      )
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is setting applicable templates for ${applicableTemplatesByUserDocument.size} user " +
          s"documents of ${projectItemIds.size} project items in project ${projectId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      _ <- ZIOUtils.when(userDocumentsToReject.nonEmpty) {
        for {
          _ <- FDBRecordDatabase.transact(DataExtractUserDocumentStoreOperations.Production) { ops =>
            RecordIO.parTraverseN(4)(userDocumentsToReject) { userDocumentId =>
              ops.update(userDocumentId)(_.copy(status = DataExtractUserDocumentStatusMessage.Rejected))
            }
          }
          _ <- natsNotificationService.publishMulti(
            userDocumentsToReject.toSeq,
            DataExtractNotificationChannels.updateUserDocumentStatus(projectId)
          )
        } yield ()
      }
      _ <- dataExtractProjectItemService.setUserDocumentsApplicableTemplates(
        applicableTemplatesByUserDocument ++ userDocumentsToReject.map(_ -> Set.empty).toMap
      )
      _ <- dataExtractProjectItemService.recalculateProjectItemStatusesAndPublish(
        projectId,
        projectItemIds.toSeq
      )
    } yield ()
  }

  def setProjectItemTags(
    projectItemId: DataExtractProjectItemId,
    updatedTags: Seq[String],
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is setting tags for project item ${projectItemId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectItemId.parent, actor)
      _ <- FDBRecordDatabase.transact(DataExtractProjectItemStoreOperations.Production)(
        _.update(projectItemId)(_.copy(tags = updatedTags))
      )
    } yield ()
  }

  def setRepeatableTemplate(
    projectId: DataExtractProjectId,
    templateFileId: FileId,
    isRepeatable: Boolean,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is setting repeatable template for project ${projectId.idString}, template " +
          s"${templateFileId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      _ <- FDBRecordDatabase.transact(DataExtractProjectModelOperations.Production)(_.update(projectId) { project =>
        project.copy(templates = project.templates.map { template =>
          if (template.id == templateFileId) {
            template.copy(isRepeatable = isRepeatable, lastUpdatedAt = Some(Instant.now))
          } else {
            template
          }
        })
      })
    } yield ()
  }

  def checkManageProjectMembersPermission(projectId: DataExtractProjectId, actor: UserId)
    : Task[CheckManageProjectMembersPermissionResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is checking manage project members permission for project ${projectId.idString}"
      )
      isSuperAdmin <- permissionService.hasSuperAdminPermission(actor)
      hasPermission <-
        if (isSuperAdmin) {
          ZIO.succeed(true)
        } else {
          permissionService.hasProjectOwnerPermission(projectId, actor)
        }
    } yield CheckManageProjectMembersPermissionResponse(hasPermission)
  }

  def createCueModule(
    projectId: DataExtractProjectId,
    actor: UserId
  )(
    using DigitizationServiceKeyspace.KeySpace
  ): Task[CreateCueModuleResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is creating cue module for project ${projectId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      project <- getProjectBasicInfoUnsafe(projectId)
      resp <- digitizationFileService.createCueModuleFile(
        parentId = projectId,
        name = s"Data extract project: ${project.projectName}",
        actor = actor,
        parentFolderIdOpt = Some(DigitizationFolderId(DigitizationFolderId.RootFolderId))
      )
    } yield CreateCueModuleResponse(resp.cueModule)
  }

  def generateDataExtractCueModule(
    projectId: DataExtractProjectId,
    selectedTemplateFileIds: Seq[FileId],
    actor: UserId
  )(
    using CueModuleServiceKeySpace
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is generating data extract cue module for project ${projectId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      templates <- getProjectTemplates(projectId)
      templateMap = templates.map { template => template.fileId -> template }.toMap
      templatesInfo <- ZIO.foreach(selectedTemplateFileIds) { templateFileId =>
        for {
          template <- ZIOUtils.fromOption(
            templateMap.get(templateFileId),
            GeneralServiceException(s"Template ${templateFileId.idString} not found")
          )
          annotationVersionId <- ZIOUtils.fromOption(
            template.annotationVersionIdOpt,
            GeneralServiceException(s"Template ${templateFileId.idString} has no annotation version")
          )
          (storageId, _) = annotationDocumentStorageService.getStorageIdBucket(annotationVersionId)
        } yield TemplateInfo(
          alias = template.alias,
          name = template.name,
          isRepeatable = template.isRepeatable,
          annotationDataS3Key = storageId.id
        )
      }
      _ <- cueModuleService.generateCueModule(
        cueModuleId = projectId.cueModuleId,
        cueGeneratorInput = GenerateDataExtractModule(
          annotationDataS3Access = ServerlessUtils.getS3Access(annotationDocumentStorageService.storageBucket),
          templatesInfo = templatesInfo
        ),
        actor = actor
      )
    } yield ()
  }

  def createCueMappingModule(
    projectId: DataExtractProjectId,
    actor: UserId
  )(
    using DigitizationServiceKeyspace.KeySpace
  ): Task[CreateCueMappingModuleResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is creating cue mapping module for project ${projectId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      project <- getProjectBasicInfoUnsafe(projectId)
      resp <- digitizationFileService.createCueModuleFile(
        parentId = projectId.cueMappingId,
        name = s"Data extract project export mappings: ${project.projectName}",
        actor = actor,
        parentFolderIdOpt = Some(DigitizationFolderId(DigitizationFolderId.RootFolderId))
      )
    } yield CreateCueMappingModuleResponse(resp.cueModule)
  }

  def generateDataExtractCueMappingModule(
    projectId: DataExtractProjectId,
    targetSaDataTemplateIds: Seq[SaDataTemplateId],
    actor: UserId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[Unit] = {
    given CueModuleZotKeySpace = keySpace.cueModuleZotKeySpace
    val sourceCueModuleId = projectId.cueModuleId
    val sourceCueMappingModuleId = projectId.cueMappingId.cueModuleId
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is generating cue mapping module for project ${projectId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      sourceVersionOpt <- cueModuleService.getLatestVersionUnsafe(sourceCueModuleId)
      sourceModule = CueGeneratorUtils.toDataExtractTableModule(
        sourceCueModuleId,
        sourceVersionOpt.map(_.zotVersionTag)
      )
      targetModules <- ZIO.foreach(targetSaDataTemplateIds) { saDataTemplateId =>
        val targetCueModuleId = saDataTemplateId.cueModuleId
        cueModuleService.getLatestVersionUnsafe(targetCueModuleId).map { targetVersionOpt =>
          CueGeneratorUtils.toSaTemplateTableModule(targetCueModuleId, targetVersionOpt.map(_.zotVersionTag))
        }
      }
      _ <- cueModuleService.generateCueModule(
        cueModuleId = sourceCueMappingModuleId,
        cueGeneratorInput = GenerateTableMappingModule(
          sourceModule = sourceModule,
          targetModules = targetModules.toList
        ),
        actor = actor
      )
    } yield ()
  }

  def updateTemplateAlias(
    projectId: DataExtractProjectId,
    templateFileId: FileId,
    newAlias: String,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is updating template alias for project ${projectId.idString}, template " +
          s"${templateFileId.idString}"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      _ <- FDBRecordDatabase.transact(DataExtractProjectModelOperations.Production)(_.update(projectId) { project =>
        project.copy(templates = project.templates.map { template =>
          if (template.id == templateFileId) {
            template.copy(aliasOpt = Some(newAlias), lastUpdatedAt = Some(Instant.now))
          } else {
            template
          }
        })
      })
    } yield ()
  }

  def generateCueTableData(
    projectItemId: DataExtractProjectItemId,
    cueModuleVersionId: CueModuleVersionId,
    actor: UserId
  )(
    using CueModuleServiceKeySpace
  ): Task[GenerateCueTableDataResponse] = {
    val projectId = projectItemId.parent
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is generating cue table data for project item ${projectItemId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      commands <- dataExtractCueService.getDataExtractCommandsUnsafe(cueModuleVersionId)
      tableSchemas <- dataExtractCueService.getTableSchemasUnsafe(cueModuleVersionId)
      (ocrFields, valuesWithValidation) <- generateCueTableDataUnsafe(projectItemId, cueModuleVersionId)
      templates <- getProjectTemplates(projectId)
      applicableTemplateFileIds <- dataExtractProjectItemService.getProjectItemApplicableTemplates(projectItemId)
      templateFileIdMap = templates.collect {
        case template if applicableTemplateFileIds.contains(template.fileId) =>
          template.alias -> template.fileId
      }.toMap
      userDocuments <- dataExtractProjectItemService.getProjectItemUserDocuments(projectItemIds = Seq(projectItemId))
      userDocumentFileIdMap = userDocuments.map { userDocument =>
        userDocument.fileId.idString -> userDocument.fileId
      }.toMap
      templateDataMap <- ZIO
        .foreachPar(templateFileIdMap) { case templateAlias -> templateFileId =>
          dataExtractStorageService.getTemplateData(projectId, templateFileId).map(templateAlias -> _)
        }
        .withParallelism(4)
    } yield GenerateCueTableDataResponse(
      commands,
      ocrFields,
      tableSchemas,
      valuesWithValidation,
      templateFileIdMap,
      userDocumentFileIdMap,
      templateDataMap
    )
  }

  def generateCueTableDataUnsafe(
    projectItemId: DataExtractProjectItemId,
    cueModuleVersionId: CueModuleVersionId
  )(
    using CueModuleServiceKeySpace
  ): Task[(ocrFields: OcrFields, valuesWithValidation: SoftValidationValuesWithValidation)] = {
    val projectCueModuleId = projectItemId.parent.cueModuleId
    for {
      _ <- ZIOUtils.validate(projectCueModuleId == cueModuleVersionId.parent)(
        GeneralServiceException(
          s"Cue module version ${cueModuleVersionId.idString} does not match project module ${projectCueModuleId.idString}"
        )
      )
      reviewedMappings <- dataExtractMappingService.getReviewedMappingsForProjectItemAndTemplates(projectItemId)
      tableData <- dataExtractCueService.generateCueTableDataFromReviewedMappingsUnsafe(
        cueModuleVersionId,
        reviewedMappings
      )
    } yield tableData
  }

  def getCueModuleVersions(
    projectId: DataExtractProjectId,
    actor: UserId
  )(
    using CueModuleServiceKeySpace
  ): Task[GetCueModuleVersionsResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is getting cue module versions for project ${projectId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      cueModuleVersions <- cueModuleService.getAllVersionsUnsafe(cueModuleId = projectId.cueModuleId)
    } yield GetCueModuleVersionsResponse(cueModuleVersions)
  }

  def transformTableValues(
    projectId: DataExtractProjectId,
    cueModuleVersionId: CueModuleVersionId,
    commands: DataExtractCommands,
    values: SoftValidationValueSchema,
    actor: UserId
  )(
    using CueModuleServiceKeySpace
  ): Task[TransformTableValuesResponse] = {
    val projectCueModuleId = projectId.cueModuleId
    for {
      _ <- ZIO.logInfo(s"User is transforming table values in project ${projectId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      _ <- ZIOUtils.validate(projectCueModuleId == cueModuleVersionId.parent)(
        GeneralServiceException(
          s"Cue module version ${cueModuleVersionId.idString} does not match project module ${projectCueModuleId.idString}"
        )
      )
      newValuesWithValidation <- dataExtractCueService.transformSoftValidationValuesWithValidationUnsafe(
        cueModuleVersionId = cueModuleVersionId,
        commands = commands,
        values = values
      )
    } yield TransformTableValuesResponse(newValuesWithValidation)
  }

  private def drawAnnotationBoxes(
    pdfFileId: FileId,
    fieldNames: Set[String],
    fields: List[PdfFieldInfo],
    actor: UserId
  ): Task[DrawAnnotationBoxesResponse] = {
    for {
      pdfStorageId <- fileService.getFileStorageId(
        actor = actor,
        fileId = pdfFileId,
        purpose = DmsTrackingActivityType.Internal,
        httpContextOpt = None
      )
      result <- drawAnnotationBoxesServerless.drawAnnotationBoxes(
        DrawAnnotationBoxesRequest(
          pdfPath = S3Utils.resolveObjectKeyWithBucket(s3Access.bucket, pdfStorageId.id),
          fieldNames = fieldNames,
          s3Access = s3Access,
          useCache = true,
          fieldsOpt = Some(fields)
        )
      )
    } yield result
  }

  private def generateSchema(
    pdfFormSchema: PdfFormSchema,
    templateS3Images: List[String]
  ): Task[GenerateSchemaResponse] = {
    generateSchemaServerless.generateSchema(
      GenerateSchemaRequest(
        documentType = None,
        s3Images = templateS3Images,
        initialFormSchema = pdfFormSchema,
        s3Access = s3Access,
        liteLlmAccess = LiteLlmAccess.fromLiteLlmConfig(
          liteLlmConfig = liteLlmConfig,
          model = LiteLlmAccess.Model.ClaudeSonnet3_7V1
        )
      )
    )
  }

  private def pdfToImages(pdfFileId: FileId, actor: UserId): Task[List[String]] = {
    for {
      pdfStorageId <- fileService.getFileStorageId(
        actor = actor,
        fileId = pdfFileId,
        purpose = DmsTrackingActivityType.Internal,
        httpContextOpt = None
      )
      outputStoragePrefix = s"${pdfFileId.parent.idString}-${java.util.UUID.randomUUID().toString}/"
      result <- muPDFServerless.pdfToImages(
        PdfToImagesRequest(
          s3Access = s3Access,
          pdfStorageId = pdfStorageId.id,
          passwordOpt = None,
          outputStoragePrefix = outputStoragePrefix,
          outputFileName = "images-%03d.jpg"
        )
      )
      s3Images = result.outputStorageIds.map { outputStorageId =>
        S3Utils.resolveObjectKeyWithBucket(s3Access.bucket, outputStorageId)
      }
    } yield s3Images
  }

  def extractData(
    projectId: DataExtractProjectId,
    userDocFileId: FileId,
    templateFileId: FileId,
    annotationVersionId: AnnotationDocumentVersionId,
    userDocPages: Set[Int],
    templateFieldNames: Set[String],
    actor: UserId
  )(
    using AnnotationDocumentService.KeySpace
  ): Task[ExtractDataResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is extracting data for project ${projectId.idString}, template " +
          s"${templateFileId.idString}, user document ${userDocFileId.idString} (pages ${userDocPages.mkString(", ")})"
      )
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      allPdfObjects <- annotationDocumentService
        .getAnnotationDocument(
          annotationVersionId.parent,
          Some(annotationVersionId),
          actor
        )
        .map(_.annotationData.pdfObjects)
      relevantPdfObjects = for {
        fieldName <- templateFieldNames.toSeq
        pdfObjectId <- allPdfObjects.nameObjectIdMap.get(fieldName)
        pdfObject <- allPdfObjects.get(pdfObjectId)
      } yield pdfObject
      fieldInfos = PdfFieldInfo
        .fromPdfObjects(
          relevantPdfObjects,
          getLabelFn = field => {
            allPdfObjects
              .getLabelTextContent(field.id)
              .collect {
                case TextContent.Derived(str) => str
                case TextContent.Custom(str)  => str
              }
              .map(TextUtils.getTextFromHtmlKeepNewLine)
          }
        )
        .toList
      (pdfFormSchema, templateS3Images) <- drawAnnotationBoxes(templateFileId, templateFieldNames, fieldInfos, actor)
        .map { resp =>
          resp.pdf_form_schema -> resp.image_path_by_page.values.toList
        }
      formSchema <- generateSchema(pdfFormSchema, templateS3Images).map(_.form_schema)
      userDocS3Images <- pdfToImages(userDocFileId, actor)
      request = FormDataExtractionRequest(
        formSchema = formSchema,
        s3Images = userDocS3Images.zipWithIndex.filter { (_, index) => userDocPages.contains(index + 1) }.map(_._1),
        s3Access = s3Access,
        liteLlmAccess = LiteLlmAccess.fromLiteLlmConfig(
          liteLlmConfig = liteLlmConfig,
          model = LiteLlmAccess.Model.ClaudeSonnet3_7V1
        )
      )
      resp <- formDataExtractionServerless.formDataExtract(request)
      fieldNameMap = pdfFormSchema.fields.view.mapValues(_.pdf_field_name.getOrElse("")).filter(_._2.nonEmpty).toMap
      result = resp.fields.flatMap { field =>
        fieldNameMap.get(field.field_name).map(_ -> field.value)
      }.toMap
    } yield ExtractDataResponse(
      newValueByFieldName = result
    )
  }

  def getAllSaDataTemplates(
    projectId: DataExtractProjectId,
    actor: UserId
  ): Task[GetAllSaDataTemplatesResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is getting all SA data templates from project ${projectId.idString}")
      _ <- permissionService.validateProjectOwnerPermission(projectId, actor)
      templateList <- saDataTemplateService
        .getAllSaDataTemplates(actorId = actor, ignorePermissionCheck = true)
        .map(_.templateList)
    } yield GetAllSaDataTemplatesResponse(templateList)
  }

}
