// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.module

import com.softwaremill.macwire.wire

import anduin.annotation.service.{AnnotationDocumentService, AnnotationDocumentStorageService}
import anduin.cue.service.{CueModuleService, CueTableService}
import anduin.dataextract.integration.{DataExtractIntegrationService, DataExtractIntegrationServiceImpl}
import anduin.dataextract.service.*
import anduin.digitization.service.DigitizationFileService
import anduin.forms.service.{DataTemplateService, FormService, FormTemplateMappingService}
import anduin.sa.service.SaDataTemplateService

trait DataExtractServiceModule extends GondorCoreServiceModule {

  def formService: FormService
  def formTemplateMappingService: FormTemplateMappingService
  def dataTemplateService: DataTemplateService
  def annotationDocumentService: AnnotationDocumentService
  def cueModuleService: CueModuleService
  def digitizationFileService: DigitizationFileService
  def annotationDocumentStorageService: AnnotationDocumentStorageService
  def cueTableService: CueTableService
  def saDataTemplateService: SaDataTemplateService

  given dataExtractService: DataExtractService = wire[DataExtractService]
  given dataExtractProjectItemService: DataExtractProjectItemService = wire[DataExtractProjectItemService]

  // Keep at least 1 unwired service to combat zinc bug
  given dataExtractPermissionService: DataExtractPermissionService =
    DataExtractPermissionService(teamService, portalUserService)

  given dataExtractMappingService: DataExtractMappingService = wire[DataExtractMappingService]
  given dataExtractBatchActionService: DataExtractBatchActionService = wire[DataExtractBatchActionService]
  given dataExtractFormMappingService: DataExtractFormMappingService = wire[DataExtractFormMappingService]
  given dataExtractAnalyticsService: DataExtractAnalyticsService = wire[DataExtractAnalyticsService]
  given dataExtractDebugService: DataExtractDebugService = wire[DataExtractDebugService]
  given dataExtractWorkbookService: DataExtractWorkbookService = wire[DataExtractWorkbookService]
  given dataExtractStorageService: DataExtractStorageService = wire[DataExtractStorageService]
  given dataExtractIntegrationService: DataExtractIntegrationService = wire[DataExtractIntegrationServiceImpl]

  given dataExtractStatusChangeEventProducerService: DataExtractStatusChangeEventProducerService =
    wire[DataExtractStatusChangeEventProducerService]

  given dataExtractCueService: DataExtractCueService = wire[DataExtractCueService]

}
