// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.gondor.server

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration

import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter

import anduin.annotation.service.AnnotationDocumentService.KeySpace as AnnotationDocumentServiceKeySpace
import anduin.cue.service.CueModuleService.CueModuleServiceKeySpace
import anduin.dataextract.endpoint.DataExtractEndpoint.*
import anduin.dataextract.protocols.{CreateProjectItemsResp, GetAccessibleProjectsResponse, GetProjectBasicInfoResponse}
import anduin.dataextract.service.{
  DataExtractBatchActionService,
  DataExtractPermissionService,
  DataExtractProjectItemService,
  DataExtractService
}
import anduin.digitization.common.DigitizationServiceKeyspace.KeySpace as DigitizationServiceKeySpace
import anduin.documentservice.textract.{TextractServiceEvidence, WithTextractServiceEvidence}
import anduin.tapir.endpoint.EmptyResponse
import anduin.tapir.server.AuthenticatedEndpointServer
import anduin.tapir.server.EndpointServer.TapirServerService
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.authorization.AuthorizationService

final case class DataExtractServer(
  protected val backendConfig: GondorBackendConfig,
  protected val authorizationService: AuthorizationService,
  dataExtractService: DataExtractService,
  dataExtractProjectItemService: DataExtractProjectItemService,
  dataExtractPermissionService: DataExtractPermissionService,
  dataExtractBatchActionService: DataExtractBatchActionService,
  override protected val interpreter: ArmeriaZioServerInterpreter[Any]
) extends AuthenticatedEndpointServer
    with WithTextractServiceEvidence {

  override protected given evidence: TextractServiceEvidence = TextractServiceEvidence.DataExtractService
  private given AnnotationDocumentServiceKeySpace = AnnotationDocumentServiceKeySpace.Production
  private given CueModuleServiceKeySpace = CueModuleServiceKeySpace.Production
  private given DigitizationServiceKeySpace = DigitizationServiceKeySpace.Production

  // add this timeout to APIs calling textract serverless functions to sync their timeouts
  private val textractUtilitiesServerlessTimeout =
    backendConfig.serverlessConfig.serverlessFunctions.textractUtilities.executionTimeout

  val services: List[TapirServerService] = List(
    authRouteCatchError(createDataExtractProject) { (params, ctx) =>
      dataExtractService.createProject(ctx.actor.userId, params)
    },
    authRouteCatchError(addTemplateToProject) { (params, ctx) =>
      dataExtractService.addTemplateToProject(params, ctx.actor.userId)
    },
    authRouteCatchError(createProjectItems) { (params, ctx) =>
      dataExtractService.createProjectItems(params, ctx.actor.userId).map(CreateProjectItemsResp(_))
    },
    authRouteCatchError(getProjectData) { (params, ctx) =>
      dataExtractService.getProjectData(ctx.actor.userId, params)
    },
    authRouteCatchError(getMappingResultFile, timeout = FiniteDuration(10, TimeUnit.MINUTES)) { (params, ctx) =>
      dataExtractBatchActionService.getMappingResultWorkbook(ctx.actor.userId, params)
    },
    authRouteCatchError(generateDebugReport, timeout = FiniteDuration(10, TimeUnit.MINUTES)) { (params, ctx) =>
      dataExtractBatchActionService.generateDebugReport(ctx.actor.userId, params)
    },
    authRouteCatchError(updateTemplatesArchivedStatus) { (params, ctx) =>
      dataExtractService.updateTemplatesArchivedStatus(params = params, actor = ctx.actor.userId)
    },
    authRouteCatchError(removeProjectItems) { (params, ctx) =>
      dataExtractService.removeProjectItems(params = params, actor = ctx.actor.userId)
    },
    authRouteCatchError(getBestPageMatching, defaultTimeout + textractUtilitiesServerlessTimeout) { (params, ctx) =>
      dataExtractService.getBestPageMatching(
        userDocumentIds = params.userDocumentIds,
        actor = ctx.actor.userId
      )
    },
    authRouteCatchError(getTopKSimilarPagesInUserDoc, defaultTimeout + textractUtilitiesServerlessTimeout) {
      (params, ctx) =>
        dataExtractService.getTopKSimilarPagesInUserDoc(
          userDocumentId = params.userDocumentId,
          templateFileId = params.templateFileId,
          topK = params.topK,
          actor = ctx.actor.userId
        )
    },
    authRouteCatchError(getUserDocumentPageContent) { (params, ctx) =>
      dataExtractService.getUserDocumentPageContent(
        userDocumentId = params.userDocumentId,
        userDocumentPage = params.userDocumentPage,
        actor = ctx.actor.userId
      )
    },
    authRouteCatchError(getReviewedMapping) { (params, ctx) =>
      dataExtractService.getReviewedMapping(
        projectItemId = params.projectItemId,
        templateFileId = params.templateFileId,
        actor = ctx.actor.userId
      )
    },
    authRouteCatchError(saveReviewedMapping) { (params, ctx) =>
      dataExtractService.saveReviewedMapping(
        projectItemId = params.projectItemId,
        templateFileId = params.templateFileId,
        reviewedMapping = params.reviewedMapping,
        actor = ctx.actor.userId
      )
    },
    authRouteCatchError(getAllErrorTags) { (params, ctx) =>
      dataExtractService.getAllErrorTags(projectId = params.projectId, actor = ctx.actor.userId)
    },
    authRouteCatchError(addErrorTag) { (params, ctx) =>
      dataExtractService.addErrorTag(projectId = params.projectId, tag = params.tag, actor = ctx.actor.userId)
    },
    authRouteCatchError(removeErrorTag) { (params, ctx) =>
      dataExtractService.removeErrorTag(projectId = params.projectId, tag = params.tag, actor = ctx.actor.userId)
    },
    authRouteCatchError(generateAnalyticsReport) { (params, ctx) =>
      dataExtractBatchActionService.generateAnalyticsReport(ctx.actor.userId, params.projectIds)
    },
    authRouteCatchError(getMappingContent, defaultTimeout + textractUtilitiesServerlessTimeout) { (params, ctx) =>
      dataExtractService.getMappingContent(
        params.userDocumentIds,
        params.pageMatches,
        params.algorithmVersion,
        ctx.actor.userId
      )
    },
    authRouteCatchError(getTemplateData) { (params, ctx) =>
      dataExtractService.getTemplateData(params.projectId, params.templateFileId, ctx.actor.userId)
    },
    authRouteCatchError(renameProject) { (params, ctx) =>
      dataExtractService.renameProject(params.projectId, params.name, ctx.actor.userId)
    },
    authRouteCatchError(getFormImportData) { (params, ctx) =>
      dataExtractService.getFormImportData(params.projectItemId, params.formVersionId, ctx.actor.userId)
    },
    authRouteCatchError(getFormImportDataWithTemplateMapping) { (params, ctx) =>
      dataExtractService.getFormImportDataWithTemplateMapping(
        params.projectItemId,
        params.formVersionId,
        params.mappingVersionId,
        ctx.actor.userId
      )
    },
    authRouteCatchError(getAccessibleProjects) { (params, ctx) =>
      dataExtractService
        .getAccessibleProjects(ctx.actor.userId, params.joinedProjectsOnly)
        .map(GetAccessibleProjectsResponse(_))
    },
    authRouteCatchError(runTextractForUserDocuments) { (params, ctx) =>
      dataExtractService.runTextractForUserDocuments(
        params.firstTextractUserDocumentIds,
        params.secondTextractUserDocumentIds,
        params.runTextractMode,
        ctx.actor.userId
      )
    },
    authRouteCatchError(getProjectItemImportedFormData) { (params, ctx) =>
      dataExtractService.getProjectItemImportedFormData(params.projectItemId, ctx.actor.userId)
    },
    authRouteCatchError(saveProjectItemImportedFormData) { (params, ctx) =>
      dataExtractService
        .saveProjectItemImportedFormData(
          params.projectItemId,
          params.formVersionId,
          params.gaiaState,
          params.importData,
          params.enableImportMode,
          ctx.actor.userId
        )
        .as(EmptyResponse())
    },
    authRouteCatchError(clearProjectItemImportedFormData) { (params, ctx) =>
      dataExtractService.clearProjectItemImportedFormData(params.projectItemId, ctx.actor.userId).as(EmptyResponse())
    },
    authRouteCatchError(getFormVersionBasicInfo) { (params, ctx) =>
      dataExtractService.getFormVersionBasicInfo(params.formVersionId, ctx.actor.userId)
    },
    authRouteCatchError(getImportedFormVersionBasicInfo) { (params, ctx) =>
      dataExtractService.getImportedFormVersionBasicInfo(params.projectItemId, ctx.actor.userId)
    },
    authRouteCatchError(setProjectConfig) { (params, ctx) =>
      dataExtractService.setProjectConfig(params.projectId, params.config, ctx.actor.userId).as(EmptyResponse())
    },
    authRouteCatchError(getProjectBasicInfo) { (params, ctx) =>
      dataExtractService.getProjectBasicInfo(params.projectId, ctx.actor.userId).map(GetProjectBasicInfoResponse(_))
    },
    authRouteCatchError(addNewMemberToProject) { (params, ctx) =>
      dataExtractService.addNewMemberToProject(ctx.actor.userId, params.projectId, params.newMembers).as(EmptyResponse())
    },
    authRouteCatchError(removeMemberFromProject) { (params, ctx) =>
      dataExtractService
        .removeMemberFromProject(ctx.actor.userId, params.projectId, params.membersToRemove)
        .as(EmptyResponse())
    },
    authRouteCatchError(getProjectMembers) { (params, ctx) =>
      dataExtractService.getProjectMembers(params.projectId, ctx.actor.userId)
    },
    authRouteCatchError(getAllUsers) { (_, ctx) => dataExtractService.getAllUsers(ctx.actor.userId) },
    authRouteCatchError(markProjectItemAsDone) { (params, ctx) =>
      dataExtractProjectItemService.markProjectItemAsDone(params.projectItemId, ctx.actor.userId).as(EmptyResponse())
    },
    authRouteCatchError(getProjectItemStatuses) { (params, ctx) =>
      dataExtractProjectItemService.getProjectItemStatuses(params.projectItemIds, ctx.actor.userId)
    },
    authRouteCatchError(getUserDocuments) { (params, ctx) =>
      dataExtractService.getUserDocuments(
        params.projectItemIds,
        params.includeNotes,
        ctx.actor.userId
      )
    },
    authRouteCatchError(setUserDocumentNotes) { (params, ctx) =>
      dataExtractService.setUserDocumentNotes(params.userDocumentId, params.notes, ctx.actor.userId).as(EmptyResponse())
    },
    authRouteCatchError(getUserDocumentStatuses) { (params, ctx) =>
      dataExtractService.getUserDocumentStatuses(params.userDocumentIds, ctx.actor.userId)
    },
    authRouteCatchError(markUserDocumentAsRejected) { (params, ctx) =>
      dataExtractService.markUserDocumentAsRejected(params.userDocumentId, ctx.actor.userId).as(EmptyResponse())
    },
    authRouteCatchError(resetUserDocumentStatus) { (params, ctx) =>
      dataExtractService.resetUserDocumentStatus(params.userDocumentId, ctx.actor.userId).as(EmptyResponse())
    },
    authRouteCatchError(renameProjectItem) { (params, ctx) =>
      dataExtractProjectItemService.renameProjectItem(params.projectItemId, params.newName, ctx.actor.userId)
    },
    authRouteCatchError(getProjectItemApplicableTemplates) { (params, ctx) =>
      dataExtractService.getProjectItemApplicableTemplates(params.projectItemId, ctx.actor.userId)
    },
    authRouteCatchError(setProjectItemsApplicableTemplates) { (params, ctx) =>
      dataExtractService
        .setProjectItemsApplicableTemplates(params.projectItemsApplicableTemplates, ctx.actor.userId)
        .as(EmptyResponse())
    },
    authRouteCatchError(setUserDocumentsApplicableTemplates) { (params, ctx) =>
      dataExtractService
        .setUserDocumentsApplicableTemplates(
          params.applicableTemplatesByUserDocument,
          params.userDocumentsToReject,
          ctx.actor.userId
        )
        .as(EmptyResponse())
    },
    authRouteCatchError(setProjectItemTags) { (params, ctx) =>
      dataExtractService.setProjectItemTags(params.projectItemId, params.updatedTags, ctx.actor.userId).as(EmptyResponse())
    },
    authRouteCatchError(setRepeatableTemplate) { (params, ctx) =>
      dataExtractService
        .setRepeatableTemplate(
          params.projectId,
          params.templateFileId,
          params.isRepeatable,
          ctx.actor.userId
        )
        .as(EmptyResponse())
    },
    authRouteCatchError(checkManageProjectMembersPermission) { (params, ctx) =>
      dataExtractService.checkManageProjectMembersPermission(params.projectId, ctx.actor.userId)
    },
    authRouteCatchError(createCueModule, FiniteDuration(2, TimeUnit.MINUTES)) { (params, ctx) =>
      dataExtractService.createCueModule(params.projectId, ctx.actor.userId)
    },
    authRouteCatchError(generateDataExtractCueModule) { (params, ctx) =>
      dataExtractService
        .generateDataExtractCueModule(params.projectId, params.selectedTemplateFileIds, ctx.actor.userId)
        .as(EmptyResponse())
    },
    authRouteCatchError(createCueMappingModule) { (params, ctx) =>
      dataExtractService.createCueMappingModule(params.projectId, ctx.actor.userId)
    },
    authRouteCatchError(updateTemplateAlias) { (params, ctx) =>
      dataExtractService
        .updateTemplateAlias(
          params.projectId,
          params.templateFileId,
          params.newAlias,
          ctx.actor.userId
        )
        .as(EmptyResponse())
    },
    authRouteCatchError(generateCueTableData, timeout = FiniteDuration(2, TimeUnit.MINUTES)) { (params, ctx) =>
      dataExtractService.generateCueTableData(params.projectItemId, params.cueModuleVersionId, ctx.actor.userId)
    },
    authRouteCatchError(getCueModuleVersions) { (params, ctx) =>
      dataExtractService.getCueModuleVersions(params.projectId, ctx.actor.userId)
    },
    authRouteCatchError(transformTableValues) { (params, ctx) =>
      dataExtractService.transformTableValues(
        params.projectId,
        params.cueModuleVersionId,
        params.commands,
        params.values,
        ctx.actor.userId
      )
    },
    authRouteCatchError(
      extractData,
      timeout = FiniteDuration(5, TimeUnit.MINUTES)
    ) { (params, ctx) =>
      dataExtractService.extractData(
        params.projectId,
        params.userDocFileId,
        params.templateFileId,
        params.annotationVersionId,
        params.userDocPages,
        params.templateFieldNames,
        ctx.actor.userId
      )
    },
    authRouteCatchError(generateDataExtractCueMappingModule) { (params, ctx) =>
      dataExtractService
        .generateDataExtractCueMappingModule(params.projectId, params.targetSaDataTemplateIds, ctx.actor.userId)
        .as(EmptyResponse())
    },
    authRouteCatchError(getAllSaDataTemplates) { (params, ctx) =>
      dataExtractService.getAllSaDataTemplates(params.projectId, ctx.actor.userId)
    }
  )

}
