package build.modules.dataextract

import build.modules.{fundsubFormData, gaia, heimdall}
import build.platform.{stargazerCore, stargazerTest}
import build_.build.util_.*
import mill.*
import mill.scalajslib.api.ModuleKind

import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object dataextractModel extends StargazerModelCrossPlatformModule {

    object jvm extends JvmModelModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        gaia.gaiaModel.jvm,
        stargazerCore.jvm,
        build.gondor.gondorModel.jvm,
        build.modules.sa.saModel.jvm
      )

    }

    object js extends JsModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        gaia.gaiaModel.js,
        stargazerCore.js,
        build.gondor.gondorModel.js,
        build.modules.sa.saModel.js
      )

    }

  }

  object dataextractCore extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        dataextractModel.jvm,
        fundsubFormData.fundsubFormData
      )

    }

    object js extends JsModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        dataextractModel.js,
        heimdall.heimdallCore.js
      )

    }

  }

  object dataextractIntegration extends Module {

    object jvm extends AnduinScalaModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        dataextractCore.jvm,
        build.gondor.gondorCore.jvm
      )

    }

  }

  object dataextract extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        dataextractCore.jvm,
        gaia.gaia.jvm,
        build.gondor.gondorCore.jvm,
        dataextractIntegration.jvm
      )

      object it extends AnduinZioTests with AnduinIntegTests {
        override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorTest, heimdall.heimdall.jvm)
      }

      object test extends JvmTests {

        override def moduleDeps = super.moduleDeps ++ Seq(stargazerTest.jvm)
      }

    }

    object js extends JsModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        dataextractCore.js,
        build.gondor.gondorCore.js,
        gaia.gaiaModel.js,
        gaia.gaia.js
      )

      object test extends JsTests {
        override def moduleKind: Target[ModuleKind] = Task(ModuleKind.CommonJSModule)
        override def moduleDeps = super.moduleDeps ++ Seq(stargazerTest.js)
      }

    }

  }

  object dataextractApp extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {
      override def moduleDeps = super.moduleDeps ++ Seq(dataextract.jvm)
    }

    object js extends JsModule with AnduinWebClientModule {
      override def webModule = AnduinWebModules.DataExtract
      override def mainClass = Some("anduin.dataextract.client.DataExtractMainApp")
      override def moduleDeps = super.moduleDeps ++ Seq(dataextract.js)
    }

  }

}
