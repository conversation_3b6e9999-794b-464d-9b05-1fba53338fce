// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataextract.client

import zio.Task

import anduin.dataextract.endpoint.DataExtractEndpoint
import anduin.dataextract.protocols.*
import anduin.service.GeneralServiceException
import anduin.tapir.client.AuthenticatedEndpointClient
import anduin.tapir.endpoint.{CommonParams, EmptyResponse}

object DataExtractEndpointClient extends AuthenticatedEndpointClient {

  val createDataExtractProject: CreateDataExtractProjectParams => Task[
    Either[GeneralServiceException, CreateDataExtractProjectResp]
  ] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.createDataExtractProject)
  }

  val addTemplateToProject
    : AddTemplateToProjectParams => Task[Either[GeneralServiceException, AddTemplateToProjectResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.addTemplateToProject)
  }

  val createProjectItems: CreateProjectItemsParams => Task[Either[GeneralServiceException, CreateProjectItemsResp]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.createProjectItems)
  }

  val generateDebugReport: GenerateDebugReportParams => Task[
    Either[GeneralServiceException, GenerateDebugReportResponse]
  ] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.generateDebugReport)
  }

  val getMappingResultFile
    : GetMappingResultWorkbookParams => Task[Either[GeneralServiceException, GetMappingResultWorkbookResp]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getMappingResultFile)
  }

  val updateTemplatesArchivedStatus: UpdateTemplatesArchivedStatusParams => Task[
    Either[GeneralServiceException, UpdateTemplatesArchivedStatusResponse]
  ] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.updateTemplatesArchivedStatus)
  }

  val removeProjectItems: RemoveDataExtractProjectItems => Task[Either[GeneralServiceException, Unit]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.removeProjectItems)
  }

  val getUserDocumentPageContent
    : GetUserDocumentPageContentParams => Task[Either[GeneralServiceException, GetUserDocumentPageContentResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getUserDocumentPageContent)
  }

  val getBestPageMatching
    : GetBestPageMatchingParams => Task[Either[GeneralServiceException, GetBestPageMatchingResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getBestPageMatching)

  }

  val getTopKSimilarPagesInUserDoc: GetTopKSimilarPagesInUserDocParams => Task[
    Either[GeneralServiceException, GetTopKSimilarPagesInUserDocResponse]
  ] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getTopKSimilarPagesInUserDoc)
  }

  val getReviewedMapping
    : GetReviewedMappingParams => Task[Either[GeneralServiceException, GetReviewedMappingResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getReviewedMapping)
  }

  val saveReviewedMapping: SaveReviewedMappingParams => Task[Either[GeneralServiceException, Unit]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.saveReviewedMapping)
  }

  val getAllErrorTags: GetAllErrorTagsParams => Task[Either[GeneralServiceException, GetAllErrorTagsResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getAllErrorTags)
  }

  val addErrorTag: AddErrorTagParams => Task[Either[GeneralServiceException, AddErrorTagResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.addErrorTag)
  }

  val removeErrorTag: RemoveErrorTagParams => Task[Either[GeneralServiceException, RemoveErrorTagResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.removeErrorTag)
  }

  val generateAnalyticsReport
    : GenerateAnalyticsReportParams => Task[Either[GeneralServiceException, GenerateAnalyticsReportResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.generateAnalyticsReport)
  }

  val getMappingContent: GetMappingContentParams => Task[Either[GeneralServiceException, GetMappingContentResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getMappingContent)
  }

  val getTemplateData: GetTemplateDataParams => Task[Either[GeneralServiceException, GetTemplateDataResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getTemplateData)
  }

  val renameProject: RenameProjectParams => Task[Either[GeneralServiceException, RenameProjectResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.renameProject)
  }

  val getProjectData: GetProjectDataParams => Task[Either[GeneralServiceException, GetProjectDataResp]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getProjectData)
  }

  // This API does not work for unreviewed data, please ensure reviewed mapping is complete before calling it
  val getFormImportData: GetFormImportDataParams => Task[Either[GeneralServiceException, GetFormImportDataResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getFormImportData)
  }

  // This API does not work for unreviewed data, please ensure reviewed mapping is complete before calling it
  val getFormImportDataWithTemplateMapping: GetFormImportDataWithTemplateMappingParams => Task[
    Either[GeneralServiceException, GetFormImportDataWithTemplateMappingResponse]
  ] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getFormImportDataWithTemplateMapping)
  }

  val getAccessibleProjects
    : GetAccessibleProjectsParams => Task[Either[GeneralServiceException, GetAccessibleProjectsResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getAccessibleProjects)
  }

  val runTextractForUserDocuments: RunTextractForUserDocumentsParams => Task[
    Either[GeneralServiceException, RunTextractForUserDocumentsResponse]
  ] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.runTextractForUserDocuments)
  }

  val getProjectItemImportedFormData: GetProjectItemImportedFormDataParams => Task[
    Either[GeneralServiceException, GetProjectItemImportedFormDataResponse]
  ] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getProjectItemImportedFormData)
  }

  val saveProjectItemImportedFormData
    : SaveProjectItemImportedFormDataParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.saveProjectItemImportedFormData)
  }

  val clearProjectItemImportedFormData
    : ClearProjectItemImportedFormDataParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.clearProjectItemImportedFormData)
  }

  val getFormVersionBasicInfo: GetFormVersionBasicInfoParams => Task[
    Either[GeneralServiceException, GetFormVersionBasicInfoResponse]
  ] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getFormVersionBasicInfo)
  }

  val getImportedFormVersionBasicInfo: GetImportedFormVersionBasicInfoParams => Task[
    Either[GeneralServiceException, GetImportedFormVersionBasicInfoResponse]
  ] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getImportedFormVersionBasicInfo)
  }

  val setProjectConfig: SetProjectConfigParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.setProjectConfig)
  }

  val getProjectBasicInfo
    : GetProjectBasicInfoParams => Task[Either[GeneralServiceException, GetProjectBasicInfoResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getProjectBasicInfo)
  }

  val addNewMemberToProject: AddNewMemberToProjectParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.addNewMemberToProject)
  }

  val removeMemberFromProject: RemoveMemberFromProjectParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.removeMemberFromProject)
  }

  val getProjectMembers: GetProjectMembersParams => Task[Either[GeneralServiceException, GetProjectMembersResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getProjectMembers)
  }

  val getAllUsers: CommonParams.Empty => Task[Either[GeneralServiceException, GetAllUsersResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getAllUsers)
  }

  val markProjectItemAsDone: MarkProjectItemAsDoneParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.markProjectItemAsDone)
  }

  val getProjectItemStatuses
    : GetProjectItemStatusesParams => Task[Either[GeneralServiceException, GetProjectItemStatusesResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getProjectItemStatuses)
  }

  val getUserDocuments: GetUserDocumentsParams => Task[Either[GeneralServiceException, GetUserDocumentsResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getUserDocuments)
  }

  val setUserDocumentNotes: SetUserDocumentNotesParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.setUserDocumentNotes)
  }

  val getUserDocumentStatuses
    : GetUserDocumentStatusesParams => Task[Either[GeneralServiceException, GetUserDocumentStatusesResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getUserDocumentStatuses)
  }

  val markUserDocumentAsRejected
    : MarkUserDocumentAsRejectedParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.markUserDocumentAsRejected)
  }

  val resetUserDocumentStatus: ResetUserDocumentStatusParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.resetUserDocumentStatus)
  }

  val renameProjectItem: RenameProjectItemParams => Task[Either[GeneralServiceException, RenameProjectItemResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.renameProjectItem)
  }

  val setProjectItemsApplicableTemplates
    : SetProjectItemsApplicableTemplatesParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.setProjectItemsApplicableTemplates)
  }

  val setUserDocumentsApplicableTemplates
    : SetUserDocumentsApplicableTemplatesParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.setUserDocumentsApplicableTemplates)
  }

  val setProjectItemTags: SetProjectItemTagsParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.setProjectItemTags)
  }

  val setRepeatableTemplate: SetRepeatableTemplateParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.setRepeatableTemplate)
  }

  val checkManageProjectMembersPermission: CheckManageProjectMembersPermissionParams => Task[
    Either[GeneralServiceException, CheckManageProjectMembersPermissionResponse]
  ] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.checkManageProjectMembersPermission)
  }

  val createCueModule: CreateCueModuleParams => Task[Either[GeneralServiceException, CreateCueModuleResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.createCueModule)
  }

  val generateDataExtractCueModule
    : GenerateDataExtractCueModuleParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.generateDataExtractCueModule)
  }

  val createCueMappingModule
    : CreateCueMappingModuleParams => Task[Either[GeneralServiceException, CreateCueMappingModuleResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.createCueMappingModule)
  }

  val updateTemplateAlias: UpdateTemplateAliasParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.updateTemplateAlias)
  }

  val generateCueTableData
    : GenerateCueTableDataParams => Task[Either[GeneralServiceException, GenerateCueTableDataResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.generateCueTableData)
  }

  val getCueModuleVersions
    : GetCueModuleVersionsParams => Task[Either[GeneralServiceException, GetCueModuleVersionsResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getCueModuleVersions)
  }

  val transformTableValues
    : TransformTableValuesParams => Task[Either[GeneralServiceException, TransformTableValuesResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.transformTableValues)
  }

  val extractData: ExtractDataParams => Task[Either[GeneralServiceException, ExtractDataResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.extractData)
  }

  val generateDataExtractCueMappingModule
    : GenerateDataExtractCueMappingModuleParams => Task[Either[GeneralServiceException, EmptyResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.generateDataExtractCueMappingModule)
  }

  val getAllSaDataTemplates
    : GetAllSaDataTemplatesParams => Task[Either[GeneralServiceException, GetAllSaDataTemplatesResponse]] = {
    toClientThrowDecodeAndSecurityFailures(DataExtractEndpoint.getAllSaDataTemplates)
  }

}
