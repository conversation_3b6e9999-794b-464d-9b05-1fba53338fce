// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataextract.protocols

import io.circe.{<PERSON><PERSON>, <PERSON><PERSON>}

import anduin.circe.generic.semiauto.{CirceCodec, deriveEnumCodec}
import anduin.cue.model.CueModuleSharedModels.{CueModule, CueModuleVersion}
import anduin.cue.model.commands.CueCommands.DataExtractCommands
import anduin.cue.model.dataextract.DataExtractCommonSchema.OcrFields
import anduin.cue.model.table.TableCommonSchema.{SoftValidationValueSchema, TableSchema}
import anduin.cue.model.table.TableSharedModels.SoftValidationValuesWithValidation
import anduin.dataextract.models.*
import anduin.dataextract.models.DataExtractSharedSchema.*
import anduin.forms.FormData
import anduin.forms.annotation.Area2D
import anduin.forms.engine.GaiaState
import anduin.forms.model.annotation.AnnotationDocumentModels.AnnotationDocumentVersionModel
import anduin.forms.model.mapping.FormTemplateMappingModels.MappingItem
import anduin.forms.model.template.DataTemplateModels.DataTemplateType
import anduin.id.annotation.AnnotationDocumentVersionId
import anduin.id.batchaction.BatchActionId
import anduin.id.cue.CueModuleVersionId
import anduin.id.dataextract.{DataExtractProjectId, DataExtractProjectItemId, DataExtractUserDocumentId}
import anduin.id.form.{FormTemplateMappingVersionId, FormVersionId}
import anduin.id.sa.SaDataTemplateId
import anduin.model.codec.MapCodecs.given
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.FileId
import anduin.sa.model.sadatatemplate.sadatatemplatemessage.SaDataTemplateMessage

final case class CreateDataExtractProjectParams(
  projectName: String,
  predefinedProjectIdOpt: Option[DataExtractProjectId] = None
) derives CirceCodec.WithDefaults

final case class CreateDataExtractProjectResp(
  projectInfo: DataExtractProjectBasicInfo
) derives CirceCodec.WithDefaults

final case class AddTemplateToProjectParams(
  projectId: DataExtractProjectId,
  versions: Seq[AnnotationDocumentVersionId]
) derives CirceCodec.WithDefaults

final case class AddTemplateToProjectResponse(
  templates: Seq[TemplateModel],
  templateVersionMap: Map[AnnotationDocumentVersionId, AnnotationDocumentVersionModel]
) derives CirceCodec.WithDefaults

final case class CreateProjectItemsParams(
  projectId: DataExtractProjectId,
  projectItemInfos: Seq[ProjectItemInfo]
) derives CirceCodec.WithDefaults

final case class ProjectItemInfo(
  nameOpt: Option[String],
  userDocumentFileIds: Seq[FileId]
) derives CirceCodec.WithDefaults

final case class CreateProjectItemsResp(
  projectItems: Seq[DataExtractProjectItem]
) derives CirceCodec.WithDefaults

final case class GetProjectDataParams(
  projectId: DataExtractProjectId,
  includeDeletedProjectItems: Boolean = false
) derives CirceCodec.WithDefaults

final case class GetProjectDataResp(
  items: Seq[DataExtractProjectItem],
  templates: Seq[TemplateModel],
  templateVersionMap: Map[AnnotationDocumentVersionId, AnnotationDocumentVersionModel],
  configOpt: Option[ProjectConfig],
  userInfoMap: Map[UserId, UserInfo],
  cueModuleOpt: Option[CueModule],
  cueMappingModuleOpt: Option[CueModule]
) derives CirceCodec.WithDefaults

final case class RemoveProjectTemplateParams(
  projectId: DataExtractProjectId,
  templates: Seq[FileId]
) derives CirceCodec.WithDefaults

final case class UpdateTemplatesArchivedStatusParams(
  projectId: DataExtractProjectId,
  templates: Seq[FileId],
  newArchivedStatus: Boolean
) derives CirceCodec.WithDefaults

final case class UpdateTemplatesArchivedStatusResponse(
  templates: Seq[TemplateModel]
) derives CirceCodec.WithDefaults

final case class RemoveDataExtractProjectItems(
  projectItemIds: Seq[DataExtractProjectItemId]
) derives CirceCodec.WithDefaults

final case class GetBestPageMatchingParams(
  userDocumentIds: Seq[DataExtractUserDocumentId]
) derives CirceCodec.WithDefaults

final case class GetBestPageMatchingResponse(
  bestPageMatchingResults: Seq[PageMatchingResult]
) derives CirceCodec.WithDefaults

final case class GetTopKSimilarPagesInUserDocParams(
  userDocumentId: DataExtractUserDocumentId,
  templateFileId: FileId,
  topK: Int
) derives CirceCodec.WithDefaults

final case class GetTopKSimilarPagesInUserDocResponse(
  similarityScores: Seq[Seq[(Int, Float)]]
) derives CirceCodec.WithDefaults

final case class GetUserDocumentPageContentParams(
  userDocumentId: DataExtractUserDocumentId,
  userDocumentPage: Int
) derives CirceCodec.WithDefaults

final case class GetUserDocumentPageContentResponse(textractBlocks: Seq[MappingExtractedContent])
    derives CirceCodec.WithDefaults

final case class GetReviewedMappingParams(
  projectItemId: DataExtractProjectItemId,
  templateFileId: FileId
) derives CirceCodec.WithDefaults

final case class GetReviewedMappingResponse(reviewedMapping: ReviewedMapping) derives CirceCodec.WithDefaults

final case class SaveReviewedMappingParams(
  projectItemId: DataExtractProjectItemId,
  templateFileId: FileId,
  reviewedMapping: ReviewedMapping
) derives CirceCodec.WithDefaults

final case class GetAllErrorTagsParams(projectId: DataExtractProjectId) derives CirceCodec.WithDefaults

final case class GetAllErrorTagsResponse(allErrorTags: Set[String]) derives CirceCodec.WithDefaults

final case class AddErrorTagParams(projectId: DataExtractProjectId, tag: String) derives CirceCodec.WithDefaults

final case class AddErrorTagResponse(allErrorTags: Set[String]) derives CirceCodec.WithDefaults

final case class RemoveErrorTagParams(projectId: DataExtractProjectId, tag: String) derives CirceCodec.WithDefaults

final case class RemoveErrorTagResponse(allErrorTags: Set[String]) derives CirceCodec.WithDefaults

final case class GenerateAnalyticsReportParams(projectIds: Set[DataExtractProjectId]) derives CirceCodec.WithDefaults

final case class GenerateAnalyticsReportResponse(batchActionId: BatchActionId) derives CirceCodec.WithDefaults

enum DebugReportDataPoint(val label: String) {
  case MappingContentAlgorithmVersion extends DebugReportDataPoint("Mapping algorithm")
  case DocumentName extends DebugReportDataPoint("Document name")
  case TemplateName extends DebugReportDataPoint("Template name")
  case DocumentStatus extends DebugReportDataPoint("Document status")
  case BlockId extends DebugReportDataPoint("Textract block ID")
  case BlockConfidence extends DebugReportDataPoint("Block confidence")
  case BlockValue extends DebugReportDataPoint("Block value")
  case MappedFieldName extends DebugReportDataPoint("Mapped field name")
  case ValuePreReview extends DebugReportDataPoint("Value (pre-review)")
  case ValuePostReview extends DebugReportDataPoint("Value (post-review)")
  case Tags extends DebugReportDataPoint("Tags")
  case UserDocumentPage extends DebugReportDataPoint("Document page no.")
  case TemplatePage extends DebugReportDataPoint("Template page no.")
  case OverlappingValue extends DebugReportDataPoint("Overlapping value")
  case BlockType extends DebugReportDataPoint("Block type")
  case KeyText extends DebugReportDataPoint("Key block text")
  case KeyConfidence extends DebugReportDataPoint("Key block confidence")
  case TextHighlight extends DebugReportDataPoint("Text highlight")
  case IsRemapped extends DebugReportDataPoint("Is remapped?")
  case IsIgnored extends DebugReportDataPoint("Is ignored?")
}

object DebugReportDataPoint {
  given Codec[DebugReportDataPoint] = deriveEnumCodec
}

enum MappingResultDataPoint(val label: String) {
  case Template extends MappingResultDataPoint("Template")
  case FieldNumber extends MappingResultDataPoint("Field no.")
  case PageNumber extends MappingResultDataPoint("Page no.")
  case GroupName extends MappingResultDataPoint("Group field")
  case OptionName extends MappingResultDataPoint("Option field")
  case GroupLabel extends MappingResultDataPoint("Group label")
  case OptionLabel extends MappingResultDataPoint("Option label")
  case Value extends MappingResultDataPoint("Value")
  case Tags extends MappingResultDataPoint("Tags")
  case ConfidenceLevel extends MappingResultDataPoint("Confidence level")
  case Remark extends MappingResultDataPoint("Remark")
}

object MappingResultDataPoint {
  given Codec[MappingResultDataPoint] = deriveEnumCodec
}

enum MappingResultSheetType(val label: String) {
  case ImportTemplate extends MappingResultSheetType("Import template")
  case RequestTracker extends MappingResultSheetType("Request tracker")
  case ProjectItems extends MappingResultSheetType("Individual project items")
}

object MappingResultSheetType {

  def toInt(sheetType: MappingResultSheetType): Int = sheetType match {
    case MappingResultSheetType.ImportTemplate => 1
    case MappingResultSheetType.RequestTracker => 2
    case MappingResultSheetType.ProjectItems   => 3
  }

  given Codec[MappingResultSheetType] = deriveEnumCodec
}

/////////////////////////////////////////////////////////////////////
// Params for DataExtractBatchActionService & Workflow/Activity Params
/////////////////////////////////////////////////////////////////////

final case class GenerateDebugReportParams(
  projectId: DataExtractProjectId,
  projectName: String,
  projectItems: Set[DataExtractProjectItemId],
  includedDataPoints: Set[DebugReportDataPoint],
  thresholds: DataExtractThresholds,
  mappingContentAlgorithmVersion: MappingContentAlgorithmVersion
) derives CirceCodec.WithDefaults

final case class GetMappingResultWorkbookParams(
  projectId: DataExtractProjectId,
  projectName: String,
  dataExtractProjectItemIds: Seq[DataExtractProjectItemId],
  overlappingThresholdOpt: Option[Float],
  contentOverlapToRemoveThresholdOpt: Option[Float],
  confidenceThresholdOpt: Option[Float],
  includedSheetTypes: Set[MappingResultSheetType],
  includedDataPoints: Set[MappingResultDataPoint],
  tags: Set[String],
  mappingContentAlgorithmVersion: MappingContentAlgorithmVersion
) derives CirceCodec.WithDefaults

final case class GetProjectItemMappingResultParams(
  projectItemId: DataExtractProjectItemId,
  mappingContentAlgorithmVersion: MappingContentAlgorithmVersion
) derives CirceCodec.WithDefaults

final case class GetUserDocumentMappingResultResp(
  fileMappingContent: FileMappingContent
) derives CirceCodec.WithDefaults

final case class GetMappingResultWorkbookInBatchActionParams(params: GetProjectItemMappingResultParams)
    derives CirceCodec.WithDefaults

final case class GenerateDebugReportResponse(
  batchActionId: BatchActionId
) derives CirceCodec.WithDefaults

final case class GetMappingResultWorkbookResp(
  batchActionId: BatchActionId
) derives CirceCodec.WithDefaults

final case class ConstructMappingWorkbookResultParams(
  projectId: DataExtractProjectId,
  projectItemIds: Seq[DataExtractProjectItemId],
  projectName: String,
  listFileMappingContent: Seq[FileMappingContent],
  overlappingThresholdOpt: Option[Float],
  contentOverlapToRemoveThresholdOpt: Option[Float],
  confidenceThresholdOpt: Option[Float],
  includedSheetTypes: Set[MappingResultSheetType],
  includedDataPoints: Set[MappingResultDataPoint],
  tags: Set[String]
) derives CirceCodec.WithDefaults

final case class ConstructMappingWorkbookResp(
  fileId: FileId
) derives CirceCodec.WithDefaults

final case class GenerateMappingResultPostExecuteResponse(fileId: FileId) derives CirceCodec.WithDefaults

final case class GenerateDebugReportPostExecuteResponse(fileId: FileId) derives CirceCodec.WithDefaults

final case class GenerateAnalyticsReportPostExecuteResponse(fileId: FileId) derives CirceCodec.WithDefaults

final case class GetMappingContentParams(
  userDocumentIds: Seq[DataExtractUserDocumentId],
  pageMatches: Seq[PageMatch],
  algorithmVersion: MappingContentAlgorithmVersion
) derives CirceCodec.WithDefaults

final case class GetMappingContentResponse(
  mappingContent: Seq[OverlappedContent],
  transformedTemplateFieldAreas: Seq[(PageMatch, String, Area2D)]
) derives CirceCodec.WithDefaults

final case class GetTemplateDataParams(
  projectId: DataExtractProjectId,
  templateFileId: FileId
) derives CirceCodec.WithDefaults

final case class GetTemplateDataResponse(
  templateData: TemplateData
) derives CirceCodec.WithDefaults

final case class RenameProjectParams(
  projectId: DataExtractProjectId,
  name: String
) derives CirceCodec.WithDefaults

final case class RenameProjectResponse(
  projectInfo: DataExtractProjectBasicInfo
) derives CirceCodec.WithDefaults

final case class GetFormImportDataParams(
  projectItemId: DataExtractProjectItemId,
  formVersionId: FormVersionId
) derives CirceCodec.WithDefaults

final case class GetFormImportDataResponse(
  formData: FormData,
  pdfData: Map[String, PdfValue],
  importData: Map[String, Json]
) derives CirceCodec.WithDefaults

final case class GetAccessibleProjectsParams(
  // if this flag is false, super admins can get all project basic infos
  joinedProjectsOnly: Boolean
) derives CirceCodec.WithDefaults

final case class GetAccessibleProjectsResponse(
  projectInfos: Seq[DataExtractProjectBasicInfo]
) derives CirceCodec.WithDefaults

final case class GetFormImportDataWithTemplateMappingParams(
  projectItemId: DataExtractProjectItemId,
  formVersionId: FormVersionId,
  mappingVersionId: FormTemplateMappingVersionId
) derives CirceCodec.WithDefaults

final case class GetFormImportDataWithTemplateMappingResponse(
  formData: FormData,
  pdfData: Map[String, PdfValue],
  mappingItems: Seq[MappingItem],
  dataTemplateType: DataTemplateType
) derives CirceCodec.WithDefaults

final case class RunTextractForUserDocumentsParams(
  firstTextractUserDocumentIds: Seq[DataExtractUserDocumentId],
  secondTextractUserDocumentIds: Seq[DataExtractUserDocumentId],
  runTextractMode: RunTextractMode
) derives CirceCodec.WithDefaults

final case class RunTextractForUserDocumentsResponse(
  statusMap: Map[DataExtractUserDocumentId, DataExtractTextractStatus]
) derives CirceCodec.WithDefaults

final case class GetProjectItemImportedFormDataParams(
  projectItemId: DataExtractProjectItemId
) derives CirceCodec.WithDefaults

final case class GetProjectItemImportedFormDataResponse(
  pdfData: Map[String, PdfValue],
  formVersionId: FormVersionId,
  formData: FormData,
  gaiaState: GaiaState,
  importData: Map[String, Json],
  enableImportMode: Boolean,
  isReadOnly: Boolean
) derives CirceCodec.WithDefaults

final case class SaveProjectItemImportedFormDataParams(
  projectItemId: DataExtractProjectItemId,
  formVersionId: FormVersionId,
  gaiaState: GaiaState,
  importData: Map[String, Json],
  enableImportMode: Boolean
) derives CirceCodec.WithDefaults

final case class ClearProjectItemImportedFormDataParams(
  projectItemId: DataExtractProjectItemId
) derives CirceCodec.WithDefaults

final case class GetFormVersionBasicInfoParams(
  formVersionId: FormVersionId
) derives CirceCodec.WithDefaults

final case class GetFormVersionBasicInfoResponse(
  formName: String,
  formVersionName: String,
  formVersionNumber: Int
) derives CirceCodec.WithDefaults

final case class GetImportedFormVersionBasicInfoParams(
  projectItemId: DataExtractProjectItemId
) derives CirceCodec.WithDefaults

final case class GetImportedFormVersionBasicInfoResponse(
  formVersionId: FormVersionId,
  formName: String,
  formVersionName: String,
  formVersionNumber: Int
) derives CirceCodec.WithDefaults

final case class SetProjectConfigParams(
  projectId: DataExtractProjectId,
  config: ProjectConfig
) derives CirceCodec.WithDefaults

final case class GetProjectBasicInfoParams(
  projectId: DataExtractProjectId
) derives CirceCodec.WithDefaults

final case class GetProjectBasicInfoResponse(
  projectInfo: DataExtractProjectBasicInfo
) derives CirceCodec.WithDefaults

final case class AddNewMemberToProjectParams(
  projectId: DataExtractProjectId,
  newMembers: Set[UserId]
) derives CirceCodec.WithDefaults

final case class RemoveMemberFromProjectParams(
  projectId: DataExtractProjectId,
  membersToRemove: Set[UserId]
) derives CirceCodec.WithDefaults

final case class GetProjectMembersParams(
  projectId: DataExtractProjectId
) derives CirceCodec.WithDefaults

final case class GetProjectMembersResponse(
  members: Seq[(UserId, UserInfo)]
) derives CirceCodec.WithDefaults

final case class GetAllUsersResponse(
  users: Seq[(UserId, UserInfo)]
) derives CirceCodec.WithDefaults

final case class MarkProjectItemAsDoneParams(
  projectItemId: DataExtractProjectItemId
) derives CirceCodec.WithDefaults

final case class GetProjectItemStatusesParams(
  projectItemIds: Seq[DataExtractProjectItemId]
) derives CirceCodec.WithDefaults

final case class GetProjectItemStatusesResponse(
  statuses: Seq[(DataExtractProjectItemId, DataExtractProjectItemStatus)]
) derives CirceCodec.WithDefaults

final case class GetUserDocumentsParams(
  projectItemIds: Seq[DataExtractProjectItemId],
  includeNotes: Boolean
) derives CirceCodec.WithDefaults

final case class GetUserDocumentsResponse(
  userDocuments: Seq[DataExtractUserDocument]
) derives CirceCodec.WithDefaults

final case class SetUserDocumentNotesParams(
  userDocumentId: DataExtractUserDocumentId,
  notes: String
) derives CirceCodec.WithDefaults

final case class GetUserDocumentStatusesParams(
  userDocumentIds: Seq[DataExtractUserDocumentId]
) derives CirceCodec.WithDefaults

final case class GetUserDocumentStatusesResponse(
  statuses: Seq[(DataExtractUserDocumentId, DataExtractUserDocumentStatus)]
) derives CirceCodec.WithDefaults

final case class MarkUserDocumentAsRejectedParams(
  userDocumentId: DataExtractUserDocumentId
) derives CirceCodec.WithDefaults

final case class ResetUserDocumentStatusParams(
  userDocumentId: DataExtractUserDocumentId
) derives CirceCodec.WithDefaults

final case class RenameProjectItemParams(
  projectItemId: DataExtractProjectItemId,
  newName: String
) derives CirceCodec.WithDefaults

final case class RenameProjectItemResponse(
  updatedProjectItem: DataExtractProjectItem
) derives CirceCodec.WithDefaults

final case class GetProjectItemApplicableTemplatesParams(
  projectItemId: DataExtractProjectItemId
) derives CirceCodec.WithDefaults

final case class GetProjectItemApplicableTemplatesResponse(
  applicableTemplates: Set[FileId]
) derives CirceCodec.WithDefaults

final case class SetProjectItemsApplicableTemplatesParams(
  projectItemsApplicableTemplates: Seq[(DataExtractProjectItemId, Set[FileId])]
) derives CirceCodec.WithDefaults

final case class SetUserDocumentsApplicableTemplatesParams(
  applicableTemplatesByUserDocument: Map[DataExtractUserDocumentId, Set[FileId]],
  userDocumentsToReject: Set[DataExtractUserDocumentId]
) derives CirceCodec.WithDefaults

final case class SetProjectItemTagsParams(
  projectItemId: DataExtractProjectItemId,
  updatedTags: Seq[String]
) derives CirceCodec.WithDefaults

final case class SetRepeatableTemplateParams(
  projectId: DataExtractProjectId,
  templateFileId: FileId,
  isRepeatable: Boolean
) derives CirceCodec.WithDefaults

final case class CheckManageProjectMembersPermissionParams(
  projectId: DataExtractProjectId
) derives CirceCodec.WithDefaults

final case class CheckManageProjectMembersPermissionResponse(
  hasPermission: Boolean
) derives CirceCodec.WithDefaults

final case class CreateCueModuleParams(
  projectId: DataExtractProjectId
) derives CirceCodec.WithDefaults

final case class CreateCueModuleResponse(
  cueModule: CueModule
) derives CirceCodec.WithDefaults

final case class GenerateDataExtractCueModuleParams(
  projectId: DataExtractProjectId,
  selectedTemplateFileIds: Seq[FileId]
) derives CirceCodec.WithDefaults

final case class CreateCueMappingModuleParams(
  projectId: DataExtractProjectId
) derives CirceCodec.WithDefaults

final case class CreateCueMappingModuleResponse(
  cueModule: CueModule
) derives CirceCodec.WithDefaults

final case class GenerateDataExtractCueMappingModuleParams(
  projectId: DataExtractProjectId,
  targetSaDataTemplateIds: Seq[SaDataTemplateId]
) derives CirceCodec.WithDefaults

final case class UpdateTemplateAliasParams(
  projectId: DataExtractProjectId,
  templateFileId: FileId,
  newAlias: String
) derives CirceCodec.WithDefaults

final case class GenerateCueTableDataParams(
  projectItemId: DataExtractProjectItemId,
  cueModuleVersionId: CueModuleVersionId
) derives CirceCodec.WithDefaults

final case class GenerateCueTableDataResponse(
  commands: DataExtractCommands,
  ocrFields: OcrFields,
  tableSchemas: Seq[TableSchema],
  valuesWithValidation: SoftValidationValuesWithValidation,
  templateFileIdMap: Map[String, FileId],
  userDocumentFileIdMap: Map[String, FileId],
  templateDataMap: Map[String, TemplateData]
) derives CirceCodec.WithDefaults

final case class GetCueModuleVersionsParams(
  projectId: DataExtractProjectId
) derives CirceCodec.WithDefaults

final case class GetCueModuleVersionsResponse(
  cueModuleVersions: Seq[CueModuleVersion]
) derives CirceCodec.WithDefaults

final case class TransformTableValuesParams(
  projectId: DataExtractProjectId,
  cueModuleVersionId: CueModuleVersionId,
  commands: DataExtractCommands,
  values: SoftValidationValueSchema
) derives CirceCodec.WithDefaults

final case class TransformTableValuesResponse(
  newValuesWithValidation: SoftValidationValuesWithValidation
) derives CirceCodec.WithDefaults

final case class ExtractDataParams(
  projectId: DataExtractProjectId,
  userDocFileId: FileId,
  templateFileId: FileId,
  annotationVersionId: AnnotationDocumentVersionId,
  userDocPages: Set[Int],
  templateFieldNames: Set[String]
) derives CirceCodec.WithDefaults

final case class ExtractDataResponse(
  newValueByFieldName: Map[String, String]
) derives CirceCodec.WithDefaults

final case class GetAllSaDataTemplatesParams(projectId: DataExtractProjectId) derives CirceCodec.WithDefaults

final case class GetAllSaDataTemplatesResponse(templateList: List[SaDataTemplateMessage])
    derives CirceCodec.WithDefaults
