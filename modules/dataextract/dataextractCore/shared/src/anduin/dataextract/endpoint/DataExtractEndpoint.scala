// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataextract.endpoint

import sttp.tapir.*

import anduin.dataextract.protocols.*
import anduin.service.GeneralServiceException
import anduin.tapir.AuthenticatedEndpoints
import anduin.tapir.AuthenticatedEndpoints.BaseAuthenticatedEndpoint
import anduin.tapir.endpoint.{CommonParams, EmptyResponse}

object DataExtractEndpoint extends AuthenticatedEndpoints {

  protected val ProjectPath = "deproject"

  val createDataExtractProject: BaseAuthenticatedEndpoint[
    CreateDataExtractProjectParams,
    GeneralServiceException,
    CreateDataExtractProjectResp
  ] = {
    authEndpoint[
      CreateDataExtractProjectParams,
      GeneralServiceException,
      CreateDataExtractProjectResp
    ](
      ProjectPath / "createDataExtractProject"
    )
  }

  val addTemplateToProject: BaseAuthenticatedEndpoint[
    AddTemplateToProjectParams,
    GeneralServiceException,
    AddTemplateToProjectResponse
  ] = {
    authEndpoint[
      AddTemplateToProjectParams,
      GeneralServiceException,
      AddTemplateToProjectResponse
    ](
      ProjectPath / "uploadTemplatesToProject"
    )
  }

  val createProjectItems: BaseAuthenticatedEndpoint[
    CreateProjectItemsParams,
    GeneralServiceException,
    CreateProjectItemsResp
  ] = {
    authEndpoint[
      CreateProjectItemsParams,
      GeneralServiceException,
      CreateProjectItemsResp
    ](
      ProjectPath / "createProjectItems"
    )
  }

  val getProjectData: BaseAuthenticatedEndpoint[
    GetProjectDataParams,
    GeneralServiceException,
    GetProjectDataResp
  ] = {
    authEndpoint[
      GetProjectDataParams,
      GeneralServiceException,
      GetProjectDataResp
    ](
      ProjectPath / "getProjectData"
    )
  }

  val generateDebugReport: BaseAuthenticatedEndpoint[
    GenerateDebugReportParams,
    GeneralServiceException,
    GenerateDebugReportResponse
  ] = {
    authEndpoint[
      GenerateDebugReportParams,
      GeneralServiceException,
      GenerateDebugReportResponse
    ](
      ProjectPath / "generateDebugReport"
    )
  }

  val getMappingResultFile: BaseAuthenticatedEndpoint[
    GetMappingResultWorkbookParams,
    GeneralServiceException,
    GetMappingResultWorkbookResp
  ] = {
    authEndpoint[
      GetMappingResultWorkbookParams,
      GeneralServiceException,
      GetMappingResultWorkbookResp
    ](
      ProjectPath / "getMappingResultFile"
    )
  }

  val generateAnalyticsReport: BaseAuthenticatedEndpoint[
    GenerateAnalyticsReportParams,
    GeneralServiceException,
    GenerateAnalyticsReportResponse
  ] = {
    authEndpoint[
      GenerateAnalyticsReportParams,
      GeneralServiceException,
      GenerateAnalyticsReportResponse
    ](
      ProjectPath / "generateAnalyticsReport"
    )
  }

  val updateTemplatesArchivedStatus: BaseAuthenticatedEndpoint[
    UpdateTemplatesArchivedStatusParams,
    GeneralServiceException,
    UpdateTemplatesArchivedStatusResponse
  ] = {
    authEndpoint[
      UpdateTemplatesArchivedStatusParams,
      GeneralServiceException,
      UpdateTemplatesArchivedStatusResponse
    ](
      ProjectPath / "updateTemplatesArchivedStatus"
    )
  }

  val removeProjectItems: BaseAuthenticatedEndpoint[RemoveDataExtractProjectItems, GeneralServiceException, Unit] = {
    authEndpoint[RemoveDataExtractProjectItems, GeneralServiceException, Unit](
      ProjectPath / "removeProjectItems"
    )
  }

  val getBestPageMatching: BaseAuthenticatedEndpoint[
    GetBestPageMatchingParams,
    GeneralServiceException,
    GetBestPageMatchingResponse
  ] = {
    authEndpoint[
      GetBestPageMatchingParams,
      GeneralServiceException,
      GetBestPageMatchingResponse
    ](ProjectPath / "getBestPageMatching")
  }

  val getTopKSimilarPagesInUserDoc: BaseAuthenticatedEndpoint[
    GetTopKSimilarPagesInUserDocParams,
    GeneralServiceException,
    GetTopKSimilarPagesInUserDocResponse
  ] = {
    authEndpoint[
      GetTopKSimilarPagesInUserDocParams,
      GeneralServiceException,
      GetTopKSimilarPagesInUserDocResponse
    ](ProjectPath / "getTopKSimilarPagesInUserDoc")
  }

  val getUserDocumentPageContent: BaseAuthenticatedEndpoint[
    GetUserDocumentPageContentParams,
    GeneralServiceException,
    GetUserDocumentPageContentResponse
  ] = {
    authEndpoint[
      GetUserDocumentPageContentParams,
      GeneralServiceException,
      GetUserDocumentPageContentResponse
    ](ProjectPath / "getUserDocumentPageContent")
  }

  val getReviewedMapping: BaseAuthenticatedEndpoint[
    GetReviewedMappingParams,
    GeneralServiceException,
    GetReviewedMappingResponse
  ] = {
    authEndpoint[
      GetReviewedMappingParams,
      GeneralServiceException,
      GetReviewedMappingResponse
    ](ProjectPath / "getReviewedMapping")
  }

  val saveReviewedMapping: BaseAuthenticatedEndpoint[SaveReviewedMappingParams, GeneralServiceException, Unit] = {
    authEndpoint[SaveReviewedMappingParams, GeneralServiceException, Unit](ProjectPath / "saveReviewedMapping")
  }

  val getAllErrorTags: BaseAuthenticatedEndpoint[
    GetAllErrorTagsParams,
    GeneralServiceException,
    GetAllErrorTagsResponse
  ] = {
    authEndpoint[
      GetAllErrorTagsParams,
      GeneralServiceException,
      GetAllErrorTagsResponse
    ](
      ProjectPath / "getAllErrorTags"
    )
  }

  val addErrorTag: BaseAuthenticatedEndpoint[AddErrorTagParams, GeneralServiceException, AddErrorTagResponse] = {
    authEndpoint[AddErrorTagParams, GeneralServiceException, AddErrorTagResponse](ProjectPath / "addErrorTag")
  }

  val removeErrorTag: BaseAuthenticatedEndpoint[RemoveErrorTagParams, GeneralServiceException, RemoveErrorTagResponse] = {
    authEndpoint[RemoveErrorTagParams, GeneralServiceException, RemoveErrorTagResponse](ProjectPath / "removeErrorTag")
  }

  val getMappingContent: BaseAuthenticatedEndpoint[
    GetMappingContentParams,
    GeneralServiceException,
    GetMappingContentResponse
  ] = {
    authEndpoint[
      GetMappingContentParams,
      GeneralServiceException,
      GetMappingContentResponse
    ](ProjectPath / "getMappingContent")
  }

  val getTemplateData: BaseAuthenticatedEndpoint[
    GetTemplateDataParams,
    GeneralServiceException,
    GetTemplateDataResponse
  ] = {
    authEndpoint[
      GetTemplateDataParams,
      GeneralServiceException,
      GetTemplateDataResponse
    ](
      ProjectPath / "getTemplateData"
    )
  }

  val renameProject: BaseAuthenticatedEndpoint[RenameProjectParams, GeneralServiceException, RenameProjectResponse] = {
    authEndpoint[RenameProjectParams, GeneralServiceException, RenameProjectResponse](
      ProjectPath / "renameProject"
    )
  }

  val getFormImportData: BaseAuthenticatedEndpoint[
    GetFormImportDataParams,
    GeneralServiceException,
    GetFormImportDataResponse
  ] = {
    authEndpoint[
      GetFormImportDataParams,
      GeneralServiceException,
      GetFormImportDataResponse
    ](ProjectPath / "getFormImportData")
  }

  val getFormImportDataWithTemplateMapping: BaseAuthenticatedEndpoint[
    GetFormImportDataWithTemplateMappingParams,
    GeneralServiceException,
    GetFormImportDataWithTemplateMappingResponse
  ] = {
    authEndpoint[
      GetFormImportDataWithTemplateMappingParams,
      GeneralServiceException,
      GetFormImportDataWithTemplateMappingResponse
    ](ProjectPath / "getFormImportDataWithTemplateMapping")
  }

  val getAccessibleProjects: BaseAuthenticatedEndpoint[
    GetAccessibleProjectsParams,
    GeneralServiceException,
    GetAccessibleProjectsResponse
  ] = {
    authEndpoint[
      GetAccessibleProjectsParams,
      GeneralServiceException,
      GetAccessibleProjectsResponse
    ](ProjectPath / "getAccessibleProjects")
  }

  val runTextractForUserDocuments: BaseAuthenticatedEndpoint[
    RunTextractForUserDocumentsParams,
    GeneralServiceException,
    RunTextractForUserDocumentsResponse
  ] = {
    authEndpoint[
      RunTextractForUserDocumentsParams,
      GeneralServiceException,
      RunTextractForUserDocumentsResponse
    ](ProjectPath / "runTextractForUserDocuments")
  }

  val getProjectItemImportedFormData: BaseAuthenticatedEndpoint[
    GetProjectItemImportedFormDataParams,
    GeneralServiceException,
    GetProjectItemImportedFormDataResponse
  ] = {
    authEndpoint[
      GetProjectItemImportedFormDataParams,
      GeneralServiceException,
      GetProjectItemImportedFormDataResponse
    ](ProjectPath / "getProjectItemImportedFormData")
  }

  val saveProjectItemImportedFormData: BaseAuthenticatedEndpoint[
    SaveProjectItemImportedFormDataParams,
    GeneralServiceException,
    EmptyResponse
  ] = {
    authEndpoint[
      SaveProjectItemImportedFormDataParams,
      GeneralServiceException,
      EmptyResponse
    ](ProjectPath / "saveProjectItemImportedFormData")
  }

  val clearProjectItemImportedFormData: BaseAuthenticatedEndpoint[
    ClearProjectItemImportedFormDataParams,
    GeneralServiceException,
    EmptyResponse
  ] = {
    authEndpoint[
      ClearProjectItemImportedFormDataParams,
      GeneralServiceException,
      EmptyResponse
    ](ProjectPath / "clearProjectItemImportedFormData")
  }

  val getFormVersionBasicInfo: BaseAuthenticatedEndpoint[
    GetFormVersionBasicInfoParams,
    GeneralServiceException,
    GetFormVersionBasicInfoResponse
  ] = {
    authEndpoint[
      GetFormVersionBasicInfoParams,
      GeneralServiceException,
      GetFormVersionBasicInfoResponse
    ](ProjectPath / "getFormVersionBasicInfo")
  }

  val getImportedFormVersionBasicInfo: BaseAuthenticatedEndpoint[
    GetImportedFormVersionBasicInfoParams,
    GeneralServiceException,
    GetImportedFormVersionBasicInfoResponse
  ] = {
    authEndpoint[
      GetImportedFormVersionBasicInfoParams,
      GeneralServiceException,
      GetImportedFormVersionBasicInfoResponse
    ](ProjectPath / "getImportedFormVersionBasicInfo")
  }

  val setProjectConfig: BaseAuthenticatedEndpoint[SetProjectConfigParams, GeneralServiceException, EmptyResponse] = {
    authEndpoint[SetProjectConfigParams, GeneralServiceException, EmptyResponse](ProjectPath / "setProjectConfig")
  }

  val getProjectBasicInfo: BaseAuthenticatedEndpoint[
    GetProjectBasicInfoParams,
    GeneralServiceException,
    GetProjectBasicInfoResponse
  ] = {
    authEndpoint[
      GetProjectBasicInfoParams,
      GeneralServiceException,
      GetProjectBasicInfoResponse
    ](ProjectPath / "getProjectBasicInfo")
  }

  val addNewMemberToProject
    : BaseAuthenticatedEndpoint[AddNewMemberToProjectParams, GeneralServiceException, EmptyResponse] = {
    authEndpoint[AddNewMemberToProjectParams, GeneralServiceException, EmptyResponse](
      ProjectPath / "addNewMemberToProject"
    )
  }

  val removeMemberFromProject
    : BaseAuthenticatedEndpoint[RemoveMemberFromProjectParams, GeneralServiceException, EmptyResponse] = {
    authEndpoint[RemoveMemberFromProjectParams, GeneralServiceException, EmptyResponse](
      ProjectPath / "removeMemberFromProject"
    )
  }

  val getProjectMembers: BaseAuthenticatedEndpoint[
    GetProjectMembersParams,
    GeneralServiceException,
    GetProjectMembersResponse
  ] = {
    authEndpoint[
      GetProjectMembersParams,
      GeneralServiceException,
      GetProjectMembersResponse
    ](ProjectPath / "getProjectMembers")
  }

  val getAllUsers: BaseAuthenticatedEndpoint[
    CommonParams.Empty,
    GeneralServiceException,
    GetAllUsersResponse
  ] = {
    authEndpoint[
      CommonParams.Empty,
      GeneralServiceException,
      GetAllUsersResponse
    ](ProjectPath / "getAllUsers")
  }

  val markProjectItemAsDone
    : BaseAuthenticatedEndpoint[MarkProjectItemAsDoneParams, GeneralServiceException, EmptyResponse] = {
    authEndpoint[MarkProjectItemAsDoneParams, GeneralServiceException, EmptyResponse](
      ProjectPath / "markProjectItemAsDone"
    )
  }

  val getProjectItemStatuses: BaseAuthenticatedEndpoint[
    GetProjectItemStatusesParams,
    GeneralServiceException,
    GetProjectItemStatusesResponse
  ] = {
    authEndpoint[
      GetProjectItemStatusesParams,
      GeneralServiceException,
      GetProjectItemStatusesResponse
    ](ProjectPath / "getProjectItemStatuses")
  }

  val getUserDocuments: BaseAuthenticatedEndpoint[
    GetUserDocumentsParams,
    GeneralServiceException,
    GetUserDocumentsResponse
  ] = {
    authEndpoint[
      GetUserDocumentsParams,
      GeneralServiceException,
      GetUserDocumentsResponse
    ](ProjectPath / "getUserDocuments")
  }

  val setUserDocumentNotes
    : BaseAuthenticatedEndpoint[SetUserDocumentNotesParams, GeneralServiceException, EmptyResponse] = {
    authEndpoint[SetUserDocumentNotesParams, GeneralServiceException, EmptyResponse](
      ProjectPath / "setUserDocumentNotes"
    )
  }

  val getUserDocumentStatuses: BaseAuthenticatedEndpoint[
    GetUserDocumentStatusesParams,
    GeneralServiceException,
    GetUserDocumentStatusesResponse
  ] = {
    authEndpoint[
      GetUserDocumentStatusesParams,
      GeneralServiceException,
      GetUserDocumentStatusesResponse
    ](ProjectPath / "getUserDocumentStatuses")
  }

  val markUserDocumentAsRejected: BaseAuthenticatedEndpoint[
    MarkUserDocumentAsRejectedParams,
    GeneralServiceException,
    EmptyResponse
  ] = {
    authEndpoint[
      MarkUserDocumentAsRejectedParams,
      GeneralServiceException,
      EmptyResponse
    ](ProjectPath / "markUserDocumentAsRejected")
  }

  val resetUserDocumentStatus
    : BaseAuthenticatedEndpoint[ResetUserDocumentStatusParams, GeneralServiceException, EmptyResponse] = {
    authEndpoint[ResetUserDocumentStatusParams, GeneralServiceException, EmptyResponse](
      ProjectPath / "resetUserDocumentStatus"
    )
  }

  val renameProjectItem: BaseAuthenticatedEndpoint[
    RenameProjectItemParams,
    GeneralServiceException,
    RenameProjectItemResponse
  ] = {
    authEndpoint[
      RenameProjectItemParams,
      GeneralServiceException,
      RenameProjectItemResponse
    ](ProjectPath / "renameProjectItem")
  }

  val getProjectItemApplicableTemplates: BaseAuthenticatedEndpoint[
    GetProjectItemApplicableTemplatesParams,
    GeneralServiceException,
    GetProjectItemApplicableTemplatesResponse
  ] = {
    authEndpoint[
      GetProjectItemApplicableTemplatesParams,
      GeneralServiceException,
      GetProjectItemApplicableTemplatesResponse
    ](ProjectPath / "getProjectItemApplicableTemplates")
  }

  val setProjectItemsApplicableTemplates: BaseAuthenticatedEndpoint[
    SetProjectItemsApplicableTemplatesParams,
    GeneralServiceException,
    EmptyResponse
  ] = {
    authEndpoint[
      SetProjectItemsApplicableTemplatesParams,
      GeneralServiceException,
      EmptyResponse
    ](ProjectPath / "setProjectItemsApplicableTemplates")
  }

  val setUserDocumentsApplicableTemplates: BaseAuthenticatedEndpoint[
    SetUserDocumentsApplicableTemplatesParams,
    GeneralServiceException,
    EmptyResponse
  ] = {
    authEndpoint[
      SetUserDocumentsApplicableTemplatesParams,
      GeneralServiceException,
      EmptyResponse
    ](ProjectPath / "setUserDocumentsApplicableTemplates")
  }

  val setProjectItemTags: BaseAuthenticatedEndpoint[
    SetProjectItemTagsParams,
    GeneralServiceException,
    EmptyResponse
  ] = {
    authEndpoint[
      SetProjectItemTagsParams,
      GeneralServiceException,
      EmptyResponse
    ](ProjectPath / "setProjectItemTags")
  }

  val setRepeatableTemplate
    : BaseAuthenticatedEndpoint[SetRepeatableTemplateParams, GeneralServiceException, EmptyResponse] = {
    authEndpoint[SetRepeatableTemplateParams, GeneralServiceException, EmptyResponse](
      ProjectPath / "setRepeatableTemplate"
    )
  }

  val checkManageProjectMembersPermission: BaseAuthenticatedEndpoint[
    CheckManageProjectMembersPermissionParams,
    GeneralServiceException,
    CheckManageProjectMembersPermissionResponse
  ] = {
    authEndpoint[
      CheckManageProjectMembersPermissionParams,
      GeneralServiceException,
      CheckManageProjectMembersPermissionResponse
    ](ProjectPath / "checkManageProjectMembersPermission")
  }

  val createCueModule: BaseAuthenticatedEndpoint[
    CreateCueModuleParams,
    GeneralServiceException,
    CreateCueModuleResponse
  ] = {
    authEndpoint[
      CreateCueModuleParams,
      GeneralServiceException,
      CreateCueModuleResponse
    ](ProjectPath / "createCueModule")
  }

  val generateDataExtractCueModule: BaseAuthenticatedEndpoint[
    GenerateDataExtractCueModuleParams,
    GeneralServiceException,
    EmptyResponse
  ] = {
    authEndpoint[
      GenerateDataExtractCueModuleParams,
      GeneralServiceException,
      EmptyResponse
    ](ProjectPath / "generateDataExtractCueModule")
  }

  val createCueMappingModule: BaseAuthenticatedEndpoint[
    CreateCueMappingModuleParams,
    GeneralServiceException,
    CreateCueMappingModuleResponse
  ] = {
    authEndpoint[
      CreateCueMappingModuleParams,
      GeneralServiceException,
      CreateCueMappingModuleResponse
    ](ProjectPath / "createCueMappingModule")
  }

  val generateDataExtractCueMappingModule: BaseAuthenticatedEndpoint[
    GenerateDataExtractCueMappingModuleParams,
    GeneralServiceException,
    EmptyResponse
  ] = {
    authEndpoint[
      GenerateDataExtractCueMappingModuleParams,
      GeneralServiceException,
      EmptyResponse
    ](ProjectPath / "generateCueMappingModule")
  }

  val updateTemplateAlias: BaseAuthenticatedEndpoint[
    UpdateTemplateAliasParams,
    GeneralServiceException,
    EmptyResponse
  ] = {
    authEndpoint[
      UpdateTemplateAliasParams,
      GeneralServiceException,
      EmptyResponse
    ](ProjectPath / "updateTemplateAlias")
  }

  val generateCueTableData: BaseAuthenticatedEndpoint[
    GenerateCueTableDataParams,
    GeneralServiceException,
    GenerateCueTableDataResponse
  ] = {
    authEndpoint[
      GenerateCueTableDataParams,
      GeneralServiceException,
      GenerateCueTableDataResponse
    ](ProjectPath / "generateCueTableData")
  }

  val getCueModuleVersions: BaseAuthenticatedEndpoint[
    GetCueModuleVersionsParams,
    GeneralServiceException,
    GetCueModuleVersionsResponse
  ] = {
    authEndpoint[
      GetCueModuleVersionsParams,
      GeneralServiceException,
      GetCueModuleVersionsResponse
    ](ProjectPath / "getCueModuleVersions")
  }

  val transformTableValues: BaseAuthenticatedEndpoint[
    TransformTableValuesParams,
    GeneralServiceException,
    TransformTableValuesResponse
  ] = {
    authEndpoint[
      TransformTableValuesParams,
      GeneralServiceException,
      TransformTableValuesResponse
    ](ProjectPath / "transformTableValues")
  }

  val extractData: BaseAuthenticatedEndpoint[
    ExtractDataParams,
    GeneralServiceException,
    ExtractDataResponse
  ] = {
    authEndpoint[
      ExtractDataParams,
      GeneralServiceException,
      ExtractDataResponse
    ](ProjectPath / "extractData")
  }

  val getAllSaDataTemplates: BaseAuthenticatedEndpoint[
    GetAllSaDataTemplatesParams,
    GeneralServiceException,
    GetAllSaDataTemplatesResponse
  ] = {
    authEndpoint[
      GetAllSaDataTemplatesParams,
      GeneralServiceException,
      GetAllSaDataTemplatesResponse
    ](ProjectPath / "getAllSaDataTemplates")
  }

}
