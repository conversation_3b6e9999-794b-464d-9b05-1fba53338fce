// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.integplatform.manage

import scala.util.Random

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.laminar.SkeletonL
import design.anduin.table.laminar.TableL
import design.anduin.style.tw.*

import anduin.id.entity.EntityId
import anduin.integplatform.component.{IntegrationLogo, TagRenderer}
import anduin.integplatform.credentials.LinkAnduinObjectsModal
import anduin.integplatform.endpoints.{InstanceInfo, InstanceProgress}
import anduin.integplatform.prismatic.PrismaticRenderer.PrismaticView
import anduin.integplatform.prismatic.{PrismaticEndpointClient, PrismaticRenderer}
import anduin.integplatform.prismatic.graphql.GraphQlModelUtils
import anduin.integplatform.prismatic.graphql.schema.{Instance, Integration, Query}
import anduin.integplatform.prismatic.model.InstanceModel
import anduin.integplatform.prismatic.model.IntegrationModel.IntegrationAvatar
import anduin.integplatform.provider.{
  ActorIntegrationAnduinObjectsProvider,
  EntityInstancesInfoProvider,
  PrismaticTokenProvider
}
import anduin.model.id.prismatic.{PrismaticInstanceId, PrismaticIntegrationId}
import anduin.utils.DateTimeUtils
import anduin.utils.endpoint.WithFetchDataL.FetchResult
import stargazer.component.routing.laminar.WithReactRouterL
import stargazer.model.routing.DynamicAuthPage.IntegPlatform.IntegPlatformBrowsePage

private[manage] final case class ManageIntegrationTable(
  entityId: EntityId
) {
  private val fetchedInstancesVar = Var[Map[PrismaticInstanceId, InstanceModel]](Map.empty)

  private val fetchInstanceEventBus = EventBus[PrismaticInstanceId]()
  private val rowClickEventBus = EventBus[InstanceInfo]()

  private val showLinkObjectsModalOptVar = Var[Option[InstanceInfo]](None)
  private val showMonitorInstanceOptVar = Var[Option[InstanceInfo]](None)

  private val instanceNameColumn = TableL.Column[InstanceInfo](
    field = "instance-name",
    title = "Integration",
    renderCell = { renderProps =>
      val instanceId = renderProps.data.prismaticInstanceId
      val instanceOptSignal = fetchedInstancesVar.signal.map(_.get(instanceId))
      div(
        tw.wPc100.hPc100,
        child <-- instanceOptSignal.splitOption(
          ifEmpty = SkeletonL(
            height = "36px",
            width = "100%",
            shape = Skeleton.Shape.Rectangle
          )(),
          project = { (instance, instanceSignal) =>
            val fetchLogoEventBus = EventBus[IntegrationAvatar.Pending]()
            div(
              tw.flex.itemsCenter,
              child <-- PrismaticTokenProvider.prismaticTokenSignal.map { token =>
                div(
                  fetchLogoEventBus.events.flatMapSwitch { pendingLogo =>
                    PrismaticEndpointClient
                      .fetchLogo(
                        token,
                        pendingLogo
                      )
                      .map { fetchedLogoOpt =>
                        fetchedInstancesVar.update(
                          _.updatedWith(instanceId)(
                            _.map(_.copy(integrationAvatar = fetchedLogoOpt))
                          )
                        )
                      }
                  } --> Observer.empty
                )
              },
              IntegrationLogo(
                instanceSignal.map(_.integrationAvatar),
                IntegrationLogo.LogoSize.Px32
              )().amend(tw.flexNone),
              div(
                tw.ml8,
                div(
                  tw.fontSemiBold.textWrap,
                  instance.name
                )
              ),
              instanceSignal
                .map(_.integrationAvatar)
                .changes
                .map(_.foreach {
                  case pending: IntegrationAvatar.Pending => fetchLogoEventBus.emit(pending)
                  case _                                  => ()
                }) --> Observer.empty,
              onMountCallback { _ =>
                instance.integrationAvatar.foreach {
                  case pending: IntegrationAvatar.Pending => fetchLogoEventBus.emit(pending)
                  case _: IntegrationAvatar.Fetched       => ()
                }
                renderProps.cellComponent.checkHeight()
              }
            )
          }
        )
      )
    },
    minWidth = Some(300)
  )

  private val statusColumn = TableL.Column[InstanceInfo](
    field = "status",
    title = "Status",
    renderCell = { renderProps =>
      val instanceId = renderProps.data.prismaticInstanceId
      val instanceOptSignal = fetchedInstancesVar.signal.map(_.get(instanceId))
      div(
        tw.wPc100.hPc100,
        child <-- instanceOptSignal.splitOption(
          ifEmpty = SkeletonL(
            height = "16px",
            width = "64px",
            shape = Skeleton.Shape.Rectangle
          )(),
          project = { (instance, _) =>
            TagRenderer.renderInstanceStatusTag(instance)
          }
        )
      )
    },
    width = Some(160)
  )

  private val anduinObjectsColumn = TableL.Column[InstanceInfo](
    field = "anduin-objects",
    title = "Anduin objects",
    renderCell = { renderProps =>
      val appConnectorId = renderProps.data.appConnectorId
      div(
        tw.wPc100.hPc100,
        ActorIntegrationAnduinObjectsProvider(
          entityId = entityId,
          integrationId = appConnectorId
        ) { case (FetchResult(isFetchingSignal, respOptSignal), _) =>
          val fundsSignal =
            respOptSignal.map(
              _.map(
                _.funds.values.toList
                  .filter(_.joinStatus.integrationJoined)
                  .sortBy(_.fundName.toLowerCase)
              ).getOrElse(List.empty)
            )

          val firmsSignal =
            respOptSignal.map(
              _.map(
                _.firms.values.toList
                  .filter(_.joinStatus.integrationJoined)
                  .sortBy(_.firmName.toLowerCase)
              ).getOrElse(List.empty)
            )
          val dataRoomsSignal =
            respOptSignal.map(
              _.map(
                _.dataRooms.values.toList
                  .filter(_.joinStatus.integrationJoined)
                  .sortBy(_.dataRoomName.toLowerCase)
              ).getOrElse(List.empty)
            )
          div(
            tw.wPc100.hPc100,
            child <-- isFetchingSignal.splitBoolean(
              whenTrue = _ =>
                div(
                  (0 until 2).map { index =>
                    val widthPercentage = Random.nextInt(36) + 50
                    SkeletonL(
                      effect = Skeleton.Effect.Wave,
                      height = "20px",
                      width = s"$widthPercentage%",
                      shape = Skeleton.Shape.Rounded
                    )().amend(when(index > 0)(tw.mt8))
                  }
                ),
              whenFalse = _ =>
                div(
                  tw.spaceY8,
                  // handle empty state
                  children <-- fundsSignal.split(_.fundId) { case (_, _, fundSignal) =>
                    div(
                      tw.flex.itemsCenter.flexFill,
                      div(
                        tw.wPx24.hPx24.mr8,
                        tw.borderAll.rounded3.bgGray6.borderGray6,
                        tw.flex.itemsCenter.justifyAround,
                        div(
                          tw.textGray0,
                          IconL(name = Val(Icon.Glyph.Vault))()
                        )
                      ),
                      div(
                        tw.flexFill.textWrap,
                        child.text <-- fundSignal.map(_.fundName)
                      ),
                      onMountCallback { _ =>
                        renderProps.cellComponent.checkHeight()
                      }
                    )
                  },
                  children <-- firmsSignal.split(_.firmId) { case (_, _, firmSignal) =>
                    div(
                      tw.flex.itemsCenter.flexFill,
                      div(
                        tw.wPx24.hPx24.mr8,
                        tw.borderAll.rounded3.bgGray6.borderGray6,
                        tw.flex.itemsCenter.justifyAround,
                        div(
                          tw.textGray0,
                          IconL(name = Val(Icon.Glyph.Office))()
                        )
                      ),
                      div(
                        tw.flexFill.textWrap,
                        child.text <-- firmSignal.map(_.firmName)
                      ),
                      onMountCallback { _ =>
                        renderProps.cellComponent.checkHeight()
                      }
                    )
                  },
                  children <-- dataRoomsSignal.split(_.dataRoomWorkflowId) { case (_, _, dataRoomSignal) =>
                    div(
                      tw.flex.itemsCenter.flexFill,
                      div(
                        tw.wPx24.hPx24.mr8,
                        tw.borderAll.rounded3.bgGray6.borderGray6,
                        tw.flex.itemsCenter.justifyAround,
                        div(
                          tw.textGray0,
                          IconL(name = Val(Icon.Glyph.Safe))()
                        )
                      ),
                      div(
                        tw.flexFill.textWrap,
                        child.text <-- dataRoomSignal.map(_.dataRoomName)
                      ),
                      onMountCallback { _ =>
                        renderProps.cellComponent.checkHeight()
                      }
                    )
                  },
                  onMountCallback { _ =>
                    renderProps.cellComponent.checkHeight()
                  }
                )
            )
          )
        }
      )
    },
    maxWidth = Some(392)
  )

  private val createdAtColumn = TableL.Column[InstanceInfo](
    field = "created-at",
    title = "Installed date",
    renderCell = { renderProps =>
      val instanceId = renderProps.data.prismaticInstanceId
      val createdAt = renderProps.data.createdAt
      val instanceOptSignal = fetchedInstancesVar.signal.map(_.get(instanceId))
      div(
        tw.wPc100.hPc100,
        child <-- instanceOptSignal.splitOption(
          ifEmpty = SkeletonL(
            height = "24px",
            width = "100%",
            shape = Skeleton.Shape.Rectangle
          )(),
          project = { (_, _) =>
            div(
              createdAt.map { createdAt =>
                DateTimeUtils.formatInstant(
                  createdAt,
                  DateTimeUtils.DefaultDateFormatter
                )(
                  using DateTimeUtils.defaultTimezone
                )
              }
            )
          }
        )
      )
    },
    maxWidth = Some(160)
  )

  private val actionColumn = TableL.Column[InstanceInfo](
    field = "action",
    title = "",
    renderCell = { renderProps =>
      val instanceId = renderProps.data.prismaticInstanceId
      val instanceOptSignal = fetchedInstancesVar.signal.map(_.get(instanceId))
      div(
        tw.wPc100.hPc100,
        child.maybe <-- instanceOptSignal.map(_.map { instanceModel =>
          DeleteIntegrationModal(instanceModel)()
        })
      )
    },
    width = Some(56)
  )

  def apply(): HtmlElement = {
    div(
      tw.hPc100.flex.flexCol,
      child <-- PrismaticTokenProvider.prismaticTokenSignal.map { token =>
        div(
          fetchInstanceEventBus.events.flatMapMerge { instanceId =>
            fetchInstance(instanceId, token)
          } --> Observer.empty
        )
      },
      rowClickEventBus.events.map { instanceInfo =>
        instanceInfo.progress match {
          case InstanceProgress.LinkObjects        => showLinkObjectsModalOptVar.set(Some(instanceInfo))
          case InstanceProgress.ConfigurePrismatic => showMonitorInstanceOptVar.set(Some(instanceInfo))
        }
      } --> Observer.empty,
      child.maybe <-- showMonitorInstanceOptVar.signal.map(_.map { instanceInfo =>
        val instanceId = instanceInfo.prismaticInstanceId
        PrismaticRenderer(
          tokenSignal = PrismaticTokenProvider.prismaticTokenSignal,
          prismaticView = PrismaticView.ConfigureInstance(instanceId),
          onDeleteInstance = Observer { _ => EntityInstancesInfoProvider.deleteInstance(instanceId) }
        )()
      }),
      child.maybe <-- showLinkObjectsModalOptVar.signal
        .combineWith(fetchedInstancesVar.signal)
        .map { case (showModalOpt, instancesMap) =>
          showModalOpt.flatMap { instanceInfo =>
            instancesMap.get(instanceInfo.prismaticInstanceId).map { instance =>
              LinkAnduinObjectsModal(
                entityId = entityId,
                prismaticInstanceId = instanceInfo.prismaticInstanceId,
                instanceSignal = Val(instance),
                appConnectorId = instanceInfo.appConnectorId,
                onClose = showLinkObjectsModalOptVar.writer.contramap(_ => None),
                onProceed = showMonitorInstanceOptVar.writer.contramap(_ => Some(instanceInfo))
              )()
            }
          }
        },
      TableL[InstanceInfo](
        options = TableL.Options(layout = TableL.Layout.FitColumns),
        dataSignal = EntityInstancesInfoProvider.ownedInstancesInfoSignal.map(_.sortBy(_.createdAt).reverse),
        columns = List(
          instanceNameColumn,
          statusColumn,
          anduinObjectsColumn,
          createdAtColumn,
          actionColumn
        ),
        placeholder = TableL.Placeholder(
          criteria = _.map(_.isEmpty),
          render = _ => renderEmptyState
        ),
        onRowRendered = Observer { renderProps =>
          renderProps.getData().foreach { instanceInfo =>
            val instanceId = instanceInfo.prismaticInstanceId
            fetchInstanceEventBus.emit(instanceId)
          }
        },
        onRowClick = Observer { row =>
          row.getData().foreach(rowClickEventBus.emit)
        }
      ).amend(tw.flexFill)
    )
  }

  private def renderEmptyState = {
    div(
      tw.bgGray1.borderAll.borderGray2.rounded4,
      tw.hPc100.wPc100.py32,
      tw.flex.flexCol.itemsCenter.justifyCenter,
      NonIdealStateL(
        icon = emptyNode,
        title = "No integrations installed yet.",
        description = "Start by browsing available integrations and install the ones you need.",
        action = WithReactRouterL { router =>
          ButtonL(
            style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
            onClick = Observer { _ =>
              router.set(IntegPlatformBrowsePage(entityId)).runNow()
              ()
            }
          )("Browse integrations")
        }
      )()
    )
  }

  private def fetchInstance(
    instanceId: PrismaticInstanceId,
    token: String
  ): EventStream[Unit] = {
    val instanceQuery = Query.instance(instanceId.value)(
      (Instance.id ~ Instance.name ~ Instance.enabled ~ Instance.configState ~
        Instance.integration(Integration.id ~ Integration.avatarUrl)).map {
        case (id, name, enabled, configState, (prismaticIntegrationId, avatarId)) =>
          InstanceModel(
            integrationId = PrismaticIntegrationId(prismaticIntegrationId),
            instanceId = PrismaticInstanceId(id),
            name = name,
            enabled = enabled,
            configState = GraphQlModelUtils.toConfigState(configState),
            integrationAvatar = avatarId.map(IntegrationAvatar.Pending.apply)
          )
      }
    )
    PrismaticEndpointClient
      .graphqlQuery(instanceQuery, token)
      .map { fetchResult =>
        val instanceModelOpt = fetchResult.getOrElse(None)
        instanceModelOpt.foreach { instanceModel =>
          fetchedInstancesVar.update(_ + (instanceId -> instanceModel))
        }
      }
  }

}
