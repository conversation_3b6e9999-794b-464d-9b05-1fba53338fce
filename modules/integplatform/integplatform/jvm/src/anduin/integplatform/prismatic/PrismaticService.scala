// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.integplatform.prismatic

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.Duration

import zio.{Task, ZIO}

import anduin.cache.AnduinCacheManager
import anduin.model.id.prismatic.{
  PrismaticCustomerId,
  PrismaticInstanceId,
  PrismaticIntegrationId,
  PrismaticVersionSequenceId
}
import anduin.integplatform.exception.IntegPlatformException.{InstanceNameExistedException, PrismaticException}
import anduin.integplatform.prismatic.PrismaticService.*
import anduin.integplatform.prismatic.graphql.GraphQlModelUtils
import anduin.integplatform.prismatic.graphql.schema.*
import anduin.integplatform.prismatic.model.*
import anduin.integplatform.prismatic.graphql.GraphQlModelUtils.*
import anduin.integplatform.prismatic.model.IntegrationModel.IntegrationAvatar
import com.anduin.stargazer.external.base64.Base64
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.utils.ZIOUtils

final case class PrismaticService(
  anduinCacheManager: AnduinCacheManager,
  prismaticClient: PrismaticClient,
  backendConfig: GondorBackendConfig
) {

  private val environmentName = backendConfig.server.deployment

  private val authorizationTokenCache = anduinCacheManager.make[Unit, String](
    name = "prismatic-authorization-token",
    expiration = Duration(1, TimeUnit.HOURS),
    makeKey = _ => ZIO.succeed("prismatic-authorization-key"),
    retrieve = _ => prismaticClient.getAuthorizationToken
  )

  private val instanceInfoCache = anduinCacheManager.make[
    PrismaticInstanceId,
    InstanceModel
  ](
    name = "prismatic-instance-info",
    expiration = Duration(1, TimeUnit.DAYS),
    makeKey = key => ZIO.succeed(s"instance-id:${Base64.toBase64(key.value.getBytes)}"),
    retrieve = instanceId => getInstanceOpt(instanceId)
  )

  private def execute[A](
    task: (authToken: String) => Task[A]
  ): Task[A] = {
    for {
      authTokenOpt <- authorizationTokenCache.getOpt(())
      authToken <- ZIOUtils.fromOption(
        authTokenOpt,
        PrismaticException("Failed to get Prismatic Authorization token")
      )
      res <- task(authToken)
    } yield res
  }

  def getIntegrationAvatar(
    avatarUrlOpt: Option[IntegrationAvatar]
  ): Task[Option[String]] = {
    ZIOUtils
      .traverseOption(avatarUrlOpt) {
        case IntegrationAvatar.Pending(avatarId) =>
          execute { authToken =>
            prismaticClient.getIntegrationAvatar(
              avatarId,
              authToken
            )
          }
        case IntegrationAvatar.Fetched(avatarUrl) => ZIO.succeed(Some(avatarUrl))
      }
      .map(_.flatten)
  }

  def createCustomer(
    customerName: String,
    externalId: Option[String]
  ): Task[PrismaticCustomerId] = execute { authToken =>
    for {
      respOpt <- prismaticClient.mutationRequest(
        Mutation.createCustomer(
          CreateCustomerInput(
            name = customerName,
            externalId = externalId,
            labels = Some(List(Some(environmentName)))
          )
        )(
          CreateCustomerPayload.customer(Customer.id)
            ~ CreateCustomerPayload.errors(ErrorType.messages).map(_.flatten)
        ),
        authToken
      )
      resp <- ZIOUtils.fromOption(
        respOpt,
        PrismaticException("Cannot create customer: None Prismatic response")
      )
      (customerIdOpt, errMessages) = resp
      customerId <- ZIOUtils.fromOption(
        customerIdOpt,
        PrismaticException(s"Cannot create customer: $errMessages")
      )
    } yield PrismaticCustomerId(customerId)
  }

  def getCustomer(
    customerId: PrismaticCustomerId
  ): Task[CustomerModel] = execute { authToken =>
    for {
      respOpt <- prismaticClient.queryRequest(
        Query
          .customer(
            customerId.value
          )(
            (Customer.name ~ Customer.labels ~ Customer.externalId)
              .map { case (name, labels, externalId) =>
                CustomerModel(
                  id = customerId,
                  name = name,
                  labels = labels.getOrElse(List.empty),
                  externalId = externalId
                )
              }
          ),
        authToken
      )
      customerModel <- ZIOUtils.fromOption(
        respOpt,
        PrismaticException(s"Customer ${customerId.value} not found")
      )
    } yield customerModel
  }

  def updateCustomer(
    customerId: PrismaticCustomerId,
    name: Option[String] = None,
    labels: Option[List[String]] = None,
    externalId: Option[String] = None
  ): Task[Unit] = execute { authToken =>
    for {
      respOpt <- prismaticClient.mutationRequest(
        Mutation.updateCustomer(
          UpdateCustomerInput(
            id = customerId.value,
            name = name,
            labels = labels.map(_.map(Some(_))),
            externalId = externalId
          )
        )(
          UpdateCustomerPayload.errors(ErrorType.messages).map(_.flatten)
        ),
        authToken
      )
      errorMsg <- ZIOUtils.fromOption(
        respOpt,
        PrismaticException("Cannot update customer: None Prismatic response")
      )
      _ <- ZIOUtils.validate(errorMsg.isEmpty) {
        PrismaticException(s"Cannot update customer: $errorMsg")
      }
    } yield ()
  }

  def deleteCustomer(
    customerId: PrismaticCustomerId
  ): Task[Unit] = execute { authToken =>
    for {
      respOpt <- prismaticClient.mutationRequest(
        Mutation.deleteCustomer(
          DeleteCustomerInput(customerId.value)
        )(
          DeleteCustomerPayload.errors(ErrorType.messages).map(_.flatten)
        ),
        authToken
      )
      errorMsg <- ZIOUtils.fromOption(
        respOpt,
        PrismaticException("Cannot delete customer: None Prismatic response")
      )
      _ <- ZIOUtils.validate(errorMsg.isEmpty) {
        PrismaticException(s"Cannot delete customer: $errorMsg")
      }
    } yield ()
  }

  def createInstance(
    customerId: PrismaticCustomerId,
    prismaticIntegrationId: PrismaticIntegrationId,
    instanceName: String,
    labels: Option[List[String]]
  ): Task[PrismaticInstanceId] = execute { authToken =>
    for {
      respOpt <- prismaticClient.mutationRequest(
        Mutation.createInstance(
          CreateInstanceInput(
            integration = prismaticIntegrationId.value,
            customer = customerId.value,
            name = instanceName,
            labels = labels.map(_.map(Some(_)))
          )
        )(
          CreateInstancePayload.instance(Instance.id)
            ~ CreateInstancePayload.errors(ErrorType.messages).map(_.flatten)
        ),
        authToken
      )
      resp <- ZIOUtils.fromOption(
        respOpt,
        PrismaticException("Cannot create Instance: None Prismatic response")
      )
      (instanceIdOpt, errMessages) = resp
      instanceId <- ZIOUtils.fromOption(
        instanceIdOpt,
        if (errMessages.exists(_.contains("name already exists"))) {
          InstanceNameExistedException(instanceName)
        } else {
          PrismaticException(s"Cannot create Instance: $errMessages")
        }
      )
    } yield PrismaticInstanceId(instanceId)
  }

  def getInstanceWithCacheOpt(
    prismaticInstanceId: PrismaticInstanceId
  ): Task[Option[InstanceModel]] =
    instanceInfoCache.getOpt(prismaticInstanceId)

  def getInstanceOpt(
    prismaticInstanceId: PrismaticInstanceId
  ): Task[Option[InstanceModel]] = execute { authToken =>
    for {
      resp <- prismaticClient.queryRequestWithErrorResponse(
        Query.instance(prismaticInstanceId.value)(
          (Instance.id ~ Instance.name ~ Instance.enabled ~ Instance.configState ~ Instance.integration(
            Integration.id ~ Integration.avatarUrl
          )).map { case (id, name, enabled, configState, (integrationId, avatarId)) =>
            model.InstanceModel(
              integrationId = PrismaticIntegrationId(integrationId),
              instanceId = PrismaticInstanceId(id),
              name = name,
              enabled = enabled,
              configState = GraphQlModelUtils.toConfigState(configState),
              integrationAvatar = avatarId.map(IntegrationModel.IntegrationAvatar.Pending.apply)
            )
          }
        ),
        authToken
      )
      instanceOpt <- resp match {
        case Left(graphqlErrors) =>
          if (graphqlErrors.exists(_.message.contains("Record not found."))) {
            ZIO.none
          } else {
            ZIO.fail(PrismaticException(graphqlErrors.headOption.map(_.message).getOrElse("Graphql Error")))
          }
        case Right(None) =>
          ZIO.fail(PrismaticException("Cannot get Instance: None Prismatic response"))
        case Right(Some(instance)) => ZIO.succeed(Some(instance))
      }

    } yield instanceOpt
  }

  def checkInstanceExisted(
    prismaticInstanceId: PrismaticInstanceId
  ): Task[Boolean] = {
    getInstanceOpt(prismaticInstanceId).map(_.isDefined)
  }

  def updateInstanceConfigVariables(
    prismaticInstanceId: PrismaticInstanceId,
    configVariables: Map[String, String]
  ): Task[Unit] = execute { authToken =>
    for {
      respOpt <- prismaticClient.mutationRequest(
        Mutation.updateInstanceConfigVariables(
          UpdateInstanceConfigVariablesInput(
            id = Some(prismaticInstanceId.value),
            configVariables = Some(configVariables.map { (key, value) =>
              Some(InputInstanceConfigVariable(key = key, values = Some(value)))
            }.toList)
          )
        )(
          UpdateInstanceConfigVariablesPayload.errors(ErrorType.messages).map(_.flatten)
        ),
        authToken
      )
      errorMsg <- ZIOUtils.fromOption(
        respOpt,
        PrismaticException("Cannot config instance: None Prismatic response")
      )
      _ <- ZIOUtils.validate(errorMsg.isEmpty) {
        PrismaticException(s"Cannot config instance: $errorMsg")
      }
    } yield ()
  }

  def deleteInstance(
    prismaticInstanceId: PrismaticInstanceId
  ): Task[Unit] = execute { authToken =>
    for {
      respOpt <- prismaticClient.mutationRequest(
        Mutation.deleteInstance(
          DeleteInstanceInput(Some(prismaticInstanceId.value))
        )(
          DeleteInstancePayload.errors(ErrorType.messages).map(_.flatten)
        ),
        authToken
      )
      errorMsg <- ZIOUtils.fromOption(
        respOpt,
        PrismaticException("Cannot delete instance: None Prismatic response")
      )
      _ <- ZIOUtils.validate(errorMsg.isEmpty) {
        PrismaticException(s"Cannot delete instance: $errorMsg")
      }
    } yield ()
  }

  def marketplaceIntegrations(): Task[List[IntegrationModel]] = execute { authToken =>
    for {
      resp <- prismaticClient.queryRequest(
        Query.marketplaceIntegrations()(
          IntegrationConnection
            .nodes(buildIntegrationModel)
            .map(_.flatten)
        ),
        authToken
      )
    } yield resp
  }

  def getIntegration(
    integrationId: PrismaticIntegrationId
  ): Task[IntegrationModel] = execute { authToken =>
    for {
      integrationModelOpt <- prismaticClient.queryRequest(
        Query.marketplaceIntegration(integrationId.value)(
          buildIntegrationModel
        ),
        authToken
      )
      integrationModel <- ZIOUtils.fromOption(
        integrationModelOpt,
        PrismaticException("Cannot get Integration: None Prismatic response")
      )
    } yield integrationModel
  }

  def getInstanceBasicInfo(
    instanceId: PrismaticInstanceId
  ): Task[PrismaticInstanceBasicInfo] = execute { authToken =>
    for {
      respOpt <- prismaticClient.queryRequest(
        Query.instance(instanceId.value)(
          (Instance.id ~ Instance.name
            ~ Instance.integration(Integration.id ~ Integration.name ~ Integration.versionSequenceId)
            ~ Instance.customer(Customer.id))
            .map { case (instanceId, instanceName, (integrationId, integrationName, versionSequenceId), customerId) =>
              PrismaticInstanceBasicInfo(
                instanceId = PrismaticInstanceId(instanceId),
                instanceName = instanceName,
                customerId = PrismaticCustomerId(customerId),
                integrationInfo = PrismaticIntegrationBasicInfo(
                  integrationId = PrismaticIntegrationId(integrationId),
                  versionSequenceId = PrismaticVersionSequenceId(versionSequenceId),
                  integrationName = integrationName
                )
              )
            }
        ),
        authToken
      )
      resp <- ZIOUtils.fromOption(
        respOpt,
        PrismaticException("Cannot get Instance basic info: None Prismatic response")
      )
    } yield resp
  }

  def getIntegrationBasicInfo(
    integrationId: PrismaticIntegrationId
  ): Task[PrismaticIntegrationBasicInfo] = execute { authToken =>
    for {
      respOpt <- prismaticClient.queryRequest(
        Query.marketplaceIntegration(integrationId.value)(
          (Integration.id ~ Integration.name ~ Integration.versionSequenceId)
            .map { case (integrationId, integrationName, versionSequenceId) =>
              PrismaticIntegrationBasicInfo(
                integrationId = PrismaticIntegrationId(integrationId),
                versionSequenceId = PrismaticVersionSequenceId(versionSequenceId),
                integrationName = integrationName
              )
            }
        ),
        authToken
      )
      resp <- ZIOUtils.fromOption(
        respOpt,
        PrismaticException("Cannot get Integration basic info: None Prismatic response")
      )
    } yield resp

  }

  def updateInstance(
    instanceId: PrismaticInstanceId,
    enabled: Option[Boolean]
  ): Task[Unit] = execute { authToken =>
    for {
      respOpt <- prismaticClient.mutationRequest(
        Mutation.updateInstance(
          UpdateInstanceInput(
            id = instanceId.value,
            enabled = enabled
          )
        )(
          UpdateInstancePayload.errors(ErrorType.messages).map(_.flatten)
        ),
        authToken
      )
      errorMsg <- ZIOUtils.fromOption(
        respOpt,
        PrismaticException("Cannot update instance: None Prismatic response")
      )
      _ <- ZIOUtils.validate(errorMsg.isEmpty) {
        PrismaticException(s"Cannot update instance: $errorMsg")
      }
    } yield ()

  }

  def invalidateInstanceCache(
    instanceId: PrismaticInstanceId
  ): Task[Unit] = {
    for {
      _ <- instanceInfoCache.invalidate(instanceId)
    } yield ()
  }

}

object PrismaticService {

  final case class PrismaticInstanceBasicInfo(
    instanceId: PrismaticInstanceId,
    instanceName: String,
    customerId: PrismaticCustomerId,
    integrationInfo: PrismaticIntegrationBasicInfo
  )

  final case class PrismaticIntegrationBasicInfo(
    integrationId: PrismaticIntegrationId,
    versionSequenceId: PrismaticVersionSequenceId,
    integrationName: String
  )

}
