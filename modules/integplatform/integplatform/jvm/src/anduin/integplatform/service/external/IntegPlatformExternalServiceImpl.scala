// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.integplatform.service.external

import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.fdb.record.{FDBOperations, FDBReadDatabase}
import anduin.fdb.record.model.RecordReadIO
import anduin.id.integration.{IntegrableAppId, IntegrationId}
import anduin.id.integplatform.AppConnectorId
import anduin.integplatform.endpoints.UserInfo
import anduin.integplatform.endpoints.external.*
import anduin.integplatform.prismatic.PrismaticService
import anduin.integplatform.service.{IntegPlatformEntityService, IntegPlatformLoggingService}
import anduin.integplatform.service.external.IntegPlatformExternalServiceImpl.*
import anduin.integplatform.store.IntegPlatformStoreOperationsUtils.*
import anduin.integplatform.store.credentials.IntegPlatformCredentialsStoreOperations
import anduin.integration.model.IntegrationPermission
import anduin.integration.service.IntegrationService
import anduin.integration.store.IntegrationStoreOperations
import anduin.model.common.user.UserId
import anduin.portaluser.ExecutiveAdmin
import com.anduin.stargazer.service.utils.ZIOUtils
import io.github.arainko.ducktape.*

import anduin.id.entity.EntityId
import anduin.id.fundsub.FundSubId
import anduin.integplatform.endpoints.validation.{
  IntegPlatformAccessCredentialsCheckParams,
  IntegPlatformUserCheckParams
}
import anduin.integplatform.instance.InstanceProgress
import anduin.integplatform.validation.IntegPlatformValidator
import anduin.model.notichannel.IntegPlatformNotificationChannels
import anduin.service.entity.EntityService
import com.anduin.stargazer.service.nats.NatsNotificationService

final case class IntegPlatformExternalServiceImpl(
  executiveAdmin: ExecutiveAdmin,
  entityService: EntityService,
  prismaticService: PrismaticService,
  integrationService: IntegrationService,
  userProfileService: UserProfileService,
  natsNotificationService: NatsNotificationService,
  integPlatformValidator: IntegPlatformValidator,
  integPlatformEntityService: IntegPlatformEntityService,
  integPlatformLoggingService: IntegPlatformLoggingService
) extends IntegPlatformExternalService {

  override def validateAppConnectorBelongToEntity(
    entityId: EntityId,
    appConnectorId: AppConnectorId,
    actor: UserId
  ): Task[Unit] = {
    val params = IntegPlatformAppConnectorCheckParams(
      appConnectorId,
      entityId
    )
    integPlatformValidator.checkCredentialsBelongToEntity.check(params, actor).unit
  }

  override def getEntityHubInfo(
    entityId: EntityId,
    actor: UserId
  ): Task[IntegrationHubInfo] = {
    for {
      entityName <- entityService.getEntityName(entityId)
      entityLogoUrl <- entityService.getEntityLogoUrl(entityId)
      canAccess <- (
        integPlatformValidator.checkEmailDomainAccess || integPlatformValidator.checkEntityHubMember
      ).check(
        IntegPlatformEntityCheckParams(entityId),
        actor
      )
    } yield IntegrationHubInfo(
      entityId = entityId,
      entityName = entityName,
      entityLogoUrl = entityLogoUrl,
      canAccessIntegrationHub = canAccess
    )
  }

  override def getAppConnectors(
    integrationIds: Set[IntegrationId],
    actor: UserId
  ): Task[List[AppConnectorBasicInfo]] = {
    for {
      _ <- ZIO.logInfo(s"Get ${integrationIds.size} integration credentials info")
      // bypass permission check
      anduinAdmin <- executiveAdmin.userId
      connectorModels <- FDBReadDatabase
        .read(
          FDBOperations[(IntegrationStoreOperations, IntegPlatformCredentialsStoreOperations)].Production
        ) { (integrationOps, credentialsOps) =>
          RecordReadIO.parTraverseN(4)(integrationIds) { integrationId =>
            for {
              _ <- integrationOps.get(
                integrationId,
                anduinAdmin,
                IntegrationPermission.ViewBasicInfo
              )
              // integrationId may belong to service account Public API, not IntegPlatform App Connector model
              appConnectorModelOpt <- credentialsOps.getOpt(integrationId)
            } yield appConnectorModelOpt
          }
        }
        .map(_.flatten)
      creatorsInfoMap <- userProfileService.batchGetUserInfos(
        connectorModels.map(_.createdBy).toSet
      )
    } yield connectorModels.map { model =>
      AppConnectorBasicInfo(
        integrationId = model.integrationId,
        entityId = model.entityId,
        creatorInfo = creatorsInfoMap
          .get(model.createdBy)
          .map(_.to[UserInfo])
          .getOrElse(UserInfo.default)
      )
    }
  }

  override def getLinkedPrismaticInstances(
    integrationIds: Set[IntegrationId],
    actor: UserId
  ): Task[List[IntegrationHubConnectedInfo]] = {
    for {
      appConnectors <- getAppConnectors(
        integrationIds = integrationIds,
        actor = actor
      )
      appConnectorsByEntity = appConnectors.groupBy(_.entityId)
      _ <- ZIO.logInfo(s"Get all installed instances that belongs to ${integrationIds.size} app connectors")
      integrationHubsInfo <- ZIO.foreach(appConnectorsByEntity.toList) { case entityId -> appConnectors =>
        for {
          entityInfo <- getEntityHubInfo(entityId, actor)
          prismaticApps <- getLinkedPrismaticInstancesInternal(
            integrationIds = appConnectors.map(_.integrationId).toSet
          )
        } yield IntegrationHubConnectedInfo(
          hubInfo = entityInfo,
          appConnectors = appConnectors,
          prismaticInstances = prismaticApps.toList
        )
      }
    } yield integrationHubsInfo
  }

  private def getLinkedPrismaticInstancesInternal(
    integrationIds: Set[IntegrationId]
  ): Task[Set[ExternalPrismaticInstanceInfo]] = {
    ZIO
      .foreach(integrationIds) { integrationId =>
        for {
          instanceIds <- executeInstance(_.get(integrationId).map(_.map(_.prismaticInstanceId)))
          instancesInfo <- ZIOUtils
            .foreachParN(4)(instanceIds) { instanceId =>
              prismaticService.getInstanceWithCacheOpt(instanceId)
            }
            .map(_.flatten)
          installedApps <- ZIO.foreach(instancesInfo) { instance =>
            prismaticService
              .getIntegrationAvatar(instance.integrationAvatar)
              .map { avatarUrlOpt =>
                ExternalPrismaticInstanceInfo(
                  instanceName = instance.name,
                  prismaticInstanceId = instance.instanceId,
                  appConnectorId = integrationId,
                  avatarUrl = avatarUrlOpt,
                  status = instance.status
                )
              }
          }
        } yield installedApps
      }
      .map(_.flatten)
  }

  override def getLinkablePrismaticInstances(
    actor: UserId,
    ignoreConnector: AppConnectorId => Boolean
  ): Task[List[IntegrationHubLinkableInfo]] = {
    for {
      accessibleEntities <- integPlatformEntityService
        .getIntegPlatformEntitiesByUser(actor)
        .map(_.map { entity =>
          IntegrationHubInfo(
            entityId = entity.entityId,
            entityName = entity.entityName,
            entityLogoUrl = entity.logoUrlOpt,
            canAccessIntegrationHub = true
          )
        })
      linkableHubsInfo <- ZIO.foreach(accessibleEntities) { entityInfo =>
        for {
          allAppConnectorIds <- executeCredentials(
            _.get(entityInfo.entityId)
              .map(_.map(_.integrationId))
          )
          appConnectorIds = allAppConnectorIds.filterNot(ignoreConnector)
          appConnectors <- getAppConnectors(
            appConnectorIds.toSet,
            actor
          )
          linkableApps <- ZIOUtils
            .foreachParN(4)(appConnectorIds) { appConnectorId =>
              for {
                instanceIds <- executeInstance { ops =>
                  ops.get(appConnectorId).map(_.map(_.prismaticInstanceId))
                }
                instancesInfo <- ZIO
                  .foreach(instanceIds) { instanceId =>
                    prismaticService.getInstanceWithCacheOpt(instanceId)
                  }
                  .map(_.flatten)
                linkableApps <- ZIO.foreach(instancesInfo) { instance =>
                  prismaticService
                    .getIntegrationAvatar(instance.integrationAvatar)
                    .map { avatarUrlOpt =>
                      ExternalPrismaticInstanceInfo(
                        instanceName = instance.name,
                        prismaticInstanceId = instance.instanceId,
                        appConnectorId = appConnectorId,
                        avatarUrl = avatarUrlOpt,
                        status = instance.status
                      )
                    }
                }
              } yield linkableApps
            }
            .map(_.flatten)
        } yield IntegrationHubLinkableInfo(
          hubInfo = entityInfo,
          appConnectors = appConnectors,
          prismaticInstances = linkableApps
        )
      }
    } yield linkableHubsInfo
  }

  override def handleLinkAppConnector(
    entityId: EntityId,
    appConnectorId: AppConnectorId,
    appId: IntegrableAppId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Integration Platform handle Anduin Object linking app connector $appConnectorId")
      _ <- natsNotificationService.publish(
        appConnectorId,
        IntegPlatformNotificationChannels.appConnectorLinking(appConnectorId)
      )
      _ <- appId match {
        case fundId: FundSubId =>
          integPlatformLoggingService.logFundSubLink(
            appConnectorId = appConnectorId,
            entityId = entityId,
            actor = actor,
            fundId = fundId
          )
        case _ => ZIO.logWarning(s"Unhandled $appId when linking app connector $appConnectorId")
      }
    } yield ()
  }

  override def handleUnlinkAppConnector(
    entityId: EntityId,
    appConnectorId: AppConnectorId,
    appId: IntegrableAppId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Integration Platform handle Anduin Object unlinking app connector $appConnectorId")
      instanceIds <- executeInstance(_.get(appConnectorId).map(_.map(_.prismaticInstanceId)))
      _ <- ZIOUtils.foreachParN(4)(instanceIds) { instanceId =>
        for {
          _ <- executeInstance(
            _.update(instanceId)(
              _.withProgress(InstanceProgress.LinkAnduinObject)
            )
          )
          _ <- prismaticService.updateInstance(
            instanceId,
            enabled = Some(false)
          )
        } yield ()
      }
      _ <- appId match {
        case fundId: FundSubId =>
          integPlatformLoggingService.logFundSubUnlink(
            appConnectorId = appConnectorId,
            entityId = entityId,
            actor = actor,
            fundId = fundId
          )
        case _ => ZIO.logWarning(s"Unhandled $appId when unlinking app connector $appConnectorId")
      }
    } yield ()
  }

}

object IntegPlatformExternalServiceImpl {

  private case class IntegPlatformEntityCheckParams(
    override val entityId: EntityId
  ) extends IntegPlatformUserCheckParams

  private case class IntegPlatformAppConnectorCheckParams(
    appConnectorId: AppConnectorId,
    entityId: EntityId
  ) extends IntegPlatformAccessCredentialsCheckParams {
    override def integrationId: AppConnectorId = appConnectorId
  }

}
