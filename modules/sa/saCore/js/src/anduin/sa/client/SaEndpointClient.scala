// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.sa.client

import zio.Task

import anduin.id.sa.{MappingDestinationId, SaProfileId}
import anduin.sa.endpoints.*
import anduin.service.GeneralServiceException
import anduin.tapir.client.AuthenticatedEndpointClient
import anduin.tapir.endpoint.CommonParams.Empty

object SaEndpointClient extends AuthenticatedEndpointClient {

  // SaProfileService
  val addSaProfile: AddSaProfileParams => Task[Either[GeneralServiceException, SaProfileId]] = {
    toClientThrowDecodeAndSecurityFailures(SaEndpoints.addSaProfile)
  }

  val updateSaProfile: UpdateSaProfileParams => Task[Either[GeneralServiceException, Unit]] = {
    toClientThrowDecodeAndSecurityFailures(SaEndpoints.updateSaProfile)
  }

  val exportProfileSaListToCsv
    : ExportProfileSaListToCsvParams => Task[Either[GeneralServiceException, ExportProfileSaListToCsvResponse]] = {
    toClientThrowDecodeAndSecurityFailures(SaEndpoints.exportProfileSaListToCsv)
  }

  val getAllSaProfileInfos: Empty => Task[Either[GeneralServiceException, SaProfileBasicInfoList]] = {
    toClientThrowDecodeAndSecurityFailures(SaEndpoints.getAllSaProfileInfos)
  }

  val getSaProfileByIds: SaProfileIdList => Task[Either[GeneralServiceException, SaProfileFullInfoList]] = {
    toClientThrowDecodeAndSecurityFailures(SaEndpoints.getSaProfileByIds)
  }

  val parseSaListFromCsv
    : ParseSaListFromCsvParams => Task[Either[GeneralServiceException, ParseSaListFromCsvResponse]] = {
    toClientThrowDecodeAndSecurityFailures(SaEndpoints.parseSaListFromCsv)
  }

  // MappingDestinationService
  val addMappingDestination
    : AddMappingDestinationParams => Task[Either[GeneralServiceException, MappingDestinationId]] = {
    toClientThrowDecodeAndSecurityFailures(SaEndpoints.addMappingDestination)
  }

  val addSaDataTemplate: AddSaDataTemplateParams => Task[Either[GeneralServiceException, AddSaDataTemplateResponse]] = {
    toClientThrowDecodeAndSecurityFailures(SaEndpoints.addSaDataTemplate)
  }

  val getAllSaDataTemplates
    : GetAllSaDataTemplatesParams => Task[Either[GeneralServiceException, GetAllSaDataTemplatesResponse]] = {
    toClientThrowDecodeAndSecurityFailures(SaEndpoints.getAllSaDataTemplates)
  }

}
