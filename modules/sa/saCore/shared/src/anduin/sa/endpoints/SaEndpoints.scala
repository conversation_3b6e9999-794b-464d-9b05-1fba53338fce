// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.sa.endpoints

import anduin.service.GeneralServiceException
import anduin.tapir.AuthenticatedEndpoints
import anduin.tapir.AuthenticatedEndpoints.BaseAuthenticatedEndpoint
import anduin.tapir.endpoint.CommonParams.Empty
import sttp.tapir.*
import anduin.id.sa.{MappingDestinationId, SaProfileId}

object SaEndpoints extends AuthenticatedEndpoints {

  private val SaPath = "sa"

  // SaProfileService
  val addSaProfile: BaseAuthenticatedEndpoint[AddSaProfileParams, GeneralServiceException, SaProfileId] = {
    authEndpoint[AddSaProfileParams, GeneralServiceException, SaProfileId](
      SaPath / "addSaProfile"
    )
  }

  val updateSaProfile: BaseAuthenticatedEndpoint[UpdateSaProfileParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateSaProfileParams, GeneralServiceException, Unit](
      SaPath / "updateSaProfile"
    )
  }

  val exportProfileSaListToCsv: BaseAuthenticatedEndpoint[
    ExportProfileSaListToCsvParams,
    GeneralServiceException,
    ExportProfileSaListToCsvResponse
  ] = {
    authEndpoint[
      ExportProfileSaListToCsvParams,
      GeneralServiceException,
      ExportProfileSaListToCsvResponse
    ](
      SaPath / "exportProfileSaListToCsv"
    )
  }

  val getAllSaProfileInfos: BaseAuthenticatedEndpoint[Empty, GeneralServiceException, SaProfileBasicInfoList] = {
    authEndpoint[Empty, GeneralServiceException, SaProfileBasicInfoList](
      SaPath / "getAllSaProfileInfos"
    )
  }

  val getSaProfileByIds: BaseAuthenticatedEndpoint[SaProfileIdList, GeneralServiceException, SaProfileFullInfoList] = {
    authEndpoint[SaProfileIdList, GeneralServiceException, SaProfileFullInfoList](
      SaPath / "getSaProfileByIds"
    )
  }

  val parseSaListFromCsv: BaseAuthenticatedEndpoint[
    ParseSaListFromCsvParams,
    GeneralServiceException,
    ParseSaListFromCsvResponse
  ] = {
    authEndpoint[
      ParseSaListFromCsvParams,
      GeneralServiceException,
      ParseSaListFromCsvResponse
    ](
      SaPath / "parseSaListFromCsv"
    )
  }

  // MappingDestinationService
  val addMappingDestination: BaseAuthenticatedEndpoint[
    AddMappingDestinationParams,
    GeneralServiceException,
    MappingDestinationId
  ] = {
    authEndpoint[
      AddMappingDestinationParams,
      GeneralServiceException,
      MappingDestinationId
    ](
      SaPath / "addMappingDestination"
    )
  }

  val addSaDataTemplate: BaseAuthenticatedEndpoint[
    AddSaDataTemplateParams,
    GeneralServiceException,
    AddSaDataTemplateResponse
  ] = {
    authEndpoint[
      AddSaDataTemplateParams,
      GeneralServiceException,
      AddSaDataTemplateResponse
    ](SaPath / "addSaDataTemplate")
  }

  val getAllSaDataTemplates: BaseAuthenticatedEndpoint[
    GetAllSaDataTemplatesParams,
    GeneralServiceException,
    GetAllSaDataTemplatesResponse
  ] = {
    authEndpoint[
      GetAllSaDataTemplatesParams,
      GeneralServiceException,
      GetAllSaDataTemplatesResponse
    ](SaPath / "getAllSaDataTemplates")
  }

}
