// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.sa.endpoints

import java.time.Instant

import anduin.id.ontology.AsaId
import anduin.model.id.FileId
import anduin.model.common.user.UserId
import anduin.sa.model.{ReorderAction, ResourceDetails, SaMessage, SaSourceInfo, SaTemplateField}
import io.circe.Json

import anduin.circe.generic.semiauto.CirceCodec
import anduin.cue.model.CommonCueTypeId
import anduin.model.codec.ProtoCodecs.given
import anduin.id.sa.{MappingDestinationId, SaDataTemplateId, SaProfileId}
import anduin.sa.model.sadatatemplate.sadatatemplatemessage.SaDataTemplateMessage
import anduin.sa.model.saprofile.saprofilemessage.SaProfileMessage
import anduin.tapir.endpoint.EmptyEndpointValidationParams

// SA Profile
final case class QuerySaProfileBySaInfoResponse(
  saProfileInfoList: List[SaProfileInfoWithFilteredSaList]
) derives CirceCodec.WithDefaults

final case class SaProfileInfoWithFilteredSaList(
  profileId: SaProfileId,
  profileName: String,
  // List of filtered SAs
  relatedSaList: List[SaMessage]
) derives CirceCodec.WithDefaults

final case class SaProfileIdList(
  profileIds: List[SaProfileId]
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults

final case class AddSaProfileParams(
  profileName: String,
  profileDescription: String = "",
  saList: List[SaMessage]
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults

final case class UpdateSaProfileParams(
  saProfileId: SaProfileId,
  newProfileNameOpt: Option[String] = None,
  newProfileDescriptionOpt: Option[String] = None,
  toAddSaList: List[SaMessage] = List.empty,
  toRemoveSaAliasSet: Set[String] = Set.empty
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults {

  lazy val noChange: Boolean = newProfileNameOpt.isEmpty && newProfileDescriptionOpt.isEmpty &&
    toAddSaList.isEmpty && toRemoveSaAliasSet.isEmpty

}

final case class UpdateSingleSaParams(
  saProfileId: SaProfileId,
  alias: String,
  newDescriptionOpt: Option[String] = None,
  newOntologyAnnotationOpt: Option[String] = None,
  newAsaIdOpt: Option[AsaId] = None,
  newSaTypeOpt: Option[CommonCueTypeId] = None,
  newSaTypeJsonSchemaOpt: Option[Json] = None,
  newSourceInfoOpt: Option[SaSourceInfo] = None
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults {

  lazy val noChange: Boolean = newDescriptionOpt.isEmpty && newOntologyAnnotationOpt.isEmpty && newAsaIdOpt.isEmpty &&
    newSaTypeOpt.isEmpty && newSourceInfoOpt.isEmpty && newSaTypeJsonSchemaOpt.isEmpty

}

final case class SaProfileBasicInfo(
  saProfileId: SaProfileId,
  profileName: String,
  profileDescription: String,
  // Source IDs and their corresponding SA list count (Note: Revise to include more info as needed)
  saCountBySourceList: List[(Option[MappingDestinationId], Int)]
) derives CirceCodec.WithDefaults

final case class SaProfileBasicInfoList(
  profileInfoList: List[SaProfileBasicInfo]
) derives CirceCodec.WithDefaults

final case class SaProfileFullInfo(
  saProfileId: SaProfileId,
  profileName: String,
  profileDescription: String,
  saList: List[SaMessage]
) derives CirceCodec.WithDefaults {

  lazy val basicInfo: SaProfileBasicInfo = SaProfileBasicInfo(
    saProfileId = saProfileId,
    profileName = profileName,
    profileDescription = profileDescription,
    saCountBySourceList = saList.groupBy(_.sourceInfoOpt.map(_.sourceId)).view.mapValues(_.size).toList
  )

}

object SaProfileFullInfo {

  def fromSaProfileMsg(msg: SaProfileMessage): SaProfileFullInfo =
    SaProfileFullInfo(
      saProfileId = msg.id,
      profileName = msg.name,
      profileDescription = msg.description,
      saList = msg.saList.toList
    )

}

final case class SaProfileFullInfoList(
  profileInfoList: List[SaProfileFullInfo]
) derives CirceCodec.WithDefaults

final case class SingleSaProfileMappingResponse(
  saProfileInfo: SaProfileFullInfo,
  fieldToSaMap: Map[String, String],
  lastUpdatedByOpt: Option[UserId],
  lastUpdatedAtOpt: Option[Instant]
) derives CirceCodec.WithDefaults

final case class QuerySaProfileMappingResponse(
  results: List[SingleSaProfileMappingResponse]
) derives CirceCodec.WithDefaults

final case class ParseSaListFromCsvParams(
  csvFileId: FileId,
  aliasIdx: Int,
  descriptionIdx: Int,
  optionParentIdx: Int,
  saTypeIdx: Int = -1,
  asaIdx: Int = -1,
  ontologyAnnotationIdx: Int = -1,
  isRepeatableIdx: Int = -1,
  startRowIdx: Int = 0
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults

final case class ParseSaListFromCsvResponse(
  saList: List[SaMessage]
) derives CirceCodec.WithDefaults

final case class ExportProfileSaListToCsvParams(
  saProfileId: SaProfileId,
  baseExportedFileNameOpt: Option[String] = None
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults

final case class ExportProfileSaListToCsvResponse(
  exportedCsvFileIdOpt: Option[FileId]
) derives CirceCodec.WithDefaults

// SA Data Template
final case class AddSaDataTemplateParams(
  name: String,
  fieldList: List[SaTemplateField],
  description: String = ""
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults

final case class AddSaDataTemplateResponse(
  template: SaDataTemplateMessage
) derives CirceCodec.WithDefaults

final case class UpdateSaDataTemplateMetaParams(
  templateId: SaDataTemplateId,
  newTemplateNameOpt: Option[String] = None,
  newTemplateDescriptionOpt: Option[String] = None
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults

final case class AddSaDataTemplateFieldsParams(
  templateId: SaDataTemplateId,
  toAddFields: List[SaTemplateField],
  addFieldAtIndexOpt: Option[Int] = None
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults

final case class RemoveSaDataTemplateFieldsParams(
  templateId: SaDataTemplateId,
  toRemoveFields: Set[SaTemplateField]
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults

final case class ReorderSaDataTemplateFieldsParams(
  templateId: SaDataTemplateId,
  reorderActions: List[ReorderAction]
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults

final case class UpdateSingleTemplateFieldParams(
  templateId: SaDataTemplateId,
  resourceId: String,
  alias: String,
  newLabelOpt: Option[String]
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults

final case class QuerySaDataTemplateResponse(
  templateList: List[SaDataTemplateMessage]
) derives CirceCodec.WithDefaults

final case class GetAllSaDataTemplatesParams(
  ignoreDeleted: Boolean = true
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults

final case class GetAllSaDataTemplatesResponse(
  templateList: List[SaDataTemplateMessage]
) derives CirceCodec.WithDefaults

// For each SaProfile that the template is mapped to, return Map[SaTemplateField, <SaProfile alias>]
final case class QueryDataTemplateSaMappingResponse(
  saProfileMappingSet: Set[(SaProfileId, Set[(SaTemplateField, String)])]
) derives CirceCodec.WithDefaults

// MappingDestination
final case class AddMappingDestinationParams(
  resourceId: String,
  resourceDescription: String,
  resourceDetails: ResourceDetails
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults
