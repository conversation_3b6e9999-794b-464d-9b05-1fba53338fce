// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.gondor.server

import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter

import anduin.sa.endpoints.SaEndpoints.*
import anduin.sa.service.{MappingDestinationService, SaDataTemplateService, SaProfileService}
import anduin.portaluser.PortalUserService
import anduin.sa.endpoints.{SaProfileBasicInfoList, SaProfileFullInfo, SaProfileFullInfoList}
import anduin.sa.service.permission.SaEndpointValidatorServer
import anduin.tapir.server.EndpointServer.TapirServerService
import anduin.tapir.server.AuthenticatedValidationEndpointServer
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.authorization.AuthorizationService

final class SaEndpointServer(
  protected val backendConfig: GondorBackendConfig,
  protected val authorizationService: AuthorizationService,
  saProfileService: SaProfileService,
  mappingDestinationService: MappingDestinationService,
  saDataTemplateService: SaDataTemplateService,
  override protected val interpreter: ArmeriaZioServerInterpreter[Any]
)(
  using val portalUserService: PortalUserService
) extends AuthenticatedValidationEndpointServer
    with SaEndpointValidatorServer {

  // SaProfileService
  private[server] val addSaProfileService = validateRouteCatchError(addSaProfile, saManagementWriteValidator) {
    (params, ctx) =>
      saProfileService.addSaProfile(ctx.actor.userId, params, ignorePermissionCheck = true)
  }

  private[server] val updateSaProfileService = validateRouteCatchError(updateSaProfile, saManagementWriteValidator) {
    (params, ctx) =>
      saProfileService.updateSaProfile(ctx.actor.userId, params, ignorePermissionCheck = true)
  }

  private[server] val exportProfileSaListToCsvService =
    validateRouteCatchError(exportProfileSaListToCsv, saManagementReadValidator) { (params, ctx) =>
      saProfileService.exportProfileSaListToCsv(ctx.actor.userId, params)
    }

  private[server] val getAllSaProfileInfosService =
    validateRouteCatchError(getAllSaProfileInfos, saManagementReadValidator) { (_, ctx) =>
      saProfileService.getAllSaProfileInfos(ctx.actor.userId).map(SaProfileBasicInfoList(_))
    }

  private[server] val getSaProfileByIdsService = validateRouteCatchError(getSaProfileByIds, saManagementReadValidator) {
    (params, ctx) =>
      saProfileService.getSaProfileByIds(ctx.actor.userId, params.profileIds).map { saProfileMsgList =>
        SaProfileFullInfoList(
          saProfileMsgList.map { saProfileMsg =>
            SaProfileFullInfo(
              saProfileId = saProfileMsg.id,
              profileName = saProfileMsg.name,
              profileDescription = saProfileMsg.description,
              saList = saProfileMsg.saList.toList
            )
          }
        )
      }
  }

  private[server] val parseSaListFromCsvService = validateRouteCatchError(parseSaListFromCsv, saManagementReadValidator) {
    (params, ctx) => saProfileService.parseSaListFromCsv(ctx.actor.userId, params)
  }

  // MappingDestinationService
  private[server] val addMappingDestinationService =
    validateRouteCatchError(addMappingDestination, saManagementWriteValidator) { (params, ctx) =>
      mappingDestinationService.addMappingDestination(
        ctx.actor.userId,
        params.resourceId,
        params.resourceDescription,
        params.resourceDetails
      )
    }

  private[server] val addSaDataTemplateService =
    validateRouteCatchError(addSaDataTemplate, saManagementWriteValidator) { (params, ctx) =>
      saDataTemplateService.addSaDataTemplate(ctx.actor.userId, params, ignorePermissionCheck = true)
    }

  private[server] val getAllSaDataTemplatesService =
    validateRouteCatchError(getAllSaDataTemplates, saManagementReadValidator) { (params, ctx) =>
      saDataTemplateService.getAllSaDataTemplates(ctx.actor.userId, params.ignoreDeleted, ignorePermissionCheck = true)
    }

  val services: List[TapirServerService] = List(
    addSaProfileService,
    updateSaProfileService,
    exportProfileSaListToCsvService,
    getAllSaProfileInfosService,
    getSaProfileByIdsService,
    parseSaListFromCsvService,
    addMappingDestinationService,
    addSaDataTemplateService,
    getAllSaDataTemplatesService
  )

}
