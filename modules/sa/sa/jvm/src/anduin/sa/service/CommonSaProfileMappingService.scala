// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.sa.service

import java.time.Instant

import zio.{Task, ZIO}
import com.apple.foundationdb.record.TupleRange

import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.id.role.portal.PortalSectionId
import anduin.id.sa.{MappingDestinationId, SaProfileId}
import anduin.model.common.user.UserId
import anduin.sa.model.saprofilemapping.*
import anduin.sa.model.saprofilemapping.saprofilemappingactivitymessage.SaProfileMappingActivityMessage
import anduin.sa.model.saprofilemapping.saprofilemappingmessage.SaProfileMappingMessage
import anduin.sa.service.database.SaProfileMappingStoreProvider.SaProfileMappingPrimaryKey
import anduin.sa.service.database.{
  SaProfileMappingActivityStoreOperations,
  SaProfileMappingStoreOperations,
  SaProfileMappingStoreProvider
}
import com.anduin.stargazer.service.GondorConfig
import com.anduin.stargazer.service.utils.ZIOUtils

final case class CommonSaProfileMappingService(
  gondorConfig: GondorConfig,
  saProfileService: SaProfileService
) {

  private val portalUserService = saProfileService.portalUserService

  // NOTE: Regarding permission
  // - For functions related to a specific type of space/space version (e.g., PDF document, form) -> leave permission checks to the SaProfileMappingService implementation of those spaces
  // - For query functions across spaces, check read permission on PortalSectionId.AsaManagement for now
  private def upsertSaProfileMapping(
    actorId: UserId,
    saProfileId: SaProfileId,
    destinationId: MappingDestinationId,
    activitiesToAdd: Seq[SaProfileMappingActivity],
    mappingToAdd: Map[String, String] = Map.empty,
    mappedFieldsToRemove: Set[String] = Set.empty,
    clearPreviousMapping: Boolean = false
  ): Task[Unit] = {
    for {
      validSaAliases <-
        if (mappingToAdd.nonEmpty) {
          saProfileService
            .getSaProfileByIds(actorId = actorId, ids = List(saProfileId))
            .map(_.flatMap(_.saList.map(_.alias)).distinct)
        } else {
          ZIO.succeed(List.empty[String])
        }
      _ <- FDBRecordDatabase.transact(
        FDBOperations[(SaProfileMappingStoreOperations, SaProfileMappingActivityStoreOperations)].Production
      ) { case (mappingOps, mappingActivityOps) =>
        // Filter mappings with valid SA aliases
        val validMappingToAdd = mappingToAdd.filter { case (_, v) => validSaAliases.contains(v) }
        val lastUpdatedInfoOpt = activitiesToAdd
          .flatMap {
            case addAct: AddSaProfileMappingActivity       => addAct.occurredAt.map(addAct.actor -> _)
            case removeAct: RemoveSaProfileMappingActivity => removeAct.occurredAt.map(removeAct.actor -> _)
            case importAct: ImportSaProfileMappingActivity => importAct.occurredAt.map(importAct.actor -> _)
            // Other activities that don't use upsertSaProfileMapping
            // (this pattern match should be checked when new activities are added -> didn't use match any here)
            case _: TransferSaProfileMappingActivity          => None
            case _: DeleteSaProfileMappingPermanentlyActivity => None
            case SaProfileMappingActivity.Empty               => None
          }
          .maxByOption(_._2.toEpochMilli)

        for {
          _ <- mappingOps.upsert(
            saProfileId = saProfileId,
            destinationId = destinationId,
            defaultData = SaProfileMappingMessage(
              saProfileId = saProfileId,
              destinationId = destinationId,
              fieldToSaMap = validMappingToAdd,
              lastUpdatedByOpt = lastUpdatedInfoOpt.map(_._1),
              lastUpdatedAtOpt = lastUpdatedInfoOpt.map(_._2)
            ),
            updateFunc = prevMsg => {
              val newFieldToSaMap = if (clearPreviousMapping) {
                validMappingToAdd
              } else {
                prevMsg.fieldToSaMap.view.filterKeys(!mappedFieldsToRemove.contains(_)).toMap ++ validMappingToAdd
              }
              prevMsg.copy(
                fieldToSaMap = newFieldToSaMap,
                lastUpdatedByOpt = lastUpdatedInfoOpt.map(_._1),
                lastUpdatedAtOpt = lastUpdatedInfoOpt.map(_._2)
              )
            }
          )
          _ <- mappingActivityOps.upsert(
            saProfileId = saProfileId,
            destinationId = destinationId,
            defaultData = SaProfileMappingActivityMessage(
              saProfileId = saProfileId,
              destinationId = destinationId,
              activities = activitiesToAdd
            ),
            updateFunc = prevMsg => prevMsg.copy(activities = prevMsg.activities ++ activitiesToAdd)
          )
        } yield ()
      }
    } yield ()
  }

  def scanToUpdateSaProfileMapping(
    actorId: UserId,
    updateFunc: List[(SaProfileMappingPrimaryKey, SaProfileMappingMessage)] => Task[Unit]
  ): Task[Unit] = {
    for {
      _ <- portalUserService.validateWritePermission(actorId, PortalSectionId.AsaManagement)
      _ <- FDBRecordDatabase.largeScan(
        SaProfileMappingStoreProvider.Production,
        SaProfileMappingStoreProvider.primaryKeyMapping,
        TupleRange.ALL,
        fn = (_, list) => updateFunc(list),
        100
      )
    } yield ()
  }

  def addSaProfileMapping(
    actorId: UserId,
    saProfileId: SaProfileId,
    destinationId: MappingDestinationId,
    mappingToAdd: Map[String, String]
  ): Task[Unit] = {
    for {
      _ <- upsertSaProfileMapping(
        actorId = actorId,
        saProfileId = saProfileId,
        destinationId = destinationId,
        mappingToAdd = mappingToAdd,
        activitiesToAdd = Seq(
          AddSaProfileMappingActivity(
            actor = actorId,
            occurredAt = Option(Instant.now),
            addedMapping = mappingToAdd
          )
        )
      )
    } yield ()
  }

  def importSaProfileMapping(
    actorId: UserId,
    saProfileId: SaProfileId,
    destinationId: MappingDestinationId,
    mappingToImport: Map[String, String],
    clearPreviousMapping: Boolean
  ): Task[Unit] = {
    for {
      _ <- upsertSaProfileMapping(
        actorId = actorId,
        saProfileId = saProfileId,
        destinationId = destinationId,
        mappingToAdd = mappingToImport,
        clearPreviousMapping = clearPreviousMapping,
        activitiesToAdd = Seq(
          ImportSaProfileMappingActivity(
            actor = actorId,
            occurredAt = Option(Instant.now),
            importedMapping = mappingToImport,
            clearPreviousMapping = clearPreviousMapping
          )
        )
      )
    } yield ()
  }

  def transferSingleSaProfileMapping(
    actorId: UserId,
    saProfileId: SaProfileId,
    srcDestinationId: MappingDestinationId,
    targetDestinationId: MappingDestinationId
  ): Task[Unit] = {
    FDBRecordDatabase.transact(
      FDBOperations[(SaProfileMappingStoreOperations, SaProfileMappingActivityStoreOperations)].Production
    ) { case (mappingOps, mappingActivityOps) =>
      for {
        transferRes <- mappingOps.transferSingleProfileMappings(
          saProfileId,
          srcDestinationId,
          targetDestinationId,
          actorIdOpt = Some(actorId)
        )
        transferActivity = TransferSaProfileMappingActivity(
          actor = actorId,
          occurredAt = Option(Instant.now),
          srcDestinationId = srcDestinationId,
          transferredMapping = transferRes
        )
        _ <- mappingActivityOps.upsert(
          saProfileId = saProfileId,
          destinationId = targetDestinationId,
          defaultData = SaProfileMappingActivityMessage(
            saProfileId = saProfileId,
            destinationId = targetDestinationId,
            activities = Seq(transferActivity)
          ),
          updateFunc = prevMsg => prevMsg.copy(activities = prevMsg.activities :+ transferActivity)
        )
      } yield ()
    }
  }

  def transferAllSaProfileMappings(
    actorId: UserId,
    srcDestinationId: MappingDestinationId,
    targetDestinationId: MappingDestinationId
  ): Task[Unit] = {
    FDBRecordDatabase.transact(
      FDBOperations[(SaProfileMappingStoreOperations, SaProfileMappingActivityStoreOperations)].Production
    ) { case (mappingOps, mappingActivityOps) =>
      for {
        transferRes <- mappingOps.transferAllProfilesMappings(
          srcDestinationId,
          targetDestinationId,
          actorIdOpt = Some(actorId)
        )
        _ <- RecordIO.traverse(transferRes.toList) { case (saProfileId, singleProfileTransferRes) =>
          val singleProfileTransferActivity = TransferSaProfileMappingActivity(
            actor = actorId,
            occurredAt = Option(Instant.now),
            srcDestinationId = srcDestinationId,
            transferredMapping = singleProfileTransferRes
          )
          mappingActivityOps.upsert(
            saProfileId = saProfileId,
            destinationId = targetDestinationId,
            defaultData = SaProfileMappingActivityMessage(
              saProfileId = saProfileId,
              destinationId = targetDestinationId,
              activities = Seq(singleProfileTransferActivity)
            ),
            updateFunc = prevMsg => prevMsg.copy(activities = prevMsg.activities :+ singleProfileTransferActivity)
          )
        }
      } yield ()
    }
  }

  def removeSaProfileMapping(
    actorId: UserId,
    saProfileId: SaProfileId,
    destinationId: MappingDestinationId,
    mappedFieldsToRemove: Set[String]
  ): Task[Unit] = {
    for {
      _ <- ZIOUtils.when(mappedFieldsToRemove.nonEmpty)(
        upsertSaProfileMapping(
          actorId = actorId,
          saProfileId = saProfileId,
          destinationId = destinationId,
          mappedFieldsToRemove = mappedFieldsToRemove,
          activitiesToAdd = Seq(
            RemoveSaProfileMappingActivity(
              actor = actorId,
              occurredAt = Option(Instant.now),
              removedFieldNames = mappedFieldsToRemove
            )
          )
        )
      )
    } yield ()
  }

  def deleteSaProfileMappingPermanently(
    actorId: UserId,
    saProfileId: SaProfileId,
    destinationId: MappingDestinationId
  ): Task[Unit] = {
    FDBRecordDatabase.transact(
      FDBOperations[(SaProfileMappingStoreOperations, SaProfileMappingActivityStoreOperations)].Production
    ) { case (mappingOps, mappingActivityOps) =>
      val deleteActivity = DeleteSaProfileMappingPermanentlyActivity(actorId, Option(Instant.now))
      for {
        _ <- mappingActivityOps.upsert(
          saProfileId = saProfileId,
          destinationId = destinationId,
          defaultData = SaProfileMappingActivityMessage(
            saProfileId = saProfileId,
            destinationId = destinationId,
            activities = Seq(deleteActivity)
          ),
          updateFunc = prevMsg => prevMsg.copy(activities = prevMsg.activities :+ deleteActivity)
        )
        _ <- mappingOps.deletePermanently(saProfileId, destinationId)
      } yield ()
    }
  }

  def getSaProfileMapping(
    saProfileId: SaProfileId,
    destinationId: MappingDestinationId
  ): Task[Option[SaProfileMappingMessage]] = {
    FDBRecordDatabase.transact(SaProfileMappingStoreOperations.Production) { ops =>
      ops.getOpt(saProfileId, destinationId)
    }
  }

  def querySaProfileMappingByProfileId(
    actorId: UserId,
    saProfileId: SaProfileId,
    ignorePermissionCheck: Boolean = false
  ): Task[List[SaProfileMappingMessage]] = {
    for {
      // Across destinations -> check AsaManagement permission
      _ <- ZIO.whenDiscard(!ignorePermissionCheck)(
        portalUserService.validateReadPermission(actorId, PortalSectionId.AsaManagement)
      )
      res <- FDBRecordDatabase.transact(SaProfileMappingStoreOperations.Production) {
        _.queryBySaProfileId(saProfileId)
      }
    } yield res
  }

  def querySaProfileMappingByDestinationId(
    destinationId: MappingDestinationId
  ): Task[List[SaProfileMappingMessage]] = {
    FDBRecordDatabase.transact(SaProfileMappingStoreOperations.Production) {
      _.queryByDestinationId(destinationId)
    }
  }

  def querySaProfileMappingBySingleSaAlias(
    actorId: UserId,
    saAlias: String,
    ignorePermissionCheck: Boolean = false
  ): Task[List[SaProfileMappingMessage]] = {
    for {
      _ <- ZIO.whenDiscard(!ignorePermissionCheck)(
        portalUserService.validateReadPermission(actorId, PortalSectionId.AsaManagement)
      )
      res <- FDBRecordDatabase.transact(SaProfileMappingStoreOperations.Production) {
        _.queryBySingleSaAlias(saAlias)
      }
    } yield res
  }

}
