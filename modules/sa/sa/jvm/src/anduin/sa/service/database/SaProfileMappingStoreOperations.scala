// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.sa.service.database

import java.time.Instant
import com.apple.foundationdb.record.{IndexScanType, TupleRange}

import anduin.fdb.record.FDBOperations
import anduin.fdb.record.model.{FDBTupleConverter, RecordIO, RecordReadTask, RecordTask}
import anduin.id.sa.{MappingDestinationId, SaProfileId}
import anduin.model.common.user.UserId
import anduin.sa.model.saprofilemapping.saprofilemappingmessage.SaProfileMappingMessage
import anduin.sa.service.database.SaProfileMappingStoreProvider.{*, given}

private[service] final case class SaProfileMappingStoreOperations(
  store: SaProfileMappingStoreProvider.Store
) {

  def getOpt(saProfileId: SaProfileId, destinationId: MappingDestinationId)
    : RecordReadTask[Option[SaProfileMappingMessage]] = {
    val key = SaProfileMappingPrimaryKey(saProfileId, destinationId)
    store.getOpt(key)
  }

  def add(record: SaProfileMappingMessage): RecordTask[Unit] = {
    store.create(record).unit
  }

  def update(
    saProfileId: SaProfileId,
    destinationId: MappingDestinationId,
    updateFunc: SaProfileMappingMessage => SaProfileMappingMessage
  ): RecordTask[Unit] = {
    val key = SaProfileMappingPrimaryKey(saProfileId, destinationId)
    store.update(key, updateFunc).unit
  }

  def upsert(
    saProfileId: SaProfileId,
    destinationId: MappingDestinationId,
    defaultData: SaProfileMappingMessage,
    updateFunc: SaProfileMappingMessage => SaProfileMappingMessage
  ): RecordTask[Unit] = {
    val key = SaProfileMappingPrimaryKey(saProfileId, destinationId)
    store
      .upsert(
        key = key,
        default = defaultData,
        updateFn = updateFunc
      )
      .unit
  }

  // Return the transferred SA mappings
  def transferSingleProfileMappings(
    saProfileId: SaProfileId,
    srcDestinationId: MappingDestinationId,
    targetDestinationId: MappingDestinationId,
    actorIdOpt: Option[UserId] = None
  ): RecordTask[Map[String, String]] = {
    for {
      srcProfileMsgOpt <- getOpt(saProfileId, srcDestinationId)
      transferredMappingsOpt <- RecordIO.traverseOption(srcProfileMsgOpt) { srcProfileMsg =>
        upsert(
          saProfileId,
          targetDestinationId,
          srcProfileMsg.copy(
            destinationId = targetDestinationId,
            lastUpdatedByOpt = actorIdOpt,
            lastUpdatedAtOpt = Some(Instant.now())
          ),
          prevMsg =>
            prevMsg.copy(
              fieldToSaMap = prevMsg.fieldToSaMap ++ srcProfileMsg.fieldToSaMap,
              lastUpdatedByOpt = actorIdOpt,
              lastUpdatedAtOpt = Some(Instant.now())
            )
        ).map(_ => Option(srcProfileMsg.fieldToSaMap))
      }
    } yield transferredMappingsOpt.getOrElse(Map.empty)
  }

  // Return the transferred profile IDs and their corresponding mappings
  def transferAllProfilesMappings(
    srcDestinationId: MappingDestinationId,
    targetDestinationId: MappingDestinationId,
    actorIdOpt: Option[UserId] = None
  ): RecordTask[Map[SaProfileId, Map[String, String]]] = {
    for {
      srcProfileMsgs <- queryByDestinationId(srcDestinationId)
      res <- RecordIO.traverse(srcProfileMsgs) { srcProfileMsg =>
        upsert(
          srcProfileMsg.saProfileId,
          targetDestinationId,
          srcProfileMsg.copy(
            destinationId = targetDestinationId,
            lastUpdatedByOpt = actorIdOpt,
            lastUpdatedAtOpt = Some(Instant.now())
          ),
          prevMsg =>
            prevMsg.copy(
              fieldToSaMap = prevMsg.fieldToSaMap ++ srcProfileMsg.fieldToSaMap,
              lastUpdatedByOpt = actorIdOpt,
              lastUpdatedAtOpt = Some(Instant.now())
            )
        ).map(_ => srcProfileMsg.saProfileId -> srcProfileMsg.fieldToSaMap)
      }
    } yield res.toMap
  }

  def idExisted(saProfileId: SaProfileId, destinationId: MappingDestinationId): RecordReadTask[Boolean] = {
    val key = SaProfileMappingPrimaryKey(saProfileId, destinationId)
    store.exist(key)
  }

  def queryBySaProfileId(
    saProfileId: SaProfileId
  ): RecordReadTask[List[SaProfileMappingMessage]] = {
    val saProfileIdKey = SaProfileMappingStoreProvider.saProfileIdTupleConverter.toTuple(saProfileId)
    val tupleRange = TupleRange.allOf(saProfileIdKey)

    store
      .scanIndexRecordsL(
        SaProfileMappingStoreProvider.saProfileIdIndexMapping,
        tupleRange,
        IndexScanType.BY_VALUE
      )
  }

  def queryByDestinationId(
    destinationId: MappingDestinationId
  ): RecordReadTask[List[SaProfileMappingMessage]] = {
    val destinationIdKey = SaProfileMappingStoreProvider.mappingDestinationIdTupleConverter.toTuple(destinationId)
    val tupleRange = TupleRange.allOf(destinationIdKey)

    store
      .scanIndexRecordsL(
        SaProfileMappingStoreProvider.destinationIdIndexMapping,
        tupleRange,
        IndexScanType.BY_VALUE
      )
  }

  def queryBySingleSaAlias(
    saAlias: String
  ): RecordReadTask[List[SaProfileMappingMessage]] = {
    val saAliasKey = FDBTupleConverter.string.toTuple(saAlias)
    val tupleRange = TupleRange.allOf(saAliasKey)

    store
      .scanIndexRecordsL(
        SaProfileMappingStoreProvider.singleSaAliasIndexMapping,
        tupleRange,
        IndexScanType.BY_VALUE
      )
  }

  def deletePermanently(saProfileId: SaProfileId, destinationId: MappingDestinationId): RecordTask[Boolean] = {
    val key = SaProfileMappingPrimaryKey(saProfileId, destinationId)
    store.delete(key)
  }

}

private[service] object SaProfileMappingStoreOperations
    extends FDBOperations.Single[SaProfileMappingStoreProvider.RecordEnum, SaProfileMappingStoreOperations](
      SaProfileMappingStoreProvider
    )
