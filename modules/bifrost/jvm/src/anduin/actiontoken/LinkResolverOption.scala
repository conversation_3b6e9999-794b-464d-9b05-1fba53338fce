// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.actiontoken

import anduin.account.authentication.LoginService
import anduin.account.protocol.BifrostAuthenticationProtocol.EnterpriseLoginUserLinkedConfigInfo
import anduin.account.protocol.BifrostCommonProtocol
import anduin.environment.EnvironmentPolicyCommonProtocols.EnvironmentAuthenticationAction.ActionMethod
import anduin.environment.EnvironmentPolicyCommonProtocols.EnvironmentAuthenticationDecision
import anduin.id.environment.EnvironmentId
import anduin.id.offering.GlobalOfferingId
import anduin.linkrestriction.LinkRestrictionProtocol
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.protobuf.account.{EnterpriseLoginConfig, EnterpriseLoginEmailDomainBinding}
import zio.{Task, ZIO}

sealed trait LinkResolverOption derives CanEqual {
  def prepare: Task[LinkRestrictionProtocol.LinkResolverOptionData]
}

object LinkResolverOption {

  final case class ResolveByAuthenticationForm(
    userId: UserId,
    loginService: LoginService,
    environmentIdOpt: Option[EnvironmentId],
    offeringIdOpt: Option[GlobalOfferingId]
  ) extends LinkResolverOption {

    private def getNormalAuthenticator: Task[BifrostCommonProtocol.LoginAuthenticator] = {
      for {
        userIdToken <- loginService.createUserIdToken(userId, None)
      } yield BifrostCommonProtocol.LoginAuthenticator.Normal(userIdToken)
    }

    private def getSSOAuthenticator(ssoConfig: EnterpriseLoginConfig, emailAddress: EmailAddress)
      : Task[BifrostCommonProtocol.LoginAuthenticator] = {
      for {
        userIdToken <- loginService.createUserIdToken(userId, None)
      } yield BifrostCommonProtocol.LoginAuthenticator.SSOEnforced(
        userIdTokenOpt = Some(userIdToken),
        domain = emailAddress.domain.value,
        ssoConfig = EnterpriseLoginUserLinkedConfigInfo(
          configId = ssoConfig.id,
          providerName = ssoConfig.providerName,
          providerLogoUrl = ssoConfig.providerLogoUrl
        )
      )
    }

    private def getAuthenticatorWithoutEnvironment(
      ssoBindingOpt: Option[EnterpriseLoginEmailDomainBinding],
      emailAddress: EmailAddress
    ): Task[BifrostCommonProtocol.LoginAuthenticator] = {
      ssoBindingOpt.fold(getNormalAuthenticator) { ssoBinding =>
        for {
          ssoConfigOpt <- loginService.enterpriseService.getOptEnterpriseLoginConfig(ssoBinding.configId)
          authenticator <- ssoConfigOpt.fold(getNormalAuthenticator) { ssoConfig =>
            getSSOAuthenticator(ssoConfig, emailAddress)
          }
        } yield authenticator
      }
    }

    private def getAuthenticatorWithEnvironment(
      environmentAuthenticationDecision: EnvironmentAuthenticationDecision,
      ssoBindingOpt: Option[EnterpriseLoginEmailDomainBinding],
      emailAddress: EmailAddress
    ): Task[BifrostCommonProtocol.LoginAuthenticator] = {
      for {
        userIdToken <- loginService.createUserIdToken(userId, None)
        anduinAuthenticatorOpt <- loginService.environmentAuthenticationService
          .decisionToActionMethod(environmentAuthenticationDecision)
          .fold(ZIO.none) {
            case _: ActionMethod.AnduinAuthentication =>
              getAuthenticatorWithoutEnvironment(ssoBindingOpt, emailAddress).map(Some(_))
            case ActionMethod.UseProvider(_) => ZIO.none
          }
      } yield BifrostCommonProtocol.LoginAuthenticator.AuthenticateByEnvironment(
        userIdTokenOpt = Some(userIdToken),
        environmentAuthentication = environmentAuthenticationDecision,
        anduinAuthenticator = anduinAuthenticatorOpt,
        immediateRedirectToSSO = true
      )
    }

    override def prepare: Task[LinkRestrictionProtocol.LinkResolverOptionData] = {
      for {
        userEmail <- loginService.userProfileService.getEmailAddress(userId)
        ssoBindingOpt <- loginService.enterpriseService.getOptEmailDomainBinding(userEmail.domain.value)
        environmentAuthenticationDecisionOpt <- loginService.environmentAuthenticationService
          .getEnvironmentAuthenticationDecision(
            userEmail,
            Some(userId),
            environmentIdOpt,
            offeringIdOpt
          )
        hasPassword <- loginService.userProfileService.hasPassword(userId)
        hasMFA <- loginService.mfaCoreService.hasMFA(userId)
        authenticator <- environmentAuthenticationDecisionOpt.fold {
          getAuthenticatorWithoutEnvironment(ssoBindingOpt, userEmail)
        } { environmentAuthenticationDecision =>
          getAuthenticatorWithEnvironment(environmentAuthenticationDecision, ssoBindingOpt, userEmail)
        }

      } yield LinkRestrictionProtocol.LinkResolverOptionData.ResolveByAuthenticationData(
        authenticator = authenticator,
        hasPassword = hasPassword,
        enablePrimaryEmailOTP = !hasMFA
      )
    }

  }

}
