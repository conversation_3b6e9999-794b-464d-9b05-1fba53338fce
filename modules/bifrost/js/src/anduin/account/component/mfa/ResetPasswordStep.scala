// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.account.component.mfa

import anduin.account.component.recovery.CompleteResetPasswordForm
import com.anduin.stargazer.client.services.account.AccountClient
import design.anduin.components.modal.ModalBody
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.toast.Toast
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellDynamicChildrenR

import com.anduin.stargazer.service.account.*
import com.raquo.laminar.api.L.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.anduin.stargazer.client.utils.ZIOUtils

final case class ResetPasswordStep(
  emailToken: String,
  isResetPassword: Boolean,
  onSuccess: String => Callback,
  close: Callback
) {
  def apply(): VdomElement = ResetPasswordStep.component(this)
}

object ResetPasswordStep {

  type Props = ResetPasswordStep

  final case class State(
    initData: Option[Either[ExchangeVerifyEmailTokenException, ExchangeVerifyEmailTokenResponse]]
  )

  private final class Backend(scope: BackendScope[Props, State]) {

    private val errorWell = (new WellDynamicChildrenR[ExchangeVerifyEmailTokenException])()

    private def renderError(error: ExchangeVerifyEmailTokenException) = {
      errorWell(
        style = Well.Style.Danger()
      )(
        trackValue = error,
        renderChildren = errorSignal => {
          div(
            child <-- errorSignal.map {
              case ExchangeVerifyEmailTokenException.InvalidToken => MFACommonComponents.InvalidTokenError
              case ExchangeVerifyEmailTokenException.ServerError  => MFACommonComponents.ServerError
            }
          )
        }
      )
    }

    private def resetPasswordSuccess(props: Props, successToken: String): Callback = {
      ZIOUtils.toReactCallbackWithErrorHandler(
        AccountClient
          .exchangeResetPasswordToken(ExchangeResetPasswordTokenParams(successToken))
          .map(
            _.fold(
              error =>
                Toast.errorCallback(
                  error match {
                    case ExchangeResetPasswordTokenException.InvalidToken => MFACommonComponents.InvalidTokenError
                    case ExchangeResetPasswordTokenException.ServerError  => MFACommonComponents.ServerError
                  }
                ),
              response => props.onSuccess(response.reverifyPasswordToken)
            )
          )
      )
    }

    private def renderForm(props: Props, response: ExchangeVerifyEmailTokenResponse) = {
      CompleteResetPasswordForm(
        token = response.resetPasswordToken,
        isCreatePassword = props.isResetPassword,
        onSuccess = successToken => resetPasswordSuccess(props, successToken)
      )()
    }

    def render(props: Props, state: State): VdomElement = {
      <.div(
        MFAModalHeader(
          title = if (props.isResetPassword) "Reset password" else "Set password",
          isClosable = true,
          close = props.close
        )(),
        ModalBody()(
          state.initData.fold(BlockIndicatorR()()) {
            _.fold(
              renderError,
              response => renderForm(props, response)
            )
          }
        )
      )
    }

    def didMount(props: Props): Callback = {
      ZIOUtils.toReactCallbackWithErrorHandler(
        AccountClient
          .exchangeVerifyEmailToken(ExchangeVerifyEmailTokenParams(props.emailToken))
          .map(response => scope.modState(_.copy(initData = Some(response))))
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(
      State(
        initData = None
      )
    )
    .renderBackend[Backend]
    .componentDidMount(scope => scope.backend.didMount(scope.props))
    .build

}
