// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.account.component.mfa

import anduin.account.protocol.BifrostSetupMFAProtocol.{SetDefaultMethodException, SetupMFAMethodException}
import anduin.mfa.MFAProtocol
import design.anduin.components.toast.Toast
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellDynamicChildrenR

import com.raquo.laminar.api.L.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class FinishSetupMethodStep(
  setupMFAToken: String,
  methodConfig: MFAProtocol.EnableMFAMethodConfig,
  close: Callback
) {
  def apply(): VdomElement = FinishSetupMethodStep.component(this)
}

object FinishSetupMethodStep {

  type Props = FinishSetupMethodStep

  final case class State(
    error: Option[SetupMFAMethodException]
  )

  private final class Backend(scope: BackendScope[Props, State]) {

    private val errorWell = (new WellDynamicChildrenR[SetupMFAMethodException])()

    private def renderForm(props: Props): VdomElement = {
      FinishSetupMethodForm(
        setupMFAToken = props.setupMFAToken,
        methodConfig = props.methodConfig,
        onChangeDefaultMethodError = error =>
          Toast.errorCallback(
            error match {
              case SetDefaultMethodException.InvalidToken => MFACommonComponents.InvalidTokenError
              case SetDefaultMethodException.ServerError  => MFACommonComponents.ServerError
            }
          ),
        onChangeDefaultMethodSucess = props.close,
        onInitError = initError => scope.modState(_.copy(error = Some(initError))),
        onContinue = _ => props.close,
        close = Some(props.close)
      )()
    }

    private def renderError(error: SetupMFAMethodException): VdomElement = {
      errorWell(
        style = Well.Style.Danger()
      )(
        trackValue = error,
        renderChildren = errorSignal => {
          div(
            child <-- errorSignal.map {
              case SetupMFAMethodException.InvalidToken => MFACommonComponents.InvalidTokenError
              case SetupMFAMethodException.ServerError  => MFACommonComponents.ServerError
            }
          )
        }
      )
    }

    def render(props: Props, state: State): VdomElement = {
      state.error.fold(renderForm(props))(renderError)
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(
      State(
        error = None
      )
    )
    .renderBackend[Backend]
    .build

}
