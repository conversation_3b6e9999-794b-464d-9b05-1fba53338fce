// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.ria.client.fetch

import com.raquo.laminar.api.L.*
import design.anduin.components.toast.Toast
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.id.ria.{RiaEntityId, RiaFundGroupId}
import anduin.protobuf.ria.{RiaEntityFundAdvisorRole, RiaEntityUserRole}
import anduin.ria.RiaClient
import anduin.ria.endpoints.*
import anduin.ria.client.fetch.CurrentUserRiaPermissionProvider.*

final case class CurrentUserRiaPermissionProvider() {

  def apply(): HtmlElement = {
    div(
      fetchRiaEntityUserRoleEventBus.events
        .flatMapSwitch(fetchRiaEntityUserRole) --> Observer.empty,
      fetchRiaFundGroupAdvisorRoleEventBus.events
        .flatMapSwitch(fetchRiaFundGroupAdvisorRole) --> Observer.empty
    )
  }

  private def fetchRiaEntityUserRole(riaEntityId: RiaEntityId): EventStream[Unit] = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.succeed(isFetchingEntityMapVar.update(_.updated(riaEntityId, true)))
        _ <- RiaClient
          .getCurrentRiaEntityUserRole(
            GetCurrentRiaEntityUserRoleParams(
              riaEntityId = riaEntityId
            )
          )
          .map(
            _.fold(
              _ => Toast.error("Failed to get RIA entity user permission"),
              res => {
                riaEntityPermissionsMapVar.update(
                  _.updated(riaEntityId, res.permissions)
                )
                res.role.foreach { role =>
                  riaEntityUserRoleMapVar.update(_.updated(riaEntityId, role))
                }
              }
            )
          )
      } yield isFetchingEntityMapVar.update(_.updated(riaEntityId, false))
    )
  }

  private def fetchRiaFundGroupAdvisorRole(riaFundGroupId: RiaFundGroupId): EventStream[Unit] = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.succeed(isFetchingFundGroupMapVar.update(_.updated(riaFundGroupId, true)))
        _ <- RiaClient
          .getCurrentRiaFundGroupAdvisorRole(
            GetCurrentRiaFundGroupAdvisorRoleParams(
              riaFundGroupId = riaFundGroupId
            )
          )
          .map(
            _.fold(
              _ => Toast.error("Failed to get RIA entity fund group advisor permission"),
              res => {
                riaEntityPermissionsMapVar.update(
                  _.updated(riaFundGroupId.parent, res.permissions)
                )
                res.role.foreach { role =>
                  riaFundGroupAdvisorRoleMapVar.update(_.updated(riaFundGroupId, role))
                }
              }
            )
          )
      } yield isFetchingFundGroupMapVar.update(_.updated(riaFundGroupId, false))
    )
  }

}

object CurrentUserRiaPermissionProvider {

  private val riaEntityUserRoleMapVar: Var[Map[RiaEntityId, RiaEntityUserRole]] = Var(Map.empty)
  private val riaEntityPermissionsMapVar: Var[Map[RiaEntityId, RiaEntityPermissions]] = Var(Map.empty)
  private val isFetchingEntityMapVar = Var[Map[RiaEntityId, Boolean]](Map.empty)
  private val riaFundGroupAdvisorRoleMapVar: Var[Map[RiaFundGroupId, RiaEntityFundAdvisorRole]] = Var(Map.empty)
  private val isFetchingFundGroupMapVar: Var[Map[RiaFundGroupId, Boolean]] = Var(Map.empty)

  private val fetchRiaEntityUserRoleEventBus = new EventBus[RiaEntityId]
  private val fetchRiaFundGroupAdvisorRoleEventBus = new EventBus[RiaFundGroupId]

  def riaEntityUserRoleOptSignal(riaEntityId: RiaEntityId): Signal[Option[RiaEntityUserRole]] =
    riaEntityUserRoleMapVar.signal.map(_.get(riaEntityId))

  def riaFundGroupAdvisorRoleOptSignal(riaFundGroupId: RiaFundGroupId): Signal[Option[RiaEntityFundAdvisorRole]] =
    riaFundGroupAdvisorRoleMapVar.signal.map(_.get(riaFundGroupId))

  def riaEntityPermissionsSignal(riaEntityId: RiaEntityId): Signal[RiaEntityPermissions] =
    riaEntityPermissionsMapVar.signal.map(_.getOrElse(riaEntityId, RiaEntityPermissions()))

  def riaFundGroupPermissionsSignal(riaFundGroupId: RiaFundGroupId): Signal[RiaFundGroupPermissions] =
    riaEntityPermissionsSignal(riaFundGroupId.parent).map(_.riaFundGroupPermissions(riaFundGroupId))

  def isFetchingSignal(riaEntityId: RiaEntityId): Signal[Boolean] =
    isFetchingEntityMapVar.signal.map(_.getOrElse(riaEntityId, false))

  def isFetchingSignal(riaFundGroupId: RiaFundGroupId): Signal[Boolean] =
    isFetchingFundGroupMapVar.signal.map(_.getOrElse(riaFundGroupId, false))

  def refetch(riaEntityId: RiaEntityId): Observer[Unit] =
    fetchRiaEntityUserRoleEventBus.toObserver.contramap(_ => riaEntityId)

  def refetch(riaFundGroupId: RiaFundGroupId): Observer[Unit] = {
    fetchRiaFundGroupAdvisorRoleEventBus.toObserver.contramap(_ => riaFundGroupId)
  }

}
