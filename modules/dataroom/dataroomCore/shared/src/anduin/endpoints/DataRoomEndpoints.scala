// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.endpoints

import anduin.link.{CheckAccessGrantedParams, CheckAccessGrantedResponse, CheckValidityException}
import anduin.service.{CommonResponse, GeneralServiceException}
import anduin.tapir.AuthenticatedEndpoints
import anduin.tapir.AuthenticatedEndpoints.BaseAuthenticatedEndpoint
import sttp.tapir.*

import anduin.dataroom.exception.graphql.DataRoomGraphqlForwardException
import anduin.stargazer.service.dataroom.{GetToaCustomizationParams, GetToaCustomizationResponse, *}
import anduin.dataroom.group.*
import anduin.file.GetDownloadUrlResponse
import anduin.tapir.endpoint.CommonParams

object DataRoomEndpoints extends AuthenticatedEndpoints {

  protected lazy val dataRoomPath = "dataRoom"

  lazy val createDataRoom
    : BaseAuthenticatedEndpoint[CreateDataRoomParams, GeneralServiceException, CreateDataRoomResponse] = {
    authEndpoint[CreateDataRoomParams, GeneralServiceException, CreateDataRoomResponse](dataRoomPath / "create")
  }

  lazy val duplicateDataRoom: BaseAuthenticatedEndpoint[
    DuplicateDataRoomParams,
    GeneralServiceException,
    CreateDataRoomResponse
  ] = {
    authEndpoint[
      DuplicateDataRoomParams,
      GeneralServiceException,
      CreateDataRoomResponse
    ](dataRoomPath / "duplicate")
  }

  lazy val setIsArchivedDataRoom: BaseAuthenticatedEndpoint[
    SetIsArchivedDataRoomParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      SetIsArchivedDataRoomParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "setIsArchived"
    )
  }

  lazy val archiveDataRoomAndRemoveUsers: BaseAuthenticatedEndpoint[
    ArchiveDataRoomAndRemoveUsersParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      ArchiveDataRoomAndRemoveUsersParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "archiveAndRemoveUsers"
    )
  }

  lazy val renameDataRoom
    : BaseAuthenticatedEndpoint[RenameDataRoomParams, GeneralServiceException, DataRoomEmptyResponse] = {
    authEndpoint[RenameDataRoomParams, GeneralServiceException, DataRoomEmptyResponse](dataRoomPath / "rename")
  }

  lazy val inviteUsers: BaseAuthenticatedEndpoint[
    InviteUsersToDataRoomParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      InviteUsersToDataRoomParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "inviteUsers"
    )
  }

  lazy val acceptInvitation: BaseAuthenticatedEndpoint[
    AcceptInvitationToDataRoomParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      AcceptInvitationToDataRoomParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "acceptInvitation"
    )
  }

  lazy val declineInvitation: BaseAuthenticatedEndpoint[
    DeclineInvitationToDataRoomParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      DeclineInvitationToDataRoomParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "declineInvitation"
    )
  }

  lazy val modifyDataRoomAssetPermissions: BaseAuthenticatedEndpoint[
    ModifyDataRoomAssetPermissionParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      ModifyDataRoomAssetPermissionParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "modifyAssetPermissions"
    )
  }

  lazy val modifyUserPermissions: BaseAuthenticatedEndpoint[
    ModifyDataRoomPermissionsParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      ModifyDataRoomPermissionsParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "modifyUserPermissions"
    )
  }

  lazy val remindInvitation: BaseAuthenticatedEndpoint[
    RemindInvitationToDataRoomParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      RemindInvitationToDataRoomParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "remindInvitation"
    )
  }

  lazy val removeUsers: BaseAuthenticatedEndpoint[
    RemoveUsersFromDataRoomParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      RemoveUsersFromDataRoomParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "removeUsers"
    )
  }

  lazy val trackUserVisitDataRoom: BaseAuthenticatedEndpoint[
    TrackUserVisitDataRoomParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      TrackUserVisitDataRoomParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "trackUserVisitDataRoom"
    )
  }

  lazy val acceptTermsOfAccess: BaseAuthenticatedEndpoint[
    AcceptTermsOfAccessToDataRoomParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      AcceptTermsOfAccessToDataRoomParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "acceptTermsOfAccess"
    )
  }

  lazy val modifyGeneralSettings: BaseAuthenticatedEndpoint[
    ModifyDataRoomGeneralSettingsParams,
    GeneralServiceException,
    ModifyDataRoomGeneralSettingsResponse
  ] = {
    authEndpoint[
      ModifyDataRoomGeneralSettingsParams,
      GeneralServiceException,
      ModifyDataRoomGeneralSettingsResponse
    ](
      dataRoomPath / "modifyGeneralSettings"
    )
  }

  lazy val addFolder: BaseAuthenticatedEndpoint[AddFolderParams, GeneralServiceException, AddFolderResponse] = {
    authEndpoint[AddFolderParams, GeneralServiceException, AddFolderResponse](dataRoomPath / "addFolder")
  }

  lazy val renameFolder: BaseAuthenticatedEndpoint[RenameFolderParams, GeneralServiceException, DataRoomEmptyResponse] = {
    authEndpoint[RenameFolderParams, GeneralServiceException, DataRoomEmptyResponse](dataRoomPath / "renameFolder")
  }

  lazy val renameFile: BaseAuthenticatedEndpoint[RenameFileParams, GeneralServiceException, CommonResponse] = {
    authEndpoint[RenameFileParams, GeneralServiceException, CommonResponse](dataRoomPath / "renameFile")
  }

  lazy val getAllFileVersions: BaseAuthenticatedEndpoint[
    GetAllFileVersionsParams,
    GeneralServiceException,
    GetAllFileVersionsResponse
  ] = {
    authEndpoint[
      GetAllFileVersionsParams,
      GeneralServiceException,
      GetAllFileVersionsResponse
    ](dataRoomPath / "getAllFileVersions")
  }

  lazy val deleteFilesAndFolders: BaseAuthenticatedEndpoint[
    DeleteFilesAndFoldersParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      DeleteFilesAndFoldersParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "deleteFilesAndFolders"
    )
  }

  lazy val restoreFiles: BaseAuthenticatedEndpoint[
    RestoreDataRoomFilesParams,
    GeneralServiceException,
    RestoreDataRoomFilesResponse
  ] =
    authEndpoint[
      RestoreDataRoomFilesParams,
      GeneralServiceException,
      RestoreDataRoomFilesResponse
    ](
      dataRoomPath / "restoreFiles"
    )

  lazy val getDownloadUrl: BaseAuthenticatedEndpoint[
    GetDownloadUrlParams,
    GetViewUrlException,
    GetDownloadUrlResponse
  ] = {
    authEndpoint[
      GetDownloadUrlParams,
      GetViewUrlException,
      GetDownloadUrlResponse
    ](dataRoomPath / "getDownloadUrl")
  }

  lazy val getDownloadFileVersionUrl: BaseAuthenticatedEndpoint[
    DataRoomSingleFileVersionDownloadRequest,
    GeneralServiceException,
    GetUrlResponse
  ] = {
    authEndpoint[
      DataRoomSingleFileVersionDownloadRequest,
      GeneralServiceException,
      GetUrlResponse
    ](dataRoomPath / "getDownloadFileVersionUrl")
  }

  lazy val getViewUrl: BaseAuthenticatedEndpoint[GetViewUrlParams, GetViewUrlException, GetUrlResponse] = {
    authEndpoint[GetViewUrlParams, GetViewUrlException, GetUrlResponse](dataRoomPath / "getViewUrl")
  }

  lazy val getTermsOfAccessUrl
    : BaseAuthenticatedEndpoint[GetTermsOfAccessUrl, GeneralServiceException, GetUrlResponse] = {
    authEndpoint[GetTermsOfAccessUrl, GeneralServiceException, GetUrlResponse](
      dataRoomPath / "getTermsOfAccessViewUrl"
    )
  }

  lazy val manualNotification: BaseAuthenticatedEndpoint[
    DataRoomManualNotificationParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      DataRoomManualNotificationParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "manualNotification"
    )
  }

  lazy val createLinkInvitation: BaseAuthenticatedEndpoint[
    CreateDataRoomLinkInvitationParams,
    GeneralServiceException,
    CreateDataRoomLinkInvitationResponse
  ] = {
    authEndpoint[
      CreateDataRoomLinkInvitationParams,
      GeneralServiceException,
      CreateDataRoomLinkInvitationResponse
    ](
      dataRoomPath / "createLinkInvitation"
    )
  }

  lazy val getSharableLinkConfig: BaseAuthenticatedEndpoint[
    GetSharableLinkConfigParams,
    GeneralServiceException,
    GetSharableLinkConfigResponse
  ] = {
    authEndpoint[
      GetSharableLinkConfigParams,
      GeneralServiceException,
      GetSharableLinkConfigResponse
    ](
      dataRoomPath / "getSharableLinkConfig"
    )
  }

  lazy val modifyLinkInvitation: BaseAuthenticatedEndpoint[
    ModifyDataRoomLinkInvitationParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      ModifyDataRoomLinkInvitationParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "modifyLinkInvitation"
    )
  }

  lazy val joinViaLinkInvitation: BaseAuthenticatedEndpoint[
    JoinDataRoomViaLinkInvitationParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      JoinDataRoomViaLinkInvitationParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "joinViaLinkInvitation"
    )
  }

  lazy val getLinkInvitationInfo: BaseAuthenticatedEndpoint[
    GetDataRoomLinkInvitationInfoParams,
    GeneralServiceException,
    GetDataRoomLinkInvitationInfoResponse
  ] = {
    authEndpoint[
      GetDataRoomLinkInvitationInfoParams,
      GeneralServiceException,
      GetDataRoomLinkInvitationInfoResponse
    ](
      dataRoomPath / "getLinkInvitationInfo"
    )
  }

  lazy val deleteLinkInvitation: BaseAuthenticatedEndpoint[
    DeleteDataRoomLinkInvitationParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      DeleteDataRoomLinkInvitationParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "deleteLinkInvitation"
    )
  }

  lazy val changeFolderOrder: BaseAuthenticatedEndpoint[
    ChangeFolderOrderParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      ChangeFolderOrderParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "changeFolderOrder"
    )
  }

  lazy val copyPreCheck
    : BaseAuthenticatedEndpoint[CopyPreCheckParams, GeneralServiceException, CopyPreCheckResponse] = {
    authEndpoint[CopyPreCheckParams, GeneralServiceException, CopyPreCheckResponse](
      dataRoomPath / "copyPreCheck"
    )
  }

  lazy val movePreCheck
    : BaseAuthenticatedEndpoint[MovePreCheckParams, GeneralServiceException, MovePreCheckResponse] = {
    authEndpoint[MovePreCheckParams, GeneralServiceException, MovePreCheckResponse](
      dataRoomPath / "movePreCheck"
    )
  }

  lazy val createShortcut
    : BaseAuthenticatedEndpoint[CreateShortcutParams, GeneralServiceException, DataRoomEmptyResponse] = {
    authEndpoint[CreateShortcutParams, GeneralServiceException, DataRoomEmptyResponse](
      dataRoomPath / "createShortcut"
    )
  }

  lazy val extractShortcut: BaseAuthenticatedEndpoint[
    ExtractShortcutParams,
    GeneralServiceException,
    ExtractShortcutResponse
  ] = {
    authEndpoint[
      ExtractShortcutParams,
      GeneralServiceException,
      ExtractShortcutResponse
    ](
      dataRoomPath / "extractShortcut"
    )
  }

  lazy val recordPageView
    : BaseAuthenticatedEndpoint[RecordPageViewParams, GeneralServiceException, DataRoomEmptyResponse] = {
    authEndpoint[RecordPageViewParams, GeneralServiceException, DataRoomEmptyResponse](
      dataRoomPath / "recordPageView"
    )
  }

  lazy val setNotificationSettings: BaseAuthenticatedEndpoint[
    SetDataRoomNotificationSettingsParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      SetDataRoomNotificationSettingsParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "setNotificationSettings"
    )
  }

  lazy val markDataRoomNotificationOnboardAsSeen
    : BaseAuthenticatedEndpoint[DataRoomEmptyParams, GeneralServiceException, DataRoomEmptyResponse] = {
    authEndpoint[DataRoomEmptyParams, GeneralServiceException, DataRoomEmptyResponse](
      dataRoomPath / "markDataRoomNotificationOnboardAsSeen"
    )
  }

  lazy val exportParticipants: BaseAuthenticatedEndpoint[
    DataRoomExportParticipantsParams,
    GeneralServiceException,
    DataRoomExportParticipantsResponse
  ] = {
    authEndpoint[
      DataRoomExportParticipantsParams,
      GeneralServiceException,
      DataRoomExportParticipantsResponse
    ](
      dataRoomPath / "exportParticipants"
    )
  }

  lazy val exportPermissions: BaseAuthenticatedEndpoint[
    DataRoomExportPermissionsParams,
    GeneralServiceException,
    DataRoomExportPermissionsResponse
  ] = {
    authEndpoint[
      DataRoomExportPermissionsParams,
      GeneralServiceException,
      DataRoomExportPermissionsResponse
    ](
      dataRoomPath / "exportPermissions"
    )
  }

  lazy val exportFileActivities: BaseAuthenticatedEndpoint[
    ExportFileActivitiesParams,
    GeneralServiceException,
    ExportFileActivitiesResponse
  ] = {
    authEndpoint[
      ExportFileActivitiesParams,
      GeneralServiceException,
      ExportFileActivitiesResponse
    ](
      dataRoomPath / "exportFileActivities"
    )
  }

  lazy val exportDataRoomActivities: BaseAuthenticatedEndpoint[
    ExportDataRoomActivitiesParams,
    GeneralServiceException,
    ExportDataRoomActivitiesResponse
  ] =
    authEndpoint[
      ExportDataRoomActivitiesParams,
      GeneralServiceException,
      ExportDataRoomActivitiesResponse
    ](
      dataRoomPath / "exportDataRoomActivities"
    )

  lazy val setDataRoomTermsOfAccessWhitelistedUsers: BaseAuthenticatedEndpoint[
    SetDataRoomTermsOfAccessWhitelistedUsersParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      SetDataRoomTermsOfAccessWhitelistedUsersParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "setDataRoomTermsOfAccessWhitelistedUsers"
    )
  }

  lazy val getDataRoomHomePage: BaseAuthenticatedEndpoint[
    GetDataRoomHomePageParams,
    GeneralServiceException,
    GetDataRoomHomePageResponse
  ] = authEndpoint[
    GetDataRoomHomePageParams,
    GeneralServiceException,
    GetDataRoomHomePageResponse
  ](dataRoomPath / "getDataRoomHomePage")

  lazy val toggleDataRoomHomePagePublishStatus: BaseAuthenticatedEndpoint[
    ToggleDataRoomHomePagePublishStatusParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] =
    authEndpoint[
      ToggleDataRoomHomePagePublishStatusParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "toggleDataRoomHomePagePublishStatus"
    )

  lazy val updateDataRoomHomePage: BaseAuthenticatedEndpoint[
    UpdateDataRoomHomePageParams,
    GeneralServiceException,
    UpdateDataRoomHomePageResponse
  ] =
    authEndpoint[
      UpdateDataRoomHomePageParams,
      GeneralServiceException,
      UpdateDataRoomHomePageResponse
    ](
      dataRoomPath / "updateDataRoomHomePage"
    )

  lazy val updateDataRoomWhiteLabel: BaseAuthenticatedEndpoint[
    UpdateDataRoomWhiteLabelParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] =
    authEndpoint[
      UpdateDataRoomWhiteLabelParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](dataRoomPath / "updateDataRoomWhiteLabel")

  lazy val sendDataRoomHomePageMessage: BaseAuthenticatedEndpoint[
    SendDataRoomHomePageMessageParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] =
    authEndpoint[
      SendDataRoomHomePageMessageParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "sendDataRoomHomePageMessage"
    )

  lazy val checkAccessGranted: BaseAuthenticatedEndpoint[
    CheckAccessGrantedParams,
    CheckValidityException,
    CheckAccessGrantedResponse
  ] =
    authEndpoint[
      CheckAccessGrantedParams,
      CheckValidityException,
      CheckAccessGrantedResponse
    ](
      dataRoomPath / "checkAccessGranted"
    )

  lazy val approveAccessRequests: BaseAuthenticatedEndpoint[
    ApproveAccessRequestsParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] =
    authEndpoint[
      ApproveAccessRequestsParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "approveAccessRequests"
    )

  lazy val declineAccessRequests: BaseAuthenticatedEndpoint[
    DeclineAccessRequestsParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] =
    authEndpoint[
      DeclineAccessRequestsParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "declineAccessRequests"
    )

  lazy val getDataRoomActivityLog: BaseAuthenticatedEndpoint[
    GetDataRoomActivityLogParams,
    GeneralServiceException,
    GetDataRoomActivityLogResponse
  ] =
    authEndpoint[
      GetDataRoomActivityLogParams,
      GeneralServiceException,
      GetDataRoomActivityLogResponse
    ](
      dataRoomPath / "getDataRoomActivityLog"
    )

  lazy val getFileActivityLog: BaseAuthenticatedEndpoint[
    GetFileActivityLogParams,
    GeneralServiceException,
    GetFileActivityLogResponse
  ] =
    authEndpoint[
      GetFileActivityLogParams,
      GeneralServiceException,
      GetFileActivityLogResponse
    ](
      dataRoomPath / "getFileActivityLog"
    )

  lazy val getThumbnailUrl: BaseAuthenticatedEndpoint[
    GetThumbnailUrlParams,
    GeneralServiceException,
    GetThumbnailUrlResponse
  ] =
    authEndpoint[
      GetThumbnailUrlParams,
      GeneralServiceException,
      GetThumbnailUrlResponse
    ](
      dataRoomPath / "getThumbnailUrl"
    )

  lazy val markDataRoomRecentFilesAsSeen: BaseAuthenticatedEndpoint[
    MarkDataRoomRecentFilesAsSeenParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      MarkDataRoomRecentFilesAsSeenParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "markDataRoomRecentFilesAsSeen"
    )
  }

  lazy val getEmailTemplate: BaseAuthenticatedEndpoint[
    GetEmailTemplateParams,
    GeneralServiceException,
    GetEmailTemplateResponse
  ] = {
    authEndpoint[
      GetEmailTemplateParams,
      GeneralServiceException,
      GetEmailTemplateResponse
    ](
      dataRoomPath / "getEmailTemplate"
    )
  }

  lazy val updateEmailTemplate: BaseAuthenticatedEndpoint[
    UpdateEmailTemplateParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      UpdateEmailTemplateParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "updateEmailTemplate"
    )
  }

  lazy val getDataRoomEmailSenderDetails: BaseAuthenticatedEndpoint[
    GetDataRoomEmailSenderDetailsParams,
    GeneralServiceException,
    GetDataRoomEmailSenderDetailsResponse
  ] = {
    authEndpoint[
      GetDataRoomEmailSenderDetailsParams,
      GeneralServiceException,
      GetDataRoomEmailSenderDetailsResponse
    ](
      dataRoomPath / "getDataRoomEmailSenderDetails"
    )
  }

  lazy val updateDataRoomEmailConfigs: BaseAuthenticatedEndpoint[
    UpdateDataRoomEmailConfigsParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      UpdateDataRoomEmailConfigsParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "updateDataRoomEmailConfigs"
    )
  }

  lazy val updateDataRoomWatermarkExceptionFiles: BaseAuthenticatedEndpoint[
    UpdateDataRoomWatermarkExceptionFilesParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      UpdateDataRoomWatermarkExceptionFilesParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "updateDataRoomWatermarkExceptionFiles"
    )
  }

  lazy val setDataRoomPointsOfContact: BaseAuthenticatedEndpoint[
    SetDataRoomPointsOfContactParams,
    GeneralServiceException,
    DataRoomEmptyResponse
  ] = {
    authEndpoint[
      SetDataRoomPointsOfContactParams,
      GeneralServiceException,
      DataRoomEmptyResponse
    ](
      dataRoomPath / "setDataRoomPointsOfContact"
    )
  }

  lazy val setViewedSearchOnboarding: BaseAuthenticatedEndpoint[
    DataRoomSetViewedSearchOnboardingParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      DataRoomSetViewedSearchOnboardingParams,
      GeneralServiceException,
      Unit
    ](
      dataRoomPath / "setViewedSearchOnboarding"
    )
  }

  lazy val setViewedGroupOnboarding: BaseAuthenticatedEndpoint[
    DataRoomSetViewedGroupOnboardingParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[DataRoomSetViewedGroupOnboardingParams, GeneralServiceException, Unit](
      dataRoomPath / "setViewedGroupOnboarding"
    )
  }

  lazy val createGroup: BaseAuthenticatedEndpoint[
    CreateDataRoomGroupParams,
    GeneralServiceException,
    CreateDataRoomGroupResponse
  ] = {
    authEndpoint[
      CreateDataRoomGroupParams,
      GeneralServiceException,
      CreateDataRoomGroupResponse
    ](
      dataRoomPath / "createGroup"
    )
  }

  lazy val renameGroup: BaseAuthenticatedEndpoint[
    RenameDataRoomGroupParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[RenameDataRoomGroupParams, GeneralServiceException, Unit](
      dataRoomPath / "renameGroup"
    )
  }

  lazy val deleteGroups: BaseAuthenticatedEndpoint[
    DeleteDataRoomGroupsParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[DeleteDataRoomGroupsParams, GeneralServiceException, Unit](
      dataRoomPath / "deleteGroups"
    )
  }

  lazy val addUsersToGroups: BaseAuthenticatedEndpoint[
    AddUsersToMultipleGroupsParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[AddUsersToMultipleGroupsParams, GeneralServiceException, Unit](
      dataRoomPath / "addUsersToGroups"
    )
  }

  lazy val removeUsersFromGroup: BaseAuthenticatedEndpoint[
    RemoveUsersFromDataRoomGroupParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[RemoveUsersFromDataRoomGroupParams, GeneralServiceException, Unit](
      dataRoomPath / "removeUserFromGroups"
    )
  }

  lazy val updateGroupPermission: BaseAuthenticatedEndpoint[
    UpdateDataRoomGroupPermissionsParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[UpdateDataRoomGroupPermissionsParams, GeneralServiceException, Unit](
      dataRoomPath / "updateGroupPermission"
    )
  }

  lazy val fullSearch: BaseAuthenticatedEndpoint[
    FullSearchDataRoomParams,
    GeneralServiceException,
    FullSearchDataRoomResponse
  ] = {
    authEndpoint[
      FullSearchDataRoomParams,
      GeneralServiceException,
      FullSearchDataRoomResponse
    ](dataRoomPath / "fullSearch")
  }

  lazy val quickSearch: BaseAuthenticatedEndpoint[
    QuickSearchDataRoomParams,
    GeneralServiceException,
    QuickSearchDataRoomResponse
  ] = {
    authEndpoint[
      QuickSearchDataRoomParams,
      GeneralServiceException,
      QuickSearchDataRoomResponse
    ](dataRoomPath / "quickSearch")
  }

  lazy val semanticSearch: BaseAuthenticatedEndpoint[
    SemanticSearchDataRoomParams,
    GeneralServiceException,
    SemanticSearchDataRoomResponse
  ] = {
    authEndpoint[
      SemanticSearchDataRoomParams,
      GeneralServiceException,
      SemanticSearchDataRoomResponse
    ](dataRoomPath / "semanticSearch")
  }

  lazy val exportReportForFolderMappingResults: BaseAuthenticatedEndpoint[
    ExportReportForFolderMappingResultsParams,
    GeneralServiceException,
    ExportReportForFolderMappingResultsResponse
  ] = {
    authEndpoint[
      ExportReportForFolderMappingResultsParams,
      GeneralServiceException,
      ExportReportForFolderMappingResultsResponse
    ](dataRoomPath / "exportReportForFolderMappingResults")
  }

  lazy val exportReportForUploadAndAutoImportFiles: BaseAuthenticatedEndpoint[
    ExportReportForUploadAndAutoImportFilesParams,
    GeneralServiceException,
    ExportReportForUploadAndAutoImportFilesResponse
  ] = {
    authEndpoint[
      ExportReportForUploadAndAutoImportFilesParams,
      GeneralServiceException,
      ExportReportForUploadAndAutoImportFilesResponse
    ](dataRoomPath / "exportReportForUploadAndAutoImportFileResponse")
  }

  lazy val getFileWithUsersInsights: BaseAuthenticatedEndpoint[
    DataRoomGetFileWithUsersInsightsParams,
    GeneralServiceException,
    DataRoomGetFileWithUsersInsightsResponse
  ] = {
    authEndpoint[
      DataRoomGetFileWithUsersInsightsParams,
      GeneralServiceException,
      DataRoomGetFileWithUsersInsightsResponse
    ](dataRoomPath / "getFileWithUsersInsights")
  }

  lazy val getUserWithFilesInsights: BaseAuthenticatedEndpoint[
    DataRoomGetUserWithFilesInsightsParams,
    GeneralServiceException,
    DataRoomGetParticipantsWithFilesInsightsResponse
  ] = {
    authEndpoint[
      DataRoomGetUserWithFilesInsightsParams,
      GeneralServiceException,
      DataRoomGetParticipantsWithFilesInsightsResponse
    ](dataRoomPath / "getUserWithFilesInsights")
  }

  lazy val getGroupWithFilesInsights: BaseAuthenticatedEndpoint[
    DataRoomGetGroupWithFilesInsightsParams,
    GeneralServiceException,
    DataRoomGetParticipantsWithFilesInsightsResponse
  ] = {
    authEndpoint[
      DataRoomGetGroupWithFilesInsightsParams,
      GeneralServiceException,
      DataRoomGetParticipantsWithFilesInsightsResponse
    ](dataRoomPath / "getGroupWithFilesInsights")
  }

  lazy val getInsightsFileTimeline: BaseAuthenticatedEndpoint[
    DataRoomGetInsightsFileTimelineParams,
    GeneralServiceException,
    DataRoomGetInsightsFileTimelineResponse
  ] = {
    authEndpoint[
      DataRoomGetInsightsFileTimelineParams,
      GeneralServiceException,
      DataRoomGetInsightsFileTimelineResponse
    ](dataRoomPath / "getInsightsFileTimeline")
  }

  lazy val getInsightsUserTimeline: BaseAuthenticatedEndpoint[
    DataRoomGetInsightsUserTimelineParams,
    GeneralServiceException,
    DataRoomGetInsightsParticipantsTimelineResponse
  ] = {
    authEndpoint[
      DataRoomGetInsightsUserTimelineParams,
      GeneralServiceException,
      DataRoomGetInsightsParticipantsTimelineResponse
    ](dataRoomPath / "getInsightsUserTimeline")
  }

  lazy val getInsightsGroupTimeline: BaseAuthenticatedEndpoint[
    DataRoomGetInsightsGroupTimelineParams,
    GeneralServiceException,
    DataRoomGetInsightsParticipantsTimelineResponse
  ] = {
    authEndpoint[
      DataRoomGetInsightsGroupTimelineParams,
      GeneralServiceException,
      DataRoomGetInsightsParticipantsTimelineResponse
    ](dataRoomPath / "getInsightsGroupTimeline")
  }

  lazy val getTopActiveUsers: BaseAuthenticatedEndpoint[
    DataRoomGetTopActiveParticipantsParams,
    GeneralServiceException,
    DataRoomGetTopActiveUsersResponse
  ] = {
    authEndpoint[
      DataRoomGetTopActiveParticipantsParams,
      GeneralServiceException,
      DataRoomGetTopActiveUsersResponse
    ](dataRoomPath / "getTopActiveUsers")
  }

  lazy val getTopActiveGroups: BaseAuthenticatedEndpoint[
    DataRoomGetTopActiveParticipantsParams,
    GeneralServiceException,
    DataRoomGetTopActiveGroupsResponse
  ] = {
    authEndpoint[
      DataRoomGetTopActiveParticipantsParams,
      GeneralServiceException,
      DataRoomGetTopActiveGroupsResponse
    ](dataRoomPath / "getTopActiveGroups")
  }

  lazy val getTopActiveFiles: BaseAuthenticatedEndpoint[
    DataRoomGetTopActiveFilesParams,
    GeneralServiceException,
    DataRoomGetTopActiveFilesResponse
  ] = {
    authEndpoint[
      DataRoomGetTopActiveFilesParams,
      GeneralServiceException,
      DataRoomGetTopActiveFilesResponse
    ](dataRoomPath / "getTopActiveFiles")
  }

  lazy val getInsightsNewestParticipants: BaseAuthenticatedEndpoint[
    DataRoomGetInsightsNewestParticipantsParams,
    GeneralServiceException,
    DataRoomGetInsightsNewestParticipantsResponse
  ] = {
    authEndpoint[
      DataRoomGetInsightsNewestParticipantsParams,
      GeneralServiceException,
      DataRoomGetInsightsNewestParticipantsResponse
    ](dataRoomPath / "getInsightsNewestParticipants")
  }

  lazy val getUserSummaryInsights: BaseAuthenticatedEndpoint[
    DataRoomGetUserSummaryInsightsParams,
    GeneralServiceException,
    DataRoomGetParticipantsSummaryInsightsResponse
  ] = {
    authEndpoint[
      DataRoomGetUserSummaryInsightsParams,
      GeneralServiceException,
      DataRoomGetParticipantsSummaryInsightsResponse
    ](dataRoomPath / "getUserSummaryInsights")
  }

  lazy val getGroupSummaryInsights: BaseAuthenticatedEndpoint[
    DataRoomGetGroupSummaryInsightsParams,
    GeneralServiceException,
    DataRoomGetParticipantsSummaryInsightsResponse
  ] = {
    authEndpoint[
      DataRoomGetGroupSummaryInsightsParams,
      GeneralServiceException,
      DataRoomGetParticipantsSummaryInsightsResponse
    ](dataRoomPath / "getGroupSummaryInsights")
  }

  lazy val getFileSummaryInsights: BaseAuthenticatedEndpoint[
    DataRoomGetFileSummaryInsightsParams,
    GeneralServiceException,
    DataRoomGetFileSummaryInsightsResponse
  ] = {
    authEndpoint[
      DataRoomGetFileSummaryInsightsParams,
      GeneralServiceException,
      DataRoomGetFileSummaryInsightsResponse
    ](dataRoomPath / "getFileSummaryInsights")
  }

  lazy val getFilePageTotalViewTime: BaseAuthenticatedEndpoint[
    DataRoomGetFilePageTotalViewTimeParams,
    GeneralServiceException,
    DataRoomGetFilePageTotalViewTimeResponse
  ] = {
    authEndpoint[
      DataRoomGetFilePageTotalViewTimeParams,
      GeneralServiceException,
      DataRoomGetFilePageTotalViewTimeResponse
    ](dataRoomPath / "getFilePageTotalViewTime")
  }

  lazy val getFilePageUserViewPercentage: BaseAuthenticatedEndpoint[
    DataRoomGetFilePageUserViewPercentageParams,
    GeneralServiceException,
    DataRoomGetFilePageUserViewPercentageResponse
  ] = {
    authEndpoint[
      DataRoomGetFilePageUserViewPercentageParams,
      GeneralServiceException,
      DataRoomGetFilePageUserViewPercentageResponse
    ](dataRoomPath / "getFilePageUserViewPercentage")
  }

  lazy val getDataRoomUsersInsights: BaseAuthenticatedEndpoint[
    DataRoomGetParticipantsInsightsParams,
    GeneralServiceException,
    DataRoomGetUsersInsightsResponse
  ] = {
    authEndpoint[
      DataRoomGetParticipantsInsightsParams,
      GeneralServiceException,
      DataRoomGetUsersInsightsResponse
    ](dataRoomPath / "getDataRoomUsersInsights")
  }

  lazy val getDataRoomGroupsInsights: BaseAuthenticatedEndpoint[
    DataRoomGetParticipantsInsightsParams,
    GeneralServiceException,
    DataRoomGetGroupsInsightsResponse
  ] = {
    authEndpoint[
      DataRoomGetParticipantsInsightsParams,
      GeneralServiceException,
      DataRoomGetGroupsInsightsResponse
    ](dataRoomPath / "getDataRoomGroupsInsights")
  }

  lazy val getDataRoomFilesInsights: BaseAuthenticatedEndpoint[
    DataRoomGetFilesInsightsParams,
    GeneralServiceException,
    DataRoomGetFilesInsightsResponse
  ] = {
    authEndpoint[
      DataRoomGetFilesInsightsParams,
      GeneralServiceException,
      DataRoomGetFilesInsightsResponse
    ](dataRoomPath / "getDataRoomFilesInsights")
  }

  lazy val getToaCustomization: BaseAuthenticatedEndpoint[
    GetToaCustomizationParams,
    GeneralServiceException,
    GetToaCustomizationResponse
  ] = {
    authEndpoint[
      GetToaCustomizationParams,
      GeneralServiceException,
      GetToaCustomizationResponse
    ](dataRoomPath / "getToaCustomization")
  }

  lazy val getDataRoomFileFolderPermission: BaseAuthenticatedEndpoint[
    GetDataRoomFileFolderPermissionParams,
    GeneralServiceException,
    GetDataRoomFileFolderPermissionResponse
  ] = {
    authEndpoint[
      GetDataRoomFileFolderPermissionParams,
      GeneralServiceException,
      GetDataRoomFileFolderPermissionResponse
    ](dataRoomPath / "getDataRoomFileFolderPermission")
  }

  lazy val getDataRoomDashboardWhitelabel: BaseAuthenticatedEndpoint[
    CommonParams.Empty,
    GeneralServiceException,
    DataRoomDashboardWhitelabel
  ] = authEndpoint[
    CommonParams.Empty,
    GeneralServiceException,
    DataRoomDashboardWhitelabel
  ](dataRoomPath / "getDataRoomDashboardWhitelabel")

  lazy val getDataRoomDashboard: BaseAuthenticatedEndpoint[
    GetDataRoomDashboardParams,
    GeneralServiceException,
    GetDataRoomDashboardResponse
  ] = authEndpoint[
    GetDataRoomDashboardParams,
    GeneralServiceException,
    GetDataRoomDashboardResponse
  ](dataRoomPath / "getDataRoomDashboard")

  lazy val getDataRoomActiveInOrg: BaseAuthenticatedEndpoint[
    GetDataRoomActiveInOrgParams,
    GeneralServiceException,
    GetDataRoomActiveInOrgResponse
  ] = authEndpoint[
    GetDataRoomActiveInOrgParams,
    GeneralServiceException,
    GetDataRoomActiveInOrgResponse
  ](dataRoomPath / "getDataRoomActiveInOrg")

  lazy val getDataRoomDetail: BaseAuthenticatedEndpoint[
    GetDataRoomDetailParams,
    DataRoomGraphqlForwardException,
    GetDataRoomDetailResponse
  ] = authEndpoint[
    GetDataRoomDetailParams,
    DataRoomGraphqlForwardException,
    GetDataRoomDetailResponse
  ](dataRoomPath / "getDataRoomDetail")

  val getDataRoomCountForOtherRegions: BaseAuthenticatedEndpoint[
    CommonParams.Empty,
    GeneralServiceException,
    GetDataRoomCountForOtherRegionsResponse
  ] = authEndpoint[
    CommonParams.Empty,
    GeneralServiceException,
    GetDataRoomCountForOtherRegionsResponse
  ](dataRoomPath / "getDataRoomCountForOtherRegions")

}
