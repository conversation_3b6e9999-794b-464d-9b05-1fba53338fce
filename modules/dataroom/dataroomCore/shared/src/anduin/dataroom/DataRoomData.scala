// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom

import java.time.Instant
import io.circe.Codec
import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.dataroom.DataRoomData.UserChanges
import anduin.dataroom.DataRoomUserData.JoinedUser
import anduin.dataroom.group.DataRoomGroupData
import anduin.dataroom.notification.{InvitationAcceptedEmailNotification, NewFileNotification, NotificationFrequency}
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.id.dataroom.DataRoomGroupId
import anduin.id.link.ProtectedLinkId
import anduin.link.ProtectedLinkStateWithEnterpriseLogin
import anduin.model.circe.MapEntry.given
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{FileId, TeamId}
import anduin.orgbilling.model.plan.{DataRoomPlan, DataRoomPremiumFeature}
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.stargazer.service.dataroom.{AuthenticationWhiteLabelData, DataRoomWhiteLabelData}

final case class DataRoomData(
  workflowId: DataRoomWorkflowId,
  latestState: DataRoomData.LatestState,
  dataRoomPlan: DataRoomPlan,
  dataRoomWhiteLabelData: DataRoomWhiteLabelData,
  authenticationWhiteLabelData: AuthenticationWhiteLabelData,
  rootFolder: DataRoomData.RootFolderPermission,
  groupMap: Map[DataRoomGroupId, DataRoomGroupData],
  deletedGroups: Map[DataRoomGroupId, DataRoomGroupData]
) {

  lazy val groupMapByTeam: Map[TeamId, DataRoomGroupData] = groupMap.map { case (_, groupData) =>
    groupData.teamId -> groupData
  }

  lazy val groupParticipants: Set[UserId] = {
    groupMap.values.flatMap(_.participants).toSet
  }

  lazy val unassignedParticipants: Set[UserId] = {
    latestState.participatingUsers.keySet -- groupParticipants
  }

  lazy val hasViewOnlyPlan: Boolean = dataRoomPlan.features.contains(DataRoomPremiumFeature.ViewOnly)

  def getGroupsOfUser(userId: UserId): Seq[DataRoomGroupData] = {
    if (unassignedParticipants.contains(userId)) {
      Seq()
    } else {
      groupMap.values.filter(_.participants.contains(userId)).toSeq
    }
  }

  def getGroupData(groupId: DataRoomGroupId, includeDeleted: Boolean = false): Option[DataRoomGroupData] = {
    groupMap
      .get(groupId)
      .orElse {
        if (includeDeleted) deletedGroups.get(groupId) else None
      }
  }

  def getParticipantRoleMap(
    userChanges: Map[UserId, UserChanges] = Map.empty,
    groupChanges: Map[DataRoomGroupId, DataRoomRole] = Map.empty
  ): Map[UserId, DataRoomRole] = {
    latestState.participatingUsers.map { case (userId, userData) =>
      val userChange = userChanges.get(userId)
      val groupIds =
        userData.userState.groupIds.toSet ++ userChange.map(_.addedGroups).getOrElse(Set.empty) -- userChange
          .map(_.removedGroups)
          .getOrElse(Set.empty)
      val groupRoles = groupIds.flatMap { groupId =>
        groupChanges.get(groupId).orElse(groupMap.get(groupId).map(_.role))
      }
      val userRole = userChange
        .flatMap(_.newRoleOpt)
        .getOrElse(
          if (groupIds.isEmpty) {
            latestState.dataRoomCreatedState.individualRoles.getOrElse(userId, DataRoomRole.Empty)
          } else {
            DataRoomRole.Empty
          }
        )
      userId -> DataRoomRoleUtils.getMaxRole(groupRoles + userRole)
    }
  }

  def isJoinedAdmin(userId: UserId, role: DataRoomRole) = {
    val isJoined = latestState.participatingUsers.get(userId).fold(false) { userData =>
      userData.teamState match {
        case _: JoinedUser => true
        case _             => false
      }
    }
    isJoined && DataRoomRoleUtils.isAdmin(role)
  }

  val joinedAdmins = {
    latestState.dataRoomCreatedState.individualRoles.filter { case (userId, role) =>
      isJoinedAdmin(userId, role)
    }.keySet
  }

  def getAdminCount(
    userChanges: Map[UserId, UserChanges] = Map.empty,
    groupChanges: Map[DataRoomGroupId, DataRoomRole] = Map.empty
  ): Int = {
    getParticipantRoleMap(userChanges, groupChanges).count { (userId, role) =>
      isJoinedAdmin(userId, role)
    }
  }

  def getRemainingSeatCount(
    userChanges: Map[UserId, UserChanges] = Map.empty,
    groupChanges: Map[DataRoomGroupId, DataRoomRole] = Map.empty
  ): Int = {
    val internalCount = getParticipantRoleMap(userChanges, groupChanges).count { (_, role) =>
      DataRoomRoleUtils.isInternal(role)
    }
    val seatCount = dataRoomPlan.totalSeats
    Math.max(0, seatCount - internalCount)
  }

  def getToaFileIdOpt(): Option[FileId] = {
    val toaOptions = latestState.dataRoomCreatedState.termsOfAccessOptions
    toaOptions.versions.lastOption.filter(_ => toaOptions.isEnabled)
  }

}

object DataRoomData {

  given Codec.AsObject[DataRoomData] = deriveCodecWithDefaults

  final case class InvitedUserData(inviter: UserId, invitedAt: Option[Instant], inviterInfo: Option[UserInfo] = None)

  object InvitedUserData {
    given Codec.AsObject[InvitedUserData] = deriveCodecWithDefaults
  }

  final case class NotificationSettings(
    invitationAcceptedEmail: InvitationAcceptedEmailNotification,
    newFileNotification: NewFileNotification,
    newFileNotificationFrequency: NotificationFrequency
  ) derives CanEqual

  object NotificationSettings {

    given Codec.AsObject[NotificationSettings] = deriveCodecWithDefaults

    val empty: NotificationSettings = NotificationSettings(
      invitationAcceptedEmail = InvitationAcceptedEmailNotification.None,
      newFileNotification = NewFileNotification.NoNotification,
      newFileNotificationFrequency = NotificationFrequency.Daily
    )

  }

  final case class LatestState(
    dataRoomCreatedState: DataRoomFrontEndState,
    participatingUsers: Map[UserId, DataRoomUserData],
    deletedUsers: Map[UserId, DataRoomUserData.UserInfo],
    creatorEntity: CreatorEntity,
    createdAt: Option[Instant],
    linkStateMap: Map[ProtectedLinkId, ProtectedLinkStateWithEnterpriseLogin],
    notificationSettings: NotificationSettings,
    hasViewedNotificationOnboarding: Boolean = true,
    homePageIsPublished: Boolean
  ) {

    def getUserInfo(userId: UserId): Option[DataRoomUserData.UserInfo] = {
      participatingUsers
        .get(userId)
        .map(_.userInfo)
        .orElse(deletedUsers.get(userId))
    }

    def getUserRole(userId: UserId): DataRoomRole = {
      dataRoomCreatedState.individualRoles.getOrElse(userId, DataRoomRole.Empty)
    }

    def isUserRole(userId: UserId)(check: DataRoomRoleUtils.Check): Boolean = {
      dataRoomCreatedState.individualRoles.get(userId).exists(check)
    }

    def isUserJoined(userId: UserId): Boolean = {
      participatingUsers.get(userId).exists {
        _.teamState match {
          case _: JoinedUser => true
          case _             => false
        }
      }
    }

    def getUserState(userId: UserId): Option[DataRoomUserData.UserState] = {
      participatingUsers.get(userId).map(_.userState)
    }

    def getJoinedAdmins: Set[UserId] = {
      val adminRoles = dataRoomCreatedState.individualRoles.collect {
        case (userId, role) if DataRoomRoleUtils.isAdmin(role) => userId
      }
      val joinedUsers = participatingUsers.collect {
        case (userId, DataRoomUserData(_, _: DataRoomUserData.JoinedUser, _)) => userId
      }
      adminRoles.toSet.intersect(joinedUsers.toSet)
    }

  }

  object LatestState {
    given Codec.AsObject[LatestState] = deriveCodecWithDefaults
  }

  final case class RootFolderPermission(userPermission: Option[FileFolderPermission])

  object RootFolderPermission {
    given Codec.AsObject[RootFolderPermission] = deriveCodecWithDefaults
  }

  final case class UserChanges(
    newRoleOpt: Option[DataRoomRole] = None,
    addedGroups: Set[DataRoomGroupId] = Set.empty,
    removedGroups: Set[DataRoomGroupId] = Set.empty
  )

}
