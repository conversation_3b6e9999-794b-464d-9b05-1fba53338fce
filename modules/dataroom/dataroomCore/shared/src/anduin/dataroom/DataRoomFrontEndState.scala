// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom

import java.time.Instant

import io.circe.Codec

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.dataroom.email.DataRoomEmailConfigOptions
import anduin.dataroom.link.{DataRoomAccessRequest, DeclinedRequest, RequestAccessStatus}
import anduin.dataroom.notification.NewFileNotificationConfig
import anduin.dataroom.role.*
import anduin.id.dataroom.DataRoomGroupId
import anduin.id.entity.EntityId
import anduin.id.link.ProtectedLinkId
import anduin.model.circe.MapEntry.given
import anduin.model.codec.ProtoCodecs
import anduin.model.common.user.UserId
import anduin.model.id.FileId
import anduin.stargazer.util.client.GraphqlClientUtils.IgnoreUnknownFieldSetCodecs.given
import anduin.stargazer.util.client.GraphqlClientUtils.ProtoEnumCodecs.given
import com.anduin.stargazer.endpoints.AssetPermissionChanges

final case class DataRoomFrontEndState(
  name: String,
  isArchived: Boolean,
  individualRoles: Map[UserId, DataRoomRole],
  creatorUserId: UserId,
  creatorEntityId: EntityId,
  termsOfAccessOptions: DataRoomFrontEndState.TermsOfAccessOptions,
  termsOfAccessCertificates: List[DataRoomFrontEndState.TermsOfAccessCertificate],
  watermarkMetadata: Option[DataRoomFrontEndState.WatermarkMetadata],
  showIndex: Boolean,
  showSearch: Boolean,
  isSemanticSearchEnabled: Boolean,
  showHomePage: Boolean,
  showWhiteLabel: Boolean,
  linkInvitationMap: Map[ProtectedLinkId, DataRoomFrontEndState.LinkInvitation],
  usersJoiningViaLinkInvitation: Map[UserId, ProtectedLinkId],
  watermarkExceptionFiles: Seq[FileId],
  dataRoomNotificationConfigs: Option[DataRoomFrontEndState.DataRoomNotificationConfigs],
  contactUserIds: Set[UserId],
  dataRoomEmailConfigs: DataRoomFrontEndState.DataRoomEmailConfigs
)

object DataRoomFrontEndState {

  given Codec.AsObject[DataRoomFrontEndState] = deriveCodecWithDefaults

  given Codec.AsObject[AssetPermissionChanges] =
    deriveCodecWithDefaults[AssetPermissionChanges]

  given Codec[DataRoomRole] = Codec.from(
    ProtoCodecs.generalSealedValueDecoder,
    ProtoCodecs.sealedValueEncoder
  )

  given Codec.AsObject[DataRoomAccessRequest] =
    deriveCodecWithDefaults[DataRoomAccessRequest]

  given Codec[RequestAccessStatus] = Codec.from(
    ProtoCodecs.generalSealedValueDecoder,
    ProtoCodecs.sealedValueEncoder
  )

  given Codec[DeclinedRequest] = Codec.from(
    ProtoCodecs.generalMessageDecoder,
    ProtoCodecs.generalMessageEncoder
  )

  given Codec[NewFileNotificationConfig] = Codec.from(
    ProtoCodecs.generalMessageDecoder,
    ProtoCodecs.generalMessageEncoder
  )

  final case class WatermarkMetadata(text: String, color: Int, layout: Int, transparency: Int)

  object WatermarkMetadata {
    given Codec.AsObject[WatermarkMetadata] = deriveCodecWithDefaults
  }

  final case class LinkInvitation(
    name: String,
    role: DataRoomRole,
    permissions: Option[AssetPermissionChanges],
    createdBy: UserId,
    createdAt: Option[Instant],
    lastUpdatedBy: UserId,
    lastUpdatedAt: Option[Instant],
    accessRequests: Map[String, DataRoomAccessRequest],
    groupIds: Set[DataRoomGroupId],
    isToaWhitelisted: Boolean
  ) {

    def getRole(dataRoomData: DataRoomData): DataRoomRole = {
      if (groupIds.isEmpty) {
        role
      } else {
        val groupRoles = groupIds.map { groupId =>
          dataRoomData.getGroupData(groupId, includeDeleted = true).map(_.role).getOrElse(DataRoomRole.Empty)
        }
        DataRoomRoleUtils.getMaxRole(groupRoles)
      }
    }

  }

  object LinkInvitation {
    given Codec.AsObject[LinkInvitation] = deriveCodecWithDefaults
  }

  final case class TermsOfAccessCertificate(
    userId: UserId,
    toaFileId: FileId,
    timestamp: Option[Instant],
    ipAddress: Option[String]
  )

  object TermsOfAccessCertificate {
    given Codec.AsObject[TermsOfAccessCertificate] = deriveCodecWithDefaults
  }

  final case class TermsOfAccessOptions(
    isEnabled: Boolean = false,
    versions: List[FileId] = List.empty,
    whitelistedUsers: Set[UserId] = Set.empty
  )

  object TermsOfAccessOptions {
    given Codec.AsObject[TermsOfAccessOptions] = deriveCodecWithDefaults
  }

  final case class DataRoomNotificationConfigs(
    newFileNotificationConfig: Option[NewFileNotificationConfig]
  )

  object DataRoomNotificationConfigs {
    given Codec.AsObject[DataRoomNotificationConfigs] = deriveCodecWithDefaults
  }

  final case class DataRoomEmailConfigs(
    invitationEmail: DataRoomEmailConfigOptions.InvitationEmail
  )

  object DataRoomEmailConfigs {
    given Codec.AsObject[DataRoomEmailConfigs] = deriveCodecWithDefaults
  }

}
