// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.documents

import io.circe.Codec

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.dataroom.documents.DataRoomFileFolderPermissionDetail.*
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.circe.MapEntry.given
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.TeamId
import anduin.protobuf.flow.file.FileFolderPermission
import com.anduin.stargazer.endpoints.{FileManagerLocation, FolderInfo}

final case class DataRoomFileFolderPermissionDetail(
  userPermissions: Map[UserId, UserFileFolderPermissionDetail],
  groupPermissions: Map[DataRoomGroupId, GroupFileFolderPermissionDetail],
  ownParentFolderUsers: Option[List[UserId]],
  ownParentFolderGroups: Option[List[DataRoomGroupId]]
) {

  def getUser(
    userId: UserId,
    updatedUserPermission: Map[UserId, Option[FileFolderPermission]] = Map(),
    updatedGroupPermission: Map[DataRoomGroupId, Option[FileFolderPermission]] = Map(),
    groupIds: List[DataRoomGroupId] = List.empty
  ): Option[FileFolderPermission] = {
    val individualPermission = updatedUserPermission.getOrElse(userId, userPermissions.get(userId).flatMap(_.permission))
    val groupPermissions = groupIds.map(groupId => getGroup(groupId, updatedGroupPermission))
    (individualPermission :: groupPermissions).maxBy(_.map(_.value))
  }

  def getGroup(
    groupId: DataRoomGroupId,
    updatedGroupPermission: Map[DataRoomGroupId, Option[FileFolderPermission]] = Map()
  ): Option[FileFolderPermission] = {
    updatedGroupPermission.getOrElse(groupId, groupPermissions.get(groupId).flatMap(_.permission))
  }

  private def getNewFileFolderNoAccessParentFolders(
    parentFolder: FolderInfo,
    permission: Option[FileFolderPermission],
    noAccessParentFolders: Option[List[NoAccessParentFolder]]
  ) = {
    permission.fold[Option[List[NoAccessParentFolder]]] {
      Some(
        NoAccessParentFolder(
          fileManagerLocation = FileManagerLocation.Folder(None, parentFolder.itemId),
          name = parentFolder.name
        ) :: noAccessParentFolders.getOrElse(List())
      )
    }(_ => noAccessParentFolders)
  }

  private def getNewFileFolderParentFolderOwners[K](
    parentFolderOwners: Option[List[K]],
    permissionMap: Map[K, Option[FileFolderPermission]]
  ) = {
    parentFolderOwners.getOrElse(List.empty[K]) ++ permissionMap.filter(_._2.contains(FileFolderPermission.Own)).keySet
  }

  def getNewFileFolderPermissionDetail(
    parentFolder: FolderInfo,
    userId: UserId,
    isUnassignedUser: Boolean
  ): DataRoomFileFolderPermissionDetail = {
    copy(
      userPermissions = userPermissions
        .updatedWith(userId) {
          _.map(
            _.copy(permission = Option.when(isUnassignedUser)(FileFolderPermission.Own))
          ) // unassigned creator have own permission on new file
        }
        .map { case (userId, permissionDetails) =>
          // As we are creating file/folder, parent folder permission should be considered
          userId -> permissionDetails.copy(
            noAccessParentFolders = getNewFileFolderNoAccessParentFolders(
              parentFolder,
              permissionDetails.permission,
              permissionDetails.noAccessParentFolders
            )
          )
        },
      groupPermissions = groupPermissions.map { case (groupId, permissionDetails) =>
        groupId -> permissionDetails.copy(
          noAccessParentFolders = getNewFileFolderNoAccessParentFolders(
            parentFolder,
            permissionDetails.permission,
            permissionDetails.noAccessParentFolders
          )
        )
      },
      ownParentFolderUsers = Some(
        getNewFileFolderParentFolderOwners(
          parentFolderOwners = ownParentFolderUsers,
          permissionMap = userPermissions.map { case (userId, userPermission) =>
            userId -> userPermission.permission
          }
        )
      ),
      ownParentFolderGroups = Some(
        getNewFileFolderParentFolderOwners(
          parentFolderOwners = ownParentFolderGroups,
          permissionMap = groupPermissions.map { case (groupId, groupPermission) =>
            groupId -> groupPermission.permission
          }
        )
      )
    )
  }

}

object DataRoomFileFolderPermissionDetail {

  given Codec.AsObject[DataRoomFileFolderPermissionDetail] = deriveCodecWithDefaults

  final case class UserFileFolderPermissionDetail(
    userInfo: UserInfo,
    permission: Option[FileFolderPermission],
    noAccessParentFolders: Option[List[NoAccessParentFolder]]
  )

  object UserFileFolderPermissionDetail {
    given Codec.AsObject[UserFileFolderPermissionDetail] = deriveCodecWithDefaults
  }

  final case class NoAccessParentFolder(
    fileManagerLocation: FileManagerLocation.Folder,
    name: String
  )

  object NoAccessParentFolder {
    given Codec.AsObject[NoAccessParentFolder] = deriveCodecWithDefaults
  }

  final case class GroupFileFolderPermissionDetail(
    teamId: TeamId,
    permission: Option[FileFolderPermission],
    noAccessParentFolders: Option[List[NoAccessParentFolder]]
  )

  object GroupFileFolderPermissionDetail {
    given Codec.AsObject[GroupFileFolderPermissionDetail] = deriveCodecWithDefaults
  }

}
