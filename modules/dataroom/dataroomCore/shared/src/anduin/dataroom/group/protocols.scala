// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.group

import java.time.Instant
import io.circe.Codec
import anduin.circe.generic.semiauto.{CirceCodec, deriveCodecWithDefaults}
import anduin.dataroom.role.DataRoomRole
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId
import anduin.model.id.TeamId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.stargazer.service.dataroom.DataRoomPermissionChanges
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import anduin.model.codec.ProtoCodecs.given
import anduin.model.codec.MapCodecs.given
import anduin.stargazer.service.dataroom.validation.{
  DataRoomGroupCheckParams,
  DataRoomInvitedOrJoinedMembersCheckParams,
  DataRoomRoleCheckParams
}

final case class CreateDataRoomGroupParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  name: String,
  role: DataRoomRole,
  assetPermissions: AssetPermissionChanges
) extends DataRoomRoleCheckParams

object CreateDataRoomGroupParams {
  given Codec.AsObject[CreateDataRoomGroupParams] = deriveCodecWithDefaults
}

final case class CreateDataRoomGroupResponse(
  groupId: DataRoomGroupId
)

object CreateDataRoomGroupResponse {
  given Codec.AsObject[CreateDataRoomGroupResponse] = deriveCodecWithDefaults
}

final case class RenameDataRoomGroupParams(
  groupId: DataRoomGroupId,
  name: String
) extends DataRoomRoleCheckParams {
  override def dataRoomWorkflowId: DataRoomWorkflowId = groupId.parent
}

object RenameDataRoomGroupParams {
  given Codec.AsObject[RenameDataRoomGroupParams] = deriveCodecWithDefaults
}

final case class UpdateDataRoomGroupPermissionsParams(
  groupId: DataRoomGroupId,
  permissionChanges: DataRoomPermissionChanges
) extends DataRoomRoleCheckParams {
  override def dataRoomWorkflowId = groupId.parent
}

object UpdateDataRoomGroupPermissionsParams {
  given Codec.AsObject[UpdateDataRoomGroupPermissionsParams] = deriveCodecWithDefaults
}

final case class DeleteDataRoomGroupsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  groupIds: Set[DataRoomGroupId],
  participantSettings: DeleteDataRoomGroupsParams.ParticipantSettings
) extends DataRoomRoleCheckParams
    with DataRoomGroupCheckParams

object DeleteDataRoomGroupsParams {
  given Codec.AsObject[DeleteDataRoomGroupsParams] = deriveCodecWithDefaults

  sealed trait ParticipantSettings derives CanEqual, CirceCodec.WithDefaultsAndTypeName

  object ParticipantSettings {
    case object Keep extends ParticipantSettings
    final case class Remove(emailNotification: Boolean) extends ParticipantSettings
  }

}

final case class AddUsersToDataRoomGroupParams(
  groupId: DataRoomGroupId,
  userIds: Set[UserId]
) extends DataRoomRoleCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams {
  override def dataRoomWorkflowId: DataRoomWorkflowId = groupId.parent
}

object AddUsersToDataRoomGroupParams {
  given Codec.AsObject[AddUsersToDataRoomGroupParams] = deriveCodecWithDefaults
}

final case class AddUsersToMultipleGroupsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  userMap: Map[DataRoomGroupId, Set[UserId]]
) extends DataRoomRoleCheckParams
    with DataRoomGroupCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams derives CirceCodec.WithDefaults {
  override def groupIds = userMap.keySet

  override def userIds: Set[UserId] = userMap.values.flatten.toSet
}

final case class RemoveUsersFromDataRoomGroupParams(
  groupId: DataRoomGroupId,
  userIds: Set[UserId]
) extends DataRoomRoleCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams {
  override def dataRoomWorkflowId = groupId.parent
}

object RemoveUsersFromDataRoomGroupParams {
  given Codec.AsObject[RemoveUsersFromDataRoomGroupParams] = deriveCodecWithDefaults
}

final case class DataRoomGroupData(
  id: DataRoomGroupId,
  name: String,
  role: DataRoomRole,
  createdAt: Option[Instant],
  creator: UserId,
  participants: Seq[UserId],
  teamId: TeamId,
  linkCount: Int,
  isDeleted: Boolean
) derives CanEqual

object DataRoomGroupData {
  given Codec.AsObject[DataRoomGroupData] = deriveCodecWithDefaults
}
