// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.endpoints

import zio.Task

import anduin.dataroom.exception.graphql.DataRoomGraphqlForwardException
import anduin.dataroom.group.*
import anduin.endpoints.DataRoomEndpoints
import anduin.file.GetDownloadUrlResponse
import anduin.link.{CheckAccessGrantedParams, CheckAccessGrantedResponse, CheckValidityException}
import anduin.service.{CommonResponse, GeneralServiceException}
import anduin.stargazer.service.dataroom.*
import anduin.tapir.client.AuthenticatedEndpointClient
import anduin.tapir.endpoint.CommonParams

object DataRoomEndpointClient extends AuthenticatedEndpointClient {

  val createDataRoom: CreateDataRoomParams => Task[Either[GeneralServiceException, CreateDataRoomResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.createDataRoom)

  val duplicateDataRoom: DuplicateDataRoomParams => Task[Either[GeneralServiceException, CreateDataRoomResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.duplicateDataRoom)

  val setIsArchivedDataRoom
    : SetIsArchivedDataRoomParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.setIsArchivedDataRoom)

  val archiveDataRoomAndRemoveUsers
    : ArchiveDataRoomAndRemoveUsersParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.archiveDataRoomAndRemoveUsers)

  val renameDataRoom: RenameDataRoomParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.renameDataRoom)

  val inviteUsers: InviteUsersToDataRoomParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.inviteUsers)

  val acceptInvitation
    : AcceptInvitationToDataRoomParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.acceptInvitation)

  val declineInvitation
    : DeclineInvitationToDataRoomParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.declineInvitation)

  val modifyDataRoomAssetPermissions
    : ModifyDataRoomAssetPermissionParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.modifyDataRoomAssetPermissions)

  val modifyUserPermissions
    : ModifyDataRoomPermissionsParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.modifyUserPermissions)

  val remindInvitation
    : RemindInvitationToDataRoomParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.remindInvitation)

  val removeUsers: RemoveUsersFromDataRoomParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.removeUsers)

  val trackUserVisitDataRoom
    : TrackUserVisitDataRoomParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.trackUserVisitDataRoom)

  val acceptTermsOfAccess
    : AcceptTermsOfAccessToDataRoomParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.acceptTermsOfAccess)

  val modifyGeneralSettings: ModifyDataRoomGeneralSettingsParams => Task[
    Either[GeneralServiceException, ModifyDataRoomGeneralSettingsResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.modifyGeneralSettings)

  val addFolder: AddFolderParams => Task[Either[GeneralServiceException, AddFolderResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.addFolder)

  val renameFolder: RenameFolderParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.renameFolder)

  val renameFile: RenameFileParams => Task[Either[GeneralServiceException, CommonResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.renameFile)

  val getAllFileVersions
    : GetAllFileVersionsParams => Task[Either[GeneralServiceException, GetAllFileVersionsResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getAllFileVersions)

  val deleteFilesAndFolders
    : DeleteFilesAndFoldersParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.deleteFilesAndFolders)

  val restoreFiles: RestoreDataRoomFilesParams => Task[Either[GeneralServiceException, RestoreDataRoomFilesResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.restoreFiles)

  val getDownloadUrl: GetDownloadUrlParams => Task[Either[GetViewUrlException, GetDownloadUrlResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getDownloadUrl)

  val getDownloadFileVersionUrl
    : DataRoomSingleFileVersionDownloadRequest => Task[Either[GeneralServiceException, GetUrlResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getDownloadFileVersionUrl)

  val getViewUrl: GetViewUrlParams => Task[Either[GetViewUrlException, GetUrlResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getViewUrl)

  val getTermsOfAccessUrl: GetTermsOfAccessUrl => Task[Either[GeneralServiceException, GetUrlResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getTermsOfAccessUrl)

  val manualNotification
    : DataRoomManualNotificationParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.manualNotification)

  val createLinkInvitation: CreateDataRoomLinkInvitationParams => Task[
    Either[GeneralServiceException, CreateDataRoomLinkInvitationResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.createLinkInvitation)

  val getSharableLinkConfig
    : GetSharableLinkConfigParams => Task[Either[GeneralServiceException, GetSharableLinkConfigResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getSharableLinkConfig)

  val modifyLinkInvitation
    : ModifyDataRoomLinkInvitationParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.modifyLinkInvitation)

  val joinViaLinkInvitation
    : JoinDataRoomViaLinkInvitationParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.joinViaLinkInvitation)

  val getLinkInvitationInfo: GetDataRoomLinkInvitationInfoParams => Task[
    Either[GeneralServiceException, GetDataRoomLinkInvitationInfoResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getLinkInvitationInfo)

  val deleteLinkInvitation
    : DeleteDataRoomLinkInvitationParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.deleteLinkInvitation)

  val changeFolderOrder: ChangeFolderOrderParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.changeFolderOrder)

  val copyPreCheck: CopyPreCheckParams => Task[Either[GeneralServiceException, CopyPreCheckResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.copyPreCheck)

  val movePreCheck: MovePreCheckParams => Task[Either[GeneralServiceException, MovePreCheckResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.movePreCheck)

  val createShortcut: CreateShortcutParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.createShortcut)

  val extractShortcut: ExtractShortcutParams => Task[Either[GeneralServiceException, ExtractShortcutResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.extractShortcut)

  val recordPageView: RecordPageViewParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.recordPageView)

  val setNotificationSettings
    : SetDataRoomNotificationSettingsParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.setNotificationSettings)

  val markDataRoomNotificationOnboardAsSeen
    : DataRoomEmptyParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(
      DataRoomEndpoints.markDataRoomNotificationOnboardAsSeen
    )

  val exportParticipants
    : DataRoomExportParticipantsParams => Task[Either[GeneralServiceException, DataRoomExportParticipantsResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.exportParticipants)

  val exportPermissions
    : DataRoomExportPermissionsParams => Task[Either[GeneralServiceException, DataRoomExportPermissionsResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.exportPermissions)

  val exportFileActivities
    : ExportFileActivitiesParams => Task[Either[GeneralServiceException, ExportFileActivitiesResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.exportFileActivities)

  val exportDataRoomActivities
    : ExportDataRoomActivitiesParams => Task[Either[GeneralServiceException, ExportDataRoomActivitiesResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.exportDataRoomActivities)

  val setDataRoomTermsOfAccessWhitelistedUsers
    : SetDataRoomTermsOfAccessWhitelistedUsersParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.setDataRoomTermsOfAccessWhitelistedUsers)

  val getDataRoomHomePage
    : GetDataRoomHomePageParams => Task[Either[GeneralServiceException, GetDataRoomHomePageResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getDataRoomHomePage)

  val toggleDataRoomHomePagePublishStatus
    : ToggleDataRoomHomePagePublishStatusParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.toggleDataRoomHomePagePublishStatus)

  val updateDataRoomHomePage
    : UpdateDataRoomHomePageParams => Task[Either[GeneralServiceException, UpdateDataRoomHomePageResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.updateDataRoomHomePage)

  val updateDataRoomWhiteLabel
    : UpdateDataRoomWhiteLabelParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.updateDataRoomWhiteLabel)

  val sendDataRoomHomePageMessage
    : SendDataRoomHomePageMessageParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.sendDataRoomHomePageMessage)

  val checkAccessGranted: CheckAccessGrantedParams => Task[Either[CheckValidityException, CheckAccessGrantedResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.checkAccessGranted)

  val approveAccessRequests
    : ApproveAccessRequestsParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.approveAccessRequests)

  val declineAccessRequests
    : DeclineAccessRequestsParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.declineAccessRequests)

  val getDataRoomActivityLog
    : GetDataRoomActivityLogParams => Task[Either[GeneralServiceException, GetDataRoomActivityLogResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getDataRoomActivityLog)

  val getFileActivityLog
    : GetFileActivityLogParams => Task[Either[GeneralServiceException, GetFileActivityLogResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getFileActivityLog)

  val getThumbnailUrl: GetThumbnailUrlParams => Task[Either[GeneralServiceException, GetThumbnailUrlResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getThumbnailUrl)

  val markDataRoomRecentFilesAsSeen
    : MarkDataRoomRecentFilesAsSeenParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.markDataRoomRecentFilesAsSeen)

  val getEmailTemplate: GetEmailTemplateParams => Task[Either[GeneralServiceException, GetEmailTemplateResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getEmailTemplate)

  val updateEmailTemplate: UpdateEmailTemplateParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.updateEmailTemplate)

  val getDataRoomEmailSenderDetails: GetDataRoomEmailSenderDetailsParams => Task[
    Either[GeneralServiceException, GetDataRoomEmailSenderDetailsResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getDataRoomEmailSenderDetails)

  val updateDataRoomEmailConfigs
    : UpdateDataRoomEmailConfigsParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.updateDataRoomEmailConfigs)

  val updateDataRoomWatermarkExceptionFiles
    : UpdateDataRoomWatermarkExceptionFilesParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.updateDataRoomWatermarkExceptionFiles)

  val setDataRoomPointsOfContact
    : SetDataRoomPointsOfContactParams => Task[Either[GeneralServiceException, DataRoomEmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.setDataRoomPointsOfContact)

  val setViewedSearchOnboarding
    : DataRoomSetViewedSearchOnboardingParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.setViewedSearchOnboarding)

  val setViewedGroupOnboarding: DataRoomSetViewedGroupOnboardingParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.setViewedGroupOnboarding)

  val createGroup: CreateDataRoomGroupParams => Task[Either[GeneralServiceException, CreateDataRoomGroupResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.createGroup)

  val renameGroup: RenameDataRoomGroupParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.renameGroup)

  val deleteGroups: DeleteDataRoomGroupsParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.deleteGroups)

  val addUsersToGroups: AddUsersToMultipleGroupsParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.addUsersToGroups)

  val removeUsersFromGroup: RemoveUsersFromDataRoomGroupParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.removeUsersFromGroup)

  val updateGroupPermission: UpdateDataRoomGroupPermissionsParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.updateGroupPermission)

  val fullSearch: FullSearchDataRoomParams => Task[Either[GeneralServiceException, FullSearchDataRoomResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.fullSearch)

  val quickSearch: QuickSearchDataRoomParams => Task[Either[GeneralServiceException, QuickSearchDataRoomResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.quickSearch)

  val semanticSearch
    : SemanticSearchDataRoomParams => Task[Either[GeneralServiceException, SemanticSearchDataRoomResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.semanticSearch)

  val exportReportForUploadAndAutoImportFiles: ExportReportForUploadAndAutoImportFilesParams => Task[
    Either[GeneralServiceException, ExportReportForUploadAndAutoImportFilesResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.exportReportForUploadAndAutoImportFiles)

  val exportReportForFolderMappingResults: ExportReportForFolderMappingResultsParams => Task[
    Either[GeneralServiceException, ExportReportForFolderMappingResultsResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.exportReportForFolderMappingResults)

  val getFileWithUsersInsights: DataRoomGetFileWithUsersInsightsParams => Task[
    Either[GeneralServiceException, DataRoomGetFileWithUsersInsightsResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getFileWithUsersInsights)

  val getUserWithFilesInsights: DataRoomGetUserWithFilesInsightsParams => Task[
    Either[GeneralServiceException, DataRoomGetParticipantsWithFilesInsightsResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getUserWithFilesInsights)

  val getGroupWithFilesInsights: DataRoomGetGroupWithFilesInsightsParams => Task[
    Either[GeneralServiceException, DataRoomGetParticipantsWithFilesInsightsResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getGroupWithFilesInsights)

  val getInsightsFileTimeline: DataRoomGetInsightsFileTimelineParams => Task[
    Either[GeneralServiceException, DataRoomGetInsightsFileTimelineResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getInsightsFileTimeline)

  val getInsightsUserTimeline: DataRoomGetInsightsUserTimelineParams => Task[
    Either[GeneralServiceException, DataRoomGetInsightsParticipantsTimelineResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getInsightsUserTimeline)

  val getInsightsGroupTimeline: DataRoomGetInsightsGroupTimelineParams => Task[
    Either[GeneralServiceException, DataRoomGetInsightsParticipantsTimelineResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getInsightsGroupTimeline)

  val getTopActiveUsers: DataRoomGetTopActiveParticipantsParams => Task[
    Either[GeneralServiceException, DataRoomGetTopActiveUsersResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getTopActiveUsers)

  val getTopActiveGroups: DataRoomGetTopActiveParticipantsParams => Task[
    Either[GeneralServiceException, DataRoomGetTopActiveGroupsResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getTopActiveGroups)

  val getTopActiveFiles: DataRoomGetTopActiveFilesParams => Task[
    Either[GeneralServiceException, DataRoomGetTopActiveFilesResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getTopActiveFiles)

  val getInsightsNewestParticipants: DataRoomGetInsightsNewestParticipantsParams => Task[
    Either[GeneralServiceException, DataRoomGetInsightsNewestParticipantsResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getInsightsNewestParticipants)

  val getUserSummaryInsights: DataRoomGetUserSummaryInsightsParams => Task[
    Either[GeneralServiceException, DataRoomGetParticipantsSummaryInsightsResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getUserSummaryInsights)

  val getGroupSummaryInsights: DataRoomGetGroupSummaryInsightsParams => Task[
    Either[GeneralServiceException, DataRoomGetParticipantsSummaryInsightsResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getGroupSummaryInsights)

  val getFileSummaryInsights: DataRoomGetFileSummaryInsightsParams => Task[
    Either[GeneralServiceException, DataRoomGetFileSummaryInsightsResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getFileSummaryInsights)

  val getFilePageTotalViewTime: DataRoomGetFilePageTotalViewTimeParams => Task[
    Either[GeneralServiceException, DataRoomGetFilePageTotalViewTimeResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getFilePageTotalViewTime)

  val getFilePageUserViewPercentage: DataRoomGetFilePageUserViewPercentageParams => Task[
    Either[GeneralServiceException, DataRoomGetFilePageUserViewPercentageResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getFilePageUserViewPercentage)

  val getDataRoomUsersInsights: DataRoomGetParticipantsInsightsParams => Task[
    Either[GeneralServiceException, DataRoomGetUsersInsightsResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getDataRoomUsersInsights)

  val getDataRoomGroupsInsights: DataRoomGetParticipantsInsightsParams => Task[
    Either[GeneralServiceException, DataRoomGetGroupsInsightsResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getDataRoomGroupsInsights)

  val getDataRoomFilesInsights: DataRoomGetFilesInsightsParams => Task[
    Either[GeneralServiceException, DataRoomGetFilesInsightsResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getDataRoomFilesInsights)

  val getToaCustomization: GetToaCustomizationParams => Task[
    Either[GeneralServiceException, GetToaCustomizationResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getToaCustomization)

  val getDataRoomFileFolderPermission: GetDataRoomFileFolderPermissionParams => Task[
    Either[GeneralServiceException, GetDataRoomFileFolderPermissionResponse]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getDataRoomFileFolderPermission)

  val getDataRoomDashboardWhitelabel: CommonParams.Empty => Task[
    Either[GeneralServiceException, DataRoomDashboardWhitelabel]
  ] = toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getDataRoomDashboardWhitelabel)

  val getDataRoomDashboard
    : GetDataRoomDashboardParams => Task[Either[GeneralServiceException, GetDataRoomDashboardResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getDataRoomDashboard)

  val getDataRoomActiveInOrg
    : GetDataRoomActiveInOrgParams => Task[Either[GeneralServiceException, GetDataRoomActiveInOrgResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getDataRoomActiveInOrg)

  val getDataRoomDetail
    : GetDataRoomDetailParams => Task[Either[DataRoomGraphqlForwardException, GetDataRoomDetailResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getDataRoomDetail)

  val getDataRoomCountForOtherRegions
    : CommonParams.Empty => Task[Either[GeneralServiceException, GetDataRoomCountForOtherRegionsResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataRoomEndpoints.getDataRoomCountForOtherRegions)

}
