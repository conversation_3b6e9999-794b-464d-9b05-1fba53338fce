syntax = "proto3";

package anduin.dataroom.webhook;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  preserve_unknown_fields: false
  single_file: true
  import: "anduin.model.id.stage.DataRoomWorkflowId"
  import: "anduin.id.dataroom.DataRoomGroupId"
  import: "anduin.model.common.user.UserId"
  preamble: "private sealed trait WebhookEventTrait {"
  preamble: "  def dataRoomId: DataRoomWorkflowId"
  preamble: "}"
  preamble: ""
  preamble: "private sealed trait EmptyWebhookEventTrait {"
  preamble: "  def dataRoomId: DataRoomWorkflowId = DataRoomWorkflowId.defaultValue.get"
  preamble: "}"
};

message DataRoomUserInvitedWebhookEvent {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  repeated string user_ids = 2 [(scalapb.field).type = "UserId"];
  repeated string group_ids = 3 [(scalapb.field).type = "DataRoomGroupId"];
}

message DataRoomUserJoinWebhookEvent {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string user_id = 2 [(scalapb.field).type = "UserId"];
  repeated string group_ids = 3 [(scalapb.field).type = "DataRoomGroupId"];
}

message DataRoomUserAddToGroupWebhookEvent {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  repeated string user_ids = 2 [(scalapb.field).type = "UserId"];
  string group_id = 4 [(scalapb.field).type = "DataRoomGroupId"];
}

message DataRoomUserRemoveFromGroupWebhookEvent {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  repeated string user_ids = 2 [(scalapb.field).type = "UserId"];
  string group_id = 4 [(scalapb.field).type = "DataRoomGroupId"];
}

message DataRoomUserRemovedWebhookEvent {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  repeated string user_ids = 2 [(scalapb.field).type = "UserId"];
}

message DataRoomUserDeclineInvitationWebhookEvent {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string user_id = 2 [(scalapb.field).type = "UserId"];
  repeated string group_ids = 3 [(scalapb.field).type = "DataRoomGroupId"];
}

message DataRoomGroupDeletedWebhookEvent {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string group_id = 2 [(scalapb.field).type = "DataRoomGroupId"];
  repeated string user_ids = 3 [(scalapb.field).type = "UserId"];
}
message DataRoomWebhookEvent {
  option (scalapb.message).sealed_oneof_extends = "WebhookEventTrait";
  option (scalapb.message).sealed_oneof_empty_extends = "EmptyWebhookEventTrait";

  oneof sealed_value {
    DataRoomUserJoinWebhookEvent data_room_user_join_webhook_event = 1;
    DataRoomUserRemovedWebhookEvent data_room_user_removed_webhook_event = 3;
    DataRoomUserInvitedWebhookEvent data_room_user_invited_webhook_event = 4;
    DataRoomUserDeclineInvitationWebhookEvent data_room_user_decline_invitation_webhook_event = 5;
    DataRoomGroupDeletedWebhookEvent data_room_group_deleted_webhook_event = 6;
    DataRoomUserAddToGroupWebhookEvent data_room_user_add_to_group_webhook_event = 7;
    DataRoomUserRemoveFromGroupWebhookEvent data_room_user_remove_from_group_webhook_event = 8;
  }
}