syntax = "proto3";

package anduin.dataroom.workflow;

import "scalapb/scalapb.proto";
import "anduin/dataroom/integration/data_room_integration_core.proto";
import "google/protobuf/struct.proto";

option (scalapb.options) = {
  package_name: "anduin.dataroom.workflow"
  single_file: true
  import: "io.circe.Json"
  import: "anduin.model.codec.ProtoCodecs.jsonStructMapper"
  import: "anduin.model.document.DocumentStorageId"
  import: "anduin.dms.DocumentStorageIdMapper.given"
};

message UploadFileItemMessage{
  repeated string dest_path = 1;
  string name = 2;
  string storage_id = 3 [(scalapb.field).type = "DocumentStorageId"];
}

message StorageFileUploadParamMessage {
  anduin.dataroom.integration.CloudProviderType cloud_provider_type = 1;
  string root_folder_id = 2;
  google.protobuf.Struct config_opt = 3 [(scalapb.field).type = "Json"];
  repeated UploadFileItemMessage files = 4;
  map<string, string> sync_meta_data = 5;
}

message StorageFileUploadResponseMessage {

}
