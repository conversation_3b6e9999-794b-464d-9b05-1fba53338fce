// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.integration.service

import zio.{Task, ZIO}

import anduin.dataroom.integration.endpoint.{
  CloudStorageConfig,
  GetAllCloudProviderSharedFoldersResponse,
  GetCloudProvidersSetupInfoResponse,
  StorageIntegrationConfig
}
import anduin.model.common.user.UserId
import anduin.model.id.{FileId, FolderId}
import anduin.service.AuthenticatedRequestContext

trait DataRoomExternalIntegrationService {

  def getBotAccountContext(): Task[AuthenticatedRequestContext]

  def syncFiles(
    rootFolderId: FolderId,
    files: Seq[FileId],
    syncMetaData: Map[String, String] = Map.empty // Only for logging now
  ): Task[Unit]

  def getStorageConfig(
    actor: UserId,
    folderId: FolderId
  ): Task[Option[StorageIntegrationConfig]]

  def createStorageConfig(
    actor: UserId,
    folderId: FolderId
  ): Task[StorageIntegrationConfig]

  def updateStorageConfig(
    actor: UserId,
    folderId: FolderId,
    configs: Seq[CloudStorageConfig]
  ): Task[Unit]

  def deleteStorageConfig(
    actor: UserId,
    folderId: FolderId
  ): Task[Unit]

  def getAllCloudProviderSharedFolders(actor: UserId): Task[GetAllCloudProviderSharedFoldersResponse]

  def getCloudProvidersSetupInfo(actor: UserId): Task[GetCloudProvidersSetupInfoResponse]

}

object DataRoomExternalIntegrationService {

  case class Mock() extends DataRoomExternalIntegrationService {

    override def getBotAccountContext(): Task[AuthenticatedRequestContext] =
      ZIO.succeed(AuthenticatedRequestContext.defaultInstance)

    override def syncFiles(
      rootFolderId: FolderId,
      files: Seq[FileId],
      syncMetaData: Map[String, String]
    ): Task[Unit] =
      ???

    override def getStorageConfig(actor: UserId, folderId: FolderId): Task[Option[StorageIntegrationConfig]] = ???

    override def createStorageConfig(actor: UserId, folderId: FolderId): Task[StorageIntegrationConfig] = ???

    override def updateStorageConfig(actor: UserId, folderId: FolderId, configs: Seq[CloudStorageConfig]): Task[Unit] =
      ???

    override def deleteStorageConfig(actor: UserId, folderId: FolderId): Task[Unit] = ???

    override def getAllCloudProviderSharedFolders(actor: UserId): Task[GetAllCloudProviderSharedFoldersResponse] = ???

    override def getCloudProvidersSetupInfo(actor: UserId): Task[GetCloudProvidersSetupInfoResponse] = ???

  }

}
