// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.tracking.participant

import anduin.component.text.SearchBox
import anduin.component.util.JsDateFormatterUtils
import anduin.dataroom.DataRoomData
import anduin.dataroom.DataRoomUserData.InvitedUser
import anduin.dataroom.components.DurationFormatter
import anduin.dataroom.detail.DataRoomUserInfoRenderer
import anduin.dataroom.detail.participants.group.DataRoomGroupNameTag
import anduin.dataroom.detail.participants.group.DataRoomGroupNameTag.RenderGroupData
import anduin.dataroom.detail.tracking.{TrackingTargetCriteria, TrackingTargetCriteriaDropdown}
import anduin.model.common.user.UserId
import design.anduin.components.nonidealstate.react.NonIdealStateR
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.table.Table
import design.anduin.components.tag.Tag
import design.anduin.components.tag.react.TagR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import stargazer.model.routing.DynamicAuthPage

import java.time.{Duration, Instant}
import stargazer.component.routing.react.WithReactRouterR
import design.anduin.components.toast.Toast
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import zio.ZIO
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.service.GeneralServiceException
import anduin.stargazer.service.dataroom.DataRoomGetUsersInsightsResponse.UserInsights
import anduin.stargazer.service.dataroom.{DataRoomGetParticipantsInsightsParams, DataRoomGetUsersInsightsResponse}
import anduin.utils.ScalaUtils
import com.anduin.stargazer.client.utils.ZIOUtils

final case class DataRoomParticipantsInsight(
  dataRoomData: DataRoomData
) derives CanEqual {

  def apply(onViewUserDetail: UserId => Callback): VdomElement =
    DataRoomParticipantsInsight.component(
      DataRoomParticipantsInsight.Props(
        this,
        onViewUserDetail
      )
    )

}

object DataRoomParticipantsInsight {

  private final case class Props(
    params: DataRoomParticipantsInsight,
    onViewUserDetail: UserId => Callback
  )

  private final case class State(
    searchTerm: String = "",
    dataRoomUsersInsights: Option[DataRoomGetUsersInsightsResponse] = None
  )

  val emptyContentStr = "--"

  private final class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomNode = {
      <.div(
        tw.flexCol,
        <.div(
          tw.heading2,
          "Participants"
        ),
        <.div(
          ComponentUtils.testId(DataRoomParticipantsInsight, "UserDropdown"),
          tw.flex.itemsCenter.py16,
          Option.when(props.params.dataRoomData.groupMap.nonEmpty) {
            WithReactRouterR { router =>
              TrackingTargetCriteriaDropdown(
                value = TrackingTargetCriteria.Individuals,
                onChange = {
                  case TrackingTargetCriteria.Individuals => Callback.empty
                  case TrackingTargetCriteria.Groups =>
                    router.set(DynamicAuthPage.DataRoomInsightsGroupsPage(props.params.dataRoomData.workflowId))
                },
                labelOpt = Some("Insights into")
              )()
            }
          },
          <.div(
            ComponentUtils.testId(DataRoomParticipantsInsight, "SearchBox"),
            tw.flex.flexFill.justifyEnd,
            SearchBox(
              placeholder = "Find participants",
              onChange = searchTerm => scope.modState(_.copy(searchTerm = searchTerm)),
              width = "280px"
            )()
          )
        ),
        renderTable(props, state)
      )
    }

    private val smallColumnWidth = "10%"

    private val mediumColumnWidth = "18%"

    private val UserTable = (new Table[UserInsights])()

    private def getUserInsightsRows(
      props: Props,
      state: State,
      dataRoomUserInsights: DataRoomGetUsersInsightsResponse
    ) = {
      val invitedUserIds =
        props.params.dataRoomData.latestState.participatingUsers.filter(user =>
          ScalaUtils.isMatch[InvitedUser](user._2.teamState)
        )
      val usersHavingActivities = dataRoomUserInsights.users.filterNot { user =>
        invitedUserIds.contains(user.userId)
      }
      val invitedUsers = invitedUserIds
        .map { case (userId, userData) =>
          (
            UserInsights(
              userId = userId,
              accessedFileCount = 0L,
              visitCount = 0L,
              lastView = None,
              lastDownload = None,
              viewDuration = None
            ),
            userData.userInfo
          )
        }
        .toSeq
        .map(_._1)
      val searchTerm = state.searchTerm.toLowerCase
      (usersHavingActivities ++ invitedUsers).filter { userInsights =>
        props.params.dataRoomData.latestState.getUserInfo(userInsights.userId).exists { userInfo =>
          userInfo.emailAddress.toLowerCase.contains(searchTerm) ||
          userInfo.fullName.toLowerCase.contains(searchTerm)
        }
      }
    }

    private def getNameColumn(props: Props): Table.Column[UserInsights] = {
      Table.Column(
        head = "Name",
        render = renderNameColumn(props),
        sortBy = Table.ColumnOrdering { entry =>
          getUsername(props, entry).map(_.toLowerCase).getOrElse("")
        }
      )
    }

    private def getGroupColumn(props: Props): Table.Column[UserInsights] = {
      Table.Column(
        head = "Group",
        render = renderGroupColumn(props),
        width = mediumColumnWidth
      )
    }

    private def getVisitColumn: Table.Column[UserInsights] = {
      Table.Column(
        head = "Visits",
        render = entry => renderSimpleColumn("Visits", Some(entry.visitCount.toString)),
        width = smallColumnWidth,
        sortBy = Table.ColumnOrdering(_.visitCount)
      )
    }

    private def getAccessColumn(state: State): Table.Column[UserInsights] = {
      val totalFile = state.dataRoomUsersInsights.fold(0L)(_.totalFileCount)
      Table.Column(
        head = "Access",
        render = entry => renderSimpleColumn("Access", Some(getAccessedPercent(entry, totalFile).round.pct)),
        width = smallColumnWidth,
        sortBy = Table.ColumnOrdering(getAccessedPercent(_, totalFile))
      )
    }

    private def getTimeSpentColumn(props: Props): Table.Column[UserInsights] = {
      Table.Column(
        head = "Total time spent",
        render = renderTimeSpent,
        width = mediumColumnWidth,
        sortBy = Table.ColumnOrdering(totalTimeSpentSortBy(props))
      )
    }

    private def getLastActionColumn(props: Props): Table.Column[UserInsights] = {
      Table.Column(
        head = "Last action",
        render = renderLastAction(props),
        width = mediumColumnWidth,
        sortBy = Table.ColumnOrdering(lastActionSortBy(props))
      )
    }

    private def renderTable(props: Props, state: State) = {
      val hasGroup = props.params.dataRoomData.groupMap.nonEmpty
      state.dataRoomUsersInsights.fold {
        BlockIndicatorR(isFullHeight = true)()
      } { dataRoomUsersInsight =>
        val rows = getUserInsightsRows(
          props,
          state,
          dataRoomUsersInsight
        )
        <.div(
          ComponentUtils.testId(DataRoomParticipantsInsight, "ParticipantTable"),
          tw.wPc100.overflowYAuto,
          if (rows.nonEmpty) {
            UserTable(
              getKey = _.userId.idString,
              style = Table.Style.Minimal,
              rows = rows,
              sortColumn = Some(4),
              sortIsAsc = false,
              headIsSticky = Some(Table.Sticky()),
              columns = Seq(
                Seq(getNameColumn(props)),
                Option.when(hasGroup)(getGroupColumn(props)),
                Seq(
                  getVisitColumn,
                  getAccessColumn(state),
                  getTimeSpentColumn(props),
                  getLastActionColumn(props)
                )
              ).flatten,
              renderRow = renderRow(props)
            )()
          } else {
            <.div(
              ComponentUtils.testId(DataRoomParticipantsInsight, "SearchEmptyState"),
              ^.height := "70vh",
              NonIdealStateR(
                icon = {
                  <.img(
                    ^.width := "50px",
                    ^.src := "/web/gondor/images/dataroom/search-users-illustration.svg"
                  )
                },
                title = s"No participant found",
                description = "Adjust your search criteria and try again"
              )()
            )
          }
        )
      }
    }

    private def renderRow(props: Props): Table.RenderRow[UserInsights] = { rows => (key, cells, item) =>
      if (isInvited(item, props.params.dataRoomData)) {
        Table.defaultRenderRow(rows)(
          key,
          cells,
          item
        )
      } else {
        <.tr(
          ComponentUtils.testId(DataRoomParticipantsInsight, "ParticipantRow"),
          ^.key := key,
          ^.onClick --> props.onViewUserDetail(item.userId),
          tw.cursorPointer.hover(tw.bgPrimary1.bgOpacity30),
          cells
        )
      }
    }

    private def totalTimeSpentSortBy(props: Props)(entry: UserInsights) = {
      val durationOpt = entry.viewDuration
      if (isInvited(entry, props.params.dataRoomData)) {
        None
      } else {
        durationOpt.orElse(Some(Duration.ofSeconds(0)))
      }
    }

    private def lastActionSortBy(props: Props)(entry: UserInsights) = {
      val instantOpt = getLastAction(entry).map(_._2)
      if (isInvited(entry, props.params.dataRoomData)) {
        None
      } else {
        instantOpt.orElse(Some(Instant.MIN))
      }
    }

    private def getUsername(props: Props, entry: UserInsights) = {
      props.params.dataRoomData.latestState.getUserInfo(entry.userId).map(_.fullName)
    }

    private def getEmail(props: Props, entry: UserInsights) = {
      props.params.dataRoomData.latestState.getUserInfo(entry.userId).map(_.emailAddress)
    }

    private def getUserGroup(props: Props, entry: UserInsights) = {
      props.params.dataRoomData.getGroupsOfUser(entry.userId)
    }

    private def renderNameColumn(props: Props)(entry: UserInsights) = {
      val userInfoOpt = props.params.dataRoomData.latestState.getUserInfo(entry.userId)
      val isDeleted = props.params.dataRoomData.latestState.deletedUsers.contains(entry.userId)
      Table.Cell(
        DataRoomUserInfoRenderer(
          entry.userId,
          emailAddress = userInfoOpt.map(_.emailAddress).getOrElse(""),
          fullName = userInfoOpt.map(_.fullName).getOrElse(""),
          isDeleted = isDeleted
        )()
      )
    }

    private def renderGroupColumn(props: Props)(entry: UserInsights) = {
      Table.Cell(
        <.div(
          ComponentUtils.testId(DataRoomParticipantsInsight, "ParticipantGroup"),
          DataRoomGroupNameTag(
            groups = getUserGroup(props, entry).map { group =>
              RenderGroupData(group.id, group.name, group.isDeleted)
            },
            maxLines = 2
          )()
        )
      )
    }

    private def renderSimpleColumn(name: String, contentOpt: Option[String]) = {
      Table.Cell(
        <.div(
          ComponentUtils.testId(DataRoomParticipantsInsight, name),
          contentOpt.getOrElse[String](emptyContentStr)
        )
      )
    }

    private def renderTimeSpent(entry: UserInsights) = {
      renderSimpleColumn("TimeSpent", entry.viewDuration.map(DurationFormatter(_)))
    }

    private def getLastAction(entry: UserInsights) = {
      List(entry.lastView.map("Viewed" -> _), entry.lastDownload.map("Downloaded" -> _)).flatten.maxByOption(_._2)
    }

    private def isInvited(user: UserInsights, dataRoomData: DataRoomData): Boolean = {
      dataRoomData.latestState.participatingUsers
        .get(user.userId)
        .exists(user => ScalaUtils.isMatch[InvitedUser](user.teamState))
    }

    private def getAccessedPercent(user: UserInsights, totalFile: Long): Double = {
      if (totalFile == 0) {
        100.0
      } else {
        user.accessedFileCount.toDouble * 100 / totalFile
      }
    }

    private def renderLastAction(props: Props)(entry: UserInsights) = {
      val lastAction = getLastAction(entry)
      Table.Cell(
        lastAction.fold[VdomNode] {
          val isUserInvited = isInvited(entry, props.params.dataRoomData)
          if (isUserInvited) {
            TagR(color = Tag.Light.Warning, label = "Invitation pending")()
          } else {
            emptyContentStr
          }
        } { case (action, timestamp) =>
          React.Fragment(
            <.div(
              ComponentUtils.testId(DataRoomParticipantsInsight, "LastAction"),
              tw.textGray8.fontSemiBold.text13.leading20,
              action
            ),
            <.div(
              ComponentUtils.testId(DataRoomParticipantsInsight, "LastActionDate"),
              tw.textGray7.fontNormal.text11.leading16,
              JsDateFormatterUtils.format(timestamp, JsDateFormatterUtils.JsDateFormat.MonthDateYearTime2)
            )
          )
        }
      )
    }

    def getDataRoomUsersInsights: Callback = {
      for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback(
          DataRoomEndpointClient
            .getDataRoomUsersInsights(
              DataRoomGetParticipantsInsightsParams(
                props.params.dataRoomData.workflowId
              )
            )
            .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
            .map(
              _.fold(
                _ =>
                  scope.modState(
                    _.copy(dataRoomUsersInsights = None),
                    Toast.errorCallback("Failed to load data room users")
                  ),
                resp => scope.modState(_.copy(dataRoomUsersInsights = Some(resp)))
              )
            )
        )

      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidMount(_.backend.getDataRoomUsersInsights)
    .componentDidUpdate { scope =>
      val prevProps = scope.prevProps
      val currProps = scope.currentProps
      Callback.when(prevProps.params != currProps.params) {
        scope.backend.getDataRoomUsersInsights
      }
    }
    .build

}
