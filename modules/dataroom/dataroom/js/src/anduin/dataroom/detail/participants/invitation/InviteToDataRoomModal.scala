// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.invitation

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.modal.Modal
import design.anduin.components.portal.PortalUtils
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.dataroom.detail.participants.invitation.message.MessageStep
import anduin.dataroom.detail.participants.invitation.participants.ParticipantsStep
import anduin.dataroom.detail.participants.invitation.permissions.{PermissionFooterSection, PermissionsStep}
import anduin.dataroom.email.{DataRoomEmailTemplateType, WithEmailTemplate}
import anduin.dataroom.group.DataRoomGroupData
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.dataroom.{DataRoomData, DataRoomPermissionCheck}
import anduin.file.explorer.header.FileHeaderActions
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.stargazer.service.dataroom.GetEmailTemplateRenderData
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.endpoints.AssetPermissionChanges

private[participants] final case class InviteToDataRoomModal(
  actorUserId: UserId,
  dataRoomWorkflowId: DataRoomWorkflowId,
  initialGroupOpt: Option[DataRoomGroupId],
  actorRole: DataRoomRole,
  joinedOrInvitedUsers: Set[String],
  dataRoomData: DataRoomData,
  invitationInfo: StateSnapshotWithModFn[InvitationInfo]
) derives CanEqual {

  private val isInvitationEmailEnabled =
    dataRoomData.latestState.dataRoomCreatedState.dataRoomEmailConfigs.invitationEmail.isEnabled

  def apply(): VdomElement = InviteToDataRoomModal.component(this)

}

private[participants] object InviteToDataRoomModal {

  private type Props = InviteToDataRoomModal

  private final case class State(
    step: InvitationStep,
    users: InvitationUsers,
    message: InvitationMessage,
    groupSettings: InvitationGroupSettings
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    private val stepStateSnapshot = {
      StateSnapshotWithModFn.withReuse
        .zoom[State, InvitationStep](_.step)(step => _.copy(step = step))
        .prepareVia(scope)
    }

    private val invitationUsersStateSnapshot = {
      StateSnapshotWithModFn.withReuse
        .zoom[State, InvitationUsers](_.users)(users => _.copy(users = users))
        .prepareVia(scope)
    }

    private val messageStateSnapshot = {
      StateSnapshotWithModFn.withReuse
        .zoom[State, InvitationMessage](_.message)(message => _.copy(message = message))
        .prepareVia(scope)
    }

    private val settingsStateSnapshot = StateSnapshotWithModFn.withReuse
      .zoom[State, PermissionSettings](_.users.settings)(settings =>
        state => state.copy(users = state.users.copy(settings = settings))
      )
      .prepareVia(scope)

    def render(props: Props, state: State): VdomElement = {
      Modal(
        title = renderTitle(props, state),
        renderTarget = renderTarget(props),
        renderContent = renderContent(props, state),
        size = Modal.Size(
          width = state.step match {
            case InvitationStep.Participants => Modal.Width.Px720
            case InvitationStep.Permissions  => Modal.Width.Px1160
            case InvitationStep.Message =>
              if (props.isInvitationEmailEnabled) Modal.Width.Px600 else Modal.Width.Px480
          }
        ),
        isClosable = Some(PortalUtils.IsClosable(onEsc = true, onOutsideClick = false)),
        afterUserClose = scope.setState(getDefaultState(props))
      )()
    }

    private def renderTarget(props: Props)(openToggle: Callback) = {
      FileHeaderActions.renderButtonWithTooltip {
        DataRoomPermissionCheck.expiredPlan(props.dataRoomData.dataRoomPlan)
      } { res =>
        Button(
          testId = "Invite",
          style = Button.Style.Full(
            color = Button.Color.Primary,
            icon = Some(Icon.Glyph.UserAdd)
          ),
          isDisabled = res.isEmpty,
          onClick = openToggle
        )("Invite")
      }
    }

    private def renderTitle(props: Props, state: State) = {
      state.step match {
        case InvitationStep.Participants =>
          if (DataRoomRoleUtils.isInternal(props.actorRole)) {
            "Add participants"
          } else {
            "Invite participants"
          }
        case InvitationStep.Permissions =>
          if (state.groupSettings.isNoGroup) {
            "Set participants' role and permissions"
          } else {
            "Review permissions"
          }
        case InvitationStep.Message =>
          if (props.isInvitationEmailEnabled) {
            "Add message"
          } else {
            "Invitation emails have been disabled"
          }
      }
    }

    private def renderContent(props: Props, state: State)(closeToggle: Callback) = {
      val step = stepStateSnapshot(state)
      val users = invitationUsersStateSnapshot(state)
      val message = messageStateSnapshot(state)
      val settings = settingsStateSnapshot(state)
      WithEmailTemplate(
        dataRoomWorkflowId = props.dataRoomWorkflowId,
        templateType = DataRoomEmailTemplateType.Invitation,
        isRendered = true,
        renderData = Some(
          GetEmailTemplateRenderData(
            inviter = Some(props.actorUserId)
          )
        ),
        onFetch = emailTemplate => scope.modState(_.copy(message = InvitationMessage.fromEmailTemplate(emailTemplate)))
      ) { (emailTemplateOpt, _, _) =>
        state.step match {
          case InvitationStep.Participants =>
            ParticipantsStep(
              step = step,
              users = users,
              actorUserId = props.actorUserId,
              actorRole = props.actorRole,
              dataRoomData = props.dataRoomData,
              existingUsers = props.joinedOrInvitedUsers,
              remainingSeatCount = props.dataRoomData.getRemainingSeatCount(),
              groupSettings = state.groupSettings,
              onGroupChange = onGroupChanged
            )()
          case InvitationStep.Permissions =>
            PermissionsStep(
              dataRoomWorkflowId = props.dataRoomWorkflowId,
              settings = settings,
              dataRoomData = props.dataRoomData,
              actorUserId = props.actorUserId,
              groupSettings = state.groupSettings,
              actorRole = props.actorRole,
              footer = PermissionFooterSection(step)()
            )()
          case InvitationStep.Message =>
            MessageStep(
              step = step,
              users = users,
              invitationInfo = props.invitationInfo,
              message = message,
              emailTemplateOpt = emailTemplateOpt,
              dataRoomWorkflowId = props.dataRoomData.workflowId,
              groupSettings = state.groupSettings,
              closeToggle = closeToggle,
              isInvitationEmailEnabled = props.isInvitationEmailEnabled
            )()
        }
      }
    }

    private def getMaxRole(groupMap: Map[DataRoomGroupId, DataRoomGroupData], groupSettings: InvitationGroupSettings)
      : DataRoomRole = {
      groupSettings match {
        case InvitationGroupSettings.NoGroup => DataRoomRole.Empty
        case InvitationGroupSettings.AddToGroups(groupIds) =>
          DataRoomRoleUtils.getMaxRole(groupIds.flatMap(groupMap.get).map(_.role))
      }
    }

    private def onGroupChanged(groupSettings: InvitationGroupSettings): Callback = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- {
          val users = invitationUsersStateSnapshot(state)
          val modifyCallback = groupSettings match {
            case InvitationGroupSettings.NoGroup =>
              users.modState { s =>
                InvitationUsers
                  .default(
                    props.dataRoomWorkflowId,
                    props.actorRole,
                    props.dataRoomData.hasViewOnlyPlan,
                    canInvite = s.settings.canInvite,
                    isInternal = DataRoomRoleUtils.isInternal(props.actorRole)
                  )
                  .copy(
                    emails = s.emails,
                    isToaRequired = s.isToaRequired
                  )
              }
            case addToGroups: InvitationGroupSettings.AddToGroups =>
              users.modState { s =>
                s.copy(
                  settings = s.settings
                    .copy(
                      role = getMaxRole(props.dataRoomData.groupMap, addToGroups),
                      assetPermission = AssetPermissionChanges()
                    )
                    .withCanInvite(s.settings.canInvite)
                )
              }
          }
          scope.modState(_.copy(groupSettings = groupSettings)) >> modifyCallback
        }
      } yield ()
    }

  }

  private def getDefaultState(props: Props) = {
    val selectedGroupOpt = Option.when(DataRoomRoleUtils.isAdmin(props.actorRole))(props.initialGroupOpt).flatten
    val users = InvitationUsers.default(
      props.dataRoomWorkflowId,
      props.actorRole,
      props.dataRoomData.hasViewOnlyPlan,
      canInvite = false,
      isInternal = DataRoomRoleUtils.isInternal(props.actorRole)
    )
    State(
      step = InvitationStep.Participants,
      users = selectedGroupOpt
        .flatMap(props.dataRoomData.groupMap.get)
        .fold(users)(group =>
          users.copy(settings = users.settings.copy(role = group.role, assetPermission = AssetPermissionChanges()))
        ),
      message = InvitationMessage.default(props.dataRoomData.latestState.dataRoomCreatedState.name),
      groupSettings = selectedGroupOpt.fold(InvitationGroupSettings.NoGroup)(groupId =>
        InvitationGroupSettings.AddToGroups(Set(groupId))
      )
    )
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps(getDefaultState)
    .renderBackend[Backend]
    .componentDidUpdate { scope =>
      val dataRoomChanged = scope.prevProps.dataRoomWorkflowId != scope.currentProps.dataRoomWorkflowId
      val groupChanged =
        (scope.prevProps.initialGroupOpt != scope.currentProps.initialGroupOpt) || (scope.prevProps.dataRoomData.groupMap != scope.currentProps.dataRoomData.groupMap)
      for {
        _ <- Callback.when(dataRoomChanged || groupChanged) {
          scope.setState(getDefaultState(scope.currentProps))
        }
      } yield ()
    }
    .build

}
