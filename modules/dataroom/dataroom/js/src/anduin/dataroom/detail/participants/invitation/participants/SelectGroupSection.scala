// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.invitation.participants

import anduin.dataroom.DataRoomData
import anduin.dataroom.detail.participants.invitation.InvitationGroupSettings.{AddToGroups, NoGroup}
import anduin.dataroom.detail.participants.invitation.{InvitationGroupSettings, SettingSection}
import anduin.dataroom.group.DataRoomGroupData
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.file.TooltipOnTruncate
import anduin.model.common.user.UserId
import design.anduin.components.avatar.ColorGenerator
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.portal.PortalWrapper
import design.anduin.components.radio.Radio
import design.anduin.components.suggest.{MultiSuggest, Suggest}
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR
import design.anduin.components.wrapper.laminar.WrapperL
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class SelectGroupSection(
  actorUserId: UserId,
  actorRole: DataRoomRole,
  dataRoomData: DataRoomData,
  groupSettings: InvitationGroupSettings,
  onChange: InvitationGroupSettings => Callback,
  isDisabled: Boolean = false,
  renderHeader: VdomNode => VdomNode,
  showMultiGroupsWarning: Boolean = false
) {

  lazy val isInternalActor = DataRoomRoleUtils.isInternal(actorRole)

  def apply(): VdomElement = SelectGroupSection.component(this)
}

object SelectGroupSection {

  private type Props = SelectGroupSection

  private val GroupMultiSuggest = (new MultiSuggest[DataRoomGroupData])()

  private val appearanceMod = new MultiSuggest.Appearance {

    val mod: TagMod = TagMod(
      MultiSuggest.Appearance.Full.mod,
      ^.maxHeight := "150px",
      tw.overflowYAuto
    )

  }

  private def render(props: Props): VdomNode = {
    Option.when(DataRoomRoleUtils.isInternal(props.actorRole)) {
      props.renderHeader {
        val isAdmin = DataRoomRoleUtils.isAdmin(props.actorRole)
        <.div(
          tw.flex.flexCol.mt8,
          <.div(
            tw.mb8,
            Radio(
              isChecked = props.groupSettings.isNoGroup,
              onChange = props.onChange(NoGroup),
              isDisabled = props.isDisabled
            )(
              <.span(
                tw.fontMedium,
                "No group ",
                <.span(tw.textGray7, "(unassigned)")
              )
            )
          ),
          <.div(
            tw.flex.flexCol.mb8,
            TooltipR(
              renderTarget = Radio(
                isChecked = props.groupSettings.isAddToGroups,
                onChange = props.onChange(AddToGroups(Set.empty)),
                isDisabled = !isAdmin || props.isDisabled
              )(
                <.span(
                  tw.fontMedium,
                  "Add to groups"
                )
              ),
              isDisabled = isAdmin,
              renderContent = _("Only admin can invite participants to the group"),
              targetWrapper = PortalWrapper.BlockContent
            )()
          ),
          props.groupSettings match {
            case NoGroup        => EmptyVdom
            case _: AddToGroups => renderSelectGroups(props)
          },
          Option.when(props.showMultiGroupsWarning && props.groupSettings.groupIds.size > 1)(
            renderMultiGroupsWarning()
          )
        )
      }
    }
  }

  private def renderSelectGroups(props: Props) = {
    Option.when(props.isInternalActor)(
      props.groupSettings match {
        case NoGroup => EmptyVdom
        case AddToGroups(groupIds) =>
          SettingSection(
            title = "Select groups",
            isRequired = true,
            subtitleOpt = Some(
              <.div(
                tw.textGray7.textSmall,
                "The groups participants will be added to"
              )
            ),
            inlineSubtitle = false
          ) {
            val items = props.dataRoomData.groupMap.values.toSeq
              .filterNot(group => groupIds.contains(group.id) || group.isDeleted)
              .map(Suggest.Option(_))
            GroupMultiSuggest(
              value = groupIds.flatMap(props.dataRoomData.groupMap.get).map(_.name).toSeq,
              onChange = groupNames => {
                val validGroups = groupNames.flatMap(name => props.dataRoomData.groupMap.values.find(_.name == name))
                props.onChange(AddToGroups(validGroups.map(_.id).toSet))
              },
              options = Suggest.Options[DataRoomGroupData](
                items = items,
                valueToString = _.name,
                renderItem = Some { renderProps =>
                  val groupData = renderProps.item.value
                  <.div(
                    renderProps.itemTagMods,
                    tw.flex.itemsCenter.px12.py8.rounded4,
                    ^.maxWidth := 264.px,
                    <.div(
                      tw.flexNone,
                      ^.color := ColorGenerator.apply(groupData.id.idString),
                      IconR(
                        name = Icon.Glyph.Square,
                        size = Icon.Size.Custom(12)
                      )()
                    ),
                    <.div(
                      tw.ml4.itemsCenter.flexFill,
                      TooltipOnTruncate(
                        renderTarget = <.div.withRef(_)(
                          tw.truncate,
                          ^.textOverflow.ellipsis,
                          groupData.name
                        ),
                        content = groupData.name
                      )()
                    )
                  )
                }
              ),
              textBox = MultiSuggest.TextBoxProps(
                if (items.isEmpty) "" else "Type or search group",
                isDisabled = props.isDisabled
              ),
              optionSelectKeys = List(
                "Enter",
                ",",
                ";",
                "\t"
              ),
              appearance = appearanceMod
            )()
          }
      }
    )
  }

  private def renderMultiGroupsWarning() = {
    <.div(
      tw.mt12,
      WellR(
        style = Well.Style.Primary()
      )(
        WrapperL(
          <.p(
            "When groups have different permission levels, participants will receive the highest level of role and access."
          )
        )
      )
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
