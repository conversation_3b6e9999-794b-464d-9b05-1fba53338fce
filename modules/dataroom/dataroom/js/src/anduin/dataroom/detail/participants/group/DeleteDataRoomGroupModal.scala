// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.group

import design.anduin.components.button.Button
import design.anduin.components.checkbox.Checkbox
import design.anduin.components.modal.{Modal, ModalBody, ModalFooterWCancel}
import design.anduin.components.radio.Radio
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import anduin.dataroom.DataRoomData
import anduin.dataroom.DataRoomData.UserChanges
import anduin.dataroom.detail.participants.group.DeleteDataRoomGroupModal.RenderTargetProps
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.group.{DataRoomGroupData, DeleteDataRoomGroupsParams}
import anduin.dataroom.group.DeleteDataRoomGroupsParams.ParticipantSettings
import anduin.dataroom.role.DataRoomRole
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.scalajs.pluralize.Pluralize
import anduin.utils.ScalaUtils
import stargazer.model.routing.{DynamicAuthPage, Page}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.anduin.stargazer.client.utils.ZIOUtils
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR
import design.anduin.components.wrapper.laminar.WrapperL

private[participants] final case class DeleteDataRoomGroupModal(
  router: RouterCtl[Page],
  actorUserId: UserId,
  dataRoomWorkflowId: DataRoomWorkflowId,
  dataRoomData: DataRoomData,
  groupDataList: List[DataRoomGroupData],
  renderTarget: RenderTargetProps => VdomNode,
  onDelete: Callback,
  onClose: Callback = Callback.empty
) {

//  private val groupIds = groupDataList.map(_.id).toSet

  private val groupUserIds = groupDataList.flatMap(_.participants).toSet

  private val groupDescription: VdomNode = if (groupDataList.size == 1) {
    <.span(tw.fontSemiBold, groupDataList.head.name)
  } else {
    s"these ${groupDataList.size} groups"
  }

  private val groupDisplay = Pluralize("group", groupDataList.size)

  private val hasInvitationLinks = groupDataList.exists(_.linkCount > 0)

  def apply(): VdomElement = DeleteDataRoomGroupModal.component(this)
}

private[participants] object DeleteDataRoomGroupModal {

  type RenderTargetProps = (openToggle: Callback, disabledReasonOpt: Option[String])

  private type Props = DeleteDataRoomGroupModal

  private final case class State(
    participantSettings: Option[DeleteDataRoomGroupsParams.ParticipantSettings],
    isBusy: Boolean
  )

  private class Backend(scope: BackendScope[Props, State]) {

    private def hasOnlyOneAdminIfDeleteGroup(props: Props) = props.dataRoomData.getAdminCount(
      props.groupUserIds.map(_ -> UserChanges(removedGroups = props.groupDataList.map(_.id).toSet)).toMap
    ) < 1

    private def hasOnlyOneAdminIfRemoveFromDataRoom(props: Props) = props.dataRoomData.getAdminCount(
      props.groupUserIds
        .map(_ -> UserChanges(newRoleOpt = Some(DataRoomRole.Empty), removedGroups = props.dataRoomData.groupMap.keySet))
        .toMap
    ) < 1

    private def hasNonEmptyGroupWhenBatchDelete(props: Props) = (props.groupDataList.size > 1 && props.groupDataList
      .exists(group => group.participants.nonEmpty || group.linkCount > 0))

    def render(props: Props, state: State): VdomNode = {
      val disabledReason = for {
        _ <- Either.cond(
          !hasNonEmptyGroupWhenBatchDelete(props),
          (),
          "To batch delete, all selected groups must have no participants and no invitation links. You can delete groups with participants or links individually."
        )
        _ <- Either.cond(
          !hasOnlyOneAdminIfDeleteGroup(props),
          (),
          "There must be at least one admin in the data room after deleting groups."
        )
      } yield ()
      Modal(
        title = s"Delete ${Pluralize(
            "group",
            props.groupDataList.size,
            inclusive = props.groupDataList.size > 1
          )}",
        renderTarget = openToggle => props.renderTarget((openToggle, disabledReason.left.toOption)),
        renderContent = renderContent(props, state),
        size = Modal.Size(Modal.Width.Px480),
        afterUserClose = props.onClose
      )()
    }

    private def isDeleteDisabled(props: Props, state: State) = {
      props.groupUserIds.nonEmpty && state.participantSettings.isEmpty
    }

    private def renderContent(props: Props, state: State)(closeToggle: Callback) = {
      React.Fragment(
        ModalBody()(
          if (props.groupUserIds.isEmpty) {
            renderEmptyUsers(props)
          } else {
            renderNonEmptyUsers(props, state)
          }
        ),
        ModalFooterWCancel(cancel = closeToggle >> props.onClose)(
          Button(
            style = Button.Style.Full(color = Button.Color.Danger, isBusy = state.isBusy),
            isDisabled = isDeleteDisabled(props, state),
            onClick = deleteGroups(closeToggle >> props.onClose)
          )("Delete")
        )
      )
    }

    private def renderGroupLinkWarning(props: Props) = {
      WellR(style = Well.Style.Warning())(
        WrapperL(
          <.p(
            "Invitation links tied exclusively to ",
            props.groupDescription,
            " will be turned off. Links tied to this group along with other groups will remain active. You can update this in ",
            Button(
              style = Button.Style.Text(),
              tpe = Button.Tpe.Link(
                props.router.urlFor(DynamicAuthPage.DataRoomLinkDashboardPage(props.dataRoomWorkflowId)).value,
                Button.Target.Blank
              )
            )("Settings"),
            "."
          )
        )
      )
    }

    private def renderEmptyUsers(props: Props) = {
      if (props.hasInvitationLinks) {
        renderGroupLinkWarning(props)
      } else {
        <.div(
          s"Are you sure you want to delete ",
          props.groupDescription,
          "?"
        )
      }
    }

    private def renderNonEmptyUsers(props: Props, state: State) = {

      <.div(
        tw.flex.flexCol,
        <.p(
          tw.text13.leading20.textGray8,
          "You’re about to delete ",
          props.groupDescription,
          ". What should happen to participants?"
        ),
        <.div(
          ComponentUtils.testId(DeleteDataRoomGroupModal, "KeepUser"),
          tw.mt16,
          TooltipR(
            renderTarget = Radio(
              isChecked = state.participantSettings.exists(ScalaUtils.isMatch[ParticipantSettings.Keep.type]),
              onChange = scope.modState(_.copy(participantSettings = Some(ParticipantSettings.Keep))),
              isDisabled = hasOnlyOneAdminIfDeleteGroup(props)
            )(
              <.div(
                tw.flexCol.itemsCenter,
                <.div(
                  tw.fontSemiBold,
                  "Keep participants in the data room"
                ),
                <.div(
                  tw.textGray7,
                  "Participants will retain access through other groups, or keep their current role and permissions if they have no other groups."
                )
              )
            ),
            renderContent = _("There must be at least one admin"),
            isDisabled = hasOnlyOneAdminIfDeleteGroup(props)
          )()
        ),
        <.div(
          ComponentUtils.testId(DeleteDataRoomGroupModal, "RemoveUser"),
          tw.mt12,
          TooltipR(
            renderTarget = Radio(
              isChecked = state.participantSettings.exists(ScalaUtils.isMatch[ParticipantSettings.Remove]),
              onChange = scope.modState(_.copy(participantSettings = Some(ParticipantSettings.Remove(true)))),
              isDisabled = hasOnlyOneAdminIfRemoveFromDataRoom(props)
            )(
              <.div(
                tw.flexCol,
                <.div(
                  tw.fontSemiBold,
                  "Remove participants from the data room"
                ),
                Option.when(state.participantSettings.exists(ScalaUtils.isMatch[ParticipantSettings.Remove]))(
                  <.div(
                    ComponentUtils.testId(DeleteDataRoomGroupModal, "NotifyUser"),
                    tw.mt16,
                    Checkbox(
                      isChecked = state.participantSettings match {
                        case Some(ParticipantSettings.Remove(false)) => true
                        case _                                       => false
                      },
                      onChange =
                        value => scope.modState(_.copy(participantSettings = Some(ParticipantSettings.Remove(!value))))
                    )("Don't notify them by email")
                  )
                )
              )
            ),
            renderContent = _("There must be at least one admin"),
            isDisabled = !hasOnlyOneAdminIfRemoveFromDataRoom(props)
          )()
        ),
        Option.when(props.hasInvitationLinks)(<.div(tw.mt16, renderGroupLinkWarning(props)))
      )
    }

    private def deleteGroups(onClose: Callback) = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- Callback.unless(isDeleteDisabled(props, state)) {
          scope.modState(
            _.copy(isBusy = true),
            ZIOUtils.toReactCallback {
              DataRoomEndpointClient
                .deleteGroups(
                  DeleteDataRoomGroupsParams(
                    dataRoomWorkflowId = props.dataRoomWorkflowId,
                    groupIds = props.groupDataList.map(_.id).toSet,
                    participantSettings = state.participantSettings.getOrElse(ParticipantSettings.Keep)
                  )
                )
                .map(resp =>
                  scope.modState(
                    _.copy(isBusy = false),
                    resp.fold(
                      _ => Toast.errorCallback(s"Failed to delete ${props.groupDisplay}") >> onClose,
                      _ =>
                        Toast.successCallback(
                          s"${props.groupDisplay.capitalize} deleted successfully"
                        ) >> props.onDelete >> onClose
                    )
                  )
                )
            }
          )
        }
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(
      State(
        participantSettings = None,
        isBusy = false
      )
    )
    .renderBackend[Backend]
    .build

}
