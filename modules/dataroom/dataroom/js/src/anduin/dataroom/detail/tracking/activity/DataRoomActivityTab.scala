// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.tracking.activity

import java.time.Instant
import scala.scalajs.js.Date

import design.anduin.components.button.Button
import design.anduin.components.portal.PortalWrapper
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.table.Table
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import anduin.component.util.JavaScriptUtils
import anduin.dataroom.DataRoomData.LatestState
import anduin.dataroom.detail.tracking.activity.ExportActivityMenu.ExportOption
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.notification.{DataRoomNotificationUtils, NotificationMode}
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId
import anduin.model.id.FileId
import anduin.stargazer.service.dataroom.ExportDataRoomActivitiesParams
import anduin.utils.StateSnapshotWithModFn

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.dataroom.DataRoomData
import anduin.dataroom.activity.*
import anduin.dataroom.role.*
import anduin.dataroom.tracking.{DataRoomActivityType, QueryUserRange}
import anduin.dms.tracking.QueryTimeRange
import com.anduin.stargazer.client.utils.ZIOUtils

final case class DataRoomActivityTab(
  dataRoomData: DataRoomData,
  queryTimeRange: StateSnapshotWithModFn[QueryTimeRange],
  activityType: StateSnapshotWithModFn[DataRoomActivityType],
  queryUserRange: StateSnapshotWithModFn[(QueryUserRange, Option[UserId])],
  currentPage: StateSnapshotWithModFn[Int]
) {
  def apply(): VdomElement = DataRoomActivityTab.component(this)
}

object DataRoomActivityTab {
  private type Props = DataRoomActivityTab

  private final case class State(
    isExporting: Boolean
  )

  final case class DataRoomActivityTableRow(
    id: String,
    timestamp: Option[Instant],
    actorName: String,
    actorEmail: String,
    activityDescription: VdomNode,
    actorIp: String,
    actorAvatarInitials: String,
    anduinAvatar: Boolean
  ) extends DataRoomActivityLogTable.RowBase

  private val DataRoomActivityTable = (new DataRoomActivityLogTable[DataRoomActivityTableRow])()

  private final class Backend(scope: BackendScope[Props, State]) {

    private def getNameString(latestState: LatestState, fallbackName: String)(userIdOpt: Option[UserId]) = {
      userIdOpt.flatMap(latestState.getUserInfo).fold(fallbackName)(_.getDisplayName)
    }

    private def getFallbackName(activity: DataRoomActivity) = {
      activity match {
        case a: RequestAccessDataRoomActivity => a.email
        case _                                => ""
      }
    }

    private def getEmailString(latestState: LatestState)(userIdOpt: Option[UserId]) = {
      userIdOpt.flatMap(latestState.getUserInfo).fold("")(_.emailAddress)
    }

    private def getAvatarInitials(latestState: LatestState)(userIdOpt: Option[UserId]) = {
      userIdOpt.flatMap(latestState.getUserInfo).fold("")(_.avatarInitials)
    }

    private def getParticipantNames(latestState: LatestState)(userIds: Seq[UserId]): VdomNode = {
      val getName: Option[UserId] => String = getNameString(latestState, "deleted user")
      userIds match {
        case firstUser :: Nil =>
          semiBoldText(getName(Some(firstUser)))
        case firstUser :: secondUser :: Nil =>
          <.span(
            semiBoldText(getName(Some(firstUser))),
            " and ",
            semiBoldText(getName(Some(secondUser)))
          )
        case firstUser :: restUsers =>
          val firstName = getName(Some(firstUser))
          val restNames = restUsers.map(userId => getName(Some(userId)))
          <.span(
            semiBoldText(firstName),
            " and ",
            TooltipR(
              targetWrapper = PortalWrapper.Inline,
              renderTarget = Button(style = Button.Style.Text())(s"${restNames.size} others"),
              renderContent = _(
                tw.textLeft,
                restNames.map(name => <.div(name)).toVdomArray
              )
            )()
          )
        case _ => EmptyVdom
      }
    }

    private def getParticipantUserIds(activity: DataRoomActivity) = activity match {
      case a: InviteUsersToDataRoomActivity                    => a.individualRoles.keys.toList
      case a: CancelInvitationToDataRoomActivity               => a.userIds.toList
      case a: ModifyDataRoomPermissionsActivity                => a.updatedRole.keys.toList
      case a: RemindInvitationToDataRoomActivity               => a.userIds.toList
      case a: RemoveUsersFromDataRoomActivity                  => a.userIds.toList
      case a: SetDataRoomTermsOfAccessWhitelistedUsersActivity => a.whitelistedUsers.toList
      case a: AddUsersToGroupActivity                          => a.userIds.toList
      case a: RemoveUsersFromGroupActivity                     => a.userIds.toList
      case a: AddPortalUserToDataRoomActivity                  => a.actor.toList
      case _                                                   => List()
    }

    private def semiBoldText(text: String) = {
      <.span(tw.fontSemiBold, text)
    }

    private def getSettingTag(value: Boolean) =
      semiBoldText(if (value) "ON" else "OFF")

    private def getArchiveAction(isArchived: Boolean) =
      if (isArchived) "Archived" else "Unarchived"

    private def getFileName(fileId: FileId, fileMap: Map[FileId, String]): String = {
      fileMap.getOrElse(fileId, "deleted file")
    }

    private def getGroupName(props: Props, groupId: DataRoomGroupId) = {
      props.dataRoomData.getGroupData(groupId, includeDeleted = true).map(_.name).getOrElse("Unknown group")
    }

    private def getActivityDescription(
      props: Props,
      activity: DataRoomActivity,
      latestState: LatestState,
      fileMap: Map[FileId, String]
    ): VdomNode = {
      val participantNames = getParticipantNames(latestState)(getParticipantUserIds(activity))

      activity match {
        case a: CreateDataRoomActivity =>
          <.span("Created this data room ", semiBoldText(a.name))
        case a: RenameDataRoomActivity =>
          <.span("Renamed this data room ", semiBoldText(a.name))
        case a: SetIsArchivedDataRoomActivity =>
          <.span(s"${getArchiveAction(a.isArchived)} this data room")
        case a: InviteUsersToDataRoomActivity =>
          // all roles are the same in batch invitation
          val role = a.individualRoles.head._2
          <.span(
            "Invited ",
            participantNames,
            " to join this data room as ",
            semiBoldText(DataRoomRoleUtils.getName(role))
          )
        case _: AddPortalUserToDataRoomActivity =>
          val actorName = getNameString(latestState, "")(activity.actor)
          <.span(
            "Invited ",
            semiBoldText(actorName),
            " to join this data room as ",
            semiBoldText("Anduin Support")
          )
        case _: AcceptInvitationToDataRoomActivity =>
          <.span(s"Joined this data room via invitation email")
        case _: DeclineInvitationToDataRoomActivity =>
          <.span(s"Declined an invitation to join this data room")
        case _: CancelInvitationToDataRoomActivity =>
          <.span(
            "Cancelled ",
            participantNames,
            "'s invitation to join this data room"
          )
        case a: ModifyDataRoomPermissionsActivity =>
          val updatedRoles = a.updatedRole.groupBy(_._2).transform((_, roleMap) => roleMap.keySet.toList).toList
          val updateDescription = updatedRoles.zipWithIndex
            .map { case ((role, userIds), idx) =>
              val participants = getParticipantNames(latestState)(userIds)
              val roleName = DataRoomRoleUtils.getName(role)
              <.span(
                TagMod.unless(idx == 0)("; "),
                participants,
                s"'s role to $roleName"
              )
            }
          <.span("Changed ", updateDescription.toVdomArray)
        case _: RemindInvitationToDataRoomActivity =>
          <.span(
            "Reminded ",
            participantNames,
            " to join this data room"
          )
        case _: RemoveUsersFromDataRoomActivity =>
          <.span(
            "Removed ",
            participantNames,
            " from this data room"
          )
        case a: UpdateTermsOfAccessToDataRoomActivity =>
          a.toaFileIdOpt.fold {
            <.span("Disabled the NDA for this data room")
          } { fileId =>
            <.span(
              "Set ",
              semiBoldText(getFileName(fileId, fileMap)),
              " as the new NDA"
            )
          }
        case _: UpdateDataRoomWatermarkActivity =>
          <.span(s"Updated this data room's watermark")
        case a: AcceptTermsOfAccessToDataRoomActivity =>
          <.span(
            "Accepted this data room's NDA (",
            semiBoldText(getFileName(a.toaFileId, fileMap)),
            ")"
          )
        case a: SetLinkInvitationActivity =>
          val action = a.isCreateOpt.fold("Set") { isCreate =>
            if (isCreate) "Created" else "Updated"
          }
          <.span(
            s"$action an invitation link ",
            semiBoldText(a.linkName)
          )
        case a: JoinViaLinkInvitationActivity =>
          <.span(
            "Joined this data room via the link ",
            semiBoldText(a.linkName)
          )
        case a: DeleteLinkInvitationActivity =>
          <.span(
            "Removed the invitation link ",
            semiBoldText(a.linkName)
          )
        case _: ChangeSingleDataRoomBillingPlanActivity =>
          EmptyVdom
        case a: ChangeShowingIndexSettingActivity =>
          <.span(
            "Turned this data room's file index ",
            getSettingTag(a.showIndex)
          )
        case a: ChangeShowingSearchSettingActivity =>
          <.span(
            "Turned this data room's search ",
            getSettingTag(a.showSearch)
          )
        case a: ChangeShowingHomePageSettingActivity =>
          <.span(
            "Turned this data room's landing page ",
            getSettingTag(a.showHomePage)
          )
        case a: ChangeShowingWhiteLabelSettingActivity =>
          <.span(
            s"Turned this data room's white labelling ",
            getSettingTag(a.showWhiteLabel)
          )
        case _: SetDataRoomTermsOfAccessWhitelistedUsersActivity =>
          <.span(
            "Updated NDA whitelist to ",
            participantNames
          )
        case _: RequestAccessDataRoomActivity =>
          <.span(
            "Requested access to this data room"
          )
        case a: ApproveRequestAccessDataRoomActivity =>
          <.span(
            "Approved access request from ",
            semiBoldText(a.email),
            ". An invitation email was sent."
          )
        case a: DeclineRequestAccessDataRoomActivity =>
          <.span(
            "Declined access request from ",
            semiBoldText(a.email)
          )
        case a: DuplicateDataRoomActivity =>
          <.span(
            "Duplicated this data room, naming it ",
            semiBoldText(a.toDataRoomName)
          )
        case _: UpdateDataRoomEmailConfigsActivity =>
          EmptyVdom
        case _: UpdateDataRoomWatermarkExceptionFilesActivity =>
          EmptyVdom
        case a: UpdateNewFileNotificationConfigActivity =>
          <.span(
            "New file notifications set to ",
            semiBoldText(a.newFileNotificationConfigOpt.fold("") { notificationConfig =>
              val notificationMode =
                DataRoomNotificationUtils.getNotificationModeName(notificationConfig.notificationMode)
              val notificationFrequency = if (notificationConfig.notificationMode != NotificationMode.DontNotify) {
                s", ${notificationConfig.notificationFrequency.name}"
              } else {
                ""
              }
              s"${notificationMode}${notificationFrequency}"
            })
          )
        case _: SetDataRoomPointsOfContactActivity =>
          EmptyVdom
        case a: CreateGroupActivity =>
          <.span(
            "Created ",
            semiBoldText(a.name),
            " group"
          )
        case a: RenameGroupActivity =>
          <.span(
            "Renamed ",
            semiBoldText(a.oldName),
            " group to ",
            semiBoldText(a.newName)
          )
        case a: UpdateGroupPermissionsActivity =>
          <.span(
            "Changed the settings of ",
            semiBoldText(getGroupName(props, a.groupId)),
            " group"
          )
        case a: DeleteGroupActivity =>
          <.span(
            "Deleted ",
            semiBoldText(getGroupName(props, a.groupId)),
            " group"
          )
        case a: AddUsersToGroupActivity =>
          <.span(
            "Added ",
            participantNames,
            " to ",
            semiBoldText(getGroupName(props, a.groupId)),
            " group"
          )
        case a: RemoveUsersFromGroupActivity =>
          <.span(
            "Removed ",
            participantNames,
            " from ",
            semiBoldText(getGroupName(props, a.groupId)),
            " group"
          )
        case _: ChangeEnableWebhookActivity => EmptyVdom
        case _: BindToEnvironmentActivity   => EmptyVdom
      }
    }

    private def convertToActivityTableRows(
      props: Props,
      activities: Seq[DataRoomActivity],
      latestState: LatestState,
      fileMap: Map[FileId, String]
    ): Seq[DataRoomActivityTableRow] = {
      activities.zipWithIndex
        .map { case (activity, index) =>
          val fallbackName = getFallbackName(activity)
          val actorName = activity match {
            case _: AddPortalUserToDataRoomActivity => "Anduin Support"
            case _                                  => getNameString(latestState, fallbackName)(activity.actor)
          }
          val actorEmail = activity match {
            case _: AddPortalUserToDataRoomActivity => ""
            case _                                  => getEmailString(latestState)(activity.actor)
          }
          val avatarInitials = getAvatarInitials(latestState)(activity.actor)
          val isAnduinAvatar = activity match {
            case _: AddPortalUserToDataRoomActivity => true
            case _                                  => false
          }
          DataRoomActivityTableRow(
            s"dataroom-activity-$index",
            activity.timestamp,
            actorName,
            actorEmail,
            getActivityDescription(
              props,
              activity,
              latestState,
              fileMap
            ),
            activity.actorIp.getOrElse(""),
            if (avatarInitials.nonEmpty) avatarInitials else actorName,
            isAnduinAvatar
          )
        }
    }

    private def renderActivityTypeDropdown(props: Props) = {
      <.div(
        ^.minWidth := "168px",
        DataRoomActivityTypeDropdown(
          activityType = props.activityType,
          labelOpt = Some("Activity")
        )()
      )
    }

    private def renderUserDropdown(props: Props) = {
      <.div(
        tw.ml32,
        ^.minWidth := "160px",
        DataRoomUserDropdown(
          queryUserRange = props.queryUserRange,
          dataRoomData = props.dataRoomData,
          labelOpt = Some("Participants")
        )()
      )
    }

    private def renderExportMenu(props: Props, state: State)(isEmptyActivity: Boolean) = {
      val isFilterAll =
        props.queryTimeRange.value == QueryTimeRange.AllTime &&
          props.queryUserRange.value._1 == QueryUserRange.AllUsers &&
          props.activityType.value == DataRoomActivityType.AllActivities

      <.div(
        tw.flexNone,
        ExportActivityMenu(
          exportOptions = Seq(
            ExportOption.Filtered(onExport(true), isDisabled = isFilterAll || isEmptyActivity),
            ExportOption.All(onExport(false), isDisabled = false)
          ),
          isDisabled = state.isExporting
        )()
      )
    }

    private def onExport(isExportFiltered: Boolean): Callback = {
      for {
        props <- scope.props
        _ <- scope.modState(
          _.copy(isExporting = true),
          Toast.loadingCallback("Exporting...")
        )
        _ <- ZIOUtils.toReactCallback {
          val queryTimeRange = if (isExportFiltered) props.queryTimeRange.value else QueryTimeRange.AllTime
          val activityType = if (isExportFiltered) props.activityType.value else DataRoomActivityType.AllActivities
          val (queryUserRange, queryUserId) =
            if (isExportFiltered) {
              props.queryUserRange.value
            } else {
              (QueryUserRange.AllUsers, None)
            }
          for {
            res <- DataRoomEndpointClient
              .exportDataRoomActivities(
                ExportDataRoomActivitiesParams(
                  props.dataRoomData.workflowId,
                  queryTimeRange,
                  activityType,
                  queryUserRange,
                  queryUserId,
                  timezoneOffsetInMinutes =
                    -new Date().getTimezoneOffset().toInt // Offset returns UTC zone - locale zone
                )
              )
          } yield {
            scope.modState(
              _.copy(isExporting = false),
              Toast.clearCallback() >> res.fold(
                _ => Toast.errorCallback("Failed to export activity log. Please try again later."),
                res => Toast.successCallback("Exported activity log successfully") >> JavaScriptUtils.download(res.url)
              )
            )
          }
        }
      } yield ()
    }

    private def renderToolbar(props: Props, state: State)(isEmptyActivity: Boolean) = {
      <.div(
        tw.flex.itemsCenter.mb16,
        <.div(
          tw.textGray7.flex.flexFill.itemsCenter,
          renderActivityTypeDropdown(props),
          renderUserDropdown(props)
        ),
        renderExportMenu(props, state)(isEmptyActivity)
      )
    }

    private def renderActivityColumn(row: DataRoomActivityTableRow) = {
      Table.Cell(
        <.div(
          tw.fontNormal.text13.leading20,
          row.activityDescription
        )
      )
    }

    def render(props: Props, state: State): VdomNode = {
      WithDataRoomActivities(
        dataRoomWorkflowId = props.dataRoomData.workflowId,
        queryTimeRange = props.queryTimeRange.value,
        activityType = props.activityType.value,
        queryUserRange = props.queryUserRange.value
      ) { case WithDataRoomActivities.WithDataRoomActivitiesData(isLoading, resultOpt) =>
        <.div(
          tw.flex.flexCol.hPc100,
          renderToolbar(props, state)(!resultOpt.exists(_.activities.nonEmpty)),
          if (isLoading) {
            BlockIndicatorR(isFullHeight = true)()
          } else {
            val activityRows = resultOpt
              .map { dataRoomActivitiesInsight =>
                convertToActivityTableRows(
                  props,
                  dataRoomActivitiesInsight.activities,
                  props.dataRoomData.latestState,
                  dataRoomActivitiesInsight.fileMap
                )
              }
              .getOrElse(Seq())

            DataRoomActivityTable(
              rows = activityRows,
              additionalColumns = List(
                DataRoomActivityLogTable.Column(
                  head = "Activity",
                  render = renderActivityColumn,
                  width = "55%"
                )
              ),
              currentPage = props.currentPage.value,
              onPageJump = page => props.currentPage.setState(page)
            )()
          }
        )
      }
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(
      State(
        isExporting = false
      )
    )
    .renderBackend[Backend]
    .build

}
