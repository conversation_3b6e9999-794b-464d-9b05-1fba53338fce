// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.documents

import java.time.Instant
import java.time.temporal.ChronoUnit
import com.raquo.laminar.api.L.TextNode
import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.menu.react.ContextMenuR
import design.anduin.components.modal.Modal
import design.anduin.components.portal.PortalUtils
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR
import design.anduin.facades.reactvirtualized.{ReactVirtualizedAutoSizer, ReactVirtualizedList}
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*
import org.scalajs.dom.{Element, HTMLElement}
import zio.{Task, ZIO}
import anduin.dataroom.detail.documents.DataRoomDocuments.DocumentColumn
import anduin.dataroom.detail.documents.autoimport.UploadAndAutoImportFilesToFolderModal
import anduin.dataroom.detail.documents.search.DataRoomSearchPanel.SearchType
import anduin.dataroom.detail.documents.search.{DataRoomSearchBar, DataRoomSearchOnboardingWell, DataRoomSearchPanel}
import anduin.dataroom.detail.reorder.ReorderButton
import anduin.dataroom.detail.tracking.DataRoomInsightsTab.DetailView
import anduin.dataroom.detail.tracking.content.FileDetailTrackingModal
import anduin.dataroom.detail.tracking.group.GroupDetailTrackingModal
import anduin.dataroom.detail.tracking.user.UserDetailTrackingModal
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.multiregion.DataRoomMultiRegionButton
import anduin.dataroom.reactive.DataRoomReactStream
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.dataroom.share.{Column, ColumnHead}
import anduin.dataroom.{DataRoomData, DataRoomFileUtils, DataRoomPermissionCheck}
import anduin.facades.core.viselectVanilla.distSrcTypesMod.SelectionEvent
import anduin.file.*
import anduin.file.ModifiedFileFolderName.*
import anduin.file.explorer.*
import anduin.file.explorer.FolderLoader.QueryResult
import anduin.file.explorer.header.*
import anduin.file.table.*
import anduin.file.table.FileTable.Row
import anduin.file.table.FileTable.Row.FileRowKey
import anduin.layout.SelectionData
import anduin.model.common.user.UserId
import anduin.model.id.notification.NotificationSpaceId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{FileId, FolderId}
import anduin.notification.NotificationUtils
import anduin.orgbilling.model.plan.BillingPlanStatus
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.protobuf.notification.*
import anduin.service.{CommonResponse, GeneralServiceException}
import anduin.stargazer.service.dataroom.*
import com.anduin.stargazer.client.modules.notification.NotificationProvider
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.endpoints.*
import com.anduin.stargazer.service.file.BatchDownloadRequest
import stargazer.model.routing.DynamicAuthPage.DataRoomDocumentsPage.getFileIdOpt
import stargazer.model.routing.DynamicAuthPage.{
  DataRoomDocumentsPage,
  DataRoomFolderPage,
  DataRoomRecentPage,
  DataRoomTrashPage
}
import stargazer.model.routing.{DynamicAuthPage, Page}

final case class DataRoomDocuments(
  router: RouterCtl[Page],
  page: DynamicAuthPage.DataRoomDocumentsPage,
  dataRoomData: DataRoomData,
  userId: UserId,
  refetch: Callback,
  renderOnError: () => VdomNode
) {
  def apply(): VdomElement = DataRoomDocuments.component(this)

  val isRecentPage: Boolean = DataRoomDocumentsPage.isRecentPage(page)
  val isTrashPage: Boolean = DataRoomDocumentsPage.isTrashPage(page)
  val showIndex: Boolean = dataRoomData.latestState.dataRoomCreatedState.showIndex && !isRecentPage && !isTrashPage
  val dataRoomWorkflowId: DataRoomWorkflowId = page.dataRoomWorkflowId

  val showSemanticSearch: Boolean =
    dataRoomData.latestState.dataRoomCreatedState.isSemanticSearchEnabled && isActorRole(DataRoomRoleUtils.isAdmin)

  private def isActorRole(check: DataRoomRoleUtils.Check) = {
    dataRoomData.latestState.dataRoomCreatedState.individualRoles
      .get(userId)
      .exists(check)
  }

  val columns: List[DataRoomDocuments.DocumentColumn] = {
    List(
      List(DocumentColumn.Check),
      Option.when(isRecentPage)(DocumentColumn.Dot),
      Option.when(showIndex)(DocumentColumn.Index),
      List(DocumentColumn.Name),
      Option.unless(isTrashPage)(DocumentColumn.Creator),
      Option.unless(isActorRole(DataRoomRoleUtils.isExternal) || isTrashPage)(DocumentColumn.Permission),
      Option.unless(isTrashPage)(DocumentColumn.UpdatedAt),
      Option.when(isTrashPage)(DocumentColumn.DeletedBy),
      Option.when(isTrashPage)(DocumentColumn.DeletedAt),
      Option.when(isActorRole(DataRoomRoleUtils.isAdmin) && !isTrashPage)(DocumentColumn.Tracking),
      List(DocumentColumn.Action)
    ).flatten
  }

  val indexColumn: Int = columns.indexOf(DocumentColumn.Index)
  val nameColumn: Int = columns.indexOf(DocumentColumn.Name)
  val updatedTimeColumn: Int = columns.indexOf(DocumentColumn.UpdatedAt)
  val deletedTimeColumn: Int = columns.indexOf(DocumentColumn.DeletedAt)

  val folderId: FolderId = page match {
    case recentPage: DataRoomRecentPage => FileManagerLocation.getRecentFilesFolderId(recentPage.dataRoomWorkflowId)
    case trashPage: DataRoomTrashPage   => FileManagerLocation.getTrashFolderId(trashPage.dataRoomWorkflowId)
    case page: DataRoomFolderPage       => page.folderId
  }

  val defaultFolderOrder: FolderOrdering =
    if (isRecentPage) {
      FolderOrdering(
        FileUpdatedTime.ordering,
        updatedTimeColumn,
        sortIsAsc = true
      )
    } else if (isTrashPage) {
      FolderOrdering(
        FileDeletedTime.ordering,
        deletedTimeColumn,
        sortIsAsc = true
      )
    } else if (showIndex) {
      FolderOrdering(
        FileIndex.ordering,
        indexColumn,
        sortIsAsc = true
      )
    } else {
      FolderOrdering(
        FileName.ordering,
        nameColumn,
        sortIsAsc = true
      )
    }

  val rootFolderId: FolderId = folderId.copy(folderParts = folderId.folderParts.copy(tail = List()))
}

object DataRoomDocuments {

  private type Props = DataRoomDocuments

  private val defaultNavigationWidth = 320

  private final case class State(
    viewedDetail: Option[DetailView],
    viewRecentFiles: Boolean,
    folderOrderMap: Map[FileManagerLocation, FolderOrdering],
    navigationWidth: Int,
    // Search query is updated when user type in search bar and passed to search panel as initial query
    searchQuery: String,
    searchType: SearchType = SearchType.Document,
    sortedByColumn: Option[Column] = None,
    sortIsAsc: Boolean = true
  )

  sealed trait DocumentColumn extends Column derives CanEqual

  object DocumentColumn {
    object Check extends DocumentColumn
    object Index extends DocumentColumn
    object Dot extends DocumentColumn
    object Name extends DocumentColumn
    object Creator extends DocumentColumn
    object Permission extends DocumentColumn
    object UpdatedAt extends DocumentColumn

    object DeletedBy extends DocumentColumn

    object DeletedAt extends DocumentColumn

    object Tracking extends DocumentColumn
    object Action extends DocumentColumn
  }

  private class Backend(scope: BackendScope[Props, State]) {

    private val SelectableCls = "selectable"
    private val SelectableDataKey = "data-selectable-index"

    private def errorToast(action: String) = {
      Toast.errorCallback(s"Failed to $action. Please try again")
    }

    private def errorToastWithReason(action: String, reason: String) = {
      Toast.errorCallback(s"Failed to $action: $reason")
    }

    private def successToast(message: String) = {
      Toast.successCallback(message)
    }

    private def convertToCallback(
      toastOnSuccess: String,
      toastOnFailure: String
    )(
      task: Task[Either[GeneralServiceException, DataRoomEmptyResponse]]
    ) = {
      for {
        response <- task
        errorCallback = Toast.errorCallback(toastOnFailure)
        successCallback = Toast.successCallback(toastOnSuccess)
      } yield response.fold(_ => errorCallback, _ => successCallback)
    }

    private def executeAddAndRenameFolder[R](
      data: LayoutData,
      task: Task[Either[GeneralServiceException, R]],
      errorCallback: Callback,
      successCallback: Callback
    )(
      getNewState: R => ModifiedFileFolderName
    ) = {
      task.map {
        _.fold(
          _ => errorCallback,
          res =>
            data.inlineEditingData.modState(
              _.copy(modifiedRowOpt = Some(getNewState(res))),
              successCallback
            )
        )
      }
    }

    private def executeActionWithErrorMessage(
      data: LayoutData,
      task: Task[Either[GeneralServiceException, CommonResponse]],
      successCallback: Callback,
      generalErrorCallback: Callback,
      messageErrorCallback: String => Callback
    )(
      getNewState: Either[GeneralServiceException, CommonResponse] => Option[ModifiedFileFolderName]
    ) = {
      task.map { resp =>
        data.inlineEditingData.modState(
          _.copy(modifiedRowOpt = getNewState(resp)),
          resp.fold(
            _ => generalErrorCallback,
            res =>
              if (res.ok) {
                successCallback
              } else {
                messageErrorCallback(res.message)
              }
          )
        )
      }
    }

    private def getDefaultOnAddFolderOrRenameItem(
      props: Props,
      data: LayoutData
    ): ModificationOptions.OnAddFolderOrRenameItemCallback = { name =>
      data.inlineEditingData.value.modifiedRowOpt.fold(ZIO.attempt(Callback.empty)) {
        case addNewFolder: AddNewFolder =>
          executeAddAndRenameFolder(
            data = data,
            task = DataRoomEndpointClient.addFolder(
              AddFolderParams(
                addNewFolder.parentFolder.folderId,
                name,
                addNewFolder.customPermissions.map(CreationCustomPermissions.toPermissionMap)
              )
            ),
            errorCallback = errorToast("add folder"),
            successCallback = successToast("Folder added")
          ) { res =>
            val hasAccess = addNewFolder.customPermissions.fold(true) { customPermissions =>
              val permissionMap = CreationCustomPermissions.toPermissionMap(customPermissions)
              val individualPermission = permissionMap.userPermissions.get(props.userId)
              val groupPermission = props.dataRoomData
                .getGroupsOfUser(props.userId)
                .headOption
                .map(_.teamId)
                .flatMap(permissionMap.teamPermissions.get)
              List(individualPermission, groupPermission).maxBy(_.map(_.value)).nonEmpty
            }
            addNewFolder.copy(
              resultFolderIdOpt = Some(res.newFolderId),
              hasAccess = hasAccess
            )
          }
        case renameFolder: RenameFolder =>
          executeAddAndRenameFolder(
            data = data,
            task = DataRoomEndpointClient.renameFolder(RenameFolderParams(renameFolder.folderId, name)),
            errorCallback = errorToast("rename folder"),
            successCallback = successToast("Folder renamed")
          ) { _ =>
            renameFolder.copy(isDone = true)
          }
        case renameFile: RenameFile =>
          executeActionWithErrorMessage(
            data = data,
            task = DataRoomEndpointClient.renameFile(RenameFileParams(renameFile.fileId, name)),
            successCallback = successToast("File renamed"),
            generalErrorCallback = errorToast("rename file"),
            messageErrorCallback = reason => errorToastWithReason("rename file", reason)
          ) {
            _.fold(
              _ => None,
              res =>
                if (res.ok) {
                  Some(renameFile.copy(isDone = true))
                } else {
                  None
                }
            )
          }
      }
    }

    private val defaultCreateShortcut: ModificationOptions.OnCreateShortcutTask = { data =>
      val error = "Failed to create link. Please try again"
      val success = "Link added"
      convertToCallback(
        success,
        error
      ) {
        DataRoomEndpointClient.createShortcut {
          CreateShortcutParams(
            data.folderId,
            data.url,
            data.name
          )
        }
      }
    }

    private def isActorRole(props: Props)(check: DataRoomRoleUtils.Check) = {
      props.dataRoomData.latestState.dataRoomCreatedState.individualRoles
        .get(props.userId)
        .exists(check)
    }

    private def getManagePermissionTreePageUrl(
      router: RouterCtl[Page],
      workflowId: DataRoomWorkflowId
    )(
      userId: UserId
    ) = {
      Some(
        router
          .urlFor(DynamicAuthPage.DataRoomAllParticipantsPage(workflowId, Some(userId)))
          .value
      )
    }

    private def getPermissionColumn(props: Props, row: Row) = {
      FilePermissionColumn(
        row = row,
        dataRoomData = props.dataRoomData,
        actorUserId = props.userId,
        onSave = Option(DataRoomFileAction.modifyPermission(props.dataRoomData, props.refetch)),
        managePermissionTreePageUrl = getManagePermissionTreePageUrl(props.router, props.dataRoomData.workflowId)
      )()
    }

    private def getTrackingColumn(props: Props, data: LayoutData, row: Row) = {
      FileTrackingColumn(
        customLayoutData = data,
        row = row,
        dataRoomData = props.dataRoomData,
        onViewUserDetail = onViewUserDetail
      )()
    }

    private def getDownloadUrl(props: Props)(request: BatchDownloadRequest) = {
      DataRoomEndpointClient
        .getDownloadUrl(
          GetDownloadUrlParams(
            request,
            forceGetWatermark = false,
            includeDeleted = props.isTrashPage
          )
        )
    }

    private def getActionColumn(props: Props, data: LayoutData, row: Row) = {
      DataRoomFileActionColumn(
        router = props.router,
        actorUserId = props.userId,
        dataRoomData = props.dataRoomData,
        data = data,
        row = row,
        isTrashPage = props.isTrashPage,
        refetch = props.refetch
      )()
    }

    def onSortColumn(
      columnIndex: Int,
      locationOpt: Option[FileManagerLocation]
    )(
      ordering: Ordering[FileTableItemCommon],
      isAscending: Boolean
    ): Callback = {
      Callback.traverseOption(locationOpt) { location =>
        scope.modState(state =>
          state.copy(
            folderOrderMap = state.folderOrderMap.updated(
              location,
              FolderOrdering(
                ordering = if (isAscending) ordering else ordering.reverse,
                sortColumn = columnIndex,
                sortIsAsc = isAscending
              )
            )
          )
        )
      }
    }

    private def getCheckColumn(data: LayoutData, row: Row) = {
      val key = Row.getRowKey(row)
      FileCheck(
        isSelected = data.selectionData.value.selectedItems.contains(key),
        onSelect = isChecked => data.selectionData.modState(_.toggle(key, Some(isChecked))),
        isDisabledOpt = None
      )()
    }

    private def getIndexColumn(props: Props, data: LayoutData, row: Row) = {
      FileIndex(
        item = row.item,
        data = data,
        showIndex = props.dataRoomData.latestState.dataRoomCreatedState.showIndex
      )()
    }

    private def getDotColumn(row: Row, newlyUploadedFileIds: Set[FileId]) = {
      val shouldShowDot = Row.getRowKey(row) match {
        case file: FileRowKey => newlyUploadedFileIds.contains(file.fileId)
        case _                => false
      }
      if (shouldShowDot) {
        <.div(
          tw.roundedFull.bgPrimary3,
          ^.height := "6px",
          ^.width := "6px"
        )
      } else {
        EmptyVdom
      }
    }

    private def getNameColumn(props: Props, data: LayoutData, row: Row, newlyUploadedFileIds: Set[FileId]) = {
      FileName(
        row = row,
        inlineEditingData = data.inlineEditingData,
        onClickLocation = { location =>
          location.folderIdOpt
            .map { folderId =>
              props.router
                .urlFor(
                  DynamicAuthPage.DataRoomFolderPage(
                    props.dataRoomWorkflowId,
                    folderId,
                    None
                  )
                )
                .value
            }
            .toLeft(Callback.empty)
        },
        onAddFolderOrRenameItem = getDefaultOnAddFolderOrRenameItem(props, data),
        renderOnAddNewFolder = {
          data.queryResultOpt
            .map(_.data)
            .collect { case folderInfo: FolderInfo =>
              folderInfo
            }
            .map { folderInfo =>
              DataRoomPermissionSelectCard(
                userId = props.userId,
                dataRoomData = props.dataRoomData,
                parentFolder = folderInfo,
                customPermissions = data.inlineEditingData.withReuse.zoomState(InlineEditingData.zoomToCustomPermissions),
                isFolder = true,
                isDisabled = false
              )()
            }
        },
        renderFile = { (file, renderTarget) =>
          DataRoomFileViewer(
            router = props.router,
            dataRoomWorkflowId = props.dataRoomWorkflowId,
            file = file,
            renderTarget = { file => _ =>
              renderTarget(
                Left(
                  props.router
                    .urlFor(
                      props.page match {
                        case page: DataRoomRecentPage => page.copy(fileIdOpt = Some(file.itemId))
                        case page: DataRoomTrashPage  => page.copy(fileIdOpt = Some(file.itemId))
                        case page: DataRoomFolderPage => page.copy(fileIdSuffix = Some(file.itemId.filePart))
                      }
                    )
                    .value
                )
              )
            },
            modalType = DataRoomFileViewer.controlled(props.router, props.page),
            includeDeleted = props.isTrashPage
          )()
        },
        useParentFolderNameAsFileSubtitle = props.isRecentPage || props.isTrashPage,
        routerOpt = Some(props.router),
        isNewlyUploadedFile = fileId => !props.isTrashPage && newlyUploadedFileIds.contains(fileId)
      )()
    }

    private def getFromColumn(row: Row) = {
      FileFrom(item = row.item)()
    }

    private def getTimeColumn(row: Row) = {
      FileUpdatedTime(item = row.item)()
    }

    private def getMoveCopyButton(props: Props, data: LayoutData) = {
      DataRoomFileMoveCopyButton.getAction(
        router = props.router,
        actorUserId = props.userId,
        dataRoomData = props.dataRoomData,
        data = data,
        includeDeleted = props.isTrashPage
      )
    }

    private def getFileUploadProps(props: Props): ModificationOptions.OnUploadFileRender = { config =>
      DataRoomFileUploadModal(
        userId = props.userId,
        dataRoomData = props.dataRoomData,
        uploadConfig = config
      )()
    }

    private def getUploadButton(props: Props, data: LayoutData, planCheck: Either[String, Unit]) = {
      FileUploadMenu.getAction(
        data = data,
        renderFileUpload = getFileUploadProps(props),
        externalDisabledReason = planCheck
      )
    }

    private def renderBatchImportFilesToFolders(props: Props, data: LayoutData) = {
      val folderInfoOpt =
        FileHeaderActions.getFolderInfoOpt(data.queryResultOpt, Some(FileFolderPermission.Write))
      val isActorAdmin =
        props.dataRoomData.latestState.dataRoomCreatedState.individualRoles
          .get(props.userId)
          .exists(DataRoomRoleUtils.isAdmin)
      folderInfoOpt
        .filter(_ => isActorAdmin)
        .fold(EmptyVdom) { folderInfo =>
          Modal(
            size = Modal.Size(Modal.Width.Px600),
            renderTarget = open =>
              TooltipR(
                renderTarget = Button(
                  onClick = open,
                  style = Button.Style.Full(
                    icon = Some(Icon.Glyph.FolderReceiveLine)
                  )
                )("Upload to multiple folders"),
                renderContent = _("Upload files and have them moved to specific folders automatically")
              )(),
            renderContent = close =>
              UploadAndAutoImportFilesToFolderModal(
                userId = props.userId,
                dataRoomData = props.dataRoomData,
                folderInfo = folderInfo,
                closeModal = close
              )(),
            isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false)),
            title = "Upload to multiple folders"
          )()

        }
    }

    private def getAddFolderButton(data: LayoutData, planCheck: Either[String, Unit]) = {
      hideOnSelection(data) {
        CreateMenu.getAction(
          data,
          defaultCreateShortcut,
          planCheck
        )
      }
    }

    private def getDownloadButton(props: Props, data: LayoutData) = {
      FileDownloadButton.getAction(
        data = data,
        getDownloadUrl = getDownloadUrl(props),
        shouldAlwaysShow = isActorRole(props)(DataRoomRoleUtils.isRestricted),
        renderViewPermission = Option {
          Button(
            style = Button.Style.Text(),
            tpe = Button.Tpe.Link(
              href = props.router
                .urlFor(
                  DynamicAuthPage.DataRoomAllParticipantsPage(
                    dataRoomWorkflowId = props.dataRoomWorkflowId,
                    editUserPermissions = Some(props.userId)
                  )
                )
                .value,
              target = Button.Target.Blank
            )
          )("View my permission")
        },
        onDownloadFail = DataRoomFileUtils.catchGetViewUrlException,
        onSuccess = resp => {
          val drGetDownloadUrlResp = resp.getData[DataRoomGetDownloadUrlMetadata]
          Callback.traverseOption(drGetDownloadUrlResp)(DataRoomFileUtils.catchGetDownloadUrlExceptionDueToWatermark)
        }
      )
    }

    private def getRenameButton(data: LayoutData, planCheck: Either[String, Unit]) = {
      FileRenameButton.getAction(data, planCheck)
    }

    private def getRemoveButton(data: LayoutData, planCheck: Either[String, Unit]) = {
      FileRemoveButton.getAction(
        data,
        DataRoomFileAction.deleteFilesAndFolders(isPermanent = false),
        planCheck
      )
    }

    private def getRemoveFromTrashButton(
      data: LayoutData,
      planCheck: Either[String, Unit]
    ) = {
      FileRemoveFromTrashButton.getAction(
        data,
        DataRoomFileAction.deleteFilesAndFolders(isPermanent = true),
        planCheck
      )
    }

    private def getRestoreButton(
      dataRoomWorkflowId: DataRoomWorkflowId,
      data: LayoutData,
      planCheck: Either[String, Unit]
    ) = {
      FileRestoreButton.getAction(
        data,
        DataRoomFileAction.restoreFiles(dataRoomWorkflowId),
        planCheck
      )
    }

    private def getReorderButton(props: Props, data: LayoutData) = {
      hideOnSelection(data) {
        props.page match {
          case page: DataRoomFolderPage =>
            ReorderButton.getAction(
              dataRoomData = props.dataRoomData,
              data = data,
              folderId = page.folderId,
              userId = props.userId
            )
          case _ => EmptyVdom
        }
      }
    }

    private def getPermissionButton(props: Props, data: LayoutData) = {
      hideOnSelection(data) {
        DataRoomViewPermissionButton.getAction(
          router = props.router,
          actorUserId = props.userId,
          dataRoomData = props.dataRoomData,
          data = data
        )
      }
    }

    private def hideOnSelection(data: LayoutData)(node: VdomNode): Option[VdomNode] = {
      if (data.selectionData.value.isEmpty) {
        Some(node)
      } else {
        None
      }
    }

    private def getHeaderActions(props: Props)(data: LayoutData): List[VdomNode] = {
      val planCheck = DataRoomPermissionCheck.expiredPlan(props.dataRoomData.dataRoomPlan)
      getFileExplorerTab(props.page).fold {
        List[VdomNode](
          getUploadButton(
            props,
            data,
            planCheck
          ),
          renderBatchImportFilesToFolders(
            props,
            data
          ),
          getAddFolderButton(data, planCheck),
          getDownloadButton(props, data),
          getRenameButton(data, planCheck),
          getRemoveButton(data, planCheck),
          getMoveCopyButton(props, data),
          getReorderButton(props, data),
          getPermissionButton(props, data)
        )
      } {
        case FileExplorer.Tab.RecentFiles => List.empty[VdomNode]
        case FileExplorer.Tab.Trash =>
          List[VdomNode](
            getDownloadButton(props, data),
            getRestoreButton(
              props.dataRoomWorkflowId,
              data,
              planCheck
            ),
            getMoveCopyButton(props, data),
            getRemoveFromTrashButton(data, planCheck)
          )
      }
    }

    private def onCloseModal = {
      scope.modState(_.copy(viewedDetail = None))
    }

    private def onViewUserDetail(userId: UserId) = {
      scope.modState(_.copy(viewedDetail = Some(DetailView.User(userId))))
    }

    private def onViewFileDetail(fileId: FileId, filename: String) = {
      scope.modState(_.copy(viewedDetail = Some(DetailView.File(fileId, filename))))
    }

    private def renderUserDetailView(props: Props, state: State) = {
      state.viewedDetail.map {
        case DetailView.User(userId) =>
          UserDetailTrackingModal(
            userId,
            props.dataRoomData,
            onViewFileDetail,
            onCloseModal
          )()
        case DetailView.Group(groupId) =>
          GroupDetailTrackingModal(
            groupId,
            props.dataRoomData,
            onViewFileDetail,
            onCloseModal
          )()
        case DetailView.File(fileId, filename) =>
          FileDetailTrackingModal(
            fileId,
            filename,
            props.dataRoomData,
            onViewUserDetail,
            onCloseModal
          )()
      }
    }

    private def renderSearchBar(props: Props, state: State) = {
      DataRoomSearchPanel(
        router = props.router,
        dataRoomWorkflowId = props.dataRoomData.workflowId,
        initialQuery = state.searchQuery,
        showSemanticSearch = false,
        searchType = state.searchType,
        renderTarget = openToggle =>
          <.div(
            tw.mb16.wPc100,
            DataRoomSearchBar(
              router = props.router,
              dataRoomWorkflowId = props.dataRoomData.workflowId,
              showSemanticSearch = false,
              placeholder = "Search data room",
              width = state.navigationWidth - 24, // horizontal padding
              onChange = _ => Callback.empty,
              onSearch = (
                query,
                searchType
              ) => scope.modState(_.copy(searchQuery = query, searchType = searchType), openToggle)
            )()
          )
      )()
    }

    private def renderOnboarding(props: Props)(queryResultOpt: Option[QueryResult]) = {
      val viewedSearchOnboarding = props.dataRoomData.latestState
        .getUserState(props.userId)
        .exists(_.viewedSearchOnboarding)
      val showSearch = props.dataRoomData.latestState.dataRoomCreatedState.showSearch
      <.div(
        tw.flexCol,
        Option.when(
          props.isActorRole(DataRoomRoleUtils.isAdmin) && !viewedSearchOnboarding && !showSearch && !props.isTrashPage
        ) {
          <.div(
            tw.mt12,
            DataRoomSearchOnboardingWell(
              router = props.router,
              dataRoomWorkflowId = props.dataRoomWorkflowId,
              userId = props.userId
            )()
          )
        },
        queryResultOpt.filter(queryResult => props.isTrashPage && queryResult.data.subItems.nonEmpty).map { _ =>
          <.div(
            tw.mt12,
            WellR(
              style = Well.Style.Primary()
            )(new TextNode("You have 30 days to restore items before they are permanently deleted"))
          )
        }
      )
    }

    private def handleContextMenuRow(
      renderContextMenuTarget: ContextMenuR.RenderTarget,
      props: Props,
      data: LayoutData,
      sortedRows: Seq[Row],
      row: Row
    ) = {
      val rowKey = Row.getRowKey(row)
      val selection = data.selectionData.value
      for {
        newRows <-
          if (selection.selectedItems.contains(rowKey)) {
            CallbackTo.pure {
              sortedRows.toList.filter(row => selection.selectedItems.contains(Row.getRowKey(row)))
            }
          } else {
            data.selectionData.setState(SelectionData.single(rowKey)).map(_ => List(row))
          }
        newRowKeys = newRows.map(Row.getRowKey)
        _ <- Callback.when(
          newRowKeys.forall(FileHeaderActions.canEditSubItem(data.queryResultOpt, FileFolderPermission.Read))
        ) {
          renderContextMenuTarget.setContent(
            DataRoomFileActionMenu(
              router = props.router,
              actorUserId = props.userId,
              dataRoomData = props.dataRoomData,
              data = data,
              rows = newRows,
              isTrashPage = props.isTrashPage,
              closeToggle = renderContextMenuTarget.onCloseMenu,
              refetch = props.refetch
            )()
          )
        }
      } yield ()
    }

    private def renderRow(
      renderContextMenuTarget: ContextMenuR.RenderTarget,
      props: Props,
      newlyUploadedFileIds: Set[FileId],
      rows: Seq[Row],
      data: LayoutData,
      renderRowProps: ReactVirtualizedList.RowRenderProps
    ) = {
      val items = rows.map(Row.getRowKey).toIndexedSeq
      val rowIndex = renderRowProps.index
      val row = rows(rowIndex)
      val isAdding = InlineEditingData.isAdding(row, data.inlineEditingData.value)
      val rowKey = FileTable.Row.getRowKey(row)
      val isAddedFolder = data.inlineEditingData.value.modifiedRowOpt.exists {
        case addNewFolder: AddNewFolder =>
          rowKey == FileTable.Row.FolderRowKey(AddNewFolder.getFolderId(addNewFolder))
        case _: RenameFolder | _: RenameFile =>
          false
      }

      <.div(
        ComponentUtils.testId(DataRoomDocuments, "Table-Row"),
        ^.key := renderRowProps.key,
        ^.style := renderRowProps.style,
        // For selection
        ^.className := SelectableCls,
        VdomAttr(SelectableDataKey) := rowKey.toString,
        tw.borderBottom.borderGray3.hover(tw.bgPrimary1.bgOpacity20),
        if (data.selectionData.value.selectedItems.contains(rowKey)) {
          tw.bgPrimary1.bgOpacity40
        } else {
          tw.hover(tw.bgPrimary1.bgOpacity20)
        },
        tw.trnsA,
        tw.flex.itemsCenter,
        renderContextMenuTarget.handleContextMenu(
          handleContextMenuRow(
            renderContextMenuTarget,
            props,
            data,
            rows,
            row
          )
        ),
        TagMod.unless(isAddedFolder) {
          TagMod(
            ^.onClickCapture ==> { e =>
              if (e.shiftKey) {
                e.preventDefaultCB >> e.stopPropagationCB >> data.selectionData.modState(_.range(items, rowKey))
              } else if (e.metaKey || e.ctrlKey) {
                e.preventDefaultCB >> e.stopPropagationCB >> data.selectionData.modState(_.toggle(rowKey))
              } else {
                Callback.empty
              }
            },
            ^.onClick --> data.selectionData.setState(SelectionData.single(rowKey)),
            ^.onDoubleClick ==>? { e =>
              Option.unless(e.shiftKey || e.metaKey || e.ctrlKey) {
                data.selectionData.setState(SelectionData.single(rowKey), props.router.set(goToLocation(props, rowKey)))
              }
            }
          )
        },
        props.columns.zipWithIndex.toVdomArray(
          using { case (column, index) =>
            val col = column match {
              case DocumentColumn.Action =>
                <.div(
                  if (isAdding) {
                    EmptyVdom
                  } else {
                    TagMod(
                      tw.px12.py8,
                      ^.width := FileTable.tinyColumnWidth,
                      getActionColumn(
                        props,
                        data,
                        row
                      )
                    )
                  }
                )
              case DocumentColumn.Check =>
                <.div(
                  tw.px12.py8,
                  ^.width := FileTable.tinyColumnWidth,
                  getCheckColumn(data, row)
                )
              case DocumentColumn.Creator =>
                <.div(
                  if (isAdding) {
                    EmptyVdom
                  } else {
                    TagMod(
                      tw.px12.py8,
                      ^.width := FileTable.expandableMediumColumnWidth,
                      getFromColumn(row)
                    )
                  }
                )
              case DocumentColumn.Dot =>
                <.div(
                  ^.width := FileTable.dotColumnWidth,
                  getDotColumn(row, newlyUploadedFileIds)
                )
              case DocumentColumn.Index =>
                <.div(
                  tw.px12.py8,
                  ^.width := FileTable.expandableSmallColumnWidth,
                  getIndexColumn(
                    props,
                    data,
                    row
                  )
                )
              case DocumentColumn.Name =>
                <.div(
                  tw.px12.py8,
                  tw.flexFill,
                  getNameColumn(
                    props,
                    data,
                    row,
                    newlyUploadedFileIds
                  )
                )
              case DocumentColumn.Permission =>
                <.div(
                  if (isAdding) {
                    EmptyVdom
                  } else {
                    TagMod(
                      tw.px12.py8,
                      ^.width := FileTable.mediumColumnWidth,
                      getPermissionColumn(props, row)
                    )
                  }
                )
              case DocumentColumn.UpdatedAt =>
                <.div(
                  if (isAdding) {
                    EmptyVdom
                  } else {
                    TagMod(
                      tw.px12.py8,
                      ^.width := FileTable.mediumColumnWidth,
                      getTimeColumn(row)
                    )
                  }
                )
              case DocumentColumn.DeletedAt =>
                <.div(
                  tw.px12.py8,
                  ^.width := FileTable.mediumColumnWidth,
                  FileDeletedTime(row.item)()
                )
              case DocumentColumn.DeletedBy =>
                <.div(
                  tw.px12.py8,
                  ^.width := FileTable.expandableMediumColumnWidth,
                  FileDeletedBy(row.item)()
                )
              case DocumentColumn.Tracking =>
                <.div(
                  if (isAdding) {
                    EmptyVdom
                  } else {
                    TagMod(
                      tw.px12.py8,
                      ^.width := FileTable.smallColumnWidth,
                      getTrackingColumn(
                        props,
                        data,
                        row
                      )
                    )
                  }
                )
            }
            col(^.key := index)
          }
        )
      )
    }

    // Handle selecting files by clicking and dragging the mouse
    private def getRowKeysFromDataAttribute(subItems: Seq[Row], elements: Seq[Element]) = {
      subItems
        .map(Row.getRowKey)
        .filter { rowKey =>
          elements.exists(elem => rowKey.toString == elem.getAttribute(SelectableDataKey))
        }
    }

    private def resetSelectionData(data: LayoutData, subItems: Seq[Row])(selectEvent: SelectionEvent) = {
      val storedItems = getRowKeysFromDataAttribute(subItems, selectEvent.store.stored.toSeq)
      data.selectionData.modState { curData =>
        curData.copy(selectedItems = curData.selectedItems -- storedItems)
      }
    }

    private def onMoveSelectByMouse(data: LayoutData, subItems: Seq[Row])(selectEvent: SelectionEvent) = {
      val addedItems = getRowKeysFromDataAttribute(subItems, selectEvent.store.changed.added.toSeq)
      val removedItems = getRowKeysFromDataAttribute(subItems, selectEvent.store.changed.removed.toSeq)
      data.selectionData.modState { curData =>
        curData.copy(selectedItems = curData.selectedItems -- removedItems ++ addedItems)
      }
    }

    private def renderColumnHeaders(props: Props, state: State, data: LayoutData, rows: Seq[Row]) = {
      <.div(
        ComponentUtils.testId(DataRoomDocuments, "Table-Head"),
        tw.flex.itemsCenter.borderBottom.borderGray3,
        // Stick at top
        tw.sticky.z1.top0,
        props.columns.zipWithIndex.toVdomArray(
          using { case (column, index) =>
            val col = column match {
              case DocumentColumn.Action =>
                <.div(
                  ^.width := FileTable.tinyColumnWidth,
                  ColumnHead(
                    column = DocumentColumn.Action,
                    renderColumn = _.renderUnsortableHead(EmptyVdom)
                  )()
                )
              case DocumentColumn.Check =>
                <.div(
                  ^.width := FileTable.tinyColumnWidth,
                  ColumnHead(
                    column = DocumentColumn.Check,
                    renderColumn = _.renderUnsortableHead(
                      FileCheck.renderHead(
                        allItems = rows.map(Row.getRowKey),
                        selection = data.selectionData
                      )
                    )
                  )()
                )
              case DocumentColumn.Creator =>
                <.div(
                  ^.width := FileTable.expandableMediumColumnWidth,
                  ColumnHead(
                    column = DocumentColumn.Creator,
                    renderColumn = _.renderUnsortableHead("Created by")
                  )()
                )
              case DocumentColumn.Dot =>
                <.div(^.width := FileTable.dotColumnWidth)
              case DocumentColumn.Index =>
                <.div(
                  ^.width := FileTable.expandableSmallColumnWidth,
                  ColumnHead(
                    column = DocumentColumn.Index,
                    sortColumn = state.sortedByColumn,
                    sortIsAsc = state.sortIsAsc,
                    onSort = c => scope.modState(_.copy(sortIsAsc = !state.sortIsAsc, sortedByColumn = Option(c))),
                    renderColumn = _.renderSortableHead("Index")
                  )()
                )
              case DocumentColumn.Name =>
                <.div(
                  tw.flexFill,
                  ColumnHead(
                    column = DocumentColumn.Name,
                    sortColumn = state.sortedByColumn,
                    sortIsAsc = state.sortIsAsc,
                    onSort = c => scope.modState(_.copy(sortIsAsc = !state.sortIsAsc, sortedByColumn = Option(c))),
                    renderColumn = _.renderSortableHead("Name")
                  )()
                )
              case DocumentColumn.Permission =>
                <.div(
                  ^.width := FileTable.mediumColumnWidth,
                  ColumnHead(
                    column = DocumentColumn.Permission,
                    renderColumn = _.renderUnsortableHead("Accessible to")
                  )()
                )
              case DocumentColumn.UpdatedAt =>
                <.div(
                  ^.width := FileTable.mediumColumnWidth,
                  ColumnHead(
                    column = DocumentColumn.UpdatedAt,
                    sortColumn = state.sortedByColumn,
                    sortIsAsc = state.sortIsAsc,
                    onSort = c => scope.modState(_.copy(sortIsAsc = !state.sortIsAsc, sortedByColumn = Option(c))),
                    renderColumn = _.renderSortableHead("Updated")
                  )()
                )
              case DocumentColumn.DeletedBy =>
                <.div(
                  ^.width := FileTable.expandableMediumColumnWidth,
                  ColumnHead(
                    column = DocumentColumn.DeletedBy,
                    sortColumn = state.sortedByColumn,
                    sortIsAsc = state.sortIsAsc,
                    onSort = c => scope.modState(_.copy(sortIsAsc = !state.sortIsAsc, sortedByColumn = Option(c))),
                    renderColumn = _.renderSortableHead("Removed by")
                  )()
                )
              case DocumentColumn.DeletedAt =>
                <.div(
                  ^.width := FileTable.mediumColumnWidth,
                  ColumnHead(
                    column = DocumentColumn.DeletedAt,
                    sortColumn = state.sortedByColumn,
                    sortIsAsc = state.sortIsAsc,
                    onSort = c => scope.modState(_.copy(sortIsAsc = !state.sortIsAsc, sortedByColumn = Option(c))),
                    renderColumn = _.renderSortableHead("Removed date")
                  )()
                )
              case DocumentColumn.Tracking =>
                <.div(
                  ^.width := FileTable.smallColumnWidth,
                  ColumnHead(
                    column = DocumentColumn.Tracking,
                    renderColumn = _.renderUnsortableHead(EmptyVdom)
                  )()
                )
            }
            col(^.key := index)
          }
        )
      )
    }

    private def renderFiles(
      props: Props,
      state: State,
      newlyUploadedFileIds: Set[FileId],
      renderFiles: FileExplorer.RenderFiles
    ) = {
      val rows = renderFiles.rows
      val data = renderFiles.data

      // Get the sorted rows
      val sortedRows = state.sortedByColumn
        .map { sortBy =>
          val sorted = sortBy match {
            case DocumentColumn.Index =>
              rows.sortBy(_.item)(
                using FileIndex.ordering
              )
            case DocumentColumn.Name =>
              rows.sortBy(_.item)(
                using FileName.ordering
              )
            case DocumentColumn.UpdatedAt =>
              rows.sortBy(_.item)(
                using FileUpdatedTime.ordering
              )
            case DocumentColumn.DeletedBy =>
              rows.sortBy(_.item)(
                using FileDeletedBy.ordering
              )
            case DocumentColumn.DeletedAt =>
              rows.sortBy(_.item)(
                using FileDeletedTime.ordering
              )
            case _ => rows
          }
          if (state.sortIsAsc) sorted else sorted.reverse
        }
        .getOrElse(rows)

      SelectableArea(
        boundary = _.querySelector(".ReactVirtualized__List") match {
          case ele: HTMLElement => Option(ele)
          case _                => None
        },
        selectableClass = SelectableCls,
        onStart = resetSelectionData(data, sortedRows),
        onMove = onMoveSelectByMouse(data, sortedRows),
        renderChildren = renderChildren => {
          <.div(
            ComponentUtils.testId(DataRoomDocuments, "FileTable"),
            tw.flex.flexCol.hPc100.px24.pt24.z2.selectNone,
            // Column headers
            renderColumnHeaders(
              props,
              state,
              data,
              rows
            ),
            // List files
            <.div.withRef(renderChildren.ref)(
              tw.flexFill,
              ContextMenuR(
                renderContent = None,
                renderTarget = renderContextMenuTarget => {
                  <.div(
                    tw.hPc100,
                    ReactVirtualizedAutoSizer(
                      childrenParam = renderProps => {
                        val scrollToAddingFolder = sortedRows.indexWhere { row =>
                          InlineEditingData.isAdding(row, data.inlineEditingData.value)
                        }
                        ReactVirtualizedList(
                          heightParam = renderProps.height,
                          rowCountParam = sortedRows.length,
                          rowHeightParam = _ => 57,
                          rowRendererParam = renderRow(
                            renderContextMenuTarget,
                            props,
                            newlyUploadedFileIds,
                            sortedRows,
                            data,
                            _
                          ).rawNode,
                          scrollToIndexParam = {
                            if (scrollToAddingFolder == -1) None else Option(scrollToAddingFolder)
                          },
                          widthParam = renderProps.width
                        ).rawNode
                      }
                    )
                  )
                }
              )()
            )
          )
        }
      )()
    }

    private def goToLocation(props: Props, rowKey: Row.RowKey) = {
      if (rowKey == Row.RecentFiles) {
        DynamicAuthPage.DataRoomRecentPage(props.dataRoomWorkflowId, None)
      } else if (rowKey == Row.Trash) {
        DynamicAuthPage.DataRoomTrashPage(props.dataRoomWorkflowId, None)
      } else {
        val pageOpt =
          for {
            (folderId, fileIdSuffixOpt) <- rowKey match {
              case Row.FolderRowKey(folderId) =>
                Option(folderId -> None)
              case Row.FileRowKey(fileId) =>
                Option(fileId.folder -> Some(fileId.filePart))
              case _ =>
                None
            }
          } yield DynamicAuthPage.DataRoomFolderPage(
            props.dataRoomWorkflowId,
            folderId,
            fileIdSuffixOpt
          )
        pageOpt.getOrElse(
          props.page match {
            case page: DataRoomFolderPage => page
            case _ =>
              DynamicAuthPage.DataRoomFolderPage(
                props.dataRoomWorkflowId,
                props.rootFolderId,
                None
              )
          }
        )
      }
    }

    private def getFileExplorerTab(page: DataRoomDocumentsPage): Option[FileExplorer.Tab] = {
      page match {
        case _: DataRoomRecentPage => Some(FileExplorer.Tab.RecentFiles)
        case _: DataRoomTrashPage  => Some(FileExplorer.Tab.Trash)
        case _                     => None
      }
    }

    def render(props: Props, state: State): VdomElement = {
      val showSearch = props.dataRoomData.latestState.dataRoomCreatedState.showSearch
      val notificationStartTime = Instant.now().minus(30, ChronoUnit.DAYS)
      NotificationProvider(
        currentUser = props.userId,
        notificationSpaceId = NotificationSpaceId.channelSystemNotificationSpaceId(props.dataRoomWorkflowId),
        notificationTypes = Set(NotificationType.DataRoomNewFile),
        notificationFilter = (model: NotificationModel) =>
          !NotificationUtils.isUserSeenNotification(props.userId, model) && model.createdAt.exists(
            _.isAfter(notificationStartTime)
          ),
        renderer = (notifications, isLoading) => {
          val newlyUploadedFileIds = notifications
            .map(_.notificationData)
            .collect { case data: DataRoomNewFileNotificationData => data.fileIds }
            .flatten
            .toSet
          React.Fragment(
            renderUserDetailView(props, state),
            FileExplorer(
              router = props.router,
              userId = props.userId,
              location = FileManagerLocation.Folder(None, props.folderId),
              goToLocation = goToLocation(props, _),
              rootLocation = FileManagerLocation.Folder(None, props.rootFolderId),
              renderSearchBar = Option.when(showSearch)(renderSearchBar(props, state: State)),
              renderOnboarding = renderOnboarding(props),
              renderUploadSuggestion = Option.when(BillingPlanStatus.checkStatus(props.dataRoomData.dataRoomPlan)) {
                getFileUploadProps(props)
              },
              headerActions = getHeaderActions(props),
              showIndex = props.showIndex,
              renderOnError = props.renderOnError,
              isShowingFile = getFileIdOpt(props.page),
              currentTabOpt = getFileExplorerTab(props.page),
              componentUnmountCb = markAllRecentFilesAsSeen(props.dataRoomWorkflowId),
              folderOrderMap = state.folderOrderMap,
              defaultFolderOrder = props.defaultFolderOrder,
              newFileCount = newlyUploadedFileIds.size,
              renderContentLoading = () => SkeletonListFolder()(),
              renderFiles = renderFiles(
                props,
                state,
                newlyUploadedFileIds,
                _
              ),
              layout = FileExplorer.Layout(
                navigationWidth = state.navigationWidth,
                onResize = resize => scope.modState(_.copy(navigationWidth = resize.firstHalfWidth.toInt))
              ),
              showTrash = props.isActorRole(DataRoomRoleUtils.isInternal) || props.isActorRole(DataRoomRoleUtils.isGuest),
              reactStream = Some(DataRoomReactStream.dataRoomFile(props.dataRoomData.workflowId)),
              renderRegion = DataRoomMultiRegionButton()()
            )()
          )
        }
      )()
    }

    def markAllRecentFilesAsSeen(dataRoomWorkflowId: DataRoomWorkflowId): Callback = {
      ZIOUtils.toReactCallback {
        DataRoomEndpointClient
          .markDataRoomRecentFilesAsSeen(MarkDataRoomRecentFilesAsSeenParams(dataRoomWorkflowId))
          .map(_ => Callback.empty)
      }
    }

  }

  private def defaultState(props: Props) = {
    State(
      viewedDetail = None,
      viewRecentFiles = props.isRecentPage,
      folderOrderMap = Map(),
      navigationWidth = defaultNavigationWidth,
      searchQuery = "",
      searchType = SearchType.Document,
      sortedByColumn = {
        val defaultSortIndex = props.defaultFolderOrder.sortColumn
        if (0 <= defaultSortIndex && defaultSortIndex <= props.columns.length) {
          Option(props.columns(defaultSortIndex))
        } else {
          None
        }
      },
      sortIsAsc = props.defaultFolderOrder.sortIsAsc
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialStateFromProps(defaultState)
    .renderBackend[Backend]
    .componentDidUpdate { scope =>
      val isTrashPageChanged = scope.currentProps.isTrashPage != scope.prevProps.isTrashPage
      val isRecentPageChanged = scope.currentProps.isRecentPage != scope.prevProps.isRecentPage
      val isShowIndexChanged =
        scope.currentProps.dataRoomData.latestState.dataRoomCreatedState.showIndex != scope.prevProps.dataRoomData.latestState.dataRoomCreatedState.showIndex
      val markRecentFilesAsSeenCb = Callback.when(isRecentPageChanged) {
        Callback.when(scope.prevState.viewRecentFiles)(
          scope.backend.markAllRecentFilesAsSeen(scope.currentProps.dataRoomWorkflowId)
        ) >>
          scope.modState(_.copy(viewRecentFiles = scope.currentProps.isRecentPage))
      }
      val resetStateCb = Callback.when(isRecentPageChanged || isShowIndexChanged || isTrashPageChanged) {
        scope.setState(defaultState(scope.currentProps).copy(navigationWidth = scope.currentState.navigationWidth))
      }
      markRecentFilesAsSeenCb >> resetStateCb
    }
    .build

}
