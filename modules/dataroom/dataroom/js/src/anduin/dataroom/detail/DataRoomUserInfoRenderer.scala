// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail

import anduin.model.common.user.UserId
import design.anduin.components.avatar.react.AvatarLabelR
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.portal.laminar.PortalWrapperL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*

import com.raquo.laminar.api.L.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[dataroom] final case class DataRoomUserInfoRenderer(
  userId: UserId,
  emailAddress: String,
  fullName: String,
  renderStar: Option[String] = None,
  showCopyEmail: Boolean = false,
  isDeleted: Boolean = false
) {

  def apply(): VdomElement = {
    DataRoomUserInfoRenderer.component(this)
  }

}

object DataRoomUserInfoRenderer {

  private type Props = DataRoomUserInfoRenderer

  private def render(props: Props): VdomElement = {
    <.div(
      ^.height := 36.px,
      AvatarLabelR(
        emailAddress = props.emailAddress,
        fullName = props.fullName,
        renderCopyIndicator = copyEle => {
          if (props.showCopyEmail) {
            copyEle
          } else {
            emptyNode
          }
        },
        renderFullName = div(
          tw.flex.itemsCenter.spaceX4,
          _,
          props.renderStar.fold[Node](emptyNode) { description =>
            span(
              tw.textWarning2,
              TooltipL(
                renderTarget = IconL(name = Val(Icon.Glyph.Star), size = Icon.Size.Custom(13))(),
                renderContent = _.amend(description),
                targetWrapper = PortalWrapperL.Inline
              )()
            )
          },
          if (props.isDeleted) {
            div(
              tw.textGray7.fontNormal,
              "(removed)"
            )
          } else {
            emptyNode
          }
        )
      )()
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
