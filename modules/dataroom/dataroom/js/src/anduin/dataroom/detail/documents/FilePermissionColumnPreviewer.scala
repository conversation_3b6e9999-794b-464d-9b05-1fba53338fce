// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.documents

import anduin.dataroom.reactive.DataRoomReactStream
import design.anduin.components.button.Button
import design.anduin.components.modal.{Modal, ModalBody, ModalFooter}
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.file.tree.PermissionTree
import anduin.model.common.user.UserId
import anduin.model.id.FolderId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.endpoints.*
import com.anduin.stargazer.service.FileServiceEndpoints.{PermissionTarget, TargetPermission}
import design.anduin.components.icon.Icon
import design.anduin.components.tooltip.react.TooltipR

final case class FilePermissionColumnPreviewer(
  dataRoomWorkflowId: DataRoomWorkflowId,
  folderId: FolderId,
  actorUserId: UserId,
  title: String,
  permissionTarget: PermissionTarget,
  changes: StateSnapshotWithModFn[AssetPermissionChanges],
  managePermissionTreePageUrl: Option[String],
  showIndex: Boolean = false
) {
  def apply(): VdomElement = FilePermissionColumnPreviewer.component(this)
}

object FilePermissionColumnPreviewer {

  private type Props = FilePermissionColumnPreviewer

  private def renderButton(openToggle: Callback) = {
    TooltipR(
      renderTarget = Button(
        style = Button.Style.Full(icon = Some(Icon.Glyph.Eye), height = Button.Height.Fix24),
        onClick = openToggle
      )(),
      renderContent = _("View permissions")
    )()
  }

  private def renderTree(props: Props) = {
    <.div(
      tw.flex.flexCol.borderBottom.borderGray3,
      ^.height := 492.px,
      PermissionTree(
        rootItem = PermissionTree.ItemType.Folder(
          FolderInfo.empty(props.folderId),
          TargetPermission.empty
        ),
        permissionTargetOpt = Some(props.permissionTarget),
        changes = props.changes,
        externalCanSelectItemCheck = (_, permissionOpt) => canSelect(props, permissionOpt),
        externalCanSelectHeaderCheck = permissionOpt => canSelect(props, permissionOpt),
        showIndex = props.showIndex,
        lowestPermission = FileFolderPermission.Read,
        reactStream = Some(DataRoomReactStream.dataRoomFile(props.dataRoomWorkflowId))
      )()
    )
  }

  private def canSelect(
    props: Props,
    permissionOpt: Option[FileFolderPermission]
  ) = {
    val isSelected = props.changes.value.recursivePermissions.get(props.folderId).contains(permissionOpt)
    Either.cond(
      isSelected,
      (),
      "This is preview-only"
    )
  }

  private def renderFooter(closeToggle: Callback) = {
    ModalFooter()(
      <.div(
        tw.flex.justifyEnd.itemsCenter,
        Button(onClick = closeToggle)("Close")
      )
    )
  }

  private def render(props: Props) = {
    Modal(
      title = props.title,
      renderTarget = renderButton,
      renderContent = closeToggle =>
        React.Fragment(
          ModalBody()(
            <.p(
              tw.pb16.textGray7,
              s"This is a preview of the participant's permissions. ",
              props.managePermissionTreePageUrl.map { url =>
                Button(
                  style = Button.Style.Text(endIcon = Some(Icon.Glyph.OpenNewWindow)),
                  tpe = Button.Tpe.Link(url, Button.Target.Blank)
                )("Manage permissions")
              }
            ),
            renderTree(props)
          ),
          renderFooter(closeToggle)
        ),
      size = Modal.Size(width = Modal.Width.Px1160)
    )()
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
