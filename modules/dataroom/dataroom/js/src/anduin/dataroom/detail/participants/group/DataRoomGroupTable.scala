// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.group

import anduin.component.tooltip.HelperTooltip
import design.anduin.components.button.Button
import design.anduin.components.checkbox.Checkbox
import design.anduin.components.icon.Icon
import design.anduin.components.menu.react.ContextMenuR
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.components.table.Table
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import anduin.component.util.JsDateFormatterUtils
import anduin.component.util.JsDateFormatterUtils.JsDateFormat
import anduin.dataroom.DataRoomData
import anduin.dataroom.group.DataRoomGroupData
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.id.dataroom.DataRoomGroupId
import anduin.layout.SelectionData
import anduin.model.common.user.UserId
import anduin.scalajs.pluralize.Pluralize
import anduin.utils.StateSnapshotWithModFn
import stargazer.model.routing.{DynamicAuthPage, Page}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[participants] final case class DataRoomGroupTable(
  router: RouterCtl[Page],
  dataRoomData: DataRoomData,
  actorUserId: UserId,
  groupDataList: Seq[DataRoomGroupData],
  groupSelection: StateSnapshotWithModFn[SelectionData[DataRoomGroupId]]
) {
  def apply(): VdomElement = DataRoomGroupTable.component(this)
}

object DataRoomGroupTable {

  private type Props = DataRoomGroupTable

  type Column = Table.Column[DataRoomGroupData]

  private val GroupTable = (new Table[DataRoomGroupData])()

  private def render(props: Props) = {
    ContextMenuR(
      renderContent = None,
      renderTarget = renderContextMenuTarget => {
        <.div(
          tw.selectNone,
          GroupTable(
            columns = getColumns(props),
            rows = props.groupDataList,
            getKey = _.id.idString,
            style = Table.Style.Minimal,
            headIsSticky = Some(Table.Sticky()),
            renderRow = renderRow(renderContextMenuTarget, props),
            sortColumn = Some(1), // Group Name row
            sortIsAsc = true
          )()
        )
      }
    )()
  }

  private def getColumns(props: Props) = {
    Seq(
      getCheckboxColumn(props),
      getNameColumn(props),
      getParticipantColumn,
      getRoleColumn,
      getLinkColumn,
      getCreationColumn(props),
      getActionColumn(props)
    )
  }

  private def getCheckboxColumn(props: Props) = {
    Table.Column[DataRoomGroupData](
      head = renderCheckboxHeader(props),
      render = entry => Table.Cell(renderCheckbox(props, entry)),
      width = "40px"
    )
  }

  private def renderCheckboxHeader(props: Props) = {
    val groupIds = props.groupDataList.map(_.id).toSet
    val checkCount = groupIds.count(props.groupSelection.value.contains)
    <.div(
      ComponentUtils.testId(DataRoomGroupTable, "Header-Checkbox"),
      tw.flexNone.wPx16.mr12,
      Checkbox(
        isChecked = checkCount > 0,
        onChange = { isChecked =>
          props.groupSelection.modState { groupSelection =>
            if (isChecked) groupSelection ++ groupIds else groupSelection -- groupIds
          }
        },
        isIndeterminate = checkCount > 0 && checkCount < groupIds.size
      )()
    )
  }

  private def renderCheckbox(props: Props, groupData: DataRoomGroupData) = {
    val groupId = groupData.id
    <.div(
      ComponentUtils.testId(DataRoomGroupTable, "Checkbox"),
      tw.flexNone.wPx16.mr12,
      ^.onClick ==> (_.stopPropagationCB),
      Checkbox(
        isChecked = props.groupSelection.value.contains(groupId),
        onChange = { isChecked =>
          props.groupSelection.modState(_.toggle(groupId, Some(isChecked)))
        }
      )()
    )
  }

  private def renderColumnHeader(title: String, centerText: Boolean = false, helperOpt: Option[String] = None) = {
    <.div(
      ComponentUtils.testId(DataRoomGroupTable, "Header-" + title),
      tw.flex.itemsCenter.textGray8.fontSemiBold.text13.leading20,
      TagMod.when(centerText)(tw.textCenter.wPc100),
      title,
      helperOpt.map { helper =>
        <.span(
          tw.ml4,
          HelperTooltip(size = Icon.Size.Custom(12))(helper)
        )
      }
    )
  }

  private def getNameColumn(props: Props) = {
    Table.Column[DataRoomGroupData](
      head = renderColumnHeader("Group"),
      render = entry =>
        Table.Cell(
          DataRoomGroupInfoRenderer(
            groupData = entry,
            onClick = Some(
              props.router.set(
                DynamicAuthPage.DataRoomGroupDetailPage(
                  props.dataRoomData.workflowId,
                  entry.id
                )
              )
            ),
            maxWidth = 400
          )()
        ),
      sortBy = Table.ColumnOrdering(_.name.toLowerCase)
    )
  }

  private def getParticipantColumn = {
    Table.Column[DataRoomGroupData](
      head = renderColumnHeader(
        "Participants",
        centerText = true,
        helperOpt = Some("A user can belong to multiple groups, so counts may exceed total unique users")
      ),
      render = entry =>
        Table.Cell(
          <.div(
            ComponentUtils.testId(DataRoomGroupTable, "UserNum"),
            entry.participants.size
          )
        ),
      sortBy = Table.ColumnOrdering(_.participants.size)
    )
  }

  private def getRoleColumn = {
    Table.Column[DataRoomGroupData](
      head = renderColumnHeader("Role"),
      render = entry =>
        Table.Cell(
          <.div(
            ComponentUtils.testId(DataRoomGroupTable, "GroupRole"),
            DataRoomRoleUtils.getName(entry.role)
          )
        ),
      sortBy = Table.ColumnOrdering(entry => DataRoomRoleUtils.getReversedLevel(entry.role) -> entry.name.toLowerCase)
    )
  }

  private def getLinkColumn = {
    Table.Column[DataRoomGroupData](
      head = renderColumnHeader("Invitation link"),
      render = entry =>
        Table.Cell(
          <.div(
            ComponentUtils.testId(DataRoomGroupTable, "GroupLink"),
            if (entry.linkCount > 0) {
              Pluralize("link", entry.linkCount, inclusive = true)
            } else {
              "--"
            }
          )
        ),
      sortBy = Table.ColumnOrdering(_.linkCount)
    )
  }

  private def getCreationColumn(props: Props) = {
    Table.Column[DataRoomGroupData](
      head = renderColumnHeader("Created by"),
      render = entry =>
        Table.Cell {
          val creatorName =
            props.dataRoomData.latestState.getUserInfo(entry.creator).map(_.getDisplayName).getOrElse("")
          <.div(
            tw.flex.flexCol,
            <.div(
              ComponentUtils.testId(DataRoomGroupTable, "GroupCreatedBy"),
              tw.text13.leading20.fontNormal.textGray8,
              creatorName
            ),
            entry.createdAt.map { createdAt =>
              <.div(
                ComponentUtils.testId(DataRoomGroupTable, "GroupCreatedOn"),
                tw.text11.leading16.fontNormal.textGray7,
                "On ",
                JsDateFormatterUtils.format(createdAt, JsDateFormat.LongDatePattern1)
              )
            }
          )
        }
    )
  }

  private def getActionColumn(props: Props) = {
    Table.Column[DataRoomGroupData](
      render = entry =>
        Table.Cell(
          <.div(
            ^.onClick ==> stopPropagation,
            ^.onDoubleClick ==> stopPropagation,
            tw.flex.justifyEnd,
            PopoverR(
              renderTarget = (openToggle, _) =>
                Button(
                  testId = "GroupActions",
                  style = Button.Style.Minimal(height = Button.Height.Fix32, icon = Some(Icon.Glyph.EllipsisHorizontal)),
                  onClick = openToggle
                )(),
              renderContent = closeToggle =>
                DataRoomGroupActionMenu(
                  actorUserId = props.actorUserId,
                  dataRoomData = props.dataRoomData,
                  rows = List(entry),
                  groupSelection = props.groupSelection,
                  onClose = closeToggle
                )(),
              position = PortalPosition.BottomRight
            )()
          )
        ),
      width = "48px"
    )
  }

  private def onContextMenu(
    renderContextMenuTarget: ContextMenuR.RenderTarget,
    props: Props,
    row: DataRoomGroupData
  ) = {
    val selection = props.groupSelection.value
    for {
      newRows <-
        if (selection.contains(row.id)) {
          CallbackTo.pure {
            props.groupDataList.toList.filter(row => selection.contains(row.id))
          }
        } else {
          props.groupSelection.setState(SelectionData.single(row.id)).map(_ => List(row))
        }
      _ <- renderContextMenuTarget.setContent(
        renderGroupActionMenu(
          props,
          newRows,
          renderContextMenuTarget.onCloseMenu
        )
      )
    } yield ()
  }

  private def renderGroupActionMenu(props: Props, rows: List[DataRoomGroupData], closeToggle: Callback) = {
    DataRoomGroupActionMenu(
      actorUserId = props.actorUserId,
      dataRoomData = props.dataRoomData,
      rows = rows,
      groupSelection = props.groupSelection,
      onClose = closeToggle
    )()
  }

  private def renderRow(
    renderContextMenuTarget: ContextMenuR.RenderTarget,
    props: Props
  ): Table.RenderRow[DataRoomGroupData] = { rows => (key, cells, row) =>
    Table.defaultRenderRow(rows)(
      key,
      cells,
      row
    )(
      renderContextMenuTarget.handleContextMenu(
        onContextMenu(
          renderContextMenuTarget,
          props,
          row
        )
      ),
      TagMod.when(props.dataRoomData.latestState.isUserRole(props.actorUserId)(DataRoomRoleUtils.isAdmin))(
        TagMod(
          ^.onClick --> props.groupSelection.setState(SelectionData.single(row.id)),
          ^.onClickCapture ==> { e =>
            if (e.shiftKey) {
              e.preventDefaultCB >> e.stopPropagationCB >> props.groupSelection.modState(
                _.range(rows.map(_.id).toIndexedSeq, row.id)
              )
            } else if (e.metaKey || e.ctrlKey) {
              e.preventDefaultCB >> e.stopPropagationCB >> props.groupSelection.modState(_.toggle(row.id))
            } else {
              Callback.empty
            }
          },
          tw.cursorPointer.trnsA
        )
      ),
      TagMod.when(props.groupSelection.value.contains(row.id)) {
        tw.bgPrimary1.bgOpacity40.hover(tw.bgPrimary1.bgOpacity40)
      }
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
