// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.group

import anduin.dataroom.DataRoomData
import anduin.dataroom.DataRoomData.UserChanges
import design.anduin.components.button.Button
import design.anduin.components.modal.{Modal, ModalBody, ModalFooterWCancel}
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.group.RemoveUsersFromDataRoomGroupParams
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId
import anduin.scalajs.pluralize.Pluralize
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.anduin.stargazer.client.utils.ZIOUtils
import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.react.InitialAvatarR

private[participants] final case class RemoveUsersFromGroupModal(
  dataRoomData: DataRoomData,
  actorUserId: UserId,
  groupId: DataRoomGroupId,
  userIds: Set[UserId],
  renderTarget: (Option[String], Callback) => VdomNode,
  onRemoved: Callback,
  onClose: Callback
) {
  def apply(): VdomElement = RemoveUsersFromGroupModal.component(this)
}

private[participants] object RemoveUsersFromGroupModal {

  private type Props = RemoveUsersFromGroupModal

  private final case class State(
    isBusy: Boolean
  )

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomNode = {
      val adminCount = props.dataRoomData.getAdminCount(
        userChanges = props.userIds.map(userId => userId -> UserChanges(removedGroups = Set(props.groupId))).toMap
      )
      Modal(
        title = "Remove from group?",
        renderTarget = openToggle =>
          props.renderTarget(
            Option.when(adminCount < 1)("There must be at least one admin in the data room after removing users"),
            openToggle
          ),
        renderContent = renderContent(props, state),
        size = Modal.Size(Modal.Width.Px600)
      )()
    }

    private def renderContent(props: Props, state: State)(closeToggle: Callback) = {
      val onClose = closeToggle >> props.onClose
      val (unassignedUsers, groupUsers) = props.userIds.partition { userId =>
        props.dataRoomData.getGroupsOfUser(userId).map(_.id) == Seq(props.groupId)
      }
      React.Fragment(
        ModalBody()(
          <.div(
            tw.flexCol,
            Seq(renderUsers(props, unassignedUsers, true), renderUsers(props, groupUsers, false)).flatten
              .mkReactFragment(
                <.div(tw.borderTop.borderGray3.wPc100.hPx1.my16)
              )
          )
        ),
        ModalFooterWCancel(cancel = onClose)(
          Button(
            style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.isBusy),
            isDisabled = props.userIds.isEmpty,
            onClick = removeUsersFromGroup(props)(onClose)
          )("Remove from group")
        )
      )
    }

    private def renderUsers(
      props: Props,
      userIds: Set[UserId],
      isUnassigned: Boolean
    ) = {
      Option.when(userIds.nonEmpty) {
        <.div(
          tw.flexCol.overflowYAuto,
          ^.maxHeight := 300.px,
          <.p(
            Pluralize("participant", userIds.size, inclusive = true),
            if (isUnassigned) {
              " will become unassigned users and keep their current role and permissions."
            } else {
              " will inherit permissions from their remaining group memberships."
            }
          ),
          <.div(
            tw.flex.flexWrap.wPc100,
            userIds.map(renderUserInfo(props)).toVdomArray
          )
        )
      }
    }

    private def renderUserInfo(props: Props)(userId: UserId) = {
      val userInfo = props.dataRoomData.latestState.participatingUsers.get(userId).map(_.userInfo)
      val userDisplay = userInfo.map(_.getDisplayName).getOrElse("")
      val userName = userInfo.map(_.fullName).getOrElse("")
      <.div(
        tw.wPc50.mt12,
        <.div(
          tw.flex.itemsCenter,
          InitialAvatarR(
            id = userId.idString,
            initials = userName.take(1).toUpperCase,
            size = InitialAvatar.Size.Px20
          )(),
          <.span(tw.ml8.fontSemiBold, userDisplay)
        )
      )
    }

    private def removeUsersFromGroup(props: Props)(onClose: Callback) = {
      Callback.when(props.userIds.nonEmpty) {
        scope.modState(
          _.copy(isBusy = true),
          ZIOUtils.toReactCallback {
            DataRoomEndpointClient
              .removeUsersFromGroup(
                RemoveUsersFromDataRoomGroupParams(props.groupId, props.userIds)
              )
              .map(resp =>
                scope.modState(
                  _.copy(isBusy = false),
                  resp.fold(
                    _ => Toast.errorCallback("Failed to remove participants from group") >> onClose,
                    _ => Toast.successCallback("Participants removed from group") >> props.onRemoved >> onClose
                  )
                )
              )
          }
        )
      }
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State(isBusy = false))
    .renderBackend[Backend]
    .build

}
