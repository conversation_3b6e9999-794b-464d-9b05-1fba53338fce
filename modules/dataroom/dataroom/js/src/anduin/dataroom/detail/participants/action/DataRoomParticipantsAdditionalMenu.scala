// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.action

import anduin.dataroom.DataRoomData
import anduin.dataroom.detail.participants.DataRoomManualNotificationModal
import anduin.dataroom.detail.participants.`export`.{DataRoomExportParticipantsModal, DataRoomExportPermissionsModal}
import anduin.dataroom.detail.participants.group.{DeleteDataRoomGroupModal, RemoveUsersFromGroupModal}
import anduin.dataroom.detail.participants.user.{DataRoomUserRowData, RemoveUsersFromDataRoomModal}
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.dataroom.terms.DataRoomUserTermsStatusModal
import anduin.file.explorer.header.FileHeaderActions
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.scalajs.pluralize.Pluralize
import anduin.stargazer.service.dataroom.DataRoomExportPermissionsParams.ExportPermissionTarget
import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.menu.react.{MenuDividerR, MenuItemR, MenuR}
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*
import stargazer.model.routing.DynamicAuthPage.DataRoomParticipantsPage
import stargazer.model.routing.Page

final case class DataRoomParticipantsAdditionalMenu(
  router: RouterCtl[Page],
  page: DataRoomParticipantsPage,
  workflowId: DataRoomWorkflowId,
  actorUserId: UserId,
  dataRoomData: DataRoomData,
  userRowDataMap: Map[UserId, DataRoomUserRowData],
  selectedUsers: Set[UserId],
  selectedGroups: Set[DataRoomGroupId],
  hasToa: Boolean,
  disabledReason: Either[String, Unit],
  onRemoveUsers: Callback,
  onDeleteGroups: Set[DataRoomGroupId] => Callback,
  showMessageAction: Boolean
) {
  private val isInternal = dataRoomData.latestState.isUserRole(actorUserId)(DataRoomRoleUtils.isInternal)
  private val isAdmin = dataRoomData.latestState.isUserRole(actorUserId)(DataRoomRoleUtils.isAdmin)
  private val toaOptions = dataRoomData.latestState.dataRoomCreatedState.termsOfAccessOptions
  private val userRowDataList = selectedUsers.flatMap(userRowDataMap.get).toList

  private val showRemoveUserActions =
    page.canManageUsers && selectedUsers.nonEmpty && isAdmin

  private val showDeleteGroupAction =
    page.isAllGroupsPage && selectedGroups.nonEmpty && isAdmin

  def apply(): VdomElement = DataRoomParticipantsAdditionalMenu.component(this)
}

object DataRoomParticipantsAdditionalMenu {

  private type Props = DataRoomParticipantsAdditionalMenu

  private def render(props: Props): VdomNode = {
    PopoverR(
      renderTarget = (openPopover, _) => renderTarget(props)(openPopover),
      renderContent = closePopover => {
        MenuR()(
          renderMessageParticipants(props)(closePopover),
          renderViewToaStatus(props)(closePopover),
          renderExportParticipants(props)(closePopover),
          renderExportPermissions(props)(closePopover),
          renderRemoveFromGroup(props)(closePopover),
          Option.when(props.showRemoveUserActions || props.showDeleteGroupAction)(MenuDividerR()()),
          renderRemoveFromDataRoom(props)(closePopover),
          renderDeleteGroups(props)(closePopover)
        )
      },
      position = PortalPosition.BottomLeft
    )()
  }

  private def renderTarget(props: Props)(openPopover: Callback) = {
    FileHeaderActions.renderButtonWithTooltip(props.disabledReason) { res =>
      Button(
        style = Button.Style.Full(),
        isDisabled = res.isEmpty,
        onClick = openPopover
      )(
        "More",
        <.span(
          tw.flexNone.pl8,
          IconR(name = Icon.Glyph.CaretDown)()
        )
      )
    }
  }

  private def renderMenuItem(icon: Icon.Name, text: String, isDisabled: Boolean = false)(onClick: Callback) = {
    MenuItemR(
      onClick = onClick,
      color = MenuItemR.ColorGray,
      icon = Some(icon),
      isDisabled = isDisabled
    )(text)
  }

  private def renderMessageParticipants(props: Props)(closePopover: Callback) = {
    Option.when(!props.page.isAllGroupsPage && props.showMessageAction)(
      DataRoomManualNotificationModal(
        userId = props.actorUserId,
        dataRoomData = props.dataRoomData,
        userRowDataMap = Map(),
        initialSelection = props.selectedUsers,
        renderTarget = renderMenuItem(Icon.Glyph.Envelope, "Send message"),
        onClose = closePopover,
        showSelection = false
      )()
    )
  }

  private def renderViewToaStatus(props: Props)(closePopover: Callback) = {
    val selectedGroupsParticipants = getUsersInSelectedGroups(props)
    props.dataRoomData
      .getToaFileIdOpt()
      .filter { _ =>
        props.isInternal || !props.toaOptions.whitelistedUsers.contains(props.actorUserId)
      }
      .map { toaFileId =>
        <.div(
          tw.flexNone.flex,
          DataRoomUserTermsStatusModal(
            userId = props.actorUserId,
            renderTarget = renderMenuItem(
              Icon.Glyph.CheckList,
              "View record of consent",
              isDisabled = !props.hasToa || (selectedGroupsParticipants.isEmpty && props.selectedUsers.isEmpty)
            ),
            toaFileId = toaFileId,
            dataRoomData = props.dataRoomData,
            onClose = closePopover,
            filterUserFn = (props.selectedUsers ++ selectedGroupsParticipants).contains
          )()
        )
      }
  }

  private def getUsersInSelectedGroups(props: Props) = {
    props.selectedGroups.collect(props.dataRoomData.groupMap).flatMap(_.participants)
  }

  private def renderExportParticipants(props: Props)(closePopover: Callback) = {
    val selectedGroupsParticipants = getUsersInSelectedGroups(props)
    DataRoomExportParticipantsModal(
      props.workflowId,
      props.selectedUsers ++ selectedGroupsParticipants,
      props.hasToa,
      closePopover,
      renderTarget = renderMenuItem(
        Icon.Glyph.Table,
        "Export participant's data",
        isDisabled = selectedGroupsParticipants.isEmpty && props.selectedUsers.isEmpty
      ),
      isGlobalAction = false
    )()
  }

  private def renderExportPermissions(props: Props)(closePopover: Callback) = {
    val selectedGroupsParticipants = getUsersInSelectedGroups(props)
    DataRoomExportPermissionsModal(
      props.workflowId,
      ExportPermissionTarget.Users(props.selectedUsers ++ selectedGroupsParticipants),
      closePopover,
      renderTarget = openModal =>
        renderMenuItem(
          Icon.Glyph.Key,
          "Export participant's permissions",
          isDisabled = selectedGroupsParticipants.isEmpty && props.selectedUsers.isEmpty
        )(openModal)
    )()
  }

  private def renderRemoveFromGroup(props: Props)(closePopover: Callback) = {
    val userIds = props.userRowDataList.map(_.userId).toSet
    val hasUnassignedUsers = props.dataRoomData.unassignedParticipants.intersect(userIds).nonEmpty
    val groupIdOpt = props.page.groupIdOpt
    groupIdOpt
      .filter(_ => props.showRemoveUserActions && !hasUnassignedUsers)
      .map(groupId =>
        RemoveUsersFromGroupModal(
          dataRoomData = props.dataRoomData,
          actorUserId = props.actorUserId,
          groupId = groupId,
          userIds = userIds,
          renderTarget = (disabledReasonOpt, openToggle) =>
            TooltipR(
              renderTarget = MenuItemR(
                icon = Some(Icon.Glyph.UserRemove),
                onClick = openToggle,
                isDisabled = disabledReasonOpt.nonEmpty
              )("Remove from group"),
              renderContent = _(disabledReasonOpt),
              isDisabled = disabledReasonOpt.isEmpty
            )(),
          onRemoved = props.onRemoveUsers,
          onClose = closePopover
        )()
      )
  }

  private def renderRemoveFromDataRoom(props: Props)(closePopover: Callback) = {
    Option.when(props.showRemoveUserActions)(
      RemoveUsersFromDataRoomModal(
        router = props.router,
        renderTarget = (canRemove, openToggle) =>
          MenuItemR(
            icon = Some(Icon.Glyph.Trash),
            onClick = openToggle,
            color = MenuItemR.ColorDanger,
            isDisabled = !canRemove
          )("Remove from data room"),
        workflowId = props.dataRoomData.workflowId,
        dataRoomData = props.dataRoomData,
        actorUserId = props.actorUserId,
        userRowDataList = props.userRowDataList,
        onRemoveNonActor = props.onRemoveUsers,
        onClose = closePopover
      )()
    )
  }

  private def renderDeleteGroups(props: Props)(closePopover: Callback) = {
    Option.when(props.showDeleteGroupAction) {
      val deleteGroupData = props.dataRoomData.groupMap
        .filter { case (groupId, _) =>
          props.selectedGroups.contains(groupId)
        }
        .values
        .toList
      <.div(
        tw.flexNone.pr8,
        DeleteDataRoomGroupModal(
          router = props.router,
          actorUserId = props.actorUserId,
          dataRoomWorkflowId = props.dataRoomData.workflowId,
          dataRoomData = props.dataRoomData,
          groupDataList = deleteGroupData,
          renderTarget = targetProps =>
            TooltipR(
              renderTarget = MenuItemR(
                icon = Some(Icon.Glyph.UserRemove),
                color = MenuItemR.ColorDanger,
                onClick = targetProps.openToggle,
                isDisabled = targetProps.disabledReasonOpt.nonEmpty
              )(s"Delete ${Pluralize("group", props.selectedGroups.size)}"),
              renderContent = _(targetProps.disabledReasonOpt),
              isDisabled = targetProps.disabledReasonOpt.isEmpty
            )(),
          onDelete = props.onDeleteGroups(deleteGroupData.map(_.id).toSet),
          onClose = closePopover
        )()
      )
    }
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
