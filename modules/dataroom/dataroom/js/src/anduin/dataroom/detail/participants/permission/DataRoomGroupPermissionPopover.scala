// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.permission

import anduin.dataroom.DataRoomData
import anduin.dataroom.detail.participants.group.{DataRoomGroupInfoRenderer, DataRoomGroupPermissionModal}
import anduin.dataroom.group.DataRoomGroupData
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId
import anduin.scalajs.pluralize.Pluralize
import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.ScalaComponent
import japgolly.scalajs.react.vdom.html_<^.*

final case class DataRoomGroupPermissionPopover(
  dataRoomData: DataRoomData,
  groupIds: Seq[DataRoomGroupId],
  actorUserId: UserId
) {
  def apply(): VdomElement = DataRoomGroupPermissionPopover.component(this)
}

object DataRoomGroupPermissionPopover {

  type Props = DataRoomGroupPermissionPopover

  private def render(props: Props) = {
    val groupDatas = props.groupIds.flatMap(props.dataRoomData.groupMap.get)
    PopoverR(
      renderTarget = (openToggle, _) =>
        <.div(
          tw.mx4,
          Button(
            style = Button.Style.Text(color = Button.Color.Primary, endIcon = Some(Icon.Glyph.CaretDown)),
            onClick = openToggle
          )(s"${Pluralize("group", groupDatas.size, true)}")
        ),
      renderContent = _ =>
        <.div(
          tw.flex.flexCol.p8.overflowXAuto,
          ^.maxHeight := 500.px,
          groupDatas.zipWithIndex.map { (groupData, index) =>
            <.div(
              tw.flex.itemsCenter,
              TagMod.when(index > 0)(tw.mt12),
              ^.width := 368.px,
              <.div(
                tw.flexFill,
                DataRoomGroupInfoRenderer(groupData, maxWidth = 220)()
              ),
              renderGroupPermission(props, groupData)
            )
          }.toVdomArray
        ),
      position = PortalPosition.TopLeft
    )()
  }

  private def renderGroupPermission(props: Props, groupData: DataRoomGroupData) = {
    <.div(
      tw.flex.itemsCenter.pl16,
      DataRoomGroupPermissionModal(
        actorUserId = props.actorUserId,
        dataRoomData = props.dataRoomData,
        groupData = groupData,
        renderTarget = (_, openToggle) =>
          Button(
            testId = "PermissionsStep-EditGroupLink",
            style = Button.Style.Text(color = Button.Color.Primary),
            onClick = openToggle
          )("Edit permissions")
      )()
    )
  }

  val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
