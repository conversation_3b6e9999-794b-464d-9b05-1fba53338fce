// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.invitation

import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[invitation] final case class SettingSection(
  title: String,
  isRequired: <PERSON><PERSON><PERSON>,
  subtitleOpt: Option[VdomElement] = None,
  inlineSubtitle: Boolean = true
) {
  def apply(children: VdomNode*): VdomElement = SettingSection.component(this)(children*)
}

private[invitation] object SettingSection {

  private type Props = SettingSection

  private def render(props: Props, children: PropsChildren) = {
    <.div(
      tw.py8,
      <.div(
        tw.flex.mb4,
        TagMod.unless(props.inlineSubtitle)(tw.flexCol),
        <.div(
          tw.flex.fontSemiBold,
          props.title,
          Option.when(props.isRequired) {
            <.span(
              tw.textDanger4.fontNormal.ml4,
              "*"
            )
          }
        ),
        props.subtitleOpt
      ),
      children
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_PC(render)
    .build

}
