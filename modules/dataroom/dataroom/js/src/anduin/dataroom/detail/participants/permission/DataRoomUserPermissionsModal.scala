// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.permission

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.{<PERSON><PERSON>, <PERSON>dal<PERSON>ody, ModalFooter}
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.{RouterCtl, SetRouteVia}
import japgolly.scalajs.react.vdom.html_<^.*
import zio.ZIO
import anduin.component.tooltip.HelperTooltip
import anduin.dataroom.billing.UpgradeSuggestionCard
import anduin.dataroom.detail.participants.group.DataRoomGroupPermissionModal
import anduin.dataroom.detail.participants.invitation.PermissionSettings
import anduin.dataroom.detail.participants.user.DataRoomUserRowData
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.dataroom.{DataRoomData, DataRoomPermissionCheck, DataRoomUserData}
import anduin.model.common.user.UserId
import anduin.orgbilling.model.plan.DataRoomPremiumFeature
import anduin.stargazer.service.dataroom.{DataRoomPermissionChanges, ModifyDataRoomPermissionsParams}
import anduin.utils.{StateSnapshotWithModFn, StringUtils}
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import com.anduin.stargazer.service.FileServiceEndpoints.PermissionTarget
import stargazer.model.routing.{DynamicAuthPage, Page}

private[participants] final case class DataRoomUserPermissionsModal(
  router: RouterCtl[Page],
  actorUserId: UserId,
  dataRoomData: DataRoomData,
  userRowData: DataRoomUserRowData,
  currentAdminCount: Int,
  refetch: Callback,
  defaultIsOpen: Boolean,
  isDisabled: Boolean,
  renderTarget: (Boolean, Callback) => VdomNode
) {

  private val actorRole = dataRoomData.latestState.getUserRole(actorUserId)
  private val isActorAdmin = DataRoomRoleUtils.isAdmin(actorRole)

  private val isTargetInvitedByActor = userRowData.teamState match {
    case invited: DataRoomUserData.InvitedUser => invited.inviter == actorUserId
    case _: DataRoomUserData.JoinedUser        => false
  }

  private val hasToA = dataRoomData.latestState.dataRoomCreatedState.termsOfAccessOptions.isEnabled
  private val canInvite = DataRoomRoleUtils.canInvite(actorRole)
  private val canInviteCurrentRoles = DataRoomRoleUtils.canInviteRoles(Set(userRowData.userRoles))(actorRole)
  private val isUnassignedUser = userRowData.isIndividualUser

  private val canEdit = (isActorAdmin || isTargetInvitedByActor && canInvite && canInviteCurrentRoles)

  def apply(): VdomElement = DataRoomUserPermissionsModal.component(this)
}

object DataRoomUserPermissionsModal {

  private type Props = DataRoomUserPermissionsModal

  private final case class State(
    settings: PermissionSettings,
    isToaRequired: Boolean,
    isBusy: Boolean
  )

  private class Backend(scope: BackendScope[Props, State]) {

    private val settingsStateSnapshot = StateSnapshotWithModFn.withReuse
      .zoom[State, PermissionSettings](_.settings)(settings => _.copy(settings = settings))
      .prepareVia(scope)

    private val isToaRequiredStateSnapshot = StateSnapshotWithModFn.withReuse
      .zoom[State, Boolean](_.isToaRequired)(isToaRequired => _.copy(isToaRequired = isToaRequired))
      .prepareVia(scope)

    private def onClickSave(props: Props, state: State, closeToggle: Callback) = {
      for {
        _ <- scope.modState(_.copy(isBusy = true))
        _ <- ZIOUtils.toReactCallback {
          for {
            params <- ZIO.attempt {
              val changes = DataRoomPermissionChanges(
                Option(state.settings.role).filter(_ != props.userRowData.userRoles),
                state.settings.assetPermission
              )
              ModifyDataRoomPermissionsParams(
                dataRoomWorkflowId = props.dataRoomData.workflowId,
                updatedUserMap = Map(props.userRowData.userId -> changes),
                isToaRequiredMap = Map(props.userRowData.userId -> state.isToaRequired)
              )
            }
            response <- DataRoomEndpointClient.modifyUserPermissions(params)
            errorCallback = Toast.errorCallback("Failed to update member permissions. Please try again")
            successCallback = props.refetch >> closeToggle >> Toast.successCallback("Member permissions updated")
          } yield scope.modState(_.copy(isBusy = false), response.fold(_ => errorCallback, _ => successCallback))
        }
      } yield ()
    }

    private def checkHasOnlyOneAdmin(props: Props, state: State): Boolean = {
      val users = Map(props.userRowData.userId -> props.userRowData)
      val changes = Map(props.userRowData.userId -> state.settings.role)
      val before = DataRoomUserRowData.getAdminCount(users, Map())
      val after = DataRoomUserRowData.getAdminCount(users, changes)
      (props.currentAdminCount + after - before) <= 1
    }

    private def isActorInviting(props: Props) = {
      props.userRowData.teamState match {
        case invited: DataRoomUserData.InvitedUser => invited.inviter == props.actorUserId
        case _: DataRoomUserData.JoinedUser        => false
      }
    }

    private def isInviting(props: Props) = {
      props.userRowData.teamState match {
        case _: DataRoomUserData.InvitedUser => true
        case _                               => false
      }
    }

    private def renderPermissionItem(
      label: String,
      helperTextOpt: Option[String] = None
    )(
      children: VdomNode
    ) = {
      <.div(
        tw.flexCol.mr48,
        <.div(
          tw.flex.itemsCenter,
          <.span(
            tw.fontSemiBold.text13.leading20,
            label
          ),
          <.span(
            tw.ml4,
            helperTextOpt.map { helperText =>
              HelperTooltip(
                size = Icon.Size.Custom(14)
              )(helperText)
            }
          )
        ),
        <.div(
          ComponentUtils.testId(DataRoomUserPermissionsModal, label),
          tw.mt4,
          children
        )
      )
    }

    private def renderRoleSetting(props: Props, state: State, settings: StateSnapshotWithModFn[PermissionSettings]) = {
      renderPermissionItem(
        label = "Role"
      ) {
        val hasOnlyOneAdmin = checkHasOnlyOneAdmin(props, state)
        DataRoomAdminRoleDropdown(
          dataRoomWorkflowId = props.dataRoomData.workflowId,
          actorRoles = props.actorRole,
          settings = settings,
          nonAdminDefaultChanges = DataRoomAdminRoleDropdown.NonAdminDefaultChanges.Empty,
          hasOnlyOneAdmin = hasOnlyOneAdmin,
          isActorInviting = isActorInviting(props),
          isInviting = isInviting(props),
          isSeatLimitReached = props.dataRoomData.getRemainingSeatCount() <= 0,
          creatorEntityName = props.dataRoomData.latestState.creatorEntity.entityModel.name,
          disabledReasonOpt = Option.unless(props.isUnassignedUser)(
            "Role can't be modified as the user inherits it from group membership"
          ),
          isDisabled = !props.isUnassignedUser
        )()
      }
    }

    private def renderInvitationSetting(props: Props, settings: StateSnapshotWithModFn[PermissionSettings]) = {
      renderPermissionItem(
        label = "Invitation"
      ) {
        <.div(
          tw.py6,
          DataRoomInviteRoleCheckbox(
            actorRoles = props.actorRole,
            modifiedRoles = settings.withReuse.zoomState(PermissionSettings.zoomToRoleSet),
            isActorInviting = isActorInviting(props)
          )("Can invite other participants")
        )
      }
    }

    private def renderToaSetting(props: Props, state: State, settings: StateSnapshotWithModFn[PermissionSettings]) = {
      Option.when(props.hasToA)(
        renderPermissionItem(
          label = "Terms of access"
        ) {
          <.div(
            tw.py6,
            DataRoomToaRequirementCheckbox(
              actorRoles = props.actorRole,
              isToaRequired = isToaRequiredStateSnapshot(state),
              modifiedRoles = settings.withReuse.zoomState(PermissionSettings.zoomToRoleSet),
              isActorInviting = isActorInviting(props)
            )("Must accept NDA to access the data room")
          )
        }
      )
    }

    private def renderPermissionSettings(
      props: Props,
      state: State,
      settings: StateSnapshotWithModFn[PermissionSettings]
    ) = {
      <.div(
        ComponentUtils.testId(DataRoomUserPermissionsModal, "PermissionSetting"),
        tw.flex.itemsStart,
        tw.py8,
        renderRoleSetting(
          props,
          state,
          settings
        ),
        renderInvitationSetting(props, settings),
        renderToaSetting(
          props,
          state,
          settings
        )
      )
    }

    private def renderGroupDescription(props: Props) = {
      props.userRowData.groupIds.toSeq match {
        case Nil => EmptyVdom
        case groupId :: Nil =>
          props.dataRoomData.getGroupData(groupId).fold(EmptyVdom) { groupData =>
            <.div(
              tw.mb16.flex.itemsCenter,
              "This participant inherits permissions from the group",
              <.b(tw.ml4, groupData.name),
              ".",
              DataRoomGroupPermissionModal(
                actorUserId = props.actorUserId,
                dataRoomData = props.dataRoomData,
                groupData = groupData,
                renderTarget = (_, openToggle) =>
                  <.div(
                    tw.ml4,
                    Button(
                      testId = "DataRoomUserPermissionsModal-EditGroupLink",
                      style = Button.Style.Text(color = Button.Color.Primary),
                      onClick = openToggle
                    )("Edit group permissions")
                  )
              )()
            )
          }
        case groupIds =>
          <.div(
            tw.mb16.flex.itemsCenter,
            "This participant inherits permissions from",
            DataRoomGroupPermissionPopover(props.dataRoomData, groupIds, props.actorUserId)(),
            " and these permissions can only be changed at the group level."
          )
      }
    }

    private def renderPermissionTree(props: Props, settings: StateSnapshotWithModFn[PermissionSettings]) = {
      <.div(
        ComponentUtils.testId(DataRoomUserPermissionsModal, "PermissionTree"),
        tw.flex.flexCol.borderBottom.borderGray3,
        ^.height := 456.px,
        DataRoomAssetPermissionSection(
          settings = settings,
          dataRoomData = props.dataRoomData,
          permissionTargetOpt = Some(PermissionTarget.SingleUser(props.userRowData.userId)),
          disabledReasonOpt = Option.unless(props.isUnassignedUser)(
            "Permissions can't be modified as the user inherits them from group membership"
          ),
          additionalItemCheck = { item =>
            val isInvitedByActor = props.userRowData.teamState match {
              case invited: DataRoomUserData.InvitedUser => invited.inviter == props.actorUserId
              case _: DataRoomUserData.JoinedUser        => false
            }
            if (isInvitedByActor) Right(()) else DataRoomPermissionCheck.ownToModify(item.data.userPermission)
          }
        )()
      )
    }

    private def renderPremiumSuggestion(props: Props) = {
      Option.unless(props.dataRoomData.dataRoomPlan.features.contains(DataRoomPremiumFeature.ViewOnly)) {
        <.div(
          tw.wFit.pb8,
          UpgradeSuggestionCard(
            reason = "to enable View Only permission",
            isShowingComparePlans = true
          )()
        )
      }
    }

    private def renderFooter(props: Props, state: State, closeToggle: Callback) = {
      ModalFooter()(
        <.div(
          tw.flex.itemsCenter,
          renderPremiumSuggestion(props),
          renderPermissionWarning(props),
          <.div(
            tw.flex.mlAuto.spaceX8,
            Button(onClick = closeToggle)(if (props.canEdit) "Cancel" else "Close"),
            if (props.canEdit) {
              Button(
                style = Button.Style.Full(isBusy = state.isBusy, color = Button.Color.Primary),
                isDisabled = shouldDisableSaving(props, state),
                onClick = onClickSave(
                  props,
                  state,
                  closeToggle >> goToAllParticipants(props)
                )
              )("Save")
            } else {
              EmptyVdom
            }
          )
        )
      )
    }

    private def renderBody(props: Props, state: State) = {
      val settings = settingsStateSnapshot(state)
      ModalBody()(
        renderGroupDescription(props),
        renderPermissionSettings(
          props,
          state,
          settings
        ),
        renderPermissionTree(props, settings)
      )
    }

    private def onOpen = {
      scope.modState { (state, props) =>
        state.copy(settings = PermissionSettings(props.userRowData.userRoles, AssetPermissionChanges()))
      }
    }

    private def renderPermissionWarning(props: Props) = {
      val userGroups = props.dataRoomData.getGroupsOfUser(props.userRowData.userId)
      if (userGroups.isEmpty) {
        <.div(
          tw.flex.itemsCenter,
          <.div(
            tw.textWarning4.pr8,
            IconR(name = Icon.Glyph.Warning)()
          ),
          "Changes will be applied to all files and subfolders. Please review the folder's content before saving your changes."
        )
      } else if (userGroups.size == 1) {
        EmptyVdom
      } else {
        <.div(
          tw.flex.itemsCenter,
          <.div(
            tw.textPrimary4.pr8,
            IconR(name = Icon.Glyph.Info)()
          ),
          "When groups have different permission levels, participants will receive the highest level of role and access"
        )
      }
    }

    private def goToAllParticipants(props: Props) = {
      if (props.userRowData.userId == props.actorUserId) {
        props.router.set(
          DynamicAuthPage.DataRoomAllParticipantsPage(props.dataRoomData.workflowId, None),
          SetRouteVia.HistoryReplace
        )
      } else {
        Callback.empty
      }
    }

    def render(props: Props, state: State): VdomElement = {
      val userInfo = props.userRowData.userInfo
      Modal(
        title = s"${StringUtils.addApostrophe(userInfo.getDisplayName)} permissions",
        renderTarget = openToggle => props.renderTarget(props.canEdit, openToggle),
        renderContent = closeToggle =>
          React.Fragment(
            renderBody(props, state),
            renderFooter(
              props,
              state,
              closeToggle
            )
          ),
        defaultIsOpened = props.defaultIsOpen,
        size = Modal.Size(Modal.Width.Px1160),
        afterUserOpen = onOpen
      )()
    }

  }

  private def shouldDisableSaving(props: Props, state: State) = {
    props.userRowData.userRoles == state.settings.role &&
    state.isToaRequired == !props.userRowData.isToaWhitelisted &&
    AssetPermissionChanges.isEmpty(state.settings.assetPermission)
  }

  private def getStateFromProps(props: Props) =
    State(
      PermissionSettings(props.userRowData.userRoles, AssetPermissionChanges()),
      isToaRequired = !props.userRowData.isToaWhitelisted,
      isBusy = false
    )

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps(getStateFromProps)
    .renderBackend[Backend]
    .componentDidUpdate { scope =>
      Callback.when(scope.prevProps.userRowData != scope.currentProps.userRowData)(
        scope.setState(getStateFromProps(scope.currentProps))
      )
    }
    .build

}
