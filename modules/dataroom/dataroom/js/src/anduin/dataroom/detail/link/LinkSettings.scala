// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.link

import java.time.{Instant, LocalDate, ZoneId, ZonedDateTime}
import japgolly.scalajs.react.Reusability
import anduin.dataroom.detail.participants.invitation.{InvitationGroupSettings, PermissionSettings}
import anduin.model.common.emailaddress.EmailAddress
import anduin.protobuf.dataroom.link.DataRoomLinkInvitationParamsData
import anduin.utils.Zoom

final case class LinkSettings(
  name: String,
  expiryDate: LinkSettings.ExpiryDateSetting,
  isDisabled: Boolean,
  securitySetting: LinkSettings.SecuritySetting,
  whitelistedDomains: LinkSettings.WhitelistedDomainSetting,
  isEnterpriseLoginEnabled: Boolean,
  groupSettings: InvitationGroupSettings,
  isToaWhitelisted: Boolean
) derives CanEqual

object LinkSettings extends Zoom[LinkSettings] {

  final case class SecuritySetting(
    isEnabled: <PERSON>olean,
    securityType: SecurityType,
    password: PasswordSetting
  ) {

    def isPasswordEnabled: Boolean = {
      if (isEnabled) {
        securityType match {
          case SecurityType.Password => true
          case _                     => false
        }
      } else {
        false
      }
    }

  }

  sealed trait SecurityType derives CanEqual {
    def name: String

    def tooltip: String
  }

  object SecurityType {

    case object AdminApproval extends SecurityType {
      val name = "Admin approval"
      val tooltip = ""
    }

    case object Password extends SecurityType {
      val name = "Password"
      val tooltip = ""
    }

  }

  final case class PasswordSetting(
    password: PasswordSetting.ValueType,
    initialIsEnabled: Boolean
  )

  object PasswordSetting {

    sealed trait ValueType derives CanEqual

    case object UseExisting extends ValueType

    final case class Change(password: String) extends ValueType
  }

  final case class ExpiryDateSetting(
    expiryDate: Instant,
    isEnabled: Boolean
  )

  final case class WhitelistedDomainSetting(
    domains: Seq[String],
    isEnabled: Boolean
  )

  object ExpiryDateSetting {

    def apply(localDate: LocalDate): Instant = {
      ZonedDateTime.of(localDate.atTime(23, 59), ZoneId.systemDefault()).toInstant
    }

    def default: Instant = {
      ExpiryDateSetting(LocalDate.now.plusDays(7))
    }

  }

  def toParams(
    link: LinkSettings,
    permissions: PermissionSettings
  ): DataRoomLinkInvitationParamsData = {
    DataRoomLinkInvitationParamsData(
      name = link.name,
      expiryDate = Option.when(link.expiryDate.isEnabled)(link.expiryDate.expiryDate),
      isDisabled = link.isDisabled,
      whitelistedDomains = if (link.whitelistedDomains.isEnabled) link.whitelistedDomains.domains.toSet else Set(),
      permissionChanges = Some(permissions.assetPermission),
      role = permissions.role,
      isRequiredAdminApproval =
        link.securitySetting.isEnabled && link.securitySetting.securityType == SecurityType.AdminApproval,
      groupIds = link.groupSettings.groupIds,
      isToaWhitelisted = link.isToaWhitelisted
    )
  }

  def isExpiryValid(linkSettings: LinkSettings): Boolean = {
    !linkSettings.expiryDate.isEnabled ||
    linkSettings.expiryDate.expiryDate.isAfter(ZonedDateTime.now(ZoneId.systemDefault()).toInstant)
  }

  def isPasswordValid(linkSettings: LinkSettings): Boolean = {
    val isPasswordNonEmpty = linkSettings.securitySetting.password.password match {
      case LinkSettings.PasswordSetting.UseExisting      => true
      case LinkSettings.PasswordSetting.Change(password) => password.nonEmpty
    }
    val isPasswordEnabled =
      linkSettings.securitySetting.isEnabled && linkSettings.securitySetting.securityType == SecurityType.Password
    !isPasswordEnabled || isPasswordNonEmpty
  }

  def areWhitelistedDomainsValid(linkSettings: LinkSettings): Boolean = {
    val domains = linkSettings.whitelistedDomains.domains
    !linkSettings.whitelistedDomains.isEnabled || domains.nonEmpty && domains.forall {
      case EmailAddress.validDomain(_) => true
      case _                           => false
    }
  }

  def isNameValid(linkSettings: LinkSettings): Boolean = {
    linkSettings.name.nonEmpty
  }

  def isValid(linkSettings: LinkSettings): Boolean = {
    isNameValid(linkSettings) &&
    isPasswordValid(linkSettings) &&
    areWhitelistedDomainsValid(linkSettings) &&
    isExpiryValid(linkSettings)
  }

  def default(groupSettings: InvitationGroupSettings = InvitationGroupSettings.NoGroup): LinkSettings = {
    LinkSettings(
      name = "",
      expiryDate = ExpiryDateSetting(
        expiryDate = ExpiryDateSetting.default,
        isEnabled = false
      ),
      isDisabled = false,
      securitySetting = SecuritySetting(
        isEnabled = false,
        securityType = SecurityType.AdminApproval,
        password = PasswordSetting(
          password = PasswordSetting.Change(""),
          initialIsEnabled = false
        )
      ),
      whitelistedDomains = WhitelistedDomainSetting(
        domains = Seq(),
        isEnabled = false
      ),
      isEnterpriseLoginEnabled = false,
      groupSettings = groupSettings,
      isToaWhitelisted = false
    )
  }

  given linkSettingsReusability: Reusability[LinkSettings] = Reusability.by_==
}
