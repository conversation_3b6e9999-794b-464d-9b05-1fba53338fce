// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.invitation

import anduin.dataroom.group.DataRoomGroupData
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils, Restricted}
import anduin.id.dataroom.DataRoomGroupId
import anduin.utils.ScalaUtils

sealed trait InvitationGroupSettings derives CanEqual {
  lazy val isNoGroup: Boolean = this == InvitationGroupSettings.NoGroup
  lazy val isAddToGroups: Boolean = ScalaUtils.isMatch[InvitationGroupSettings.AddToGroups](this)
  def groupIds: Set[DataRoomGroupId]
  def isValid: <PERSON>olean

  def getRole(groupData: DataRoomGroupId => Option[DataRoomGroupData]): DataRoomRole = {
    if (groupIds.isEmpty) {
      Restricted()
    } else {
      DataRoomRoleUtils.getMaxRole(groupIds.flatMap(groupData).map(_.role))
    }
  }

}

object InvitationGroupSettings {

  case object NoGroup extends InvitationGroupSettings {
    override val groupIds: Set[DataRoomGroupId] = Set.empty
    override def isValid: Boolean = true
  }

  final case class AddToGroups(groupIds: Set[DataRoomGroupId]) extends InvitationGroupSettings {
    override def isValid: Boolean = groupIds.nonEmpty
  }

}
