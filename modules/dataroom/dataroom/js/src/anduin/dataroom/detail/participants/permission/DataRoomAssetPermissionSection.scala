// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.permission

import anduin.dataroom.detail.participants.invitation.PermissionSettings
import anduin.dataroom.reactive.DataRoomReactStream
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.dataroom.{DataRoomData, DataRoomPermissionCheck}
import anduin.file.tree.PermissionTree
import anduin.file.tree.PermissionTree.ItemType
import anduin.model.id.FolderId
import anduin.orgbilling.model.plan.DataRoomPremiumFeature
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.endpoints.FolderInfo
import com.anduin.stargazer.service.FileServiceEndpoints.{PermissionTarget, TargetPermission}
import com.anduin.stargazer.utils.FileFolderPermissionUtils
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class DataRoomAssetPermissionSection(
  settings: StateSnapshotWithModFn[PermissionSettings],
  dataRoomData: DataRoomData,
  permissionTargetOpt: Option[PermissionTarget] = None,
  disabledReasonOpt: Option[String] = None,
  additionalItemCheck: ItemType => Either[String, Unit] = _ => Right(())
) {
  def apply(): VdomElement = DataRoomAssetPermissionSection.component(this)
}

object DataRoomAssetPermissionSection {

  private type Props = DataRoomAssetPermissionSection

  private def getDisabledReason(props: Props, optionPermissionOpt: Option[FileFolderPermission]) = {
    for {
      _ <- props.disabledReasonOpt.toLeft(())
      _ <- DataRoomPermissionCheck.permissionTree(
        roleOpt = Some(props.settings.value.role),
        dataRoomPlan = props.dataRoomData.dataRoomPlan,
        optionPermissionOpt = optionPermissionOpt
      )
    } yield ()
  }

  private def render(props: Props) = {
    val shouldRenderSameAsMeHeader = FileFolderPermissionUtils.optOrdering.lt(
      props.dataRoomData.rootFolder.userPermission,
      Some(DataRoomRoleUtils.getMaxPermission(props.settings.value.role))
    ) && props.disabledReasonOpt.isEmpty
    PermissionTree(
      rootItem = PermissionTree.ItemType.Folder(
        FolderInfo.empty(FolderId.channelSystemFolderId(props.dataRoomData.workflowId)),
        TargetPermission.empty
      ),
      permissionTargetOpt = props.permissionTargetOpt,
      changes = props.settings.withReuse.zoomState(PermissionSettings.zoomToChanges),
      externalCanSelectItemCheck = { (item, optionPermissionOpt) =>
        for {
          _ <- getDisabledReason(props, optionPermissionOpt)
          _ <- props.additionalItemCheck(item)
        } yield ()
      },
      externalCanSelectHeaderCheck = { optionPermissionOpt =>
        for {
          _ <- getDisabledReason(props, optionPermissionOpt)
          _ <- DataRoomPermissionCheck.actorRootFolder(
            props.dataRoomData.rootFolder.userPermission,
            optionPermissionOpt
          )
        } yield ()
      },
      showIndex = props.dataRoomData.latestState.dataRoomCreatedState.showIndex,
      lowestPermission = if (props.dataRoomData.dataRoomPlan.features.contains(DataRoomPremiumFeature.ViewOnly)) {
        FileFolderPermission.ViewOnly
      } else {
        FileFolderPermission.Read
      },
      currentUpperBound = DataRoomRoleUtils.getMaxPermission(props.settings.value.role),
      disabledReasonOpt = props.disabledReasonOpt,
      showApplyMaxPermissionButton = Option.when(shouldRenderSameAsMeHeader)("Same as me"),
      reactStream = Some(DataRoomReactStream.dataRoomFile(props.dataRoomData.workflowId)),
      fetchOnMount = false
    )()
  }

  private given propsReusability: Reusability[Props] = Reusability.by_==

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .configure(Reusability.shouldComponentUpdate)
    .build

}
