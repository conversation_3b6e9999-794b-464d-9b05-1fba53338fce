// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.invitation.participants

import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.utils.StateSnapshotWithModFn
import design.anduin.components.selector.react.SelectorR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import anduin.dataroom.detail.participants.invitation.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[participants] final case class SelectSideSection(
  dataRoomWorkflowId: DataRoomWorkflowId,
  hasViewOnlyPlan: Boolean,
  actorRole: DataRoomRole,
  remainingSeatCount: Int,
  users: StateSnapshotWithModFn[InvitationUsers]
) {
  def apply(): VdomElement = SelectSideSection.component(this)
}

private[participants] object SelectSideSection {

  private type Props = SelectSideSection

  private def renderInvitationTypeChoice(
    props: Props,
    name: String,
    isInternal: Boolean,
    description: String
  ) = {
    val isSelected = DataRoomRoleUtils.isInternal(props.users.value.settings.role) == isInternal
    val isDisabled = isInternal && props.remainingSeatCount <= 0

    SelectorR(
      isDisabled = isDisabled,
      isSelected = isSelected,
      onClick = {
        props.users.modStateOption { state =>
          Option.when(isInternal != DataRoomRoleUtils.isInternal(state.settings.role)) {
            state.copy(
              settings = PermissionSettings
                .default(
                  props.dataRoomWorkflowId,
                  props.hasViewOnlyPlan,
                  props.actorRole,
                  isInternal
                )
                .withCanInvite(state.settings.canInvite)
            )
          }
        }
      }
    )(
      <.div(
        ComponentUtils.testId(SelectSideSection, name),
        tw.p16.textLeft,
        <.div(tw.fontSemiBold.text15.leading20.mb4.textGray8, name),
        <.div(tw.text13.leading20.textGray7, description)
      )
    )
  }

  private def render(props: Props) = {
    SettingSection(title = "Who are you inviting", isRequired = true)(
      <.div(
        tw.flex.spaceX16,
        renderInvitationTypeChoice(
          props = props,
          name = "Team members",
          isInternal = true,
          description = "People from your organization or close collaborators, such as your legal counsel"
        ),
        renderInvitationTypeChoice(
          props = props,
          name = "External guests",
          isInternal = false,
          description = "People outside of your organization, such as investors or other third-party stakeholders"
        )
      )
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
