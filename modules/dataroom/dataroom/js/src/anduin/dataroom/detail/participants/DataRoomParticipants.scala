// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.nonidealstate.react.NonIdealStateR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import anduin.component.text.SearchBox
import anduin.component.tooltip.HelperTooltip
import anduin.dataroom.detail.link.{ModifyDataRoomLinkInvitationSettings, WithSharableLinkConfig}
import anduin.dataroom.detail.participants.action.DataRoomParticipantsAdditionalMenu
import anduin.dataroom.detail.participants.permission.DataRoomUserPermissionsModal
import anduin.dataroom.detail.participants.reminder.{
  BatchReminderWidget,
  ReminderInfo,
  SendBatchReminders<PERSON><PERSON>on,
  SendBatchRemindersModal
}
import anduin.dataroom.detail.participants.user.{DataRoomUserRowData, DataRoomUserTable}
import anduin.dataroom.{DataRoomData, DataRoomFrontEndState, DataRoomPermissionCheck, DataRoomUserData}
import anduin.file.explorer.header.FileHeaderActions
import anduin.id.dataroom.DataRoomGroupId
import anduin.layout.SelectionData
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.scalajs.pluralize.Pluralize
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.client.localstorage.{AsyncSessionStorage, SessionStorageKey}
import stargazer.model.routing.{DynamicAuthPage, Page}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.dataroom.detail.participants.group.*
import anduin.dataroom.detail.participants.invitation.*
import anduin.dataroom.role.*
import stargazer.model.routing.DynamicAuthPage.*

final case class DataRoomParticipants(
  router: RouterCtl[Page],
  userId: UserId,
  page: DataRoomParticipantsPage,
  dataRoomData: DataRoomData,
  refetch: Callback,
  editUserPermissions: Option[UserId],
  openRequestAccessModalByDefault: Boolean = false
) {

  val isInternalUser: Boolean =
    dataRoomData.latestState.dataRoomCreatedState.individualRoles
      .get(userId)
      .exists(DataRoomRoleUtils.isInternal)

  val scopeUserIds: Set[UserId] = page match {
    case _: DynamicAuthPage.DataRoomAllParticipantsPage        => dataRoomData.latestState.participatingUsers.keySet
    case _: DynamicAuthPage.DataRoomUnassignedParticipantsPage => dataRoomData.unassignedParticipants
    case _: DynamicAuthPage.DataRoomAccessRequestsPage         => Set.empty[UserId]
    case _: DynamicAuthPage.DataRoomAllGroupsPage              => dataRoomData.groupParticipants
    case page: DynamicAuthPage.DataRoomGroupDetailPage =>
      dataRoomData.groupMap.get(page.dataRoomGroupId).map(_.participants.toSet).getOrElse(Set.empty[UserId])
  }

  val actorRole: DataRoomRole =
    dataRoomData.latestState.dataRoomCreatedState.individualRoles.getOrElse(userId, Restricted())

  def apply(): VdomElement = DataRoomParticipants.component(this)

}

object DataRoomParticipants {

  private type Props = DataRoomParticipants

  private type DismissedLinkList = Set[(UserId, DataRoomWorkflowId)]

  private final case class State(
    userSelection: SelectionData[UserId],
    groupSelection: SelectionData[DataRoomGroupId],
    invitationInfo: InvitationInfo,
    reminderInfo: ReminderInfo,
    isBatchReminderModalOpened: Boolean,
    dismissedLinkList: DismissedLinkList,
    searchedTerm: String
  )

  private def onSingleSelection[A, D](selection: SelectionData[A], dataMap: Map[A, D]): Option[D] = {
    selection.selectedItems.headOption.filter(_ => selection.selectedItems.size == 1).flatMap(dataMap.get)
  }

  private final class Backend(scope: BackendScope[Props, State]) {

    given userSelectionReusability: Reusability[SelectionData[UserId]] = SelectionData.reusability[UserId]

    given groupSelectionReusability: Reusability[SelectionData[DataRoomGroupId]] =
      SelectionData.reusability[DataRoomGroupId]

    private val userSelectionSnapshot = StateSnapshotWithModFn.withReuse
      .zoom[State, SelectionData[UserId]](_.userSelection)(selection => _.copy(userSelection = selection))
      .prepareVia(scope)

    private val groupSelectionSnapshot = StateSnapshotWithModFn.withReuse
      .zoom[State, SelectionData[DataRoomGroupId]](_.groupSelection)(selection => _.copy(groupSelection = selection))
      .prepareVia(scope)

    private val invitationInfoSnapshot = StateSnapshotWithModFn.withReuse
      .zoom[State, InvitationInfo](_.invitationInfo)(invitationInfo => _.copy(invitationInfo = invitationInfo))
      .prepareVia(scope)

    private val reminderInfoSnapshot = StateSnapshotWithModFn.withReuse
      .zoom[State, ReminderInfo](_.reminderInfo)(reminderInfo => _.copy(reminderInfo = reminderInfo))
      .prepareVia(scope)

    def render(props: Props, state: State): VdomNode = {
      React.Fragment(
        if (props.isInternalUser) {
          DataRoomParticipantsNavigationLayout(
            router = props.router,
            userId = props.userId,
            page = props.page,
            dataRoomData = props.dataRoomData,
            renderContent = renderContent(props, state)
          )()
        } else {
          <.div(
            tw.hPc100.flex.flexFill,
            tw.borderTop.border1.borderGray3,
            renderContent(props, state)
          )
        },
        renderBatchWidgets(state)
      )
    }

    private def renderContent(props: Props, state: State) = {
      val userRowDataMap = getUserRowDataMap(props)
      <.div(
        ComponentUtils.testId(DataRoomParticipants, "Content"),
        tw.hPc100.p24.bgGray0.flex.flexCol,
        renderHeader(
          props,
          state,
          userRowDataMap
        ),
        props.page match {
          case _: DynamicAuthPage.DataRoomAllGroupsPage => renderGroupTable(props, state)
          case _                                        => renderParticipantTable(props, state)(userRowDataMap)
        },
        props.editUserPermissions.flatMap(userRowDataMap.get).map { userRowData =>
          Option.when(canViewUserPermission(props)(userRowData))(
            DataRoomUserPermissionsModal(
              router = props.router,
              actorUserId = props.userId,
              dataRoomData = props.dataRoomData,
              userRowData = userRowData,
              currentAdminCount = DataRoomUserRowData.getAdminCount(userRowDataMap, Map()),
              refetch = props.refetch,
              defaultIsOpen = true,
              isDisabled = false,
              renderTarget = (_, _) => EmptyVdom
            )()
          )
        }
      )
    }

    private def renderGroupTable(props: Props, state: State) = {
      val groupDataList = props.dataRoomData.groupMap.values.toSeq
      val filteredGroups = groupDataList.filter(_.name.toLowerCase.contains(state.searchedTerm.toLowerCase))
      if (filteredGroups.nonEmpty) {
        <.div(
          ComponentUtils.testId(DataRoomParticipants, "GroupTable"),
          tw.overflowYAuto.hPc100,
          DataRoomGroupTable(
            router = props.router,
            dataRoomData = props.dataRoomData,
            actorUserId = props.userId,
            groupDataList = filteredGroups,
            groupSelection = groupSelectionSnapshot(state)
          )()
        )
      } else {
        renderEmptyState(
          props,
          groupDataList.isEmpty,
          filteredGroups.isEmpty
        )
      }
    }

    private def renderButton(
      props: Props,
      iconName: Icon.Name,
      name: String,
      isDisabled: Boolean = false
    )(
      onClick: Callback
    ) = {
      FileHeaderActions.renderButtonWithTooltip {
        DataRoomPermissionCheck.expiredPlan(props.dataRoomData.dataRoomPlan)
      } { res =>
        Button(
          testId = name,
          style = Button.Style.Full(icon = Some(iconName)),
          isDisabled = res.isEmpty || isDisabled,
          onClick = onClick
        )(name)
      }
    }

    private def getJoinedUsers(props: Props, userRowDataMap: Map[UserId, DataRoomUserRowData]) = {
      userRowDataMap - props.userId
    }

    private def getInvitedUsers(userRowDataMap: Map[UserId, DataRoomUserRowData]) = {
      userRowDataMap.filter { case (_, userRowData) =>
        userRowData.teamState match {
          case _: DataRoomUserData.JoinedUser  => false
          case _: DataRoomUserData.InvitedUser => true
        }
      }
    }

    private def showBatchRemindersButton(selection: Set[UserId], invitedUsers: Map[UserId, DataRoomUserRowData]) = {
      val reminderSize = if (selection.isEmpty) {
        invitedUsers.size
      } else {
        selection.size
      }
      val allInvited = selection.forall(invitedUsers.contains)
      allInvited && reminderSize > 0
    }

    private def renderTitle(props: Props) = {
      React.Fragment(
        <.h3(
          ComponentUtils.testId(DataRoomParticipants, "Title"),
          tw.textGray8.fontSemiBold,
          props.page match {
            case _: DynamicAuthPage.DataRoomAllParticipantsPage =>
              if (props.isInternalUser) "All users" else "Participants"
            case _: DynamicAuthPage.DataRoomUnassignedParticipantsPage =>
              <.div(
                tw.flex.itemsCenter,
                "Unassigned users",
                <.span(
                  tw.ml8,
                  HelperTooltip()(
                    "Unassigned users are users who do not belong to any group and whose permissions can be customized individually."
                  )
                )
              )
            case _: DynamicAuthPage.DataRoomAccessRequestsPage => "Access requests"
            case _: DynamicAuthPage.DataRoomAllGroupsPage      => "All groups"
            case page: DynamicAuthPage.DataRoomGroupDetailPage =>
              props.dataRoomData.groupMap.get(page.dataRoomGroupId).map { groupData =>
                val creatorNameOpt =
                  props.dataRoomData.latestState
                    .getUserInfo(groupData.creator)
                    .map(_.getDisplayName)
                React.Fragment(
                  EditableDataRoomGroupName(
                    groupId = groupData.id,
                    groupName = groupData.name,
                    editable = props.dataRoomData.latestState.isUserRole(props.userId)(DataRoomRoleUtils.isAdmin)
                  )(),
                  creatorNameOpt.map(creatorName =>
                    <.div(
                      ComponentUtils.testId(DataRoomParticipants, "Creator"),
                      tw.text11.leading16.fontNormal.textGray7,
                      s"Created by $creatorName"
                    )
                  )
                )
              }
          }
        )
      )
    }

    private def renderActionMenu(props: Props, joinedUsers: Map[UserId, DataRoomUserRowData]) = {
      <.div(
        tw.flexNone.mlAuto,
        DataRoomParticipantGlobalActionMenu(
          props.userId,
          props.page,
          props.dataRoomData,
          joinedUsers,
          props.scopeUserIds
        )()
      )
    }

    private def renderHeader(props: Props, state: State, userRowDataMap: Map[UserId, DataRoomUserRowData]) = {
      val joinedUsers = getJoinedUsers(props, userRowDataMap).filter { case (userId, _) =>
        props.scopeUserIds.contains(userId)
      }
      val invitedUsers = getInvitedUsers(userRowDataMap).filter { case (userId, _) =>
        props.scopeUserIds.contains(userId)
      }
      React.Fragment(
        <.div(
          ComponentUtils.testId(DataRoomParticipants, "ActionMenu"),
          tw.flex.itemsStart,
          renderTitle(props),
          Option.when(props.dataRoomData.latestState.isUserRole(props.userId)(DataRoomRoleUtils.isInternal))(
            renderActionMenu(props, joinedUsers)
          )
        ),
        Option.when(
          DataRoomRoleUtils.isAdmin(props.actorRole) &&
            props.dataRoomData.groupMap.isEmpty &&
            !props.dataRoomData.latestState
              .getUserState(props.userId)
              .exists(_.viewedGroupOnboarding)
        ) {
          DataRoomGroupOnboardingWell(
            router = props.router,
            actorUserId = props.userId,
            dataRoomData = props.dataRoomData
          )()
        },
        renderLinkList(props, state),
        <.div(
          tw.flex.itemsCenter.py16,
          renderInviteToDataRoom(props, state)(userRowDataMap),
          renderCreateGroup(props, state),
          renderCreateLinkResult(props, state),
          renderUserPermissions(props, state)(userRowDataMap),
          renderGroupPermissions(props, state),
          renderRenameGroup(props, state),
          renderMoveToGroup(props, state),
          renderManualNotification(props, state)(joinedUsers),
          renderBatchReminders(props, state)(invitedUsers),
          renderMoreMenu(props, state)(userRowDataMap, invitedUsers),
          renderSearchBox(props)
        )
      )
    }

    private def renderInviteToDataRoom(
      props: Props,
      state: State
    )(
      userRowDataMap: Map[UserId, DataRoomUserRowData]
    ) = {
      Option.when(
        props.page.canManageUsers &&
          state.userSelection.isEmpty &&
          DataRoomRoleUtils.canInvite(props.actorRole) &&
          (DataRoomRoleUtils.isAdmin(props.actorRole) || props.page.groupIdOpt.isEmpty)
      ) {
        <.div(
          tw.flexNone.pr8,
          InviteToDataRoomModal(
            actorUserId = props.userId,
            dataRoomWorkflowId = props.dataRoomData.workflowId,
            actorRole = props.actorRole,
            joinedOrInvitedUsers = userRowDataMap.map(_._2.userInfo.emailAddress).toSet,
            dataRoomData = props.dataRoomData,
            invitationInfo = invitationInfoSnapshot(state),
            initialGroupOpt = props.page.groupIdOpt
          )()
        )
      }
    }

    private def renderCreateGroup(props: Props, state: State) = {
      Option.when(
        props.page.isAllGroupsPage && DataRoomRoleUtils.isAdmin(props.actorRole) && state.groupSelection.isEmpty
      ) {
        <.div(
          tw.flexNone.pr8,
          CreateDataRoomGroupModal(
            router = props.router,
            actorUserId = props.userId,
            dataRoomData = props.dataRoomData,
            renderTarget = openModal =>
              Button(
                testId = "create-a-group",
                style = Button.Style.Full(color = Button.Color.Primary, icon = Some(Icon.Glyph.Plus)),
                onClick = openModal
              )("Create group")
          )()
        )
      }
    }

    private def renderCreateLinkResult(props: Props, state: State) = {
      Option.when(
        props.page.canManageUsers && state.userSelection.isEmpty && DataRoomRoleUtils.isAdmin(props.actorRole)
      ) {
        WithSharableLinkConfig(
          dataRoomWorkflowId = props.dataRoomData.workflowId,
          renderFn = { (_, sharableLinkConfigOpt) =>
            Option.when(sharableLinkConfigOpt.exists(_.isSharableLinkEnabled)) {
              <.div(
                tw.flexNone.pr8,
                ModifyDataRoomLinkInvitationSettings(
                  router = props.router,
                  renderTarget = renderButton(
                    props = props,
                    iconName = Icon.Glyph.Link,
                    name = "Create invitation link"
                  ),
                  dataRoomData = props.dataRoomData,
                  actorUserId = props.userId,
                  mode = ModifyDataRoomLinkInvitationSettings.Mode.Create,
                  showManageLink = true,
                  onClose = Callback.empty,
                  setLink = _ => Callback.empty,
                  groupSettings = props.page.groupIdOpt.fold(InvitationGroupSettings.NoGroup)(groupId =>
                    InvitationGroupSettings.AddToGroups(Set(groupId))
                  )
                )()
              )
            }
          }
        )()
      }
    }

    private def canViewUserPermission(props: Props)(userRowData: DataRoomUserRowData) = {
      val isRestricted = props.dataRoomData.latestState.isUserRole(props.userId)(DataRoomRoleUtils.isRestricted)
      val isInviter = userRowData.teamState match {
        case _: DataRoomUserData.JoinedUser        => false
        case invited: DataRoomUserData.InvitedUser => invited.inviter == props.userId
      }
      !isRestricted || isInviter
    }

    private def renderUserPermissions(props: Props, state: State)(userRowDataMap: Map[UserId, DataRoomUserRowData]) = {
      onSingleSelection(state.userSelection, userRowDataMap).map { userRowData =>
        val currentAdminCount = DataRoomUserRowData.getAdminCount(userRowDataMap, Map())
        Option.when(props.page.canManageUsers && canViewUserPermission(props)(userRowData)) {
          <.div(
            tw.flexNone.pr8,
            DataRoomUserPermissionsModal(
              router = props.router,
              actorUserId = props.userId,
              dataRoomData = props.dataRoomData,
              userRowData = userRowData,
              currentAdminCount = currentAdminCount,
              refetch = props.refetch,
              defaultIsOpen = false,
              isDisabled = false,
              renderTarget = (canEdit, openToggle) =>
                renderButton(
                  props,
                  if (canEdit) Icon.Glyph.Edit else Icon.Glyph.Eye,
                  if (canEdit) "Edit permissions" else "View permissions"
                )(openToggle)
            )()
          )
        }
      }
    }

    private def renderGroupPermissions(props: Props, state: State) = {
      Option.when(props.page.isAllGroupsPage && DataRoomRoleUtils.isInternal(props.actorRole)) {
        onSingleSelection(state.groupSelection, props.dataRoomData.groupMap).map { groupData =>
          <.div(
            tw.flexNone.pr8,
            DataRoomGroupPermissionModal(
              actorUserId = props.userId,
              dataRoomData = props.dataRoomData,
              groupData = groupData,
              renderTarget = (canEdit, openModal) =>
                renderButton(
                  props,
                  if (canEdit) Icon.Glyph.Edit else Icon.Glyph.Eye,
                  if (canEdit) "Edit group permissions" else "View group permissions"
                )(openModal)
            )()
          )
        }
      }
    }

    private def renderRenameGroup(props: Props, state: State) = {
      Option.when(props.page.isAllGroupsPage && DataRoomRoleUtils.isAdmin(props.actorRole)) {
        onSingleSelection(state.groupSelection, props.dataRoomData.groupMap).map { groupData =>
          <.div(
            tw.flexNone.pr8,
            RenameDataRoomGroupModal(
              groupId = groupData.id,
              groupName = groupData.name,
              renderTarget = renderButton(
                props,
                iconName = Icon.Glyph.EditField,
                name = "Rename group"
              ),
              onClose = Callback.empty
            )()
          )
        }
      }
    }

    private def renderMoveToGroup(props: Props, state: State) = {
      Option.when(
        props.page.canManageUsers &&
          state.userSelection.nonEmpty &&
          props.dataRoomData.groupMap.nonEmpty &&
          DataRoomRoleUtils.isAdmin(props.actorRole)
      ) {
        <.div(
          tw.flexNone.pr8,
          AddUsersToGroupModal(
            router = props.router,
            actorUserId = props.userId,
            userIds = state.userSelection.selectedItems,
            dataRoomData = props.dataRoomData,
            renderTarget = openToggle =>
              renderButton(
                props,
                Icon.Glyph.Plus,
                "Add to groups"
              )(openToggle),
            onClose = Callback.empty
          )()
        )
      }
    }

    private def renderManualNotification(props: Props, state: State)(joinedUsers: Map[UserId, DataRoomUserRowData]) = {
      val isMessagePoC = props.page.isAllUsersPage && !props.isInternalUser
      val isMessageGroups = props.page.isAllGroupsPage && props.isInternalUser && state.groupSelection.nonEmpty
      val groupUsers = props.dataRoomData.groupMap
        .filter { case (groupId, _) =>
          state.groupSelection.contains(groupId)
        }
        .values
        .flatMap(_.participants)
        .toSet
        .intersect(joinedUsers.keySet)
      Option.when(isMessagePoC || (isMessageGroups && groupUsers.nonEmpty)) {
        val groupDisplay = Pluralize("group", state.groupSelection.size)
        val (userSelection, label, showSelection) = if (isMessageGroups) {
          (groupUsers, s"Message $groupDisplay", true)
        } else {
          (
            state.userSelection.selectedItems.intersect(
              joinedUsers.keySet
            ),
            s"Message ${props.dataRoomData.latestState.creatorEntity.entityModel.name}",
            false
          )
        }
        <.div(
          tw.flexNone.pr8,
          DataRoomManualNotificationModal(
            userId = props.userId,
            dataRoomData = props.dataRoomData,
            userRowDataMap = joinedUsers,
            initialSelection = userSelection,
            showSelection = showSelection,
            renderTarget = renderButton(
              props,
              Icon.Glyph.Envelope,
              label
            ),
            onClose = Callback.empty
          )()
        )
      }
    }

    private def renderBatchReminders(
      props: Props,
      state: State
    )(
      invitedUsers: Map[UserId, DataRoomUserRowData]
    ) = {
      if (
        props.page.canManageUsers &&
        showBatchRemindersButton(state.userSelection.selectedItems, invitedUsers) &&
        (DataRoomRoleUtils.canInvite(props.actorRole) || DataRoomRoleUtils.isInternal(props.actorRole))
      ) {
        <.div(
          tw.flexNone.pr8,
          SendBatchRemindersButton(
            dataRoomWorkflowId = props.dataRoomData.workflowId,
            invitedUserRowDataMap = invitedUsers,
            selection = state.userSelection.selectedItems,
            onClick = scope.modState(
              _.copy(
                isBatchReminderModalOpened = true,
                reminderInfo = ReminderInfo.Empty
              )
            ),
            isDisabled = invitedUsers.isEmpty || !state.reminderInfo.isCompleted
          )(),
          SendBatchRemindersModal(
            dataRoomWorkflowId = props.dataRoomData.workflowId,
            invitedUserRowDataMap = invitedUsers,
            reminderInfo = reminderInfoSnapshot(state),
            selection = state.userSelection.selectedItems,
            isOpened = state.isBatchReminderModalOpened,
            onMinimize = scope.modState(_.copy(isBatchReminderModalOpened = false)),
            onClose = scope.modState(_.copy(reminderInfo = ReminderInfo.Empty, isBatchReminderModalOpened = false))
          )()
        )
      } else if (!props.page.isAllGroupsPage) {
        <.div(
          tw.flexNone.pr8,
          DataRoomManualNotificationModal(
            userId = props.userId,
            dataRoomData = props.dataRoomData,
            userRowDataMap = Map(),
            initialSelection = state.userSelection.selectedItems,
            renderTarget = renderButton(props = props, iconName = Icon.Glyph.Envelope, name = "Send message"),
            onClose = Callback.empty,
            showSelection = false
          )()
        )
      } else {
        EmptyVdom
      }
    }

    private def renderMoreMenu(
      props: Props,
      state: State
    )(
      userRowDataMap: Map[UserId, DataRoomUserRowData],
      invitedUsers: Map[UserId, DataRoomUserRowData]
    ) = {
      val toaOptions = props.dataRoomData.latestState.dataRoomCreatedState.termsOfAccessOptions
      val hasToa = toaOptions.isEnabled && toaOptions.versions.nonEmpty
      Option.when(
        state.userSelection.nonEmpty || (state.groupSelection.nonEmpty && DataRoomRoleUtils.isAdmin(props.actorRole))
      )(
        <.div(
          ComponentUtils.testId(DataRoomParticipants, "More"),
          tw.flexNone.pr8,
          DataRoomParticipantsAdditionalMenu(
            router = props.router,
            page = props.page,
            workflowId = props.dataRoomData.workflowId,
            actorUserId = props.userId,
            dataRoomData = props.dataRoomData,
            userRowDataMap = userRowDataMap,
            selectedUsers = state.userSelection.selectedItems,
            selectedGroups = state.groupSelection.selectedItems,
            hasToa = hasToa,
            disabledReason = DataRoomPermissionCheck.expiredPlan(props.dataRoomData.dataRoomPlan),
            onRemoveUsers = scope.setState(getDefaultState),
            onDeleteGroups = deletedGroupIds =>
              scope.setState(
                getDefaultState,
                Callback.when(deletedGroupIds.size == props.dataRoomData.groupMap.size)(
                  props.router.set(DynamicAuthPage.DataRoomAllParticipantsPage(props.dataRoomData.workflowId, None))
                )
              ),
            showMessageAction = showBatchRemindersButton(
              state.userSelection.selectedItems,
              invitedUsers
            )
          )()
        )
      )
    }

    private def renderLinkList(props: Props, state: State) = {
      val latestState = props.dataRoomData.latestState
      val linkCount = latestState.linkStateMap.count { case (linkId, linkStateWithEnterpriseLogin) =>
        val linkState = linkStateWithEnterpriseLogin.linkState
        val isInMap = latestState.dataRoomCreatedState.linkInvitationMap.get(linkId).exists { linkInvitation =>
          props.page.isAllUsersPage ||
          (props.page.isAllGroupsPage && linkInvitation.groupIds.nonEmpty) ||
          (props.page.isGroupParticipantPage && props.page.groupIdOpt.exists(linkInvitation.groupIds.contains))
        }
        !linkState.isExpired && !linkState.isDisabled && isInMap
      }
      val isPlanNotExpired = DataRoomPermissionCheck.expiredPlan(props.dataRoomData.dataRoomPlan).isRight
      val isDismissed = state.dismissedLinkList.contains(props.userId -> props.dataRoomData.workflowId)
      Option.when(linkCount > 0 && isPlanNotExpired && !isDismissed) {
        <.div(
          tw.flex.itemsCenter.p12.mt12,
          tw.bgPrimary1.bgOpacity40.rounded4,
          <.div(
            tw.flexNone.flex.itemsCenter.justifyCenter,
            tw.wPx32.hPx32.roundedFull.bgPrimary1.textPrimary5,
            IconR(name = Icon.Glyph.Link)()
          ),
          <.div(
            ComponentUtils.testId(DataRoomParticipants, "LinkList"),
            tw.pl12.pr16,
            tw.textGray8.text13.leading20.fontSemiBold,
            Pluralize(
              "active invitation link",
              linkCount,
              inclusive = true
            ),
            Option.when(props.page.groupIdOpt.isDefined)(
              <.span(" associated to this group")
            )
          ),
          <.div(tw.flexNone.borderLeft.borderGray3.hPx24),
          <.div(
            tw.flexNone.pl16,
            Button(
              testId = "Manage links",
              style = Button.Style.Text(),
              tpe = Button.Tpe.Link(
                href = props.router
                  .urlFor(DynamicAuthPage.DataRoomLinkDashboardPage(props.dataRoomData.workflowId))
                  .value
              )
            )("Manage links")
          ),
          <.div(
            tw.flexFill.flex.justifyEnd.itemsStart,
            Button(
              onClick = dismissLinkList(props, state),
              style = Button.Style.Minimal(icon = Some(Icon.Glyph.Cross), color = Button.Color.Primary)
            )()
          )
        )
      }
    }

    private def dismissLinkList(props: Props, state: State) = {
      val newDismissedLinkList = state.dismissedLinkList + (props.userId -> props.dataRoomData.workflowId)
      scope.modState(
        _.copy(dismissedLinkList = newDismissedLinkList),
        AsyncSessionStorage
          .set[DismissedLinkList](SessionStorageKey.DataRoomActiveInvitationLinkListKey, newDismissedLinkList)
      )
    }

    private def renderExpiredPlanWell = {
      <.div(
        tw.flex.itemsCenter.bgDanger1.mt20.py12.rounded4,
        <.div(
          tw.flexNone.textDanger4.pl12.pr8,
          IconR(Icon.Glyph.Info)()
        ),
        <.div(
          tw.flexFill.textGray8.fontNormal.text13.leading20,
          "External guests can no longer access this data room"
        )
      )
    }

    private def renderEmptyState(props: Props, isDataEmpty: Boolean, isSearchEmpty: Boolean) = {
      NonIdealStateR(
        icon = <.img(^.src := "/web/gondor/images/dataroom/users-illustration.svg"),
        title = {
          props.page match {
            case _: DataRoomUnassignedParticipantsPage if isDataEmpty => "This space is empty"
            case _: DataRoomGroupDetailPage if isDataEmpty            => "This group is empty"
            case _: DataRoomGroupDetailPage | _: DataRoomParticipantsUserPage if isSearchEmpty =>
              "No users found"
            case _: DataRoomAllGroupsPage if isSearchEmpty => "No groups found"
            case _                                         => ""
          }
        },
        description = {
          <.div(
            tw.textCenter,
            props.page match {
              case _: DataRoomUnassignedParticipantsPage if isDataEmpty =>
                "There are no unassigned participants to display"
              case _: DataRoomGroupDetailPage if isDataEmpty => "There are no participants to display"
              case _: DataRoomGroupDetailPage | _: DataRoomParticipantsUserPage if isSearchEmpty =>
                React.Fragment(
                  <.p("No users were found matching your search term"),
                  <.p("Try again with the exact user name or email address.")
                )
              case _: DataRoomAllGroupsPage if isSearchEmpty =>
                React.Fragment(
                  <.p("No groups were found matching your search term"),
                  <.p("Try again with the exact group name.")
                )
              case _ => ""
            }
          )
        }
      )()
    }

    private def renderParticipantTable(props: Props, state: State)(userRowDataMap: Map[UserId, DataRoomUserRowData]) = {
      val isPlanExpired = DataRoomPermissionCheck.expiredPlan(props.dataRoomData.dataRoomPlan).isLeft
      val currentAdminCount = DataRoomUserRowData.getAdminCount(userRowDataMap, Map())
      val scopeUserRowData = userRowDataMap
        .filter { case (userId, _) => props.scopeUserIds.contains(userId) }
        .values
        .toSeq
      val searchedUserTermLowerCase = state.searchedTerm.toLowerCase
      val filteredUserRowData = scopeUserRowData.filter(userRowData =>
        userRowData.userInfo.fullName.toLowerCase
          .contains(searchedUserTermLowerCase) || userRowData.userInfo.emailAddress.toLowerCase.contains(
          searchedUserTermLowerCase
        )
      )
      <.div(
        ComponentUtils.testId(DataRoomParticipants, "ParticipantTable"),
        tw.overflowYAuto.hPc100,
        Option.when(isPlanExpired)(renderExpiredPlanWell),
        if (filteredUserRowData.nonEmpty) {
          DataRoomUserTable(
            router = props.router,
            page = props.page,
            dataRoomData = props.dataRoomData,
            actorUserId = props.userId,
            userRowDataList = filteredUserRowData,
            userSelection = userSelectionSnapshot(state),
            currentAdminCount = currentAdminCount,
            refetch = props.refetch,
            shouldShowGroup = props.isInternalUser
          )()
        } else {
          renderEmptyState(
            props,
            scopeUserRowData.isEmpty,
            filteredUserRowData.isEmpty
          )
        }
      )
    }

    private def getToaFileIdOpt(createdState: DataRoomFrontEndState) = {
      val toaOptions = createdState.termsOfAccessOptions
      toaOptions.versions.lastOption.filter(_ => toaOptions.isEnabled)
    }

    private def getCertificateMap(createdState: DataRoomFrontEndState) = {
      val toaFileIdOpt = getToaFileIdOpt(createdState)
      val certificateList = for {
        certificate <- createdState.termsOfAccessCertificates
        if toaFileIdOpt.contains(certificate.toaFileId)
      } yield certificate.userId -> certificate
      certificateList.toMap
    }

    private def getUserRowDataMap(props: Props) = {
      val loggedUserId = props.userId
      val latestState = props.dataRoomData.latestState
      val joinedAdmins = props.dataRoomData.latestState.getJoinedAdmins
      val isLoggedUserAdmin = joinedAdmins.contains(loggedUserId)
      val toaWhitelistedUsers = latestState.dataRoomCreatedState.termsOfAccessOptions.whitelistedUsers
      val certificateMap = getCertificateMap(latestState.dataRoomCreatedState) -- toaWhitelistedUsers
      for {
        (userId, DataRoomUserData(userInfo, teamState, userState)) <- latestState.participatingUsers
      } yield {
        val userRoles = latestState.dataRoomCreatedState.individualRoles.getOrElse(userId, DataRoomRole.Empty)
        val canRemove = teamState match {
          case DataRoomUserData.JoinedUser(_, _) =>
            (joinedAdmins != Set(userId)) && (userId == loggedUserId || isLoggedUserAdmin)
          case DataRoomUserData.InvitedUser(inviter, _, _) =>
            inviter == loggedUserId || isLoggedUserAdmin
        }
        val certificateOpt = certificateMap.get(userId)
        val isToaWhitelisted = toaWhitelistedUsers.contains(userId)
        val isIndividualUser = userState.isIndividualUser
        userId -> DataRoomUserRowData(
          userId,
          userInfo,
          teamState,
          userRoles,
          canRemove,
          certificateOpt,
          isToaWhitelisted,
          isIndividualUser,
          userState.groupIds
        )
      }
    }

    private def renderBatchWidgets(state: State) = {
      <.div(
        ^.width := "360px",
        tw.fixed.bottom0.right0.mr24.mb40,
        BatchInvitationWidget(
          state.invitationInfo,
          isOpened = state.invitationInfo.invitationStatus.nonEmpty,
          onClose = scope.modState(_.copy(invitationInfo = InvitationInfo.Empty))
        )(),
        <.div(tw.my8),
        BatchReminderWidget(
          state.reminderInfo,
          isOpened = state.reminderInfo.reminderStatus.nonEmpty,
          onViewDetails = scope.modState(_.copy(isBatchReminderModalOpened = true)),
          onClose = scope.modState(_.copy(reminderInfo = ReminderInfo.Empty))
        )()
      )
    }

    private def renderSearchBox(props: Props) = {
      Option.when(
        (props.page.canManageUsers && props.scopeUserIds.nonEmpty) ||
          (props.page.isAllGroupsPage && props.dataRoomData.groupMap.nonEmpty)
      ) {
        val searchTarget = if (props.page.canManageUsers) "user" else "group"
        <.div(
          ComponentUtils.testId(DataRoomParticipants, "SearchBox"),
          ^.key := "search-box-" + props.page.toString,
          tw.mlAuto,
          SearchBox(
            placeholder = s"Find $searchTarget...",
            onChange = name => scope.modState(_.copy(searchedTerm = name)),
            width = "280px"
          )()
        )
      }
    }

    def clearSelection: Callback = {
      scope.modState { state =>
        state.copy(
          userSelection = SelectionData.empty,
          groupSelection = SelectionData.empty,
          searchedTerm = ""
        )
      }
    }

    def checkDismissedLinkList: Callback = {
      for {
        dismissedLinkListOpt <- AsyncSessionStorage.get[DismissedLinkList](
          SessionStorageKey.DataRoomActiveInvitationLinkListKey
        )
        _ <- Callback.traverseOption(dismissedLinkListOpt) { dismissedLinkList =>
          scope.modState(_.copy(dismissedLinkList = dismissedLinkList))
        }
      } yield ()
    }

  }

  private val getDefaultState = {
    State(
      SelectionData.empty,
      SelectionData.empty,
      InvitationInfo.Empty,
      ReminderInfo.Empty,
      isBatchReminderModalOpened = false,
      dismissedLinkList = Set(),
      searchedTerm = ""
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(getDefaultState)
    .renderBackend[Backend]
    .componentDidMount(_.backend.checkDismissedLinkList)
    .componentDidUpdate { scope =>
      Callback.when(scope.prevProps.page != scope.currentProps.page) {
        scope.backend.clearSelection
      }
    }
    .build

}
