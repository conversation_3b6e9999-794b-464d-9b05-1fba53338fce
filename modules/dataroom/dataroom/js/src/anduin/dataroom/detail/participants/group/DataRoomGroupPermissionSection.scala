// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.group

import design.anduin.components.field.Field
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.dataroom.DataRoomData
import anduin.dataroom.detail.participants.invitation.PermissionSettings
import anduin.dataroom.detail.participants.permission.{DataRoomAdminRoleDropdown, DataRoomAssetPermissionSection}
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils, Member}
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.service.FileServiceEndpoints.PermissionTarget

private[participants] final case class DataRoomGroupPermissionSection(
  actorUserId: UserId,
  dataRoomData: DataRoomData,
  targetGroupId: Option[DataRoomGroupId],
  settings: StateSnapshotWithModFn[PermissionSettings],
  roleCheck: DataRoomRoleUtils.Check,
  isDisabled: Boolean,
  hasOnlyOneAdmin: Boolean = false
) {

  private val actorRole =
    dataRoomData.latestState.dataRoomCreatedState.individualRoles.getOrElse(actorUserId, DataRoomRole.Empty)

  def apply(): VdomElement = DataRoomGroupPermissionSection.component(this)
}

private[participants] object DataRoomGroupPermissionSection {

  private type Props = DataRoomGroupPermissionSection

  private def render(props: Props) = {
    React.Fragment(
      renderRoleField(props),
      renderPermissionTree(props)
    )
  }

  private def renderRoleField(props: Props) = {
    val readOnlyReasonOpt = for {
      _ <- Either.cond(
        !props.isDisabled,
        (),
        "The role can't be edited\n as it is inherited from the group"
      )
      _ <- Either.cond(
        !props.hasOnlyOneAdmin,
        (),
        "This group's role can't be changed as it would leave your data room without an admin"
      )
    } yield ()
    Field(
      label = Some("Group role"),
      requirement = Field.Requirement.Required,
      id = Some("group-role"),
      labelSize = Field.LabelSize.Px15
    )(
      <.div(
        tw.flex,
        ComponentUtils.testId(DataRoomGroupPermissionSection, "GroupRole"),
        DataRoomAdminRoleDropdown(
          dataRoomWorkflowId = props.dataRoomData.workflowId,
          actorRoles = props.actorRole,
          settings = props.settings,
          nonAdminDefaultChanges = DataRoomAdminRoleDropdown.NonAdminDefaultChanges.Download,
          hasOnlyOneAdmin = props.hasOnlyOneAdmin,
          isActorInviting = false,
          isInviting = false,
          isSeatLimitReached = props.dataRoomData.getRemainingSeatCount(
            groupChanges = props.targetGroupId
              .map { groupId =>
                Map(groupId -> Member())
              }
              .getOrElse(Map.empty)
          ) <= 0,
          creatorEntityName = props.dataRoomData.latestState.creatorEntity.entityModel.name,
          roleCheck = props.roleCheck,
          disabledReasonOpt = readOnlyReasonOpt.left.toOption
        )()
      )
    )
  }

  private def renderPermissionTree(props: Props) = {
    val targetTeamIdOpt = props.targetGroupId.flatMap { groupId =>
      props.dataRoomData.groupMap.get(groupId).map(_.teamId)
    }
    <.div(
      ComponentUtils.testId(DataRoomGroupPermissionSection, "GroupPermissionTree"),
      tw.flex.flexCol.borderBottom.borderGray3,
      ^.height := 456.px,
      DataRoomAssetPermissionSection(
        settings = props.settings,
        dataRoomData = props.dataRoomData,
        permissionTargetOpt = targetTeamIdOpt.map(PermissionTarget.SingleTeam(_)),
        disabledReasonOpt = Option.when(props.isDisabled)("Only admins can modify this group's permissions")
      )()
    )
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
