// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.invitation.permissions

import design.anduin.components.dropdown.Dropdown
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import anduin.dataroom.detail.participants.invitation.{InvitationGroupSettings, PermissionSettings, SettingSection}
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.utils.FileFolderPermissionUtils
import design.anduin.components.util.ComponentUtils
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.dataroom.role.*

private[permissions] final case class SelectRoleSection(
  dataRoomWorkflowId: DataRoomWorkflowId,
  hasViewOnlyPlan: Boolean,
  settings: StateSnapshotWithModFn[PermissionSettings],
  actorRole: DataRoomRole,
  groupSettings: InvitationGroupSettings
) {

  def apply(): VdomElement = SelectRoleSection.component(this)
}

private[permissions] object SelectRoleSection {

  private type Props = SelectRoleSection

  private final case class State(
    showAdminCanInviteWarning: Boolean = false
  )

  private val orSeparator = " or "

  private val RoleDropdown = (new Dropdown[DataRoomRole])()

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomNode = {
      if (DataRoomRoleUtils.isRestricted(props.actorRole)) {
        EmptyVdom
      } else {
        SettingSection(
          title = "Role",
          isRequired = false
        )(renderRoleDropdown(props, state))
      }
    }

    private def renderItem(renderProps: Dropdown.RenderItemProps[DataRoomRole]) = {
      val role = renderProps.item.value
      <.div(
        <.button(
          tw.flex.itemsCenter,
          tw.py8.px20.leading16,
          tw.wPc100.textLeft,
          renderProps.itemTagMods,
          renderProps.defaultIcon,
          <.div(
            tw.ml8,
            <.div(
              tw.text13.leading20.fontSemiBold,
              DataRoomRoleUtils.getName(role)
            ),
            <.div(
              tw.text11.leading16,
              getDescriptions(role).mkReactFragment(EmptyVdom)(
                using <.p(_)
              )
            )
          ),
          <.span(tw.wPx16)
        )
      )
    }

    private def renderRoleDropdown(props: Props, state: State) = {
      <.div(
        ComponentUtils.testId(SelectRoleSection, "RoleDropDown"),
        RoleDropdown(
          value = Some(props.settings.value.role),
          valueToString = DataRoomRoleUtils.getName,
          onChange = role => {
            val showWarningCb = Callback.when(!props.settings.value.canInvite && DataRoomRoleUtils.isAdmin(role))(
              scope.modState(_.copy(showAdminCanInviteWarning = true))
            )
            val clearWarningCb =
              Callback.when(state.showAdminCanInviteWarning)(scope.modState(_.copy(showAdminCanInviteWarning = false)))
            props.settings.modState(
              _ =>
                PermissionSettings.default(
                  props.dataRoomWorkflowId,
                  props.hasViewOnlyPlan,
                  props.actorRole,
                  role
                ),
              showWarningCb >> clearWarningCb
            )
          },
          items = getOptions(props),
          button = Dropdown.Button(
            isReadOnly = props.groupSettings.isAddToGroups
          ),
          menu = Dropdown.Menu(
            renderItem = Some(renderItem)
          ),
          itemWidth = 470,
          maxHeight = 480
        )(),
        TagMod.when(state.showAdminCanInviteWarning) {
          <.p(tw.mt8.textGray7, "By default, users with admin role are able to invite participants")
        }
      )
    }

    private def getOptions(props: Props) = {
      val isInternal = DataRoomRoleUtils.isInternal(props.settings.value.role)
      val canInvite = props.settings.value.canInvite
      val options = if (isInternal) {
        Seq[Dropdown.Item[DataRoomRole]](
          Dropdown.Item(Admin()),
          Dropdown.Item(Member(canInvite = canInvite))
        )
      } else {
        Seq[Dropdown.Item[DataRoomRole]](
          Dropdown.Item(Guest(canInvite = canInvite)),
          Dropdown.Item(Restricted(canInvite = canInvite))
        )
      }
      options.map { option =>
        option.copy(isDisabled = !DataRoomRoleUtils.canInviteRoles(Set(option.value))(props.actorRole))
      }
    }

    private def getDescriptions(role: DataRoomRole): Seq[VdomNode] = {
      role match {
        case _: Admin =>
          Seq("Full control over the data room.", "Own all files and folders.")
        case _: Member =>
          Seq("Access to participants.", "Customizable permission on files and folders.")
        case _: Guest =>
          getExternalDescription(isRestricted = false)
        case _: Restricted =>
          getExternalDescription(isRestricted = true)
        case DataRoomRole.Empty =>
          Seq()
      }
    }

    private def renderPermission(permission: FileFolderPermission) = {
      <.span(
        tw.fontSemiBold,
        FileFolderPermissionUtils.getPermissionName(permission)
      )
    }

    private def getExternalDescription(isRestricted: Boolean): Seq[VdomNode] = {
      val permissionList = if (isRestricted) {
        React.Fragment(
          Seq(
            FileFolderPermission.ViewOnly,
            FileFolderPermission.Read
          ).mkReactFragment(
            EmptyVdom,
            orSeparator,
            " only"
          )(
            using renderPermission
          )
        )
      } else {
        React.Fragment(
          Seq(
            FileFolderPermission.ViewOnly,
            FileFolderPermission.Read,
            FileFolderPermission.Write
          ).mkReactFragment(", ")(
            using renderPermission
          ),
          orSeparator,
          renderPermission(FileFolderPermission.Own)
        )
      }
      Seq(
        React.Fragment(
          "Permitted to ",
          permissionList,
          "."
        )
      ) ++ Seq("Can see people they invited and points of contact")
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
