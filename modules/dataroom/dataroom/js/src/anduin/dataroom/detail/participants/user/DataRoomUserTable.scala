// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.user

import design.anduin.components.button.Button
import design.anduin.components.checkbox.Checkbox
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.menu.react.ContextMenuR
import design.anduin.components.portal.PortalWrapper
import design.anduin.components.table.Table
import design.anduin.components.tag.Tag
import design.anduin.components.tag.react.TagR
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import anduin.component.tooltip.HelperTooltip
import anduin.component.util.JsDateFormatterUtils
import anduin.component.util.JsDateFormatterUtils.JsDateFormat
import anduin.dataroom.detail.DataRoomUserInfoRenderer
import anduin.dataroom.detail.participants.group.DataRoomGroupNameTag
import anduin.dataroom.detail.participants.group.DataRoomGroupNameTag.RenderGroupData
import anduin.dataroom.detail.participants.reminder.RemindInvitationButton
import anduin.dataroom.detail.participants.user.DataRoomUserTable.Column
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.{DataRoomData, DataRoomPermissionCheck, DataRoomUserData}
import anduin.file.explorer.header.FileHeaderActions
import anduin.layout.SelectionData
import anduin.model.common.user.UserId
import anduin.utils.StateSnapshotWithModFn
import stargazer.model.routing.{DynamicAuthPage, Page}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.dataroom.role.*
import anduin.stargazer.service.dataroom.*
import com.anduin.stargazer.client.utils.ZIOUtils

private[participants] final case class DataRoomUserTable(
  router: RouterCtl[Page],
  page: DynamicAuthPage.DataRoomParticipantsPage,
  dataRoomData: DataRoomData,
  actorUserId: UserId,
  userRowDataList: Seq[DataRoomUserRowData],
  userSelection: StateSnapshotWithModFn[SelectionData[UserId]],
  currentAdminCount: Int,
  refetch: Callback,
  shouldShowGroup: Boolean = false
) {

  private val createdState = dataRoomData.latestState.dataRoomCreatedState
  private val isToaEnabled = createdState.termsOfAccessOptions.isEnabled
  private val isActorInternal = dataRoomData.latestState.isUserRole(actorUserId)(DataRoomRoleUtils.isInternal)
  private val isAdmin = dataRoomData.latestState.isUserRole(actorUserId)(DataRoomRoleUtils.isAdmin)
  private val shouldShowCheckbox = isAdmin

  private val columns = Seq(
    if (shouldShowCheckbox) Seq(Column.Checkbox) else Seq(),
    Seq(
      Column.Info,
      Column.Role
    ),
    if (shouldShowGroup) Seq(Column.Group) else Seq(),
    Seq(Column.CanInvite),
    if (isToaEnabled && isActorInternal) Seq(Column.ToaStatus) else Seq(),
    Seq(Column.LastActive, Column.Action)
  ).flatten

  def apply(): VdomElement = {
    DataRoomUserTable.component(this)
  }

}

private[participants] object DataRoomUserTable {

  private type Props = DataRoomUserTable

  private val ParticipantTable = (new Table[DataRoomUserRowData])()

  private sealed trait Column derives CanEqual

  private object Column {
    case object Checkbox extends Column
    case object Info extends Column
    case object Role extends Column
    case object Group extends Column
    case object CanInvite extends Column
    case object ToaStatus extends Column
    case object LastActive extends Column
    case object Action extends Column
  }

  private def isActorRole(props: Props)(check: DataRoomRoleUtils.Check) = {
    props.dataRoomData.latestState.dataRoomCreatedState.individualRoles
      .get(props.actorUserId)
      .exists(check)
  }

  private def renderColumnHeader(title: String, centerText: Boolean = false, helperOpt: Option[String] = None) = {
    <.div(
      ComponentUtils.testId(DataRoomUserTable, title),
      tw.flex.flexFill.itemsCenter.textGray8.fontSemiBold.text13.leading20,
      TagMod.when(centerText)(tw.justifyCenter),
      title,
      helperOpt.map { helper =>
        <.span(
          tw.ml4,
          HelperTooltip(size = Icon.Size.Custom(12))(helper)
        )
      }
    )
  }

  private def renderCheckboxHeader(props: Props) = {
    val userIds = props.userRowDataList.map(_.userId).toSet
    val checkCount = userIds.count(props.userSelection.value.contains)
    <.div(
      tw.flexNone.wPx16.mr12,
      Checkbox(
        testId = "CheckboxHeader",
        isChecked = checkCount > 0,
        onChange = { isChecked =>
          props.userSelection.modState { userSelection =>
            if (isChecked) userSelection ++ userIds else userSelection -- userIds
          }
        },
        isIndeterminate = checkCount > 0 && checkCount < userIds.size
      )()
    )
  }

  private def renderCheckbox(props: Props, userRowData: DataRoomUserRowData) = {
    val userId = userRowData.userId
    <.div(
      tw.flexNone.wPx16.mr12,
      ^.onClick ==> (_.stopPropagationCB),
      Checkbox(
        testId = "Checkbox",
        isChecked = props.userSelection.value.contains(userId),
        onChange = { isChecked =>
          props.userSelection.modState { userSelection =>
            userSelection.toggle(userId, Some(isChecked))
          }
        }
      )()
    )
  }

  private def renderInfo(props: Props, userRowData: DataRoomUserRowData) = {
    DataRoomUserInfoRenderer(
      userId = userRowData.userId,
      emailAddress = userRowData.userInfo.emailAddress,
      fullName = userRowData.userInfo.fullName,
      renderStar = Option.when(userRowData.userId == props.createdState.creatorUserId)("Data room creator")
    )()
  }

  private def renderRole(userRowData: DataRoomUserRowData) = {
    <.div(
      ComponentUtils.testId(DataRoomUserTable, "UserRole"),
      DataRoomRoleUtils.getName(userRowData.userRoles)
    )
  }

  private def renderGroup(props: Props)(userRowData: DataRoomUserRowData) = {
    DataRoomGroupNameTag(
      groups = props.dataRoomData.getGroupsOfUser(userRowData.userId).map { groupData =>
        RenderGroupData(groupData.id, groupData.name, groupData.isDeleted)
      }
    )()
  }

  private def withPlanCheck(props: Props)(renderTarget: Boolean => VdomNode) = {
    FileHeaderActions.renderButtonWithTooltip {
      DataRoomPermissionCheck.expiredPlan(props.dataRoomData.dataRoomPlan)
    } { res =>
      renderTarget(res.isEmpty)
    }
  }

  private def renderCanInvite(userRowData: DataRoomUserRowData) = {
    val canInvite = DataRoomRoleUtils.canInvite(userRowData.userRoles)
    <.div(
      tw.flex.itemsCenter.justifyCenter,
      <.span(
        ComponentUtils.testId(DataRoomUserTable, "CanInvite"),
        tw.flexNone,
        TooltipR(
          renderTarget = <.span(
            if (canInvite) {
              tw.textSuccess4
            } else {
              tw.textGray4
            },
            IconR(name = if (canInvite) Icon.Glyph.Check else Icon.Glyph.Minus)()
          ),
          renderContent = _(
            if (canInvite) {
              "This user can invite other participants"
            } else {
              "This user doesn't have permission to invite other participants"
            }
          )
        )()
      )
    )
  }

  private def renderToaStatus(userData: DataRoomUserRowData) = {
    <.div(
      tw.flex.justifyCenter,
      <.div(
        ComponentUtils.testId(DataRoomUserTable, "TOA"),
        tw.wFit,
        TagMod.when(userData.certificateOpt.nonEmpty)(tw.cursorPointer),
        userData.certificateOpt.fold {
          if (userData.isToaWhitelisted) {
            TagR(color = Tag.Light.Warning, label = "Not applicable")()
          } else {
            TagR(color = Tag.Light.Gray, label = "Pending")()
          }
        } { certificate =>
          val timestamp = certificate.timestamp
            .map(ts => JsDateFormatterUtils.format(ts, JsDateFormatterUtils.JsDateFormat.LongDateShortTimePattern))
            .getOrElse("")
          TooltipR(
            renderTarget = TagR(color = Tag.Light.Success, label = "Accepted")(),
            renderContent = _(s"Accepted on $timestamp")
          )()
        }
      )
    )
  }

  private def onClickCancelInvitation(props: Props, userRowData: DataRoomUserRowData) = {
    ZIOUtils.toReactCallbackUnit {
      for {
        _ <- DataRoomEndpointClient.removeUsers(
          RemoveUsersFromDataRoomParams(
            dataRoomWorkflowId = props.dataRoomData.workflowId,
            userIds = Set(userRowData.userId),
            doNotNotifyByEmail = true
          )
        )
      } yield ()
    } >> Toast.successCallback("Invitation canceled")
  }

  private def renderJoinDate(props: Props, userRowData: DataRoomUserRowData): VdomNode = {
    userRowData.teamState match {
      case DataRoomUserData.JoinedUser(inviter, joinedAt) =>
        val createdState = props.dataRoomData.latestState.dataRoomCreatedState
        val linkOpt = for {
          linkId <- createdState.usersJoiningViaLinkInvitation.get(userRowData.userId)
          linkData <- createdState.linkInvitationMap.get(linkId)
        } yield <.div(
          ComponentUtils.testId(DataRoomUserTable, "LinkInvited"),
          tw.flex.itemsCenter,
          "Invited via link",
          <.span(
            tw.ml4,
            HelperTooltip(
              size = Icon.Size.Custom(12)
            )(linkData.name)
          )
        )
        val inviterOpt: Option[VdomElement] = for {
          inviterData <- props.dataRoomData.latestState.getUserInfo(inviter)
        } yield <.span(
          ComponentUtils.testId(DataRoomUserTable, "UserInvited"),
          s"Invited by ${if (userRowData.userId == inviter) "Anduin Support" else inviterData.fullName}"
        )
        val detailJoinedStatusOpt: Option[VdomElement] =
          linkOpt.orElse(inviterOpt).filterNot(_ => userRowData.userId == createdState.creatorUserId)
        Option.when(props.actorUserId == userRowData.userId || !isActorRole(props)(DataRoomRoleUtils.isRestricted)) {
          <.div(
            <.p(
              ComponentUtils.testId(DataRoomUserTable, "joinedDate"),
              "Joined ",
              joinedAt.map(JsDateFormatterUtils.format(_, JsDateFormat.LongDatePattern1)).getOrElse("")
            ),
            detailJoinedStatusOpt.map { detail =>
              <.div(
                tw.textGray7.text11.leading16,
                detail
              )
            }
          )
        }
      case DataRoomUserData.InvitedUser(inviter, invitedAt, remindedAt) =>
        val inviterOpt = for {
          inviterData <- props.dataRoomData.latestState.getUserInfo(inviter)
        } yield s"Invited by ${if (userRowData.userId == inviter) "Anduin Support" else inviterData.fullName}"
        React.Fragment(
          TooltipR(
            renderTarget = remindedAt.fold {
              <.span(
                ComponentUtils.testId(DataRoomUserTable, "InvitedDate"),
                "Invited ",
                invitedAt.map(JsDateFormatterUtils.format(_, JsDateFormat.LongDatePattern1)).getOrElse("")
              )
            } { remindedAt =>
              <.span(
                ComponentUtils.testId(DataRoomUserTable, "RemindedDate"),
                "Reminded ",
                JsDateFormatterUtils.format(remindedAt, JsDateFormat.LongDatePattern1)
              )
            },
            targetWrapper = PortalWrapper.Inline,
            renderContent = _(inviterOpt),
            isDisabled = inviterOpt.isEmpty
          )(),
          <.div(
            ^.onClick ==> (_.stopPropagationCB),
            tw.flex.whitespacePre.text11,
            List(
              Option.when(isActorRole(props)(DataRoomRoleUtils.isInternal) || inviter == props.actorUserId) {
                withPlanCheck(props) { isDisabled =>
                  RemindInvitationButton(
                    dataRoomWorkflowId = props.dataRoomData.workflowId,
                    isDisabled = isDisabled,
                    inviter = inviter,
                    invitee = userRowData.userId,
                    invitedAt = invitedAt,
                    remindedAt = remindedAt
                  )()
                }
              },
              Option.when(userRowData.canRemove) {
                withPlanCheck(props) { isDisabled =>
                  Button(
                    testId = "InvitationCancel",
                    style = Button.Style.Text(color = Button.Color.Danger),
                    isDisabled = isDisabled,
                    onClick = onClickCancelInvitation(props, userRowData)
                  )("Cancel")
                }
              }
            ).flatten.mkReactFragment(" or ")
          )
        )
    }
  }

  private def getCheckboxColumn(props: Props) = {
    Table.Column[DataRoomUserRowData](
      head = renderCheckboxHeader(props),
      render = entry => Table.Cell(renderCheckbox(props, entry)),
      width = "40px"
    )
  }

  private def getInfoColumn(props: Props) = {
    Table.Column[DataRoomUserRowData](
      head = renderColumnHeader("User"),
      render = entry => Table.Cell(renderInfo(props, entry)),
      sortBy = Table.ColumnOrdering(_.userInfo.getDisplayName.toLowerCase)
    )
  }

  private def getRoleColumn(props: Props) = {
    Table.Column[DataRoomUserRowData](
      head = renderColumnHeader(
        "Role",
        helperOpt = Option.when(
          props.page.groupIdOpt.isDefined || (props.page.isAllUsersPage && props.dataRoomData.groupMap.nonEmpty)
        ) {
          "Participants in multiple groups will receive the highest role and access level across all their groups"
        }
      ),
      render = entry =>
        Table.Cell(
          renderRole(
            entry
          )
        ),
      width = "10%",
      sortBy = if (props.page.groupIdOpt.isEmpty) {
        Table.ColumnOrdering(entry =>
          DataRoomRoleUtils.getReversedLevel(entry.userRoles) -> entry.userInfo.getDisplayName.toLowerCase
        )
      } else {
        Table.ColumnOrderingEmpty()
      }
    )
  }

  private def getGroupColumn(props: Props) = {
    Table.Column[DataRoomUserRowData](
      head = renderColumnHeader("Group"),
      render = entry => Table.Cell(renderGroup(props)(entry)),
      width = "20%",
      sortBy = Table.ColumnOrdering(entry =>
        props.dataRoomData.getGroupsOfUser(entry.userId).headOption.map(_.name).getOrElse("Unassigned")
      )
    )
  }

  private def getCanInviteColumn = {
    Table.Column[DataRoomUserRowData](
      head = renderColumnHeader(
        "Can invite",
        centerText = true
      ),
      render = entry => Table.Cell(renderCanInvite(entry)),
      width = "10%"
    )
  }

  private def getToaStatusColumn = {
    Table.Column[DataRoomUserRowData](
      head = renderColumnHeader(
        "Terms of Access",
        centerText = true
      ),
      render = entry => Table.Cell(renderToaStatus(entry)),
      width = "15%"
    )
  }

  private def getLastActiveColumn(props: Props) = {
    Table.Column[DataRoomUserRowData](
      head = renderColumnHeader("Status"),
      render = entry => Table.Cell(renderJoinDate(props, entry)),
      width = "200px"
    )
  }

  private def getActionColumn(props: Props) = {
    Table.Column[DataRoomUserRowData](
      render = entry =>
        Table.Cell(
          content = DataRoomUserActionColumn(
            router = props.router,
            page = props.page,
            actorUserId = props.actorUserId,
            dataRoomData = props.dataRoomData,
            row = entry,
            userSelection = props.userSelection,
            currentAdminCount = props.currentAdminCount,
            refetch = props.refetch
          )()
        ),
      width = "48px"
    )
  }

  private def getColumns(props: Props) = {
    props.columns.map {
      case Column.Checkbox   => getCheckboxColumn(props)
      case Column.Info       => getInfoColumn(props)
      case Column.Role       => getRoleColumn(props)
      case Column.Group      => getGroupColumn(props)
      case Column.CanInvite  => getCanInviteColumn
      case Column.ToaStatus  => getToaStatusColumn
      case Column.LastActive => getLastActiveColumn(props)
      case Column.Action     => getActionColumn(props)
    }
  }

  private def getRows(props: Props) = {
    props.userRowDataList.sortBy { user =>
      (user.userId != props.actorUserId) -> user.userInfo.fullName.toLowerCase
    }
  }

  private def renderUserActionMenu(props: Props, rows: List[DataRoomUserRowData], closeToggle: Callback) = {
    DataRoomUserActionMenu(
      router = props.router,
      page = props.page,
      actorUserId = props.actorUserId,
      dataRoomData = props.dataRoomData,
      rows = rows,
      userSelection = props.userSelection,
      currentAdminCount = props.currentAdminCount,
      refetch = props.refetch,
      onClose = closeToggle
    )()
  }

  private def onContextMenu(
    renderContextMenuTarget: ContextMenuR.RenderTarget,
    props: Props,
    row: DataRoomUserRowData
  ) = {
    Callback.when(props.shouldShowCheckbox) {
      val selection = props.userSelection.value
      for {
        newRows <-
          if (selection.contains(row.userId)) {
            CallbackTo.pure {
              props.userRowDataList.toList.filter(row => selection.contains(row.userId))
            }
          } else {
            props.userSelection.setState(SelectionData.single(row.userId)).map(_ => List(row))
          }
        userActions = DataRoomUserActionMenu.getUserActions(
          props.dataRoomData,
          props.actorUserId,
          newRows
        )
        _ <- Callback.when(userActions.nonEmpty) {
          renderContextMenuTarget.setContent(
            renderUserActionMenu(
              props,
              newRows,
              renderContextMenuTarget.onCloseMenu
            )
          )
        }
      } yield ()
    }
  }

  private def renderRow(
    renderContextMenuTarget: ContextMenuR.RenderTarget,
    props: Props
  ): Table.RenderRow[DataRoomUserRowData] = { rows => (key, cells, row) =>
    Table.defaultRenderRow(rows)(
      key,
      cells,
      row
    )(
      renderContextMenuTarget.handleContextMenu(
        onContextMenu(
          renderContextMenuTarget,
          props,
          row
        )
      ),
      TagMod.when(isActorRole(props)(DataRoomRoleUtils.isAdmin)) {
        TagMod(
          ^.onClick --> props.userSelection.setState(SelectionData.single(row.userId)),
          ^.onClickCapture ==> { e =>
            if (e.shiftKey) {
              e.preventDefaultCB >> e.stopPropagationCB >> props.userSelection.modState(
                _.range(rows.map(_.userId).toIndexedSeq, row.userId)
              )
            } else if (e.metaKey || e.ctrlKey) {
              e.preventDefaultCB >> e.stopPropagationCB >> props.userSelection.modState(_.toggle(row.userId))
            } else {
              Callback.empty
            }
          },
          tw.cursorPointer.trnsA
        )
      },
      TagMod.when(props.userSelection.value.contains(row.userId)) {
        tw.bgPrimary1.bgOpacity40.hover(tw.bgPrimary1.bgOpacity40)
      }
    )
  }

  private def render(props: Props) = {
    val sortColumn = if (props.page.groupIdOpt.isEmpty) {
      props.columns.indexOf(Column.Role)
    } else {
      props.columns.indexOf(Column.Info)
    }
    ContextMenuR(
      renderContent = None,
      renderTarget = renderContextMenuTarget => {
        <.div(
          tw.selectNone,
          ParticipantTable(
            columns = getColumns(props),
            rows = getRows(props),
            getKey = _.userId.idString,
            style = Table.Style.Minimal,
            headIsSticky = Some(Table.Sticky()),
            renderRow = renderRow(renderContextMenuTarget, props),
            sortColumn = Some(sortColumn),
            sortIsAsc = true
          )()
        )
      }
    )()
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
