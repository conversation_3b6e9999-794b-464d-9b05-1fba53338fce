// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.group

import anduin.dataroom.detail.participants.group.DataRoomGroupNameTag.RenderGroupData
import anduin.file.TooltipOnTruncate
import anduin.id.dataroom.DataRoomGroupId
import design.anduin.components.avatar.ColorGenerator
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.responsive.react.ListR
import design.anduin.components.tag.Tag
import design.anduin.components.tag.react.TagR
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.ScalaComponent
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class DataRoomGroupNameTag(
  groups: Seq[RenderGroupData],
  maxLines: Int = 1,
  unassignedLabel: String = "Unassigned"
) {

  def apply(): VdomNode = DataRoomGroupNameTag.component(this)

}

object DataRoomGroupNameTag {

  private type Props = DataRoomGroupNameTag

  final case class RenderGroupData(
    groupId: DataRoomGroupId,
    groupName: String,
    isDeleted: Boolean = false,
    isNew: Boolean = false
  )

  private val maxTagWidth = 150.px

  private def nameStyle(boldName: Boolean = false) =
    TagMod(
      if (boldName) {
        tw.text13.leading20.fontSemiBold
      } else {
        tw.text11.leading16
      }
    )

  def renderSingleGroup(data: RenderGroupData, hasBorder: Boolean = true, boldName: Boolean = false): VdomElement = {
    if (data.isDeleted) {
      TooltipR(
        renderTarget = <.div(
          ^.maxWidth := maxTagWidth,
          ^.textOverflow.ellipsis,
          tw.truncate,
          TagR(label = data.groupName, color = Tag.Light.Danger)()
        ),
        renderContent = _("Group deleted")
      )()
    } else {
      <.div(
        tw.flex.itemsCenter,
        nameStyle(boldName),
        TagMod.when(hasBorder)(
          TagMod(
            tw.borderAll.border1.rounded3.px6.py2,
            if (data.isNew) tw.borderPrimary2.bgPrimary1 else tw.borderGray3.bgGray0
          )
        ),
        ^.maxWidth := maxTagWidth,
        <.div(
          tw.flexNone,
          ^.color := ColorGenerator.apply(data.groupId.idString),
          IconR(
            name = Icon.Glyph.Square,
            size = Icon.Size.Custom(12)
          )()
        ),
        <.div(
          tw.ml4.itemsCenter.flexFill,
          TooltipOnTruncate(
            renderTarget = <.div.withRef(_)(
              tw.truncate,
              ^.textOverflow.ellipsis,
              data.groupName
            ),
            content = data.groupName
          )()
        )
      )
    }
  }

  private def render(props: Props): VdomNode = {
    if (props.groups.isEmpty) {
      <.span(
        tw.textGray7,
        props.unassignedLabel
      )
    } else {
      ListR(
        items = props.groups
          .map(renderSingleGroup(_))
          .toList,
        maxLines = props.maxLines,
        renderMoreButton = Some(renderProps =>
          <.div(
            tw.flex.itemsCenter.borderAll.border1.borderGray3.rounded3.px6.py2.bgGray0,
            nameStyle(),
            ^.onClick --> renderProps.onClick,
            "+",
            renderProps.numberOfRemainingItems
          )
        )
      )()
    }
  }

  private val component =
    ScalaComponent
      .builder[Props](getClass.getSimpleName)
      .render_P(render)
      .build

}
