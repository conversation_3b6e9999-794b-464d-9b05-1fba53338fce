// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants

import anduin.component.util.JsDateFormatterUtils
import anduin.dataroom.detail.link.ModifyDataRoomLinkInvitationSettings
import anduin.dataroom.detail.participants.group.DataRoomGroupNameTag
import anduin.dataroom.detail.participants.group.DataRoomGroupNameTag.RenderGroupData
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.dataroom.{DataRoomData, DataRoomFrontEndState}
import anduin.file.TooltipOnTruncate
import anduin.id.link.ProtectedLinkId
import anduin.link.ProtectedLinkStateWithEnterpriseLogin
import anduin.model.common.user.UserId
import anduin.orgbilling.SubscriptionContactModal
import anduin.orgbilling.model.plan.BillingProduct
import anduin.scalajs.pluralize.Pluralize
import anduin.stargazer.service.dataroom.{ApproveAccessRequestsParams, DeclineAccessRequestsParams}
import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.react.InitialAvatarR
import design.anduin.components.badge.Badge
import design.anduin.components.badge.react.BadgeR
import design.anduin.components.button.Button
import design.anduin.components.checkbox.Checkbox
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.{Modal, ModalBody, ModalFooterWCancel}
import design.anduin.components.portal.PortalWrapper
import design.anduin.components.tab.deprecated.DeprecatedTab
import design.anduin.components.table.Table
import design.anduin.components.tag.Tag
import design.anduin.components.tag.react.TagR
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import stargazer.model.routing.{DynamicAuthPage, Page}

import scala.scalajs.js.Date
import anduin.dataroom.link.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.anduin.stargazer.client.utils.ZIOUtils

private[participants] final case class DataRoomAccessRequests(
  router: RouterCtl[Page],
  userId: UserId,
  page: DynamicAuthPage.DataRoomParticipantsPage,
  dataRoomData: DataRoomData,
  refetch: Callback
) {
  def apply(): VdomElement = DataRoomAccessRequests.component(this)
}

object DataRoomAccessRequests {
  private type Props = DataRoomAccessRequests

  private case class Request(
    linkId: ProtectedLinkId,
    email: String,
    linkStateWithEnterpriseLogin: ProtectedLinkStateWithEnterpriseLogin,
    linkInvitation: DataRoomFrontEndState.LinkInvitation,
    accessRequest: DataRoomAccessRequest
  ) {
    val rowKey: RowKey = (linkId, email)
  }

  private type RowKey = (ProtectedLinkId, String)

  private case class State(
    selectingRows: Set[RowKey] = Set.empty,
    batchApproveBtnBusy: Boolean = false,
    batchDeclineBtnBusy: Boolean = false,
    singleApproveBusy: Set[RowKey] = Set.empty,
    singleDeclineBusy: Set[RowKey] = Set.empty,
    hasNotifyEmail: Boolean = true
  )

  private sealed trait AccessRequestsTab derives CanEqual {
    def name: String
    def requestFilters(accessRequests: Seq[Request]): Seq[Request]
  }

  private object AccessRequestsTab {

    case object PendingRequests extends AccessRequestsTab {
      val name: String = "Pending requests"

      def requestFilters(accessRequests: Seq[Request]): Seq[Request] = {
        accessRequests.filter(_.accessRequest.status match {
          case _: PendingRequest => true
          case _                 => false
        })
      }

    }

    case object History extends AccessRequestsTab {
      val name: String = "History"

      def requestFilters(accessRequests: Seq[Request]): Seq[Request] = {
        accessRequests.filter(_.accessRequest.status match {
          case _: PendingRequest => false
          case _                 => true
        })
      }

    }

  }

  private val sealLimitReachTooltip =
    "Requests can't be approved because you have no available seats for internal members"

  private val groupDeletedTooltip =
    "Requests can't be approved because associated group was deleted"

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomNode = {
      <.div(
        tw.hPc100.flex.flexFill,
        tw.borderTop.border1.borderGray3,
        DataRoomParticipantsNavigationLayout(
          router = props.router,
          userId = props.userId,
          page = props.page,
          dataRoomData = props.dataRoomData,
          renderContent = renderAccessRequests(props, state)
        )()
      )
    }

    private def renderAccessRequests(props: Props, state: State): VdomElement = {
      val allRequests = getAllAccessRequest(props)
      val pendingRequests = AccessRequestsTab.PendingRequests.requestFilters(allRequests)
      val historyRequests = AccessRequestsTab.History.requestFilters(allRequests)
      <.div(
        tw.hPc100.p24.bgGray0.flex.flexCol,
        <.div(
          tw.flex.itemsCenter,
          <.h3(
            tw.textGray8.fontSemiBold,
            "Access requests"
          )
        ),
        <.div(
          tw.flex.flexCol.flexFill.mt8,
          DeprecatedTab(
            panels = List(
              DeprecatedTab.Panel(
                title = {
                  <.span(
                    ComponentUtils.testId(DataRoomAccessRequest, "Pending_Request"),
                    tw.flex.itemsCenter,
                    AccessRequestsTab.PendingRequests.name,
                    Option.when(pendingRequests.nonEmpty)(
                      <.span(
                        ComponentUtils.testId(DataRoomAccessRequest, "Pending_Request_Num"),
                        tw.ml8,
                        BadgeR(
                          color = Badge.Color.Primary,
                          theme = Badge.Theme.Bold,
                          count = Some(pendingRequests.size)
                        )()
                      )
                    )
                  )
                },
                renderContent = (_: DeprecatedTab.RenderPanelContent) => {
                  renderAccessRequestTab(props, state)(AccessRequestsTab.PendingRequests, pendingRequests)
                }
              ),
              DeprecatedTab.Panel(
                title = s"${AccessRequestsTab.History.name} (${historyRequests.size})",
                renderContent = (_: DeprecatedTab.RenderPanelContent) => {
                  renderAccessRequestTab(props, state)(AccessRequestsTab.History, historyRequests)
                }
              )
            ),
            defaultPanel = 0,
            style = DeprecatedTab.Style.Minimal(isFullHeight = true)
          )()
        )
      )
    }

    private def getAllAccessRequest(props: Props) = {
      for {
        (linkId, linkState) <- props.dataRoomData.latestState.linkStateMap.toSeq
        linkInvitationMap = props.dataRoomData.latestState.dataRoomCreatedState.linkInvitationMap
        linkInvitation <- linkInvitationMap.get(linkId).toSeq
        (email, request) <- linkInvitation.accessRequests
      } yield Request(
        linkId,
        email,
        linkState,
        linkInvitation,
        request
      )
    }

    private def renderAccessRequestTab(
      props: Props,
      state: State
    )(
      currentTab: AccessRequestsTab,
      requests: Seq[Request]
    ) = {
      <.div(
        tw.flex.overflowYAuto.hPc100,
        if (requests.nonEmpty) {
          <.div(
            tw.flex.flexCol.flexFill,
            if (currentTab == AccessRequestsTab.PendingRequests) {
              <.div(
                tw.flex.justifyStart.itemsCenter.pb20,
                renderBatchActions(
                  props,
                  state,
                  requests
                )
              )
            } else {
              EmptyVdom
            },
            <.div(
              renderSeatLimitsWarning(
                props,
                state,
                requests
              )
            ),
            <.div(
              tw.hPc100.overflowYAuto.pb20,
              renderRequestTable(props, state)(currentTab)
            )
          )
        } else {
          renderEmptyRequests(
            if (currentTab == AccessRequestsTab.PendingRequests) {
              "There are no pending requests to show"
            } else {
              "There is no history to show"
            },
            if (currentTab == AccessRequestsTab.PendingRequests) {
              "You'll be notified after someone requests access to the data room"
            } else {
              "You haven't approved or declined any requests yet"
            }
          )
        }
      )
    }

    private def renderSeatLimitsWarning(props: Props, state: State, requests: Seq[Request]) = {
      val availableSeats = props.dataRoomData.getRemainingSeatCount()
      val addingInternalSeats = requests.count { request =>
        state.selectingRows.contains(request.rowKey) && DataRoomRoleUtils.isInternal(
          request.linkInvitation.getRole(props.dataRoomData)
        )
      }
      val hasInternalRequests =
        requests.exists(request => DataRoomRoleUtils.isInternal(request.linkInvitation.getRole(props.dataRoomData)))
      TagMod.when(hasInternalRequests && (availableSeats == 0 || addingInternalSeats > availableSeats)) {
        <.div(
          tw.flex.itemsCenter.py12.px16.mb16,
          ^.backgroundColor := "#F1F3FF",
          <.div(
            tw.mr8,
            ^.color := "#5F6AC8",
            IconR(name = Icon.Glyph.Upgrade)()
          ),
          if (availableSeats == 0) {
            <.div(
              ComponentUtils.testId(DataRoomAccessRequest, "Warning_no_seat_left"),
              tw.flex.itemsCenter,
              "No new",
              <.span(tw.fontSemiBold.mx4, "internal"),
              "team members (Admin or Member roles) can be approved as you don't have any seats available.",
              <.div(
                ComponentUtils.testId(DataRoomAccessRequest, "Purchase_more_seat"),
                tw.ml4,
                SubscriptionContactModal(
                  entityIdOpt = Some(props.dataRoomData.latestState.creatorEntity.entityModel.id),
                  productTypeOpt = Some(BillingProduct.DataRoom),
                  buttonTitle = "Purchase more seats"
                )()
              )
            )
          } else {
            <.div(
              ComponentUtils.testId(DataRoomAccessRequest, "Warning_limited_seat"),
              tw.flex.itemsCenter,
              s"Your plan only has",
              <.span(tw.fontSemiBold.mx4, availableSeats),
              s"available ${Pluralize(
                  "seat",
                  availableSeats,
                  inclusive = false
                )} for internal members.",
              <.div(
                ComponentUtils.testId(DataRoomAccessRequest, "Purchase_add_seat"),
                tw.mx4,
                SubscriptionContactModal(
                  entityIdOpt = Some(props.dataRoomData.latestState.creatorEntity.entityModel.id),
                  productTypeOpt = Some(BillingProduct.DataRoom),
                  buttonTitle = "Purchase additional seats"
                )()
              ),
              "to add more Admins or Members."
            )
          }
        )
      }
    }

    private def renderDeclineAccessRequestsModal(
      emails: Set[String],
      hasNotifyEmail: Boolean,
      isBusy: Boolean,
      onDecline: Callback,
      closeModal: Callback
    ) = {
      val pronoun = if (emails.size > 1) "these" else "this"
      val requestStr = Pluralize("request", emails.size)
      val firstEmailOpt = emails.headOption
      val restEmails = TagMod.when(emails.size > 1) {
        <.span(
          " and ",
          TooltipR(
            targetWrapper = PortalWrapper.Inline,
            renderTarget = Button(style = Button.Style.Text())(s"${emails.tail.size} others"),
            renderContent = _(
              tw.textLeft,
              emails.tail.map(email => <.div(email)).toVdomArray
            )
          )()
        )
      }
      firstEmailOpt.map { firstEmail =>
        React.Fragment(
          ModalBody()(
            <.div(
              <.div(
                tw.flexNone.text13.textGray8.leading20,
                <.p(s"Are you sure you want to decline $pronoun access $requestStr?"),
                <.div(
                  ComponentUtils.testId(DataRoomAccessRequest, "Decline_notification"),
                  tw.my12,
                  s"If you decline $pronoun $requestStr, ",
                  <.span(tw.fontSemiBold, firstEmail),
                  restEmails,
                  " won't be able to access the data room."
                )
              ),
              Checkbox(
                testId = "DataRoomAccessRequests_Notify_Checkbox",
                isChecked = !hasNotifyEmail,
                onChange = isChecked => scope.modState(_.copy(hasNotifyEmail = !isChecked))
              )(
                <.div(
                  tw.inlineFlex,
                  <.div(
                    tw.mr8,
                    "Don't notify them by email"
                  ),
                  TooltipR(
                    renderTarget = <.div(
                      tw.textGray5.py2,
                      IconR(Icon.Glyph.Question, size = Icon.Size.Px16)()
                    ),
                    renderContent = _(
                      "Check this box if you don't want to notify these people that their access requests were declined"
                    )
                  )()
                )
              )
            )
          ),
          ModalFooterWCancel(cancel = closeModal)(
            Button(
              style = Button.Style.Full(color = Button.Color.Primary, isBusy = isBusy),
              onClick = onDecline >> closeModal
            )("Decline")
          )
        )
      }
    }

    private def renderBatchActions(props: Props, state: State, requests: Seq[Request]) = {
      val availableSeats = props.dataRoomData.getRemainingSeatCount()
      val selectingRequests = requests.filter(request => state.selectingRows.contains(request.rowKey))
      val addingInternalSeats = selectingRequests.count { request =>
        DataRoomRoleUtils.isInternal(request.linkInvitation.getRole(props.dataRoomData))
      }
      val passSeatsCheck = availableSeats >= addingInternalSeats
      val canApproveAll = selectingRequests.forall { request =>
        getApproveDisabledReasonOpt(props)(request).isEmpty
      }
      val tooltip = if (state.selectingRows.isEmpty) {
        "Select requests to enable batch actions"
      } else if (!passSeatsCheck) {
        sealLimitReachTooltip
      } else if (!canApproveAll) {
        groupDeletedTooltip
      } else {
        ""
      }
      React.Fragment.withKey("batch-actions")(
        <.div(
          ComponentUtils.testId(DataRoomAccessRequest, "Batch_Approve"),
          tw.mr8,
          TooltipR(
            renderTarget = Button(
              style = Button.Style.Full(
                color = Button.Color.Primary,
                icon = Some(Icon.Glyph.Check),
                isBusy = state.batchApproveBtnBusy
              ),
              isDisabled = state.selectingRows.isEmpty || state.batchDeclineBtnBusy || !passSeatsCheck || !canApproveAll,
              onClick = batchApproveRequests()
            )("Approve"),
            renderContent = _(tooltip),
            isDisabled = tooltip.isEmpty
          )()
        ),
        Modal(
          title = "Decline access requests?",
          renderTarget = openModal =>
            Button(
              testId = "DataRoomAccessRequests_Batch_Decline",
              style = Button.Style.Full(isBusy = state.batchDeclineBtnBusy, icon = Some(Icon.Glyph.Cross)),
              isDisabled = state.selectingRows.isEmpty || state.batchApproveBtnBusy,
              onClick = openModal
            )("Decline"),
          renderContent = closeModal =>
            renderDeclineAccessRequestsModal(
              state.selectingRows.map(_._2),
              state.hasNotifyEmail,
              state.batchDeclineBtnBusy,
              batchDeclineRequests(),
              closeModal
            ),
          size = Modal.Size(width = Modal.Width.Px480)
        )()
      )
    }

    private def renderEmptyRequests(title: String, subtitle: String) = {
      <.div(
        ComponentUtils.testId(DataRoomAccessRequest, "No_Request"),
        tw.flex.flexCol.flexFill.itemsCenter.justifyCenter,
        <.img(
          ^.src := "/web/gondor/images/dataroom/empty-list.svg"
        ),
        <.div(
          tw.mt20.fontSemiBold.text15.leading20,
          title
        ),
        <.div(
          tw.mt4.leading20.textGray7,
          subtitle
        )
      )
    }

    private val RequestTable = (new Table[Request])()

    private def renderRequestTable(props: Props, state: State)(currentTab: AccessRequestsTab) = {
      val rows = currentTab.requestFilters(getAllAccessRequest(props))
      RequestTable(
        columns = if (currentTab == AccessRequestsTab.PendingRequests) {
          Seq(
            checkColumn(state, rows),
            emailColumn(props, state)(currentTab),
            groupColumn(props)(currentTab),
            requestedColumn,
            invitationLinkColumn(props),
            actionColumn(props, state)
          )
        } else {
          Seq(
            emailColumn(props, state)(currentTab),
            groupColumn(props)(currentTab),
            statusColumn(props),
            invitationLinkColumn(props)
          )
        },
        rows = rows,
        getKey = request => request.linkId.idString + request.email,
        style = Table.Style.Minimal,
        sortColumn = if (currentTab == AccessRequestsTab.PendingRequests) Some(2) else Some(1),
        renderRow = { _ => (key, cells, row) =>
          {
            val isSelecting = state.selectingRows.contains(row.rowKey)
            <.tr(
              ^.key := key,
              tw.hover(tw.bgGray1),
              TagMod.when(isSelecting)(tw.bgPrimary1),
              cells
            )
          }
        }
      )()
    }

    private def checkColumn(state: State, rows: Seq[Request]) = {
      val nSelectingRows = state.selectingRows.size
      Table.Column[Request](
        head = Checkbox(
          isChecked = nSelectingRows == rows.size,
          isIndeterminate = 0 < nSelectingRows && nSelectingRows < rows.size,
          onChange =
            isCheck => scope.modState(_.copy(selectingRows = if (isCheck) rows.map(_.rowKey).toSet else Set.empty))
        )(),
        render = request =>
          Table.Cell(
            Checkbox(
              testId = "DataRoomAccessRequests_checkbox",
              isChecked = state.selectingRows.contains(request.rowKey),
              onChange = isCheck =>
                scope.modState(
                  _.copy(selectingRows =
                    if (isCheck) state.selectingRows + request.rowKey else state.selectingRows - request.rowKey
                  )
                )
            )()
          ),
        width = "3%"
      )
    }

    private def emailColumn(props: Props, state: State)(currentTab: AccessRequestsTab) = {
      Table.Column[Request](
        head = "Email",
        render = request =>
          Table.Cell(
            <.div(
              tw.flex.itemsCenter,
              TagMod.when(currentTab == AccessRequestsTab.PendingRequests)(tw.cursorPointer),
              ^.onClick --> {
                val isChecked = state.selectingRows.contains(request.rowKey)
                Callback.when(currentTab == AccessRequestsTab.PendingRequests)(
                  scope.modState(
                    _.copy(selectingRows =
                      if (isChecked) state.selectingRows - request.rowKey else state.selectingRows + request.rowKey
                    )
                  )
                )
              },
              <.div(
                tw.flexNone,
                InitialAvatarR(
                  id = s"${Date.now()}",
                  initials = "",
                  size = InitialAvatar.Size.Px24
                )()
              ),
              <.div(
                tw.fontSemiBold.ml8.flexFill,
                <.div(ComponentUtils.testId(DataRoomAccessRequest, "UserEmail"), request.email),
                <.div(
                  tw.text11.flex.textGray7,
                  <.span(tw.fontSemiBold, "Role:"),
                  <.span(
                    ComponentUtils.testId(DataRoomAccessRequest, "UserRole"),
                    tw.ml4.fontNormal,
                    DataRoomRoleUtils.getName(request.linkInvitation.getRole(props.dataRoomData))
                  )
                )
              ),
              TagMod.when(currentTab == AccessRequestsTab.PendingRequests) {
                request.accessRequest.lastDeclined.map { lastDeclined =>
                  for {
                    declinerName <- props.dataRoomData.latestState.participatingUsers
                      .get(lastDeclined.declinedBy)
                      .map(_.userInfo.fullName)
                    prevRequestDate <- lastDeclined.requestedAt.map(
                      JsDateFormatterUtils.format(_, JsDateFormatterUtils.JsDateFormat.LongDatePattern1)
                    )
                  } yield {
                    <.div(
                      ComponentUtils.testId(DataRoomAccessRequest, "Warning_Old_User"),
                      tw.flexNone.textWarning4,
                      TooltipR(
                        renderTarget = IconR(name = Icon.Glyph.Warning)(),
                        renderContent = _(
                          s"This person already requested access on $prevRequestDate and was declined by $declinerName"
                        )
                      )()
                    )
                  }
                }
              }
            )
          ),
        sortBy = Table.ColumnOrderingCaseInsensitive(_.email),
        width = "27%"
      )
    }

    private def groupColumn(props: Props)(currentTab: AccessRequestsTab) = {
      Table.Column[Request](
        head = "Group",
        render = request => {
          val groupIds = currentTab match {
            case AccessRequestsTab.PendingRequests => request.linkInvitation.groupIds
            case AccessRequestsTab.History         => request.accessRequest.groupIds
          }
          Table.Cell(
            DataRoomGroupNameTag(
              groups = groupIds.toSeq.flatMap { groupId =>
                props.dataRoomData.getGroupData(groupId, includeDeleted = true).map { groupData =>
                  RenderGroupData(groupId, groupData.name, groupData.isDeleted)
                }
              }
            )()
          )
        },
        width = "15%"
      )
    }

    private def requestedColumn = {
      val getRequestedAt = (request: Request) => {
        request.accessRequest.status match {
          case s: PendingRequest         => s.requestedAt
          case s: ApprovedRequest        => s.requestedAt
          case s: DeclinedRequest        => s.requestedAt
          case RequestAccessStatus.Empty => None
        }
      }
      Table.Column[Request](
        head = "Requested",
        render = request =>
          Table.Cell(
            <.div(
              ComponentUtils.testId(DataRoomAccessRequest, "Requested_Date"),
              getRequestedAt(request).map(
                JsDateFormatterUtils.format(_, JsDateFormatterUtils.JsDateFormat.LongDatePattern)
              )
            )
          ),
        sortBy = Table.ColumnOrdering(request => getRequestedAt(request).map(_.toEpochMilli).getOrElse(0L)),
        width = "15%"
      )
    }

    private def invitationLinkColumn(props: Props) = {
      Table.Column[Request](
        head = "Invitation link",
        render = request =>
          Table.Cell(
            <.div(
              ComponentUtils.testId(DataRoomAccessRequest, "Link_Name"),
              TooltipOnTruncate(
                renderTarget = <.div.withRef(_)(
                  tw.truncate,
                  request.linkInvitation.name
                ),
                content = request.linkInvitation.name
              )(),
              ModifyDataRoomLinkInvitationSettings(
                props.router,
                openToggle =>
                  Button(
                    testId = "DataRoomAccessRequests_Link_URL",
                    style = Button.Style.Text(),
                    onClick = openToggle
                  )("Link settings"),
                props.dataRoomData,
                props.userId,
                ModifyDataRoomLinkInvitationSettings.Mode.Update(
                  request.linkId,
                  request.linkInvitation,
                  request.linkStateWithEnterpriseLogin.linkState
                ),
                showManageLink = false,
                onClose = Callback.empty,
                setLink = _ => props.refetch
              )()
            )
          ),
        sortBy = Table.ColumnOrderingCaseInsensitive(_.linkInvitation.name),
        width = "20%"
      )
    }

    private def getApproveDisabledReasonOpt(props: Props)(request: Request): Option[String] = {
      val disabledReason = for {
        _ <- Either.cond(
          !DataRoomRoleUtils.isInternal(request.linkInvitation.getRole(props.dataRoomData)) ||
            props.dataRoomData.getRemainingSeatCount() > 0,
          (),
          sealLimitReachTooltip
        )
        _ <- Either.cond(
          !request.linkInvitation.groupIds.exists(props.dataRoomData.deletedGroups.contains),
          (),
          groupDeletedTooltip
        )
      } yield ()
      disabledReason.left.toOption
    }

    private def actionColumn(props: Props, state: State) = {
      Table.Column[Request](
        head = "Actions",
        render = request => {
          val isDeclining = state.singleDeclineBusy.contains(request.rowKey)
          val isApproving = state.singleApproveBusy.contains(request.rowKey)
          val approvedDisabledReasonOpt = getApproveDisabledReasonOpt(props)(request)
          Table.Cell(
            <.div(
              tw.flex,
              React.Fragment.withKey(s"single-action-${request.rowKey._1}-${request.rowKey._2}")(
                Modal(
                  title = "Decline access request?",
                  renderTarget = openModal =>
                    Button(
                      testId = "DataRoomAccessRequest_UserDeclineBtn",
                      style = Button.Style.Ghost(
                        color = Button.Color.Gray9,
                        isBusy = isDeclining,
                        height = Button.Height.Fix24
                      ),
                      isDisabled = isApproving,
                      onClick = openModal
                    )("Decline"),
                  renderContent = closeModal =>
                    renderDeclineAccessRequestsModal(
                      Set(request.email),
                      state.hasNotifyEmail,
                      isDeclining,
                      onDeclineSingleRequest(request),
                      closeModal
                    ),
                  size = Modal.Size(width = Modal.Width.Px480)
                )(),
                TooltipR(
                  renderTarget = <.div(
                    tw.ml8,
                    Button(
                      testId = "DataRoomAccessRequest_UserApproveBtn",
                      style = Button.Style.Ghost(
                        color = Button.Color.Primary,
                        isBusy = isApproving,
                        height = Button.Height.Fix24
                      ),
                      isDisabled = isDeclining || approvedDisabledReasonOpt.isDefined,
                      onClick = onApproveSingleRequest(request)
                    )("Approve")
                  ),
                  renderContent = _(approvedDisabledReasonOpt.whenDefined),
                  isDisabled = approvedDisabledReasonOpt.isEmpty
                )()
              )
            )
          )
        },
        width = "20%"
      )
    }

    private def statusColumn(props: Props) = {
      Table.Column[Request](
        head = "Status",
        render = request => {
          val (isApproved, actor, timestamp) = request.accessRequest.status match {
            case s: ApprovedRequest => (true, Some(s.approvedBy), s.requestedAt)
            case s: DeclinedRequest => (false, Some(s.declinedBy), s.declinedAt)
            case _                  => (false, None, None)
          }
          val actorName = actor.flatMap(userId => props.dataRoomData.latestState.participatingUsers.get(userId))
          Table.Cell(
            <.div(
              tw.flex.itemsCenter.textGray7,
              <.div(
                ComponentUtils.testId(DataRoomAccessRequest, "Status"),
                TagR(
                  color = if (isApproved) Tag.Light.Success else Tag.Light.Danger,
                  label = if (isApproved) "Approved" else "Declined"
                )()
              ),
              actorName.map { name =>
                <.div(
                  tw.ml4,
                  "by ",
                  <.span(
                    ComponentUtils.testId(DataRoomAccessRequest, "Approver_Name"),
                    tw.fontSemiBold,
                    name.userInfo.fullName
                  )
                )
              },
              timestamp.map { timestamp =>
                <.div(
                  ComponentUtils.testId(DataRoomAccessRequest, "Approved_Date"),
                  tw.ml4,
                  s" (${JsDateFormatterUtils.format(timestamp, JsDateFormatterUtils.JsDateFormat.LongDatePattern)})"
                )
              }
            )
          )
        },
        sortBy = Table.ColumnOrdering(request => {
          val timestamp = request.accessRequest.status match {
            case s: ApprovedRequest => s.approvedAt
            case s: DeclinedRequest => s.declinedAt
            case _                  => None
          }
          timestamp.map(_.toEpochMilli).getOrElse(0L)
        })
      )
    }

    private def getSelectingRequests(props: Props, state: State) = {
      getAllAccessRequest(props)
        .filter(request =>
          state.selectingRows.contains(
            request.rowKey
          ) && request.accessRequest.status.asMessage.sealedValue.isPendingRequest
        )
        .map { request =>
          anduin.stargazer.service.dataroom.DataRoomAccessRequest(request.linkId, request.email)
        }
    }

    private def batchApproveRequests() = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- scope.modState(_.copy(batchApproveBtnBusy = true))
        _ <- ZIOUtils.toReactCallback(
          DataRoomEndpointClient
            .approveAccessRequests(
              ApproveAccessRequestsParams(
                props.dataRoomData.workflowId,
                getSelectingRequests(props, state)
              )
            )
            .map { resp =>
              resp.fold(
                _ => Toast.errorCallback("Failed to approve requests"),
                _ =>
                  scope.modState(
                    _.copy(selectingRows = Set.empty, batchApproveBtnBusy = false),
                    props.refetch >> Toast.successCallback("Requests approved")
                  )
              )
            }
        )
      } yield ()
    }

    private def batchDeclineRequests() = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- scope.modState(_.copy(batchDeclineBtnBusy = true))
        _ <- ZIOUtils.toReactCallback(
          DataRoomEndpointClient
            .declineAccessRequests(
              DeclineAccessRequestsParams(
                props.dataRoomData.workflowId,
                getSelectingRequests(props, state),
                state.hasNotifyEmail
              )
            )
            .map { resp =>
              resp.fold(
                _ => Toast.errorCallback("Failed to decline requests"),
                _ =>
                  scope.modState(
                    _.copy(selectingRows = Set.empty, batchDeclineBtnBusy = false),
                    props.refetch >> Toast.successCallback("Requests declined")
                  )
              )
            }
        )
      } yield ()
    }

    private def onApproveSingleRequest(request: Request) = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- scope.modState(_.copy(singleApproveBusy = state.singleApproveBusy + request.rowKey))
        _ <- ZIOUtils.toReactCallback(
          DataRoomEndpointClient
            .approveAccessRequests(
              ApproveAccessRequestsParams(
                props.dataRoomData.workflowId,
                Seq(anduin.stargazer.service.dataroom.DataRoomAccessRequest(request.linkId, request.email))
              )
            )
            .map { resp =>
              resp.fold(
                _ => Toast.errorCallback("Failed to approve request"),
                _ =>
                  scope.modState(
                    state =>
                      state.copy(
                        selectingRows = state.selectingRows - request.rowKey,
                        singleApproveBusy = state.singleApproveBusy - request.rowKey
                      ),
                    props.refetch >> Toast.successCallback("Request approved")
                  )
              )
            }
        )
      } yield ()
    }

    private def onDeclineSingleRequest(request: Request) = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- scope.modState(_.copy(singleDeclineBusy = state.singleDeclineBusy + request.rowKey))
        _ <- ZIOUtils.toReactCallback(
          DataRoomEndpointClient
            .declineAccessRequests(
              DeclineAccessRequestsParams(
                props.dataRoomData.workflowId,
                Seq(anduin.stargazer.service.dataroom.DataRoomAccessRequest(request.linkId, request.email)),
                state.hasNotifyEmail
              )
            )
            .map { resp =>
              resp.fold(
                _ => Toast.errorCallback("Failed to decline request"),
                _ =>
                  scope.modState(
                    state =>
                      state.copy(
                        selectingRows = state.selectingRows - request.rowKey,
                        singleDeclineBusy = state.singleDeclineBusy - request.rowKey
                      ),
                    props.refetch >> Toast.successCallback("Request declined")
                  )
              )
            }
        )
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
