// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.settings

import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.layout.HResizableStack
import design.anduin.components.list.react.ListR
import design.anduin.components.util.ComponentUtils
import design.anduin.components.wrapper.react.WrapperR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.extra.router.RouterCtl

import anduin.dataroom.DataRoomData
import anduin.dataroom.detail.DataRoomNonAdminRedirector
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.model.common.user.UserId
import stargazer.model.routing.{DynamicAuthPage, Page}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import org.scalajs.dom.HTMLElement

import anduin.dataroom.detail.settings.integplatform.DataRoomIntegrationsSetting
import anduin.dataroom.multiregion.DataRoomMultiRegionButton

private[detail] final case class DataRoomSettingsTab(
  router: RouterCtl[Page],
  page: DynamicAuthPage.DataRoomSettingsPage,
  userId: UserId,
  dataRoomData: DataRoomData,
  refetch: Callback
) {
  def apply(): VdomElement = DataRoomSettingsTab.component(this)
}

object DataRoomSettingsTab {

  private type Props = DataRoomSettingsTab

  private final case class SettingTypes(
    icon: Icon.Name,
    name: String,
    page: DynamicAuthPage.DataRoomSettingsPage
  )

  private def isActorAdmin(props: Props) = {
    props.dataRoomData.latestState.dataRoomCreatedState.individualRoles
      .get(props.userId)
      .exists(_.asMessage.sealedValue.isAdmin)
  }

  private def getSettingPages(props: Props) = {
    val dataRoomWorkflowId = props.page.dataRoomWorkflowId
    List(
      List(
        SettingTypes(
          Icon.Glyph.Cog,
          "General",
          DynamicAuthPage.DataRoomGeneralSettingsPage(dataRoomWorkflowId)
        )
      ),
      if (isActorAdmin(props)) {
        List(
          SettingTypes(
            Icon.Glyph.Link,
            "Invitation links",
            DynamicAuthPage.DataRoomLinkDashboardPage(dataRoomWorkflowId)
          ),
          SettingTypes(
            Icon.Glyph.UserStar,
            "Points of contact",
            DynamicAuthPage.DataRoomContactSettingsPage(dataRoomWorkflowId)
          ),
          SettingTypes(
            Icon.Glyph.Envelope,
            "Email settings",
            DynamicAuthPage.DataRoomEmailSettingsPage(dataRoomWorkflowId)
          ),
          SettingTypes(
            Icon.Glyph.GridAdd,
            "Integrations",
            DynamicAuthPage.DataRoomIntegrationsSettingsPage(dataRoomWorkflowId, addIntegrations = false)
          )
        )
      } else {
        List()
      }
    ).flatten
  }

  private def render(props: Props) = {
    HResizableStack(
      modifier = HResizableStack
        .Modifier(
          sizeConstrain = s => s.sidebarWidth >= 72 & s.sidebarWidth <= 320
        )
        .content((ele: VdomTagOf[HTMLElement]) =>
          ele(
            ComponentUtils.testId(DataRoomSettingsTab, "RightPanel"),
            tw.bgGray0.overflowAuto.p24
          )
        )
        .sidebar((ele: VdomTagOf[HTMLElement]) =>
          ele(
            ComponentUtils.testId(DataRoomSettingsTab, "LeftPanel"),
            tw.bgGray0,
            ^.width := 320.px
          )
        )
        .splitter((ele: VdomTagOf[HTMLElement]) =>
          ele(
            tw.flex.itemsCenter.justifyCenter.bgGray2,
            ^.width := 6.px,
            <.div(
              tw.flexNone.itemsCenter,
              <.span(
                ^.width := 2.px,
                tw.textGray7,
                IconR(name = Icon.Glyph.EllipsisVertical)()
              )
            )
          )
        )
    )(
      HResizableStack.Slots.Sidebar(renderSettingNavigation(props)),
      renderSettingContent(props)
    )
  }

  private def renderSettingNavigation(props: Props) = {
    <.div(
      tw.flex.flexCol.hPc100,
      <.div(
        tw.p16.flexFill,
        ListR(
          items = getSettingPages(props).map { settingType =>
            ListR.Item(
              icon = Option(settingType.icon),
              isSelected = Option(settingType.page == props.page),
              url = props.router.urlFor(settingType.page).value,
              renderContent = () => settingType.name
            )
          }
        )()
      ),
      <.div(
        tw.borderTop.borderGray2.py16.px20,
        DataRoomMultiRegionButton()()
      )
    )
  }

  private def renderSettingContent(props: Props) = {
    props.page match {
      case _: DynamicAuthPage.DataRoomGeneralSettingsPage =>
        DataRoomGeneralSettings(
          props.router,
          props.userId,
          props.dataRoomData,
          props.refetch
        )()
      case _: DynamicAuthPage.DataRoomLinkDashboardPage =>
        DataRoomLinkDashboard(
          props.router,
          props.userId,
          props.dataRoomData
        )()
      case _: DynamicAuthPage.DataRoomContactSettingsPage =>
        DataRoomContactSettings(
          props.router,
          props.userId,
          props.dataRoomData
        )()
      case _: DynamicAuthPage.DataRoomEmailSettingsPage =>
        DataRoomEmailSettings(
          props.router,
          props.userId,
          props.dataRoomData
        )()
      case p: DynamicAuthPage.DataRoomIntegrationsSettingsPage =>
        val showAddIntegration = p.addIntegrations
        WrapperR(
          DataRoomIntegrationsSetting(
            props.router,
            props.userId,
            props.dataRoomData,
            showAddIntegration
          )()
        )()
    }
  }

  private def checkUnauthorized(props: Props) = {
    DataRoomNonAdminRedirector.checkUnauthorized(
      router = props.router,
      dataRoomData = props.dataRoomData,
      userId = props.userId,
      check = props.page match {
        case _: DynamicAuthPage.DataRoomGeneralSettingsPage =>
          DataRoomRoleUtils.isInternal
        case _: DynamicAuthPage.DataRoomLinkDashboardPage =>
          DataRoomRoleUtils.isAdmin
        case _: DynamicAuthPage.DataRoomContactSettingsPage =>
          DataRoomRoleUtils.isAdmin
        case _: DynamicAuthPage.DataRoomEmailSettingsPage =>
          DataRoomRoleUtils.isAdmin
        case _: DynamicAuthPage.DataRoomIntegrationsSettingsPage =>
          DataRoomRoleUtils.isAdmin
      }
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .componentDidMount(scope => checkUnauthorized(scope.props))
    .componentDidUpdate(scope => checkUnauthorized(scope.currentProps))
    .build

}
