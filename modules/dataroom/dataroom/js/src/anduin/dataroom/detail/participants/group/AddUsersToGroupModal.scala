// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.group

import design.anduin.components.button.Button
import design.anduin.components.modal.{<PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, Modal<PERSON>ooter, ModalFooterWCancel}
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import anduin.dataroom.{DataRoomData, DataRoomUserData}
import anduin.dataroom.detail.DataRoomUserInfoRenderer
import anduin.dataroom.detail.documents.FilePermissionColumnPreviewer
import anduin.dataroom.detail.participants.group.DataRoomGroupNameTag.RenderGroupData
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.group.{AddUsersToMultipleGroupsParams, DataRoomGroupData}
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId
import anduin.model.id.FolderId
import anduin.scalajs.pluralize.Pluralize
import anduin.utils.{StateSnapshotWithModFn, StringUtils}
import stargazer.model.routing.Page
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import com.anduin.stargazer.service.FileServiceEndpoints.PermissionTarget
import design.anduin.components.accordion.react.AccordionR
import design.anduin.components.badge.Badge
import design.anduin.components.badge.react.BadgeR
import design.anduin.components.checkbox.Checkbox
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.table.{Table, TableStyle}
import design.anduin.components.util.ComponentUtils
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR
import stargazer.util.HtmlTagUtils.semiBoldText
import com.raquo.laminar.api.L.*

private[participants] final case class AddUsersToGroupModal(
  router: RouterCtl[Page],
  actorUserId: UserId,
  userIds: Set[UserId],
  dataRoomData: DataRoomData,
  renderTarget: Callback => VdomNode,
  onClose: Callback
) {

  private val currentSeatCount = dataRoomData.latestState.participatingUsers.keySet.count(userId =>
    dataRoomData.latestState.isUserRole(userId)(DataRoomRoleUtils.isInternal)
  )

  def apply(): VdomElement = AddUsersToGroupModal.component(this)
}

private[participants] object AddUsersToGroupModal {

  private type Props = AddUsersToGroupModal

  private enum Step {
    case SelectGroups, ReviewChanges
  }

  private final case class State(
    step: Step,
    selectedGroupIds: Set[DataRoomGroupId],
    permissionChanges: AssetPermissionChanges,
    isBusy: Boolean
  )

  private val border: TagMod = tw.border1.borderGray3

  private object RoundedTableStyle extends TableStyle.Full {
    override val table: TagMod = TagMod(border, tw.borderAll.rounded8.overflowHidden)
    override val td: TagMod = TagMod(border, tw.borderTop)
    override val th: TagMod = TagMod(tw.bgGray1)
  }

  private final case class ReviewUserData(userId: UserId, groupIds: Seq[(DataRoomGroupId, Boolean)])

  private val GroupSelectionTable = (new Table[DataRoomGroupData])()
  private val ReviewUserTable = (new Table[ReviewUserData])()

  private class Backend(scope: BackendScope[Props, State]) {

    private val permissionChangesStateSnapshot = StateSnapshotWithModFn.withReuse
      .zoom[State, AssetPermissionChanges](_.permissionChanges)(changes => _.copy(permissionChanges = changes))
      .prepareVia(scope)

    def render(props: Props, state: State): VdomNode = {
      val targetGroups = props.dataRoomData.groupMap.values.toSeq
        .sortBy(_.name.toLowerCase)
      Modal(
        title = state.step match {
          case Step.SelectGroups  => "Add to groups"
          case Step.ReviewChanges => "Review changes"
        },
        renderTarget = props.renderTarget,
        renderContent = closeToggle => {
          val onClose = scope.setState(defaultState, closeToggle >> props.onClose)
          state.step match {
            case Step.SelectGroups  => renderSelectGroups(props, state, targetGroups)(onClose)
            case Step.ReviewChanges => renderReviewChanges(props, state)(onClose)
          }
        },
        afterUserClose = scope.setState(defaultState, props.onClose),
        size = Modal.Size(Modal.Width.Px720)
      )()
    }

    private def renderSelectGroups(
      props: Props,
      state: State,
      targetGroups: Seq[DataRoomGroupData]
    )(
      onClose: Callback
    ) = {
      val externalCount =
        props.userIds.count(userId => props.dataRoomData.latestState.isUserRole(userId)(DataRoomRoleUtils.isExternal))
      val (alreadyMemberGroups, restGroups) = targetGroups.partition { groupData =>
        groupData.participants.toSet.intersect(props.userIds).size == props.userIds.size
      }
      val (exceedSeatLimitGroups, validGroups) = restGroups.partition { groupData =>
        DataRoomRoleUtils.isInternal(
          groupData.role
        ) && (externalCount > 0) && (props.currentSeatCount + externalCount > props.dataRoomData.dataRoomPlan.totalSeats)
      }
      React.Fragment(
        ModalBody()(
          <.div(
            tw.flexCol,
            List(
              if (validGroups.nonEmpty) {
                Some(renderGroupTable(props, state, validGroups))
              } else {
                Some(
                  <.div(
                    s"You can’t add ${Pluralize("participant", props.userIds.size, true)} to any groups due to ${
                        if (alreadyMemberGroups.nonEmpty) "existing memberships" else ""
                      }${if (alreadyMemberGroups.nonEmpty && exceedSeatLimitGroups.nonEmpty) " or " else ""}${
                        if (exceedSeatLimitGroups.nonEmpty) "seat limit restrictions" else ""
                      }"
                  )
                )
              },
              Option.when(alreadyMemberGroups.nonEmpty)(
                renderInvalidGroups(
                  "Groups all selected participants already belongs to",
                  alreadyMemberGroups,
                  isExpanded = validGroups.isEmpty
                )
              ),
              Option.when(exceedSeatLimitGroups.nonEmpty)(
                renderInvalidGroups(
                  "Groups with insufficient seat limit",
                  exceedSeatLimitGroups,
                  isExpanded = validGroups.isEmpty
                )
              )
            ).flatten.mkReactFragment(<.div(tw.pt16))
          )
        ),
        ModalFooterWCancel(cancel = onClose)(
          Button(
            style = Button.Style.Full(color = Button.Color.Primary),
            isDisabled = state.selectedGroupIds.isEmpty,
            onClick = scope.modState(_.copy(step = Step.ReviewChanges))
          )("Next: Review changes")
        )
      )
    }

    private def renderInvalidGroups(title: String, groups: Seq[DataRoomGroupData], isExpanded: Boolean) = {
      <.div(
        tw.borderTop.borderLeft.borderRight.border1.borderGray3.rounded8.overflowHidden,
        AccordionR(
          initialActiveItem = Option.when(isExpanded)(0),
          items = List(
            AccordionR.Item(
              renderHeader = <.div(
                tw.flex.itemsCenter,
                title,
                <.div(
                  tw.ml8,
                  BadgeR(
                    color = Badge.Color.Gray,
                    theme = Badge.Theme.Light,
                    count = Some(groups.size)
                  )()
                )
              ),
              renderContent = renderGroupNameList(groups)
            )
          )
        )()
      )
    }

    private def renderGroupNameList(groups: Seq[DataRoomGroupData]) = {
      <.div(
        tw.flex.flexWrap.px12.bgGray1.wPc100.gapY12.overflowYAuto,
        ^.paddingTop := 10.px,
        ^.paddingBottom := 10.px,
        ^.maxHeight := 300.px,
        groups.map { group =>
          <.div(
            tw.wPc50,
            DataRoomGroupNameTag
              .renderSingleGroup(
                RenderGroupData(group.id, group.name, group.isDeleted),
                hasBorder = false,
                boldName = true
              )
          )
        }.toVdomArray
      )
    }

    private def renderGroupTable(props: Props, state: State, groups: Seq[DataRoomGroupData]) = {
      val groupIds = groups.map(_.id).toSet
      <.div(
        tw.flexCol,
        <.p(tw.mb16, s"Select the group you'd like to add ${Pluralize("participant", props.userIds.size, true)} to"),
        <.div(
          ^.maxHeight := 400.px,
          tw.overflowYAuto,
          GroupSelectionTable(
            columns = Seq(
              getCheckboxColumn(state, groupIds),
              getNameColumn,
              getRoleColumn,
              getPermissionColumn(props)
            ),
            rows = groups,
            getKey = _.id.idString,
            sortColumn = Some(1),
            style = RoundedTableStyle,
            headIsSticky = Some(Table.Sticky()),
            renderRow = { _ => (key, cells, _) =>
              <.tr(
                ^.key := key,
                ComponentUtils.testId(Table, "Row"),
                tw.group.hover(tw.bgGray1),
                cells
              )
            }
          )()
        )
      )
    }

    private def getReviewUserData(props: Props, state: State) = {
      val changedUsers = props.userIds.flatMap { userId =>
        val currentGroupIds = props.dataRoomData.getGroupsOfUser(userId).map(_.id).toSet
        val allGroupIds = currentGroupIds ++ state.selectedGroupIds
        val newGroupIds = allGroupIds.diff(currentGroupIds)
        Option.when(newGroupIds.nonEmpty)(
          ReviewUserData(userId, newGroupIds.toSeq.map(_ -> true) ++ currentGroupIds.toSeq.map(_ -> false))
        )
      }.toSeq
      val adminCount = props.dataRoomData.getAdminCount(
        userChanges = changedUsers.map { case ReviewUserData(userId, groups) =>
          userId -> DataRoomData.UserChanges(addedGroups = groups.filter(_._2).map(_._1).toSet)
        }.toMap
      )
      if (adminCount > 0) {
        changedUsers -> Set.empty
      } else {
        val joinedAdmins = props.dataRoomData.joinedAdmins
        changedUsers.filterNot(user => joinedAdmins.contains(user._1)) -> joinedAdmins
      }
    }

    private def renderReviewChanges(props: Props, state: State)(onClose: Callback) = {
      val (changedUsers, joinedAdmins) = getReviewUserData(props, state)
      val hasAdminGroup = changedUsers
        .flatMap(_.groupIds.filter(_._2).map(_._1))
        .exists(groupId => props.dataRoomData.getGroupData(groupId).exists(group => DataRoomRoleUtils.isAdmin(group.role)))
      React.Fragment(
        ModalBody()(
          Seq(
            Option.when(changedUsers.nonEmpty) {
              <.div(
                tw.flexCol,
                WellR(
                  style =
                    if (hasAdminGroup) Well.Style.Warning(Some(Icon.Glyph.Info))
                    else Well.Style.Primary(Some(Icon.Glyph.Info))
                )(
                  if (hasAdminGroup) {
                    p(
                      "You’re adding participants to a group with ",
                      b("Admin"),
                      " role. This will grant them ",
                      b("full control over content, permissions, and participants"),
                      " in this data room."
                    )
                  } else {
                    p("When groups have different permission levels, participants will receive the highest level of role and access.")
                  }
                ),
                <.div(
                  tw.mt16,
                  WellR(
                    style = Well.Style.Gray(Some(Icon.Glyph.Info))
                  )(
                    p("Review the final group memberships and permissions for each participant before updating.")
                  )
                ),
                <.div(
                  tw.mt16,
                  renderReviewUserTable(props, state)(changedUsers)
                )
              )
            },
            Option.when(joinedAdmins.nonEmpty)(renderCurrentAdmins(props, joinedAdmins))
          ).flatten.mkReactFragment(<.div(tw.mt16))
        ),
        ModalFooter()(
          <.div(
            tw.flex.flexFill,
            <.div(
              tw.flex,
              Button(
                style = Button.Style.Full(icon = Some(Icon.Glyph.ChevronLeft)),
                onClick = scope.modState(_.copy(step = Step.SelectGroups))
              )("Back")
            ),
            <.div(
              tw.flex.flexFill.justifyEnd,
              <.div(tw.mr8, Button(onClick = onClose)("Cancel")),
              Button(
                style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.isBusy),
                isDisabled = changedUsers.isEmpty,
                onClick = addToGroups(
                  props,
                  state,
                  onClose
                )
              )("Update group membership")
            )
          )
        )
      )
    }

    private def renderReviewUserTable(
      props: Props,
      state: State
    )(
      changedUsers: Seq[ReviewUserData]
    ) = {
      <.div(
        ^.maxHeight := 400.px,
        tw.overflowYAuto,
        ReviewUserTable(
          columns = Seq(
            getUserInfoColumn(props),
            getUserPermissionColumn(props, state),
            getUserGroupColumn(props)
          ),
          rows = changedUsers,
          getKey = _.userId.idString,
          style = RoundedTableStyle,
          headIsSticky = Some(Table.Sticky()),
          renderRow = { _ => (key, cells, _) =>
            <.tr(
              ^.key := key,
              ComponentUtils.testId(Table, "Row"),
              tw.group.hover(tw.bgGray1),
              cells
            )
          }
        )()
      )
    }

    private def renderCurrentAdmins(props: Props, joinedAdmins: Set[UserId]) = {
      <.div(
        tw.flex.flexCol.border1.borderWarning3.rounded8.borderAll.overflowHidden,
        <.div(
          tw.flex.itemsCenter.p12.bgWarning1,
          <.div(
            tw.textWarning4,
            IconR(name = Icon.Glyph.Warning)()
          ),
          <.div(
            tw.ml4,
            s"${Pluralize("participant", joinedAdmins.size, true)} won’t be added to selected groups to maintain ",
            semiBoldText("Admin"),
            " access"
          )
        ),
        <.div(
          tw.flex.flexCol.p12.bgWarning1.bgOpacity40,
          <.div(
            tw.mb4,
            "These users are the only Admins in the data room and aren’t in any groups. Adding them to non-Admin groups would remove their Admin role and leave the room without an Admin."
          ),
          <.div(
            ^.maxHeight := 200.px,
            tw.flex.flexWrap.overflowYAuto,
            joinedAdmins.flatMap { userId =>
              props.dataRoomData.latestState.participatingUsers.get(userId).map { userData =>
                <.div(
                  tw.wPc50.mt12,
                  DataRoomUserInfoRenderer(userId, userData.userInfo.emailAddress, userData.userInfo.fullName)()
                )
              }
            }.toVdomArray
          )
        )
      )
    }

    private def getUserInfoColumn(props: Props) = {
      Table.Column[ReviewUserData](
        head = "User",
        render = entry => {
          val userInfoOpt = props.dataRoomData.latestState.participatingUsers.get(entry.userId)
          Table.Cell(
            DataRoomUserInfoRenderer(
              entry.userId,
              emailAddress = userInfoOpt.map(_.userInfo.emailAddress).getOrElse(""),
              fullName = userInfoOpt.map(_.userInfo.fullName).getOrElse("")
            )()
          )
        }
      )
    }

    private def getUserPermissionColumn(props: Props, state: State) = {
      Table.Column[ReviewUserData](
        head = "",
        render = entry => {
          val teamIds = entry.groupIds
            .map(_._1)
            .flatMap(
              props.dataRoomData.getGroupData(_).map(_.teamId)
            )
            .toSet
          val userInfo = props.dataRoomData.latestState.participatingUsers
            .get(entry.userId)
            .map(_.userInfo)
            .getOrElse(DataRoomUserData.UserInfo("", "", ""))
          val nameWithApostrophe = if (props.actorUserId == entry.userId) {
            "your"
          } else {
            StringUtils.addApostrophe(if (userInfo.fullName.nonEmpty) userInfo.fullName else userInfo.emailAddress)
          }
          Table.Cell(
            <.div(
              tw.flex.flexFill.itemsCenter.invisible.groupHover(tw.visible),
              FilePermissionColumnPreviewer(
                dataRoomWorkflowId = props.dataRoomData.workflowId,
                folderId = FolderId.channelSystemFolderId(props.dataRoomData.workflowId),
                actorUserId = props.actorUserId,
                title = s"$nameWithApostrophe permissions",
                permissionTarget = PermissionTarget.MultipleTeams(teamIds),
                changes = permissionChangesStateSnapshot(state),
                managePermissionTreePageUrl = None,
                showIndex = props.dataRoomData.latestState.dataRoomCreatedState.showIndex
              )()
            )
          )
        }
      )
    }

    private def getUserGroupColumn(props: Props) = {
      Table.Column[ReviewUserData](
        head = "Group",
        render = entry => {
          val groups = entry.groupIds
            .flatMap { (groupId, isNew) =>
              props.dataRoomData.getGroupData(groupId).map(_ -> isNew)
            }
            .map { (groupData, isNew) =>
              RenderGroupData(groupData.id, groupData.name, groupData.isDeleted, isNew)
            }
          Table.Cell(
            DataRoomGroupNameTag(groups, maxLines = 2)()
          )
        }
      )
    }

    private def getCheckboxColumn(state: State, validGroupIds: Set[DataRoomGroupId]) = {
      Table.Column[DataRoomGroupData](
        head = renderCheckboxHeader(state, validGroupIds),
        render = entry => Table.Cell(renderCheckbox(state, entry)),
        width = "40px"
      )
    }

    private def renderCheckboxHeader(state: State, validGroupIds: Set[DataRoomGroupId]) = {
      <.div(
        tw.flexNone.wPx16,
        Checkbox(
          testId = "CheckboxHeader",
          isChecked = state.selectedGroupIds.nonEmpty,
          onChange = { isChecked =>
            scope.modState { state =>
              if (isChecked) state.copy(selectedGroupIds = validGroupIds) else state.copy(selectedGroupIds = Set.empty)
            }
          },
          isIndeterminate = state.selectedGroupIds.nonEmpty && state.selectedGroupIds.size < validGroupIds.size
        )()
      )
    }

    private def renderCheckbox(state: State, entry: DataRoomGroupData) = {
      <.div(
        tw.flexNone.wPx16,
        ^.onClick ==> (_.stopPropagationCB),
        Checkbox(
          testId = "Checkbox",
          isChecked = state.selectedGroupIds.contains(entry.id),
          onChange = { isChecked =>
            scope.modState { s =>
              if (isChecked) s.copy(selectedGroupIds = s.selectedGroupIds + entry.id)
              else s.copy(selectedGroupIds = s.selectedGroupIds - entry.id)
            }
          }
        )()
      )
    }

    private def getNameColumn = {
      Table.Column[DataRoomGroupData](
        head = "Group",
        render = entry =>
          Table.Cell(
            DataRoomGroupInfoRenderer(entry, subtitleOpt = None)()
          ),
        sortBy = Table.ColumnOrdering(_.name.toLowerCase),
        width = "60%"
      )
    }

    private def getRoleColumn = {
      Table.Column[DataRoomGroupData](
        head = "Role",
        render = entry =>
          Table.Cell(
            DataRoomRoleUtils.getName(entry.role)
          ),
        sortBy = Table.ColumnOrdering(entry => DataRoomRoleUtils.getLevel(entry.role))
      )
    }

    private def getPermissionColumn(props: Props) = {
      Table.Column[DataRoomGroupData](
        head = "",
        render = entry =>
          Table.Cell(
            DataRoomGroupPermissionModal(
              actorUserId = props.actorUserId,
              dataRoomData = props.dataRoomData,
              groupData = entry,
              renderTarget = (_, openToggle) =>
                <.div(
                  tw.flex.justifyEnd.itemsCenter.invisible.groupHover(tw.visible),
                  Button(
                    testId = "View group permission",
                    style = Button.Style.Text(),
                    onClick = openToggle
                  )("View permission")
                ),
              isReadOnly = true
            )()
          )
      )
    }

    private def partitionGroupUsers(props: Props, state: State): Map[DataRoomGroupId, Set[UserId]] = {
      val (changedUsers, _) = getReviewUserData(props, state)
      changedUsers
        .flatMap(changes => changes.groupIds.filter(_._2).map(_._1).map(_ -> changes.userId))
        .groupMap(_._1)(_._2)
        .map { (groupId, userIds) =>
          groupId -> userIds.toSet
        }
    }

    private def addToGroups(props: Props, state: State, onClose: Callback): Callback = {
      Callback.when(state.selectedGroupIds.nonEmpty) {
        scope.modState(
          _.copy(isBusy = true),
          ZIOUtils.toReactCallback {
            DataRoomEndpointClient
              .addUsersToGroups(
                AddUsersToMultipleGroupsParams(
                  dataRoomWorkflowId = props.dataRoomData.workflowId,
                  userMap = partitionGroupUsers(props, state)
                )
              )
              .map(resp =>
                scope.modState(
                  _.copy(isBusy = false),
                  resp.fold(
                    _ => Toast.errorCallback("Failed to add participants to groups") >> onClose,
                    _ => Toast.successCallback("Participants successfully added") >> onClose
                  )
                )
              )
          }
        )
      }
    }

  }

  given Reusability[AssetPermissionChanges] = Reusability.by_==

  private val defaultState = State(
    step = Step.SelectGroups,
    selectedGroupIds = Set.empty,
    permissionChanges = AssetPermissionChanges(),
    isBusy = false
  )

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(defaultState)
    .renderBackend[Backend]
    .build

}
