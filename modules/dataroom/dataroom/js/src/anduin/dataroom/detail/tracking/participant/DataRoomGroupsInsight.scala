// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.tracking.participant

import anduin.component.text.SearchBox
import anduin.component.tooltip.HelperTooltip
import anduin.component.util.JsDateFormatterUtils
import anduin.dataroom.DataRoomData
import anduin.dataroom.components.DurationFormatter
import anduin.dataroom.detail.participants.group.DataRoomGroupInfoRenderer
import anduin.dataroom.detail.tracking.{TrackingTargetCriteria, TrackingTargetCriteriaDropdown}
import anduin.id.dataroom.DataRoomGroupId
import design.anduin.components.nonidealstate.react.NonIdealStateR
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.table.Table
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import stargazer.model.routing.DynamicAuthPage

import java.time.{Duration, Instant}
import design.anduin.components.toast.Toast
import stargazer.component.routing.react.WithReactRouterR
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import zio.ZIO
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.service.GeneralServiceException
import anduin.stargazer.service.dataroom.DataRoomGetGroupsInsightsResponse.GroupInsights
import anduin.stargazer.service.dataroom.{DataRoomGetGroupsInsightsResponse, DataRoomGetParticipantsInsightsParams}
import com.anduin.stargazer.client.utils.ZIOUtils
import design.anduin.components.icon.Icon

final case class DataRoomGroupsInsight(
  dataRoomData: DataRoomData
) derives CanEqual {

  def apply(onViewGroupDetail: DataRoomGroupId => Callback): VdomElement =
    DataRoomGroupsInsight.component(
      DataRoomGroupsInsight.Props(
        this,
        onViewGroupDetail
      )
    )

}

object DataRoomGroupsInsight {

  private final case class Props(
    params: DataRoomGroupsInsight,
    onViewGroupDetail: DataRoomGroupId => Callback
  )

  val emptyContentStr = "--"

  private final case class State(
    searchTerm: String = "",
    dataRoomGroupsInsights: Option[DataRoomGetGroupsInsightsResponse] = None
  )

  private final class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      <.div(
        tw.flexCol,
        <.div(
          tw.heading2,
          "Participants"
        ),
        <.div(
          ComponentUtils.testId(DataRoomGroupsInsight, "GroupDropDown"),
          tw.flex.itemsCenter.py16,
          Option.when(props.params.dataRoomData.groupMap.nonEmpty) {
            WithReactRouterR { router =>
              TrackingTargetCriteriaDropdown(
                value = TrackingTargetCriteria.Groups,
                onChange = {
                  case TrackingTargetCriteria.Groups => Callback.empty
                  case TrackingTargetCriteria.Individuals =>
                    router.set(DynamicAuthPage.DataRoomInsightsParticipantsPage(props.params.dataRoomData.workflowId))
                },
                labelOpt = Some("Insights into")
              )()
            }
          },
          <.div(
            ComponentUtils.testId(DataRoomGroupsInsight, "SearchBox"),
            tw.flex.flexFill.justifyEnd,
            SearchBox(
              placeholder = "Find groups",
              onChange = searchTerm => scope.modState(_.copy(searchTerm = searchTerm)),
              width = "280px"
            )()
          )
        ),
        renderTable(props, state)
      )
    }

    private val smallColumnWidth = "14%"

    private val mediumColumnWidth = "18%"

    private val GroupTable = (new Table[GroupInsights])()

    private def getGroupName(props: Props, entry: GroupInsights) = {
      props.params.dataRoomData.getGroupData(entry.groupId).map(_.name).getOrElse("Unassigned")
    }

    private def getGroupParticipants(props: Props)(entry: GroupInsights) = {
      props.params.dataRoomData.getGroupData(entry.groupId).fold(0)(_.participants.size)
    }

    private def getGroupColumn(props: Props): Table.Column[GroupInsights] = {
      Table.Column(
        head = "Group",
        render = renderGroupColumn(props),
        sortBy = Table.ColumnOrdering { entry =>
          getGroupName(props, entry).toLowerCase
        }
      )
    }

    private def getParticipantColumn(props: Props): Table.Column[GroupInsights] = {
      Table.Column(
        head = <.div(
          tw.flex.itemsCenter,
          "Participants",
          <.span(
            tw.ml4,
            HelperTooltip(
              size = Icon.Size.Custom(12)
            )("A user can belong to multiple groups, so counts may exceed total unique users")
          )
        ),
        width = smallColumnWidth,
        render = entry =>
          Table.Cell(
            <.div(ComponentUtils.testId(DataRoomGroupsInsight, "ParticipantNum"), getGroupParticipants(props)(entry))
          ),
        sortBy = Table.ColumnOrdering(getGroupParticipants(props))
      )
    }

    private def getVisitColumn: Table.Column[GroupInsights] = {
      Table.Column(
        head = "Visits",
        render = entry => renderSimpleColumn("Visit", Some(entry.visitCount.toString)),
        width = smallColumnWidth,
        sortBy = Table.ColumnOrdering(_.visitCount)
      )
    }

    private def getAccessColumn(
      totalFileCount: Long
    ): Table.Column[GroupInsights] = {
      Table.Column(
        head = "Access",
        render = entry => renderSimpleColumn("Access", Some(getAccessedPercent(entry, totalFileCount).round.pct)),
        width = smallColumnWidth,
        sortBy = Table.ColumnOrdering(getAccessedPercent(_, totalFileCount))
      )
    }

    private def getTimeSpentColumn: Table.Column[GroupInsights] = {
      Table.Column(
        head = "Total time spent",
        render = renderTimeSpent,
        width = mediumColumnWidth,
        sortBy = Table.ColumnOrdering(totalTimeSpentSortBy)
      )
    }

    private def getLastActionColumn: Table.Column[GroupInsights] = {
      Table.Column(
        head = "Last action",
        render = renderLastAction,
        width = mediumColumnWidth,
        sortBy = Table.ColumnOrdering(lastActionSortBy)
      )
    }

    private def getGroupInsightsRows(
      props: Props,
      state: State,
      dataRoomGroupsInsight: DataRoomGetGroupsInsightsResponse
    ) = {
      dataRoomGroupsInsight.groups.filter { groupInsight =>
        props.params.dataRoomData
          .getGroupData(groupInsight.groupId)
          .exists(
            _.name.toLowerCase.contains(state.searchTerm.toLowerCase)
          )
      }
    }

    private def renderTable(
      props: Props,
      state: State
    ) = {
      state.dataRoomGroupsInsights.fold {
        BlockIndicatorR(isFullHeight = true)()
      } { dataRoomGroupsInsight =>
        val rows = getGroupInsightsRows(
          props,
          state,
          dataRoomGroupsInsight
        )
        <.div(
          ComponentUtils.testId(DataRoomGroupsInsight, "GroupTable"),
          tw.wPc100.overflowYAuto,
          if (rows.nonEmpty) {
            GroupTable(
              getKey = _.groupId.idString,
              style = Table.Style.Minimal,
              rows = rows,
              sortColumn = Some(4),
              sortIsAsc = false,
              headIsSticky = Some(Table.Sticky()),
              columns = Seq(
                getGroupColumn(props),
                getParticipantColumn(props),
                getVisitColumn,
                getAccessColumn(dataRoomGroupsInsight.totalFileCount),
                getTimeSpentColumn,
                getLastActionColumn
              ),
              renderRow = renderRow(props)
            )()
          } else {
            <.div(
              ComponentUtils.testId(DataRoomGroupsInsight, "EmptySearch"),
              ^.height := "70vh",
              NonIdealStateR(
                icon = {
                  <.img(
                    ^.width := "50px",
                    ^.src := "/web/gondor/images/dataroom/search-users-illustration.svg"
                  )
                },
                title = s"No group found",
                description = "Adjust your search criteria and try again"
              )()
            )
          }
        )
      }
    }

    private def renderRow(props: Props): Table.RenderRow[GroupInsights] = { rows => (key, cells, item) =>
      Table.defaultRenderRow(rows)(
        key,
        cells,
        item
      )(
        TagMod(
          ^.onClick --> props.onViewGroupDetail(item.groupId),
          tw.cursorPointer.hover(tw.bgPrimary1.bgOpacity30)
        )
      )
    }

    private def totalTimeSpentSortBy(entry: GroupInsights) = {
      entry.viewDuration.orElse(Some(Duration.ofSeconds(0)))
    }

    private def lastActionSortBy(entry: GroupInsights) = {
      getLastAction(entry).map(_._2).orElse(Some(Instant.MIN))
    }

    private def renderGroupColumn(props: Props)(entry: GroupInsights) = {
      Table.Cell(
        props.params.dataRoomData.getGroupData(entry.groupId, includeDeleted = true).fold(EmptyVdom) { groupData =>
          DataRoomGroupInfoRenderer(groupData, subtitleOpt = None)()
        }
      )
    }

    private def renderSimpleColumn(name: String, contentOpt: Option[String]) = {
      Table.Cell(
        <.div(ComponentUtils.testId(DataRoomGroupsInsight, name), contentOpt.getOrElse[String](emptyContentStr))
      )
    }

    private def renderTimeSpent(entry: GroupInsights) = {
      renderSimpleColumn("TimeSpent", entry.viewDuration.map(DurationFormatter(_)))
    }

    private def getLastAction(entry: GroupInsights) = {
      List(entry.lastView.map("Viewed" -> _), entry.lastDownload.map("Downloaded" -> _)).flatten.maxByOption(_._2)
    }

    private def renderLastAction(entry: GroupInsights) = {
      Table.Cell(
        getLastAction(entry).map { case (action, timestamp) =>
          React.Fragment(
            <.div(
              ComponentUtils.testId(DataRoomGroupsInsight, "LastAction"),
              tw.textGray8.fontSemiBold.text13.leading20,
              action
            ),
            <.div(
              ComponentUtils.testId(DataRoomGroupsInsight, "LastActionDate"),
              tw.textGray7.fontNormal.text11.leading16,
              JsDateFormatterUtils.format(timestamp, JsDateFormatterUtils.JsDateFormat.MonthDateYearTime2)
            )
          )
        }
      )
    }

    private def getAccessedPercent(groupInsights: GroupInsights, totalFile: Long): Double = {
      if (totalFile == 0) {
        100.0
      } else {
        groupInsights.accessedFileCount.toDouble * 100 / totalFile
      }
    }

    def getDataRoomGroupsInsights: Callback = {
      for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback(
          DataRoomEndpointClient
            .getDataRoomGroupsInsights(
              DataRoomGetParticipantsInsightsParams(
                props.params.dataRoomData.workflowId
              )
            )
            .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
            .map(
              _.fold(
                _ =>
                  scope.modState(
                    _.copy(dataRoomGroupsInsights = None),
                    Toast.errorCallback("Failed to load data room groups")
                  ),
                resp => scope.modState(_.copy(dataRoomGroupsInsights = Some(resp)))
              )
            )
        )

      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidMount(_.backend.getDataRoomGroupsInsights)
    .componentDidUpdate { scope =>
      val prevProps = scope.prevProps
      val currProps = scope.currentProps
      Callback.when(prevProps.params != currProps.params) {
        scope.backend.getDataRoomGroupsInsights
      }
    }
    .build

}
