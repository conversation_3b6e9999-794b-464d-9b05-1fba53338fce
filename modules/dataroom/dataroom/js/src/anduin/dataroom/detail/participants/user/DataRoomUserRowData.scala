// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.user

import anduin.dataroom.role.DataRoomRole
import anduin.dataroom.{DataRoomFrontEndState, DataRoomUserData}
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId

private[participants] final case class DataRoomUserRowData(
  userId: UserId,
  userInfo: DataRoomUserData.UserInfo,
  teamState: DataRoomUserData.TeamState,
  userRoles: DataRoomRole,
  canRemove: Boolean,
  certificateOpt: Option[DataRoomFrontEndState.TermsOfAccessCertificate],
  isToaWhitelisted: Boolean,
  isIndividualUser: Boolean,
  groupIds: Seq[DataRoomGroupId]
) derives CanEqual

object DataRoomUserRowData {

  def getAdminCount(users: Map[UserId, DataRoomUserRowData], modified: Map[UserId, DataRoomRole]): Int = {
    users.count { case (userId, userRowData) =>
      userRowData.teamState match {
        case _: DataRoomUserData.JoinedUser =>
          modified.getOrElse(userId, userRowData.userRoles).asMessage.sealedValue.isAdmin
        case _: DataRoomUserData.InvitedUser =>
          false
      }
    }
  }

}
