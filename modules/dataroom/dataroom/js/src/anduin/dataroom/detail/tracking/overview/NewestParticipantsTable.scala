// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.tracking.overview

import java.time.{Instant, ZoneId}
import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.react.InitialAvatarR
import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.nonidealstate.react.NonIdealStateR
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.react.SkeletonR
import design.anduin.components.table.Table
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import anduin.component.util.JsDateFormatterUtils
import anduin.component.util.JsDateFormatterUtils.JsDateFormat
import anduin.dataroom.DataRoomUserData.JoinedUser
import anduin.dataroom.detail.participants.group.DataRoomGroupNameTag
import anduin.dataroom.detail.participants.group.DataRoomGroupNameTag.RenderGroupData
import anduin.dataroom.{DataRoomData, DataRoomUserData}
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId
import stargazer.component.routing.react.WithReactRouterR
import stargazer.model.routing.DynamicAuthPage
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import zio.ZIO
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.service.GeneralServiceException
import anduin.stargazer.service.dataroom.{
  DataRoomGetInsightsNewestParticipantsParams,
  DataRoomGetInsightsNewestParticipantsResponse
}
import com.anduin.stargazer.client.utils.ZIOUtils

private[overview] final case class NewestParticipantsTable(
  startTime: Option[Instant],
  dataRoomData: DataRoomData
) derives CanEqual {

  def apply(onViewUserDetail: UserId => Callback): VdomElement = {
    NewestParticipantsTable.component(
      NewestParticipantsTable.Props(
        this,
        onViewUserDetail
      )
    )
  }

}

private[overview] object NewestParticipantsTable {

  private val MaxRow = 5

  private case class Participant(
    userId: UserId,
    userInfo: DataRoomUserData.UserInfo,
    joinDate: Option[Instant],
    groupIds: Seq[DataRoomGroupId]
  )

  private final case class Props(
    params: NewestParticipantsTable,
    onViewUserDetail: UserId => Callback
  ) {

    val newestParticipants: List[Participant] = {
      val newestUsers = params.dataRoomData.latestState.participatingUsers
        .collect { case (userId, DataRoomUserData(userInfo, JoinedUser(_, joinedAt), userState)) =>
          Participant(
            userId,
            userInfo,
            joinedAt,
            userState.groupIds
          )
        }
        .filter { participant =>
          params.startTime.forall(startTime =>
            participant.joinDate.exists(_.isAfter(startTime)) || participant.joinDate.contains(startTime)
          )
        }
        .toList
        .sortBy(-_.joinDate.map(_.toEpochMilli).getOrElse(0L))
      newestUsers.take(NewestParticipantsTable.MaxRow)
    }

    val numberNewestParticipants: Int = newestParticipants.size
  }

  private final case class State(
    insightsNewestParticipants: Option[DataRoomGetInsightsNewestParticipantsResponse]
  )

  private val NewestParticipantTable = (new Table[Participant])()

  private final case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      <.div(
        if (props.numberNewestParticipants == 0) {
          renderEmptyState()
        } else {
          <.div(
            ComponentUtils.testId(NewestParticipantTable, "NewUserTable"),
            tw.pr32,
            renderTable(props, state),
            TagMod.when(props.numberNewestParticipants > MaxRow)(
              renderViewAllButton(props)
            )
          )
        }
      )
    }

    private def renderEmptyState() = {
      <.div(
        tw.wPc100,
        tw.textGray7.bgGray1.rounded4,
        ^.height := "316px",
        NonIdealStateR(
          icon = {
            <.img(
              ^.width := "50px",
              ^.src := "/web/gondor/images/dataroom/users-illustration.svg"
            )
          },
          description = {
            <.div(
              ComponentUtils.testId(NewestParticipantTable, "EmptyStateWarning"),
              "No insights to show for this timeframe"
            )
          }
        )()
      )
    }

    private def renderViewAllButton(props: Props) = {
      WithReactRouterR { router =>
        <.div(
          ComponentUtils.testId(NewestParticipantTable, "ViewAllBtn"),
          tw.flex.justifyCenter.mt20,
          Button(
            style = Button.Style.Minimal(color = Button.Color.Primary),
            onClick = router.set(DynamicAuthPage.DataRoomInsightsParticipantsPage(props.params.dataRoomData.workflowId))
          )("View all")
        )
      }
    }

    private def renderTable(props: Props, state: State) = {
      NewestParticipantTable(
        getKey = _.userId.idString,
        style = Table.Style.Minimal,
        rows = props.newestParticipants,
        columns = Seq(
          Table.Column(
            head = "Name",
            render = renderName,
            width = "25%"
          ),
          Table.Column(
            head = "Group",
            render = renderGroup(props.params.dataRoomData),
            width = "30%"
          ),
          Table.Column(
            head = "Joining date",
            render = renderJoiningDate,
            width = "20%"
          ),
          Table.Column(
            head = renderAccessHeader,
            render = renderAccess(state),
            width = "20%"
          ),
          Table.Column(
            render = _ => renderChevronRight,
            width = "5%"
          )
        ),
        renderRow = renderRow(props)
      )()
    }

    private def renderRow(props: Props): Table.RenderRow[Participant] = { _ => (key, cells, item) =>
      <.tr(
        ComponentUtils.testId(NewestParticipantTable, "Row"),
        ^.key := key,
        ^.onClick --> props.onViewUserDetail(item.userId),
        tw.cursorPointer.hover(tw.bgPrimary1.bgOpacity30),
        cells
      )
    }

    private def renderName(participant: Participant) = {
      Table.Cell(
        <.div(
          tw.wPc100,
          <.div(
            tw.flex.itemsCenter,
            InitialAvatarR(
              id = participant.userId.idString,
              initials = participant.userInfo.avatarInitials,
              size = InitialAvatar.Size.Px24
            )(),
            <.div(
              tw.flexCol.pl8.wPc100,
              <.div(
                tw.wFit,
                TooltipR(
                  renderTarget = <.div(
                    ComponentUtils.testId(NewestParticipantTable, "UserName"),
                    tw.truncate,
                    participant.userInfo.fullName
                  ),
                  renderContent = _(participant.userInfo.emailAddress)
                )()
              )
            )
          )
        )
      )
    }

    private def renderGroup(dataRoomData: DataRoomData)(participant: Participant) = {
      Table.Cell(
        <.div(
          ComponentUtils.testId(NewestParticipantTable, "UserGroup"),
          DataRoomGroupNameTag(
            participant.groupIds.flatMap(dataRoomData.getGroupData(_)).map { groupData =>
              RenderGroupData(groupData.id, groupData.name, groupData.isDeleted)
            }
          )()
        )
      )
    }

    private def renderJoiningDate(participant: Participant) = {
      Table.Cell(
        <.div(
          ComponentUtils.testId(NewestParticipantTable, "JoiningDate"),
          participant.joinDate.map(JsDateFormatterUtils.format(_, JsDateFormat.LongDatePattern1)).getOrElse("")
        )
      )
    }

    private def renderAccess(state: State)(participant: Participant) = {
      val accessOpt = for {
        dataRoomInsightsNewestParticipants <- state.insightsNewestParticipants
        userData <- dataRoomInsightsNewestParticipants.users.get(participant.userId)
      } yield userData.distinctFileCount.toDouble * 100.0 / dataRoomInsightsNewestParticipants.totalFileCount
      Table.Cell(
        accessOpt.fold(
          SkeletonR(
            effect = Skeleton.Effect.Wave,
            height = "20px",
            width = "80%",
            shape = Skeleton.Shape.Rounded
          )()
        ) { percent =>
          <.div(
            ComponentUtils.testId(NewestParticipantTable, "AccessPercent"),
            percent.round.pct
          )
        }
      )
    }

    private def renderChevronRight = {
      Table.Cell(
        <.div(
          ComponentUtils.testId(NewestParticipantTable, "ExpandBtn"),
          tw.flex.itemsCenter.justifyEnd.textGray4,
          <.div(
            IconR(name = Icon.Glyph.ChevronRight)()
          )
        )
      )
    }

    private def renderAccessHeader = {
      <.div(
        tw.flex.itemsCenter,
        <.span("Access"),
        <.div(
          tw.ml4,
          TooltipR(
            renderTarget = <.span(
              tw.textGray6,
              IconR(name = Icon.Glyph.Question, size = Icon.Size.Custom(12))()
            ),
            renderContent = _("Percentage of files in the data room that have been viewed or downloaded")
          )()
        )
      )
    }

    def getInsightsNewestParticipants: Callback = {
      for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback(
          DataRoomEndpointClient
            .getInsightsNewestParticipants(
              DataRoomGetInsightsNewestParticipantsParams(
                props.params.dataRoomData.workflowId,
                props.newestParticipants.map(_.userId).toSet,
                ZoneId.systemDefault()
              )
            )
            .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
            .map(
              _.fold(
                _ =>
                  scope.modState(
                    _.copy(insightsNewestParticipants = None),
                    Toast.errorCallback("Failed to load newest participants")
                  ),
                resp => scope.modState(_.copy(insightsNewestParticipants = Some(resp)))
              )
            )
        )
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State(insightsNewestParticipants = None))
    .renderBackend[Backend]
    .componentDidMount(_.backend.getInsightsNewestParticipants)
    .componentDidUpdate { scope =>
      val prevProps = scope.prevProps
      val currProps = scope.currentProps
      Callback.when(prevProps.params != currProps.params) {
        scope.backend.getInsightsNewestParticipants
      }
    }
    .build

}
