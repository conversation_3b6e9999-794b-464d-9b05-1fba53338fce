// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.`export`

import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import design.anduin.components.button.Button
import design.anduin.components.modal.{Modal, ModalBody, ModalFooterWCancel}
import design.anduin.components.toast.Toast
import anduin.component.util.JavaScriptUtils
import anduin.dataroom.QuickFileSelection
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.model.id.FileId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.stargazer.service.dataroom.DataRoomExportPermissionsParams
import anduin.stargazer.service.dataroom.DataRoomExportPermissionsParams.ExportPermissionTarget
import anduin.utils.StateSnapshotWithModFn
import stargazer.component.routing.react.WithReactRouterR
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.anduin.stargazer.client.utils.ZIOUtils

final case class DataRoomExportPermissionsModal(
  dataRoomWorkflowId: DataRoomWorkflowId,
  exportPermissionTarget: ExportPermissionTarget,
  onClose: Callback,
  renderTarget: Callback => VdomNode
) {
  def apply(): VdomElement = DataRoomExportPermissionsModal.component(this)
}

object DataRoomExportPermissionsModal {

  private type Props = DataRoomExportPermissionsModal

  final case class State(
    isBusy: Boolean,
    fileIds: Set[FileId]
  )

  private final class Backend(scope: BackendScope[Props, State]) {

    given fileIdsReusability: Reusability[Set[FileId]] = Reusability.by_==

    private val fileIdsSnapshot = StateSnapshotWithModFn.withReuse
      .zoom[State, Set[FileId]](_.fileIds)(fileIds => _.copy(fileIds = fileIds))
      .prepareVia(scope)

    def render(props: Props, state: State): VdomNode = {
      val targetLabel = props.exportPermissionTarget match {
        case _: ExportPermissionTarget.Users  => "participant"
        case _: ExportPermissionTarget.Groups => "group"
      }
      Modal(
        renderTarget = props.renderTarget,
        renderContent = closeModal =>
          React.Fragment(
            ModalBody()(
              <.div(
                tw.flexNone.itemsCenter,
                renderDescription,
                renderTree(props, state)
              )
            ),
            ModalFooterWCancel(cancel = closeModal >> props.onClose)(
              Button(
                style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.isBusy),
                onClick = onExport(closeModal),
                isDisabled = state.fileIds.isEmpty
              )("Download report")
            )
          ),
        title = s"Export $targetLabel's permissions",
        size = Modal.Size(width = Modal.Width.Px720)
      )()
    }

    private def renderDescription = {
      <.div(
        tw.text13.leading20,
        "Select folders/files you'd like to include in your permissions report. When ready, click ",
        <.span(tw.fontSemiBold, "Download report"),
        "."
      )
    }

    private def renderTree(props: Props, state: State) = {
      <.div(
        tw.borderAll.border1.borderGray3.rounded3,
        tw.overflowYAuto.px12.py8.mt12.maxWPc100,
        ^.minHeight := "392px",
        ^.maxHeight := "80vh",
        WithReactRouterR { router =>
          QuickFileSelection(
            router,
            props.dataRoomWorkflowId,
            fileIdsSnapshot(state)
          )()
        }
      )
    }

    private def onExport(closeModal: Callback): Callback = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- scope.modState(_.copy(isBusy = true))
        _ <- ZIOUtils.toReactCallback {
          val params = DataRoomExportPermissionsParams(
            dataRoomWorkflowId = props.dataRoomWorkflowId,
            fileIds = state.fileIds,
            target = props.exportPermissionTarget
          )
          for {
            res <- DataRoomEndpointClient.exportPermissions(params)
          } yield {
            scope.modState(
              _.copy(isBusy = false),
              res.fold(
                _ => Toast.errorCallback("Unable to generate permissions report. Please try again"),
                resp =>
                  Toast.successCallback(
                    "Permissions report generated"
                  ) >> closeModal >> props.onClose >> JavaScriptUtils.download(resp.url)
              )
            )
          }
        }
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(
      State(
        fileIds = Set(),
        isBusy = false
      )
    )
    .renderBackend[Backend]
    .build

}
