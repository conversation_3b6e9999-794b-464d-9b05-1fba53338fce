// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.tracking.overview

import scala.scalajs.js
import design.anduin.components.button.Button
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import anduin.dataroom.DataRoomData
import anduin.dataroom.DataRoomUserData.{InvitedUser, JoinedUser}
import anduin.dataroom.detail.tracking.{EmptyChart, FullSizeChart}
import anduin.dms.tracking.QueryTimeRange
import anduin.dms.tracking.QueryTimeRange.toTimestampRange
import anduin.facades.chart.echarts.anon.{BorderType, FontStyle, Rich}
import anduin.facades.chart.echarts.echarts.EChartOption.SeriesPie.DataObject
import anduin.facades.chart.echarts.echarts.EChartOption.{Series, TextStyleWithRich}
import anduin.facades.chart.echarts.echartsStrings
import stargazer.model.routing.{DynamicAuthPage, Page}

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.facades.chart.echarts.echarts.*

private[overview] final case class Invitations(
  router: RouterCtl[Page],
  dataRoomData: DataRoomData,
  queryTimeRange: QueryTimeRange
) {
  def apply(): VdomElement = Invitations.component(this)
}

private[overview] object Invitations {

  private type Props = Invitations

  private def render(props: Props) = {
    <.div(
      tw.relative.flexCol,
      <.div(
        ComponentUtils.testId(Invitations, "InvitationChartTitle"),
        tw.heading3.pb16,
        "Invitations"
      ),
      renderChart(props)
    )
  }

  private def renderDetailButton(props: Props) = {
    <.div(
      ComponentUtils.testId(Invitations, "ViewDetailsBtn"),
      tw.flex.justifyCenter.mt20,
      Button(
        style = Button.Style.Minimal(color = Button.Color.Primary),
        onClick = props.router.set(
          DynamicAuthPage.DataRoomAllParticipantsPage(props.dataRoomData.workflowId, None)
        )
      )("View details")
    )
  }

  private val AcceptInvitationLabel = "Accepted"
  private val PendingInvitationLabel = "Pending"

  private def formatter(total: Int): js.Function1[DataObject, String] = { params =>
    s"${(params.value.asInstanceOf[Double] / total * 100).round.toInt}%" // scalafix:ok DisableSyntax.asInstanceOf
  }

  private def renderChart(props: Props) = {
    val startTimeOpt = toTimestampRange(props.queryTimeRange)._1
    val acceptedInvitations =
      props.dataRoomData.latestState.participatingUsers.count { case (_, userData) =>
        userData.teamState match {
          case _: InvitedUser => false
          case JoinedUser(_, joinedAt) =>
            !(startTimeOpt zip joinedAt).exists((startTime, joinedAt) => startTime.isAfter(joinedAt))
        }
      }
    val pendingInvitations =
      props.dataRoomData.latestState.participatingUsers.count { case (_, userData) =>
        userData.teamState match {
          case _: JoinedUser => false
          case InvitedUser(_, invitedAt, _) =>
            !(startTimeOpt zip invitedAt).exists((startTime, invitedAt) => startTime.isAfter(invitedAt))
        }
      }
    val totalInvitations = acceptedInvitations + pendingInvitations
    val options: EChartOption[Series] = EChartOption[Series]()
      .setTooltip(getChartTooltipOption)
      .setLegend(getChartLegendOption)
      .setSeries(
        js.Array[Series](
          EChartOption
            .SeriesPie()
            .setType("pie")
            .setRadius(js.Array[Any]("30%", "80%"))
            .setCenter(js.Array("50%", "42%"))
            .setLabel(
              Rich()
                .setFormatter(formatter(totalInvitations))
                .setPosition("inside")
                .setFontSize(13)
                .setLineHeight(20)
                .setFontWeight(600)
            )
            .setData(js.Array(getChartData(acceptedInvitations, pendingInvitations)*))
        )
      )

    val params = EChartsResponsiveOption().setBaseOption(options)
    if (totalInvitations > 0) {
      React.Fragment(
        <.div(
          ComponentUtils.testId(Invitations, "InvitationChart"),
          ^.height := "276px",
          FullSizeChart(params = params)()
        ),
        renderDetailButton(props)
      )
    } else {
      <.div(
        ComponentUtils.testId(Invitations, "EmptyChart"),
        ^.height := "316px",
        EmptyChart()()
      )
    }
  }

  private def getChartTooltipOption = {
    EChartOption
      .Tooltip()
      .setTrigger(echartsStrings.item)
      .setBackgroundColor("rgba(255, 255, 255, 0.9)")
      .setPadding(16.0)
      .setBorderColor("#809AAD")
      .setBorderWidth(1)
  }

  private def getChartLegendOption = {
    EChartOption
      .Legend()
      .setOrient(echartsStrings.horizontal)
      .setTop("90%")
      .setItemHeight(16)
      .setItemWidth(16)
      .setItemGap(12)
      .setTextStyle(
        TextStyleWithRich()
          .setFontSize(13)
          .setLineHeight(20)
          .setPaddingVarargs(
            0,
            0,
            0,
            8
          )
      )
      .setData(
        js.Array(AcceptInvitationLabel, PendingInvitationLabel)
      )
  }

  private def getChartData(acceptedInvitations: Int, pendingInvitations: Int) = {
    Seq(
      DataObject()
        .setItemStyle(BorderType().setColor("#2B95D6"))
        .setValue(acceptedInvitations.toDouble)
        .setName(AcceptInvitationLabel)
        .setLabel(FontStyle().setShow(acceptedInvitations > 0)),
      DataObject()
        .setItemStyle(BorderType().setColor("#D1DFE9"))
        .setValue(pendingInvitations.toDouble)
        .setName(PendingInvitationLabel)
        .setLabel(FontStyle().setShow(pendingInvitations > 0))
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
