// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.invitation.participants

import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.ModalBody
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.dataroom.DataRoomData
import anduin.dataroom.detail.participants.invitation.participants.email.EmailSection
import anduin.dataroom.detail.participants.invitation.{
  InvitationGroupSettings,
  InvitationStep,
  InvitationUsers,
  SettingSection
}
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.model.common.user.UserId
import anduin.orgbilling.SubscriptionContactModal
import anduin.orgbilling.model.plan.BillingProduct
import anduin.utils.StateSnapshotWithModFn

private[invitation] final case class ParticipantsStep(
  step: StateSnapshotWithModFn[InvitationStep],
  users: StateSnapshotWithModFn[InvitationUsers],
  actorUserId: UserId,
  actorRole: DataRoomRole,
  dataRoomData: DataRoomData,
  existingUsers: Set[String],
  remainingSeatCount: Int,
  groupSettings: InvitationGroupSettings,
  onGroupChange: InvitationGroupSettings => Callback
) {
  def apply(): VdomElement = ParticipantsStep.component(this)
}

private[invitation] object ParticipantsStep {

  private type Props = ParticipantsStep

  private def render(props: Props) = {
    val isInternalActor = DataRoomRoleUtils.isInternal(props.actorRole)
    React.Fragment(
      ModalBody()(
        EmailSection(
          users = props.users,
          actorUserId = props.actorUserId,
          actorRole = props.actorRole,
          dataRoomData = props.dataRoomData,
          existingUsers = props.existingUsers,
          remainingSeatCount = props.remainingSeatCount
        )(),
        Option.when(isInternalActor)(
          SelectGroupSection(
            actorUserId = props.actorUserId,
            actorRole = props.actorRole,
            dataRoomData = props.dataRoomData,
            groupSettings = props.groupSettings,
            onChange = props.onGroupChange,
            renderHeader = content =>
              SettingSection(
                title = "Group",
                isRequired = true,
                subtitleOpt = Some(
                  <.div(
                    tw.textGray7.textSmall,
                    "Choose how participants will be assigned to groups"
                  )
                ),
                inlineSubtitle = false
              )(content)
          )()
        ),
        Option.when(isInternalActor && props.groupSettings.isNoGroup)(
          SelectSideSection(
            dataRoomWorkflowId = props.dataRoomData.workflowId,
            hasViewOnlyPlan = props.dataRoomData.hasViewOnlyPlan,
            actorRole = props.actorRole,
            remainingSeatCount = props.remainingSeatCount,
            users = props.users
          )()
        ),
        Option.when(props.remainingSeatCount <= 0 && DataRoomRoleUtils.isInternal(props.actorRole)) {
          <.div(
            tw.flex.itemsStart.bgWarning1.mb20.p12.rounded4,
            <.div(
              tw.flexNone.textWarning4.pr8.pt2,
              IconR(Icon.Glyph.Info)()
            ),
            <.div(
              ComponentUtils.testId(ParticipantsStep, "Warning"),
              tw.flexFill.textGray8.fontNormal.text13.leading20,
              "Increase seat limit to invite more internal members. ",
              SubscriptionContactModal(
                entityIdOpt = Some(props.dataRoomData.latestState.creatorEntity.entityModel.id),
                productTypeOpt = Some(BillingProduct.DataRoom)
              )()
            )
          )
        },
        CheckboxSection(
          users = props.users,
          actorRole = props.actorRole,
          hasTermsOfAccess = props.dataRoomData.latestState.dataRoomCreatedState.termsOfAccessOptions.isEnabled,
          groupSettings = props.groupSettings
        )()
      ),
      FooterSection(
        step = props.step,
        users = props.users,
        existingUsers = props.existingUsers,
        remainingSeatCount = props.remainingSeatCount,
        actorRole = props.actorRole,
        groupSettings = props.groupSettings
      )()
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
