// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.link

import anduin.dataroom.billing.{UpgradeSuggestionCard, WithBaseURL}
import anduin.dataroom.detail.link.step.DataRoomLinkInvitationSectionRenderer
import anduin.dataroom.detail.participants.invitation.permissions.PermissionsStep
import anduin.dataroom.detail.participants.invitation.{InvitationGroupSettings, PermissionSettings}
import anduin.dataroom.reactive.DataRoomReactStream
import anduin.dataroom.role.DataRoomRoleReusability.given
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.dataroom.{DataRoomData, DataRoomPermissionCheck}
import anduin.file.tree.PermissionTree
import anduin.model.common.user.UserId
import anduin.model.id.FolderId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.orgbilling.model.plan.DataRoomPremiumFeature
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.endpoints.{AssetPermissionChanges, FolderInfo}
import com.anduin.stargazer.service.FileServiceEndpoints.TargetPermission
import com.anduin.stargazer.utils.FileFolderPermissionUtils
import design.anduin.components.button.Button
import design.anduin.components.dropdown.Dropdown
import design.anduin.components.modal.{Modal, ModalBody, ModalFooter, ModalFooterWCancel}
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import japgolly.scalajs.react.vdom.svg_<^.{< as Svg, ^ as SvgAttr}

import scala.annotation.nowarn

private[link] final case class DataRoomInvitationAssetPermissionDropdown(
  dataRoomWorkflowId: DataRoomWorkflowId,
  actorUserId: UserId,
  actorRoles: DataRoomRole,
  email: String,
  settings: StateSnapshotWithModFn[PermissionSettings],
  dataRoomData: DataRoomData,
  groupSettings: InvitationGroupSettings,
  isDisabled: Boolean = false
) {
  def apply(): VdomElement = DataRoomInvitationAssetPermissionDropdown.component(this)
}

private[link] object DataRoomInvitationAssetPermissionDropdown {

  private type Props = DataRoomInvitationAssetPermissionDropdown

  private val AssetDropdown = (new Dropdown[AssetDropdownOption])()

  private def getDefaultOption(props: Props, permission: FileFolderPermission) = {
    AssetPermissionChanges.allFoldersWithRootChannel(props.dataRoomWorkflowId, permission)
  }

  private def onUpdate(
    props: Props,
    permission: FileFolderPermission
  ) = {
    props.settings.modState {
      _.copy(
        assetPermission = getDefaultOption(props, permission)
      )
    }
  }

  private def onChange(props: Props, openToggle: Callback)(option: AssetDropdownOption) = {
    option match {
      case fixed: AssetDropdownOption.Fixed =>
        onUpdate(
          props,
          fixed.permission
        )
      case AssetDropdownOption.CustomOnButton | AssetDropdownOption.CustomOnMenu =>
        openToggle
    }
  }

  private def hasViewOnly(props: Props) = {
    props.dataRoomData.dataRoomPlan.features.contains(DataRoomPremiumFeature.ViewOnly)
  }

  private def renderFullOption(props: Props)(renderItemProps: Dropdown.RenderItemProps[AssetDropdownOption]) = {
    val option = renderItemProps.item.value
    val upgrade = option == AssetDropdownOption.ViewOnly && !hasViewOnly(props)

    <.div(
      tw.flex.justifyBetween.itemsCenter,
      <.button(
        tw.flex.itemsCenter,
        tw.py8.px16.leading16,
        tw.textLeft,
        TagMod.unless(upgrade)(tw.wPc100),
        renderItemProps.itemTagMods,
        renderItemProps.defaultIcon,
        <.div(
          <.div(
            tw.text13.leading20.fontSemiBold,
            option.text
          ),
          <.div(
            tw.text11.textGray6,
            option match {
              case _: AssetDropdownOption.SameAsMe =>
                "Grant this person the same permissions as me"
              case AssetDropdownOption.CustomOnMenu | AssetDropdownOption.CustomOnButton =>
                "Set custom permissions for the user at the file/folder level"
              case fixed: AssetDropdownOption.Fixed =>
                FileFolderPermissionUtils.getPermissionSubtitles(fixed.permission)
            }
          )
        ),
        <.span(tw.wPx16)
      ),
      TagMod.when(upgrade) {
        WithBaseURL((baseURLOpt, _) =>
          baseURLOpt.fold(EmptyVdom) { baseURL =>
            <.div(
              tw.flex.mr16,
              TooltipR(
                renderTarget = {
                  <.a(
                    tw.flex.itemsCenter.justifyCenter.p8.rounded2,
                    tw.text11.leading16,
                    ^.backgroundColor := "#F1F3FF",
                    ^.textDecoration.none,
                    Svg.svg(
                      ^.xmlns := "http://www.w3.org/2000/svg",
                      SvgAttr.width := "16",
                      SvgAttr.height := "16",
                      SvgAttr.viewBox := "0 0 16 16",
                      SvgAttr.fill := "none",
                      Svg.path(
                        SvgAttr.d :=
                          """
                            |M8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16C12.4183 16 16 12.4183 16 8C16
                            |5.87827 15.1571 3.84344 13.6569 2.34315C12.1566 0.842855 10.1217 0 8 0ZM11.4255
                            |11.4255C11.1418 11.7074 10.6837 11.7074 10.4 11.4255L8 9.02545L5.60727 11.4255C5.31872
                            |11.6726 4.88858 11.656 4.61995 11.3873C4.35132 11.1187 4.3347 10.6886 4.58181 10.4L7.49091
                            |7.49091C7.77457 7.20893 8.2327 7.20893 8.51636 7.49091L11.4255 10.4C11.7074 10.6837
                            |11.7074 11.1418 11.4255 11.4255ZM10.4 7.78909C10.6837 8.07107 11.1418 8.07107 11.4255
                            |7.78909C11.7074 7.50543 11.7074 7.0473 11.4255 6.76364L8.51636 3.85455C8.2327 3.57256
                            |7.77457 3.57256 7.49091 3.85455L4.58181 6.76364C4.3347 7.05219 4.35132 7.48232 4.61995
                            |7.75095C4.88858 8.01959 5.31872 8.0362 5.60727 7.78909L8 5.38909L10.4 7.78909Z
                            |""".stripMargin,
                        SvgAttr.fillRule := "evenodd",
                        SvgAttr.clipRule := "evenodd",
                        SvgAttr.fill := "#5F6AC8"
                      )
                    ),
                    <.div(tw.mx4, "Upgrade to"),
                    <.div(tw.fontSemiBold, "Business"),
                    ^.href := s"$baseURL/#/entity/${props.dataRoomData.latestState.creatorEntity.entityModel.id.idString}/subscription",
                    ^.target.blank
                  )
                },
                renderContent = _("The View permission is a premium feature of our Business plan")
              )()
            )
          }
        )()
      }
    )
  }

  private def renderOption(option: AssetDropdownOption) = {
    <.div(
      option.text
    )
  }

  private def getOptions(props: Props) = {
    val isTargetAdmin = DataRoomRoleUtils.isAdmin(props.settings.value.role)
    val options = AssetDropdownOption
      .getOptions(
        props.dataRoomWorkflowId,
        props.actorRoles,
        props.dataRoomData.rootFolder.userPermission,
        props.settings.value
      )
      .values
      .toSeq ++ Option.when(!isTargetAdmin)(AssetDropdownOption.CustomOnMenu)
    options.map { option =>
      val isDisabled = option == AssetDropdownOption.ViewOnly && !hasViewOnly(props)
      Dropdown.Item(option, isDisabled)
    }
  }

  private def getDisabledReasonOpt(props: Props) = {
    val disabledReason = for {
      _ <- Either.cond(
        props.groupSettings.isNoGroup,
        (),
        "The permissions can't be edited as it is inherited from the groups you selected above"
      )
      _ <- Either.cond(
        !props.settings.value.role.asMessage.sealedValue.isAdmin,
        (),
        "Admins can always invite other participants"
      )
    } yield ()
    disabledReason.left.toOption
  }

  private def renderDropdown(props: Props)(openToggle: Callback) = {
    val disabledReason: String = getDisabledReasonOpt(props).getOrElse("")
    <.div(
      tw.flex.itemsCenter,
      <.div(
        ^.width := DataRoomLinkInvitationSectionRenderer.dropdownWidth,
        TooltipR(
          renderTarget = AssetDropdown(
            value = Some(
              AssetDropdownOption(
                props.dataRoomWorkflowId,
                props.actorRoles,
                props.dataRoomData.rootFolder.userPermission,
                props.settings.value
              )
            ),
            valueToString = _.text,
            onChange = onChange(props, openToggle),
            items = getOptions(props),
            menu = Dropdown.Menu(
              renderItem = Some(renderFullOption(props))
            ),
            button = Dropdown.Button(
              isReadOnly = disabledReason.nonEmpty || props.isDisabled,
              appearance = Dropdown.Appearance.Full(isFullWidth = true),
              renderValue = Some(renderOption)
            )
          )(),
          renderContent = _(disabledReason),
          isDisabled = disabledReason.isEmpty
        )()
      ),
      Modal(
        title = "Review permissions",
        renderTarget = openToggle =>
          <.div(
            tw.ml12,
            Button(
              testId = "View-Permissions",
              style = Button.Style.Text(),
              onClick = openToggle
            )("View permissions")
          ),
        renderContent = closeToggle =>
          PermissionsStep(
            dataRoomWorkflowId = props.dataRoomWorkflowId,
            settings = props.settings,
            dataRoomData = props.dataRoomData,
            actorUserId = props.actorUserId,
            groupSettings = props.groupSettings,
            actorRole = props.dataRoomData.latestState.getUserRole(props.actorUserId),
            footer = ModalFooterWCancel(
              cancel = closeToggle,
              cancelLabel = "Close"
            )()
          )(),
        size = Modal.Size(Modal.Width.Px1160)
      )()
    )
  }

  private def renderPermissionTable(props: Props)(closeToggle: Callback) = {
    React.Fragment(
      ModalBody()(
        <.div(
          tw.flex.flexCol.borderBottom.borderGray3,
          ^.height := 528.px,
          PermissionTree(
            rootItem = PermissionTree.ItemType.Folder(
              FolderInfo.empty(FolderId.channelSystemFolderId(props.dataRoomWorkflowId)),
              TargetPermission.empty
            ),
            permissionTargetOpt = None,
            changes = props.settings.withReuse.zoomState(PermissionSettings.zoomToChanges),
            externalCanSelectItemCheck = (_, optionPermissionOpt) => canSelect(props, optionPermissionOpt),
            externalCanSelectHeaderCheck = optionPermissionOpt => canSelect(props, optionPermissionOpt),
            showIndex = props.dataRoomData.latestState.dataRoomCreatedState.showIndex,
            lowestPermission = if (hasViewOnly(props)) {
              FileFolderPermission.ViewOnly
            } else {
              FileFolderPermission.Read
            },
            currentUpperBound = DataRoomRoleUtils.getMaxPermission(props.settings.value.role),
            reactStream = Some(DataRoomReactStream.dataRoomFile(props.dataRoomWorkflowId))
          )()
        )
      ),
      ModalFooter()(
        <.div(
          tw.flex.itemsCenter.spaceX8,
          if (hasViewOnly(props)) tw.justifyEnd else tw.justifyBetween,
          if (!hasViewOnly(props)) {
            <.div(
              ComponentUtils.testId(DataRoomInvitationAssetPermissionDropdown, "UpgradeWarning"),
              tw.flexNone,
              UpgradeSuggestionCard(
                reason = "to enable View Only permission",
                isShowingComparePlans = true
              )()
            )
          } else {
            EmptyVdom
          },
          Button(testId = "Close", onClick = closeToggle)("Close")
        )
      )
    )
  }

  private def canSelect(
    props: Props,
    optionPermissionOpt: Option[FileFolderPermission]
  ) = {
    DataRoomPermissionCheck.permissionTree(
      roleOpt = Some(props.settings.value.role),
      dataRoomPlan = props.dataRoomData.dataRoomPlan,
      optionPermissionOpt = optionPermissionOpt
    )
  }

  private def getTitle(props: Props) = {
    val email = props.email.trim
    if (email.nonEmpty) s"Select asset permissions for $email" else "Select asset permissions"
  }

  private def render(props: Props) = {
    Modal(
      renderTarget = renderDropdown(props),
      renderContent = renderPermissionTable(props),
      title = getTitle(props),
      size = Modal.Size(Modal.Width.Px1160)
    )()
  }

  given dataRoomWorkflowIdReusability: Reusability[DataRoomWorkflowId] = Reusability.by_==

  given userIdReusability: Reusability[UserId] = Reusability.by_==

  given permissionReusability: Reusability[FileFolderPermission] = Reusability.by_==

  given dataRoomDataReusability: Reusability[DataRoomData] = Reusability.by_==

  given groupSettingsReusability: Reusability[InvitationGroupSettings] = Reusability.by_==

  @nowarn
  given propsReusability: Reusability[Props] = Reusability.derive

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .configure(Reusability.shouldComponentUpdate)
    .build

}
