// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.user

import anduin.dataroom.detail.participants.DataRoomManualNotificationModal
import anduin.dataroom.detail.participants.`export`.{DataRoomExportParticipantsModal, DataRoomExportPermissionsModal}
import anduin.dataroom.detail.participants.group.{AddUsersToGroupModal, RemoveUsersFromGroupModal}
import anduin.dataroom.detail.participants.permission.DataRoomUserPermissionsModal
import anduin.dataroom.detail.participants.user.DataRoomUserActionMenu.getUserActions
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.dataroom.{DataRoomData, DataRoomPermissionCheck, DataRoomUserData}
import anduin.file.explorer.header.FileHeaderActions
import anduin.layout.SelectionData
import anduin.model.common.user.UserId
import anduin.stargazer.service.dataroom.DataRoomExportPermissionsParams.ExportPermissionTarget
import anduin.utils.StateSnapshotWithModFn
import design.anduin.components.icon.Icon
import design.anduin.components.menu.react.{MenuDividerR, MenuItemR, MenuR}
import design.anduin.components.tooltip.react.TooltipR
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.VdomElement
import stargazer.model.routing.{DynamicAuthPage, Page}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

case class DataRoomUserActionMenu(
  router: RouterCtl[Page],
  page: DynamicAuthPage.DataRoomParticipantsPage,
  actorUserId: UserId,
  dataRoomData: DataRoomData,
  rows: List[DataRoomUserRowData],
  userSelection: StateSnapshotWithModFn[SelectionData[UserId]],
  currentAdminCount: Int,
  refetch: Callback,
  onClose: Callback
) {

  private val hasToA = dataRoomData.latestState.dataRoomCreatedState.termsOfAccessOptions.isEnabled

  private val rowUserIds = rows.map(_.userId).toSet

  private val userActions = getUserActions(
    dataRoomData,
    actorUserId,
    rows
  )

  def apply(): VdomElement = DataRoomUserActionMenu.component(this)
}

object DataRoomUserActionMenu {

  private type Props = DataRoomUserActionMenu

  sealed trait UserAction derives CanEqual

  object UserAction {
    case object ManagePermission extends UserAction
    case object MoveToGroup extends UserAction
    case object Message extends UserAction
    case object ExportParticipants extends UserAction
    case object ExportPermissions extends UserAction
    case object RemoveFromGroup extends UserAction
    case object RemoveFromDataRoom extends UserAction
  }

  private def onSingleSelection(rows: List[DataRoomUserRowData]) = {
    rows.headOption.filter(_ => rows.size == 1)
  }

  private def canManagePermission(dataRoomData: DataRoomData, actorUserId: UserId)(userRowData: DataRoomUserRowData) = {
    val isInternal = dataRoomData.latestState.isUserRole(actorUserId)(DataRoomRoleUtils.isInternal)
    val isInviter = userRowData.teamState match {
      case _: DataRoomUserData.JoinedUser        => false
      case invited: DataRoomUserData.InvitedUser => invited.inviter == actorUserId
    }
    isInternal || isInviter
  }

  def getUserActions(
    dataRoomData: DataRoomData,
    actorUserId: UserId,
    rows: List[DataRoomUserRowData]
  ): Seq[UserAction] = {
    val isActorAdmin = dataRoomData.latestState.isUserRole(actorUserId)(DataRoomRoleUtils.isAdmin)
    val isActorInternal = dataRoomData.latestState.isUserRole(actorUserId)(DataRoomRoleUtils.isInternal)
    val rowUserIds = rows.map(_.userId).toSet
    val joinedAdmins = dataRoomData.latestState.getJoinedAdmins
    val selectedAdmins = joinedAdmins.intersect(rowUserIds)
    Seq(
      onSingleSelection(rows)
        .filter(canManagePermission(dataRoomData, actorUserId))
        .map(_ => UserAction.ManagePermission),
      Option.when(isActorAdmin)(UserAction.MoveToGroup),
      Option.when(!rowUserIds.contains(actorUserId) && isActorInternal)(UserAction.Message),
      Option.when(isActorAdmin)(UserAction.ExportParticipants),
      Option.when(isActorAdmin)(UserAction.ExportPermissions),
      Option.when(isActorAdmin && !rowUserIds.exists(dataRoomData.unassignedParticipants.contains))(
        UserAction.RemoveFromGroup
      ),
      Option.when(rows.forall(_.canRemove) && joinedAdmins != selectedAdmins)(UserAction.RemoveFromDataRoom)
    ).flatten
  }

  private def render(props: Props) = {
    MenuR()(
      props.userActions
        .map[VdomNode] {
          case UserAction.ManagePermission   => renderUserPermissions(props)
          case UserAction.MoveToGroup        => renderMoveToGroup(props)
          case UserAction.Message            => renderMessageParticipant(props)
          case UserAction.ExportParticipants => renderExportParticipants(props)
          case UserAction.ExportPermissions  => renderExportPermissions(props)
          case UserAction.RemoveFromGroup    => renderRemoveFromGroup(props)
          case UserAction.RemoveFromDataRoom => renderRemoveFromDataRoom(props)
        }
        .toVdomArray
    )
  }

  private def withPlanCheck(props: Props)(renderTarget: Boolean => VdomNode) = {
    FileHeaderActions.renderButtonWithTooltip {
      DataRoomPermissionCheck.expiredPlan(props.dataRoomData.dataRoomPlan)
    } { res =>
      renderTarget(res.isEmpty)
    }
  }

  private def renderMenuItem(
    icon: Icon.Name,
    text: String,
    isDisabled: Boolean,
    color: MenuItemR.Color = MenuItemR.ColorGray
  )(
    onClick: Callback
  ) = {
    MenuItemR(
      onClick = onClick,
      color = color,
      icon = Some(icon),
      isDisabled = isDisabled
    )(text)
  }

  private def renderUserPermissions(props: Props) = {
    onSingleSelection(props.rows).map { userRowData =>
      withPlanCheck(props) { isDisabled =>
        DataRoomUserPermissionsModal(
          router = props.router,
          actorUserId = props.actorUserId,
          dataRoomData = props.dataRoomData,
          userRowData = userRowData,
          currentAdminCount = props.currentAdminCount,
          refetch = props.refetch,
          defaultIsOpen = false,
          isDisabled = isDisabled,
          renderTarget = (canEdit, openToggle) =>
            renderMenuItem(
              icon = if (canEdit) Icon.Glyph.Edit else Icon.Glyph.Eye,
              text = if (canEdit) "Edit permissions" else "View permissions",
              isDisabled = isDisabled
            )(openToggle)
        )()
      }
    }
  }

  private def renderMoveToGroup(props: Props) = {
    withPlanCheck(props) { isPlanDisabled =>
      Option.when(
        props.dataRoomData.latestState
          .isUserRole(props.actorUserId)(DataRoomRoleUtils.isAdmin) && props.dataRoomData.groupMap.nonEmpty
      ) {
        AddUsersToGroupModal(
          router = props.router,
          actorUserId = props.actorUserId,
          userIds = props.rowUserIds,
          dataRoomData = props.dataRoomData,
          renderTarget = openToggle =>
            renderMenuItem(
              icon = Icon.Glyph.Plus,
              text = "Add to groups",
              isDisabled = isPlanDisabled
            )(openToggle),
          onClose = props.onClose
        )()
      }
    }
  }

  private def renderMessageParticipant(props: Props) = {
    withPlanCheck(props) { isDisabled =>
      DataRoomManualNotificationModal(
        userId = props.actorUserId,
        dataRoomData = props.dataRoomData,
        userRowDataMap = Map(),
        initialSelection = props.rowUserIds,
        renderTarget = renderMenuItem(
          Icon.Glyph.Envelope,
          "Message",
          isDisabled = isDisabled
        ),
        onClose = props.onClose,
        showSelection = false
      )()
    }
  }

  private def renderExportParticipants(props: Props) = {
    withPlanCheck(props) { isDisabled =>
      DataRoomExportParticipantsModal(
        props.dataRoomData.workflowId,
        props.rowUserIds,
        props.hasToA,
        props.onClose,
        renderTarget = renderMenuItem(
          Icon.Glyph.Table,
          "Export participant's data",
          isDisabled = isDisabled
        ),
        isGlobalAction = false
      )()
    }
  }

  private def renderExportPermissions(props: Props) = {
    withPlanCheck(props) { isDisabled =>
      DataRoomExportPermissionsModal(
        props.dataRoomData.workflowId,
        ExportPermissionTarget.Users(props.rowUserIds),
        props.onClose,
        renderTarget = renderMenuItem(
          Icon.Glyph.Key,
          "Export participant's permissions",
          isDisabled = isDisabled
        )
      )()
    }
  }

  private def renderRemoveFromGroup(props: Props) = {
    props.page.groupIdOpt.map { groupId =>
      withPlanCheck(props) { isDisabled =>
        RemoveUsersFromGroupModal(
          dataRoomData = props.dataRoomData,
          actorUserId = props.actorUserId,
          groupId = groupId,
          userIds = props.rowUserIds,
          renderTarget = (disabledReasonOpt, openToggle) =>
            TooltipR(
              renderTarget = renderMenuItem(
                icon = Icon.Glyph.UserRemove,
                text = "Remove from group",
                isDisabled = isDisabled || disabledReasonOpt.nonEmpty
              )(openToggle),
              renderContent = _(disabledReasonOpt),
              isDisabled = disabledReasonOpt.isEmpty
            )(),
          onRemoved = Callback.empty,
          onClose = props.onClose
        )()
      }
    }
  }

  private def renderRemoveFromDataRoom(props: Props) = {
    withPlanCheck(props) { isDisabled =>
      val isRemoveSelf = onSingleSelection(props.rows).exists(_.userId == props.actorUserId)
      RemoveUsersFromDataRoomModal(
        router = props.router,
        renderTarget = (canRemove, openToggle) =>
          Option.when(canRemove)(
            React.Fragment(
              Option.when(props.userActions.size > 1)(MenuDividerR()()),
              renderMenuItem(
                Icon.Glyph.Trash,
                if (isRemoveSelf) "Leave data room" else "Remove from data room",
                isDisabled = isDisabled || !canRemove,
                color = MenuItemR.ColorDanger
              )(openToggle)
            )
          ),
        workflowId = props.dataRoomData.workflowId,
        dataRoomData = props.dataRoomData,
        actorUserId = props.actorUserId,
        userRowDataList = props.rows,
        onRemoveNonActor =
          props.userSelection.modState(state => state.copy(selectedItems = state.selectedItems -- props.rowUserIds))
      )()
    }
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
