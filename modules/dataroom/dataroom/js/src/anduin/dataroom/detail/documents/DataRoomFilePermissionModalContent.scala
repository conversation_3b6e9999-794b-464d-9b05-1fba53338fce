// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.documents

import anduin.component.text.SearchBox
import design.anduin.components.button.Button
import design.anduin.components.dropdown.Dropdown
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, ModalHeader}
import design.anduin.components.radio.Radio
import design.anduin.components.responsive.ScreenWidth
import design.anduin.components.responsive.react.WithScreenWidthR
import design.anduin.components.selector.react.SelectorR
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import zio.Task
import anduin.dataroom.*
import anduin.dataroom.billing.UpgradeSuggestionCard
import anduin.dataroom.detail.DataRoomUserInfoRenderer
import anduin.dataroom.detail.participants.group.DataRoomGroupNameTag.RenderGroupData
import anduin.dataroom.detail.participants.group.{DataRoomGroupInfoRenderer, DataRoomGroupNameTag}
import anduin.dataroom.documents.DataRoomFileFolderPermissionDetail
import anduin.dataroom.documents.DataRoomFileFolderPermissionDetail.NoAccessParentFolder
import anduin.dataroom.group.DataRoomGroupData
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.file.explorer.DmsIcon
import anduin.file.tree.PermissionTree.AssetPermissionChangesReusability.given
import anduin.file.{FilePermissionExplanationPopover, TooltipOnTruncate}
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId
import anduin.model.id.FolderId
import anduin.orgbilling.model.plan.DataRoomPremiumFeature
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.utils.{StateSnapshotCollectionHelper, StateSnapshotWithModFn, StringUtils}
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.endpoints.*
import com.anduin.stargazer.service.FileServiceEndpoints.PermissionTarget
import com.anduin.stargazer.utils.FileFolderPermissionUtils
import design.anduin.components.segment.react.SegmentR

final case class DataRoomFilePermissionModalContent(
  item: DataRoomFilePermissionModalContent.Item,
  permissionDetail: DataRoomFileFolderPermissionDetail,
  initialUserChanges: Map[UserId, Option[FileFolderPermission]],
  initialGroupChanges: Map[DataRoomGroupId, Option[FileFolderPermission]],
  dataRoomData: DataRoomData,
  actorUserId: UserId,
  onSave: Option[DataRoomFilePermissionModalContent.OnSave],
  closeToggle: Callback,
  managePermissionTreePageUrl: UserId => Option[String]
) {
  def apply(): VdomElement = DataRoomFilePermissionModalContent.component(this)

  private val actorGroupIds: List[DataRoomGroupId] = dataRoomData.getGroupsOfUser(actorUserId).map(_.id).toList

  private val actorPermissionOpt: Option[FileFolderPermission] =
    permissionDetail.getUser(actorUserId, groupIds = actorGroupIds)

}

object DataRoomFilePermissionModalContent {

  sealed trait Item derives CanEqual {
    def name: String
  }

  object Item {

    final case class Folder(name: String, childrenCount: Int, folderIdOpt: Option[FolderId]) extends Item

    final case class File(name: String) extends Item
  }

  private type Props = DataRoomFilePermissionModalContent

  type OnSave = PermissionChanges => Task[Callback]

  enum ViewingMode {
    case Users, Groups
  }

  private final case class State(
    viewingMode: ViewingMode,
    searchQuery: String,
    userPermissionChanges: Map[UserId, Option[FileFolderPermission]], // None = No access
    groupPermissionChanges: Map[DataRoomGroupId, Option[FileFolderPermission]],
    lastSelectAllOption: Option[Option[FileFolderPermission]],
    isBtnBusy: Boolean
  )

  private val rowSep = <.div(tw.borderTop.borderGray3.wPc100.hPx1)

  private def assetPermissionChangesStateSnapshotHelper[K] = {
    StateSnapshotCollectionHelper[
      Seq[(K, FolderId)],
      Map[K, Option[FileFolderPermission]],
      AssetPermissionChanges
    ](
      { changes => state =>
        AssetPermissionChanges(
          Map(),
          Map(),
          changes.flatMap { case (targetId, folderId) =>
            state.get(targetId).map(folderId -> _)
          }.toMap
        )
      },
      _ => _ => identity
    )
  }

  private val PermissionDropdown = (new Dropdown[Option[FileFolderPermission]])()

  private case class Backend(scope: BackendScope[Props, State]) {

    private val userPermissionChangesStateSnapshot = StateSnapshotWithModFn.withReuse
      .zoom[State, Map[UserId, Option[FileFolderPermission]]](_.userPermissionChanges)(_ => identity)
      .prepareVia(scope)

    @annotation.unused
    private val groupPermissionChangesStateSnapshot = StateSnapshotWithModFn.withReuse
      .zoom[State, Map[DataRoomGroupId, Option[FileFolderPermission]]](_.groupPermissionChanges)(_ => identity)
      .prepareVia(scope)

    private val permissionOptions = List(Option.empty[FileFolderPermission]) ++ FileFolderPermission.values.map(Some(_))

    private def onUpdateUserPermission(
      permissionDetail: DataRoomFileFolderPermissionDetail,
      userId: UserId,
      newPermissionOpt: Option[FileFolderPermission]
    ) = {
      scope.modState { state =>
        val currentPermissionOpt = permissionDetail.getUser(userId)
        state.copy(
          userPermissionChanges = if (currentPermissionOpt == newPermissionOpt) {
            state.userPermissionChanges - userId
          } else {
            state.userPermissionChanges.updated(userId, newPermissionOpt)
          },
          lastSelectAllOption = None
        )
      }
    }

    private def onUpdateMultiplePermissions(
      permissionDetail: DataRoomFileFolderPermissionDetail,
      targetUsers: Set[UserId] = Set.empty,
      targetGroups: Set[DataRoomGroupId] = Set.empty,
      newPermissionOpt: Option[FileFolderPermission]
    ) = {
      val (usersToRevoke, usersToAdd) = targetUsers.partition {
        permissionDetail.userPermissions.get(_).exists(_.permission == newPermissionOpt)
      }
      val (groupsToRevoke, groupsToAdd) = targetGroups.partition {
        permissionDetail.groupPermissions.get(_).exists(_.permission == newPermissionOpt)
      }
      scope.modState { state =>
        state.copy(
          userPermissionChanges =
            state.userPermissionChanges -- usersToRevoke ++ usersToAdd.map(_ -> newPermissionOpt).toMap,
          groupPermissionChanges =
            state.groupPermissionChanges -- groupsToRevoke ++ groupsToAdd.map(_ -> newPermissionOpt).toMap,
          lastSelectAllOption = Option.when(state.viewingMode == ViewingMode.Users)(newPermissionOpt)
        )
      }
    }

    private def onUpdateGroupPermissions(
      permissionDetail: DataRoomFileFolderPermissionDetail,
      groupData: DataRoomGroupData
    )(
      newPermissionOpt: Option[FileFolderPermission]
    ) = {
      val groupId = groupData.id
      val currentPermissionOpt = permissionDetail.getGroup(groupId)
      val newState: State => State = { state =>
        state.copy(
          groupPermissionChanges = if (currentPermissionOpt == newPermissionOpt) {
            state.groupPermissionChanges - groupId
          } else {
            state.groupPermissionChanges.updated(groupId, newPermissionOpt)
          }
        )
      }
      scope.modState(newState)
    }

    private def renderPermissionChoice(
      isChecked: Boolean,
      onUpdate: Callback,
      disabledReasonOpt: Option[String],
      optionPermissionOpt: Option[FileFolderPermission]
    ) = {
      <.div(
        ComponentUtils.testId(
          DataRoomFilePermissionModalContent,
          "Permission-" + FileFolderPermissionUtils.getPermissionName(optionPermissionOpt)
        ),
        tw.wPx16,
        TooltipR(
          renderTarget = Radio(
            isChecked = isChecked,
            onChange = onUpdate,
            isDisabled = disabledReasonOpt.nonEmpty && !isChecked
          )(),
          renderContent = _(disabledReasonOpt.getOrElse[String]("")),
          isDisabled = disabledReasonOpt.forall(_.isEmpty) || isChecked
        )()
      )
    }

    private def renderDropdown(
      selectedPermissionOpt: Option[FileFolderPermission],
      onUpdate: Option[FileFolderPermission] => Callback,
      getDisabledReasonOpt: Option[FileFolderPermission] => Option[String]
    ) = {
      val canModify = permissionOptions.count(getDisabledReasonOpt(_).isEmpty) > 1
      if (canModify) {
        <.label(
          tw.flex.itemsCenter.hPx40.cursorPointer,
          PermissionDropdown(
            value = Some(selectedPermissionOpt),
            valueToString = FileFolderPermissionUtils.getPermissionName,
            onChange = onUpdate,
            items = permissionOptions.map { optionPermissionOpt =>
              Dropdown.Item(optionPermissionOpt, getDisabledReasonOpt(optionPermissionOpt).nonEmpty)
            },
            menu = Dropdown.Menu(
              renderItemBody = Option { item =>
                <.div(
                  ^.minHeight := "40px",
                  ^.maxWidth := "240px",
                  tw.flex.flexCol.justifyCenter.text13.leading20.pl8,
                  <.div(
                    tw.fontSemiBold,
                    FileFolderPermissionUtils.getPermissionName(item.value)
                  ),
                  <.div(
                    tw.fontNormal,
                    item.value.map(FileFolderPermissionUtils.getPermissionSubtitles)
                  )
                )
              }
            ),
            button = Dropdown.Button(appearance = Dropdown.Appearance.Text())
          )()
        )
      } else {
        <.div(
          tw.textPrimary5,
          FileFolderPermissionUtils.getPermissionName(selectedPermissionOpt)
        )
      }
    }

    private def onSave(
      props: Props,
      state: State
    ) = {
      val teamMap = for {
        (groupId, permission) <- state.groupPermissionChanges
        teamId <- props.dataRoomData.groupMap.get(groupId).map(_.teamId)
      } yield (teamId, permission)
      val permissionChanges = PermissionChanges.fromOptMaps(
        userMap = state.userPermissionChanges,
        teamMap = teamMap
      )
      for {
        _ <- scope.modState(_.copy(isBtnBusy = true))
        _ <- Callback.traverseOption(props.onSave) { onSave =>
          ZIOUtils.toReactCallback(
            onSave(permissionChanges).map {
              _ >> scope.modState(_.copy(isBtnBusy = false), props.closeToggle)
            }
          )
        }
      } yield ()
    }

    private def renderRowLayout(
      renderName: VdomNode,
      renderData: VdomNode,
      additionalStyle: TagMod = TagMod.empty
    ) = {
      <.div(
        ComponentUtils.testId(DataRoomFilePermissionModalContent, "UserRow"),
        tw.flex.itemsCenter.rounded3.group.hover(tw.bgGray1),
        additionalStyle,
        <.div(
          tw.flexFill,
          renderName
        ),
        renderData
      )
    }

    private def renderRadioRowLayout(
      renderName: VdomNode,
      additionalStyle: TagMod = TagMod.empty
    )(
      renderChoice: Option[FileFolderPermission] => VdomNode
    ) = {
      renderRowLayout(
        renderName = renderName,
        renderData = permissionOptions.toVdomArray(
          using { optionPermissionOpt =>
            <.div(
              ComponentUtils.testId(
                DataRoomFilePermissionModalContent,
                "Radio-" + optionPermissionOpt.fold("no-access")(_.name.toLowerCase)
              ),
              ^.key := optionPermissionOpt.fold("no-access")(_.name.toLowerCase),
              tw.flexNone.flex.itemsCenter.justifyCenter.wPx128,
              renderChoice(optionPermissionOpt)
            )
          }
        ),
        additionalStyle = additionalStyle
      )
    }

    private def renderPermissionRow(
      props: Props,
      state: State,
      groups: List[DataRoomGroupData],
      userId: UserId,
      userInfo: DataRoomUserData.UserInfo,
      renderName: VdomNode,
      onUpdate: Option[FileFolderPermission] => Callback,
      getDisabledReasonOpt: Option[FileFolderPermission] => Option[String],
      isLargeScreen: Boolean
    ): VdomNode = {
      val selectedPermissionOpt = props.permissionDetail.getUser(
        userId,
        state.userPermissionChanges,
        state.groupPermissionChanges,
        groups.map(_.id)
      )
      val nameWithApostrophe = if (props.actorUserId == userId) {
        "your"
      } else {
        StringUtils.addApostrophe(if (userInfo.fullName.nonEmpty) userInfo.fullName else userInfo.emailAddress)
      }
      val title = s"$nameWithApostrophe permissions"
      if (isLargeScreen) {
        val previewedFolderIdOpt = props.item match {
          case folder: Item.Folder if folder.childrenCount > 0 && isLargeScreen => folder.folderIdOpt
          case _                                                                => None
        }
        val warningReasonOpt = for {
          lastSelectAllOption <- state.lastSelectAllOption
          if lastSelectAllOption != selectedPermissionOpt && groups.isEmpty
          reasonOpt <- getDisabledReasonOpt(lastSelectAllOption)
        } yield reasonOpt
        renderRadioRowLayout(
          renderName = <.div(
            tw.flex.itemsCenter.py8,
            <.div(
              tw.flex.flexFill.itemsCenter,
              <.div(
                tw.flexFill,
                renderName
              ),
              previewedFolderIdOpt.fold(EmptyVdom) { folderId =>
                val changes =
                  if (groups.isEmpty) {
                    assetPermissionChangesStateSnapshotHelper(userPermissionChangesStateSnapshot(state)) {
                      Seq(userId -> folderId)
                    }
                  } else {
                    assetPermissionChangesStateSnapshotHelper(groupPermissionChangesStateSnapshot(state)) {
                      groups.map { group =>
                        group.id -> folderId
                      }
                    }
                  }
                <.div(
                  tw.flexNone.invisible.groupHover(tw.visible).mx16,
                  FilePermissionColumnPreviewer(
                    dataRoomWorkflowId = props.dataRoomData.workflowId,
                    folderId = folderId,
                    actorUserId = props.actorUserId,
                    title = title,
                    permissionTarget = PermissionTarget.SingleUser(userId),
                    changes = changes,
                    managePermissionTreePageUrl = props.managePermissionTreePageUrl(userId),
                    showIndex = props.dataRoomData.latestState.dataRoomCreatedState.showIndex
                  )()
                )
              },
              <.div(
                tw.flex.flexFill,
                DataRoomGroupNameTag(
                  groups = groups.map(group => RenderGroupData(group.id, group.name, group.isDeleted)),
                  unassignedLabel = "No group (unassigned)"
                )()
              )
            ),
            <.div(
              tw.wPx32.flexNone.flex.itemsCenter.justifyCenter.textWarning4,
              warningReasonOpt.fold(EmptyVdom) { warningReason =>
                TooltipR(
                  renderTarget = IconR(Icon.Glyph.Info)(),
                  renderContent = _(warningReason),
                  isDisabled = warningReason.isEmpty
                )()
              }
            )
          )
        ) { optionPermissionOpt =>
          renderPermissionChoice(
            isChecked = selectedPermissionOpt == optionPermissionOpt,
            onUpdate = onUpdate(optionPermissionOpt),
            disabledReasonOpt = getDisabledReasonOpt(optionPermissionOpt),
            optionPermissionOpt = optionPermissionOpt
          )
        }
      } else {
        renderRowLayout(
          renderName = renderName,
          renderData = renderDropdown(
            selectedPermissionOpt,
            onUpdate,
            getDisabledReasonOpt
          ),
          additionalStyle = TagMod(tw.ml48)
        )
      }
    }

    private def getFolderChildCount(props: Props) = {
      props.item match {
        case folder: Item.Folder => folder.childrenCount
        case _: Item.File        => 0
      }
    }

    private def checkNoAccessToAccess(
      currentPermissionOpt: Option[FileFolderPermission],
      noAccessParentFolders: Option[List[NoAccessParentFolder]],
      optionPermissionOpt: Option[FileFolderPermission]
    ) = {
      val canChangeParent = noAccessParentFolders.exists(_.nonEmpty)
      val fromNoAccessAndCantChange = currentPermissionOpt.isEmpty && noAccessParentFolders.forall(_.nonEmpty)
      val toAccess = optionPermissionOpt.nonEmpty
      Either.cond(
        !(toAccess && fromNoAccessAndCantChange),
        (),
        "Permission can't be modified as they don't have access to the parent folder" +
          (if (canChangeParent) "" else ". Please contact an admin if you think this is a mistake")
      )
    }

    private def getDisabledReasonOpt[K](
      props: Props,
      targetId: K,
      parentFolderOwners: Option[List[K]],
      currentPermissionOpt: Option[FileFolderPermission],
      noAccessParentFolders: Option[List[NoAccessParentFolder]],
      roleOpt: Option[DataRoomRole]
    ) = {
      val getReason = { (optionPermissionOpt: Option[FileFolderPermission]) =>
        val either = for {
          _ <- Either.cond(
            props.onSave.nonEmpty,
            (),
            ""
          )
          _ <- DataRoomPermissionCheck.permissionTree(
            roleOpt = roleOpt,
            dataRoomPlan = props.dataRoomData.dataRoomPlan,
            optionPermissionOpt = optionPermissionOpt
          )
          _ <- DataRoomPermissionCheck.ownToModify(props.actorPermissionOpt)
          _ <- DataRoomPermissionCheck
            .parentOwners(parentFolderOwners.getOrElse(List()), targetId)
          _ <- checkNoAccessToAccess(
            currentPermissionOpt,
            noAccessParentFolders,
            optionPermissionOpt
          )
          _ <- FileFolderPermissionUtils.canChangePermission(
            props.actorPermissionOpt,
            currentPermissionOpt,
            optionPermissionOpt
          )
        } yield ()
        either.left.toOption
      }
      getReason
    }

    private def getUserDisabledReasonOpt(
      props: Props,
      state: State,
      userId: UserId,
      groupIds: List[DataRoomGroupId]
    )(
      permissionOpt: Option[FileFolderPermission]
    ) = {
      getDisabledReasonOpt(
        props = props,
        targetId = userId,
        parentFolderOwners = props.permissionDetail.ownParentFolderUsers,
        currentPermissionOpt = props.permissionDetail.getUser(
          userId,
          state.userPermissionChanges,
          state.groupPermissionChanges,
          groupIds
        ),
        noAccessParentFolders = props.permissionDetail.userPermissions.get(userId).flatMap(_.noAccessParentFolders),
        roleOpt = props.dataRoomData.latestState.dataRoomCreatedState.individualRoles.get(userId)
      )(permissionOpt).orElse(
        groupIds.headOption.map(_ => "Permission can't be modified as the user inherits from group membership")
      )
    }

    private def getGroupDisabledReasonOpt(
      props: Props,
      state: State,
      groupId: DataRoomGroupId
    )(
      permissionOpt: Option[FileFolderPermission]
    ) = {
      getDisabledReasonOpt(
        props = props,
        targetId = groupId,
        parentFolderOwners = props.permissionDetail.ownParentFolderGroups,
        currentPermissionOpt = props.permissionDetail.getGroup(
          groupId,
          state.groupPermissionChanges
        ),
        noAccessParentFolders = props.permissionDetail.groupPermissions.get(groupId).flatMap(_.noAccessParentFolders),
        roleOpt = props.dataRoomData.groupMap.get(groupId).map(_.role)
      )(permissionOpt).orElse {
        val isActorAdmin = props.dataRoomData.latestState.isUserRole(props.actorUserId)(DataRoomRoleUtils.isAdmin)
        Option.unless(isActorAdmin)("Only admins can manage group permissions")
      }
    }

    private def renderUserPermissionRow(
      props: Props,
      state: State,
      userId: UserId,
      userData: DataRoomUserData,
      isLargeScreen: Boolean
    ) = {
      val groups = userData.userState.groupIds.toList.flatMap(props.dataRoomData.getGroupData(_))
      renderPermissionRow(
        props = props,
        state = state,
        groups = groups,
        userId = userId,
        userInfo = userData.userInfo,
        renderName = DataRoomUserInfoRenderer(
          userId = userId,
          emailAddress = userData.userInfo.emailAddress,
          fullName = userData.userInfo.fullName
        )(),
        onUpdate = onUpdateUserPermission(
          props.permissionDetail,
          userId,
          _
        ),
        getDisabledReasonOpt = getUserDisabledReasonOpt(
          props,
          state,
          userId,
          groups.map(_.id)
        ),
        isLargeScreen = isLargeScreen
      )
    }

    private def renderGroupName(
      props: Props,
      state: State,
      isLargeScreen: Boolean
    )(
      groupData: DataRoomGroupData
    ) = {
      val previewedFolderIdOpt = props.item match {
        case folder: Item.Folder if folder.childrenCount > 0 && isLargeScreen => folder.folderIdOpt
        case _                                                                => None
      }
      <.div(
        tw.flex.itemsCenter,
        <.div(
          ComponentUtils.testId(DataRoomFilePermissionModalContent, "GroupName"),
          tw.justifyStart.py8,
          ^.width := "278px",
          DataRoomGroupInfoRenderer(
            groupData,
            Some(DataRoomGroupInfoRenderer.GroupSubtitle.MemberCount)
          )()
        ),
        previewedFolderIdOpt.fold(EmptyVdom) { folderId =>
          val changes =
            assetPermissionChangesStateSnapshotHelper(groupPermissionChangesStateSnapshot(state)) {
              Seq(groupData.id -> folderId)
            }
          <.div(
            tw.flexNone.invisible.groupHover(tw.visible).mx16,
            FilePermissionColumnPreviewer(
              dataRoomWorkflowId = props.dataRoomData.workflowId,
              folderId = folderId,
              actorUserId = props.actorUserId,
              title = s"${groupData.name}'s permissions",
              permissionTarget = PermissionTarget.SingleTeam(groupData.teamId),
              changes = changes,
              managePermissionTreePageUrl = None,
              showIndex = props.dataRoomData.latestState.dataRoomCreatedState.showIndex
            )()
          )
        }
      )
    }

    private def renderUnassignedPermissions(
      props: Props,
      validSelectionChoices: Map[UserId, Set[Option[FileFolderPermission]]]
    )(
      optionPermissionOpt: Option[FileFolderPermission]
    ) = {
      val validTargetUsers = validSelectionChoices.filter {
        _._2.contains(optionPermissionOpt)
      }.keySet
      if (validTargetUsers.nonEmpty) {
        val isNonRestrictedPermission = FileFolderPermissionUtils.optOrdering.gt(
          optionPermissionOpt,
          Option(DataRoomRoleUtils.maxRestrictedPermission)
        )
        <.div(
          ComponentUtils.testId(DataRoomFilePermissionModalContent, "UnassignedHeader"),
          tw.flex.justifyCenter,
          TooltipR(
            renderTarget = Button(
              style = Button.Style.Minimal(icon = Some(Icon.Glyph.CheckCircleLine)),
              onClick = onUpdateMultiplePermissions(
                props.permissionDetail,
                targetUsers = validTargetUsers,
                newPermissionOpt = optionPermissionOpt
              )
            )(),
            renderContent = _(
              "Apply to all unassigned users",
              Option.when(isNonRestrictedPermission)(
                React.Fragment(
                  <.br(),
                  "(except Observers)"
                )
              )
            )
          )()
        )
      } else {
        EmptyVdom
      }
    }

    private def renderGroupPermissions(
      props: Props,
      state: State,
      groupData: DataRoomGroupData
    )(
      optionPermissionOpt: Option[FileFolderPermission]
    ) = {
      val selectedPermissionOpt = props.permissionDetail.getGroup(
        groupData.id,
        state.groupPermissionChanges
      )
      <.div(
        ComponentUtils.testId(DataRoomFilePermissionModalContent, "GroupPermission"),
        tw.flex.justifyCenter,
        renderPermissionChoice(
          isChecked = selectedPermissionOpt == optionPermissionOpt,
          onUpdate = onUpdateGroupPermissions(props.permissionDetail, groupData)(optionPermissionOpt),
          disabledReasonOpt = getGroupDisabledReasonOpt(
            props,
            state,
            groupData.id
          )(optionPermissionOpt),
          optionPermissionOpt = optionPermissionOpt
        )
      )
    }

    private def renderGroupHeader(
      props: Props,
      state: State,
      groupData: DataRoomGroupData,
      isLargeScreen: Boolean
    ) = {
      val isActorInternal = props.dataRoomData.latestState.isUserRole(props.actorUserId)(DataRoomRoleUtils.isInternal)
      <.div(
        ComponentUtils.testId(DataRoomFilePermissionModalContent, "GroupHeader"),
        tw.wPc100,
        if (isLargeScreen && isActorInternal) {
          renderRadioRowLayout(
            renderName = renderGroupName(props, state, isLargeScreen)(groupData)
          ) { permissionOpt =>
            renderGroupPermissions(
              props,
              state,
              groupData
            )(permissionOpt)
          }
        } else {
          renderRowLayout(
            renderName = renderGroupName(props, state, isLargeScreen)(groupData),
            renderData = {
              val selectedPermissionOpt = props.permissionDetail.getGroup(
                groupData.id,
                state.groupPermissionChanges
              )
              renderDropdown(
                selectedPermissionOpt,
                onUpdateGroupPermissions(
                  props.permissionDetail,
                  groupData
                ),
                getGroupDisabledReasonOpt(
                  props,
                  state,
                  groupData.id
                )
              )
            }
          )
        }
      )
    }

    private def renderGroup(
      props: Props,
      state: State,
      isLargeScreen: Boolean
    )(
      groupData: DataRoomGroupData
    ) = {
      <.div(
        ^.key := groupData.id.idString,
        renderGroupHeader(
          props,
          state,
          groupData,
          isLargeScreen
        )
      )
    }

    private def isPermissionsChanged(props: Props, state: State) = {
      val noUserPermissionsChange =
        state.userPermissionChanges.isEmpty && state.userPermissionChanges == props.initialUserChanges
      val noGroupPermissionsChange =
        state.groupPermissionChanges.isEmpty && state.groupPermissionChanges == props.initialGroupChanges
      !(noUserPermissionsChange && noGroupPermissionsChange)
    }

    private def renderSaveButton(props: Props, state: State)(onClick: Callback) = {
      props.onSave.fold(EmptyVdom) { _ =>
        Button(
          style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.isBtnBusy),
          isDisabled = !isPermissionsChanged(props, state),
          onClick = onClick
        )("Save")
      }
    }

    private def renderContent(
      props: Props,
      state: State,
      isLargeScreen: Boolean
    ) = {
      <.div(
        tw.overflowYAuto,
        state.viewingMode match {
          case ViewingMode.Users  => renderUserView(props, state, isLargeScreen)
          case ViewingMode.Groups => renderGroupView(props, state, isLargeScreen)
        }
      )
    }

    private def renderUserView(props: Props, state: State, isLargeScreen: Boolean) = {
      props.dataRoomData.latestState.participatingUsers.toSeq
        .sortBy(_._2.userState.groupIds.size)(
          using Ordering[Int].reverse
        )
        .map { (userId, userData) =>
          renderUserPermissionRow(
            props,
            state,
            userId,
            userData,
            isLargeScreen
          )
        }
        .mkReactFragment(rowSep)
    }

    private def renderGroupView(props: Props, state: State, isLargeScreen: Boolean) = {
      props.dataRoomData.groupMap.toSeq
        .map { (_, groupData) => renderGroup(props, state, isLargeScreen)(groupData) }
        .mkReactFragment(rowSep)
    }

    private def renderPremiumSuggestion(props: Props, style: TagMod) = {
      if (!props.dataRoomData.dataRoomPlan.features.contains(DataRoomPremiumFeature.ViewOnly)) {
        <.div(
          style,
          UpgradeSuggestionCard(
            reason = "to enable View Only permission",
            isShowingComparePlans = false
          )()
        )
      } else {
        EmptyVdom
      }
    }

    private def renderFooter(props: Props, state: State) = {
      ModalFooter()(
        <.div(
          tw.flex.itemsCenter,
          renderPremiumSuggestion(props, tw.flexNone),
          renderWarning(props, state),
          <.div(tw.flexFill),
          <.div(
            tw.flex.mlAuto.spaceX8,
            Button(onClick = props.closeToggle)(props.onSave.fold("Close")(_ => "Cancel")),
            renderSaveButton(props, state)(onSave(props, state))
          )
        )
      )
    }

    private def renderHeader(props: Props) = {
      <.div(
        tw.flexNone.flex.itemsCenter,
        <.div(
          tw.flexNone.pr12,
          DmsIcon(
            iconType = props.item match {
              case folder: Item.Folder => DmsIcon.Folder(folder.childrenCount)
              case file: Item.File     => DmsIcon.File(file.name)
            }
          )()
        ),
        TooltipOnTruncate(
          renderTarget = <.div.withRef(_)(
            ComponentUtils.testId(DataRoomFilePermissionModalContent, "Filename"),
            tw.truncate,
            props.item.name
          ),
          content = props.item.name
        )()
      )
    }

    private def renderWarning(props: Props, state: State) = {
      if (props.onSave.nonEmpty && getFolderChildCount(props) > 0 && isPermissionsChanged(props, state)) {
        <.div(
          tw.flex.itemsCenter.mr8,
          <.div(tw.textWarning4.pr4, IconR(name = Icon.Glyph.Warning)()),
          <.p(
            "Changes to this folder's permission will also be applied to all files and subfolders."
          )
        )
      } else {
        EmptyVdom
      }
    }

    private def renderTableHeader(props: Props, state: State) = {
      <.div(
        tw.flexCol,
        <.div(
          tw.flex.itemsCenter.pb12,
//          renderSearchBox(state),
          renderViewSegment(props, state)
        ),
        <.div(
          tw.flex.itemsEnd.itemsCenter.py12,
          tw.borderBottom.borderGray2.border1,
          tw.sticky.top0.bgGray0.z2,
          <.div(
            tw.flexFill,
            FilePermissionExplanationPopover()
          ),
          permissionOptions.toVdomArray(
            using renderOptionHeader(props, state)
          )
        )
      )
    }

    private def renderSearchBox(state: State) = {
      <.div(
        tw.flex.justifyStart,
        SearchBox(
          placeholder = s"Find ${state.viewingMode.toString.toLowerCase}",
          initialValue = state.searchQuery,
          onChange = text => scope.modState(_.copy(searchQuery = text))
        )()
      )
    }

    private def renderViewSegment(props: Props, state: State) = {
      Option.when(props.dataRoomData.groupMap.nonEmpty)(
        <.div(
          tw.flex.flexFill,
          SegmentR(
            items = ViewingMode.values.toSeq.map(viewingMode => SegmentR.Item(renderContent = () => viewingMode.toString)),
            activeItem = Some(state.viewingMode.ordinal),
            onClick = Some(index => scope.modState(_.copy(viewingMode = ViewingMode.fromOrdinal(index), searchQuery = "")))
          )()
        )
      )
    }

    private def renderOptionHeader(props: Props, state: State)(optionPermissionOpt: Option[FileFolderPermission]) = {
      val isSelectedGroupPermission = props.dataRoomData.groupMap.keySet.forall { groupId =>
        optionPermissionOpt == props.permissionDetail.getGroup(
          groupId,
          state.groupPermissionChanges
        )
      }
      val isSelectedUserPermission = props.dataRoomData.unassignedParticipants.forall { userId =>
        optionPermissionOpt == props.permissionDetail.getUser(
          userId,
          state.userPermissionChanges,
          state.groupPermissionChanges
        )
      }
      val disabledReason = for {
        _ <- DataRoomPermissionCheck.ownToModify(props.actorPermissionOpt)
        _ <- FileFolderPermissionUtils.canChangePermission(
          props.actorPermissionOpt,
          currentPermissionOpt = None,
          optionPermissionOpt
        )
      } yield ()
      val title = FileFolderPermissionUtils.getPermissionName(optionPermissionOpt)
      <.div(
        ComponentUtils.testId(DataRoomFilePermissionModalContent, title),
        ^.key := title,
        tw.flexNone.wPx128.itemsCenter.justifyCenter,
        tw.hidden,
        tw.lg(tw.flex),
        TooltipR(
          renderTarget = {
            <.div(
              ^.width := "110px",
              SelectorR(
                isDisabled = disabledReason.isLeft || props.onSave.isEmpty,
                isSelected = isSelectedGroupPermission && isSelectedUserPermission,
                onClick = {
                  val (groupsToUpdate, unassignedUserToUpdate) =
                    if (state.viewingMode == ViewingMode.Groups) {
                      props.dataRoomData.groupMap.keySet.filterNot { groupId =>
                        getGroupDisabledReasonOpt(
                          props,
                          state,
                          groupId
                        )(optionPermissionOpt).isDefined
                      } -> Set.empty[UserId]
                    } else {
                      Set.empty[DataRoomGroupId] -> props.dataRoomData.unassignedParticipants.filterNot { userId =>
                        getUserDisabledReasonOpt(
                          props,
                          state,
                          userId,
                          groupIds = List.empty
                        )(optionPermissionOpt).isDefined
                      }
                    }
                  onUpdateMultiplePermissions(
                    props.permissionDetail,
                    targetUsers = unassignedUserToUpdate,
                    targetGroups = groupsToUpdate,
                    newPermissionOpt = optionPermissionOpt
                  )
                }
              )(
                <.div(
                  tw.py8.fontSemiBold,
                  TagMod.when(disabledReason.isLeft)(tw.opacity50),
                  title
                )
              )
            )
          },
          renderContent = _(disabledReason.left.getOrElse[String]("")),
          isDisabled = disabledReason.isRight
        )()
      )
    }

    def render(props: Props, state: State): VdomNode = {
      WithScreenWidthR(
        render = screenWidth => {
          val isLargeScreen = screenWidth >= ScreenWidth.Large
          React.Fragment(
            ModalHeader(
              title = renderHeader(props),
              isClosable = true,
              close = props.closeToggle
            )(),
            ModalBody()(
              <.div(
                tw.flex.flexCol.borderBottom.borderGray3,
                ^.maxHeight := 528.px,
                renderTableHeader(props, state),
                renderContent(
                  props,
                  state,
                  isLargeScreen
                )
              )
            ),
            renderFooter(props, state)
          )
        }
      )()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps { props =>
      State(
        viewingMode = ViewingMode.Users,
        searchQuery = "",
        userPermissionChanges = props.initialUserChanges,
        groupPermissionChanges = props.initialGroupChanges,
        lastSelectAllOption = None,
        isBtnBusy = false
      )
    }
    .renderBackend[Backend]
    .componentDidUpdate { scope =>
      Callback.when(
        scope.prevProps.initialUserChanges != scope.currentProps.initialUserChanges ||
          scope.prevProps.initialGroupChanges != scope.currentProps.initialGroupChanges
      )(
        scope.modState(
          _.copy(
            userPermissionChanges = scope.currentProps.initialUserChanges,
            groupPermissionChanges = scope.currentProps.initialGroupChanges
          )
        )
      )
    }
    .build

}
