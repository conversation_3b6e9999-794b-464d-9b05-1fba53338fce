// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.link

import anduin.dataroom.detail.link.LinkSettings.isNameValid
import anduin.dataroom.detail.link.step.{
  DataRoomInvitationLinkCreationResult,
  DataRoomLinkInvitationPermissions,
  DataRoomLinkInvitationSettings
}
import anduin.dataroom.detail.participants.invitation.{InvitationGroupSettings, PermissionSettings}
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.dataroom.{DataRoomData, DataRoomFrontEndState}
import anduin.id.link.ProtectedLinkId
import anduin.link.ProtectedLinkState
import anduin.model.common.user.UserId
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.stargazer.service.dataroom.{
  CreateDataRoomLinkInvitationParams,
  GetSharableLinkConfigParams,
  GetSharableLinkConfigResponse,
  ModifyDataRoomLinkInvitationParams
}
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.{Modal, ModalBody, ModalFooterWCancel}
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.react.SkeletonR
import design.anduin.components.stepper.react.StepperHeaderR
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import stargazer.model.routing.{DynamicAuthPage, Page}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.enumeration.StringEnum
import com.anduin.stargazer.client.utils.ZIOUtils

final case class ModifyDataRoomLinkInvitationSettings(
  router: RouterCtl[Page],
  renderTarget: Callback => VdomNode,
  dataRoomData: DataRoomData,
  actorUserId: UserId,
  mode: ModifyDataRoomLinkInvitationSettings.Mode,
  showManageLink: Boolean,
  onClose: Callback,
  setLink: CreationLinkResult => Callback,
  groupSettings: InvitationGroupSettings = InvitationGroupSettings.NoGroup,
  sharableLinkConfigOpt: Option[GetSharableLinkConfigResponse] = None
) {
  def apply(): VdomElement = ModifyDataRoomLinkInvitationSettings.component(this)
}

object ModifyDataRoomLinkInvitationSettings {

  private type Props = ModifyDataRoomLinkInvitationSettings

  sealed trait Mode derives CanEqual

  object Mode {

    case object Create extends Mode

    final case class Update(
      linkId: ProtectedLinkId,
      linkInvitationInfo: DataRoomFrontEndState.LinkInvitation,
      linkState: ProtectedLinkState
    ) extends Mode

  }

  enum Step(val value: String, val isConfiguring: Boolean) extends StringEnum {

    case Permission extends Step("Link permissions", isConfiguring = true)

    case Setting extends Step("Advanced settings", isConfiguring = true)

    case Result extends Step("Copy link", isConfiguring = false)
  }

  private final case class State(
    step: Step,
    permissionSettings: PermissionSettings,
    linkSettings: LinkSettings,
    isBusy: Boolean,
    isGetting: Boolean,
    sharableLinkConfigOpt: Option[GetSharableLinkConfigResponse] = None,
    linkResultOpt: Option[CreationLinkResult] = None
  )

  private final class Backend(scope: BackendScope[Props, State]) {

    private val permissionStateSnapshot = StateSnapshotWithModFn.withReuse
      .zoom[State, PermissionSettings](_.permissionSettings)(newValue => _.copy(permissionSettings = newValue))
      .prepareVia(scope)

    private val linkStateSnapshot = StateSnapshotWithModFn.withReuse
      .zoom[State, LinkSettings](_.linkSettings)(newValue => _.copy(linkSettings = newValue))
      .prepareVia(scope)

    private def getActorRoles(props: Props) = {
      props.dataRoomData.latestState.dataRoomCreatedState.individualRoles
        .getOrElse(props.actorUserId, DataRoomRole.Empty)
    }

    private def renderContent(props: Props, state: State)(closeToggle: Callback) = {
      <.div(
        tw.trnsA,
        renderStepHeader(state),
        renderStepBody(props, state),
        renderFooter(
          props,
          state,
          closeToggle
        )
      )
    }

    private def renderStepHeader(state: State) = {
      <.div(
        tw.py24,
        StepperHeaderR(
          titles = Step.values.map(_.value).toSeq,
          currentStep = Step.values.indexOf(state.step)
        )()
      )
    }

    private def renderStepBody(props: Props, state: State) = {
      if (state.isGetting) {
        <.div(
          tw.mb20,
          0.until(3)
            .toVdomArray(
              using { _ =>
                <.div(
                  tw.px32,
                  ^.marginTop := "12px",
                  SkeletonR(
                    effect = Skeleton.Effect.Wave,
                    height = "24px",
                    width = "100%",
                    shape = Skeleton.Shape.Rounded
                  )()
                )
              }
            )
        )
      } else {
        <.div(
          tw.flex.flexCol,
          <.div(
            tw.borderTop.borderGray3,
            ^.padding := "0px 28px 24px 28px",
            state.step match {
              case Step.Permission =>
                DataRoomLinkInvitationPermissions(
                  actorUserId = props.actorUserId,
                  dataRoomData = props.dataRoomData,
                  actorRoles = getActorRoles(props),
                  permissionSettings = permissionStateSnapshot(state),
                  linkSettings = linkStateSnapshot(state),
                  sharableLinkConfigOpt = state.sharableLinkConfigOpt
                )()
              case Step.Setting =>
                DataRoomLinkInvitationSettings(
                  actorUserId = props.actorUserId,
                  dataRoomData = props.dataRoomData,
                  actorRoles = getActorRoles(props),
                  permissionSettings = permissionStateSnapshot(state),
                  linkSettings = linkStateSnapshot(state),
                  sharableLinkConfigOpt = state.sharableLinkConfigOpt
                )()
              case Step.Result =>
                state.linkResultOpt.map { createdLink =>
                  DataRoomInvitationLinkCreationResult(
                    router = props.router,
                    dataRoomWorkflowId = props.dataRoomData.workflowId,
                    createdLink = createdLink,
                    closeToggle = Callback.empty,
                    showManageLink = props.showManageLink
                  )()
                }
            }
          ),
          if (state.step.isConfiguring) {
            state.sharableLinkConfigOpt.flatMap(_.ssoProviderName).map { ssoProviderName =>
              <.div(
                tw.flex.itemsCenter.justifyCenter,
                tw.wPc100.bgPrimary1.py8,
                <.div(tw.textPrimary4.mr8, IconR(name = Icon.Glyph.Info, size = Icon.Size.Px16)()),
                s"Participants will be required to log in with $ssoProviderName"
              )
            }
          } else {
            EmptyVdom
          }
        )
      }
    }

    private def onSubmit(props: Props, state: State) = {
      for {
        _ <- scope.modState(_.copy(isBusy = true))
        _ <- ZIOUtils.toReactCallback {
          for {
            either <- props.mode match {
              case Mode.Create =>
                DataRoomEndpointClient
                  .createLinkInvitation(
                    CreateDataRoomLinkInvitationParams(
                      dataRoomWorkflowId = props.dataRoomData.workflowId,
                      data = LinkSettings.toParams(state.linkSettings, state.permissionSettings),
                      password = state.linkSettings.securitySetting.password.password match {
                        case LinkSettings.PasswordSetting.Change(password)
                            if password.nonEmpty && state.linkSettings.securitySetting.isPasswordEnabled =>
                          Some(password)
                        case _ =>
                          None
                      },
                      enableEnterpriseLogin = state.linkSettings.isEnterpriseLoginEnabled
                    )
                  )
                  .map(_.map(_.linkId))
              case Mode.Update(linkId, _, linkState) =>
                DataRoomEndpointClient
                  .modifyLinkInvitation(
                    ModifyDataRoomLinkInvitationParams(
                      dataRoomWorkflowId = props.dataRoomData.workflowId,
                      linkId = linkId,
                      data = LinkSettings.toParams(state.linkSettings, state.permissionSettings),
                      password = if (state.linkSettings.securitySetting.isPasswordEnabled) {
                        state.linkSettings.securitySetting.password.password match {
                          case LinkSettings.PasswordSetting.Change(password) =>
                            Some(ModifyDataRoomLinkInvitationParams.PasswordChange(Some(password)))
                          case LinkSettings.PasswordSetting.UseExisting =>
                            None
                        }
                      } else {
                        Some(ModifyDataRoomLinkInvitationParams.PasswordChange(None))
                      },
                      enableEnterpriseLoginChange = Option.when(
                        linkState.isEnterpriseLoginEnabled != state.linkSettings.isEnterpriseLoginEnabled
                      )(state.linkSettings.isEnterpriseLoginEnabled)
                    )
                  )
                  .map(_.map(_ => linkId))
            }
            callback = either.fold(
              _ => Toast.errorCallback("Fail to update link. Please try again"),
              linkId => {
                val linkResult = CreationLinkResult(
                  linkId = linkId,
                  hasPassword = state.linkSettings.securitySetting.isPasswordEnabled,
                  expiryDate = Option.when(state.linkSettings.expiryDate.isEnabled) {
                    state.linkSettings.expiryDate.expiryDate
                  }
                )
                scope.modState(
                  _.copy(
                    step = Step.Result,
                    linkResultOpt = Some(linkResult)
                  ),
                  props.setLink(linkResult)
                )
              }
            )
          } yield scope.modState(_.copy(isBusy = false), callback)
        }
      } yield ()
    }

    private def renderFooter(props: Props, state: State, closeToggle: Callback) = {
      <.div(
        tw.borderTop.borderGray3,
        tw.flex.itemsCenter.justifyEnd,
        ^.padding := "30px 28px 30px 28px",
        <.div(
          tw.flexFill.flex.itemsCenter,
          Option.when(state.step == Step.Setting) {
            <.div(
              tw.mr16,
              Button(
                onClick = scope.modState(_.copy(step = Step.Permission))
              )("Back")
            )
          },
          Option.when(props.showManageLink) {
            val targetPage = DynamicAuthPage.DataRoomLinkDashboardPage(props.dataRoomData.workflowId)
            Button(
              style = Button.Style.Text(),
              tpe = Button.Tpe.Link(href = props.router.urlFor(targetPage).value),
              onClick = closeToggle
            )(
              <.div(
                ComponentUtils.testId(ModifyDataRoomLinkInvitationSettings, "Manage links"),
                tw.flex.itemsCenter,
                <.div(
                  tw.flexNone.pr8,
                  IconR(name = Icon.Glyph.OpenNewWindow)()
                ),
                <.div(
                  tw.flexNone,
                  "Manage your existing links"
                )
              )
            )
          }
        ),
        <.div(
          tw.flexNone.pr8,
          renderCloseButton(props, state)(closeToggle)
        ),
        <.div(
          tw.flexNone,
          renderConfirmationButton(props, state)
        )
      )
    }

    private def renderCloseButton(props: Props, state: State)(closeToggle: Callback) = {
      val initialState = getInitialState(props)
      state.step match {
        case Step.Permission | Step.Setting =>
          if (
            initialState.linkSettings == state.linkSettings && initialState.permissionSettings == state.permissionSettings
          ) {
            Button(onClick = scope.setState(initialState, closeToggle))("Cancel")
          } else {
            renderCancelConfirmationModal(initialState, closeToggle)
          }
        case Step.Result =>
          Button(onClick = scope.setState(initialState, closeToggle))("Close")
      }
    }

    private def renderCancelConfirmationModal(initialState: State, closeToggle: Callback) = {
      Modal(
        title = "Cancel invitation link?",
        renderTarget = openConfirmModal => Button(onClick = openConfirmModal)("Cancel"),
        renderContent = closeConfirmModal =>
          React.Fragment(
            ModalBody()(
              <.p("Are you sure you want to cancel? All changes will be lost.")
            ),
            ModalFooterWCancel(closeConfirmModal, "Back")(
              Button(
                style = Button.Style.Full(color = Button.Color.Primary),
                onClick = scope.setState(initialState, closeConfirmModal >> closeToggle)
              )("Confirm")
            )
          )
      )()
    }

    private def renderConfirmationButton(props: Props, state: State): VdomNode = {
      val isGroupDeleted = state.linkSettings.groupSettings.groupIds.exists(
        props.dataRoomData.deletedGroups.contains
      )
      state.step match {
        case Step.Permission =>
          Button(
            style = Button.Style.Full(color = Button.Color.Primary),
            onClick = scope.modState(_.copy(step = Step.Setting)),
            isDisabled = !isNameValid(state.linkSettings) || isGroupDeleted || !state.linkSettings.groupSettings.isValid
          )("Next")
        case Step.Setting =>
          val initialState = getInitialState(props)
          val isLinkChanged =
            state.linkSettings != initialState.linkSettings || state.permissionSettings != initialState.permissionSettings
          Button(
            style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.isBusy),
            onClick = onSubmit(
              props,
              state
            ),
            isDisabled = !LinkSettings.isValid(state.linkSettings) || !isLinkChanged || state.isGetting
          )(
            props.mode match {
              case Mode.Create    => "Create"
              case _: Mode.Update => "Update"
            }
          )
        case Step.Result =>
          EmptyVdom
      }
    }

    def render(props: Props, state: State): VdomNode = {
      Modal(
        renderTarget = props.renderTarget,
        renderContent = renderContent(props, state),
        size = Modal.Size(width = Modal.Width.Px600),
        afterUserClose = props.onClose,
        afterOpen = getSharableLinkConfig(props),
        isClosable = None
      )()
    }

    private def getSharableLinkConfig(props: Props) = {
      props.sharableLinkConfigOpt.fold(
        scope.modState(
          _.copy(sharableLinkConfigOpt = None, isGetting = true),
          ZIOUtils.toReactCallback(
            DataRoomEndpointClient
              .getSharableLinkConfig(GetSharableLinkConfigParams(props.dataRoomData.workflowId))
              .map(
                _.fold(
                  _ => scope.modState(_.copy(isGetting = false)),
                  resp => scope.modState(_.copy(isGetting = false, sharableLinkConfigOpt = Some(resp)))
                )
              )
          )
        )
      ) { config =>
        scope.modState(_.copy(sharableLinkConfigOpt = Some(config), isGetting = false))
      }
    }

  }

  private def getInitialPermissionSettings(props: Props) = {
    props.mode match {
      case Mode.Create =>
        PermissionSettings(
          role = props.groupSettings.getRole(props.dataRoomData.getGroupData(_)),
          assetPermission = props.groupSettings match {
            case InvitationGroupSettings.NoGroup =>
              AssetPermissionChanges.allFoldersWithRootChannel(
                channel = props.dataRoomData.workflowId,
                permission = FileFolderPermission.Read
              )
            case InvitationGroupSettings.AddToGroups(groupIds) =>
              AssetPermissionChanges()
          }
        )
      case Mode.Update(_, linkInvitationInfo, _) =>
        PermissionSettings(
          role = if (linkInvitationInfo.groupIds.isEmpty) {
            linkInvitationInfo.role
          } else {
            DataRoomRoleUtils.getMaxRole(
              linkInvitationInfo.groupIds.flatMap(props.dataRoomData.getGroupData(_)).map(_.role)
            )
          },
          assetPermission = if (linkInvitationInfo.groupIds.isEmpty) {
            linkInvitationInfo.permissions.getOrElse(AssetPermissionChanges())
          } else {
            AssetPermissionChanges()
          }
        )
    }
  }

  private def getInitialState(props: Props) = {
    props.mode match {
      case Mode.Create =>
        State(
          step = Step.Permission,
          permissionSettings = getInitialPermissionSettings(props),
          linkSettings = LinkSettings.default(props.groupSettings),
          isBusy = false,
          isGetting = false
        )
      case Mode.Update(_, linkInvitationInfo, linkState) =>
        State(
          step = Step.Permission,
          permissionSettings = getInitialPermissionSettings(props),
          linkSettings = LinkSettings(
            name = linkInvitationInfo.name,
            securitySetting = LinkSettings.SecuritySetting(
              isEnabled = linkState.hasPassword || linkState.isRequiredApproval,
              securityType = if (linkState.isRequiredApproval) {
                LinkSettings.SecurityType.AdminApproval
              } else {
                LinkSettings.SecurityType.Password
              },
              password = LinkSettings.PasswordSetting(
                password = if (linkState.hasPassword) {
                  LinkSettings.PasswordSetting.UseExisting
                } else {
                  LinkSettings.PasswordSetting.Change("")
                },
                initialIsEnabled = linkState.hasPassword
              )
            ),
            expiryDate = LinkSettings.ExpiryDateSetting(
              expiryDate = linkState.expiryDate.getOrElse(LinkSettings.ExpiryDateSetting.default),
              isEnabled = linkState.expiryDate.nonEmpty
            ),
            isDisabled = false,
            whitelistedDomains = LinkSettings.WhitelistedDomainSetting(
              domains = linkState.whitelistedDomains.toSeq,
              isEnabled = linkState.whitelistedDomains.nonEmpty
            ),
            isEnterpriseLoginEnabled = linkState.isEnterpriseLoginEnabled,
            groupSettings = if (linkInvitationInfo.groupIds.isEmpty) {
              InvitationGroupSettings.NoGroup
            } else {
              InvitationGroupSettings.AddToGroups(linkInvitationInfo.groupIds)
            },
            isToaWhitelisted = linkInvitationInfo.isToaWhitelisted
          ),
          isBusy = false,
          isGetting = false
        )
    }
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps(getInitialState)
    .renderBackend[Backend]
    .componentDidUpdate { scope =>
      val prevGroupData = scope.currentState.linkSettings.groupSettings.groupIds
        .flatMap(groupId => scope.prevProps.dataRoomData.getGroupData(groupId, includeDeleted = true))
      val currentGroupData = scope.currentState.linkSettings.groupSettings.groupIds
        .flatMap(groupId => scope.currentProps.dataRoomData.getGroupData(groupId, includeDeleted = true))
      if (scope.prevProps.groupSettings != scope.currentProps.groupSettings) {
        scope.setState(getInitialState(scope.currentProps))
      } else if (prevGroupData != currentGroupData) {
        scope.modState(_.copy(permissionSettings = getInitialPermissionSettings(scope.currentProps)))
      } else {
        Callback.empty
      }
    }
    .build

}
