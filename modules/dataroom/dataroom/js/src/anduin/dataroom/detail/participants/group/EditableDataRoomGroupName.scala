// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.group

import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.group.RenameDataRoomGroupParams
import anduin.id.dataroom.DataRoomGroupId
import design.anduin.components.inline.react.InlineEditR
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import scala.annotation.unused

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.anduin.stargazer.client.utils.ZIOUtils

private[participants] final case class EditableDataRoomGroupName(
  groupId: DataRoomGroupId,
  groupName: String,
  editable: Boolean
) {
  def apply(): VdomElement = EditableDataRoomGroupName.component(this)
}

private[participants] object EditableDataRoomGroupName {

  private type Props = EditableDataRoomGroupName

  private class Backend(@unused scope: BackendScope[Props, Unit]) { // scalafix:ok

    def render(props: Props): VdomNode = {
      InlineEditR(
        value = props.groupName,
        renderViewMode = renderProps => {
          <.div(
            tw.flex.itemsCenter,
            <.div(
              ComponentUtils.testId(EditableDataRoomGroupName, "GroupName"),
              tw.breakWords.mr4,
              renderProps.value
            ),
            TagMod.when(props.editable) {
              renderProps.renderDefaultSwitcher("Rename group")
            }
          )
        },
        renderEditMode = renderProps => {
          <.div(
            tw.fontNormal,
            renderProps.renderContainer
          )
        },
        onSave = renameGroup(props.groupId, _)
      )()
    }

    private def renameGroup(groupId: DataRoomGroupId, name: String) = {
      ZIOUtils.toReactCallback {
        DataRoomEndpointClient
          .renameGroup(
            RenameDataRoomGroupParams(
              groupId = groupId,
              name = name
            )
          )
          .map {
            _.fold(
              _ => Toast.errorCallback("Failed to rename group"),
              _ => Toast.successCallback("Group renamed")
            )
          }
      }
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .renderBackend[Backend]
    .build

}
