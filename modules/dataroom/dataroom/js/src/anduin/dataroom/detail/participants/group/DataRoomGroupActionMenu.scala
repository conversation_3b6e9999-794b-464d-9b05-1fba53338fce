// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.group

import anduin.dataroom.detail.participants.DataRoomManualNotificationModal
import anduin.dataroom.detail.participants.`export`.{DataRoomExportParticipantsModal, DataRoomExportPermissionsModal}
import anduin.dataroom.group.DataRoomGroupData
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.dataroom.terms.DataRoomUserTermsStatusModal
import anduin.dataroom.{DataRoomData, DataRoomPermissionCheck}
import anduin.id.dataroom.DataRoomGroupId
import anduin.layout.SelectionData
import anduin.model.common.user.UserId
import anduin.utils.StateSnapshotWithModFn
import design.anduin.components.icon.Icon
import design.anduin.components.menu.react.{MenuDividerR, MenuItemR, MenuR}
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import anduin.file.explorer.header.FileHeaderActions
import anduin.stargazer.service.dataroom.DataRoomExportPermissionsParams.ExportPermissionTarget
import design.anduin.components.tooltip.react.TooltipR
import stargazer.component.routing.react.WithReactRouterR
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[participants] final case class DataRoomGroupActionMenu(
  actorUserId: UserId,
  dataRoomData: DataRoomData,
  rows: List[DataRoomGroupData],
  groupSelection: StateSnapshotWithModFn[SelectionData[DataRoomGroupId]],
  onClose: Callback
) {

  private val isAdmin = dataRoomData.latestState.isUserRole(actorUserId)(DataRoomRoleUtils.isAdmin)
  private val isInternal = dataRoomData.latestState.isUserRole(actorUserId)(DataRoomRoleUtils.isInternal)
  private val toaOptions = dataRoomData.latestState.dataRoomCreatedState.termsOfAccessOptions
  private val hasToA = dataRoomData.latestState.dataRoomCreatedState.termsOfAccessOptions.isEnabled
  private val groupIds = rows.map(_.id).toSet
  private val groupUserIds = rows.flatMap(_.participants).toSet

  def apply(): VdomElement = DataRoomGroupActionMenu.component(this)
}

private[participants] object DataRoomGroupActionMenu {

  private type Props = DataRoomGroupActionMenu

  private def onSingleSelection(props: Props) = {
    props.rows.headOption.filter(_ => props.rows.size == 1)
  }

  private def render(props: Props) = {
    MenuR()(
      renderGroupPermissions(props),
      renderRenameGroup(props),
      renderMessageGroups(props),
      renderViewToaStatus(props),
      renderExportParticipants(props),
      renderExportPermissions(props),
      renderDeleteGroups(props)
    )
  }

  private def withPlanCheck(props: Props)(renderTarget: Boolean => VdomNode) = {
    FileHeaderActions.renderButtonWithTooltip {
      DataRoomPermissionCheck.expiredPlan(props.dataRoomData.dataRoomPlan)
    } { res =>
      renderTarget(res.isEmpty)
    }
  }

  private def renderGroupPermissions(props: Props) = {
    onSingleSelection(props).map { groupData =>
      withPlanCheck(props) { isDisabled =>
        Option.when(props.isInternal)(
          DataRoomGroupPermissionModal(
            actorUserId = props.actorUserId,
            dataRoomData = props.dataRoomData,
            groupData = groupData,
            renderTarget = (canEdit, openToggle) =>
              MenuItemR(
                icon = Some(if (canEdit) Icon.Glyph.Edit else Icon.Glyph.Eye),
                onClick = openToggle,
                isDisabled = isDisabled
              )(if (canEdit) "Edit group permissions" else "View group permissions"),
            onClose = props.onClose
          )()
        )
      }
    }
  }

  private def renderRenameGroup(props: Props) = {
    onSingleSelection(props).map { groupData =>
      withPlanCheck(props) { isDisabled =>
        Option.when(props.isAdmin)(
          RenameDataRoomGroupModal(
            groupId = groupData.id,
            groupName = groupData.name,
            renderTarget = openToggle =>
              MenuItemR(
                icon = Some(Icon.Glyph.EditField),
                onClick = openToggle,
                isDisabled = isDisabled
              )("Rename group"),
            onClose = props.onClose
          )()
        )
      }
    }
  }

  private def renderMessageGroups(props: Props) = {
    Option.when(props.isInternal) {
      withPlanCheck(props) { isDisabled =>
        DataRoomManualNotificationModal(
          userId = props.actorUserId,
          dataRoomData = props.dataRoomData,
          userRowDataMap = Map(),
          initialSelection = props.groupUserIds,
          renderTarget = openToggle =>
            MenuItemR(
              icon = Some(Icon.Glyph.Envelope),
              onClick = openToggle,
              isDisabled = isDisabled || props.groupUserIds.isEmpty
            )("Message group participants"),
          onClose = props.onClose,
          showSelection = false
        )()
      }
    }
  }

  private def renderViewToaStatus(props: Props) = {
    props.dataRoomData
      .getToaFileIdOpt()
      .filter { _ =>
        props.isInternal || !props.toaOptions.whitelistedUsers.contains(props.actorUserId)
      }
      .map { toaFileId =>
        <.div(
          tw.flexNone.flex,
          DataRoomUserTermsStatusModal(
            userId = props.actorUserId,
            renderTarget = openToggle =>
              MenuItemR(
                icon = Some(Icon.Glyph.CheckList),
                isDisabled = !props.hasToA || props.groupUserIds.isEmpty,
                onClick = openToggle
              )("View record of consent"),
            toaFileId = toaFileId,
            dataRoomData = props.dataRoomData,
            onClose = props.onClose,
            filterUserFn = props.groupUserIds.contains
          )()
        )
      }
  }

  private def renderExportParticipants(props: Props) = {
    withPlanCheck(props) { isDisabled =>
      DataRoomExportParticipantsModal(
        props.dataRoomData.workflowId,
        props.groupUserIds,
        props.hasToA,
        props.onClose,
        renderTarget = openToggle =>
          MenuItemR(
            icon = Some(Icon.Glyph.Table),
            isDisabled = isDisabled || props.groupUserIds.isEmpty,
            onClick = openToggle
          )("Export participant's data"),
        isGlobalAction = false
      )()
    }
  }

  private def renderExportPermissions(props: Props) = {
    withPlanCheck(props) { isDisabled =>
      DataRoomExportPermissionsModal(
        props.dataRoomData.workflowId,
        ExportPermissionTarget.Users(props.groupUserIds),
        props.onClose,
        renderTarget = openToggle =>
          MenuItemR(
            icon = Some(Icon.Glyph.Key),
            isDisabled = isDisabled || props.groupUserIds.isEmpty,
            onClick = openToggle
          )("Export participant's permissions")
      )()
    }
  }

  private def renderDeleteGroups(props: Props) = {
    withPlanCheck(props) { isDisabled =>
      Option.when(props.isAdmin)(
        React.Fragment(
          MenuDividerR()(),
          WithReactRouterR { router =>
            DeleteDataRoomGroupModal(
              router = router,
              renderTarget = targetProps =>
                TooltipR(
                  renderTarget = MenuItemR(
                    icon = Some(Icon.Glyph.Trash),
                    isDisabled = isDisabled || targetProps.disabledReasonOpt.nonEmpty,
                    onClick = targetProps.openToggle,
                    color = MenuItemR.ColorDanger
                  )("Delete group"),
                  renderContent = _(targetProps.disabledReasonOpt),
                  isDisabled = targetProps.disabledReasonOpt.isEmpty
                )(),
              actorUserId = props.actorUserId,
              dataRoomWorkflowId = props.dataRoomData.workflowId,
              dataRoomData = props.dataRoomData,
              groupDataList = props.rows,
              onDelete = props.groupSelection.modState(_ -- props.groupIds),
              onClose = props.onClose
            )()
          }
        )
      )
    }
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
