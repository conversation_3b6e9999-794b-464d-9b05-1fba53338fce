// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.invitation.message

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.{<PERSON>dalBody, ModalFooter}
import design.anduin.components.tag.react.TagR
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import zio.ZIO

import anduin.dataroom.detail.participants.invitation.*
import anduin.dataroom.detail.participants.invitation.InvitationInfo.InvitationStatus
import anduin.dataroom.email.{DataRoomEditableEmailTemplate, EmailTemplateEditor}
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.scalajs.pluralize.Pluralize
import anduin.stargazer.service.dataroom.{
  DataRoomGroupPermissionChanges,
  DataRoomPermissionChanges,
  InviteUsersToDataRoomParams
}
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.client.utils.ZIOUtils

final case class MessageStep(
  step: StateSnapshotWithModFn[InvitationStep],
  users: StateSnapshotWithModFn[InvitationUsers],
  invitationInfo: StateSnapshotWithModFn[InvitationInfo],
  message: StateSnapshotWithModFn[InvitationMessage],
  emailTemplateOpt: Option[DataRoomEditableEmailTemplate],
  dataRoomWorkflowId: DataRoomWorkflowId,
  groupSettings: InvitationGroupSettings,
  closeToggle: Callback,
  isInvitationEmailEnabled: Boolean
) {
  def apply(): VdomElement = MessageStep.component(this)
}

object MessageStep {

  private type Props = MessageStep

  private final case class State(
    isBusy: Boolean
  )

  private val MaxBatchInvitationCount = 10

  private final class Backend(scope: BackendScope[Props, State]) {

    private def renderSection(
      title: String,
      subtitle: VdomNode = EmptyVdom
    )(
      input: VdomNode*
    ) = {
      <.label(
        <.div(
          tw.flex.itemsCenter.pb4,
          <.span(
            tw.textGray8.fontSemiBold.text13.leading20,
            title
          ),
          <.div(tw.flexFill),
          <.span(subtitle)
        ),
        <.div(
          tw.pb12,
          React.Fragment(input*)
        )
      )
    }

    private def renderRecipients(props: Props) = {
      val emailCount = props.users.value.emails.length
      val emailDisplay = Pluralize(
        "email",
        emailCount,
        true
      )
      val subtitle = if (emailCount > 0) {
        <.span(
          ComponentUtils.testId(MessageStep, "EmailNumbers"),
          tw.ml8.text13.leading20.textGray7,
          emailDisplay
        )
      } else {
        EmptyVdom
      }
      renderSection("Recipients", subtitle)(
        <.div(
          ^.maxHeight := "150px",
          tw.flex.flexWrap.pl6.pr2.py2.overflowYAuto,
          tw.borderAll.borderGray4.border1.rounded3,
          props.users.value.emails.toVdomArray(
            using { email =>
              <.div(
                ComponentUtils.testId(MessageStep, "Recipients"),
                tw.flex.itemsCenter.hPx24.pr4.my2,
                TagR(label = email)()
              )
            }
          )
        )
      )
    }

    private def getUserRoleMap(users: InvitationUsers) = {
      val userList = users.emails.map(_ -> (users.settings.role -> users.settings.assetPermission))
      userList.toMap.view.mapValues { case (roleSet, changes) =>
        DataRoomPermissionChanges(Some(roleSet), changes)
      }.toMap
    }

    private def onClickSend(props: Props) = {
      val emailCount = props.users.value.emails.length
      if (emailCount <= MaxBatchInvitationCount) {
        sendSmallBatchInvitation(props)
      } else {
        sendLargeBatchInvitation
      }
    }

    private def inviteUsers(props: Props, userRoleMap: Map[String, DataRoomPermissionChanges]) = {
      DataRoomEndpointClient.inviteUsers(
        InviteUsersToDataRoomParams(
          dataRoomWorkflowId = props.dataRoomWorkflowId,
          individualPermissionMap = if (props.groupSettings.isNoGroup) userRoleMap else Map(),
          groupPermissionMap = props.groupSettings match {
            case InvitationGroupSettings.NoGroup => Map()
            case InvitationGroupSettings.AddToGroups(groupIds) =>
              userRoleMap.map { case (email, _) =>
                email -> DataRoomGroupPermissionChanges(
                  groupIds = groupIds,
                  canInvite = DataRoomRoleUtils.canInvite(props.users.value.settings.role)
                )
              }
          },
          isToaRequired = props.users.value.isToaRequired,
          subject = props.message.value.subject,
          message = props.message.value.message,
          buttonLabel = props.message.value.buttonLabel,
          shouldSendEmail = props.isInvitationEmailEnabled
        )
      )
    }

    private def sendSmallBatchInvitation(props: Props) = {
      val userRoleMap = getUserRoleMap(props.users.value)
      val userDisplay = Pluralize(
        "user",
        userRoleMap.size,
        false
      )
      val actionName = if (props.isInvitationEmailEnabled) ("invite", "invited") else ("add", "added")
      for {
        _ <- scope.modState(_.copy(isBusy = true))
        _ <- ZIOUtils.toReactCallback {
          for {
            resp <- inviteUsers(props, userRoleMap)
          } yield {
            resp.fold(
              _ =>
                scope.modState(
                  _.copy(isBusy = false),
                  Toast.errorCallback(s"Failed to ${actionName._1} $userDisplay. Please try again")
                ),
              _ =>
                scope.modState(
                  _.copy(isBusy = false),
                  props.closeToggle >> Toast.successCallback(s"${userDisplay.capitalize} ${actionName._2} successfully")
                )
            )
          }
        }
      } yield ()
    }

    private def sendLargeBatchInvitation = {
      for {
        props <- scope.props
        _ <- props.invitationInfo.modState(_.startInvitation(props.users.value.emails.toSet), props.closeToggle)
        _ <- sendBatchInvitationRecursive(props, props.users.value.emails)
      } yield ()
    }

    private def sendBatchInvitationRecursive(props: Props, emails: Vector[String]): Callback = {
      Callback.when(emails.nonEmpty) {
        val batchEmails = emails.take(MaxBatchInvitationCount)
        val batchUsers = props.users.value.copy(emails = batchEmails)
        val userRoleMap = getUserRoleMap(batchUsers)
        ZIOUtils.toReactCallback {
          inviteUsers(props, userRoleMap)
            .map(resp =>
              props.invitationInfo.modState(
                _.updateInvitationStatus(
                  batchEmails.toSet,
                  resp.fold(_ => InvitationStatus.Failed, _ => InvitationStatus.Success)
                )
              )
            )
            .catchAll(_ =>
              ZIO.attempt {
                props.invitationInfo.modState(
                  _.updateInvitationStatus(
                    batchEmails.toSet,
                    InvitationStatus.Failed
                  )
                )
              }
            )
        } >> sendBatchInvitationRecursive(props, emails.drop(MaxBatchInvitationCount))
      }
    }

    private def getIsDisabled(props: Props) = {
      if (props.isInvitationEmailEnabled) {
        for {
          _ <- Either.cond(
            props.message.value.subject.trim.nonEmpty,
            (),
            "Subject must be non-empty"
          )
          _ <- Either.cond(
            props.message.value.buttonLabel.trim.nonEmpty,
            (),
            "Email call-to-action must be non-empty"
          )
        } yield ()
      } else {
        Right(())
      }
    }

    private def renderFooter(props: Props, state: State) = {
      val disabledReason = getIsDisabled(props)
      ModalFooter()(
        <.div(
          tw.flex.itemsCenter.justifyBetween.spaceX8,
          Button(
            style = Button.Style.Full(),
            onClick = props.step.setState(InvitationStep.Permissions)
          )("Back"),
          TooltipR(
            renderTarget = Button(
              style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.isBusy),
              isDisabled = disabledReason.isLeft,
              onClick = onClickSend(props)
            )(if (props.isInvitationEmailEnabled) "Send invitation" else "Add participants"),
            renderContent = _(disabledReason.left.getOrElse[String]("")),
            isDisabled = disabledReason.isRight
          )()
        )
      )
    }

    private def renderEmailEditor(props: Props) = {
      React.Fragment(
        renderRecipients(props),
        EmailTemplateEditor(
          emailTemplate = props.message.value.toEmailTemplate,
          resetEmailTemplateOpt = props.emailTemplateOpt,
          onTemplateChange =
            (newTemplate, cb) => props.message.setState(InvitationMessage.fromEmailTemplate(newTemplate), cb)
        )()
      )
    }

    private def renderEmailDisabledExplanation = {
      React.Fragment(
        <.div(
          tw.flex.itemsCenter.bgWarning1.p12.rounded4,
          <.div(
            tw.textWarning4,
            IconR(Icon.Glyph.Warning)()
          ),
          <.div(
            tw.textGray8.ml8,
            "An admin has disabled invitation emails for this data room"
          )
        ),
        <.p(
          tw.mt16,
          "By clicking on ",
          <.span(tw.fontSemiBold, "Add participants"),
          " below, new users will be added to the data room but no email notifications will be sent to them. ",
          "They will be able to see the pending invitation when logging in to the app."
        )
      )
    }

    def render(props: Props, state: State): VdomNode = {
      React.Fragment(
        ModalBody()(
          if (props.isInvitationEmailEnabled) {
            renderEmailEditor(props)
          } else {
            renderEmailDisabledExplanation
          }
        ),
        renderFooter(props, state)
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State(isBusy = false))
    .renderBackend[Backend]
    .build

}
