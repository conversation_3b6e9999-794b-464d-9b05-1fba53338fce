// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.settings

import anduin.component.util.JsDateFormatterUtils
import anduin.dataroom.detail.link.step.DataRoomInvitationLinkCreationResult
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.{DataRoomData, DataRoomFrontEndState}
import anduin.id.link.ProtectedLinkId
import anduin.link.ProtectedLinkState
import anduin.model.common.user.UserId
import anduin.protobuf.dataroom.link.DataRoomLinkInvitationParamsData
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.stargazer.service.dataroom.{
  GetSharableLinkConfigParams,
  GetSharableLinkConfigResponse,
  ModifyDataRoomLinkInvitationParams
}
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import com.anduin.stargazer.utils.FileFolderPermissionUtils
import design.anduin.components.button.Button
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.clipboard.react.CopyToClipboardR
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.menu.react.{MenuDividerR, MenuItemR, MenuR}
import design.anduin.components.nonidealstate.react.NonIdealStateR
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.components.switcher.react.SwitcherR
import design.anduin.components.table.Table
import design.anduin.components.util.ComponentUtils
import design.anduin.components.wrapper.TargetWrapper
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import stargazer.model.routing.{DynamicNonAuthPage, Page}
import anduin.dataroom.detail.link.*
import com.raquo.laminar.api.L.*
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.dataroom.detail.link.step.DataRoomInvitationLinkCreationResult.SetLink
import anduin.dataroom.detail.participants.group.DataRoomGroupNameTag
import anduin.dataroom.detail.participants.group.DataRoomGroupNameTag.RenderGroupData
import com.anduin.stargazer.client.utils.ZIOUtils

private[detail] final case class DataRoomLinkDashboard(
  router: RouterCtl[Page],
  userId: UserId,
  dataRoomData: DataRoomData
) {
  def apply(): VdomElement = DataRoomLinkDashboard.component(this)
}

object DataRoomLinkDashboard {

  def isReadOnly(configOpt: Option[GetSharableLinkConfigResponse]): Boolean = !configOpt.exists(_.isSharableLinkEnabled)

  private type Props = DataRoomLinkDashboard

  private final case class State(
    isGetting: Boolean = true,
    sharableLinkConfigOpt: Option[GetSharableLinkConfigResponse] = None
  ) {
    def isSharableLinkEnabled: Boolean = sharableLinkConfigOpt.exists(_.isSharableLinkEnabled)
  }

  private final case class Row(
    linkId: ProtectedLinkId,
    linkState: ProtectedLinkState,
    linkInvitationInfo: DataRoomFrontEndState.LinkInvitation,
    enterpriseLoginUrl: Option[String]
  )

  private final class Backend(scope: BackendScope[Props, State]) {

    private val setLinkCB: DataRoomInvitationLinkCreationResult.SetLink = _ => Callback.empty

    private val LinkTable = (new Table[Row])()

    private val mediumColWidth = "228px"

    private def getRows(props: Props) = {
      for {
        (linkId, linkInvitationInfo) <- props.dataRoomData.latestState.dataRoomCreatedState.linkInvitationMap.toList
        linkStateWithEnterpriseLogin <- props.dataRoomData.latestState.linkStateMap.get(linkId)
      } yield Row(
        linkId,
        linkStateWithEnterpriseLogin.linkState,
        linkInvitationInfo,
        linkStateWithEnterpriseLogin.enterpriseLoginUrl
      )
    }

    private def renderModifyLinkSettings(
      props: Props,
      state: State,
      item: Row,
      onClose: Callback,
      setLink: DataRoomInvitationLinkCreationResult.SetLink
    )(
      renderTarget: Callback => VdomNode
    ) = {
      ModifyDataRoomLinkInvitationSettings(
        router = props.router,
        renderTarget = renderTarget,
        dataRoomData = props.dataRoomData,
        actorUserId = props.userId,
        mode = ModifyDataRoomLinkInvitationSettings.Mode.Update(
          linkId = item.linkId,
          linkInvitationInfo = item.linkInvitationInfo,
          linkState = item.linkState
        ),
        showManageLink = false,
        onClose = onClose,
        setLink = setLink,
        sharableLinkConfigOpt = state.sharableLinkConfigOpt
      )()
    }

    private def renderCreateLink(
      props: Props,
      state: State,
      setLink: DataRoomInvitationLinkCreationResult.SetLink
    )(
      renderTarget: Callback => VdomNode
    ) = {
      ModifyDataRoomLinkInvitationSettings(
        router = props.router,
        renderTarget = renderTarget,
        dataRoomData = props.dataRoomData,
        actorUserId = props.userId,
        mode = ModifyDataRoomLinkInvitationSettings.Mode.Create,
        showManageLink = false,
        onClose = Callback.empty,
        setLink = setLink,
        sharableLinkConfigOpt = state.sharableLinkConfigOpt
      )()
    }

    private def onChangeIsDisabled(props: Props, row: Row, isDisabled: Boolean) = {
      ZIOUtils.toReactCallbackUnit {
        DataRoomEndpointClient.modifyLinkInvitation(
          ModifyDataRoomLinkInvitationParams(
            dataRoomWorkflowId = props.dataRoomData.workflowId,
            linkId = row.linkId,
            data = DataRoomLinkInvitationParamsData(
              name = row.linkInvitationInfo.name,
              expiryDate = row.linkState.expiryDate,
              isDisabled = isDisabled,
              whitelistedDomains = row.linkState.whitelistedDomains,
              role = row.linkInvitationInfo.role,
              permissionChanges = row.linkInvitationInfo.permissions,
              isRequiredAdminApproval = row.linkState.isRequiredApproval,
              groupIds = row.linkInvitationInfo.groupIds
            ),
            password = None,
            enableEnterpriseLoginChange = None
          )
        )
      }
    }

    private def getStatusColumn(props: Props, state: State, setLink: DataRoomInvitationLinkCreationResult.SetLink) =
      Table.Column[Row](
        head = "",
        render = { item =>
          Table.Cell(
            <.div(
              ComponentUtils.testId(DataRoomLinkDashboard, "Status"),
              renderModifyLinkSettings(
                props = props,
                state = state,
                item = item,
                onClose = Callback.empty,
                setLink = setLink
              ) { openToggle =>
                SwitcherR(
                  isChecked = !item.linkState.isExpired && !item.linkState.isDisabled && state.isSharableLinkEnabled,
                  onChange = { isEnabled =>
                    if (isEnabled && item.linkState.isExpired) {
                      openToggle
                    } else {
                      onChangeIsDisabled(
                        props,
                        item,
                        !isEnabled
                      )
                    }
                  },
                  isDisabled = isReadOnly(state.sharableLinkConfigOpt)
                )()
              }
            )
          )
        },
        width = "70px"
      )

    private def getNameColumn(props: Props, state: State, setLink: DataRoomInvitationLinkCreationResult.SetLink) =
      Table.Column[Row](
        head = "Invitation link",
        render = { item =>
          val url =
            item.enterpriseLoginUrl.getOrElse(props.router.urlFor(DynamicNonAuthPage.ProtectedLink(item.linkId)).value)
          val hasDeletedGroup = item.linkInvitationInfo.groupIds.nonEmpty && item.linkInvitationInfo.groupIds.forall(
            groupId => props.dataRoomData.getGroupData(groupId, includeDeleted = true).exists(_.isDeleted)
          )
          Table.Cell(
            <.div(
              tw.text13.leading20.itemsCenter,
              <.div(
                tw.flex,
                <.div(
                  ComponentUtils.testId(DataRoomLinkDashboard, "Link-name"),
                  tw.textGray8.fontSemiBold.truncate,
                  item.linkInvitationInfo.name
                ),
                Option.when(hasDeletedGroup) {
                  PopoverR(
                    position = PortalPosition.BottomCenter,
                    renderTarget = (openToggle, _) =>
                      <.div(
                        tw.ml4.textWarning4.cursorPointer,
                        ^.onClick --> openToggle,
                        IconR(name = Icon.Glyph.Warning)()
                      ),
                    renderContent = closeToggle => {
                      <.div(
                        tw.flexCol.px12.py8,
                        ^.width := 320.px,
                        <.div(
                          tw.fontSemiBold,
                          "This link is not active because all its linked groups were deleted."
                        ),
                        <.div(
                          tw.flex.mt8,
                          renderModifyLinkSettings(
                            props = props,
                            state = state,
                            item = item,
                            onClose = closeToggle,
                            setLink = setLink
                          ) { openToggle =>
                            Button(
                              style = Button.Style.Text(),
                              onClick = openToggle
                            )("Update link settings")
                          },
                          <.div(tw.ml4, "to enable the link again.")
                        )
                      )
                    }
                  )()
                }
              ),
              <.div(
                tw.flex.itemsCenter.hPx20.pt4,
                <.div(
                  ComponentUtils.testId(DataRoomLinkDashboard, "Link-url"),
                  tw.flexFill.truncate,
                  ^.maxWidth := "fit-content",
                  Button(
                    style = Button.Style.Text(),
                    tpe = Button.Tpe.Link(href = url, target = Button.Target.Blank)
                  )(url)
                ),
                <.div(
                  ComponentUtils.testId(DataRoomLinkDashboard, "Copy-url"),
                  tw.flexNone.ml4.wPx32.hPx32.overflowHidden,
                  CopyToClipboardR(
                    content = url,
                    renderTarget = content => {
                      div(
                        ButtonL(
                          style = ButtonL.Style.Minimal(icon = Some(Icon.Glyph.Duplicate)),
                          title = "Copy to clipboard"
                        )(),
                        content
                      )
                    },
                    targetWrapper = TargetWrapper.Block
                  )()
                )
              )
            )
          )
        },
        sortBy = Table.ColumnOrderingCaseInsensitive(_.linkInvitationInfo.name)
      )

    private def getLastUpdatedColumn(props: Props) =
      Table.Column[Row](
        head = "Last edited",
        render = { item =>
          Table.Cell(
            <.div(
              tw.textGray8,
              props.dataRoomData.latestState.participatingUsers.get(item.linkInvitationInfo.lastUpdatedBy).map { user =>
                <.div(
                  ComponentUtils.testId(DataRoomLinkDashboard, "Link-creator"),
                  tw.text13.leading20.fontSemiBold,
                  user.userInfo.fullName
                )
              },
              item.linkInvitationInfo.lastUpdatedAt.map { lastUpdatedAt =>
                <.div(
                  ComponentUtils.testId(DataRoomLinkDashboard, "Link-created-date"),
                  tw.text11.leading20.fontNormal.pt4,
                  JsDateFormatterUtils.format(lastUpdatedAt, JsDateFormatterUtils.JsDateFormat.LongDatePattern1)
                )
              }
            )
          )
        },
        width = mediumColWidth,
        sortBy = Table.ColumnOrdering(_.linkInvitationInfo.lastUpdatedAt)
      )

    private def getGroupColumn(props: Props) = {
      Table.Column[Row](
        head = "Group",
        render = { item =>
          val groupIds = item.linkInvitationInfo.groupIds
          Table.Cell(
            DataRoomGroupNameTag(
              groups = groupIds.toSeq.flatMap { groupId =>
                props.dataRoomData.getGroupData(groupId, includeDeleted = true).map { groupData =>
                  RenderGroupData(groupId, groupData.name, groupData.isDeleted)
                }
              },
              maxLines = 2
            )()
          )
        },
        width = mediumColWidth
      )
    }

    private def getPermissionColumn(props: Props) = {

      def getPermissionName: Row => String = { item =>
        val matcher = Function.unlift[Option[AssetPermissionChanges], FileFolderPermission] { changes =>
          FileFolderPermission.values.find { permission =>
            changes.contains(AssetPermissionChanges.allFoldersWithRootChannel(props.dataRoomData.workflowId, permission))
          }
        }
        item.linkInvitationInfo.permissions match {
          case matcher(permission) => FileFolderPermissionUtils.getPermissionName(permission)
          case _                   => "Custom"
        }
      }

      Table.Column[Row](
        head = "Permission",
        render = { item =>
          Table.Cell(
            <.div(
              ComponentUtils.testId(DataRoomLinkDashboard, "Link-permission"),
              getPermissionName(item)
            )
          )
        },
        width = "150px",
        sortBy = Table.ColumnOrdering(getPermissionName)
      )
    }

    private def getActionColumn(props: Props, state: State, setLink: DataRoomInvitationLinkCreationResult.SetLink) = {
      Table.Column[Row](
        render = { item =>
          Table.Cell(
            <.div(
              ComponentUtils.testId(DataRoomLinkDashboard, "ActionBtn"),
              PopoverR(
                renderTarget = { (openToggle, isOpen) =>
                  Button(
                    style = Button.Style.Minimal(icon = Some(Icon.Glyph.EllipsisHorizontal), isSelected = isOpen),
                    onClick = openToggle
                  )()
                },
                renderContent = { closeMenuToggle =>
                  MenuR()(
                    renderModifyLinkSettings(
                      props = props,
                      state = state,
                      item = item,
                      onClose = closeMenuToggle,
                      setLink = setLink
                    ) { openToggle =>
                      MenuItemR(
                        onClick = openToggle,
                        icon = Some(Icon.Glyph.Cog)
                      )("Link settings")
                    },
                    DataRoomUsersJoiningViaLinkModal(
                      renderTarget = { openToggle =>
                        MenuItemR(
                          onClick = openToggle,
                          icon = Some(Icon.Glyph.UserGroup)
                        )("Participants")
                      },
                      dataRoomData = props.dataRoomData,
                      linkId = item.linkId,
                      onClose = closeMenuToggle
                    )(),
                    MenuDividerR()(),
                    DataRoomDeleteLinkInvitationModal(
                      renderTarget = { openToggle =>
                        MenuItemR(
                          onClick = openToggle,
                          color = MenuItemR.ColorDanger,
                          icon = Some(Icon.Glyph.Trash)
                        )("Delete link")
                      },
                      dataRoomWorkflowId = props.dataRoomData.workflowId,
                      linkId = item.linkId,
                      linkName = item.linkInvitationInfo.name,
                      closeMenuToggle = closeMenuToggle
                    )()
                  )
                },
                position = PortalPosition.BottomRight
              )()
            )
          )
        },
        width = "64px"
      )
    }

    private def getColumns(props: Props, state: State, setLink: DataRoomInvitationLinkCreationResult.SetLink) = {
      Seq(
        getStatusColumn(props, state, setLink),
        getNameColumn(props, state, setLink),
        getGroupColumn(props),
        getPermissionColumn(props),
        getLastUpdatedColumn(props),
        getActionColumn(props, state, setLink)
      )
    }

    private def renderTable(
      props: Props,
      state: State,
      rows: List[Row],
      setLink: DataRoomInvitationLinkCreationResult.SetLink
    ) = {
      LinkTable(
        columns = getColumns(props, state, setLink),
        rows = rows,
        getKey = _.linkId.idString,
        style = Table.Style.Minimal,
        sortColumn = Some(1)
      )()
    }

    private def renderNonEmptyState(
      props: Props,
      state: State,
      rows: List[Row]
    ) = {
      React.Fragment(
        <.div(
          tw.flex.itemsCenter.justifyBetween,
          <.div(
            tw.fontSemiBold.text20.leading32,
            "Invitation links"
          ),
          Option.when(state.isSharableLinkEnabled) {
            renderCreateLink(props, state, setLinkCB) { openToggle =>
              Button(
                testId = "New-invitation-link",
                style = Button.Style.Full(
                  icon = Some(Icon.Glyph.Link)
                ),
                onClick = openToggle
              )("Create invitation link")
            }
          }
        ),
        if (!state.isSharableLinkEnabled) {
          <.div(
            tw.mt12,
            WellR(style = Well.Style.Gray(icon = Some(Icon.Glyph.Info)))(
              "Invitation links are disabled based on this data room’s authentication policy. Participants can’t use any existing links to join."
            )
          )
        } else {
          state.sharableLinkConfigOpt.flatMap(_.ssoProviderName).map { ssoProviderName =>
            <.div(
              tw.mt12,
              WellR(style = Well.Style.Primary())(s"Participants will be required to log in with $ssoProviderName")
            )
          }
        },
        <.div(
          tw.flexFill.overflowYAuto.mt16,
          renderTable(
            props,
            state,
            rows,
            setLinkCB
          )
        )
      )
    }

    private def renderEmptyState(
      props: Props,
      state: State,
      invisible: Boolean,
      setLink: DataRoomInvitationLinkCreationResult.SetLink
    ) = {
      <.div(
        if (invisible) {
          // To keep link result modal open
          TagMod(
            tw.invisible,
            ^.height := "0px"
          )
        } else {
          TagMod(
            tw.myAuto
          )
        },
        tw.flexNone,
        if (state.isSharableLinkEnabled) {
          renderEmptyStateCreatable(props, state, setLink)
        } else {
          renderEmptyStateDisabledSharableLink()
        }
      )
    }

    private def renderEmptyStateCreatable(props: Props, state: State, setLink: SetLink) = {
      NonIdealStateR(
        icon = {
          <.div(
            tw.flex.itemsCenter.justifyCenter,
            tw.p16.bgPrimary1.bgOpacity40.roundedFull.textPrimary5,
            ^.width := "56px",
            ^.height := "56px",
            IconR(name = Icon.Glyph.Link, size = Icon.Size.Px24)()
          )
        },
        title = {
          <.div(
            tw.textCenter,
            ComponentUtils.testId(DataRoomLinkDashboard, "no-invitation-link"),
            "You haven't created any links yet"
          )
        },
        action = {
          renderCreateLink(props, state, setLink) { openToggle =>
            Button(
              testId = "Create-one",
              style = Button.Style.Ghost(
                color = Button.Color.Primary,
                icon = Some(Icon.Glyph.Link)
              ),
              onClick = openToggle
            )("Create invitation link")
          }
        }
      )()
    }

    private def renderEmptyStateDisabledSharableLink() = {
      NonIdealStateR(
        icon = {
          <.div(
            tw.flex.itemsCenter.justifyCenter,
            tw.p16.bgGray2.roundedFull.textGray7,
            ^.width := "56px",
            ^.height := "56px",
            IconR(name = Icon.Glyph.Info, size = Icon.Size.Px24)()
          )
        },
        title = {
          <.div(
            tw.textCenter,
            ComponentUtils.testId(DataRoomLinkDashboard, "no-invitation-link"),
            "Invitation links are disabled"
          )
        },
        description = {
          <.div(
            tw.textCenter,
            "Invitation links are disabled based on this data room’s authentication policy. Participants can’t use any existing links to join."
          )
        }
      )()
    }

    def render(props: Props, state: State): VdomElement = {
      val rows = getRows(props)
      if (state.isGetting) {
        BlockIndicatorR()()
      } else {
        <.div(
          tw.flex.flexCol.hPc100,
          Option.when(rows.nonEmpty) {
            renderNonEmptyState(props, state, rows)
          },
          renderEmptyState(
            props,
            state,
            rows.nonEmpty,
            setLinkCB
          )
        )
      }
    }

    def getSharableLinkConfig: Callback = {
      for {
        props <- scope.props
        _ <- scope.modState(
          _.copy(sharableLinkConfigOpt = None, isGetting = true),
          ZIOUtils.toReactCallback(
            DataRoomEndpointClient
              .getSharableLinkConfig(GetSharableLinkConfigParams(props.dataRoomData.workflowId))
              .map(
                _.fold(
                  _ => scope.modState(_.copy(isGetting = false)),
                  resp => scope.modState(_.copy(isGetting = false, sharableLinkConfigOpt = Some(resp)))
                )
              )
          )
        )
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidMount(_.backend.getSharableLinkConfig)
    .build

}
