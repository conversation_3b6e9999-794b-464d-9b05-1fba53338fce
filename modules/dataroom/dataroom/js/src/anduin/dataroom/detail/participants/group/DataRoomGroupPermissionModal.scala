// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.group

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, ModalFooterWCancel}
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.dataroom.DataRoomData
import anduin.dataroom.detail.participants.invitation.PermissionSettings
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.group.{DataRoomGroupData, UpdateDataRoomGroupPermissionsParams}
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.model.common.user.UserId
import anduin.stargazer.service.dataroom.DataRoomPermissionChanges
import anduin.utils.{StateSnapshotWithModFn, StringUtils}
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import stargazer.util.HtmlTagUtils.semiBoldText

private[dataroom] final case class DataRoomGroupPermissionModal(
  actorUserId: UserId,
  dataRoomData: DataRoomData,
  groupData: DataRoomGroupData,
  renderTarget: (Boolean, Callback) => VdomNode,
  onClose: Callback = Callback.empty,
  isReadOnly: Boolean = false
) {

  private val isActorAdmin = dataRoomData.latestState.isUserRole(actorUserId)(DataRoomRoleUtils.isAdmin)

  def apply(): VdomElement = DataRoomGroupPermissionModal.component(this)
}

private[dataroom] object DataRoomGroupPermissionModal {

  private type Props = DataRoomGroupPermissionModal

  private final case class State(
    settings: PermissionSettings,
    isBusy: Boolean,
    isReadOnly: Boolean
  )

  private class Backend(scope: BackendScope[Props, State]) {

    private val settingsStateSnapshot = StateSnapshotWithModFn.withReuse
      .zoom[State, PermissionSettings](_.settings)(settings => _.copy(settings = settings))
      .prepareVia(scope)

    def render(props: Props, state: State): VdomNode = {
      Modal(
        title = s"${StringUtils.addApostrophe(props.groupData.name)} permissions",
        renderTarget = openToggle => props.renderTarget(props.isActorAdmin, openToggle),
        renderContent = closeToggle =>
          React.Fragment(
            renderBody(props, state),
            renderFooter(
              props,
              state,
              closeToggle
            )
          ),
        size = Modal.Size(Modal.Width.Px1160),
        afterUserClose = scope.setState(getDefaultState(props), props.onClose)
      )()
    }

    private def renderBody(props: Props, state: State) = {
      ModalBody()(
        if (props.isReadOnly) {
          renderReadOnlyDescription(props, state)
        } else {
          EmptyVdom
        },
        <.div(
          ComponentUtils.testId(DataRoomGroupPermissionModal, "GroupPermissionTree"),
          DataRoomGroupPermissionSection(
            actorUserId = props.actorUserId,
            dataRoomData = props.dataRoomData,
            targetGroupId = Some(props.groupData.id),
            settings = settingsStateSnapshot(state),
            roleCheck = DataRoomRoleUtils.all,
            isDisabled = !props.isActorAdmin || state.isReadOnly,
            hasOnlyOneAdmin = props.dataRoomData.getAdminCount(
              groupChanges = Map(props.groupData.id -> DataRoomRole.Empty)
            ) < 1
          )()
        )
      )
    }

    private def renderReadOnlyDescription(props: Props, state: State) = {
      <.div(
        tw.pb24,
        "The permissions below are applied to all members of ",
        <.span(tw.fontSemiBold, props.groupData.name),
        " group. ",
        if (props.isActorAdmin && state.isReadOnly) {
          Button(
            style = Button.Style.Text(),
            onClick = scope.modState(_.copy(isReadOnly = false))
          )("Edit group settings")
        } else {
          EmptyVdom
        }
      )
    }

    private def renderFooter(props: Props, state: State, closeToggle: Callback) = {
      ModalFooter()(
        if (state.isReadOnly || !props.isActorAdmin) {
          Button(
            style = Button.Style.Full(),
            onClick = closeToggle >> props.onClose
          )("Back")
        } else {
          <.div(
            tw.flex.itemsCenter.justifyBetween.spaceX8,
            <.div(
              tw.flex.itemsStart.mr8,
              <.div(
                tw.flexNone.textWarning4.pt2.pr8,
                IconR(name = Icon.Glyph.Warning)()
              ),
              <.p(
                tw.flexFill.textGray8.text13.leading20,
                "Changes will be applied to all files and subfolders. Please review the folder's content before saving your changes."
              )
            ),
            <.div(
              tw.flex.itemsCenter.spaceX8,
              Button(
                style = Button.Style.Full(),
                onClick = closeToggle >> props.onClose
              )("Cancel"),
              renderWarningModal(props, state)(
                closeToggle,
                renderTarget = openWarning =>
                  Button(
                    style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.isBusy),
                    isDisabled = !isPermissionChanged(props, state),
                    onClick = if (props.groupData.participants.isEmpty) {
                      updateGroupPermission(props, state)(closeToggle >> props.onClose)
                    } else {
                      openWarning
                    }
                  )("Save")
              )
            )
          )
        }
      )
    }

    private def renderWarningModal(
      props: Props,
      state: State
    )(
      closeToggle: Callback,
      renderTarget: Callback => VdomNode
    ) = {
      val isAdminRole = DataRoomRoleUtils.isAdmin(state.settings.role)
      Modal(
        title = if (isAdminRole) "Set group role to Admin?" else "Update group permissions?",
        renderTarget = renderTarget,
        renderContent = closeWarning =>
          React.Fragment(
            ModalBody()(
              <.div(
                if (isAdminRole) {
                  renderAdminWarning()
                } else {
                  renderNonAdminWarning()
                },
                <.p(
                  tw.fontBold.mt16,
                  "Are you sure you want to apply this update?"
                )
              )
            ),
            ModalFooterWCancel(cancel = closeWarning, cancelLabel = "Back")(
              Button(
                style = Button.Style
                  .Full(color = if (isAdminRole) Button.Color.Warning else Button.Color.Primary, isBusy = state.isBusy),
                onClick = updateGroupPermission(props, state)(closeWarning >> closeToggle >> props.onClose)
              )("Update")
            )
          )
      )()
    }

    private def renderIcon(
      name: Icon.Name,
      bgColor: TagMod,
      iconColor: TagMod
    ) = {
      <.div(
        tw.roundedFull.flex.p6.mr12.hPx24.wPx24,
        bgColor,
        <.div(
          iconColor,
          IconR(name, size = Icon.Size.Custom(12))()
        )
      )
    }

    private def renderAdminWarning() = {
      <.div(
        tw.flexCol,
        <.p(
          "Setting this group’s role to ",
          semiBoldText("Admin"),
          " will apply to all current and future participants in this group."
        ),
        <.div(
          tw.flex.mt16,
          renderIcon(Icon.Glyph.FileText, tw.bgWarning1, tw.textWarning4),
          <.p(
            "Participants will gain ",
            semiBoldText("Own"),
            " permissions to all files and folders in this data room."
          )
        ),
        <.div(
          tw.flex.mt16,
          renderIcon(Icon.Glyph.UserEdit, tw.bgWarning1, tw.textWarning4),
          <.div(
            tw.flexCol,
            <.p(
              "Participants’ roles will be set to ",
              semiBoldText("Admin"),
              " and gain ",
              semiBoldText("full control over content, permissions, and participants"),
              " in this data room."
            ),
            <.p(
              tw.textGray7.textSmall,
              "For each participant, their overall role in the data room will reflect Admin if it’s their highest role across all groups."
            )
          )
        )
      )
    }

    private def renderNonAdminWarning() = {
      <.div(
        tw.flexCol,
        <.p("Changes will apply to all current and future participants in this group."),
        <.div(
          tw.flex.mt16,
          renderIcon(Icon.Glyph.FileText, tw.bgPrimary1, tw.textPrimary4),
          <.p("If you update group permissions, participants’ access to files and folders will change accordingly.")
        ),
        <.div(
          tw.flex.mt16,
          renderIcon(Icon.Glyph.UserEdit, tw.bgPrimary1, tw.textPrimary4),
          <.p("If you update the group role, participants’ roles may also change if it becomes their highest role across all their groups.")
        )
      )
    }

    private def isPermissionChanged(props: Props, state: State) = {
      props.groupData.role != state.settings.role || !AssetPermissionChanges.isEmpty(state.settings.assetPermission)
    }

    private def updateGroupPermission(props: Props, state: State)(onClose: Callback) = {
      Callback.when(isPermissionChanged(props, state)) {
        scope.modState(
          _.copy(isBusy = true),
          ZIOUtils.toReactCallback {
            DataRoomEndpointClient
              .updateGroupPermission(
                UpdateDataRoomGroupPermissionsParams(
                  groupId = props.groupData.id,
                  permissionChanges = DataRoomPermissionChanges(
                    roleSet = Some(state.settings.role),
                    assetPermissions = state.settings.assetPermission
                  )
                )
              )
              .map(resp =>
                scope.setState(
                  getDefaultState(props),
                  resp.fold(
                    _ => Toast.errorCallback("Failed to update group permission") >> onClose,
                    _ => Toast.successCallback("Group permissions updated") >> onClose
                  )
                )
              )
          }
        )
      }
    }

  }

  private def getDefaultState(props: Props) = {
    State(
      settings = PermissionSettings(props.groupData.role, AssetPermissionChanges()),
      isBusy = false,
      isReadOnly = props.isReadOnly
    )
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps(getDefaultState)
    .renderBackend[Backend]
    .componentDidUpdate { scope =>
      Callback.when(scope.prevProps.groupData != scope.currentProps.groupData)(
        scope.setState(getDefaultState(scope.currentProps))
      )
    }
    .build

}
