// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.menu.react.{MenuDividerR, MenuItemR, MenuR}
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import anduin.dataroom.detail.participants.`export`.{DataRoomExportParticipantsModal, DataRoomExportPermissionsModal}
import anduin.dataroom.detail.participants.group.{DataRoomGroupPermissionModal, DeleteDataRoomGroupModal}
import anduin.dataroom.detail.participants.user.DataRoomUserRowData
import anduin.dataroom.role.{DataRoomRoleUtils, Restricted}
import anduin.dataroom.terms.DataRoomUserTermsStatusModal
import anduin.dataroom.{DataRoomData, DataRoomFrontEndState}
import anduin.model.common.user.UserId
import anduin.stargazer.service.dataroom.DataRoomExportPermissionsParams.ExportPermissionTarget
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import stargazer.component.routing.react.WithReactRouterR
import stargazer.model.routing.DynamicAuthPage
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class DataRoomParticipantGlobalActionMenu(
  userId: UserId,
  page: DynamicAuthPage.DataRoomParticipantsPage,
  dataRoomData: DataRoomData,
  userRowDataMap: Map[UserId, DataRoomUserRowData],
  scopeUserIds: Set[UserId]
) {

  private val hasOtherParticipants = dataRoomData.latestState.participatingUsers.size > 1

  private val hasToA = dataRoomData.latestState.dataRoomCreatedState.termsOfAccessOptions.isEnabled

  private val toaOptions = dataRoomData.latestState.dataRoomCreatedState.termsOfAccessOptions

  private val actorRole = dataRoomData.latestState.dataRoomCreatedState.individualRoles.getOrElse(userId, Restricted())

  def apply(): VdomElement = DataRoomParticipantGlobalActionMenu.component(this)
}

object DataRoomParticipantGlobalActionMenu {

  private type Props = DataRoomParticipantGlobalActionMenu

  private def render(props: Props): VdomNode = {
    PopoverR(
      renderTarget = (openPopover, _) => renderTarget(props)(openPopover),
      renderContent = closePopover =>
        MenuR()(
          renderGroupPermissions(props)(closePopover),
          renderMessageParticipants(props)(closePopover),
          renderViewToAStatus(props)(closePopover),
          if (DataRoomRoleUtils.isAdmin(props.actorRole)) {
            React.Fragment(
              renderExportParticipants(props)(closePopover),
              renderExportUserPermissions(props)(closePopover),
              renderExportGroupPermissions(props)(closePopover),
              renderDeleteGroup(props)(closePopover)
            )
          } else {
            EmptyVdom
          }
        ),
      position = PortalPosition.BottomRight
    )()
  }

  private def renderTarget(props: Props)(openPopover: Callback) = {
    Button(
      style = Button.Style.Minimal(icon = Some(Icon.Glyph.Cog), color = Button.Color.Primary),
      isDisabled = props.page.groupIdOpt.isEmpty && !(props.hasOtherParticipants || props.hasToA),
      onClick = openPopover
    )(
      props.page match {
        case _: DynamicAuthPage.DataRoomParticipantsUserPage | _: DynamicAuthPage.DataRoomAccessRequestsPage |
            _: DynamicAuthPage.DataRoomAllGroupsPage =>
          "Global actions"
        case _: DynamicAuthPage.DataRoomGroupDetailPage => "Group actions"
      },
      <.span(
        ComponentUtils.testId(DataRoomParticipantGlobalActionMenu, "GlobalAction"),
        tw.flexNone.pl8,
        IconR(name = Icon.Glyph.CaretDown)()
      )
    )
  }

  private def renderMenuItem(
    icon: Icon.Name,
    text: String,
    isDisabled: Boolean,
    color: MenuItemR.Color = MenuItemR.ColorGray
  )(
    onClick: Callback
  ) = {
    MenuItemR(
      onClick = onClick,
      color = color,
      icon = Some(icon),
      isDisabled = isDisabled
    )(text)
  }

  private def renderGroupPermissions(props: Props)(onClose: Callback) = {
    props.page.groupIdOpt.flatMap(props.dataRoomData.groupMap.get).map { groupData =>
      DataRoomGroupPermissionModal(
        actorUserId = props.userId,
        dataRoomData = props.dataRoomData,
        groupData = groupData,
        renderTarget = (canEdit, openToggle) =>
          renderMenuItem(
            if (canEdit) Icon.Glyph.Edit else Icon.Glyph.Eye,
            if (canEdit) "Edit group permissions" else "View group permission",
            isDisabled = false
          )(openToggle),
        onClose = onClose
      )()
    }
  }

  private def renderMessageParticipants(props: Props)(onClose: Callback) = {
    DataRoomManualNotificationModal(
      userId = props.userId,
      dataRoomData = props.dataRoomData,
      userRowDataMap = props.userRowDataMap,
      initialSelection = props.scopeUserIds,
      renderTarget = renderMenuItem(
        Icon.Glyph.Envelope,
        "Message participants",
        !props.hasOtherParticipants || props.scopeUserIds.isEmpty
      ),
      onClose = onClose
    )()
  }

  private def getToaFileIdOpt(createdState: DataRoomFrontEndState) = {
    val toaOptions = createdState.termsOfAccessOptions
    toaOptions.versions.lastOption.filter(_ => toaOptions.isEnabled)
  }

  private def renderViewToAStatus(props: Props)(onClose: Callback) = {
    getToaFileIdOpt(props.dataRoomData.latestState.dataRoomCreatedState)
      .filter { _ =>
        DataRoomRoleUtils.isInternal(props.actorRole) || !props.toaOptions.whitelistedUsers.contains(props.userId)
      }
      .map { toaFileId =>
        <.div(
          tw.flexNone.flex,
          DataRoomUserTermsStatusModal(
            userId = props.userId,
            renderTarget = renderMenuItem(
              Icon.Glyph.CheckList,
              "View record of consent",
              isDisabled = !props.hasToA || props.scopeUserIds.isEmpty
            ),
            toaFileId = toaFileId,
            dataRoomData = props.dataRoomData,
            onClose = onClose,
            filterUserFn = props.scopeUserIds.contains
          )()
        )
      }
  }

  private def renderExportParticipants(props: Props)(onClose: Callback) = {
    DataRoomExportParticipantsModal(
      props.dataRoomData.workflowId,
      props.scopeUserIds,
      props.hasToA,
      onClose,
      renderTarget = renderMenuItem(
        Icon.Glyph.Table,
        "Export participant's data",
        !props.hasOtherParticipants || props.scopeUserIds.isEmpty
      ),
      isGlobalAction = true
    )()
  }

  private def renderExportUserPermissions(props: Props)(onClose: Callback) = {
    DataRoomExportPermissionsModal(
      props.dataRoomData.workflowId,
      ExportPermissionTarget.Users(props.scopeUserIds),
      onClose,
      renderTarget = renderMenuItem(
        Icon.Glyph.Key,
        "Export participant's permissions",
        !props.hasOtherParticipants || props.scopeUserIds.isEmpty
      )
    )()
  }

  private def renderExportGroupPermissions(props: Props)(onClose: Callback) = {
    val groupIds = if (props.page.isAllGroupsPage || props.page.isAllUsersPage) {
      props.dataRoomData.groupMap.keySet
    } else {
      props.page.groupIdOpt.toSet
    }
    Option.when(groupIds.nonEmpty) {
      DataRoomExportPermissionsModal(
        props.dataRoomData.workflowId,
        ExportPermissionTarget.Groups(groupIds),
        onClose,
        renderTarget = renderMenuItem(
          Icon.Glyph.UserGroup,
          "Export group's permissions",
          isDisabled = false
        )
      )()
    }
  }

  private def renderDeleteGroup(props: Props)(onClose: Callback) = {
    props.page.groupIdOpt.map { groupId =>
      val groupDataOpt = props.dataRoomData.groupMap.get(groupId)
      React.Fragment(
        MenuDividerR()(),
        WithReactRouterR { router =>
          DeleteDataRoomGroupModal(
            router = router,
            actorUserId = props.userId,
            dataRoomWorkflowId = props.dataRoomData.workflowId,
            dataRoomData = props.dataRoomData,
            groupDataList = groupDataOpt.toList,
            renderTarget = targetProps =>
              TooltipR(
                renderTarget = renderMenuItem(
                  icon = Icon.Glyph.Trash,
                  text = "Delete group",
                  isDisabled = targetProps.disabledReasonOpt.nonEmpty,
                  color = MenuItemR.ColorDanger
                )(targetProps.openToggle),
                renderContent = _(targetProps.disabledReasonOpt),
                isDisabled = targetProps.disabledReasonOpt.isEmpty
              )(),
            onDelete = router.set(DynamicAuthPage.DataRoomAllParticipantsPage(props.dataRoomData.workflowId, None)),
            onClose = onClose
          )()
        }
      )
    }
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
