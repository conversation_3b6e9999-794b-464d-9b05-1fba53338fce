// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.link.step

import design.anduin.components.icon.Icon
import design.anduin.components.textbox.TextBox
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import anduin.dataroom.DataRoomData
import anduin.dataroom.detail.link.{DataRoomInvitationAssetPermissionDropdown, LinkSettings}
import anduin.dataroom.detail.participants.invitation.{InvitationGroupSettings, PermissionSettings}
import anduin.dataroom.detail.participants.invitation.participants.SelectGroupSection
import anduin.dataroom.detail.participants.permission.DataRoomAdminRoleDropdown
import anduin.model.common.user.UserId
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.raquo.laminar.api.L.*
import anduin.dataroom.detail.settings.DataRoomLinkDashboard
import anduin.dataroom.role.*
import anduin.id.dataroom.DataRoomGroupId
import anduin.stargazer.service.dataroom.GetSharableLinkConfigResponse

final case class DataRoomLinkInvitationPermissions(
  actorUserId: UserId,
  dataRoomData: DataRoomData,
  actorRoles: DataRoomRole,
  permissionSettings: StateSnapshotWithModFn[PermissionSettings],
  linkSettings: StateSnapshotWithModFn[LinkSettings],
  sharableLinkConfigOpt: Option[GetSharableLinkConfigResponse]
) {
  def apply(): VdomElement = DataRoomLinkInvitationPermissions.component(this)
}

object DataRoomLinkInvitationPermissions {

  private type Props = DataRoomLinkInvitationPermissions

  private def render(props: Props) = {
    React.Fragment(
      if (!props.sharableLinkConfigOpt.exists(_.isSharableLinkEnabled)) {
        <.div(
          tw.mt12,
          WellR(style = Well.Style.Gray(icon = Some(Icon.Glyph.Info)))(
            "This link can’t be used because invitation links are disabled based on this data room’s authentication policy. You can still view its settings."
          )
        )
      } else {
        EmptyVdom
      },
      renderLinkName(props),
      renderGroup(props),
      renderRole(props),
      renderPermission(props)
    )
  }

  private def renderLinkName(props: Props) = {
    DataRoomLinkInvitationSectionRenderer.render(
      title = "Link name",
      subtitleOpt = Some("The name assigned to this invitation link for tracking purposes"),
      isRequired = true,
      isDisabled = DataRoomLinkDashboard.isReadOnly(props.sharableLinkConfigOpt)
    )(
      TextBox(
        value = props.linkSettings.value.name,
        onChange = newValue => props.linkSettings.modState(_.copy(name = newValue)),
        placeholder = "Enter link name...",
        isDisabled = DataRoomLinkDashboard.isReadOnly(props.sharableLinkConfigOpt)
      )()
    )
  }

  private def renderGroup(props: Props) = {
    SelectGroupSection(
      actorUserId = props.actorUserId,
      actorRole = props.actorRoles,
      dataRoomData = props.dataRoomData,
      groupSettings = props.linkSettings.value.groupSettings,
      onChange = {
        case InvitationGroupSettings.NoGroup               => onSelectUnassigned(props)
        case InvitationGroupSettings.AddToGroups(groupIds) => onSelectGroups(props)(groupIds)
      },
      isDisabled = DataRoomLinkDashboard.isReadOnly(props.sharableLinkConfigOpt),
      renderHeader = content =>
        DataRoomLinkInvitationSectionRenderer.render(
          title = "Group",
          subtitleOpt = Some("Choose how participants using this link will be assigned to groups"),
          isRequired = true,
          isDisabled = DataRoomLinkDashboard.isReadOnly(props.sharableLinkConfigOpt)
        )(content),
      showMultiGroupsWarning = true
    )()
  }

  private def onSelectGroups(props: Props)(groupIds: Set[DataRoomGroupId]) = {
    val role = if (groupIds.isEmpty) {
      Restricted()
    } else {
      val groupRoles = groupIds.flatMap(props.dataRoomData.groupMap.get).map(_.role)
      DataRoomRoleUtils.getMaxRole(groupRoles)
    }
    props.linkSettings.modState(
      _.copy(groupSettings = InvitationGroupSettings.AddToGroups(groupIds)),
      props.permissionSettings.setState(PermissionSettings(role, AssetPermissionChanges()))
    )
  }

  private def onSelectUnassigned(props: Props) = {
    props.linkSettings.modState(
      _.copy(groupSettings = InvitationGroupSettings.NoGroup),
      props.permissionSettings.setState(
        PermissionSettings(
          role = Restricted(),
          AssetPermissionChanges.allFoldersWithRootChannel(props.dataRoomData.workflowId, FileFolderPermission.Read)
        )
      )
    )
  }

  private def renderRole(props: Props) = {
    DataRoomLinkInvitationSectionRenderer.render(
      title = "Role",
      subtitleOpt = Some("The default role issued to invited members"),
      isRequired = true,
      isDisabled = DataRoomLinkDashboard.isReadOnly(props.sharableLinkConfigOpt)
    )(
      <.div(
        ^.width := DataRoomLinkInvitationSectionRenderer.dropdownWidth,
        DataRoomAdminRoleDropdown(
          dataRoomWorkflowId = props.dataRoomData.workflowId,
          actorRoles = props.actorRoles,
          settings = props.permissionSettings,
          nonAdminDefaultChanges = DataRoomAdminRoleDropdown.NonAdminDefaultChanges.Download,
          hasOnlyOneAdmin = false,
          isActorInviting = false,
          isInviting = false,
          isSeatLimitReached = props.dataRoomData.getRemainingSeatCount() <= 0,
          creatorEntityName = props.dataRoomData.latestState.creatorEntity.entityModel.name,
          disabledReasonOpt = Option.when(props.linkSettings.value.groupSettings.isAddToGroups)(
            "The role can't be edited as it is inherited\nfrom the groups you selected above"
          ),
          isDisabled = DataRoomLinkDashboard.isReadOnly(props.sharableLinkConfigOpt)
        )()
      )
    )
  }

  private def renderPermission(props: Props) = {
    DataRoomLinkInvitationSectionRenderer.render(
      title = "Permission",
      subtitleOpt = Some("The default permission granted to invited members"),
      isRequired = true,
      isDisabled = DataRoomLinkDashboard.isReadOnly(props.sharableLinkConfigOpt)
    )(
      <.div(
        ^.minWidth := DataRoomLinkInvitationSectionRenderer.dropdownWidth,
        DataRoomInvitationAssetPermissionDropdown(
          dataRoomWorkflowId = props.dataRoomData.workflowId,
          actorUserId = props.actorUserId,
          actorRoles = props.actorRoles,
          email = "users joining via this link",
          settings = props.permissionSettings,
          dataRoomData = props.dataRoomData,
          groupSettings = props.linkSettings.value.groupSettings,
          isDisabled = DataRoomLinkDashboard.isReadOnly(props.sharableLinkConfigOpt)
        )()
      )
    )
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
