// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.invitation.participants

import design.anduin.components.checkbox.Checkbox
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import anduin.dataroom.detail.participants.invitation.{
  InvitationGroupSettings,
  InvitationUsers,
  PermissionSettings,
  SettingSection
}
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.utils.StateSnapshotWithModFn
import design.anduin.components.util.ComponentUtils
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class CheckboxSection(
  users: StateSnapshotWithModFn[InvitationUsers],
  actorRole: DataRoomRole,
  hasTermsOfAccess: Boolean,
  groupSettings: InvitationGroupSettings
) {
  def apply(): VdomElement = CheckboxSection.component(this)
}

object CheckboxSection {

  private type Props = CheckboxSection

  private def renderCheckbox(
    isSelected: StateSnapshotWithModFn[Boolean],
    isDisabled: <PERSON><PERSON><PERSON>,
    title: String,
    disabledReasonOpt: Option[String] = None,
    testId: String
  ) = {
    <.label(
      ComponentUtils.testId(CheckboxSection, testId),
      tw.flex.itemsCenter.cursorPointer,
      TooltipR(
        renderTarget = Checkbox(
          isChecked = isSelected.value,
          onChange = isSelected.setState,
          isDisabled = isDisabled
        )(),
        renderContent = _(disabledReasonOpt.whenDefined),
        isDisabled = !isDisabled || disabledReasonOpt.exists(_.isEmpty)
      )(),
      <.div(
        tw.pl8,
        title
      )
    )
  }

  private def render(props: Props) = {
    val isAdmin = DataRoomRoleUtils.isAdmin(props.users.value.settings.role)
    SettingSection("Advanced settings", isRequired = false)(
      renderCheckbox(
        isSelected = props.users.withReuse
          .zoomState(InvitationUsers.zoomToSettings)
          .withReuse
          .zoomState(PermissionSettings.zoomToCanInvite),
        isDisabled = isAdmin,
        title = "Can invite other participants",
        disabledReasonOpt = Option.when(isAdmin)(
          "Admins are always able to invite participants."
        ),
        testId = "InvitePermission"
      ),
      Option.when(props.hasTermsOfAccess) {
        React.Fragment(
          <.div(tw.hPx8),
          renderCheckbox(
            isSelected = props.users.withReuse.zoomState(InvitationUsers.zoomToIsToaRequired),
            isDisabled = !DataRoomRoleUtils.isAdmin(props.actorRole),
            title = "Must accept NDA to access the data room",
            testId = "NDAPermission"
          )
        )
      }
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
