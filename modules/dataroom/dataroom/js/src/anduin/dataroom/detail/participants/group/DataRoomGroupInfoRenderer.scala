// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.group

import anduin.dataroom.detail.participants.group.DataRoomGroupInfoRenderer.GroupSubtitle
import anduin.dataroom.group.DataRoomGroupData
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.file.TooltipOnTruncate
import anduin.scalajs.pluralize.Pluralize
import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.react.InitialAvatarR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.{Callback, ScalaComponent}
import japgolly.scalajs.react.vdom.html_<^.*

final case class DataRoomGroupInfoRenderer(
  groupData: DataRoomGroupData,
  subtitleOpt: Option[GroupSubtitle] = Some(GroupSubtitle.Role),
  onClick: Option[Callback] = None,
  maxWidth: Int = 300
) {

  def apply(): VdomElement = DataRoomGroupInfoRenderer.component(this)

}

object DataRoomGroupInfoRenderer {

  type Props = DataRoomGroupInfoRenderer

  enum GroupSubtitle {
    case Role, MemberCount
  }

  private def render(props: Props) = {
    <.div(
      ^.maxWidth := props.maxWidth.px,
      props.onClick.map(onClick => ^.onClick --> onClick).toTagMod,
      tw.flex.itemsCenter,
      <.div(
        tw.textGray7.mr8,
        InitialAvatarR(
          id = props.groupData.id.idString,
          initials = props.groupData.name.take(1).toUpperCase,
          kind = InitialAvatar.Kind.Organization
        )()
      ),
      <.div(
        tw.wFit.maxWPc100.flex.flexCol,
        TooltipOnTruncate(
          renderTarget = <.div.withRef(_)(
            tw.textGray8.fontSemiBold.text13.leading20.truncate,
            ^.textOverflow.ellipsis,
            props.groupData.name
          ),
          content = props.groupData.name
        )(),
        props.subtitleOpt.map { subtitle =>
          <.div(
            tw.textGray7.textSmall,
            subtitle match {
              case GroupSubtitle.Role => DataRoomRoleUtils.getName(props.groupData.role)
              case GroupSubtitle.MemberCount =>
                if (props.groupData.participants.isEmpty) {
                  "No participants"
                } else {
                  Pluralize("participant", props.groupData.participants.size, inclusive = true)
                }
            }
          )
        }
      )
    )
  }

  val component = ScalaComponent
    .builder[DataRoomGroupInfoRenderer](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
