// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.invitation.permissions

import design.anduin.components.button.Button
import design.anduin.components.modal.ModalFooter
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.dataroom.detail.participants.invitation.InvitationStep
import anduin.utils.StateSnapshotWithModFn

private[invitation] final case class PermissionFooterSection(
  step: StateSnapshotWithModFn[InvitationStep]
) {
  def apply(): VdomElement = PermissionFooterSection.component(this)
}

private[invitation] object PermissionFooterSection {

  private type Props = PermissionFooterSection

  private def render(props: Props): VdomNode = {
    ModalFooter()(
      <.div(
        tw.flex.itemsCenter.justifyBetween.spaceX8,
        Button(
          style = Button.Style.Full(),
          onClick = props.step.setState(InvitationStep.Participants)
        )("Back"),
        Button(
          style = Button.Style.Full(color = Button.Color.Primary),
          onClick = props.step.setState(InvitationStep.Message)
        )("Next: Add message")
      )
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
