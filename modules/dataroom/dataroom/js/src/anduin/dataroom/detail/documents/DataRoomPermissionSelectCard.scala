// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.documents

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.modal.Modal
import design.anduin.components.portal.PortalUtils.IsClosable
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import zio.ZIO

import anduin.dataroom.role.DataRoomRoleUtils
import anduin.dataroom.{DataRoomActionLoggerJS, DataRoomData}
import anduin.file.CreationCustomPermissions
import anduin.model.common.user.UserId
import anduin.protobuf.flow.file.FileFolderPermissionMap
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.endpoints.{FolderInfo, PermissionChanges}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.dataroom.documents.DataRoomFileFolderPermissionDetail

// This component should only be used when creating file/folder
final case class DataRoomPermissionSelectCard(
  userId: UserId,
  dataRoomData: DataRoomData,
  parentFolder: FolderInfo,
  customPermissions: StateSnapshotWithModFn[Option[CreationCustomPermissions]],
  isFolder: Boolean,
  isDisabled: Boolean
) {
  def apply(): VdomElement = DataRoomPermissionSelectCard.component(this)
}

object DataRoomPermissionSelectCard {

  private type Props = DataRoomPermissionSelectCard

  private def renderButton(props: Props, openToggle: Callback) = {
    Button(
      testId = "ChangePermission",
      style = Button.Style.Ghost(
        height = Button.Height.Fix24,
        icon = Some(Icon.Glyph.UserEdit)
      ),
      onClick = openToggle >>
        DataRoomActionLoggerJS.logActionEventDataRoomClickChangePermission(
          props.dataRoomData,
          if (props.isFolder) {
            DataRoomActionLoggerJS.ChangePermissionPlace.CreatingFolder
          } else {
            DataRoomActionLoggerJS.ChangePermissionPlace.UploadingFileFolder
          }
        ),
      isDisabled = props.isDisabled
    )("Edit permissions")
  }

  private def getCurrentChanges(props: Props) = {
    val changes = props.customPermissions.value.fold {
      PermissionChanges()
    } { customPermissions =>
      customPermissions.changes
    }
    PermissionChanges.toOptMaps(changes)
  }

  private def renderContent(
    props: Props,
    permissionDetail: DataRoomFileFolderPermissionDetail
  )(
    closeToggle: Callback
  ) = {
    val currentChanges = getCurrentChanges(props)
    val groupChanges = for {
      (teamId, changes) <- currentChanges._2
      groupId <- props.dataRoomData.groupMapByTeam.get(teamId).map(_.id)
    } yield (groupId, changes)
    DataRoomFilePermissionModalContent(
      item = if (props.isFolder) {
        DataRoomFilePermissionModalContent.Item.Folder(
          "New folder permissions",
          0,
          None
        )
      } else {
        DataRoomFilePermissionModalContent.Item.File("New file permissions")
      },
      permissionDetail = permissionDetail,
      initialUserChanges = currentChanges._1,
      initialGroupChanges = groupChanges,
      dataRoomData = props.dataRoomData,
      actorUserId = props.userId,
      onSave = Option { changes =>
        ZIO.attempt {
          val userPermissions = permissionDetail.userPermissions.flatMap { case (userId, detail) =>
            detail.permission.map(userId -> _)
          }
          val teamPermissions = permissionDetail.groupPermissions.flatMap { case (_, detail) =>
            detail.permission.map(detail.teamId -> _)
          }
          props.customPermissions
            .setState {
              Option.unless(PermissionChanges.isEmpty(changes)) {
                CreationCustomPermissions(FileFolderPermissionMap(userPermissions, teamPermissions), changes)
              }
            }
        }
      },
      closeToggle = closeToggle,
      managePermissionTreePageUrl = _ => None
    )()
  }

  private def partitionUsers(props: Props, permissionDetail: DataRoomFileFolderPermissionDetail) = {
    val (currentUserChanges, currentTeamChanges) = getCurrentChanges(props)
    val currentGroupChanges = for {
      (teamId, changes) <- currentTeamChanges
      groupId <- props.dataRoomData.groupMapByTeam.get(teamId).map(_.id)
    } yield (groupId, changes)
    val allPermissions = props.dataRoomData.latestState.participatingUsers.keySet.map { userId =>
      val groupIds = props.dataRoomData.getGroupsOfUser(userId).map(_.id).toList
      userId -> permissionDetail.getUser(
        userId,
        currentUserChanges,
        currentGroupChanges,
        groupIds
      )
    }.toMap
    val accessors = allPermissions.filter(_._2.nonEmpty).keySet
    accessors.partition { userId =>
      props.dataRoomData.latestState.dataRoomCreatedState.individualRoles
        .get(userId)
        .exists(DataRoomRoleUtils.isInternal)
    }
  }

  private def getManagePermissionsModal(props: Props, permissionDetail: DataRoomFileFolderPermissionDetail) = {
    Modal(
      renderTarget = renderButton(props, _),
      renderContent = renderContent(props, permissionDetail),
      isClosable = Some(IsClosable(onEsc = false, onOutsideClick = false)),
      size = Modal.Size(
        width = Modal.Width.Px1160,
        height = Modal.Height.Content
      )
    )()
  }

  private def renderExplanation(props: Props, permissionDetail: DataRoomFileFolderPermissionDetail) = {
    val (internal, external) = partitionUsers(props, permissionDetail)
    <.div(
      tw.flex.itemsCenter.justifyBetween,
      <.div(
        ComponentUtils.testId(DataRoomPermissionSelectCard, "Explanation"),
        s"Uploaded files will be visible to ",
        <.span(tw.fontSemiBold, internal.size, " internal"),
        " & ",
        <.span(tw.fontSemiBold, external.size, " external"),
        " members"
      ),
      getManagePermissionsModal(props, permissionDetail)
    )
  }

  private def renderLoading(props: Props) = {
    <.div(
      ^.cls := "loading-block",
      tw.hPx24.wPc100,
      <.div(
        tw.invisible,
        renderExplanation(
          props,
          DataRoomFileFolderPermissionDetail(
            Map(),
            Map(),
            None,
            None
          )
        )
      )
    )
  }

  private def render(props: Props): VdomNode = {
    Option.when(props.dataRoomData.latestState.isUserRole(props.userId)(DataRoomRoleUtils.isInternal)) {
      <.div(
        tw.py12.pl16.pr12.borderAll.rounded4,
        tw.borderGray3.border1.bgGray0.hPx48,
        WithDataRoomFileFolderPermissionDetails(
          dataRoomWorkflowId = props.dataRoomData.workflowId,
          fileFolderId = Right(props.parentFolder.itemId),
          renderLoading = renderLoading(props)
        ) { (details, _) =>
          val updatedDetails = details.getNewFileFolderPermissionDetail(
            parentFolder = props.parentFolder,
            userId = props.userId,
            isUnassignedUser = props.dataRoomData.unassignedParticipants.contains(props.userId)
          )
          renderExplanation(props, updatedDetails)
        }
      )
    }
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
