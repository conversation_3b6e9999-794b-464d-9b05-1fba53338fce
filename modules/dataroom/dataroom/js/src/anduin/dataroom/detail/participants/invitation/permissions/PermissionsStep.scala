// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.invitation.permissions

import anduin.dataroom.DataRoomData
import anduin.dataroom.detail.participants.group.DataRoomGroupPermissionModal
import anduin.dataroom.detail.participants.invitation.{InvitationGroupSettings, PermissionSettings}
import anduin.dataroom.detail.participants.permission.{DataRoomAssetPermissionSection, DataRoomGroupPermissionPopover}
import anduin.dataroom.role.DataRoomRole
import anduin.file.TooltipOnTruncate
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.service.FileServiceEndpoints.PermissionTarget
import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.ModalBody
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class PermissionsStep(
  dataRoomWorkflowId: DataRoomWorkflowId,
  settings: StateSnapshotWithModFn[PermissionSettings],
  dataRoomData: DataRoomData,
  actorUserId: UserId,
  groupSettings: InvitationGroupSettings,
  actorRole: DataRoomRole,
  footer: VdomNode
) {
  def apply(): VdomElement = PermissionsStep.component(this)
}

object PermissionsStep {

  private type Props = PermissionsStep

  private def render(props: Props) = {
    val permissionTargetOpt = props.groupSettings match {
      case InvitationGroupSettings.NoGroup => Option.empty[PermissionTarget]
      case InvitationGroupSettings.AddToGroups(groupIds) =>
        val teamIds = groupIds.flatMap(props.dataRoomData.groupMap.get).map(_.teamId)
        Some(PermissionTarget.MultipleTeams(teamIds))
    }
    React.Fragment(
      ModalBody()(
        props.groupSettings match {
          case InvitationGroupSettings.NoGroup               => EmptyVdom
          case InvitationGroupSettings.AddToGroups(groupIds) => renderGroupHelpText(props, groupIds.toSeq)
        },
        <.div(
          tw.flex.flexFill,
          SelectRoleSection(
            dataRoomWorkflowId = props.dataRoomWorkflowId,
            hasViewOnlyPlan = props.dataRoomData.hasViewOnlyPlan,
            settings = props.settings,
            actorRole = props.actorRole,
            groupSettings = props.groupSettings
          )(),
          renderMultiGroupsWarning(props)
        ),
        <.div(
          ComponentUtils.testId(PermissionsStep, "GroupPermissionTree"),
          tw.flex.flexCol.borderBottom.borderGray3,
          ^.height := { if (props.groupSettings.isNoGroup) 456.px else 420.px },
          DataRoomAssetPermissionSection(
            settings = props.settings,
            dataRoomData = props.dataRoomData,
            permissionTargetOpt = permissionTargetOpt,
            disabledReasonOpt =
              Option.when(props.groupSettings.isAddToGroups)("Permissions are inherited from the group")
          )()
        )
      ),
      props.footer
    )
  }

  private def renderMultiGroupsWarning(props: Props) = {
    val showWarning = props.groupSettings match {
      case InvitationGroupSettings.NoGroup => false
      case InvitationGroupSettings.AddToGroups(groupIds) =>
        groupIds.flatMap(props.dataRoomData.getGroupData(_)).size > 1
    }
    Option.when(showWarning)(
      <.div(
        tw.flex.flexFill.justifyEnd.itemsEnd.py8,
        <.div(
          tw.flex.itemsCenter.py6,
          <.div(
            tw.textPrimary4,
            IconR(name = Icon.Glyph.Info)()
          ),
          <.div(
            tw.mx8,
            "When groups have different permission levels, participants will receive the highest level of role and access"
          )
        )
      )
    )
  }

  private def renderGroupHelpText(props: Props, groupIds: Seq[DataRoomGroupId]): VdomNode = {
    groupIds match {
      case Nil => EmptyVdom
      case groupId :: Nil =>
        props.dataRoomData.groupMap.get(groupId).fold(EmptyVdom) { groupData =>
          <.div(
            ComponentUtils.testId(PermissionsStep, "Instruction"),
            tw.flex.mb16,
            "Participants will inherit permissions from the group ",
            <.div(
              ^.maxWidth := 250.px,
              TooltipOnTruncate(
                renderTarget = <.div.withRef(_)(
                  tw.ml4.fontSemiBold.truncate,
                  ^.textOverflow.ellipsis,
                  groupData.name
                ),
                content = groupData.name
              )()
            ),
            ". These permissions can only be changed at the group level.",
            DataRoomGroupPermissionModal(
              actorUserId = props.actorUserId,
              dataRoomData = props.dataRoomData,
              groupData = groupData,
              renderTarget = (_, openToggle) =>
                <.div(
                  tw.ml4,
                  Button(
                    testId = "PermissionsStep-EditGroupLink",
                    style = Button.Style.Text(color = Button.Color.Primary),
                    onClick = openToggle
                  )("Edit group permissions")
                )
            )()
          )
        }
      case _ =>
        <.div(
          tw.flex.itemsCenter,
          ComponentUtils.testId(PermissionsStep, "Instruction"),
          tw.mb16,
          "Participants will inherit permissions from ",
          DataRoomGroupPermissionPopover(props.dataRoomData, groupIds, props.actorUserId)(),
          " and these permissions can only be changed at the group level."
        )
    }
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
