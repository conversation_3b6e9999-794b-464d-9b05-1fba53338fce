// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.user

import anduin.dataroom.DataRoomData
import anduin.dataroom.detail.participants.user.DataRoomUserActionMenu.UserAction
import anduin.layout.SelectionData
import anduin.model.common.user.UserId
import anduin.utils.StateSnapshotWithModFn
import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import stargazer.model.routing.{DynamicAuthPage, Page}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class DataRoomUserActionColumn(
  router: RouterCtl[Page],
  page: DynamicAuthPage.DataRoomParticipantsPage,
  actorUserId: UserId,
  dataRoomData: DataRoomData,
  row: DataRoomUserRowData,
  userSelection: StateSnapshotWithModFn[SelectionData[UserId]],
  currentAdminCount: Int,
  refetch: Callback
) {
  def apply(): VdomElement = DataRoomUserActionColumn.component(this)

  val userActions: Seq[UserAction] = DataRoomUserActionMenu.getUserActions(
    dataRoomData,
    actorUserId,
    List(row)
  )

}

object DataRoomUserActionColumn {

  private type Props = DataRoomUserActionColumn

  private def render(props: Props) = {
    Option.when(props.userActions.nonEmpty)(
      <.div(
        ^.onClick ==> stopPropagation,
        ^.onDoubleClick ==> stopPropagation,
        tw.flex.justifyEnd,
        PopoverR(
          renderTarget = (openToggle, _) => renderTarget(props)(openToggle),
          renderContent = renderMenu(props),
          position = PortalPosition.BottomRight
        )()
      )
    )
  }

  private def renderTarget(props: Props)(openToggle: Callback) = {
    Option.when(props.userActions.nonEmpty) {
      Button(
        testId = "UserActions",
        style = Button.Style.Minimal(height = Button.Height.Fix32, icon = Some(Icon.Glyph.EllipsisHorizontal)),
        onClick = openToggle
      )()
    }
  }

  private def renderMenu(props: Props)(closeToggle: Callback) = {
    DataRoomUserActionMenu(
      router = props.router,
      page = props.page,
      actorUserId = props.actorUserId,
      dataRoomData = props.dataRoomData,
      rows = List(props.row),
      userSelection = props.userSelection,
      currentAdminCount = props.currentAdminCount,
      refetch = props.refetch,
      onClose = closeToggle
    )()
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
