// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.participants.invitation.participants

import design.anduin.components.button.Button
import design.anduin.components.modal.ModalFooter
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.dataroom.detail.participants.invitation.{InvitationStep, InvitationUsers, InvitationGroupSettings}
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.model.common.emailaddress.EmailAddress
import anduin.utils.StateSnapshotWithModFn

private[participants] final case class FooterSection(
  step: StateSnapshotWithModFn[InvitationStep],
  users: StateSnapshotWithModFn[InvitationUsers],
  existingUsers: Set[String],
  remainingSeatCount: Int,
  actorRole: DataRoomRole,
  groupSettings: InvitationGroupSettings
) {
  def apply(): VdomElement = FooterSection.component(this)
}

private[participants] object FooterSection {

  private type Props = FooterSection

  private def getIsDisabled(props: Props) = {
    val allEmails = props.users.value.emails
    val isAllDistinct = allEmails.toSet.size == allEmails.size
    val isAllEmailsValid = allEmails.forall { email =>
      EmailAddress.isValid(email) && email.nonEmpty && !props.existingUsers.contains(email)
    }
    val isThereAtLeastOne = allEmails.nonEmpty
    val isInvitingInternal = DataRoomRoleUtils.isInternal(props.users.value.settings.role)
    val isLessThanSeatLimit = !isInvitingInternal || allEmails.size <= props.remainingSeatCount
    val validGroup = props.groupSettings match {
      case InvitationGroupSettings.NoGroup               => true
      case InvitationGroupSettings.AddToGroups(groupIds) => groupIds.nonEmpty
    }
    !(isAllDistinct && isAllEmailsValid && isThereAtLeastOne && isLessThanSeatLimit && validGroup)
  }

  private def render(props: Props) = {
    val nextLabel = if (props.groupSettings.isAddToGroups) {
      "Review permissions"
    } else if (DataRoomRoleUtils.isRestricted(props.actorRole)) {
      "Set permissions"
    } else {
      "Set role and permissions"
    }
    ModalFooter()(
      <.div(
        tw.flex.itemsCenter.justifyEnd,
        Button(
          style = Button.Style.Full(color = Button.Color.Primary),
          isDisabled = getIsDisabled(props),
          onClick = props.step.setState(InvitationStep.Permissions)
        )(s"Next: $nextLabel")
      )
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
