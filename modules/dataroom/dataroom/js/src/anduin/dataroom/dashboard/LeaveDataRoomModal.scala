// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.dashboard

import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.model.common.user.UserId
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.stargazer.service.dataroom.RemoveUsersFromDataRoomParams
import com.anduin.stargazer.client.utils.ZIOUtils
import design.anduin.components.button.Button
import design.anduin.components.modal.{Modal, ModalBody, ModalFooterWCancel}
import design.anduin.components.toast.Toast
import japgolly.scalajs.react.vdom.VdomNode
import stargazer.util.HtmlTagUtils.semiBoldText

final case class LeaveDataRoomModal(
  dataRoomData: DataRoomDashboardData,
  userId: UserId,
  renderTarget: (<PERSON><PERSON><PERSON>, Callback) => VdomNode,
  onClose: Callback,
  refetch: Callback
) {
  def apply(): VdomElement = LeaveDataRoomModal.component(this)
}

object LeaveDataRoomModal {

  type Props = LeaveDataRoomModal

  final case class State(
    isBusy: Boolean
  )

  private def canRemove(props: Props): Boolean = {
    props.dataRoomData.latestState.dataRoomCreatedState.individualRoles.get(props.userId).exists { role =>
      !DataRoomRoleUtils.isAdmin(role) || {
        val joinedAdminCount = props.dataRoomData.latestState.joinedUsers.count { userId =>
          props.dataRoomData.latestState.dataRoomCreatedState.individualRoles
            .get(userId)
            .exists(DataRoomRoleUtils.isAdmin)
        }
        joinedAdminCount > 1
      }
    }
  }

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      Modal(
        renderTarget = openToggle => props.renderTarget(canRemove(props), openToggle),
        title = "Leave data room",
        renderContent = closeToggle =>
          React.Fragment(
            ModalBody()(
              <.p(
                "Are you sure you want to leave ",
                semiBoldText(props.dataRoomData.latestState.dataRoomCreatedState.name),
                "?"
              ),
              <.p("You will not be able to access the data room and its content.")
            ),
            ModalFooterWCancel(closeToggle)(
              Button(
                style = Button.Style.Full(isBusy = state.isBusy, color = Button.Color.Danger),
                onClick = leaveDataRoom(closeToggle)
              )("Leave")
            )
          ),
        afterUserClose = props.onClose
      )()
    }

    private def leaveDataRoom(closeToggle: Callback) = for {
      props <- scope.props
      _ <- scope.modState(_.copy(isBusy = true))
      _ <- ZIOUtils.toReactCallback {
        for {
          resp <- DataRoomEndpointClient.removeUsers(
            RemoveUsersFromDataRoomParams(
              props.dataRoomData.workflowId,
              Set(props.userId),
              doNotNotifyByEmail = false
            )
          )
        } yield scope.modState(
          _.copy(isBusy = false),
          resp.fold(
            _ => Toast.errorCallback("Failed to leave data room"),
            _ => Toast.successCallback("Left data room successfully") >> props.refetch
          ) >> closeToggle
        )
      }
    } yield ()

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State(isBusy = false))
    .renderBackend[Backend]
    .build

}
