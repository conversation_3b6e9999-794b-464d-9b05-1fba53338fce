// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.dashboard

import anduin.component.util.JsDateFormatterUtils
import anduin.dataroom.*
import anduin.dataroom.dashboard.DataRoomDashboardTable.Column.LastUpdated
import anduin.model.common.user.UserId
import anduin.orgbilling.model.plan.DataRoomPlan
import anduin.protobuf.page.DataRoomDashboardFilter
import design.anduin.components.button.Button
import design.anduin.components.clipboard.react.CopyToClipboardR
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.menu.react.{MenuDividerR, MenuItemR, MenuR}
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.components.tag.Tag
import design.anduin.components.tag.react.TagR
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.facades.reactvirtualized.{ReactVirtualizedAutoSizer, ReactVirtualizedList}
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import stargazer.model.routing.Page

final case class DataRoomDashboardTable(
  router: RouterCtl[Page],
  userId: UserId,
  filter: DataRoomDashboardFilter,
  dataRooms: Seq[DataRoomDashboardData],
  participatedOrgBillingModels: Option[Seq[DataRoomOrgBillingModel]],
  refetch: Callback
) {

  val columns: Seq[DataRoomDashboardTable.Column] = {
    val isInvitation = filter.status.isInvitation
    val isContainingAdmin = dataRooms.exists(DataRoomDashboardTable.isAdmin(this))
    Seq(
      Seq(
        DataRoomDashboardTable.Column.Name,
        DataRoomDashboardTable.Column.DataRoomId,
        DataRoomDashboardTable.Column.CreatorEntity
      ),
      if (isInvitation) {
        Seq(
          DataRoomDashboardTable.Column.InviterUser,
          DataRoomDashboardTable.Column.InvitationDate,
          DataRoomDashboardTable.Column.RespondInvitation
        )
      } else {
        Seq(
          DataRoomDashboardTable.Column.Creator,
          DataRoomDashboardTable.Column.LastUpdated,
          DataRoomDashboardTable.Column.Status
        )
      },
      if (!isInvitation && isContainingAdmin) Seq(DataRoomDashboardTable.Column.Action) else Seq()
    ).flatten
  }

  def apply(): VdomElement = DataRoomDashboardTable.component(this)
}

object DataRoomDashboardTable {

  val nameColumnWidth = "30%"
  val actionSmallColumnWidth = "56px"
  val actionLargeColumnWidth = "180px"
  val statusColumnWidth = "12%"
  val mediumColumnWidth = "14%"

  private type Props = DataRoomDashboardTable

  enum Column derives CanEqual {

    case Name, DataRoomId, CreatorEntity, InviterUser, InvitationDate, RespondInvitation, Creator, LastUpdated, Status,
      Action

  }

  private def isAdmin(props: Props)(dataRoom: DataRoomDashboardData) = {
    dataRoom.latestState.userInvitedOpt.isEmpty && dataRoom.latestState.dataRoomCreatedState.individualRoles
      .get(props.userId)
      .exists(_.asMessage.sealedValue.isAdmin)
  }

  private case class State(
    sortedByColumn: Option[Column] = Option(LastUpdated),
    sortIsAsc: Boolean = false
  )

  private case class Backend(scope: BackendScope[Props, State]) {

    private def checkPlan(dataRoom: DataRoomDashboardData, allowGracePeriod: Boolean = false) = {
      DataRoomPermissionCheck
        .expiredPlan(dataRoom.dataRoomPlan, allowGracePeriod)
    }

    private def renderDataRoomIcon(dataRoom: DataRoomDashboardData) = {
      <.div(
        tw.flex.itemsCenter,
        checkPlan(dataRoom).fold(
          errorMessage =>
            TooltipR(
              renderTarget = IconR(name = Icon.Illustration.Lock, size = Icon.Size.Px24)(),
              renderContent = _(errorMessage)
            )(),
          _ =>
            dataRoom.dataRoomWhiteLabelData.dataRoomIconUrl.fold(
              IconR(name = Icon.Glyph.Safe, size = Icon.Size.Px24)()
            ) { iconUrl =>
              <.img(
                tw.wPx24.hPx24.rounded2,
                ^.src := iconUrl
              )
            }
        )
      )
    }

    private def renderDataRoomName(dataRoom: DataRoomDashboardData) = {
      val isInvitation = dataRoom.latestState.userInvitedOpt.isDefined
      <.div(
        tw.flex.itemsCenter,
        <.div(
          tw.flexNone,
          renderDataRoomIcon(dataRoom)
        ),
        <.div(
          <.div(
            ComponentUtils.testId(DataRoomDashboardTable, "DrName"),
            tw.flexFill.fontSemiBold.pl8,
            dataRoom.latestState.dataRoomCreatedState.name
          ),
          TagMod.unless(isInvitation) {
            <.div(
              tw.flexFill.fontNormal.text11.leading16.textPrimary5.pl8,
              ^.onClick ==> stopPropagation,
              DataRoomMemberModalButton(
                creatorUserId = dataRoom.latestState.dataRoomCreatedState.creatorUserId,
                creatorEntity = dataRoom.latestState.creatorEntity,
                roleMap = dataRoom.latestState.dataRoomCreatedState.individualRoles,
                participatingUsers = dataRoom.latestState.participatingUserInfos,
                isArchived = dataRoom.latestState.dataRoomCreatedState.isArchived
              )()
            )
          }
        )
      )
    }

    private def renderCreatorEntity(dataRoom: DataRoomDashboardData) = {
      <.div(
        ComponentUtils.testId(DataRoomDashboardTable, "Entity"),
        tw.flex.itemsCenter,
        <.div(
          tw.flexFill.truncate,
          dataRoom.latestState.creatorEntity.entityModel.name
        )
      )
    }

    private def getCreatorName(dataRoom: DataRoomDashboardData) = {
      dataRoom.latestState.dataRoomCreatedState.creatorUserInfo.fullNameString
    }

    private def renderCreator(dataRoom: DataRoomDashboardData) = {
      React.Fragment(
        <.span(
          ComponentUtils.testId(DataRoomDashboardTable, "Creator"),
          getCreatorName(dataRoom)
        ),
        dataRoom.latestState.createdAt.map { createdAt =>
          <.p(
            ComponentUtils.testId(DataRoomDashboardTable, "CreateDate"),
            tw.textGray7.text11.leading16,
            "Created on ",
            JsDateFormatterUtils.format(createdAt, JsDateFormatterUtils.JsDateFormat.LongDatePattern1)
          )
        }
      )
    }

    private def getStatus(dataRoom: DataRoomDashboardData) = {
      if (dataRoom.latestState.dataRoomCreatedState.isArchived) {
        Tag.Light.Danger -> "Archived"
      } else {
        dataRoom.latestState.userInvitedOpt.fold {
          checkPlan(dataRoom).fold(
            _ =>
              dataRoom.dataRoomPlan match {
                case DataRoomPlan.DataRoomBusinessPlan(_, _, isTrial) if isTrial =>
                  Tag.Bold.Gray -> "Trial ended"
                case _ => Tag.Bold.Danger -> "Expired"
              },
            _ => Tag.Light.Primary -> "Active"
          )
        } { _ =>
          Tag.Light.Warning -> "Pending"
        }
      }
    }

    private def renderLastUpdated(dataRoom: DataRoomDashboardData) = {
      dataRoom.latestState.lastUpdatedAt.map { lastUpdatedAt =>
        React.Fragment(
          <.span(
            ComponentUtils.testId(DataRoomDashboardTable, "LastUpdatedDate"),
            JsDateFormatterUtils.format(lastUpdatedAt, JsDateFormatterUtils.JsDateFormat.LongDatePattern1)
          ),
          <.p(
            ComponentUtils.testId(DataRoomDashboardTable, "LastUpdatedTime"),
            tw.textGray7.text11.leading16,
            JsDateFormatterUtils.format(lastUpdatedAt, JsDateFormatterUtils.JsDateFormat.LongTimePattern2)
          )
        )
      }
    }

    private def renderStatus(dataRoom: DataRoomDashboardData) = {
      val (tagColor, text) = getStatus(dataRoom)
      val errorMessage = checkPlan(dataRoom)
      <.div(
        ComponentUtils.testId(DataRoomDashboardTable, "Status"),
        tw.flex,
        TooltipR(
          renderTarget = TagR(color = tagColor, label = text)(),
          renderContent = _(errorMessage.left.getOrElse[String]("")),
          isDisabled = errorMessage.isRight
        )()
      )
    }

    private def getInviterUserInfo(dataRoom: DataRoomDashboardData) = {
      dataRoom.latestState.userInvitedOpt.flatMap(_.inviterInfo)
    }

    private def getInvitationDateInfo(dataRoom: DataRoomDashboardData) = {
      dataRoom.latestState.userInvitedOpt.flatMap(_.invitedAt)
    }

    private def renderInviterUser(dataRoom: DataRoomDashboardData) = {
      getInviterUserInfo(dataRoom).map { userInfo =>
        <.div(
          ComponentUtils.testId(DataRoomDashboardTable, "Inviter"),
          userInfo.getDisplayName
        )
      }
    }

    private def renderInvitationDate(dataRoom: DataRoomDashboardData) = {
      getInvitationDateInfo(dataRoom).map { invitedAt =>
        <.div(
          ComponentUtils.testId(DataRoomDashboardTable, "InvitedDate"),
          JsDateFormatterUtils.format(invitedAt, JsDateFormatterUtils.JsDateFormat.LongDatePattern1)
        )
      }
    }

    private def renderDataRoomId(dataRoom: DataRoomDashboardData) = {
      <.div(
        ^.onClick ==> (_.stopPropagationCB),
        CopyToClipboardR(
          content = dataRoom.workflowId.idString,
          renderTarget = content => {
            div(
              ComponentUtils.testIdL(DataRoomDashboardTable, "DataRoomId"),
              tw.flex.itemsCenter,
              div(
                tw.fontMono.breakAll.mr4,
                content
              ),
              ButtonL(
                style = ButtonL.Style.Minimal(icon = Some(Icon.Glyph.Duplicate)),
                title = "Copy to clipboard"
              )()
            )
          }
        )()
      )
    }

    private def renderRespondInvitation(props: Props, dataRoom: DataRoomDashboardData) = {
      <.div(
        ^.onClick ==> { (e: ReactEventFromHtml) =>
          e.stopPropagationCB
        },
        tw.flex.itemsCenter.justifyEnd,
        dataRoom.latestState.userInvitedOpt.map { _ =>
          React.Fragment(
            DataRoomAcceptInvitationFromDashboardButton(
              router = props.router,
              userId = props.userId,
              dataRoomData = dataRoom,
              refetch = props.refetch
            )(),
            <.div(
              tw.pl8,
              DataRoomDeclineInvitationButton(dataRoom.workflowId, props.refetch)()
            )
          )
        }
      )
    }

    private def renderTarget(dataRoom: DataRoomDashboardData)(openToggle: Callback) = {
      MenuItemR(
        onClick = openToggle,
        icon = Some(Icon.Glyph.Archive)
      )(
        if (dataRoom.latestState.dataRoomCreatedState.isArchived) "Unarchive" else "Archive"
      )
    }

    private def renderAction(props: Props, dataRoom: DataRoomDashboardData) = {
      val isActorAdmin = isAdmin(props)(dataRoom)
      val isPlanExpiredBeyondGracePeriod = checkPlan(
        dataRoom,
        allowGracePeriod = true
      ).isLeft
      val isArchived = dataRoom.latestState.dataRoomCreatedState.isArchived
      val entitiesWithPlan = DataRoomDashboardUtils.getEntitiesWithPlan(props.participatedOrgBillingModels)
      // Premium features of the subscription plan of user organization
      val orgPremiumFeats = DataRoomDashboardUtils.getPremiumFeaturesFromPlan(
        props.participatedOrgBillingModels.map(_.map(_.dataRoomPlan)).getOrElse(Seq.empty)
      )
      // Premium features of the subscription plan of the data room
      val dataRoomPremiumFeats = DataRoomDashboardUtils.getPremiumFeaturesFromPlan(Seq(dataRoom.dataRoomPlan))
      // Premium features that are enabled in general settings of the data room
      val dataRoomRequiredPremiumFeats =
        DataRoomDashboardUtils.getDataRoomRequiredPremiumFeatures(dataRoom.latestState.dataRoomCreatedState)
      val isDuplicatable =
        dataRoomPremiumFeats.subsetOf(orgPremiumFeats) && dataRoomRequiredPremiumFeats.subsetOf(orgPremiumFeats)
      if (!isPlanExpiredBeyondGracePeriod) {
        <.div(
          ^.onClick ==> { (e: ReactEventFromHtml) =>
            e.stopPropagationCB
          },
          PopoverR(
            renderTarget = { (openToggle, _) =>
              Button(
                testId = "Actions",
                style = Button.Style.Minimal(height = Button.Height.Fix32, icon = Some(Icon.Glyph.EllipsisHorizontal)),
                onClick = openToggle
              )()
            },
            renderContent = { closeToggle =>
              MenuR()(
                Option.when(isActorAdmin && !isArchived && entitiesWithPlan.nonEmpty) {
                  DuplicateDataRoomMenuItem(
                    router = props.router,
                    dataRoomWorkflowId = dataRoom.workflowId,
                    oldName = dataRoom.latestState.dataRoomCreatedState.name,
                    disableTooltipTextOpt = if (isDuplicatable) {
                      None
                    } else {
                      Some("Can't be duplicated as your current plan doesn't support the same features")
                    },
                    closeMenuToggle = closeToggle,
                    refetch = props.refetch
                  )()
                },
                Option.when(isActorAdmin && !isArchived) {
                  RenameDataRoomMenuItem(
                    dataRoom.workflowId,
                    dataRoom.latestState.dataRoomCreatedState.name,
                    closeToggle,
                    props.refetch
                  )()
                },
                Option.when(isActorAdmin && !isArchived) {
                  SetIsArchivedDataRoomMenuItem(
                    dataRoomWorkflowId = dataRoom.workflowId,
                    renderTarget = renderTarget(dataRoom),
                    roleMap = dataRoom.latestState.dataRoomCreatedState.individualRoles,
                    users =
                      dataRoom.latestState.participatingUserInfos.removed(props.userId).map { (userId, userInfo) =>
                        userId -> DataRoomUserData.UserInfo(userInfo)
                      },
                    creatorEntity = dataRoom.latestState.creatorEntity,
                    currentIsArchived = dataRoom.latestState.dataRoomCreatedState.isArchived,
                    name = dataRoom.latestState.dataRoomCreatedState.name,
                    closeMenuToggle = closeToggle,
                    onUpdate = props.refetch
                  )()
                },
                Option.unless(isArchived) {
                  LeaveDataRoomModal(
                    dataRoomData = dataRoom,
                    userId = props.userId,
                    renderTarget = (canRemove, openToggle) => {
                      Option.when(canRemove) {
                        React.Fragment(
                          Option.when(isActorAdmin)(MenuDividerR()()),
                          MenuItemR(
                            onClick = openToggle,
                            icon = Some(Icon.Glyph.Logout),
                            color = MenuItemR.ColorDanger
                          )("Leave data room")
                        )
                      }
                    },
                    onClose = closeToggle,
                    refetch = props.refetch
                  )()
                }
              )
            },
            position = PortalPosition.BottomRight
          )()
        )
      } else {
        <.div()
      }
    }

    private def onSortColumn(column: Column) = {
      for {
        state <- scope.state
        sortIsAsc =
          if (state.sortedByColumn.contains(column)) {
            !state.sortIsAsc
          } else {
            true
          }
        _ <- scope.modState(
          _.copy(
            sortIsAsc = sortIsAsc,
            sortedByColumn = Option(column)
          )
        )
      } yield ()
    }

    private def renderColumnHeaders(props: Props, state: State) = {
      <.div(
        tw.border1.borderGray3.borderBottom.borderTop.bgGray1,
        ComponentUtils.testId(DataRoomDashboardTable, "Table-Head"),
        tw.flex.itemsCenter.borderBottom.borderGray3,
        // Stick at top
        tw.sticky.z1.top0,
        props.columns.zipWithIndex.toVdomArray(
          using { case (column, index) =>
            val col = column match {
              case Column.Action =>
                <.div(
                  ^.width := actionSmallColumnWidth,
                  DataRoomDashboardColumnHead(
                    column = Column.Action
                  )()
                )
              case Column.Creator =>
                <.div(
                  ^.width := mediumColumnWidth,
                  DataRoomDashboardColumnHead(
                    column = Column.Creator,
                    sortColumn = state.sortedByColumn,
                    sortIsAsc = state.sortIsAsc,
                    onSort = onSortColumn
                  )()
                )
              case Column.CreatorEntity =>
                <.div(
                  ^.width := mediumColumnWidth,
                  DataRoomDashboardColumnHead(
                    column = Column.CreatorEntity,
                    sortColumn = state.sortedByColumn,
                    sortIsAsc = state.sortIsAsc,
                    onSort = onSortColumn
                  )()
                )
              case Column.DataRoomId =>
                <.div(
                  ^.width := mediumColumnWidth,
                  DataRoomDashboardColumnHead(
                    column = Column.DataRoomId,
                    sortColumn = state.sortedByColumn,
                    sortIsAsc = state.sortIsAsc,
                    onSort = onSortColumn
                  )()
                )
              case Column.InvitationDate =>
                <.div(
                  ^.width := mediumColumnWidth,
                  DataRoomDashboardColumnHead(
                    column = Column.InvitationDate,
                    sortColumn = state.sortedByColumn,
                    sortIsAsc = state.sortIsAsc,
                    onSort = onSortColumn
                  )()
                )
              case Column.InviterUser =>
                <.div(
                  ^.width := mediumColumnWidth,
                  DataRoomDashboardColumnHead(
                    column = Column.InviterUser,
                    sortColumn = state.sortedByColumn,
                    sortIsAsc = state.sortIsAsc,
                    onSort = onSortColumn
                  )()
                )
              case Column.LastUpdated =>
                <.div(
                  ^.width := mediumColumnWidth,
                  DataRoomDashboardColumnHead(
                    column = Column.LastUpdated,
                    sortColumn = state.sortedByColumn,
                    sortIsAsc = state.sortIsAsc,
                    onSort = onSortColumn
                  )()
                )
              case Column.Name =>
                <.div(
                  ^.width := nameColumnWidth,
                  DataRoomDashboardColumnHead(
                    column = Column.Name,
                    sortColumn = state.sortedByColumn,
                    sortIsAsc = state.sortIsAsc,
                    onSort = onSortColumn
                  )()
                )
              case Column.RespondInvitation =>
                <.div(
                  ^.width := actionLargeColumnWidth,
                  DataRoomDashboardColumnHead(
                    column = Column.RespondInvitation
                  )()
                )
              case Column.Status =>
                <.div(
                  ^.width := statusColumnWidth,
                  DataRoomDashboardColumnHead(
                    column = Column.Status,
                    sortColumn = state.sortedByColumn,
                    sortIsAsc = state.sortIsAsc,
                    onSort = onSortColumn
                  )()
                )
            }
            col(^.key := index)
          }
        )
      )
    }

    private def renderVirtualizedDataRoomItem(
      props: Props,
      dataRooms: Seq[DataRoomDashboardData],
      renderRowProps: ReactVirtualizedList.RowRenderProps
    ) = {
      val rowIndex = renderRowProps.index
      val dataRoom = dataRooms(rowIndex)
      val page = dataRoom.pageWhenAccess
      <.div(
        ^.key := dataRoom.workflowId.idString,
        ^.style := renderRowProps.style,
        tw.flex.itemsCenter.borderBottom.border1.borderGray3.hover(tw.bgGray1),
        TagMod.when(!dataRoom.latestState.dataRoomCreatedState.isArchived) {
          TagMod(
            props.router.setOnClick(page),
            tw.cursorPointer
          )
        },
        props.columns.zipWithIndex.toVdomArray(
          using { case (column, index) =>
            val col = column match {
              case Column.Action =>
                <.div(
                  tw.p12,
                  ^.width := actionSmallColumnWidth,
                  renderAction(props, dataRoom)
                )
              case Column.Creator =>
                <.div(
                  tw.p12,
                  ^.width := mediumColumnWidth,
                  renderCreator(dataRoom)
                )
              case Column.CreatorEntity =>
                <.div(
                  tw.p12,
                  ^.width := mediumColumnWidth,
                  renderCreatorEntity(dataRoom)
                )
              case Column.DataRoomId =>
                <.div(
                  tw.p12,
                  ^.width := mediumColumnWidth,
                  renderDataRoomId(dataRoom)
                )
              case Column.InvitationDate =>
                <.div(
                  tw.p12,
                  ^.width := mediumColumnWidth,
                  renderInvitationDate(dataRoom)
                )
              case Column.InviterUser =>
                <.div(
                  tw.p12,
                  ^.width := mediumColumnWidth,
                  renderInviterUser(dataRoom)
                )
              case Column.LastUpdated =>
                <.div(
                  tw.p12,
                  ^.width := mediumColumnWidth,
                  renderLastUpdated(dataRoom)
                )
              case Column.Name =>
                <.div(
                  tw.p12,
                  ^.width := nameColumnWidth,
                  renderDataRoomName(dataRoom)
                )
              case Column.RespondInvitation =>
                <.div(
                  tw.p12,
                  ^.width := actionLargeColumnWidth,
                  renderRespondInvitation(props, dataRoom)
                )
              case Column.Status =>
                <.div(
                  tw.p12,
                  ^.width := statusColumnWidth,
                  renderStatus(dataRoom)
                )
            }
            col(^.key := index)
          }
        )
      )
    }

    def render(props: Props, state: State): VdomElement = {
      val rows = props.dataRooms
      val sortedRows = state.sortedByColumn
        .map { sortBy =>
          val sorted = sortBy match {
            case Column.Creator        => rows.sortBy(data => getCreatorName(data).toLowerCase)
            case Column.CreatorEntity  => rows.sortBy(_.latestState.creatorEntity.entityModel.name.toLowerCase)
            case Column.DataRoomId     => rows.sortBy(_.workflowId.idString.toLowerCase)
            case Column.InvitationDate => rows.sortBy(getInvitationDateInfo)
            case Column.InviterUser =>
              rows.sortBy(getInviterUserInfo(_).map(_.getDisplayName.toLowerCase))
            case Column.LastUpdated => rows.sortBy(_.latestState.lastUpdatedAt)
            case Column.Name        => rows.sortBy(_.latestState.dataRoomCreatedState.name.toLowerCase)
            case Column.Status      => rows.sortBy(getStatus(_)._2)
            case _                  => rows
          }
          if (state.sortIsAsc) sorted else sorted.reverse
        }
        .getOrElse(rows)
      <.div(
        tw.flex.flexCol.hPc100.overflowHidden,
        ComponentUtils.testId(DataRoomDashboardTable, "Table-Container"),
        // Header
        renderColumnHeaders(props, state),
        <.div(
          tw.flexFill,
          ComponentUtils.testId(DataRoomDashboardTable, "Table-Body"),
          ReactVirtualizedAutoSizer(
            childrenParam = renderProps => {
              ReactVirtualizedList(
                heightParam = renderProps.height,
                rowCountParam = sortedRows.length,
                rowHeightParam = _ => 61,
                rowRendererParam = renderVirtualizedDataRoomItem(
                  props,
                  sortedRows,
                  _
                ).rawNode,
                widthParam = renderProps.width
              ).rawNode
            }
          )
        )
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
