// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.reactive

import anduin.file.ReactStream
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.notichannel.DataRoomNotificationChannels
import com.anduin.stargazer.notiCenter.NotificationCenter

object DataRoomReactStream {

  def dataRoomFile(dataRoomWorkflowId: DataRoomWorkflowId) =
    ReactStream(
      watch = NotificationCenter.eventSignal(
        DataRoomNotificationChannels.dataRoomFile(dataRoomWorkflowId)
      ),
      channel = DataRoomNotificationChannels.dataRoomFile(dataRoomWorkflowId)
    )

}
