// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom

import anduin.file.TooltipOnTruncate
import anduin.file.explorer.DmsIcon
import anduin.file.tree.FileTreeLoader
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{FileId, FolderId}
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.endpoints.{FileInfo, FileManagerLocation, FolderInfo}
import design.anduin.components.button.Button
import design.anduin.components.checkbox.Checkbox
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.tree.Tree
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import stargazer.model.routing.{DynamicAuthPage, Page}

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class QuickFileSelection(
  router: RouterCtl[Page],
  dataRoomWorkflowId: DataRoomWorkflowId,
  fileIds: StateSnapshotWithModFn[Set[FileId]]
) {
  def apply(): VdomElement = QuickFileSelection.component(this)
}

object QuickFileSelection {
  private type Props = QuickFileSelection

  private type ItemType = Either[FolderInfo, FileInfo]

  private val TypedTree = (new Tree[ItemType])()

  private def render(props: Props) = {
    FileTreeLoader.tree(
      FileManagerLocation.Folder(None, FolderId.channelSystemFolderId(props.dataRoomWorkflowId)),
      includeAllFileIds = true
    ) {
      _.collect { case rootFolderInfo: FolderInfo =>
        if (!isRootEmpty(rootFolderInfo)) {
          React.Fragment(
            renderNonEmptyRoot(props)(rootFolderInfo),
            renderExpandable(
              props,
              rootFolderInfo
            )
          )
        } else {
          renderEmptyRoot(props)
        }
      }.getOrElse(BlockIndicatorR()())
    }
  }

  private def renderExpandable(props: Props, rootFolderInfo: FolderInfo) = {
    TypedTree(
      node = Left(rootFolderInfo),
      render = renderItem(props, rootFolderInfo),
      getKey = _.fold(_.itemId, _.itemId).idString,
      loader = loader,
      shouldExpanded = shouldExpanded(props),
      rendererOnExpand = renderer
    )()
  }

  private def renderEmptyRoot(props: Props) = {
    <.div(
      tw.flex.itemsCenter.justifyCenter,
      ^.height := "374px",
      <.div(
        tw.flexNone.flex.flexCol.itemsCenter,
        <.div(
          tw.textGray3,
          IconR(name = Icon.Glyph.FileText, size = Icon.Size.Custom(64))()
        ),
        <.p(
          tw.textGray6.py16,
          "You haven't uploaded any document to this data room"
        ),
        Button(
          style = Button.Style.Minimal(
            color = Button.Color.Primary,
            icon = Some(Icon.Glyph.OpenNewWindow)
          ),
          tpe = Button.Tpe.Link(
            href = props.router
              .urlFor(
                DynamicAuthPage.DataRoomFolderPage(
                  props.dataRoomWorkflowId,
                  FolderId.channelSystemFolderId(props.dataRoomWorkflowId),
                  None
                )
              )
              .value,
            target = Button.Target.Blank
          )
        )("Go to Documents tab")
      )
    )
  }

  private def isRootEmpty(folderInfo: FolderInfo) = {
    getChildCount(folderInfo) == 0 ||
    folderInfo.files.forall(_.isEmpty) && folderInfo.folders.forall(_.forall(getChildCount(_) == 0))
  }

  private def shouldExpanded(props: Props)(node: ItemType) = {
    isRootItem(props, node) // is root
  }

  private def getFolderAllFileIds(rootFolderInfo: FolderInfo)(folderId: FolderId): Set[FileId] = {
    rootFolderInfo.fileIds.filter(_.ancestorFolders.contains(folderId))
  }

  private def isFolderChecked(props: Props, rootFolderInfo: FolderInfo)(folderInfo: FolderInfo): (Boolean, Boolean) = {
    val allFileIds = getFolderAllFileIds(rootFolderInfo)(folderInfo.itemId)
    val isChecked = props.fileIds.value.intersect(allFileIds).nonEmpty
    val isIndeterminate = isChecked && !allFileIds.subsetOf(props.fileIds.value)
    (isChecked, isIndeterminate)
  }

  private def onToggleFolder(
    props: Props,
    rootFolderInfo: FolderInfo,
    folderInfo: FolderInfo
  )(
    newValue: Boolean
  ): Callback = {
    if (newValue) {
      props.fileIds.modState(_ ++ getFolderAllFileIds(rootFolderInfo)(folderInfo.itemId))
    } else {
      props.fileIds.modState(_ -- getFolderAllFileIds(rootFolderInfo)(folderInfo.itemId))
    }
  }

  private def onToggleFile(props: Props, fileInfo: FileInfo)(newValue: Boolean): Callback = {
    if (newValue) {
      props.fileIds.modState(_ + fileInfo.itemId)
    } else {
      props.fileIds.modState(_ - fileInfo.itemId)
    }
  }

  private def renderNonEmptyRoot(props: Props)(rootFolderInfo: FolderInfo) = {
    val (isChecked, isIndeterminate) = isFolderChecked(props, rootFolderInfo)(rootFolderInfo)
    <.div(
      tw.flex.itemsCenter.pt2.pb6.pl4,
      <.div(
        tw.flexNone.wPx20,
        ^.onClick ==> stopPropagation,
        Checkbox(
          isChecked = isChecked,
          isIndeterminate = isIndeterminate,
          onChange = onToggleFolder(
            props,
            rootFolderInfo,
            rootFolderInfo
          )
        )()
      ),
      <.div(
        tw.flexNone.pr4.textGray7,
        IconR(name = Icon.Glyph.Safe)()
      ),
      <.div(
        tw.flexFill,
        TooltipOnTruncate(
          renderTarget = <.div.withRef(_)(
            tw.textGray8.fontSemiBold.text13.leading20.truncate,
            rootFolderInfo.name
          ),
          content = rootFolderInfo.name
        )()
      )
    )
  }

  private def renderer(item: ItemType, renderItem: ItemType => VdomElement) = {
    item.fold(
      folderInfo => {
        if (folderInfo.folders.isEmpty || folderInfo.files.isEmpty) {
          FileTreeLoader.tree(
            FileManagerLocation.Folder(None, folderInfo.itemId),
            includeAllFileIds = true
          ) { fileManagerLocationInfoOpt =>
            renderItem {
              val folderInfoOpt = fileManagerLocationInfoOpt.collect { case folderInfo: FolderInfo =>
                folderInfo
              }
              Left(folderInfoOpt.getOrElse(folderInfo))
            }
          }
        } else {
          renderItem(item)
        }
      },
      fileInfo => renderItem(Right(fileInfo))
    )
  }

  private def renderItemWithCheckbox(
    rProps: Tree.NodeRenderProps[ItemType],
    isChecked: Boolean,
    isIndeterminate: Boolean = false,
    isDisabled: Boolean = false,
    disabledReason: String = "",
    onToggle: Boolean => Callback
  ) = {
    <.div(
      TooltipR(
        renderTarget = <.div(
          tw.flex.itemsCenter,
          <.div(
            tw.flexNone.wPx20,
            ^.onClick ==> stopPropagation,
            Checkbox(
              isChecked = isChecked && !isDisabled,
              isDisabled = isDisabled,
              isIndeterminate = isIndeterminate,
              onChange = onToggle
            )()
          ),
          <.div(
            tw.flex.itemsCenter,
            TagMod.when(isDisabled)(tw.opacity40),
            renderItemDescription(rProps)
          )
        ),
        renderContent = _(disabledReason),
        isDisabled = !isDisabled
      )()
    )
  }

  private def renderItemDescription(rProps: Tree.NodeRenderProps[ItemType]) = {
    val name = rProps.node.fold(_.name, _.name)
    React.Fragment(
      <.div(
        tw.pr4,
        DmsIcon(
          iconType = rProps.node.fold(
            folderInfo => DmsIcon.Folder(getChildCount(folderInfo)),
            fileInfo => DmsIcon.File(fileInfo.name)
          ),
          size = Icon.Size.Px16
        )()
      ),
      <.div(
        tw.flexFill,
        ^.maxWidth := 300.px,
        TooltipOnTruncate(
          renderTarget = <.div.withRef(_)(
            tw.textGray8.fontNormal.text13.leading20.truncate,
            name
          ),
          content = name
        )()
      )
    )
  }

  private def renderItem(props: Props, rootFolderInfo: FolderInfo)(rProps: Tree.NodeRenderProps[ItemType]) = {
    Option.when(rProps.ancestorNodes.nonEmpty) {
      rProps.node.fold(
        folderInfo => {
          val (isChecked, isIndeterminate) = isFolderChecked(props, rootFolderInfo)(folderInfo)
          <.button(
            tw.flex.itemsCenter.pt2.pb6,
            ^.onClick --> rProps.toggle,
            renderItemWithCheckbox(
              rProps,
              isChecked,
              isIndeterminate,
              isDisabled = getChildCount(folderInfo) == 0,
              disabledReason = "This folder is empty",
              onToggle = onToggleFolder(
                props,
                rootFolderInfo,
                folderInfo
              )
            )
          )
        },
        fileInfo => {
          val isChecked = props.fileIds.value.contains(fileInfo.itemId)
          <.label(
            tw.flex.itemsCenter.pt2.pb6.cursorPointer,
            renderItemWithCheckbox(
              rProps,
              isChecked,
              onToggle = onToggleFile(props, fileInfo)
            )
          )
        }
      )
    }
  }

  private def getChildCount(folderInfo: FolderInfo) = {
    folderInfo.subFolderCount + folderInfo.fileCount
  }

  private def hasChildren(item: ItemType) = {
    item.left.exists(getChildCount(_) > 0)
  }

  private val loader = Tree.Loader.Sync[ItemType](
    loadSync = item => {
      item.fold[Seq[ItemType]](
        folderInfo => {
          folderInfo.subfolders.map(Left(_)) ++ folderInfo.fileList.map(Right(_))
        },
        _ => Seq()
      )
    },
    hasChildrenOpt = Some(hasChildren)
  )

  private def isRootItem(props: Props, node: ItemType) = {
    node.left.toOption.map(_.itemId).contains(FolderId.channelSystemFolderId(props.dataRoomWorkflowId))
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
