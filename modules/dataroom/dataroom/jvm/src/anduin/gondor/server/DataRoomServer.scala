// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.gondor.server

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration

import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter
import zio.{UIO, ZIO}

import anduin.dataroom.customization.DataRoomCustomizationService
import anduin.dataroom.email.DataRoomEmailService
import anduin.environment.{EnvironmentAuthenticationIntegrationService, InheritEnvironmentMode}
import anduin.dataroom.group.DataRoomGroupService
import anduin.dataroom.homepage.DataRoomHomePageService
import anduin.dataroom.notification.DataRoomNotificationService
import anduin.dataroom.participant.DataRoomParticipantService
import anduin.dataroom.service.*
import anduin.dataroom.service.exporter.DataRoomExportService
import anduin.dataroom.validator.DataRoomValidator
import anduin.dataroom.whitelabel.DataRoomWhiteLabelService
import anduin.endpoints.DataRoomEndpoints.*
import anduin.file.GetDownloadUrlResponse
import anduin.link.CheckValidityException
import anduin.stargazer.service.dataroom.{GetUrlResponse, GetViewUrlException}
import anduin.tapir.server.EndpointServer.TapirServerService
import anduin.tapir.server.{
  AuthenticatedEndpointValidator,
  AuthenticatedValidationEndpointServer,
  EnvironmentValidationEndpointServer
}
import com.anduin.stargazer.apps.stargazer.StargazerSettings
import com.anduin.stargazer.service.GondorConfig
import com.anduin.stargazer.service.authorization.AuthorizationService
import com.anduin.stargazer.service.utils.ZIOUtils

final case class DataRoomServer(
  gondorConfig: GondorConfig,
  protected val authorizationService: AuthorizationService,
  dataRoomService: DataRoomService,
  dataRoomFileService: DataRoomFileService,
  dataRoomParticipantService: DataRoomParticipantService,
  dataRoomGroupService: DataRoomGroupService,
  dataRoomWhiteLabelService: DataRoomWhiteLabelService,
  dataRoomTermsOfAccessService: DataRoomTermsOfAccessService,
  dataRoomProtectedLinkService: DataRoomProtectedLinkService,
  dataRoomNotificationSettingsService: DataRoomNotificationService,
  dataRoomHomePageService: DataRoomHomePageService,
  dataRoomExportService: DataRoomExportService,
  dataRoomActivityService: DataRoomActivityService,
  dataRoomEmailService: DataRoomEmailService,
  dataRoomWatermarkService: DataRoomWatermarkService,
  dataRoomContactService: DataRoomContactService,
  dataRoomSearchService: DataRoomSearchService,
  dataRoomSemanticSearchService: DataRoomSemanticSearchService,
  dataRoomTrashService: DataRoomTrashService,
  dataRoomFileUploadEmailService: DataRoomFileUploadEmailService,
  dataRoomValidator: DataRoomValidator,
  dataRoomInsightsService: DataRoomInsightsService,
  dataRoomCustomizationService: DataRoomCustomizationService,
  override protected val interpreter: ArmeriaZioServerInterpreter[Any],
  environmentAuthenticationIntegrationService: EnvironmentAuthenticationIntegrationService
) extends AuthenticatedValidationEndpointServer
    with EnvironmentValidationEndpointServer {

  private val longTaskTimeOut: FiniteDuration =
    StargazerSettings.gondorConfig.backendConfig.servicesTimeout.dataRoomLongTask

  private val ApplyWatermarkTimeOut =
    StargazerSettings.gondorConfig.backendConfig.serverlessConfig.serverlessFunctions.watermarkGenerator.executionTimeout

  private val GetFileFolderActivitiesTimeout = FiniteDuration(90, TimeUnit.SECONDS)

  val services: List[TapirServerService] = List(
    validateRouteCatchError(createDataRoom, dataRoomValidator.checkEntityMember) { (params, ctx) =>
      dataRoomService.createDataRoom(
        params,
        ctx,
        inheritEnvironmentMode = InheritEnvironmentMode.FromCtx(ctx)
      )
    },
    validateRouteCatchError(
      duplicateDataRoom,
      dataRoomValidator.checkJoinedAdmin,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomService.duplicateDataRoom(
        params,
        ctx
      )
    },
    validateRouteCatchError(setIsArchivedDataRoom, dataRoomValidator.checkJoinedAdmin) { (params, ctx) =>
      dataRoomService.setIsArchivedDataRoom(
        params,
        ctx.actor,
        Some(ctx)
      )
    },
    validateRouteCatchError(
      inviteUsers,
      dataRoomValidator.checkJoinedUser && dataRoomValidator.checkDataRoomGroups,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomParticipantService.inviteUsers(
        params,
        ctx.actor,
        Some(ctx)
      )
    },
    validateRouteCatchError(acceptInvitation, dataRoomValidator.checkInvitedUser) { (params, ctx) =>
      dataRoomParticipantService.acceptInvitation(params, ctx)
    },
    validateRouteCatchError(declineInvitation, dataRoomValidator.checkInvitedUser) { (params, ctx) =>
      dataRoomParticipantService.declineInvitation(
        params,
        ctx.actor,
        Some(ctx)
      )
    },
    validateRouteCatchError(
      modifyDataRoomAssetPermissions,
      dataRoomValidator.checkJoinedUser && dataRoomValidator.checkInvitedOrJoinedMembers && dataRoomValidator.checkDataRoomGroups && dataRoomValidator.checkDataRoomAsset,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomFileService.modifyDataRoomAssetPermission(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    validateRouteCatchError(
      modifyUserPermissions,
      dataRoomValidator.checkJoinedUser && dataRoomValidator.checkInvitedOrJoinedMembers,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomParticipantService.modifyUserPermissions(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    validateRouteCatchError(
      remindInvitation,
      dataRoomValidator.checkJoinedUser && dataRoomValidator.checkInvitedOrJoinedMembers
    ) { (params, ctx) =>
      dataRoomParticipantService.remindInvitation(
        params,
        ctx.actor,
        Some(ctx)
      )
    },
    validateRouteCatchError(
      removeUsers,
      dataRoomValidator.checkJoinedUser && dataRoomValidator.checkInvitedOrJoinedMembers,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomParticipantService.removeUsers(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    validateRouteCatchError(
      archiveDataRoomAndRemoveUsers,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkInvitedOrJoinedMembers
    ) { (params, ctx) =>
      dataRoomService.archiveAndRemoveUsersUnsafe(
        params,
        ctx.actor,
        Some(ctx)
      )
    },
    validateRouteCatchError(
      manualNotification,
      dataRoomValidator.checkJoinedUser && dataRoomValidator.checkInvitedOrJoinedMembers
    ) { (params, ctx) =>
      dataRoomParticipantService.manualNotification(
        params,
        ctx.actor,
        Some(ctx)
      )
    },
    validateRouteCatchError(getSharableLinkConfig, dataRoomValidator.checkJoinedAdmin) { (params, ctx) =>
      dataRoomProtectedLinkService.getSharableLinkConfig(
        params,
        ctx.actor.userId
      )
    },
    validateRouteCatchError(createLinkInvitation, dataRoomValidator.checkJoinedAdmin) { (params, ctx) =>
      dataRoomProtectedLinkService.createDataRoomLinkInvitation(
        params,
        ctx.actor,
        Some(ctx)
      )
    },
    validateRouteCatchError(
      modifyLinkInvitation,
      dataRoomValidator.checkJoinedAdmin,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomProtectedLinkService.modifyDataRoomLinkInvitation(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    validateRouteCatchError(joinViaLinkInvitation, AuthenticatedEndpointValidator.empty) { (params, ctx) =>
      dataRoomProtectedLinkService.joinDataRoomViaLinkInvitation(params, ctx)
    },
    validateRouteCatchError(getLinkInvitationInfo, AuthenticatedEndpointValidator.empty) { (params, ctx) =>
      dataRoomProtectedLinkService.getLinkInvitationInfo(params, ctx.actor.userId)
    },
    validateRouteCatchError(deleteLinkInvitation, dataRoomValidator.checkJoinedAdmin) { (params, ctx) =>
      dataRoomProtectedLinkService.deleteLinkInvitation(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    validateRouteCatchError(trackUserVisitDataRoom, dataRoomValidator.checkJoinedUserWithoutTerms) { (params, ctx) =>
      dataRoomService.trackUserVisitDataRoomUnsafe(
        params,
        ctx.actor,
        Some(ctx)
      )
    },
    validateRouteCatchError(acceptTermsOfAccess, dataRoomValidator.checkTermsOfAccess) { (params, ctx) =>
      dataRoomTermsOfAccessService.acceptTermsOfAccess(
        params,
        ctx.actor,
        Some(ctx)
      )
    },
    validateRouteCatchError(modifyGeneralSettings, dataRoomValidator.checkJoinedAdmin) { (params, ctx) =>
      dataRoomService.modifyGeneralSettings(
        params,
        ctx
      )
    },
    validateRouteCatchError(addFolder, dataRoomValidator.checkDataRoomAsset) { (params, ctx) =>
      dataRoomFileService.addFolderUnsafe(
        params,
        ctx.actor,
        Some(ctx)
      )
    },
    validateRouteCatchError(renameFolder, dataRoomValidator.checkDataRoomAsset) { (params, ctx) =>
      dataRoomFileService.renameFolderUnsafe(params, ctx.actor)
    },
    validateRouteCatchError(renameFile, dataRoomValidator.checkDataRoomAsset) { (params, ctx) =>
      dataRoomFileService.renameFileUnsafe(params, ctx)
    },
    validateRouteCatchError(getAllFileVersions, dataRoomValidator.checkDataRoomAsset) { (params, ctx) =>
      dataRoomFileService.getAllFileVersionsUnsafe(params, ctx.actor.userId, Some(ctx))
    },
    validateRouteCatchError(
      deleteFilesAndFolders,
      dataRoomValidator.checkDataRoomAsset,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomFileService.deleteFilesAndFoldersUnsafe(params, ctx.actor.userId, Some(ctx))
    },
    validateRouteCatchError(
      restoreFiles,
      dataRoomValidator.checkCanHaveOwnPermission && dataRoomValidator.checkDataRoomAsset,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomTrashService.restoreFilesUnsafe(params, ctx)
    },
    validateRoute(
      getDownloadUrl,
      dataRoomValidator.checkDataRoomAsset,
      gondorConfig.frontendConfig.documentService.zipTaskTimeout.plus(ApplyWatermarkTimeOut)
    ) { (params, ctx) =>
      ZIOUtils.toTaskEither[GetDownloadUrlResponse, GetViewUrlException](
        dataRoomFileService
          .getDownloadUrlUnsafe(params, ctx.actor.userId, Some(ctx))
          .tapErrorCause { cause =>
            ZIO.logErrorCause("Failed to get download url, cause: ", cause)
          }
      ) {
        case ex: GetViewUrlException => ex
        case _: Throwable            => GetViewUrlException.ServerException
      }
    },
    validateRouteCatchError(
      getDownloadFileVersionUrl,
      dataRoomValidator.checkDataRoomAsset
    ) { (params, ctx) =>
      dataRoomFileService.getDownloadFileVersionUrlUnsafe(params, ctx.actor.userId, Some(ctx))
    },
    validateRoute(
      getViewUrl,
      dataRoomValidator.checkDataRoomAsset,
      timeout = ApplyWatermarkTimeOut
    ) { (params, ctx) =>
      ZIOUtils.toTaskEither[GetUrlResponse, GetViewUrlException](
        dataRoomFileService
          .getViewUrlUnsafe(params, ctx)
          .tapErrorCause { cause =>
            ZIO.logErrorCause("Failed to get view url, cause: ", cause)
          }
      ) {
        case ex: GetViewUrlException => ex
        case _: Throwable            => GetViewUrlException.ServerException
      }
    },
    validateRouteCatchError(getTermsOfAccessUrl, dataRoomValidator.checkTermsOfAccess) { (params, ctx) =>
      dataRoomFileService.getTermsOfAccessUrlUnsafe(params, ctx)
    },
    validateRouteCatchError(
      changeFolderOrder,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkDataRoomAsset
    ) { (params, ctx) =>
      dataRoomFileService.changeFolderOrderUnsafe(params, ctx)
    },
    validateRouteCatchError(copyPreCheck, dataRoomValidator.checkDataRoomAsset) { (params, ctx) =>
      dataRoomFileService.copyPreCheckUnsafe(params, ctx.actor)
    },
    validateRouteCatchError(movePreCheck, dataRoomValidator.checkDataRoomAsset) { (params, ctx) =>
      dataRoomFileService.movePreCheckUnsafe(params, ctx.actor)
    },
    validateRouteCatchError(createShortcut, dataRoomValidator.checkDataRoomAsset) { (params, ctx) =>
      dataRoomFileService.createShortcutUnsafe(
        params,
        ctx.actor,
        Some(ctx)
      )
    },
    validateRouteCatchError(extractShortcut, dataRoomValidator.checkDataRoomAsset) { (params, ctx) =>
      dataRoomFileService.extractShortcutUnsafe(params, ctx)
    },
    validateRouteCatchError(recordPageView, dataRoomValidator.checkDataRoomAsset) { (params, ctx) =>
      dataRoomFileService.recordPageViewUnsafe(params, ctx)
    },
    validateRouteCatchError(setNotificationSettings, dataRoomValidator.checkJoinedUser) { (params, ctx) =>
      dataRoomNotificationSettingsService.setDataRoomNotificationSettingsUnsafe(params, ctx.actor)
    },
    validateRouteCatchError(markDataRoomNotificationOnboardAsSeen, AuthenticatedEndpointValidator.empty) { (_, ctx) =>
      dataRoomService.markDataRoomNotificationOnboardAsSeen(ctx.actor.userId)
    },
    validateRouteCatchError(
      exportParticipants,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkInvitedOrJoinedMembers,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomExportService.exportParticipants(params, ctx)
    },
    validateRouteCatchError(
      exportPermissions,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkDataRoomAsset && dataRoomValidator.checkInvitedOrJoinedMembers && dataRoomValidator.checkDataRoomGroups,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomExportService.exportPermissionsUnsafe(params, ctx)
    },
    validateRouteCatchError(
      exportFileActivities,
      dataRoomValidator.checkJoinedAdmin,
      GetFileFolderActivitiesTimeout
    ) { (params, ctx) =>
      dataRoomExportService.exportFileActivities(params, ctx)
    },
    validateRouteCatchError(
      exportDataRoomActivities,
      dataRoomValidator.checkJoinedAdmin,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomExportService.exportDataRoomActivities(params, ctx)
    },
    validateRouteCatchError(
      setDataRoomTermsOfAccessWhitelistedUsers,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkInvitedOrJoinedMembers
    ) { (params, ctx) =>
      dataRoomTermsOfAccessService.setDataRoomTermsOfAccessWhitelistedUsersUnsafe(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    validateRouteCatchError(getDataRoomHomePage, dataRoomValidator.checkHomePage) { (params, ctx) =>
      dataRoomHomePageService.getDataRoomHomePageUnsafe(params, ctx.actor.userId)
    },
    validateRouteCatchError(toggleDataRoomHomePagePublishStatus, dataRoomValidator.checkJoinedAdmin) { (params, ctx) =>
      dataRoomHomePageService.toggleDataRoomHomePagePublishStatus(params, ctx)
    },
    validateRouteCatchError(updateDataRoomHomePage, dataRoomValidator.checkJoinedAdmin) { (params, ctx) =>
      dataRoomHomePageService.updateDataRoomHomePage(
        params,
        ctx
      )
    },
    validateRouteCatchError(sendDataRoomHomePageMessage, dataRoomValidator.checkJoinedUser) { (params, ctx) =>
      dataRoomHomePageService.sendMessage(params, ctx)
    },
    validateRoute(checkAccessGranted, AuthenticatedEndpointValidator.empty) { (params, ctx) =>
      val task = dataRoomProtectedLinkService.checkAccessGranted(params, ctx)
      ZIOUtils.toTaskEitherZIO(task)(handleCheckValidityException)
    },
    validateRouteCatchError(
      approveAccessRequests,
      dataRoomValidator.checkJoinedAdmin,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomProtectedLinkService.approveAccessRequests(params, ctx)
    },
    validateRouteCatchError(declineAccessRequests, dataRoomValidator.checkJoinedAdmin) { (params, ctx) =>
      dataRoomProtectedLinkService.declineAccessRequests(params, ctx)
    },
    validateRouteCatchError(
      getDataRoomActivityLog,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomActivityService.getDataRoomActivityLog(
        params,
        ctx.actor.userId
      )
    },
    validateRouteCatchError(
      getFileActivityLog,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight,
      GetFileFolderActivitiesTimeout
    ) { (params, ctx) =>
      dataRoomActivityService.getFileActivityLog(
        params,
        ctx.actor.userId
      )
    },
    validateRouteCatchError(getThumbnailUrl, dataRoomValidator.checkDataRoomAsset) { (params, ctx) =>
      dataRoomFileService.getThumbnailUrlUnsafe(params, ctx.actor)
    },
    validateRouteCatchError(markDataRoomRecentFilesAsSeen, dataRoomValidator.checkJoinedUser) { (params, ctx) =>
      dataRoomService.markDataRoomRecentFilesAsSeen(params, ctx)
    },
    validateRouteCatchError(getEmailTemplate, dataRoomValidator.checkJoinedUser) { (params, ctx) =>
      dataRoomEmailService.getEmailTemplate(params, ctx.actor.userId)
    },
    validateRouteCatchError(updateEmailTemplate, dataRoomValidator.checkJoinedAdmin) { (params, ctx) =>
      dataRoomEmailService.updateEmailTemplateUnsafe(params, ctx.actor.userId)
    },
    validateRouteCatchError(getDataRoomEmailSenderDetails, dataRoomValidator.checkJoinedAdmin) { (params, ctx) =>
      dataRoomEmailService.getDataRoomEmailSenderDetails(params, ctx.actor.userId)
    },
    validateRouteCatchError(updateDataRoomEmailConfigs, dataRoomValidator.checkJoinedAdmin) { (params, ctx) =>
      dataRoomEmailService.updateDataRoomEmailConfigs(params, ctx)
    },
    validateRouteCatchError(updateDataRoomWatermarkExceptionFiles, dataRoomValidator.checkJoinedAdmin) {
      (params, ctx) =>
        dataRoomWatermarkService.updateDataRoomWatermarkExceptionFilesUnsafe(params, ctx)
    },
    validateRouteCatchError(setDataRoomPointsOfContact, dataRoomValidator.checkJoinedAdmin) { (params, ctx) =>
      dataRoomContactService.setDataRoomPointsOfContact(params, ctx)
    },
    validateRouteCatchError(setViewedSearchOnboarding, dataRoomValidator.checkJoinedUser) { (params, ctx) =>
      dataRoomParticipantService.setViewedSearchOnboarding(params, ctx)
    },
    validateRouteCatchError(setViewedGroupOnboarding, dataRoomValidator.checkJoinedUser) { (params, ctx) =>
      dataRoomParticipantService.setViewedGroupOnboarding(params, ctx)
    },
    validateRouteCatchError(createGroup, dataRoomValidator.checkJoinedAdmin, longTaskTimeOut) { (params, ctx) =>
      dataRoomGroupService.createGroup(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    validateRouteCatchError(renameGroup, dataRoomValidator.checkJoinedAdmin) { (params, ctx) =>
      dataRoomGroupService.renameGroupUnsafe(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    validateRouteCatchError(
      deleteGroups,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkDataRoomGroups,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomGroupService.deleteGroups(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    validateRouteCatchError(
      addUsersToGroups,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkInvitedOrJoinedMembers && dataRoomValidator.checkDataRoomGroups,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomGroupService
        .addUsersToMultipleGroupsUnsafe(
          params,
          ctx.actor.userId,
          Some(ctx)
        )
    },
    validateRouteCatchError(
      removeUsersFromGroup,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkInvitedOrJoinedMembers,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomGroupService
        .removeUsersFromGroupUnsafe(
          params,
          ctx.actor.userId,
          Some(ctx)
        )
    },
    validateRouteCatchError(
      updateGroupPermission,
      dataRoomValidator.checkJoinedAdmin,
      longTaskTimeOut
    ) { (params, ctx) =>
      dataRoomGroupService.updateGroupPermission(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    validateRouteCatchError(fullSearch, dataRoomValidator.checkJoinedUser) { (params, ctx) =>
      dataRoomSearchService.fullSearchUnsafe(params, ctx.actor.userId)
    },
    validateRouteCatchError(quickSearch, dataRoomValidator.checkJoinedUser) { (params, ctx) =>
      dataRoomSearchService.quickSearchUnsafe(params, ctx.actor.userId)
    },
    validateRouteCatchError(semanticSearch, dataRoomValidator.checkJoinedUser) { (params, ctx) =>
      dataRoomSemanticSearchService.keywordSearchUnsafe(params, ctx.actor.userId)
    },
    validateRouteCatchError(exportReportForUploadAndAutoImportFiles, dataRoomValidator.checkJoinedAdmin) {
      (params, ctx) =>
        dataRoomExportService.exportReportForUploadAndAutoImportFiles(
          params,
          ctx
        )
    },
    validateRouteCatchError(
      exportReportForFolderMappingResults,
      dataRoomValidator.checkJoinedAdmin
    ) { (params, ctx) =>
      dataRoomExportService.exportReportForFolderMappingResults(
        params,
        ctx
      )
    },
    validateRouteCatchError(
      getFileWithUsersInsights,
      dataRoomValidator.checkDataRoomAsset && dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight
    ) { (params, _) =>
      dataRoomInsightsService.getFileWithUsersInsightsUnsafe(params)
    },
    validateRouteCatchError(
      getUserWithFilesInsights,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight && dataRoomValidator.checkRemovedOrJoinedMembers && dataRoomValidator.checkDataRoomOptionalAsset
    ) { (params, ctx) =>
      dataRoomInsightsService.getUserWithFilesInsightsUnsafe(params, ctx.actor.userId)
    },
    validateRouteCatchError(
      getGroupWithFilesInsights,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight && dataRoomValidator.checkDataRoomGroups && dataRoomValidator.checkDataRoomOptionalAsset
    ) { (params, ctx) =>
      dataRoomInsightsService.getGroupWithFilesInsightsUnsafe(params, ctx.actor.userId)
    },
    validateRouteCatchError(
      getInsightsFileTimeline,
      dataRoomValidator.checkDataRoomAsset && dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight
    ) { (params, _) =>
      dataRoomInsightsService.getInsightsFileTimelineUnsafe(params)
    },
    validateRouteCatchError(
      getInsightsUserTimeline,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight && dataRoomValidator.checkRemovedOrJoinedMembers
    ) { (params, _) =>
      dataRoomInsightsService.getInsightsUserTimelineUnsafe(params)
    },
    validateRouteCatchError(
      getInsightsGroupTimeline,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight && dataRoomValidator.checkDataRoomGroups
    ) { (params, ctx) =>
      dataRoomInsightsService.getInsightsGroupTimelineUnsafe(params, ctx.actor.userId)
    },
    validateRouteCatchError(
      getTopActiveUsers,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight
    ) { (params, _) =>
      dataRoomInsightsService.getTopActiveUsersUnsafe(params)
    },
    validateRouteCatchError(
      getTopActiveGroups,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight
    ) { (params, ctx) =>
      dataRoomInsightsService.getTopActiveGroupsUnsafe(params, ctx.actor.userId)
    },
    validateRouteCatchError(
      getTopActiveFiles,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight
    ) { (params, ctx) =>
      dataRoomInsightsService.getTopActiveFilesUnsafe(params, ctx.actor.userId)
    },
    validateRouteCatchError(
      getInsightsNewestParticipants,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight && dataRoomValidator.checkInvitedOrJoinedMembers
    ) { (params, _) =>
      dataRoomInsightsService.getInsightsNewestParticipantsUnsafe(params)
    },
    validateRouteCatchError(
      getUserSummaryInsights,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight && dataRoomValidator.checkRemovedOrJoinedMembers
    ) { (params, _) =>
      dataRoomInsightsService.getUserSummaryInsightsUnsafe(params)
    },
    validateRouteCatchError(
      getGroupSummaryInsights,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight && dataRoomValidator.checkDataRoomGroups
    ) { (params, ctx) =>
      dataRoomInsightsService.getGroupSummaryInsightsUnsafe(params, ctx.actor.userId)
    },
    validateRouteCatchError(
      getFileSummaryInsights,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight && dataRoomValidator.checkDataRoomAsset
    ) { (params, _) =>
      dataRoomInsightsService.getFileSummaryInsightsUnsafe(params)
    },
    validateRouteCatchError(
      getFilePageTotalViewTime,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight && dataRoomValidator.checkDataRoomAsset && dataRoomValidator.checkRemovedOrJoinedMembers
    ) { (params, ctx) =>
      dataRoomInsightsService.getFilePageTotalViewTimeUnsafe(params, ctx.actor.userId)
    },
    validateRouteCatchError(
      getFilePageUserViewPercentage,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight && dataRoomValidator.checkDataRoomAsset
    ) { (params, ctx) =>
      dataRoomInsightsService.getFilePageUserViewPercentageUnsafe(params, ctx.actor.userId)
    },
    validateEnvironmentRouteCatchError(
      getDataRoomUsersInsights,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight
    ) { (params, _) =>
      dataRoomInsightsService.getDataRoomUsersInsightsUnsafe(params)
    },
    validateRouteCatchError(
      getDataRoomGroupsInsights,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight
    ) { (params, ctx) =>
      dataRoomInsightsService.getDataRoomGroupsInsightsUnsafe(params, ctx.actor.userId)
    },
    validateRouteCatchError(
      getDataRoomFilesInsights,
      dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkPremiumPlanInsight
    ) { (params, ctx) =>
      dataRoomInsightsService.getDataRoomFilesInsightsUnsafe(params, ctx.actor.userId)
    },
    validateRouteCatchError(
      updateDataRoomWhiteLabel,
      dataRoomWhiteLabelService.whiteLabelValidator
    ) { (params, ctx) =>
      dataRoomWhiteLabelService.updateDataRoomWhiteLabelUnsafe(params, ctx)
    },
    validateRouteCatchError(
      getToaCustomization,
      AuthenticatedEndpointValidator.empty
    ) { (params, ctx) =>
      dataRoomCustomizationService.getTermsOfAccessCopy(params.dataRoomWorkflowId, ctx.actor.userId)
    },
    validateRouteCatchError(
      getDataRoomFileFolderPermission,
      dataRoomValidator.checkJoinedUser && dataRoomValidator.checkDataRoomAsset
    ) { (params, ctx) =>
      dataRoomFileService.getFileFolderPermissionDetail(params, ctx.actor.userId)
    },
    validateRouteCatchError(
      getDataRoomDashboardWhitelabel,
      AuthenticatedEndpointValidator.empty
    ) { (_, ctx) =>
      dataRoomService.getDataRoomDashboardLayout(ctx)
    },
    validateRouteCatchError(
      getDataRoomDashboard,
      AuthenticatedEndpointValidator.empty
    ) { (params, ctx) =>
      dataRoomService.getDataRoomDashboard(params, ctx)
    },
    validateRouteCatchError(
      getDataRoomActiveInOrg,
      AuthenticatedEndpointValidator.empty
    ) { (params, ctx) =>
      dataRoomService.getDataRoomActiveInOrg(params, ctx)
    },
    validateRoute(
      getDataRoomDetail,
      AuthenticatedEndpointValidator.empty
    ) { (params, ctx) =>
      dataRoomService.getDataRoomDetail(params, ctx).either
    },
    validateRouteCatchError(
      getDataRoomCountForOtherRegions,
      AuthenticatedEndpointValidator.empty
    ) { (_, ctx) =>
      dataRoomService.getDataRoomCountForOtherRegions(ctx.actor.userId)
    }
  )

  private def handleCheckValidityException: PartialFunction[Throwable, UIO[CheckValidityException]] = {
    case error: CheckValidityException => ZIO.succeed(error)
    case error: Throwable =>
      ZIO.logErrorCause("Cannot check link validity", error.toCause).as(CheckValidityException.ServerError)
  }

}
