// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.validator

import zio.Task

import anduin.dataroom.exception.*
import anduin.dataroom.flow.DataRoomValidateOperations
import anduin.dataroom.flow.DataRoomValidateOperations.{PlanCheck, SeatCheck}
import anduin.dataroom.homepage.DataRoomHomePageStoreOperations
import anduin.dataroom.role.*
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.model.common.user.UserId
import anduin.model.id.stage.{DataRoomTermsOfAccessId, DataRoomWorkflowId}
import anduin.orgbilling.model.plan.DataRoomPremiumFeature
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.service.entity.common.EntityPermissionVerification
import anduin.stargazer.service.dataroom.validation.*
import anduin.tapir.server.AuthenticatedEndpointValidator
import anduin.team.TeamService
import com.anduin.stargazer.service.utils.ZIOUtils

final case class DataRoomValidator(
  teamService: TeamService
) {

  private def concatUserIds(userIds: Set[UserId]): String = {
    userIds.map(_.idString).mkString(", ")
  }

  final case class DataRoomRoleCheckValidator(
    override val id: String,
    roleChecks: Set[DataRoomRoleUtils.Check]
  ) extends AuthenticatedEndpointValidator[DataRoomRoleCheckParams](id) {

    override def validate(params: DataRoomRoleCheckParams, userId: UserId): Task[Unit] = {
      FDBRecordDatabase
        .transact(DataRoomValidateOperations.Production) { validateOps =>
          validateOps.validateAndGetCurrentState(
            params.dataRoomWorkflowId,
            userId,
            roleChecks,
            checkArchivedStatus = params.checkArchivedStatus
          )
        }
        .unit
    }

  }

  object DataRoomEntityMemberCheckValidator
      extends AuthenticatedEndpointValidator[DataRoomEntityMemberCheckParams]("dataroom.checkEntityMember") {

    override def validate(params: DataRoomEntityMemberCheckParams, userId: UserId) = {
      EntityPermissionVerification.verifyEntityMember(
        userId,
        params.entityId,
        Some(this.getClass.getSimpleName)
      )
    }

  }

  object DataRoomGroupCheckValidator
      extends AuthenticatedEndpointValidator[DataRoomGroupCheckParams]("dataroom.checkDataRoomGroups") {

    override def validate(params: DataRoomGroupCheckParams, userId: UserId) = {
      val nonBelongDataRoomGroupIds = params.groupIds.filterNot(_.parent == params.dataRoomWorkflowId)
      ZIOUtils.validate(nonBelongDataRoomGroupIds.isEmpty) {
        DataRoomGroupNotFoundException(
          dataRoomWorkflowId = params.dataRoomWorkflowId,
          dataRoomGroupIds = nonBelongDataRoomGroupIds
        )
      }
    }

  }

  object DataRoomInvitedUserCheckValidator
      extends AuthenticatedEndpointValidator[DataRoomInvitedUserCheckParams]("dataroom.checkInvitedUser") {

    override def validate(params: DataRoomInvitedUserCheckParams, userId: UserId) = {
      FDBRecordDatabase
        .transact(DataRoomValidateOperations.Production) { validateOps =>
          validateOps.validateAndGetCurrentState(
            params.dataRoomWorkflowId,
            userId,
            Set(),
            checkJoined = DataRoomValidateOperations.MemberStateCheck.RequireInvitedOnly,
            checkTermsAccepted = false,
            checkTotalSeats = SeatCheck.Require(
              userType = SeatCheck.UserType.JoinedUsersWith(Set(userId)),
              modification = None
            )
          )
        }
        .unit
    }

  }

  final case class DataRoomJoinedUserCheckValidator(override val id: String, checkTermsAccepted: Boolean)
      extends AuthenticatedEndpointValidator[DataRoomJoinedUserCheckParams](id) {

    override def validate(params: DataRoomJoinedUserCheckParams, userId: UserId) = {
      FDBRecordDatabase
        .transact(DataRoomValidateOperations.Production)(
          _.validateAndGetCurrentState(
            params.dataRoomWorkflowId,
            userId,
            roleChecks = Set(),
            checkTermsAccepted = checkTermsAccepted
          )
        )
        .unit
    }

  }

  object DataRoomInvitedOrJoinedMembersCheckValidator
      extends AuthenticatedEndpointValidator[DataRoomInvitedOrJoinedMembersCheckParams](
        "dataroom.checkInvitedOrJoinedMembers"
      ) {

    override def validate(params: DataRoomInvitedOrJoinedMembersCheckParams, userId: UserId) = {
      for {
        (dataRoomTeamId, _) <- FDBRecordDatabase.transact(DataRoomValidateOperations.Production) { validateOps =>
          for {
            (dataRoomTeamId, dataRoomState) <- validateOps.validateAndGetCurrentState(
              params.dataRoomWorkflowId,
              userId,
              roleChecks = Set()
            )
          } yield (dataRoomTeamId, dataRoomState)
        }
        invitedAndJoinedMembers <- teamService.getInvitedAndJoinedMembers(dataRoomTeamId)
        _ <- ZIOUtils.validate(params.userIds.subsetOf(invitedAndJoinedMembers)) {
          DataRoomUserNotFoundException(
            userIds = params.userIds.diff(invitedAndJoinedMembers),
            dataRoomWorkflowId = params.dataRoomWorkflowId
          )
        }
      } yield ()
    }

  }

  object DataRoomAssetCheckValidator
      extends AuthenticatedEndpointValidator[DataRoomAssetCheckParams]("dataroom.checkAsset") {

    override def validate(params: DataRoomAssetCheckParams, userId: UserId) = {
      for {
        dataRoomId <- validateAndGetDataRoomWorkflowId(params)
        userCheckParams = new DataRoomJoinedUserCheckParams {
          override def dataRoomWorkflowId = dataRoomId
        }
        _ <- checkJoinedUser.validate(userCheckParams, userId)
      } yield ()
    }

  }

  object DataRoomOptionalAssetCheckValidator
      extends AuthenticatedEndpointValidator[DataRoomAssetCheckParams](
        "dataroom.checkOptionalAsset"
      ) {

    override def validate(params: DataRoomAssetCheckParams, userId: UserId): Task[Unit] = {
      val nonEmptyAsset = params.fileIds.nonEmpty || params.folderIds.nonEmpty
      ZIOUtils.when(nonEmptyAsset)(
        checkDataRoomAsset.validate(params, userId)
      )
    }

  }

  object DataRoomTermsOfAccessCheckValidator
      extends AuthenticatedEndpointValidator[DataRoomTermsOfAccessCheckParams]("dataroom.checkTermsOfAccess") {

    override def validate(params: DataRoomTermsOfAccessCheckParams, userId: UserId) = {
      for {
        termsOfAccessId <- ZIOUtils.optionToTask(
          params.toaFileId.channel match {
            case dataRoomTermsOfAccessId: DataRoomTermsOfAccessId => Some(dataRoomTermsOfAccessId)
            case _                                                => None
          },
          new RuntimeException(s"File ${params.toaFileId}'s channel is not DataRoomTermsOfAccessId")
        )
        (_, dataRoomCreatedState) <- FDBRecordDatabase.transact(DataRoomValidateOperations.Production) {
          _.validateAndGetCurrentState(
            dataRoomWorkflowId = termsOfAccessId.parent,
            userId = userId,
            roleChecks = Set(),
            checkJoined = params.linkIdOpt.fold(
              DataRoomValidateOperations.MemberStateCheck.RequireInvitedOrJoined
            )(_ => DataRoomValidateOperations.MemberStateCheck.NoRequirement),
            checkTermsAccepted = false
          )
        }
        _ <- ZIOUtils.validate(
          dataRoomCreatedState.termsOfAccessOptions.exists(_.versions.contains(params.toaFileId))
        ) {
          new RuntimeException(
            s"File ${params.toaFileId} is not terms of access for data room ${termsOfAccessId.parent}"
          )
        }
        _ <- ZIOUtils.traverseOptionUnit(params.linkIdOpt) { linkId =>
          ZIOUtils.validate(dataRoomCreatedState.linkInvitationMap.get(linkId).exists(!_.isToaWhitelisted)) {
            new RuntimeException(
              s"Terms of access file ${params.toaFileId} is not accessible via link ${params.linkIdOpt}"
            )
          }
        }
      } yield ()
    }

  }

  object DataRoomHomePageCheckValidator
      extends AuthenticatedEndpointValidator[DataRoomHomePageCheckParams]("dataroom.checkHomePage") {

    override def validate(params: DataRoomHomePageCheckParams, userId: UserId) = {
      FDBRecordDatabase.transact(
        FDBOperations[(DataRoomValidateOperations, DataRoomHomePageStoreOperations)].Production
      ) { case (validateOps, homePageOps) =>
        for {
          (_, dataRoomState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId,
            roleChecks = Set()
          )
          dataRoomHomePageModel <- homePageOps.get(params.dataRoomWorkflowId)
          isAdmin <- validateOps.roleOps
            .getParticipantRoleOpt(params.dataRoomWorkflowId, userId)
            .map(_.exists(DataRoomRoleUtils.isAdmin))
          _ <- RecordIO.validate(dataRoomState.showHomePage && (isAdmin || dataRoomHomePageModel.published)) {
            new RuntimeException("Non-admin user can't get data room home page when it's unpublished")
          }
        } yield ()
      }
    }

  }

  final case class DataRoomPremiumPlanCheckValidator(
    override val id: String,
    planCheck: PlanCheck
  ) extends AuthenticatedEndpointValidator[DataRoomPremiumPlanCheckParams](id) {

    override def validate(params: DataRoomPremiumPlanCheckParams, userId: UserId) = {
      for {
        _ <- FDBRecordDatabase
          .transact(DataRoomValidateOperations.Production) { validateOps =>
            validateOps.validateAndGetCurrentState(
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              userId = userId,
              roleChecks = Set(),
              checkValidPlanWithFeatures = planCheck
            )
          }
          .unit
      } yield ()
    }

  }

  object DataRoomRemovedOrJoinedMembersCheckValidator
      extends AuthenticatedEndpointValidator[DataRoomInvitedOrJoinedMembersCheckParams](
        "dataroom.checkRemovedOrJoinedMembers"
      ) {

    override def validate(params: DataRoomInvitedOrJoinedMembersCheckParams, userId: UserId) = {
      for {
        (dataRoomTeamId, _) <- FDBRecordDatabase.transact(DataRoomValidateOperations.Production) { validateOps =>
          for {
            (dataRoomTeamId, dataRoomState) <- validateOps.validateAndGetCurrentState(
              params.dataRoomWorkflowId,
              userId,
              roleChecks = Set()
            )
          } yield (dataRoomTeamId, dataRoomState)
        }
        allMembers <- teamService.getAllMembers(dataRoomTeamId)
        _ <- ZIOUtils.validate(params.userIds.subsetOf(allMembers)) {
          new RuntimeException(
            s"Users ${concatUserIds(params.userIds.diff(allMembers))} are not member or used to be a member of data room ${params.dataRoomWorkflowId.idString}"
          )
        }
      } yield ()
    }

  }

  private def validateAndGetDataRoomWorkflowId(params: DataRoomAssetCheckParams): Task[DataRoomWorkflowId] = {
    val fileChannels = params.fileIds.map { fileId =>
      fileId.channel match {
        case dataRoomWorkflowId: DataRoomWorkflowId => Some(dataRoomWorkflowId)
        case _                                      => None
      }
    }
    val folderChannels = params.folderIds.map { folderId =>
      folderId.channel match {
        case dataRoomWorkflowId: DataRoomWorkflowId => Some(dataRoomWorkflowId)
        case _                                      => None
      }
    }
    for {
      dataRoomIdOpt <- ZIOUtils.uniqueSeqToTask(
        fileChannels ++ folderChannels,
        DataRoomAssetException(
          fileIds = params.fileIds,
          folderIds = params.folderIds,
          assetDataRoomIdOpt = params.assetDataRoomIdOpt,
          message = s"${fileChannels ++ folderChannels}'s channels are not unique"
        )
      )
      dataRoomId <- ZIOUtils.fromOption(
        dataRoomIdOpt,
        DataRoomAssetException(
          fileIds = params.fileIds,
          folderIds = params.folderIds,
          assetDataRoomIdOpt = params.assetDataRoomIdOpt,
          message = s"${fileChannels ++ folderChannels}'s channel is not DataRoomWorkflowId"
        )
      )
      _ <- ZIOUtils.traverseOption(params.assetDataRoomIdOpt) { assetDataRoomId =>
        ZIOUtils.validate(dataRoomId == assetDataRoomId)(
          DataRoomAssetException(
            fileIds = params.fileIds,
            folderIds = params.folderIds,
            assetDataRoomIdOpt = params.assetDataRoomIdOpt,
            message = s"Asset ${params.fileIds ++ params.folderIds} do not belong to $assetDataRoomId"
          )
        )
      }
    } yield dataRoomId
  }

  lazy val checkJoinedAdmin = DataRoomRoleCheckValidator("dataroom.checkJoinedAdmin", Set(DataRoomRoleUtils.isAdmin))

  lazy val checkCanHaveOwnPermission = DataRoomRoleCheckValidator(
    "dataroom.checkCanHaveOwnPermission",
    Set(DataRoomRoleUtils.canHavePermission(FileFolderPermission.Own))
  )

  lazy val checkInvitedUser = DataRoomInvitedUserCheckValidator

  lazy val checkJoinedUser =
    DataRoomJoinedUserCheckValidator("dataroom.checkJoinedUser", checkTermsAccepted = true)

  lazy val checkJoinedUserWithoutTerms =
    DataRoomJoinedUserCheckValidator("dataroom.checkJoinedUserWithoutTerms", checkTermsAccepted = false)

  lazy val checkInvitedOrJoinedMembers = DataRoomInvitedOrJoinedMembersCheckValidator
  lazy val checkRemovedOrJoinedMembers = DataRoomRemovedOrJoinedMembersCheckValidator
  lazy val checkEntityMember = DataRoomEntityMemberCheckValidator
  lazy val checkDataRoomGroups = DataRoomGroupCheckValidator
  lazy val checkDataRoomAsset = DataRoomAssetCheckValidator
  lazy val checkDataRoomOptionalAsset = DataRoomOptionalAssetCheckValidator
  lazy val checkTermsOfAccess = DataRoomTermsOfAccessCheckValidator
  lazy val checkHomePage = DataRoomHomePageCheckValidator

  lazy val checkPremiumPlanInsight = DataRoomPremiumPlanCheckValidator(
    "dataroom.checkPremiumPlanInsight",
    PlanCheck.RequirePlan(Set(DataRoomPremiumFeature.Insights))
  )

  lazy val checkPremiumPlanHomePage = DataRoomPremiumPlanCheckValidator(
    "dataroom.checkPremiumPlanHomePage",
    PlanCheck.RequirePlan(Set(DataRoomPremiumFeature.HomePage))
  )

  lazy val checkPremiumPlanWhiteLabel = DataRoomPremiumPlanCheckValidator(
    "dataroom.checkPremiumPlanWhiteLabel",
    PlanCheck.RequirePlan(Set(DataRoomPremiumFeature.Whitelabel))
  )

}
