// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service.exporter

import java.time.Instant

import io.circe.Codec

import anduin.circe.generic.semiauto.deriveEnumCodec
import anduin.dataroom.group.state.DataRoomGroupCreatedSharedFlowState
import anduin.dataroom.participant.DataRoomParticipant
import anduin.dataroom.service.exporter.ExportFileActivitiesGenerator.*
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.{FileId, FolderId}
import anduin.stargazer.service.dataroom.{DataRoomFileFolderActivityInfo, FileInfo, FolderBasicInfo}

private[exporter] final case class ExportFileActivitiesGenerator(
  userInfoMap: Map[UserId, UserInfo],
  fileInfoMap: Map[FileId, FileInfo],
  folderInfoMap: Map[FolderId, FolderBasicInfo],
  timezoneOffsetInMinutes: Int,
  groupMap: Map[DataRoomGroupId, DataRoomGroupCreatedSharedFlowState],
  participantMap: Map[UserId, DataRoomParticipant],
  activities: Seq[DataRoomFileFolderActivityInfo]
) extends ExportGenerator[DataRoomFileFolderActivityInfo, Field] {

  protected val keys: List[DataRoomFileFolderActivityInfo] = {
    activities
      .sortBy(_.timestamp)(
        using Ordering[Option[Instant]].reverse
      )
      .toList
  }

  protected val fields: List[Field] = Field.values.toList

  protected def getRow(activity: DataRoomFileFolderActivityInfo): Extract = {
    case Field.Timestamp =>
      List(DataRoomExportUtils.convertTimestamp(activity.timestamp, timezoneOffsetInMinutes))
    case Field.ActivityType   => List(activity.activityName)
    case Field.FileName       => List(getFileName(activity, fileInfoMap, folderInfoMap))
    case Field.FileId         => List(getFileId(activity))
    case Field.OriginalFileId => List(getOriginalFileId(activity))
    case Field.VersionIndex   => List(getVersionIndex(activity))
    case Field.ActorName =>
      List(userInfoMap.get(activity.actor).fold("")(_.fullNameString))
    case Field.ActorEmail =>
      List(userInfoMap.get(activity.actor).fold("")(_.emailAddressStr))
    case Field.ActorEmailDomain =>
      List(userInfoMap.get(activity.actor).flatMap(_.emailAddress).fold("")(_.domain.value))
    case Field.Group =>
      List(
        DataRoomExportUtils.getGroupNames(groupMap)(
          participantMap.get(activity.actor).map(_.groupIds).getOrElse(Set.empty)
        )
      )
    case Field.ActorIpAddress =>
      List(activity.actorIp.getOrElse(""))
  }

  protected val getHeader: Extract = { field =>
    List(Field.getName(field))
  }

}

private[exporter] object ExportFileActivitiesGenerator {

  enum Field {

    case Timestamp, ActivityType, FileName, FileId,
      VersionIndex, OriginalFileId, ActorName, Group, ActorEmail,
      ActorEmailDomain, ActorIpAddress

  }

  object Field {

    given Codec[Field] = deriveEnumCodec

    def getName(field: Field): String = {
      field match {
        case Timestamp        => "Date and time"
        case ActivityType     => "Activity type"
        case FileName         => "File name"
        case FileId           => "File ID"
        case OriginalFileId   => "Original file ID"
        case ActorName        => "Actor name"
        case Group            => "Actor group"
        case ActorEmail       => "Actor email"
        case ActorEmailDomain => "Email domain"
        case ActorIpAddress   => "User IP"
        case VersionIndex     => "Version"
      }
    }

  }

  def getFileName(
    activity: DataRoomFileFolderActivityInfo,
    fileInfoMap: Map[FileId, FileInfo],
    folderInfoMap: Map[FolderId, FolderBasicInfo]
  ): String = activity match {
    case fileActivity: DataRoomFileFolderActivityInfo.DataRoomFileActivityInfo =>
      DataRoomExportUtils.getFileName(fileInfoMap)(fileActivity.fileIdOpt)
    case folderActivity: DataRoomFileFolderActivityInfo.DataRoomFolderActivityInfo =>
      DataRoomExportUtils.getFolderName(folderInfoMap, folderActivity.folderIdOpt)
  }

  def getFileId(activity: DataRoomFileFolderActivityInfo): String =
    activity match {
      case fileActivity: DataRoomFileFolderActivityInfo.DataRoomFileActivityInfo =>
        fileActivity.fileIdOpt.fold("")(_.idString)
      case folderActivity: DataRoomFileFolderActivityInfo.DataRoomFolderActivityInfo =>
        folderActivity.folderIdOpt.fold("")(_.idString)
    }

  def getOriginalFileId(activity: DataRoomFileFolderActivityInfo): String =
    activity match {
      case fileActivity: DataRoomFileFolderActivityInfo.DataRoomFileActivityInfo =>
        fileActivity.originalFileIdOpt.fold("")(_.idString)
      case _ => ""
    }

  def getVersionIndex(activity: DataRoomFileFolderActivityInfo): String =
    activity match {
      case fileActivity: DataRoomFileFolderActivityInfo.DataRoomFileActivityInfo =>
        fileActivity.versionIndexOpt.map(versionIndex => s"${versionIndex + 1}").getOrElse("")
      case _ => ""
    }

}
