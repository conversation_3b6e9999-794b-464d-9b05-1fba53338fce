// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service

import java.time.Instant

import anduin.dataroom.flow.{DataRoomEventStoreOperations, DataRoomValidateOperations}
import anduin.dataroom.group.DataRoomGroupEventStoreOperations
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.dataroom.tracking.{DataRoomActivityType, QueryUserRange}
import anduin.dms.DmsFeature
import anduin.dms.service.{DmsActivityService, FileService}
import anduin.fdb.record.model.{RecordIO, RecordTask}
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import io.github.arainko.ducktape.*

import anduin.dataroom.activity.*
import anduin.dataroom.event.*
import anduin.dataroom.group.event.*
import anduin.stargazer.service.dataroom.*
import zio.{Task, ZIO}

import anduin.dataroom.flow.DataRoomValidateOperations.PlanCheck
import anduin.dataroom.service.dmsmetadata.ModifyPermissionSourceOfEventMetaData
import anduin.dms.activity.{
  CreateFileActivity,
  FileActivityType,
  FolderFlowActivity,
  ModifyPermissionsActivity,
  RenameFileActivity
}
import anduin.model.id.FileId
import anduin.orgbilling.model.plan.DataRoomPremiumFeature

final class DataRoomActivityService(
  dmsActivityService: DmsActivityService,
  fileService: FileService
) {

  /** Convert DataRoomFlowEvent (JVM only) to DataRoomActivity (shared between JS and JVM for activity log)
    *
    * If you add new flow event, please follow these steps to make sure new event is handled properly:
    *
    *   1. Add corresponding activity in `DataRoomActivity.scala`
    *   2. Add flow event conversion here
    *   3. Activities will be shown in both activity log (front end) and exported csv, so if you don't want to surface
    *      new activity to user, consider adding it in below `isValidActivity`
    *
    * Below steps are applied for surfaced new activity:
    *
    *   4. Add activity graphql schema in `DataRoomActivitySchema.scala`
    *   5. Add activity graphql fragment in `DataRoomActivitiesInsight.graphql`
    *   6. Add an dropdown option in `DataRoomActivityType` and `DataRoomActivityTypeDropdown`
    *   7. Add activity's description in activity log (`DataRoomActivityTab`) and export file
    *      (`ExportDataRoomActivitiesGenerator`)
    *
    * Please correct this comment if any steps are missing
    * @param event
    *   target flow event
    * @return
    *   corresponding activity
    */
  def convertDataRoomEventToActivity(event: DataRoomFlowEvent): DataRoomActivity = {
    event match {
      case e: CreateDataRoom                  => e.to[CreateDataRoomActivity]
      case e: RenameDataRoom                  => e.to[RenameDataRoomActivity]
      case e: SetIsArchivedDataRoom           => e.to[SetIsArchivedDataRoomActivity]
      case e: AcceptInvitationToDataRoom      => e.to[AcceptInvitationToDataRoomActivity]
      case e: DeclineInvitationToDataRoom     => e.to[DeclineInvitationToDataRoomActivity]
      case e: CancelInvitationToDataRoom      => e.to[CancelInvitationToDataRoomActivity]
      case e: RemindInvitationToDataRoom      => e.to[RemindInvitationToDataRoomActivity]
      case e: RemoveUsersFromDataRoom         => e.to[RemoveUsersFromDataRoomActivity]
      case e: UpdateTermsOfAccessToDataRoom   => e.to[UpdateTermsOfAccessToDataRoomActivity]
      case e: UpdateDataRoomWatermark         => e.to[UpdateDataRoomWatermarkActivity]
      case e: JoinViaLinkInvitation           => e.to[JoinViaLinkInvitationActivity]
      case e: DeleteLinkInvitation            => e.to[DeleteLinkInvitationActivity]
      case e: ChangeSingleDataRoomBillingPlan => e.to[ChangeSingleDataRoomBillingPlanActivity]
      case e: ChangeShowingIndexSetting       => e.to[ChangeShowingIndexSettingActivity]
      case e: ChangeShowingSearchSetting      => e.to[ChangeShowingSearchSettingActivity]
      case e: ChangeShowingHomePageSetting    => e.to[ChangeShowingHomePageSettingActivity]
      case e: ChangeShowingWhiteLabelSetting  => e.to[ChangeShowingWhiteLabelSettingActivity]
      case e: RequestAccessDataRoom           => e.to[RequestAccessDataRoomActivity]
      case e: ApproveRequestAccessDataRoom    => e.to[ApproveRequestAccessDataRoomActivity]
      case e: DeclineRequestAccessDataRoom    => e.to[DeclineRequestAccessDataRoomActivity]
      case e: DuplicateDataRoom               => e.to[DuplicateDataRoomActivity]
      case e: UpdateDataRoomEmailConfigs      => e.to[UpdateDataRoomEmailConfigsActivity]
      case e: InviteUsersToDataRoom           => e.to[InviteUsersToDataRoomActivity]
      case e: AddPortalUserToDataRoom         => e.to[AddPortalUserToDataRoomActivity]
      case e: ModifyDataRoomPermissions       => e.to[ModifyDataRoomPermissionsActivity]
      case e: SetLinkInvitation =>
        e.into[SetLinkInvitationActivity]
          .transform(Field.computed(_.linkName, _.data.map(_.name).getOrElse("")))
      case e: AcceptTermsOfAccessToDataRoom =>
        e.into[AcceptTermsOfAccessToDataRoomActivity]
          .transform(Field.renamed(_.toaFileId, _.toaFileIdOpt))
      case e: SetDataRoomTermsOfAccessWhitelistedUsers =>
        e.to[SetDataRoomTermsOfAccessWhitelistedUsersActivity]
      case e: UpdateDataRoomWatermarkExceptionFiles =>
        e.to[UpdateDataRoomWatermarkExceptionFilesActivity]
      case e: UpdateNewFileNotificationConfig =>
        e.to[UpdateNewFileNotificationConfigActivity]
      case e: SetDataRoomPointsOfContact =>
        e.to[SetDataRoomPointsOfContactActivity]
      case e: ChangeEnableWebhook =>
        e.to[ChangeEnableWebhookActivity]
      case e: BindToEnvironment =>
        e.to[BindToEnvironmentActivity]
    }
  }

  def convertGroupEventToActivity(event: DataRoomGroupFlowEvent): Option[DataRoomActivity] = {
    event.groupId.map(id =>
      event match {
        case e: CreateGroup => e.into[CreateGroupActivity].transform(Field.computed(_.groupId, _ => id))
        case e: RenameGroup => e.into[RenameGroupActivity].transform(Field.computed(_.groupId, _ => id))
        case e: UpdateGroupPermissions =>
          e.into[UpdateGroupPermissionsActivity].transform(Field.computed(_.groupId, _ => id))
        case e: DeleteGroup => e.into[DeleteGroupActivity].transform(Field.computed(_.groupId, _ => id))
        case e: AddUsersToGroup =>
          e.into[AddUsersToGroupActivity]
            .transform(
              Field.computed(_.groupId, _ => id),
              Field.computed(_.userIds, event => event.addedUsers ++ event.movedUsers)
            )
        case e: RemoveUsersFromGroup =>
          e.into[RemoveUsersFromGroupActivity].transform(Field.computed(_.groupId, _ => id))
      }
    )
  }

  private def executeWithDataRoomEvent[A](task: DataRoomEventStoreOperations => RecordTask[A]): Task[A] = {
    FDBRecordDatabase.transact(
      DataRoomEventStoreOperations.Production
    ) { eventOps =>
      task(eventOps)
    }
  }

  private def executeWithGroupEvent[A](task: DataRoomGroupEventStoreOperations => RecordTask[A]): Task[A] = {
    FDBRecordDatabase.transact(
      DataRoomGroupEventStoreOperations.Production
    ) { eventOps =>
      task(eventOps)
    }
  }

  private def filterDataRoomActivitiesByTimestamp(activities: List[DataRoomActivity], fromTs: Instant, toTs: Instant) =
    activities.filter(a =>
      a.timestamp.exists { ts =>
        ts.isAfter(fromTs) && ts.isBefore(toTs)
      }
    )

  private def isValidActivity(activity: DataRoomActivity): Boolean = activity match {
    case a: ModifyDataRoomPermissionsActivity                => a.updatedRole.nonEmpty
    case a: RemindInvitationToDataRoomActivity               => a.userIds.nonEmpty
    case a: RemoveUsersFromDataRoomActivity                  => a.userIds.nonEmpty
    case a: SetDataRoomTermsOfAccessWhitelistedUsersActivity => a.whitelistedUsers.nonEmpty
    case a: UpdateNewFileNotificationConfigActivity          => a.newFileNotificationConfigOpt.nonEmpty
    case a: InviteUsersToDataRoomActivity                    => !a.isApproved
    case _: ChangeSingleDataRoomBillingPlanActivity          => false
    case _: UpdateDataRoomEmailConfigsActivity               => false
    case _: UpdateDataRoomWatermarkExceptionFilesActivity    => false
    case _: SetDataRoomPointsOfContactActivity               => false
    case _: ChangeEnableWebhookActivity                      => false
    case _: BindToEnvironmentActivity                        => false
    case _                                                   => true
  }

  private def getAllDataRoomActivities(dataRoomWorkflowId: DataRoomWorkflowId) = {
    for {
      dataRoomEvents <- executeWithDataRoomEvent(ops => ops.getAllEvents(dataRoomWorkflowId))
      groupEvents <- executeWithGroupEvent(_.getEventsByDataRoom(dataRoomWorkflowId))
    } yield {
      dataRoomEvents.map(e => convertDataRoomEventToActivity(e._2)) ++ groupEvents.flatMap(convertGroupEventToActivity)
    }
  }

  private def getDataRoomActivitiesByActor(
    dataRoomWorkflowId: DataRoomWorkflowId,
    actor: UserId
  ) = {
    for {
      dataRoomEvents <- executeWithDataRoomEvent(_.getEventsByActor(dataRoomWorkflowId, actor))
      groupEvents <- executeWithGroupEvent(_.getEventsByDataRoomAndActor(dataRoomWorkflowId, actor))
    } yield {
      dataRoomEvents.map(convertDataRoomEventToActivity) ++ groupEvents.flatMap(convertGroupEventToActivity)
    }
  }

  private def getDataRoomActivitiesByTimestamp(
    dataRoomWorkflowId: DataRoomWorkflowId,
    fromTimestamp: Instant,
    toTimestamp: Instant
  ) = {
    for {
      dataRoomEvents <- executeWithDataRoomEvent(
        _.getEventsByTimestamp(
          dataRoomWorkflowId,
          fromTimestamp,
          toTimestamp
        )
      )
      groupEvents <- executeWithGroupEvent(
        _.getEventsByDataRoomAndTimestamp(
          dataRoomWorkflowId,
          fromTimestamp,
          toTimestamp
        )
      )
    } yield {
      dataRoomEvents.map(convertDataRoomEventToActivity) ++ groupEvents.flatMap(convertGroupEventToActivity)
    }
  }

  def getDataRoomActivityLog(
    params: GetDataRoomActivityLogParams,
    actor: UserId
  ): Task[GetDataRoomActivityLogResponse] = {
    val dataRoomWorkflowId = params.dataRoomWorkflowId
    for {
      participantRoles <- FDBRecordDatabase.transact(
        DataRoomValidateOperations.Production
      ) { validateOps =>
        for {
          _ <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId,
            userId = actor,
            roleChecks = Set(DataRoomRoleUtils.isAdmin),
            checkValidPlanWithFeatures = PlanCheck.RequirePlan(Set(DataRoomPremiumFeature.Insights))
          )
          participantRoles <- validateOps.roleOps.getAllParticipantRoleMap(dataRoomWorkflowId)
        } yield participantRoles
      }
      activitiesByActorOpt <- params.activityActorId match {
        case Some(activityActor) =>
          getDataRoomActivitiesByActor(dataRoomWorkflowId, activityActor).map(Some(_))
        case _ => ZIO.succeed(None)
      }
      activitiesByTimestampOpt <- (params.fromTimestamp, params.toTimestamp) match {
        case (Some(fromTs), Some(toTs)) =>
          activitiesByActorOpt match {
            case None =>
              getDataRoomActivitiesByTimestamp(
                dataRoomWorkflowId,
                fromTs,
                toTs
              ).map(Some(_))
            case Some(activitiesByActor) =>
              zio.ZIO
                .attempt(
                  filterDataRoomActivitiesByTimestamp(
                    activitiesByActor,
                    fromTs,
                    toTs
                  )
                )
                .map(Some(_))
          }
        case _ => ZIO.succeed(activitiesByActorOpt)
      }
      allActivities <- activitiesByTimestampOpt match {
        case None                        => getAllDataRoomActivities(dataRoomWorkflowId)
        case Some(activitiesByTimestamp) => ZIO.succeed(activitiesByTimestamp)
      }
      activities = allActivities.filter(isValidActivity)
      activitiesFilteredByTypes =
        activities.filter { activity =>
          val hasAllActivities = params.activityTypes.contains(DataRoomActivityType.AllActivities)
          hasAllActivities || params.activityTypes
            .map(_.toString)
            .contains(activity.getClass.getSimpleName)
        }
      activitiesFilteredByActorRole = params.activityActorRole match {
        case Some(roleName) =>
          activitiesFilteredByTypes.filter { activity =>
            activity.actor.flatMap(participantRoles.get).exists { role =>
              DataRoomRoleUtils.getName(role) == roleName
            }
          }
        case None => activitiesFilteredByTypes
      }
      fileIds = activitiesFilteredByActorRole.flatMap {
        case a: UpdateTermsOfAccessToDataRoomActivity => a.toaFileIdOpt.fold(Seq[FileId]())(fileId => Seq(fileId))
        case a: AcceptTermsOfAccessToDataRoomActivity => Seq(a.toaFileId)
        case _                                        => Seq[FileId]()
      }
      fileMap <- fileService
        .getMultipleFileNameAndDeletedStateUnsafe(fileIds)(
          using DmsFeature.DataRoom
        )
        .map(_.transform { case (_, (fileName, _)) => fileName })
    } yield GetDataRoomActivityLogResponse(
      activities = activitiesFilteredByActorRole,
      fileMap = fileMap
    )
  }

  private def getFileFolderActivitiesFromDms(
    dataRoomWorkflowId: DataRoomWorkflowId,
    fromTimestamp: Instant,
    toTimestamp: Instant,
    activityType: FileActivityType
  ): Task[List[DataRoomFileFolderActivityInfo]] = {
    for {
      dmsFileActivities <- dmsActivityService.getChannelFileActivities(
        dataRoomWorkflowId,
        fromTimestamp,
        toTimestamp,
        activityType
      )
      dmsFolderActivities <- dmsActivityService.getChannelFolderActivities(
        dataRoomWorkflowId,
        fromTimestamp,
        toTimestamp,
        activityType
      )
    } yield {
      val dataRoomFileActivitiesInfo = dmsFileActivities
        .filter {
          case modifyPermission: ModifyPermissionsActivity =>
            val sourceOfEventOpt = ModifyPermissionSourceOfEventMetaData.fromMetadataMap(modifyPermission.metadata)
            sourceOfEventOpt.isDefined
          case _ => true
        }
        .map { activity =>
          val newFileNameOpt = activity match {
            case a: CreateFileActivity => Some(a.name)
            case a: RenameFileActivity => Some(a.name)
            case _                     => Option.empty[String]
          }
          val originalFileId = activity match {
            case createFileActivity: CreateFileActivity => createFileActivity.originalFileId
            case _                                      => None
          }
          DataRoomFileFolderActivityInfo.DataRoomFileActivityInfo(
            activity.getName,
            activity.actor,
            activity.getActorIp,
            activity.timestamp,
            activity.fileId,
            originalFileId,
            newFileNameOpt,
            activity.activityType,
            activity.versionIndexOpt,
            activity.getDescription
          )
        }
      val dataRoomFolderActivitiesInfo = dmsFolderActivities
        .filter { case modifyPermission: FolderFlowActivity.ModifyPermissionsActivity =>
          val sourceOfEventOpt = ModifyPermissionSourceOfEventMetaData.fromMetadataMap(modifyPermission.metadata)
          sourceOfEventOpt.isDefined
        }
        .map { activity =>
          DataRoomFileFolderActivityInfo.DataRoomFolderActivityInfo(
            activity.getName,
            activity.actor,
            activity.actorIp,
            activity.timestamp,
            activity.folderId,
            activity.activityType,
            activity.getDescription
          )
        }
      dataRoomFileActivitiesInfo ++ dataRoomFolderActivitiesInfo
    }
  }

  def getFileActivityLog(
    params: GetFileActivityLogParams,
    actor: UserId
  ): Task[GetFileActivityLogResponse] = {
    val (actorId, actorRole) = QueryUserRange.toUserParams(params.queryUserRange, params.queryUserId)
    val refinedTypes = if (params.activityTypes.contains(FileActivityType.AllActivities)) {
      Set(FileActivityType.AllActivities)
    } else {
      params.activityTypes
    }
    for {
      (_, createdAt, participantRoles) <- FDBRecordDatabase.transact(
        FDBOperations[(DataRoomValidateOperations, DataRoomEventStoreOperations)].Production
      ) { case (validateOps, eventOps) =>
        for {
          (_, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = actor,
            roleChecks = Set(DataRoomRoleUtils.isAdmin),
            checkValidPlanWithFeatures = PlanCheck.RequirePlan(Set(DataRoomPremiumFeature.Insights))
          )
          firstEvent <- eventOps.getFirstEvent(params.dataRoomWorkflowId)
          createdAt <- RecordIO.fromOption(
            firstEvent.timestamp,
            new RuntimeException(s"Unable to get data room ${params.dataRoomWorkflowId} creation time")
          )
          participantRoles <- validateOps.roleOps.getAllParticipantRoleMap(params.dataRoomWorkflowId)
        } yield (createdState, createdAt, participantRoles)
      }
      activities <- ZIO
        .foreach(refinedTypes.toList) { activityType =>
          getFileFolderActivitiesFromDms(
            params.dataRoomWorkflowId,
            params.fromTimestamp.getOrElse(createdAt),
            params.toTimestamp.getOrElse(Instant.now),
            activityType
          )
        }
        .map(_.flatten)
      activitiesFilteredByActor = actorId match {
        case Some(userId) => activities.filter(_.actor == userId)
        case _            => activities
      }
      activitiesFilteredByRole = actorRole match {
        case Some(roleName) =>
          activitiesFilteredByActor.filter { activity =>
            participantRoles.get(activity.actor).exists { role =>
              DataRoomRoleUtils.getName(role) == roleName
            }
          }
        case _ => activitiesFilteredByActor
      }
      (fileIdOpts, folderIdOpts) = activitiesFilteredByRole.partitionMap {
        case activity: DataRoomFileFolderActivityInfo.DataRoomFileActivityInfo =>
          Left(activity.fileIdOpt)
        case activity: DataRoomFileFolderActivityInfo.DataRoomFolderActivityInfo =>
          Right(activity.folderIdOpt)
      }
      fileIds = fileIdOpts.flatten
      folderIds = folderIdOpts.flatten
      fileMap <- fileService
        .getMultipleFileNameAndDeletedStateUnsafe(fileIds)(
          using DmsFeature.DataRoom
        )
        .map(
          _.transform { case (_, (fileName, isDeleted)) => FileInfo(fileName, isDeleted) }
        )
      folderMap <- fileService
        .getMultipleFolderNameAndDeletedStateUnsafe(folderIds)(
          using DmsFeature.DataRoom
        )
        .map(
          _.view
            .mapValues { case (folderName, isDeleted) =>
              FolderBasicInfo(folderName, isDeleted)
            }
            .toMap
        )
    } yield GetFileActivityLogResponse(
      activities = activitiesFilteredByRole,
      fileMap = fileMap,
      folderMap = folderMap
    )
  }

}
