// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service

import java.time.Instant

import io.github.arainko.ducktape.*
import zio.{IO, Task, UIO, ZIO}

import anduin.account.profile.UserProfileService
import anduin.customdomain.CustomDomainGlobalDatabaseService
import anduin.dataroom.*
import anduin.dataroom.DataRoomData.InvitedUserData
import anduin.dataroom.DataRoomFrontEndState.TermsOfAccessOptions
import anduin.dataroom.billing.DataRoomBillingOperations
import anduin.dataroom.dashboard.DataRoomDashboardData
import anduin.dataroom.email.*
import anduin.dataroom.email.generate.{DataRoomCreatedEmailGenerate, DataRoomSetIsArchivedEmailGenerate}
import anduin.dataroom.event.WatermarkMetadata
import anduin.dataroom.exception.*
import anduin.dataroom.exception.graphql.*
import anduin.dataroom.flow.*
import anduin.dataroom.flow.DataRoomValidateOperations.{MemberStateCheck, PlanCheck}
import anduin.dataroom.globaldatabase.DataRoomGlobalDatabaseService
import anduin.dataroom.homepage.{DataRoomHomePageService, DataRoomHomePageStoreOperations}
import anduin.dataroom.notification.{
  DataRoomNotificationConfigs,
  DataRoomNotificationService,
  DataRoomNotificationSettingsOperations,
  NewFileNotificationConfig
}
import anduin.dataroom.participant.{
  DataRoomParticipantOperations,
  DataRoomParticipantRoleOperations,
  DataRoomParticipantService
}
import anduin.dataroom.role.*
import anduin.dataroom.service.DataRoomServiceUtils.*
import anduin.dataroom.service.FileMoveCopyEndpoints.MoveCopyFileFolderParams
import anduin.dataroom.service.tracking.DataRoomTrackingService
import anduin.dataroom.state.DataRoomCreatedSharedFlowState
import anduin.dataroom.user.DataRoomUserTrackingService
import anduin.dataroom.validator.DataRoomValidator
import anduin.dataroom.whitelabel.{
  DataRoomAuthenticationWhiteLabelOperations,
  DataRoomWhiteLabelOperations,
  DataRoomWhiteLabelService
}
import anduin.dms.DmsFeature.DataRoom
import anduin.dms.channel.DmsChannelModelStoreOperations
import anduin.dms.file.state.FileStateStoreOperations
import anduin.dms.service.{DmsSearchService, FileService}
import anduin.documentservice.watermark.WatermarkLayout
import anduin.environment.{EnvironmentCheck, EnvironmentService, EnvironmentWhitelabelService, InheritEnvironmentMode}
import anduin.fdb.record.model.{RecordIO, RecordTask}
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.greylin.modelti.DataRoomParticipantState
import anduin.greylin.{GreylinDataService, modelti, operation}
import anduin.id.entity.EntityId
import anduin.id.offering.{GlobalOfferingId, OfferingId}
import anduin.link.{LinkGeneratorService, ProtectedLinkStoreOperations, ProtectedLinkWhitelabelValue}
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{DataRoomWorkflowIdFactory, FileId, FolderId, TransactionIdFactory}
import anduin.multiregion.{MultiRegionCode, PublicMultiRegionService}
import anduin.notification.NotificationService
import anduin.orgbilling.model.plan.{DataRoomPlan, DataRoomPremiumFeature}
import anduin.protobuf.flow.file.{FileFolderPermission, FileFolderPermissionMap}
import anduin.rag.SearchIndexState
import anduin.service.entity.EntityServiceUtils
import anduin.service.entity.common.{EntityPermissionVerification, EntityUtils}
import anduin.service.{AuthenticatedRequestContext, ServiceActor}
import anduin.stargazer.service.dataroom.*
import anduin.stargazer.service.dataroom.GetDataRoomActiveInOrgResponse.DataRoomActiveInOrg
import anduin.team.*
import anduin.team.TeamServiceParams.*
import anduin.user.CommonUserUtils
import anduin.util.FilenameUtils
import anduin.utils.ScalaUtils
import anduin.whitelabel.auth.AuthenticationWhitelabelStoreOperations
import anduin.whitelabel.dashboard.EnvironmentDashboardWhitelabelProtocols.DashboardWhitelabelData
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.orgbilling.OrgBillingService
import com.anduin.stargazer.service.orgbilling.storage.OrgBillingStoreOperations
import com.anduin.stargazer.service.utils.ZIOUtils

final case class DataRoomService(
  fileService: FileService,
  dataRoomFileMoveCopyService: DataRoomFileMoveCopyService,
  dataRoomEmailSenderService: DataRoomEmailSenderService,
  backendConfig: GondorBackendConfig,
  dataRoomLoggingService: DataRoomLoggingService,
  dataRoomSharedFlowService: DataRoomSharedFlowService,
  teamService: TeamService,
  dataRoomContactService: DataRoomContactService,
  dmsSearchService: DmsSearchService,
  dataRoomTrackingService: DataRoomTrackingService,
  dataRoomHomePageService: DataRoomHomePageService,
  dataRoomWhiteLabelService: DataRoomWhiteLabelService,
  dataRoomParticipantService: DataRoomParticipantService,
  dataRoomProtectedLinkService: DataRoomProtectedLinkService,
  dataRoomNotificationService: DataRoomNotificationService,
  notificationService: NotificationService,
  dataRoomUserTrackingService: DataRoomUserTrackingService,
  greylinDataService: GreylinDataService,
  dataRoomValidator: DataRoomValidator,
  orgBillingService: OrgBillingService,
  dataRoomGlobalDatabaseService: DataRoomGlobalDatabaseService,
  publicMultiRegionService: PublicMultiRegionService,
  customDomainGlobalDatabaseService: CustomDomainGlobalDatabaseService,
  environmentService: EnvironmentService,
  environmentWhitelabelService: EnvironmentWhitelabelService,
  dataRoomSemanticSearchService: DataRoomSemanticSearchService
)(
  using val userProfileService: UserProfileService,
  val linkGeneratorService: LinkGeneratorService,
  val dataRoomEmailService: DataRoomEmailService
) {

  given fileServiceImp: FileService = fileService
  given homePageServiceImp: DataRoomHomePageService = dataRoomHomePageService

  def createDataRoom(
    params: CreateDataRoomParams,
    ctx: AuthenticatedRequestContext,
    checkIfActorInEntity: Boolean = true,
    planOpt: Option[DataRoomPlan] = None,
    toaSettingDisabled: Boolean = false,
    isDemoDataRoom: Boolean = false,
    inheritEnvironmentMode: InheritEnvironmentMode = InheritEnvironmentMode.AnduinDefault
  ): Task[CreateDataRoomResponse] = {
    val actor = ctx.actor.userId
    for {
      _ <- ZIO.logInfo(s"Creating data room for user ${actor.idString}")
      _ <- ZIOUtils.when(checkIfActorInEntity) {
        EntityPermissionVerification.verifyEntityMember(
          actor,
          params.entityId,
          Some(this.getClass.getSimpleName)
        )
      }
      _ <- ZIOUtils.validate {
        val requiredFeatures = Seq(
          Option.when(params.tempToaFileIdOpt.nonEmpty)(DataRoomPremiumFeature.TermsOfAccess),
          Option.when(params.watermarkMetadata.nonEmpty)(DataRoomPremiumFeature.Watermark),
          Option.when(params.showHomePage)(DataRoomPremiumFeature.HomePage),
          Option.when(params.showWhiteLabel)(DataRoomPremiumFeature.Whitelabel)
        ).flatten
        FDBRecordDatabase.transact(DataRoomBillingOperations.Production) {
          _.checkEntityHavingDataRoomPremiumFeatures(
            planOpt.toRight(params.entityId),
            requiredFeatures*
          )
        }
      } {
        new RuntimeException(s"Entity ${params.entityId} only has Free Data Room Plan")
      }
      dataRoomWorkflowId = DataRoomWorkflowIdFactory.unsafeRandomId(TransactionIdFactory.unsafeRandomId)
      individualUserTeam <- teamService.createNewTeam(
        CreateNewTeamParams(
          parent = dataRoomWorkflowId.parent,
          associatedEntity = None
        )
      )
      _ <- teamService.addMember(
        AddMemberParams(
          inviter = actor,
          invitee = actor,
          teamId = individualUserTeam
        )
      )
      toaFileIdOpt <- ZIOUtils.traverseOption(params.tempToaFileIdOpt) { tempToaFileId =>
        DataRoomServiceUtils.copyTerms(
          dataRoomWorkflowId,
          actor,
          individualUserTeam,
          Seq(actor),
          tempToaFileId,
          Some(ctx)
        )
      }
      dataRoomNotificationConfigsOpt = Option.when(params.newFileNotificationConfig.isDefined) {
        DataRoomNotificationConfigs(
          newFileNotificationConfig = params.newFileNotificationConfig
        )
      }
      _ <- FDBRecordDatabase.transact(
        FDBOperations[
          ((DataRoomFlowOperations, DataRoomModelStoreOperations), DataRoomParticipantOperations)
        ].Production
      ) { case ((flowOps, modelOps), participantOps) =>
        for {
          _ <- participantOps.upsertParticipant(
            dataRoomWorkflowId,
            actor,
            _.copy(role = Admin())
          )
          _ <- modelOps.add(
            dataRoomWorkflowId,
            individualUserTeam,
            isDemoDataRoom
          )
          currentState <- flowOps.createDataRoom(
            dataRoomWorkflowId = dataRoomWorkflowId,
            actor = actor,
            params = params,
            toaFileIdOpt = toaFileIdOpt,
            packageOpt = planOpt.map(OrgBillingStoreOperations.toDataRoomPackageProto),
            toaSettingDisabled = toaSettingDisabled,
            dataRoomNotificationConfigsOpt = dataRoomNotificationConfigsOpt,
            environmentIdOpt = inheritEnvironmentMode.environmentIdOpt,
            actorIp = ctx.getClientIP
          )
          _ <- selfAcceptTermsOfAccess(
            flowOps,
            currentState,
            dataRoomWorkflowId,
            ctx,
            toaFileIdOpt
          )
          _ <- modelOps.store.toRecordIO {
            dataRoomContactService.syncContactRole(dataRoomWorkflowId, added = Set(actor))
          }
        } yield currentState
      }
      _ <-
        if (params.showWhiteLabel) {
          dataRoomWhiteLabelService.setDataRoomWhiteLabelBasedOnEntity(
            ctx,
            dataRoomWorkflowId,
            params.entityId
          )
        } else {
          dataRoomWhiteLabelService.createEmptyAuthenticationWhitelabel(dataRoomWorkflowId)
        }
      _ <- ZIOUtils.when(params.showSearch) {
        FDBRecordDatabase
          .transact(DmsChannelModelStoreOperations.Production)(
            _.upsert(
              dataRoomWorkflowId,
              _.copy(
                enabledSearch = true,
                searchIndexState = Option
                  .when(dataRoomSemanticSearchService.isEnabled)(SearchIndexState.FilenameKeywordIndexed)
                  .getOrElse(SearchIndexState.NotIndexed)
              )
            )
          )
          .unit
      }
      _ <- fileService.createSystemFolderForChannel(
        channel = dataRoomWorkflowId,
        folderName = params.name,
        creator = actor,
        permissionOpts = FileFolderPermissionMap().addUserPermissions(actor -> FileFolderPermission.Own)
      )
      authenticationWhitelabelIdOpt <- dataRoomWhiteLabelService.getAuthenticationWhitelabelId(dataRoomWorkflowId)
      _ <-
        dataRoomEmailSenderService.sendEmail(
          dataRoomWorkflowId,
          DataRoomCreatedEmailGenerate(
            dataRoomWorkflowId,
            actor,
            FilenameUtils.sanitizeFileFolderName(params.name),
            authenticationWhitelabelIdOpt
          )
        )

      // Inject data to pipeline
      expirationDate = planOpt.collect {
        case plan: DataRoomPlan.DataRoomProPlan      => plan.expDate
        case plan: DataRoomPlan.DataRoomBusinessPlan => plan.expDate
      }
      isInheritedFromOrg = planOpt.isEmpty
      _ <- greylinDataService.runUnit(
        operation.DataRoomOperations.insert(
          modelti.DataRoom(
            id = dataRoomWorkflowId,
            name = params.name,
            entityId = params.entityId,
            isArchived = false,
            expirationDate = expirationDate,
            isExpirationDateInheritedFromOrg = isInheritedFromOrg
          )
        )
      )
      _ <- greylinDataService.runUnit(
        operation.DataRoomParticipantOperations.addParticipants(
          List(
            modelti.DataRoomParticipant(
              actor,
              dataRoomWorkflowId,
              state = Some(DataRoomParticipantState.Joined)
            )
          )
        )
      )
      _ <- dataRoomLoggingService.logEventCreateDataRoom(
        dataRoomWorkflowId,
        params.name,
        params.entityId,
        actor,
        Some(ctx)
      )
      _ <- ZIOUtils.when(params.showHomePage) {
        dataRoomHomePageService.ensureInitialized(dataRoomWorkflowId, ctx)
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(dataRoomWorkflowId)
      // Best effort to add contact, the result of this task won't affect the main task
      _ <- {
        for {
          _ <- dataRoomContactService.addContactsToDataRoom(
            preferredActor = actor,
            userIds = Set(actor),
            dataRoomWorkflowId = dataRoomWorkflowId
          )
          _ <- dataRoomContactService.updateDataRoomGroupName(
            actor,
            dataRoomWorkflowId,
            params.name
          )
        } yield ()
      }.catchAllCause { err =>
        ZIO.logWarningCause(s"failed to add contact to data room space: $actor to $dataRoomWorkflowId with err = ", err)
      }
      _ <- ZIO.logInfo(s"Done creating data room ${dataRoomWorkflowId.idString} for user ${actor.idString}")
    } yield CreateDataRoomResponse(dataRoomWorkflowId)
  }

  def archiveAndRemoveUsersUnsafe(
    params: ArchiveDataRoomAndRemoveUsersParams,
    actor: ServiceActor,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[DataRoomEmptyResponse] = {
    for {
      _ <- dataRoomParticipantService.removeUsers(
        params = RemoveUsersFromDataRoomParams(
          dataRoomWorkflowId = params.dataRoomWorkflowId,
          userIds = params.removedUsers,
          doNotNotifyByEmail = true
        ),
        actor = actor.userId,
        httpContext = httpContext
      )
      _ <- setIsArchivedDataRoom(
        SetIsArchivedDataRoomParams(
          dataRoomWorkflowId = params.dataRoomWorkflowId,
          isArchived = true,
          doNotNotifyByEmail = params.doNotNotifyByEmail
        ),
        actor,
        httpContext
      )
    } yield DataRoomEmptyResponse()
  }

  def duplicateDataRoom(
    params: DuplicateDataRoomParams,
    ctx: AuthenticatedRequestContext
  ): Task[CreateDataRoomResponse] = {
    val actor = ctx.actor.userId
    val fromDataRoomWorkflowId = params.fromDataRoomWorkflowId
    val orgRootFolder = FolderId.channelSystemFolderId(fromDataRoomWorkflowId)
    for {
      _ <- ZIO.logInfo(s"Duplicating data room ${fromDataRoomWorkflowId.idString} for user ${actor.idString}")
      // 1. Check if actor is the admin of the dataroom. Only admin can duplicate data room
      (state, whiteLabelOpt, homePageOpt, notificationSetting, dataRoomPlan) <- FDBRecordDatabase.transact(
        FDBOperations[
          (
            ((DataRoomValidateOperations, DataRoomWhiteLabelOperations), DataRoomBillingOperations),
            (DataRoomHomePageStoreOperations, DataRoomNotificationSettingsOperations)
          )
        ].Production
      ) { case (((validateOps, whiteLabelOps), billingOps), (homePageOps, notifOps)) =>
        for {
          (_, drState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = fromDataRoomWorkflowId,
            userId = actor,
            roleChecks = Set(DataRoomRoleUtils.isAdmin)
          )
          whiteLabelModel <- whiteLabelOps.getDataRoomWhiteLabelOpt(fromDataRoomWorkflowId)
          homePageModel <- homePageOps.getOpt(fromDataRoomWorkflowId)
          notifSetting <- notifOps.getInvitationAcceptedEmail(
            fromDataRoomWorkflowId,
            actor,
            drState.dataRoomNotificationConfigs
          )
          dataRoomPlan <- billingOps.getPlan(drState)
        } yield (drState, whiteLabelModel, homePageModel, notifSetting, dataRoomPlan)
      }
      // 2. If admin, create a new data room
      whiteLabelPlanAvailable = dataRoomPlan.features.contains(DataRoomPremiumFeature.Whitelabel)
      homePagePlanAvailabe = dataRoomPlan.features.contains(DataRoomPremiumFeature.HomePage)
      watermarkParamOpt =
        if (dataRoomPlan.features.contains(DataRoomPremiumFeature.Watermark)) {
          getWatermarkMetadataParamsOpt(state)
        } else {
          None
        }
      tempToaFileIdOpt = state.termsOfAccessOptions
        .flatMap(_.versions.lastOption)
        .filter(_ => dataRoomPlan.features.contains(DataRoomPremiumFeature.TermsOfAccess))
      createParams = CreateDataRoomParams(
        name = params.newName,
        entityId = state.creatorEntityId,
        watermarkMetadata = watermarkParamOpt,
        tempToaFileIdOpt = tempToaFileIdOpt,
        showIndex = state.showIndex,
        showHomePage = if (homePagePlanAvailabe) state.showHomePage else false,
        showWhiteLabel = if (whiteLabelPlanAvailable) state.showWhiteLabel else false
      )
      res <- createDataRoom(
        params = createParams,
        ctx = ctx,
        toaSettingDisabled = state.termsOfAccessOptions.exists(!_.isEnabled),
        inheritEnvironmentMode = InheritEnvironmentMode.FromCtx(ctx)
      )
      toDataRoomWorkflowId = res.dataRoomWorkflowId
      // 3. Copy files and folders to new data room
      destRootFolder = FolderId.channelSystemFolderId(toDataRoomWorkflowId)
      (fileIds, folderIds) <- getFilesAndFolders(orgRootFolder, actor)
      copyRes <-
        if (fileIds.nonEmpty || folderIds.nonEmpty) {
          val copyParams = MoveCopyDataRoomFileParams(folderIds, fileIds)
          val uploadParams = UploadDataRoomFileParams(destRootFolder, None)
          val taskParams = MoveCopyFileFolderParams(
            getStorageApiParams = copyParams,
            uploadParams = uploadParams
          )
          dataRoomFileMoveCopyService
            .copyItems(taskParams, ctx)
            .tapErrorCause { err =>
              ZIO.logWarning(
                s"Failed to copy files and folders from data room ${fromDataRoomWorkflowId.idString} " +
                  s"to ${toDataRoomWorkflowId.idString}"
              )
            }
            .map(Some(_))
        } else {
          ZIO.attempt(None)
        }
      _ <- ZIOUtils.traverseOption(copyRes) { _ =>
        copyFolderOrder(
          orgRootFolder,
          destRootFolder,
          actor
        )
      }
      // 4. Copy data room settings to the new data room, including white label, homepage data and term of access,
      //    AND update the original data room flow state
      _ <- ZIOUtils.when(
        !createParams.showHomePage &&
          homePageOpt.isDefined &&
          homePagePlanAvailabe
      ) {
        // Note: In the case that showHomePage is `true`, home page has been initialized in the createDataRoom step
        //       Here we cover the case where home page setting is disabled, but there is home page data
        dataRoomHomePageService.ensureInitialized(toDataRoomWorkflowId, ctx)
      }
      newHomePageModelOpt <-
        if (homePagePlanAvailabe) {
          copyHomePageModel(
            homePageOpt,
            toDataRoomWorkflowId,
            actor,
            ctx
          )
        } else {
          ZIO.attempt(None)
        }
      _ <- FDBRecordDatabase.transact(
        FDBOperations[
          (
            (DataRoomWhiteLabelOperations, DataRoomHomePageStoreOperations),
            (DataRoomFlowOperations, DataRoomNotificationSettingsOperations)
          )
        ].Production
      ) { case ((whiteLabelOps, homePageOps), (flowOps, notifOps)) =>
        for {
          _ <- RecordIO.when(whiteLabelPlanAvailable) {
            RecordIO.traverseOptionUnit(whiteLabelOpt) { whiteLabelModel =>
              whiteLabelOps
                .upsertDataRoomWhiteLabel(toDataRoomWorkflowId)(_ =>
                  whiteLabelModel.copy(dataRoomId = toDataRoomWorkflowId)
                )
                .unit
            }
          }
          _ <- RecordIO.traverseOptionUnit(newHomePageModelOpt) { newHomePageModel =>
            homePageOps.update(toDataRoomWorkflowId, actor)(_ => newHomePageModel)
          }
          _ <- notifOps.setInvitationAcceptedEmail(
            dataRoomWorkflowId = toDataRoomWorkflowId,
            userId = actor,
            invitationAcceptedEmailNotification = notificationSetting,
            notificationConfigsOpt = state.dataRoomNotificationConfigs
          )
          _ <- flowOps
            .duplicateDataRoom(
              fromDataRoomWorkflowId = fromDataRoomWorkflowId,
              toDataRoomWorkflowId = toDataRoomWorkflowId,
              toDataRoomName = params.newName,
              actor = actor,
              actorIp = ctx.getClientIP
            )
            .unit
        } yield ()
      }
      // 5. Log the duplication event to Amplitude
      _ <- dataRoomLoggingService.logEventDuplicateDataRoom(
        fromDataRoomWorkflowId,
        toDataRoomWorkflowId,
        params.newName,
        state.creatorEntityId,
        actor,
        Some(ctx)
      )
      _ <- ZIO.logInfo(
        s"Done duplicating data room ${fromDataRoomWorkflowId.idString} " +
          s"to ${toDataRoomWorkflowId.idString} for user ${actor.idString}"
      )
    } yield res
  }

  def setIsArchivedDataRoom(
    params: SetIsArchivedDataRoomParams,
    actor: ServiceActor,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[DataRoomEmptyResponse] = {
    for {
      (teamId, createdState, participantRoles) <- execute { (flowOps, validateOps) =>
        for {
          (teamId, _) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = actor.userId,
            roleChecks = Set(DataRoomRoleUtils.isAdmin),
            checkArchivedStatus = None
          )
          createdState <- flowOps.setIsArchivedDataRoom(
            params.dataRoomWorkflowId,
            actor.userId,
            params.isArchived,
            httpContext.flatMap(ctx => ctx.getClientIP)
          )
          participantRoles <- validateOps.roleOps.getAllParticipantRoleMap(params.dataRoomWorkflowId)
        } yield (teamId, createdState, participantRoles)
      }
      _ <- greylinDataService.runUnit(
        operation.DataRoomOperations.update(
          id = params.dataRoomWorkflowId
        )(_.copy(isArchived = params.isArchived))
      )
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
      receivers <-
        if (params.doNotNotifyByEmail) {
          ZIO.attempt(Set(actor.userId))
        } else {
          teamService.getJoinedMembers(teamId)
        }
      userInfo <- userProfileService.getUserInfo(actor.userId)
      isSenderCreatorEntityMember <- EntityPermissionVerification.checkEntityMembership(
        actor.userId,
        createdState.creatorEntityId
      )
      entityNameOpt <-
        if (isSenderCreatorEntityMember) {
          EntityServiceUtils
            .execute(_.getEntityModel(createdState.creatorEntityId))
            .map(entityModel => Some(entityModel.name))
        } else {
          ZIO.attempt(None)
        }
      _ <- dataRoomEmailSenderService.sendEmail(
        params.dataRoomWorkflowId,
        DataRoomSetIsArchivedEmailGenerate(
          dataRoomWorkflowId = params.dataRoomWorkflowId,
          dataRoomName = createdState.name,
          receivers = receivers.toSeq,
          individualRoles = participantRoles,
          actor = actor.userId,
          actorName = CommonUserUtils.fullNameOrEmailString(userInfo),
          actorEmail = userInfo.emailAddressStr,
          entityName = entityNameOpt,
          isArchived = params.isArchived
        )
      )
      _ <- dataRoomLoggingService.logEventArchiveDataRoom(
        params.dataRoomWorkflowId,
        createdState.name,
        createdState.creatorEntityId,
        actor.userId,
        params.isArchived,
        httpContext
      )
    } yield DataRoomEmptyResponse()
  }

  def trackUserVisitDataRoom(
    params: TrackUserVisitDataRoomParams,
    actor: ServiceActor,
    httpContext: Option[AuthenticatedRequestContext],
    timestamp: Instant = Instant.now
  ): Task[DataRoomEmptyResponse] = {
    dataRoomValidator.checkJoinedUserWithoutTerms.validate(params, actor.userId) *>
      trackUserVisitDataRoomUnsafe(params, actor, httpContext, timestamp)
  }

  def trackUserVisitDataRoomUnsafe(
    params: TrackUserVisitDataRoomParams,
    actor: ServiceActor,
    httpContext: Option[AuthenticatedRequestContext],
    timestamp: Instant = Instant.now
  ): Task[DataRoomEmptyResponse] = {
    val actorIPAddress = httpContext.flatMap(_.getClientIP)
    dataRoomTrackingService
      .addVisitActivity(
        timestamp,
        actor.userId,
        params.dataRoomWorkflowId,
        actorIPAddress
      )
      .as(DataRoomEmptyResponse())
  }

  private def getNewToaFileIdOpt(
    params: ModifyDataRoomGeneralSettingsParams,
    ctx: AuthenticatedRequestContext
  ) = {
    params.modifyTermsOfAccessOpt.fold(ZIO.attempt(Option.empty[FileId])) { modifyType =>
      val dataRoomWorkflowId = params.dataRoomWorkflowId
      for {
        (individualUserTeam, currentState, participantRoles) <- execute { (_, validateOps) =>
          for {
            (individualUserTeam, currentStae) <- validateOps.validateAndGetCurrentState(
              dataRoomWorkflowId = dataRoomWorkflowId,
              userId = ctx.actor.userId,
              roleChecks = Set(DataRoomRoleUtils.isAdmin)
            )
            participantRoles <- validateOps.roleOps.getAllParticipantRoleMap(dataRoomWorkflowId)
          } yield (individualUserTeam, currentStae, participantRoles)
        }
        toaFileIdOpt <- modifyType match {
          case _: ModifyDataRoomTermsOfAccessTypes.Disable =>
            ZIO.attempt(None)
          case ModifyDataRoomTermsOfAccessTypes.Enable(toaFileId) =>
            ZIOUtils
              .validate(currentState.termsOfAccessOptions.exists(_.versions.contains(toaFileId))) {
                new RuntimeException(s"File $toaFileId isn't an existing $dataRoomWorkflowId terms of access")
              }
              .map(_ => Option(toaFileId))
          case ModifyDataRoomTermsOfAccessTypes.Upload(tempToaFileId) =>
            val currentAdmins = participantRoles.filter(_._2 == Admin()).keys.toSeq
            DataRoomServiceUtils
              .copyTerms(
                dataRoomWorkflowId = dataRoomWorkflowId,
                userId = ctx.actor.userId,
                individualUserTeam = individualUserTeam,
                currentAdmins = currentAdmins,
                tempToaFileId = tempToaFileId,
                ctx = Some(ctx)
              )
              .map(Some(_))
        }
      } yield toaFileIdOpt
    }
  }

  private def modifyTermsOfAccess(
    flowOps: DataRoomFlowOperations,
    currentState: DataRoomCreatedSharedFlowState,
    params: ModifyDataRoomGeneralSettingsParams,
    ctx: AuthenticatedRequestContext,
    newToaFileIdOpt: Option[FileId]
  ) = {
    for {
      _ <- flowOps.updateTermsOfAccess(
        params.dataRoomWorkflowId,
        ctx.actor.userId,
        newToaFileIdOpt,
        ctx.getClientIP
      )
      _ <- selfAcceptTermsOfAccess(
        flowOps,
        currentState,
        params.dataRoomWorkflowId,
        ctx,
        newToaFileIdOpt
      )
    } yield ()
  }

  private def modifyDataRoomWatermark(
    flowOps: DataRoomFlowOperations,
    params: ModifyDataRoomGeneralSettingsParams,
    ctx: AuthenticatedRequestContext
  ) = {
    val newWatermarkMetadata = params.watermarkMetadata.map { params =>
      val layout = params.layout match {
        case anduin.stargazer.service.dataroom.WatermarkLayout.Subtle => WatermarkLayout.Subtle
        case anduin.stargazer.service.dataroom.WatermarkLayout.Normal => WatermarkLayout.Normal
        case anduin.stargazer.service.dataroom.WatermarkLayout.Heavy  => WatermarkLayout.Heavy
      }
      WatermarkMetadata(
        params.text,
        params.color.value,
        layout,
        params.transparency.toAlpha
      )
    }
    for {
      _ <- flowOps.updateDataRoomWatermark(
        params.dataRoomWorkflowId,
        ctx.actor.userId,
        newWatermarkMetadata,
        ctx.getClientIP
      )
    } yield ()
  }

  private def modifyShowIndexSetting(
    flowOps: DataRoomFlowOperations,
    dataRoomWorkflowId: DataRoomWorkflowId,
    ctx: AuthenticatedRequestContext,
    showIndex: Boolean
  ) = {
    for {
      _ <- flowOps.updateShowIndexSetting(
        dataRoomWorkflowId,
        ctx.actor.userId,
        showIndex,
        ctx.getClientIP
      )
    } yield ()
  }

  def modifyShowSearchSetting(
    flowOps: DataRoomFlowOperations,
    dmsChannelOps: DmsChannelModelStoreOperations,
    dataRoomWorkflowId: DataRoomWorkflowId,
    ctx: AuthenticatedRequestContext,
    showSearch: Boolean
  ): RecordTask[Unit] = {
    for {
      _ <- dmsChannelOps.upsert(dataRoomWorkflowId, _.copy(enabledSearch = showSearch))
      _ <- flowOps.updateShowSearchSetting(
        dataRoomWorkflowId,
        ctx.actor.userId,
        showSearch,
        ctx.getClientIP
      )
    } yield ()
  }

  private def modifyShowHomePageSetting(
    flowOps: DataRoomFlowOperations,
    dataRoomWorkflowId: DataRoomWorkflowId,
    ctx: AuthenticatedRequestContext,
    showHomePage: Boolean
  ) = {
    for {
      _ <- flowOps.updateShowHomePageSetting(
        dataRoomWorkflowId,
        ctx.actor.userId,
        showHomePage,
        ctx.getClientIP
      )
    } yield ()
  }

  private def modifyShowWhiteLabelSetting(
    flowOps: DataRoomFlowOperations,
    drAuthWhitelabelOps: DataRoomAuthenticationWhiteLabelOperations,
    whiteLabelOps: DataRoomWhiteLabelOperations,
    authWhitelabelOps: AuthenticationWhitelabelStoreOperations,
    dataRoomWorkflowId: DataRoomWorkflowId,
    ctx: AuthenticatedRequestContext,
    showWhiteLabel: Boolean
  ) = {
    for {
      _ <- flowOps.updateShowWhiteLabelSetting(
        dataRoomWorkflowId,
        ctx.actor.userId,
        showWhiteLabel,
        ctx.getClientIP
      )
      _ <- dataRoomWhiteLabelService.updateDataRoomLinksWhiteLabel(
        flowOps.stateOps,
        drAuthWhitelabelOps,
        whiteLabelOps,
        authWhitelabelOps,
        dataRoomWorkflowId
      )
    } yield ()
  }

  private def modifyNewFileNotificationConfig(
    flowOps: DataRoomFlowOperations,
    dataRoomState: DataRoomCreatedSharedFlowState,
    newFileNotificationConfig: NewFileNotificationConfig,
    ctx: AuthenticatedRequestContext
  ) = {
    for {
      _ <- flowOps.updateNewFileNotificationConfig(
        dataRoomState.dataRoomWorkflowId,
        ctx.actor.userId,
        ctx.getClientIP,
        Some(newFileNotificationConfig)
      )
    } yield ()
  }

  private def updateAllProtectedLinkWhiteLabelHeaderTitle(
    linkOps: ProtectedLinkStoreOperations,
    dataRoomState: DataRoomCreatedSharedFlowState,
    dataRoomName: String
  ) = {
    for {
      links <- RecordIO.succeed(dataRoomState.linkInvitationMap)
      _ <- RecordIO.parTraverseN(2)(links.keys) { linkId =>
        for {
          whiteLabelOpt <- linkOps.getWhitelabelState(linkId)
          _ <- RecordIO.traverseOptionUnit(whiteLabelOpt) { whiteLabel =>
            linkOps
              .updateWhitelabel(
                linkId,
                whiteLabel.copy(
                  customValues =
                    whiteLabel.customValues.updated(ProtectedLinkWhitelabelValue.EmailHeaderTitle.value, dataRoomName)
                )
              )
              .unit
          }
        } yield ()
      }
    } yield ()
  }

  def modifyGeneralSettings(
    params: ModifyDataRoomGeneralSettingsParams,
    ctx: AuthenticatedRequestContext
  ): Task[ModifyDataRoomGeneralSettingsResponse] = {
    val dataRoomWorkflowId = params.dataRoomWorkflowId
    for {
      newToaFileIdOpt <- getNewToaFileIdOpt(
        params,
        ctx
      )
      prevState <- FDBRecordDatabase.transact(
        FDBOperations[
          (
            (DataRoomFlowOperations, DataRoomValidateOperations, DmsChannelModelStoreOperations),
            (
              (ProtectedLinkStoreOperations, DataRoomWhiteLabelOperations),
              (AuthenticationWhitelabelStoreOperations, DataRoomAuthenticationWhiteLabelOperations)
            )
          )
        ].Production
      ) {
        case (
              (flowOps, validateOps, dmsChannelOps),
              ((linkOps, whiteLabelOps), (authWhitelabelOps, drAuthWhitelabelOps))
            ) =>
          val requiredFeatures = Set(
            Option.when(params.modifyTermsOfAccessOpt.nonEmpty)(DataRoomPremiumFeature.TermsOfAccess),
            Option.when(params.isWatermarkMetadataChanged)(DataRoomPremiumFeature.Watermark),
            Option.when(params.showHomePage.nonEmpty)(DataRoomPremiumFeature.HomePage),
            Option.when(params.showWhiteLabel.nonEmpty)(DataRoomPremiumFeature.Whitelabel)
          ).flatten[DataRoomPremiumFeature]
          for {
            (_, currentState) <- validateOps.validateAndGetCurrentState(
              dataRoomWorkflowId = dataRoomWorkflowId,
              userId = ctx.actor.userId,
              roleChecks = Set(DataRoomRoleUtils.isAdmin),
              checkValidPlanWithFeatures = PlanCheck.RequirePlan(requiredFeatures)
            )
            _ <- RecordIO.traverse(params.nameOpt) { name =>
              for {
                _ <- flowOps.renameDataRoom(
                  params.dataRoomWorkflowId,
                  ctx.actor.userId,
                  name,
                  ctx.getClientIP
                )
                _ <- updateAllProtectedLinkWhiteLabelHeaderTitle(
                  linkOps,
                  currentState,
                  name
                )
              } yield ()
            }
            _ <- RecordIO.traverse(params.modifyTermsOfAccessOpt) { _ =>
              modifyTermsOfAccess(
                flowOps,
                currentState,
                params,
                ctx,
                newToaFileIdOpt
              )
            }
            _ <- RecordIO.when(params.isWatermarkMetadataChanged) {
              modifyDataRoomWatermark(
                flowOps,
                params,
                ctx
              )
            }
            _ <- RecordIO.traverseOptionUnit(params.showIndex) { showIndex =>
              modifyShowIndexSetting(
                flowOps,
                params.dataRoomWorkflowId,
                ctx,
                showIndex
              )
            }
            _ <- RecordIO.traverseOptionUnit(params.showSearch) { showSearch =>
              modifyShowSearchSetting(
                flowOps,
                dmsChannelOps,
                params.dataRoomWorkflowId,
                ctx,
                showSearch
              )
            }
            _ <- RecordIO.traverseOptionUnit(params.showHomePage) { showHomePage =>
              modifyShowHomePageSetting(
                flowOps,
                params.dataRoomWorkflowId,
                ctx,
                showHomePage
              )
            }
            _ <- RecordIO.traverseOptionUnit(params.showWhiteLabel) { showWhiteLabel =>
              modifyShowWhiteLabelSetting(
                flowOps,
                drAuthWhitelabelOps,
                whiteLabelOps,
                authWhitelabelOps,
                params.dataRoomWorkflowId,
                ctx,
                showWhiteLabel
              )
            }
            _ <- RecordIO.traverseOptionUnit(params.newFileNotificationConfig) { newFileNotificationConfig =>
              modifyNewFileNotificationConfig(
                flowOps,
                currentState,
                newFileNotificationConfig,
                ctx
              )
            }
          } yield currentState
      }
      _ <- ZIOUtils.when(params.showHomePage.getOrElse(false)) {
        dataRoomHomePageService.ensureInitialized(dataRoomWorkflowId, ctx)
      }
      // Search setting changed from off -> on, run a background task to add files/folders index entry
      _ <- ZIOUtils
        .when(params.showSearch.contains(true) && !prevState.showSearch) {
          for {
            _ <- ZIO.logInfo(s"User ${ctx.actor.userId} enabled search for data room ${params.dataRoomWorkflowId}")
            _ <- dataRoomParticipantService.setViewedSearchOnboarding(
              DataRoomSetViewedSearchOnboardingParams(params.dataRoomWorkflowId),
              ctx
            )
            _ <- dmsSearchService.startSearchIndexWorkflow(
              channel = params.dataRoomWorkflowId,
              actor = ctx.actor.userId
            )
            _ <- dataRoomSemanticSearchService.startUpdateSearchIndexWorkflow(
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              actor = ctx.actor.userId
            )
            _ <- dataRoomLoggingService.logEventEnableSearch(
              ctx.actor.userId,
              params.dataRoomWorkflowId,
              params.nameOpt.getOrElse(prevState.name),
              Some(ctx)
            )
          } yield ()
        }
      _ <- ZIOUtils.traverseOption(params.nameOpt) { name =>
        for {
          _ <- fileService.updateFolderName(
            actor = ctx.actor.userId,
            folderId = FolderId.channelSystemFolderId(params.dataRoomWorkflowId),
            newName = name
          )
          _ <-
            dataRoomContactService
              .updateDataRoomGroupName(
                ctx.actor.userId,
                params.dataRoomWorkflowId,
                name
              )
              .catchAllCause { err =>
                ZIO.logWarningCause(
                  s"failed to update contact group name actor: ${ctx.actor.userId} to ${params.dataRoomWorkflowId}",
                  err
                )
              }
          _ <- greylinDataService.runUnit(
            operation.DataRoomOperations.update(params.dataRoomWorkflowId)(_.withName(name))
          )
        } yield ()
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
    } yield ModifyDataRoomGeneralSettingsResponse(newToaFileIdOpt)
  }

  def markDataRoomRecentFilesAsSeen(
    params: MarkDataRoomRecentFilesAsSeenParams,
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomEmptyResponse] = {
    DataRoomServiceUtils
      .markDataRoomAllFilesAsSeen(
        ctx.actor.userId,
        params.dataRoomWorkflowId,
        notificationService
      )
      .map(_ => DataRoomEmptyResponse())
  }

  def checkDataRoomNotificationOnboard(
    actor: UserId
  ): Task[Boolean] = {
    dataRoomUserTrackingService
      .getUserTrackingInternal(actor)
      .map(_.seenDataRoomNotificationOnboard)
  }

  def markDataRoomNotificationOnboardAsSeen(
    actor: UserId
  ): Task[DataRoomEmptyResponse] = {
    dataRoomUserTrackingService
      .updateUserTrackingInternal(actor, _.withSeenDataRoomNotificationOnboard(true))
      .map(_ => DataRoomEmptyResponse())
  }

  def getAllDataRooms(
    actor: UserId,
    isArchived: Boolean,
    includeInvited: Boolean,
    includeToaNotAccepted: Boolean,
    includeGroup: Boolean,
    planCheck: PlanCheck,
    parallelism: Int = 8,
    environmentCheck: EnvironmentCheck
  ): Task[List[DataRoomDataTuple]] = {
    for {
      workflowIds <- dataRoomParticipantService.getParticipatingDataRooms(actor)
      dataRooms <- ZIOUtils.foreachParN(parallelism)(workflowIds) { workflowId =>
        getDataRoomState(
          actor = actor,
          dataRoomWorkflowId = workflowId,
          isArchived = isArchived,
          includeInvited = includeInvited,
          includeToaNotAccepted = includeToaNotAccepted,
          includeGroup = includeGroup,
          planCheck = planCheck,
          environmentCheck = environmentCheck
        ).either
      }
    } yield dataRooms.flatMap(_.toOption)
  }

  private def getCreatorEntity(creatorEntityId: EntityId, actor: UserId) = {
    for {
      entityModel <- EntityServiceUtils.execute(_.getEntityModel(creatorEntityId))
      isUserAMember <- EntityPermissionVerification.checkEntityMembership(actor, creatorEntityId)
    } yield CreatorEntity(
      DataRoomEntityModel(creatorEntityId, entityModel.name, entityModel.entityType),
      isUserAMember
    )
  }

  private def convertToDashboardData(dataRoomTuple: DataRoomDataTuple, actor: UserId): Task[DataRoomDashboardData] = {
    for {
      dataRoomWhiteLabel <- dataRoomWhiteLabelService.getWhiteLabelData(actor, dataRoomTuple.workflowId)
      createdState = dataRoomTuple.createdState
      creatorEntity <- getCreatorEntity(createdState.creatorEntityId, actor)
      creatorUserInfo <- userProfileService.getUserInfo(createdState.creatorUserId)
      lastUpdatedAt <- fileService.getChannelLastUpdatedTimestamp(dataRoomTuple.workflowId)
      participatingUserIds = dataRoomTuple.teamStateMap.keySet
      participatingUserInfos <- userProfileService.batchGetUserInfos(participatingUserIds)
      userInvitedOpt <- ZIOUtils.traverseOption(dataRoomTuple.teamStateMap.get(actor).collect {
        case userInvited: UserInvited => userInvited
      }) { userInvited =>
        userProfileService
          .getUserInfo(userInvited.inviter)
          .map(inviterInfo => InvitedUserData(userInvited.inviter, userInvited.invitedAt, Some(inviterInfo)))
      }
      latestState = DataRoomDashboardData.LatestState(
        createdState
          .into[DataRoomDashboardData.FrontEndState]
          .transform(
            Field.computed(
              _.isToaEnabled,
              _.termsOfAccessOptions.exists(_.isEnabled)
            ),
            Field.computed(
              _.isWatermarkEnabled,
              _.watermarkMetadata.isDefined
            ),
            Field.const(
              _.creatorUserInfo,
              creatorUserInfo
            ),
            Field.const(
              _.individualRoles,
              dataRoomTuple.participantRoles
            )
          ),
        participatingUserInfos = participatingUserInfos,
        joinedUsers = dataRoomTuple.teamStateMap.collect {
          case (userId, teamState) if ScalaUtils.isMatch[UserJoined](teamState) => userId
        }.toSet,
        userInvitedOpt = userInvitedOpt,
        creatorEntity = creatorEntity,
        createdAt = dataRoomTuple.createdAt,
        lastUpdatedAt = lastUpdatedAt,
        homePageIsPublished = dataRoomTuple.isHomePagePublished
      )
    } yield DataRoomDashboardData(
      dataRoomTuple.workflowId,
      latestState,
      dataRoomTuple.dataRoomPlan,
      DataRoomDashboardData.DataRoomWhiteLabelData(dataRoomWhiteLabel.dataRoomIconUrl)
    )
  }

  private def getPendingInvitationCount(
    actor: UserId,
    environmentCheck: EnvironmentCheck,
    parallelism: Int = 8
  ): Task[Int] = {
    for {
      dataRoomWorkflowIds <- dataRoomParticipantService.getParticipatingDataRooms(actor)
      pendingInvitations <- ZIOUtils.foreachParN(parallelism)(dataRoomWorkflowIds) { workflowId =>
        FDBRecordDatabase
          .transact(DataRoomValidateOperations.Production) { validateOps =>
            for {
              (_, _) <- validateOps.validateAndGetCurrentState(
                dataRoomWorkflowId = workflowId,
                userId = actor,
                roleChecks = Set(),
                checkJoined = MemberStateCheck.RequireInvitedOnly,
                checkTermsAccepted = false,
                checkArchivedStatus = Some(false),
                checkValidPlanWithFeatures = PlanCheck.NoRequirement,
                environmentCheck = environmentCheck
              )
            } yield ()
          }
          .either
      }
    } yield pendingInvitations.count(_.isRight)
  }

  def getDataRoomDashboardLayout(
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomDashboardWhitelabel] = {
    for {
      environmentIdOpt <- environmentService.getCurrentEnvironmentIdOpt(ctx)
      dashboardWhitelabelData <- environmentIdOpt.fold(ZIO.succeed(DashboardWhitelabelData.AnduinDefault)) {
        environmentId => dataRoomWhiteLabelService.getDashboardWhitelabelData(environmentId)
      }
    } yield DataRoomDashboardWhitelabel(environmentDashboardWhitelabel = dashboardWhitelabelData)
  }

  def getDataRoomDashboard(
    params: GetDataRoomDashboardParams,
    ctx: AuthenticatedRequestContext,
    parallelism: Int = 8
  ): Task[GetDataRoomDashboardResponse] = {
    val actor = ctx.actor.userId
    for {
      environmentIdOpt <- environmentService.getCurrentEnvironmentIdOpt(ctx)
      environmentCheck = EnvironmentCheck.from(environmentIdOpt)
      dataRoomTuples <- getAllDataRooms(
        actor,
        params.isArchived,
        params.includeInvited,
        includeToaNotAccepted = true,
        includeGroup = false,
        planCheck = PlanCheck.NoRequirement,
        environmentCheck = environmentCheck
      )
      dataRoomDashboardData <- ZIOUtils.foreachParN(parallelism)(dataRoomTuples)(convertToDashboardData(_, actor))
      pendingInvitationCount <- getPendingInvitationCount(actor, environmentCheck)
      participatedEntities <- EntityUtils.getAllEntityOfUser(actor)
      orgBillingModels <- ZIOUtils.foreachParN(parallelism)(participatedEntities.map(_.entityId)) { entityId =>
        orgBillingService.getOrgBillingModel(entityId).either.map(_.toOption)
      }
      userInfo <- userProfileService.getUserInfo(actor)

    } yield GetDataRoomDashboardResponse(
      dataRoomDashboardData,
      pendingInvitationCount,
      participatedEntities.map { entityModel =>
        DataRoomEntityModel(entityModel.entityId, entityModel.name, entityModel.entityType)
      },
      orgBillingModels.flatten.map { orgBillingModel =>
        DataRoomOrgBillingModel(orgBillingModel.id, orgBillingModel.dataRoomPlan)
      },
      DataRoomUserData.UserInfo(userInfo)
    )
  }

  def getDataRoomActiveInOrg(
    params: GetDataRoomActiveInOrgParams,
    ctx: AuthenticatedRequestContext,
    parallelism: Int = 8
  ): Task[GetDataRoomActiveInOrgResponse] = {
    val actor = ctx.actor.userId
    for {
      environmentIdOpt <- environmentService.getCurrentEnvironmentIdOpt(ctx)
      dataRoomTuples <- getAllDataRooms(
        actor,
        isArchived = false,
        includeInvited = params.includeInvited,
        includeToaNotAccepted = true,
        includeGroup = false,
        planCheck = PlanCheck.NoRequirement,
        environmentCheck = EnvironmentCheck.from(environmentIdOpt)
      )
      dataRooms <- ZIOUtils.foreachParN(parallelism)(dataRoomTuples) { dataRoom =>
        for {
          dataRoomWhiteLabel <- dataRoomWhiteLabelService.getWhiteLabelData(actor, dataRoom.workflowId)
        } yield DataRoomActiveInOrg(
          dataRoom.workflowId,
          dataRoom.createdState.name,
          dataRoom.dataRoomPlan,
          dataRoomWhiteLabel.dataRoomIconUrl,
          userInvitedOpt = dataRoom.teamStateMap.get(actor).collect { case userInvited: UserInvited =>
            InvitedUserData(userInvited.inviter, userInvited.invitedAt)
          },
          isShowingHomePage = dataRoom.isHomePagePublished && dataRoom.createdState.showHomePage
        )
      }
    } yield GetDataRoomActiveInOrgResponse(dataRooms)
  }

  private def getParticipatingUsers(
    dataRoomWorkflowId: DataRoomWorkflowId,
    teamStateMap: Map[UserId, TeamMemberFlowState]
  ) = {
    for {
      userInfoMap <- DataRoomServiceUtils.batchGetUserInfos(teamStateMap.keySet)
      userStateMap <- dataRoomParticipantService.getDataRoomParticipants(
        dataRoomWorkflowId,
        userInfoMap.keySet
      )
    } yield {
      for {
        (userId, teamState) <- teamStateMap
        userInfo <- userInfoMap.get(userId)
        userState <- userStateMap.get(userId)
      } yield userId -> DataRoomServiceUtils.convertToDataRoomUserData(userInfo, teamState, userState)
    }
  }

  private def convertToFrontEndState(dataRoomTuple: DataRoomDataTuple): Task[DataRoomFrontEndState] = {
    val createdState = dataRoomTuple.createdState
    val watermarkMetadata = createdState.watermarkMetadata.map { metadata =>
      DataRoomFrontEndState.WatermarkMetadata(
        metadata.text,
        metadata.color,
        metadata.layout match {
          case WatermarkLayout.Subtle          => anduin.stargazer.service.dataroom.WatermarkLayout.Subtle.value
          case WatermarkLayout.Normal          => anduin.stargazer.service.dataroom.WatermarkLayout.Normal.value
          case WatermarkLayout.Heavy           => anduin.stargazer.service.dataroom.WatermarkLayout.Heavy.value
          case WatermarkLayout.Unrecognized(_) => anduin.stargazer.service.dataroom.WatermarkLayout.Heavy.value
        },
        WatermarkTransparency.fromAlpha(metadata.alpha).getOrElse(WatermarkTransparency.SeventyFive).value
      )
    }
    for {
      dataRoomEmailConfigs <- dataRoomEmailService.getDataRoomEmailClientConfigs(
        createdState.creatorEntityId,
        createdState.dataRoomEmailConfigs
      )
    } yield {
      createdState
        .into[DataRoomFrontEndState]
        .transform(
          Field.computed(
            _.termsOfAccessOptions,
            _.termsOfAccessOptions
              .map(_.to[DataRoomFrontEndState.TermsOfAccessOptions])
              .getOrElse(DataRoomFrontEndState.TermsOfAccessOptions())
          ),
          Field.computed(
            _.termsOfAccessCertificates,
            _.termsOfAccessCertificates.toList.map(_.to[DataRoomFrontEndState.TermsOfAccessCertificate])
          ),
          Field.const(
            _.watermarkMetadata,
            watermarkMetadata
          ),
          Field.const(
            _.dataRoomEmailConfigs,
            dataRoomEmailConfigs
          ),
          Field.const(
            _.individualRoles,
            dataRoomTuple.participantRoles
          ),
          Field.const(
            _.isSemanticSearchEnabled,
            dataRoomTuple.createdState.showSearch && dataRoomSemanticSearchService.isEnabled
          )
        )
    }
  }

  private def convertToDataRoomData(dataRoomTuple: DataRoomDataTuple, actor: UserId): Task[DataRoomData] = {
    val dataRoomWorkflowId = dataRoomTuple.workflowId
    for {
      dataRoomWhiteLabel <- dataRoomWhiteLabelService.getWhiteLabelData(actor, dataRoomWorkflowId)
      authenticationWhiteLabel <- dataRoomWhiteLabelService.getAuthenticationWhiteLabelData(
        dataRoomWorkflowId,
        actor
      )
      rootFolderPermission <- fileService.getFolderPermission(actor)(
        FolderId.channelSystemFolderId(dataRoomWorkflowId)
      )
      participatingUsers <- getParticipatingUsers(dataRoomTuple.workflowId, dataRoomTuple.teamStateMap)
      deletedUsers <- DataRoomServiceUtils.batchGetUserInfos(dataRoomTuple.deletedUsers)
      creatorEntity <- getCreatorEntity(dataRoomTuple.createdState.creatorEntityId, actor)
      linkStateMap <- dataRoomProtectedLinkService.getLinkStateMap(dataRoomTuple.createdState.linkInvitationMap)
      notificationSettings <- dataRoomNotificationService
        .getDataRoomNotificationSettings(
          GetDataRoomNotificationSettingsParams(dataRoomWorkflowId),
          actor
        )
        .map(_.to[DataRoomData.NotificationSettings])
      hasViewedNotificationOnboarding <- checkDataRoomNotificationOnboard(actor)
      frontEndState <- convertToFrontEndState(dataRoomTuple)
      latestState = DataRoomData.LatestState(
        frontEndState,
        participatingUsers,
        deletedUsers.view.mapValues(DataRoomUserData.UserInfo.apply).toMap,
        creatorEntity,
        dataRoomTuple.createdAt,
        linkStateMap,
        notificationSettings,
        hasViewedNotificationOnboarding,
        dataRoomTuple.isHomePagePublished
      )
      dataRoomData = dataRoomTuple
        .into[DataRoomData]
        .transform(
          Field.const(_.latestState, latestState),
          Field.const(_.dataRoomWhiteLabelData, dataRoomWhiteLabel),
          Field.const(_.authenticationWhiteLabelData, authenticationWhiteLabel),
          Field.const(_.rootFolder, DataRoomData.RootFolderPermission(rootFolderPermission))
        )
    } yield dataRoomData
  }

  def getDataRoomDetail(
    params: GetDataRoomDetailParams,
    ctx: AuthenticatedRequestContext
  ): IO[DataRoomGraphqlForwardException, GetDataRoomDetailResponse] = {
    val dataRoomWorkflowId = params.dataRoomWorkflowId
    val actor = ctx.actor.userId
    val catchError: Throwable => UIO[DataRoomGraphqlForwardException] = {
      case _: DataRoomMemberStateCheckException =>
        getInvitationPendingException(dataRoomWorkflowId, actor)
      case _: DataRoomToaNotAcceptedException =>
        getToaPendingException(dataRoomWorkflowId, actor)
      case _: DataRoomArchivedException =>
        getArchivedException(dataRoomWorkflowId, actor)
      case _: DataRoomInvalidPlanException =>
        getInvalidPlanException(dataRoomWorkflowId, actor)
      case _ =>
        getNoAccessException(actor)
    }
    val task = for {
      environmentIdOpt <- environmentService.getCurrentEnvironmentIdOpt(ctx)
      dataRoomTuple <- getDataRoomState(
        actor,
        params.dataRoomWorkflowId,
        isArchived = false,
        includeInvited = false,
        includeGroup = true,
        includeToaNotAccepted = false,
        planCheck = PlanCheck.RequirePlan(Set()),
        environmentCheck = EnvironmentCheck.from(environmentIdOpt)
      )
      dataRoomData <- convertToDataRoomData(dataRoomTuple, actor)
    } yield GetDataRoomDetailResponse(dataRoomData)
    task.flatMapError(catchError)
  }

  private def getToaFileIdOpt(createdState: DataRoomCreatedSharedFlowState, actor: UserId) = {
    for {
      toaOptions <- createdState.termsOfAccessOptions
      toaFileId <- toaOptions.versions.lastOption
      isWhitelisted = toaOptions.whitelistedUsers.contains(actor)
      hasAccepted = createdState.termsOfAccessCertificates.exists { entry =>
        entry.userId == actor && entry.toaFileId == toaFileId
      }
      if toaOptions.isEnabled && !isWhitelisted && !hasAccepted
    } yield toaFileId
  }

  private def getToaFile(
    fileStateOps: FileStateStoreOperations,
    toaFileId: FileId,
    hasAcceptedBefore: Boolean
  ) = {
    for {
      fileName <- fileStateOps.getNameUnsafe(toaFileId)
    } yield AcceptancePendingAdditionalFields.Target.ToaFile(
      toaFileId,
      fileName,
      hasAcceptedBefore
    )
  }

  private def getNoAccessException(actor: UserId): UIO[DataRoomGraphqlForwardException] = {
    userProfileService.getUserInfo(actor).either.map { userInfoEither =>
      DataRoomGraphqlForwardException.noAccess(NoAccessAdditionalFields(userInfoEither.toOption))
    }
  }

  private def getInvitationPendingException(
    dataRoomWorkflowId: DataRoomWorkflowId,
    actor: UserId
  ): UIO[DataRoomGraphqlForwardException] = {
    FDBRecordDatabase
      .transact(
        FDBOperations[
          (
            ((DataRoomModelStoreOperations, DataRoomStateStoreOperations), DataRoomBillingOperations),
            ((FileStateStoreOperations, DataRoomParticipantRoleOperations), TeamServiceOperations)
          )
        ].Production
      ) { case (((modelOps, stateOps), billingOps), ((fileStateOps, participantRoleOps), teamOps)) =>
        for {
          teamId <- modelOps.getTeamId(dataRoomWorkflowId)
          teamMemberInfo <- teamOps.getTeamMemberInfo(teamId)
          participantRoles <- participantRoleOps.getAllParticipantRoleMap(dataRoomWorkflowId)
          isInvited = teamMemberInfo.invitedUsers.contains(actor)
          exception <-
            if (isInvited) {
              for {
                createdState <- stateOps.getState(dataRoomWorkflowId)
                toaFileIdOpt = getToaFileIdOpt(createdState, actor)
                hasAcceptedBefore = createdState.termsOfAccessCertificates.exists(_.userId == actor)
                toaFileOpt <- RecordIO.traverse(toaFileIdOpt) {
                  getToaFile(
                    fileStateOps,
                    _,
                    hasAcceptedBefore
                  )
                }
                joinedUsers = teamMemberInfo.joinedMembers
                newSeatCount = participantRoles.count { case (userId, role) =>
                  val isJoined = joinedUsers.contains(userId)
                  val isActor = userId == actor
                  val isInternal = DataRoomRoleUtils.isInternal(role)
                  (isJoined || isActor) && isInternal
                }
                dataRoomPlan <- billingOps.getPlan(createdState)
              } yield DataRoomGraphqlForwardException.acceptancePending(
                AcceptancePendingAdditionalFields(
                  target = AcceptancePendingAdditionalFields.Target.Invitation(
                    toaFileOpt = toaFileOpt,
                    isSeatLimitReached = newSeatCount > dataRoomPlan.totalSeats
                  ),
                  dataRoomName = createdState.name
                )
              )
            } else {
              RecordIO.fail(new RuntimeException(s"User ${actor} is not invited into data room"))
            }
        } yield exception
      }
      .orElse(getNoAccessException(actor))
  }

  private def getToaPendingException(
    dataRoomWorkflowId: DataRoomWorkflowId,
    actor: UserId
  ): UIO[DataRoomGraphqlForwardException] = {
    FDBRecordDatabase
      .transact(
        FDBOperations[(DataRoomStateStoreOperations, FileStateStoreOperations)].Production
      ) { case (stateOps, fileStateOps) =>
        for {
          createdState <- stateOps.getState(dataRoomWorkflowId)
          toaFileIdOpt = getToaFileIdOpt(createdState, actor)
          toaFileId <- RecordIO.fromOption(
            toaFileIdOpt,
            new RuntimeException("DataRoomToaNotAcceptedException is thrown while cannot find not accepted ToA")
          )
          hasAcceptedBefore = createdState.termsOfAccessCertificates.exists(_.userId == actor)
          toaFile <- getToaFile(
            fileStateOps,
            toaFileId,
            hasAcceptedBefore
          )
        } yield DataRoomGraphqlForwardException.acceptancePending(
          AcceptancePendingAdditionalFields(
            target = AcceptancePendingAdditionalFields.Target.TermsOfAccess(toaFile),
            dataRoomName = createdState.name
          )
        )
      }
      .orElse(getNoAccessException(actor))
  }

  private def getArchivedException(dataRoomWorkflowId: DataRoomWorkflowId, actor: UserId)
    : UIO[DataRoomGraphqlForwardException] = {
    val exception = for {
      (dataRoomName, creatorEntityId, actorUserId, timestamp) <- FDBRecordDatabase.transact(
        FDBOperations[(DataRoomStateStoreOperations, DataRoomEventStoreOperations)].Production
      ) { case (stateOps, eventOps) =>
        for {
          createdState <- stateOps.getState(dataRoomWorkflowId)
          lastEventOpt <- eventOps.getLastSetArchivedEvent(dataRoomWorkflowId)
          (actorUserId, timestamp) <- RecordIO.fromOption(
            lastEventOpt,
            new RuntimeException(s"Unable to extract last event of $dataRoomWorkflowId")
          )
        } yield (createdState.name, createdState.creatorEntityId, actorUserId, timestamp)

      }
      actorName <- actorUserId.fold(ZIO.attempt(""))(
        userProfileService.getUserInfo(_).map(CommonUserUtils.fullNameOrEmailString)
      )
      entityName <- EntityServiceUtils.execute(_.getEntityModel(creatorEntityId)).map(_.name)
    } yield DataRoomGraphqlForwardException.archived(
      ArchivedAdditionalFields(
        dataRoomName,
        actorName,
        timestamp,
        entityName
      )
    )
    exception.orElse(getNoAccessException(actor))
  }

  def getInvalidPlanException(dataRoomWorkflowId: DataRoomWorkflowId, actor: UserId)
    : UIO[DataRoomGraphqlForwardException] = {
    val exception = for {
      (dataRoomName, planName, isInternal) <- FDBRecordDatabase.transact(
        FDBOperations[
          ((DataRoomStateStoreOperations, DataRoomParticipantRoleOperations), DataRoomBillingOperations)
        ].Production
      ) { case ((stateOps, participantRoleOps), billingOps) =>
        for {
          createdState <- stateOps.getState(dataRoomWorkflowId)
          dataRoomPlan <- billingOps.getPlan(createdState)
          planName = dataRoomPlan match {
            case business: DataRoomPlan.DataRoomBusinessPlan if business.isTrial =>
              "trial"
            case _ =>
              s"${dataRoomPlan.name} plan"
          }
          isInternal <- participantRoleOps
            .getParticipantRoleOpt(
              dataRoomWorkflowId,
              actor
            )
            .map(_.fold(false)(DataRoomRoleUtils.isInternal))
        } yield (createdState.name, planName, isInternal)
      }
    } yield DataRoomGraphqlForwardException.invalidPlan(
      InvalidPlanAdditionalFields(
        dataRoomName,
        planName,
        isInternal
      )
    )
    exception.orElse(getNoAccessException(actor))
  }

  def getDataRoomCountForOtherRegions(actor: UserId): Task[GetDataRoomCountForOtherRegionsResponse] = {
    for {
      regionalDataRoom <- dataRoomGlobalDatabaseService
        .getDataRoomPerRegionForUser(actor)
      regionInfos = regionalDataRoom.flatMap { regionInfo =>
        Option.when(
          regionInfo.region != publicMultiRegionService.currentRegion && regionInfo.region != MultiRegionCode.Unrecognized
        )(
          DataRoomCountForRegion(
            region = regionInfo.region,
            state = regionInfo.state,
            count = regionInfo.dataRoomCount
          )
        )
      }
      links <- customDomainGlobalDatabaseService.getOfferingLinksForAllRegion(
        GlobalOfferingId.fromOfferingId(OfferingId.DataRoom)
      )
    } yield GetDataRoomCountForOtherRegionsResponse(regionInfos, links)
  }

}
