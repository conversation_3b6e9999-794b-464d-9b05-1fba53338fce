// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service

import anduin.account.authentication.LoginPublicService
import anduin.account.enterprise.{EnterprisePublicService, EnterpriseService}
import anduin.account.profile.UserProfileService
import anduin.account.protocol.BifrostAuthenticationProtocol.EnterpriseLoginUserLinkedConfigInfo
import anduin.account.protocol.BifrostCommonProtocol
import anduin.dataroom.email.generate.{
  DataRoomJoinViaLinkInvitationEmailGenerate,
  DataRoomRejectionEmailGenerate,
  DataRoomRequestAccessEmailGenerate
}
import anduin.dataroom.email.{DataRoomEmailSenderService, DataRoomEmailService, DataRoomEmailTemplateType}
import anduin.dataroom.flow.DataRoomValidateOperations.SeatCheck
import anduin.dataroom.group.state.DataRoomGroupCreatedSharedFlowState
import anduin.dataroom.group.{
  AddUsersToDataRoomGroupParams,
  DataRoomGroupOperations,
  DataRoomGroupService,
  DataRoomGroupStateStoreOperations
}
import anduin.dataroom.homepage.DataRoomHomePageService
import anduin.dataroom.link.PendingRequest
import anduin.dataroom.notification.{DataRoomNotificationSettingsOperations, InvitationAcceptedEmailNotification}
import anduin.dataroom.participant.{
  DataRoomParticipantOperations,
  DataRoomParticipantRoleOperations,
  DataRoomParticipantService
}
import anduin.dataroom.state.{DataRoomCreatedSharedFlowState, LinkInvitation}
import anduin.dataroom.whitelabel.DataRoomAuthenticationWhiteLabelOperations
import anduin.fdb.record.DefaultCluster
import anduin.dms.DmsFeature.DataRoom
import anduin.fdb.record.model.{RecordIO, RecordTask}
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.id.dataroom.DataRoomGroupId
import anduin.id.entity.EntityId
import anduin.id.offering.OfferingId
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.model.id.GlobalOfferingIdFactory
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.protobuf.dataroom.link.{DataRoomLinkInvitation, DataRoomLinkInvitationParamsData, DataRoomLinkTypes}
import anduin.service.{AuthenticatedRequestContext, GeneralServiceException, PublicRequestContext, ServiceActor}
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import com.anduin.stargazer.service.GondorBackendConfig
import io.circe.parser.decode

import stargazer.model.routing.DynamicAuthPage.DataRoomDetailHomePage
import java.time.Instant

import anduin.dataroom.flow.*
import anduin.dataroom.role.*
import anduin.link.*
import anduin.model.codec.ProtoCodecs.given
import anduin.stargazer.service.dataroom.*
import anduin.team.TeamServiceParams.*
import anduin.team.*
import com.anduin.stargazer.service.utils.ZIOUtils
import io.circe.syntax.*
import zio.{Task, ZIO}

import anduin.dataroom.service.dmsmetadata.ModifyPermissionSourceOfEventMetaData.ModifyPermissionSourceOfEvent
import anduin.dataroom.service.webhook.DataRoomWebhookProducerService
import anduin.dms.service.FileService
import anduin.environment.{EnvironmentAuthenticationIntegrationService, EnvironmentReauthenticationPolicyService}
import anduin.greylin.modelti.DataRoomParticipantState
import anduin.greylin.{GreylinDataService, modelti, operation}
import anduin.id.engagement.EngagementId
import anduin.id.link.ProtectedLinkId

final case class DataRoomProtectedLinkService(
  fileService: FileService,
  backendConfig: GondorBackendConfig,
  dataRoomLoggingService: DataRoomLoggingService,
  dataRoomSharedFlowService: DataRoomSharedFlowService,
  dataRoomContactService: DataRoomContactService,
  dataRoomTermsOfAccessService: DataRoomTermsOfAccessService,
  dataRoomParticipantService: DataRoomParticipantService,
  dataRoomHomePageService: DataRoomHomePageService,
  dataRoomPermissionService: DataRoomPermissionService,
  dataRoomGroupService: DataRoomGroupService,
  dataRoomWebhookProducer: DataRoomWebhookProducerService,
  protectedLinkService: ProtectedLinkService,
  dataRoomEmailSenderService: DataRoomEmailSenderService,
  loginPublicService: LoginPublicService,
  enterprisePublicService: EnterprisePublicService,
  greylinDataService: GreylinDataService,
  environmentPolicyService: EnvironmentReauthenticationPolicyService,
  environmentAuthenticationIntegrationService: EnvironmentAuthenticationIntegrationService,
  enterpriseService: EnterpriseService
)(
  using val linkGeneratorService: LinkGeneratorService,
  val userProfileService: UserProfileService,
  val dataRoomEmailService: DataRoomEmailService
) {

  private def verifyLinkInvitationParams(
    dataRoomWorkflowId: DataRoomWorkflowId,
    data: DataRoomLinkInvitationParamsData,
    password: Option[String]
  ) = {
    for {
      _ <- RecordIO.validate(data.name.nonEmpty) {
        new RuntimeException("Link name is empty")
      }
      _ <- RecordIO.validate(password.forall(_.nonEmpty)) {
        new RuntimeException("Password is empty")
      }
      _ <- RecordIO.validate(data.expiryDate.forall(_.isAfter(Instant.now))) {
        new RuntimeException("Expiry date is in the past")
      }
      _ <- RecordIO.validate(data.whitelistedDomains.forall(EmailAddress.validDomain.matches)) {
        new RuntimeException("Whitelisted domains are invalid")
      }
      _ <- RecordIO.validate(data.groupIds.forall(_.parent == dataRoomWorkflowId)) {
        new RuntimeException("Group does not belong to data room")
      }
    } yield ()
  }

  private def getWorkflowIdFromLinkAsset(assetOpt: Option[String]): Option[DataRoomWorkflowId] = {
    val parser = Function.unlift[String, Option[DataRoomWorkflowId]] { asset =>
      decode[DataRoomLinkTypes](asset).toOption.collect { case DataRoomLinkInvitation(dataRoomWorkflowId, _) =>
        Some(dataRoomWorkflowId)
      }
    }

    assetOpt.flatMap(parser)
  }

  def createDataRoomLinkInvitation(
    params: CreateDataRoomLinkInvitationParams,
    actor: ServiceActor,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[CreateDataRoomLinkInvitationResponse] = {
    for {
      (createdState, drAuthWhitelabelModelOpt) <- FDBRecordDatabase.transact(
        FDBOperations[
          ((DataRoomValidateOperations, DataRoomGroupOperations), DataRoomAuthenticationWhiteLabelOperations)
        ].Production
      ) { case ((validateOps, groupOps), drAuthWhitelabelOps) =>
        for {
          (_, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = actor.userId,
            roleChecks = Set(DataRoomRoleUtils.isAdmin)
          )
          _ <- RecordIO.when(params.data.groupIds.isEmpty) {
            dataRoomPermissionService.validatePermissionChanges(
              validateOps = validateOps,
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              createdState = createdState,
              currentRoleAndChangeList = List(
                None -> DataRoomPermissionChanges(
                  Some(params.data.role),
                  params.data.permissionChanges.getOrElse(AssetPermissionChanges())
                )
              )
            )
          }
          _ <- verifyLinkInvitationParams(
            params.dataRoomWorkflowId,
            params.data,
            params.password
          )
          _ <- RecordIO.traverse(params.data.groupIds) { groupId =>
            groupOps.validateGroupStatusAndGetState(groupId)
          }
          drAuthWhitelabelModelOpt <- drAuthWhitelabelOps.getDataRoomAuthenticationWhiteLabelOpt(
            params.dataRoomWorkflowId
          )
        } yield createdState -> drAuthWhitelabelModelOpt
      }
      enterpriseLoginParams <-
        if (params.enableEnterpriseLogin) {
          getEnterpriseLoginParams(
            entityId = createdState.creatorEntityId,
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            actor = actor.userId
          ).map(Some(_))
        } else {
          ZIO.attempt(Option.empty[ProtectedLinkService.EnterpriseLoginParams])
        }
      (linkId, _) <- protectedLinkService.create(
        passwordOpt = params.password,
        expiryDate = params.data.expiryDate,
        whitelistedDomains = params.data.whitelistedDomains,
        asset = DataRoomLinkInvitation(params.dataRoomWorkflowId).asMessage.asJson.noSpaces,
        authenticationWhitelabelId = drAuthWhitelabelModelOpt.map(_.authenticationWhitelabelId),
        isRequiredApproval = params.data.isRequiredAdminApproval,
        enterpriseLoginParamsOpt = enterpriseLoginParams
      )
      _ <- FDBRecordDatabase.transact(FDBOperations[DataRoomFlowOperations].Production)(
        _.setLinkInvitation(
          actor = actor.userId,
          dataRoomWorkflowId = params.dataRoomWorkflowId,
          linkId = linkId,
          data = params.data,
          passwordChange = Some(params.password.nonEmpty),
          actorIp = httpContext.flatMap(_.getClientIP),
          isCreateOpt = Some(true)
        )
      )
      _ <- dataRoomLoggingService.logEventCreateInvitationLink(
        params.dataRoomWorkflowId,
        createdState.name,
        createdState.creatorEntityId,
        actor.userId,
        params.data.expiryDate.isDefined,
        params.password.isDefined,
        params.data.whitelistedDomains.nonEmpty,
        httpContext
      )
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
    } yield CreateDataRoomLinkInvitationResponse(linkId)
  }

  def modifyDataRoomLinkInvitation(
    params: ModifyDataRoomLinkInvitationParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext] = None
  ): Task[DataRoomEmptyResponse] = {
    for {
      createdState <- FDBRecordDatabase.transact(
        FDBOperations[(DataRoomValidateOperations, DataRoomGroupOperations)].Production
      ) { case (validateOps, groupOps) =>
        for {
          (_, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = actor,
            roleChecks = Set(DataRoomRoleUtils.isAdmin)
          )
          _ <- RecordIO.when(params.data.groupIds.isEmpty) {
            dataRoomPermissionService.validatePermissionChanges(
              validateOps = validateOps,
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              createdState = createdState,
              currentRoleAndChangeList = List(
                None -> DataRoomPermissionChanges(
                  Some(params.data.role),
                  params.data.permissionChanges.getOrElse(AssetPermissionChanges())
                )
              )
            )
          }
          _ <- RecordIO.validate(createdState.linkInvitationMap.contains(params.linkId)) {
            new RuntimeException(
              s"Data room ${params.dataRoomWorkflowId} doesn't track link ${params.linkId}"
            )
          }
          _ <- verifyLinkInvitationParams(
            params.dataRoomWorkflowId,
            params.data,
            params.password.flatMap(_.password)
          )
          _ <- RecordIO.traverse(params.data.groupIds) { groupId =>
            groupOps.validateGroupStatusAndGetState(groupId)
          }
        } yield createdState
      }
      enterpriseLoginChange <-
        params.enableEnterpriseLoginChange.fold(ZIO.attempt(Option.empty[ProtectedLinkService.EnterpriseLoginChange])) {
          enableEnterpriseLoginChange =>
            if (enableEnterpriseLoginChange) {
              getEnterpriseLoginParams(
                entityId = createdState.creatorEntityId,
                dataRoomWorkflowId = params.dataRoomWorkflowId,
                actor = actor
              ).map(params => Some(ProtectedLinkService.EnterpriseLoginChange(Some(params))))
            } else {
              ZIO.attempt(Some(ProtectedLinkService.EnterpriseLoginChange(None)))
            }
        }
      _ <- protectedLinkService.update(
        linkId = params.linkId,
        passwordChange = params.password.map(_.password),
        expiryDateChange = Some(params.data.expiryDate),
        isDisabledChange = Some(params.data.isDisabled),
        whitelistDomainsChange = Some(params.data.whitelistedDomains),
        isRequireApprovalChange = Some(params.data.isRequiredAdminApproval),
        enterpriseLoginChange = enterpriseLoginChange
      )
      _ <- FDBRecordDatabase.transact(FDBOperations[DataRoomFlowOperations].Production)(
        _.setLinkInvitation(
          actor = actor,
          dataRoomWorkflowId = params.dataRoomWorkflowId,
          linkId = params.linkId,
          data = params.data,
          passwordChange = params.password.map(_.password.nonEmpty),
          actorIp = httpContext.flatMap(_.getClientIP),
          isCreateOpt = Some(false)
        )
      )
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
    } yield DataRoomEmptyResponse()
  }

  def joinDataRoomViaLinkInvitation(
    params: JoinDataRoomViaLinkInvitationParams,
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomEmptyResponse] = {
    val workflowId = params.dataRoomWorkflowId
    val actor = ctx.actor.userId
    for {
      emailAddress <- userProfileService.getEmailAddress(actor)
      emailDomain = emailAddress.domain.value
      linkState <- protectedLinkService.getLinkState(params.linkId)

      (createdState, _, activeGroupIds, dataRoomLinkData, emailRecipients) <- FDBRecordDatabase.transact(
        FDBOperations[
          (
            (
              ((DataRoomFlowOperations, DataRoomValidateOperations), DataRoomNotificationSettingsOperations),
              (ProtectedLinkStoreOperations, TeamServiceOperations)
            ),
            (DataRoomGroupOperations, DataRoomParticipantOperations)
          )
        ].Production
      ) { case ((((flowOps, validateOps), notificationOps), (linkOps, teamOps)), (groupOps, participantOps)) =>
        for {
          _ <- RecordIO.validate(!linkState.isRequiredApproval) {
            new RuntimeException(s"This link ${params.linkId} is required admin approval")
          }
          (individualUserTeam, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = workflowId,
            userId = actor,
            roleChecks = Set(),
            checkJoined = DataRoomValidateOperations.MemberStateCheck.NoRequirement,
            checkTermsAccepted = false,
            checkTotalSeats = DataRoomValidateOperations.SeatCheck.Require(
              userType = DataRoomValidateOperations.SeatCheck.UserType.JoinedUsersWith(Set()),
              modification = Some(DataRoomValidateOperations.SeatCheck.Modification.JoinViaLink(params.linkId))
            )
          )
          participantRoles <- validateOps.roleOps.getAllParticipantRoleMap(workflowId)
          _ <- RecordIO.validate {
            TeamServiceUtils
              .getTeamMemberFlowState(validateOps.teamMemberStateOps.store)(individualUserTeam, actor)
              .map {
                _.forall {
                  case _: UserJoined => false
                  case _             => true
                }
              }
          } {
            new RuntimeException(s"User $actor has already joined data room $workflowId")
          }
          dataRoomLinkData <- RecordIO.fromOption(
            createdState.linkInvitationMap.get(params.linkId),
            new RuntimeException(s"Data room $workflowId doesn't track link ${params.linkId}")
          )
          activeGroupIds <- RecordIO
            .traverse(dataRoomLinkData.groupIds) { groupId =>
              groupOps.validateGroupStatusAndGetState(groupId).either.map(_.toOption)
            }
            .map(_.toSeq.flatten.flatMap(_.id))
          _ <- linkOps.grantAccess(
            params.linkId,
            actor,
            emailDomain,
            Instant.now
          )
          _ <- flowOps.joinViaLinkInvitation(
            workflowId,
            actor,
            params.linkId,
            ctx.getClientIP,
            dataRoomLinkData.name
          )
          _ <- participantOps.upsertParticipant(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = actor,
            _.copy(
              isRemoved = false,
              groupIds = Set.empty
            )
          )
          _ <- teamOps.addMember(
            AddMemberParams(
              inviter = dataRoomLinkData.lastUpdatedBy,
              invitee = actor,
              teamId = individualUserTeam,
              skipPermission = true
            )
          )
          _ <-
            if (dataRoomLinkData.isToaWhitelisted) {
              flowOps.setDataRoomTermsOfAccessWhitelistedUsers(
                workflowId,
                dataRoomLinkData.createdBy,
                createdState.termsOfAccessOptions.map(_.whitelistedUsers + actor).getOrElse(Set(actor))
              )
            } else {
              RecordIO.traverse(params.toaFileIdOpt) { toaFileId =>
                dataRoomTermsOfAccessService.accept(
                  flowOps = flowOps,
                  participantOps = participantOps,
                  createdState = createdState,
                  params = AcceptTermsOfAccessToDataRoomParams(workflowId, toaFileId, linkIdOpt = Some(params.linkId)),
                  actor = actor,
                  httpContext = Some(ctx)
                )
              }
            }
          _ <- flowOps.stateOps.store.toRecordIO {
            val isInternal = DataRoomRoleUtils.isInternal(dataRoomLinkData.role)
            dataRoomContactService.syncContactRole(
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              added = Option.when(isInternal)(ctx.actor.userId).toSet
            )
          }
          adminUsers = (participantRoles - ctx.actor.userId).flatMap { case (userId, role) =>
            Option.when(DataRoomRoleUtils.isAdmin(role))(userId)
          }
          emailRecipients <- RecordIO.parTraverseN(64)(adminUsers) { userId =>
            notificationOps
              .getInvitationAcceptedEmail(
                params.dataRoomWorkflowId,
                userId,
                createdState.dataRoomNotificationConfigs
              )
              .map {
                case InvitationAcceptedEmailNotification.All =>
                  Some(userId)
                case _ => None
              }
          }
        } yield (createdState, participantRoles, activeGroupIds, dataRoomLinkData, emailRecipients.flatten.toSet)
      }
      _ <- ZIO.foreach(activeGroupIds.toSeq) { groupId =>
        dataRoomGroupService.addUsersToGroup(
          params = AddUsersToDataRoomGroupParams(
            groupId = groupId,
            userIds = Set(actor)
          ),
          actor = dataRoomLinkData.lastUpdatedBy,
          ctx = None
        )
      }
      _ <- dataRoomTermsOfAccessService.changeTermsFolderPermissionsUnsafe(
        actor,
        workflowId,
        Map(actor -> dataRoomLinkData.role)
      )
      _ <- dataRoomHomePageService.changeHomePageFolderPermissions(
        actor,
        workflowId,
        Map(actor -> dataRoomLinkData.role),
        FileService.PermissionModMode.Unsafe
      )
      _ <- ZIOUtils.when(dataRoomLinkData.groupIds.isEmpty) {
        dataRoomPermissionService.modifyAssetPermissions(
          actor = actor,
          dataRoomWorkflowId = workflowId,
          userChanges = Map(
            actor -> DataRoomPermissionChanges(
              Some(dataRoomLinkData.role),
              dataRoomLinkData.permissions.getOrElse(AssetPermissionChanges())
            )
          ),
          mode = FileService.PermissionModMode.Unsafe,
          actorIp = ctx.getClientIP,
          modifyPermissionSourceOfEvent = Some(ModifyPermissionSourceOfEvent.JoinViaInvitationLink)
        )
      }
      _ <- dataRoomLoggingService.logEventUserJoinDataRoom(
        dataRoomId = workflowId,
        dataRoomName = createdState.name,
        creatorEntity = createdState.creatorEntityId,
        actor = ctx.actor,
        httpContext = Some(ctx)
      )
      _ <- ZIOUtils.when(emailRecipients.nonEmpty) {
        dataRoomEmailSenderService.sendEmail(
          params.dataRoomWorkflowId,
          DataRoomJoinViaLinkInvitationEmailGenerate(
            params.dataRoomWorkflowId,
            ctx.actor.userId,
            emailRecipients,
            createdState.name
          )
        )
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
      _ <-
        dataRoomContactService
          .addContactsToDataRoom(
            preferredActor = dataRoomLinkData.createdBy,
            userIds = Set(actor),
            dataRoomWorkflowId = params.dataRoomWorkflowId
          )
          .catchAllCause { err =>
            ZIO.logWarningCause(
              s"failed to add contact to data room space: $actor to ${params.dataRoomWorkflowId}",
              err
            )
          }
      _ <- greylinDataService.runUnit(
        operation.DataRoomParticipantOperations.addParticipants(
          List(
            modelti.DataRoomParticipant(
              actor,
              params.dataRoomWorkflowId,
              state = Some(DataRoomParticipantState.Joined)
            )
          )
        )
      )
      _ <- dataRoomWebhookProducer.produceUserJoinEvent(
        params.dataRoomWorkflowId,
        actor,
        activeGroupIds.toSeq
      )
    } yield DataRoomEmptyResponse()
  }

  def getLinkInvitationInfo(
    params: GetDataRoomLinkInvitationInfoParams,
    actor: UserId
  ): Task[GetDataRoomLinkInvitationInfoResponse] = {
    for {
      emailAddress <- userProfileService.getEmailAddress(actor)
      emailDomain = emailAddress.domain.value
      (linkName, toaFileIdOpt, hasJoined, isSeatLimitReached) <- FDBRecordDatabase.transact(
        FDBOperations[(DataRoomValidateOperations, (TeamServiceOperations, ProtectedLinkStoreOperations))].Production
      ) { case (validateOps, (teamOps, linkOps)) =>
        for {
          (individualUserTeam, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = actor,
            roleChecks = Set(),
            checkJoined = DataRoomValidateOperations.MemberStateCheck.NoRequirement,
            checkTermsAccepted = false
          )
          participantRoles <- validateOps.roleOps.getAllParticipantRoleMap(params.dataRoomWorkflowId)
          teamMemberInfo <- teamOps.getTeamMemberInfo(individualUserTeam)
          hasJoined = teamMemberInfo.joinedMembers.contains(actor)
          linkInfo <- RecordIO.fromOption(
            createdState.linkInvitationMap.get(params.linkId),
            new RuntimeException(s"Data room ${params.dataRoomWorkflowId} doesn't track link ${params.linkId}")
          )
          isInternal = DataRoomRoleUtils.isInternal(linkInfo.role)
          currentSeatCount = participantRoles.count { case (userId, role) =>
            DataRoomRoleUtils.isInternal(role) && teamMemberInfo.joinedMembers.contains(userId)
          }
          dataRoomPlan <- validateOps.billingOps.getPlan(createdState)
          isSeatLimitReached = !hasJoined && isInternal && currentSeatCount >= dataRoomPlan.totalSeats
          _ <-
            if (hasJoined) {
              linkOps.grantAccess(
                params.linkId,
                actor,
                emailDomain,
                Instant.now
              )
            } else {
              linkOps.checkValidity(
                params.linkId,
                actor,
                emailDomain,
                None,
                Instant.now
              )
            }
          toaFileIdOpt = createdState.termsOfAccessOptions
            .flatMap { options =>
              options.versions.lastOption.filter { _ =>
                options.isEnabled
              }
            }
            .filterNot(_ => linkInfo.isToaWhitelisted)
        } yield (createdState.name, toaFileIdOpt, hasJoined, isSeatLimitReached)
      }
      toaFileIdName <- ZIOUtils.traverseOption(toaFileIdOpt)(fileId => fileService.getFileNameUnsafe(fileId))
    } yield GetDataRoomLinkInvitationInfoResponse(
      linkName,
      toaFileIdOpt,
      toaFileIdName,
      hasJoined,
      isSeatLimitReached
    )
  }

  def deleteLinkInvitation(
    params: DeleteDataRoomLinkInvitationParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext] = None
  ): Task[DataRoomEmptyResponse] = {
    for {
      _ <- FDBRecordDatabase.transact(
        FDBOperations[
          ((DataRoomFlowOperations, DataRoomValidateOperations), ProtectedLinkStoreOperations)
        ].Production
      ) { case ((flowOps, validateOps), linkOps) =>
        for {
          (_, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = actor,
            roleChecks = Set(DataRoomRoleUtils.isAdmin)
          )
          _ <- RecordIO.validate(createdState.linkInvitationMap.contains(params.linkId)) {
            new RuntimeException(s"Data room ${params.dataRoomWorkflowId} doesn't track link ${params.linkId}")
          }
          _ <- linkOps.update(
            linkId = params.linkId,
            passwordChange = None,
            expiryDateChange = None,
            isDisabledChange = Some(true),
            whitelistDomainsChange = None,
            isRequireApprovalChange = None
          )
          _ <- flowOps.deleteLinkInvitation(
            params.dataRoomWorkflowId,
            actor,
            params.linkId,
            ctx.flatMap(_.getClientIP),
            linkName = createdState.linkInvitationMap(params.linkId).name
          )
        } yield ()
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
    } yield DataRoomEmptyResponse()
  }

  def getLinkName(
    params: GetLinkNameParams
  ): Task[GetLinkNameResponse] = {
    for {
      linkName <- FDBRecordDatabase.transact(
        FDBOperations[
          (DataRoomStateStoreOperations, ProtectedLinkStoreOperations)
        ].Production
      ) { case (stateOps, linkOps) =>
        for {
          assetOpt <- linkOps.getLinkAsset(params.linkId)
          dataRoomWorkflowIdOpt = getWorkflowIdFromLinkAsset(assetOpt)

          dataRoomName <- dataRoomWorkflowIdOpt.fold[RecordTask[String]](
            RecordIO.succeed("Data room")
          ) { dataRoomWorkflowId =>
            stateOps.getState(dataRoomWorkflowId).map(_.name)
          }
        } yield dataRoomName
      }
    } yield GetLinkNameResponse(linkName)
  }

  def checkAccessGranted(
    params: CheckAccessGrantedParams,
    ctx: AuthenticatedRequestContext
  ): Task[CheckAccessGrantedResponse] = {
    val userId = ctx.actor.userId
    for {
      (isAccessGranted, assetOpt) <- FDBRecordDatabase.transact(
        FDBOperations[
          (
            (TeamServiceOperations, DataRoomModelStoreOperations),
            ((DataRoomFlowOperations, DataRoomParticipantRoleOperations), ProtectedLinkStoreOperations)
          )
        ].Production
      ) { case ((teamOps, drModelOps), ((flowOps, participantRoleOps), linkOps)) =>
        for {
          // Verify link state
          _ <- linkOps.checkValidityPublic(
            params.linkId,
            None,
            Instant.now
          )
          assetOpt <- linkOps.getLinkAsset(params.linkId)

          dataRoomWorkflowId <- RecordIO.fromOption(
            getWorkflowIdFromLinkAsset(assetOpt),
            new RuntimeException(s"Can not extract dataRoomId from link ${params.linkId}")
          )

          individualUserTeam <- drModelOps.getTeamId(dataRoomWorkflowId)
          teamMemberInfo <- teamOps.getTeamMemberInfo(individualUserTeam)
          drState <- flowOps.stateOps.getState(dataRoomWorkflowId)
          _ <- RecordIO.fromOption(
            drState.linkInvitationMap.get(params.linkId),
            CheckValidityException.InvalidLink(params.linkId)
          )
          userIsParticipant <- participantRoleOps
            .getParticipantRole(dataRoomWorkflowId, userId)
            .as(true)
            .orElseSucceed(false)
          isAccessGranted = teamMemberInfo.joinedMembers.contains(userId) && userIsParticipant
        } yield (isAccessGranted, assetOpt)
      }
    } yield CheckAccessGrantedResponse(
      isAccessGranted = isAccessGranted,
      assetOpt = assetOpt.filter(_ => isAccessGranted)
    )
  }

  def requestAccess(
    params: RequestAccessParams,
    ctx: Option[PublicRequestContext] = None
  ): Task[RequestAccessResponse] = {
    for {
      _ <- ZIO.logInfo(s"${params.email} requests to access data room with link = ${params.linkId}")
      _ <- protectedLinkService.verifyCaptchaResponse(params.captchaResponse)
      emailAddress <- ZIOUtils
        .optionToTask(EmailAddress.unapply(params.email), CheckValidityException.InvalidEmailAddress(params.email))
      userIdOpt <- userProfileService.getUserIdFromEmailAddress(params.email).either.map(_.toOption)
      linkState <- protectedLinkService.getLinkState(params.linkId)
      // Verify link state
      _ <- ZIOUtils.validate(linkState.isRequiredApproval)(
        CheckValidityException.LinkIsNotRequiredApproval(params.linkId)
      )

      (status, teamMemberInfo, drState, currentAdmins) <- FDBRecordDatabase.transact(
        FDBOperations[
          (
            (TeamServiceOperations, DataRoomModelStoreOperations),
            ((DataRoomFlowOperations, DataRoomParticipantRoleOperations), ProtectedLinkStoreOperations)
          )
        ].Production
      ) { case ((teamOps, drModelOps), ((flowOps, participantRoleOps), linkOps)) =>
        for {
          _ <- linkOps.checkExpiredAndDomain(
            params.linkId,
            now = Instant.now,
            emailDomain = emailAddress.domain.value
          )
          assetOpt <- linkOps.getLinkAsset(params.linkId)
          dataRoomWorkflowId <- RecordIO.fromOption(
            getWorkflowIdFromLinkAsset(assetOpt),
            new RuntimeException(s"Can not extract dataRoomId from link ${params.linkId}")
          )

          individualUserTeam <- drModelOps.getTeamId(dataRoomWorkflowId)
          teamMemberInfo <- teamOps.getTeamMemberInfo(individualUserTeam)

          joinedOrInvitedStatusPreCheck = userIdOpt.flatMap { userId =>
            if (teamMemberInfo.joinedMembers.contains(userId)) {
              Some(
                RequestAccessResponseStatus.UserJoined(
                  DataRoomDetailHomePage(dataRoomWorkflowId)
                )
              )
            } else if (teamMemberInfo.invitedUsers.contains(userId)) {
              Some(RequestAccessResponseStatus.UserInvited)
            } else {
              None
            }
          }

          drState <- flowOps.stateOps.getState(dataRoomWorkflowId)

          status <- joinedOrInvitedStatusPreCheck.fold[RecordTask[RequestAccessResponseStatus]] {
            for {
              linkState <- RecordIO.fromOption(
                drState.linkInvitationMap.get(params.linkId),
                CheckValidityException.InvalidLink(params.linkId)
              )
              accessRequestOpt = linkState.accessRequests.get(emailAddress.address)
              status <- accessRequestOpt.fold[RecordTask[RequestAccessResponseStatus]] {
                flowOps
                  .requestAccessDataRoom(
                    dataRoomWorkflowId,
                    params.linkId,
                    emailAddress.address,
                    ctx.flatMap(_.getClientIP)
                  )
                  .map(_ => RequestAccessResponseStatus.RequestSent)
              } { accessRequest =>
                accessRequest.status match {
                  case _: PendingRequest => RecordIO.succeed(RequestAccessResponseStatus.ApprovalPending)
                  case _ =>
                    flowOps
                      .requestAccessDataRoom(
                        dataRoomWorkflowId,
                        params.linkId,
                        emailAddress.address
                      )
                      .map(_ => RequestAccessResponseStatus.RequestSent)
                }
              }
            } yield status
          }(status => RecordIO.succeed(status))
          currentAdmins <- participantRoleOps.getCurrentAdmins(dataRoomWorkflowId)
        } yield (status, teamMemberInfo, drState, currentAdmins)
      }

      joinedAdmins = currentAdmins.intersect(teamMemberInfo.joinedMembers)
      _ <- ZIOUtils.when(status == RequestAccessResponseStatus.RequestSent) {
        ZIO
          .foreach(joinedAdmins) { admin =>
            dataRoomEmailSenderService.sendEmail(
              drState.dataRoomWorkflowId,
              DataRoomRequestAccessEmailGenerate(
                drState.dataRoomWorkflowId,
                admin,
                drState.name,
                emailAddress.address
              )
            )
          }
          .unit
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(drState.dataRoomWorkflowId)
      _ <- ZIO.logInfo(s"DONE ${params.email} requested to access data room with link = ${params.linkId}")
      response <- status match {
        case RequestAccessResponseStatus.UserJoined(_) =>
          for {
            emailAddressOpt <- ZIOUtils.traverseOption(userIdOpt)(userId => userProfileService.getEmailAddress(userId))
            ssoBindingOpt <- ZIOUtils
              .traverseOption(emailAddressOpt)(emailAddress =>
                enterprisePublicService.getOptEmailDomainBinding(emailAddress.domain.value)
              )
              .map(_.flatten)
            ssoConfigOpt <- ZIOUtils
              .traverseOption(ssoBindingOpt)(ssoBinding =>
                enterprisePublicService.getOptEnterpriseLoginConfig(ssoBinding.configId)
              )
              .map(_.flatten)
            response <- ssoConfigOpt.fold {
              userIdOpt.fold(
                ZIO.attempt(
                  RequestAccessResponse(
                    status = status,
                    authenticator = BifrostCommonProtocol.LoginAuthenticator.Normal(""),
                    hasPassword = false,
                    enablePrimaryEmailOTP = false,
                    linkedSso = Seq.empty
                  )
                )
              ) { userId =>
                for {
                  userIdToken <- loginPublicService.createUserIdToken(userId, Some(linkState.linkId))
                  hasPassword <- userProfileService.hasPassword(userId)
                  hasMFA <- userProfileService.hasMFA(userId)
                  linkedSso <- enterprisePublicService.getLinkedConfigInfosForUser(userId, filterOutEnvironment = true)
                } yield RequestAccessResponse(
                  status = status,
                  authenticator = BifrostCommonProtocol.LoginAuthenticator.Normal(userIdToken),
                  hasPassword = hasPassword,
                  enablePrimaryEmailOTP = !hasMFA,
                  linkedSso = linkedSso
                )
              }
            } { ssoConfig =>
              for {
                userIdTokenOpt <- ZIOUtils.traverseOption(userIdOpt)(userId =>
                  loginPublicService.createUserIdToken(userId, Some(linkState.linkId))
                )
              } yield RequestAccessResponse(
                status = status,
                authenticator = BifrostCommonProtocol.LoginAuthenticator.SSOEnforced(
                  userIdTokenOpt = userIdTokenOpt,
                  domain = emailAddress.domain.value,
                  ssoConfig = EnterpriseLoginUserLinkedConfigInfo(
                    configId = ssoConfig.id,
                    providerName = ssoConfig.providerName,
                    providerLogoUrl = ssoConfig.providerLogoUrl
                  )
                ),
                hasPassword = false,
                enablePrimaryEmailOTP = false,
                linkedSso = Seq.empty
              )
            }
          } yield response
        case _ =>
          ZIO.attempt(
            RequestAccessResponse(
              status = status,
              authenticator = BifrostCommonProtocol.LoginAuthenticator.Normal(""),
              hasPassword = false,
              enablePrimaryEmailOTP = false,
              linkedSso = Seq.empty
            )
          )
      }
    } yield response
  }

  private def partitionAccessRequests(
    createdState: DataRoomCreatedSharedFlowState,
    groupMap: Map[DataRoomGroupId, DataRoomGroupCreatedSharedFlowState],
    requests: Seq[DataRoomAccessRequest]
  ): Task[(Map[String, DataRoomPermissionChanges], Map[String, DataRoomGroupPermissionChanges])] = {
    requests match {
      case request :: rest =>
        for {
          (individualPermissionMap, groupPermissionMap) <- partitionAccessRequests(
            createdState,
            groupMap,
            rest
          )
          linkState <- ZIOUtils.optionToTask(
            createdState.linkInvitationMap.get(request.linkId),
            new RuntimeException(s"Data room ${createdState.dataRoomWorkflowId} does not have link ${request.linkId}")
          )
          (individualPermissionResult, groupPermissionResult) <-
            if (linkState.groupIds.isEmpty) {
              val entry = request.email -> DataRoomPermissionChanges(
                Some(linkState.role),
                linkState.permissions.getOrElse(AssetPermissionChanges()),
                linkState.isToaWhitelisted
              )
              ZIO.succeed((individualPermissionMap + entry) -> groupPermissionMap)
            } else {
              for {
                activeGroupIds <- ZIO.succeed(linkState.groupIds.flatMap(groupMap.get).flatMap(_.id))
                entry = request.email -> DataRoomGroupPermissionChanges(
                  groupIds = activeGroupIds,
                  canInvite = false,
                  linkState.isToaWhitelisted
                )
              } yield {
                individualPermissionMap -> (groupPermissionMap + entry)
              }
            }
        } yield (individualPermissionResult, groupPermissionResult)
      case _ =>
        ZIO.succeed(Map.empty[String, DataRoomPermissionChanges] -> Map.empty[String, DataRoomGroupPermissionChanges])
    }
  }

  def approveAccessRequests(
    params: ApproveAccessRequestsParams,
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomEmptyResponse] = {
    for {
      _ <- ZIO.logInfo(s"${ctx.actor.userId} approves data room access requests ${params.requests}")
      emailToUserId <- ZIOUtils
        .foreachParN(5)(params.requests) { request =>
          userProfileService
            .getUserIdFromEmailAddress(request.email)
            .either
            .map(userIdOpt => request.email -> userIdOpt.toOption)
        }
        .map(_.toMap)
      (createdState, groupMap, joinedUsers, invitedUsers) <- FDBRecordDatabase.transact(
        FDBOperations[
          ((DataRoomValidateOperations, TeamServiceOperations), DataRoomGroupStateStoreOperations)
        ].Production
      ) { case ((validateOps, teamOps), groupStateOps) =>
        for {
          (individualUserTeam, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = ctx.actor.userId,
            roleChecks = Set(DataRoomRoleUtils.isAdmin),
            checkTotalSeats = SeatCheck.NoRequirement
          )
          // Check all access requests are pending
          requestsAreValid = params.requests.forall { request =>
            createdState.linkInvitationMap.get(request.linkId).exists { linkInvitation =>
              linkInvitation.accessRequests.get(request.email).exists { request =>
                request.status.asMessage.sealedValue.isPendingRequest
              }
            }
          }
          _ <- RecordIO.validate(requestsAreValid)(
            new RuntimeException("One or some access requests are not pending request")
          )

          teamInfo <- teamOps.getTeamMemberInfo(individualUserTeam)
          joinedUsers = emailToUserId.filter { case (_, userIdOpt) =>
            userIdOpt.exists(teamInfo.joinedMembers.contains)
          }
          invitedUsers = emailToUserId.filter { case (_, userIdOpt) =>
            userIdOpt.exists(teamInfo.invitedUsers.contains)
          }
          groupMap <- groupStateOps.getAllGroupsMap(params.dataRoomWorkflowId)
        } yield (createdState, groupMap, joinedUsers, invitedUsers)
      }
      shouldInviteRequests = params.requests.filter { request =>
        !(joinedUsers.keySet.contains(request.email) || invitedUsers.keySet.contains(request.email))
      }
      (individualPermissionMap, groupPermissionMap) <- partitionAccessRequests(
        createdState,
        groupMap,
        shouldInviteRequests
      )
      invitationTemplate <- dataRoomEmailService
        .getEmailTemplate(
          GetEmailTemplateParams(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            templateType = DataRoomEmailTemplateType.Invitation
          ),
          ctx.actor.userId
        )
        .map(_.template)
      (toaWhitelistedIndividual, toaRequiredIndividual) = individualPermissionMap.partition(_._2.isToaWhitelisted)
      (toaWhitelistedGroup, toaRequiredGroup) = groupPermissionMap.partition(_._2.isToaWhitelisted)
      _ <- ZIOUtils.unless(toaRequiredGroup.isEmpty && toaRequiredIndividual.isEmpty)(
        dataRoomParticipantService
          .inviteUsers(
            InviteUsersToDataRoomParams(
              params.dataRoomWorkflowId,
              individualPermissionMap = toaRequiredIndividual,
              groupPermissionMap = toaRequiredGroup,
              isToaRequired = true,
              subject = invitationTemplate.subject,
              message = invitationTemplate.body,
              buttonLabel = invitationTemplate.callToAction,
              isApproved = true
            ),
            ctx.actor,
            Some(ctx)
          )
          .map(_ => ())
      )
      _ <- ZIOUtils.unless(toaWhitelistedGroup.isEmpty && toaWhitelistedIndividual.isEmpty)(
        dataRoomParticipantService
          .inviteUsers(
            InviteUsersToDataRoomParams(
              params.dataRoomWorkflowId,
              individualPermissionMap = toaWhitelistedIndividual,
              groupPermissionMap = toaWhitelistedGroup,
              isToaRequired = false,
              subject = invitationTemplate.subject,
              message = invitationTemplate.body,
              buttonLabel = invitationTemplate.callToAction,
              isApproved = true
            ),
            ctx.actor,
            Some(ctx)
          )
          .map(_ => ())
      )
      // Only change to approved after invite, since the seats check is done in invite api
      _ <- FDBRecordDatabase.transact(DataRoomFlowOperations.Production) { flowOps =>
        RecordIO.parTraverseN(2)(params.requests) { request =>
          flowOps.approveRequestAccessDataRoom(
            params.dataRoomWorkflowId,
            ctx.actor.userId,
            request.linkId,
            request.email,
            ctx.getClientIP
          )
        }
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
    } yield DataRoomEmptyResponse()
  }

  def declineAccessRequests(
    params: DeclineAccessRequestsParams,
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomEmptyResponse] = {
    for {
      _ <- ZIO.logInfo(s"${ctx.actor.userId} declines data room access requests ${params.requests}")
      createdState <- FDBRecordDatabase.transact(
        FDBOperations[
          (DataRoomValidateOperations, DataRoomFlowOperations)
        ].Production
      ) { case (validateOps, flowOps) =>
        for {
          (_, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = ctx.actor.userId,
            roleChecks = Set(DataRoomRoleUtils.isAdmin),
            checkTotalSeats = SeatCheck.NoRequirement
          )
          // Check all access requests are pending
          requestsAreValid = params.requests.forall { request =>
            createdState.linkInvitationMap.get(request.linkId).exists { linkInvitation =>
              linkInvitation.accessRequests.get(request.email).exists { request =>
                request.status.asMessage.sealedValue.isPendingRequest
              }
            }
          }
          _ <- RecordIO.validate(requestsAreValid)(
            new RuntimeException("One or some access requests are not pending request")
          )
          _ <- RecordIO.parTraverseN(2)(params.requests) { request =>
            flowOps.declineRequestAccessDataRoom(
              params.dataRoomWorkflowId,
              ctx.actor.userId,
              request.linkId,
              request.email,
              ctx.getClientIP
            )
          }
        } yield createdState
      }
      _ <- ZIOUtils.when(params.hasNotifyEmail) {
        for {
          _ <- dataRoomEmailSenderService.sendEmail(
            params.dataRoomWorkflowId,
            DataRoomRejectionEmailGenerate(
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              dataRoomName = createdState.name,
              actor = ctx.actor.userId,
              receiverEmails = params.requests.map(_.email)
            )
          )
        } yield ()
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
    } yield DataRoomEmptyResponse()
  }

  def getSharableLinkConfig(
    params: GetSharableLinkConfigParams,
    actor: UserId
  ): Task[GetSharableLinkConfigResponse] = {
    for {
      createdState <- FDBRecordDatabase.transact(FDBOperations[DataRoomValidateOperations].Production) { validateOps =>
        for {
          (_, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = actor,
            roleChecks = Set(DataRoomRoleUtils.isAdmin)
          )
        } yield createdState
      }
      environmentIdOpt = createdState.environmentIdOpt
      environmentHasSSOPolicy <- environmentIdOpt.fold(ZIO.succeed(false)) { environmentId =>
        environmentPolicyService.hasReauthenticationPolicy(environmentId)
      }
      binding <- enterprisePublicService.getOptEntityBinding(createdState.creatorEntityId)
      isEnterpriseLoginEnabled = !environmentHasSSOPolicy && binding.exists(
        _.offeringIds.contains(GlobalOfferingIdFactory.generateId(OfferingId.DataRoom))
      )
      isSharableLinkEnabled <- environmentIdOpt.fold(ZIO.succeed(true)) { environmentId =>
        environmentAuthenticationIntegrationService.isSharableLinkEnabled(
          GlobalOfferingIdFactory.generateId(OfferingId.DataRoom),
          environmentId
        )
      }
      ssoProviderForProtectedLinkOpt <- environmentIdOpt.fold(ZIO.attempt(None)) { environmentId =>
        for {
          configIdOpt <- environmentAuthenticationIntegrationService.resolveSsoProviderForProtectedLink(
            GlobalOfferingIdFactory.generateId(OfferingId.DataRoom),
            environmentId
          )
          configOpt <- ZIOUtils.traverseOption2(configIdOpt) { configId =>
            enterpriseService.getOptEnterpriseLoginConfig(configId)
          }
        } yield configOpt
      }
    } yield GetSharableLinkConfigResponse(
      isEnterpriseLoginEnabled = isEnterpriseLoginEnabled,
      isSharableLinkEnabled = isSharableLinkEnabled,
      ssoProviderName = ssoProviderForProtectedLinkOpt.map(_.providerName)
    )
  }

  private def getEnterpriseLoginParams(entityId: EntityId, dataRoomWorkflowId: DataRoomWorkflowId, actor: UserId) = {
    for {
      (binding, enterpriseLoginConfig) <- enterprisePublicService.getEntityBinding(entityId)
      _ <- ZIOUtils.validate(
        binding.offeringIds.contains(GlobalOfferingIdFactory.generateId(OfferingId.DataRoom))
      )(GeneralServiceException("Single sign on is not enabled for this data room"))
    } yield ProtectedLinkService.EnterpriseLoginParams(
      configId = enterpriseLoginConfig.id,
      offeringId = Some(OfferingId.DataRoom),
      creatorId = actor,
      context = s"Data room ${dataRoomWorkflowId.idString}",
      environmentId = None,
      engagementId = Some(EngagementId.DataRoom(dataRoomWorkflowId))
    )
  }

  def getLinkStateMap(linkInvitationMap: Map[ProtectedLinkId, LinkInvitation], parallelism: Int = 8)
    : Task[Map[ProtectedLinkId, ProtectedLinkStateWithEnterpriseLogin]] = {
    ZIOUtils
      .foreachParN(parallelism)(linkInvitationMap.keys) { linkId =>
        protectedLinkService
          .getLinkStateWithEnterpriseLogin(linkId, GlobalOfferingIdFactory.generateId(OfferingId.DataRoom))
          .map(linkId -> _)
      }
      .map(_.toMap)
  }

}
