// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service.exporter

import java.time.Instant

import anduin.dataroom.group.state.DataRoomGroupCreatedSharedFlowState
import anduin.dataroom.notification.{DataRoomNotificationUtils, NotificationMode}
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.dataroom.service.exporter.ExportDataRoomActivitiesGenerator.*
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.FileId
import anduin.stargazer.service.dataroom.FileInfo
import anduin.dataroom.activity.*
import anduin.dataroom.participant.DataRoomParticipant

private[exporter] final case class ExportDataRoomActivitiesGenerator(
  userInfoMap: Map[UserId, UserInfo],
  fileInfoMap: Map[FileId, FileInfo],
  groupMap: Map[DataRoomGroupId, DataRoomGroupCreatedSharedFlowState],
  participantMap: Map[UserId, DataRoomParticipant],
  timezoneOffsetInMinutes: Int,
  activities: Seq[DataRoomActivity]
) extends ExportGenerator[DataRoomActivity, Field] {

  protected val keys: List[DataRoomActivity] = {
    activities
      .sortBy(_.timestamp)(
        using Ordering[Option[Instant]].reverse
      )
      .toList
  }

  protected val fields: List[Field] = Field.values.toList

  protected def getRow(activity: DataRoomActivity): Extract = {
    case Field.Timestamp =>
      List(DataRoomExportUtils.convertTimestamp(activity.timestamp, timezoneOffsetInMinutes))
    case Field.ActorName =>
      List(activity.actor.flatMap(userInfoMap.get).fold("")(_.fullNameString))
    case Field.ActorEmail =>
      List(getActorEmail(activity, userInfoMap))
    case Field.ActorEmailDomain =>
      List(getActorEmailDomain(activity, userInfoMap))
    case Field.Group =>
      List(
        activity.actor.fold("") { actor =>
          DataRoomExportUtils.getGroupNames(groupMap)(
            participantMap.get(actor).map(_.groupIds).getOrElse(Set.empty)
          )
        }
      )
    case Field.ActivityDescription =>
      List(getActivityDescription(activity, userInfoMap, fileInfoMap, groupMap))
    case Field.ActorIp =>
      List(activity.actorIp.getOrElse(""))
  }

  protected val getHeader: Extract = { field =>
    List(Field.getName(field))
  }

}

object ExportDataRoomActivitiesGenerator {

  enum Field {
    case Timestamp, ActorName, Group, ActorEmail, ActorEmailDomain, ActivityDescription, ActorIp
  }

  object Field {

    def getName(field: Field): String = field match {
      case Timestamp           => "Date and time"
      case ActorName           => "Actor name"
      case Group               => "Actor Group"
      case ActorEmail          => "Actor email"
      case ActorEmailDomain    => "Email domain"
      case ActivityDescription => "Activity"
      case ActorIp             => "User IP"
    }

  }

  private def getSettingText(setting: Boolean) = if (setting) "ON" else "OFF"

  def getActorEmail(
    activity: DataRoomActivity,
    userInfoMap: Map[UserId, UserInfo]
  ): String = {
    activity match {
      case a: RequestAccessDataRoomActivity => a.email
      case _                                => activity.actor.flatMap(userInfoMap.get).fold("")(_.emailAddressStr)
    }
  }

  def getActorEmailDomain(
    activity: DataRoomActivity,
    userInfoMap: Map[UserId, UserInfo]
  ): String = {
    activity match {
      case a: RequestAccessDataRoomActivity => EmailAddress.unapply(a.email).fold("")(_.domain.value)
      case _ => activity.actor.flatMap(userInfoMap.get).flatMap(_.emailAddress).fold("")(_.domain.value)
    }
  }

  def getActivityDescription(
    activity: DataRoomActivity,
    userInfoMap: Map[UserId, UserInfo],
    fileInfoMap: Map[FileId, FileInfo],
    groupMap: Map[DataRoomGroupId, DataRoomGroupCreatedSharedFlowState]
  ): String = activity match {
    case a: CreateDataRoomActivity =>
      s"Created this data room ${a.name}"
    case a: RenameDataRoomActivity =>
      s"Renamed this data room ${a.name}"
    case a: SetIsArchivedDataRoomActivity =>
      s"${if (a.isArchived) "Archived" else "Unarchived"} this data room"
    case a: InviteUsersToDataRoomActivity =>
      // in batch invite, all roles are the same
      val userIds = a.individualRoles.keys.toList
      val role = a.individualRoles.head._2
      val participants = DataRoomExportUtils.getUserDisplayNameList(userInfoMap)(userIds)
      s"Invited $participants to join this data room as ${DataRoomRoleUtils.getName(role)}"
    case _: AddPortalUserToDataRoomActivity =>
      val actorName = activity.actor.flatMap(userInfoMap.get).fold("")(_.getDisplayName)
      s"Invited $actorName to join this data room as Anduin Support"
    case _: AcceptInvitationToDataRoomActivity =>
      "Joined this data room via invitation email"
    case _: DeclineInvitationToDataRoomActivity =>
      "Declined an invitation to join this data room"
    case a: CancelInvitationToDataRoomActivity =>
      val name = DataRoomExportUtils.getUserDisplayNameList(userInfoMap)(a.userIds.toList)
      s"Cancelled $name's invitation to join this data room"
    case a: ModifyDataRoomPermissionsActivity =>
      val updatedRoles = a.updatedRole.groupBy(_._2).transform((_, roleMap) => roleMap.keySet.toList).toList
      val updateDescription = updatedRoles
        .map { case (role, userIds) =>
          val participants = DataRoomExportUtils.getUserDisplayNameList(userInfoMap)(userIds)
          val roleName = DataRoomRoleUtils.getName(role)
          s"${participants}'s role to $roleName"
        }
        .mkString("; ")
      s"Changed $updateDescription"
    case a: RemindInvitationToDataRoomActivity =>
      val names = DataRoomExportUtils.getUserDisplayNameList(userInfoMap)(a.userIds.toList)
      s"Reminded $names to join this data room"
    case a: RemoveUsersFromDataRoomActivity =>
      val names = DataRoomExportUtils.getUserDisplayNameList(userInfoMap)(a.userIds.toList)
      s"Removed $names from this data room"
    case a: UpdateTermsOfAccessToDataRoomActivity =>
      a.toaFileIdOpt.fold(
        "Disabled the NDA for this data room"
      ) { fileId =>
        val fileName = DataRoomExportUtils.getFileName(fileInfoMap)(Some(fileId))
        s"Set $fileName as the new NDA"
      }
    case _: UpdateDataRoomWatermarkActivity =>
      s"Updated this data room's watermark"
    case a: AcceptTermsOfAccessToDataRoomActivity =>
      val fileName = DataRoomExportUtils.getFileName(fileInfoMap)(Some(a.toaFileId))
      s"Accepted this data room's NDA ($fileName)"
    case a: SetLinkInvitationActivity =>
      val action = a.isCreateOpt.fold("Set")(isCreate => if (isCreate) "Created" else "Updated")
      s"$action an invitation link ${a.linkName}"
    case a: DeleteLinkInvitationActivity =>
      s"Removed the invitation link ${a.linkName}"
    case a: JoinViaLinkInvitationActivity =>
      s"Joined this data room via the link ${a.linkName}"
    case a: ChangeShowingIndexSettingActivity =>
      s"Turned this data room's file index ${getSettingText(a.showIndex)}"
    case a: ChangeShowingSearchSettingActivity =>
      s"Turned this data room's search ${getSettingText(a.showSearch)}"
    case a: ChangeShowingHomePageSettingActivity =>
      s"Turned this data room's landing page ${getSettingText(a.showHomePage)}"
    case a: ChangeShowingWhiteLabelSettingActivity =>
      s"Turned this data room's white labelling ${getSettingText(a.showWhiteLabel)}"
    case a: SetDataRoomTermsOfAccessWhitelistedUsersActivity =>
      val names = DataRoomExportUtils.getUserDisplayNameList(userInfoMap)(a.whitelistedUsers.toList)
      s"Updated NDA whitelist to $names"
    case _: RequestAccessDataRoomActivity =>
      s"Requested access to this data room"
    case a: ApproveRequestAccessDataRoomActivity =>
      s"Approved access request from ${a.email}. An invitation email was sent."
    case a: DeclineRequestAccessDataRoomActivity =>
      s"Declined access request from ${a.email}"
    case a: DuplicateDataRoomActivity =>
      s"Duplicated the data room and named it ${a.toDataRoomName}"
    case a: UpdateNewFileNotificationConfigActivity =>
      a.newFileNotificationConfigOpt.fold("") { notificationConfig =>
        val notificationMode = DataRoomNotificationUtils.getNotificationModeName(notificationConfig.notificationMode)
        val notificationFrequency = if (notificationConfig.notificationMode != NotificationMode.DontNotify) {
          s", ${notificationConfig.notificationFrequency.name}"
        } else {
          ""
        }
        s"New file notifications set to ${notificationMode}${notificationFrequency}"
      }
    case _: ChangeSingleDataRoomBillingPlanActivity       => ""
    case _: UpdateDataRoomEmailConfigsActivity            => ""
    case _: UpdateDataRoomWatermarkExceptionFilesActivity => ""
    case _: SetDataRoomPointsOfContactActivity            => ""
    case a: CreateGroupActivity =>
      s"Created ${a.name} group"
    case a: RenameGroupActivity =>
      s"Renamed ${a.oldName} group to ${a.newName}"
    case a: UpdateGroupPermissionsActivity =>
      val groupName = DataRoomExportUtils.getGroupNames(groupMap)(Set(a.groupId))
      s"Changed the settings of $groupName group"
    case a: DeleteGroupActivity =>
      val groupName = DataRoomExportUtils.getGroupNames(groupMap)(Set(a.groupId))
      s"Deleted $groupName group"
    case a: AddUsersToGroupActivity =>
      val names = DataRoomExportUtils.getUserDisplayNameList(userInfoMap)(a.userIds.toList)
      val groupName = DataRoomExportUtils.getGroupNames(groupMap)(Set(a.groupId))
      s"Added $names to $groupName"
    case a: RemoveUsersFromGroupActivity =>
      val names = DataRoomExportUtils.getUserDisplayNameList(userInfoMap)(a.userIds.toList)
      val groupName = DataRoomExportUtils.getGroupNames(groupMap)(Set(a.groupId))
      s"Removed $names from $groupName"
    case a: ChangeEnableWebhookActivity =>
      s"Enable this data room's webhook setting to ${getSettingText(a.enableWebhook)}"
    case _: BindToEnvironmentActivity => ""
  }

}
