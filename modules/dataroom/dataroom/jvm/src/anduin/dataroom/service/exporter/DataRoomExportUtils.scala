// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service.exporter

import java.time.format.DateTimeFormatter
import java.time.{Instant, ZoneOffset}
import scala.annotation.tailrec

import anduin.dataroom.group.state.DataRoomGroupCreatedSharedFlowState
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.{FileId, FolderId}
import anduin.stargazer.service.dataroom.{FileInfo, FolderBasicInfo}
import anduin.utils.DateTimeUtils

object DataRoomExportUtils {

  def convertTimestamp(
    timestampOpt: Option[Instant],
    timezoneOffsetInMinutes: Int,
    format: DateTimeFormatter = DateTimeUtils.MonthDayTimeTimezoneFormatter2
  ): String = {
    timestampOpt.fold("") { timestamp =>
      DateTimeUtils.formatInstant(timestamp, format)(
        using ZoneOffset.ofTotalSeconds(timezoneOffsetInMinutes * 60)
      )
    }
  }

  def getFileDisplayName(fileNameMap: Map[FileId, String])(fileIdOpt: Option[FileId]): String = {
    fileIdOpt.flatMap(fileNameMap.get).getOrElse("a deleted file")
  }

  def getUserDisplayName(userInfoMap: Map[UserId, UserInfo])(userId: UserId): String = {
    userInfoMap.get(userId).map(_.getDisplayName).getOrElse("deleted user")
  }

  def getUserDisplayNameList(userInfoMap: Map[UserId, UserInfo])(userIds: List[UserId]): String = {
    val getName: UserId => String = getUserDisplayName(userInfoMap)
    userIds match {
      case head :: Nil  => getName(head)
      case head :: tail => tail.map(getName).mkString(", ") + " and " + getName(head)
      case _            => ""
    }
  }

  def getFileName(fileMap: Map[FileId, FileInfo])(fileIdOpt: Option[FileId]): String = {
    fileIdOpt.flatMap(fileMap.get).fold("Deleted file") { fileInfo =>
      val deletedSuffix = if (fileInfo.isDeleted) " (deleted)" else ""
      fileInfo.fileName + deletedSuffix
    }
  }

  def getFolderName(folderMap: Map[FolderId, FolderBasicInfo], folderIdOpt: Option[FolderId]): String = {
    folderIdOpt.flatMap(folderMap.get).fold("Unknown folder") { folderInfo =>
      val deletedSuffix = if (folderInfo.isDeleted) " (deleted)" else ""
      folderInfo.folderName + deletedSuffix
    }
  }

  def getGroupNames(
    groupMap: Map[DataRoomGroupId, DataRoomGroupCreatedSharedFlowState]
  )(
    groupIds: Set[DataRoomGroupId]
  ): String = {
    if (groupIds.isEmpty) {
      "Unassigned"
    } else {
      groupIds.flatMap(groupMap.get).map(_.name).mkString(", ")
    }
  }

  @tailrec
  private def getAncestors(folderId: FolderId, res: List[FolderId]): List[FolderId] = {
    folderId.parentFolder match {
      case Some(parentFolderId) => getAncestors(parentFolderId, parentFolderId :: res)
      case None                 => res
    }
  }

  def getAncestors(fileId: FileId): List[FolderId] = {
    getAncestors(fileId.folder, List(fileId.folder))
  }

}
