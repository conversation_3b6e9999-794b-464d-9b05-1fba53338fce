// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service.exporter

import java.time.format.DateTimeFormatter
import java.time.{Duration, Instant, ZoneId, ZoneOffset}
import sttp.model.MediaType
import zio.stream.Stream
import zio.{Task, ZIO}
import anduin.account.profile.UserProfileService
import anduin.dataroom.activity.{
  AcceptTermsOfAccessToDataRoomActivity,
  DataRoomActivity,
  UpdateTermsOfAccessToDataRoomActivity
}
import anduin.dataroom.group.DataRoomGroupStateStoreOperations
import anduin.dataroom.participant.{DataRoomParticipant, DataRoomParticipantOperations}
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.dataroom.service.exporter.ExportPermissionsGenerator.PermissionRow
import anduin.dataroom.service.{DataRoomActivityService, DataRoomServiceUtils}
import anduin.dataroom.state.DataRoomCreatedSharedFlowState
import anduin.dms.DmsFeature
import anduin.dms.file.viewertracking.DmsViewerTrackingService
import anduin.dms.service.{DmsActivityService, FileService}
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.{FileId, FolderId}
import anduin.service.AuthenticatedRequestContext
import anduin.stargazer.service.dataroom.DataRoomExportParticipantsParams.Field
import anduin.storageservice.common.FileContentOrigin
import anduin.team.flow.memberflow.TeamMemberStateStoreOperations
import anduin.utils.{DateTimeUtils, ScalaUtils}
import com.anduin.stargazer.service.api.FileDownloadService
import com.anduin.stargazer.service.file.BatchDownloadRequest
import com.anduin.stargazer.service.utils.ZIOUtils
import anduin.dataroom.flow.*
import anduin.dataroom.service.tracking.DataRoomTrackingService
import anduin.dms.file.state.FileStateStoreOperations
import anduin.fdb.record.model.{RecordIO, RecordReadIO}
import anduin.documentcontent.csv.CsvUtils
import anduin.stargazer.service.dataroom.*
import anduin.team.*
import anduin.user.UserService
import org.apache.commons.io.FileUtils
import anduin.brienne.dataroom.api.external.activity.DataRoomActivityExportField
import anduin.dataroom.service.exporter.DataRoomExportService.*
import anduin.dataroom.service.exporter.DataRoomExportService.ExportGenericActivitiesParams.*
import anduin.dataroom.tracking.{DataRoomActivityType, QueryUserRange}
import anduin.dms.activity.FileActivityType
import anduin.dms.tracking.QueryTimeRange
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.stargazer.service.dataroom.DataRoomExportPermissionsParams.ExportPermissionTarget
import com.anduin.stargazer.service.CommonConfig

final case class DataRoomExportService(
  fileService: FileService,
  fileDownloadService: FileDownloadService,
  userProfileService: UserProfileService,
  dmsViewerTrackingService: DmsViewerTrackingService,
  dmsActivityService: DmsActivityService,
  dataRoomActivityService: DataRoomActivityService,
  dataRoomTrackingService: DataRoomTrackingService,
  userService: UserService,
  commonConfig: CommonConfig
) {

  private val publicApiLinkExpirationTime = commonConfig.s3Config.publicApiLinkExpiration

  private def formatTimestamp(timestampOpt: Option[Instant], timezoneOffsetInMinutes: Int) = {
    timestampOpt.fold("") { timestamp =>
      val str = DateTimeUtils.formatInstant(
        timestamp,
        DateTimeUtils.MonthDayTimeTimezoneFormatter
      )(
        using ZoneOffset.ofTotalSeconds(timezoneOffsetInMinutes * 60)
      )
      s" on $str"
    }
  }

  private def getUserInfoMap(memberStates: Map[UserId, TeamMemberFlowState]) = {
    DataRoomServiceUtils.batchGetUserInfos {
      memberStates.keySet ++ memberStates.collect { case (_, invited: UserInvited) =>
        invited.inviter
      }
    }(
      using userProfileService
    )
  }

  private def getFileInfoMap(fileIds: List[FileId]) = {
    fileService
      .getMultipleFileNameAndDeletedStateUnsafe(fileIds)(
        using DmsFeature.DataRoom
      )
      .map(
        _.transform { case (_, (fileName, isDeleted)) => FileInfo(fileName, isDeleted) }
      )
  }

  private def getFolderNameMap(folderIds: Set[FolderId], maxConcurrent: Int = 4) = {
    ZIOUtils
      .foreachParN(maxConcurrent)(folderIds.toList) { folderId =>
        fileService
          .getFolderNameUnsafe(folderId)(
            using DmsFeature.DataRoom
          )
          .map(folderId -> _)
      }
      .map(_.toMap)
  }

  private def getSortedUsers(userIds: Set[UserId], userInfoMap: Map[UserId, UserInfo]) = {
    userIds.toList.sortBy { userId =>
      userInfoMap.get(userId).fold("")(_.fullNameString)
    }
  }

  private def getRoleMap(
    params: DataRoomExportParticipantsParams,
    participantRoles: Map[UserId, DataRoomRole]
  ) = {
    if (params.fields.contains(Field.Role)) {
      participantRoles.map { case (userId, role) =>
        userId -> DataRoomRoleUtils.getName(role)
      }
    } else {
      Map.empty[UserId, String]
    }
  }

  private def getStatusMap(
    params: DataRoomExportParticipantsParams,
    memberStates: Map[UserId, TeamMemberFlowState],
    userInfoMap: Map[UserId, UserInfo]
  ) = {
    if (params.fields.contains(Field.Status)) {
      for {
        (userId, memberState) <- memberStates
      } yield {
        userId -> {
          memberState match {
            case invited: UserInvited =>
              val inviter = userInfoMap.get(invited.inviter).fold("")(_.fullNameString)
              val invitedAt = formatTimestamp(invited.invitedAt, params.timezoneOffsetInMinutes)
              s"Invited by $inviter$invitedAt"
            case joined: UserJoined =>
              val inviter = userInfoMap.get(joined.inviter).fold("")(_.fullNameString)
              val joinedAt = formatTimestamp(joined.joinedAt, params.timezoneOffsetInMinutes)
              s"Joined$joinedAt. Invited by $inviter"
            case removed: UserRemoved =>
              val remover = userInfoMap.get(removed.removedUser).fold("")(_.fullNameString)
              val removedAt = formatTimestamp(removed.removedAt, params.timezoneOffsetInMinutes)
              s"Removed by $remover$removedAt"
            case _ => ""
          }
        }
      }
    } else {
      Map.empty[UserId, String]
    }
  }

  private def getCertificates(
    params: DataRoomExportParticipantsParams,
    createdState: DataRoomCreatedSharedFlowState
  ) = {
    if (params.fields.contains(Field.TermsOfAccess)) {
      val toaFileIdOpt = createdState.termsOfAccessOptions.filter(_.isEnabled).flatMap(_.versions.lastOption)
      val certificateList = for {
        certificate <- createdState.termsOfAccessCertificates
        if toaFileIdOpt.contains(certificate.toaFileId) && params.userIds.contains(certificate.userId)
        timestamp = formatTimestamp(certificate.timestamp, params.timezoneOffsetInMinutes)
        ipAddress = certificate.ipAddress.fold("")(ip => s". IP address: $ip")
      } yield certificate.userId -> s"Accepted$timestamp$ipAddress"
      certificateList.toMap
    } else {
      Map.empty[UserId, String]
    }
  }

  private def uploadCsv(
    fileName: String,
    csv: Stream[Throwable, Byte],
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext] = None,
    linkExpiration: Option[Duration] = None
  ) = {
    given dmsFeature: DmsFeature = DmsFeature.Public
    for {
      folderId <- fileService.createUserTemporaryFolderIfNeeded(actor)
      fileId <- fileService.uploadFile(
        folderId,
        s"$fileName.csv",
        FileContentOrigin.FromSource(
          csv,
          MediaType(
            "text",
            "csv",
            Some("UTF8")
          )
        ),
        actor
      )
      response <- fileDownloadService.getBatchDownloadData(
        actor,
        BatchDownloadRequest(fileIds = Seq(fileId)),
        ctx,
        linkExpiration = linkExpiration
      )
    } yield response.url
  }

  def exportParticipants(
    params: DataRoomExportParticipantsParams,
    httpContext: AuthenticatedRequestContext
  ): Task[DataRoomExportParticipantsResponse] = {
    given dmsFeature: DmsFeature = DmsFeature.DataRoom
    for {
      _ <- ZIO.logInfo(
        s"Exporting users ${params.userIds} with fields ${params.fields} for data room ${params.dataRoomWorkflowId}."
      )
      _ <- ZIOUtils.validate(params.fields.contains(Field.Name)) {
        new RuntimeException("Must always include Name field")
      }
      removedUserIds <-
        if (params.includeRemovedParticipants) {
          for {
            removedParticipants <- FDBRecordDatabase.transact(DataRoomParticipantOperations.Production)(
              _.getRemovedParticipants(params.dataRoomWorkflowId)
            )
          } yield removedParticipants.map(_.userId)
        } else {
          ZIO.succeed(Seq.empty)
        }
      (createdState, memberStates, groupMap, participantMap, participantRoles, dataRoomCreatedAt) <- FDBRecordDatabase
        .transact(
          FDBOperations[
            (
              (DataRoomValidateOperations, TeamMemberStateStoreOperations),
              (DataRoomGroupStateStoreOperations, DataRoomParticipantOperations),
              DataRoomEventStoreOperations
            )
          ].Production
        ) { case ((validateOps, teamMemberStateOps), (groupStoreOps, participantOps), eventOps) =>
          for {
            (teamId, createdState) <- validateOps.validateAndGetCurrentState(
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              userId = httpContext.actor.userId,
              roleChecks = Set(DataRoomRoleUtils.isAdmin)
            )
            memberStates <- teamMemberStateOps.getAllMemberStates(teamId)
            groupMap <- groupStoreOps.getAllGroupsMap(params.dataRoomWorkflowId)
            participantMap <- participantOps.getParticipantMap(
              params.dataRoomWorkflowId,
              params.includeRemovedParticipants
            )
            participantRoles <- validateOps.roleOps.getAllParticipantRoleMap(params.dataRoomWorkflowId)
            dataRoomCreatedAtOpt <- eventOps.getFirstEvent(params.dataRoomWorkflowId).map(_.timestamp)
            dataRoomCreatedAt <- RecordIO.fromOption(
              dataRoomCreatedAtOpt,
              new RuntimeException(s"Data room ${params.dataRoomWorkflowId} cannot get creation time")
            )
          } yield (createdState, memberStates, groupMap, participantMap, participantRoles, dataRoomCreatedAt)
        }
      totalFileCount <- FDBRecordDatabase.transact(FileStateStoreOperations.Production) {
        _.countAllFilesIncludeDeleted(params.dataRoomWorkflowId)
      }
      userVisitCountMap <- dataRoomTrackingService
        .getAllUserDataRoomVisitCount(params.dataRoomWorkflowId)
        .map(
          _.map(userWithVisitCount => userWithVisitCount.userId -> userWithVisitCount.visitCount).toMap
        )
      userInsightsMap <- dmsViewerTrackingService
        .getDataRoomUserInsights(
          params.dataRoomWorkflowId,
          dataRoomCreatedAt,
          Instant.now
        )
        .map(_.map(userInsight => userInsight.userId -> userInsight).toMap)
      userInfoMap <- getUserInfoMap(memberStates)
      userIds = params.userIds ++ removedUserIds.toSet
      sortedUsers = getSortedUsers(userIds, userInfoMap)
      fields = Field.values.filter(params.fields.contains).toList
      roleMap = getRoleMap(params, participantRoles)
      statusMap = getStatusMap(
        params,
        memberStates,
        userInfoMap
      )
      certificates = getCertificates(params, createdState)
      csv <- ExportParticipantsGenerator(
        keys = sortedUsers,
        fields = fields,
        userInfoMap = userInfoMap,
        roleMap = roleMap,
        statusMap = statusMap,
        groupMap = groupMap,
        participantMap = participantMap,
        userVisitCountMap = userVisitCountMap,
        userInsightsMap = userInsightsMap,
        totalFileCount = totalFileCount,
        certificates = certificates,
        timezoneOffsetInMinutes = params.timezoneOffsetInMinutes
      )()
      url <- uploadCsv(
        s"${createdState.name} participants",
        csv,
        httpContext.actor.userId,
        Some(httpContext)
      )
      _ <- ZIO.logInfo(
        s"Done exporting users ${params.userIds} with fields ${params.fields} for data room ${params.dataRoomWorkflowId}."
      )
    } yield DataRoomExportParticipantsResponse(url)
  }

  def exportPermissionsUnsafe(
    params: DataRoomExportPermissionsParams,
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomExportPermissionsResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"Exporting permissions of ${params.target} on ${params.fileIds} for data room ${params.dataRoomWorkflowId}."
      )
      isUserExport = ScalaUtils.isMatch[ExportPermissionTarget.Users](params.target)
      (createdState, groupMap, participantMap) <- FDBRecordDatabase.transact(
        FDBOperations[
          (
            DataRoomValidateOperations,
            (DataRoomGroupStateStoreOperations, DataRoomParticipantOperations)
          )
        ].Production
      ) { case (validateOps, (groupStoreOps, participantOps)) =>
        for {
          (_, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = ctx.actor.userId,
            roleChecks = Set(DataRoomRoleUtils.isAdmin)
          )
          groupMap <- groupStoreOps.getAllGroupsMap(params.dataRoomWorkflowId)
          participantMap <-
            if (isUserExport) {
              participantOps.getParticipantMap(params.dataRoomWorkflowId)
            } else {
              RecordReadIO.succeed(Map.empty[UserId, DataRoomParticipant])
            }
        } yield (createdState, groupMap, participantMap)
      }
      userInfoMap <-
        if (isUserExport) {
          DataRoomServiceUtils.batchGetUserInfos(params.userIds)(
            using userProfileService
          )
        } else {
          ZIO.succeed(Map.empty[UserId, UserInfo])
        }
      fileInfoMap <- getFileInfoMap(params.fileIds.toList)
      permissionMap <- ZIO
        .foreachPar(params.fileIds) { fileId =>
          fileService
            .getFilePermissionMap(ctx.actor.userId)(fileId)(
              using DmsFeature.DataRoom
            )
            .map(fileId -> _)
        }
        .map(_.toMap)
      folderIds = params.fileIds.flatMap(DataRoomExportUtils.getAncestors)
      folderNameMap <- getFolderNameMap(folderIds)
      hasGroupUser = params.userIds.exists { userId =>
        participantMap.get(userId).exists(_.groupIds.nonEmpty)
      }
      permissionFields = params.target match {
        case ExportPermissionTarget.Users(userIds) =>
          userIds.toList
            .sortBy { userId =>
              val groupName =
                participantMap.get(userId).flatMap(_.groupIds.headOption.flatMap(groupMap.get)).fold("")(_.name)
              val email = userInfoMap.get(userId).fold("")(_.emailAddressStr)
              groupName -> email
            }
            .map(ExportPermissionsGenerator.Field.UserPermission.apply)
        case ExportPermissionTarget.Groups(groupIds) =>
          groupIds.toList
            .sortBy { groupId =>
              groupMap.get(groupId).fold("")(_.name)
            }
            .map(ExportPermissionsGenerator.Field.GroupPermission.apply)
      }
      csv <- ExportPermissionsGenerator(
        keys = List(
          Option.when(hasGroupUser && isUserExport)(PermissionRow.Group),
          params.fileIds.toList.sortBy(_.idString).reverse.map(PermissionRow.File.apply)
        ).flatten,
        fields = List(
          ExportPermissionsGenerator.Field.ParentFolder,
          ExportPermissionsGenerator.Field.FileName
        ) ::: permissionFields,
        userInfoMap = userInfoMap,
        fileInfoMap = fileInfoMap,
        folderNameMap = folderNameMap,
        permissionMap = permissionMap,
        groupMap = groupMap,
        participantMap = participantMap
      )()
      url <- uploadCsv(
        s"${createdState.name} permissions report",
        csv,
        ctx.actor.userId,
        Some(ctx)
      )
      _ <- ZIO.logInfo(
        s"Done exporting permissions of ${params.target} on ${params.fileIds} for data room ${params.dataRoomWorkflowId}."
      )
    } yield DataRoomExportPermissionsResponse(url)
  }

  def exportFileActivities(
    params: ExportFileActivitiesParams,
    httpContext: AuthenticatedRequestContext
  ): Task[ExportFileActivitiesResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${httpContext.actor.userId} is exporting file activities for data room ${params.dataRoomWorkflowId}."
      )
      (createdState, memberStates, groupMap, participantMap) <- FDBRecordDatabase.transact(
        FDBOperations[
          (DataRoomValidateOperations, DataRoomGroupStateStoreOperations, DataRoomParticipantOperations)
        ].Production
      ) { case (validateOps, groupStoreOps, participantOps) =>
        for {
          (teamId, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = httpContext.actor.userId,
            roleChecks = Set(DataRoomRoleUtils.isAdmin)
          )
          memberStates <- validateOps.teamMemberStateOps.getAllMemberStates(teamId)
          groupMap <- groupStoreOps.getAllGroupsMap(params.dataRoomWorkflowId)
          participantMap <- participantOps.getParticipantMap(params.dataRoomWorkflowId)
        } yield (createdState, memberStates, groupMap, participantMap)
      }
      userInfoMap <- getUserInfoMap(memberStates)
      (fromTimestamp, toTimestamp) = QueryTimeRange.toTimestampRange(params.queryTimeRange)
      (activities, fileInfoMap, folderInfoMap) <- dataRoomActivityService
        .getFileActivityLog(
          GetFileActivityLogParams(
            params.dataRoomWorkflowId,
            fromTimestamp,
            toTimestamp,
            Set(params.activityType),
            params.queryUserRange,
            params.queryUserId
          ),
          httpContext.actor.userId
        )
        .map(res => (res.activities, res.fileMap, res.folderMap))
      csv <- ExportFileActivitiesGenerator(
        userInfoMap = userInfoMap,
        fileInfoMap = fileInfoMap,
        folderInfoMap = folderInfoMap,
        timezoneOffsetInMinutes = params.timezoneOffsetInMinutes,
        groupMap = groupMap,
        participantMap = participantMap,
        activities = activities
      )()
      exportDate = DataRoomExportUtils.convertTimestamp(
        Some(Instant.now),
        params.timezoneOffsetInMinutes,
        DateTimeFormatter.ofPattern("MMM dd yyyy")
      )
      url <- uploadCsv(
        s"${createdState.name} documents activities - $exportDate",
        csv,
        httpContext.actor.userId,
        Some(httpContext)
      )
      _ <- ZIO.logInfo(s"Done exporting tracking activities for data room ${params.dataRoomWorkflowId}.")
    } yield ExportFileActivitiesResponse(url)
  }

  def exportDataRoomActivities(
    params: ExportDataRoomActivitiesParams,
    httpContext: AuthenticatedRequestContext
  ): Task[ExportDataRoomActivitiesResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${httpContext.actor.userId} is exporting data room activities for data room ${params.dataRoomWorkflowId}."
      )
      (createdState, memberStates, groupMap, participantMap) <- FDBRecordDatabase.transact(
        FDBOperations[
          (DataRoomValidateOperations, DataRoomGroupStateStoreOperations, DataRoomParticipantOperations)
        ].Production
      ) { case (validateOps, groupStoreOps, participantOps) =>
        for {
          (teamId, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = httpContext.actor.userId,
            roleChecks = Set(DataRoomRoleUtils.isAdmin)
          )
          memberStates <- validateOps.teamMemberStateOps.getAllMemberStates(teamId)
          groupMap <- groupStoreOps.getAllGroupsMap(params.dataRoomWorkflowId, includeArchived = true)
          participantMap <- participantOps.getParticipantMap(params.dataRoomWorkflowId)
        } yield (createdState, memberStates, groupMap, participantMap)
      }
      userInfoMap <- getUserInfoMap(memberStates)
      activities <- dataRoomActivityService
        .getDataRoomActivityLog(
          GetDataRoomActivityLogParams.fromQueryRange(
            params.dataRoomWorkflowId,
            params.queryTimeRange,
            params.activityType,
            params.queryUserRange,
            params.queryUserId
          ),
          httpContext.actor.userId
        )
        .map(_.activities)
      fileIds = fileIdsFromDataRoomActivities(activities)
      fileInfoMap <- getFileInfoMap(fileIds)
      csv <- ExportDataRoomActivitiesGenerator(
        userInfoMap = userInfoMap,
        fileInfoMap = fileInfoMap,
        groupMap = groupMap,
        participantMap = participantMap,
        timezoneOffsetInMinutes = params.timezoneOffsetInMinutes,
        activities = activities
      )()
      exportDate = DataRoomExportUtils.convertTimestamp(
        Some(Instant.now),
        params.timezoneOffsetInMinutes,
        DateTimeFormatter.ofPattern("MMM dd yyyy")
      )
      url <- uploadCsv(
        s"${createdState.name} data room activities - $exportDate",
        csv,
        httpContext.actor.userId,
        Some(httpContext)
      )
      _ <- ZIO.logInfo(s"Done exporting data room activities for data room ${params.dataRoomWorkflowId}.")
    } yield ExportDataRoomActivitiesResponse(url)
  }

  private def getDisplayNameForMappedFolderResult(
    mappedFolderResult: MappedFolderResult
  ): Task[String] = {
    mappedFolderResult match {
      case MappedFolderResult.MatchedFolder(folderId) =>
        fileService.getFolderNameUnsafe(folderId)(
          using DmsFeature.DataRoom
        )
      case _: MappedFolderResult.DuplicatedFolders => ZIO.succeed("Duplicated folders")
      case MappedFolderResult.NoMatchedFolders     => ZIO.succeed("No destination folder")
    }
  }

  private def getNoteForMappedFolderResult(mappedFolderResult: MappedFolderResult): Task[String] = {
    mappedFolderResult match {
      case MappedFolderResult.NoMatchedFolders | _: MappedFolderResult.MatchedFolder => ZIO.succeed("")
      case MappedFolderResult.DuplicatedFolders(folderIds) =>
        for {
          folderNames <- fileService
            .getFolderNamesUnsafe(folderIds)(
              using DmsFeature.DataRoom
            )
            .map(_.values)
        } yield "Detected folders: " + folderNames.mkString(", ")
    }
  }

  def exportReportForUploadAndAutoImportFiles(
    params: ExportReportForUploadAndAutoImportFilesParams,
    ctx: AuthenticatedRequestContext
  ): Task[ExportReportForUploadAndAutoImportFilesResponse] = {
    val actorId = ctx.actor.userId
    given dmsFeature: DmsFeature = DmsFeature.Public
    for {
      _ <- ZIO.logInfo(s"User $actorId exports report for upload and auto import files")
      headerRow <- ZIO.attempt(
        List(
          "File name",
          "Size",
          "Detected folder",
          "Uploaded folder",
          "Upload status",
          "Note"
        )
      )
      dataRows <- ZIO.foreach(params.fileUploadAndImportResults.toList) { fileResult =>
        for {
          mappedFolderResultDisplayName <- getDisplayNameForMappedFolderResult(fileResult.mappedFolderResult)
          uploadedFolderName <- fileResult.fileIdOpt.fold {
            ZIO.succeed("")
          } { fileId =>
            fileService.getFolderNameUnsafe(fileId.folder)(
              using DmsFeature.DataRoom
            )
          }
          note <- getNoteForMappedFolderResult(fileResult.mappedFolderResult)
        } yield List(
          fileResult.fileName,
          FileUtils.byteCountToDisplaySize(fileResult.size),
          mappedFolderResultDisplayName,
          uploadedFolderName,
          if (fileResult.isUploadSuccessful) "Successful" else "Failed",
          note
        )
      }
      reportSource <- CsvUtils.createCsvTask(
        headerRow +: dataRows
      )
      folderToUpload <-
        fileService.createUserTemporaryFolderIfNeeded(
          actorId
        )
      userZoneId <- userService.getUserTimeZone(actorId).map(_.getOrElse(DateTimeUtils.defaultTimezone))
      formattedDateTime = DateTimeUtils.formatInstant(
        Instant.now,
        DateTimeFormatter.ofPattern("yyyy-MM-dd hh-mm-a")
      )(
        using userZoneId
      )
      fileId <- fileService.uploadFile(
        folderToUpload,
        s"Upload_and_auto_import_report_$formattedDateTime.csv",
        FileContentOrigin.FromSource(reportSource, MediaType.TextCsv),
        ctx.actor.userId
      )
    } yield ExportReportForUploadAndAutoImportFilesResponse(fileId)
  }

  def exportReportForFolderMappingResults(
    params: ExportReportForFolderMappingResultsParams,
    ctx: AuthenticatedRequestContext
  ): Task[ExportReportForFolderMappingResultsResponse] = {
    given dmsFeature: DmsFeature = DmsFeature.Public
    val actorId = ctx.actor.userId
    for {
      _ <- ZIO.logInfo(s"User $actorId exports report for folder mapping results")
      headerRow <- ZIO.attempt(
        List(
          "File name",
          "Size",
          "Detected folder",
          "Note"
        )
      )
      dataRows <- ZIO.foreach(params.folderMappingResults.toList) { mappedFolderResult =>
        for {
          mappedFolderResultDisplayName <- getDisplayNameForMappedFolderResult(mappedFolderResult.mappedFolderResult)
          note <- getNoteForMappedFolderResult(mappedFolderResult.mappedFolderResult)
        } yield List(
          mappedFolderResult.fileName,
          FileUtils.byteCountToDisplaySize(mappedFolderResult.size),
          mappedFolderResultDisplayName,
          note
        )
      }
      reportSource <- CsvUtils.createCsvTask(
        headerRow +: dataRows
      )
      folderToUpload <- fileService.createUserTemporaryFolderIfNeeded(actorId)
      userZoneId <- userService.getUserTimeZone(actorId).map(_.getOrElse(DateTimeUtils.defaultTimezone))
      formattedDateTime = DateTimeUtils.formatInstant(
        Instant.now,
        DateTimeFormatter.ofPattern("yyyy-MM-dd hh-mm-a")
      )(
        using userZoneId
      )
      fileId <- fileService.uploadFile(
        folderToUpload,
        s"Mapping_report_$formattedDateTime.csv",
        FileContentOrigin.FromSource(reportSource, MediaType.TextCsv),
        ctx.actor.userId
      )
    } yield ExportReportForFolderMappingResultsResponse(fileId)
  }

  def exportGenericActivities(
    params: ExportGenericActivitiesParams,
    actor: UserId
  ): Task[ExportGenericActivitiesResponse] = {
    for {
      _ <- ZIO.logInfo(s"Export generic activities inside data room ${params.dataRoomWorkflowId}")
      (createdState, memberStates, groupMap, participantMap, participantRoles) <- FDBRecordDatabase.transact(
        FDBOperations[
          (DataRoomValidateOperations, DataRoomGroupStateStoreOperations, DataRoomParticipantOperations)
        ].Production
      ) { case (validateOps, groupStoreOps, participantOps) =>
        for {
          (teamId, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = actor,
            roleChecks = Set(DataRoomRoleUtils.isAdmin)
          )
          memberStates <- validateOps.teamMemberStateOps.getAllMemberStates(teamId)
          groupMap <- groupStoreOps.getAllGroupsMap(params.dataRoomWorkflowId, includeArchived = true)
          participantMap <- participantOps.getParticipantMap(params.dataRoomWorkflowId)
          participantRoles <- validateOps.roleOps.getAllParticipantRoleMap(params.dataRoomWorkflowId)
        } yield (createdState, memberStates, groupMap, participantMap, participantRoles)
      }
      userInfoMap <- getUserInfoMap(memberStates)
      (contentActivities, fileInfoMap, folderInfoMap) <- dataRoomActivityService
        .getFileActivityLog(
          GetFileActivityLogParams(
            params.dataRoomWorkflowId,
            params.fromTimestamp,
            params.toTimestamp,
            params.filterFileFolderActivities,
            QueryUserRange.AllUsers,
            queryUserId = None
          ),
          actor
        )
        .map(res => (res.activities, res.fileMap, res.folderMap))
      filteredContentActivities = contentActivities.filter { activity =>
        params.isValidActor(Some(activity.actor), participantMap, participantRoles)
        && params.isValidFileId(activity.fileIdOpt)
      }
      dataRoomActivities <- dataRoomActivityService
        .getDataRoomActivityLog(
          GetDataRoomActivityLogParams(
            params.dataRoomWorkflowId,
            params.fromTimestamp,
            params.toTimestamp,
            activityTypes = params.filterDataRoomActivities
          ),
          actor
        )
        .map(_.activities)
      filteredDataRoomActivities = dataRoomActivities.filter { activity =>
        params.isValidActor(activity.actor, participantMap, participantRoles)
        && params.isValidFileId(fileId = None)
      }
      fileIdsWithDataRoomActivity = fileIdsFromDataRoomActivities(filteredDataRoomActivities)
      fileDataRoomActivityInfoMap <- getFileInfoMap(fileIdsWithDataRoomActivity)
      given ZoneId = params.zoneId
      csv <- ExportGenericActivitiesGenerator(
        fileActivities = filteredContentActivities,
        dataRoomActivities = filteredDataRoomActivities,
        userInfoMap = userInfoMap,
        fileInfoMap = fileInfoMap ++ fileDataRoomActivityInfoMap,
        folderInfoMap = folderInfoMap,
        groupMap = groupMap,
        participantMap = participantMap,
        fields = params.refinedExportFields
      )()
      exportDate = DateTimeUtils.formatInstant(
        Instant.now,
        DateTimeFormatter.ofPattern("MMM dd yyyy")
      )
      url <- uploadCsv(
        s"${createdState.name} activities - $exportDate",
        csv,
        actor,
        linkExpiration = Some(publicApiLinkExpirationTime)
      )
      _ <- ZIO.logInfo(s"Done exporting generic activities inside data room ${params.dataRoomWorkflowId}")
    } yield ExportGenericActivitiesResponse(url)
  }

  private def fileIdsFromDataRoomActivities(
    dataRoomActivities: Seq[DataRoomActivity]
  ): List[FileId] = {
    dataRoomActivities.flatMap {
      case a: UpdateTermsOfAccessToDataRoomActivity => a.toaFileIdOpt
      case a: AcceptTermsOfAccessToDataRoomActivity => Some(a.toaFileId)
      case _                                        => None
    }.toList
  }

}

object DataRoomExportService {

  final case class ExportGenericActivitiesParams(
    dataRoomWorkflowId: DataRoomWorkflowId,
    filterDataRoomActivities: Set[DataRoomActivityType],
    filterFileFolderActivities: Set[FileActivityType],
    zoneId: ZoneId,
    fromTimestamp: Option[Instant],
    toTimestamp: Option[Instant],
    filterByRole: Option[Set[DataRoomRole]],
    filterUser: Option[Set[UserId]],
    filterGroup: Option[Set[DataRoomGroupId]],
    filterFileId: Option[Set[FileId]],
    exportFields: Option[List[DataRoomActivityExportField]]
  ) {

    private lazy val hasActorFilter = filterByRole.nonEmpty || filterUser.nonEmpty || filterGroup.nonEmpty
    private lazy val hasFileFilter = filterFileId.nonEmpty

    def isValidActor(
      actorOpt: Option[UserId],
      participantMap: Map[UserId, DataRoomParticipant],
      participantRoles: Map[UserId, DataRoomRole]
    ): Boolean = {
      actorOpt.fold {
        // If there is no applied filter, we allow anonymous activities.
        !hasActorFilter
      } { actor =>
        filterByRole.forall(_.exists(role => roleCheck(actor, role, participantRoles)))
        && filterUser.forall(_.contains(actor))
        && filterGroup.forall(_.exists(groupId => groupCheck(actor, groupId, participantMap)))
      }
    }

    def isValidFileId(fileId: Option[FileId]): Boolean = {
      fileId.fold(!hasFileFilter) { fileId =>
        filterFileId.forall(_.contains(fileId))
      }
    }

    lazy val refinedExportFields: List[DataRoomActivityExportField] = {
      exportFields.getOrElse {
        val onlyDataRoomActivities = filterFileFolderActivities.isEmpty
        if (onlyDataRoomActivities) {
          DataRoomActivitiesFields
        } else {
          DataRoomActivityExportField.values.toList
        }
      }
    }

  }

  object ExportGenericActivitiesParams {

    private def roleCheck(
      actor: UserId,
      targetRole: DataRoomRole,
      participantRoles: Map[UserId, DataRoomRole]
    ): Boolean = {
      participantRoles.get(actor).contains(targetRole)
    }

    private def groupCheck(
      actor: UserId,
      groupId: DataRoomGroupId,
      participantMap: Map[UserId, DataRoomParticipant]
    ): Boolean = {
      participantMap.get(actor).map(_.groupIds).exists(_.contains(groupId))
    }

    private val DataRoomActivitiesFields = List(
      DataRoomActivityExportField.Timestamp,
      DataRoomActivityExportField.ActorName,
      DataRoomActivityExportField.ActorEmail,
      DataRoomActivityExportField.ActorEmailDomain,
      DataRoomActivityExportField.ActorIp,
      DataRoomActivityExportField.Group,
      DataRoomActivityExportField.Description
    )

  }

  final case class ExportGenericActivitiesResponse(
    url: String
  )

}
