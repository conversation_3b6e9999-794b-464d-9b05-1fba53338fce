// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service

import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.contact.endpoint.{ContactGroupInfo, ContactInfo}
import anduin.contact.internal.fdb.ContactOperations
import anduin.contact.service.core.ContactService.ContactQueryType
import anduin.contact.service.core.{ContactGroupService, ContactService}
import anduin.dataroom.flow.{DataRoomFlowOperations, DataRoomSharedFlowService, DataRoomValidateOperations}
import anduin.dataroom.participant.DataRoomParticipantRoleOperations
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.dataroom.state.DataRoomCreatedSharedFlowState
import anduin.dataroom.whitelabel.{DataRoomAuthenticationWhiteLabelOperations, DataRoomWhiteLabelService}
import anduin.fdb.client.io.FDBIO
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.id.contact.ContactGroupId
import anduin.id.role.UserRoleId
import anduin.id.user.UserRestrictedId
import anduin.model.common.user.UserId
import anduin.model.id.stage.{DataRoomContactRoleId, DataRoomWorkflowId}
import anduin.role.CompositeRoleService
import anduin.service.AuthenticatedRequestContext
import anduin.stargazer.service.dataroom.{DataRoomEmptyResponse, SetDataRoomPointsOfContactParams}
import anduin.whitelabel.auth.AuthenticationWhitelabelStoreOperations
import com.anduin.stargazer.service.utils.ZIOUtils

final case class DataRoomContactService(
  contactService: ContactService,
  contactGroupService: ContactGroupService,
  dataRoomSharedFlowService: DataRoomSharedFlowService,
  dataRoomWhiteLabelService: DataRoomWhiteLabelService
)(
  using val userProfileService: UserProfileService
) {

  def syncContactRole(
    dataRoomWorkflowId: DataRoomWorkflowId,
    removed: Set[UserId] = Set(),
    added: Set[UserId] = Set()
  ): FDBIO[Unit] = {
    for {
      _ <- CompositeRoleService.removeParentComposite(
        DataRoomContactRoleId(dataRoomWorkflowId),
        removed.map(userId => UserRoleId(UserRestrictedId(userId)))
      )
      _ <- FDBIO.parTraverseN(4)(added) { userId =>
        CompositeRoleService.addComposite(
          UserRoleId(UserRestrictedId(userId)),
          Set(DataRoomContactRoleId(dataRoomWorkflowId))
        )
      }
    } yield ()
  }

  private def getActor(
    preferredActor: UserId,
    participantRoles: Map[UserId, DataRoomRole]
  ) = {
    if (participantRoles.get(preferredActor).exists(DataRoomRoleUtils.isAdmin)) {
      ZIO.succeed(preferredActor)
    } else {
      ZIOUtils.optionToTask(
        participantRoles.collectFirst {
          case (userId, role) if DataRoomRoleUtils.isAdmin(role) => userId
        },
        new RuntimeException(s"Missing admin")
      )
    }
  }

  def updateDataRoomGroupName(
    actor: UserId,
    dataRoomWorkflowId: DataRoomWorkflowId,
    dataRoomName: String
  ): Task[Unit] = {
    val contactGroupId = ContactGroupId.defaultSpaceGroupId(dataRoomWorkflowId)
    for {
      _ <- FDBRecordDatabase.transactC(
        FDBOperations[
          (ContactOperations, (DataRoomAuthenticationWhiteLabelOperations, AuthenticationWhitelabelStoreOperations))
        ].Production
      ) { case (ctx, (contactOps, (drAuthWhiteLabelOps, authWhiteLabelOps))) =>
        for {
          _ <- contactOps.updateGroup(
            actor,
            contactGroupId,
            ContactGroupInfo(s"Data Room: $dataRoomName", "")
          )(
            using ctx
          )
          _ <- dataRoomWhiteLabelService.updateAuthenticationWhitelabelEmailHeaderTitle(
            dataRoomName,
            authWhiteLabelOps,
            drAuthWhiteLabelOps,
            dataRoomWorkflowId
          )
        } yield ()
      }
      _ <- ZIO.logInfo(s"$actor update data room group name ${dataRoomWorkflowId.parent.idString} to $dataRoomName")
    } yield ()
  }

  def addContactsToDataRoom(
    preferredActor: UserId,
    userIds: Set[UserId],
    dataRoomWorkflowId: DataRoomWorkflowId
  ): Task[Unit] = {
    val contactGroupId = ContactGroupId.defaultSpaceGroupId(dataRoomWorkflowId)
    for {
      /*
        Since data room invitation doesn't require invitee name when inviting, we will try to get user info from
        userProfileService.
        If user is new who doesn't have name, this data will be updated when user accept invitation
       */
      userInfos <- DataRoomServiceUtils.batchGetUserInfos(userIds)
      participantRoles <- DataRoomParticipantRoleOperations.transact(_.getAllParticipantRoleMap(dataRoomWorkflowId))
      actor <- getActor(preferredActor, participantRoles)
      _ <- FDBRecordDatabase.transactC(ContactOperations.Production) { case (ctx, contactOps) =>
        for {
          _ <- RecordIO.traverse(userInfos.toList) { case (userId, userInfo) =>
            contactOps.createNewContact(
              actor,
              contactGroupId,
              contactInfo = ContactInfo.fromUserInfo(userId, userInfo)
            )(
              using ctx
            )
          }
        } yield ()
      }
      _ <- ZIO.logInfo(s"$userIds is added to contact of data room ${dataRoomWorkflowId.idString}")
    } yield ()
  }

  // Update user info when this user accepts data room invitation
  def updateUserInfoAfterAcceptedInvitation(
    preferredActor: UserId,
    createdState: DataRoomCreatedSharedFlowState,
    target: UserId,
    dataRoomWorkflowId: DataRoomWorkflowId
  ): Task[Unit] = {
    for {
      userInfo <- userProfileService.getUserInfo(target)
      participantRoles <- DataRoomParticipantRoleOperations.transact(_.getAllParticipantRoleMap(dataRoomWorkflowId))
      actor <- getActor(preferredActor, participantRoles)
      contacts <- contactService.queryContactsInSpaces(
        actor = actor,
        spaces = List(dataRoomWorkflowId),
        queryType = ContactQueryType.ByEmailExact(userInfo.emailAddressStr)
      )
      _ <- FDBRecordDatabase.transactC(ContactOperations.Production) { case (ctx, contactOps) =>
        for {
          _ <- RecordIO.parTraverseN(2)(contacts._1) { contact =>
            // Only update when this user doesn't have name
            RecordIO.when(contact.contactInfo.fullName == "") {
              contactOps.updateContacts(actor, Seq((contact.contactId, ContactInfo.fromUserInfo(target, userInfo))))(
                using ctx
              )
            }
          }
        } yield ()
      }
    } yield ()
  }

  def setDataRoomPointsOfContact(
    params: SetDataRoomPointsOfContactParams,
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomEmptyResponse] = {
    for {
      _ <- FDBRecordDatabase.transact(
        FDBOperations[(DataRoomValidateOperations, DataRoomFlowOperations)].Production
      ) { case (validateOps, flowOps) =>
        for {
          _ <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = ctx.actor.userId,
            roleChecks = Set(DataRoomRoleUtils.isAdmin)
          )
          participantRoles <- validateOps.roleOps.getAllParticipantRoleMap(params.dataRoomWorkflowId)
          internalRoleMap = validateOps.filterByRoles(participantRoles, Set(DataRoomRoleUtils.isInternal))
          _ <- RecordIO.validate(
            params.contactUserIds.forall(internalRoleMap.contains)
          ) {
            new RuntimeException(s"Points of contact ${params.contactUserIds} are not internal members")
          }
          _ <- flowOps.setDataRoomPointsOfContact(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            actor = ctx.actor.userId,
            actorIp = ctx.getClientIP,
            contactUserIds = params.contactUserIds
          )
        } yield ()
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
    } yield DataRoomEmptyResponse()
  }

  def getDataRoomPointsOfContact(
    dataRoomWorkflowId: DataRoomWorkflowId,
    actor: UserId
  ): Task[(Set[UserId], Boolean)] = {
    for {
      contactUserIds <- FDBRecordDatabase.transact(
        FDBOperations[DataRoomValidateOperations].Production
      ) { validateOps =>
        for {
          (_, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = dataRoomWorkflowId,
            userId = actor,
            roleChecks = Set()
          )
          participantRoles <- validateOps.roleOps.getAllParticipantRoleMap(dataRoomWorkflowId)
          contactUserIds =
            if (createdState.contactUserIds.nonEmpty) {
              createdState.contactUserIds -> false
            } else {
              validateOps.filterByRoles(participantRoles, Set(DataRoomRoleUtils.isAdmin)) -> true
            }
        } yield contactUserIds
      }
    } yield contactUserIds
  }

}
