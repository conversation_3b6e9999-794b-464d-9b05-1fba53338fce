// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service.webhook

import io.circe.syntax.*
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.dataroom.flow.DataRoomValidateOperations
import anduin.dataroom.flow.DataRoomValidateOperations.MemberStateCheck
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.dataroom.service.webhook.DataRoomIntegrationWebhookServiceImpl.BatchUserPerPayload
import anduin.dataroom.webhook.*
import anduin.fdb.record.FDBRecordDatabase
import anduin.integration.service.linkage.IntegrationWebhookExternalService
import anduin.integration.service.linkage.IntegrationWebhookExternalService.WebhookEventMessage
import anduin.kafka.{KafkaService, Topic}
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.webhook.DataRoomEventType
import com.anduin.stargazer.service.GondorConfig

final case class DataRoomIntegrationWebhookServiceImpl(
  kafkaService: KafkaService,
  gondorConfig: GondorConfig,
  userProfileService: UserProfileService
) extends IntegrationWebhookExternalService[DataRoomWorkflowId, DataRoomWebhookEvent] {

  override def appEventTopic: Topic[DataRoomWorkflowId, DataRoomWebhookEvent] = Topic(
    gondorConfig.publicApiConfig.webhookConfig.dataRoomPayloadKafkaTopic
  )

  /** Some events may produce multiple EventTypes inherently, e.g Remove users may trigger User Move Group event.
    */
  // TODO: @dungdq refactor to make event payload typesafe!!
  override def handleAppEvent(
    serviceAccount: UserId,
    dataRoomId: DataRoomWorkflowId,
    appEvent: DataRoomWebhookEvent
  ): Task[List[WebhookEventMessage]] = {
    val dataRoomIdPayload = Seq(
      "dataRoomId" -> dataRoomId.idString.asJson
    )
    for {
      _ <- FDBRecordDatabase.transact(DataRoomValidateOperations.Production)(
        _.validateAndGetCurrentState(
          dataRoomId,
          serviceAccount,
          Set(DataRoomRoleUtils.isAdmin),
          MemberStateCheck.RequireJoined
        )
      )
      events <- appEvent match {
        case DataRoomWebhookEvent.Empty => ZIO.succeed(List.empty)
        case e: DataRoomUserJoinWebhookEvent =>
          for {
            email <- userProfileService.getEmailAddress(e.userId)
            event = WebhookEventMessage(
              eventType = DataRoomEventType.UserJoin,
              metadata = dataRoomIdPayload ++ Seq(
                "email" -> email.address.asJson,
                "groupIds" -> e.groupIds.asJson
              )
            )
          } yield List(event)
        case e: DataRoomUserAddToGroupWebhookEvent =>
          for {
            emails <- userProfileService
              .batchGetUserInfos(e.userIds.toSet)
              .map(_.values.map(_.emailAddressStr))
            events = emails.grouped(BatchUserPerPayload).map { emails =>
              WebhookEventMessage(
                eventType = DataRoomEventType.UserAddToGroup,
                metadata = dataRoomIdPayload ++ Seq(
                  "emails" -> emails.asJson,
                  "groupId" -> e.groupId.asJson
                )
              )
            }
          } yield events.toList
        case e: DataRoomUserRemoveFromGroupWebhookEvent =>
          for {
            emails <- userProfileService
              .batchGetUserInfos(e.userIds.toSet)
              .map(_.values.map(_.emailAddressStr))
            events = emails.grouped(BatchUserPerPayload).map { emails =>
              WebhookEventMessage(
                eventType = DataRoomEventType.UserRemoveFromGroup,
                metadata = dataRoomIdPayload ++ Seq(
                  "emails" -> emails.asJson,
                  "groupId" -> e.groupId.asJson
                )
              )
            }
          } yield events.toList
        case e: DataRoomUserRemovedWebhookEvent =>
          for {
            emails <- userProfileService
              .batchGetUserInfos(e.userIds.toSet)
              .map(_.values.map(_.emailAddressStr))
            events = emails.grouped(BatchUserPerPayload).map { emails =>
              WebhookEventMessage(
                eventType = DataRoomEventType.UserRemoved,
                metadata = dataRoomIdPayload ++ Seq(
                  "emails" -> emails.asJson
                )
              )
            }
          } yield events.toList
        case e: DataRoomUserInvitedWebhookEvent =>
          for {
            emails <- userProfileService
              .batchGetUserInfos(e.userIds.toSet)
              .map(_.values.map(_.emailAddressStr))
            events = emails.grouped(BatchUserPerPayload).map { emails =>
              WebhookEventMessage(
                eventType = DataRoomEventType.UserInvited,
                metadata = dataRoomIdPayload ++ Seq(
                  "emails" -> emails.asJson,
                  "groupIds" -> e.groupIds.asJson
                )
              )
            }
            addGroupEvents = e.groupIds.toList
              .flatMap { groupId =>
                emails.grouped(BatchUserPerPayload).map { emails =>
                  WebhookEventMessage(
                    eventType = DataRoomEventType.UserAddToGroup,
                    metadata = dataRoomIdPayload ++ Seq(
                      "emails" -> emails.asJson,
                      "groupId" -> groupId.asJson
                    )
                  )
                }
              }
          } yield events.toList ++ addGroupEvents
        case e: DataRoomUserDeclineInvitationWebhookEvent =>
          for {
            email <- userProfileService.getEmailAddress(e.userId)
            event = WebhookEventMessage(
              eventType = DataRoomEventType.UserDeclineInvitation,
              metadata = dataRoomIdPayload ++ Seq(
                "email" -> email.address.asJson,
                "groupIds" -> e.groupIds.asJson
              )
            )
            moveGroupEventOpt = e.groupIds.map { groupId =>
              WebhookEventMessage(
                eventType = DataRoomEventType.UserRemoveFromGroup,
                metadata = dataRoomIdPayload ++ Seq(
                  "email" -> email.address.asJson,
                  "groupId" -> groupId.asJson
                )
              )
            }
          } yield List(event) ++ moveGroupEventOpt.toList
        case e: DataRoomGroupDeletedWebhookEvent =>
          for {
            emails <- userProfileService
              .batchGetUserInfos(e.userIds.toSet)
              .map(_.values.map(_.emailAddressStr))
            events = emails.grouped(BatchUserPerPayload).map { emails =>
              WebhookEventMessage(
                eventType = DataRoomEventType.UserRemoveFromGroup,
                metadata = dataRoomIdPayload ++ Seq(
                  "emails" -> emails.asJson,
                  "groupId" -> e.groupId.asJson
                )
              )
            }
          } yield events.toList
      }
    } yield events
  }

}

object DataRoomIntegrationWebhookServiceImpl {
  private val BatchUserPerPayload = 100
}
