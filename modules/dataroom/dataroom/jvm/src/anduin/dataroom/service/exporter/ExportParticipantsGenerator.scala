// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service.exporter

import java.time.Instant

import anduin.dataroom.group.state.DataRoomGroupCreatedSharedFlowState
import anduin.dataroom.participant.DataRoomParticipant
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.{UserId, UserInfo}
import anduin.stargazer.service.dataroom.DataRoomExportParticipantsParams.Field
import anduin.dms.file.viewertracking.query.DataRoomUsersInsight.UserInsights
import anduin.utils.DateTimeUtils
import scala.math.Ordering.Option

import anduin.dataroom.service.exporter.ExportParticipantsGenerator.{DownloadAction, UserAction, ViewAction}

private[exporter] final case class ExportParticipantsGenerator(
  keys: List[UserId],
  fields: List[Field],
  userInfoMap: Map[UserId, UserInfo],
  roleMap: Map[UserId, String],
  statusMap: Map[UserId, String],
  groupMap: Map[DataRoomGroupId, DataRoomGroupCreatedSharedFlowState],
  participantMap: Map[UserId, DataRoomParticipant],
  userVisitCountMap: Map[UserId, Long],
  userInsightsMap: Map[UserId, UserInsights],
  totalFileCount: Long,
  certificates: Map[UserId, String],
  timezoneOffsetInMinutes: Int
) extends ExportGenerator[UserId, Field] {

  protected def getRow(userId: UserId): Extract = {
    case Field.Name =>
      userInfoMap.get(userId).fold(List("", "")) { userInfo =>
        List(userInfo.firstName, userInfo.lastName)
      }
    case Field.Email =>
      userInfoMap.get(userId).fold(List("")) { userInfo =>
        List(userInfo.emailAddressStr)
      }
    case Field.Group =>
      List(
        DataRoomExportUtils.getGroupNames(groupMap)(participantMap.get(userId).map(_.groupIds).getOrElse(Set.empty))
      )
    case Field.Role =>
      List(roleMap.getOrElse(userId, ""))
    case Field.RemovedMember =>
      val isRemoved = participantMap.get(userId).exists(_.isRemoved == true)
      List(if (isRemoved) "Yes" else "")
    case Field.VisitCount =>
      List(userVisitCountMap.get(userId).fold("")(_.toString))
    case Field.AccessCount =>
      List(s"${getAccessPercent(userId)}%")
    case Field.TotalTimeSpent =>
      val timeSpentOpt = userInsightsMap
        .get(userId)
        .flatMap(_.viewDuration)
      List(timeSpentOpt.fold("")(timeSpent => s"${DateTimeUtils.formatDurationHMS(timeSpent)}"))
    case Field.LastAction =>
      List(getLastAction(userId))
    case Field.Status =>
      List(statusMap.getOrElse(userId, ""))
    case Field.TermsOfAccess =>
      List(certificates.getOrElse(userId, "Pending"))
  }

  private def getAccessPercent(userId: UserId) = {
    val accessCount = userInsightsMap.get(userId).map(_.accessedFiles).getOrElse(0L)
    if (totalFileCount != 0) {
      (accessCount.toDouble * 100 / totalFileCount).round
    } else {
      0
    }
  }

  private def getLastAction(userId: UserId): String = {
    val lastViewAction = ViewAction(userInsightsMap.get(userId).flatMap(_.lastView))
    val lastDownloadAction = DownloadAction(userInsightsMap.get(userId).flatMap(_.lastDownload))
    val lastUserAction: UserAction = Seq(lastViewAction, lastDownloadAction).maxBy(_.timestamp)
    lastUserAction.timestamp.fold("") { timestamp =>
      s"${lastUserAction.label} ${DateTimeUtils.formatInstant(
          timestamp,
          DateTimeUtils.MonthDayTimeTimezoneFormatter
        )(
          using DateTimeUtils.timezoneByMinuteOffset(timezoneOffsetInMinutes)
        )}"
    }
  }

  protected val getHeader: Extract = {
    case Field.Name => List("First name", "Last name")
    case field @ _  => List(Field.getName(field))
  }

}

private[exporter] object ExportParticipantsGenerator {

  sealed trait UserAction {
    def timestamp: Option[Instant]
    def label: String
  }

  final case class ViewAction(timestamp: Option[Instant]) extends UserAction {
    override def label: String = "View"
  }

  final case class DownloadAction(timestamp: Option[Instant]) extends UserAction {
    override def label: String = "Download"
  }

}
