// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service.exporter

import anduin.dataroom.group.state.DataRoomGroupCreatedSharedFlowState
import anduin.dataroom.participant.DataRoomParticipant
import anduin.dataroom.service.exporter.ExportPermissionsGenerator.{Field, PermissionRow}
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.{FileId, FolderId, TeamId}
import anduin.protobuf.flow.file.FileFolderPermissionMap
import anduin.stargazer.service.dataroom.FileInfo
import com.anduin.stargazer.utils.FileFolderPermissionUtils

private[exporter] final case class ExportPermissionsGenerator(
  keys: List[PermissionRow],
  fields: List[Field],
  userInfoMap: Map[UserId, UserInfo],
  fileInfoMap: Map[FileId, FileInfo],
  folderNameMap: Map[FolderId, String],
  permissionMap: Map[FileId, FileFolderPermissionMap],
  groupMap: Map[DataRoomGroupId, DataRoomGroupCreatedSharedFlowState],
  participantMap: Map[UserId, DataRoomParticipant]
) extends ExportGenerator[PermissionRow, Field] {

  private val userTeamMap = participantMap.map { case (userId, participantModel) =>
    userId -> participantModel.groupIds.flatMap(groupId => groupMap.get(groupId).map(_.teamId))
  }

  protected def getFileRow(fileId: FileId): Extract = {
    case Field.ParentFolder => List(getParentFolder(fileId))
    case Field.FileName     => List(fileInfoMap.get(fileId).map(_.fileName).getOrElse(""))
    case Field.UserPermission(userId) =>
      List(
        getUserPermission(fileId, userId)
          .map(FileFolderPermissionUtils.getPermissionName)
          .getOrElse("No access")
      )
    case Field.GroupPermission(groupId) =>
      List(
        getGroupPermission(fileId, groupId)
          .map(FileFolderPermissionUtils.getPermissionName)
          .getOrElse("No access")
      )
  }

  protected def getGroupRow: Extract = {
    case Field.ParentFolder => List("")
    case Field.FileName     => List("")
    case Field.UserPermission(userId) =>
      List(
        DataRoomExportUtils.getGroupNames(groupMap)(participantMap.get(userId).map(_.groupIds).getOrElse(Set.empty))
      )
    case _: Field.GroupPermission => List("")
  }

  protected def getRow(row: PermissionRow): Extract = {
    row match {
      case PermissionRow.Group        => getGroupRow
      case PermissionRow.File(fileId) => getFileRow(fileId)
    }
  }

  private def getParentFolder(fileId: FileId): String = {
    DataRoomExportUtils.getAncestors(fileId).map(folderNameMap).mkString("/")
  }

  private def getUserPermission(fileId: FileId, userId: UserId) = {
    val filePermissionMap = permissionMap.get(fileId)
    val individualPermission = filePermissionMap.flatMap(_.userPermissions.get(userId))
    val teamPermissions =
      userTeamMap
        .getOrElse(userId, List.empty[TeamId])
        .map(teamId => filePermissionMap.flatMap(_.teamPermissions.get(teamId)))
        .toSet
    (teamPermissions + individualPermission).maxBy(_.map(_.value))
  }

  private def getGroupPermission(fileId: FileId, groupId: DataRoomGroupId) = {
    val filePermissionMap = permissionMap.get(fileId)
    groupMap.get(groupId).map(_.teamId).flatMap(teamId => filePermissionMap.flatMap(_.teamPermissions.get(teamId)))
  }

  protected val getHeader: Extract = {
    case Field.ParentFolder             => List("Folder")
    case Field.FileName                 => List("File")
    case Field.UserPermission(userId)   => List(userInfoMap.get(userId).map(_.emailAddressStr).getOrElse(""))
    case Field.GroupPermission(groupId) => List(groupMap.get(groupId).map(_.name).getOrElse(""))
  }

}

object ExportPermissionsGenerator {

  sealed trait Field derives CanEqual

  object Field {
    case object ParentFolder extends Field
    case object FileName extends Field
    final case class UserPermission(userId: UserId) extends Field
    final case class GroupPermission(groupId: DataRoomGroupId) extends Field
  }

  sealed trait PermissionRow derives CanEqual

  object PermissionRow {
    final case class File(fileId: FileId) extends PermissionRow
    case object Group extends PermissionRow
  }

}
