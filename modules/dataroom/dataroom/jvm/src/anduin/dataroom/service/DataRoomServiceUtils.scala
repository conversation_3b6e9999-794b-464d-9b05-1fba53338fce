// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service

import java.time.Instant
import scala.annotation.tailrec
import io.github.arainko.ducktape.*
import zio.{Task, ZIO}
import anduin.account.profile.UserProfileService
import anduin.dataroom.DataRoomUserData
import anduin.dataroom.billing.DataRoomBillingOperations
import anduin.environment.EnvironmentCheck
import anduin.dataroom.flow.DataRoomValidateOperations.{MemberStateCheck, PlanCheck}
import anduin.dataroom.flow.{DataRoomEventStoreOperations, DataRoomFlowOperations, DataRoomValidateOperations}
import anduin.dataroom.group.state.DataRoomGroupCreatedSharedFlowState
import anduin.dataroom.group.{DataRoomGroupData, DataRoomGroupStateStoreOperations}
import anduin.dataroom.homepage.DataRoomHomePageConstants.DataRoomHomePageFileType
import anduin.dataroom.homepage.DataRoomHomePageConstants.DataRoomHomePageFileType.*
import anduin.dataroom.homepage.{DataRoomHomePageService, DataRoomHomePageStoreOperations}
import anduin.dataroom.model.homepage.*
import anduin.dataroom.participant.{DataRoomParticipant, DataRoomParticipantOperations}
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.dataroom.state.{DataRoomCreatedSharedFlowState, LinkInvitation}
import anduin.dms.DmsFeature.DataRoom
import anduin.dms.file.FileStateVersionOperations
import anduin.dms.file.state.FileStateStoreOperations
import anduin.dms.folder.state.FolderStateStoreOperations
import anduin.dms.service.FileService
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.dms.upload.flow.state.DirectFileUploadData
import anduin.dms.{DmsFolderOrderOperations, DmsTreeTraversalOperations}
import anduin.documentservice.watermark.WatermarkLayout
import anduin.fdb.record.model.{RecordIO, RecordTask}
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.file.FileMetadataKey
import anduin.id.dataroom.DataRoomGroupId
import anduin.id.link.ProtectedLinkId
import anduin.id.upload.FileUploadId
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.document.DocumentStorageId
import anduin.model.id.notification.NotificationSpaceId
import anduin.model.id.stage.{DataRoomTermsOfAccessId, DataRoomWorkflowId}
import anduin.model.id.{DataRoomHomeSectionIdFactory, FileId, FolderId, TeamId}
import anduin.notification.NotificationService
import anduin.orgbilling.model.plan.{BillingPlanStatus, DataRoomPlan}
import anduin.protobuf.flow.file.{FileFolderPermission, FileFolderPermissionMap}
import anduin.protobuf.notification.*
import anduin.radix.RadixId
import anduin.service.{AuthenticatedRequestContext, RequestContext}
import anduin.stargazer.service.dataroom.{
  UploadDataRoomHomePageFilesParams,
  WatermarkColor,
  WatermarkMetadataParams,
  WatermarkTransparency
}
import anduin.storageservice.common.FileContentOrigin
import anduin.team.*
import com.anduin.stargazer.service.utils.ZIOUtils

object DataRoomServiceUtils {

  final case class DataRoomDataTuple(
    workflowId: DataRoomWorkflowId,
    createdState: DataRoomCreatedSharedFlowState,
    participantRoles: Map[UserId, DataRoomRole],
    dataRoomPlan: DataRoomPlan,
    createdAt: Option[Instant],
    teamStateMap: Map[UserId, TeamMemberFlowState],
    deletedUsers: Set[UserId],
    isHomePagePublished: Boolean,
    groupMap: Map[DataRoomGroupId, DataRoomGroupData],
    deletedGroups: Map[DataRoomGroupId, DataRoomGroupData]
  ) {
    def isExpired: Boolean = !BillingPlanStatus.checkStatus(dataRoomPlan)
  }

  private val rootFolderName = "Data room root folder"

  def execute[A](task: (DataRoomFlowOperations, DataRoomValidateOperations) => RecordTask[A]): Task[A] = {
    FDBRecordDatabase.transact(
      FDBOperations[(DataRoomFlowOperations, DataRoomValidateOperations)].Production
    ) { case (flowOps, validateOps) =>
      task(flowOps, validateOps)
    }
  }

  def uploadTerms(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userId: UserId,
    individualUserTeam: TeamId,
    currentAdmins: Seq[UserId],
    fileName: String,
    fileContent: FileContentOrigin,
    userIp: Option[String] = None
  )(
    using fileService: FileService
  ): Task[FileId] = {
    val toaId = DataRoomTermsOfAccessId(dataRoomWorkflowId)
    val folderId = FolderId.channelSystemFolderId(toaId)
    for {
      isExisting <- fileService.existFolder(userId)(folderId)
      _ <- ZIOUtils.unless(isExisting) {
        fileService.createSystemFolderForChannel(
          channel = toaId,
          folderName = "Terms of Access",
          creator = userId,
          permissionOpts = FileFolderPermissionMap()
            .addAllUserPermissions(currentAdmins.map(_ -> FileFolderPermission.Write))
            .addTeamPermissions(individualUserTeam -> FileFolderPermission.Read)
        )
      }
      fileId <- fileService.uploadFile(
        parentFolderId = folderId,
        fileName = fileName,
        content = fileContent,
        uploader = userId,
        permissionOpt = Some(
          FileFolderPermissionMap().addTeamPermissions(individualUserTeam -> FileFolderPermission.Read)
        ),
        uploaderIp = userIp
      )
    } yield fileId
  }

  def copyTerms(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userId: UserId,
    individualUserTeam: TeamId,
    currentAdmins: Seq[UserId],
    tempToaFileId: FileId,
    ctx: Option[RequestContext]
  )(
    using fileService: FileService
  ): Task[FileId] = {
    for {
      toaStorageId <- fileService.getFileStorageId(userId, tempToaFileId, DmsTrackingActivityType.Internal, ctx)
      toaName <- fileService.getFileName(userId)(tempToaFileId)
      toaFileId <- uploadTerms(
        dataRoomWorkflowId,
        userId,
        individualUserTeam,
        currentAdmins,
        toaName,
        FileContentOrigin.FromStorageId(toaStorageId, fileService.backendConfig.aws.S3.bucket),
        userIp = ctx.flatMap(_.getClientIP)
      )
    } yield toaFileId
  }

  private def hasUserNotAccepted(
    currentState: DataRoomCreatedSharedFlowState,
    actor: UserId,
    toaFileId: FileId
  ) = {
    !currentState.termsOfAccessCertificates.exists { certificate =>
      certificate.userId == actor && certificate.toaFileId == toaFileId
    }
  }

  def selfAcceptTermsOfAccess(
    flowOps: DataRoomFlowOperations,
    currentState: DataRoomCreatedSharedFlowState,
    dataRoomWorkflowId: DataRoomWorkflowId,
    ctx: AuthenticatedRequestContext,
    toaFileIdOpt: Option[FileId]
  ): RecordTask[Option[Unit]] = {
    RecordIO.traverse(toaFileIdOpt) { toaFileId =>
      RecordIO.when(
        hasUserNotAccepted(
          currentState,
          ctx.actor.userId,
          toaFileId
        )
      ) {
        flowOps.acceptTermsOfAccess(
          dataRoomWorkflowId = dataRoomWorkflowId,
          actor = ctx.actor.userId,
          fileId = toaFileId,
          ipAddress = ctx.getClientIP
        )
      }
    }
  }

  def getFilesAndFolders(
    rootFolderId: FolderId,
    actor: UserId
  ): Task[(List[FileId], List[FolderId])] = {
    FDBRecordDatabase.transact(
      FDBOperations[(FileStateStoreOperations, FolderStateStoreOperations)].Production
    ) { case (fileOps, folderOps) =>
      for {
        fileIds <- fileOps.getFileIds(
          actor = actor,
          folderId = rootFolderId
        )
        folderIds <- folderOps.getSubfolderIds(
          actor = actor,
          folderId = rootFolderId
        )
      } yield (fileIds, folderIds)
    }
  }

  def getFileUploadData(
    fileId: FileId,
    actor: UserId,
    ctx: AuthenticatedRequestContext,
    metadata: Map[String, String] = Map.empty
  )(
    using fileService: FileService
  ): Task[DirectFileUploadData] = {
    for {
      fileName <- fileService.getFileName(actor)(fileId)
      storageId <- fileService.getFileStorageId(
        actor,
        fileId,
        DmsTrackingActivityType.Internal,
        Some(ctx)
      )
    } yield {
      DirectFileUploadData(
        FileUploadId.unsafeRandomId(fileName),
        fileName,
        storageId = storageId,
        bucket = fileService.s3Service.s3Config.bucket,
        metadata = metadata
      )
    }
  }

  def getWatermarkMetadataParamsOpt(
    currentState: DataRoomCreatedSharedFlowState
  ): Option[WatermarkMetadataParams] = {
    currentState.watermarkMetadata.map { wmData =>
      val layout = wmData.layout match {
        case WatermarkLayout.Subtle => anduin.stargazer.service.dataroom.WatermarkLayout.Subtle
        case WatermarkLayout.Normal => anduin.stargazer.service.dataroom.WatermarkLayout.Normal
        case WatermarkLayout.Heavy  => anduin.stargazer.service.dataroom.WatermarkLayout.Heavy
        case _                      => anduin.stargazer.service.dataroom.WatermarkLayout.Subtle
      }
      WatermarkMetadataParams(
        text = wmData.text,
        color = WatermarkColor.fromInt(wmData.color).getOrElse(WatermarkColor.Red),
        layout = layout,
        transparency = WatermarkTransparency.fromAlpha(wmData.alpha).getOrElse(WatermarkTransparency.Fifty)
      )
    }
  }

  def copyHomePageModel(
    homePageOpt: Option[DataRoomHomePageModel],
    newDataroomId: DataRoomWorkflowId,
    actor: UserId,
    ctx: AuthenticatedRequestContext
  )(
    using dataRoomHomePageService: DataRoomHomePageService,
    fileService: FileService
  ): Task[Option[DataRoomHomePageModel]] = {
    ZIOUtils.traverseOption(homePageOpt) { homePageModel =>
      val fileIdWithTypes = homePageModel.sections.flatMap {
        case s: HeaderSection => s.imageFileId.map(_ -> DataRoomHomePageFileType.HeaderBackgroundImage)
        case s: IntroSection  => s.mediaFileId.map(_ -> DataRoomHomePageFileType.IntroMediaFile)
        case s: FooterSection => s.imageFileId.map(_ -> DataRoomHomePageFileType.FooterBackgroundImage)
        case _                => None
      }
      val destRootFolder = FolderId.channelSystemFolderId(newDataroomId)
      val newSectionId = DataRoomHomeSectionIdFactory.unsafeRandomId(newDataroomId)
      for {
        fileDataList <- ZIO.foreach(fileIdWithTypes) { (fileId, fileType) =>
          getFileUploadData(
            fileId,
            actor,
            ctx,
            Map(FileMetadataKey.dataRoomHomePageFileType.value -> fileType.value)
          )
        }
        fileMap <- dataRoomHomePageService
          .internalUploadHomePageFiles(
            UploadDataRoomHomePageFilesParams(newDataroomId),
            fileDataList,
            Map.empty,
            ctx
          )
          .map(_.fileMap)
        documentFileIds <- getHomePageDocumentFileIds(
          homePageModel,
          destRootFolder,
          actor
        )
      } yield {
        val sections = homePageModel.sections
        val newSections = sections.flatMap {
          case sec: HeaderSection =>
            Some(sec.copy(sectionId = newSectionId, imageFileId = fileMap.get(HeaderBackgroundImage)))
          case sec: IntroSection => Some(sec.copy(sectionId = newSectionId, mediaFileId = fileMap.get(IntroMediaFile)))
          case sec: FooterSection =>
            Some(sec.copy(sectionId = newSectionId, imageFileId = fileMap.get(FooterBackgroundImage)))
          case sec: DocumentSection      => Some(sec.copy(sectionId = newSectionId, fileIds = documentFileIds))
          case sec: QuestionSection      => Some(sec.copy(sectionId = newSectionId))
          case DataRoomHomeSection.Empty => None
        }
        homePageModel.copy(dataRoomWorkflowId = newDataroomId, sections = newSections)
      }
    }
  }

  def getFileStorageIds(
    fileIds: Seq[FileId],
    fileStateVersionOps: FileStateVersionOperations,
    actor: UserId
  ): RecordTask[Seq[(FileId, DocumentStorageId)]] = {
    val fileMap = fileIds.map { fId =>
      fileStateVersionOps
        .getStorageId(
          actor,
          fId,
          DmsTrackingActivityType.Internal
        )
        .map(fId -> _)
    }
    gatherRecordIOs[(FileId, DocumentStorageId)](fileMap)
  }

  def gatherRecordIOs[A](input: Seq[RecordTask[A]]): RecordTask[Seq[A]] = {
    input.foldLeft[RecordTask[Seq[A]]](RecordIO.succeed(Seq.empty)) { case (res, item) =>
      for {
        r <- res
        it <- item
      } yield r :+ it
    }
  }

  private[dataroom] def getFoldersWithOrders(
    rootFolder: FolderId,
    actor: UserId
  ): Task[Seq[((FolderId, String), Map[RadixId, (String, Long)])]] = {
    // Note: return Seq[((folderId, folderName), Map(folderId/fileId -> (name, order)))]
    FDBRecordDatabase
      .transact(
        FDBOperations[
          (
            (DmsTreeTraversalOperations, DmsFolderOrderOperations),
            (FolderStateStoreOperations, FileStateVersionOperations)
          )
        ].Production
      ) { case ((dmsTreeOps, dmsFolderOps), (folderOps, fileStateVersionOps)) =>
        for {
          res <- dmsTreeOps
            .traverseFolderTrees[Option[((FolderId, String), Map[RadixId, (String, Long)])]](
              actor,
              Seq(rootFolder),
              folderFunc = folderId => {
                for {
                  keyName <-
                    if (folderId == rootFolder) {
                      RecordIO.succeed(rootFolderName)
                    } else {
                      folderOps.getName(actor, folderId)
                    }
                  order <- dmsFolderOps.getFolderOrder(actor, folderId)
                  folderInfo = order.map { case (radixId, ord) =>
                    val nameIO = radixId match {
                      case id: FolderId => folderOps.getName(actor, id)
                      case id: FileId   => fileStateVersionOps.getName(actor, id)
                      case _            => RecordIO.succeed("")
                    }
                    nameIO.map(x => (radixId, (x, ord)))
                  }.toSeq
                  res <- gatherRecordIOs(folderInfo)
                } yield Option((folderId, keyName) -> res.toMap)
              },
              fileFunc = _ => RecordIO.succeed(None),
              filter = _ => true
            )
            .map(_.flatten)
        } yield res
      }
  }

  def copyFolderOrder(
    orgRootFolderId: FolderId,
    destRootFolderId: FolderId,
    actor: UserId
  )(
    using fileService: FileService
  ): Task[Unit] = {
    // Note: We copy folder orders after all files and folders are successfully copied to the duplicated data room.
    //       This function takes advantage of "duplication", which means that the new data room has the same folder
    //       structure, same number of children in each duplicated folders, and especially, same names of the
    //       folders and files.
    for {
      orgFolderWithOrders <- getFoldersWithOrders(orgRootFolderId, actor)
      destFolderWithOrders <- getFoldersWithOrders(destRootFolderId, actor)
      _ <- ZIOUtils.when(orgFolderWithOrders.size == destFolderWithOrders.size) {
        val newFolderOrders = destFolderWithOrders.map { case ((fId, fIdName), destChildOrders) =>
          val orgMatch = orgFolderWithOrders.find { case ((_, orgFIdName), orgChildOrders) =>
            fIdName == orgFIdName && destChildOrders.size == orgChildOrders.size
          }
          val resOrders = orgMatch.fold[Map[RadixId, Long]](Map.empty) { case (_, orgChildOrders) =>
            val orgNameOrders = orgChildOrders.values.toSeq
            val res = destChildOrders.toSeq.foldLeft[(Seq[(RadixId, Long)], Seq[(String, Long)])](
              (Seq.empty, orgNameOrders)
            ) { case (resAndOrgOrders, item) =>
              val orgNmOrds = resAndOrgOrders._2
              val foundOrderOpt = orgNmOrds.find(_._1 == item._2._1).map(item._1 -> _._2)
              (
                resAndOrgOrders._1 ++ foundOrderOpt.toSeq,
                foundOrderOpt.fold(orgNmOrds) { case (_, ord) =>
                  orgNmOrds.filterNot(org => org._1 == item._2._1 && org._2 == ord)
                }
              )
            }
            res._1.toMap
          }
          fId -> resOrders
        }
        ZIO
          .foreach(newFolderOrders) { case (folderId, orderMap) =>
            fileService.changeFolderOrder(
              actor,
              folderId,
              orderMap
            )
          }
          .map(_ => ())
      }
    } yield ()
  }

  private def getHomePageDocumentFileIds(
    homePageModel: DataRoomHomePageModel,
    destRootFolder: FolderId,
    actor: UserId
  ): Task[Seq[FileId]] = {
    val homePageDocFileIds = homePageModel.sections.flatMap {
      case sec: DocumentSection => sec.fileIds
      case _                    => Seq.empty
    }
    FDBRecordDatabase
      .transact(
        FDBOperations[(DmsTreeTraversalOperations, FileStateVersionOperations)].Production
      ) { case (dmsTreeOps, fileInfoOps) =>
        for {
          orgHomePageDocumentStorageIds <- getFileStorageIds(
            homePageDocFileIds,
            fileInfoOps,
            actor
          )
          fileIds <- dmsTreeOps.getAllFoldersAndFiles(actor, Seq(destRootFolder)).map(_._2)
          newFileStorageIds <- getFileStorageIds(
            fileIds,
            fileInfoOps,
            actor
          )
        } yield {
          orgHomePageDocumentStorageIds.foldLeft[Seq[(FileId, DocumentStorageId)]](Seq.empty) { case (res, item) =>
            val s = newFileStorageIds.find { case (fId, sId) =>
              sId == item._2 && !res.contains((fId, sId))
            }
            res ++ s.toSeq
          }
        }
      }
      .map(_.map(_._1))
  }

  @tailrec
  private def getFolderIdsInPath(folderId: FolderId, res: List[FolderId]): List[FolderId] = {
    folderId.parentFolder match {
      case Some(parentFolderId) => getFolderIdsInPath(parentFolderId, folderId +: res)
      case None                 => folderId +: res
    }
  }

  def getFilePath(
    actor: UserId,
    fileId: FileId
  )(
    using fileService: FileService
  ): Task[String] = {
    val res = for {
      fileName <- fileService.getFileName(actor)(fileId)
      folderNames <- ZIO.foreach(getFolderIdsInPath(fileId.folder, List())) {
        fileService.getFolderName(actor)(_)
      }
    } yield folderNames.drop(1) :+ fileName
    res.map(_.mkString("/"))
  }

  def markDataRoomAllFilesAsSeen(
    actor: UserId,
    dataRoomWorkflowId: DataRoomWorkflowId,
    notificationService: NotificationService
  ): Task[Unit] = {
    val notificationSpaceId = NotificationSpaceId.channelSystemNotificationSpaceId(dataRoomWorkflowId)
    notificationService.markNotificationsAsSeenBySpaceAndTypeRecursively(
      notificationSpaceId,
      NotificationType.DataRoomNewFile,
      actor
    )
  }

  /** Similar to `UserProfileService.batchGetUserInfos` but segment `userIds` to allow getting more than 1000 users
    */
  def batchGetUserInfos(
    userIds: Set[UserId],
    actorOpt: Option[UserId] = None
  )(
    using userProfileService: UserProfileService
  ): Task[Map[UserId, UserInfo]] = {
    val maxSupportedSize = 1000 // `UserProfileKeycloakService.batchGetUserInfos`
    ZIO
      .foreach(userIds.grouped(maxSupportedSize).toSeq) { batchUserIds =>
        userProfileService.batchGetUserInfos(batchUserIds, actorOpt)
      }
      .map(
        _.reduceOption(_ ++ _).getOrElse(Map.empty)
      )
  }

  def convertToDataRoomUserData(
    userInfo: UserInfo,
    teamMemberState: TeamMemberFlowState,
    participantState: DataRoomParticipant
  ): DataRoomUserData = {
    val teamState = teamMemberState match {
      case UserJoined(inviter, _, joinedAt, _) => DataRoomUserData.JoinedUser(inviter, joinedAt)
      case UserInvited(inviter, _, invitedAt, remindedAt, _) =>
        DataRoomUserData.InvitedUser(inviter, invitedAt, remindedAt)
      case _ => throw new IllegalStateException(s"Invalid data room member state: ${teamMemberState}")
    }
    val userState = participantState
      .into[DataRoomUserData.UserState]
      .transform(
        Field.computed(_.isIndividualUser, _.groupIds.isEmpty)
      )
    DataRoomUserData(
      DataRoomUserData.UserInfo(userInfo),
      teamState,
      userState
    )
  }

  def getDataRoomState(
    actor: UserId,
    dataRoomWorkflowId: DataRoomWorkflowId,
    isArchived: Boolean,
    includeInvited: Boolean,
    includeToaNotAccepted: Boolean,
    includeGroup: Boolean,
    planCheck: PlanCheck,
    environmentCheck: EnvironmentCheck
  ): Task[DataRoomDataTuple] = {
    FDBRecordDatabase.transact(
      FDBOperations[
        (
          (
            (DataRoomValidateOperations, DataRoomEventStoreOperations),
            (DataRoomBillingOperations, DataRoomHomePageStoreOperations)
          ),
          (DataRoomParticipantOperations, DataRoomGroupStateStoreOperations)
        )
      ].Production
    ) { case (((validateOps, eventOps), (billingOps, homePageOps)), (participantOps, groupOps)) =>
      for {
        (teamId, dataRoomCreatedState) <- validateOps.validateAndGetCurrentState(
          dataRoomWorkflowId = dataRoomWorkflowId,
          userId = actor,
          roleChecks = Set(),
          checkJoined = if (includeInvited) MemberStateCheck.RequireInvitedOrJoined else MemberStateCheck.RequireJoined,
          checkTermsAccepted = !includeToaNotAccepted,
          checkArchivedStatus = Some(isArchived),
          checkValidPlanWithFeatures = planCheck,
          environmentCheck = environmentCheck
        )
        participantRoles <- validateOps.roleOps.getAllParticipantRoleMap(dataRoomWorkflowId)
        canViewGroups = participantRoles
          .get(actor)
          .exists(role => DataRoomRoleUtils.isInternal(role))
        dataRoomPlan <- billingOps.getPlan(dataRoomCreatedState)
        firstEvent <- eventOps.getFirstEvent(dataRoomWorkflowId)
        (teamStateMap, deletedUsers) <- getTeamStateMap(
          validateOps,
          dataRoomCreatedState,
          teamId,
          actor
        )
        isHomePagePublished <- homePageOps.getOpt(dataRoomWorkflowId).map(_.exists(_.published))
        allGroups <-
          if (canViewGroups && includeGroup) {
            groupOps.getAllGroups(dataRoomWorkflowId, includeArchived = true)
          } else {
            RecordIO.succeed(Seq.empty[DataRoomGroupCreatedSharedFlowState])
          }
        groupParticipantMap <-
          if (canViewGroups && includeGroup) {
            participantOps.getGroupParticipantMap(dataRoomWorkflowId)
          } else {
            RecordIO.succeed(Map.empty[DataRoomGroupId, Seq[UserId]])
          }
        groupMap <- RecordIO.succeed(
          getDataRoomGroupData(
            allGroups,
            groupParticipantMap,
            dataRoomCreatedState.linkInvitationMap,
            isArchived = false
          )
        )
        deletedGroups <- RecordIO.succeed(
          getDataRoomGroupData(
            allGroups,
            Map(),
            Map(),
            isArchived = true
          )
        )
        (filteredState, filteredPartipants) = DataRoomStateUtils.filterState(
          actor = actor,
          state = dataRoomCreatedState,
          participantRoles = participantRoles,
          teamStateMap = teamStateMap
        )
      } yield DataRoomDataTuple(
        workflowId = dataRoomWorkflowId,
        createdState = filteredState,
        participantRoles = filteredPartipants,
        dataRoomPlan = dataRoomPlan,
        createdAt = firstEvent.timestamp,
        teamStateMap = teamStateMap,
        deletedUsers = deletedUsers,
        isHomePagePublished = isHomePagePublished,
        groupMap = groupMap,
        deletedGroups = deletedGroups
      )
    }
  }

  private def filterVisibleToExternalTeamStateMap(
    actor: UserId,
    contactUserIds: Set[UserId]
  ) = { (teamStateMap: Map[UserId, TeamMemberFlowState]) =>
    val inviterMatcher: PartialFunction[TeamMemberFlowState, UserId] = {
      case invited: UserInvited => invited.inviter
      case joined: UserJoined   => joined.inviter
    }
    teamStateMap.filter {
      case (userId, inviterMatcher(inviter)) => inviter == actor || contactUserIds.contains(userId) || userId == actor
      case (userId, _)                       => contactUserIds.contains(userId)
    }
  }

  private def filterTeamStateMap(
    createdState: DataRoomCreatedSharedFlowState,
    participantRoles: Map[UserId, DataRoomRole],
    rawTeamStateMap: Map[UserId, TeamMemberFlowState],
    actor: UserId
  ) = {
    val (teamStateMap, deletedUserMap) = rawTeamStateMap.partition {
      case (_, _: UserJoined | _: UserInvited)    => true
      case (_, _: UserRemoved | _: UserRequested) => false
    }
    // Actor was invited and viewing pending invitations, inviter info should be included
    val actorInviterOpt = rawTeamStateMap.get(actor).flatMap {
      case invited: UserInvited => Some(invited.inviter)
      case _                    => None
    }
    if (participantRoles.get(actor).exists(DataRoomRoleUtils.isInternal)) {
      teamStateMap -> deletedUserMap.keySet
    } else {
      val teamStateMapFilterer = filterVisibleToExternalTeamStateMap(
        actor,
        createdState.contactUserIds -- deletedUserMap.keySet ++ actorInviterOpt.toSet
      )
      teamStateMapFilterer(teamStateMap) -> teamStateMapFilterer(deletedUserMap).keySet
    }
  }

  private def getTeamStateMap(
    validateOps: DataRoomValidateOperations,
    createdState: DataRoomCreatedSharedFlowState,
    teamId: TeamId,
    actor: UserId
  ) = {
    for {
      rawTeamStateMap <- validateOps.teamMemberStateOps.getAllMemberStates(teamId)
      participantRoles <- validateOps.roleOps.getAllParticipantRoleMap(createdState.dataRoomWorkflowId)
    } yield filterTeamStateMap(
      createdState,
      participantRoles,
      rawTeamStateMap,
      actor
    )
  }

  private def getDataRoomGroupData(
    allGroups: Seq[DataRoomGroupCreatedSharedFlowState],
    groupParticipantMap: Map[DataRoomGroupId, Seq[UserId]],
    linkInvitationMap: Map[ProtectedLinkId, LinkInvitation],
    isArchived: Boolean
  ) = {
    allGroups
      .filter(_.isArchived == isArchived)
      .flatMap { group =>
        group.id.map { id =>
          id -> DataRoomGroupData(
            id = id,
            name = group.name,
            role = group.role,
            createdAt = group.createdAt,
            creator = group.creator,
            participants = groupParticipantMap.getOrElse(id, Seq.empty[UserId]),
            teamId = group.teamId,
            isDeleted = group.isArchived,
            linkCount = linkInvitationMap.count(_._2.groupIds.contains(id))
          )
        }
      }
      .toMap
  }

}
