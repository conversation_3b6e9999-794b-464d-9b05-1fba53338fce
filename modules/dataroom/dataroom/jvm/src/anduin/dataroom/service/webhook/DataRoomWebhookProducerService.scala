// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service.webhook

import zio.{Task, ZIO}

import anduin.dataroom.flow.DataRoomStateStoreOperations
import anduin.dataroom.webhook.*
import anduin.fdb.record.FDBRecordDatabase
import anduin.id.dataroom.DataRoomGroupId
import anduin.kafka.{KafkaService, KafkaSimpleProducer, Topic}
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import com.anduin.stargazer.service.GondorConfig
import com.anduin.stargazer.service.utils.ZIOUtils

final case class DataRoomWebhookProducerService(
  kafkaService: KafkaService,
  gondorConfig: GondorConfig
) {

  private val webhookTopic = Topic[DataRoomWorkflowId, DataRoomWebhookEvent](
    gondorConfig.publicApiConfig.webhookConfig.dataRoomPayloadKafkaTopic
  )

  private val webhookProducer: KafkaSimpleProducer[DataRoomWorkflowId, DataRoomWebhookEvent] =
    KafkaSimpleProducer(
      kafkaService = kafkaService,
      topic = webhookTopic,
      onSendMessage = { message =>
        ZIO.logInfo(s"Data Room ${message.key} produces webhook event ${message.value.getClass.getSimpleName}")
      }
    )

  private def produce(
    dataRoomWorkflowId: DataRoomWorkflowId,
    event: DataRoomWebhookEvent
  ): Task[Unit] = {
    for {
      isEnableWebhook <- FDBRecordDatabase
        .transact(DataRoomStateStoreOperations.Production)(
          _.getState(dataRoomWorkflowId)
        )
        .map(_.enableWebhook)
      _ <- ZIOUtils.when(isEnableWebhook) {
        webhookProducer.send(Topic.Message(dataRoomWorkflowId, event))
      }
    } yield ()
  }

  def produceUserJoinEvent(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userId: UserId,
    groupIds: Seq[DataRoomGroupId]
  ): Task[Unit] = {
    produce(
      dataRoomWorkflowId,
      DataRoomUserJoinWebhookEvent(
        dataRoomId = dataRoomWorkflowId,
        userId = userId,
        groupIds = groupIds
      )
    )
  }

  def produceUserAddToGroupEvent(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userIds: Seq[UserId],
    groupId: DataRoomGroupId
  ): Task[Unit] = {
    produce(
      dataRoomWorkflowId,
      DataRoomUserAddToGroupWebhookEvent(
        dataRoomId = dataRoomWorkflowId,
        userIds = userIds,
        groupId = groupId
      )
    )
  }

  def produceUserRemoveFromGroupEvent(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userIds: Seq[UserId],
    groupId: DataRoomGroupId
  ): Task[Unit] = {
    produce(
      dataRoomWorkflowId,
      DataRoomUserRemoveFromGroupWebhookEvent(
        dataRoomId = dataRoomWorkflowId,
        userIds = userIds,
        groupId = groupId
      )
    )
  }

  def produceUserRemovedEvent(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userIds: Seq[UserId]
  ): Task[Unit] = {
    produce(
      dataRoomWorkflowId,
      DataRoomUserRemovedWebhookEvent(
        dataRoomId = dataRoomWorkflowId,
        userIds = userIds
      )
    )
  }

  def produceUserInvitedEvent(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userIds: Seq[UserId],
    groupIds: Seq[DataRoomGroupId]
  ): Task[Unit] = {
    produce(
      dataRoomWorkflowId,
      DataRoomUserInvitedWebhookEvent(
        dataRoomId = dataRoomWorkflowId,
        userIds = userIds,
        groupIds = groupIds
      )
    )
  }

  def produceUserDeclineInvitationEvent(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userId: UserId,
    groupIds: Seq[DataRoomGroupId]
  ): Task[Unit] = {
    produce(
      dataRoomWorkflowId,
      DataRoomUserDeclineInvitationWebhookEvent(
        dataRoomId = dataRoomWorkflowId,
        userId = userId,
        groupIds = groupIds
      )
    )
  }

  def produceGroupDeletedEvent(
    dataRoomWorkflowId: DataRoomWorkflowId,
    groupId: DataRoomGroupId,
    userIds: Seq[UserId]
  ): Task[Unit] = {
    produce(
      dataRoomWorkflowId,
      DataRoomGroupDeletedWebhookEvent(
        dataRoomId = dataRoomWorkflowId,
        groupId = groupId,
        userIds = userIds
      )
    )
  }

}
