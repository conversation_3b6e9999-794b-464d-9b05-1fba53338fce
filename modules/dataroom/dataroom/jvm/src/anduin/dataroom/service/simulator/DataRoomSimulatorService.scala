// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service.simulator

import java.time.{Instant, LocalDate}
import scala.util.Random

import io.temporal.api.enums.v1.WorkflowIdReusePolicy
import monocle.Iso
import sttp.model.MediaType
import zio.temporal.ZRetryOptions
import zio.temporal.workflow.ZWorkflowStub
import zio.{Task, ZIO}

import anduin.account.authentication.CaptchaService
import anduin.account.profile.UserProfileService
import anduin.account.protocol.BifrostCommonProtocol.CaptchaResponse
import anduin.dataroom.email.simulator.{DataRoomSimulatorWarningEmailGenerate, DataRoomStartSimulatorEmailGenerate}
import anduin.dataroom.event.DataRoomEmailConfigs
import anduin.dataroom.flow.{DataRoomEventStoreOperations, DataRoomFlowOperations, DataRoomStateStoreOperations}
import anduin.dataroom.group.{AddUsersToDataRoomGroupParams, CreateDataRoomGroupParams, DataRoomGroupService}
import anduin.dataroom.homepage.DataRoomHomePageService
import anduin.dataroom.participant.DataRoomParticipantService
import anduin.dataroom.role.*
import anduin.dataroom.service.*
import anduin.dataroom.service.simulator.DataRoomSimulatorScenario.*
import anduin.dataroom.service.simulator.DataRoomSimulatorService.simulatorScenarioTypeIso
import anduin.dataroom.service.simulator.fdb.DataRoomSimulatorStoreOperations
import anduin.dataroom.simulator.DataRoomSimulatorProgress
import anduin.dataroom.whitelabel.DataRoomWhiteLabelService
import anduin.dataroom.workflow.simulator.*
import anduin.dataroom.workflow.simulator.impl.DataRoomSimulatorPopulateDataWorkflowImpl
import anduin.dms.DmsFeature
import anduin.dms.DmsFeature.DataRoom
import anduin.dms.file.viewertracking.DmsViewerTrackingService
import anduin.dms.service.FileService
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.dms.upload.FileUploadHandler
import anduin.endpoints.simulator.DataRoomSimulatorEndpoints.*
import anduin.endpoints.simulator.DataRoomSimulatorPublicEndpoints.*
import anduin.entity.model.EntityRole
import anduin.fdb.record.FDBRecordDatabase
import anduin.file.tracker.{PageView, PageViewInterval}
import anduin.id.dataroom.DataRoomGroupId
import anduin.id.entity.EntityId
import anduin.link.{LinkGeneratorService, RequestAccessParams}
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.{UserAttributes, UserId, UserInfo}
import anduin.model.document.DocumentStorageId
import anduin.model.id.email.sending.EmailSystemSpaceId
import anduin.model.id.stage.{DataRoomTermsOfAccessId, DataRoomWorkflowId}
import anduin.model.id.{BatchUploadIdFactory, FileId, FolderId, TemporalWorkflowId}
import anduin.model.notichannel.DataRoomNotificationChannels
import anduin.orgbilling.model.plan.DataRoomPlan
import anduin.protobuf.UserTypeMessage
import anduin.protobuf.dataroom.link.DataRoomLinkInvitationParamsData
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.service.*
import anduin.service.entity.invitation.EntityInvitationService
import anduin.stargazer.service.dataroom.*
import anduin.stargazer.service.dataroom.UpdateDataRoomWhiteLabelParams.WhiteLabelFileUpdate
import anduin.storageservice.common.FileContentOrigin
import anduin.storageservice.s3.S3Service
import anduin.temporal.TemporalEnvironment
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.email.EmailSenderService
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.utils.EmailAddressUtils

final class DataRoomSimulatorService(
  backendConfig: GondorBackendConfig,
  dataRoomService: DataRoomService,
  dataRoomFileService: DataRoomFileService,
  dataRoomParticipantService: DataRoomParticipantService,
  dataRoomFileUploadService: DataRoomFileUploadService,
  fileService: FileService,
  dmsViewerTrackingService: DmsViewerTrackingService,
  s3Service: S3Service,
  entityInvitationService: EntityInvitationService,
  captchaService: CaptchaService,
  emailSenderService: EmailSenderService,
  dataRoomHomePageService: DataRoomHomePageService,
  dataRoomWhiteLabelService: DataRoomWhiteLabelService,
  dataRoomProtectedLinkService: DataRoomProtectedLinkService,
  dataRoomGroupService: DataRoomGroupService,
  temporalEnvironment: TemporalEnvironment,
  natsNotificationService: NatsNotificationService
)(
  using val linkGeneratorService: LinkGeneratorService,
  val userProfileService: UserProfileService
) {

  private val parallelism = 4

  private val blacklistedDomains = EmailAddressUtils.PopularEmailDomains

  private def isBlacklisted(emailAddress: String) = {
    EmailAddress.unapply(emailAddress).forall(email => blacklistedDomains.contains(email.domain.value))
  }

  private def getUserInfo(user: User) = {
    UserInfo(
      emailAddressStr = user.emailAddress,
      firstName = user.fullName.firstName,
      lastName = user.fullName.lastName
    )
  }

  private def getServiceActor(userId: UserId, userInfo: UserInfo) = {
    ServiceActor(
      userId = userId,
      userInfo = userInfo,
      userAttributes = UserAttributes.DEFAULT,
      userSessionIdOpt = None,
      environmentIdOpt = None
    )
  }

  private def impersonate(ctx: RequestContext, userId: UserId, userInfo: UserInfo) = {
    AuthenticatedRequestContext(
      actor = getServiceActor(userId, userInfo),
      headers = ctx.headers,
      cookies = ctx.cookies
    )
  }

  private def initializeS3Resource(
    adminUserIdTask: Task[UserId],
    resource: String,
    contentType: MediaType,
    storageIdOpt: Option[DocumentStorageId] = None
  ): DataRoomHomePageService.ResourceInitialization = {
    val storageId = storageIdOpt.getOrElse(DocumentStorageId(resource))
    val task = for {
      _ <- ZIO.logInfo(s"Initializing S3 resource $resource")
      isExisted <- s3Service.checkExisted(
        storageId = storageId,
        bucket = fileService.gondorConfig.backendConfig.aws.S3.bucket
      )
      _ <- adminUserIdTask
      _ <- ZIOUtils.unless(isExisted) {
        s3Service.copyObject(
          sourceStorageId = DocumentStorageId(resource),
          sourceBucket = fileService.gondorConfig.backendConfig.aws.S3.resourcesBucket,
          destStorageId = storageId,
          destBucket = fileService.gondorConfig.backendConfig.aws.S3.bucket,
          contentType = contentType.toString()
        )
      }
    } yield ()
    DataRoomHomePageService.ResourceInitialization(storageId, task)
  }

  def initializeResourcesSimulator(adminUserId: UserId): Task[Unit] = {
    for {
      _ <- ZIO.foreach(DataRoomSimulatorScenario.dataRoomInitResources) { resource =>
        initializeS3Resource(
          adminUserIdTask = ZIO.succeed(adminUserId),
          resource = resource.resource,
          contentType = resource.contentType,
          storageIdOpt = Some(resource.storageId)
        ).initTask
      }
    } yield ()
  }

  def start(
    params: StartDataRoomSimulatorParams,
    ctx: PublicRequestContext
  ): Task[StartDataRoomSimulatorResponse] = {
    for {
      _ <- ZIO.logInfo(s"Starting data room simulator for email ${params.emailAddress}")
      idOpt <-
        if (isBlacklisted(params.emailAddress)) {
          for {
            _ <- ZIO.logWarning(s"Email domain for ${params.emailAddress} was blacklisted")
            _ <-
              emailSenderService.enqueue(
                DataRoomSimulatorWarningEmailGenerate(params.emailAddress),
                EmailSystemSpaceId.System
              )
          } yield Option.empty[DataRoomWorkflowId]
        } else {
          startInternal(emailAddress = params.emailAddress, ctx = ctx).map(resp => Option(resp.dataRoomId))
        }
      _ <- ZIO.logInfo(s"Initialized data room simulator $idOpt")
    } yield StartDataRoomSimulatorResponse(idOpt)
  }

  def startMarketingDataroomForFundSubIntegration(
    emailAddress: String,
    entityId: EntityId,
    fundSubUrl: String,
    ctx: PublicRequestContext
  ): Task[List[StartMarketingDataRoomResponse]] = { // // Marketing DR for the Us Form Demo
    val scenarios = List(
      DataRoomSimulatorScenario.NucleusCapitalDataRoomSimulatorScenario,
      DataRoomSimulatorScenario.NucleusInnovationDataRoomSimulatorScenario
    )
    for {
      _ <- ZIO.logInfo(s"Starting marketing data room simulator for email $emailAddress")
      responses <- ZIOUtils.foreachParN(2)(scenarios) { scenario =>
        startInternal(
          emailAddress = scenario.emailAddressOpt.getOrElse(emailAddress),
          ctx = ctx,
          customFundSubUrlOpt = Some(fundSubUrl),
          customEntityIdOpt = Some(entityId),
          scenario = scenario
        ).map { case (workflowId, sharableLinkId) =>
          StartMarketingDataRoomResponse(
            dataRoomWorkflowId = workflowId,
            dataRoomName = scenario.dataRoomName,
            shareableLinkId = sharableLinkId
          )
        }
      }
    } yield responses
  }

  def startSandboxSessionForAnduinMember(
    actor: UserId,
    ctx: AuthenticatedRequestContext
  ): Task[InternalStartDataRoomSimulatorResponse] = {
    for {
      actorEmail <- userProfileService.getEmailAddress(actor)
      _ <- validateAnduinDomainEmail(actorEmail)
      _ <- ZIO.logInfo(s"Internally starting data room simulator for email ${actorEmail.address}")
      (dataRoomWorkflowId, _) <- startInternal(emailAddress = actorEmail.address, ctx = ctx)
      _ <- ZIO.logInfo(s"Internally initialized data room simulator ${dataRoomWorkflowId.idString}")
    } yield InternalStartDataRoomSimulatorResponse(dataRoomWorkflowId)
  }

  def startWithPrivateToken(
    params: StartDataRoomSimulatorBackdoorParams,
    ctx: PublicRequestContext
  ): Task[StartDataRoomSimulatorBackdoorResponse] = {
    for {
      _ <- ZIO.logInfo(s"Internally starting data room simulator for email ${params.email}")
      _ <- ZIOUtils.validate(params.token == backendConfig.dataRoomSimulatorConfig.privateToken)(
        GeneralServiceException("Invalid token")
      )
      (dataRoomWorkflowId, _) <- startInternal(emailAddress = params.email, ctx = ctx)
      userId <- userProfileService.getUserIdFromEmailAddress(params.email)
      generatedLinkTask = DataRoomStartSimulatorEmailGenerate(
        dataRoomWorkflowId,
        userId
      ).redirectUrlTerm.value.headOption.map(_.getter)
      generatedLink <- generatedLinkTask.fold[Task[String]](ZIO.succeed(""))(identity)
      _ <- ZIO.logInfo(s"Internally initialized data room simulator ${dataRoomWorkflowId.idString}")
    } yield StartDataRoomSimulatorBackdoorResponse(dataRoomWorkflowId, generatedLink)
  }

  private def validateAnduinDomainEmail(email: EmailAddress) = {
    val domain = email.domain.value
    ZIOUtils.validate(domain.endsWith("anduintransact.com"))(
      new RuntimeException("You do not have permission for this API!")
    )
  }

  private def createPopulateWorkflowStub(dataRoomWorkflowId: DataRoomWorkflowId) = {
    DataRoomSimulatorPopulateDataWorkflowImpl.instance
      .getWorkflowStub(
        TemporalWorkflowId.unsafeFromSuffix(s"DataRoomSimulatorPopulateData-${dataRoomWorkflowId.idString}"),
        _.withWorkflowIdReusePolicy(WorkflowIdReusePolicy.WORKFLOW_ID_REUSE_POLICY_REJECT_DUPLICATE)
          .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(1))
      )
      .provideEnvironment(temporalEnvironment.workflowClient)
  }

  private def startInternal(
    emailAddress: String,
    ctx: RequestContext,
    customEntityIdOpt: Option[EntityId] = None,
    customFundSubUrlOpt: Option[String] = None,
    scenario: DataRoomSimulatorScenario.CustomDataRoomSimulatorScenario =
      DataRoomSimulatorScenario.DefaultDataRoomSimulatorScenario
  ) = {
    val dataRoomEntityId = customEntityIdOpt.getOrElse(entityId)
    for {
      userInfo <- ZIO.attempt {
        UserInfo(
          emailAddress,
          guestName.firstName,
          guestName.lastName
        )
      }
      creationResult <- userProfileService.createRegisteredUser(
        userInfo = userInfo,
        userType = UserTypeMessage.RegisteredUser,
        password = Some(guestPassword)
      )
      userId = creationResult.userId
      _ <- entityInvitationService.addEntityMember(
        actor = userId,
        userId = userId,
        role = EntityRole.Member,
        entityId = dataRoomEntityId,
        skipPermission = true
      )
      watermarkParams = WatermarkMetadataParams(
        watermarkText,
        WatermarkColor.Red,
        WatermarkLayout.Subtle,
        WatermarkTransparency.Fifty
      )
      temporaryFolderId <- fileService.createUserTemporaryFolderIfNeeded(userId)(
        using DmsFeature.Public
      )
      toaFileId <- fileService.uploadFile(
        parentFolderId = temporaryFolderId,
        fileName = "Terms of Access.pdf",
        content = FileContentOrigin.FromStorageId(dummyPdfStorageId, s3Service.s3Config.bucket),
        uploader = userId
      )(
        using DmsFeature.Public
      )
      authCtx = impersonate(
        ctx,
        userId,
        userInfo
      )
      response <- dataRoomService.createDataRoom(
        params = CreateDataRoomParams(
          scenario.dataRoomName,
          dataRoomEntityId,
          Some(watermarkParams),
          Some(toaFileId),
          showIndex = true,
          showSearch = true,
          showHomePage = true,
          showWhiteLabel = true
        ),
        ctx = authCtx,
        planOpt = Some(
          DataRoomPlan.DataRoomBusinessPlan(
            expDate = LocalDate.now().plusDays(158),
            extraSeats = 15,
            isTrial = false
          )
        ),
        isDemoDataRoom = true
      )
      dataRoomWorkflowId = response.dataRoomWorkflowId
      _ <- emailSenderService.enqueue(
        DataRoomStartSimulatorEmailGenerate(
          dataRoomWorkflowId,
          userId
        ),
        EmailSystemSpaceId.System
      )
      // update simulator data room email configs
      _ <- FDBRecordDatabase.transact(DataRoomFlowOperations.Production) {
        _.updateDataRoomEmailConfigs(
          dataRoomWorkflowId = dataRoomWorkflowId,
          actor = userId,
          emailConfigs = Some(
            DataRoomEmailConfigs(
              senderNameEnabled = true,
              senderName = "Nucleus Tech",
              senderAddressNamePartEnabled = true,
              senderAddressNamePart = "nucleustech",
              customSenderDisabled = true
            )
          )
        )
      }
      createdAt = Instant.now.minusSeconds(Random.between(timeWindow, minDataRoomCreationDate))
      _ <- FDBRecordDatabase.transact(DataRoomEventStoreOperations.Production) {
        _.unsafeModifyCreatedAt(dataRoomWorkflowId, createdAt)
      }
      _ <- FDBRecordDatabase.transact(DataRoomSimulatorStoreOperations.Production) {
        _.createDataRoomSimulatorSession(
          anduin.dataroom.simulator.DataRoomSimulatorSession(
            dataRoomWorkflowId = dataRoomWorkflowId,
            creator = userId,
            createdAt = Some(Instant.now()),
            progress = DataRoomSimulatorProgress.Created
          )
        )
      }
      workflow <- createPopulateWorkflowStub(dataRoomWorkflowId)
      _ <- ZWorkflowStub.start(
        workflow.populate(
          PopulateDataRoomSimulatorParams(
            dataRoomWorkflowId,
            userId,
            customFundSubUrlOpt = customFundSubUrlOpt,
            scenario = simulatorScenarioTypeIso.reverseGet(scenario)
          )
        )
      )
      shareableLink <- dataRoomProtectedLinkService
        .createDataRoomLinkInvitation(
          CreateDataRoomLinkInvitationParams(
            dataRoomWorkflowId = dataRoomWorkflowId,
            data = DataRoomLinkInvitationParamsData(
              name = "View file invitation link",
              role = Restricted(),
              permissionChanges =
                Some(AssetPermissionChanges.allFoldersWithRootChannel(dataRoomWorkflowId, FileFolderPermission.Read))
            ),
            password = None,
            enableEnterpriseLogin = false
          ),
          actor = authCtx.actor,
          httpContext = Some(authCtx)
        )
        .map(_.linkId)

    } yield (dataRoomId = dataRoomWorkflowId, shareableLink = shareableLink)
  }

  private def getSessionWithDataRoomMetadata(actor: UserId, session: anduin.dataroom.simulator.DataRoomSimulatorSession)
    : Task[DataRoomSimulatorSession] = {
    val drId = session.dataRoomWorkflowId
    for {
      dataRoomName <- FDBRecordDatabase.transact(
        DataRoomStateStoreOperations.Production
      ) { stateOps =>
        stateOps.getState(drId).map(_.name)
      }
      logoUrlOpt <- dataRoomWhiteLabelService
        .getWhiteLabelData(
          actor,
          drId
        )
        .map(_.dataRoomIconUrl)
    } yield DataRoomSimulatorSession(
      dataRoomWorkflowId = drId,
      dataRoomName = dataRoomName,
      createdAt = session.createdAt,
      progress = session.progress,
      logoUrl = logoUrlOpt.getOrElse(""),
      percentageProgress = session.percentageProgress
    )
  }

  def getSimulatorDashboardData(actor: UserId): Task[GetDataRoomSimulatorDashboardResponse] = {
    for {
      actorEmail <- userProfileService.getEmailAddress(actor)
      _ <- validateAnduinDomainEmail(actorEmail)
      mySessions <- FDBRecordDatabase.transact(DataRoomSimulatorStoreOperations.Production) {
        _.getSessionsByUser(actor)
      }
      mySessionData <- ZIO.foreach(mySessions) { session =>
        getSessionWithDataRoomMetadata(actor, session)
      }
    } yield GetDataRoomSimulatorDashboardResponse(
      mySessionData.toList
    )
  }

  private def createAllMembers = {
    ZIOUtils.foreachParN(parallelism)(allUsers) { user =>
      for {
        _ <- ZIO.logInfo(s"Creating user $user")
        creationResult <- userProfileService.createRegisteredUser(
          userInfo = getUserInfo(user),
          userType = UserTypeMessage.RegisteredUser,
          password = Some(guestPassword)
        )
      } yield creationResult.userId -> user
    }
  }

  private def createAllGroups(
    dataRoomWorkflowId: DataRoomWorkflowId,
    ctx: AuthenticatedRequestContext
  ) = {
    ZIOUtils.foreachParN(parallelism)(allGroups) { group =>
      for {
        _ <- ZIO.logInfo(s"Creating group $group")
        result <- dataRoomGroupService.createGroup(
          params = CreateDataRoomGroupParams(
            dataRoomWorkflowId = dataRoomWorkflowId,
            name = group.name,
            role = group.role,
            assetPermissions = AssetPermissionChanges.allFoldersWithRootChannel(dataRoomWorkflowId, group.permission)
          ),
          actor = ctx.actor.userId,
          ctx = Some(ctx)
        )
      } yield group.name -> result.groupId
    }
  }

  private def inviteToDataRoom(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userMap: Map[UserId, User],
    groupMap: Map[String, DataRoomGroupId],
    ctx: AuthenticatedRequestContext
  ) = {
    val individualPermissionMap = for {
      (_, user) <- userMap
      assetPermission <- user.permission match {
        case _: GroupPermission => None
        case IndividualPermission(role, permission) =>
          Some(
            DataRoomPermissionChanges(
              roleSet = Some(role),
              assetPermissions = AssetPermissionChanges.allFoldersWithRootChannel(dataRoomWorkflowId, permission)
            )
          )
      }
    } yield (user.emailAddress, assetPermission)

    val groupPermissionMap = for {
      (_, user) <- userMap
      assetPermission <- user.permission match {
        case _: IndividualPermission => None
        case GroupPermission(group, canInvite) =>
          groupMap.get(group.name).map { groupId =>
            DataRoomGroupPermissionChanges(
              groupIds = Set(groupId),
              canInvite = canInvite
            )
          }
      }
    } yield (user.emailAddress, assetPermission)

    dataRoomParticipantService.inviteUsers(
      InviteUsersToDataRoomParams(
        dataRoomWorkflowId = dataRoomWorkflowId,
        individualPermissionMap = individualPermissionMap,
        groupPermissionMap = groupPermissionMap,
        isToaRequired = true,
        subject = "Subject",
        message = "",
        buttonLabel = "CTA"
      ),
      ctx.actor,
      Some(ctx)
    )
  }

  private def getLastToaFileIdOpt(dataRoomWorkflowId: DataRoomWorkflowId, actor: UserId) = {
    fileService
      .getFiles(actor)(FolderId.channelSystemFolderId(DataRoomTermsOfAccessId(dataRoomWorkflowId)))
      .map(_.headOption)
  }

  private def isAdmin(user: User) = {
    user.permission match {
      case IndividualPermission(role, _) => DataRoomRoleUtils.isAdmin(role)
      case GroupPermission(group, _)     => DataRoomRoleUtils.isAdmin(group.role)
    }
  }

  private def allMembersAcceptInvitation(
    dataRoomWorkflowId: DataRoomWorkflowId,
    members: Seq[(UserId, User)],
    lastToaFileIdOpt: Option[FileId],
    ctx: AuthenticatedRequestContext
  ) = {
    val joinedMembers = members.filter { case (_, user) =>
      user.activity.count > 0 || isAdmin(user)
    }
    ZIO.foreach(joinedMembers) { case (userId, user) =>
      val userInfo = getUserInfo(user)
      val impCtx = impersonate(
        ctx,
        userId,
        userInfo
      )
      for {
        _ <- ZIO.logInfo(
          s"User ${userInfo.fullNameString} is accepting invitation to data room simulator $dataRoomWorkflowId"
        )
        _ <- ZIO.foreach(lastToaFileIdOpt.toSeq) { toaFileId =>
          dataRoomFileService.getTermsOfAccessUrl(
            GetTermsOfAccessUrl(toaFileId, linkIdOpt = None, purpose = DmsTrackingActivityType.View),
            impCtx
          )
        }
        _ <- dataRoomParticipantService.acceptInvitation(
          AcceptInvitationToDataRoomParams(dataRoomWorkflowId, lastToaFileIdOpt),
          impCtx
        )
      } yield ()
    }
  }

  private def randomAdmin(admins: IndexedSeq[(UserId, UserInfo)]) = {
    admins(Random.nextInt(admins.size))
  }

  private def createInvitationLinkAndRequestAccess(
    dataRoomWorkflowId: DataRoomWorkflowId,
    ctx: AuthenticatedRequestContext
  ) = {
    for {
      internalLinkId <- dataRoomProtectedLinkService
        .createDataRoomLinkInvitation(
          CreateDataRoomLinkInvitationParams(
            dataRoomWorkflowId = dataRoomWorkflowId,
            data = DataRoomLinkInvitationParamsData(
              name = "Internal invitation link",
              role = Member(),
              permissionChanges =
                Some(AssetPermissionChanges.allFoldersWithRootChannel(dataRoomWorkflowId, FileFolderPermission.Write)),
              isRequiredAdminApproval = true
            ),
            password = None,
            enableEnterpriseLogin = false
          ),
          actor = ctx.actor,
          httpContext = Some(ctx)
        )
        .map(_.linkId)
      externalLinkId <- dataRoomProtectedLinkService
        .createDataRoomLinkInvitation(
          CreateDataRoomLinkInvitationParams(
            dataRoomWorkflowId = dataRoomWorkflowId,
            data = DataRoomLinkInvitationParamsData(
              name = "External invitation link",
              role = Guest(),
              permissionChanges =
                Some(AssetPermissionChanges.allFoldersWithRootChannel(dataRoomWorkflowId, FileFolderPermission.Read)),
              isRequiredAdminApproval = true
            ),
            password = None,
            enableEnterpriseLogin = false
          ),
          actor = ctx.actor,
          httpContext = Some(ctx)
        )
        .map(_.linkId)
      _ <- ZIO.foreach(publicUsers) { user =>
        dataRoomProtectedLinkService.requestAccess(
          RequestAccessParams(
            linkId = if (DataRoomRoleUtils.isInternal(user.role)) internalLinkId else externalLinkId,
            captchaResponse = CaptchaResponse.BackdoorResponse(captchaService.generateCaptchaBackdoorToken),
            email = user.emailAddress
          )
        )
      }
    } yield ()
  }

  private def initFolderStructure(
    dataRoomWorkflowId: DataRoomWorkflowId,
    structure: Seq[Folder],
    members: Seq[(UserId, User)],
    ctx: AuthenticatedRequestContext
  ) = {
    val admins = members
      .filter(entry => isAdmin(entry._2))
      .map { case (userId, user) =>
        userId -> getUserInfo(user)
      }
      .toIndexedSeq
      .appended(ctx.actor.userId -> ctx.actor.userInfo)
    val rootFolderId = FolderId.channelSystemFolderId(dataRoomWorkflowId)
    val initialFolders = structure.map(rootFolderId -> _)
    val initialFiles = Set.empty[FileId]
    ZIOUtils.tailRecM(initialFolders -> initialFiles) {
      case (Nil, files) =>
        ZIO.attempt(Right(files))
      case (folders @ _, currentFiles) =>
        for {
          resList <- ZIOUtils.foreachParN(parallelism)(folders) { case (parentFolderId, folder) =>
            val (userId, userInfo) = randomAdmin(admins)
            for {
              res <- dataRoomFileService.addFolder(
                AddFolderParams(
                  parentFolderId,
                  folder.name,
                  None
                ),
                getServiceActor(userId, userInfo),
                Some(ctx)
              )
            } yield folder.folders.map(res.newFolderId -> _) -> folder.files.map(res.newFolderId -> _)
          }
          nextFolders = resList.flatMap(_._1)
          files = resList.flatMap(_._2)
          newFiles <- ZIOUtils.foreachParN(parallelism)(files) { case (parentFolderId, getFile) =>
            val (userId, userInfo) = randomAdmin(admins)
            dataRoomFileUploadService.uploadFileToDataRoom(
              FileUploadHandler.CreateInput(
                BatchUploadIdFactory.unsafeRandomId,
                UploadDataRoomFileParams(parentFolderId, None),
                Seq(getFile(s3Service.s3Config.resourcesBucket)),
                Seq(),
                impersonate(
                  ctx,
                  userId,
                  userInfo
                )
              ),
              fileContent = Map.empty
            )
          }
        } yield Left(nextFolders -> (currentFiles ++ newFiles.flatMap(_.fileIds)))
    }
  }

  private def addFileTracking(
    members: Seq[(UserId, User)],
    allFiles: Set[FileId],
    ctx: AuthenticatedRequestContext
  ) = {
    val now = Instant.now
    ZIOUtils.foreachParN(parallelism)(members.groupBy(_._2.activity).toSeq) { case (activity, users) =>
      val files = activity.fileCountLimit.fold {
        allFiles.toSeq
      } { limit =>
        Random.shuffle(allFiles.toSeq).take(limit)
      }
      val timestamps = List.fill(activity.count)(now.minusSeconds(Random.nextLong(timeWindow))).distinct
      ZIOUtils.foreachParN(parallelism)(timestamps) { timestamp =>
        val fileId = files(Random.nextInt(files.size))
        val (userId, user) = users(Random.nextInt(users.size))
        val userInfo = getUserInfo(user)
        val activityType = activity.types.toSeq(Random.nextInt(activity.types.size))
        for {
          _ <- ZIO.logInfo(s"Adding $activityType on file $fileId for user $userId at $timestamp")
          versionIndex <- fileService.getCurrentVersion(userId)(fileId)
          _ <- dmsViewerTrackingService.recordFirstEvent(
            actor = userId,
            fileId = fileId,
            versionIndex = versionIndex,
            session = timestamp,
            ipAddress = ctx.getClientIP,
            purpose = activityType
          )
          _ <- ZIOUtils.when(activityType == DmsTrackingActivityType.View) {
            val sessionLength = Random.between(minFilePageViewSessionLength, maxFilePageViewSessionLength)
            val rawEvents = List.fill(Random.nextInt(maxFilePageTurnCount)) {
              PageView(
                page = Option(Random.nextInt(filePageCount + 1)).filter(_ > 0),
                timestamp = timestamp.plusSeconds(Random.nextLong(sessionLength))
              )
            }
            val pageViewInterval = PageViewInterval(
              events = rawEvents.sortBy(_.timestamp).distinctBy(_.timestamp),
              end = timestamp.plusSeconds(sessionLength)
            )
            ZIOUtils.when(pageViewInterval.events.nonEmpty) {
              dmsViewerTrackingService.recordPageView(
                actor = userId,
                fileId = fileId,
                versionIndex = versionIndex,
                session = timestamp,
                interval = pageViewInterval
              )
            }
          }
        } yield (userId, userInfo) -> timestamp
      }
    }
  }

  private def addVisitTracking(
    dataRoomWorkflowId: DataRoomWorkflowId,
    lastVisitMap: Map[(UserId, UserInfo), Instant],
    ctx: AuthenticatedRequestContext
  ) = {
    ZIOUtils.foreachParN(parallelism)(lastVisitMap.toSeq) { case ((userId, userInfo), timestamp) =>
      dataRoomService.trackUserVisitDataRoom(
        params = TrackUserVisitDataRoomParams(dataRoomWorkflowId),
        actor = getServiceActor(userId, userInfo),
        httpContext = Some(
          impersonate(
            ctx,
            userId,
            userInfo
          )
        ),
        timestamp = timestamp.minusSeconds(sessionLength)
      )
    }
  }

  private def addDataRoomHomePageData(
    dataRoomWorkflowId: DataRoomWorkflowId,
    fileIds: Set[FileId],
    ctx: AuthenticatedRequestContext,
    scenario: DataRoomSimulatorScenario.CustomDataRoomSimulatorScenario,
    customButtonDestinationLinkOpt: Option[String] = None
  ) = {
    for {
      oldDataRoomState <- dataRoomHomePageService
        .getDataRoomHomePage(GetDataRoomHomePageParams(dataRoomWorkflowId), ctx.actor.userId)
        .map(_.state)
      files = scenario.dataRoomHomePageFiles(s3Service.s3Config.resourcesBucket)
      params = UploadDataRoomHomePageFilesParams(dataRoomWorkflowId)
      uploadResp <- dataRoomHomePageService.internalUploadHomePageFiles(params, files, Map.empty, ctx)
      _ <- dataRoomHomePageService.updateDataRoomHomePage(
        UpdateDataRoomHomePageParams(
          dataRoomWorkflowId,
          lastUpdate = None,
          scenario.generateDataRoomHomePageState(
            oldDataRoomState,
            fileIds.toSeq,
            customButtonDestinationLinkOpt = customButtonDestinationLinkOpt
          ),
          uploadResp.fileMap,
          uploadResp.thumbnailMap,
          forceUpdate = true
        ),
        ctx
      )
    } yield ()
  }

  private def getWhiteLabelFileStorageId(
    actor: UserId,
    dataRoomWorkflowId: DataRoomWorkflowId,
    filePath: String
  ): Task[DocumentStorageId] = {
    dataRoomWhiteLabelService.uploadDataRoomWhitelabelFile(
      actor,
      dataRoomWorkflowId,
      filePath,
      DocumentStorageId(filePath),
      s3Service.s3Config.resourcesBucket
    )
  }

  private def isMatchCustomerWhiteLabel(emailOpt: Option[EmailAddress])(customWhiteLabel: CustomWhiteLabel) = {
    val domainOpt = emailOpt.map(_.domain)
    val mailboxOpt = emailOpt.map(_.mailbox)
    val isBackdoor = domainOpt.contains(
      EmailAddress.Domain("anduintransact.com")
    ) && customWhiteLabel.backdoorEmailSuffixOpt.exists(suffix => mailboxOpt.exists(_.value.endsWith(s"+$suffix")))
    val isDomainMatched = domainOpt.exists(customWhiteLabel.domains.contains)
    isBackdoor || isDomainMatched
  }

  private def addDataRoomWhiteLabelData(
    dataRoomWorkflowId: DataRoomWorkflowId,
    scenario: CustomDataRoomSimulatorScenario,
    ctx: AuthenticatedRequestContext
  ) = {
    val customWhiteLabel =
      customWhiteLabels
        .find(isMatchCustomerWhiteLabel(ctx.actor.userInfo.emailAddress))
        .getOrElse(scenario.defaultWhiteLabel)
    for {
      whiteLabelData <- dataRoomWhiteLabelService.getWhiteLabelData(
        ctx.actor.userId,
        dataRoomWorkflowId
      )
      logoDocStorageIdOpt <- ZIOUtils.traverseOption(customWhiteLabel.logoPath) { logoPath =>
        getWhiteLabelFileStorageId(
          ctx.actor.userId,
          dataRoomWorkflowId,
          logoPath
        )
      }
      iconDocStorageIdOpt <- ZIOUtils.traverseOption(customWhiteLabel.iconPath) { iconPath =>
        getWhiteLabelFileStorageId(
          ctx.actor.userId,
          dataRoomWorkflowId,
          iconPath
        )
      }
      _ <- dataRoomWhiteLabelService.updateDataRoomWhiteLabelUnsafe(
        UpdateDataRoomWhiteLabelParams(
          dataRoomWorkflowId = dataRoomWorkflowId,
          logoUpdateOpt = Some(WhiteLabelFileUpdate(logoDocStorageIdOpt)),
          iconUpdateOpt = Some(WhiteLabelFileUpdate(iconDocStorageIdOpt)),
          customValues = whiteLabelData.customValues
            .map(customValue => (customValue.key, customValue.value))
            .toMap ++ customWhiteLabel.customValueMap
        ),
        ctx
      )
    } yield ()
  }

  private def getAuthenticatedRequestContext(userId: UserId) = {
    for {
      userInfo <- userProfileService.getUserInfo(userId)
      serviceActor = ServiceActor(
        userId,
        userInfo,
        UserAttributes.DEFAULT,
        None,
        None
      )
    } yield AuthenticatedRequestContext(
      serviceActor,
      Seq.empty,
      Seq.empty
    )
  }

  def populateParticipants(
    params: PopulateDataRoomSimulatorParams
  ): Task[PopulateDataRoomSimulatorParticipantsResponse] = {
    for {
      _ <- ZIO.logInfo(s"Populating participants for data room simulator ${params.dataRoomWorkflowId}")
      ctx <- getAuthenticatedRequestContext(params.actor)
      members <- createAllMembers
      groups <- createAllGroups(params.dataRoomWorkflowId, ctx).map(_.toMap)
      // Add guest account to admin group
      _ <- ZIOUtils.traverseOptionUnit(groups.get(fundManagers.name)) { groupId =>
        dataRoomGroupService.addUsersToGroup(
          params = AddUsersToDataRoomGroupParams(
            groupId = groupId,
            userIds = Set(params.actor)
          ),
          actor = params.actor,
          ctx = None
        )
      }
      _ <- inviteToDataRoom(
        dataRoomWorkflowId = params.dataRoomWorkflowId,
        userMap = members.toMap,
        groupMap = groups,
        ctx = ctx
      )
      lastToaFileIdOpt <- getLastToaFileIdOpt(params.dataRoomWorkflowId, params.actor)
      _ <- allMembersAcceptInvitation(
        dataRoomWorkflowId = params.dataRoomWorkflowId,
        members = members,
        lastToaFileIdOpt = lastToaFileIdOpt,
        ctx = ctx
      )
      _ <- createInvitationLinkAndRequestAccess(
        dataRoomWorkflowId = params.dataRoomWorkflowId,
        ctx = ctx
      )
      _ <- updatePopulatingDataProgress(params.dataRoomWorkflowId)
    } yield PopulateDataRoomSimulatorParticipantsResponse(members.map(_._1))
  }

  def populateDocuments(
    params: PopulateDataRoomSimulatorDocumentsParams
  ): Task[PopulateDataRoomSimulatorDocumentsResponse] = {
    for {
      _ <- ZIO.logInfo(s"Populating documents for data room simulator ${params.dataRoomWorkflowId}")
      ctx <- getAuthenticatedRequestContext(params.actor)
      members = params.memberIds zip allUsers
      homepageFileIds <- initFolderStructure(
        dataRoomWorkflowId = params.dataRoomWorkflowId,
        homepageStructure,
        members = members,
        ctx = ctx
      )
      documentsFileIds <- initFolderStructure(
        dataRoomWorkflowId = params.dataRoomWorkflowId,
        folderStructure,
        members = members,
        ctx = ctx
      )
      _ <- updatePopulatingDataProgress(params.dataRoomWorkflowId)
    } yield PopulateDataRoomSimulatorDocumentsResponse(
      homepageFileIds,
      documentsFileIds
    )
  }

  def populateHomepage(
    params: PopulateDataRoomSimulatorHomepageParams
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Populating homepage for data room simulator ${params.dataRoomWorkflowId}")
      ctx <- getAuthenticatedRequestContext(params.actor)
      _ <- addDataRoomHomePageData(
        dataRoomWorkflowId = params.dataRoomWorkflowId,
        fileIds = params.homepageFileIds,
        ctx = ctx,
        scenario = simulatorScenarioTypeIso.get(params.scenario),
        customButtonDestinationLinkOpt = params.customFundSubUrlOpt
      )
      _ <- addDataRoomWhiteLabelData(
        dataRoomWorkflowId = params.dataRoomWorkflowId,
        scenario = simulatorScenarioTypeIso.get(params.scenario),
        ctx = ctx
      )
      _ <- updatePopulatingDataProgress(params.dataRoomWorkflowId)
    } yield ()
  }

  def populateInsights(
    params: PopulateDataRoomSimulatorInsightsParams
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Populating insights for data room simulator ${params.dataRoomWorkflowId}")
      ctx <- getAuthenticatedRequestContext(params.actor)
      members = params.memberIds zip allUsers
      lastViewDownloadMap <- addFileTracking(
        members = members,
        allFiles = params.fileIds,
        ctx = ctx
      )
      memberLastVisitMap = lastViewDownloadMap.flatten.groupMapReduce(_._1)(_._2)(Ordering[Instant].max)
      adminLastVisitMap = for {
        (userId, user) <- members
        if isAdmin(user)
      } yield (userId, getUserInfo(user)) -> Instant.now
      _ <- addVisitTracking(
        dataRoomWorkflowId = params.dataRoomWorkflowId,
        lastVisitMap = memberLastVisitMap ++ adminLastVisitMap,
        ctx = ctx
      )
      _ <- updatePopulatingDataProgress(params.dataRoomWorkflowId)
    } yield ()
  }

  def updatePopulatingDataProgress(
    dataRoomId: DataRoomWorkflowId
  ): Task[Unit] = {
    for {
      _ <- FDBRecordDatabase.transact(DataRoomSimulatorStoreOperations.Production) {
        _.updateDataRoomSimulatorSession(dataRoomId)(model =>
          model.copy(
            percentageProgress = Math.min(100, model.percentageProgress + 20)
          )
        )
      }
      _ <- natsNotificationService.publish(
        dataRoomId,
        DataRoomNotificationChannels.dataRoomSimulatorSession(dataRoomId)
      )
    } yield ()
  }

  def completePopulatingData(
    params: PopulateDataRoomSimulatorParams
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Populated data for data room simulator ${params.dataRoomWorkflowId}")
      _ <- FDBRecordDatabase.transact(DataRoomSimulatorStoreOperations.Production) {
        _.updateDataRoomSimulatorSession(params.dataRoomWorkflowId)(
          _.copy(
            percentageProgress = 100,
            progress = DataRoomSimulatorProgress.Populated
          )
        )
      }
      _ <- natsNotificationService.publish(
        params.dataRoomWorkflowId,
        DataRoomNotificationChannels.dataRoomSimulatorSession(params.dataRoomWorkflowId)
      )
    } yield ()
  }

  def onPopulateError(
    params: PopulateDataRoomSimulatorParams
  ): Task[Unit] = {
    for {
      _ <- FDBRecordDatabase.transact(DataRoomSimulatorStoreOperations.Production) {
        _.updateDataRoomSimulatorSession(params.dataRoomWorkflowId)(
          _.copy(
            progress = DataRoomSimulatorProgress.Failed
          )
        )
      }
      _ <- natsNotificationService.publish(
        params.dataRoomWorkflowId,
        DataRoomNotificationChannels.dataRoomSimulatorSession(params.dataRoomWorkflowId)
      )
    } yield ()
  }

  def getProgress(
    params: GetDataRoomSimulatorProgressParams
  ): Task[GetDataRoomSimulatorProgressResponse] = {
    FDBRecordDatabase.transact(DataRoomSimulatorStoreOperations.Production) {
      _.getDataRoomSimulatorSession(params.dataRoomWorkflowId).map(session =>
        GetDataRoomSimulatorProgressResponse(session.progress)
      )
    }
  }

  def getSession(
    actor: UserId,
    params: GetDataRoomSimulatorSessionParams
  ): Task[DataRoomSimulatorSession] = {
    for {
      session <- FDBRecordDatabase.transact(DataRoomSimulatorStoreOperations.Production) {
        _.getDataRoomSimulatorSession(params.dataRoomWorkflowId)
      }
      sessionWithData <- getSessionWithDataRoomMetadata(actor, session)
    } yield sessionWithData
  }

}

object DataRoomSimulatorService {

  given simulatorScenarioTypeIso: Iso[Scenario, DataRoomSimulatorScenario.CustomDataRoomSimulatorScenario] =
    Iso[Scenario, DataRoomSimulatorScenario.CustomDataRoomSimulatorScenario] {
      case Scenario.DEFAULT            => DataRoomSimulatorScenario.DefaultDataRoomSimulatorScenario
      case Scenario.NUCLEUS_CAPITAL    => DataRoomSimulatorScenario.NucleusCapitalDataRoomSimulatorScenario
      case Scenario.NUCLEUS_INNOVATION => DataRoomSimulatorScenario.NucleusInnovationDataRoomSimulatorScenario
      case Scenario.Unrecognized(_)    => DataRoomSimulatorScenario.DefaultDataRoomSimulatorScenario
    } {
      case DataRoomSimulatorScenario.DefaultDataRoomSimulatorScenario           => Scenario.DEFAULT
      case DataRoomSimulatorScenario.NucleusCapitalDataRoomSimulatorScenario    => Scenario.NUCLEUS_CAPITAL
      case DataRoomSimulatorScenario.NucleusInnovationDataRoomSimulatorScenario => Scenario.NUCLEUS_INNOVATION
    }

}
