// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service

import anduin.dataroom.exception.DataRoomFileFolderIncompatiblePermissionException

import scala.annotation.tailrec
import anduin.dataroom.group.DataRoomGroupStateStoreOperations
import anduin.dataroom.state.DataRoomCreatedSharedFlowState
import anduin.dms.DmsFeature.DataRoom
import anduin.dms.file.state.FileStateStoreOperations
import anduin.dms.folder.state.FolderStateStoreOperations
import anduin.dms.{DmsPermissionOperations, DmsTreeTraversalOperations}
import anduin.fdb.record.model.{RecordIO, RecordTask}
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.greylin.modelti
import anduin.id.entity.EntityId
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{FileId, FolderId, TeamId}
import anduin.orgbilling.model.plan.DataRoomPremiumFeature
import anduin.protobuf.flow.file.{FileFolderPermission, FileFolderPermissionMap}
import anduin.team.TeamServiceOperations
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.utils.FileFolderPermissionUtils.ordering

import scala.math.Ordering.Implicits.*
import zio.{Task, ZIO}
import anduin.dataroom.flow.*
import anduin.dataroom.flow.DataRoomValidateOperations.PlanCheck
import anduin.dataroom.role.*
import anduin.dataroom.service.dmsmetadata.ModifyPermissionSourceOfEventMetaData
import anduin.dataroom.service.dmsmetadata.ModifyPermissionSourceOfEventMetaData.ModifyPermissionSourceOfEvent
import anduin.dms.service.FileService
import anduin.stargazer.service.dataroom.*
import com.anduin.stargazer.service.utils.ZIOUtils

final case class DataRoomPermissionService(
  fileService: FileService,
  gondorBackendConfig: GondorBackendConfig
) {

  private type UserOrTeamId = Either[UserId, TeamId]

  def convertRoleToGreylinRole(role: DataRoomRole): modelti.DataRoomRole = {
    role match {
      case _: Admin           => modelti.DataRoomRole.Admin
      case _: Member          => modelti.DataRoomRole.Member
      case _: Guest           => modelti.DataRoomRole.Guest
      case _: Restricted      => modelti.DataRoomRole.Restricted
      case DataRoomRole.Empty => modelti.DataRoomRole.Empty
    }
  }

  def validateAsset(
    folderId: FolderId,
    actor: UserId,
    checkValidPlanWithFeatures: PlanCheck = PlanCheck.RequirePlan(Set())
  ): Task[(DataRoomWorkflowId, DataRoomCreatedSharedFlowState)] = {
    for {
      dataRoomWorkflowId <- ZIOUtils.optionToTask(
        folderId.channel match {
          case dataRoomWorkflowId: DataRoomWorkflowId => Some(dataRoomWorkflowId)
          case _                                      => None
        },
        new RuntimeException(s"Folder $folderId's channel is not DataRoomWorkflowId")
      )
      (_, dataRoomCreatedState) <- FDBRecordDatabase.transact(DataRoomValidateOperations.Production) {
        _.validateAndGetCurrentState(
          dataRoomWorkflowId = dataRoomWorkflowId,
          userId = actor,
          roleChecks = Set(),
          checkValidPlanWithFeatures = checkValidPlanWithFeatures
        )
      }
    } yield dataRoomWorkflowId -> dataRoomCreatedState
  }

  private[dataroom] def validatePermissionByRole(
    role: DataRoomRole,
    permissionOpt: Option[FileFolderPermission],
    hasViewOnly: Boolean
  ) = {
    role match {
      case _: Admin => permissionOpt.contains(FileFolderPermission.Own)
      case _: DataRoomNonAdminRole =>
        (permissionOpt.contains(FileFolderPermission.ViewOnly) && hasViewOnly) ||
        !permissionOpt.exists(_ > DataRoomRoleUtils.getMaxPermission(role))
      case DataRoomRole.Empty => true // Ignore corrupted role
    }
  }

  private[dataroom] def validateNewFileFolderPermission(
    createdState: DataRoomCreatedSharedFlowState,
    parentFolder: FolderId,
    permissionMapOpt: Option[FileFolderPermissionMap],
    creator: UserId
  ) = {
    val dataRoomWorkflowId = createdState.dataRoomWorkflowId
    ZIOUtils.traverseOptionUnit(permissionMapOpt) { permissionMap =>
      for {
        parentPermissionMap <- fileService.getFolderPermissionMap(creator)(parentFolder)
        _ <- FDBRecordDatabase.transact(
          FDBOperations[
            (DataRoomValidateOperations, (DataRoomGroupStateStoreOperations, TeamServiceOperations))
          ].Production
        ) { case (validateOps, (groupStoreOps, teamOps)) =>
          for {
            hasViewOnly <- validateOps.billingOps.checkHavingDataRoomPremiumFeatures(
              createdState,
              DataRoomPremiumFeature.ViewOnly
            )
            groupStates <- groupStoreOps.getAllGroups(dataRoomWorkflowId)
            groupParticipants <- RecordIO
              .parTraverseN(4)(groupStates) { groupState =>
                teamOps.getTeamMemberInfo(groupState.teamId).map(_.joinedMembers)
              }
              .map(_.flatten.toSet)
            _ <- RecordIO.validate(
              permissionMap.userPermissions.keySet.intersect(groupParticipants).isEmpty
            ) {
              new RuntimeException(
                s"Permission map for new file folder $permissionMap in data room $dataRoomWorkflowId parent folder $parentFolder contains group users"
              )
            }
            creatorRole <- validateOps.roleOps.getParticipantRole(createdState.dataRoomWorkflowId, creator)
            _ <- RecordIO.validateAll {
              groupStates.map { groupState =>
                val childPermissionOpt = permissionMap.teamPermissions.get(groupState.teamId)
                val parentPermissionOpt = parentPermissionMap.teamPermissions.get(groupState.teamId)
                val result = validatePermissionByRole(
                  groupState.role,
                  childPermissionOpt,
                  hasViewOnly
                ) && (DataRoomRoleUtils.isAdmin(creatorRole) || parentPermissionOpt == childPermissionOpt)
                (groupState.id, groupState.role) -> result
              }
            } { (groupId, role) =>
              new RuntimeException(s"Permission map $permissionMap is not compatible with group ${groupId} role ${role}")
            }
            participantRoles <- validateOps.roleOps.getAllParticipantRoleMap(dataRoomWorkflowId)
            _ <- RecordIO.validateAll {
              participantRoles.toSeq.map { (userId, role) =>
                val childPermissionOpt = permissionMap.userPermissions.get(userId)
                val isValidGroupPermission = groupParticipants.contains(userId) && childPermissionOpt.isEmpty
                val isValidUnassignedPermission = !groupParticipants.contains(userId) && validatePermissionByRole(
                  role,
                  childPermissionOpt,
                  hasViewOnly
                )
                val result = isValidGroupPermission || isValidUnassignedPermission
                (userId, role) -> result
              }
            } { (userId, role) =>
              new RuntimeException(
                s"Permission map $permissionMap is not compatible with individual user ${userId} role ${role}"
              )
            }
          } yield ()
        }
      } yield ()
    }
  }

  private[dataroom] def validatePermissionChanges(
    validateOps: DataRoomValidateOperations,
    dataRoomWorkflowId: DataRoomWorkflowId,
    createdState: DataRoomCreatedSharedFlowState,
    currentRoleAndChangeList: List[(Option[DataRoomRole], DataRoomPermissionChanges)]
  ): RecordTask[Unit] = {
    for {
      hasViewOnly <- validateOps.billingOps.checkHavingDataRoomPremiumFeatures(
        createdState,
        DataRoomPremiumFeature.ViewOnly
      )
      _ <- RecordIO.parTraverseN(4)(currentRoleAndChangeList) { case (currentRoleOpt, changes) =>
        val updatedRole = changes.roleSet.orElse(currentRoleOpt)
        val ownAndNoAccess = Set[Option[FileFolderPermission]](Some(FileFolderPermission.Own), None)
        for {
          _ <- RecordIO.validate {
            changes.assetPermissions.folderPermissions.values.forall(!ownAndNoAccess.contains(_))
          } {
            new RuntimeException(s"Changes $changes shouldn't have No Access and Own in folder single permissions")
          }
          _ <- RecordIO.validate {
            updatedRole.fold(false) {
              case _: Admin =>
                changes.assetPermissions == AssetPermissionChanges.allFoldersWithRootChannel(dataRoomWorkflowId)
              case role: DataRoomNonAdminRole =>
                val actualPermissions = changes.assetPermissions.recursivePermissions.values.flatten.toSet ++
                  changes.assetPermissions.folderPermissions.values.flatten.toSet ++
                  changes.assetPermissions.filePermissions.values.flatten.toSet
                val expectedPermissions = FileFolderPermission.values
                  .toSet[FileFolderPermission]
                  .removedAll(Option.unless(hasViewOnly)(FileFolderPermission.ViewOnly))
                  .filter(_ <= DataRoomRoleUtils.getMaxPermission(role))
                actualPermissions.forall(expectedPermissions.contains)
              case DataRoomRole.Empty =>
                false
            }
          }(DataRoomFileFolderIncompatiblePermissionException(changes, updatedRole))
        } yield ()
      }
    } yield ()
  }

  private def getUserRestrictedChanges[K](key: K)(permissionMap: FileFolderPermissionMap): Seq[(K, UserOrTeamId)] = {
    for {
      (userId, permission) <- permissionMap.userPermissions.toSeq
      if permission.value > DataRoomRoleUtils.maxRestrictedPermission.value
    } yield (key, Left(userId))
  }

  private def getTeamRestrictedChanges[K](key: K)(permissionMap: FileFolderPermissionMap): Seq[(K, UserOrTeamId)] = {
    for {
      (teamId, permission) <- permissionMap.teamPermissions.toSeq
      if permission.value > DataRoomRoleUtils.maxRestrictedPermission.value
    } yield (key, Right(teamId))
  }

  private def getUsersAndTeamsWithEditOrHigher[K](
    ids: Seq[K],
    getPermissionMap: K => Task[FileFolderPermissionMap]
  ): Task[Map[UserOrTeamId, Seq[K]]] = {
    for {
      userOrTeamWithIdList <- ZIO.foreachPar(ids) { id =>
        getPermissionMap(id).map { permissionMap =>
          getUserRestrictedChanges(id)(permissionMap) ++ getTeamRestrictedChanges(id)(permissionMap)
        }
      }
    } yield userOrTeamWithIdList.flatten.groupMap(_._2)(_._1)
  }

  @tailrec
  private def getAncestors(folderId: FolderId, res: Set[FolderId]): Set[FolderId] = {
    folderId.parentFolder match {
      case Some(parentFolderId) => getAncestors(parentFolderId, res + parentFolderId)
      case None                 => res
    }
  }

  private def convertToMaxRestrictedPermission[K](
    itemMap: Map[UserOrTeamId, Seq[K]],
    targetId: UserOrTeamId,
    singleChanges: Set[K],
    recursiveChanges: Set[FolderId],
    getFolder: K => FolderId
  ) = {
    for {
      id <- itemMap.getOrElse(targetId, Seq())
      if !singleChanges.contains(id)
      folderId = getFolder(id)
      if getAncestors(folderId, Set(folderId)).forall(!recursiveChanges.contains(_))
    } yield id -> Some(DataRoomRoleUtils.maxRestrictedPermission)
  }

  private def appendNewRestrictedChanges(
    actor: UserId,
    dataRoomWorkflowId: DataRoomWorkflowId,
    changes: Map[UserOrTeamId, DataRoomPermissionChanges]
  ) = {
    val newRestricted = changes.filter(_._2.roleSet.exists(DataRoomRoleUtils.isRestricted))
    if (newRestricted.isEmpty) {
      ZIO.attempt {
        changes.map { case (targetId, permissionChanges) =>
          targetId -> permissionChanges.assetPermissions
        }
      }
    } else {
      for {
        (allFolders, allFiles) <- FDBRecordDatabase.transact(DmsTreeTraversalOperations.Production) {
          _.getAllFoldersAndFiles(actor, Seq(FolderId.channelSystemFolderId(dataRoomWorkflowId)))
        }
        folderMap <- getUsersAndTeamsWithEditOrHigher(allFolders, fileService.getFolderPermissionMap(actor))
        fileMap <- getUsersAndTeamsWithEditOrHigher(allFiles, fileService.getFilePermissionMap(actor))
      } yield {
        for {
          (targetId, originalChanges) <- newRestricted
        } yield {
          targetId -> originalChanges.assetPermissions
            .addAllFolderPermissions(
              convertToMaxRestrictedPermission[FolderId](
                folderMap,
                targetId,
                originalChanges.assetPermissions.folderPermissions.keySet,
                originalChanges.assetPermissions.recursivePermissions.keySet,
                identity
              )
            )
            .addAllFilePermissions(
              convertToMaxRestrictedPermission[FileId](
                fileMap,
                targetId,
                originalChanges.assetPermissions.filePermissions.keySet,
                originalChanges.assetPermissions.recursivePermissions.keySet,
                _.folder
              )
            )
        }
      }
    }
  }

  private[dataroom] def validateDataRoomAssetPermissionChanges(
    dataRoomWorkflowId: DataRoomWorkflowId,
    permissionChangesList: Iterable[DataRoomPermissionChanges]
  ) = {
    ZIOUtils.validate {
      permissionChangesList
        .forall { permissionChanges =>
          val fileChannels = permissionChanges.assetPermissions.filePermissions.keySet.map(_.channel)
          val folderChannels = permissionChanges.assetPermissions.folderPermissions.keySet.map(_.channel)
          val recursiveChannels = permissionChanges.assetPermissions.recursivePermissions.keySet.map(_.channel)
          (fileChannels ++ folderChannels ++ recursiveChannels).forall(_ == dataRoomWorkflowId)
        }
    } {
      new RuntimeException(
        s"Changes $permissionChangesList contain assets from channels other than data room ${dataRoomWorkflowId.idString}"
      )
    }
  }

  private[dataroom] def modifyAssetPermissions(
    actor: UserId,
    dataRoomWorkflowId: DataRoomWorkflowId,
    userChanges: Map[UserId, DataRoomPermissionChanges] = Map(),
    teamChanges: Map[TeamId, DataRoomPermissionChanges] = Map(),
    mode: FileService.PermissionModMode,
    shouldIgnoreDeletedFiles: Boolean = false,
    actorIp: Option[String],
    modifyPermissionSourceOfEvent: Option[ModifyPermissionSourceOfEvent]
  ) = {
    val userAndTeamChanges: Map[UserOrTeamId, DataRoomPermissionChanges] =
      (userChanges.map { case (userId, changes) => Left(userId) -> changes } ++
        teamChanges.map { case (teamId, changes) => Right(teamId) -> changes }).toMap
    val allFolderIds = (userChanges.values ++ teamChanges.values).flatMap { permissionChanges =>
      permissionChanges.assetPermissions.folderPermissions.keySet ++ permissionChanges.assetPermissions.recursivePermissions.keySet
    }
    val allFileIds = (userChanges.values ++ teamChanges.values).flatMap { permissionChanges =>
      permissionChanges.assetPermissions.filePermissions.keySet
    }
    val modifyFolderPermissionMetadata = modifyPermissionSourceOfEvent
      .fold[Map[FolderId, Map[String, String]]](Map.empty) { sourceOfEvent =>
        allFolderIds.map { folderId =>
          folderId -> Map(ModifyPermissionSourceOfEventMetaData.EventKey -> sourceOfEvent.value)
        }.toMap
      }
    val modifyFilePermissionMetadata =
      modifyPermissionSourceOfEvent.fold[Map[FileId, Map[String, String]]](Map.empty) { sourceOfEvent =>
        allFileIds.map { fileId =>
          fileId -> Map(ModifyPermissionSourceOfEventMetaData.EventKey -> sourceOfEvent.value)
        }.toMap
      }

    for {
      _ <- validateDataRoomAssetPermissionChanges(dataRoomWorkflowId, userAndTeamChanges.values)
      assetChanges <- appendNewRestrictedChanges(
        actor,
        dataRoomWorkflowId,
        userAndTeamChanges
      )
      _ <- fileService.modifyAssetPermissionsRecursively(
        actor = actor,
        changes = assetChanges,
        mode = mode,
        shouldIgnoreDeletedFiles = shouldIgnoreDeletedFiles,
        actorIp = actorIp,
        modifyFolderPermissionMetadata = modifyFolderPermissionMetadata,
        modifyFilePermissionMetadata = modifyFilePermissionMetadata
      )
    } yield ()
  }

  private[dataroom] def transferAssetPermissions(
    actor: UserId,
    teamAndUsersMap: Map[TeamId, Set[UserId]],
    folderIds: Seq[FolderId],
    fileIds: Seq[FileId],
    mode: FileService.PermissionModMode,
    shouldRevokeTeamPermission: Boolean,
    maxBatchCount: Int = 1000,
    actorIp: Option[String]
  ) = {
    for {
      _ <- ZIO.logInfo(
        s"Transferring asset permission for $teamAndUsersMap, folderSize = ${folderIds.size}, fileSize = ${fileIds.size}"
      )
      _ <- ZIO.foreach(folderIds.sortBy(_.folderParts.size).grouped(maxBatchCount).toList) { batchFolderIds =>
        FDBRecordDatabase.transact(
          FDBOperations[(FolderStateStoreOperations, DmsPermissionOperations)].Production
        ) { case (folderStateOps, dmsPermissionOps) =>
          RecordIO.traverse(batchFolderIds) { folderId =>
            dmsPermissionOps.transferTeamPermissions(
              actor,
              folderId,
              teamAndUsersMap,
              folderStateOps.getPermissionMap,
              shouldRevokeTeamPermission
            ) { changes =>
              dmsPermissionOps.modifyFolderPermissions(
                actor,
                folderId,
                changes,
                mode,
                actorIp = actorIp
              )
            }
          }
        }
      }
      _ <- ZIOUtils.foreachParN(4)(fileIds.grouped(maxBatchCount).toList) { batchFileIds =>
        FDBRecordDatabase.transact(
          FDBOperations[(FileStateStoreOperations, DmsPermissionOperations)].Production
        ) { case (fileStateOps, dmsPermissionOps) =>
          RecordIO.parTraverseN(64)(batchFileIds) { fileId =>
            dmsPermissionOps.transferTeamPermissions(
              actor,
              fileId,
              teamAndUsersMap,
              fileStateOps.getPermissionMap,
              shouldRevokeTeamPermission
            ) { changes =>
              dmsPermissionOps.modifyFilePermissions(
                actor,
                fileId,
                changes,
                mode,
                actorIp = actorIp
              )
            }
          }
        }
      }
    } yield ()
  }

  // Check if a data room entity is Anduin internal entity
  // Since there is no way to determine which entity is internal, configuration is used for this task
  def isAnduinInternalDataroomEntity(entityId: EntityId): Boolean = {
    val internalEntities = gondorBackendConfig.anduinInternalEntities.split(',')
    internalEntities.contains(entityId.toString)
  }

}
