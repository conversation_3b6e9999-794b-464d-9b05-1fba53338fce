// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.group

import zio.{Task, ZIO}
import anduin.dataroom.flow.{DataRoomSharedFlowService, DataRoomStateStoreOperations, DataRoomValidateOperations}
import anduin.dataroom.group.DataRoomGroupOperations.AddUsersToGroupResult
import anduin.dataroom.group.DataRoomGroupValidateOperations.{ValidateGroupChangesResult, ValidateGroupUsers}
import anduin.dataroom.group.state.DataRoomGroupCreatedSharedFlowState
import anduin.dataroom.participant.DataRoomParticipantService
import anduin.dataroom.participant.DataRoomParticipantService.UserPermissionChangeMode
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.dataroom.service.DataRoomPermissionService
import anduin.dataroom.service.dmsmetadata.ModifyPermissionSourceOfEventMetaData.ModifyPermissionSourceOfEvent
import anduin.dataroom.service.webhook.DataRoomWebhookProducerService
import anduin.dataroom.validator.DataRoomValidator
import anduin.dms.DmsFeature.DataRoom
import anduin.dms.DmsTreeTraversalOperations
import anduin.dms.service.FileService
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.greylin.{GreylinDataService, modelti, operation}
import anduin.id.dataroom.DataRoomGroupId
import anduin.link.ProtectedLinkStoreOperations
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{FolderId, TeamId}
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.service.AuthenticatedRequestContext
import anduin.stargazer.service.dataroom.{
  DataRoomParticipantRoleChange,
  DataRoomPermissionChanges,
  RemoveUsersFromDataRoomParams
}
import anduin.team.{TeamService, TeamServiceOperations}
import com.anduin.stargazer.endpoints.PermissionChanges
import com.anduin.stargazer.service.utils.ZIOUtils

final case class DataRoomGroupService(
  dataRoomSharedFlowService: DataRoomSharedFlowService,
  dataRoomPermissionService: DataRoomPermissionService,
  dataRoomParticipantService: DataRoomParticipantService,
  teamService: TeamService,
  fileService: FileService,
  greylinDataService: GreylinDataService,
  dataRoomValidator: DataRoomValidator,
  dataRoomWebhookProducer: DataRoomWebhookProducerService
) {

  def getGroupAndMembers(
    groupId: DataRoomGroupId,
    actor: UserId
  ): Task[(DataRoomGroupCreatedSharedFlowState, Set[UserId])] = {
    for {
      (groupState, members) <- FDBRecordDatabase.transact(
        FDBOperations[(DataRoomGroupOperations, DataRoomValidateOperations)].Production
      ) { case (groupOps, validateOps) =>
        for {
          _ <- validateOps.validateAndGetCurrentState(
            groupId.parent,
            actor,
            roleChecks = Set(DataRoomRoleUtils.isInternal)
          )
          (groupState, joinedMembers) <- groupOps.getGroupAndMembers(groupId)
        } yield groupState -> joinedMembers
      }
    } yield groupState -> members
  }

  def getAllGroups(
    dataRoomWorkflowId: DataRoomWorkflowId,
    actor: UserId
  ): Task[Seq[DataRoomGroupCreatedSharedFlowState]] = {
    for {
      allGroups <- FDBRecordDatabase.transact(
        FDBOperations[(DataRoomGroupStateStoreOperations, DataRoomValidateOperations)].Production
      ) { case (stateOps, validateOps) =>
        for {
          _ <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId,
            actor,
            roleChecks = Set(DataRoomRoleUtils.isInternal)
          )
          allGroups <- stateOps.getAllGroups(dataRoomWorkflowId)
        } yield allGroups
      }
    } yield allGroups
  }

  def getAllGroupsWithMembers(
    dataRoomWorkflowId: DataRoomWorkflowId,
    actor: UserId,
    includePendingInvitation: Boolean = true
  ): Task[Seq[(DataRoomGroupCreatedSharedFlowState, Set[UserId])]] = {
    FDBRecordDatabase.transact(
      FDBOperations[
        ((DataRoomGroupStateStoreOperations, DataRoomValidateOperations), TeamServiceOperations)
      ].Production
    ) { case ((stateOps, validateOps), teamOps) =>
      for {
        (teamId, _) <- validateOps.validateAndGetCurrentState(
          dataRoomWorkflowId,
          actor,
          roleChecks = Set(DataRoomRoleUtils.isInternal)
        )
        allGroups <- stateOps.getAllGroups(dataRoomWorkflowId)
        allGroupsWithMembers <- RecordIO.parTraverseN(16)(allGroups) { group =>
          for {
            allMembers <- teamOps.getTeamMemberInfo(group.teamId).map(_.joinedMembers)
            members <-
              if (!includePendingInvitation) {
                teamOps.getTeamMemberInfo(teamId).map(_.joinedMembers.intersect(allMembers))
              } else {
                RecordIO.succeed(allMembers)
              }
          } yield group -> members
        }
      } yield allGroupsWithMembers
    }
  }

  def createGroup(
    params: CreateDataRoomGroupParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[CreateDataRoomGroupResponse] = {
    val groupPermissions = DataRoomPermissionChanges(
      roleSet = Some(params.role),
      assetPermissions = params.assetPermissions
    )
    val sanitizedName = params.name.trim
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is creating group ${params.name} in ${params.dataRoomWorkflowId.idString}"
      )
      (groupId, teamId) <- FDBRecordDatabase.transact(
        FDBOperations[(DataRoomGroupOperations, DataRoomValidateOperations)].Production
      ) { case (groupOps, validateOps) =>
        for {
          _ <- RecordIO.validate(sanitizedName.nonEmpty)(new RuntimeException("Group name must not be empty"))
          (_, dataRoomState) <- validateOps.validateAndGetCurrentState(
            params.dataRoomWorkflowId,
            actor,
            roleChecks = Set(DataRoomRoleUtils.isAdmin)
          )
          _ <- dataRoomPermissionService.validatePermissionChanges(
            validateOps,
            params.dataRoomWorkflowId,
            dataRoomState,
            currentRoleAndChangeList = List((None, groupPermissions))
          )
          (groupId, teamId) <- groupOps.createGroup(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            entityId = dataRoomState.creatorEntityId,
            actor = actor,
            name = sanitizedName,
            role = params.role,
            actorIpOpt = ctx.flatMap(_.getClientIP)
          )
        } yield groupId -> teamId
      }
      _ <- greylinDataService.runUnit(
        operation.DataRoomGroupOperations.insert(
          modelti.DataRoomGroup(
            groupId,
            params.dataRoomWorkflowId,
            params.name,
            dataRoomPermissionService.convertRoleToGreylinRole(params.role)
          )
        )
      )
      // Set group permission
      _ <- dataRoomPermissionService.modifyAssetPermissions(
        actor,
        params.dataRoomWorkflowId,
        teamChanges = Map(teamId -> groupPermissions),
        mode = FileService.PermissionModMode.Validate(onFailure = FileService.PermissionModMode.OnFailure.BestEffort),
        actorIp = ctx.flatMap(_.getClientIP),
        modifyPermissionSourceOfEvent = Some(ModifyPermissionSourceOfEvent.CreateDataRoomGroup)
      )
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
    } yield CreateDataRoomGroupResponse(
      groupId = groupId
    )
  }

  def renameGroup(
    params: RenameDataRoomGroupParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    dataRoomValidator.checkJoinedAdmin(params, actor) *> renameGroupUnsafe(params, actor, ctx)
  }

  def renameGroupUnsafe(
    params: RenameDataRoomGroupParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    val sanitizedName = params.name.trim
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is renaming group ${params.groupId.idString} with name ${params.name}")
      _ <- FDBRecordDatabase.transact(DataRoomGroupOperations.Production) { groupOps =>
        for {
          _ <- RecordIO.validate(sanitizedName.nonEmpty)(new RuntimeException("Group name must not be empty"))
          groupState <- groupOps.validateGroupStatusAndGetState(params.groupId)
          _ <- groupOps.flowOps.renameGroup(
            params.groupId,
            actor,
            sanitizedName,
            groupState.name,
            ctx.flatMap(_.getClientIP)
          )
        } yield ()
      }
      _ <- greylinDataService.runUnit(
        operation.DataRoomGroupOperations.update(params.groupId)(_.withName(params.name))
      )
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.groupId.parent)
    } yield ()
  }

  def updateGroupPermission(
    params: UpdateDataRoomGroupPermissionsParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext],
    notifyUpdate: Boolean = true
  ): Task[Unit] = {
    val dataRoomWorkflowId = params.dataRoomWorkflowId
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is updating group ${params.groupId.idString} permission, changes = ${params.permissionChanges}"
      )
      (groupState, currentRoleMap, updatedRoleMap) <- FDBRecordDatabase.transact(
        DataRoomGroupValidateOperations.Production
      ) { groupValidateOps =>
        val updatedGroupRoles = params.permissionChanges.roleSet.map { role =>
          params.groupId -> role
        }.toMap
        for {
          ValidateGroupChangesResult(groupState, dataRoomState, currentRoleMap, updatedRoleMap) <- groupValidateOps
            .validateGroupChanges(
              params.groupId,
              ValidateGroupUsers.AllGroupMembers,
              actor,
              updatedGroupRoles = updatedGroupRoles
            )
          _ <- dataRoomPermissionService.validatePermissionChanges(
            groupValidateOps.validateOps,
            params.groupId.parent,
            dataRoomState,
            currentRoleAndChangeList = List((Some(groupState.role), params.permissionChanges))
          )
          _ <- groupValidateOps.groupOps.updateGroupPermissions(
            groupId = params.groupId,
            actor = actor,
            updatedRole = params.permissionChanges.roleSet.getOrElse(groupState.role),
            actorIpOpt = ctx.flatMap(_.getClientIP)
          )
        } yield (groupState, currentRoleMap, updatedRoleMap)
      }
      _ <- ZIOUtils.traverseOption(params.permissionChanges.roleSet) { role =>
        greylinDataService.runUnit(
          operation.DataRoomGroupOperations
            .update(params.groupId)(_.copy(groupRole = dataRoomPermissionService.convertRoleToGreylinRole(role)))
        )
      }
      // Update group permission
      _ <- dataRoomPermissionService.modifyAssetPermissions(
        actor,
        params.groupId.parent,
        teamChanges = Map(groupState.teamId -> params.permissionChanges),
        mode = FileService.PermissionModMode.Validate(onFailure = FileService.PermissionModMode.OnFailure.BestEffort),
        actorIp = ctx.flatMap(_.getClientIP),
        modifyPermissionSourceOfEvent = Some(ModifyPermissionSourceOfEvent.AssignedToTargetAsset)
      )
      _ <- dataRoomParticipantService.modifyPermissionsWhenRoleChanges(
        dataRoomWorkflowId,
        currentRoleMap.toSeq.map { (userId, oldRole) =>
          DataRoomParticipantRoleChange(userId, Some(oldRole), updatedRoleMap.get(userId))
        },
        actor,
        mode = UserPermissionChangeMode.Group(canChangeRole = true),
        ctx
      )
      _ <- ZIOUtils.when(notifyUpdate) {
        dataRoomSharedFlowService.modifyLastUpdate(params.groupId.parent) *>
          dataRoomSharedFlowService.modifyFileUpdated(params.groupId.parent)
      }
    } yield ()
  }

  private def deleteSingleGroup(
    groupId: DataRoomGroupId,
    participantSettings: DeleteDataRoomGroupsParams.ParticipantSettings,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    for {
      (groupState, participants, currentRoleMap, updatedRoleMap) <- FDBRecordDatabase.transact(
        DataRoomGroupValidateOperations.Production
      ) { groupValidateOps =>
        for {
          ValidateGroupChangesResult(groupState, _, currentRoleMap, updatedRoleMap) <- groupValidateOps
            .validateGroupChanges(
              groupId,
              ValidateGroupUsers.AllGroupMembers,
              actor,
              removedGroupIds = Set(groupId)
            )
          participants <- groupValidateOps.validateOps.roleOps.participantOps
            .getParticipants(groupId.parent, currentRoleMap.keySet.toSeq)
        } yield (groupState, participants, currentRoleMap, updatedRoleMap)
      }
      singleGroupUsers = participants.filter(_.groupIds == Set(groupId)).map(_.userId)
      _ <- transferAssetPermissions(
        groupId.parent,
        teamIdToRemovedUsersMap = Map(
          groupState.teamId -> singleGroupUsers.toSet
        ),
        shouldRevokeTeamPermission = true,
        actor,
        ctx
      )
      _ <- dataRoomParticipantService.modifyPermissionsWhenRoleChanges(
        groupId.parent,
        currentRoleMap.toSeq.map { (userId, oldRole) =>
          DataRoomParticipantRoleChange(userId, Some(oldRole), updatedRoleMap.get(userId))
        },
        actor,
        mode = UserPermissionChangeMode.Group(canChangeRole = true),
        ctx
      )
      _ <- FDBRecordDatabase.transact(DataRoomGroupOperations.Production)(
        _.deleteGroup(
          groupId = groupId,
          actor = actor,
          actorIpOpt = ctx.flatMap(_.getClientIP)
        )
      )
      _ <- participantSettings match {
        case DeleteDataRoomGroupsParams.ParticipantSettings.Keep => ZIO.unit
        case DeleteDataRoomGroupsParams.ParticipantSettings.Remove(shouldNotifyEmail) =>
          dataRoomParticipantService
            .removeUsers(
              params = RemoveUsersFromDataRoomParams(
                dataRoomWorkflowId = groupId.parent,
                userIds = currentRoleMap.keySet,
                doNotNotifyByEmail = !shouldNotifyEmail
              ),
              actor = actor,
              shouldRemoveFromCurrentGroup = false,
              httpContext = ctx
            )
      }
      _ <- greylinDataService.runUnit(
        operation.DataRoomGroupOperations.delete(List(groupId))
      )
      _ <- dataRoomWebhookProducer.produceGroupDeletedEvent(
        groupId.parent,
        groupId,
        currentRoleMap.keySet.toSeq
      )
    } yield ()
  }

  private def validateEmptyGroups(dataRoomWorkflowId: DataRoomWorkflowId, groupIds: Seq[DataRoomGroupId])
    : Task[Unit] = {
    FDBRecordDatabase.transact(
      FDBOperations[
        (DataRoomStateStoreOperations, (DataRoomGroupStateStoreOperations, TeamServiceOperations))
      ].Production
    ) { case (dataRoomStateOps, (groupOps, teamOps)) =>
      for {
        dataRoomState <- dataRoomStateOps.getState(dataRoomWorkflowId)
        _ <- RecordIO.traverse(groupIds) { groupId =>
          for {
            groupState <- groupOps.getState(groupId)
            joinedMembers <- teamOps.getTeamMemberInfo(groupState.teamId).map(_.joinedMembers)
            groupLinks = dataRoomState.linkInvitationMap.count(_._2.groupIds.contains(groupId))
            _ <- RecordIO.validate(joinedMembers.isEmpty && groupLinks == 0) {
              new RuntimeException(s"Data room group ${groupId} has members or invitation links")
            }
          } yield ()
        }
      } yield ()
    }
  }

  def deleteGroups(
    params: DeleteDataRoomGroupsParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is deleting groups ${params.groupIds}")
      _ <- params.groupIds.toList match {
        case Nil => ZIO.unit
        case groupId :: Nil =>
          deleteSingleGroup(groupId, params.participantSettings, actor, ctx)
        case groupIds =>
          for {
            _ <- validateEmptyGroups(params.dataRoomWorkflowId, groupIds)
            _ <- ZIO.foreach(groupIds)(groupId => deleteSingleGroup(groupId, params.participantSettings, actor, ctx))
          } yield ()
      }
      _ <- disableGroupLinkInvitations(params.dataRoomWorkflowId, params.groupIds)
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
      _ <- dataRoomSharedFlowService.modifyFileUpdated(params.dataRoomWorkflowId)
    } yield ()
  }

  def addUsersToGroup(
    params: AddUsersToDataRoomGroupParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Seq[UserId]] = {
    val validator = dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkInvitedOrJoinedMembers
    validator.validate(params, actor) *> addUsersToGroupUnsafe(params, actor, ctx)
  }

  def addUsersToMultipleGroupsUnsafe(
    params: AddUsersToMultipleGroupsParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    for {
      addMembers <- ZIO
        .foreach(params.userMap.toSeq) { (groupId, userIds) =>
          addUsersToGroupUnsafe(AddUsersToDataRoomGroupParams(groupId, userIds), actor, ctx)
        }
        .map(_.flatten.toSet)
      _ <- ZIOUtils.when(addMembers.nonEmpty) {
        dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId) *>
          dataRoomSharedFlowService.modifyFileUpdated(params.dataRoomWorkflowId)
      }
    } yield ()
  }

  private def addUsersToGroupUnsafe(
    params: AddUsersToDataRoomGroupParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Seq[UserId]] = {
    for {
      dataRoomWorkflowId = params.groupId.parent
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is adding users ${concatUserIds(params.userIds)} to group ${params.groupId.idString}"
      )
      ValidateGroupChangesResult(_, _, currentRoleMap, updatedRoleMap) <- DataRoomGroupValidateOperations.transact(
        _.validateGroupChanges(
          params.groupId,
          ValidateGroupUsers.NewGroupMembers(params.userIds),
          actor,
          roleChecks = Set.empty,
          addedGroupIds = Set(params.groupId)
        )
      )

      AddUsersToGroupResult(addedUnassignedUsers, addedGroupUsers, _) <- FDBRecordDatabase.transact(
        FDBOperations[DataRoomGroupOperations].Production
      )(
        _.addUsersToGroup(
          params.groupId,
          params.userIds,
          actor,
          ctx.flatMap(_.getClientIP)
        )
      )
      _ <- ZIOUtils.when(addedUnassignedUsers.nonEmpty)(
        // Remove all users individual permission of new added users
        fileService.modifyPermissionsRecursively(
          actor = actor,
          rootFolderId = FolderId.channelSystemFolderId(params.groupId.parent),
          changes = PermissionChanges(revokedUsers = addedUnassignedUsers.toSet),
          mode = FileService.PermissionModMode.Unsafe
        )
      )
      addedMembers = addedUnassignedUsers ++ addedGroupUsers
      // Update permissions when participant's role changed
      _ <- ZIOUtils.when(addedMembers.nonEmpty) {
        for {
          _ <- dataRoomParticipantService.modifyPermissionsWhenRoleChanges(
            dataRoomWorkflowId,
            addedMembers.map { userId =>
              DataRoomParticipantRoleChange(userId, currentRoleMap.get(userId), updatedRoleMap.get(userId))
            },
            actor,
            UserPermissionChangeMode.Group(canChangeRole = true),
            ctx
          )
          // send webhook event
          _ <- dataRoomWebhookProducer.produceUserAddToGroupEvent(
            dataRoomWorkflowId,
            addedMembers,
            params.groupId
          )
        } yield ()
      }
    } yield addedMembers
  }

  def removeUsersFromGroup(
    params: RemoveUsersFromDataRoomGroupParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    val validator = dataRoomValidator.checkJoinedAdmin && dataRoomValidator.checkInvitedOrJoinedMembers
    validator.validate(params, actor) *> removeUsersFromGroupUnsafe(params, actor, ctx)
  }

  def removeUsersFromGroupUnsafe(
    params: RemoveUsersFromDataRoomGroupParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    val dataRoomWorkflowId = params.groupId.parent
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is removing users ${concatUserIds(params.userIds)} from group ${params.groupId.idString}"
      )
      _ <- ZIOUtils.validate(params.userIds.nonEmpty)(new RuntimeException("Removed user list is empty"))
      (groupState, participants, currentRoleMap, updatedRoleMap) <- DataRoomGroupValidateOperations.transact {
        groupValidateOps =>
          for {
            ValidateGroupChangesResult(groupState, _, currentRoleMap, updatedRoleMap) <-
              groupValidateOps.validateGroupChanges(
                params.groupId,
                ValidateGroupUsers.CurrentGroupMembers(params.userIds),
                actor,
                removedGroupIds = Set(params.groupId)
              )
            participants <- groupValidateOps.validateOps.roleOps.participantOps
              .getParticipants(dataRoomWorkflowId, params.userIds.toSeq)
          } yield (groupState, participants, currentRoleMap, updatedRoleMap)
      }
      removedMembers = params.userIds
      // Only transfer asset permissions to unassigned users after removing
      removedUnassignedMembers = participants
        .filter(_.groupIds == Set(params.groupId))
        .map(_.userId)
      _ <- transferAssetPermissions(
        dataRoomWorkflowId = params.groupId.parent,
        teamIdToRemovedUsersMap = Map(groupState.teamId -> removedUnassignedMembers.toSet),
        shouldRevokeTeamPermission = false,
        actor = actor,
        ctx = ctx
      )
      _ <- FDBRecordDatabase.transact(
        FDBOperations[DataRoomGroupOperations].Production
      ) { groupOps =>
        for {
          _ <- groupOps.removeUsersFromGroup(
            groupId = params.groupId,
            userIds = params.userIds,
            actor = actor,
            actorIpOpt = ctx.flatMap(_.getClientIP)
          )
        } yield ()
      }
      _ <- dataRoomParticipantService.modifyPermissionsWhenRoleChanges(
        dataRoomWorkflowId,
        removedMembers.map { userId =>
          DataRoomParticipantRoleChange(userId, currentRoleMap.get(userId), updatedRoleMap.get(userId))
        }.toSeq,
        actor,
        UserPermissionChangeMode.Group(canChangeRole = true),
        ctx
      )
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.groupId.parent).unit
      _ <- dataRoomWebhookProducer.produceUserRemoveFromGroupEvent(
        params.groupId.parent,
        removedMembers.toSeq,
        params.groupId
      )
    } yield ()
  }

  private def disableGroupLinkInvitations(
    dataRoomWorkflowId: DataRoomWorkflowId,
    deletedGroupIds: Set[DataRoomGroupId]
  ): Task[Unit] = {
    FDBRecordDatabase.transact(
      FDBOperations[
        ((ProtectedLinkStoreOperations, DataRoomStateStoreOperations), DataRoomGroupStateStoreOperations)
      ].Production
    ) { case ((linkOps, stateOps), groupOps) =>
      for {
        createdState <- stateOps.getState(dataRoomWorkflowId)
        groupMap <- groupOps.getAllGroupsMap(dataRoomWorkflowId, includeArchived = true)
        _ <- RecordIO.traverse(createdState.linkInvitationMap.toSeq) { (linkId, linkInvitation) =>
          val isAllGroupDeleted =
            linkInvitation.groupIds.nonEmpty && linkInvitation.groupIds.forall(groupMap.get(_).exists(_.isArchived))
          RecordIO.when(linkInvitation.groupIds.intersect(deletedGroupIds).nonEmpty && isAllGroupDeleted) {
            // Just disable the link
            linkOps.update(
              linkId = linkId,
              passwordChange = None,
              expiryDateChange = None,
              isDisabledChange = Option(true),
              whitelistDomainsChange = None,
              isRequireApprovalChange = None
            )
          }
        }
      } yield ()
    }
  }

  private def transferAssetPermissions(
    dataRoomWorkflowId: DataRoomWorkflowId,
    teamIdToRemovedUsersMap: Map[TeamId, Set[UserId]],
    shouldRevokeTeamPermission: Boolean,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ) = {
    for {
      _ <- ZIO.logInfo(s"Transfer asset permissions of data room $dataRoomWorkflowId for $teamIdToRemovedUsersMap")
      (folderIds, fileIds) <- FDBRecordDatabase.transact(DmsTreeTraversalOperations.Production) {
        _.getAllFoldersAndFiles(
          actor = actor,
          rootFolders = Seq(FolderId.channelSystemFolderId(dataRoomWorkflowId)),
          requiredPermission = FileFolderPermission.Own
        )
      }
      _ <- dataRoomPermissionService.transferAssetPermissions(
        actor,
        teamIdToRemovedUsersMap,
        folderIds,
        fileIds,
        mode = FileService.PermissionModMode.Unsafe,
        shouldRevokeTeamPermission = shouldRevokeTeamPermission,
        actorIp = ctx.flatMap(_.getClientIP)
      )
    } yield ()
  }

  private def concatUserIds(userIds: Set[UserId]): String = {
    userIds.map(_.idString).mkString(", ")
  }

}
