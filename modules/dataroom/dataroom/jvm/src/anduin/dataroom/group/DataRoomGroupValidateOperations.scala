// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.group

import anduin.dataroom.flow.DataRoomValidateOperations
import anduin.dataroom.flow.DataRoomValidateOperations.SeatCheck
import anduin.dataroom.group.DataRoomGroupValidateOperations.{ValidateGroupChangesResult, ValidateGroupUsers}
import anduin.dataroom.group.state.DataRoomGroupCreatedSharedFlowState
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.dataroom.state.DataRoomCreatedSharedFlowState
import anduin.fdb.record.FDBOperations
import anduin.fdb.record.model.{RecordIO, RecordTask}
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId

final case class DataRoomGroupValidateOperations(
  groupOps: DataRoomGroupOperations,
  validateOps: DataRoomValidateOperations
) {

  def validateGroupChanges(
    groupId: DataRoomGroupId,
    users: ValidateGroupUsers,
    actor: UserId,
    roleChecks: Set[DataRoomRoleUtils.Check] = Set(DataRoomRoleUtils.isAdmin),
    updatedIndividualRoles: Map[UserId, DataRoomRole] = Map.empty[UserId, DataRoomRole],
    updatedGroupRoles: Map[DataRoomGroupId, DataRoomRole] = Map.empty[DataRoomGroupId, DataRoomRole],
    addedGroupIds: Set[DataRoomGroupId] = Set.empty[DataRoomGroupId],
    removedGroupIds: Set[DataRoomGroupId] = Set.empty[DataRoomGroupId]
  ): RecordTask[ValidateGroupChangesResult] = {
    val dataRoomWorkflowId = groupId.parent
    for {
      groupState <- groupOps.validateGroupStatusAndGetState(groupId)
      _ <- RecordIO.validateNot(groupState.isArchived)(
        new RuntimeException(s"Group ${groupId.idString} is archived")
      )
      userIds <- users match {
        case ValidateGroupUsers.AllGroupMembers =>
          groupOps.teamOps.getTeamMemberInfo(groupState.teamId).map(_.joinedMembers)
        case ValidateGroupUsers.CurrentGroupMembers(userIds) =>
          for {
            groupUserIds <- groupOps.teamOps.getTeamMemberInfo(groupState.teamId).map(_.joinedMembers)
            _ <- RecordIO.validate(userIds.forall(groupUserIds.contains))(
              new RuntimeException(s"Invalid group $groupId user ids: ${userIds.diff(groupUserIds)}")
            )
          } yield userIds
        case ValidateGroupUsers.NewGroupMembers(userIds, ignoreCurrentMember) =>
          if (ignoreCurrentMember) {
            RecordIO.succeed(userIds)
          } else {
            for {
              groupUserIds <- groupOps.teamOps.getTeamMemberInfo(groupState.teamId).map(_.joinedMembers)
              _ <- RecordIO.validateNot(userIds.exists(groupUserIds.contains))(
                new RuntimeException(s"Invalid user ids: ${userIds.diff(groupUserIds)}")
              )
            } yield userIds
          }
      }
      currentRoleMap <- validateOps.roleOps.getParticipantRoleMap(dataRoomWorkflowId, userIds)
      updatedRoleMap <- validateOps.roleOps.getParticipantRoleMap(
        dataRoomWorkflowId,
        userIds,
        updatedIndividualRoles = updatedIndividualRoles,
        updatedGroupRoles = updatedGroupRoles,
        addedGroupIds = addedGroupIds,
        removedGroupIds = removedGroupIds
      )
      (_, dataRoomState) <- validateOps.validateAndGetCurrentState(
        dataRoomWorkflowId,
        actor,
        roleChecks = roleChecks,
        checkTotalSeats = SeatCheck.Require(
          userType = SeatCheck.UserType.AllUsers,
          modification = Some(SeatCheck.Modification.ExistingUsers(updatedRoleMap))
        )
      )
      _ <- validateOps.validateAtLeastOneJoinedAdminWhenUpdatingRoles(
        dataRoomWorkflowId,
        updatedRoleMap,
        actor
      )
    } yield ValidateGroupChangesResult(groupState, dataRoomState, currentRoleMap, updatedRoleMap)
  }

}

object DataRoomGroupValidateOperations
    extends FDBOperations.Multi[
      DataRoomGroupOperations,
      DataRoomValidateOperations,
      DataRoomGroupValidateOperations
    ] {

  override def combine(
    groupOps: DataRoomGroupOperations,
    validateOps: DataRoomValidateOperations
  ): DataRoomGroupValidateOperations = DataRoomGroupValidateOperations(groupOps, validateOps)

  final case class ValidateGroupChangesResult(
    groupState: DataRoomGroupCreatedSharedFlowState,
    dataRoomState: DataRoomCreatedSharedFlowState,
    currentRoleMap: Map[UserId, DataRoomRole],
    updatedRoleMap: Map[UserId, DataRoomRole]
  )

  sealed trait ValidateGroupUsers derives CanEqual

  object ValidateGroupUsers {
    case object AllGroupMembers extends ValidateGroupUsers
    final case class CurrentGroupMembers(userIds: Set[UserId]) extends ValidateGroupUsers

    final case class NewGroupMembers(userIds: Set[UserId], ignoreCurrentMember: Boolean = true)
        extends ValidateGroupUsers

  }

}
