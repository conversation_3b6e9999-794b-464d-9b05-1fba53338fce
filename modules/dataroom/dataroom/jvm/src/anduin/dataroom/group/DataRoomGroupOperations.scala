// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.group

import anduin.dataroom.group.DataRoomGroupOperations.{AddUsersToGroupResult, RemovedUsersByGroup}
import anduin.dataroom.group.state.DataRoomGroupCreatedSharedFlowState
import anduin.dataroom.participant.DataRoomParticipantRoleOperations
import anduin.dataroom.role.DataRoomRole
import anduin.fdb.record.FDBOperations
import anduin.fdb.record.model.{RecordIO, RecordTask}
import anduin.id.dataroom.DataRoomGroupId
import anduin.id.entity.EntityId
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{DataRoomGroupIdFactory, TeamId}
import anduin.team.TeamServiceOperations
import anduin.team.TeamServiceParams.{AddMemberParams, CreateNewTeamParams, RemoveMemberParams}

final case class DataRoomGroupOperations(
  flowOps: DataRoomGroupFlowOperations,
  stateOps: DataRoomGroupStateStoreOperations,
  roleOps: DataRoomParticipantRoleOperations,
  teamOps: TeamServiceOperations
) {

  lazy val participantOps = roleOps.participantOps

  def validateGroupStatusAndGetState(
    groupId: DataRoomGroupId,
    includeArchived: Boolean = false
  ): RecordTask[DataRoomGroupCreatedSharedFlowState] = {
    for {
      state <- stateOps.getState(groupId)
      _ <- RecordIO.validate(!state.isArchived || includeArchived)(
        new RuntimeException(s"Group ${groupId.idString} is archived")
      )
    } yield state
  }

  def getGroupAndMembers(groupId: DataRoomGroupId): RecordTask[(DataRoomGroupCreatedSharedFlowState, Set[UserId])] = {
    for {
      groupState <- stateOps.getState(groupId)
      joinedMembers <- teamOps.getTeamMemberInfo(groupState.teamId).map(_.joinedMembers)
    } yield groupState -> joinedMembers
  }

  def createGroup(
    dataRoomWorkflowId: DataRoomWorkflowId,
    entityId: EntityId,
    actor: UserId,
    name: String,
    role: DataRoomRole,
    actorIpOpt: Option[String] = None
  ): RecordTask[(DataRoomGroupId, TeamId)] = {
    val groupId = DataRoomGroupIdFactory.unsafeRandomId(dataRoomWorkflowId)
    for {
      teamId <- teamOps.createNewTeam(
        CreateNewTeamParams(
          parent = groupId,
          associatedEntity = Some(entityId)
        )
      )
      // Create group model
      _ <- flowOps.createGroup(
        groupId,
        teamId,
        actor,
        name,
        role,
        actorIpOpt
      )
    } yield groupId -> teamId
  }

  def updateGroupPermissions(
    groupId: DataRoomGroupId,
    actor: UserId,
    updatedRole: DataRoomRole,
    actorIpOpt: Option[String] = None
  ): RecordTask[Unit] = {
    for {
      _ <- flowOps.updateGroupPermissions(
        groupId,
        actor,
        updatedRole,
        actorIpOpt
      )
    } yield ()
  }

  def deleteGroup(groupId: DataRoomGroupId, actor: UserId, actorIpOpt: Option[String] = None): RecordTask[Set[UserId]] = {
    for {
      groupState <- stateOps.getState(groupId)
      joinedMembers <- teamOps.getTeamMemberInfo(groupState.teamId).map(_.joinedMembers)
      // Remove all joined members from group team
      _ <- RecordIO.parTraverseN(4)(joinedMembers) { userId =>
        teamOps.removeMember(
          RemoveMemberParams(
            remover = actor,
            removee = userId,
            teamId = groupState.teamId,
            skipPermission = true
          )
        )
      }
      _ <- flowOps.deleteGroup(
        groupId,
        actor,
        actorIpOpt
      )
      _ <- participantOps.updateParticipants(
        groupId.parent,
        joinedMembers,
        model => model.copy(groupIds = model.groupIds - groupId)
      )
      _ <- RecordIO.traverse(joinedMembers) { userId =>
        roleOps.updateParticipantRole(
          groupId.parent,
          userId,
          Some(groupState.role),
          canInviteOpt = None
        )
      }
    } yield joinedMembers
  }

  def addUsersToGroup(
    groupId: DataRoomGroupId,
    userIds: Set[UserId],
    actor: UserId,
    actorIpOpt: Option[String] = None
  ): RecordTask[AddUsersToGroupResult] = {
    val dataRoomWorkflowId = groupId.parent
    for {
      allGroupsMap <- stateOps.getAllGroupsMap(dataRoomWorkflowId)
      groupState <- RecordIO.fromOption(
        allGroupsMap.get(groupId),
        new RuntimeException(s"Cannot get group state of ${groupId.idString}")
      )
      participantModels <- participantOps.getParticipants(dataRoomWorkflowId, userIds.toSeq)
      (unassignedUsers, groupUsers) = participantModels.partition(_.groupIds.isEmpty)
      (currentGroupUsers, newGroupUsers) = groupUsers.partition(_.groupIds.contains(groupId))
      addedMembers = (unassignedUsers ++ newGroupUsers).map(_.userId)
      _ <- RecordIO.logInfo(
        s"Adding users into data room group ${groupId}:\nunassignedUsers=${unassignedUsers.map(_.userId)}\nnewGroupUsers=${newGroupUsers.map(_.userId)}\ncurrentGroupUsers=${currentGroupUsers.map(_.userId)}"
      )
      _ <- participantOps.updateParticipants(
        groupId.parent,
        addedMembers.toSet,
        model => model.copy(groupIds = model.groupIds + groupId)
      )
      _ <- RecordIO.traverse(addedMembers) { userId =>
        for {
          _ <- teamOps.addMember(
            AddMemberParams(
              inviter = actor,
              invitee = userId,
              teamId = groupState.teamId,
              skipPermission = true
            )
          )
          _ <- roleOps.updateParticipantRole(
            groupId.parent,
            userId,
            Some(groupState.role),
            canInviteOpt = None
          )
        } yield ()
      }
      // Record activity
      _ <- RecordIO.when(addedMembers.nonEmpty) {
        flowOps.addUsersToGroup(
          groupId,
          actor,
          addedMembers,
          actorIpOpt
        )
      }
    } yield AddUsersToGroupResult(
      addedUnassignedUsers = unassignedUsers.map(_.userId),
      addedGroupUsers = newGroupUsers.map(_.userId),
      currentGroupUsers = currentGroupUsers.map(_.userId)
    )
  }

  def removeUsersFromGroup(
    groupId: DataRoomGroupId,
    userIds: Set[UserId],
    actor: UserId,
    actorIpOpt: Option[String] = None
  ): RecordTask[Unit] = {
    RecordIO.when(userIds.nonEmpty) {
      for {
        _ <- RecordIO.logInfo(s"User ${actor} is removing ${userIds} from data room group ${groupId}")
        groupState <- stateOps.getState(groupId)
        _ <- RecordIO.parTraverseN(4)(userIds) { userId =>
          teamOps.removeMember(
            RemoveMemberParams(
              remover = actor,
              removee = userId,
              teamId = groupState.teamId,
              skipPermission = true
            )
          )
        }
        _ <- participantOps.updateParticipants(
          groupId.parent,
          userIds,
          model => model.copy(groupIds = model.groupIds - groupId)
        )
        _ <- RecordIO.traverse(userIds) { userId =>
          roleOps.updateParticipantRole(
            groupId.parent,
            userId,
            Some(groupState.role),
            canInviteOpt = None
          )
        }
        _ <- flowOps.removeUsersFromGroup(
          groupId,
          actor,
          userIds,
          actorIpOpt
        )
      } yield ()
    }
  }

  def removeUsersFromCurrentGroup(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userIds: Seq[UserId],
    actor: UserId,
    actorIpOpt: Option[String] = None
  ): RecordTask[RemovedUsersByGroup] = {
    for {
      participants <- participantOps.getParticipants(dataRoomWorkflowId, userIds)
      removedUsersByGroup <- RecordIO.succeed(
        participants
          .flatMap(user => user.groupIds.map(_ -> user.userId))
          .groupBy(_._1)
          .view
          .mapValues(_.map(_._2))
          .toMap
      )
      _ <- RecordIO.parTraverseN(64)(removedUsersByGroup.toList) { case (groupId, removedUsers) =>
        removeUsersFromGroup(
          groupId,
          removedUsers.toSet,
          actor,
          actorIpOpt
        )
      }
    } yield removedUsersByGroup
  }

}

object DataRoomGroupOperations
    extends FDBOperations.Multi[
      (DataRoomGroupFlowOperations, DataRoomGroupStateStoreOperations),
      (DataRoomParticipantRoleOperations, TeamServiceOperations),
      DataRoomGroupOperations
    ] {

  def combine(
    a: (DataRoomGroupFlowOperations, DataRoomGroupStateStoreOperations),
    b: (DataRoomParticipantRoleOperations, TeamServiceOperations)
  ): DataRoomGroupOperations =
    DataRoomGroupOperations(
      a._1,
      a._2,
      b._1,
      b._2
    )

  type RemovedUsersByGroup = Map[DataRoomGroupId, List[UserId]]

  final case class AddUsersToGroupResult(
    addedUnassignedUsers: Seq[UserId],
    addedGroupUsers: Seq[UserId],
    currentGroupUsers: Seq[UserId]
  )

}
