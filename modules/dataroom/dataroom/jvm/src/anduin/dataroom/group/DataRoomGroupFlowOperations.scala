// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.group

import anduin.dataroom.group.event.{
  AddUsersToGroup,
  CreateGroup,
  DeleteGroup,
  RemoveUsersFromGroup,
  RenameGroup,
  UpdateGroupPermissions
}
import anduin.dataroom.group.state.DataRoomGroupCreatedSharedFlowState
import anduin.dataroom.role.DataRoomRole
import anduin.fdb.record.FDBOperations
import anduin.fdb.record.model.RecordTask
import anduin.flow.fdb.FlowOperations
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId
import anduin.model.id.TeamId

final case class DataRoomGroupFlowOperations(
  stateOps: DataRoomGroupStateStoreOperations,
  eventOps: DataRoomGroupEventStoreOperations
) extends FlowOperations(
      stateOps,
      eventOps,
      DataRoomGroupFlow.eventHandler
    ) {

  def createGroup(
    groupId: DataRoomGroupId,
    teamId: TeamId,
    actor: UserId,
    name: String,
    role: DataRoomRole,
    actorIpOpt: Option[String] = None
  ): RecordTask[DataRoomGroupCreatedSharedFlowState] = {
    for {
      createdState <- executeEvent(groupId) {
        CreateGroup.of(
          Some(groupId),
          _,
          _,
          actor = Some(actor),
          actorIp = actorIpOpt,
          name = name,
          teamId = teamId,
          role = role
        )
      }
    } yield createdState
  }

  def deleteGroup(
    groupId: DataRoomGroupId,
    actor: UserId,
    actorIpOpt: Option[String] = None
  ): RecordTask[DataRoomGroupCreatedSharedFlowState] = {
    executeEvent(groupId)(
      DeleteGroup(
        Some(groupId),
        _,
        _,
        Some(actor),
        actorIpOpt
      )
    )
  }

  def updateGroupPermissions(
    groupId: DataRoomGroupId,
    actor: UserId,
    role: DataRoomRole,
    actorIpOpt: Option[String] = None
  ): RecordTask[DataRoomGroupCreatedSharedFlowState] = {
    executeEvent(groupId) {
      UpdateGroupPermissions(
        Some(groupId),
        _,
        _,
        Some(actor),
        role,
        actorIpOpt
      )
    }
  }

  def renameGroup(
    groupId: DataRoomGroupId,
    actor: UserId,
    newName: String,
    oldName: String,
    actorIpOpt: Option[String] = None
  ): RecordTask[DataRoomGroupCreatedSharedFlowState] = {
    executeEvent(groupId) {
      RenameGroup(
        Some(groupId),
        _,
        _,
        Some(actor),
        actorIpOpt,
        newName,
        oldName
      )
    }
  }

  def addUsersToGroup(
    groupId: DataRoomGroupId,
    actor: UserId,
    addedMembers: Seq[UserId],
    actorIpOpt: Option[String] = None
  ): RecordTask[DataRoomGroupCreatedSharedFlowState] = {
    executeEvent(groupId) {
      AddUsersToGroup(
        Some(groupId),
        _,
        _,
        Some(actor),
        actorIpOpt,
        addedMembers
      )
    }
  }

  def removeUsersFromGroup(
    groupId: DataRoomGroupId,
    actor: UserId,
    userIds: Set[UserId],
    actorIpOpt: Option[String] = None
  ): RecordTask[DataRoomGroupCreatedSharedFlowState] = {
    executeEvent(groupId) {
      RemoveUsersFromGroup(
        Some(groupId),
        _,
        _,
        Some(actor),
        actorIpOpt,
        userIds.toSeq
      )
    }
  }

}

object DataRoomGroupFlowOperations
    extends FDBOperations.Multi[
      DataRoomGroupStateStoreOperations,
      DataRoomGroupEventStoreOperations,
      DataRoomGroupFlowOperations
    ] {

  def combine(a: DataRoomGroupStateStoreOperations, b: DataRoomGroupEventStoreOperations): DataRoomGroupFlowOperations =
    apply(a, b)

}
