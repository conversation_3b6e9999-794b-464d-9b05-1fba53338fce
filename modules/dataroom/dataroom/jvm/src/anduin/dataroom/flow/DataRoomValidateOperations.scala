// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.flow

import anduin.dataroom.billing.DataRoomBillingOperations
import anduin.dataroom.flow.DataRoomValidateOperations.{MemberStateCheck, PlanCheck, SeatCheck}
import anduin.fdb.record.FDBOperations
import anduin.fdb.record.model.{RecordIO, RecordReadIO, RecordReadTask, RecordTask}
import anduin.id.link.ProtectedLinkId
import anduin.model.common.user.UserId
import anduin.model.id.TeamId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.orgbilling.model.plan.DataRoomPremiumFeature
import anduin.team.flow.memberflow.TeamMemberStateStoreOperations
import anduin.team.{TeamMemberFlowState, TeamServiceUtils, UserInvited, UserJoined}
import anduin.dataroom.exception.*
import anduin.dataroom.participant.DataRoomParticipantRoleOperations
import anduin.dataroom.role.*
import anduin.environment.EnvironmentCheck
import anduin.dataroom.state.*
import anduin.utils.ScalaUtils

final case class DataRoomValidateOperations(
  modelOps: DataRoomModelStoreOperations,
  stateOps: DataRoomStateStoreOperations,
  billingOps: DataRoomBillingOperations,
  teamMemberStateOps: TeamMemberStateStoreOperations,
  roleOps: DataRoomParticipantRoleOperations
) {

  def getMemberStateCheckResult(
    userId: UserId,
    dataRoomWorkflowId: DataRoomWorkflowId,
    checkJoined: MemberStateCheck
  ): RecordReadTask[(TeamId, MemberStateCheck.CheckResult)] = {
    for {
      teamId <- modelOps.getTeamId(dataRoomWorkflowId)
      checkResult <- {
        checkJoined match {
          case MemberStateCheck.MemberStateRequirement(check) =>
            TeamServiceUtils.getTeamMemberFlowState(teamMemberStateOps.store)(teamId, userId).map(check)
          case MemberStateCheck.NoRequirement =>
            RecordReadIO.succeed[MemberStateCheck.CheckResult](MemberStateCheck.Passed)
        }
      }
    } yield teamId -> checkResult
  }

  private def memberStatePreCheck(
    userId: UserId,
    dataRoomWorkflowId: DataRoomWorkflowId,
    checkResult: MemberStateCheck.CheckResult
  ) = {
    checkResult match {
      case MemberStateCheck.PreCheckFailed =>
        RecordIO.fail {
          DataRoomMemberStateCheckException(
            userId,
            dataRoomWorkflowId,
            DataRoomMemberStateCheckException.Reason.NotJoinedOrInvited
          )
        }
      case MemberStateCheck.Passed | _: MemberStateCheck.PostCheckFailed =>
        RecordIO.unit
    }
  }

  private def memberStatePostCheck(
    userId: UserId,
    dataRoomWorkflowId: DataRoomWorkflowId,
    checkResult: MemberStateCheck.CheckResult
  ) = {
    checkResult match {
      case MemberStateCheck.PostCheckFailed(reason) =>
        RecordIO.fail {
          DataRoomMemberStateCheckException(
            userId,
            dataRoomWorkflowId,
            reason
          )
        }
      case MemberStateCheck.Passed | MemberStateCheck.PreCheckFailed =>
        RecordIO.unit
    }
  }

  def filterByRoles[A](roleMap: Map[A, DataRoomRole], target: Set[DataRoomRoleUtils.Check]): Set[A] = {
    for {
      (key, role) <- roleMap.toSet
      if target.forall(_(role))
    } yield key
  }

  private def checkRoleTask(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userId: UserId,
    roleChecks: Set[DataRoomRoleUtils.Check]
  ) = {
    for {
      role <- roleOps.getParticipantRole(
        dataRoomWorkflowId = dataRoomWorkflowId,
        userId = userId
      )
      validIndividualUsers = filterByRoles(Map(userId -> role), roleChecks)
      _ <- RecordIO.validate(validIndividualUsers.contains(userId)) {
        DataRoomFailedRoleCheckException(userId, dataRoomWorkflowId, roleChecks)
      }
    } yield ()
  }

  private def checkArchivedStatusTask(
    userId: UserId,
    dataRoomCreatedState: DataRoomCreatedSharedFlowState
  )(
    isArchived: Boolean
  ) = {
    RecordIO.validate(dataRoomCreatedState.isArchived == isArchived) {
      DataRoomArchivedException(userId, dataRoomCreatedState.dataRoomWorkflowId)
    }
  }

  private def validateTermsAcceptedTask(userId: UserId, dataRoomCreatedState: DataRoomCreatedSharedFlowState) = {
    val toaFileIdOpt = dataRoomCreatedState.termsOfAccessOptions.flatMap { options =>
      options.versions.lastOption.filter(_ => options.isEnabled && !options.whitelistedUsers.contains(userId))
    }
    RecordIO.traverse(toaFileIdOpt) { toaFileId =>
      val hasTermsAccepted = dataRoomCreatedState.termsOfAccessCertificates.exists { entry =>
        entry.toaFileId == toaFileId && entry.userId == userId
      }
      RecordIO.validate(hasTermsAccepted) {
        DataRoomToaNotAcceptedException(userId, toaFileId, dataRoomCreatedState.dataRoomWorkflowId)
      }
    }
  }

  def checkTermsAcceptedTask(
    userId: UserId,
    dataRoomCreatedState: DataRoomCreatedSharedFlowState
  ): RecordTask[Boolean] = {
    validateTermsAcceptedTask(userId, dataRoomCreatedState)
      .as(true)
      .catchSome { case _: DataRoomToaNotAcceptedException =>
        RecordIO.succeed(false)
      }
  }

  private def validateEnvironmentTask(
    dataRoomCreatedState: DataRoomCreatedSharedFlowState,
    environmentCheck: EnvironmentCheck
  ): RecordTask[Unit] = {
    RecordIO.validate(environmentCheck.check(dataRoomCreatedState.environmentIdOpt)) {
      DataRoomEnvironmentCheckException(
        dataRoomWorkflowId = dataRoomCreatedState.dataRoomWorkflowId,
        dataRoomEnvironment = dataRoomCreatedState.environmentIdOpt
      )
    }
  }

  private def checkValidPlanWithFeaturesTask(
    userId: UserId,
    dataRoomCreatedState: DataRoomCreatedSharedFlowState,
    planCheck: PlanCheck
  ) = {
    RecordIO.traverseOptionUnit {
      planCheck match {
        case PlanCheck.RequirePlan(features) =>
          Some(features)
        case PlanCheck.NoRequirement =>
          None
      }
    } { features =>
      RecordIO.validate {
        billingOps.checkHavingDataRoomPremiumFeatures(dataRoomCreatedState, features.toSeq*)
      } {
        DataRoomInvalidPlanException(
          userId,
          dataRoomCreatedState.dataRoomWorkflowId,
          features
        )
      }
    }
  }

  private def getExistingRoleMap(
    dataRoomCreatedState: DataRoomCreatedSharedFlowState,
    teamId: TeamId,
    userType: SeatCheck.UserType
  ) = {
    userType match {
      case SeatCheck.UserType.AllUsers =>
        roleOps.getAllParticipantRoleMap(dataRoomCreatedState.dataRoomWorkflowId).toRecordIO
      case SeatCheck.UserType.JoinedUsersWith(additional) =>
        for {
          roleMap <- roleOps.getAllParticipantRoleMap(dataRoomCreatedState.dataRoomWorkflowId).toRecordIO
          teamMemberInfo <- TeamServiceUtils.getTeamMemberInfo(teamMemberStateOps.store)(teamId)
          joinedUsers = teamMemberInfo.joinedMembers ++ additional
        } yield {
          roleMap.filter { case (userId, _) =>
            joinedUsers.contains(userId)
          }
        }
    }
  }

  private def getRoles(
    dataRoomCreatedState: DataRoomCreatedSharedFlowState,
    existingRoleMap: Map[UserId, DataRoomRole],
    modification: Option[SeatCheck.Modification]
  ) = {
    modification.fold(existingRoleMap.values.toSeq) {
      case SeatCheck.Modification.ExistingUsers(roleMap) =>
        (existingRoleMap ++ roleMap).values.toSeq
      case SeatCheck.Modification.AddedUsers(roles) =>
        existingRoleMap.values.toSeq ++ roles
      case SeatCheck.Modification.JoinViaLink(linkId) =>
        existingRoleMap.values.toSeq ++ dataRoomCreatedState.linkInvitationMap.get(linkId).map(_.role)
    }
  }

  private def checkTotalSeatsTask(
    userId: UserId,
    dataRoomCreatedState: DataRoomCreatedSharedFlowState,
    teamId: TeamId,
    seatCheck: SeatCheck
  ) = {
    seatCheck match {
      case SeatCheck.Require(userType, modification) =>
        for {
          existingRoleMap <- getExistingRoleMap(
            dataRoomCreatedState,
            teamId,
            userType
          )
          newRoles = getRoles(
            dataRoomCreatedState,
            existingRoleMap,
            modification
          )
          plan <- billingOps.getPlan(dataRoomCreatedState)
          totalSeats = plan.totalSeats
          oldSeatCount = existingRoleMap.values.count(DataRoomRoleUtils.isInternal)
          newSeatCount = newRoles.count(DataRoomRoleUtils.isInternal)
          _ <- RecordIO.validate(newSeatCount <= totalSeats || oldSeatCount >= newSeatCount) {
            DataRoomNotEnoughSeatException(
              userId = userId,
              dataRoomWorkflowId = dataRoomCreatedState.dataRoomWorkflowId,
              requiredSeats = newSeatCount,
              totalSeats = totalSeats
            )
          }
        } yield ()
      case SeatCheck.NoRequirement =>
        RecordIO.unit
    }
  }

  /** Order of checks:
    *   1. Member state pre-check: Throw when user is neither joined nor invited 2. Archive check: Throw when data room
    *      is archived 3. Plan expiration and feature check: Throw when plan is expired or doesn't have particular
    *      features 4. Total seats: Throw when plan doesn't have required amount of seats 5. Member state post-check:
    *      Throw when user member state doesn't match (e.g must be invited to accept invitation) 6. Terms of Access
    *      check: Throw when user haven't accepted Terms of Access 7. Role: Throw when user doesn't have required role
    *      The exceptions for checks 1, 2, 3, 5 and 6 can be propagated to frontend, hence they're required to be in
    *      order. In particular, 1 shows No Access screen, 2 shows Archive screen, 3 shows No Plan screen, 5 shows
    *      invitation acceptance modal and 6 shows Terms of Access acceptance modal.
    */
  def validateAndGetCurrentState(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userId: UserId,
    roleChecks: Set[DataRoomRoleUtils.Check],
    checkJoined: MemberStateCheck = MemberStateCheck.RequireJoined,
    checkTermsAccepted: Boolean = true,
    checkArchivedStatus: Option[Boolean] = Some(false),
    checkValidPlanWithFeatures: PlanCheck = PlanCheck.RequirePlan(Set()),
    checkTotalSeats: SeatCheck = SeatCheck.NoRequirement,
    environmentCheck: EnvironmentCheck = EnvironmentCheck.Bypass
  ): RecordTask[(TeamId, DataRoomCreatedSharedFlowState)] = {
    val recordTask = for {
      (teamId, memberStateCheckResult) <- getMemberStateCheckResult(
        userId,
        dataRoomWorkflowId,
        checkJoined
      )
      _ <- memberStatePreCheck(
        userId,
        dataRoomWorkflowId,
        memberStateCheckResult
      )
      dataRoomCreatedState <- stateOps.getState(dataRoomWorkflowId)
      _ <- RecordIO.traverseOptionUnit(checkArchivedStatus)(checkArchivedStatusTask(userId, dataRoomCreatedState))
      _ <- validateEnvironmentTask(dataRoomCreatedState, environmentCheck)
      _ <- checkValidPlanWithFeaturesTask(
        userId,
        dataRoomCreatedState,
        checkValidPlanWithFeatures
      )
      _ <- checkTotalSeatsTask(
        userId,
        dataRoomCreatedState,
        teamId,
        checkTotalSeats
      )
      _ <- memberStatePostCheck(
        userId,
        dataRoomWorkflowId,
        memberStateCheckResult
      )
      _ <- RecordIO.when(checkTermsAccepted)(validateTermsAcceptedTask(userId, dataRoomCreatedState))
      _ <- RecordIO.when(roleChecks.nonEmpty)(
        checkRoleTask(
          dataRoomWorkflowId,
          userId,
          roleChecks
        )
      )
    } yield teamId -> dataRoomCreatedState

    recordTask
      .tapErrorCause { err =>
        RecordIO.logWarningCause(s"Failed to validate and get current state of data room $dataRoomWorkflowId", err)
      }
      .mapError {
        case ex: DataRoomException => ex
        case _                     => DataRoomNoAccessException(userId, dataRoomWorkflowId)
      }
  }

  def validateAtLeastOneJoinedAdminWhenUpdatingRoles(
    dataRoomWorkflowId: DataRoomWorkflowId,
    newUserRoles: Map[UserId, DataRoomRole],
    actor: UserId
  ): RecordReadTask[Unit] = {
    for {
      individualRoleMap <- roleOps.getAllParticipantRoleMap(dataRoomWorkflowId)

      teamId <- modelOps.getTeamId(dataRoomWorkflowId)
      memberStateMap <- teamMemberStateOps.getAllMemberStates(teamId)

      members = ScalaUtils.intersectMap(
        memberStateMap,
        individualRoleMap
      )
      joinedAdmins = members.collect {
        case (userId, (memberState, role)) if {
              val isAdmin = DataRoomRoleUtils.isAdmin(role)
              val isJoined = TeamServiceUtils.isJoined(memberState)
              isAdmin && isJoined
            } =>
          userId
      }

      hasJoinedAdminAfterUpdating = joinedAdmins.exists { userId =>
        val notAffected = !newUserRoles.contains(userId)
        val isAdminAfterUpdating = newUserRoles.get(userId).exists(DataRoomRoleUtils.isAdmin)
        notAffected || isAdminAfterUpdating
      }
      _ <- RecordReadIO.validate(hasJoinedAdminAfterUpdating) {
        DataRoomRequireAtLeastOneJoinedAdminException(actor, dataRoomWorkflowId)
      }
    } yield ()
  }

}

object DataRoomValidateOperations
    extends FDBOperations.Multi[
      (DataRoomModelStoreOperations, DataRoomStateStoreOperations),
      (DataRoomBillingOperations, (TeamMemberStateStoreOperations, DataRoomParticipantRoleOperations)),
      DataRoomValidateOperations
    ] {

  def combine(
    a: (DataRoomModelStoreOperations, DataRoomStateStoreOperations),
    b: (DataRoomBillingOperations, (TeamMemberStateStoreOperations, DataRoomParticipantRoleOperations))
  ): DataRoomValidateOperations =
    DataRoomValidateOperations(
      a._1,
      a._2,
      b._1,
      b._2._1,
      b._2._2
    )

  sealed trait MemberStateCheck derives CanEqual

  object MemberStateCheck {

    sealed trait CheckResult derives CanEqual

    case object Passed extends CheckResult

    case object PreCheckFailed extends CheckResult

    final case class PostCheckFailed(reason: DataRoomMemberStateCheckException.Reason) extends CheckResult

    type CheckFunc = Option[TeamMemberFlowState] => CheckResult

    final case class MemberStateRequirement(
      check: CheckFunc
    ) extends MemberStateCheck

    val RequireJoined: MemberStateRequirement = MemberStateRequirement {
      case Some(_: UserInvited) => PostCheckFailed(DataRoomMemberStateCheckException.Reason.NotJoined)
      case Some(_: UserJoined)  => Passed
      case _                    => PreCheckFailed
    }

    val RequireInvitedOnly: MemberStateRequirement = MemberStateRequirement {
      case Some(_: UserInvited) => Passed
      case Some(_: UserJoined)  => PostCheckFailed(DataRoomMemberStateCheckException.Reason.NotInvited)
      case _                    => PreCheckFailed
    }

    val RequireInvitedOrJoined: MemberStateRequirement = MemberStateRequirement {
      case Some(_: UserJoined | _: UserInvited) => Passed
      case _                                    => PreCheckFailed
    }

    case object NoRequirement extends MemberStateCheck
  }

  sealed trait PlanCheck derives CanEqual

  object PlanCheck {

    final case class RequirePlan(features: Set[DataRoomPremiumFeature]) extends PlanCheck

    case object NoRequirement extends PlanCheck
  }

  sealed trait SeatCheck derives CanEqual

  object SeatCheck {

    sealed trait UserType derives CanEqual

    object UserType {

      final case class JoinedUsersWith(additional: Set[UserId]) extends UserType

      case object AllUsers extends UserType
    }

    sealed trait Modification derives CanEqual

    object Modification {

      final case class ExistingUsers(roleMap: Map[UserId, DataRoomRole]) extends Modification

      final case class AddedUsers(roles: Seq[DataRoomRole]) extends Modification

      final case class JoinViaLink(linkId: ProtectedLinkId) extends Modification
    }

    final case class Require(
      userType: UserType,
      modification: Option[Modification]
    ) extends SeatCheck

    case object NoRequirement extends SeatCheck
  }

}
