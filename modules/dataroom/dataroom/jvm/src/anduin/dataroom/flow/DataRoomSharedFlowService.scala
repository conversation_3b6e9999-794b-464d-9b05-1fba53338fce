// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.flow

import anduin.dataroom.flow.DataRoomSharedFlowService.*
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.notichannel.DataRoomNotificationChannels
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils
import zio.{Task, ZIO}

import scala.collection.concurrent.TrieMap

final case class DataRoomSharedFlowService(
  natsNotificationService: NatsNotificationService
) {

  private def addNotification(
    dataRoomWorkflowId: DataRoomWorkflowId,
    notificationChannel: NotificationChannel
  ): Task[Unit] = {
    ZIOUtils.when(notificationSets.putIfAbsent((dataRoomWorkflowId, notificationChannel), ()).isEmpty) {
      ZIO.logInfo(s"Adding notification for ${dataRoomWorkflowId}") *>
        publishNotification(dataRoomWorkflowId, notificationChannel).delay(debounceDuration).forkDaemon
    }
  }

  private def publishNotification(
    dataRoomWorkflowId: DataRoomWorkflowId,
    notificationChannel: NotificationChannel
  ): Task[Unit] = {
    ZIOUtils.when(notificationSets.contains((dataRoomWorkflowId, notificationChannel))) {
      for {
        _ <- ZIO.logInfo(s"Publishing notification for ${dataRoomWorkflowId}")
        natsChannel = notificationChannel match {
          case NotificationChannel.DataRoomUpdated => DataRoomNotificationChannels.dataRoomUpdated(dataRoomWorkflowId)
          case NotificationChannel.DataRoomFile    => DataRoomNotificationChannels.dataRoomFile(dataRoomWorkflowId)
        }
        _ <- natsNotificationService.publish(dataRoomWorkflowId, natsChannel)
        _ <- ZIO.attempt(notificationSets.remove((dataRoomWorkflowId, notificationChannel)))
      } yield ()
    }
  }

  def modifyLastUpdate(dataRoomWorkflowId: DataRoomWorkflowId): Task[Unit] = {
    addNotification(
      dataRoomWorkflowId,
      NotificationChannel.DataRoomUpdated
    )
  }

  def modifyFileUpdated(dataRoomWorkflowId: DataRoomWorkflowId): Task[Unit] = {
    addNotification(
      dataRoomWorkflowId,
      NotificationChannel.DataRoomFile
    )
  }

}

object DataRoomSharedFlowService {

  private val debounceDuration = zio.Duration.fromMillis(200)

  private enum NotificationChannel {
    case DataRoomUpdated, DataRoomFile
  }

  private val notificationSets = TrieMap[(DataRoomWorkflowId, NotificationChannel), Unit]()
}
