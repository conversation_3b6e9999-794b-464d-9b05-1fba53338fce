// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.participant

import anduin.dataroom.group.DataRoomGroupStateStoreOperations
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import anduin.fdb.record.FDBOperations
import anduin.fdb.record.model.{RecordReadIO, RecordReadTask, RecordTask}
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId

final case class DataRoomParticipantRoleOperations(
  groupOps: DataRoomGroupStateStoreOperations,
  participantOps: DataRoomParticipantOperations
) {

  def updateParticipantRole(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userId: UserId,
    roleOpt: Option[DataRoomRole],
    canInviteOpt: Option[Boolean]
  ): RecordTask[DataRoomParticipant] = {
    participantOps
      .upsertParticipant(
        dataRoomWorkflowId,
        userId,
        updateFn = participantModel => {
          val role = roleOpt.getOrElse(participantModel.role)
          val canInvite = canInviteOpt.getOrElse(participantModel.canInvite)
          // Unassigned user, only update role if participant role is empty
          if (participantModel.groupIds.isEmpty) {
            participantModel.copy(role = role, canInvite = canInvite)
          } else {
            // Group user, participant role will be equal to group role
            participantModel.copy(role = DataRoomRole.Empty, canInvite = canInvite)
          }
        }
      )
  }

  // participantRole = max(individualRole, groupRoles...)
  def getParticipantRole(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userId: UserId,
    updatedIndividualRoles: Map[UserId, DataRoomRole] = Map.empty[UserId, DataRoomRole],
    updatedGroupRoles: Map[DataRoomGroupId, DataRoomRole] = Map.empty[DataRoomGroupId, DataRoomRole],
    addedGroupIds: Set[DataRoomGroupId] = Set.empty[DataRoomGroupId],
    removedGroupIds: Set[DataRoomGroupId] = Set.empty[DataRoomGroupId]
  ): RecordReadTask[DataRoomRole] = {
    getParticipantRoleOpt(
      dataRoomWorkflowId,
      userId,
      updatedIndividualRoles,
      updatedGroupRoles,
      addedGroupIds,
      removedGroupIds
    ).flatMap { roleOpt =>
      RecordReadIO.fromOption(
        roleOpt,
        new RuntimeException(s"Data room participant ${dataRoomWorkflowId}, ${userId} does not exist")
      )
    }
  }

  def getParticipantRoleMap(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userIds: Set[UserId],
    updatedIndividualRoles: Map[UserId, DataRoomRole] = Map.empty[UserId, DataRoomRole],
    updatedGroupRoles: Map[DataRoomGroupId, DataRoomRole] = Map.empty[DataRoomGroupId, DataRoomRole],
    addedGroupIds: Set[DataRoomGroupId] = Set.empty[DataRoomGroupId],
    removedGroupIds: Set[DataRoomGroupId] = Set.empty[DataRoomGroupId]
  ): RecordReadTask[Map[UserId, DataRoomRole]] = {
    RecordReadIO
      .traverse(userIds) { userId =>
        getParticipantRole(
          dataRoomWorkflowId,
          userId,
          updatedIndividualRoles,
          updatedGroupRoles,
          addedGroupIds,
          removedGroupIds
        ).map(userId -> _)
      }
      .map(_.toMap)
  }

  private def computeParticipantRole(
    participantModel: DataRoomParticipant,
    updatedIndividualRoles: Map[UserId, DataRoomRole] = Map.empty[UserId, DataRoomRole],
    updatedGroupRoles: Map[DataRoomGroupId, DataRoomRole] = Map.empty[DataRoomGroupId, DataRoomRole],
    addedGroupIds: Set[DataRoomGroupId] = Set.empty[DataRoomGroupId],
    removedGroupIds: Set[DataRoomGroupId] = Set.empty[DataRoomGroupId]
  ): RecordReadTask[DataRoomRole] = {
    for {
      individualRole <- RecordReadIO.succeed(
        updatedIndividualRoles.getOrElse(participantModel.userId, participantModel.role)
      )
      oldGroupIds = participantModel.groupIds
      newGroupIds = participantModel.groupIds ++ addedGroupIds -- removedGroupIds
      oldGroupRoles <- RecordReadIO.traverse(oldGroupIds) { groupId =>
        groupOps.getState(groupId).map(_.role)
      }
      newGroupRoles <- RecordReadIO.traverse(newGroupIds) { groupId =>
        updatedGroupRoles
          .get(groupId)
          .fold {
            groupOps.getState(groupId).map(_.role)
          } { groupRole =>
            RecordReadIO.succeed(groupRole)
          }
      }
      allRoles =
        if (oldGroupIds.isEmpty && newGroupIds.isEmpty) {
          Set(individualRole)
        } else if (newGroupIds.isEmpty) {
          oldGroupRoles.toSet
        } else {
          newGroupRoles.toSet
        }
      maxRole = DataRoomRoleUtils.getMaxRole(allRoles)
    } yield DataRoomRoleUtils.getRoleWithCanInvite(maxRole, participantModel.canInvite)
  }

  def getParticipantRoleOpt(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userId: UserId,
    updatedIndividualRoles: Map[UserId, DataRoomRole] = Map.empty[UserId, DataRoomRole],
    updatedGroupRoles: Map[DataRoomGroupId, DataRoomRole] = Map.empty,
    addedGroupIds: Set[DataRoomGroupId] = Set.empty[DataRoomGroupId],
    removedGroupIds: Set[DataRoomGroupId] = Set.empty[DataRoomGroupId]
  ): RecordReadTask[Option[DataRoomRole]] = {
    for {
      modelOpt <- participantOps.getOptParticipant(dataRoomWorkflowId, userId)
      roleOpt <- modelOpt.fold(RecordReadIO.succeed(Option.empty[DataRoomRole])) { model =>
        computeParticipantRole(
          model,
          updatedIndividualRoles,
          updatedGroupRoles,
          addedGroupIds,
          removedGroupIds
        ).map(Some(_))
      }
    } yield roleOpt
  }

  def getAllParticipantRoleMap(
    dataRoomWorkflowId: DataRoomWorkflowId,
    updatedIndividualRoles: Map[UserId, DataRoomRole] = Map.empty,
    updatedGroupRoles: Map[DataRoomGroupId, DataRoomRole] = Map.empty,
    addedGroupIds: Set[DataRoomGroupId] = Set.empty[DataRoomGroupId],
    removedGroupIds: Set[DataRoomGroupId] = Set.empty[DataRoomGroupId]
  ): RecordReadTask[Map[UserId, DataRoomRole]] = {
    for {
      participantMap <- participantOps.getParticipantMap(dataRoomWorkflowId)
      roleMap <- RecordReadIO.traverse(participantMap.toSeq) { case (userId, participant) =>
        computeParticipantRole(
          participant,
          updatedIndividualRoles,
          updatedGroupRoles,
          addedGroupIds,
          removedGroupIds
        ).map(userId -> _)
      }
    } yield roleMap.toMap
  }

  def getCurrentAdmins(
    dataRoomWorkflowId: DataRoomWorkflowId
  ): RecordReadTask[Set[UserId]] = {
    for {
      participantMap <- getAllParticipantRoleMap(dataRoomWorkflowId)
    } yield participantMap.collect {
      case (userId, role) if DataRoomRoleUtils.isAdmin(role) => userId
    }.toSet
  }

}

object DataRoomParticipantRoleOperations
    extends FDBOperations.Multi[
      DataRoomGroupStateStoreOperations,
      DataRoomParticipantOperations,
      DataRoomParticipantRoleOperations
    ] {

  override def combine(
    a: DataRoomGroupStateStoreOperations,
    b: DataRoomParticipantOperations
  ): DataRoomParticipantRoleOperations = {
    DataRoomParticipantRoleOperations(a, b)
  }

}
