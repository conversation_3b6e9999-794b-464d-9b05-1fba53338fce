// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.participant

import java.time.Instant
import zio.{Task, ZIO}
import anduin.account.profile.UserProfileService
import anduin.dataroom.email.generate.{
  DataRoomInvitationAcceptedEmailGenerate,
  DataRoomInvitationEmailGenerate,
  DataRoomManualNotificationMultipleEmailGenerate,
  DataRoomUserRemovalEmailGenerate
}
import anduin.dataroom.exception.DataRoomException
import anduin.dataroom.flow.DataRoomValidateOperations.SeatCheck
import anduin.dataroom.group.DataRoomGroupOperations
import anduin.dataroom.homepage.DataRoomHomePageService
import anduin.dataroom.notification.{DataRoomNotificationSettingsOperations, InvitationAcceptedEmailNotification}
import anduin.dataroom.participant.DataRoomParticipantService.UserPermissionChangeMode
import anduin.dataroom.whitelabel.DataRoomWhiteLabelService
import anduin.dms.DmsFeature.DataRoom
import anduin.fdb.record.model.{RecordIO, RecordTask}
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.greylin.{GreylinDataService, modelti, operation}
import anduin.model.common.user.UserId
import anduin.model.id.stage.{DataRoomTermsOfAccessId, DataRoomWorkflowId}
import anduin.model.id.{FileId, FolderId, TeamId}
import anduin.portaluser.PortalUserService
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.service.{AuthenticatedRequestContext, ServiceActor}
import anduin.user.UserService
import com.anduin.stargazer.endpoints.{AssetPermissionChanges, PermissionChanges}
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.utils.ZIOUtils
import anduin.dataroom.email.*
import anduin.dataroom.flow.*
import anduin.dataroom.role.*
import anduin.dataroom.service.*
import anduin.dataroom.service.dmsmetadata.ModifyPermissionSourceOfEventMetaData.ModifyPermissionSourceOfEvent
import anduin.dataroom.service.webhook.DataRoomWebhookProducerService
import anduin.dataroom.validator.DataRoomValidator
import anduin.dms.service.FileService
import anduin.greylin.modelti.DataRoomParticipantState
import anduin.id.dataroom.DataRoomGroupId
import anduin.link.*
import anduin.model.user.FullName
import anduin.stargazer.service.dataroom.*
import anduin.team.TeamServiceParams.*
import anduin.team.*
import anduin.team.flow.memberflow.TeamMemberStateStoreOperations

final case class DataRoomParticipantService(
  fileService: FileService,
  dataRoomEmailSenderService: DataRoomEmailSenderService,
  backendConfig: GondorBackendConfig,
  dataRoomLoggingService: DataRoomLoggingService,
  teamService: TeamService,
  protectedLinkService: ProtectedLinkService,
  dataRoomSharedFlowService: DataRoomSharedFlowService,
  dataRoomContactService: DataRoomContactService,
  dataRoomTermsOfAccessService: DataRoomTermsOfAccessService,
  dataRoomHomePageService: DataRoomHomePageService,
  dataRoomWhiteLabelService: DataRoomWhiteLabelService,
  dataRoomPermissionService: DataRoomPermissionService,
  dataRoomValidator: DataRoomValidator,
  portalUserService: PortalUserService,
  greylinDataService: GreylinDataService,
  dataRoomWebhookProducer: DataRoomWebhookProducerService
)(
  using val linkGeneratorService: LinkGeneratorService,
  val userService: UserService,
  val userProfileService: UserProfileService,
  val dataRoomEmailService: DataRoomEmailService
) {

  private def execute[A](task: (DataRoomFlowOperations, DataRoomValidateOperations) => RecordTask[A]) = {
    FDBRecordDatabase.transact(
      FDBOperations[(DataRoomFlowOperations, DataRoomValidateOperations)].Production
    ) { case (flowOps, validateOps) =>
      task(flowOps, validateOps)
    }
  }

  private def executeWithGroup[A](
    task: (
      DataRoomFlowOperations,
      DataRoomValidateOperations,
      DataRoomGroupOperations
    ) => RecordTask[A]
  ) = {
    FDBRecordDatabase.transact(
      FDBOperations[((DataRoomFlowOperations, DataRoomValidateOperations), DataRoomGroupOperations)].Production
    ) { case ((flowOps, validateOps), groupOps) =>
      task(
        flowOps,
        validateOps,
        groupOps
      )
    }
  }

  private def modifyInvitationAndExisting(
    actor: UserId,
    dataRoomWorkflowId: DataRoomWorkflowId,
    invitationModified: Map[UserId, DataRoomPermissionChanges],
    existing: Map[UserId, DataRoomPermissionChanges],
    actorIp: Option[String],
    modifyPermissionSourceOfEvent: Option[ModifyPermissionSourceOfEvent]
  ) = {
    for {
      _ <- ZIOUtils.when(invitationModified.nonEmpty) {
        dataRoomPermissionService.modifyAssetPermissions(
          actor = actor,
          dataRoomWorkflowId = dataRoomWorkflowId,
          userChanges = invitationModified,
          mode = FileService.PermissionModMode.Validate(onFailure = FileService.PermissionModMode.OnFailure.BestEffort),
          shouldIgnoreDeletedFiles = true,
          actorIp = actorIp,
          modifyPermissionSourceOfEvent = modifyPermissionSourceOfEvent
        )
      }
      _ <- ZIOUtils.when(existing.nonEmpty) {
        dataRoomPermissionService.modifyAssetPermissions(
          actor = actor,
          dataRoomWorkflowId = dataRoomWorkflowId,
          userChanges = existing,
          mode = FileService.PermissionModMode.Validate(
            requirement = FileService.PermissionModMode.Requirement.Fixed(FileFolderPermission.Own),
            onFailure = FileService.PermissionModMode.OnFailure.BestEffort
          ),
          shouldIgnoreDeletedFiles = true,
          actorIp = actorIp,
          modifyPermissionSourceOfEvent = modifyPermissionSourceOfEvent
        )
      }
    } yield ()
  }

  private def validateInviteeNotInDataRoom(
    dataRoomWorkflowId: DataRoomWorkflowId,
    teamId: TeamId,
    emails: Set[String],
    actor: UserId
  ) = {
    for {
      inviterEmail <- userProfileService.getEmailAddress(actor)
      _ <- ZIO.foreach(emails) { emailStr =>
        for {
          invitee <- userProfileService
            .maybeCreateZombieUser(
              emailStr,
              FullName(),
              Some(inviterEmail.address)
            )
            .map(_.userId)
          memberStateOpt <- teamService.getMemberStateOpt(teamId, invitee)
          _ <- ZIOUtils.validate {
            memberStateOpt.fold(true) {
              case _: UserInvited | _: UserJoined => false
              case _                              => true
            }
          } {
            new RuntimeException(
              s"User ${emailStr} is already invited or joined data room ${dataRoomWorkflowId.idString}"
            )
          }
        } yield ()
      }
    } yield ()
  }

  def inviteUsers(
    params: InviteUsersToDataRoomParams,
    actor: ServiceActor,
    ctx: Option[AuthenticatedRequestContext] = None,
    parallelism: Int = 10
  ): Task[DataRoomEmptyResponse] = {
    val dataRoomWorkflowId = params.dataRoomWorkflowId
    for {
      _ <- ZIO.logInfo(
        s"Start InviteUsersToDataRoom|dataRoom=${params.dataRoomWorkflowId.idString}|actor=${actor.userId.idString}"
      )
      _ <- ZIOUtils.validate(
        params.groupPermissionMap.keySet.intersect(params.individualPermissionMap.keySet).isEmpty
      ) {
        new RuntimeException(
          "Invite users permission map has both individual and group permission"
        )
      }
      _ <- dataRoomValidator.checkDataRoomGroups.validate(params, actor.userId)
      refinedUserPermissionMap <- FDBRecordDatabase.transact(DataRoomGroupOperations.Production) { groupOps =>
        for {
          groupMap <- groupOps.stateOps.getAllGroupsMap(dataRoomWorkflowId)
          groupUserPermissions <- RecordIO.traverse(params.groupPermissionMap.toSeq) { (userEmail, groupChanges) =>
            for {
              _ <- RecordIO.validate(groupChanges.groupIds.nonEmpty) {
                new RuntimeException(s"User ${userEmail} group permission map $groupChanges has empty groupIds")
              }
              groupRoles <- RecordIO.traverse(groupChanges.groupIds) { groupId =>
                RecordIO
                  .fromOption(groupMap.get(groupId), new RuntimeException(s"Group ${groupId} does not exist"))
                  .map(state => DataRoomRoleUtils.getRoleWithCanInvite(state.role, groupChanges.canInvite))
              }
            } yield userEmail -> DataRoomPermissionChanges(
              roleSet = Some(DataRoomRoleUtils.getMaxRole(groupRoles.toSet)),
              assetPermissions = AssetPermissionChanges()
            )
          }
        } yield groupUserPermissions.toMap ++ params.individualPermissionMap
      }
      individualTeamId <- FDBRecordDatabase.transact(DataRoomValidateOperations.Production) { validateOps =>
        val requiredRoles = if (params.groupPermissionMap.isEmpty) {
          Set(DataRoomRoleUtils.canInviteRoles(refinedUserPermissionMap.values.flatMap(_.roleSet).toSet))
        } else {
          Set(DataRoomRoleUtils.isAdmin)
        }
        for {
          (individualTeamId, curState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = actor.userId,
            roleChecks = requiredRoles,
            checkTotalSeats = SeatCheck.Require(
              userType = SeatCheck.UserType.AllUsers,
              modification = Some(SeatCheck.Modification.AddedUsers(refinedUserPermissionMap.toSeq.flatMap(_._2.roleSet)))
            )
          )
          _ <- dataRoomPermissionService.validatePermissionChanges(
            validateOps = validateOps,
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            createdState = curState,
            currentRoleAndChangeList = params.individualPermissionMap.values.toList.map(None -> _)
          )
        } yield individualTeamId
      }
      _ <- validateInviteeNotInDataRoom(
        params.dataRoomWorkflowId,
        individualTeamId,
        refinedUserPermissionMap.keySet,
        actor.userId
      )
      userChanges <-
        ZIOUtils
          .foreachParN(parallelism)(refinedUserPermissionMap.toList) { case (emailStr, changes) =>
            for {
              response <- teamService.inviteAndCreateUser(
                InviteAndCreateUserParams(
                  inviter = actor.userId,
                  inviteeEmail = emailStr,
                  teamId = individualTeamId
                )
              )
              userId <- response match {
                case userInvitedState: UserInvited =>
                  ZIO.attempt(userInvitedState.invitee)
                case _ =>
                  ZIO.fail(new RuntimeException("Response is not UserInvited after inviteUser"))
              }
              _ <- FDBRecordDatabase.transact(
                FDBOperations[DataRoomParticipantOperations].Production
              )(
                _.upsertParticipant(
                  dataRoomWorkflowId = params.dataRoomWorkflowId,
                  userId = userId,
                  _.copy(
                    isRemoved = false,
                    groupIds = Set.empty
                  )
                )
              )
            } yield emailStr -> (userId, changes)
          }
          .map(_.toMap)
      roleMap = userChanges.flatMap { case (_, (userId, changes)) =>
        changes.roleSet.map(userId -> _)
      }
      dataRoomCreatedState <- execute { (flowOps, _) =>
        flowOps.inviteUsers(
          params.dataRoomWorkflowId,
          actor.userId,
          roleMap,
          params.isToaRequired,
          ctx.flatMap(_.getClientIP),
          params.isApproved
        )
      }
      _ <- dataRoomTermsOfAccessService.changeTermsFolderPermissionsUnsafe(
        actor.userId,
        dataRoomWorkflowId,
        roleMap
      )
      _ <- dataRoomHomePageService.changeHomePageFolderPermissions(
        actor.userId,
        dataRoomWorkflowId,
        roleMap
      )
      _ <- greylinDataService.runUnit(
        operation.DataRoomParticipantOperations.addParticipants(
          roleMap.map { case (userId, _) =>
            modelti.DataRoomParticipant(
              userId = userId,
              dataRoomId = dataRoomWorkflowId,
              state = Some(DataRoomParticipantState.Invited)
            )
          }.toList
        )
      )

      // Modify permissions of unassigned users
      individualChanges = userChanges
        .filter(entry => params.individualPermissionMap.keySet.contains(entry._1))
        .values
        .toMap
      _ <- modifyInvitationAndExisting(
        actor = actor.userId,
        dataRoomWorkflowId = params.dataRoomWorkflowId,
        invitationModified = individualChanges,
        existing = Map(),
        actorIp = ctx.flatMap(_.getClientIP),
        modifyPermissionSourceOfEvent = Some(ModifyPermissionSourceOfEvent.InviteParticipant)
      )
      // Add users to group
      groupChanges = params.groupPermissionMap.toSeq
        .flatMap(changes => changes._2.groupIds.map(_ -> changes._1))
        .groupMap(_._1)(_._2) // group emails by groupId
        .map { case (groupId, emails) =>
          // convert emails to userIds
          groupId -> emails.flatMap(email => userChanges.get(email).map(_._1))
        }
      _ <- ZIO.foreach(groupChanges.toSeq) { case (groupId, userIds) =>
        for {
          _ <- FDBRecordDatabase.transact(
            FDBOperations[DataRoomGroupOperations].Production
          )(
            _.addUsersToGroup(
              groupId = groupId,
              userIds = userIds.toSet,
              actor = actor.userId,
              actorIpOpt = ctx.flatMap(_.getClientIP)
            )
          )

        } yield ()
      }
      // Send webhook event for grouped users
      userByAddedGroups = groupChanges.toSeq
        .flatMap { (groupId, userIds) =>
          userIds.map(_ -> groupId)
        }
        .groupMap(_._1)(_._2)
        .toSeq
        .map(e => e._1 -> e._2.toSet)
        .groupMap(_._2)(_._1)
      _ <- ZIO.foreach(userByAddedGroups.toSeq) { (groupIds, userIds) =>
        dataRoomWebhookProducer.produceUserInvitedEvent(
          params.dataRoomWorkflowId,
          userIds,
          groupIds.toSeq
        )
      }
      // Send webhook event for unassigned users
      _ <- dataRoomWebhookProducer.produceUserInvitedEvent(
        params.dataRoomWorkflowId,
        userIds = individualChanges.keys.toSeq,
        groupIds = Seq.empty
      )
      userIds = userChanges.map(_._2._1).toSeq
      authenticationWhitelabelIdOpt <- dataRoomWhiteLabelService.getAuthenticationWhitelabelId(dataRoomWorkflowId)
      _ <- ZIOUtils.when(params.shouldSendEmail) {
        ZIOUtils
          .foreachParN(parallelism)(userIds) { invitedUser =>
            for {
              allVariables <- dataRoomEmailService.getAllVariables(
                dataRoomCreatedState,
                actor.userId,
                Some(actor.userId),
                Some(invitedUser),
                Some(Instant.now())
              )
              _ <- dataRoomEmailSenderService.sendEmail(
                dataRoomWorkflowId,
                DataRoomInvitationEmailGenerate(
                  dataRoomWorkflowId = dataRoomWorkflowId,
                  inviter = actor.userId,
                  invitedUser = invitedUser,
                  dataRoomName = dataRoomCreatedState.name,
                  subject = DataRoomEmailUtils.populateSubjectVariables(params.subject, allVariables),
                  message = DataRoomEmailUtils.populateFieldVariables(params.message, allVariables),
                  buttonLabel = DataRoomEmailUtils.populateFieldVariables(params.buttonLabel, allVariables),
                  invitedAt = None,
                  authenticationWhitelabelIdOpt = authenticationWhitelabelIdOpt
                )
              )
            } yield ()
          }
          .map(_ => ())
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
      _ <- dataRoomLoggingService.logEventInviteUserToDataRoom(
        dataRoomWorkflowId,
        dataRoomCreatedState.name,
        dataRoomCreatedState.creatorEntityId,
        actor,
        userIds,
        ctx
      )

      // Best effort to add contact, the result of this task won't affect the main task
      _ <-
        dataRoomContactService
          .addContactsToDataRoom(
            preferredActor = actor.userId,
            userIds = userIds.toSet,
            dataRoomWorkflowId = params.dataRoomWorkflowId
          )
          .catchAllCause { err =>
            ZIO.logWarningCause(
              s"failed to add contact to data room space: ${params.individualPermissionMap.keys.toList} to ${params.dataRoomWorkflowId}",
              err
            )
          }
    } yield DataRoomEmptyResponse()
  }

  def acceptInvitation(
    params: AcceptInvitationToDataRoomParams,
    ctx: AuthenticatedRequestContext,
    shouldSendEmail: Boolean = true
  ): Task[DataRoomEmptyResponse] = {
    for {
      (dataRoomCreatedState, inviter, emailRecipients, groupIds) <- FDBRecordDatabase.transact(
        FDBOperations[
          (
            (DataRoomFlowOperations, DataRoomValidateOperations),
            ((DataRoomNotificationSettingsOperations, TeamServiceOperations), DataRoomParticipantOperations)
          )
        ].Production
      ) { case ((flowOps, validateOps), ((notificationOps, teamOps), participantOps)) =>
        for {
          (individualUserTeam, _) <- validateOps.validateAndGetCurrentState(
            params.dataRoomWorkflowId,
            ctx.actor.userId,
            Set(),
            checkJoined = DataRoomValidateOperations.MemberStateCheck.RequireInvitedOnly,
            checkTermsAccepted = false,
            checkTotalSeats = SeatCheck.Require(
              userType = SeatCheck.UserType.JoinedUsersWith(Set(ctx.actor.userId)),
              modification = None
            )
          )
          teamState <- teamOps.acceptInvitation(AcceptInviteParams(ctx.actor.userId, individualUserTeam))
          dataRoomCreatedState <- flowOps.acceptInvitation(
            params.dataRoomWorkflowId,
            ctx.actor.userId,
            ctx.getClientIP
          )
          _ <- RecordIO.traverse(params.toaFileIdOpt) { toaFileId =>
            dataRoomTermsOfAccessService.accept(
              flowOps = flowOps,
              participantOps = participantOps,
              createdState = dataRoomCreatedState,
              params = AcceptTermsOfAccessToDataRoomParams(params.dataRoomWorkflowId, toaFileId, linkIdOpt = None),
              actor = ctx.actor.userId,
              httpContext = Some(ctx)
            )
          }
          isInternal <- validateOps.roleOps
            .getParticipantRole(
              params.dataRoomWorkflowId,
              ctx.actor.userId
            )
            .map(
              DataRoomRoleUtils.isInternal
            )
          _ <- flowOps.stateOps.store.toRecordIO {
            dataRoomContactService.syncContactRole(
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              added = Option.when(isInternal)(ctx.actor.userId).toSet
            )
          }
          userJoined <- teamState match {
            case userJoined: UserJoined =>
              RecordIO.succeed(userJoined)
            case _ =>
              RecordIO.fail(new RuntimeException("User is not marked as joined after accepting invitation"))
          }
          adminUsers <- validateOps.roleOps
            .getCurrentAdmins(params.dataRoomWorkflowId)
            .map(_ - ctx.actor.userId)
          emailRecipients <- RecordIO.parTraverseN(64)(adminUsers) { userId =>
            notificationOps
              .getInvitationAcceptedEmail(
                params.dataRoomWorkflowId,
                userId,
                dataRoomCreatedState.dataRoomNotificationConfigs
              )
              .map {
                case InvitationAcceptedEmailNotification.MyInvitationsOnly =>
                  Option.when(userJoined.inviter == userId)(userId)
                case InvitationAcceptedEmailNotification.All =>
                  Some(userId)
                case InvitationAcceptedEmailNotification.None | _: InvitationAcceptedEmailNotification.Unrecognized =>
                  None
              }
          }
          groupIds <- participantOps
            .getParticipant(
              params.dataRoomWorkflowId,
              ctx.actor.userId
            )
            .map(_.groupIds)
        } yield (dataRoomCreatedState, userJoined.inviter, emailRecipients.flatten.toSet, groupIds)
      }
      _ <- ZIOUtils.when(shouldSendEmail) {
        dataRoomEmailSenderService.sendEmail(
          params.dataRoomWorkflowId,
          DataRoomInvitationAcceptedEmailGenerate(
            params.dataRoomWorkflowId,
            inviter,
            ctx.actor.userId,
            emailRecipients,
            dataRoomCreatedState.name
          )
        )
      }
      _ <- dataRoomLoggingService.logEventUserJoinDataRoom(
        params.dataRoomWorkflowId,
        dataRoomCreatedState.name,
        dataRoomCreatedState.creatorEntityId,
        ctx.actor,
        Some(ctx)
      )
      _ <- dataRoomWebhookProducer.produceUserJoinEvent(
        params.dataRoomWorkflowId,
        ctx.actor.userId,
        groupIds.toSeq
      )
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
      _ <-
        dataRoomContactService
          .updateUserInfoAfterAcceptedInvitation(
            preferredActor = inviter,
            createdState = dataRoomCreatedState,
            target = ctx.actor.userId,
            dataRoomWorkflowId = params.dataRoomWorkflowId
          )
          .catchAllCause { err =>
            ZIO.logWarningCause(
              s"failed to update contact to data room space: ${params.dataRoomWorkflowId} of user ${ctx.actor.userId}",
              err
            )
          }
      _ <- greylinDataService.runUnit(
        operation.DataRoomParticipantOperations.update(ctx.actor.userId, params.dataRoomWorkflowId)(
          _.copy(state = Some(DataRoomParticipantState.Joined))
        )
      )
    } yield DataRoomEmptyResponse()
  }

  def declineInvitation(
    params: DeclineInvitationToDataRoomParams,
    actor: ServiceActor,
    ctx: Option[AuthenticatedRequestContext] = None
  ): Task[DataRoomEmptyResponse] = {
    for {
      removedGroups <- executeWithGroup { (flowOps, validateOps, groupOps) =>
        for {
          (teamId, _) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = actor.userId,
            roleChecks = Set(),
            checkJoined = DataRoomValidateOperations.MemberStateCheck.RequireInvitedOnly,
            checkTermsAccepted = false,
            checkArchivedStatus = None
          )
          _ <- flowOps.declineInvitation(
            params.dataRoomWorkflowId,
            actor.userId,
            ctx.flatMap(_.getClientIP)
          )
          _ <- groupOps.teamOps.declineInvitation(DeclineInviteParams(actor.userId, teamId))
          removedGroups <- groupOps
            .removeUsersFromCurrentGroup(
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              userIds = Seq(actor.userId),
              actor = actor.userId,
              actorIpOpt = ctx.flatMap(_.getClientIP)
            )
            .map(
              _.filter { case (_, userIds) =>
                userIds.contains(actor.userId)
              }.keys
            )
          _ <- groupOps.participantOps.removeParticipants(params.dataRoomWorkflowId, Seq(actor.userId))
        } yield removedGroups
      }
      _ <- dataRoomTermsOfAccessService.changeTermsFolderPermissionsUnsafe(
        actor.userId,
        params.dataRoomWorkflowId,
        Map(actor.userId -> DataRoomRole.Empty)
      )
      _ <- dataRoomHomePageService.changeHomePageFolderPermissions(
        actor.userId,
        params.dataRoomWorkflowId,
        Map(actor.userId -> DataRoomRole.Empty)
      )
      _ <- fileService.modifyPermissionsRecursively(
        actor = actor.userId,
        rootFolderId = FolderId.channelSystemFolderId(params.dataRoomWorkflowId),
        changes = PermissionChanges(revokedUsers = Set(actor.userId)),
        mode = FileService.PermissionModMode.Unsafe
      )
      _ <- greylinDataService.runUnit(
        operation.DataRoomParticipantOperations.removeParticipants(
          List((actor.userId, params.dataRoomWorkflowId))
        )
      )
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
      _ <- dataRoomWebhookProducer.produceUserDeclineInvitationEvent(
        params.dataRoomWorkflowId,
        actor.userId,
        removedGroups.toSeq
      )
    } yield DataRoomEmptyResponse()
  }

  private def getInviterOpt(
    validateOps: DataRoomValidateOperations,
    individualUserTeam: TeamId,
    target: UserId
  ) = {
    for {
      memberState <- TeamServiceUtils.getTeamMemberFlowState(validateOps.teamMemberStateOps.store)(
        individualUserTeam,
        target
      )
    } yield {
      memberState.collect { case userInvited: UserInvited =>
        userInvited.inviter
      }
    }
  }

  private def validateUserPermissionChanges(
    participantOps: DataRoomParticipantOperations,
    dataRoomWorkflowId: DataRoomWorkflowId,
    participantRoles: Map[UserId, DataRoomRole],
    permissionChanges: Map[UserId, DataRoomPermissionChanges],
    mode: UserPermissionChangeMode
  ) = {
    for {
      groupMap <- participantOps.getGroupParticipantMap(dataRoomWorkflowId)
      allGroupUsers <- RecordIO.succeed(groupMap.values.flatten.toSet)
      (groupUserChanges, individualUserChanges) = permissionChanges.partition { case (userId, _) =>
        allGroupUsers.contains(userId)
      }
      _ <- mode match {
        case UserPermissionChangeMode.Individual =>
          RecordIO.validate(groupUserChanges.isEmpty) {
            new RuntimeException(s"Modify permission changes $permissionChanges contain group users in individual mode")
          }
        case UserPermissionChangeMode.Group(canChangeRole) =>
          for {
            _ <- RecordIO.validate(individualUserChanges.isEmpty) {
              new RuntimeException(
                s"Modify permission changes $permissionChanges contain individual users in group mode"
              )
            }
            _ <- RecordIO.validate {
              groupUserChanges.forall { case (userId, permissionChanges) =>
                val currentRoleLevel =
                  DataRoomRoleUtils.getLevel(participantRoles.getOrElse(userId, DataRoomRole.Empty))
                val updatedRoleLevel = permissionChanges.roleSet.fold(currentRoleLevel) { newRole =>
                  DataRoomRoleUtils.getLevel(newRole)
                }
                (canChangeRole || currentRoleLevel == updatedRoleLevel) && AssetPermissionChanges.isEmpty(
                  permissionChanges.assetPermissions
                )
              }
            } {
              new RuntimeException(s"Modify permission changes $permissionChanges have invalid changes in group mode")
            }
          } yield ()
      }
    } yield ()
  }

  private[dataroom] def modifyPermissionsWhenRoleChanges(
    dataRoomWorkflowId: DataRoomWorkflowId,
    roleChanges: Seq[DataRoomParticipantRoleChange],
    actor: UserId,
    mode: UserPermissionChangeMode,
    ctx: Option[AuthenticatedRequestContext] = None
  ): Task[Unit] = {
    val validRoleChanges = roleChanges.filter { roleChange =>
      roleChange.oldRoleOpt != roleChange.newRoleOpt
    }
    ZIOUtils.when(validRoleChanges.nonEmpty) {
      modifyPermissions(
        params = ModifyDataRoomPermissionsParams(
          dataRoomWorkflowId = dataRoomWorkflowId,
          updatedUserMap = validRoleChanges.map { roleChange =>
            roleChange.userId -> DataRoomPermissionChanges(
              roleSet = Some(roleChange.newRoleOpt.getOrElse(DataRoomRole.Empty)),
              assetPermissions = AssetPermissionChanges()
            )
          }.toMap
        ),
        actor = actor,
        ctx = ctx,
        mode = mode
      )
    }
  }

  def modifyUserPermissions(
    params: ModifyDataRoomPermissionsParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext] = None
  ): Task[DataRoomEmptyResponse] = {
    for {
      participantMap <- getDataRoomParticipants(params.dataRoomWorkflowId, params.updatedUserMap.keySet)
      isAllIndividualUsers = participantMap.forall { case (_, participantData) =>
        participantData.groupIds.isEmpty
      }
      _ <- modifyPermissions(
        params,
        actor,
        ctx,
        mode = if (isAllIndividualUsers) {
          UserPermissionChangeMode.Individual
        } else {
          UserPermissionChangeMode.Group(canChangeRole = false)
        },
        modifyPermissionSourceOfEvent = Some(ModifyPermissionSourceOfEvent.AssignedToTargetAsset)
      )
    } yield DataRoomEmptyResponse()
  }

  def modifyPermissions(
    params: ModifyDataRoomPermissionsParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext] = None,
    mode: UserPermissionChangeMode = UserPermissionChangeMode.Individual,
    modifyPermissionSourceOfEvent: Option[ModifyPermissionSourceOfEvent] = None,
    notifyUpdate: Boolean = true
  ): Task[DataRoomEmptyResponse] = {
    val updatedUserRoleMap = params.updatedUserMap.flatMap { case (userId, changes) =>
      changes.roleSet.map(userId -> _)
    }
    for {
      _ <- ZIO.logInfo(s"User $actor modified permission of users ${params.updatedUserMap.keys.mkString(", ")}")
      (invitationModified, existing) <- FDBRecordDatabase.transact(
        FDBOperations[((DataRoomFlowOperations, DataRoomValidateOperations), DataRoomParticipantOperations)].Production
      ) { case ((flowOps, validateOps), participantOps) =>
        for {
          individualUserTeam <- validateOps.modelOps.getTeamId(params.dataRoomWorkflowId)
          inviterOptList <- RecordIO.parTraverseN(4)(params.updatedUserMap.keySet) { userId =>
            for {
              inviterOpt <- getInviterOpt(
                validateOps,
                individualUserTeam,
                userId
              )
            } yield inviterOpt.map(userId -> _)
          }
          inviterMap = inviterOptList.flatten.toMap
          (invitationModified, existing) = params.updatedUserMap.partition { case (userId, _) =>
            inviterMap.get(userId).contains(actor)
          }
          roleChecks = {
            if (existing.get(actor).exists(_.roleSet.exists(!DataRoomRoleUtils.isAdmin(_)))) {
              // Current admin downgrade himself, shouldn't check his role
              Set.empty[DataRoomRoleUtils.Check]
            } else if (existing.exists(_._2.roleSet.nonEmpty)) {
              Set[DataRoomRoleUtils.Check](DataRoomRoleUtils.isAdmin)
            } else {
              val invitedRoles = invitationModified.flatMap(_._2.roleSet).toSet
              if (invitedRoles.nonEmpty) {
                Set[DataRoomRoleUtils.Check](
                  DataRoomRoleUtils.canInviteRoles(invitationModified.flatMap(_._2.roleSet).toSet)
                )
              } else {
                Set.empty[DataRoomRoleUtils.Check]
              }
            }
          }
          (_, curState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = actor,
            roleChecks = roleChecks,
            checkTotalSeats = SeatCheck.Require(
              userType = SeatCheck.UserType.AllUsers,
              modification = Some(SeatCheck.Modification.ExistingUsers(updatedUserRoleMap))
            )
          )
          participantRoles <- validateOps.roleOps.getAllParticipantRoleMap(params.dataRoomWorkflowId)
          _ <- validateUserPermissionChanges(
            participantOps = participantOps,
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            participantRoles = participantRoles,
            permissionChanges = params.updatedUserMap,
            mode = mode
          )
          _ <- RecordIO.when(mode == UserPermissionChangeMode.Individual) {
            val currentRoleAndChangeList = params.updatedUserMap.toList
              .filter { case (_, changes) => // No permission change
                changes.roleSet.nonEmpty || !AssetPermissionChanges.isEmpty(changes.assetPermissions)
              }
              .map { case (userId, changes) =>
                participantRoles.get(userId) -> changes
              }
            dataRoomPermissionService.validatePermissionChanges(
              validateOps = validateOps,
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              createdState = curState,
              currentRoleAndChangeList = currentRoleAndChangeList
            )
          }
          (toaRequiredUsers, toaWhitelistedUsers) = params.isToaRequiredMap.partition(_._2)
          _ <- flowOps.modifyPermissions(
            params.dataRoomWorkflowId,
            actor,
            updatedUserRoleMap,
            toaWhitelistedUsers.keySet,
            toaRequiredUsers.keySet,
            ctx.flatMap(_.getClientIP)
          )
          _ <- flowOps.stateOps.store.toRecordIO {
            val isInternalMap = params.updatedUserMap.flatMap { case (userId, changes) =>
              changes.roleSet.map(role => userId -> DataRoomRoleUtils.isInternal(role))
            }
            val (internalUsers, externalUsers) = isInternalMap.partition(_._2)
            dataRoomContactService.syncContactRole(
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              added = internalUsers.keySet,
              removed = externalUsers.keySet
            )
          }
        } yield invitationModified -> existing
      }
      _ <- dataRoomTermsOfAccessService.changeTermsFolderPermissionsUnsafe(
        actor,
        params.dataRoomWorkflowId,
        updatedUserRoleMap
      )
      _ <- dataRoomHomePageService.changeHomePageFolderPermissions(
        actor,
        params.dataRoomWorkflowId,
        updatedUserRoleMap
      )
      _ <- ZIOUtils.when(mode == UserPermissionChangeMode.Individual) {
        for {
          _ <- modifyInvitationAndExisting(
            actor = actor,
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            invitationModified = invitationModified,
            existing = existing,
            actorIp = ctx.flatMap(_.getClientIP),
            modifyPermissionSourceOfEvent = modifyPermissionSourceOfEvent
          )
        } yield ()
      }
      _ <- ZIOUtils.when(notifyUpdate) {
        dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId) *>
          dataRoomSharedFlowService.modifyFileUpdated(params.dataRoomWorkflowId)
      }
    } yield DataRoomEmptyResponse()
  }

  def remindInvitation(
    params: RemindInvitationToDataRoomParams,
    actor: ServiceActor,
    ctx: Option[AuthenticatedRequestContext] = None
  ): Task[DataRoomEmptyResponse] = {
    for {
      (teamId, dataRoomCreatedState) <- execute { (flowOps, validateOps) =>
        for {
          teamId <- validateOps.modelOps.getTeamId(params.dataRoomWorkflowId)
          inviterOptList <- RecordIO.parTraverseN(64)(params.userIds) { invitedUser =>
            getInviterOpt(
              validateOps,
              teamId,
              invitedUser
            ).map(invitedUser -> _)
          }
          _ <- RecordIO.parTraverseN(64)(inviterOptList) { case (invited, inviterOpt) =>
            RecordIO.validate(inviterOpt.nonEmpty) {
              new DataRoomException(s"Unable to remind invitation user $invited who are not invited")
            }
          }
          areOnlyInvitedByActor = inviterOptList.flatMap(_._2).toSet == Set(actor.userId)
          _ <- validateOps.validateAndGetCurrentState(
            params.dataRoomWorkflowId,
            actor.userId,
            if (areOnlyInvitedByActor) Set() else Set(DataRoomRoleUtils.isInternal)
          )
          dataRoomCreatedState <- flowOps.remindInvitation(
            params.dataRoomWorkflowId,
            actor.userId,
            params.userIds.toSeq,
            ctx.flatMap(_.getClientIP)
          )
        } yield teamId -> dataRoomCreatedState
      }
      invitedUsers <- ZIO.foreach(params.userIds) { invitedUser =>
        teamService
          .remindInvitedUser(
            RemindInvitedUserParams(
              actor.userId,
              invitedUser,
              teamId
            )
          )
          .map(invitedUser -> _)
      }
      authenticationWhitelabelIdOpt <- dataRoomWhiteLabelService.getAuthenticationWhitelabelId(params.dataRoomWorkflowId)
      _ <- ZIO.foreach(invitedUsers) { case (invitedUser, flowState) =>
        for {
          userInvited <- flowState match {
            case userInvited: UserInvited =>
              ZIO.attempt(userInvited)
            case _ =>
              ZIO.fail(new RuntimeException("User is not in UserInvited state after reminder"))
          }
          allVariables <- dataRoomEmailService.getAllVariables(
            dataRoomCreatedState,
            actor.userId,
            Some(userInvited.inviter),
            Some(userInvited.invitee),
            userInvited.invitedAt
          )
          _ <- dataRoomEmailSenderService.sendEmail(
            params.dataRoomWorkflowId,
            generate.DataRoomInvitationEmailGenerate(
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              inviter = userInvited.inviter,
              invitedUser = invitedUser,
              dataRoomName = dataRoomCreatedState.name,
              subject = DataRoomEmailUtils.populateSubjectVariables(params.subject, allVariables),
              message = DataRoomEmailUtils.populateFieldVariables(params.message, allVariables),
              buttonLabel = DataRoomEmailUtils.populateFieldVariables(params.buttonLabel, allVariables),
              invitedAt = userInvited.invitedAt,
              authenticationWhitelabelIdOpt = authenticationWhitelabelIdOpt
            )
          )
        } yield ()
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
    } yield DataRoomEmptyResponse()
  }

  def removeUsers(
    params: RemoveUsersFromDataRoomParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext] = None,
    shouldRemoveFromCurrentGroup: Boolean = true,
    validatePermission: Boolean = true
  ): Task[DataRoomEmptyResponse] = {
    for {
      (teamId, dataRoomCreatedState, removedUsers, _, usersLeaveByGroup) <- executeWithGroup {
        (flowOps, validateOps, groupOps) =>
          val teamOps = groupOps.teamOps
          for {
            individualUserTeam <- validateOps.modelOps.getTeamId(params.dataRoomWorkflowId)
            inviterOptMap <- RecordIO.parTraverseN(4)(params.userIds) { userId =>
              for {
                inviterOpt <- getInviterOpt(
                  validateOps,
                  individualUserTeam,
                  userId
                )
              } yield userId -> inviterOpt
            }
            (removedUsers, cancelledInvitations) = inviterOptMap.partitionMap { case (userId, inviterOpt) =>
              inviterOpt.fold[Either[UserId, UserId]](Left(userId))(_ => Right(userId))
            }
            areAllActorOrInvitedByActor = inviterOptMap.forall { case (userId, inviterOpt) =>
              userId == actor || inviterOpt.contains(actor)
            }
            _ <- RecordIO.when(validatePermission) {
              validateOps.validateAndGetCurrentState(
                dataRoomWorkflowId = params.dataRoomWorkflowId,
                userId = actor,
                roleChecks = if (areAllActorOrInvitedByActor) Set() else Set(DataRoomRoleUtils.isAdmin)
              )
            }
            _ <- validateOps.validateAtLeastOneJoinedAdminWhenUpdatingRoles(
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              newUserRoles = params.userIds.map(_ -> DataRoomRole.Empty).toMap,
              actor = actor
            )
            _ <- flowOps.cancelInvitation(
              params.dataRoomWorkflowId,
              actor,
              cancelledInvitations,
              httpContext.flatMap(_.getClientIP)
            )
            _ <- RecordIO.parTraverseN(2)(cancelledInvitations) { userId =>
              teamOps.cancelInvitation(
                CancelInviteParams(
                  actor,
                  userId,
                  individualUserTeam
                )
              )
            }
            dataRoomCreatedState <- flowOps.removeUsers(
              params.dataRoomWorkflowId,
              actor,
              removedUsers,
              httpContext.flatMap(_.getClientIP)
            )
            _ <- RecordIO.parTraverseN(2)(removedUsers) { removedUser =>
              teamOps
                .removeMember(
                  RemoveMemberParams(
                    actor,
                    removedUser,
                    individualUserTeam
                  )
                )
                .catchAllCause { cause =>
                  RecordIO.logWarningCause(
                    s"Failed to remove user ${removedUser} from data room team ${params.dataRoomWorkflowId}",
                    cause
                  )
                }
            }
            _ <- flowOps.stateOps.store.toRecordIO {
              dataRoomContactService.syncContactRole(
                dataRoomWorkflowId = params.dataRoomWorkflowId,
                removed = params.userIds.toSet
              )
            }
            usersLeaveByGroup <-
              if (shouldRemoveFromCurrentGroup) {
                groupOps.removeUsersFromCurrentGroup(
                  dataRoomWorkflowId = params.dataRoomWorkflowId,
                  userIds = params.userIds.toSeq,
                  actor = actor,
                  actorIpOpt = httpContext.flatMap(_.getClientIP)
                )
              } else RecordIO.succeed(Map.empty[DataRoomGroupId, List[UserId]])
          } yield (individualUserTeam, dataRoomCreatedState, removedUsers, cancelledInvitations, usersLeaveByGroup)
      }
      _ <- dataRoomTermsOfAccessService.changeTermsFolderPermissionsUnsafe(
        actor,
        params.dataRoomWorkflowId,
        params.userIds.map(_ -> DataRoomRole.Empty).toMap
      )
      _ <- dataRoomHomePageService.changeHomePageFolderPermissions(
        actor,
        params.dataRoomWorkflowId,
        params.userIds.map(_ -> DataRoomRole.Empty).toMap
      )
      _ <- fileService.modifyPermissionsRecursively(
        actor = actor,
        rootFolderId = FolderId.channelSystemFolderId(params.dataRoomWorkflowId),
        changes = PermissionChanges(revokedUsers = params.userIds.toSet),
        mode = FileService.PermissionModMode.Unsafe,
        actorIp = httpContext.flatMap(_.getClientIP)
      )
      _ <- greylinDataService.runUnit(
        operation.DataRoomParticipantOperations
          .removeParticipants(params.userIds.map((_, params.dataRoomWorkflowId)).toList)
      )
      _ <-
        if (params.doNotNotifyByEmail) {
          ZIO.logInfo("Skipped sending notification email due to flag doNotNotifyByEmail")
        } else {
          for {
            participantRoles <- getDataRoomRoleMap(params.dataRoomWorkflowId)
            joinedUsers <- teamService.getJoinedMembers(teamId)
            adminUsers = joinedUsers.filter {
              participantRoles.get(_).exists(DataRoomRoleUtils.isAdmin)
            }
            _ <- ZIOUtils.when(removedUsers.nonEmpty) {
              dataRoomEmailSenderService.sendEmail(
                params.dataRoomWorkflowId,
                DataRoomUserRemovalEmailGenerate(
                  params.dataRoomWorkflowId,
                  actor,
                  removedUsers,
                  adminUsers,
                  dataRoomCreatedState.name
                )
              )
            }
          } yield ()
        }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
      _ <- dataRoomSharedFlowService.modifyFileUpdated(params.dataRoomWorkflowId)
      _ <- ZIO.foreach(dataRoomCreatedState.linkInvitationMap.keys.toSeq) { linkId =>
        protectedLinkService.revokeAccess(linkId, params.userIds)
      }
      _ <- FDBRecordDatabase.transact(FDBOperations[DataRoomParticipantOperations].Production) { ops =>
        ops.removeParticipants(params.dataRoomWorkflowId, params.userIds.toSeq)
      }

      // send webhook for grouped users
      _ <- ZIO.foreachDiscard(usersLeaveByGroup) { case groupId -> userIds =>
        dataRoomWebhookProducer.produceUserRemoveFromGroupEvent(
          params.dataRoomWorkflowId,
          userIds,
          groupId
        )
      }
      _ <- dataRoomWebhookProducer.produceUserRemovedEvent(
        params.dataRoomWorkflowId,
        params.userIds.toSeq
      )
    } yield DataRoomEmptyResponse()
  }

  def manualNotification(
    params: DataRoomManualNotificationParams,
    actor: ServiceActor,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[DataRoomEmptyResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"Actor ${actor.userId} is manually sending email in dataroom ${params.dataRoomWorkflowId.idString}"
      )
      (teamId, createdState) <- execute { (_, validateOps) =>
        validateOps.validateAndGetCurrentState(
          dataRoomWorkflowId = params.dataRoomWorkflowId,
          userId = actor.userId,
          roleChecks = Set()
        )
      }
      (recipients, showContactSuggestion) <-
        if ((params.recipients - actor.userId).nonEmpty) {
          ZIO.succeed(params.recipients -> false)
        } else {
          dataRoomContactService
            .getDataRoomPointsOfContact(params.dataRoomWorkflowId, actor.userId)
            .map(poc => poc._1 ++ params.recipients -> poc._2)
        }
      _ <- ZIOUtils.validate {
        ZIO
          .foreachPar(recipients) { userId =>
            teamService.isMemberOf(userId, Set(teamId))
          }
          .map(_.forall(identity))
      } {
        new RuntimeException(s"There exists at least 1 user who hasn't joined ${params.dataRoomWorkflowId.idString}")
      }
      _ <- ZIOUtils.validate(params.subject.trim.nonEmpty && params.message.trim.nonEmpty) {
        new RuntimeException("Either subject or message is empty")
      }
      (remainingRecipients, sendToActor) = recipients.toSeq.partition(_ == actor.userId)
      _ <- ZIO.foreach(Seq(remainingRecipients, sendToActor)) { recipientsGroup =>
        dataRoomEmailSenderService.sendEmail(
          params.dataRoomWorkflowId,
          DataRoomManualNotificationMultipleEmailGenerate(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            sender = actor.userId,
            receivers = recipientsGroup,
            dataRoomName = createdState.name,
            subject = DataRoomEmailUtils.sanitizeSubject(params.subject.trim),
            message = DataRoomEmailUtils.sanitize(params.message.trim),
            showContactSuggestion = showContactSuggestion
          )
        )
      }
      _ <- dataRoomLoggingService.logEventSendParticipantMessage(
        params.dataRoomWorkflowId,
        createdState.name,
        createdState.creatorEntityId,
        actor.userId,
        httpContext
      )
    } yield DataRoomEmptyResponse()
  }

  def getDataRoomParticipants(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userIds: Set[UserId]
  ): Task[Map[UserId, DataRoomParticipant]] = {
    FDBRecordDatabase
      .transact(
        DataRoomParticipantOperations.Production
      ) { participantOps =>
        RecordIO.parTraverseN(16)(userIds) { userId =>
          participantOps.getOptParticipant(dataRoomWorkflowId, userId).map(_.map(userId -> _))
        }
      }
      .map(_.flatten.toMap)
  }

  def setViewedSearchOnboarding(
    params: DataRoomSetViewedSearchOnboardingParams,
    ctx: AuthenticatedRequestContext
  ): Task[Unit] = {
    FDBRecordDatabase.transact(
      DataRoomParticipantOperations.Production
    ) {
      _.updateParticipants(
        params.dataRoomWorkflowId,
        Set(ctx.actor.userId),
        _.copy(viewedSearchOnboarding = true)
      )
    }
  }

  def setViewedGroupOnboarding(
    params: DataRoomSetViewedGroupOnboardingParams,
    ctx: AuthenticatedRequestContext
  ): Task[Unit] = {
    FDBRecordDatabase.transact(
      DataRoomParticipantOperations.Production
    ) {
      _.updateParticipants(
        params.dataRoomWorkflowId,
        Set(ctx.actor.userId),
        _.copy(viewedGroupOnboarding = true)
      )
    }
  }

  def getParticipatingDataRooms(userId: UserId): Task[List[DataRoomWorkflowId]] = {
    FDBRecordDatabase.transact(DataRoomParticipantOperations.Production)(_.getUserDataRooms(userId))
  }

  def getAdminDataRooms(userId: UserId): Task[List[DataRoomWorkflowId]] = {
    FDBRecordDatabase.transact(
      FDBOperations[(DataRoomParticipantOperations, DataRoomParticipantRoleOperations)].Production
    ) { (participantOps, roleOps) =>
      for {
        participatingDataRooms <- participantOps.getUserDataRooms(userId)
        adminDataRooms <- RecordIO
          .parTraverseN(4)(participatingDataRooms) { dataRoomId =>
            roleOps.getParticipantRole(dataRoomId, userId).map { role =>
              if (DataRoomRoleUtils.isAdmin(role)) {
                Some(dataRoomId)
              } else {
                None
              }
            }
          }
          .map(_.flatten)
      } yield adminDataRooms
    }
  }

  def getMemberState(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userId: UserId
  ): Task[Option[TeamMemberFlowState]] = {
    for {
      memberStateOpt <- FDBRecordDatabase.transact(
        FDBOperations[(DataRoomModelStoreOperations, TeamMemberStateStoreOperations)].Production
      ) { case (modelStoreOps, teamStoreOps) =>
        for {
          teamId <- modelStoreOps.getTeamId(dataRoomWorkflowId)
          memberStateOpt <- teamStoreOps.getMemberState(teamId, userId)
        } yield memberStateOpt
      }
    } yield memberStateOpt
  }

  private[dataroom] def addUserReviewedToA(
    toaFileId: FileId,
    ctx: AuthenticatedRequestContext
  ): Task[Unit] = {
    for {
      termsOfAccessId <- ZIOUtils.optionToTask(
        toaFileId.channel match {
          case dataRoomTermsOfAccessId: DataRoomTermsOfAccessId => Some(dataRoomTermsOfAccessId)
          case _                                                => None
        },
        new RuntimeException(s"File $toaFileId's channel is not DataRoomTermsOfAccessId")
      )
      dataRoomWorkflowId = termsOfAccessId.parent
      _ <- FDBRecordDatabase.transact(FDBOperations[DataRoomParticipantOperations].Production) { participantOps =>
        for {
          _ <- participantOps.upsertParticipant(
            dataRoomWorkflowId,
            ctx.actor.userId,
            participant =>
              participant.copy(
                reviewedToaFiles = participant.reviewedToaFiles + toaFileId
              )
          )
        } yield ()
      }
      _ <- ZIO.logInfo(s"User ${ctx.actor.userId} reviewed Terms of Access file $toaFileId")
    } yield ()
  }

  def getAllParticipants(
    dataRoomWorkflowId: DataRoomWorkflowId
  ): Task[Map[UserId, DataRoomParticipant]] = {
    FDBRecordDatabase.transact(DataRoomParticipantOperations.Production)(
      _.getParticipantMap(dataRoomWorkflowId)
    )
  }

  def getAllUnassignedParticipants(
    dataRoomWorkflowId: DataRoomWorkflowId
  ): Task[Map[UserId, DataRoomParticipant]] = {
    getAllParticipants(dataRoomWorkflowId).map(_.filter(_._2.groupIds.isEmpty))
  }

  def getDataRoomRoleMap(
    dataRoomWorkflowId: DataRoomWorkflowId,
    updatedIndividualRoles: Map[UserId, DataRoomRole] = Map.empty[UserId, DataRoomRole],
    updatedGroupRoles: Map[DataRoomGroupId, DataRoomRole] = Map.empty[DataRoomGroupId, DataRoomRole]
  ): Task[Map[UserId, DataRoomRole]] = {
    DataRoomParticipantRoleOperations.transact(
      _.getAllParticipantRoleMap(dataRoomWorkflowId, updatedIndividualRoles, updatedGroupRoles)
    )
  }

  def getParticipantRoleMap(
    dataRoomWorkflowId: DataRoomWorkflowId,
    userIds: Seq[UserId],
    updatedIndividualRoles: Map[UserId, DataRoomRole] = Map.empty[UserId, DataRoomRole],
    updatedGroupRoles: Map[DataRoomGroupId, DataRoomRole] = Map.empty[DataRoomGroupId, DataRoomRole]
  ): Task[Map[UserId, DataRoomRole]] = {
    DataRoomParticipantRoleOperations
      .transact { roleOps =>
        RecordIO.traverse(userIds) { userId =>
          roleOps
            .getParticipantRole(
              dataRoomWorkflowId,
              userId,
              updatedIndividualRoles,
              updatedGroupRoles
            )
            .map(userId -> _)
        }
      }
      .map(_.toMap)
  }

}

object DataRoomParticipantService {

  sealed trait UserPermissionChangeMode derives CanEqual

  object UserPermissionChangeMode {

    case object Individual extends UserPermissionChangeMode

    final case class Group(canChangeRole: Boolean) extends UserPermissionChangeMode

  }

}
