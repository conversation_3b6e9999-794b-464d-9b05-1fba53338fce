// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.notification

import anduin.dataroom.flow.{DataRoomFlowOperations, DataRoomSharedFlowService, DataRoomValidateOperations}
import anduin.dataroom.role.DataRoomRoleUtils
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.service.{AuthenticatedRequestContext, ServiceActor}

import anduin.stargazer.service.dataroom.*
import zio.{Task, ZIO}

final case class DataRoomNotificationService(
  dataRoomSharedFlowService: DataRoomSharedFlowService
) {

  def getDataRoomNotificationSettings(
    params: GetDataRoomNotificationSettingsParams,
    actor: UserId
  ): Task[GetDataRoomNotificationSettingsResponse] = {
    for {
      _ <- ZIO.logInfo(s"Getting notification settings for user $actor with params $params")
      res <- FDBRecordDatabase.transact(
        FDBOperations[(DataRoomNotificationSettingsOperations, DataRoomValidateOperations)].Production
      ) { case (notificationOps, validateOps) =>
        for {
          (_, drState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = actor,
            roleChecks = Set()
          )
          isAdmin <- validateOps.roleOps
            .getParticipantRole(params.dataRoomWorkflowId, actor)
            .map(DataRoomRoleUtils.isAdmin)
          settings <- notificationOps.get(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            userId = actor,
            notificationConfigsOpt = drState.dataRoomNotificationConfigs
          )
        } yield GetDataRoomNotificationSettingsResponse(
          isAdmin,
          // If user is not an admin, always returns None for invitation accepted email notification
          if (isAdmin) settings.invitationAcceptedEmail else InvitationAcceptedEmailNotification.None,
          settings.newFileNotification,
          settings.newFileNotificationFrequency
        )
      }
    } yield res
  }

  def setDataRoomNotificationSettingsUnsafe(
    params: SetDataRoomNotificationSettingsParams,
    actor: ServiceActor
  ): Task[DataRoomEmptyResponse] = {
    for {
      _ <- ZIO.logInfo(s"Setting notification settings for user ${actor.userId} with params $params")
      _ <- FDBRecordDatabase.transact(DataRoomNotificationSettingsOperations.Production) { notificationOps =>
        notificationOps.setNotificationSettings(
          dataRoomWorkflowId = params.dataRoomWorkflowId,
          userId = actor.userId,
          invitationAcceptedEmailNotification = params.invitationAcceptedEmailNotification,
          newFileNotification = params.newFileNotification,
          newFileNotificationFrequency = params.newFileNotificationFrequency
        )
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
    } yield DataRoomEmptyResponse()
  }

  private val defaultNewFileNotificationConfig = NewFileNotificationConfig(
    notificationMode = NotificationMode.ParticipantsChoice,
    notificationFrequency = NotificationFrequency.Daily
  )

  def getDefaultNotificationConfigs: DataRoomNotificationConfigs =
    DataRoomNotificationConfigs(
      newFileNotificationConfig = Some(defaultNewFileNotificationConfig)
    )

  def getDataRoomNotificationConfigs(
    dataRoomWorkflowId: DataRoomWorkflowId,
    actor: UserId,
    bypassAdminCheck: Boolean = false
  ): Task[DataRoomNotificationConfigs] = {
    for {
      dataRoomNotificationConfigs <- FDBRecordDatabase.transact(
        FDBOperations[DataRoomValidateOperations].Production
      ) { validateOps =>
        for {
          (_, createdState) <- validateOps.validateAndGetCurrentState(
            dataRoomWorkflowId = dataRoomWorkflowId,
            userId = actor,
            roleChecks = if (bypassAdminCheck) Set() else Set(DataRoomRoleUtils.isAdmin)
          )
        } yield normalizeDataRoomNotificationConfigs(createdState.dataRoomNotificationConfigs)
      }
    } yield dataRoomNotificationConfigs
  }

  def normalizeDataRoomNotificationConfigs(
    dataRoomNotificationConfigsOpt: Option[DataRoomNotificationConfigs]
  ): DataRoomNotificationConfigs = {
    DataRoomNotificationConfigs(
      newFileNotificationConfig = Some(
        dataRoomNotificationConfigsOpt
          .flatMap(_.newFileNotificationConfig)
          .getOrElse(defaultNewFileNotificationConfig)
      )
    )
  }

  def updateDataRoomNotificationConfigs(
    params: UpdateDataRoomNotificationConfigsParams,
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomEmptyResponse] = {
    for {
      _ <- ZIO.logInfo(s"Setting notification configs for data room ${params.dataRoomWorkflowId}")
      _ <- FDBRecordDatabase.transact(
        FDBOperations[DataRoomFlowOperations].Production
      ) { flowOps =>
        for {
          _ <- flowOps.updateNewFileNotificationConfig(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            actor = ctx.actor.userId,
            actorIp = ctx.getClientIP,
            newFileNotificationConfigOpt = params.newFileNotificationConfigOpt
          )
        } yield ()
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
    } yield DataRoomEmptyResponse()
  }

}
