// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.email

import anduin.dataroom.event.DataRoomEmailConfigs
import anduin.email.smtp.CustomSmtpServerConfigModel
import anduin.encryption.StreamingEncryptionService
import anduin.entity.model.EntityDataRoomEmailConfigs
import anduin.model.common.emailaddress.EmailAddress
import zio.{Task, ZIO}
import anduin.model.id.stage.DataRoomWorkflowId
import com.anduin.stargazer.external.base64.Base64
import com.anduin.stargazer.service.email.EmailProviderBuilderConfig.DeprecatedSmtpBuilderConfig
import com.anduin.stargazer.service.email.generate.GenerateEmail
import com.anduin.stargazer.service.email.{EmailProviderBuilderConfig, EmailSenderService}

final case class DataRoomEmailSenderService(
  emailSenderService: EmailSenderService,
  encryptionService: StreamingEncryptionService,
  dataRoomEmailService: DataRoomEmailService
) {

  def sendEmail(
    dataRoomWorkflowId: DataRoomWorkflowId,
    generateEmail: => GenerateEmail
  ): Task[Unit] = {
    for {
      smtpConfigOpt <- getDataRoomSmtpConfig(dataRoomWorkflowId)
      emailProvider <- smtpConfigOpt match {
        case Some(smtpConfig) =>
          for {
            password <- encryptionService
              .decrypt(Base64.toByteArray(smtpConfig.encryptedPassword))
              .map(String(_))
          } yield DeprecatedSmtpBuilderConfig(
            from = smtpConfig.from.map { email =>
              EmailAddress(
                name = Some(email.name),
                address = email.address
              )
            },
            host = smtpConfig.host,
            port = smtpConfig.port,
            username = smtpConfig.userName,
            password = password,
            tls = smtpConfig.tls
          )
        case None => ZIO.succeed(EmailProviderBuilderConfig.defaultBuilderConfig)
      }
      _ <- emailSenderService.enqueue(
        generateEmail,
        dataRoomWorkflowId,
        emailProvider
      )
    } yield ()
  }

  private def getDataRoomSmtpConfig(
    dataRoomWorkflowId: DataRoomWorkflowId
  ): Task[Option[CustomSmtpServerConfigModel]] = {
    for {
      emailConfig <- dataRoomEmailService.getDataRoomEmailServerConfigs(dataRoomWorkflowId)
      smtpConfig = emailConfig.flatMap {
        case drConfig: DataRoomEmailConfigs =>
          if (drConfig.enableCustomSmtp) drConfig.smtpConfig else None
        case entityConfig: EntityDataRoomEmailConfigs =>
          if (entityConfig.enableCustomSmtp) entityConfig.smtpConfig else None
      }
    } yield smtpConfig
  }

}
