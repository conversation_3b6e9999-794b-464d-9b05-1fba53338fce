// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.homepage

import anduin.account.profile.UserProfileService
import anduin.dataroom.email.{DataRoomEmailSenderService, DataRoomEmailService}
import anduin.dataroom.email.generate.DataRoomHomePageMessageSingleEmailGenerate
import anduin.dataroom.flow.DataRoomValidateOperations.PlanCheck
import anduin.dataroom.homepage.DataRoomHomeSection.{DataRoomHomeFooterSection, DataRoomHomeHeaderSection}
import anduin.dataroom.role.{DataRoomRole, DataRoomRoleUtils}
import DataRoomHomePageConstants.DataRoomHomePageFileType
import anduin.dataroom.service.DataRoomLoggingService
import anduin.dms.DmsFeature.DataRoom
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.link.LinkGeneratorService
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.model.document.DocumentStorageId
import anduin.model.id.stage.{DataRoomHomePageId, DataRoomWorkflowId}
import anduin.model.id.{BatchUploadIdFactory, DataRoomHomeSectionId, FileId, FolderId}
import anduin.orgbilling.model.plan.DataRoomPremiumFeature
import anduin.portaluser.ExecutiveAdmin
import anduin.protobuf.flow.file.{FileFolderPermission, FileFolderPermissionMap}
import anduin.service.{AuthenticatedRequestContext, GeneralServiceException}
import anduin.storageservice.common.FileContentOrigin
import anduin.util.FilenameUtils
import anduin.utils.stream.ZStreamIOUtils
import com.anduin.stargazer.endpoints.PermissionChanges
import com.anduin.stargazer.service.api.FileDownloadService
import anduin.dms.service.FileService.PermissionModMode
import anduin.dms.upload.{FileUploadHandler, FileUploadService}
import sttp.model.MediaType
import anduin.dataroom.flow.*
import anduin.stargazer.service.dataroom.*
import com.anduin.stargazer.service.utils.ZIOUtils
import zio.{Task, ZIO}
import anduin.dataroom.validator.DataRoomValidator
import anduin.dms.service.FileService
import anduin.dms.upload.flow.state.DirectFileUploadData
import anduin.documentservice.thumbnail.ThumbnailFileService
import anduin.file.FileMetadataKey
import anduin.id.ModelIdRegistry
import anduin.id.upload.{BatchUploadId, FileUploadId}
import anduin.utils.stream.ZStreamIOUtils.ByteStream

final class DataRoomHomePageService(
  fileService: FileService,
  executiveAdmin: ExecutiveAdmin,
  dataRoomLoggingService: DataRoomLoggingService,
  dataRoomEmailSenderService: DataRoomEmailSenderService,
  thumbnailFileService: ThumbnailFileService,
  fileUploadService: FileUploadService,
  dataRoomValidator: DataRoomValidator
)(
  using val linkGeneratorService: LinkGeneratorService,
  val fileDownloadService: FileDownloadService,
  val userProfileService: UserProfileService,
  val dataRoomEmailService: DataRoomEmailService
) {

  private def validate(
    validateOps: DataRoomValidateOperations,
    dataRoomWorkflowId: DataRoomWorkflowId,
    actor: UserId
  ) = {
    validateOps.validateAndGetCurrentState(
      dataRoomWorkflowId = dataRoomWorkflowId,
      userId = actor,
      roleChecks = Set(DataRoomRoleUtils.isAdmin),
      checkValidPlanWithFeatures = PlanCheck.RequirePlan(Set(DataRoomPremiumFeature.HomePage))
    )
  }

  def ensureInitialized(
    dataRoomWorkflowId: DataRoomWorkflowId,
    ctx: AuthenticatedRequestContext
  ): Task[Unit] = {
    for {
      isExisting <- FDBRecordDatabase.transact(DataRoomHomePageStoreOperations.Production) {
        _.exists(dataRoomWorkflowId)
      }
      _ <- ZIOUtils.unless(isExisting) {
        for {
          uploadResp <- internalUploadHomePageFiles(
            UploadDataRoomHomePageFilesParams(dataRoomWorkflowId),
            defaultFileList,
            Map.empty,
            ctx
          )
          _ <- FDBRecordDatabase.transact(DataRoomHomePageStoreOperations.Production) {
            _.create(
              DataRoomHomePageStoreOperations.getDefaultDataRoomHomePage(
                dataRoomWorkflowId,
                ctx.actor.userId,
                uploadResp.fileMap
              )
            )
          }
        } yield ()
      }
    } yield ()
  }

  private def getHomePageFileMap(state: DataRoomHomePageState, actor: UserId) = {
    val fileIds = state.sections.flatMap {
      case intro: DataRoomHomeSection.DataRoomHomeIntroSection       => intro.mediaFileUrl.flatMap(_.fileIdOpt).toList
      case document: DataRoomHomeSection.DataRoomHomeDocumentSection => document.fileIds
      case _                                                         => Seq()
    }
    ZIOUtils
      .foreachParN(4)(fileIds) { fileId =>
        for {
          name <- fileService.getFileName(actor)(fileId)
          permission <- fileService.getFilePermission(actor)(fileId)
        } yield fileId -> DataRoomHomePageFileInfo(name, permission)
      }
      .map(_.toMap)
  }

  def getDataRoomHomePage(
    params: GetDataRoomHomePageParams,
    actor: UserId
  ): Task[GetDataRoomHomePageResponse] = {
    dataRoomValidator.checkHomePage(params, actor) *> getDataRoomHomePageUnsafe(params, actor)
  }

  def getDataRoomHomePageUnsafe(
    params: GetDataRoomHomePageParams,
    actor: UserId
  ): Task[GetDataRoomHomePageResponse] = {
    for {
      homePageModel <- FDBRecordDatabase.transact(DataRoomHomePageStoreOperations.Production)(
        _.get(params.dataRoomWorkflowId)
      )
      homePageState <- DataRoomHomePageModelConverter.stateFromProtoModel(homePageModel, actor)
      fileMap <- getHomePageFileMap(homePageState, actor)
    } yield GetDataRoomHomePageResponse(
      homePageState,
      DataRoomHomePageAdditional(fileMap)
    )
  }

  def toggleDataRoomHomePagePublishStatus(
    params: ToggleDataRoomHomePagePublishStatusParams,
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomEmptyResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"START ${ctx.actor.userId} set home page status of ${params.dataRoomWorkflowId} to ${params.setToPublished}"
      )
      dataRoomState <- FDBRecordDatabase.transact(
        FDBOperations[(DataRoomValidateOperations, DataRoomHomePageStoreOperations)].Production
      ) { case (validateOps, homePageOps) =>
        for {
          (_, dataRoomState) <- validate(
            validateOps = validateOps,
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            actor = ctx.actor.userId
          )
          _ <- homePageOps.update(params.dataRoomWorkflowId, ctx.actor.userId) {
            _.withPublished(params.setToPublished)
          }
        } yield dataRoomState
      }
      _ <- ZIOUtils.when(params.setToPublished)(
        dataRoomLoggingService.logEventPublishHomePage(
          params.dataRoomWorkflowId,
          dataRoomState.name,
          dataRoomState.creatorEntityId,
          ctx.actor,
          Some(ctx)
        )
      )
      _ <- ZIO.logInfo(
        s"DONE ${ctx.actor.userId} set home page status of ${params.dataRoomWorkflowId} to ${params.setToPublished}"
      )
    } yield DataRoomEmptyResponse()
  }

  def updateDataRoomHomePage(
    params: UpdateDataRoomHomePageParams,
    ctx: AuthenticatedRequestContext
  ): Task[UpdateDataRoomHomePageResponse] = {
    for {
      _ <- ZIO.logInfo(s"START ${ctx.actor.userId} update data room home page of ${params.dataRoomWorkflowId}")
      _ <- FDBRecordDatabase.transact(DataRoomValidateOperations.Production) { validateOps =>
        validate(validateOps, params.dataRoomWorkflowId, ctx.actor.userId)
      }
      homePageFolderId = FolderId.channelSystemFolderId(DataRoomHomePageId(params.dataRoomWorkflowId))
      _ <- ZIOUtils.validate(params.fileMap.forall(_._2.parent == homePageFolderId)) {
        GeneralServiceException(
          s"Failed to update data room home page of ${params.dataRoomWorkflowId}, homepage files not belong to homepage folder"
        )
      }
      _ <- ZIO.foreachDiscard(params.fileThumbnail) { (fileId, thumbnailFileId) =>
        thumbnailFileService.replaceFileThumbnail(fileId, thumbnailFileId, ctx.actor.userId)
      }
      updatedSections = DataRoomHomePageModelConverter.protoModelFromState(
        params.newState,
        params.fileMap
      )
      lastConflictUpdated <- FDBRecordDatabase.transact(DataRoomHomePageStoreOperations.Production) { homePageOps =>
        for {
          homePageModel <- homePageOps.get(params.dataRoomWorkflowId)
          lastConflictUpdated = {
            for {
              modifiedAt <- homePageModel.modifiedAt
              if !params.forceUpdate && !params.lastUpdate.contains(modifiedAt)
            } yield UpdateHomePageConflictDetail(modifiedAt, homePageModel.modifier)
          }
          _ <- RecordIO.when(lastConflictUpdated.isEmpty) {
            homePageOps.update(params.dataRoomWorkflowId, ctx.actor.userId)(_ => updatedSections)
          }
        } yield lastConflictUpdated
      }
      _ <- ZIO.logInfo(s"DONE ${ctx.actor.userId} update data room home page of ${params.dataRoomWorkflowId}")
    } yield UpdateDataRoomHomePageResponse(lastConflictUpdated)
  }

  private def uploadDocumentThumbnails(homePageFolder: FolderId, files: Seq[DirectFileUploadData], actor: UserId) = {
    val thumbnails = for {
      file <- files
      _ <- file.metadata
        .get(FileMetadataKey.dataRoomHomePageFileType.value)
        .filter(Set(DataRoomHomePageFileType.DocumentThumbnailImage.value))
      fileId <- file.metadata.get(FileMetadataKey.originalFileId.value).flatMap(ModelIdRegistry.parser.parseAs[FileId])
    } yield fileId -> file
    ZIO.foreach(thumbnails) { case (fileId, thumbnailFile) =>
      for {
        fileName <- fileService.getFileName(actor)(fileId)
        thumbnailFileName = FilenameUtils.getThumbnailFileName(fileName)
        thumbnailFileId <- fileService.uploadFile(
          parentFolderId = homePageFolder,
          fileName = thumbnailFileName,
          content = FileContentOrigin.FromStorageId(thumbnailFile.storageId, thumbnailFile.bucket),
          uploader = actor
        )
      } yield fileId -> thumbnailFileId
    }
  }

  def internalUploadHomePageFiles(
    params: UploadDataRoomHomePageFilesParams,
    files: Seq[DirectFileUploadData],
    fileContent: Map[FileUploadId, ByteStream],
    ctx: AuthenticatedRequestContext
  ): Task[UploadDataRoomHomePageFilesResponse] = {
    val batchUploadId = BatchUploadIdFactory.unsafeRandomId
    for {
      _ <- createUploadHomePageFiles(
        FileUploadHandler.CreateInput(
          batchUploadId,
          params,
          files,
          Seq.empty,
          ctx
        )
      )
      _ <- ZIO.foreachDiscard(fileContent) { (fileUploadId, content) =>
        fileUploadService.uploadFileContent(
          batchUploadId,
          fileUploadId,
          content,
          ctx.actor.userId
        )
      }
      uploadResp <- completeUploadHomePageFiles(
        FileUploadHandler.CompleteInput(batchUploadId, params, files, Seq.empty, ctx)
      )
    } yield uploadResp
  }

  private def createUploadHomePageFiles(
    input: FileUploadHandler.CreateInput[UploadDataRoomHomePageFilesParams]
  ): Task[BatchUploadId] = {
    val actor = input.httpRequestContext.actor.userId
    for {
      _ <- FDBRecordDatabase.transact(DataRoomValidateOperations.Production) { validateOps =>
        validate(validateOps, input.params.dataRoomWorkflowId, actor)
      }
      _ <- fileUploadService.createDirectUploadUnsafe(
        input.batchUploadId,
        input.params.apiName,
        Some(input.params.toJsonString),
        input.files,
        input.emptyFolders,
        actor
      )
    } yield input.batchUploadId
  }

  private def completeUploadHomePageFiles(
    input: FileUploadHandler.CompleteInput[UploadDataRoomHomePageFilesParams]
  ): Task[UploadDataRoomHomePageFilesResponse] = {
    val dataRoomWorkflowId = input.params.dataRoomWorkflowId
    val homePageId = DataRoomHomePageId(dataRoomWorkflowId)
    val folderId = FolderId.channelSystemFolderId(homePageId)
    val actor = input.httpRequestContext.actor.userId
    for {
      permissionMap <- FDBRecordDatabase.transact(DataRoomValidateOperations.Production) { validateOps =>
        for {
          (teamId, _) <- validate(
            validateOps = validateOps,
            dataRoomWorkflowId = dataRoomWorkflowId,
            actor = actor
          )
          currentAdmins <- validateOps.roleOps.getCurrentAdmins(dataRoomWorkflowId)
          permissionMap = FileFolderPermissionMap()
            .addAllUserPermissions(currentAdmins.map(_ -> FileFolderPermission.Write))
            .addTeamPermissions(teamId -> FileFolderPermission.Read)
        } yield permissionMap
      }
      isExisting <- fileService.existFolder(actor)(folderId)
      _ <- ZIOUtils.unless(isExisting) {
        fileService.createSystemFolderForChannel(
          channel = homePageId,
          folderName = "Home Page",
          creator = actor,
          permissionOpts = permissionMap
        )
      }
      fileIds <- ZIO.foreach {
        for {
          file <- input.files
          fileType <- file.metadata
            .get(FileMetadataKey.dataRoomHomePageFileType.value)
            .filterNot(Set(DataRoomHomePageFileType.DocumentThumbnailImage.value))
          dataRoomHomePageFileType <- DataRoomHomePageFileType.values.find(_.value == fileType)
        } yield dataRoomHomePageFileType -> file
      } { case (fileType, fileData) =>
        fileService
          .uploadFile(
            parentFolderId = folderId,
            fileName = fileData.filePath,
            content = FileContentOrigin.FromStorageId(fileData.storageId, fileData.bucket),
            uploader = actor,
            uploaderIp = input.httpRequestContext.getClientIP
          )
          .map(fileType -> _)
      }
      thumbnailFiles <- uploadDocumentThumbnails(folderId, input.files, actor)
      _ <- fileUploadService.completeDirectUpload(input.batchUploadId, actor)
    } yield UploadDataRoomHomePageFilesResponse(fileIds.toMap, thumbnailFiles.toMap)
  }

  private[dataroom] def changeHomePageFolderPermissions(
    actor: UserId,
    dataRoomWorkflowId: DataRoomWorkflowId,
    userRoleMap: Map[UserId, DataRoomRole],
    mode: PermissionModMode = PermissionModMode.Validate()
  ): Task[Unit] = {
    val folderId = FolderId.channelSystemFolderId(DataRoomHomePageId(dataRoomWorkflowId))
    for {
      isExisting <- fileService.existFolder(actor)(folderId)
      _ <- ZIOUtils.when(isExisting) {
        val (newAdmins, revokedAdmins) = userRoleMap.partition(_._2.asMessage.sealedValue.isAdmin)
        fileService.modifyFolderPermissions(
          actor = actor,
          folderId = folderId,
          changes = PermissionChanges(
            permissionMap = FileFolderPermissionMap(newAdmins.view.mapValues(_ => FileFolderPermission.Write).toMap),
            revokedUsers = revokedAdmins.keySet
          ),
          mode = mode
        )
      }
    } yield ()
  }

  val uploadDataRoomHomePageFilesHandler: FileUploadHandler = {
    FileUploadHandler[UploadDataRoomHomePageFilesParams, UploadDataRoomHomePageFilesResponse](
      createUploadHomePageFiles,
      completeUploadHomePageFiles
    )
  }

  private def getResourceInitialization(
    fileType: DataRoomHomePageFileType,
    fileName: String,
    extension: String = "jpg",
    contentType: MediaType = MediaType.ImageJpeg
  ) = {
    FilenameUtils.getFilename(fileType.value, Some(extension)) -> {
      initializeS3Resource(
        adminUserIdTask = executiveAdmin.userId,
        resource = s"dataroom/homepage/$fileName",
        contentType = contentType
      )
    }
  }

  private lazy val defaultFileMap = Map(
    getResourceInitialization(DataRoomHomePageFileType.HeaderBackgroundImage, "bg-dr-banner-default.jpg"),
    getResourceInitialization(DataRoomHomePageFileType.IntroMediaFile, "bg-dr-intro-default.jpg")
  )

  private lazy val defaultFileList = defaultFileMap.toSeq.map { case (fileType, fileInitResult) =>
    DirectFileUploadData(
      FileUploadId.unsafeRandomId(fileType),
      fileType,
      storageId = fileInitResult.storageId,
      bucket = fileDownloadService.s3Service.s3Config.bucket,
      metadata = Map(
        FileMetadataKey.dataRoomHomePageFileType.value -> fileType
      )
    )
  }

  private def initializeS3Resource(
    adminUserIdTask: Task[UserId],
    resource: String,
    contentType: MediaType,
    storageIdOpt: Option[DocumentStorageId] = None
  ): DataRoomHomePageService.ResourceInitialization = {
    val storageId = storageIdOpt.getOrElse(DocumentStorageId(resource))
    val task = for {
      _ <- ZIO.logInfo(s"Initializing S3 resource $resource")
      isExisted <- fileDownloadService.s3Service.checkExisted(
        storageId = storageId,
        bucket = fileService.gondorConfig.backendConfig.aws.S3.bucket
      )
      adminUserId <- adminUserIdTask
      _ <- ZIOUtils.unless(isExisted) {
        fileDownloadService.s3Service.uploadStreamToS3(
          storageId = storageId,
          verifiedUser = adminUserId,
          content = ZStreamIOUtils.fromResource(s"/$resource"),
          contentType = contentType.toString(),
          bucket = fileService.gondorConfig.backendConfig.aws.S3.bucket
        )
      }
    } yield ()
    DataRoomHomePageService.ResourceInitialization(storageId, task)
  }

  def initializeResources: Task[Unit] = {
    ZIO.collectAll(defaultFileMap.map(_._2.initTask)).unit
  }

  def getCtaEmailFromSection(
    homepageState: DataRoomHomePageState,
    sectionId: DataRoomHomeSectionId
  ): Seq[EmailAddress] = {
    if (homepageState.published) {
      homepageState.sections
        .flatMap {
          case footer: DataRoomHomeFooterSection =>
            footer.cta.flatMap(_.emailAddressOpt).filter(_ => footer.enabled && footer.sectionId == sectionId)
          case header: DataRoomHomeHeaderSection =>
            header.cta.flatMap(_.emailAddressOpt).filter(_ => header.enabled && header.sectionId == sectionId)
          case _ => Option.empty[EmailAddress]
        }
    } else {
      Seq()
    }
  }

  def sendMessage(
    params: SendDataRoomHomePageMessageParams,
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomEmptyResponse] = {
    val actor = ctx.actor.userId
    for {
      _ <- ZIOUtils.validate(params.subject.trim.nonEmpty && params.message.trim.nonEmpty) {
        new RuntimeException("Either home page cta subject or message is empty")
      }
      homepageState <- getDataRoomHomePage(GetDataRoomHomePageParams(params.dataRoomWorkflowId), actor).map(_.state)
      dataRoomName <- FDBRecordDatabase
        .transact(DataRoomStateStoreOperations.Production)(
          _.getState(params.dataRoomWorkflowId)
        )
        .map(_.name)
      ctaEmail = getCtaEmailFromSection(homepageState, params.sectionId)
      _ <- ZIOUtils.validate(ctaEmail.nonEmpty) {
        new RuntimeException("Cannot get home page cta email address")
      }
      _ <- ZIO.foreach(ctaEmail) { receiver =>
        for {
          _ <- dataRoomEmailSenderService.sendEmail(
            params.dataRoomWorkflowId,
            DataRoomHomePageMessageSingleEmailGenerate(
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              sender = actor,
              receiver = receiver,
              dataRoomName = dataRoomName,
              subject = params.subject,
              message = params.message
            )
          )
          _ <- ZIOUtils.when(params.sendCopy) {
            for {
              actorEmail <- userProfileService.getEmailAddress(actor)
              _ <- dataRoomEmailSenderService.sendEmail(
                params.dataRoomWorkflowId,
                DataRoomHomePageMessageSingleEmailGenerate(
                  dataRoomWorkflowId = params.dataRoomWorkflowId,
                  sender = actor,
                  receiver = actorEmail,
                  dataRoomName = dataRoomName,
                  subject = params.subject,
                  message = params.message,
                  isCopy = true
                )
              )
            } yield ()
          }
        } yield ()

      }
    } yield DataRoomEmptyResponse()
  }

}

object DataRoomHomePageService {

  final case class ResourceInitialization(
    storageId: DocumentStorageId,
    initTask: Task[Unit]
  )

}
