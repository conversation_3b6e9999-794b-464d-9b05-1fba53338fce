// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.integration.service

import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.environment.EnvironmentCheck
import anduin.dataroom.flow.DataRoomValidateOperations.PlanCheck
import anduin.dataroom.group.{CreateDataRoomGroupParams, DataRoomGroupService}
import anduin.dataroom.integration.service.DataRoomAdminPortalService.ChangeExtraSeatsSingleDataRoomParams
import anduin.dataroom.participant.DataRoomParticipantService
import anduin.dataroom.role.Admin
import anduin.dataroom.service.DataRoomService
import anduin.dataroom.validator.DataRoomValidator
import anduin.id.dataroom.DataRoomGroupId
import anduin.id.integration.IntegrationId
import anduin.integration.model.{IntegrationModel, IntegrationPermission}
import anduin.integration.service.IntegrationExternalService.IntegratedApps.DataRoomInfo
import anduin.integration.service.IntegrationService
import anduin.integration.service.linkage.IntegrationGateProtocol
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.portaluser.PortalAdmin
import anduin.protobuf.UserTypeMessage
import anduin.service.{AuthenticatedRequestContext, ServiceActor}
import anduin.stargazer.service.dataroom.{
  AcceptInvitationToDataRoomParams,
  DataRoomGroupPermissionChanges,
  InviteUsersToDataRoomParams,
  RemoveUsersFromDataRoomParams
}
import anduin.stargazer.service.dataroom.validation.DataRoomRoleCheckParams
import com.anduin.stargazer.endpoints.AssetPermissionChanges

final case class DataRoomIntegrationGateService(
  integrationService: IntegrationService,
  userProfileService: UserProfileService,
  dataRoomAdminPortalService: DataRoomAdminPortalService,
  dataRoomParticipantService: DataRoomParticipantService,
  dataRoomGroupService: DataRoomGroupService,
  dataRoomValidator: DataRoomValidator,
  dataRoomService: DataRoomService,
  portalAdmin: PortalAdmin
) extends IntegrationGateProtocol[DataRoomWorkflowId, DataRoomInfo] {
  private val AnduinAdminBotGroup = "Anduin Bot"

  override def lookupApps(
    integrationId: IntegrationId,
    actor: UserId
  ): Task[Set[DataRoomInfo]] = {
    for {
      serviceAccount <- integrationService
        .getIntegration(
          integrationId,
          actor,
          IntegrationPermission.ViewLinkedApps
        )
        .map(_.serviceAccount)
      dataRooms <- dataRoomService.getAllDataRooms(
        actor = serviceAccount,
        isArchived = false,
        includeInvited = false,
        includeToaNotAccepted = false,
        includeGroup = false,
        planCheck = PlanCheck.NoRequirement,
        environmentCheck = EnvironmentCheck.Bypass
      )
    } yield dataRooms.map { dataRoom =>
      DataRoomInfo(
        id = dataRoom.workflowId,
        name = dataRoom.createdState.name,
        enableWebhook = dataRoom.createdState.enableWebhook
      )
    }.toSet
  }

  override def lookupIntegrations(
    dataRoomId: DataRoomWorkflowId
  ): Task[Set[IntegrationModel]] = {
    for {
      anduinAdmin <- portalAdmin.userId
      // Must optimize to avoid getting all participants
      allParticipantIds <- dataRoomParticipantService
        .getAllParticipants(
          dataRoomId
        )
        .map(_.keySet)
      userTypeMap <- userProfileService.batchGetUserType(
        allParticipantIds
      )
      serviceAccounts = userTypeMap.collect { case (userId, UserTypeMessage.ServiceAccount) =>
        userId
      }.toList
      // bypass Integration permission check
      integrations <- integrationService
        .getIntegrationsByServiceAccount(
          serviceAccounts,
          anduinAdmin,
          IntegrationPermission.ViewBasicInfo
        )
        .map(_.values)
    } yield integrations.toSet
  }

  override def addIntegrationAsSuperAdmin(
    integrationId: IntegrationId,
    dataRoomId: DataRoomWorkflowId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Add integration $integrationId to data room $dataRoomId as super admin")
      _ <- dataRoomValidator.checkJoinedAdmin.validate(
        new DataRoomRoleCheckParams {
          override def dataRoomWorkflowId: DataRoomWorkflowId = dataRoomId
        },
        userId = actor
      )
      serviceAccount <- integrationService
        .getIntegration(
          integrationId,
          actor,
          IntegrationPermission.ViewBasicInfo
        )
        .map(_.serviceAccount)

      anduinBotAdminGroup <- getOrCreateAnduinBotGroup(dataRoomId, actor)

      serviceAccountInfo <- userProfileService.getUserInfo(serviceAccount)
      serviceAccountEmail = serviceAccountInfo.emailAddressStr
      serviceAccountCtx = AuthenticatedRequestContext.defaultInstance.copy(
        actor = ServiceActor.defaultServiceActor.copy(
          userId = serviceAccount,
          userInfo = serviceAccountInfo
        )
      )

      actorInfo <- userProfileService.getUserInfo(actor)
      serviceActor = ServiceActor.defaultServiceActor.copy(
        userId = actor,
        userInfo = actorInfo
      )
      actorCtx = AuthenticatedRequestContext.defaultInstance.copy(
        actor = serviceActor
      )

      _ <- dataRoomAdminPortalService.changeExtraSeatsSingleDataRoomUnsafe(
        ChangeExtraSeatsSingleDataRoomParams(
          dataRoomWorkflowId = dataRoomId,
          updateExtraSeatsFn = _ + 1
        ),
        actorCtx
      )

      _ <- dataRoomParticipantService.inviteUsers(
        InviteUsersToDataRoomParams(
          dataRoomId,
          groupPermissionMap = Map(
            serviceAccountEmail -> DataRoomGroupPermissionChanges(
              groupIds = Set(anduinBotAdminGroup),
              canInvite = true
            )
          ),
          isToaRequired = false,
          subject = "Subject",
          message = "Message",
          buttonLabel = "Button Label",
          isApproved = true,
          shouldSendEmail = false
        ),
        actor = serviceActor
      )

      _ <- dataRoomParticipantService.acceptInvitation(
        AcceptInvitationToDataRoomParams(
          dataRoomWorkflowId = dataRoomId,
          toaFileIdOpt = None
        ),
        ctx = serviceAccountCtx,
        shouldSendEmail = false
      )

    } yield ()
  }

  override def removeIntegration(
    integrationId: IntegrationId,
    dataRoomId: DataRoomWorkflowId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Remove integration $integrationId from data room $dataRoomId")
      anduinAdmin <- portalAdmin.userId
      serviceAccount <- integrationService
        .getIntegration(
          integrationId,
          anduinAdmin,
          IntegrationPermission.ViewBasicInfo
        )
        .map(_.serviceAccount)
      _ <- dataRoomParticipantService.removeUsers(
        params = RemoveUsersFromDataRoomParams(
          dataRoomWorkflowId = dataRoomId,
          userIds = Set(serviceAccount),
          doNotNotifyByEmail = true
        ),
        actor = actor,
        httpContext = None
      )
    } yield ()
  }

  private def getOrCreateAnduinBotGroup(dataRoomId: DataRoomWorkflowId, actor: UserId): Task[DataRoomGroupId] = {
    for {
      allGroups <- dataRoomGroupService.getAllGroups(dataRoomId, actor)
      anduinBotGroupOpt = allGroups
        .find { group =>
          group.name.equalsIgnoreCase(AnduinAdminBotGroup)
        }
        .flatMap(_.id)
      anduinBotGroup <- anduinBotGroupOpt.fold(
        dataRoomGroupService
          .createGroup(
            CreateDataRoomGroupParams(
              dataRoomWorkflowId = dataRoomId,
              name = AnduinAdminBotGroup,
              role = Admin(),
              assetPermissions = AssetPermissionChanges.allFoldersWithRootChannel(dataRoomId)
            ),
            actor,
            ctx = None
          )
          .map(_.groupId)
      )(ZIO.succeed)
    } yield anduinBotGroup
  }

}
