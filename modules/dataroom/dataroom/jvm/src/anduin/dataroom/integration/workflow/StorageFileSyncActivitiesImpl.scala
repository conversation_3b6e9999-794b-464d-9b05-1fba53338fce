package anduin.dataroom.integration.workflow

import zio.{Task, ZIO}

import anduin.dataroom.integration.CloudProviderType
import anduin.dataroom.workflow.{StorageFileUploadParamMessage, StorageFileUploadResponseMessage, UploadFileItemMessage}
import anduin.serverless.functions.RcloneServerless
import anduin.service.GeneralServiceException
import anduin.storageservice.box.BoxService
import anduin.storageservice.common.BaseStorageService
import anduin.storageservice.common.BaseStorageService.UploadFileItem
import anduin.storageservice.drive.DriveService
import anduin.storageservice.dropbox.DropboxService
import anduin.storageservice.sftp.SftpServerService
import anduin.storageservice.sharepoint.SharePointService
import anduin.workflow.TemporalWorkflowService

final case class StorageFileSyncActivitiesImpl(
  rcloneServerless: RcloneServerless,
  boxService: BoxService,
  driveService: DriveService,
  sharePointService: SharePointService,
  sftpServerService: SftpServerService,
  dropboxService: DropboxService
)(
  using val temporalWorkflowService: TemporalWorkflowService
) extends StorageFileSyncActivities {

  private def getStorageService(storageType: CloudProviderType): Task[BaseStorageService] = {
    storageType match {
      case CloudProviderType.BOX         => ZIO.succeed(boxService)
      case CloudProviderType.DRIVE       => ZIO.succeed(driveService)
      case CloudProviderType.SHARE_POINT => ZIO.succeed(sharePointService)
      case CloudProviderType.DROPBOX     => ZIO.succeed(dropboxService)
      case CloudProviderType.SFTP_SERVER => ZIO.succeed(sftpServerService)
      case CloudProviderType.Unrecognized(_) =>
        ZIO.fail(GeneralServiceException("Cannot get storage service for unrecognized storage type"))
    }
  }

  override def startSyncFiles(params: StorageFileUploadParamMessage): StorageFileUploadResponseMessage = {
    val task = for {
      storageService <- getStorageService(params.cloudProviderType)
      _ <- storageService.sendFilesToFolder(
        rootFolderId = params.rootFolderId,
        files = params.files.map(convertToFileUploadItem),
        configOpt = params.configOpt
      )
    } yield StorageFileUploadResponseMessage()
    TemporalWorkflowService.runActivity(task)
  }

  private def convertToFileUploadItem(protoMessage: UploadFileItemMessage): UploadFileItem =
    UploadFileItem(
      protoMessage.destPath,
      protoMessage.name,
      protoMessage.storageId
    )

}
