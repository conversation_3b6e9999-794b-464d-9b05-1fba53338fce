// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.integration.service

import java.time.{Instant, LocalDate}

import io.circe.Codec
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.dataroom.billing.DataRoomBillingOperations
import anduin.dataroom.email.*
import anduin.dataroom.email.DataRoomEmailConfigOptions.*
import anduin.dataroom.email.generate.*
import anduin.dataroom.event.DataRoomEmailConfigs
import anduin.dataroom.flow.*
import anduin.dataroom.flow.DataRoomValidateOperations.SeatCheck
import anduin.dataroom.group.DataRoomGroupStateStoreOperations
import anduin.dataroom.homepage.DataRoomHomePageService
import anduin.dataroom.integration.fdb.DataRoomIntegrationStoreOperations
import anduin.dataroom.integration.service.DataRoomAdminPortalService.DataRoomInsights.DataRoomEntityWithChangeLogs
import anduin.dataroom.integration.service.DataRoomAdminPortalService.{
  ChangeExtraSeatsSingleDataRoomParams,
  DataRoomInsights,
  GetInternalDataRoomInsightsResponse
}
import anduin.dataroom.integration.validation.DataRoomPortalValidator
import anduin.dataroom.participant.{
  DataRoomParticipantOperations,
  DataRoomParticipantRoleOperations,
  DataRoomParticipantService
}
import anduin.dataroom.role.{Admin, DataRoomRole, DataRoomRoleUtils}
import anduin.dataroom.service.*
import anduin.dataroom.service.tracking.DataRoomTrackingService
import anduin.dataroom.whitelabel.DataRoomWhiteLabelService
import anduin.dms.DmsFeature.DataRoom
import anduin.dms.DmsTreeTraversalOperations
import anduin.dms.channel.DmsChannelModelStoreOperations
import anduin.dms.file.FileStateVersionOperations
import anduin.dms.service.{DmsSearchService, FileService}
import anduin.documentservice.watermark.WatermarkFileStorageOperations
import anduin.email.smtp.{CustomEmailAddressModel, CustomSmtpServerConfigModel}
import anduin.email.{CustomSmtpServerConfig, CustomSmtpServerConfigParams}
import anduin.encryption.StreamingEncryptionService
import anduin.entity.model.EntityDataRoomEmailConfigs
import anduin.entity.model.EntityModel.EntityTrackingType
import anduin.entity.repository.EntityModelStoreOperations
import anduin.environment.{EnvironmentStoreOperations, InheritEnvironmentMode}
import anduin.fdb.record.model.{RecordIO, RecordTask}
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.greylin.modelti.DataRoomParticipantState
import anduin.greylin.{GreylinDataService, modelti, operation}
import anduin.id.entity.EntityId
import anduin.id.role.portal.PortalSectionId
import anduin.link.LinkGeneratorService
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.user.UserId
import anduin.model.id.FolderId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.orgbilling.model.plan.DataRoomPlan
import anduin.orgbilling.model.plan.DataRoomPlan.{DataRoomBusinessPlan, DataRoomProPlan}
import anduin.portaluser.PortalUserService
import anduin.protobuf.orgbilling.dataroom.{DataRoomBusiness, DataRoomPro}
import anduin.service.entity.{EntityService, EntityServiceUtils}
import anduin.service.{AuthenticatedRequestContext, GeneralServiceException, ServiceActor}
import anduin.stargazer.service.dataroom.*
import anduin.team.TeamService
import anduin.team.TeamServiceParams.*
import anduin.user.UserService
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import com.anduin.stargazer.external.base64.Base64
import com.anduin.stargazer.service.ServiceFeature
import com.anduin.stargazer.service.orgbilling.OrgBillingService
import com.anduin.stargazer.service.orgbilling.storage.{ChangeDataRoomPlanLogOperations, OrgBillingStoreOperations}
import com.anduin.stargazer.service.utils.ZIOUtils

final class DataRoomAdminPortalService(
  portalUserService: PortalUserService,
  dataRoomService: DataRoomService,
  dataRoomParticipantService: DataRoomParticipantService,
  dataRoomSharedFlowService: DataRoomSharedFlowService,
  orgBillingService: OrgBillingService,
  entityService: EntityService,
  dataRoomFileUploadEmailService: DataRoomFileUploadEmailService,
  dataRoomTrackingService: DataRoomTrackingService,
  dataRoomTermsOfAccessService: DataRoomTermsOfAccessService,
  dataRoomHomePageService: DataRoomHomePageService,
  dataRoomWhiteLabelService: DataRoomWhiteLabelService,
  dmsSearchService: DmsSearchService,
  dataRoomPermissionService: DataRoomPermissionService,
  teamService: TeamService,
  dataRoomEmailSenderService: DataRoomEmailSenderService,
  greylinDataService: GreylinDataService,
  dataRoomPortalValidator: DataRoomPortalValidator,
  encryptionService: StreamingEncryptionService,
  serviceFeature: ServiceFeature,
  dataRoomSemanticSearchService: DataRoomSemanticSearchService
)(
  using val linkGeneratorService: LinkGeneratorService,
  userService: UserService, // scalafix:ok
  val userProfileService: UserProfileService,
  val dataRoomEmailService: DataRoomEmailService
) {

  def createDataRoomOnBehalf(
    params: CreateDataRoomOnBehalfParams,
    ctx: AuthenticatedRequestContext
  ): Task[CreateDataRoomResponse] = {
    dataRoomPortalValidator.checkWriteDataRoomPortal.validate(params, ctx.actor.userId) *>
      createDataRoomOnBehalfUnsafe(params, ctx)
  }

  def createDataRoomOnBehalfUnsafe(
    params: CreateDataRoomOnBehalfParams,
    ctx: AuthenticatedRequestContext,
    inheritEnvironmentMode: InheritEnvironmentMode = InheritEnvironmentMode.AnduinDefault
  ): Task[CreateDataRoomResponse] = {
    for {
      creatorUserId <- userProfileService.getUserFromEmailAddress(params.onBehalfOf.address).map(_._1)
      newCtx = ctx.copy(actor = ctx.actor.copy(userId = creatorUserId))
      resp <- dataRoomService.createDataRoom(
        params.createDataRoomParams,
        newCtx,
        checkIfActorInEntity = false,
        planOpt = params.planOpt,
        inheritEnvironmentMode = inheritEnvironmentMode
      )
    } yield resp

  }

  def changeSingleDataRoomPlan(
    params: ChangeSingleDataRoomPlanParams,
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomEmptyResponse] = {
    dataRoomPortalValidator.checkWriteDataRoomPortal(params, ctx.actor.userId) *>
      changeSingleDataRoomPlanUnsafe(params, ctx)
  }

  private def modifyBillingPlanInGreylin(dataRoomWorkflowId: DataRoomWorkflowId, newPackage: Option[DataRoomPlan])
    : Task[Unit] = {
    val expirationDate = newPackage.collect {
      case plan: DataRoomPlan.DataRoomProPlan      => plan.expDate
      case plan: DataRoomPlan.DataRoomBusinessPlan => plan.expDate
    }
    val isInheritedFromOrg = newPackage.isEmpty
    for {
      _ <- greylinDataService.runUnit(
        operation.DataRoomOperations.update(dataRoomWorkflowId)(dataRoom =>
          dataRoom.copy(
            expirationDate = expirationDate,
            isExpirationDateInheritedFromOrg = isInheritedFromOrg
          )
        )
      )
    } yield ()
  }

  def changeSingleDataRoomPlanUnsafe(
    params: ChangeSingleDataRoomPlanParams,
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomEmptyResponse] = {
    for {
      _ <- FDBRecordDatabase.transact(DataRoomFlowOperations.Production) { flowOps =>
        for {
          createdState <- flowOps.stateOps.getState(params.dataRoomWorkflowId)
          newPackage <- RecordIO.succeed(params.packageOpt.map(OrgBillingStoreOperations.toDataRoomPackageProto))
          _ <- RecordIO.when(createdState.packageOpt.map(_.plan) != newPackage) {
            flowOps.changeSingleDataRoomBillingPlan(
              params.dataRoomWorkflowId,
              ctx.actor.userId,
              newPackage,
              ctx.getClientIP
            )
          }
        } yield ()
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
      _ <- modifyBillingPlanInGreylin(params.dataRoomWorkflowId, params.packageOpt)
    } yield DataRoomEmptyResponse()
  }

  def changeExtraSeatsSingleDataRoomUnsafe(
    params: ChangeExtraSeatsSingleDataRoomParams,
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomEmptyResponse] = {
    for {
      newPackage <- FDBRecordDatabase.transact(
        FDBOperations[(DataRoomBillingOperations, DataRoomFlowOperations)].Production
      ) { case (billingOps, flowOps) =>
        for {
          createdState <- flowOps.stateOps.getState(params.dataRoomWorkflowId)
          oldPackage <- billingOps.getPlan(createdState)
          newPackage = oldPackage.updateExtraSeats(params.updateExtraSeatsFn)
          _ <- RecordIO.when(oldPackage != newPackage) {
            flowOps.changeSingleDataRoomBillingPlan(
              params.dataRoomWorkflowId,
              ctx.actor.userId,
              Some(OrgBillingStoreOperations.toDataRoomPackageProto(newPackage)),
              ctx.getClientIP
            )
          }
        } yield newPackage
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
      _ <- modifyBillingPlanInGreylin(params.dataRoomWorkflowId, Some(newPackage))
    } yield DataRoomEmptyResponse()
  }

  def getDataRoomInfo(
    params: GetDataRoomInfoParams,
    ctx: AuthenticatedRequestContext
  ): Task[GetDataRoomInfoResponse] = {
    dataRoomPortalValidator.checkReadDataRoomPortal(params, ctx.actor.userId) *>
      getDataRoomInfoUnsafe(params, ctx)
  }

  def getDataRoomInfoUnsafe(
    params: GetDataRoomInfoParams,
    ctx: AuthenticatedRequestContext
  ): Task[GetDataRoomInfoResponse] = {
    for {
      (createdState, entityBillingModel, entityModel) <- FDBRecordDatabase.transact(
        FDBOperations[
          ((DataRoomStateStoreOperations, OrgBillingStoreOperations), EntityModelStoreOperations)
        ].Production
      ) { case ((stateOps, billingOps), entityOps) =>
        for {
          createdState <- stateOps.getState(params.dataRoomWorkflowId)
          entityBillingModel <- billingOps.getOrgBillingModelRecordIO(createdState.creatorEntityId)
          entityModel <- entityOps.getEntityModel(createdState.creatorEntityId)
        } yield (createdState, entityBillingModel, entityModel)
      }
    } yield GetDataRoomInfoResponse(
      name = createdState.name,
      creatorEntity = createdState.creatorEntityId,
      creatorEntityName = entityModel.name,
      creatorEntityPlan = entityBillingModel.dataRoomPlan,
      planOpt = createdState.packageOpt.map(pkg => OrgBillingStoreOperations.toDataRoomPlan(pkg.plan)),
      enableWebhook = createdState.enableWebhook,
      isArchived = createdState.isArchived
    )
  }

  def getAllDataRoomEntitiesUnsafe(
    actor: UserId
  ): Task[GetAllDataRoomEntitiesResponse] = {
    for {
      allEntities <- entityService.getEntityModels
      businessEntities = allEntities.filter(entry => EntityServiceUtils.isBusinessEntity(entry._2.entityType))
      allEntitiesBilling <- orgBillingService.getAllEntityBillingModels(businessEntities.keySet)
    } yield {
      GetAllDataRoomEntitiesResponse(
        businessEntities.flatMap { case (entityId, entityModel) =>
          val billingOpt = allEntitiesBilling.get(entityId)
          billingOpt.map { billing =>
            entityId -> GetAllDataRoomEntitiesResponse.DataRoomEntity(
              entityModel.name,
              billing.dataRoomPlan,
              dataRoomPermissionService.isAnduinInternalDataroomEntity(entityId)
            )
          }
        }
      )
    }
  }

  // Sending file upload digest email
  def sendFileUploadDigestEmailUnsafe(
    params: SendDataRoomFileUploadDigestEmailParams,
    actor: UserId
  ): Task[DataRoomEmptyResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"[DataRoomService] $actor is sending data room file upload digest email on ${params.dataRoomWorkflowId}"
      )
      _ <- dataRoomFileUploadEmailService.sendDataRoomDigestEmails(params.dataRoomWorkflowId)
    } yield DataRoomEmptyResponse()
  }

  private def validateUpdateDataRoomEmailConfigsData(
    data: UpdateDataRoomEmailConfigsData
  ) = {
    ZIOUtils.validate(
      (!data.senderNameEnabled || data.senderName.nonEmpty) &&
        (!data.senderAddressNamePartEnabled || data.senderAddressNamePart.nonEmpty) &&
        data.replyToSenderTemplateTypes.subsetOf(DataRoomEmailConstants.replyToSenderTemplateTypes.toSet) &&
        data.replyToEmailsTemplateTypes.subsetOf(DataRoomEmailConstants.customReplyTemplateTypes.toSet) &&
        data.customCcTemplateTypes.subsetOf(DataRoomEmailConstants.customCcTemplateTypes.toSet) &&
        data.customBccTemplateTypes.subsetOf(DataRoomEmailConstants.customBccTemplateTypes.toSet)
    ) {
      GeneralServiceException("Invalid update data room email configs data")
    }
  }

  // Email configs
  def updateDataRoomEmailConfigsUnsafe(
    params: UpdateSingleDataRoomEmailConfigsParams,
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomEmptyResponse] = {
    val updateData = params.updateData
    for {
      _ <- ZIOUtils.when(!params.inheritFromEntity)(
        validateUpdateDataRoomEmailConfigsData(updateData)
      )
      smtpConfig <- getSmtpConfigModel(updateData)
      _ <- FDBRecordDatabase.transact(DataRoomFlowOperations.Production) { flowOps =>
        for {
          createdStateOpt <- flowOps.stateOps.getStateOpt(params.dataRoomWorkflowId)
          emailConfigs = createdStateOpt.flatMap(_.dataRoomEmailConfigs)
          newEmailConfigs = Option.unless(params.inheritFromEntity) {
            DataRoomEmailConfigs(
              senderNameEnabled = updateData.senderNameEnabled,
              senderName = updateData.senderName,
              customEmailReplyEnabled = updateData.customEmailReplyEnabled,
              replyToEmails = updateData.replyToEmails,
              senderAddressNamePartEnabled = updateData.senderAddressNamePartEnabled,
              senderAddressNamePart = updateData.senderAddressNamePart,
              customEmailReplyTemplateTypes = updateData.replyToEmailsTemplateTypes,
              replyToSenderEnabled = updateData.replyToSenderEnabled,
              replyToSenderTemplateTypes = updateData.replyToSenderTemplateTypes,
              customEmailCcEnabled = updateData.customCcEmailsEnabled,
              ccEmails = updateData.customCcEmails,
              customCcTemplateTypes = updateData.customCcTemplateTypes,
              customEmailBccEnabled = updateData.customBccEmailsEnabled,
              bccEmails = updateData.customBccEmails,
              customBccTemplateTypes = updateData.customBccTemplateTypes,
              enableCustomSmtp = updateData.customSmtpConfigs.enabled,
              smtpConfig = smtpConfig
            )
          }
          _ <- RecordIO.when(emailConfigs != newEmailConfigs && createdStateOpt.isDefined) {
            flowOps.updateDataRoomEmailConfigs(
              params.dataRoomWorkflowId,
              ctx.actor.userId,
              newEmailConfigs,
              ctx.getClientIP
            )
          }
        } yield ()
      }
    } yield DataRoomEmptyResponse()
  }

  def updateEntityDataRoomEmailConfigsUnsafe(
    params: UpdateEntityDataRoomEmailConfigsParams,
    actor: UserId
  ): Task[Unit] = {
    val updateData = params.updateData
    for {
      _ <- validateUpdateDataRoomEmailConfigsData(updateData)
      _ <- ZIO.logInfo(s"User $actor updates entity data room email configs for entity ${params.entityId}")
      smtpConfig <- getSmtpConfigModel(updateData)
      _ <- EntityServiceUtils.execute(
        _.updateEntityRestricted(
          params.entityId,
          _.withDataRoomEmailConfigs(
            EntityDataRoomEmailConfigs(
              updateData.senderNameEnabled,
              updateData.senderName,
              updateData.senderAddressNamePartEnabled,
              updateData.senderAddressNamePart,
              updateData.customEmailReplyEnabled,
              updateData.replyToEmails,
              updateData.replyToEmailsTemplateTypes,
              updateData.replyToSenderEnabled,
              updateData.replyToSenderTemplateTypes,
              updateData.customCcEmailsEnabled,
              updateData.customCcEmails,
              updateData.customCcTemplateTypes,
              updateData.customBccEmailsEnabled,
              updateData.customBccEmails,
              updateData.customBccTemplateTypes,
              updateData.customSmtpConfigs.enabled,
              smtpConfig = smtpConfig,
              updateData.allowDisablingInvitationEmail
            )
          )
        )
      )
    } yield ()
  }

  private def getSmtpConfigModel(
    updateData: UpdateDataRoomEmailConfigsData
  ): Task[Option[CustomSmtpServerConfigModel]] = {
    ZIOUtils.whenOption(updateData.customSmtpConfigs.enabled) {
      val smtpConfig = updateData.customSmtpConfigs.config
      for {
        encryptedPassword <- encryptionService.encrypt(smtpConfig.rawPassword.getBytes)
      } yield CustomSmtpServerConfigModel(
        from = Some(
          CustomEmailAddressModel(
            name = smtpConfig.from.name,
            address = smtpConfig.from.address
          )
        ),
        host = smtpConfig.host,
        port = smtpConfig.port,
        userName = smtpConfig.userName,
        encryptedPassword = Base64.toBase64(encryptedPassword),
        tls = smtpConfig.tls
      )
    }
  }

  def getEntityDataRoomsUnsafe(
    params: GetEntityDataRoomsParams,
    actor: UserId
  ): Task[GetEntityDataRoomsResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor get all data rooms under entity ${params.entityId}")

      // 1. Get entity members
      entityModel <- entityService.queryEntityId(params.entityId)
      entityJoinedUsers = entityModel._2.membersInfo.keySet

      // 2. Get all data rooms under entity
      dataRoomStates <- FDBRecordDatabase.transact(FDBOperations[DataRoomStateStoreOperations].Production)(
        _.getAllDataRoomStatesWithEntityId(params.entityId)
      )
      dataRoomData <- ZIOUtils
        .wanderUnorderedIgnoreFailed(dataRoomStates) { state =>
          FDBRecordDatabase.transact(
            FDBOperations[
              (
                DataRoomEventStoreOperations,
                DataRoomIntegrationStoreOperations,
                DataRoomValidateOperations,
                EnvironmentStoreOperations
              )
            ].Production
          ) { case (eventOps, integrationOps, validateOps, environmentOps) =>
            val dataRoomId = state.dataRoomWorkflowId
            for {
              firstEvent <- eventOps.getFirstEvent(dataRoomId)
              storageConfigs <- integrationOps.getDataRoomStorageConfigs(dataRoomId)
              roleMap <- validateOps.roleOps.getAllParticipantRoleMap(dataRoomId)
              participantIds = roleMap.keySet
              adminIds = roleMap
                .filter(_._2 match {
                  case _: Admin => true
                  case _        => false
                })
                .keySet

              // get joined and pending admins
              activeAdmins <- RecordIO
                .traverse(adminIds.toSeq)(adminId =>
                  for {
                    (_, hasJoined) <- validateOps.getMemberStateCheckResult(
                      adminId,
                      dataRoomId,
                      DataRoomValidateOperations.MemberStateCheck.RequireJoined
                    )
                  } yield adminId -> (hasJoined == DataRoomValidateOperations.MemberStateCheck.Passed)
                )
                .map(_.filter(_._2).map(_._1).toSet)

              pendingAdmins = adminIds -- activeAdmins

              // get if the data room is accessed by any entity members
              isAccessedByEntityMembers = participantIds.intersect(entityJoinedUsers).nonEmpty && !state.isArchived

              environmentName <- RecordIO.traverse(state.environmentIdOpt) { environmentId =>
                environmentOps.getEnvironment(environmentId).map(_.name)
              }
            } yield (
              state,
              firstEvent.timestamp,
              storageConfigs,
              activeAdmins,
              pendingAdmins,
              isAccessedByEntityMembers,
              environmentName
            )
          }
        }
    } yield {
      // 3. Get data room info
      val res = dataRoomData.map {
        case (
              dataRoom,
              createdAt,
              storageConfigs,
              activeAdmins,
              pendingAdmins,
              isAccessedByEntityMembers,
              environmentName
            ) =>
          val entityEmailConfigs = entityModel._2.dataRoomEmailConfigs
          DataRoomInfo(
            dataRoomWorkflowId = dataRoom.dataRoomWorkflowId,
            name = dataRoom.name,
            createdAt = createdAt,
            dataRoomEmailConfigs = dataRoom.dataRoomEmailConfigs.map(drConfigs =>
              convertToPortalDataRoomEmailConfigs(drConfigs, entityEmailConfigs)
            ),
            entityDataRoomEmailConfigs = entityEmailConfigs.map(
              convertToPortalEntityDataRoomEmailConfigs
            ),
            showWhiteLabel = dataRoom.showWhiteLabel,
            showSearch = dataRoom.showSearch,
            integrationConfig = PortalDataRoomIntegrationConfig(
              internalIntegratedIds = storageConfigs.flatMap(_.internalIntegratedIds).toSet,
              cloudIntegratedTypes = storageConfigs.flatMap(_.configs.filter(_.isEnabled).map(_.storageType)).toSet
            ),
            activeAdmins = activeAdmins,
            pendingAdmins = pendingAdmins,
            isArchived = dataRoom.isArchived,
            isAccessedByEntityMembers = isAccessedByEntityMembers,
            enableWebhook = dataRoom.enableWebhook,
            environmentIdOpt = dataRoom.environmentIdOpt,
            environmentNameOpt = environmentName
          )
      }
      GetEntityDataRoomsResponse(res)
    }
  }

  def getEntityDataRoomEmailConfigsUnsafe(
    params: GetEntityDataRoomEmailConfigsParams,
    actor: UserId
  ): Task[GetEntityDataRoomEmailConfigsResponse] = {
    for {
      entityModel <- entityService.queryEntityId(params.entityId)
    } yield GetEntityDataRoomEmailConfigsResponse(
      entityModel._2.dataRoomEmailConfigs.map(
        convertToPortalEntityDataRoomEmailConfigs
      )
    )
  }

  def convertToPortalDataRoomEmailConfigs(
    dataRoomConfigs: DataRoomEmailConfigs,
    entityConfigsOpt: Option[EntityDataRoomEmailConfigs]
  ): PortalDataRoomEmailConfigs = PortalDataRoomEmailConfigs(
    SenderName(dataRoomConfigs.senderNameEnabled, dataRoomConfigs.senderName),
    SenderAddressName(dataRoomConfigs.senderAddressNamePartEnabled, dataRoomConfigs.senderAddressNamePart),
    ReplyToSender(dataRoomConfigs.replyToSenderEnabled, dataRoomConfigs.replyToSenderTemplateTypes),
    ReplyToEmails(
      dataRoomConfigs.customEmailReplyEnabled,
      dataRoomConfigs.replyToEmails.map(_.address),
      dataRoomConfigs.customEmailReplyTemplateTypes
    ),
    CcEmails(
      dataRoomConfigs.customEmailCcEnabled,
      dataRoomConfigs.ccEmails.map(_.address),
      dataRoomConfigs.customCcTemplateTypes
    ),
    BccEmails(
      dataRoomConfigs.customEmailBccEnabled,
      dataRoomConfigs.bccEmails.map(_.address),
      dataRoomConfigs.customBccTemplateTypes
    ),
    CustomSmtpServerConfigParams(
      enabled = dataRoomConfigs.enableCustomSmtp,
      edited = false,
      config = dataRoomConfigs.smtpConfig
        .map(CustomSmtpServerConfig.from)
        .getOrElse(CustomSmtpServerConfig.Default)
    ),
    InvitationEmail(
      isEnabled = !dataRoomConfigs.invitationEmailDisabled,
      allowDisabling = entityConfigsOpt.exists(_.allowDisablingInvitationEmail)
    )
  )

  def convertToPortalEntityDataRoomEmailConfigs(
    configs: EntityDataRoomEmailConfigs
  ): PortalDataRoomEmailConfigs = PortalDataRoomEmailConfigs(
    SenderName(configs.senderNameEnabled, configs.senderName),
    SenderAddressName(configs.senderAddressNamePartEnabled, configs.senderAddressNamePart),
    ReplyToSender(configs.replyToSenderEnabled, configs.replyToSenderTemplateTypes),
    ReplyToEmails(
      configs.customEmailReplyEnabled,
      configs.replyToEmails.map(_.address),
      configs.customEmailReplyTemplateTypes
    ),
    CcEmails(
      configs.customEmailCcEnabled,
      configs.ccEmails.map(_.address),
      configs.customCcTemplateTypes
    ),
    BccEmails(
      configs.customEmailBccEnabled,
      configs.bccEmails.map(_.address),
      configs.customBccTemplateTypes
    ),
    CustomSmtpServerConfigParams(
      enabled = configs.enableCustomSmtp,
      edited = false,
      config = configs.smtpConfig
        .map(CustomSmtpServerConfig.from)
        .getOrElse(CustomSmtpServerConfig.Default)
    ),
    InvitationEmail(
      allowDisabling = configs.allowDisablingInvitationEmail
    )
  )

  def cleanWatermarkFilesUnsafe(
    params: CleanWatermarkFilesParams,
    actor: UserId
  ): Task[DataRoomEmptyResponse] = {
    val rootFolder = FolderId.channelSystemFolderId(params.dataRoomWorkflowId)
    for {
      // Get all file ids in data room
      fileIds <- FDBRecordDatabase.transact(DmsTreeTraversalOperations.Production) {
        _.getAllFoldersAndFiles(
          actor = actor,
          rootFolders = Seq(rootFolder)
        ).map(_._2)
      }
      // Convert file ids to storage ids
      fileAndStorageIds <- FDBRecordDatabase.transact(FileStateVersionOperations.Production) {
        DataRoomServiceUtils.getFileStorageIds(
          fileIds,
          _,
          actor
        )
      }
      _ <- ZIO.logInfo(s"Start cleaning watermark cache for $rootFolder, ${fileAndStorageIds.size} files found")
      // Scan database to get all records and delete
      allRecords <- ZIO
        .foreach(fileAndStorageIds.map(_._2)) { storageId =>
          FDBRecordDatabase.transact(WatermarkFileStorageOperations.Production) { watermarkFileOps =>
            for {
              records <- watermarkFileOps.getDocumentWatermarkRecords(storageId)
              _ <- RecordIO.parTraverseN(2)(records)(watermarkFileOps.deleteWatermarkRecord)
            } yield records
          }
        }
        .map(_.flatten)
      _ <- ZIO.logInfo(
        s"Cleaned watermark cache for $rootFolder, ${allRecords.size} records found, ${allRecords.take(10)}"
      )
    } yield DataRoomEmptyResponse()
  }

  def changeDataRoomSearchSettingUnsafe(
    params: ChangeDataRoomSearchSettingParams,
    ctx: AuthenticatedRequestContext
  ): Task[DataRoomEmptyResponse] = {
    for {
      _ <- FDBRecordDatabase.transact(
        FDBOperations[(DmsChannelModelStoreOperations, DataRoomFlowOperations)].Production
      ) { case (dmsChannelOps, flowOps) =>
        for {
          _ <- dataRoomService.modifyShowSearchSetting(
            flowOps,
            dmsChannelOps,
            params.dataRoomWorkflowId,
            ctx,
            params.showSearch
          )
        } yield ()
      }
      _ <- ZIOUtils.when(params.showSearch) {
        dmsSearchService.startSearchIndexWorkflow(
          channel = params.dataRoomWorkflowId,
          actor = ctx.actor.userId
        )
      }
      _ <- ZIOUtils.when(params.showSearch) {
        dataRoomSemanticSearchService.startUpdateSearchIndexWorkflow(
          dataRoomWorkflowId = params.dataRoomWorkflowId,
          actor = ctx.actor.userId
        )
      }
    } yield DataRoomEmptyResponse()
  }

  def getInternalDataRoomInsights(
    actor: UserId,
    parallelism: Int = 64
  ): Task[GetInternalDataRoomInsightsResponse] = {
    for {
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.DataRoom)
      dataRoomWorkflowIds <- FDBRecordDatabase.transact(DataRoomModelStoreOperations.Production)(ops =>
        ops.getAllDataRooms
      )
      dataRoomInsights <- ZIOUtils
        .foreachParN(parallelism)(dataRoomWorkflowIds)(dataRoomWorkflowId =>
          getSingleDataRoomInsights(dataRoomWorkflowId)
            .map(Some(_))
            .catchAllCause { error =>
              ZIO.logErrorCause(s"Failed to get insight of DR ${dataRoomWorkflowId.idString}: ", error).as(None)
            }
        )
        .map(_.flatten)
      entitiesWithDataRoom = dataRoomInsights.collect {
        case insights: DataRoomInsights if !insights.settings.integrationEnabled =>
          (insights.dataRoomInfo.entityId, insights.dataRoomInfo.entityName, insights.dataRoomInfo.entityTrackingType)
      }
      groupedEntities = entitiesWithDataRoom.groupBy(_._1).toSeq
      allEntitiesBilling <- orgBillingService.getAllEntityBillingModels(groupedEntities.map(_._1).toSet)
      entities <- ZIOUtils.foreachParN(4)(groupedEntities) { case (entityId, occurrences) =>
        val entityName = occurrences.headOption.map(_._2).getOrElse("")
        val entityTrackingType = occurrences.headOption.map(_._3).getOrElse(EntityTrackingType.Internal)
        for {
          logs <- FDBRecordDatabase
            .transact(ChangeDataRoomPlanLogOperations.Production) { ops =>
              ops.getChangeDataRoomLog(entityId)
            }
            .orElseSucceed(List.empty)
        } yield {
          val plan = allEntitiesBilling.get(entityId).map(_.dataRoomPlan) match {
            case Some(_: DataRoomProPlan)             => "Pro"
            case Some(business: DataRoomBusinessPlan) => if (business.isTrial) "Trial" else "Business"
            case _                                    => ""
          }
          val changeLogs = logs.map { log =>
            DataRoomEntityWithChangeLogs.ChangeLog(
              at = log.changeDate.map(_.toEpochMilli).getOrElse(0L),
              plan = log.dataRoomPackage match {
                case _: DataRoomPro             => "Pro"
                case business: DataRoomBusiness => if (business.isTrial) "Trial" else "Business"
                case _                          => ""
              }
            )
          }
          DataRoomEntityWithChangeLogs(
            entityId = entityId,
            entityName = entityName,
            entityTrackingType = entityTrackingType,
            dataRoomCount = occurrences.size,
            plan = plan,
            changeLogs = changeLogs
          )
        }
      }
    } yield GetInternalDataRoomInsightsResponse(
      dataRoomInsights = dataRoomInsights,
      entities = entities
    )
  }

  private def getSingleDataRoomInsights(
    dataRoomWorkflowId: DataRoomWorkflowId
  ): Task[DataRoomInsights] = {
    for {
      (createdState, allGroups, storageConfigs, participants, participantRoles) <- FDBRecordDatabase.transact(
        FDBOperations[
          (
            DataRoomStateStoreOperations,
            DataRoomGroupStateStoreOperations,
            DataRoomIntegrationStoreOperations,
            DataRoomParticipantOperations,
            DataRoomParticipantRoleOperations
          )
        ].Production
      ) { case (stateOps, groupStoreOps, integrationOps, participantOps, participantRoleOps) =>
        for {
          createdState <- stateOps.getState(dataRoomWorkflowId)
          allGroups <- groupStoreOps.getAllGroups(dataRoomWorkflowId)
          storageConfigs <- integrationOps.getDataRoomStorageConfigs(dataRoomWorkflowId)
          participants <- participantOps.getGroupParticipantMap(dataRoomWorkflowId)
          participantRoles <- participantRoleOps.getAllParticipantRoleMap(dataRoomWorkflowId)
        } yield (createdState, allGroups, storageConfigs, participants, participantRoles)
      }
      entityModel <- entityService.queryEntityId(createdState.creatorEntityId).map(_._1)
      (internalUsers, externalUsers) = participantRoles.partition { case (_, role) =>
        DataRoomRoleUtils.isInternal(role)
      }
      internalVisitCount <- dataRoomTrackingService.getGroupDataRoomVisitCount(
        dataRoomWorkflowId,
        internalUsers.keySet.toList
      )
      externalVisitCount <- dataRoomTrackingService.getGroupDataRoomVisitCount(
        dataRoomWorkflowId,
        externalUsers.keySet.toList
      )
    } yield {
      val packageOpt = createdState.packageOpt.map(_.plan)
      val (plan, expDate, extraSeat, totalSeat) = packageOpt match {
        case Some(pro: DataRoomPro) =>
          ("Pro", pro.expDate, pro.extraSeat, DataRoomProPlan.defaultSeats + pro.extraSeat)
        case Some(business: DataRoomBusiness) =>
          val plan = if (business.isTrial) "Trial" else "Business"
          (plan, business.expDate, business.extraSeat, DataRoomBusinessPlan.defaultSeats + business.extraSeat)
        case _ =>
          ("", 0L, 0, 0)
      }
      val notificationConfig = createdState.getDataRoomNotificationConfigs.getNewFileNotificationConfig
      val internalIntegratedIds = storageConfigs.flatMap(_.internalIntegratedIds).toSet
      val cloudIntegratedTypes = storageConfigs.flatMap(_.configs.filter(_.isEnabled).map(_.storageType)).toSet
      val users = participants
        .flatMap { case (groupId, users) =>
          val groupOpt = allGroups.find(_.id.contains(groupId))
          users.map { userId =>
            DataRoomInsights.User(
              userId = userId,
              dataRoomWorkflowId = dataRoomWorkflowId,
              role = groupOpt.map(group => DataRoomRoleUtils.getName(group.role)).getOrElse(""),
              groupName = groupOpt.map(_.name).getOrElse("")
            )
          }
        }
      DataRoomInsights(
        dataRoomInfo = DataRoomInsights.Info(
          dataRoomWorkflowId = dataRoomWorkflowId,
          dataRoomName = createdState.name,
          entityId = createdState.creatorEntityId,
          entityName = entityModel.name,
          entityTrackingType = entityModel.entityTrackingType,
          plan = plan,
          extraSeat = extraSeat,
          totalSeat = totalSeat,
          expirationDate = DateTimeUtils.fromLocalDateToInstant(LocalDate.ofEpochDay(expDate)).toEpochMilli
        ),
        roleInsights = DataRoomInsights.Role(
          adminCount = participantRoles.count { case (_, role) => DataRoomRoleUtils.isAdmin(role) },
          memberCount = participantRoles.count { case (_, role) => DataRoomRoleUtils.isMember(role) },
          contributorCount = participantRoles.count { case (_, role) => DataRoomRoleUtils.isGuest(role) },
          observerCount = participantRoles.count { case (_, role) => DataRoomRoleUtils.isRestricted(role) }
        ),
        visitorInsights = DataRoomInsights.Visitor(
          internalVisitCount = internalVisitCount,
          externalVisitCount = externalVisitCount
        ),
        linkInsights = DataRoomInsights.InvitationLink(
          linkCount = createdState.linkInvitationMap.size
        ),
        groupInsights = DataRoomInsights.Group(
          groupCount = allGroups.size,
          groupNames = allGroups.map(_.name)
        ),
        settings = DataRoomInsights.Settings(
          whiteLabelEnabled = createdState.showWhiteLabel,
          documentSearchEnabled = createdState.showSearch,
          homePageEnabled = createdState.showHomePage,
          termsOfAccessEnabled = createdState.termsOfAccessOptions.exists(_.isEnabled),
          watermarkEnabled = createdState.watermarkMetadata.nonEmpty,
          integrationEnabled = internalIntegratedIds.nonEmpty || cloudIntegratedTypes.nonEmpty,
          notificationMode = notificationConfig.notificationMode.name,
          notificationFrequency = notificationConfig.notificationFrequency.name,
          cloudIntegrations = cloudIntegratedTypes.toSeq.map(_.name)
        ),
        users = users.toSeq
      )
    }
  }

  def batchRemoveUserFromDataRoomUnsafe(
    params: BatchRemoveUserFromDataRoomParams,
    ctx: AuthenticatedRequestContext,
    parallelism: Int = 4
  ): Task[Unit] = {
    for {
      _ <- ZIOUtils.foreachParN(parallelism)(params.removedUserMap.toList) { case (dataRoomWorkflowId, removedUsers) =>
        dataRoomParticipantService.removeUsers(
          RemoveUsersFromDataRoomParams(
            dataRoomWorkflowId,
            removedUsers,
            doNotNotifyByEmail = true
          ),
          actor = ctx.actor.userId,
          httpContext = Some(ctx),
          validatePermission = false
        )
      }
    } yield ()
  }

  private def execute[A](task: (DataRoomFlowOperations, DataRoomValidateOperations) => RecordTask[A]) = {
    FDBRecordDatabase.transact(
      FDBOperations[(DataRoomFlowOperations, DataRoomValidateOperations)].Production
    ) { case (flowOps, validateOps) =>
      task(flowOps, validateOps)
    }
  }

  def addPortalUserToDataRoom(
    params: AddPortalUserToDataRoomParams,
    actor: ServiceActor,
    ctx: Option[AuthenticatedRequestContext] = None
  ): Task[DataRoomEmptyResponse] = {
    dataRoomPortalValidator.checkWriteDataRoomPortal(params, actor.userId) *>
      addPortalUserToDataRoomUnsafe(params, actor, ctx)
  }

  def addPortalUserToDataRoomUnsafe(
    params: AddPortalUserToDataRoomParams,
    actor: ServiceActor,
    ctx: Option[AuthenticatedRequestContext] = None,
    doNotSendEmailToPortalUser: Boolean = false
  ): Task[DataRoomEmptyResponse] = {
    val roleMap: Map[UserId, DataRoomRole] = Map(actor.userId -> Admin())
    val individualChanges: Map[UserId, DataRoomPermissionChanges] = Map(
      actor.userId -> DataRoomPermissionChanges(
        Some(Admin()),
        AssetPermissionChanges.allFoldersWithRootChannel(params.dataRoomWorkflowId)
      )
    )
    for {
      _ <- ZIO.logInfo(s"${actor.userId} is adding themself to data room ${params.dataRoomWorkflowId}")
      adminId <- FDBRecordDatabase.transact(
        FDBOperations[(DataRoomStateStoreOperations, DataRoomValidateOperations)].Production
      ) { case (stateOps, validateOps) =>
        for {
          state <- stateOps.getState(params.dataRoomWorkflowId)
          _ <- RecordIO.validate(!dataRoomPermissionService.isAnduinInternalDataroomEntity(state.creatorEntityId))(
            GeneralServiceException(
              s"${actor.userId} cannot assign themself to Anduin internal dataroom ${params.dataRoomWorkflowId}"
            )
          )
          adminIds <- validateOps.roleOps.getCurrentAdmins(params.dataRoomWorkflowId)

          // Filter joined only
          joinedAdminIds <- RecordIO
            .parTraverseN(10)(adminIds)(adminId =>
              for {
                (_, hasJoined) <- validateOps.getMemberStateCheckResult(
                  adminId,
                  params.dataRoomWorkflowId,
                  DataRoomValidateOperations.MemberStateCheck.RequireJoined
                )
                termsAccepted <- validateOps.checkTermsAcceptedTask(adminId, state)
              } yield adminId -> {
                val isJoinedAdmin = hasJoined == DataRoomValidateOperations.MemberStateCheck.Passed
                isJoinedAdmin && termsAccepted
              }
            )
            .map(_.filter(_._2).map(_._1))
          adminIdOpt = joinedAdminIds.headOption

          adminId <- RecordIO
            .fromOption(adminIdOpt, GeneralServiceException(s"Data room ${params.dataRoomWorkflowId} has no admins"))
        } yield adminId
      }

      (individualTeamId, createdState) <- FDBRecordDatabase.transact(DataRoomValidateOperations.Production) {
        validateOps =>
          val requiredRoles = Set.empty[DataRoomRoleUtils.Check]
          for {
            (_, hasJoinedOrInvited) <- validateOps.getMemberStateCheckResult(
              actor.userId,
              params.dataRoomWorkflowId,
              DataRoomValidateOperations.MemberStateCheck.RequireInvitedOrJoined
            )
            _ <- RecordIO.validate(hasJoinedOrInvited != DataRoomValidateOperations.MemberStateCheck.Passed)(
              GeneralServiceException(
                s"User ${actor.userId} has been invited or joined data room ${params.dataRoomWorkflowId}"
              )
            )

            (individualTeamId, curState) <- validateOps.validateAndGetCurrentState(
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              userId = adminId,
              roleChecks = requiredRoles,
              checkTotalSeats = SeatCheck.Require(
                userType = SeatCheck.UserType.AllUsers,
                modification = Some(SeatCheck.Modification.AddedUsers(Seq(Admin())))
              )
            )
            _ <- dataRoomPermissionService.validatePermissionChanges(
              validateOps = validateOps,
              dataRoomWorkflowId = params.dataRoomWorkflowId,
              createdState = curState,
              currentRoleAndChangeList = individualChanges.values.toList.map(None -> _)
            )
          } yield (individualTeamId, curState)
      }
      _ <- teamService.inviteAndCreateUser(
        InviteAndCreateUserParams(
          inviter = actor.userId,
          inviteeEmail = actor.userInfo.emailAddressStr,
          teamId = individualTeamId,
          skipPermission = true
        )
      )
      _ <- FDBRecordDatabase.transact(
        FDBOperations[DataRoomParticipantOperations].Production
      )(
        _.upsertParticipant(
          dataRoomWorkflowId = params.dataRoomWorkflowId,
          userId = actor.userId,
          _.copy(
            isRemoved = false,
            groupIds = Set.empty
          )
        )
      )
      _ <- execute { (flowOps, _) =>
        flowOps.inviteUsers(
          params.dataRoomWorkflowId,
          adminId,
          roleMap,
          isToaRequired = false,
          ctx.flatMap(_.getClientIP),
          isApproved = true
        )
      }
      _ <- dataRoomTermsOfAccessService.changeTermsFolderPermissionsUnsafe(
        adminId,
        params.dataRoomWorkflowId,
        roleMap
      )
      _ <- dataRoomHomePageService.changeHomePageFolderPermissions(
        adminId,
        params.dataRoomWorkflowId,
        roleMap
      )
      _ <- greylinDataService.runUnit(
        operation.DataRoomParticipantOperations.addParticipants(
          List(
            modelti.DataRoomParticipant(
              userId = actor.userId,
              dataRoomId = params.dataRoomWorkflowId,
              state = Some(DataRoomParticipantState.Invited)
            )
          )
        )
      )

      // give own permission to all folders and files
      _ <- dataRoomPermissionService.modifyAssetPermissions(
        actor = actor.userId,
        dataRoomWorkflowId = params.dataRoomWorkflowId,
        userChanges = individualChanges,
        mode = FileService.PermissionModMode.Unsafe,
        actorIp = ctx.flatMap(_.getClientIP),
        modifyPermissionSourceOfEvent = None
      )
      _ <- FDBRecordDatabase.transact(FDBOperations[DataRoomFlowOperations].Production)(
        _.addPortalUserToDataRoom(
          params.dataRoomWorkflowId,
          actor.userId,
          ctx.flatMap(_.getClientIP)
        )
      )

      // Send email
      authenticationWhitelabelIdOpt <- dataRoomWhiteLabelService.getAuthenticationWhitelabelId(params.dataRoomWorkflowId)
      allVariables <- dataRoomEmailService.getAllVariables(
        createdState,
        adminId,
        Some(actor.userId),
        Some(actor.userId),
        Some(Instant.now())
      )

      emailTemplate <- dataRoomEmailService.getEmailTemplate(
        GetEmailTemplateParams(
          params.dataRoomWorkflowId,
          DataRoomEmailTemplateType.Invitation,
          isRendered = true
        ),
        adminId
      )
      _ <- ZIO.when(!doNotSendEmailToPortalUser) {
        dataRoomEmailSenderService.sendEmail(
          params.dataRoomWorkflowId,
          DataRoomInvitationEmailGenerate(
            dataRoomWorkflowId = params.dataRoomWorkflowId,
            inviter = actor.userId,
            invitedUser = actor.userId,
            dataRoomName = createdState.name,
            subject = DataRoomEmailUtils.populateSubjectVariables(emailTemplate.template.subject, allVariables),
            message = DataRoomEmailUtils.populateFieldVariables(emailTemplate.template.body, allVariables),
            buttonLabel = "Access data room",
            invitedAt = None,
            authenticationWhitelabelIdOpt = authenticationWhitelabelIdOpt
          )
        )
      }
      _ <- dataRoomSharedFlowService.modifyLastUpdate(params.dataRoomWorkflowId)
    } yield DataRoomEmptyResponse()
  }

  def removePortalUserFromDataRoom(
    params: RemovePortalUserFromDataRoomParams,
    actor: ServiceActor,
    httpContext: Option[AuthenticatedRequestContext] = None
  ): Task[DataRoomEmptyResponse] = {
    dataRoomPortalValidator.checkWriteDataRoomPortal(params, actor.userId) *>
      removePortalUserFromDataRoomUnsafe(params, actor, httpContext)
  }

  def removePortalUserFromDataRoomUnsafe(
    params: RemovePortalUserFromDataRoomParams,
    actor: ServiceActor,
    httpContext: Option[AuthenticatedRequestContext] = None
  ): Task[DataRoomEmptyResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.userId} is removing themself from data room ${params.dataRoomWorkflowId}")
      hasJoined <- execute((_, validateOps) =>
        for {
          (_, hasJoined) <- validateOps.getMemberStateCheckResult(
            actor.userId,
            params.dataRoomWorkflowId,
            DataRoomValidateOperations.MemberStateCheck.RequireJoined
          )
        } yield hasJoined == DataRoomValidateOperations.MemberStateCheck.Passed
      )
      _ <-
        if (hasJoined) {
          for {
            _ <- FDBRecordDatabase.transact(
              FDBOperations[DataRoomValidateOperations].Production
            ) { validateOps =>
              for {
                adminIds <- validateOps.roleOps.getCurrentAdmins(params.dataRoomWorkflowId)
                // Filter joined only
                joinedAdminIds <- RecordIO
                  .parTraverseN(10)(adminIds)(adminId =>
                    for {
                      (_, hasJoined) <- validateOps.getMemberStateCheckResult(
                        adminId,
                        params.dataRoomWorkflowId,
                        DataRoomValidateOperations.MemberStateCheck.RequireJoined
                      )
                    } yield adminId -> (hasJoined == DataRoomValidateOperations.MemberStateCheck.Passed)
                  )
                  .map(_.filter(_._2).map(_._1))

                isActorAdmin = joinedAdminIds.contains(actor.userId)
                currentAdminCount = joinedAdminIds.size
                _ <- RecordIO
                  .validate(!(isActorAdmin && currentAdminCount <= 1))(
                    GeneralServiceException(s"Data room ${params.dataRoomWorkflowId} has only 1 admin remaining")
                  )
              } yield ()
            }
            _ <- dataRoomParticipantService.removeUsers(
              RemoveUsersFromDataRoomParams(
                params.dataRoomWorkflowId,
                Set(actor.userId),
                doNotNotifyByEmail = params.doNotNotifyByEmail
              ),
              actor.userId,
              httpContext
            )
          } yield ()
        } else {
          dataRoomParticipantService.declineInvitation(
            DeclineInvitationToDataRoomParams(params.dataRoomWorkflowId),
            actor,
            httpContext
          )
        }
    } yield DataRoomEmptyResponse()
  }

  def enableDataRoomWebhookUnsafe(
    params: EnableDataRoomWebhookParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext] = None
  ): Task[DataRoomEmptyResponse] = {
    FDBRecordDatabase
      .transact(DataRoomFlowOperations.Production) { flowOps =>
        flowOps.changeEnableWebhook(
          params.dataRoomWorkflowId,
          actor,
          httpContext.flatMap(_.getClientIP),
          params.enableWebhook
        )
      }
      .as(DataRoomEmptyResponse())
  }

}

object DataRoomAdminPortalService {

  final case class ChangeExtraSeatsSingleDataRoomParams(
    dataRoomWorkflowId: DataRoomWorkflowId,
    updateExtraSeatsFn: Int => Int
  )

  final case class GetInternalDataRoomInsightsResponse(
    dataRoomInsights: Seq[DataRoomInsights],
    entities: Seq[DataRoomEntityWithChangeLogs]
  )

  object GetInternalDataRoomInsightsResponse {
    given Codec.AsObject[GetInternalDataRoomInsightsResponse] = deriveCodecWithDefaults
  }

  final case class DataRoomInsights(
    dataRoomInfo: DataRoomInsights.Info,
    roleInsights: DataRoomInsights.Role,
    visitorInsights: DataRoomInsights.Visitor,
    linkInsights: DataRoomInsights.InvitationLink,
    groupInsights: DataRoomInsights.Group,
    settings: DataRoomInsights.Settings,
    users: Seq[DataRoomInsights.User]
  )

  object DataRoomInsights {

    given Codec.AsObject[DataRoomInsights] = deriveCodecWithDefaults

    final case class Info(
      dataRoomWorkflowId: DataRoomWorkflowId,
      dataRoomName: String,
      entityId: EntityId,
      entityName: String,
      entityTrackingType: EntityTrackingType,
      plan: String,
      extraSeat: Int,
      totalSeat: Int,
      expirationDate: Long // epoch milli
    )

    object Info {
      given Codec.AsObject[Info] = deriveCodecWithDefaults
    }

    final case class Role(
      adminCount: Int,
      memberCount: Int,
      contributorCount: Int,
      observerCount: Int
    )

    object Role {
      given Codec.AsObject[Role] = deriveCodecWithDefaults
    }

    final case class Visitor(
      internalVisitCount: Long,
      externalVisitCount: Long
    )

    object Visitor {
      given Codec.AsObject[Visitor] = deriveCodecWithDefaults
    }

    final case class InvitationLink(
      linkCount: Int
    )

    object InvitationLink {
      given Codec.AsObject[InvitationLink] = deriveCodecWithDefaults
    }

    final case class Group(
      groupCount: Int,
      groupNames: Seq[String]
    )

    object Group {
      given Codec.AsObject[Group] = deriveCodecWithDefaults
    }

    final case class Settings(
      whiteLabelEnabled: Boolean,
      documentSearchEnabled: Boolean,
      homePageEnabled: Boolean,
      termsOfAccessEnabled: Boolean,
      watermarkEnabled: Boolean,
      integrationEnabled: Boolean,
      notificationMode: String,
      notificationFrequency: String,
      cloudIntegrations: Seq[String]
    )

    object Settings {
      given Codec.AsObject[Settings] = deriveCodecWithDefaults
    }

    final case class User(
      userId: UserId,
      dataRoomWorkflowId: DataRoomWorkflowId,
      role: String,
      groupName: String
    )

    object User {
      given Codec.AsObject[User] = deriveCodecWithDefaults
    }

    final case class DataRoomEntityWithChangeLogs(
      entityId: EntityId,
      entityName: String,
      entityTrackingType: EntityTrackingType,
      dataRoomCount: Int,
      plan: String,
      changeLogs: List[DataRoomEntityWithChangeLogs.ChangeLog]
    )

    object DataRoomEntityWithChangeLogs {

      given Codec.AsObject[DataRoomEntityWithChangeLogs] = deriveCodecWithDefaults

      final case class ChangeLog(
        at: Long,
        plan: String
      )

      object ChangeLog {
        given Codec.AsObject[ChangeLog] = deriveCodecWithDefaults
      }

    }

  }

}
