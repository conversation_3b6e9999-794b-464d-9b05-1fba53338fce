package anduin.dataroom.integration.workflow

import zio.temporal.{workflowInterface, workflowMethod}

import anduin.dataroom.workflow.{StorageFileUploadParamMessage, StorageFileUploadResponseMessage}
import anduin.workflow.WorkflowQueue
import anduin.workflow.common.{TemporalWorkflow, TemporalWorkflowCompanion}

@workflowInterface
trait StorageFileSyncWorkflow
    extends TemporalWorkflow[StorageFileUploadParamMessage, StorageFileUploadResponseMessage] {

  @workflowMethod
  override def run(params: StorageFileUploadParamMessage): StorageFileUploadResponseMessage

}

object StorageFileSyncWorkflow extends TemporalWorkflowCompanion[StorageFileSyncWorkflow] {
  override def workflowQueue: WorkflowQueue = WorkflowQueue.StorageFileUpload

  override val maximumRetryAttempts: Int = 1

}
