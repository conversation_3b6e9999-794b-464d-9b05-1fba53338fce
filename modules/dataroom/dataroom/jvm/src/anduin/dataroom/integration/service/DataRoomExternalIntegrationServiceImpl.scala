// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.integration.service

import io.circe.Json
import zio.{Cause, Task, ZIO}

import anduin.dataroom.bot.{DataRoomBotService, DataRoomIntegrationBotUser}
import anduin.dataroom.integration.fdb.{DataRoomIntegrationStoreOperations, DataRoomIntegrationStoreProvider}
import anduin.dataroom.integration.{CloudProviderType, DataRoomIntegrationUtils}
import anduin.dms.DmsFeature.DataRoom
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.fdb.record.model.RecordTask
import anduin.fdb.record.{FDBRecordDatabase, FDBRecordKeySpace}
import anduin.id.dataroom.DataRoomIntegrationConfigId
import anduin.id.role.portal.PortalSectionId
import anduin.model.common.user.UserId
import anduin.model.id.{FileId, FolderId}
import anduin.portaluser.PortalUserService
import anduin.service.{AuthenticatedRequestContext, GeneralServiceException}
import anduin.storageservice.box.BoxService
import anduin.storageservice.common.BaseStorageService
import anduin.storageservice.drive.DriveService
import anduin.storageservice.dropbox.DropboxService
import anduin.storageservice.s3.S3Service
import anduin.storageservice.sftp.SftpServerService
import anduin.storageservice.sharepoint.SharePointService
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.authorization.AuthorizationService
import com.anduin.stargazer.service.utils.ZIOUtils
import anduin.dataroom.integration.endpoint.*
import anduin.dataroom.integration.workflow.StorageFileSyncWorkflow
import anduin.dataroom.workflow.{StorageFileUploadParamMessage, StorageFileUploadResponseMessage, UploadFileItemMessage}
import anduin.dms.service.FileService
import anduin.storageservice.common.BaseStorageService.UploadFileItem
import anduin.workflow.TemporalWorkflowService

/** Note that data room integration config supports one folder with multiple configs, but for now we only need one
  * config per folder, so all apis here just use folderId as identity of the config.
  */
final case class DataRoomExternalIntegrationServiceImpl(
  gondorBackendConfig: GondorBackendConfig,
  authorizationService: AuthorizationService,
  portalUserService: PortalUserService,
  dataRoomBotService: DataRoomBotService,
  fileService: FileService,
  boxService: BoxService,
  driveService: DriveService,
  sharePointService: SharePointService,
  dropboxService: DropboxService,
  sftpServerService: SftpServerService,
  dataRoomIntegBotUser: DataRoomIntegrationBotUser,
  s3Service: S3Service,
  temporalWorkflowService: TemporalWorkflowService
) extends DataRoomExternalIntegrationService {

  def getStorageConfig(
    actor: UserId,
    folderId: FolderId
  ): Task[Option[StorageIntegrationConfig]] = {
    for {
      _ <- ZIO.logInfo(s"User $actor get storage config of $folderId")
      _ <- portalUserService.hasAnyReadPermissions(actor, Seq(PortalSectionId.DataRoom, PortalSectionId.FundSub))
      configOpt <- transact { store =>
        val configId = DataRoomIntegrationConfigId.defaultFolderConfigId(folderId)
        DataRoomIntegrationStoreOperations(store).getStorageConfig(configId)
      }
    } yield configOpt.map(DataRoomIntegrationUtils.transformStorageIntegrationConfigProto)
  }

  def createStorageConfig(
    actor: UserId,
    folderId: FolderId
  ): Task[StorageIntegrationConfig] = {
    for {
      _ <- ZIO.logInfo(s"User $actor create storage config for $folderId")
      _ <- portalUserService.hasAnyWritePermissions(actor, Seq(PortalSectionId.DataRoom, PortalSectionId.FundSub))
      botUser <- dataRoomIntegBotUser.userId
      _ <- dataRoomBotService.verifyAccess(
        botUser,
        folderId,
        dataRoomIntegBotUser.userInfo.emailAddressStr
      )
      config <- transact { store =>
        val configId = DataRoomIntegrationConfigId.defaultFolderConfigId(folderId)
        DataRoomIntegrationStoreOperations(store).createStorageConfig(botUser, configId)
      }
    } yield DataRoomIntegrationUtils.transformStorageIntegrationConfigProto(config)
  }

  def updateStorageConfig(
    actor: UserId,
    folderId: FolderId,
    configs: Seq[CloudStorageConfig]
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor update storage config for $folderId")
      _ <- portalUserService.hasAnyWritePermissions(actor, Seq(PortalSectionId.DataRoom, PortalSectionId.FundSub))
      botUser <- dataRoomIntegBotUser.userId
      _ <- dataRoomBotService.verifyAccess(
        botUser,
        folderId,
        dataRoomIntegBotUser.userInfo.emailAddressStr
      )
      _ <- transact { store =>
        val storeOps = DataRoomIntegrationStoreOperations(store)
        val configId = DataRoomIntegrationConfigId.defaultFolderConfigId(folderId)
        for {
          _ <- storeOps.upsertStorageConfig(
            configId = configId,
            configs = Some(configs.map(DataRoomIntegrationUtils.transformCloudStorageConfig))
          )
        } yield ()
      }
    } yield ()
  }

  def deleteStorageConfig(
    actor: UserId,
    folderId: FolderId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor delete storage config of $folderId")
      _ <- portalUserService.hasAnyWritePermissions(actor, Seq(PortalSectionId.DataRoom, PortalSectionId.FundSub))
      botUser <- dataRoomIntegBotUser.userId
      _ <- dataRoomBotService.verifyAccess(
        botUser,
        folderId,
        dataRoomIntegBotUser.userInfo.emailAddressStr
      )
      _ <- transact { store =>
        val configId = DataRoomIntegrationConfigId.defaultFolderConfigId(folderId)
        DataRoomIntegrationStoreOperations(store).deleteStorageConfig(configId)
      }
    } yield ()
  }

  def getAllCloudProviderSharedFolders(actor: UserId): Task[GetAllCloudProviderSharedFoldersResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor get shared folders of all cloud providers")
      _ <- portalUserService.hasAnyReadPermissions(actor, Seq(PortalSectionId.DataRoom, PortalSectionId.FundSub))
      folderMap <- ZIO.foreachPar(CloudProviderType.values) { storageType =>
        for {
          storageService <- getStorageService(storageType)
          folders <- storageService.listRootSharedFolders()
        } yield storageType -> folders.map(folder =>
          CloudProviderFolderInfo(
            folder.id,
            folder.name,
            folder.owners
          )
        )
      }
    } yield GetAllCloudProviderSharedFoldersResponse(folderMap.toMap)
  }

  def getCloudProvidersSetupInfo(actor: UserId): Task[GetCloudProvidersSetupInfoResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor get cloud providers setup info")
      _ <- portalUserService.hasAnyReadPermissions(actor, Seq(PortalSectionId.DataRoom, PortalSectionId.FundSub))
      boxAccount <- boxService.getBoxAppEmailId
      driveAccount <- driveService.getServiceAccountEmail
      sharePointAccount <- sharePointService.getBotAccountEmail
      dropboxAccount <- dropboxService.getBotAccountEmail
      dataRoomBot <- ZIO.attempt(dataRoomIntegBotUser.userInfo.emailAddressStr)
    } yield GetCloudProvidersSetupInfoResponse(
      dataRoomBot,
      Map(
        CloudProviderType.BOX -> boxAccount,
        CloudProviderType.DRIVE -> driveAccount,
        CloudProviderType.SHARE_POINT -> sharePointAccount,
        CloudProviderType.DROPBOX -> dropboxAccount
      ),
      gondorBackendConfig.sftpServerConfig.publicKey
    )
  }

  override def syncFiles(
    rootFolderId: FolderId,
    files: Seq[FileId],
    syncMetaData: Map[String, String] = Map.empty // Only for logging now
  ): Task[Unit] = {
    for {
      _ <- ZIOUtils.validate(files.forall(file => rootFolderId.isAncestorOf(file)))(
        new RuntimeException(s"Some files in $files are not descendant of root $rootFolderId")
      )
      botUser <- dataRoomIntegBotUser.userId
      _ <- ZIO.logInfo(s"Sync ${files.size} files to external storage, DataRoom folder ID: $rootFolderId")
      // Construct folder structure from files and root folder
      fileUploadItems <- ZIO.foreach(files) { fileId =>
        for {
          (folderPath, fileName) <- getFilePathFromRoot(
            botUser,
            rootFolderId,
            fileId
          )
          storageId <- fileService.getFileStorageId(
            botUser,
            fileId,
            DmsTrackingActivityType.Internal,
            None
          )
        } yield BaseStorageService.UploadFileItem(
          destPath = folderPath,
          name = fileName,
          storageId = storageId
        )
      }
      // Sync files to external storage
      _ <- syncFilesToCloudProviders(
        rootFolderId,
        fileUploadItems,
        syncMetaData
      )
    } yield ()
  }

  private def getFilePathFromRoot(actor: UserId, rootFolderId: FolderId, fileId: FileId) = {
    for {
      fileName <- fileService.getFileName(actor)(fileId)
      folderPath <- ZIOUtils.tailRecM[Any, Throwable, (FolderId, Seq[String]), Seq[String]](
        fileId.folder -> Seq.empty[String]
      ) { case (currentFolder, currentPath) =>
        if (currentFolder == rootFolderId) {
          ZIO.succeed(Right(currentPath))
        } else {
          for {
            folderName <- fileService.getFolderName(actor)(currentFolder)
          } yield Left((currentFolder.parentFolder.getOrElse(rootFolderId), Seq(folderName) ++ currentPath))
        }
      }
    } yield folderPath -> fileName
  }

  private def syncFilesToCloudProviders(
    folderId: FolderId,
    files: Seq[BaseStorageService.UploadFileItem],
    syncMetaData: Map[String, String]
  ) = {
    for {
      config <- transact { store =>
        val configId = DataRoomIntegrationConfigId.defaultFolderConfigId(folderId)
        DataRoomIntegrationStoreOperations(store).getStorageConfig(configId)
      }
      allConfigs <- ZIO.attempt(config.map(_.configs).getOrElse(Seq.empty))
      _ <- ZIO.foreachDiscard(allConfigs) { storageConfig =>
        ZIOUtils.when(storageConfig.isEnabled) {
          for {
            _ <- ZIO.logInfo(s"Start syncing files to ${storageConfig.storageType}")
            _ <- startSyncFilesJob(
              storageType = storageConfig.storageType,
              rootFolderId = storageConfig.rootFolderId,
              configOpt = storageConfig.configOpt,
              files = files,
              syncMetaData = syncMetaData
            )
          } yield ()

        }
      }
    } yield ()
  }

  private def startSyncFilesJob(
    storageType: CloudProviderType,
    rootFolderId: String,
    configOpt: Option[Json],
    files: Seq[BaseStorageService.UploadFileItem],
    syncMetaData: Map[String, String]
  ): Task[Unit] = {
    val temporalParams = StorageFileUploadParamMessage(
      storageType,
      rootFolderId,
      configOpt,
      files.map(convertToUploadFileItemMessage),
      syncMetaData
    )
    temporalWorkflowService
      .runAsync[
        StorageFileUploadParamMessage,
        StorageFileUploadResponseMessage,
        StorageFileSyncWorkflow
      ](temporalParams)
      .unit
      .tapErrorCause { cause =>
        onSyncFileError(temporalParams, cause)
      }
  }

  private def onSyncFileError(params: StorageFileUploadParamMessage, error: Cause[?]): Task[Unit] = {
    ZIO.logErrorCause(
      s"""Cannot sync file to ${params.cloudProviderType} for:
         | - Root folder: ${params.rootFolderId}
         | - Files: ${params.files}
         | - ConfigOpt: ${params.configOpt}
         | - Others: ${params.syncMetaData}
         |""".stripMargin,
      error
    )
  }

  private def convertToUploadFileItemMessage(file: UploadFileItem): UploadFileItemMessage =
    UploadFileItemMessage(
      file.destPath,
      file.name,
      file.storageId
    )

  override def getBotAccountContext(): Task[AuthenticatedRequestContext] = {
    for {
      botUser <- dataRoomIntegBotUser.userId
      context <- getContext(botUser, authorizationService)
    } yield context
  }

  private def getStorageService(storageType: CloudProviderType): Task[BaseStorageService] = {
    storageType match {
      case CloudProviderType.BOX         => ZIO.succeed(boxService)
      case CloudProviderType.DRIVE       => ZIO.succeed(driveService)
      case CloudProviderType.SHARE_POINT => ZIO.succeed(sharePointService)
      case CloudProviderType.DROPBOX     => ZIO.succeed(dropboxService)
      case CloudProviderType.SFTP_SERVER => ZIO.succeed(sftpServerService)
      case CloudProviderType.Unrecognized(_) =>
        ZIO.fail(GeneralServiceException("Cannot get storage service for unrecognized storage type"))
    }
  }

  def transact[T](
    ops: DataRoomIntegrationStoreProvider.Store => RecordTask[T]
  ): Task[T] = {
    FDBRecordDatabase.transact(DataRoomIntegrationStoreProvider(FDBRecordKeySpace))(ops)
  }

  private def getContext(actor: UserId, authorizationService: AuthorizationService): Task[AuthenticatedRequestContext] = {
    for {
      token <- zio.ZIO.attempt(authorizationService.generateAnonymousToken(actor))
      actor <- authorizationService.authenticateSession(token)
    } yield AuthenticatedRequestContext(
      actor,
      headers = Seq("Authorization" -> s"Bearer $token"),
      cookies = Seq.empty
    )
  }

}
