package anduin.dataroom.integration.workflow

import java.time.Duration

import zio.temporal.ZRetryOptions
import zio.temporal.activity.ZActivityOptions
import zio.temporal.workflow.ZWorkflow

import anduin.dataroom.workflow.{StorageFileUploadParamMessage, StorageFileUploadResponseMessage}
import anduin.workflow.*
import anduin.workflow.common.TemporalWorkflowImplCompanion

class StorageFileSyncWorkflowImpl extends StorageFileSyncWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[StorageFileSyncActivities](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofMinutes(45))
        .withTaskQueue(ActivityQueue.StorageFileSync.queueName)
        .withRetryOptions(
          // Retry after 5, 10, 20 minutes
          ZRetryOptions.default
            .withMaximumAttempts(4)
            .withBackoffCoefficient(2)
            .withInitialInterval(Duration.ofMinutes(5))
        )
    )

  override def run(params: StorageFileUploadParamMessage): StorageFileUploadResponseMessage = {
    runAsync(params).getOrThrow
  }

  override def runAsync(params: StorageFileUploadParamMessage): WorkflowTask[StorageFileUploadResponseMessage] =
    WorkflowTask.executeActivity(
      activities.startSyncFiles(params)
    )

}

object StorageFileSyncWorkflowImpl
    extends TemporalWorkflowImplCompanion[StorageFileSyncWorkflow, StorageFileSyncWorkflowImpl] {

  val instance = WorkflowImpl.derived[StorageFileSyncWorkflow, StorageFileSyncWorkflowImpl]

}
