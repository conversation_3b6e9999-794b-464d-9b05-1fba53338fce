package anduin.dataroom.integration.workflow

import zio.temporal.{activityInterface, activityMethod}

import anduin.dataroom.workflow.{StorageFileUploadParamMessage, StorageFileUploadResponseMessage}
import anduin.workflow.ActivityQueue
import anduin.workflow.common.{TemporalActivity, TemporalActivityCompanion}

@activityInterface
trait StorageFileSyncActivities extends TemporalActivity {

  @activityMethod
  def startSyncFiles(params: StorageFileUploadParamMessage): StorageFileUploadResponseMessage

}

object StorageFileSyncActivities
    extends TemporalActivityCompanion[StorageFileSyncActivities](ActivityQueue.StorageFileSync)
