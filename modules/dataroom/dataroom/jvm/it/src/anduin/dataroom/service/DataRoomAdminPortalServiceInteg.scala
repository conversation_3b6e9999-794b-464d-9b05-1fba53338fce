// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service

import java.time.LocalDate

import sttp.model.MediaType

import anduin.dataroom.flow.{DataRoomEventStoreProvider, DataRoomStateStoreOperations, DataRoomStateStoreProvider}
import anduin.dataroom.role.*
import anduin.fdb.record.FDBRecordDatabase
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{EntityIdFactory, FolderId}
import anduin.orgbilling.model.plan.DataRoomPlan
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.service.{AuthenticatedRequestContext, ServiceActor}
import anduin.stargazer.service.dataroom.*
import anduin.storageservice.common.FileContentOrigin
import anduin.testing.GondorCoreIntegUtils
import anduin.utils.stream.ZStreamIOUtils
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import anduin.testing.DataRoomBaseInteg
import anduin.entity.EntityTestUtils
import com.anduin.stargazer.service.orgbilling.storage.OrgBillingStoreOperations
import zio.test.*

import anduin.dataroom.participant.DataRoomParticipantRoleOperations

object DataRoomAdminPortalServiceInteg extends DataRoomBaseInteg with GondorCoreIntegUtils { self =>

  // scalafix:off DisableSyntax.var
  private var entityId = EntityIdFactory.unsafeRandomId
  private val entityDataRoomPlan = DataRoomPlan.DataRoomBusinessPlan(LocalDate.now().minusDays(158), isTrial = true)
  private var businessDataRoomId: DataRoomWorkflowId = scala.compiletime.uninitialized
  private var admin: UserId = scala.compiletime.uninitialized
  private var adminContext: AuthenticatedRequestContext = scala.compiletime.uninitialized
  // scalafix:on

  private val entityName = "Test Data room portal service"

  private val icContext = AuthenticatedRequestContext(
    actor = ServiceActor.defaultServiceActor.copy(userId = userIC),
    headers = Seq.empty,
    cookies = Seq.empty
  )

  private val ccContext = AuthenticatedRequestContext(
    actor = ServiceActor.defaultServiceActor.copy(userId = userCC),
    headers = Seq.empty,
    cookies = Seq.empty
  )

  override def spec = suite("Data Room Admin Portal Service")(
    test("Rebuild indexes") {
      for {
        _ <- FDBRecordDatabase
          .buildIndexes(
            DataRoomEventStoreProvider.Production,
            DataRoomStateStoreProvider.Production
          )
      } yield assertCompletes
    },
    test("Create ExecutiveAdmin") {
      for {
        id <- executiveAdmin.userId
      } yield {
        admin = id
        adminContext = AuthenticatedRequestContext(
          actor = ServiceActor.defaultServiceActor.copy(userId = id),
          headers = Seq.empty,
          cookies = Seq.empty
        )
        assertCompletes
      }
    },
    test("Create entity") {
      for {
        createdEntityId <- EntityTestUtils.createEntity(
          entityName,
          "DRPortal",
          userIC
        )
      } yield {
        entityId = createdEntityId
        assertCompletes
      }
    },

    // This step to avoid trigger trial data room business of newly created org
    test("Expire the trial data room of newly entity") {
      for {
        _ <- orgBillingService.changeDataRoomPlan(
          entityId,
          OrgBillingStoreOperations.toDataRoomPackageProto(entityDataRoomPlan),
          admin
        )
      } yield assertCompletes
    },
    test("Normal user should not create data room on-behalf") {
      for {
        result <- dataRoomAdminPortalService
          .createDataRoomOnBehalf(
            CreateDataRoomOnBehalfParams(
              CreateDataRoomParams(
                "Covid-17",
                entityId,
                None,
                showIndex = false
              ),
              EmailAddress.unapply(userICEmail).get,
              None
            ),
            ccContext
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("User should not create data room on-behalf by him-self") {
      for {
        result <- dataRoomAdminPortalService
          .createDataRoomOnBehalf(
            CreateDataRoomOnBehalfParams(
              CreateDataRoomParams(
                "Covid-18",
                entityId,
                None,
                showIndex = false
              ),
              EmailAddress.unapply(userICEmail).get,
              None
            ),
            icContext
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("Anduin admin should be able to create data room on behalf of other") {
      val expDate = LocalDate.now().plusMonths(1)
      for {
        drId <- dataRoomAdminPortalService.createDataRoomOnBehalf(
          CreateDataRoomOnBehalfParams(
            CreateDataRoomParams(
              "Covid-19",
              entityId,
              None,
              showIndex = false
            ),
            EmailAddress.unapply(userICEmail).get,
            Some(DataRoomPlan.DataRoomProPlan(expDate))
          ),
          adminContext
        )
        createdState <- FDBRecordDatabase.transact(DataRoomStateStoreProvider.Production) {
          DataRoomStateStoreOperations(_).getState(drId.dataRoomWorkflowId)
        }
      } yield {
        assertTrue(
          createdState.packageOpt
            .map(_.plan)
            .contains(
              OrgBillingStoreOperations.toDataRoomPackageProto(
                DataRoomPlan.DataRoomProPlan(expDate)
              )
            )
        )
      }
    },
    test("Anduin admin should be able to create data room with Business plan on behalf of other") {
      val expDate = LocalDate.now().plusMonths(1)
      for {
        drId <- dataRoomAdminPortalService.createDataRoomOnBehalf(
          CreateDataRoomOnBehalfParams(
            CreateDataRoomParams(
              "Covid-20",
              entityId,
              None,
              showIndex = false
            ),
            EmailAddress.unapply(userICEmail).get,
            Some(DataRoomPlan.DataRoomBusinessPlan(expDate))
          ),
          adminContext
        )
        createdState <- FDBRecordDatabase.transact(DataRoomStateStoreProvider.Production) {
          DataRoomStateStoreOperations(_).getState(drId.dataRoomWorkflowId)
        }
      } yield {
        businessDataRoomId = drId.dataRoomWorkflowId
        assertTrue(
          createdState.packageOpt
            .map(_.plan)
            .contains(
              OrgBillingStoreOperations.toDataRoomPackageProto(DataRoomPlan.DataRoomBusinessPlan(expDate))
            )
        )
      }
    },
    test("IC can enable watermark") {
      val watermarkMetadataParams = WatermarkMetadataParams(
        "Da Nang",
        WatermarkColor.Gray,
        WatermarkLayout.Subtle,
        WatermarkTransparency.Fifty
      )
      for {
        _ <- dataRoomService.modifyGeneralSettings(
          ModifyDataRoomGeneralSettingsParams(
            businessDataRoomId,
            None,
            None,
            isWatermarkMetadataChanged = true,
            watermarkMetadata = Some(watermarkMetadataParams),
            showIndex = None
          ),
          icContext
        )
        createdState <- FDBRecordDatabase.transact(DataRoomStateStoreProvider.Production) {
          DataRoomStateStoreOperations(_).getState(businessDataRoomId)
        }
      } yield {
        assertTrue(
          createdState.watermarkMetadata.isDefined,
          createdState.watermarkMetadata.map(_.text).contains(watermarkMetadataParams.text),
          createdState.watermarkMetadata.map(_.color).contains(watermarkMetadataParams.color.value)
        )
      }
    },
    test("IC can disable watermark") {
      for {
        _ <- dataRoomService.modifyGeneralSettings(
          ModifyDataRoomGeneralSettingsParams(
            businessDataRoomId,
            None,
            None,
            isWatermarkMetadataChanged = true,
            watermarkMetadata = None,
            showIndex = None
          ),
          icContext
        )
        createdState <- FDBRecordDatabase.transact(DataRoomStateStoreProvider.Production) {
          DataRoomStateStoreOperations(_).getState(businessDataRoomId)
        }
      } yield assertTrue(createdState.watermarkMetadata.isEmpty)
    },
    test("Enable term of access") {
      for {
        _ <- uploadTermOfAccess(icContext.actor, businessDataRoomId)
        createdState <- FDBRecordDatabase.transact(DataRoomStateStoreProvider.Production) {
          DataRoomStateStoreOperations(_).getState(businessDataRoomId)
        }
      } yield {
        assertTrue(
          createdState.termsOfAccessOptions.isDefined,
          createdState.termsOfAccessCertificates.size == 1,
          createdState.termsOfAccessOptions.get.isEnabled == true && createdState.termsOfAccessOptions.get.versions.size == 1
        )
      }
    },
    test("Invite CC as view only permission") {
      for {
        _ <- dataRoomParticipantService.inviteUsers(
          InviteUsersToDataRoomParams(
            businessDataRoomId,
            individualPermissionMap = Map(
              userCCEmail -> DataRoomPermissionChanges(
                roleSet = Some(Member()),
                assetPermissions = AssetPermissionChanges(
                  folderPermissions = Map.empty,
                  filePermissions = Map.empty,
                  recursivePermissions = Map(
                    FolderId.channelSystemFolderId(businessDataRoomId) -> Some(FileFolderPermission.ViewOnly)
                  )
                )
              )
            ),
            isToaRequired = true,
            subject = "Subject",
            message = "Hello",
            buttonLabel = "CTA"
          ),
          icContext.actor
        )
        validateFolderPermissionOfCC <-
          fileService
            .validateFolder(userCC, FileFolderPermission.ViewOnly)(
              FolderId.channelSystemFolderId(businessDataRoomId)
            )(
              using anduin.dms.DmsFeature.DataRoom
            )
            .either
        roleMap <- DataRoomParticipantRoleOperations.transact(
          _.getAllParticipantRoleMap(businessDataRoomId)
        )
      } yield {
        assertTrue(
          roleMap.size == 2,
          roleMap.get(userCC).contains(Member()),
          validateFolderPermissionOfCC.isRight
        )
      }
    },
    test("Non-admin should not change single data room plan") {
      for {
        resp1 <-
          dataRoomAdminPortalService
            .changeSingleDataRoomPlan(
              ChangeSingleDataRoomPlanParams(businessDataRoomId, None),
              icContext
            )
            .either
        resp2 <-
          dataRoomAdminPortalService
            .changeSingleDataRoomPlan(
              ChangeSingleDataRoomPlanParams(businessDataRoomId, None),
              ccContext
            )
            .either
      } yield {
        assertTrue(resp1.isLeft, resp2.isLeft)
      }
    },
    test("Anduin admin should change single data room plan") {
      for {
        _ <- dataRoomAdminPortalService.changeSingleDataRoomPlan(
          ChangeSingleDataRoomPlanParams(businessDataRoomId, None),
          adminContext
        )
        createdState <- FDBRecordDatabase.transact(DataRoomStateStoreProvider.Production) {
          DataRoomStateStoreOperations(_).getState(businessDataRoomId)
        }
      } yield {
        assertTrue(createdState.packageOpt.isEmpty)
      }
    },
    test("IC should not enable watermark") {
      for {
        result <- dataRoomService
          .modifyGeneralSettings(
            ModifyDataRoomGeneralSettingsParams(
              businessDataRoomId,
              None,
              None,
              isWatermarkMetadataChanged = true,
              watermarkMetadata = Some(
                WatermarkMetadataParams(
                  "C Hospital",
                  WatermarkColor.Gray,
                  WatermarkLayout.Subtle,
                  WatermarkTransparency.Fifty
                )
              ),
              showIndex = None
            ),
            icContext
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("Should not change term of access") {
      for {
        result <- uploadTermOfAccess(icContext.actor, businessDataRoomId).either
      } yield assertTrue(result.isLeft)
    },
    test("Should not Invite CM as view only permission") {
      for {
        result <- dataRoomParticipantService
          .inviteUsers(
            InviteUsersToDataRoomParams(
              businessDataRoomId,
              individualPermissionMap = Map(
                userCMEmail -> DataRoomPermissionChanges(
                  roleSet = Some(Member()),
                  assetPermissions = AssetPermissionChanges(
                    folderPermissions = Map.empty,
                    filePermissions = Map.empty,
                    recursivePermissions = Map(
                      FolderId.channelSystemFolderId(businessDataRoomId) -> Some(FileFolderPermission.ViewOnly)
                    )
                  )
                )
              ),
              isToaRequired = true,
              subject = "Subject",
              message = "Hello",
              buttonLabel = "CTA"
            ),
            icContext.actor
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("Non-admin should not get data room info via admin api") {
      for {
        resp1 <-
          dataRoomAdminPortalService
            .getDataRoomInfo(
              GetDataRoomInfoParams(businessDataRoomId),
              icContext
            )
            .either
        resp2 <-
          dataRoomAdminPortalService
            .getDataRoomInfo(
              GetDataRoomInfoParams(businessDataRoomId),
              ccContext
            )
            .either
      } yield {
        assertTrue(resp1.isLeft, resp2.isLeft)
      }
    },
    test("Anduin Admin should get data room info via admin api") {
      for {
        resp <-
          dataRoomAdminPortalService
            .getDataRoomInfo(
              GetDataRoomInfoParams(businessDataRoomId),
              adminContext
            )
            .either
      } yield {
        assertTrue(
          resp.isRight,
          resp.toOption.contains(
            GetDataRoomInfoResponse(
              name = "Covid-20",
              creatorEntity = entityId,
              creatorEntityName = entityName,
              creatorEntityPlan = entityDataRoomPlan,
              planOpt = None,
              enableWebhook = false,
              isArchived = false
            )
          )
        )
      }
    }
  ) @@ TestAspect.sequential

  private def uploadTermOfAccess(
    actor: ServiceActor,
    dataRoomId: DataRoomWorkflowId
  ) = {
    for {
      tempFolderId <- fileService.createUserTemporaryFolderIfNeeded(actor.userId)
      tempToaFileId <- fileService.uploadFile(
        tempFolderId,
        "name",
        FileContentOrigin.FromSource(
          ZStreamIOUtils.fromResource("/documents/normal.pdf"),
          MediaType.ApplicationPdf
        ),
        actor.userId
      )
      resp <- dataRoomService.modifyGeneralSettings(
        ModifyDataRoomGeneralSettingsParams(
          dataRoomId,
          Some(ModifyDataRoomTermsOfAccessTypes.Upload(tempToaFileId)),
          None,
          isWatermarkMetadataChanged = false,
          None,
          showIndex = None
        ),
        AuthenticatedRequestContext(
          actor,
          Seq(),
          Seq()
        )
      )
    } yield resp
  }

}
