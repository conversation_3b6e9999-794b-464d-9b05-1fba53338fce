// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service

import java.net.URI
import java.time.LocalDate

import sttp.model.MediaType
import zio.stream.Stream
import zio.{Task, ZIO}

import anduin.dataroom.flow.{DataRoomStateStoreOperations, DataRoomStateStoreProvider}
import anduin.dataroom.role.*
import anduin.dataroom.service.tracking.DataRoomTrackingService.UserWithVisitCount
import anduin.dms.DmsFeature
import anduin.dms.file.state.FileStateStoreOperations
import anduin.dms.folder.state.FolderStateStoreOperations
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.documentservice.watermark.WatermarkLayout as WatermarkLayoutMessage
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.id.entity.EntityId
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{FileId, FolderId}
import anduin.orgbilling.model.plan.DataRoomPlan
import anduin.protobuf.flow.file.{FileFolderPermission, FileFolderPermissionMap}
import anduin.serverless.common.ServerlessModels.watermark.WatermarkGeneratorRequest
import anduin.serverless.utils.ServerlessUtils
import anduin.service.{AuthenticatedRequestContext, ServiceActor}
import anduin.stargazer.service.dataroom.*
import anduin.storageservice.common.FileContentOrigin
import anduin.utils.stream.ZStreamIOUtils
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import anduin.testing.DataRoomBaseInteg
import anduin.entity.EntityTestUtils
import com.anduin.stargazer.service.orgbilling.storage.OrgBillingStoreOperations
import zio.test.*

import anduin.dataroom.participant.DataRoomParticipantRoleOperations
import anduin.id.link.ProtectedLinkId
import anduin.protobuf.dataroom.link.DataRoomLinkInvitationParamsData
import anduin.testing.GondorCoreIntegUtils

object DataRoomServiceInteg extends DataRoomBaseInteg with GondorCoreIntegUtils { self =>

  override lazy val dmsFeature: DmsFeature = DmsFeature.DataRoom

  // scalafix:off DisableSyntax.var
  private var ic: ServiceActor = scala.compiletime.uninitialized
  private var cc: ServiceActor = scala.compiletime.uninitialized
  private var cm: ServiceActor = scala.compiletime.uninitialized
  private var cu: ServiceActor = scala.compiletime.uninitialized
  private var entityId: EntityId = scala.compiletime.uninitialized
  private var dataroomId: DataRoomWorkflowId = scala.compiletime.uninitialized
  private var toaFileId: FileId = scala.compiletime.uninitialized
  private var randomFileId: FileId = scala.compiletime.uninitialized
  private var invitationLinkId: ProtectedLinkId = scala.compiletime.uninitialized
  // scalafix:on

  val pdfPath = "/documents/normal.pdf"
  val testDataRoomName = "Test data room"
  val duplicatedDataRoomName = "Test data room (Copy)"

  override def spec = suite("DataRoomServiceInteg")(
    setupSuite,
    DRServiceSuite,
    folderPermissionSuite,
    invitePermissionSuite,
    archiverSuite,
    watermarkSuite,
    termOfAccessSuite,
    duplicatingDRSuite
  ) @@ TestAspect.sequential

  private def setupSuite = suite("Setup test context")(
    test("Set up test user") {
      for {
        icActor <- authenticateUser(userIC)
        ccActor <- authenticateUser(userCC)
        cmActor <- authenticateUser(userCM)
        cuActor <- authenticateUser(userCU)
      } yield {
        ic = icActor
        cc = ccActor
        cm = cmActor
        cu = cuActor
        assertCompletes
      }
    },
    test("Create entity") {
      for {
        id <- EntityTestUtils.createEntity(
          "Dataroom integ org",
          "DIO",
          userIC
        )
      } yield {
        entityId = id
        assertCompletes
      }
    },
    test("Set premium package for entity") {
      for {
        _ <- orgBillingService.changeDataRoomPlan(
          entityId,
          OrgBillingStoreOperations.toDataRoomPackageProto(DataRoomPlan.DataRoomBusinessPlan(LocalDate.now().plusDays(15))),
          userIC
        )
      } yield assertCompletes
    }
  )

  private def DRServiceSuite = suite("DataRoomService integ")(
    test("Create a new dataroom") {
      for {
        resp <- dataRoomService.createDataRoom(
          CreateDataRoomParams(
            testDataRoomName,
            entityId,
            None,
            None,
            showIndex = false
          ),
          AuthenticatedRequestContext(
            ic,
            Seq(),
            Seq()
          )
        )
        _ <- ZIO.attempt { dataroomId = resp.dataRoomWorkflowId }
        createdState <- getCreatedState(dataroomId)
        roleMap <- getRoleMap(dataroomId)
      } yield {
        assertTrue(
          createdState.name == testDataRoomName,
          roleMap.size == 1,
          roleMap == Map(
            ic.userId -> Admin()
          ),
          createdState.creatorEntityId == entityId
        )
      }
    },
    test("Non-data room member shouldn't log visit activity") {
      for {
        result <- dataRoomService
          .trackUserVisitDataRoom(
            TrackUserVisitDataRoomParams(dataroomId),
            cc,
            None
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("Rename data room") {
      val newName = "New name data room"
      for {
        _ <- dataRoomService.modifyGeneralSettings(
          ModifyDataRoomGeneralSettingsParams(
            dataRoomWorkflowId = dataroomId,
            modifyTermsOfAccessOpt = None,
            nameOpt = Some(newName),
            watermarkMetadata = None,
            showIndex = None
          ),
          AuthenticatedRequestContext(
            ic,
            Seq(),
            Seq()
          )
        )
        createdState <- getCreatedState(dataroomId)
        folderName <- fileService.getFolderName(ic.userId)(FolderId.channelSystemFolderId(dataroomId))
      } yield {
        assertTrue(createdState.name == newName, folderName == newName)
      }
    },
    test("Invite new member to data room") {
      for {
        _ <- dataRoomParticipantService.inviteUsers(
          InviteUsersToDataRoomParams(
            dataroomId,
            individualPermissionMap = Map(
              userCCEmail -> DataRoomPermissionChanges(
                roleSet = Some(Admin()),
                assetPermissions = AssetPermissionChanges(
                  folderPermissions = Map.empty,
                  filePermissions = Map.empty,
                  recursivePermissions = Map(
                    FolderId.channelSystemFolderId(dataroomId) -> Some(FileFolderPermission.Own)
                  )
                )
              ),
              userCMEmail -> DataRoomPermissionChanges(
                roleSet = Some(Member(canInvite = true)),
                assetPermissions = AssetPermissionChanges(
                  folderPermissions = Map.empty,
                  filePermissions = Map.empty,
                  recursivePermissions = Map.empty
                )
              )
            ),
            isToaRequired = true,
            subject = "",
            message = "",
            buttonLabel = ""
          ),
          ic
        )
        validateFolderPermissionOfCC <-
          fileService
            .validateFolder(cc.userId, FileFolderPermission.Own)(FolderId.channelSystemFolderId(dataroomId))
            .either
        validateFolderPermissionOfCM <-
          fileService
            .validateFolder(cm.userId, FileFolderPermission.Own)(FolderId.channelSystemFolderId(dataroomId))
            .either
        _ <- getCreatedState(dataroomId)
        roleMap <- getRoleMap(dataroomId)
      } yield {
        assertTrue(
          validateFolderPermissionOfCC.isRight,
          validateFolderPermissionOfCM.isLeft,
          roleMap.size == 3,
          roleMap
            .get(cc.userId)
            .contains(
              Admin()
            ),
          roleMap.get(cm.userId).contains(Member(canInvite = true))
        )
      }
    },
    test("User CC decline invitation") {
      for {
        _ <- dataRoomParticipantService.declineInvitation(
          DeclineInvitationToDataRoomParams(dataroomId),
          cc
        )
        validateFolderPermissionOfCC <-
          fileService
            .validateFolder(cc.userId, FileFolderPermission.Own)(FolderId.channelSystemFolderId(dataroomId))
            .either
        _ <- getCreatedState(dataroomId)
        roleMap <- getRoleMap(dataroomId)
      } yield {
        assertTrue(
          validateFolderPermissionOfCC.isLeft,
          roleMap.size == 2
        )
      }
    },
    test("Re-invite CC to dataroom") {
      for {
        _ <- dataRoomParticipantService.inviteUsers(
          InviteUsersToDataRoomParams(
            dataroomId,
            individualPermissionMap = Map(
              userCCEmail -> DataRoomPermissionChanges(
                roleSet = Some(Admin()),
                assetPermissions = AssetPermissionChanges(
                  folderPermissions = Map.empty,
                  filePermissions = Map.empty,
                  recursivePermissions = Map(
                    FolderId.channelSystemFolderId(dataroomId) -> Some(FileFolderPermission.Own)
                  )
                )
              )
            ),
            isToaRequired = true,
            subject = "",
            message = "",
            buttonLabel = ""
          ),
          ic
        )
        validateFolderPermissionOfCC <-
          fileService
            .validateFolder(cc.userId, FileFolderPermission.Own)(FolderId.channelSystemFolderId(dataroomId))
            .either
        roleMap <- getRoleMap(dataroomId)
      } yield {
        assertTrue(
          validateFolderPermissionOfCC.isRight,
          roleMap.size == 3,
          roleMap
            .get(cc.userId)
            .contains(
              Admin()
            ),
          roleMap.get(cm.userId).contains(Member(canInvite = true))
        )
      }
    },
    test("Cancel CC invitation") {
      for {
        _ <- dataRoomParticipantService.removeUsers(
          RemoveUsersFromDataRoomParams(
            dataroomId,
            Set(cc.userId),
            doNotNotifyByEmail = true
          ),
          ic.userId
        )
        validateFolderPermissionOfCC <-
          fileService
            .validateFolder(cc.userId, FileFolderPermission.Own)(FolderId.channelSystemFolderId(dataroomId))
            .either
        roleMap <- getRoleMap(dataroomId)
      } yield {
        assertTrue(
          validateFolderPermissionOfCC.isLeft,
          roleMap.size == 2
        )
      }
    },
    test("Modify permissions of CM") {
      for {
        _ <- dataRoomParticipantService.modifyPermissions(
          ModifyDataRoomPermissionsParams(
            dataroomId,
            updatedUserMap = Map(
              cm.userId -> DataRoomPermissionChanges(
                roleSet = Some(Guest(canInvite = true)),
                assetPermissions = AssetPermissionChanges(
                  filePermissions = Map.empty,
                  folderPermissions = Map.empty,
                  recursivePermissions = Map(
                    FolderId.channelSystemFolderId(dataroomId) -> Some(FileFolderPermission.Write)
                  )
                )
              )
            )
          ),
          ic.userId
        )
        validateFolderPermissionOfCM <-
          fileService
            .validateFolder(cm.userId, FileFolderPermission.Write)(FolderId.channelSystemFolderId(dataroomId))
            .either
        roleMap <- getRoleMap(dataroomId)
      } yield {
        assertTrue(
          validateFolderPermissionOfCM.isRight,
          roleMap.size == 2,
          roleMap.get(cm.userId).contains(Guest(canInvite = true))
        )
      }
    },
    test("CM accept invitation") {
      for {
        _ <- dataRoomParticipantService.acceptInvitation(
          AcceptInvitationToDataRoomParams(dataroomId, None),
          AuthenticatedRequestContext(
            cm,
            Seq(),
            Seq()
          )
        )
        validateFolderPermissionOfCM <-
          fileService
            .validateFolder(cm.userId, FileFolderPermission.Write)(FolderId.channelSystemFolderId(dataroomId))
            .either
        roleMap <- getRoleMap(dataroomId)
      } yield {
        assertTrue(
          validateFolderPermissionOfCM.isRight,
          roleMap.size == 2,
          roleMap.get(cm.userId).contains(Guest(canInvite = true))
        )
      }
    },
    test("Track new user visit data room") {
      for {
        _ <- dataRoomService.trackUserVisitDataRoom(
          TrackUserVisitDataRoomParams(dataroomId),
          ic,
          None
        )
        _ <- dataRoomService.trackUserVisitDataRoom(
          TrackUserVisitDataRoomParams(dataroomId),
          ic,
          None
        )
        visitCount1 <- dataRoomTrackingService.getAllUserDataRoomVisitCount(dataroomId)
        _ <- dataRoomService.trackUserVisitDataRoom(
          TrackUserVisitDataRoomParams(dataroomId),
          ic,
          None
        )
        _ <- dataRoomService.trackUserVisitDataRoom(
          TrackUserVisitDataRoomParams(dataroomId),
          cm,
          None
        )
        _ <- dataRoomService.trackUserVisitDataRoom(
          TrackUserVisitDataRoomParams(dataroomId),
          cm,
          None
        )
        visitCount2 <- dataRoomTrackingService.getAllUserDataRoomVisitCount(dataroomId)
      } yield {
        val expectedVisitCount1 = Set(ic -> 2).map { case (actor, count) =>
          UserWithVisitCount(actor.userId, count.toLong)
        }
        val expectedVisitCount2 = Set(ic -> 3, cm -> 2).map { case (actor, count) =>
          UserWithVisitCount(actor.userId, count.toLong)
        }
        assertTrue(
          visitCount1.toSet.equals(expectedVisitCount1),
          visitCount2.toSet.equals(expectedVisitCount2)
        )
      }
    }
  )

  private def folderPermissionSuite = {
    // scalafix:off DisableSyntax.var, DisableSyntax.null
    var a1FolderId: FolderId = null
    var a1b1FolderId: FolderId = null
    var a1b1c1FolderId: FolderId = null
    var a1b1c2FolderId: FolderId = null
    var a1b2FolderId: FolderId = null
    // scalafix:on

    suite("Dataroom permission at folders integ")(
      test("Create folders") {
        for {
          a1 <- ZIO.attempt(FolderId.channelSystemFolderId(dataroomId))
          a1b1 <- fileService.createEmptyFolder(
            a1,
            "a1b1",
            ic.userId,
            setAuthorAsOwner = true
          )
          a1b1c1 <- fileService.createEmptyFolder(
            a1b1,
            "a1b1c1",
            ic.userId,
            setAuthorAsOwner = true
          )
          a1b1c2 <- fileService.createEmptyFolder(
            a1b1,
            "a1b1c2",
            ic.userId,
            setAuthorAsOwner = true
          )
          a1b2 <- fileService.createEmptyFolder(
            a1,
            "a1b2",
            ic.userId,
            setAuthorAsOwner = true
          )
        } yield {
          a1FolderId = a1
          a1b1FolderId = a1b1
          a1b1c1FolderId = a1b1c1
          a1b1c2FolderId = a1b1c2
          a1b2FolderId = a1b2
          assertCompletes
        }
      },
      test("Try downgrading folder permission of CM") {
        for {
          _ <- dataRoomParticipantService.modifyPermissions(
            ModifyDataRoomPermissionsParams(
              dataroomId,
              Map(
                cm.userId -> DataRoomPermissionChanges(
                  None,
                  AssetPermissionChanges(
                    folderPermissions = Map(
                      a1FolderId -> Some(FileFolderPermission.ViewOnly),
                      a1b2FolderId -> Some(FileFolderPermission.ViewOnly)
                    ),
                    filePermissions = Map.empty,
                    recursivePermissions = Map(
                      a1b1FolderId -> Some(FileFolderPermission.Read)
                    )
                  )
                )
              )
            ),
            ic.userId
          )
          checkFolderA1 <-
            fileService
              .validateFolder(cm.userId, FileFolderPermission.Read)(a1FolderId)
              .either
          _ <-
            fileService
              .validateFolder(cm.userId, FileFolderPermission.Read)(a1b1FolderId)
          _ <-
            fileService
              .validateFolder(cm.userId, FileFolderPermission.Read)(a1b1c1FolderId)
          checkFolderA1B1C2 <-
            fileService
              .validateFolder(cm.userId, FileFolderPermission.Write)(a1b1c2FolderId)
              .either
          checkFolderA1B2 <-
            fileService
              .validateFolder(cm.userId, FileFolderPermission.Read)(a1b2FolderId)
              .either
        } yield {
          assertCompletes && assertTrue(
            checkFolderA1.isLeft,
            checkFolderA1B1C2.isLeft,
            checkFolderA1B2.isLeft
          )
        }
      },
      test("CM invite CC to dataroom") {
        for {
          _ <- dataRoomParticipantService.inviteUsers(
            InviteUsersToDataRoomParams(
              dataroomId,
              Map(
                userCCEmail -> DataRoomPermissionChanges(
                  Some(Guest()),
                  AssetPermissionChanges(
                    folderPermissions = Map(
                      a1b1c1FolderId -> Some(FileFolderPermission.Read)
                    ),
                    filePermissions = Map.empty,
                    recursivePermissions = Map(
                      a1b1c2FolderId -> Some(FileFolderPermission.Write)
                    )
                  )
                )
              ),
              isToaRequired = true,
              subject = "",
              message = "",
              buttonLabel = ""
            ),
            cm
          )
          checkFolderA1 <-
            fileService
              .validateFolder(cc.userId, FileFolderPermission.Write)(a1FolderId)
              .either
          checkFolderA1B1 <-
            fileService
              .validateFolder(cc.userId, FileFolderPermission.Write)(a1b1FolderId)
              .either
          _ <-
            fileService
              .validateFolder(cc.userId, FileFolderPermission.Read)(a1b1c1FolderId)
          checkWriteFolderA1B1C1 <-
            fileService
              .validateFolder(cc.userId, FileFolderPermission.Write)(a1b1c1FolderId)
              .either
          _ <-
            fileService
              .validateFolder(cc.userId, FileFolderPermission.Read)(a1b1c2FolderId)
          checkFolderA1B2 <-
            fileService
              .validateFolder(cc.userId, FileFolderPermission.Write)(a1b2FolderId)
              .either
        } yield {
          assertCompletes &&
          assertTrue(
            checkFolderA1.isLeft,
            checkFolderA1B1.isLeft,
            checkWriteFolderA1B1C1.isLeft,
            checkFolderA1B2.isLeft
          )
        }
      },
      test("CC accept invitation and then kick CC out from data room") {
        for {
          _ <- dataRoomParticipantService.acceptInvitation(
            AcceptInvitationToDataRoomParams(dataroomId, None),
            AuthenticatedRequestContext(
              cc,
              Seq(),
              Seq()
            )
          )
          _ <- dataRoomParticipantService.removeUsers(
            RemoveUsersFromDataRoomParams(
              dataRoomWorkflowId = dataroomId,
              userIds = Set(cc.userId),
              doNotNotifyByEmail = true
            ),
            ic.userId
          )
        } yield assertCompletes
      },
      test("Change permission of CM to can not invite") {
        for {
          _ <- dataRoomParticipantService.modifyPermissions(
            ModifyDataRoomPermissionsParams(
              dataroomId,
              updatedUserMap = Map(
                cm.userId -> DataRoomPermissionChanges(
                  roleSet = Some(Member()),
                  assetPermissions = AssetPermissionChanges(
                    filePermissions = Map.empty,
                    folderPermissions = Map.empty,
                    recursivePermissions = Map(
                      FolderId.channelSystemFolderId(dataroomId) -> Some(FileFolderPermission.Write)
                    )
                  )
                )
              )
            ),
            ic.userId
          )
          validateFolderPermissionOfCM <-
            fileService
              .validateFolder(cm.userId, FileFolderPermission.Write)(FolderId.channelSystemFolderId(dataroomId))
              .either
          roleMap <- getRoleMap(dataroomId)
        } yield {
          assertTrue(
            validateFolderPermissionOfCM.isRight,
            roleMap.size == 2,
            roleMap
              .get(cm.userId)
              .contains(
                Member()
              )
          )
        }
      }
    )

  }

  private def invitePermissionSuite = suite("Data room invite permission integ test")(
    test("CM shouldn't invite others to dataroom") {
      for {
        result <- dataRoomParticipantService
          .inviteUsers(
            InviteUsersToDataRoomParams(
              dataroomId,
              Map(
                userCCEmail -> DataRoomPermissionChanges(
                  roleSet = None,
                  AssetPermissionChanges(
                    folderPermissions = Map.empty,
                    filePermissions = Map.empty,
                    recursivePermissions =
                      Map(FolderId.channelSystemFolderId(dataroomId) -> Some(FileFolderPermission.Read))
                  )
                )
              ),
              isToaRequired = true,
              subject = "",
              message = "",
              buttonLabel = ""
            ),
            cm
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("IC change permission of CM to can Invite but not Admin") {
      for {
        _ <- dataRoomParticipantService.modifyPermissions(
          ModifyDataRoomPermissionsParams(
            dataroomId,
            Map(
              cm.userId -> DataRoomPermissionChanges(
                roleSet = Some(Member(canInvite = true)),
                assetPermissions = AssetPermissionChanges(
                  folderPermissions = Map.empty,
                  filePermissions = Map.empty,
                  recursivePermissions =
                    Map(FolderId.channelSystemFolderId(dataroomId) -> Some(FileFolderPermission.Read))
                )
              )
            )
          ),
          ic.userId
        )
        validateFolderPermissionOfCM <-
          fileService
            .validateFolder(cm.userId, FileFolderPermission.Write)(FolderId.channelSystemFolderId(dataroomId))
            .either
        roleMap <- getRoleMap(dataroomId)
      } yield {
        assertTrue(
          validateFolderPermissionOfCM.isLeft,
          roleMap.size == 2,
          roleMap.get(cm.userId).contains(Member(canInvite = true))
        )
      }
    },
    test("CM can not invite CC to data room as an admin") {
      for {
        result <- dataRoomParticipantService
          .inviteUsers(
            InviteUsersToDataRoomParams(
              dataroomId,
              Map(
                userCCEmail -> DataRoomPermissionChanges(
                  roleSet = Some(Admin()),
                  AssetPermissionChanges(
                    folderPermissions = Map.empty,
                    filePermissions = Map.empty,
                    recursivePermissions = Map.empty
                  )
                )
              ),
              isToaRequired = true,
              subject = "",
              message = "",
              buttonLabel = ""
            ),
            cm
          )
          .either
      } yield assertTrue(result.isLeft)

    },
    test("CM can not invite CC to data room can write to folder, folder permission will be down to Read") {
      for {
        _ <- dataRoomParticipantService.inviteUsers(
          InviteUsersToDataRoomParams(
            dataroomId,
            Map(
              userCCEmail -> DataRoomPermissionChanges(
                roleSet = Some(Member(canInvite = true)),
                AssetPermissionChanges(
                  folderPermissions = Map.empty,
                  filePermissions = Map.empty,
                  recursivePermissions =
                    Map(FolderId.channelSystemFolderId(dataroomId) -> Some(FileFolderPermission.Write))
                )
              )
            ),
            isToaRequired = true,
            subject = "",
            message = "",
            buttonLabel = ""
          ),
          cm
        )
        validateWriteFolderPermissionOfCC <-
          fileService
            .validateFolder(cc.userId, FileFolderPermission.Write)(FolderId.channelSystemFolderId(dataroomId))
            .either
        validateReadFolderPermissionOfCC <-
          fileService
            .validateFolder(cc.userId, FileFolderPermission.Read)(FolderId.channelSystemFolderId(dataroomId))
            .either
        roleMap <- getRoleMap(dataroomId)
      } yield {
        assertTrue(
          validateWriteFolderPermissionOfCC.isLeft,
          validateReadFolderPermissionOfCC.isRight,
          roleMap.size == 3,
          roleMap.get(cc.userId).contains(Member(canInvite = true))
        )
      }
    },
    test("CM can update role and folder permission of CC") {
      for {
        _ <-
          dataRoomParticipantService
            .modifyPermissions(
              ModifyDataRoomPermissionsParams(
                dataroomId,
                updatedUserMap = Map(
                  cc.userId -> DataRoomPermissionChanges(
                    roleSet = Some(Restricted()),
                    assetPermissions = AssetPermissionChanges(
                      folderPermissions = Map.empty,
                      filePermissions = Map.empty,
                      recursivePermissions = Map(
                        FolderId.channelSystemFolderId(dataroomId) -> Some(FileFolderPermission.ViewOnly)
                      )
                    )
                  )
                )
              ),
              cm.userId
            )
        validateReadFolderPermissionOfCC <-
          fileService
            .validateFolder(cc.userId, FileFolderPermission.Read)(FolderId.channelSystemFolderId(dataroomId))
            .either
        _ <-
          fileService
            .validateFolder(cc.userId, FileFolderPermission.ViewOnly)(FolderId.channelSystemFolderId(dataroomId))
        roleMap <- getRoleMap(dataroomId)
      } yield {
        assertTrue(
          validateReadFolderPermissionOfCC.isLeft,
          roleMap.size == 3,
          roleMap.get(cc.userId).contains(Restricted())
        )
      }
    },
    test("CC accept invitation to data room") {
      for {
        _ <- dataRoomParticipantService.acceptInvitation(
          AcceptInvitationToDataRoomParams(dataroomId, None),
          AuthenticatedRequestContext(
            cc,
            Seq(),
            Seq()
          )
        )
      } yield assertCompletes
    },
    test("CM unable to update folder permission of CC after accepted") {
      for {
        res <-
          dataRoomParticipantService
            .modifyPermissions(
              ModifyDataRoomPermissionsParams(
                dataroomId,
                updatedUserMap = Map(
                  cc.userId -> DataRoomPermissionChanges(
                    roleSet = Some(Guest()),
                    assetPermissions = AssetPermissionChanges(
                      folderPermissions = Map.empty,
                      filePermissions = Map.empty,
                      recursivePermissions = Map(
                        FolderId.channelSystemFolderId(dataroomId) -> Some(FileFolderPermission.Read)
                      )
                    )
                  )
                )
              ),
              cm.userId
            )
            .either
        validateOwnFolderPermissionOfCC <-
          fileService
            .validateFolder(cc.userId, FileFolderPermission.Read)(FolderId.channelSystemFolderId(dataroomId))
            .either
        _ <-
          fileService
            .validateFolder(cc.userId, FileFolderPermission.ViewOnly)(FolderId.channelSystemFolderId(dataroomId))
        roleMap <- getRoleMap(dataroomId)
      } yield {
        assertTrue(
          res.isLeft,
          validateOwnFolderPermissionOfCC.isLeft,
          roleMap.size == 3,
          roleMap.get(cc.userId).contains(Restricted())
        )
      }
    },
    test("IC update folder permission of CC to empty") {
      for {
        _ <- dataRoomParticipantService.modifyPermissions(
          ModifyDataRoomPermissionsParams(
            dataroomId,
            updatedUserMap = Map(
              cc.userId -> DataRoomPermissionChanges(
                roleSet = None,
                assetPermissions = AssetPermissionChanges(
                  folderPermissions = Map.empty,
                  filePermissions = Map.empty,
                  recursivePermissions = Map(FolderId.channelSystemFolderId(dataroomId) -> None)
                )
              )
            )
          ),
          ic.userId
        )
        validateWriteFolderPermissionOfCC <-
          fileService
            .validateFolder(cc.userId, FileFolderPermission.Write)(FolderId.channelSystemFolderId(dataroomId))
            .either
        validateReadFolderPermissionOfCC <-
          fileService
            .validateFolder(cc.userId, FileFolderPermission.Read)(FolderId.channelSystemFolderId(dataroomId))
            .either
        roleMap <- getRoleMap(dataroomId)
      } yield {
        assertTrue(
          validateWriteFolderPermissionOfCC.isLeft,
          validateReadFolderPermissionOfCC.isLeft,
          roleMap.size == 3,
          roleMap.get(cc.userId).contains(Restricted())
        )
      }
    }
  )

  private def archiverSuite = suite("Data Room archive integ test")(
    test("Non admin user should not archive data room") {
      for {
        result <- dataRoomService
          .setIsArchivedDataRoom(
            SetIsArchivedDataRoomParams(
              dataroomId,
              isArchived = true,
              doNotNotifyByEmail = true
            ),
            cm,
            None
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("Admin should be able to archive data room") {
      for {
        _ <- dataRoomService.setIsArchivedDataRoom(
          SetIsArchivedDataRoomParams(
            dataroomId,
            isArchived = true,
            doNotNotifyByEmail = true
          ),
          ic,
          None
        )
        createdState <- getCreatedState(dataroomId)
      } yield {
        assertTrue(createdState.isArchived == true)
      }
    },
    test("Should not invite new people to archived data room") {
      for {
        result <- dataRoomParticipantService
          .inviteUsers(
            InviteUsersToDataRoomParams(
              dataroomId,
              Map(
                userBM1Email -> DataRoomPermissionChanges(
                  roleSet = Some(Admin()),
                  AssetPermissionChanges(
                    folderPermissions = Map.empty,
                    filePermissions = Map.empty,
                    recursivePermissions = Map.empty
                  )
                )
              ),
              isToaRequired = true,
              subject = "",
              message = "",
              buttonLabel = ""
            ),
            ic
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("Should not modify user permission in archived data room") {
      for {
        result <- dataRoomParticipantService
          .modifyPermissions(
            ModifyDataRoomPermissionsParams(
              dataroomId,
              Map(
                cc.userId -> DataRoomPermissionChanges(
                  roleSet = Some(Admin()),
                  AssetPermissionChanges(
                    folderPermissions = Map.empty,
                    filePermissions = Map.empty,
                    recursivePermissions = Map.empty
                  )
                )
              )
            ),
            ic.userId
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("Admin should be able to un-archive data room") {
      for {
        _ <- dataRoomService.setIsArchivedDataRoom(
          SetIsArchivedDataRoomParams(
            dataroomId,
            isArchived = false,
            doNotNotifyByEmail = true
          ),
          ic,
          None
        )
        createdState <- getCreatedState(dataroomId)
      } yield {
        assertTrue(createdState.isArchived == false)
      }
    },
    test("Should be able to modify user permission data room after un-archiving") {
      for {
        _ <- dataRoomParticipantService.modifyPermissions(
          ModifyDataRoomPermissionsParams(
            dataroomId,
            Map(
              cc.userId -> DataRoomPermissionChanges(
                roleSet = Some(Admin()),
                AssetPermissionChanges(
                  folderPermissions = Map.empty,
                  filePermissions = Map.empty,
                  recursivePermissions = Map(
                    FolderId.channelSystemFolderId(dataroomId) -> Some(FileFolderPermission.Own)
                  )
                )
              )
            )
          ),
          ic.userId
        )
      } yield assertCompletes
    }
  )

  private def watermarkSuite = suite("Watermark integ")(
    test("Create new data room with watermark") {
      val watermarkMetadataParams = WatermarkMetadataParams(
        "Confidential",
        WatermarkColor.Red,
        WatermarkLayout.Subtle,
        WatermarkTransparency.Fifty
      )
      for {
        resp <- dataRoomService.createDataRoom(
          CreateDataRoomParams(
            "Data Room with watermark",
            entityId,
            Some(watermarkMetadataParams),
            showIndex = false
          ),
          AuthenticatedRequestContext(
            ic,
            Seq(),
            Seq()
          )
        )
        _ <- ZIO.attempt { dataroomId = resp.dataRoomWorkflowId }
        createdState <- getCreatedState(dataroomId)
      } yield {
        assertTrue(
          createdState.watermarkMetadata.isDefined,
          createdState.watermarkMetadata.map(_.text).contains(watermarkMetadataParams.text),
          createdState.watermarkMetadata.map(_.color).contains(watermarkMetadataParams.color.value)
        )
      }
    },
    test("Disable watermark") {
      for {
        _ <- dataRoomService.modifyGeneralSettings(
          ModifyDataRoomGeneralSettingsParams(
            dataroomId,
            None,
            None,
            isWatermarkMetadataChanged = true,
            watermarkMetadata = None,
            showIndex = None
          ),
          AuthenticatedRequestContext(
            ic,
            Seq(),
            Seq()
          )
        )
        createdState <- getCreatedState(dataroomId)
      } yield { assertTrue(createdState.watermarkMetadata.isEmpty) }
    },
    test("Turn on show index") {
      for {
        _ <- dataRoomService.modifyGeneralSettings(
          ModifyDataRoomGeneralSettingsParams(
            dataroomId,
            None,
            None,
            watermarkMetadata = None,
            showIndex = Some(true)
          ),
          AuthenticatedRequestContext(
            ic,
            Seq(),
            Seq()
          )
        )
        createdState <- getCreatedState(dataroomId)
      } yield { assertTrue(createdState.showIndex == true) }
    },
    test("Invite CM to data room") {
      for {
        _ <- dataRoomParticipantService.inviteUsers(
          InviteUsersToDataRoomParams(
            dataroomId,
            individualPermissionMap = Map(
              userCMEmail -> DataRoomPermissionChanges(
                roleSet = Some(Member(canInvite = true)),
                assetPermissions = AssetPermissionChanges(
                  folderPermissions = Map.empty,
                  filePermissions = Map.empty,
                  recursivePermissions = Map.empty
                )
              )
            ),
            isToaRequired = true,
            subject = "",
            message = "",
            buttonLabel = ""
          ),
          ic
        )
      } yield assertCompletes
    },
    test("CM accept invitation to data room") {
      for {
        _ <- dataRoomParticipantService.acceptInvitation(
          AcceptInvitationToDataRoomParams(dataroomId, None),
          AuthenticatedRequestContext(
            cm,
            Seq(),
            Seq()
          )
        )
      } yield assertCompletes
    },
    test("Non-admin user should not change watermark setting") {
      for {
        result <- dataRoomService
          .modifyGeneralSettings(
            ModifyDataRoomGeneralSettingsParams(
              dataroomId,
              None,
              None,
              isWatermarkMetadataChanged = true,
              watermarkMetadata = Some(
                WatermarkMetadataParams(
                  "Test",
                  WatermarkColor.Gray,
                  WatermarkLayout.Subtle,
                  WatermarkTransparency.Fifty
                )
              ),
              showIndex = None
            ),
            AuthenticatedRequestContext(
              cm,
              Seq(),
              Seq()
            )
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("Non-admin user should not change show index setting") {
      for {
        result <- dataRoomService
          .modifyGeneralSettings(
            ModifyDataRoomGeneralSettingsParams(
              dataroomId,
              None,
              None,
              watermarkMetadata = None,
              showIndex = Some(true)
            ),
            AuthenticatedRequestContext(
              cm,
              Seq(),
              Seq()
            )
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("IC upload a file to data room") {
      for {
        fileId <- addNewFile(
          ic,
          FolderId.channelSystemFolderId(dataroomId),
          FileFolderPermissionMap(
            userPermissions = Map(
              ic.userId -> FileFolderPermission.Own,
              cm.userId -> FileFolderPermission.Read
            )
          )
        )
      } yield {
        randomFileId = fileId
        assertCompletes
      }
    },
    test("Enable watermark again") {
      val watermarkMetadataParams = WatermarkMetadataParams(
        "Cozy",
        WatermarkColor.Gray,
        WatermarkLayout.Subtle,
        WatermarkTransparency.Fifty
      )
      for {
        _ <- dataRoomService.modifyGeneralSettings(
          ModifyDataRoomGeneralSettingsParams(
            dataroomId,
            None,
            None,
            isWatermarkMetadataChanged = true,
            watermarkMetadata = Some(watermarkMetadataParams),
            showIndex = None
          ),
          AuthenticatedRequestContext(
            ic,
            Seq(),
            Seq()
          )
        )
        createdState <- getCreatedState(dataroomId)
      } yield {
        assertTrue(
          createdState.watermarkMetadata.isDefined,
          createdState.watermarkMetadata.map(_.text).contains(watermarkMetadataParams.text),
          createdState.watermarkMetadata.map(_.color).contains(watermarkMetadataParams.color.value)
        )
      }
    },
    test("CM as Read permission should get watermarked file") {
      for {
        downloadResponse <-
          dataRoomFileService
            .getViewUrl(
              GetViewUrlParams(
                randomFileId,
                None,
                forceGetWatermark = false
              ),
              AuthenticatedRequestContext(
                cm,
                Seq.empty,
                Seq.empty
              )
            )
        downloadSource <- downloadFile(downloadResponse.url)
        downloadedContent <- getContent(downloadSource)

        inputSource <- getResource(pdfPath)
        inputDocStorageId = s3Service.generateRandomStorageId(prefixIdOpt = None, "normal.pdf")
        _ <- s3Service.uploadStreamToS3(
          inputDocStorageId,
          cm.userId,
          inputSource,
          "application/pdf",
          s3Config.bucket
        )
        watermarkStorageId = s3Service.generateRandomStorageId(prefixIdOpt = None, "normal_watermark.pdf")
        _ <- watermarkGeneratorServerless.generateWatermark(
          WatermarkGeneratorRequest(
            inputDocStorageId.id,
            watermarkStorageId.id,
            s"Cozy  -  $userCMEmail  -  ",
            WatermarkColor.Gray.value,
            0.5.toFloat,
            WatermarkLayoutMessage.Subtle.name,
            ServerlessUtils.getS3Access()
          )
        )
        outputContent <- ZIO.scoped {
          s3Service.downloadFromS3Scoped(s3Config.bucket, watermarkStorageId.id).flatMap(_.runCollect)
        }
      } yield {
        assertTrue(
          downloadedContent.length - outputContent.length < 100,
          downloadedContent.length - outputContent.length > -100
        )
      }
    }
  )

  private def termOfAccessSuite = suite("Term of Access integ")(
    test("Enable term of access") {
      for {
        _ <- uploadTermOfAccess(ic, dataroomId)
        createdState <- getCreatedState(dataroomId)
      } yield {
        val options = createdState.termsOfAccessOptions.get
        toaFileId = options.versions.lastOption
          .getOrElse(throw new RuntimeException("Missing term of access file"))
        assertTrue(
          options.isEnabled == true && options.versions.size == 1,
          createdState.termsOfAccessCertificates.size == 1
        )
      }
    },
    test("IC invites CC") {
      for {
        _ <- dataRoomParticipantService.inviteUsers(
          InviteUsersToDataRoomParams(
            dataroomId,
            individualPermissionMap = Map(
              userCCEmail -> DataRoomPermissionChanges(
                roleSet = Some(Admin()),
                assetPermissions = AssetPermissionChanges(
                  folderPermissions = Map.empty,
                  filePermissions = Map.empty,
                  recursivePermissions = Map(
                    FolderId.channelSystemFolderId(dataroomId) -> Some(FileFolderPermission.Own)
                  )
                )
              )
            ),
            isToaRequired = true,
            subject = "",
            message = "",
            buttonLabel = ""
          ),
          ic
        )
      } yield assertCompletes
    },
    test("CC can't rename data room because hasn't accepted invitation and ToA") {
      for {
        result <- dataRoomService
          .modifyGeneralSettings(
            ModifyDataRoomGeneralSettingsParams(
              dataroomId,
              None,
              Some("Nothing"),
              isWatermarkMetadataChanged = false,
              None,
              showIndex = None
            ),
            AuthenticatedRequestContext(
              cc,
              Seq(),
              Seq()
            )
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("CC accepts invitation with ToA") {
      val ctx = AuthenticatedRequestContext(
        cc,
        Seq(),
        Seq()
      )
      for {
        _ <- dataRoomFileService.getTermsOfAccessUrl(
          GetTermsOfAccessUrl(
            toaFileId = toaFileId,
            linkIdOpt = None,
            purpose = DmsTrackingActivityType.Download
          ),
          ctx
        )
        _ <- dataRoomParticipantService.acceptInvitation(
          AcceptInvitationToDataRoomParams(dataroomId, Some(toaFileId)),
          ctx
        )
      } yield assertCompletes
    },
    test("CC can rename data room after accepting ToA") {
      for {
        _ <- dataRoomService.modifyGeneralSettings(
          ModifyDataRoomGeneralSettingsParams(
            dataroomId,
            None,
            Some("Some"),
            isWatermarkMetadataChanged = false,
            None,
            showIndex = None
          ),
          AuthenticatedRequestContext(
            cc,
            Seq(),
            Seq()
          )
        )
        createdState <- getCreatedState(dataroomId)
      } yield {
        assertTrue(createdState.name == "Some")
      }
    },
    test("CM should not get pdf url of a file before accepting ToA") {
      for {
        result <-
          dataRoomFileService
            .getViewUrl(
              GetViewUrlParams(
                randomFileId,
                None,
                forceGetWatermark = false
              ),
              AuthenticatedRequestContext(
                cm,
                Seq.empty,
                Seq.empty
              )
            )
            .either
      } yield assertTrue(result.isLeft)
    },
    test("Whitelist CM from ToA") {
      for {
        _ <- dataRoomTermsOfAccessService.setDataRoomTermsOfAccessWhitelistedUsers(
          SetDataRoomTermsOfAccessWhitelistedUsersParams(
            dataroomId,
            Set(cm.userId)
          ),
          ic.userId
        )
      } yield assertCompletes
    },
    test("CM should get pdf url of a file after being whitelisted from ToA") {
      for {
        _ <- dataRoomFileService.getViewUrl(
          GetViewUrlParams(
            randomFileId,
            None,
            forceGetWatermark = false
          ),
          AuthenticatedRequestContext(
            cm,
            Seq.empty,
            Seq.empty
          )
        )
      } yield assertCompletes
    },
    test("Remove CM from ToA whitelist") {
      for {
        _ <- dataRoomTermsOfAccessService.setDataRoomTermsOfAccessWhitelistedUsers(
          SetDataRoomTermsOfAccessWhitelistedUsersParams(
            dataroomId,
            Set()
          ),
          ic.userId
        )
      } yield assertCompletes
    },
    test("CM can't get pdf url of a file before removed from whitelist") {
      for {
        result <- dataRoomFileService
          .getViewUrl(
            GetViewUrlParams(
              randomFileId,
              None,
              forceGetWatermark = false
            ),
            AuthenticatedRequestContext(
              cm,
              Seq.empty,
              Seq.empty
            )
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("CM accept ToA") {
      for {
        _ <- dataRoomFileService.getTermsOfAccessUrl(
          GetTermsOfAccessUrl(
            toaFileId = toaFileId,
            linkIdOpt = None,
            purpose = DmsTrackingActivityType.Download
          ),
          AuthenticatedRequestContext(
            cm,
            Seq(),
            Seq()
          )
        )
        _ <- dataRoomTermsOfAccessService.acceptTermsOfAccess(
          AcceptTermsOfAccessToDataRoomParams(
            dataroomId,
            toaFileId,
            linkIdOpt = None
          ),
          cm,
          None
        )
        createdState <- getCreatedState(dataroomId)
      } yield {
        val certi = createdState.termsOfAccessCertificates.filter(_.userId == cm.userId)
        assertTrue(
          createdState.termsOfAccessCertificates.size == 3,
          certi.size == 1,
          certi.headOption.map(_.toaFileId).contains(toaFileId)
        )
      }
    },
    test("CM can get pdf url of a file after accepting ToA") {
      for {
        _ <-
          dataRoomFileService
            .getViewUrl(
              GetViewUrlParams(
                randomFileId,
                None,
                forceGetWatermark = false
              ),
              AuthenticatedRequestContext(
                cm,
                Seq.empty,
                Seq.empty
              )
            )
      } yield assertCompletes
    },
    test("Uploading new version of term of access") {
      for {
        _ <- uploadTermOfAccess(ic, dataroomId)
        createdState <- getCreatedState(dataroomId)
      } yield {
        val options = createdState.termsOfAccessOptions.get
        toaFileId = options.versions.lastOption
          .getOrElse(throw new RuntimeException("Missing term of access file"))

        assertTrue(
          options.isEnabled == true && options.versions.size == 2
        )
      }
    },
    test("CM should not get pdf url of a file before accepting the new ToA") {
      for {
        result <-
          dataRoomFileService
            .getViewUrl(
              GetViewUrlParams(
                randomFileId,
                None,
                forceGetWatermark = false
              ),
              AuthenticatedRequestContext(
                cm,
                Seq.empty,
                Seq.empty
              )
            )
            .either
      } yield assertTrue(result.isLeft)
    },
    test("CU cannot view term of access without invitation link") {
      for {
        result <- dataRoomFileService
          .getTermsOfAccessUrl(
            GetTermsOfAccessUrl(toaFileId, linkIdOpt = None, DmsTrackingActivityType.View),
            AuthenticatedRequestContext(
              cu,
              Seq.empty,
              Seq.empty
            )
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("IC create invitation link with terms of access") {
      for {
        resp <- dataRoomProtectedLinkService.createDataRoomLinkInvitation(
          params = CreateDataRoomLinkInvitationParams(
            dataRoomWorkflowId = dataroomId,
            data = DataRoomLinkInvitationParamsData(
              name = "Terms of Access invitation link",
              role = anduin.dataroom.role.Guest()
            ),
            password = None,
            enableEnterpriseLogin = false
          ),
          actor = ic,
          httpContext = Some(
            AuthenticatedRequestContext(
              ic,
              Seq.empty,
              Seq.empty
            )
          )
        )
      } yield {
        invitationLinkId = resp.linkId
        assertCompletes
      }
    },
    test("CU can view term of access with invitation link") {
      dataRoomFileService
        .getTermsOfAccessUrl(
          GetTermsOfAccessUrl(
            toaFileId,
            linkIdOpt = Some(invitationLinkId),
            DmsTrackingActivityType.View
          ),
          AuthenticatedRequestContext(
            cu,
            Seq.empty,
            Seq.empty
          )
        )
        .as(assertCompletes)
    }
  )

  private def duplicatingDRSuite = suite("Duplicating data room integ")(
    test("Duplicate data room") {
      for {
        resp <- dataRoomService.duplicateDataRoom(
          params = DuplicateDataRoomParams(
            fromDataRoomWorkflowId = dataroomId,
            newName = duplicatedDataRoomName
          ),
          AuthenticatedRequestContext(
            ic,
            Seq(),
            Seq()
          )
        )
        newDataroomId = resp.dataRoomWorkflowId
        orgCreatedState <- getCreatedState(newDataroomId)
        newCreatedState <- getCreatedState(newDataroomId)
        (orgFileIds, orgFolderIds) <- getFilesAndFolders(FolderId.channelSystemFolderId(dataroomId), ic.userId)
        (newFileIds, newFolderIds) <- getFilesAndFolders(FolderId.channelSystemFolderId(newDataroomId), ic.userId)
        roleMap <- getRoleMap(newDataroomId)
      } yield {
        assertTrue(
          newCreatedState.name == duplicatedDataRoomName,
          roleMap.size == 1,
          roleMap == Map(
            ic.userId -> Admin()
          ),
          newCreatedState.creatorEntityId == entityId,
          newFileIds.size == orgFileIds.size,
          newFolderIds.size == orgFolderIds.size,
          //
          orgCreatedState.termsOfAccessOptions.map(_.versions.nonEmpty) == newCreatedState.termsOfAccessOptions
            .map(_.versions.nonEmpty),
          orgCreatedState.termsOfAccessOptions.map(_.isEnabled) == newCreatedState.termsOfAccessOptions.map(
            _.isEnabled
          ),
          orgCreatedState.showIndex == newCreatedState.showIndex,
          orgCreatedState.showHomePage == newCreatedState.showHomePage,
          orgCreatedState.showWhiteLabel == newCreatedState.showWhiteLabel,
          orgCreatedState.watermarkMetadata == newCreatedState.watermarkMetadata
        )
      }
    }
  )

  private def getFilesAndFolders(rootFolderId: FolderId, actor: UserId): Task[(List[FileId], List[FolderId])] = {
    FDBRecordDatabase.transact(
      FDBOperations[(FileStateStoreOperations, FolderStateStoreOperations)].Production
    ) { case (fileOps, folderOps) =>
      for {
        fileIds <- fileOps.getFileIds(
          actor = actor,
          folderId = rootFolderId
        )
        folderIds <- folderOps.getSubfolderIds(
          actor = actor,
          folderId = rootFolderId
        )
      } yield (fileIds, folderIds)
    }
  }

  private def getCreatedState(dataroomId: DataRoomWorkflowId) = {
    FDBRecordDatabase.transact(DataRoomStateStoreProvider.Production) {
      DataRoomStateStoreOperations(_).getState(dataroomId)
    }
  }

  private def getRoleMap(dataRoomId: DataRoomWorkflowId): Task[Map[UserId, DataRoomRole]] = {
    DataRoomParticipantRoleOperations.transact(
      _.getAllParticipantRoleMap(dataRoomId)
    )
  }

  private def uploadTermOfAccess(
    actor: ServiceActor,
    dataRoomId: DataRoomWorkflowId
  ) = {
    for {
      tempFolderId <- fileService.createUserTemporaryFolderIfNeeded(actor.userId)
      tempToaFileId <- fileService.uploadFile(
        tempFolderId,
        "name",
        FileContentOrigin.FromSource(
          ZStreamIOUtils.fromResource(pdfPath),
          MediaType.ApplicationPdf
        ),
        actor.userId
      )
      resp <- dataRoomService.modifyGeneralSettings(
        ModifyDataRoomGeneralSettingsParams(
          dataRoomId,
          Some(ModifyDataRoomTermsOfAccessTypes.Upload(tempToaFileId)),
          None,
          isWatermarkMetadataChanged = false,
          None,
          showIndex = None
        ),
        AuthenticatedRequestContext(
          actor,
          Seq(),
          Seq()
        )
      )
    } yield resp
  }

  private def addNewFile(actor: ServiceActor, folderId: FolderId, permissionMap: FileFolderPermissionMap) = {
    fileService.uploadFile(
      parentFolderId = folderId,
      fileName = "doc.pdf",
      content = FileContentOrigin.FromSource(ZStreamIOUtils.fromResource(pdfPath), MediaType.ApplicationPdf),
      uploader = actor.userId,
      permissionOpt = Some(permissionMap)
    )
  }

  private def getResource(path: String) = ZIO.attempt(ZStreamIOUtils.fromResource(path, getClass))

  private def downloadFile(url: String) = {
    zio.ZIO.attempt {
      ZStreamIOUtils.fromInputStream(new URI(url).toURL.openStream())
    }
  }

  private def getContent(source: Stream[Throwable, Byte]): Task[Array[Byte]] = {
    ZStreamIOUtils.toByteArray(source)
  }

}
