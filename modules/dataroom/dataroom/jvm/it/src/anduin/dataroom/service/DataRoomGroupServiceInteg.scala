// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service

import java.time.LocalDate

import net.datafaker.Faker
import sttp.model.MediaType

import anduin.account.protocol.BifrostCommonProtocol.CaptchaResponse
import anduin.dataroom.flow.{DataRoomModelStoreProvider, DataRoomStateStoreOperations}
import anduin.dataroom.participant.{
  DataRoomParticipantOperations,
  DataRoomParticipantRoleOperations,
  DataRoomParticipantStoreProvider
}
import anduin.dms.DmsFeature
import anduin.dms.DmsTreeTraversalOperations
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.id.dataroom.DataRoomGroupId
import anduin.id.entity.EntityId
import anduin.id.link.ProtectedLinkId
import anduin.link.{RequestAccessParams, RequestAccessResponseStatus}
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{FileId, FolderId, TeamId}
import anduin.protobuf.UserTypeMessage.RegisteredUser
import anduin.protobuf.dataroom.link.DataRoomLinkInvitationParamsData
import anduin.protobuf.flow.file.{FileFolderPermission, FileFolderPermissionMap}
import anduin.protobuf.orgbilling.dataroom.DataRoomBusiness
import anduin.service.{AuthenticatedRequestContext, ServiceActor}
import anduin.storageservice.common.FileContentOrigin
import anduin.utils.stream.ZStreamIOUtils
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import anduin.testing.DataRoomBaseInteg
import anduin.entity.EntityTestUtils
import java.util.Locale

import zio.{Task, ZIO}

import anduin.dataroom.group.*
import anduin.dataroom.role.*
import anduin.stargazer.service.dataroom.*
import anduin.testing.{GondorCoreIntegUtils, RandomUtils}
import zio.test.*

object DataRoomGroupServiceInteg extends DataRoomBaseInteg with GondorCoreIntegUtils {

  override lazy val dmsFeature: DmsFeature = DmsFeature.DataRoom

  private val faker = new Faker(Locale.US)

  private var adminUserId = UserId.defaultValue.get // scalafix:ok
  private var member1UserId = UserId.defaultValue.get // scalafix:ok
  private var member2UserId = UserId.defaultValue.get // scalafix:ok
  private var member3UserId = UserId.defaultValue.get // scalafix:ok
  private var observer1UserId = UserId.defaultValue.get // scalafix:ok
  private var observer2UserId = UserId.defaultValue.get // scalafix:ok
  private var restricted1UserId = UserId.defaultValue.get // scalafix:ok
  private var restricted2UserId = UserId.defaultValue.get // scalafix:ok
  private var restricted3UserId = UserId.defaultValue.get // scalafix:ok
  private var restricted4UserId = UserId.defaultValue.get // scalafix:ok
  // scalafix:off DisableSyntax.var
  private var entityId: EntityId = scala.compiletime.uninitialized
  private var dataRoomId: DataRoomWorkflowId = scala.compiletime.uninitialized
  private var adminGroupId: DataRoomGroupId = scala.compiletime.uninitialized
  private var group1Id: DataRoomGroupId = scala.compiletime.uninitialized
  private var group2Id: DataRoomGroupId = scala.compiletime.uninitialized
  private var group3Id: DataRoomGroupId = scala.compiletime.uninitialized

  private var group1TeamId: TeamId = scala.compiletime.uninitialized

  private var adminUserContext: AuthenticatedRequestContext = scala.compiletime.uninitialized

  var a1FolderId: FolderId = scala.compiletime.uninitialized
  var a1b1FolderId: FolderId = scala.compiletime.uninitialized
  var a1b1c1FolderId: FolderId = scala.compiletime.uninitialized
  var a1b1c2FolderId: FolderId = scala.compiletime.uninitialized
  var a1b2FolderId: FolderId = scala.compiletime.uninitialized

  var a1f1FileId: FileId = scala.compiletime.uninitialized

  var group1LinkId: ProtectedLinkId = scala.compiletime.uninitialized

  val pdfPath = "/documents/normal.pdf"

  override def spec = suite("DataRoomGroupServiceInteg")(
    setupSuite,
    integSuite
  ) @@ TestAspect.sequential

  private def setupSuite = suite("Setup test context")(
    test("Rebuild indexes") {
      for {
        _ <- FDBRecordDatabase
          .buildIndexes(
            DataRoomParticipantStoreProvider.Production,
            DataRoomGroupEventStoreProvider.Production,
            DataRoomGroupStateStoreProvider.Production,
            DataRoomModelStoreProvider.Production
          )
      } yield assertCompletes
    },
    test("Set up test users") {
      for {
        _ <- createTestUser(
          firstName = faker.name().firstName(),
          lastName = faker.name().lastName()
        ).map(adminUserId = _)
        _ <- createTestUser(
          firstName = faker.name().firstName(),
          lastName = faker.name().lastName()
        ).map(member1UserId = _)
        _ <- createTestUser(
          firstName = faker.name().firstName(),
          lastName = faker.name().lastName()
        ).map(member2UserId = _)
        _ <- createTestUser(
          firstName = faker.name().firstName(),
          lastName = faker.name().lastName()
        ).map(member3UserId = _)
        _ <- createTestUser(
          firstName = faker.name().firstName(),
          lastName = faker.name().lastName()
        ).map(observer1UserId = _)
        _ <- createTestUser(
          firstName = faker.name().firstName(),
          lastName = faker.name().lastName()
        ).map(observer2UserId = _)
        _ <- createTestUser(
          firstName = faker.name().firstName(),
          lastName = faker.name().lastName()
        ).map(restricted1UserId = _)
        _ <- createTestUser(
          firstName = faker.name().firstName(),
          lastName = faker.name().lastName()
        ).map(restricted2UserId = _)
        _ <- createTestUser(
          firstName = faker.name().firstName(),
          lastName = faker.name().lastName()
        ).map(restricted3UserId = _)
        _ <- createTestUser(
          firstName = faker.name().firstName(),
          lastName = faker.name().lastName()
        ).map(restricted4UserId = _)
        adminCtx <- getAuthenticatedContext(adminUserId)
      } yield {
        adminUserContext = adminCtx
        assertCompletes
      }
    },
    test("Create entity") {
      for {
        id <- EntityTestUtils.createEntity(
          name = "DataRoomParticipan Integ Org",
          alias = "DPIO",
          creator = adminUserId
        )
      } yield {
        entityId = id
        assertCompletes
      }
    },
    test("Set premium package for entity") {
      for {
        _ <- orgBillingService
          .changeDataRoomPlan(
            entityId,
            DataRoomBusiness(expDate = LocalDate.ofEpochDay(LocalDate.now().toEpochDay + 100).toEpochDay),
            adminUserId
          )
      } yield assertCompletes
    },
    test("Create a new Data Room") {
      for {
        resp <- dataRoomService.createDataRoom(
          CreateDataRoomParams(
            "Test participant DR",
            entityId,
            None,
            None,
            showIndex = false
          ),
          adminUserContext
        )
      } yield {
        dataRoomId = resp.dataRoomWorkflowId
        assertCompletes
      }
    },
    test("Set up files and folders") {
      for {
        a1 <- ZIO.attempt(FolderId.channelSystemFolderId(dataRoomId))
        a1b1 <- fileService.createEmptyFolder(
          a1,
          "a1b1",
          adminUserId,
          setAuthorAsOwner = true
        )
        a1b1c1 <- fileService.createEmptyFolder(
          a1b1,
          "a1b1c1",
          adminUserId,
          setAuthorAsOwner = true
        )
        a1b1c2 <- fileService.createEmptyFolder(
          a1b1,
          "a1b1c2",
          adminUserId,
          setAuthorAsOwner = true
        )
        a1b2 <- fileService.createEmptyFolder(
          a1,
          "a1b2",
          adminUserId,
          setAuthorAsOwner = true
        )
        a1f1 <- addNewFile(
          adminUserContext.actor,
          a1,
          FileFolderPermissionMap(
            userPermissions = Map(
              adminUserId -> FileFolderPermission.Own
            )
          )
        )
      } yield {
        a1FolderId = a1
        a1b1FolderId = a1b1
        a1b1c1FolderId = a1b1c1
        a1b1c2FolderId = a1b1c2
        a1b2FolderId = a1b2
        a1f1FileId = a1f1
        assertCompletes
      }
    }
  )

  private def createGroupSuite = {

    def createGroupTask(
      actor: UserId,
      name: String,
      role: DataRoomRole,
      asset: AssetPermissionChanges
    ): Task[CreateDataRoomGroupResponse] = {
      for {
        ctx <- getAuthenticatedContext(actor)
        resp <- dataRoomGroupService.createGroup(
          params = CreateDataRoomGroupParams(
            dataRoomWorkflowId = dataRoomId,
            name = name,
            role = role,
            assetPermissions = asset
          ),
          actor = ctx.actor.userId,
          ctx = Some(ctx)
        )
      } yield resp
    }

    suite("Create groups")(
      test("Admin can create Admin group") {
        for {
          resp <- createGroupTask(
            actor = adminUserId,
            name = "Test Admin group",
            role = Admin(),
            asset = AssetPermissionChanges.allFoldersWithRootChannel(dataRoomId, FileFolderPermission.Own)
          )
        } yield {
          adminGroupId = resp.groupId
          assertCompletes
        }
      },
      test("Admin can create Member group with Own permissions") {
        for {
          (_, folderIds) <- getFilesAndFolders(FolderId.channelSystemFolderId(dataRoomId), adminUserId)
          assetPermissions = AssetPermissionChanges(
            recursivePermissions = folderIds.map(_ -> Some(FileFolderPermission.Own)).toMap
          )
          resp <- createGroupTask(
            actor = adminUserId,
            name = "Test Member group",
            role = Member(),
            asset = assetPermissions
          )
          groupState <- dataRoomGroupService.getGroupAndMembers(resp.groupId, adminUserId).map(_._1)
        } yield {
          group1Id = resp.groupId
          group1TeamId = groupState.teamId
          assertCompletes
        }
      },
      test("Admin can create Guest group with Write permissions") {
        for {
          (_, folderIds) <- getFilesAndFolders(FolderId.channelSystemFolderId(dataRoomId), adminUserId)
          assetPermissions = AssetPermissionChanges(
            recursivePermissions = folderIds.map(_ -> Some(FileFolderPermission.Write)).toMap
          )
          resp <- createGroupTask(
            actor = adminUserId,
            name = "Guest group",
            role = Guest(),
            asset = assetPermissions
          )
        } yield {
          group2Id = resp.groupId
          assertCompletes
        }
      },
      suite("Member, observer and restricted member cannot create group")(
        test("Member cannot create group") {
          for {
            result <- createGroupTask(
              member1UserId,
              "Group name",
              Restricted(),
              AssetPermissionChanges()
            ).either
          } yield assertTrue(result.isLeft)
        },
        test("Observer cannot create group") {
          for {
            result <- createGroupTask(
              observer1UserId,
              "Group name",
              Restricted(),
              AssetPermissionChanges()
            ).either
          } yield assertTrue(result.isLeft)
        },
        test("Restricted member cannot create group") {
          for {
            result <- createGroupTask(
              restricted1UserId,
              "Group name",
              Restricted(),
              AssetPermissionChanges()
            ).either
          } yield assertTrue(result.isLeft)
        }
      )
    )

  }

  private def adminAbleToMovethemselvesSuite = suite("Admin should be able to move themselves into/out of group")(
    test("Admin should be able to move themselves into Admin group") {
      for {
        _ <- dataRoomGroupService.addUsersToGroup(
          params = AddUsersToDataRoomGroupParams(
            groupId = adminGroupId,
            userIds = Set(
              adminUserId
            )
          ),
          actor = adminUserId,
          ctx = Some(adminUserContext)
        )
        (_, members) <- dataRoomGroupService.getGroupAndMembers(
          groupId = adminGroupId,
          actor = adminUserId
        )
        (fileIds, folderIds) <- getFilesAndFolders(FolderId.channelSystemFolderId(dataRoomId), adminUserId)
        filePermissions <- ZIO.foreach(fileIds)(fileService.getFilePermission(adminUserId))
        folderPermissions <- ZIO.foreach(folderIds)(fileService.getFolderPermission(adminUserId))
      } yield {
        assertTrue(
          members == Set(adminUserId),
          filePermissions == List.fill(filePermissions.size)(Some(FileFolderPermission.Own)),
          folderPermissions == List.fill(folderPermissions.size)(Some(FileFolderPermission.Own))
        )
      }
    },
    test("Admin should keep own permission after delete Admin group") {
      for {
        _ <- dataRoomGroupService.deleteGroups(
          params = DeleteDataRoomGroupsParams(
            dataRoomId,
            Set(adminGroupId),
            DeleteDataRoomGroupsParams.ParticipantSettings.Keep
          ),
          actor = adminUserId,
          ctx = Some(adminUserContext)
        )
        (fileIds, folderIds) <- getFilesAndFolders(FolderId.channelSystemFolderId(dataRoomId), adminUserId)
        filePermissions <- ZIO.foreach(fileIds)(fileService.getFilePermission(adminUserId))
        folderPermissions <- ZIO.foreach(folderIds)(fileService.getFolderPermission(adminUserId))
      } yield {
        assertTrue(
          filePermissions == List.fill(filePermissions.size)(Some(FileFolderPermission.Own)),
          folderPermissions == List.fill(folderPermissions.size)(Some(FileFolderPermission.Own))
        )
      }
    }
  )

  private def inviteUserWithParticularGroupSuite = {
    def inviteTask(actor: UserId): Task[Unit] = {
      for {
        restricted3Info <- userProfileService.getUserInfo(restricted3UserId)
        ctx <- getAuthenticatedContext(actor)
        _ <- dataRoomParticipantService.inviteUsers(
          InviteUsersToDataRoomParams(
            dataRoomId,
            groupPermissionMap = Map(
              restricted3Info.emailAddressStr -> DataRoomGroupPermissionChanges(
                groupIds = Set(group1Id),
                canInvite = true
              )
            ),
            isToaRequired = true,
            subject = "",
            message = "",
            buttonLabel = ""
          ),
          ctx.actor,
          Some(ctx)
        )
      } yield ()
    }

    suite("Invite users to data room with particular group")(
      test("Admin member can invite users to data room with particular group") {
        for {
          member2Info <- userProfileService.getUserInfo(member2UserId)
          member3Info <- userProfileService.getUserInfo(member3UserId)
          _ <- dataRoomParticipantService.inviteUsers(
            InviteUsersToDataRoomParams(
              dataRoomId,
              groupPermissionMap = Map(
                member2Info.emailAddressStr -> DataRoomGroupPermissionChanges(
                  groupIds = Set(group1Id),
                  canInvite = true
                ),
                member3Info.emailAddressStr -> DataRoomGroupPermissionChanges(
                  groupIds = Set(group1Id),
                  canInvite = true
                )
              ),
              isToaRequired = true,
              subject = "",
              message = "",
              buttonLabel = ""
            ),
            adminUserContext.actor,
            Some(adminUserContext)
          )
          (_, members) <- dataRoomGroupService.getGroupAndMembers(
            groupId = group1Id,
            actor = adminUserId
          )
          roleMap <- DataRoomParticipantRoleOperations.transact(
            _.getAllParticipantRoleMap(dataRoomId)
          )
          participantMap <- FDBRecordDatabase.transact(
            FDBOperations[DataRoomParticipantOperations].Production
          )(_.getParticipantMap(dataRoomId))
        } yield {
          assertTrue(
            members == Set(member2UserId, member3UserId),

            // validate data room role map
            roleMap.get(member2UserId).contains(Member(canInvite = true)),
            roleMap.get(member3UserId).contains(Member(canInvite = true)),

            // validate participants data
            participantMap(member2UserId).groupIds == Set(group1Id),
            participantMap(member3UserId).groupIds == Set(group1Id)
          )
        }
      },
      suite("Member, observer and restricted member cannot invite users to data room with particular group")(
        test("Member cannot invite users to data room with particular group") {
          for {
            result <- inviteTask(member1UserId).either
          } yield assertTrue(result.isLeft)

        },
        test("Observer cannot invite users to data room with particular group") {
          for {
            result <- inviteTask(observer1UserId).either
          } yield assertTrue(result.isLeft)

        },
        test("Restricted member cannot invite users to data room with particular group") {
          for {
            result <- inviteTask(restricted1UserId).either
          } yield assertTrue(result.isLeft)
        }
      )
    )
  }

  private def addUserSuite = {
    def addTask(actor: UserId): Task[Unit] = {
      for {
        ctx <- getAuthenticatedContext(actor)
        _ <- dataRoomGroupService.addUsersToGroup(
          params = AddUsersToDataRoomGroupParams(
            groupId = group1Id,
            userIds = Set(restricted2UserId)
          ),
          actor = ctx.actor.userId,
          ctx = Some(ctx)
        )
      } yield ()
    }

    suite("Add users to group")(
      suite("Member, observer and restricted member cannot add users to group")(
        test("Member cannot add users to group") {
          for {
            result <- addTask(member1UserId).either
          } yield assertTrue(result.isLeft)
        },
        test("Observer cannot add users to group") {
          for {
            result <- addTask(observer1UserId).either
          } yield assertTrue(result.isLeft)
        },
        test("Restricted member cannot add users to group") {
          for {
            result <- addTask(restricted1UserId).either
          } yield assertTrue(result.isLeft)
        }
      ),
      test("Admin should be able to add users to group") {
        for {
          _ <- dataRoomGroupService.addUsersToGroup(
            params = AddUsersToDataRoomGroupParams(
              groupId = group1Id,
              userIds = Set(
                member1UserId,
                member2UserId,
                observer1UserId
              )
            ),
            actor = adminUserId,
            ctx = Some(adminUserContext)
          )
          (_, members) <- dataRoomGroupService.getGroupAndMembers(
            groupId = group1Id,
            actor = adminUserId
          )
          roleMap <- DataRoomParticipantRoleOperations.transact(
            _.getAllParticipantRoleMap(dataRoomId)
          )
          participantMap <- FDBRecordDatabase.transact(
            FDBOperations[DataRoomParticipantOperations].Production
          )(_.getParticipantMap(dataRoomId))
        } yield {
          assertTrue(
            // validate group members
            members == Set(
              member1UserId,
              member2UserId,
              member3UserId,
              observer1UserId
            ),
            // validate data room role map
            roleMap.get(member1UserId).contains(Member(canInvite = true)),
            roleMap.get(member2UserId).contains(Member(canInvite = true)),
            roleMap.get(member3UserId).contains(Member(canInvite = true)),
            roleMap.get(observer1UserId).contains(Member(canInvite = true)),

            // validate participant data
            participantMap(member1UserId).groupIds == Set(group1Id),
            participantMap(member2UserId).groupIds == Set(group1Id),
            participantMap(member3UserId).groupIds == Set(group1Id),
            participantMap(observer1UserId).groupIds == Set(group1Id)
          )
        }
      }
    )
  }

  private def updateGroupRoleSuite = {
    def updateRoleTask(actor: UserId): Task[Unit] = {
      for {
        ctx <- getAuthenticatedContext(actor)
        _ <- dataRoomGroupService.updateGroupPermission(
          params = UpdateDataRoomGroupPermissionsParams(
            groupId = group2Id,
            permissionChanges = DataRoomPermissionChanges(
              roleSet = Some(Restricted()),
              assetPermissions = AssetPermissionChanges()
            )
          ),
          actor = ctx.actor.userId,
          ctx = Some(ctx)
        )
      } yield ()
    }

    suite("Update group role")(
      test("Admin should be able to update group role") {
        for {
          _ <- DataRoomParticipantRoleOperations.transact(
            _.getAllParticipantRoleMap(dataRoomId)
          )
          _ <- dataRoomGroupService.updateGroupPermission(
            params = UpdateDataRoomGroupPermissionsParams(
              groupId = group2Id,
              permissionChanges =
                DataRoomPermissionChanges(roleSet = Some(Member()), assetPermissions = AssetPermissionChanges())
            ),
            actor = adminUserId,
            ctx = Some(adminUserContext)
          )
          (groupState, _) <- dataRoomGroupService.getGroupAndMembers(
            groupId = group2Id,
            actor = adminUserId
          )
          roleMap <- DataRoomParticipantRoleOperations.transact(
            _.getAllParticipantRoleMap(dataRoomId)
          )
        } yield {
          assertTrue(
            groupState.role == Member(),
            roleMap.get(member1UserId).contains(Member(canInvite = true)),
            roleMap.get(observer2UserId).contains(Member())
          )
        }
      },
      suite("Member, observer and restricted member cannot update group role")(
        test("Member cannot update group role") {
          for {
            result <- updateRoleTask(member1UserId).either
          } yield assertTrue(result.isLeft)
        },
        test("Observer cannot update group role") {
          for {
            result <- updateRoleTask(observer1UserId).either
          } yield assertTrue(result.isLeft)
        },
        test("Restricted member cannot update group role") {
          for {
            result <- updateRoleTask(restricted1UserId).either
          } yield assertTrue(result.isLeft)
        }
      )
    )
  }

  private def removeUserSuite = {
    def removeUsersFromGroupTask(actor: UserId): Task[Unit] = {
      for {
        ctx <- getAuthenticatedContext(actor)
        _ <- dataRoomGroupService.removeUsersFromGroupUnsafe(
          params = RemoveUsersFromDataRoomGroupParams(
            groupId = group1Id,
            userIds = Set(member2UserId)
          ),
          actor = ctx.actor.userId,
          ctx = Some(ctx)
        )
      } yield ()
    }

    suite("Remove users from group")(
      suite("Member, observer and restricted member cannot remove users from group")(
        test("Member cannot remove users from group") {
          for {
            result <- removeUsersFromGroupTask(member1UserId).either
          } yield assertTrue(result.isLeft)
        },
        test("Observer cannot remove users from group") {
          for {
            result <- removeUsersFromGroupTask(observer1UserId).either
          } yield assertTrue(result.isLeft)
        },
        test("Restricted member cannot remove users from group") {
          for {
            result <- removeUsersFromGroupTask(restricted1UserId).either
          } yield assertTrue(result.isLeft)
        }
      ),
      test("Admin should be able to remove users from group") {
        for {
          _ <- removeUsersFromGroupTask(adminUserId)
          (_, members) <- dataRoomGroupService.getGroupAndMembers(
            groupId = group1Id,
            actor = adminUserId
          )
          roleMap <- DataRoomParticipantRoleOperations.transact(
            _.getAllParticipantRoleMap(dataRoomId)
          )
          participantMap <- FDBRecordDatabase.transact(
            FDBOperations[DataRoomParticipantOperations].Production
          )(_.getParticipantMap(dataRoomId))
          (_, folderIds) <- getFilesAndFolders(FolderId.channelSystemFolderId(dataRoomId), adminUserId)
          userPermissions <- ZIO.foreach(folderIds)(
            fileService.getFolderPermission(member2UserId)
          )
        } yield {
          assertTrue(
            members == Set(member1UserId, observer1UserId),
            roleMap.get(member2UserId).contains(Member(canInvite = true)),
            userPermissions == List.fill(folderIds.size)(Some(FileFolderPermission.ViewOnly)),
            participantMap(member2UserId).groupIds == Set.empty[DataRoomGroupId]
          )
        }
      }
    )
  }

  private def groupInvitationLinkSuite = suite("Group invitation links")(
    test("Increase seat count") {
      for {
        _ <- orgBillingService
          .changeDataRoomPlan(
            entityId,
            DataRoomBusiness(expDate = LocalDate.ofEpochDay(LocalDate.now().toEpochDay + 100).toEpochDay, extraSeat = 3),
            adminUserId
          )
      } yield assertCompletes
    },
    test("Admin should be able to create group invitation link") {
      for {
        resp <- dataRoomProtectedLinkService.createDataRoomLinkInvitation(
          params = CreateDataRoomLinkInvitationParams(
            dataRoomWorkflowId = dataRoomId,
            data = DataRoomLinkInvitationParamsData(
              name = "Admin approval group invitation link",
              isRequiredAdminApproval = true,
              groupIds = Set(group1Id)
            ),
            password = None,
            enableEnterpriseLogin = false
          ),
          actor = adminUserContext.actor,
          httpContext = Some(adminUserContext)
        )
      } yield {
        group1LinkId = resp.linkId
        assertCompletes
      }
    },
    test("Member should be able to request access to group") {
      for {
        restricted3Info <- userProfileService.getUserInfo(restricted3UserId)
        restricted4Info <- userProfileService.getUserInfo(restricted4UserId)
        resp4 <- dataRoomProtectedLinkService.requestAccess(
          params = RequestAccessParams(
            linkId = group1LinkId,
            captchaResponse = CaptchaResponse.NoResponse,
            email = restricted4Info.emailAddressStr
          )
        )
        resp3 <- dataRoomProtectedLinkService.requestAccess(
          params = RequestAccessParams(
            linkId = group1LinkId,
            captchaResponse = CaptchaResponse.NoResponse,
            email = restricted3Info.emailAddressStr
          )
        )
      } yield {
        assertTrue(
          resp4.status == RequestAccessResponseStatus.RequestSent,
          resp3.status == RequestAccessResponseStatus.RequestSent
        )
      }
    },
    test("Admin should be able to grant access to join group") {
      for {
        restricted3Info <- userProfileService.getUserInfo(restricted3UserId)
        _ <- dataRoomProtectedLinkService.approveAccessRequests(
          params = ApproveAccessRequestsParams(
            dataRoomWorkflowId = dataRoomId,
            requests = Seq(
              DataRoomAccessRequest(
                linkId = group1LinkId,
                email = restricted3Info.emailAddressStr
              )
            )
          ),
          ctx = adminUserContext
        )
        restricted3Context <- getAuthenticatedContext(restricted3UserId)
        _ <- dataRoomParticipantService.acceptInvitation(
          params = AcceptInvitationToDataRoomParams(
            dataRoomWorkflowId = dataRoomId,
            toaFileIdOpt = None
          ),
          ctx = restricted3Context
        )
        (_, members) <- dataRoomGroupService.getGroupAndMembers(
          groupId = group1Id,
          actor = adminUserId
        )
        participantMap <- FDBRecordDatabase.transact(
          FDBOperations[DataRoomParticipantOperations].Production
        )(_.getParticipantMap(dataRoomId))
        roleMap <- DataRoomParticipantRoleOperations.transact(
          _.getAllParticipantRoleMap(dataRoomId)
        )
      } yield {
        assertTrue(
          members == Set(member1UserId, observer1UserId, restricted3UserId),
          participantMap(restricted3UserId).groupIds == Set(group1Id),
          roleMap.get(restricted3UserId).contains(Member())
        )
      }
    }
  )

  private def deleteGroupSuite = {
    def deleteGroupTask(userId: UserId): Task[Unit] = {
      for {
        ctx <- getAuthenticatedContext(userId)
        _ <- dataRoomGroupService.deleteGroups(
          params = DeleteDataRoomGroupsParams(
            dataRoomId,
            Set(group1Id),
            DeleteDataRoomGroupsParams.ParticipantSettings.Keep
          ),
          actor = ctx.actor.userId,
          ctx = Some(ctx)
        )
      } yield ()
    }

    suite("Delete groups")(
      suite("Member, observer and restricted member cannot delete group")(
        test("Member cannot delete group") {
          for {
            result <- deleteGroupTask(member1UserId).either
          } yield assertTrue(result.isLeft)
        },
        test("Observer cannot delete group") {
          for {
            result <- deleteGroupTask(observer1UserId).either
          } yield assertTrue(result.isLeft)
        },
        test("Restricted member cannot delete group") {
          for {
            result <- deleteGroupTask(restricted1UserId).either
          } yield assertTrue(result.isLeft)
        }
      ),
      test("Admin should be able to delete group") {
        for {
          _ <- deleteGroupTask(adminUserId)
          (groupState, members) <- dataRoomGroupService.getGroupAndMembers(
            groupId = group1Id,
            actor = adminUserId
          )
        } yield {
          assertTrue(
            groupState.isArchived == true,
            members.isEmpty
          )
        }

      }
    )
  }

  private def postDeleteGroupSuite = suite("Post delete group")(
    test("Cannot add members to a deleted group") {
      for {
        result <- dataRoomGroupService
          .addUsersToGroup(
            params = AddUsersToDataRoomGroupParams(
              groupId = group1Id,
              userIds = Set(member1UserId)
            ),
            actor = adminUserId,
            ctx = Some(adminUserContext)
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("Admin cannot approve access request to deleted group") {
      for {
        restricted4Info <- userProfileService.getUserInfo(restricted4UserId)
        result <- dataRoomProtectedLinkService
          .approveAccessRequests(
            params = ApproveAccessRequestsParams(
              dataRoomWorkflowId = dataRoomId,
              requests = Seq(
                DataRoomAccessRequest(
                  linkId = group1LinkId,
                  email = restricted4Info.emailAddressStr
                )
              )
            ),
            ctx = adminUserContext
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("Admin should be able to decline access request to deleted group") {
      for {
        restricted4Info <- userProfileService.getUserInfo(restricted4UserId)
        resp <- dataRoomProtectedLinkService.declineAccessRequests(
          params = DeclineAccessRequestsParams(
            dataRoomWorkflowId = dataRoomId,
            requests = Seq(
              DataRoomAccessRequest(
                linkId = group1LinkId,
                email = restricted4Info.emailAddressStr
              )
            ),
            hasNotifyEmail = false
          ),
          ctx = adminUserContext
        )
      } yield {
        assertTrue(
          resp == DataRoomEmptyResponse()
        )
      }
    },
    test("Deleted group invitation link should be disabled") {
      for {
        linkState <- protectedLinkService.getLinkState(group1LinkId)
        linkInvitation <- FDBRecordDatabase.transact(
          FDBOperations[DataRoomStateStoreOperations].Production
        ) { stateOps =>
          for {
            createdState <- stateOps.getState(dataRoomId)
            linkInvitation = createdState.linkInvitationMap(group1LinkId)
          } yield linkInvitation
        }
      } yield {
        assertTrue(
          linkState.isDisabled,
          linkInvitation.groupIds == Set(group1Id)
        )
      }
    },
    test("Admin should be able to modify group link invitation") {
      for {
        resp <- dataRoomProtectedLinkService.modifyDataRoomLinkInvitation(
          ModifyDataRoomLinkInvitationParams(
            dataRoomWorkflowId = dataRoomId,
            linkId = group1LinkId,
            data = DataRoomLinkInvitationParamsData(
              name = "Public group invitation link",
              groupIds = Set(group2Id)
            ),
            password = None,
            enableEnterpriseLoginChange = None
          ),
          actor = adminUserId,
          httpContext = Some(adminUserContext)
        )
      } yield {
        assertTrue(
          resp == DataRoomEmptyResponse()
        )
      }
    },
    test("Public user should be able to join data room via group invitation link") {
      for {
        ctx <- getAuthenticatedContext(restricted4UserId)
        _ <- dataRoomProtectedLinkService.joinDataRoomViaLinkInvitation(
          params = JoinDataRoomViaLinkInvitationParams(
            dataRoomWorkflowId = dataRoomId,
            linkId = group1LinkId,
            toaFileIdOpt = None
          ),
          ctx = ctx
        )
        (_, members) <- dataRoomGroupService.getGroupAndMembers(
          groupId = group2Id,
          actor = adminUserId
        )
        participantMap <- FDBRecordDatabase.transact(
          FDBOperations[DataRoomParticipantOperations].Production
        )(_.getParticipantMap(dataRoomId))
        roleMap <- DataRoomParticipantRoleOperations.transact(
          _.getAllParticipantRoleMap(dataRoomId)
        )
      } yield {
        assertTrue(
          members.contains(restricted4UserId) == true,
          participantMap.get(restricted4UserId).get.groupIds == Set(group2Id),
          roleMap.get(restricted4UserId).contains(Member())
        )
      }
    }
  )

  private def integSuite = suite("DataRoomGroupServiceInteg")(
    test("Invite users to data room") {
      for {
        member1Info <- userProfileService.getUserInfo(member1UserId)
        observer1Info <- userProfileService.getUserInfo(observer1UserId)
        observer2Info <- userProfileService.getUserInfo(observer2UserId)
        restricted1Info <- userProfileService.getUserInfo(restricted1UserId)
        restricted2Info <- userProfileService.getUserInfo(restricted2UserId)
        _ <- dataRoomParticipantService.inviteUsers(
          InviteUsersToDataRoomParams(
            dataRoomId,
            Map(
              member1Info.emailAddressStr -> DataRoomPermissionChanges(
                roleSet = Some(Member(canInvite = true)),
                AssetPermissionChanges()
              ),
              observer1Info.emailAddressStr -> DataRoomPermissionChanges(
                roleSet = Some(Guest(canInvite = true)),
                AssetPermissionChanges()
              ),
              observer2Info.emailAddressStr -> DataRoomPermissionChanges(
                roleSet = Some(Guest()),
                AssetPermissionChanges()
              ),
              restricted1Info.emailAddressStr -> DataRoomPermissionChanges(
                roleSet = Some(Restricted(canInvite = true)),
                AssetPermissionChanges()
              ),
              restricted2Info.emailAddressStr -> DataRoomPermissionChanges(
                roleSet = Some(Restricted()),
                AssetPermissionChanges()
              )
            ),
            isToaRequired = true,
            subject = "",
            message = "",
            buttonLabel = ""
          ),
          adminUserContext.actor,
          Some(adminUserContext)
        )
      } yield assertCompletes
    },
    test("Invited users can accept invitation") {
      for {
        member1Ctx <- getAuthenticatedContext(member1UserId)
        _ <- dataRoomParticipantService.acceptInvitation(
          AcceptInvitationToDataRoomParams(dataRoomId, None),
          member1Ctx,
          shouldSendEmail = false
        )
        observer1Ctx <- getAuthenticatedContext(observer1UserId)
        _ <- dataRoomParticipantService.acceptInvitation(
          AcceptInvitationToDataRoomParams(dataRoomId, None),
          observer1Ctx,
          shouldSendEmail = false
        )
        restricted1Ctx <- getAuthenticatedContext(restricted1UserId)
        _ <- dataRoomParticipantService.acceptInvitation(
          AcceptInvitationToDataRoomParams(dataRoomId, None),
          restricted1Ctx,
          shouldSendEmail = false
        )
      } yield assertCompletes
    },
    createGroupSuite,
    test("Check all created group") {
      for {
        allGroups <- dataRoomGroupService.getAllGroups(
          dataRoomWorkflowId = dataRoomId,
          actor = adminUserId
        )
      } yield {
        assertTrue(
          allGroups.flatMap(_.id).toSet == Set(
            adminGroupId,
            group1Id,
            group2Id
          )
        )
      }
    },
    test("Admin should be able to rename group") {
      val updatedName = "Test Member group - edited"
      for {
        _ <- dataRoomGroupService.renameGroup(
          params = RenameDataRoomGroupParams(
            groupId = group1Id,
            name = updatedName
          ),
          actor = adminUserId,
          ctx = Some(adminUserContext)
        )
        (groupState, _) <- dataRoomGroupService.getGroupAndMembers(
          groupId = group1Id,
          actor = adminUserId
        )
      } yield assertTrue(groupState.name == updatedName)
    },
    adminAbleToMovethemselvesSuite,
    inviteUserWithParticularGroupSuite,
    addUserSuite,
    test("User should be removed from group after decline data room invitation") {
      for {
        ctx <- getAuthenticatedContext(member3UserId)
        _ <- dataRoomParticipantService.declineInvitation(
          params = DeclineInvitationToDataRoomParams(dataRoomWorkflowId = dataRoomId),
          actor = ctx.actor,
          ctx = Some(ctx)
        )
        (_, members) <- dataRoomGroupService.getGroupAndMembers(
          groupId = group1Id,
          actor = adminUserId
        )
      } yield {
        assertTrue(
          // validate group members
          members == Set(
            member1UserId,
            member2UserId,
            observer1UserId
          )
        )
      }
    },
    test("Admin should be able to add users to other groups") {
      for {
        _ <- dataRoomGroupService.addUsersToGroup(
          params = AddUsersToDataRoomGroupParams(
            groupId = group2Id,
            userIds = Set(
              member1UserId,
              observer2UserId
            )
          ),
          actor = adminUserId,
          ctx = Some(adminUserContext)
        )
        (_, group1Members) <- dataRoomGroupService.getGroupAndMembers(
          groupId = group1Id,
          actor = adminUserId
        )
        (_, group2Members) <- dataRoomGroupService.getGroupAndMembers(
          groupId = group2Id,
          actor = adminUserId
        )
        roleMap <- DataRoomParticipantRoleOperations.transact(
          _.getAllParticipantRoleMap(dataRoomId)
        )
        participantMap <- FDBRecordDatabase.transact(
          FDBOperations[DataRoomParticipantOperations].Production
        )(_.getParticipantMap(dataRoomId))
      } yield {
        assertTrue(
          group1Members == Set(
            member1UserId,
            member2UserId,
            observer1UserId
          ),
          group2Members == Set(
            member1UserId,
            observer2UserId
          ),
          // validate each member role in data room state
          roleMap.get(member2UserId).contains(Member(canInvite = true)),
          roleMap.get(observer1UserId).contains(Member(canInvite = true)),
          // Member 1 is multi-group user with Member permission from group1
          roleMap.get(member1UserId).contains(Member(canInvite = true)),
          roleMap.get(observer2UserId).contains(Guest()),

          // validate participant data
          participantMap(member2UserId).groupIds == Set(group1Id),
          participantMap(observer1UserId).groupIds == Set(group1Id),
          participantMap(member1UserId).groupIds == Set(group1Id, group2Id),
          participantMap(observer2UserId).groupIds == Set(group2Id)
        )
      }
    },
    updateGroupRoleSuite,
    test("Admin should be able to update group permission") {
      for {
        (_, folderIds) <- getFilesAndFolders(FolderId.channelSystemFolderId(dataRoomId), adminUserId)
        assetPermissions = AssetPermissionChanges(
          recursivePermissions = folderIds.map(_ -> Some(FileFolderPermission.ViewOnly)).toMap
        )
        _ <- dataRoomGroupService.updateGroupPermission(
          params = UpdateDataRoomGroupPermissionsParams(
            groupId = group1Id,
            permissionChanges = DataRoomPermissionChanges(roleSet = None, assetPermissions = assetPermissions)
          ),
          actor = adminUserId,
          ctx = Some(adminUserContext)
        )
        teamPermissions <- ZIO.foreach(folderIds) {
          fileService.getFolderTeamPermission(adminUserId, group1TeamId)
        }
      } yield {
        assertTrue(
          teamPermissions == List.fill(folderIds.size)(Some(FileFolderPermission.ViewOnly))
        )
      }
    },
    test("Cannot add user to group that exceed seat limitation") {
      for {
        result <- dataRoomGroupService
          .addUsersToGroup(
            params = AddUsersToDataRoomGroupParams(
              groupId = group1Id,
              userIds = Set(
                restricted1UserId,
                restricted2UserId
              )
            ),
            actor = adminUserId,
            ctx = Some(adminUserContext)
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    suite("Cannot update group role that make the total seat exceed the limitation")(
      test("Create new external group & add members") {
        for {
          resp <- dataRoomGroupService.createGroup(
            params = CreateDataRoomGroupParams(
              dataRoomWorkflowId = dataRoomId,
              name = "External group",
              role = Restricted(),
              assetPermissions = AssetPermissionChanges()
            ),
            actor = adminUserId,
            ctx = Some(adminUserContext)
          )
          _ <- dataRoomGroupService.addUsersToGroup(
            params = AddUsersToDataRoomGroupParams(
              groupId = resp.groupId,
              userIds = Set(
                restricted1UserId,
                restricted2UserId
              )
            ),
            actor = adminUserId,
            ctx = Some(adminUserContext)
          )
        } yield {
          group3Id = resp.groupId
          assertCompletes
        }
      },
      test("Cannot update group role that make the total seat exceed limitation") {
        for {
          result <- dataRoomGroupService
            .updateGroupPermission(
              params = UpdateDataRoomGroupPermissionsParams(
                groupId = group3Id,
                permissionChanges = DataRoomPermissionChanges(
                  roleSet = Some(Member()),
                  assetPermissions = AssetPermissionChanges()
                )
              ),
              actor = adminUserId,
              ctx = Some(adminUserContext)
            )
            .either
        } yield assertTrue(result.isLeft)
      }
    ),
    removeUserSuite,
    groupInvitationLinkSuite,
    deleteGroupSuite,
    postDeleteGroupSuite
  )

  private def addNewFile(actor: ServiceActor, folderId: FolderId, permissionMap: FileFolderPermissionMap) = {
    fileService.uploadFile(
      parentFolderId = folderId,
      fileName = "doc.pdf",
      content = FileContentOrigin.FromSource(ZStreamIOUtils.fromResource(pdfPath), MediaType.ApplicationPdf),
      uploader = actor.userId,
      permissionOpt = Some(permissionMap)
    )
  }

  private def getFilesAndFolders(rootFolderId: FolderId, actor: UserId): Task[(Seq[FileId], Seq[FolderId])] = {
    FDBRecordDatabase
      .transact(
        FDBOperations[DmsTreeTraversalOperations].Production
      )(_.getAllFoldersAndFiles(actor, Seq(rootFolderId)))
      .map(_.swap)
  }

  private def createTestUser(
    firstName: String,
    lastName: String
  ): Task[UserId] = {
    userProfileService
      .createRegisteredUser(
        emptyUserInfo
          .copy(
            emailAddressStr = RandomUtils.generateEmailAddress,
            firstName = firstName,
            lastName = lastName
          ),
        userType = RegisteredUser,
        password = Some(secretPassword)
      )
      .map(_.userId)
  }

}
