// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service.flow

import anduin.dataroom.event.CreateDataRoom
import anduin.dataroom.flow.*
import anduin.dataroom.link.DeclinedRequest
import anduin.dataroom.role.*
import anduin.fdb.record.model.RecordTask
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.model.id.*
import anduin.model.id.stage.{DataRoomTermsOfAccessId, DataRoomWorkflowId}
import anduin.protobuf.dataroom.link.DataRoomLinkInvitationParamsData
import anduin.stargazer.service.dataroom.*
import anduin.testing.GondorCoreIntegUtils
import anduin.testing.DataRoomBaseInteg
import zio.test.*

import anduin.dataroom.participant.DataRoomParticipantRoleOperations

object DataRoomFlowInteg extends DataRoomBaseInteg with GondorCoreIntegUtils {

  private val workflowId1 = DataRoomWorkflowIdFactory.unsafeRandomId(TransactionIdFactory.unsafeRandomId)

  private val workflowId2 = DataRoomWorkflowIdFactory.unsafeRandomId(TransactionIdFactory.unsafeRandomId)

  private val workflowId3 = DataRoomWorkflowIdFactory.unsafeRandomId(TransactionIdFactory.unsafeRandomId)

  private val userId1 = UserIdFactory.unsafeRandomId

  private val userId2 = UserIdFactory.unsafeRandomId

  private val entityId1 = EntityIdFactory.unsafeRandomId

  private val entityId2 = EntityIdFactory.unsafeRandomId

  private val toaFileId1 = generateFileId(workflowId1)

  private val protectedLinkId = ProtectedLinkIdFactory.unsafeRandomId

  private def generateFileId(workflowId: DataRoomWorkflowId) = {
    FileIdFactory.generate(FolderId.channelSystemFolderId(DataRoomTermsOfAccessId(workflowId)))
  }

  private def execute[A](task: (DataRoomFlowOperations, DataRoomModelStoreOperations) => RecordTask[A]) = {
    FDBRecordDatabase.transact(FDBOperations[(DataRoomFlowOperations, DataRoomModelStoreOperations)].Production) {
      case (flowOps, modelOps) => task(flowOps, modelOps)
    }
  }

  private def getRoleMap(dataroomId: DataRoomWorkflowId) = {
    DataRoomParticipantRoleOperations.transact(
      _.getAllParticipantRoleMap(dataroomId)
    )
  }

  // scalafix:off DisableSyntax.isInstanceOf
  override def spec = suite("DataRoomFlowInteg")(
    test("Add event") {
      execute { (flowOps, modelOps) =>
        for {
          _ <- modelOps.add(workflowId1, TeamIdFactory.unsafeRandomId(workflowId1.parent))
          _ <- modelOps.add(workflowId2, TeamIdFactory.unsafeRandomId(workflowId2.parent))
          _ <- modelOps.add(workflowId3, TeamIdFactory.unsafeRandomId(workflowId3.parent))
          _ <- flowOps.createDataRoom(
            workflowId1,
            userId1,
            CreateDataRoomParams(
              "Test 1",
              entityId1,
              Some(
                WatermarkMetadataParams(
                  "Hello",
                  WatermarkColor.Red,
                  WatermarkLayout.Subtle,
                  WatermarkTransparency.Fifty
                )
              ),
              showIndex = false,
              showHomePage = false
            ),
            Some(toaFileId1),
            None
          )
          _ <- flowOps.renameDataRoom(
            workflowId1,
            userId2,
            "Test renamed 1a"
          )
          _ <- flowOps.renameDataRoom(
            workflowId1,
            userId1,
            "Test renamed 1b"
          )
          _ <- flowOps.renameDataRoom(
            workflowId1,
            userId2,
            "Test renamed 1c"
          )
          _ <- flowOps.acceptTermsOfAccess(
            workflowId1,
            userId2,
            toaFileId1,
            Some("1.1.1.1")
          )
          _ <- flowOps.createDataRoom(
            workflowId2,
            userId2,
            CreateDataRoomParams(
              "Test 2",
              entityId2,
              Some(
                WatermarkMetadataParams(
                  "Hello",
                  WatermarkColor.Blue,
                  WatermarkLayout.Subtle,
                  WatermarkTransparency.Fifty
                )
              ),
              showIndex = false,
              showHomePage = false
            ),
            Some(generateFileId(workflowId2)),
            None
          )
          _ <- flowOps.inviteUsers(
            workflowId2,
            userId2,
            Map(userId1 -> Member()),
            isToaRequired = true
          )
          _ <- flowOps.renameDataRoom(
            workflowId2,
            userId2,
            "Test renamed 2a"
          )
          _ <- flowOps.renameDataRoom(
            workflowId2,
            userId1,
            "Test renamed 2b"
          )
          _ <- flowOps.createDataRoom(
            workflowId3,
            userId2,
            CreateDataRoomParams(
              "Test 3",
              entityId2,
              None,
              showIndex = false,
              showHomePage = false
            ),
            None,
            None
          )
          _ <- flowOps.renameDataRoom(
            workflowId3,
            userId1,
            "Test renamed 3a"
          )
          _ <- flowOps.renameDataRoom(
            workflowId3,
            userId2,
            "Test renamed 3b"
          )
          _ <- flowOps.renameDataRoom(
            workflowId3,
            userId1,
            "Test renamed 3c"
          )
          _ <- flowOps.setLinkInvitation(
            workflowId3,
            userId1,
            protectedLinkId,
            DataRoomLinkInvitationParamsData(
              name = "Test request access",
              isRequiredAdminApproval = true
            ),
            passwordChange = None
          )
          _ <- flowOps.requestAccessDataRoom(
            workflowId3,
            protectedLinkId,
            userBM1Email
          )
          _ <- flowOps.declineRequestAccessDataRoom(
            workflowId3,
            userId1,
            protectedLinkId,
            userBM1Email
          )
          _ <- flowOps.requestAccessDataRoom(
            workflowId3,
            protectedLinkId,
            userBM1Email
          )
          _ <- flowOps.declineRequestAccessDataRoom(
            workflowId3,
            userId1,
            protectedLinkId,
            userBM1Email
          )
        } yield assertCompletes
      }
    },
    test("Get state and events") {
      for {
        (
          state1,
          state2,
          state3,
          all1,
          all2,
          all3,
          first1,
          first2,
          first3,
          queriedByTrxnId1,
          queriedByTrxnId2,
          queriedByTrxnId3
        ) <- execute { (flowOps, modelOps) =>
          for {
            state1 <- flowOps.stateOps.getState(workflowId1)
            state2 <- flowOps.stateOps.getState(workflowId2)
            state3 <- flowOps.stateOps.getState(workflowId3)
            all1 <- flowOps.eventOps.getAllEvents(workflowId1)
            all2 <- flowOps.eventOps.getAllEvents(workflowId2)
            all3 <- flowOps.eventOps.getAllEvents(workflowId3)
            first1 <- flowOps.eventOps.getFirstEvent(workflowId1)
            first2 <- flowOps.eventOps.getFirstEvent(workflowId2)
            first3 <- flowOps.eventOps.getFirstEvent(workflowId3)
            queriedByTrxnId1 <- modelOps.getOpt(workflowId1.parent)
            queriedByTrxnId2 <- modelOps.getOpt(workflowId2.parent)
            queriedByTrxnId3 <- modelOps.getOpt(workflowId3.parent)
          } yield (
            state1,
            state2,
            state3,
            all1,
            all2,
            all3,
            first1,
            first2,
            first3,
            queriedByTrxnId1,
            queriedByTrxnId2,
            queriedByTrxnId3
          )
        }
        roleMap1 <- getRoleMap(workflowId1)
        roleMap2 <- getRoleMap(workflowId2)
        roleMap3 <- getRoleMap(workflowId3)
      } yield {

        val linkState =
          state3.linkInvitationMap.getOrElse(protectedLinkId, throw new RuntimeException("Failed to get link state"))
        val requestAccess =
          linkState.accessRequests.getOrElse(userBM1Email, throw new RuntimeException("Failed to get request access"))

        assertTrue(
          state1.dataRoomWorkflowId == workflowId1,
          state2.dataRoomWorkflowId == workflowId2,
          state3.dataRoomWorkflowId == workflowId3,
          state1.name == "Test renamed 1c",
          state2.name == "Test renamed 2b",
          state3.name == "Test renamed 3c",
          roleMap1 == Map(userId1 -> Admin()),
          roleMap2 == Map(
            userId1 -> Member(),
            userId2 -> Admin()
          ),
          roleMap3 == Map(userId2 -> Admin()),
          state1.creatorUserId == userId1,
          state2.creatorUserId == userId2,
          state3.creatorUserId == userId2,
          state1.creatorEntityId == entityId1,
          state2.creatorEntityId == entityId2,
          state3.creatorEntityId == entityId2,
          state1.termsOfAccessOptions.get.isEnabled == true,
          state2.termsOfAccessOptions.get.isEnabled == true,
          state3.termsOfAccessOptions.get.isEnabled == false,
          state1.termsOfAccessOptions.get.versions.length == 1,
          state2.termsOfAccessOptions.get.versions.length == 1,
          state3.termsOfAccessOptions.get.versions.length == 0,
          state3.linkInvitationMap.size == 1,
          state3.linkInvitationMap.get(protectedLinkId) != None,
          linkState.accessRequests.size == 1,
          linkState.accessRequests.get(userBM1Email) != None,
          requestAccess.status match {
            case DeclinedRequest(userId, _, _, _) => userId == userId1
            case _ =>
              throw new Exception(s"Wrong request access state ${requestAccess.status}, expected DeclinedRequest")
          },
          requestAccess.lastDeclined != None,
          all1.length == 5,
          all2.length == 4,
          all3.length == 9,
          queriedByTrxnId1.contains(workflowId1),
          queriedByTrxnId2.contains(workflowId2),
          queriedByTrxnId3.contains(workflowId3),
          first1.isInstanceOf[CreateDataRoom],
          first2.isInstanceOf[CreateDataRoom],
          first3.isInstanceOf[CreateDataRoom]
        )
      }
    }
  ) @@ TestAspect.sequential

}
