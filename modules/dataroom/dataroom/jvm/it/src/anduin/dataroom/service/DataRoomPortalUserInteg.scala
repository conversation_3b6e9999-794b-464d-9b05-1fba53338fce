// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service

import java.time.LocalDate

import zio.ZIO

import anduin.dataroom.flow.{DataRoomStateStoreOperations, DataRoomStateStoreProvider}
import anduin.dataroom.role.Admin
import anduin.fdb.record.FDBRecordDatabase
import anduin.id.entity.EntityId
import anduin.model.common.user.UserAttributes
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.orgbilling.model.plan.DataRoomPlan
import anduin.service.{AuthenticatedRequestContext, ServiceActor}
import anduin.stargazer.service.dataroom.*
import anduin.testing.GondorCoreIntegUtils
import anduin.testing.DataRoomBaseInteg
import anduin.entity.EntityTestUtils
import com.anduin.stargazer.service.orgbilling.storage.OrgBillingStoreOperations
import zio.test.*

import anduin.dataroom.participant.DataRoomParticipantRoleOperations

object DataRoomPortalUserInteg extends DataRoomBaseInteg with GondorCoreIntegUtils {

  // scalafix:off DisableSyntax.var
  private var ic: ServiceActor = scala.compiletime.uninitialized
  private var admin: ServiceActor = scala.compiletime.uninitialized
  private var entityId: EntityId = scala.compiletime.uninitialized
  private var dataroomId: DataRoomWorkflowId = scala.compiletime.uninitialized
  // scalafix:on

  val testDataRoomName = "Test data room"

  override def spec = suite("DataRoomPortalUserInteg")(
    setupSuite,
    integSuite
  ) @@ TestAspect.sequential

  private def setupSuite = suite("Setup test context")(
    test("Set up test user") {
      for {
        icActor <- authenticateUser(userIC)
      } yield {
        ic = icActor
        assertCompletes
      }
    },
    test("Ensure portal admin is created") {
      for {
        adminId <- executiveAdmin.userId
      } yield {
        admin = ServiceActor(
          adminId,
          executiveAdmin.userInfo,
          UserAttributes(false),
          None,
          None
        )
        assertCompletes
      }
    },
    test("Create entity") {
      for {
        id <- EntityTestUtils.createEntity(
          "Dataroom integ org",
          "DIO",
          userIC
        )
      } yield {
        entityId = id
        assertCompletes
      }
    },
    test("Set premium package for entity") {
      for {
        _ <- orgBillingService.changeDataRoomPlan(
          entityId,
          OrgBillingStoreOperations.toDataRoomPackageProto(DataRoomPlan.DataRoomBusinessPlan(LocalDate.now().plusDays(15))),
          userIC
        )
      } yield assertCompletes
    }
  )

  private def integSuite = suite("DataRoomPortalUserInteg")(
    test("Create a new dataroom") {
      for {
        resp <- dataRoomService.createDataRoom(
          CreateDataRoomParams(
            testDataRoomName,
            entityId,
            None,
            None,
            showIndex = false
          ),
          AuthenticatedRequestContext(
            ic,
            Seq(),
            Seq()
          )
        )
        _ <- ZIO.attempt {
          dataroomId = resp.dataRoomWorkflowId
        }
        createdState <- getCreatedState(dataroomId)
        roleMap <- getRoleMap(dataroomId)
      } yield {
        assertTrue(
          createdState.name == testDataRoomName,
          roleMap.size == 1,
          roleMap == Map(
            ic.userId -> Admin()
          ),
          createdState.creatorEntityId == entityId
        )
      }
    },
    test("Admin tries to join dataroom for the first time") {
      for {
        _ <- dataRoomAdminPortalService
          .addPortalUserToDataRoom(
            AddPortalUserToDataRoomParams(dataroomId),
            admin,
            None
          )
      } yield assertCompletes
    },
    test("Admin tries to join dataroom for the second time") {
      for {
        result <- dataRoomAdminPortalService
          .addPortalUserToDataRoom(
            AddPortalUserToDataRoomParams(dataroomId),
            admin,
            None
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("Admin tries to decline to join dataroom for the first time") {
      for {
        _ <- dataRoomAdminPortalService
          .removePortalUserFromDataRoom(
            RemovePortalUserFromDataRoomParams(dataroomId),
            admin,
            None
          )
      } yield assertCompletes
    },
    test("Admin tries to decline to join dataroom for the second time") {
      for {
        result <- dataRoomAdminPortalService
          .removePortalUserFromDataRoom(
            RemovePortalUserFromDataRoomParams(dataroomId),
            admin,
            None
          )
          .either
      } yield assertTrue(result.isLeft)
    },
    test("Admin tries to leave after joining dataroom") {
      for {
        _ <- dataRoomAdminPortalService
          .addPortalUserToDataRoom(
            AddPortalUserToDataRoomParams(dataroomId),
            admin,
            None
          )
        _ <- dataRoomParticipantService
          .acceptInvitation(
            AcceptInvitationToDataRoomParams(
              dataroomId,
              None
            ),
            AuthenticatedRequestContext(
              admin,
              Seq(),
              Seq()
            ),
            shouldSendEmail = false
          )
        _ <- dataRoomAdminPortalService
          .removePortalUserFromDataRoom(
            RemovePortalUserFromDataRoomParams(dataroomId),
            admin,
            None
          )
      } yield assertCompletes
    },
    test("Admin tries to leave when only 1 admin left dataroom") {
      val task = for {
        _ <- dataRoomAdminPortalService
          .addPortalUserToDataRoom(
            AddPortalUserToDataRoomParams(dataroomId),
            admin,
            None
          )
        _ <- dataRoomParticipantService
          .acceptInvitation(
            AcceptInvitationToDataRoomParams(
              dataroomId,
              None
            ),
            AuthenticatedRequestContext(
              admin,
              Seq(),
              Seq()
            ),
            shouldSendEmail = false
          )
        _ <- dataRoomParticipantService
          .removeUsers(
            RemoveUsersFromDataRoomParams(
              dataroomId,
              Set(ic.userId),
              true
            ),
            ic.userId
          )
        _ <- dataRoomAdminPortalService
          .removePortalUserFromDataRoom(
            RemovePortalUserFromDataRoomParams(dataroomId),
            admin,
            None
          )
      } yield ()

      for {
        result <- task.either
      } yield assertTrue(result.isLeft)
    }
  )

  private def getCreatedState(dataroomId: DataRoomWorkflowId) = {
    FDBRecordDatabase.transact(DataRoomStateStoreProvider.Production) {
      DataRoomStateStoreOperations(_).getState(dataroomId)
    }
  }

  private def getRoleMap(dataroomId: DataRoomWorkflowId) = {
    DataRoomParticipantRoleOperations.transact(
      _.getAllParticipantRoleMap(dataroomId)
    )
  }

}
