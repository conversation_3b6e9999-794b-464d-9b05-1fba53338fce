// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.gondor.server

import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter
import sttp.apispec.openapi.circe.yaml.*
import sttp.apispec.openapi.{OpenAPI, Server}
import sttp.tapir.docs.openapi.OpenAPIDocsInterpreter
import sttp.tapir.swagger.{SwaggerUI, SwaggerUIOptions}

import anduin.brienne.dataroom.api.external.*
import anduin.brienne.endpoint.DataRoomPublicApiEndpoints.*
import anduin.brienne.dataroom.service.DataRoomPublicApiService
import anduin.tapir.server.EndpointServer.TapirServerService
import anduin.tapir.server.PublicApiTapirServerAuthentication
import com.anduin.stargazer.service.authorization.AuthorizationService

final case class DataRoomPublicApiServer(
  dataRoomPublicApiService: DataRoomPublicApiService,
  protected val interpreter: ArmeriaZioServerInterpreter[Any],
  protected val authorizationService: AuthorizationService
) extends PublicApiTapirServerAuthentication {

  private val dataRoomServices: List[TapirServerService] = List(
    publicApiRoute {
      List(
        publicApiEndpoint(listDataRooms) { (queryParams, ctx) =>
          dataRoomPublicApiService.listDataRooms(queryParams, ctx)
        },
        publicApiEndpoint(listDataRoomsByEmail) { case ((_, bodyParams, queryParams), ctx) =>
          dataRoomPublicApiService.listDataRoomsByEmail(
            ListDataRoomsByEmail.Params(
              queryParams = queryParams,
              bodyParams = bodyParams
            ),
            ctx
          )
        }
      )
    }
  )

  private val userServices: List[TapirServerService] = List(
    publicApiRoute {
      List(
        publicApiEndpoint(listDataRoomUsers) { (dataRoomId, ctx) =>
          dataRoomPublicApiService.listDataRoomUsers(ListDataRoomUsers.PathParams(dataRoomId), ctx)
        },
        publicApiEndpoint(inviteUserSync) { case ((dataRoomId, bodyParams), ctx) =>
          dataRoomPublicApiService.inviteUserSync(
            InviteUserSync.Params(
              pathParams = InviteUserSync.PathParams(dataRoomId),
              bodyParams = bodyParams
            ),
            ctx
          )
        },
        publicApiEndpoint(addUserToGroupSync) { case ((dataRoomId, bodyParams), ctx) =>
          dataRoomPublicApiService.addUserToGroupSync(
            AddUserToGroupSync.Params(
              pathParams = AddUserToGroupSync.PathParams(dataRoomId),
              bodyParams = bodyParams
            ),
            ctx
          )
        },
        publicApiEndpoint(removeUserFromGroupSync) { case ((dataRoomId, bodyParams), ctx) =>
          dataRoomPublicApiService.removeUserFromGroupSync(
            RemoveUserFromGroupSync.Params(
              pathParams = RemoveUserFromGroupSync.PathParams(dataRoomId),
              bodyParams = bodyParams
            ),
            ctx
          )
        },
        publicApiEndpoint(removeUserSync) { case ((dataRoomId, bodyParams), ctx) =>
          dataRoomPublicApiService.removeUserSync(
            RemoveUserSync.Params(
              pathParams = RemoveUserSync.PathParams(dataRoomId),
              bodyParams = bodyParams
            ),
            ctx
          )
        }
      )
    }
  )

  private val groupServices: List[TapirServerService] = List(
    publicApiRoute {
      List(
        publicApiEndpoint(listDataRoomGroups) { (dataRoomId, ctx) =>
          dataRoomPublicApiService.listDataRoomGroups(ListDataRoomGroups.PathParams(dataRoomId), ctx)
        },
        publicApiEndpoint(getGroupFolderPermission) { case ((dataRoomId, groupId, folderId), ctx) =>
          dataRoomPublicApiService.getGroupFolderPermission(
            GetGroupFolderPermission.PathParams(dataRoomId, groupId, folderId),
            ctx
          )
        },
        publicApiEndpoint(createDataRoomGroup) { case ((dataRoomId, bodyParams), ctx) =>
          dataRoomPublicApiService.createGroup(
            CreateDataRoomGroup.Params(
              pathParams = CreateDataRoomGroup.PathParams(dataRoomId),
              bodyParams = bodyParams
            ),
            ctx
          )
        },
        publicApiEndpoint(renameDataRoomGroup) { case (((dataRoomId, groupId), bodyParams), ctx) =>
          dataRoomPublicApiService.renameGroup(
            RenameDataRoomGroup.Params(
              pathParams = RenameDataRoomGroup.PathParams(dataRoomId, groupId),
              bodyParams = bodyParams
            ),
            ctx
          )
        },
        publicApiEndpoint(updateDataRoomGroupPermission) { case (((dataRoomId, groupId), bodyParams), ctx) =>
          dataRoomPublicApiService.updateGroupPermission(
            UpdateDataRoomGroupPermission.Params(
              pathParams = UpdateDataRoomGroupPermission.PathParams(dataRoomId, groupId),
              bodyParams = bodyParams
            ),
            ctx
          )
        },
        publicApiEndpoint(deleteDataRoomGroup) { case (((dataRoomId, groupId), bodyParams), ctx) =>
          dataRoomPublicApiService.deleteGroup(
            DeleteDataRoomGroup.Params(
              pathParams = DeleteDataRoomGroup.PathParams(dataRoomId, groupId),
              bodyParams = bodyParams
            ),
            ctx
          )
        }
      )
    }
  )

  private val insightsServices: List[TapirServerService] = List(
    publicApiRoute {
      List(
        publicApiEndpoint(exportActivitiesAsync) { case ((dataRoomId, bodyParamsOpt), ctx) =>
          dataRoomPublicApiService.exportActivitiesAsync(
            DataRoomExportActivities.Params(
              pathParams = DataRoomExportActivities.PathParams(dataRoomId),
              bodyParams = bodyParamsOpt.getOrElse(DataRoomExportActivities.BodyParams())
            ),
            ctx
          )
        },
        publicApiEndpoint(getFileInsights) { case (((dataRoomId, fileId), bodyParams), ctx) =>
          dataRoomPublicApiService.getFileInsights(
            GetFileInsights.Params(
              pathParams = GetFileInsights.PathParams(dataRoomId, fileId),
              bodyParams = bodyParams.getOrElse(GetFileInsights.BodyParams())
            ),
            ctx
          )
        },
        publicApiEndpoint(getUserInsights) { case ((dataRoomId, bodyParams), ctx) =>
          dataRoomPublicApiService.getUserInsights(
            GetUserInsights.Params(
              pathParams = GetUserInsights.PathParams(dataRoomId),
              bodyParams = bodyParams
            ),
            ctx
          )
        },
        publicApiEndpoint(getGroupInsights) { case (((dataRoomId, groupId), bodyParams), ctx) =>
          dataRoomPublicApiService.getGroupInsights(
            GetGroupInsights.Params(
              pathParams = GetGroupInsights.PathParams(dataRoomId, groupId),
              bodyParams = bodyParams.getOrElse(GetGroupInsights.BodyParams())
            ),
            ctx
          )
        }
      )
    }
  )

  private val folderServices: List[TapirServerService] = List(
    publicApiRoute {
      List(
        publicApiEndpoint(getFolderStructure) { case ((dataRoomId, folderId, queryParams), ctx) =>
          dataRoomPublicApiService.getFolderStructure(
            GetFolderStructure.Params(
              pathParams = GetFolderStructure.PathParams(dataRoomId, folderId),
              queryParams = queryParams
            ),
            ctx
          )
        },
        publicApiEndpoint(getFolderPermission) { case ((dataRoomId, folderId), ctx) =>
          dataRoomPublicApiService.getFolderPermission(
            GetFolderPermission.PathParams(dataRoomId, folderId),
            ctx
          )
        },
        publicApiEndpoint(createFolder) { case (((dataRoomId, folderId), bodyParams), ctx) =>
          dataRoomPublicApiService.createFolder(
            CreateFolder.Params(
              pathParams = CreateFolder.PathParams(dataRoomId, folderId),
              bodyParams = bodyParams
            ),
            ctx
          )
        },
        publicApiEndpoint(updateFolderPermission) { case (((dataRoomId, folderId), bodyParams), ctx) =>
          dataRoomPublicApiService.updateFolderPermission(
            UpdateFolderPermission.Params(
              pathParams = UpdateFolderPermission.PathParams(dataRoomId, folderId),
              bodyParams = bodyParams
            ),
            ctx
          )
        },
        publicApiEndpoint(batchDownloadContent) { case ((dataRoomId, bodyParams), ctx) =>
          dataRoomPublicApiService.batchDownloadContent(
            BatchDownloadContent.Params(
              pathParams = BatchDownloadContent.PathParams(dataRoomId),
              bodyParams = bodyParams
            ),
            ctx
          )
        },
        publicApiEndpoint(batchDeleteContent) { case ((dataRoomId, bodyParams), ctx) =>
          dataRoomPublicApiService.batchDeleteContent(
            BatchDeleteContent.Params(
              pathParams = BatchDeleteContent.PathParams(dataRoomId),
              bodyParams = bodyParams
            ),
            ctx
          )
        }
      )
    }
  )

  private val fileServices: List[TapirServerService] = List(
    publicApiRoute {
      List(
        publicApiEndpoint(getFileInfo) { case ((dataRoomId, fileId, queryParams), ctx) =>
          dataRoomPublicApiService.getFileInfo(
            GetFileInfo.Params(
              pathParams = GetFileInfo.PathParams(dataRoomId, fileId),
              queryParams = queryParams
            ),
            ctx
          )
        },
        publicApiEndpoint(getFilePermission) { case ((dataRoomId, fileId), ctx) =>
          dataRoomPublicApiService.getFilePermission(
            GetFilePermission.PathParams(dataRoomId, fileId),
            ctx
          )
        },
        publicApiEndpoint(getFileDownloadUrl) { case ((dataRoomId, fileId, queryParams), ctx) =>
          dataRoomPublicApiService.getFileDownloadUrl(
            GetFileDownloadUrl.Params(
              pathParams = GetFileDownloadUrl.PathParams(dataRoomId, fileId),
              queryParams = queryParams
            ),
            ctx
          )
        },
        publicApiEndpoint(uploadSingleFile) { case (((dataRoomId, folderId), bodyParams), ctx) =>
          dataRoomPublicApiService.uploadSingleFile(
            UploadSingleFile.Params(
              pathParams = UploadSingleFile.PathParams(dataRoomId, folderId),
              bodyParams = bodyParams
            ),
            ctx
          )
        },
        publicApiEndpoint(uploadNewVersion) { case (((dataRoomId, fileId), bodyParams), ctx) =>
          dataRoomPublicApiService.uploadNewVersion(
            UploadNewFileVersion.Params(
              pathParams = UploadNewFileVersion.PathParams(dataRoomId, fileId),
              bodyParams = bodyParams
            ),
            ctx
          )
        },
        publicApiEndpoint(deleteSingleFile) { case (((dataRoomId, fileId), bodyParams), ctx) =>
          dataRoomPublicApiService.deleteSingleFile(
            DeleteSingleFile.Params(
              pathParams = DeleteSingleFile.PathParams(dataRoomId, fileId),
              bodyParams = bodyParams
            ),
            ctx
          )
        }
      )
    }
  )

  private val docs: OpenAPI = OpenAPIDocsInterpreter().toOpenAPI(
    List(
      listDataRooms,
      listDataRoomsByEmail,
      listDataRoomUsers,
      listDataRoomGroups,
      inviteUserSync,
      addUserToGroupSync,
      removeUserFromGroupSync,
      removeUserSync,
      getGroupFolderPermission,
      createDataRoomGroup,
      renameDataRoomGroup,
      updateDataRoomGroupPermission,
      deleteDataRoomGroup,
      exportActivitiesAsync,
      getFileInsights,
      getUserInsights,
      getGroupInsights,
      getFolderStructure,
      getFolderPermission,
      createFolder,
      updateFolderPermission,
      getFileInfo,
      getFilePermission,
      getFileDownloadUrl,
      uploadSingleFile,
      uploadNewVersion,
      deleteSingleFile,
      batchDownloadContent,
      batchDeleteContent
    ),
    title = "DataRoom Public API",
    version = "1.0"
  )

  private val swaggerServices = List(
    publicApiRoute {
      SwaggerUI[zio.Task](
        refineOpenAPIDocs(
          docs,
          servers = List(
            Server(
              url = "https://api-demo.anduin.dev",
              description = Option("Sand box environment")
            ),
            Server(
              url = "https://api.anduin.app",
              description = Option("Production environment")
            ),
            Server(
              url = "https://api.eu.anduin.app",
              description = Option("EU Production environment")
            )
          )
        ).toYaml,
        SwaggerUIOptions.default.copy(
          List(
            "api",
            "v1",
            "dataroom",
            "docs"
          )
        )
      )
    }
  )

  val services: List[TapirServerService] = List(
    dataRoomServices,
    userServices,
    groupServices,
    swaggerServices,
    insightsServices,
    folderServices,
    fileServices
  ).flatten

}
