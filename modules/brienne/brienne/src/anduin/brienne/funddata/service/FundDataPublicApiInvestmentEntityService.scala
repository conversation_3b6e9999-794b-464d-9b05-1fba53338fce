// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.brienne.funddata.service

import zio.{Task, ZIO}

import anduin.brienne.funddata.api.external.*
import anduin.brienne.workflow.core.RequestError
import anduin.funddata.investmententity.FundDataInvestmentEntityService
import anduin.funddata.permission.FundDataPermissionService
import anduin.funddata.tag.{FundDataTagService, FundDataTagUtils}
import anduin.id.funddata.{FundDataFirmId, FundDataInvestmentEntityId}
import anduin.service.PublicApiContext
import io.github.arainko.ducktape.*

import anduin.tapir.endpoint.UpdateOptionOperator
import anduin.funddata.endpoint.UpdateProperty
import anduin.funddata.error.FundDataError.FundDataValidationError
import anduin.funddata.investmententity.note.FundDataInvestmentEntityNoteService
import anduin.funddata.investor.FundDataInvestorService
import anduin.funddata.note.FundDataNoteUtils
import anduin.id.tag.TagItemId
import anduin.model.common.user.UserId
import anduin.protobuf.funddata.event.ManuallyCreateInvestmentEntity
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FundDataPublicApiInvestmentEntityService(
  fundDataPermissionService: FundDataPermissionService,
  fundDataTagService: FundDataTagService,
  fundDataInvestorService: FundDataInvestorService,
  fundDataInvestmentEntityService: FundDataInvestmentEntityService,
  fundDataInvestmentEntityNoteService: FundDataInvestmentEntityNoteService
) extends FundDataPublicApiBaseService {

  def listInvestmentEntities(
    firmId: FundDataFirmId,
    clientIdParam: String,
    ctx: PublicApiContext
  ): Task[Either[RequestError, ListInvestmentEntitiesResponse]] = {
    catchError(ctx) {
      for {
        _ <- ZIO.logInfo(s"PublicApi: ${ctx.serviceAccount} list investment entities in $clientIdParam")
        clientId <- getClientIdFromClientIdParam(firmId, clientIdParam)
        _ <- fundDataPermissionService.validateUserCanViewClient(clientId, ctx.serviceAccount)
        investorTypeList <- fundDataTagService.getFirmTagListInternal(firmId)(
          using FundDataTagService.InvestorType
        )
        jurisdictionList <- fundDataTagService.getFirmTagListInternal(firmId)(
          using FundDataTagService.Jurisdiction
        )
        riskLevelList <- fundDataTagService.getFirmTagListInternal(firmId)(
          using FundDataTagService.RiskLevelAssessment
        )
        resp <- fundDataInvestmentEntityService.getInvestmentEntities(
          clientId,
          actor = ctx.serviceAccount
        )
        investmentEntityNotes <- fundDataInvestmentEntityNoteService.getInvestmentEntitiesNote(
          firmId = firmId,
          investmentEntityIds = resp.data.map(_._1).toSet
        )
      } yield ListInvestmentEntitiesResponse(
        data = resp.data.map(
          _._2
            .into[InvestmentEntity]
            .transform(
              Field.renamed(_.id, _.investmentEntityId),
              Field.computed(_.trackingId, i => Option.when(i.customId.trim.nonEmpty)(i.customId)),
              Field.computed(_.`type`, i => FundDataTagUtils.getTagName(i.investorType, investorTypeList)),
              Field.computed(_.jurisdiction, i => FundDataTagUtils.getTagName(i.jurisdictionType, jurisdictionList)),
              Field.computed(_.riskLevel, i => FundDataTagUtils.getTagName(i.riskAssessmentLevel, riskLevelList)),
              Field.computed(_.clientId, _.investmentEntityId.parent),
              Field.computed(
                _.note,
                i =>
                  investmentEntityNotes
                    .getOrElse(i.investmentEntityId, None)
                    .map(_.content)
                    .map(FundDataNoteUtils.getTextFromNoteContent)
              )
            )
        ),
        total = resp.pagination.total
      )
    }
  }

  def createInvestmentEntity(
    firmId: FundDataFirmId,
    clientIdParam: String,
    bodyParam: CreateInvestmentEntityParams,
    ctx: PublicApiContext
  ): Task[Either[RequestError, InvestmentEntity]] = {
    catchError(ctx) {
      for {
        _ <- ZIO.logInfo(s"PublicApi: ${ctx.serviceAccount} create investment entity in $clientIdParam")
        clientId <- getClientIdFromClientIdParam(firmId, clientIdParam)
        _ <- fundDataPermissionService.validateUserCanCreateInvestmentEntity(clientId, ctx.serviceAccount)
        investorTypeOpt <- ZIOUtils.traverseOption(bodyParam.`type`) { typeStr =>
          for {
            investorTypeList <- fundDataTagService.getFirmTagListInternal(firmId)(
              using FundDataTagService.InvestorType
            )
            investorType <- ZIOUtils.fromOption(
              FundDataTagUtils.computeTagFromString(typeStr, investorTypeList),
              FundDataValidationError(
                s"type is invalid; list of available types: ${investorTypeList.tagItems.map(_.name).mkString(", ")}"
              )
            )
          } yield investorType.tagItemId
        }
        jurisdictionTypeOpt <- ZIOUtils.traverseOption(bodyParam.jurisdiction) { jurisdictionStr =>
          for {
            jurisdictionList <- fundDataTagService.getFirmTagListInternal(firmId)(
              using FundDataTagService.Jurisdiction
            )
            jurisdiction <- ZIOUtils.fromOption(
              FundDataTagUtils.computeTagFromString(jurisdictionStr, jurisdictionList),
              FundDataValidationError(
                s"jurisdiction is invalid; list of available jurisdictions: ${jurisdictionList.tagItems.map(_.name).mkString(", ")}"
              )
            )
          } yield jurisdiction.tagItemId
        }
        riskLevelOpt <- ZIOUtils.traverseOption(bodyParam.riskLevel) { riskLevelStr =>
          for {
            riskLevelList <- fundDataTagService.getFirmTagListInternal(firmId)(
              using FundDataTagService.RiskLevelAssessment
            )
            riskLevel <- ZIOUtils.fromOption(
              FundDataTagUtils.computeTagFromString(riskLevelStr, riskLevelList),
              FundDataValidationError(
                s"risk level is invalid; list of available risk levels: ${riskLevelList.tagItems.map(_.name).mkString(", ")}"
              )
            )
          } yield riskLevel.tagItemId
        }
        investmentEntityId <- fundDataInvestmentEntityService.createInvestmentEntityUnsafe(
          investorId = clientId,
          name = bodyParam.name,
          customId = bodyParam.trackingId.getOrElse(""),
          jurisdictionType = jurisdictionTypeOpt,
          jurisdictionOtherType = "",
          investorType = investorTypeOpt,
          investorOtherType = "",
          riskLevelOpt = riskLevelOpt,
          noteOpt = bodyParam.note.filter(_.nonEmpty),
          actor = ctx.serviceAccount,
          source = ManuallyCreateInvestmentEntity()
        )
        investmentEntity <- getInvestmentEntityInternal(investmentEntityId, ctx.serviceAccount)
      } yield investmentEntity
    }
  }

  def getInvestmentEntity(
    firmId: FundDataFirmId,
    investmentEntityIdParam: String,
    ctx: PublicApiContext
  ): Task[Either[RequestError, InvestmentEntity]] = {
    catchError(ctx) {
      for {
        _ <- ZIO.logInfo(s"PublicApi: ${ctx.serviceAccount} get investment entity $investmentEntityIdParam in $firmId")
        investmentEntityId <- getInvestmentEntityIdFromInvestmentEntityIdParam(firmId, investmentEntityIdParam)
        _ <- fundDataPermissionService.validateUserCanViewInvestmentEntity(investmentEntityId, ctx.serviceAccount)
        investmentEntity <- getInvestmentEntityInternal(investmentEntityId, ctx.serviceAccount)
      } yield investmentEntity
    }
  }

  def updateInvestmentEntity(
    firmId: FundDataFirmId,
    investmentEntityIdParam: String,
    bodyParam: UpdateInvestmentEntityParams,
    ctx: PublicApiContext
  ): Task[Either[RequestError, InvestmentEntity]] = {
    catchError(ctx) {
      for {
        _ <- ZIO.logInfo(
          s"PublicApi: ${ctx.serviceAccount} update investment entity $investmentEntityIdParam in $firmId"
        )
        investmentEntityId <- getInvestmentEntityIdFromInvestmentEntityIdParam(firmId, investmentEntityIdParam)
        _ <- fundDataPermissionService.validateUserCanEditInvestmentEntity(investmentEntityId, ctx.serviceAccount)
        investorTypeUpdate <- bodyParam.`type` match {
          case None =>
            ZIO.succeed(UpdateOptionOperator[TagItemId](newValueOpt = None)) // Keep unchanged
          case Some(typeStr) =>
            if (typeStr.isEmpty) {
              ZIO.succeed(UpdateOptionOperator[TagItemId](newValueOpt = Some(None))) // Clear
            } else {
              for {
                investorTypeList <- fundDataTagService.getFirmTagListInternal(firmId)(
                  using FundDataTagService.InvestorType
                )
                investorType <- ZIOUtils.fromOption(
                  FundDataTagUtils.computeTagFromString(typeStr, investorTypeList),
                  FundDataValidationError(
                    s"type is invalid; list of available types: ${investorTypeList.tagItems.map(_.name).mkString(", ")}"
                  )
                )
              } yield UpdateOptionOperator[TagItemId](newValueOpt = Some(Some(investorType.tagItemId))) // Update
            }
        }
        jurisdictionTypeUpdate <- bodyParam.jurisdiction match {
          case None =>
            ZIO.succeed(UpdateOptionOperator[TagItemId](newValueOpt = None)) // Keep unchanged
          case Some(jurisdictionStr) =>
            if (jurisdictionStr.isEmpty) {
              ZIO.succeed(UpdateOptionOperator[TagItemId](newValueOpt = Some(None))) // Clear
            } else {
              for {
                jurisdictionList <- fundDataTagService.getFirmTagListInternal(firmId)(
                  using FundDataTagService.Jurisdiction
                )
                jurisdiction <- ZIOUtils.fromOption(
                  FundDataTagUtils.computeTagFromString(jurisdictionStr, jurisdictionList),
                  FundDataValidationError(
                    s"jurisdiction is invalid; list of available jurisdictions: ${jurisdictionList.tagItems.map(_.name).mkString(", ")}"
                  )
                )
              } yield UpdateOptionOperator[TagItemId](newValueOpt = Some(Some(jurisdiction.tagItemId)))
            }
        }
        riskLevelOpt <- ZIOUtils.traverseOption(bodyParam.riskLevel) { riskLevelStr =>
          for {
            riskLevelList <- fundDataTagService.getFirmTagListInternal(firmId)(
              using FundDataTagService.RiskLevelAssessment
            )
            riskLevel <- ZIOUtils.fromOption(
              FundDataTagUtils.computeTagFromString(riskLevelStr, riskLevelList),
              FundDataValidationError(
                s"risk level is invalid; list of available risk levels: ${riskLevelList.tagItems.map(_.name).mkString(", ")}"
              )
            )
          } yield riskLevel.tagItemId
        }
        _ <- fundDataInvestmentEntityService.editInvestmentEntityInternal(
          investmentEntityId = investmentEntityId,
          name = bodyParam.name,
          customId = bodyParam.trackingId,
          jurisdictionType = jurisdictionTypeUpdate,
          investorType = investorTypeUpdate,
          riskLevel = riskLevelOpt,
          noteUpdate = UpdateProperty(bodyParam.note),
          actor = ctx.serviceAccount
        )
        investmentEntity <- getInvestmentEntityInternal(investmentEntityId, ctx.serviceAccount)
      } yield investmentEntity
    }
  }

  def deleteInvestmentEntity(
    firmId: FundDataFirmId,
    investmentEntityIdParam: String,
    ctx: PublicApiContext
  ): Task[Either[RequestError, Unit]] = {
    catchError(ctx) {
      for {
        _ <- ZIO.logInfo(
          s"PublicApi: ${ctx.serviceAccount} delete investment entity $investmentEntityIdParam in $firmId"
        )
        investmentEntityId <- getInvestmentEntityIdFromInvestmentEntityIdParam(firmId, investmentEntityIdParam)
        _ <- fundDataPermissionService.validateUserCanDeleteInvestmentEntity(investmentEntityId, ctx.serviceAccount)
        _ <- fundDataInvestmentEntityService.deleteInvestmentEntity(investmentEntityId, ctx.serviceAccount)
      } yield ()
    }
  }

  private def getInvestmentEntityInternal(
    investmentEntityId: FundDataInvestmentEntityId,
    actor: UserId
  ) = {
    val firmId = investmentEntityId.firmId
    for {
      investmentEntity <- fundDataInvestmentEntityService.getInvestmentEntityDetail(investmentEntityId, actor)
      investorTypeList <- fundDataTagService.getFirmTagListInternal(firmId)(
        using FundDataTagService.InvestorType
      )
      jurisdictionList <- fundDataTagService.getFirmTagListInternal(firmId)(
        using FundDataTagService.Jurisdiction
      )
      riskLevelList <- fundDataTagService.getFirmTagListInternal(firmId)(
        using FundDataTagService.RiskLevelAssessment
      )
      noteOpt <- fundDataInvestmentEntityNoteService
        .getInvestmentEntitiesNote(firmId, Set(investmentEntityId))
        .map(_.getOrElse(investmentEntityId, None))
    } yield investmentEntity
      .into[InvestmentEntity]
      .transform(
        Field.renamed(_.id, _.investmentEntityId),
        Field.computed(_.trackingId, i => Option.when(i.customId.trim.nonEmpty)(i.customId)),
        Field.computed(_.`type`, i => FundDataTagUtils.getTagName(i.investorType, investorTypeList)),
        Field.computed(_.jurisdiction, i => FundDataTagUtils.getTagName(i.jurisdictionType, jurisdictionList)),
        Field.computed(_.riskLevel, i => FundDataTagUtils.getTagName(i.riskAssessment, riskLevelList)),
        Field.computed(_.clientId, _.investmentEntityId.parent),
        Field.const(_.note, noteOpt.map(_.content).map(FundDataNoteUtils.getTextFromNoteContent))
      )
  }

}
