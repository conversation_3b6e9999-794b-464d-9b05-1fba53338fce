// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.brienne.dataroom.service

import zio.{Task, ZIO}
import anduin.brienne.dataroom.api.external.*
import anduin.brienne.dataroom.api.external.PublicDataRoomRole.toProtoRole
import anduin.brienne.dataroom.workflow.codec.DataRoomGenericSyncApiCodec
import anduin.brienne.dataroom.workflow.*
import anduin.brienne.dms.service.PublicFileIdConverter
import anduin.brienne.fundsub.api.external.GeneralRequestError
import anduin.brienne.fundsub.api.external.GeneralRequestError.ServerErrorResponse
import anduin.brienne.workflow.api.external.AsyncApiResponse
import anduin.brienne.workflow.core.{RequestError, ServerErrorResponse as ServerErrorResponseProto}
import anduin.brienne.workflow.utils.PublicApiWorkflowUtils
import anduin.dms.service.PublicFileService
import anduin.model.id.TemporalWorkflowId
import anduin.service.PublicApiContext
import anduin.temporal.TemporalEnvironment
import anduin.workflow.TemporalWorkflowService

final case class DataRoomPublicApiService(
  publicFileService: PublicFileService
)(
  using temporalWorkflowService: TemporalWorkflowService,
  temporalEnvironment: TemporalEnvironment
) extends PublicFileIdConverter {

  /** This function should be used to handle failures that occur outside of the workflow. Such errors may arise during
    * parameter transformation, response generation, data conversion, or similar processes.
    */
  private def convertError[R](
    task: Task[Either[GeneralRequestError, R]]
  ): Task[Either[GeneralRequestError, R]] = {
    task.catchAllCause { err =>
      ZIO.logErrorCause(s"Failed when executing Data Room API", err)
        *> ZIO.succeed(Left(ServerErrorResponse("Internal server error")))
    }
  }

  private def executeGenericSync[
    E <: DataRoomApiEndpoint &
      DataRoomGenericSyncApiCodec[
        ? <: DataRoomGenericSyncParams,
        ? <: DataRoomGenericSyncResponse
      ]
  ](
    using endpoint: E
  )(
    params: endpoint.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, endpoint.Response]] = convertError {
    for {
      workflowRequest <- endpoint.encodeWorkflowRequest(
        params,
        ctx
      )
      workflowRes <- temporalWorkflowService.runSync[
        DataRoomGenericSyncApiInput,
        DataRoomGenericSyncApiOutput,
        DataRoomGenericSyncApiWorkflow
      ](workflowRequest)
      res <- endpoint.decodeWorkflowResponse(workflowRes)
    } yield res
  }

  def listDataRooms(
    params: ListDataRooms.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, ListDataRooms.Response]] = {
    executeGenericSync[ListDataRooms.type](params, ctx)
  }

  def listDataRoomsByEmail(
    params: ListDataRoomsByEmail.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, ListDataRoomsByEmail.Response]] = {
    executeGenericSync[ListDataRoomsByEmail.type](params, ctx)
  }

  def listDataRoomUsers(
    params: ListDataRoomUsers.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, ListDataRoomUsers.Response]] = {
    executeGenericSync[ListDataRoomUsers.type](params, ctx)
  }

  def listDataRoomGroups(
    params: ListDataRoomGroups.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, ListDataRoomGroups.Response]] = {
    executeGenericSync[ListDataRoomGroups.type](params, ctx)
  }

  def inviteUserSync(
    params: InviteUserSync.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, InviteUserSync.Response]] = {
    executeGenericSync[InviteUserSync.type](params, ctx)
  }

  def addUserToGroupSync(
    params: AddUserToGroupSync.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, AddUserToGroupSync.Response]] = {
    executeGenericSync[AddUserToGroupSync.type](params, ctx)
  }

  def removeUserFromGroupSync(
    params: RemoveUserFromGroupSync.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, RemoveUserFromGroupSync.Response]] = {
    executeGenericSync[RemoveUserFromGroupSync.type](params, ctx)
  }

  def removeUserSync(
    params: RemoveUserSync.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, RemoveUserSync.Response]] = {
    executeGenericSync[RemoveUserSync.type](params, ctx)
  }

  def exportActivitiesAsync(
    params: DataRoomExportActivities.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, AsyncApiResponse]] = convertError {
    val bodyParams = params.bodyParams
    val paramsM = DataRoomExportActivitiesWorkflowInput(
      dataRoomId = params.pathParams.dataRoomId,
      fromTimestamp = Some(bodyParams.fromTimestamp),
      toTimestamp = Some(bodyParams.toTimestamp),
      dataRoomActivities = bodyParams.dataRoomActivities,
      contentActivities = bodyParams.contentActivities,
      exportFields = bodyParams.columns.map(ExportedFieldsWrapper.apply),
      filter = ExportActivitiesFilter(
        filteredUsers = bodyParams.filterUserEmail.map(FilterUsersInput.apply),
        filteredGroupIds = bodyParams.filterGroup.map(FilterGroupIdsInput.apply),
        filteredRoles = bodyParams.filterRole.map(roleFilter => FilterRolesInput(roleFilter.map(toProtoRole))),
        filteredFileIds = bodyParams.filterFile.map(FilterFileIdsInput.apply)
      ),
      zoneId = Some(bodyParams.timezone)
    )
    for {
      resp <- PublicApiWorkflowUtils.executeAsync[
        DataRoomExportActivitiesWorkflowInput,
        DataRoomExportActivitiesWorkflowOutput,
        DataRoomExportActivitiesWorkflow
      ](paramsM, ctx)
    } yield resp.left.map(GeneralRequestError.fromRequestError)
  }

  def getFileInsights(
    params: GetFileInsights.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, GetFileInsights.Response]] = {
    executeGenericSync[GetFileInsights.type](params, ctx)
  }

  def getUserInsights(
    params: GetUserInsights.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, GetUserInsights.Response]] = {
    executeGenericSync[GetUserInsights.type](params, ctx)
  }

  def getGroupInsights(
    params: GetGroupInsights.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, GetGroupInsights.Response]] = {
    executeGenericSync[GetGroupInsights.type](params, ctx)
  }

  def getFolderStructure(
    params: GetFolderStructure.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, GetFolderStructure.Response]] = {
    executeGenericSync[GetFolderStructure.type](params, ctx)
  }

  def createFolder(
    params: CreateFolder.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, CreateFolder.Response]] = {
    executeGenericSync[CreateFolder.type](params, ctx)
  }

  def getFolderPermission(
    params: GetFolderPermission.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, GetFolderPermission.Response]] = {
    executeGenericSync[GetFolderPermission.type](params, ctx)
  }

  def updateFolderPermission(
    params: UpdateFolderPermission.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, UpdateFolderPermission.Response]] = {
    executeGenericSync[UpdateFolderPermission.type](params, ctx)
  }

  def batchDownloadContent(
    params: BatchDownloadContent.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, BatchDownloadContent.Response]] = {
    val paramsM = DataRoomBatchDownloadContentWorkflowInput(
      dataRoomId = params.pathParams.dataRoomId,
      fileIds = params.bodyParams.fileIds,
      folderIds = params.bodyParams.folderIds,
      zipFileName = params.bodyParams.renamedFile,
      withWatermark = params.bodyParams.withWatermark
    )
    for {
      resp <- PublicApiWorkflowUtils.executeAsync[
        DataRoomBatchDownloadContentWorkflowInput,
        DataRoomBatchDownloadContentWorkflowOutput,
        DataRoomBatchDownloadContentWorkflow
      ](paramsM, ctx)
    } yield resp.left.map(GeneralRequestError.fromRequestError)
  }

  def batchDeleteContent(
    params: BatchDeleteContent.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, BatchDeleteContent.Response]] = {
    val paramsM = DataRoomDeleteFileFolderWorkflowInput(
      dataRoomId = params.pathParams.dataRoomId,
      fileIds = params.bodyParams.fileIds,
      folderIds = params.bodyParams.folderIds,
      isPermanent = params.bodyParams.isPermanent
    )
    for {
      resp <- PublicApiWorkflowUtils.executeAsync[
        DataRoomDeleteFileFolderWorkflowInput,
        DataRoomDeleteFileFolderWorkflowOutput,
        DataRoomDeleteFileFolderWorkflow
      ](paramsM, ctx)
    } yield resp.left.map(GeneralRequestError.fromRequestError)
  }

  def getFileInfo(
    params: GetFileInfo.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, GetFileInfo.Response]] = {
    executeGenericSync[GetFileInfo.type](params, ctx)
  }

  def getFilePermission(
    params: GetFilePermission.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, GetFilePermission.Response]] = {
    executeGenericSync[GetFilePermission.type](params, ctx)
  }

  def getFileDownloadUrl(
    params: GetFileDownloadUrl.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, GetFileDownloadUrl.Response]] = {
    executeGenericSync[GetFileDownloadUrl.type](params, ctx)
  }

  def uploadSingleFile(
    params: UploadSingleFile.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, UploadSingleFile.Response]] = {
    convertFileId(
      publicApiFileId = params.bodyParams.sourceFileId,
      actor = ctx.serviceAccount
    ) { fileId =>
      val paramsM = DataRoomUploadFilesWorkflowInput(
        dataRoomId = params.pathParams.dataRoomId,
        folderId = params.pathParams.folderId,
        fileIds = Seq(fileId),
        sendNotification = params.bodyParams.sendNotification
      )
      for {
        workflowOutput <- PublicApiWorkflowUtils.executeSync[
          DataRoomUploadFilesWorkflowInput,
          DataRoomUploadFilesWorkflowOutput,
          DataRoomPublicUploadFilesWorkflow
        ](paramsM, ctx)
        resp <- ZIO.attempt(
          workflowOutput
            .flatMap(
              _.files.headOption
                .toRight[RequestError](ServerErrorResponseProto("Empty files uploaded"))
                .map { file =>
                  UploadSingleFile.Response(fileName = file.name, fileId = file.fileId)
                }
            )
        )
      } yield resp
    }.map(_.left.map(GeneralRequestError.fromRequestError))
  }

  def deleteSingleFile(
    params: DeleteSingleFile.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, DeleteSingleFile.Response]] = {
    val paramsM = DataRoomDeleteFileFolderWorkflowInput(
      dataRoomId = params.pathParams.dataRoomWorkflowId,
      fileIds = Seq(params.pathParams.fileId),
      folderIds = Seq.empty,
      isPermanent = params.bodyParams.isPermanent
    )
    for {
      workflowOutput <- PublicApiWorkflowUtils.executeSync[
        DataRoomDeleteFileFolderWorkflowInput,
        DataRoomDeleteFileFolderWorkflowOutput,
        DataRoomDeleteFileFolderWorkflow
      ](paramsM, ctx)
      resp <- ZIO.attempt(workflowOutput.map(_ => EmptyResponse()))
    } yield resp.left.map(GeneralRequestError.fromRequestError)
  }

  def uploadNewVersion(
    params: UploadNewFileVersion.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, UploadNewFileVersion.Response]] = {
    executeGenericSync[UploadNewFileVersion.type](params, ctx)
  }

  def createGroup(
    params: CreateDataRoomGroup.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, CreateDataRoomGroup.Response]] = {
    executeGenericSync[CreateDataRoomGroup.type](params, ctx)
  }

  def renameGroup(
    params: RenameDataRoomGroup.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, RenameDataRoomGroup.Response]] = {
    executeGenericSync[RenameDataRoomGroup.type](params, ctx)
  }

  def getGroupFolderPermission(
    params: GetGroupFolderPermission.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, GetGroupFolderPermission.Response]] = {
    executeGenericSync[GetGroupFolderPermission.type](params, ctx)
  }

  def deleteGroup(
    params: DeleteDataRoomGroup.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, DeleteDataRoomGroup.Response]] = {
    executeGenericSync[DeleteDataRoomGroup.type](params, ctx)
  }

  def updateGroupPermission(
    params: UpdateDataRoomGroupPermission.Params,
    ctx: PublicApiContext
  ): Task[Either[GeneralRequestError, UpdateDataRoomGroupPermission.Response]] = {
    executeGenericSync[UpdateDataRoomGroupPermission.type](params, ctx)
  }

}
