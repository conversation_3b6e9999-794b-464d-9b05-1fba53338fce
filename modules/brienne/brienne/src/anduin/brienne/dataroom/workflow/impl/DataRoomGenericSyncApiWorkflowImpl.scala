// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.brienne.dataroom.workflow.impl

import anduin.brienne.dataroom.workflow.*
import anduin.brienne.dataroom.workflow.DataRoomGenericSyncApiOutput.Value
import anduin.brienne.dataroom.workflow.activity.*
import anduin.brienne.workflow.core.{RequestError, ServerErrorResponse}
import anduin.workflow.TemporalWorkflowService.newActivityStub
import anduin.workflow.WorkflowTask
import anduin.workflow.common.TemporalWorkflowImplCompanion

class DataRoomGenericSyncApiWorkflowImpl extends DataRoomGenericSyncApiWorkflow {

  private val dataRoomsActivity = newActivityStub[DataRoomsApiActivity]
  private val insightsActivity = newActivityStub[DataRoomInsightsApiActivity]
  private val fileFolderActivity = newActivityStub[DataRoomFileFolderApiActivity]
  private val userGroupActivity = newActivityStub[DataRoomUserGroupApiActivity]
  private val fileUploadActivity = newActivityStub[DataRoomUploadFilesActivity]

  override def runAsync(
    input: DataRoomGenericSyncApiInput
  ): WorkflowTask[DataRoomGenericSyncApiOutput] = {
    val ctx = input.ctx
    for {
      resp <- input.params match {
        case params: ListDataRoomsInputMessage =>
          WorkflowTask.executeActivity(dataRoomsActivity.listAccessibleDataRooms(params, ctx))
        case params: ListDataRoomsByEmailInputMessage =>
          WorkflowTask.executeActivity(dataRoomsActivity.listDataRoomsByEmail(params, ctx))

        case params: ListDataRoomUsersInputMessage =>
          WorkflowTask.executeActivity(userGroupActivity.listUsers(params, ctx))
        case params: InviteUserSyncInputMessage =>
          WorkflowTask.executeActivity(userGroupActivity.inviteUser(params, ctx))
        case params: AddUserToGroupSyncInputMessage =>
          WorkflowTask.executeActivity(userGroupActivity.addUserToGroup(params, ctx))
        case params: RemoveUserFromGroupSyncInputMessage =>
          WorkflowTask.executeActivity(userGroupActivity.removeUserFromGroup(params, ctx))
        case params: RemoveUserSyncInputMessage =>
          WorkflowTask.executeActivity(userGroupActivity.removeUser(params, ctx))

        case params: CreateGroupInputMessage =>
          WorkflowTask.executeActivity(userGroupActivity.createGroup(params, ctx))
        case params: RenameGroupInputMessage =>
          WorkflowTask.executeActivity(userGroupActivity.renameGroup(params, ctx))
        case params: DeleteGroupInputMessage =>
          WorkflowTask.executeActivity(userGroupActivity.deleteGroup(params, ctx))
        case params: UpdateGroupPermissionInputMessage =>
          WorkflowTask.executeActivity(userGroupActivity.updateGroupPermission(params, ctx))
        case params: GetGroupFolderPermissionInputMessage =>
          WorkflowTask.executeActivity(userGroupActivity.getGroupFolderPermission(params, ctx))

        case params: GetFileInsightsInputMessage =>
          WorkflowTask.executeActivity(insightsActivity.getFileInsights(params, ctx))
        case params: GetUserInsightsInputMessage =>
          WorkflowTask.executeActivity(insightsActivity.getUserInsights(params, ctx))
        case params: GetGroupInsightsInputMessage =>
          WorkflowTask.executeActivity(insightsActivity.getGroupInsights(params, ctx))

        case params: GetFolderStructureInputMessage =>
          WorkflowTask.executeActivity(fileFolderActivity.getFolderStructure(params, ctx))
        case params: GetFolderPermissionInputMessage =>
          WorkflowTask.executeActivity(fileFolderActivity.getFolderPermission(params, ctx))
        case params: CreateFolderInputMessage =>
          WorkflowTask.executeActivity(fileFolderActivity.createFolder(params, ctx))
        case params: UpdateFileFolderPermissionInputMessage =>
          WorkflowTask.executeActivity(fileFolderActivity.updateFolderPermission(params, ctx))

        case params: GetFileInfoInputMessage =>
          WorkflowTask.executeActivity(fileFolderActivity.getFileInfo(params, ctx))
        case params: GetFilePermissionInputMessage =>
          WorkflowTask.executeActivity(fileFolderActivity.getFilePermission(params, ctx))
        case params: GetFileDownloadUrlInputMessage =>
          WorkflowTask.executeActivity(fileFolderActivity.getFileDownloadUrl(params, ctx))
        case params: UploadNewVersionInputMessage =>
          WorkflowTask.executeActivity(fileUploadActivity.uploadNewVersion(params, ctx))

        case DataRoomGenericSyncParams.Empty =>
          WorkflowTask.succeed(
            Left[RequestError, DataRoomGenericSyncResponse](ServerErrorResponse("Failed to decode params"))
          )
      }
    } yield resp.fold(
      error => DataRoomGenericSyncApiOutput(Value.Error(Some(error))),
      response => DataRoomGenericSyncApiOutput(Value.Success(response))
    )
  }

  override def run(input: DataRoomGenericSyncApiInput): DataRoomGenericSyncApiOutput = {
    runAsync(input).getOrThrow
  }

}

object DataRoomGenericSyncApiWorkflowImpl
    extends TemporalWorkflowImplCompanion[DataRoomGenericSyncApiWorkflow, DataRoomGenericSyncApiWorkflowImpl]
