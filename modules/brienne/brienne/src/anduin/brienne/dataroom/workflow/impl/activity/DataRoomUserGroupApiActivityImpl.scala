// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.brienne.dataroom.workflow.impl.activity

import anduin.account.profile.UserProfileService
import anduin.brienne.dataroom.workflow.*
import anduin.brienne.dataroom.workflow.activity.DataRoomUserGroupApiActivity
import anduin.brienne.workflow.core.RequestError
import anduin.brienne.workflow.utils.PublicApiTemporalWorkflowService
import anduin.dataroom.email.{DataRoomEmailService, DataRoomEmailTemplateType}
import anduin.dataroom.flow.DataRoomValidateOperations.PlanCheck
import anduin.dataroom.group.{
  AddUsersToDataRoomGroupParams,
  CreateDataRoomGroupParams,
  DataRoomGroupData,
  DataRoomGroupService,
  DeleteDataRoomGroupsParams,
  RemoveUsersFromDataRoomGroupParams,
  RenameDataRoomGroupParams,
  UpdateDataRoomGroupPermissionsParams
}
import anduin.dataroom.participant.DataRoomParticipantService
import anduin.dataroom.role.DataRoomRole
import anduin.dataroom.service.DataRoomServiceUtils
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.{UserId, UserInfo}
import anduin.service.PublicApiContext
import anduin.stargazer.service.dataroom.{
  DataRoomGroupPermissionChanges,
  DataRoomPermissionChanges,
  GetEmailTemplateParams,
  InviteUsersToDataRoomParams,
  RemoveUsersFromDataRoomParams
}
import anduin.team.{TeamMemberFlowState, UserInvited, UserJoined}
import anduin.utils.ScalaUtils
import com.anduin.stargazer.endpoints.{FileFolderCommon, FileInfo, FileManagerLocation, FolderInfo}
import zio.ZIO
import anduin.brienne.dataroom.exception.DataRoomPublicException.GroupNotFoundDataRoomPublicException
import anduin.environment.EnvironmentCheck
import anduin.dms.service.FileManagerService
import anduin.dms.DmsFeature.DataRoom
import com.anduin.stargazer.service.FileServiceEndpoints.{GetFileTreeParams, PermissionTarget, TargetPermission}
import com.anduin.stargazer.service.utils.ZIOUtils
import anduin.dataroom.validator.DataRoomPublicApiValidator

final case class DataRoomUserGroupApiActivityImpl(
  temporalWorkflowService: PublicApiTemporalWorkflowService,
  userProfileService: UserProfileService,
  fileManagerService: FileManagerService,
  dataRoomParticipantService: DataRoomParticipantService,
  dataRoomEmailService: DataRoomEmailService,
  dataRoomGroupService: DataRoomGroupService,
  dataRoomPublicApiValidator: DataRoomPublicApiValidator
) extends DataRoomUserGroupApiActivity {

  override def listUsers(
    params: ListDataRoomUsersInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, ListDataRoomUsersOutputMessage] = execute(params, ctx) {
    val actor = ctx.serviceAccount
    val dataRoomId = params.dataRoomId

    for {
      _ <- ZIO.logInfo(s"$actor list data room users on params $params")
      dataRoomTuple <- DataRoomServiceUtils
        .getDataRoomState(
          actor = actor,
          dataRoomWorkflowId = dataRoomId,
          isArchived = false,
          includeInvited = false,
          includeGroup = true,
          includeToaNotAccepted = false,
          planCheck = PlanCheck.RequirePlan(Set()),
          environmentCheck = EnvironmentCheck.Bypass
        )

      allUserIds = dataRoomTuple.teamStateMap.keySet
      userInfoMap <- userProfileService.batchGetUserInfos(allUserIds)

      users = convertAllUsersInfo(
        teamStateMap = dataRoomTuple.teamStateMap,
        individualRoleMap = dataRoomTuple.participantRoles,
        userInfoMap = userInfoMap
      )

      groups = convertGroupsInfo(dataRoomTuple.groupMap)
    } yield ListDataRoomUsersOutputMessage(
      dataRoomId = dataRoomId,
      users = users,
      groups = groups
    )
  }

  override def inviteUser(
    params: InviteUserSyncInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, InviteUserSyncOutputMessage] = execute(params, ctx) {
    val actor = ctx.serviceAccount
    val dataRoomId = params.dataRoomId
    for {
      _ <- ZIO.logInfo(s"$actor invite user sync to data room $dataRoomId with groups ${params.groupIds}")
      _ <- dataRoomPublicApiValidator.validateGroupExists(
        groupIds = params.groupIds.toSet,
        dataRoomWorkflowId = dataRoomId,
        actor = actor
      )
      emailTemplate <- dataRoomEmailService
        .getEmailTemplate(
          GetEmailTemplateParams(
            dataRoomWorkflowId = dataRoomId,
            templateType = DataRoomEmailTemplateType.Invitation
          ),
          actor
        )
        .map(_.template)
      serviceActor <- getServiceActor(ctx)
      _ <- dataRoomParticipantService.inviteUsers(
        InviteUsersToDataRoomParams(
          dataRoomWorkflowId = dataRoomId,
          groupPermissionMap = Map(
            params.email -> DataRoomGroupPermissionChanges(
              groupIds = params.groupIds.toSet,
              canInvite = params.canInvite
            )
          ),
          isToaRequired = params.requireTermsOfAccess,
          subject = emailTemplate.subject,
          message = emailTemplate.body,
          buttonLabel = emailTemplate.callToAction,
          shouldSendEmail = !params.skipInvitationEmail
        ),
        actor = serviceActor
      )
    } yield InviteUserSyncOutputMessage()
  }

  override def addUserToGroup(
    params: AddUserToGroupSyncInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, AddUserToGroupSyncOutputMessage] = execute(params, ctx) {
    val actor = ctx.serviceAccount
    val dataRoomId = params.dataRoomId
    for {
      _ <- ZIO.logInfo(s"$actor add user to group ${params.groupId} in data room $dataRoomId")
      _ <- dataRoomPublicApiValidator.validateGroupExists(
        groupIds = Set(params.groupId),
        dataRoomWorkflowId = dataRoomId,
        actor = actor
      )
      toMoveUserId <- userProfileService.getUserIdFromEmailAddress(params.email).catchEmailNotFound
      _ <- dataRoomGroupService.addUsersToGroup(
        AddUsersToDataRoomGroupParams(
          groupId = params.groupId,
          userIds = Set(toMoveUserId)
        ),
        actor,
        ctx = None
      )
    } yield AddUserToGroupSyncOutputMessage()
  }

  override def removeUserFromGroup(
    params: RemoveUserFromGroupSyncInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, RemoveUserFromGroupSyncOutputMessage] = execute(params, ctx) {
    val actor = ctx.serviceAccount
    val dataRoomId = params.dataRoomId
    for {
      _ <- ZIO.logInfo(s"$actor remove user from group ${params.groupId} in data room $dataRoomId")
      _ <- dataRoomPublicApiValidator.validateGroupExists(
        groupIds = Set(params.groupId),
        dataRoomWorkflowId = dataRoomId,
        actor = actor
      )
      toRemovedUserId <- userProfileService.getUserIdFromEmailAddress(params.email).catchEmailNotFound
      _ <- dataRoomGroupService.removeUsersFromGroup(
        RemoveUsersFromDataRoomGroupParams(
          groupId = params.groupId,
          userIds = Set(toRemovedUserId)
        ),
        actor,
        ctx = None
      )
    } yield RemoveUserFromGroupSyncOutputMessage()
  }

  override def removeUser(
    params: RemoveUserSyncInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, RemoveUserSyncOutputMessage] = execute(params, ctx) {
    val actor = ctx.serviceAccount
    val dataRoomId = params.dataRoomId
    for {
      _ <- ZIO.logInfo(s"$actor remove user from data room $dataRoomId")
      toRemoveUserId <- userProfileService.getUserIdFromEmailAddress(params.email).catchEmailNotFound
      _ <- dataRoomParticipantService.removeUsers(
        RemoveUsersFromDataRoomParams(
          dataRoomWorkflowId = dataRoomId,
          userIds = Set(toRemoveUserId),
          doNotNotifyByEmail = !params.notifyEmail
        ),
        actor
      )
    } yield RemoveUserSyncOutputMessage()
  }

  override def createGroup(
    params: CreateGroupInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, CreateGroupOutputMessage] = execute(params, ctx) {
    val actor = ctx.serviceAccount
    val dataRoomId = params.dataRoomId
    for {
      _ <- ZIO.logInfo(s"$actor create group in data room $dataRoomId")
      groupId <- dataRoomGroupService
        .createGroup(
          CreateDataRoomGroupParams(
            dataRoomWorkflowId = params.dataRoomId,
            name = params.groupName,
            role = params.role,
            assetPermissions = params.contentPermission
          ),
          actor = actor,
          ctx = None
        )
        .map(_.groupId)
    } yield CreateGroupOutputMessage(groupId)
  }

  override def renameGroup(
    params: RenameGroupInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, EmptyResponseOutputMessage] = execute(params, ctx) {
    val actor = ctx.serviceAccount
    for {
      _ <- ZIO.logInfo(s"$actor rename group ${params.groupId} in data room ${params.dataRoomId}")
      _ <- dataRoomPublicApiValidator.validateGroupExists(
        groupIds = Set(params.groupId),
        dataRoomWorkflowId = params.dataRoomId,
        actor = actor
      )
      _ <- dataRoomGroupService.renameGroup(
        RenameDataRoomGroupParams(
          groupId = params.groupId,
          name = params.newGroupName
        ),
        actor = actor,
        ctx = None
      )
    } yield EmptyResponseOutputMessage()
  }

  override def deleteGroup(
    params: DeleteGroupInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, EmptyResponseOutputMessage] = execute(params, ctx) {
    val actor = ctx.serviceAccount
    for {
      _ <- ZIO.logInfo(s"$actor delete group ${params.groupId} in data room ${params.dataRoomId}")
      _ <- dataRoomPublicApiValidator.validateGroupExists(
        groupIds = Set(params.groupId),
        dataRoomWorkflowId = params.dataRoomId,
        actor = actor
      )
      participantSettings =
        if (params.shouldRemoveUsers) {
          DeleteDataRoomGroupsParams.ParticipantSettings.Remove(params.shouldNotifyEmail)
        } else {
          DeleteDataRoomGroupsParams.ParticipantSettings.Keep
        }
      _ <- dataRoomGroupService.deleteGroups(
        DeleteDataRoomGroupsParams(
          dataRoomWorkflowId = params.dataRoomId,
          groupIds = Set(params.groupId),
          participantSettings = participantSettings
        ),
        actor = actor,
        ctx = None
      )
    } yield EmptyResponseOutputMessage()
  }

  override def updateGroupPermission(
    params: UpdateGroupPermissionInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, EmptyResponseOutputMessage] = execute(params, ctx) {
    val actor = ctx.serviceAccount
    for {
      _ <- ZIO.logInfo(s"$actor update group ${params.groupId} permission in data room ${params.dataRoomId}")
      _ <- dataRoomPublicApiValidator.validateGroupExists(
        groupIds = Set(params.groupId),
        dataRoomWorkflowId = params.dataRoomId,
        actor = actor
      )
      _ <- dataRoomGroupService.updateGroupPermission(
        UpdateDataRoomGroupPermissionsParams(
          groupId = params.groupId,
          permissionChanges = DataRoomPermissionChanges(
            roleSet = params.role.map(_.value),
            assetPermissions = params.permissionChange
          )
        ),
        actor = actor,
        ctx = None
      )
    } yield EmptyResponseOutputMessage()
  }

  override def getGroupFolderPermission(
    params: GetGroupFolderPermissionInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, GetGroupFolderPermissionOutputMessage] = execute(params, ctx) {
    val actor = ctx.serviceAccount
    val dataRoomId = params.dataRoomId
    for {
      _ <- ZIO.logInfo(
        s"$actor get group ${params.groupId} folder ${params.folderId} permission in data room $dataRoomId"
      )
      _ <- dataRoomPublicApiValidator.validateAssetExists(
        fileIds = Set.empty,
        folderIds = Set(params.folderId),
        dataRoomWorkflowId = params.dataRoomId,
        actor = actor
      )
      allGroups <- dataRoomGroupService.getAllGroups(
        dataRoomId,
        actor
      )
      teamId <- ZIOUtils.fromOption(
        allGroups.find(_.id.contains(params.groupId)).map(_.teamId),
        GroupNotFoundDataRoomPublicException(dataRoomId, Set(params.groupId))
      )
      folderData <- fileManagerService.getFileTree(
        GetFileTreeParams(
          location = FileManagerLocation.Folder(viewKeyOpt = None, folderId = params.folderId),
          includeAllFileIds = true,
          permissionTargetOpt = Some(PermissionTarget.SingleTeam(teamId))
        ),
        actor,
        environmentCheck = EnvironmentCheck.Bypass
      )
      targetPermission = folderData.targetPermissionOpt.getOrElse(TargetPermission.empty)
      rootFolderPermission = targetPermission.parentPermission
      (subFolders, files) = folderData.data.subItems
        .flatMap {
          case fileFolder: FileFolderCommon => Some(fileFolder)
          case _                            => None
        }
        .partitionMap {
          case folder: FolderInfo =>
            Left(
              FolderUserGroupPermission(
                folder = FolderBasicInfo(folder.itemId, folder.name),
                permission = targetPermission.folders.get(folder.itemId)
              )
            )
          case file: FileInfo =>
            Right(
              FileUserGroupPermission(
                file = FileBasicInfo(file.itemId, file.name),
                permission = targetPermission.files.get(file.itemId)
              )
            )
        }
    } yield GetGroupFolderPermissionOutputMessage(
      permission = rootFolderPermission,
      subFolders = subFolders,
      files = files
    )
  }

  private def convertAllUsersInfo(
    teamStateMap: Map[UserId, TeamMemberFlowState],
    individualRoleMap: Map[UserId, DataRoomRole],
    userInfoMap: Map[UserId, UserInfo]
  ): Seq[DataRoomUserInfo] = {
    ScalaUtils
      .intersectMap(teamStateMap, individualRoleMap, userInfoMap)
      .map { case (userId, (teamState, role, userInfo)) =>
        DataRoomUserInfo(
          userId = userId,
          email = userInfo.emailAddressStr,
          firstName = userInfo.firstName,
          lastName = userInfo.lastName,
          joinedAt = ScalaUtils.downcastOpt[TeamMemberFlowState, UserJoined](teamState).flatMap(_.joinedAt),
          invitedAt = ScalaUtils.downcastOpt[TeamMemberFlowState, UserInvited](teamState).flatMap(_.invitedAt),
          role = role
        )
      }
      .toSeq
  }

  private def convertGroupsInfo(
    groupDataMap: Map[DataRoomGroupId, DataRoomGroupData]
  ): Seq[DataRoomGroupInfo] = {
    groupDataMap.values.map { groupData =>
      DataRoomGroupInfo(
        groupId = groupData.id,
        name = groupData.name,
        role = groupData.role,
        userIds = groupData.participants.toSet,
        createdAt = groupData.createdAt,
        createdBy = groupData.creator
      )
    }.toSeq
  }

}
