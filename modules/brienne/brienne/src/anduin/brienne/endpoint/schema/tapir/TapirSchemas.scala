// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.brienne.endpoint.schema.tapir

import scalapb.UnknownFieldSet
import sttp.tapir.Schema.SName
import sttp.tapir.SchemaType.{SBoolean, SOption, SProductField, SString}
import sttp.tapir.macros.CreateDerivedEnumerationSchema
import sttp.tapir.{FieldName, Schema, SchemaType, ValidationResult, Validator}

import anduin.brienne.fundsub.api.*
import anduin.brienne.fundsub.api.external.{
  FundSubRequestStatusResponse,
  GeneralFundSubApiResponse,
  GeneralRequestError,
  RequestChangeType
}
import anduin.brienne.workflow.core.*
import anduin.evendim.model.datalake.OrderStatus
import anduin.protobuf.amlcheck.{AmlCheckEntityRole, AmlCheckInvestorType, AmlCheckProvider, AmlCheckStatus}

object TapirSchemas
    extends OrderSchemas
    with WebhookSchemas
    with FormFieldSchemas
    with FundDataSchemas
    with FundSubscriptionSchemas
    with NewWebhookSchemas
    with EmailTemplateSchemas {

  given unknownFieldSetSchema: Schema[UnknownFieldSet] =
    Schema.schemaForUnit.as[UnknownFieldSet].hidden(false)

  given authorizationResponseSchema: Schema[GeneralRequestError.AuthorizationResponse] = Schema
    .derived[GeneralRequestError.AuthorizationResponse]

  given validationResponseSchema: Schema[GeneralRequestError.ValidationResponse] = Schema
    .derived[GeneralRequestError.ValidationResponse]

  given forbiddenResponseSchema: Schema[GeneralRequestError.ForbiddenResponse] = Schema
    .derived[GeneralRequestError.ForbiddenResponse]

  given notFoundResponseSchema: Schema[GeneralRequestError.NotFoundResponse] = Schema
    .derived[GeneralRequestError.NotFoundResponse]

  given serverErrorResponseSchema: Schema[GeneralRequestError.ServerErrorResponse] = Schema
    .derived[GeneralRequestError.ServerErrorResponse]

  given requestErrorSchema: Schema[GeneralRequestError] =
    Schema.oneOfUsingField[GeneralRequestError, String](
      _.errorType,
      identity
    )(
      "AuthorizationResponse" -> authorizationResponseSchema,
      "ValidationResponse" -> validationResponseSchema,
      "ServerErrorResponse" -> serverErrorResponseSchema
    )

  given publicApiRequestStatusSchema: Schema[PublicApiRequestStatus] = {
    new CreateDerivedEnumerationSchema[PublicApiRequestStatus](
      Validator.Enumeration(
        possibleValues = List(
          PublicApiRequestStatus.Pending,
          PublicApiRequestStatus.Completed,
          PublicApiRequestStatus.Failed,
          PublicApiRequestStatus.InProcessing
        ),
        encode = Option((a: PublicApiRequestStatus) => Option(a.value)),
        name = Option(SName("RequestStatus"))
      )
    ).apply(
      schemaType = SchemaType.SInteger[PublicApiRequestStatus](),
      default = Option(PublicApiRequestStatus.Pending)
    ).description(
      """
        |The status of the request
        |There can be 4 values:
        |* `0 - Pending`: the request is received but the execution is not yet started
        |* `1 - Completed`: the request is completed
        |* `2 - Failed`: the request is failed
        |* `3 - In Processing`: the request is executing
        |""".stripMargin
    )
  }

  given publicApiRequestStatusIntSchema: Schema[Int] = {
    new CreateDerivedEnumerationSchema[Int](
      Validator.Enumeration(
        possibleValues = List(
          PublicApiRequestStatus.Pending,
          PublicApiRequestStatus.Completed,
          PublicApiRequestStatus.Failed,
          PublicApiRequestStatus.InProcessing
        ).map(_.value),
        encode = Option(a => Option(a)),
        name = Option(SName("RequestStatus"))
      )
    ).apply(
      schemaType = SchemaType.SInteger[Int](),
      default = Option(PublicApiRequestStatus.Pending.value)
    ).description(
      """
        |The status of the request
        |There can be 4 values:
        |* `0 - Pending`: the request is received but the execution is not yet started
        |* `1 - Completed`: the request is completed
        |* `2 - Failed`: the request is failed
        |* `3 - In Processing`: the request is executing
        |""".stripMargin
    )
  }

  given singleOrderResponseSchema: Schema[SingleOrderResponse] = Schema.derived[SingleOrderResponse]

  given testApiBodyParamsSchema: Schema[TestApiBodyParams] = Schema.derived[TestApiBodyParams]

  given getFileDownloadUrlGeneralResponseSchema: Schema[GeneralFundSubApiResponse.GetFileDownloadUrlResponse] =
    Schema.derived[GeneralFundSubApiResponse.GetFileDownloadUrlResponse]

  given getOrdersFormDataGeneralResponseSchema: Schema[GeneralFundSubApiResponse.GetOrdersFormDataResponse] =
    Schema.derived[GeneralFundSubApiResponse.GetOrdersFormDataResponse]

  given getBulkSingleOrderGeneralResponseSchema: Schema[GeneralFundSubApiResponse.SingleOrderResponse] =
    Schema
      .derived[GeneralFundSubApiResponse.SingleOrderResponse]
      .modify(
        _.status
      )(_ => publicApiRequestStatusIntSchema)

  given getBulkCreateOrdersGeneralResponseSchema: Schema[GeneralFundSubApiResponse.BulkCreateOrdersResponse] =
    Schema.derived[GeneralFundSubApiResponse.BulkCreateOrdersResponse]

  given activateOfflineOrderGeneralResponseSchema: Schema[GeneralFundSubApiResponse.ActivateOfflineOrderResponse] =
    Schema.derived[GeneralFundSubApiResponse.ActivateOfflineOrderResponse]

  given getFundInvitationLinkGeneralResponseSchema: Schema[GeneralFundSubApiResponse.GetFundInvitationLinkResponse] =
    Schema.derived[GeneralFundSubApiResponse.GetFundInvitationLinkResponse]

  given createWebhookGeneralResponseSchema: Schema[GeneralFundSubApiResponse.CreateWebhookResponse] =
    Schema.derived[GeneralFundSubApiResponse.CreateWebhookResponse]

  given getRequestResultGeneralResponseSchema: Schema[GeneralFundSubApiResponse.GetRequestResultResponse] = {
    Schema(
      schemaType = SchemaType.SProduct(
        List(
          SchemaType.SProductField[GeneralFundSubApiResponse.GetRequestResultResponse, Int](
            FieldName("status"),
            publicApiRequestStatusIntSchema,
            response => Option(response.status)
          ),
          SchemaType.SProductField[GeneralFundSubApiResponse.GetRequestResultResponse, Option[GeneralFundSubApiResponse]](
            FieldName("output"),
            Schema[GeneralFundSubApiResponse](
              schemaType = SchemaType.SRef(
                Schema.SName(GeneralFundSubApiResponse.getFullName)
              ),
              isOptional = true
            ).asOption,
            response => Option(response.output)
          ),
          SchemaType.SProductField[GeneralFundSubApiResponse.GetRequestResultResponse, Option[GeneralRequestError]](
            FieldName("error"),
            requestErrorSchema.asOption,
            response => Option(response.error)
          )
        )
      ),
      name = Some(SName(GeneralFundSubApiResponse.GetRequestResultResponse.getFullName))
    )
  }

  given webhookInfoGeneralSchema: Schema[GeneralFundSubApiResponse.WebhookInfo] =
    Schema
      .derived[GeneralFundSubApiResponse.WebhookInfo]
      .modify(_.enabledEvents)(_ => webhookEventsListSchema)
      .modify(_.url)(_.validate(TapirValidators.urlValidator).copy(isOptional = false))

  given getAllFundWebhooksGeneralSchema: Schema[GeneralFundSubApiResponse.GetAllFundWebhooksResponse] =
    Schema.derived[GeneralFundSubApiResponse.GetAllFundWebhooksResponse]

  given removeWebhookGeneralResponseSchema: Schema[GeneralFundSubApiResponse.RemoveWebhookResponse] =
    Schema.derived[GeneralFundSubApiResponse.RemoveWebhookResponse]

  given updateWebhookGeneralResponseSchema: Schema[GeneralFundSubApiResponse.UpdateWebhookResponse] =
    Schema
      .derived[GeneralFundSubApiResponse.UpdateWebhookResponse]
      .modify(_.enabledEvents)(_ => webhookEventsListSchema)

  given getStandardFormFieldsGeneralResponseSchema: Schema[GeneralFundSubApiResponse.GetStandardFormFieldsResponse] =
    Schema.derived[GeneralFundSubApiResponse.GetStandardFormFieldsResponse]

  given getStandardFormFieldGeneralResponseSchema: Schema[GeneralFundSubApiResponse.GetStandardFormFieldResponse] =
    Schema.derived[GeneralFundSubApiResponse.GetStandardFormFieldResponse]

  given testApiGeneralResponseSchema: Schema[GeneralFundSubApiResponse.TestApiResponse] =
    Schema.derived[GeneralFundSubApiResponse.TestApiResponse]

  given generalFundSubApiResponseSchema: Schema[GeneralFundSubApiResponse] =
    Schema.oneOfUsingField[GeneralFundSubApiResponse, String](_.responseType, identity)(
      "GetFileDownloadUrlResponse" -> getFileDownloadUrlGeneralResponseSchema,
      "GetOrdersFormDataResponse" -> getOrdersFormDataGeneralResponseSchema,
      "GetFundInvitationLinkResponse" -> getFundInvitationLinkGeneralResponseSchema,
      "ActivateOfflineOrderResponse" -> activateOfflineOrderGeneralResponseSchema,
      "CreateWebhookResponse" -> createWebhookGeneralResponseSchema,
      "WebhookInfo" -> webhookInfoGeneralSchema,
      "GetAllFundWebhooksResponse" -> getAllFundWebhooksGeneralSchema,
      "RemoveWebhookResponse" -> removeWebhookGeneralResponseSchema,
      "UpdateWebhookResponse" -> updateWebhookGeneralResponseSchema,
      "GetStandardFormFieldsResponse" -> getStandardFormFieldsGeneralResponseSchema,
      "GetStandardFormFieldResponse" -> getStandardFormFieldGeneralResponseSchema,
      "BulkCreateOrdersResponse" -> getBulkCreateOrdersGeneralResponseSchema,
      "TestApiResponse" -> testApiGeneralResponseSchema
    )

  given fundSubRequestStatusResponseSchema: Schema[FundSubRequestStatusResponse] =
    Schema
      .derived[FundSubRequestStatusResponse]
      .modify(_.status) { _ => publicApiRequestStatusIntSchema }

  given updateCloseParamsSchema: Schema[anduin.brienne.fundsub.api.external.UpdateCloseParams] = Schema(
    schemaType = SchemaType.SProduct[anduin.brienne.fundsub.api.external.UpdateCloseParams](
      List(
        SProductField(
          _name = FieldName("name"),
          _schema = Schema[String](
            isOptional = true,
            schemaType = SString(),
            encodedExample = Option("Close 2")
          ),
          _get = params => params.name
        ),
        SProductField(
          _name = FieldName("customId"),
          _schema = Schema[String](
            schemaType = SString(),
            isOptional = true,
            description = Option("Custom ID assigned by user to the Close"),
            encodedExample = Option("Close123")
          ),
          _get = params => params.customId
        ),
        SProductField(
          _name = FieldName("targetDate"),
          _schema = Schema[String](
            schemaType = SString(),
            isOptional = true,
            description = Option("Date string in MM/dd/yyyy format"),
            encodedExample = Option("04/20/2024")
          ),
          _get = params => params.targetDate
        )
      )
    )
  )

  given createCloseParamsSchema: Schema[anduin.brienne.fundsub.api.external.CreateCloseParams] = Schema(
    schemaType = SchemaType.SProduct[anduin.brienne.fundsub.api.external.CreateCloseParams](
      List(
        SProductField(
          _name = FieldName("name"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option("Close 2")
          ),
          _get = params => Option(params.name)
        ),
        SProductField(
          _name = FieldName("customId"),
          _schema = Schema[String](
            schemaType = SString(),
            isOptional = true,
            description = Option("Custom ID assigned by user to the Close"),
            encodedExample = Option("Close123")
          ),
          _get = params => params.customId
        ),
        SProductField(
          _name = FieldName("targetDate"),
          _schema = Schema[String](
            schemaType = SString(),
            isOptional = true,
            description = Option("Date string in MM/dd/yyyy format"),
            encodedExample = Option("04/20/2024")
          ),
          _get = params => params.targetDate
        )
      )
    )
  )

  given createCloseResponseSchema: Schema[anduin.brienne.fundsub.api.external.CreateCloseResponse] =
    Schema(
      schemaType = SchemaType.SProduct[anduin.brienne.fundsub.api.external.CreateCloseResponse](
        List(
          SProductField(
            _name = FieldName("id"),
            _schema = fundSubCloseIdSchema,
            _get = params => Option(params.id)
          ),
          SProductField(
            _name = FieldName("name"),
            _schema = Schema[String](
              schemaType = SString(),
              encodedExample = Option("Close 2")
            ),
            _get = params => Option(params.name)
          ),
          SProductField(
            _name = FieldName("customId"),
            _schema = Schema[String](
              schemaType = SString(),
              description = Option("Custom ID assigned by user to the Close"),
              encodedExample = Option("Close123")
            ),
            _get = params => Option(params.customId)
          ),
          SProductField(
            _name = FieldName("targetDate"),
            _schema = Schema[String](
              schemaType = SString(),
              description = Option("Date string in MM/dd/yyyy format"),
              encodedExample = Option("04/20/2024")
            ),
            _get = params => Option(params.targetDate)
          )
        )
      )
    )

  given updateCloseResponseSchema: Schema[anduin.brienne.fundsub.api.external.UpdateCloseResponse] =
    Schema(
      schemaType = SchemaType.SProduct[anduin.brienne.fundsub.api.external.UpdateCloseResponse](
        List(
          SProductField(
            _name = FieldName("id"),
            _schema = fundSubCloseIdSchema,
            _get = params => Option(params.id)
          ),
          SProductField(
            _name = FieldName("name"),
            _schema = Schema[String](
              schemaType = SString(),
              encodedExample = Option("Close 2")
            ),
            _get = params => Option(params.name)
          ),
          SProductField(
            _name = FieldName("customId"),
            _schema = Schema[String](
              schemaType = SString(),
              description = Option("Custom ID assigned by user to the Close"),
              encodedExample = Option("Close123")
            ),
            _get = params => Option(params.customId)
          ),
          SProductField(
            _name = FieldName("targetDate"),
            _schema = Schema[String](
              schemaType = SString(),
              description = Option("Date string in MM/dd/yyyy format"),
              encodedExample = Option("04/20/2024")
            ),
            _get = params => Option(params.targetDate)
          )
        )
      )
    )

  given updateOrderParamsSchema: Schema[anduin.brienne.fundsub.api.external.UpdateOrderParams] =
    Schema(
      schemaType = SchemaType.SProduct[anduin.brienne.fundsub.api.external.UpdateOrderParams](
        List(
          SProductField(
            _name = FieldName("closeId"),
            _schema = fundSubCloseIdSchema.asOption,
            _get = params => Option(params.closeId)
          ),
          SProductField(
            _name = FieldName("customId"),
            _schema = Schema[String](
              schemaType = SString(),
              isOptional = true,
              description = Option("Custom ID assigned by user to the Order"),
              encodedExample = Option("Investor123")
            ),
            _get = params => params.customId
          ),
          SProductField(
            _name = FieldName("metadata"),
            _schema = Schema[Option[Map[String, String]]](
              schemaType = SchemaType.SProduct[Option[Map[String, String]]](
                fields = List.empty
              ),
              isOptional = true,
              name = Option(SName("metadata")),
              description = Option("Order metadata"),
              encodedExample = Option("""{"Opportunity Id": "ID12345", "Client Name": "JV VC"}""")
            ),
            _get = params => Option(params.metadata)
          ),
          SProductField(
            _name = FieldName("addTags"),
            _schema = Schema[Option[List[String]]](
              schemaType = SchemaType.SProduct[Option[List[String]]](
                fields = List.empty
              ),
              isOptional = true,
              description = Option("New tags that will be added to this order"),
              encodedExample = Option("[\"Processed\", \"Batch 2\"]")
            ),
            _get = params => Option(params.addTags)
          ),
          SProductField(
            _name = FieldName("removeTags"),
            _schema = Schema[Option[List[String]]](
              schemaType = SchemaType.SProduct[Option[List[String]]](
                fields = List.empty
              ),
              isOptional = true,
              description = Option("Tags that will be removed from this order"),
              encodedExample = Option("[\"Not processed\", \"AML/KYC pending\"]")
            ),
            _get = params => Option(params.removeTags)
          )
        )
      )
    )

  given updateOrderResponseSchema: Schema[anduin.brienne.fundsub.api.external.UpdateOrderResponse] = Schema(
    schemaType = SchemaType.SProduct[anduin.brienne.fundsub.api.external.UpdateOrderResponse](
      List(
        SProductField(
          _name = FieldName("id"),
          _schema = fundSubLpIdSchema,
          _get = params => Option(params.id)
        ),
        SProductField(
          _name = FieldName("closeId"),
          _schema = fundSubCloseIdSchema,
          _get = params => Option(params.closeId)
        ),
        SProductField(
          _name = FieldName("customId"),
          _schema = Schema[String](
            schemaType = SString(),
            isOptional = true,
            description = Option("Custom ID assigned by user to the Order"),
            encodedExample = Option("Investor123")
          ),
          _get = params => Option(params.customId)
        ),
        SProductField(
          _name = FieldName("metadata"),
          _schema = Schema[Map[String, String]](
            schemaType = SchemaType.SProduct[Map[String, String]](
              fields = List.empty
            ),
            isOptional = true,
            name = Option(SName("metadata")),
            description = Option("Order metadata"),
            encodedExample = Option("""{"Opportunity Id": "ID12345", "Client Name": "JV VC"}""")
          ),
          _get = params => Option(params.metadata)
        ),
        SProductField(
          _name = FieldName("tags"),
          _schema = Schema[Seq[String]](
            schemaType = SchemaType.SProduct[Seq[String]](
              fields = List.empty
            ),
            isOptional = true,
            description = Option("Order tags"),
            encodedExample = Option("""["Processing", "AML/KYC Validating"]""")
          ),
          _get = params => Option(params.tags)
        )
      )
    )
  )

  given removeOrderParamsSchema: Schema[anduin.brienne.fundsub.api.external.RemoveOrderParams] =
    Schema(
      schemaType = SchemaType.SProduct[anduin.brienne.fundsub.api.external.RemoveOrderParams](
        List(
          SProductField(
            _name = FieldName("sendEmail"),
            _schema = Schema[Boolean](
              schemaType = SBoolean(),
              isOptional = true,
              description = Option("Should send email to the investor and collaborators about the order removal."),
              default = Option(false, Option("false")),
              encodedExample = Option("false")
            ),
            _get = params => Option(params.sendEmail)
          )
        )
      )
    )

  given addCollaboratorParamsSchema: Schema[anduin.brienne.fundsub.api.external.AddCollaboratorParams] = Schema(
    schemaType = SchemaType.SProduct[anduin.brienne.fundsub.api.external.AddCollaboratorParams](
      List(
        SProductField(
          _name = FieldName("email"),
          _schema = emailSchema,
          _get = params => Option(params.email)
        ),
        SProductField(
          _name = FieldName("firstName"),
          _schema = Schema[String](
            schemaType = SString(),
            description = Option("First Name"),
            encodedExample = Option("John")
          ),
          _get = params => Option(params.firstName)
        ),
        SProductField(
          _name = FieldName("lastName"),
          _schema = Schema[String](
            schemaType = SString(),
            description = Option("Last Name"),
            encodedExample = Option("Doe")
          ),
          _get = params => Option(params.lastName)
        ),
        SProductField(
          _name = FieldName("emailTemplate"),
          _schema = emailTemplateParamsSchema
            .copy(
              isOptional = true
            )
            .asOption,
          _get = params => Some(params.emailTemplate)
        ),
        SProductField(
          _name = FieldName("enableSso"),
          _schema = Schema[Boolean](
            schemaType = SBoolean(),
            isOptional = true,
            description = Option("Should SSO be enabled on the invitation email link."),
            default = Option(false, Option("false")),
            encodedExample = Option("false")
          ),
          _get = params => Option(params.enableSso)
        )
      )
    )
  )

  given addCollaboratorResponseSchema: Schema[external.AddCollaboratorResponse] = Schema(
    schemaType = SchemaType.SProduct(
      List(
        SProductField(
          _name = FieldName("userId"),
          _schema = userIdSchema,
          _get = params => Option(params.userId)
        ),
        SProductField(
          _name = FieldName("email"),
          _schema = Schema[String](
            schemaType = SString(),
            validator = TapirValidators.emailValidator,
            encodedExample = Option("<EMAIL>")
          ),
          _get = params => Option(params.email)
        )
      )
    )
  )

  given removeCollaboratorParamsSchema: Schema[external.RemoveCollaboratorParams] = Schema(
    schemaType = SchemaType.SProduct[external.RemoveCollaboratorParams](
      List(
        SProductField(
          _name = FieldName("email"),
          _schema = emailSchema,
          _get = params => Option(params.email)
        )
      )
    )
  )

  given amlCheckSchema: Schema[external.AmlCheck] = Schema.derived[external.AmlCheck]

  given amlCheckInfoSchema: Schema[external.AmlCheckInfo] = Schema(
    schemaType = SchemaType.SProduct[external.AmlCheckInfo](
      List(
        SProductField(
          _name = FieldName("orderBelongTo"),
          _schema = fundSubLpIdSchema,
          _get = params => Option(params.orderBelongTo)
        ),
        SProductField(
          _name = FieldName("status"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option(AmlCheckStatus.RESULT_AVAILABLE.name),
            validator = Validator.custom[String] { value =>
              if (AmlCheckStatus.values.map(_.name).contains(value)) {
                ValidationResult.Valid
              } else {
                ValidationResult.Invalid(s"status must be one of: ${AmlCheckStatus.values.map(_.name).mkString(", ")}")
              }
            },
            description = Option(
              s"Aml check status of the entity. Should be one of: ${AmlCheckStatus.values.map(_.name).mkString(", ")}"
            )
          ),
          _get = params => Option(params.status)
        ),
        SProductField(
          _name = FieldName("provider"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option(AmlCheckProvider.WORLDCHECK.name),
            validator = Validator.custom[String] { value =>
              if (AmlCheckProvider.values.map(_.name).contains(value)) {
                ValidationResult.Valid
              } else {
                ValidationResult.Invalid(s"status must be one of: ${AmlCheckProvider.values.map(_.name).mkString(", ")}")
              }
            },
            description = Option(
              s"Aml check provider of the entity. Should be one of: ${AmlCheckProvider.values.map(_.name).mkString(", ")}"
            )
          ),
          _get = params => Option(params.provider)
        ),
        SProductField(
          _name = FieldName("role"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option(AmlCheckEntityRole.INVESTOR.name),
            validator = Validator.custom[String] { value =>
              if (AmlCheckEntityRole.values.map(_.name).contains(value)) {
                ValidationResult.Valid
              } else {
                ValidationResult.Invalid(s"role must be one of: ${AmlCheckEntityRole.values.map(_.name).mkString(", ")}")
              }
            },
            description =
              Option(s"Role of the entity. Should be one of: ${AmlCheckEntityRole.values.map(_.name).mkString(", ")})")
          ),
          _get = params => Option(params.role)
        ),
        SProductField(
          _name = FieldName("name"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option("John Doe")
          ),
          _get = params => Option(params.name)
        ),
        SProductField(
          _name = FieldName("dateOfBirth"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option("2000-01-01")
          ),
          _get = params => Option(params.dateOfBirth)
        ),
        SProductField(
          _name = FieldName("dateOfFormation"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option("2000-01-01")
          ),
          _get = params => Option(params.dateOfFormation)
        ),
        SProductField(
          _name = FieldName("postalCode"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option("12345")
          ),
          _get = params => Option(params.postalCode)
        ),
        SProductField(
          _name = FieldName("investorType"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option(AmlCheckInvestorType.INDIVIDUAL.name),
            validator = Validator.custom[String] { value =>
              if (AmlCheckInvestorType.values.map(_.name).contains(value)) {
                ValidationResult.Valid
              } else {
                ValidationResult.Invalid(
                  s"investor type must be one of: ${AmlCheckInvestorType.values.map(_.name).mkString(", ")}"
                )
              }
            },
            description = Option(
              s"Investor type of the entity. Should be one of: ${AmlCheckInvestorType.values.map(_.name).mkString(", ")})"
            )
          ),
          _get = params => Option(params.investorType)
        ),
        SProductField(
          _name = FieldName("country"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option("Canada")
          ),
          _get = params => Option(params.country)
        ),
        SProductField(
          _name = FieldName("jurisdiction"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option("CA")
          ),
          _get = params => Option(params.jurisdiction)
        ),
        SProductField(
          _name = FieldName("identificationNumber"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option("*********")
          ),
          _get = params => Option(params.identificationNumber)
        ),
        SProductField(
          _name = FieldName("identificationType"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option("passport")
          ),
          _get = params => Option(params.identificationType)
        ),
        SProductField(
          _name = FieldName("streetAddress"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option("123 Main St")
          ),
          _get = params => Option(params.streetAddress)
        ),
        SProductField(
          _name = FieldName("stateOrProvince"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option("BC")
          ),
          _get = params => Option(params.stateOrProvince)
        ),
        SProductField(
          _name = FieldName("city"),
          _schema = Schema[String](
            schemaType = SString(),
            encodedExample = Option("Vancouver")
          ),
          _get = params => Option(params.city)
        ),
        SProductField(
          _name = FieldName("externalLink"),
          _schema = Schema[Option[String]](
            schemaType = SOption(
              Schema[String](
                schemaType = SString(),
                encodedExample = Option("https://anduintransact.com/"),
                validator = TapirValidators.urlValidator,
                description = Option(s"External link to third party provide aml check, should be a link")
              )
            )(identity)
          ),
          _get = params => Option(params.externalLink)
        )
      )
    )
  )

  given createAmlCheckParamsSchema: Schema[external.CreateAmlCheckParams] = Schema.derived[external.CreateAmlCheckParams]

  given createAmlCheckResponseSchema: Schema[external.CreateAmlCheckResponse] =
    Schema.derived[external.CreateAmlCheckResponse]

  given updateAmlCheckParamsSchema: Schema[external.UpdateAmlCheckParams] =
    Schema.derived[external.UpdateAmlCheckParams]

  given updateAmlCheckSchema: Schema[external.UpdateAmlCheck] = Schema(
    schemaType = SchemaType.SProduct[external.UpdateAmlCheck](
      List(
        SProductField(
          _name = FieldName("id"),
          _schema = amlCheckIdSchema,
          _get = params => Option(params.id)
        ),
        SProductField(
          _name = FieldName("status"),
          _schema = Schema[Option[String]](
            schemaType = SOption(
              Schema[String](
                schemaType = SString(),
                encodedExample = Option(AmlCheckStatus.RESULT_AVAILABLE.name),
                validator = Validator.custom[String] { value =>
                  if (AmlCheckStatus.values.map(_.name).contains(value)) {
                    ValidationResult.Valid
                  } else {
                    ValidationResult.Invalid(
                      s"status must be one of: ${AmlCheckStatus.values.map(_.name).mkString(", ")}"
                    )
                  }
                },
                description = Option(
                  s"Aml check status of the entity. Should be one of: ${AmlCheckStatus.values.map(_.name).mkString(", ")}"
                )
              )
            )(identity)
          ),
          _get = params => Option(params.status)
        ),
        SProductField(
          _name = FieldName("externalLink"),
          _schema = Schema[Option[String]](
            schemaType = SOption(
              Schema[String](
                schemaType = SString(),
                encodedExample = Option("https://anduintransact.com/"),
                validator = TapirValidators.urlValidator,
                description = Option(s"External link to third party provide aml check, should be a link")
              )
            )(identity)
          ),
          _get = params => Option(params.externalLink)
        )
      )
    )
  )

  given getAmlCheckByOrderResponseSchema: Schema[external.GetAmlCheckByOrderResponse] =
    Schema.derived[external.GetAmlCheckByOrderResponse]

  given getOrdersWithoutAmlCheckParamsSchema: Schema[external.GetOrdersWithoutAmlCheckParams] =
    Schema(
      schemaType = SchemaType.SProduct[external.GetOrdersWithoutAmlCheckParams](
        List(
          SProductField(
            _name = FieldName("orderStatusFilter"),
            _schema = Schema[Option[String]](
              schemaType = SOption(
                Schema[String](
                  schemaType = SString(),
                  encodedExample = Option(OrderStatus.SUBMITTED.value),
                  validator = Validator.custom[String] { value =>
                    if (OrderStatus.values.map(_.value).contains(value)) {
                      ValidationResult.Valid
                    } else {
                      ValidationResult.Invalid(s"status must be one of: ${OrderStatus.values.mkString(", ")}")
                    }
                  },
                  description = Option(s"Order status to filter. Should be one of: ${OrderStatus.values.mkString(", ")}")
                )
              )(identity)
            ),
            _get = params => Option(params.orderStatusFilter)
          )
        )
      )
    )

  given getOrdersWithoutAmlCheckResponseSchema: Schema[external.GetOrdersWithoutAmlCheckResponse] =
    Schema.derived[external.GetOrdersWithoutAmlCheckResponse]

  given triggerCustomFlowParamsSchema: Schema[external.TriggerCustomFlowParams] = Schema(
    schemaType = SchemaType.SProduct[external.TriggerCustomFlowParams](
      List(
        SProductField(
          _name = FieldName("flowName"),
          _schema = Schema[String](
            schemaType = SString(),
            description = Option("Name of the flow you want to trigger"),
            encodedExample = Option("Get Form Data with Correspondences list")
          ),
          _get = params => Option(params.flowName)
        ),
        SProductField(
          _name = FieldName("params"),
          _schema = Schema[String](
            schemaType = SString(),
            description = Option("Parameter of the target flow")
          ),
          _get = params => Option(params.params.toString)
        )
      )
    )
  )

  given triggerCustomFlowResponseSchema: Schema[external.TriggerCustomFlowResponse] = Schema(
    schemaType = SchemaType.SProduct[external.TriggerCustomFlowResponse](
      List(
        SProductField(
          _name = FieldName("result"),
          _schema = Schema[String](
            schemaType = SString(),
            description = Option("Result of the triggered flow")
          ),
          _get = params => Option(params.result.toString)
        )
      )
    )
  )

  given emptyResponseSchema: Schema[external.EmptyResponse] = Schema(
    schemaType = SchemaType.SProduct[external.EmptyResponse](
      List()
    )
  )

  given submitNewSubscriptionFormVersionParamsSchema: Schema[external.SubmitNewSubscriptionFormVersionParams] = Schema
    .derived[external.SubmitNewSubscriptionFormVersionParams]
    .modify(
      _.formData
    )(_.description("Import data to the specified template `templateId`").copy(isOptional = true))

  given submitNewSubscriptionFormVersionResponseSchema: Schema[external.SubmitNewSubscriptionFormVersionResponse] =
    Schema
      .derived[external.SubmitNewSubscriptionFormVersionResponse]
      .modify(
        _.newVersionIndex
      )(_.description("The new subscription form version"))

  given requestChangeTypeSchema: Schema[RequestChangeType] = {
    new CreateDerivedEnumerationSchema[RequestChangeType](
      Validator.Enumeration(
        possibleValues = RequestChangeType.values.toList,
        encode = Option((a: RequestChangeType) => Option(a.toString)),
        name = Option(SName("RequestChangeType"))
      )
    ).apply(
      schemaType = SchemaType.SString[RequestChangeType](),
      default = Option(RequestChangeType.UPDATE_FORM)
    ).description(
      """
        |The change request type
        |There can be 2 options:
        |* `SIGNING_FORM`: the investor only need to re-apply signatures.
        |* `UPDATE_FORM`: the investor need to update the form and hence need to re-apply signatures eventually.
        |""".stripMargin
    )
  }

  given requestChangeParamsSchema: Schema[external.RequestChangeParams] = Schema
    .derived[external.RequestChangeParams]

  given requestChangeResponseSchema: Schema[external.RequestChangeResponse] = Schema(
    schemaType = SchemaType.SProduct[external.RequestChangeResponse](
      List(
        SProductField(
          _name = FieldName("success"),
          _schema = Schema[Boolean](
            schemaType = SBoolean()
          ),
          _get = params => Option(true)
        )
      )
    )
  )

  given amlKycDocGroupSchema: Schema[external.AmlKycDocGroup] = Schema
    .derived[external.AmlKycDocGroup]
    .modify(_.name)(
      _.description(
        "Name of the AML/KYC document group."
      )
        .encodedExample("KYC documents")
    )
    .modify(_.docs)(
      _.description("Name of documents in this group")
        .encodedExample(List("Driving license", "Utility bill"))
    )

  given getFundAmlKycDocGroupsResponseSchema: Schema[external.GetFundAmlKycDocGroupsResponse] = Schema
    .derived[external.GetFundAmlKycDocGroupsResponse]
    .modify(_.amlKycDocGroups)(
      _.description(
        "All AML/KYC document groups supported in this fund."
      )
    )

}
