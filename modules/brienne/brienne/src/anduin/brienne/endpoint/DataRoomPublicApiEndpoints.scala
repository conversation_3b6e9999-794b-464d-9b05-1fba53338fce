// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.brienne.endpoint

import sttp.model.Method
import sttp.tapir.*
import sttp.tapir.EndpointIO.{Example, Info}
import anduin.brienne.ApiDocLoaderUtils
import anduin.brienne.dataroom.api.external.*
import anduin.brienne.endpoint.docs.DataRoomDocs
import anduin.brienne.workflow.api.external.AsyncApiResponse
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.id.{FileId, FolderId}
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.tapir.RadixIdTapirCodecs

object DataRoomPublicApiEndpoints extends PublicApiEndpoints {
  override protected def basePath: EndpointInput[Unit] = "api" / "v1" / "dataroom"

  private val DataRoomTag = "Data Room"
  private val UserTag = "User"
  private val GroupTag = "Group"
  private val InsightsTag = "Insights"
  private val FolderTag = "Folder"
  private val FileTag = "File"

  private val UserPath = "user"
  private val GroupPath = "group"
  private val ContentPath = "content"
  private val FilePath = "file"
  private val FolderPath = "folder"
  private val InsightsPath = "insights"
  private val ActivityPath = "activity"
  private val PermissionPath = "permission"

  private val InvitePath = "invite"
  private val AddPath = "add"
  private val RemovePath = "remove"
  private val ExportPath = "export"
  private val OverviewPath = "overview"
  private val UploadPath = "upload"
  private val RenamePath = "rename"
  private val UploadNewVersionPath = "upload-new-version"
  private val DownloadPath = "download"
  private val BatchDeletePath = "batch-delete"
  private val BatchDownloadPath = "batch-download"

  private lazy val dataRoomIdPath = EndpointInput.PathCapture[DataRoomWorkflowId](
    name = Option("data-room-id"),
    codec = RadixIdTapirCodecs.radixIdCodec[DataRoomWorkflowId],
    info = Info[DataRoomWorkflowId](
      description = Option("The Anduin ID of your Data Room"),
      examples = List(Example.of(DataRoomWorkflowId.defaultValue.get)),
      deprecated = false,
      attributes = AttributeMap.Empty
    )
  )

  private lazy val fileIdPath = EndpointInput.PathCapture[FileId](
    name = Option("file-id"),
    codec = RadixIdTapirCodecs.radixIdCodec[FileId],
    info = Info[FileId](
      description = Option("The unique ID of the File object"),
      examples = List.empty,
      deprecated = false,
      attributes = AttributeMap.Empty
    )
  )

  private lazy val folderIdPath = EndpointInput.PathCapture[FolderId](
    name = Option("folder-id"),
    codec = RadixIdTapirCodecs.radixIdCodec[FolderId],
    info = Info[FolderId](
      description = Option("The unique ID of the Folder object"),
      examples = List.empty,
      deprecated = false,
      attributes = AttributeMap.Empty
    )
  )

  private lazy val groupIdPath = EndpointInput.PathCapture[DataRoomGroupId](
    name = Option("group-id"),
    codec = RadixIdTapirCodecs.radixIdCodec[DataRoomGroupId],
    info = Info[DataRoomGroupId](
      description = Option("The Anduin ID of the Data Room group"),
      examples = List.empty,
      deprecated = false,
      attributes = AttributeMap.Empty
    )
  )

  val listDataRooms = basePathEmptyBodyPublicApiEndpoint[
    ListDataRooms.Response
  ](
    Method.GET,
    TapirEndpointInfo(
      name = "List Data Rooms",
      summary = "List Data Rooms",
      description = ApiDocLoaderUtils.load(DataRoomDocs.ListDataRooms),
      tags = List(DataRoomTag)
    )
  ).in(ListDataRooms.QueryParams.isArchivedQuery)

  val listDataRoomsByEmail = publicApiEndpoint[
    Unit,
    ListDataRoomsByEmail.BodyParams,
    ListDataRoomsByEmail.Response
  ](
    UserPath,
    Method.GET,
    TapirEndpointInfo(
      name = "List Data Rooms by email",
      summary = "List Data Rooms by email",
      description = ApiDocLoaderUtils.load(DataRoomDocs.ListDataRoomsByEmail),
      tags = List(DataRoomTag)
    )
  ).in(ListDataRoomsByEmail.QueryParams.isArchivedQuery)

  val listDataRoomUsers = emptyBodyPublicApiEndpoint[
    DataRoomWorkflowId,
    ListDataRoomUsers.Response
  ](
    dataRoomIdPath / UserPath,
    Method.GET,
    TapirEndpointInfo(
      name = "List Data Room users",
      summary = "List Data Room users",
      description = ApiDocLoaderUtils.load(DataRoomDocs.ListDataRoomUsers),
      tags = List(UserTag)
    )
  )

  val listDataRoomGroups = emptyBodyPublicApiEndpoint[
    DataRoomWorkflowId,
    ListDataRoomGroups.Response
  ](
    dataRoomIdPath / GroupPath,
    Method.GET,
    TapirEndpointInfo(
      name = "List Data Room groups",
      summary = "List Data Room groups",
      description = ApiDocLoaderUtils.load(DataRoomDocs.ListDataRoomGroups),
      tags = List(GroupTag)
    )
  )

  val inviteUserSync = publicApiEndpoint[
    DataRoomWorkflowId,
    InviteUserSync.BodyParams,
    InviteUserSync.Response
  ](
    dataRoomIdPath / UserPath / InvitePath,
    Method.POST,
    TapirEndpointInfo(
      name = "Invite user",
      summary = "Invite user",
      description = ApiDocLoaderUtils.load(DataRoomDocs.InviteUserSync),
      tags = List(UserTag)
    )
  )

  val addUserToGroupSync = publicApiEndpoint[
    DataRoomWorkflowId,
    AddUserToGroupSync.BodyParams,
    AddUserToGroupSync.Response
  ](
    dataRoomIdPath / UserPath / AddPath / GroupPath,
    Method.PUT,
    TapirEndpointInfo(
      name = "Add user to group",
      summary = "Add user to group",
      description = ApiDocLoaderUtils.load(DataRoomDocs.AddUserToGroupSync),
      tags = List(UserTag)
    )
  )

  val removeUserFromGroupSync = publicApiEndpoint[
    DataRoomWorkflowId,
    RemoveUserFromGroupSync.BodyParams,
    RemoveUserFromGroupSync.Response
  ](
    dataRoomIdPath / UserPath / RemovePath / GroupPath,
    Method.DELETE,
    TapirEndpointInfo(
      name = "Remove user from group",
      summary = "Remove user from group",
      description = ApiDocLoaderUtils.load(DataRoomDocs.RemoveUserFromGroupSync),
      tags = List(UserTag)
    )
  )

  val removeUserSync = publicApiEndpoint[
    DataRoomWorkflowId,
    RemoveUserSync.BodyParams,
    RemoveUserSync.Response
  ](
    dataRoomIdPath / UserPath / RemovePath,
    Method.DELETE,
    TapirEndpointInfo(
      name = "Remove user",
      summary = "Remove user",
      description = ApiDocLoaderUtils.load(DataRoomDocs.RemoveUserSync),
      tags = List(UserTag)
    )
  )

  val getGroupFolderPermission = emptyBodyPublicApiEndpoint[
    (DataRoomWorkflowId, DataRoomGroupId, FolderId),
    GetGroupFolderPermission.Response
  ](
    dataRoomIdPath / GroupPath / groupIdPath / PermissionPath / folderIdPath,
    Method.GET,
    TapirEndpointInfo(
      name = "Get group folder permission",
      summary = "Get group folder permission",
      description = "Get permission of a group on a folder and its subfolders and files",
      tags = List(GroupTag)
    )
  )

  val createDataRoomGroup = publicApiEndpoint[
    DataRoomWorkflowId,
    CreateDataRoomGroup.BodyParams,
    CreateDataRoomGroup.Response
  ](
    dataRoomIdPath / GroupPath,
    Method.POST,
    TapirEndpointInfo(
      name = "Create group",
      summary = "Create group",
      description = "Create a new Data Room group",
      tags = List(GroupTag)
    )
  )

  val renameDataRoomGroup = publicApiEndpoint[
    (DataRoomWorkflowId, DataRoomGroupId),
    RenameDataRoomGroup.BodyParams,
    RenameDataRoomGroup.Response
  ](
    dataRoomIdPath / GroupPath / groupIdPath / RenamePath,
    Method.PUT,
    TapirEndpointInfo(
      name = "Rename group",
      summary = "Rename group",
      description = "Rename a Data Room group",
      tags = List(GroupTag)
    )
  )

  val deleteDataRoomGroup = publicApiEndpoint[
    (DataRoomWorkflowId, DataRoomGroupId),
    DeleteDataRoomGroup.BodyParams,
    DeleteDataRoomGroup.Response
  ](
    dataRoomIdPath / GroupPath / groupIdPath,
    Method.DELETE,
    TapirEndpointInfo(
      name = "Delete group",
      summary = "Delete group",
      description = "Delete a Data Room group",
      tags = List(GroupTag)
    )
  )

  val updateDataRoomGroupPermission = publicApiEndpoint[
    (DataRoomWorkflowId, DataRoomGroupId),
    UpdateDataRoomGroupPermission.BodyParams,
    UpdateDataRoomGroupPermission.Response
  ](
    dataRoomIdPath / GroupPath / groupIdPath / PermissionPath,
    Method.PUT,
    TapirEndpointInfo(
      name = "Update group permission",
      summary = "Update group permission",
      description = "Update a Data Room group permission",
      tags = List(GroupTag)
    )
  )

  val exportActivitiesAsync = publicApiEndpoint[
    DataRoomWorkflowId,
    Option[DataRoomExportActivities.BodyParams],
    AsyncApiResponse
  ](
    dataRoomIdPath / InsightsPath / ActivityPath / ExportPath,
    Method.POST,
    TapirEndpointInfo(
      name = "Export activities",
      summary = "Export activities",
      description = ApiDocLoaderUtils.load(DataRoomDocs.ExportActivitiesAsync),
      tags = List(InsightsTag)
    )
  )

  val getFileInsights = publicApiEndpoint[
    (DataRoomWorkflowId, FileId),
    Option[GetFileInsights.BodyParams],
    GetFileInsights.Response
  ](
    dataRoomIdPath / InsightsPath / FilePath / fileIdPath / OverviewPath,
    Method.POST,
    TapirEndpointInfo(
      name = "Get file insights",
      summary = "Get file insights",
      description = ApiDocLoaderUtils.load(DataRoomDocs.GetFileInsights),
      tags = List(InsightsTag)
    )
  )

  val getUserInsights = publicApiEndpoint[
    DataRoomWorkflowId,
    GetUserInsights.BodyParams,
    GetUserInsights.Response
  ](
    dataRoomIdPath / InsightsPath / UserPath / OverviewPath,
    Method.POST,
    TapirEndpointInfo(
      name = "Get user insights",
      summary = "Get user insights",
      description = ApiDocLoaderUtils.load(DataRoomDocs.GetUserInsights),
      tags = List(InsightsTag)
    )
  )

  val getGroupInsights = publicApiEndpoint[
    (DataRoomWorkflowId, DataRoomGroupId),
    Option[GetGroupInsights.BodyParams],
    GetGroupInsights.Response
  ](
    dataRoomIdPath / InsightsPath / GroupPath / groupIdPath / OverviewPath,
    Method.POST,
    TapirEndpointInfo(
      name = "Get group insights",
      summary = "Get group insights",
      description = ApiDocLoaderUtils.load(DataRoomDocs.GetGroupInsights),
      tags = List(InsightsTag)
    )
  )

  val getFolderStructure = emptyBodyPublicApiEndpoint[
    (DataRoomWorkflowId, FolderId),
    GetFolderStructure.Response
  ](
    dataRoomIdPath / FolderPath / folderIdPath,
    Method.GET,
    TapirEndpointInfo(
      name = "Get folder structure",
      summary = "Get folder structure",
      description = ApiDocLoaderUtils.load(DataRoomDocs.GetFolderStructure),
      tags = List(FolderTag)
    )
  ).in(GetFolderStructure.QueryParams.queryInput)

  val getFolderPermission = emptyBodyPublicApiEndpoint[
    (DataRoomWorkflowId, FolderId),
    GetFolderPermission.Response
  ](
    dataRoomIdPath / FolderPath / folderIdPath / PermissionPath,
    Method.GET,
    TapirEndpointInfo(
      name = "Get folder permission",
      summary = "Get folder permission",
      description = "Get detailed permission of a specific folder",
      tags = List(FolderTag)
    )
  )

  val createFolder = publicApiEndpoint[
    (DataRoomWorkflowId, FolderId),
    CreateFolder.BodyParams,
    CreateFolder.Response
  ](
    dataRoomIdPath / FolderPath / folderIdPath,
    Method.POST,
    TapirEndpointInfo(
      name = "Create folder",
      summary = "Create folder",
      description = "Create an empty folder as a subfolder of another one",
      tags = List(FolderTag)
    )
  )

  val updateFolderPermission = publicApiEndpoint[
    (DataRoomWorkflowId, FolderId),
    UpdateFolderPermission.BodyParams,
    UpdateFolderPermission.Response
  ](
    dataRoomIdPath / FolderPath / folderIdPath / PermissionPath,
    Method.PUT,
    TapirEndpointInfo(
      name = "Update folder permission",
      summary = "Update folder permission",
      description = "Update the permission of a folder",
      tags = List(FolderTag)
    )
  )

  val batchDownloadContent = publicApiEndpoint[
    DataRoomWorkflowId,
    BatchDownloadContent.BodyParams,
    BatchDownloadContent.Response
  ](
    dataRoomIdPath / ContentPath / BatchDownloadPath,
    Method.POST,
    TapirEndpointInfo(
      name = "Batch download content",
      summary = "Batch download content",
      description = ApiDocLoaderUtils.load(DataRoomDocs.BatchDownloadContent),
      tags = List(FolderTag, FileTag)
    )
  )

  val batchDeleteContent = publicApiEndpoint[
    DataRoomWorkflowId,
    BatchDeleteContent.BodyParams,
    AsyncApiResponse
  ](
    dataRoomIdPath / ContentPath / BatchDeletePath,
    Method.DELETE,
    TapirEndpointInfo(
      name = "Batch delete content",
      summary = "Batch delete content",
      description = ApiDocLoaderUtils.load(DataRoomDocs.BatchDeleteContent),
      tags = List(FolderTag, FileTag)
    )
  )

  val getFileInfo = emptyBodyPublicApiEndpoint[
    (DataRoomWorkflowId, FileId),
    GetFileInfo.Response
  ](
    dataRoomIdPath / FilePath / fileIdPath,
    Method.GET,
    TapirEndpointInfo(
      name = "Get file info",
      summary = "Get file info",
      description = "Get general information of a file",
      tags = List(FileTag)
    )
  ).in(GetFileInfo.QueryParams.queryInput)

  val getFilePermission = emptyBodyPublicApiEndpoint[
    (DataRoomWorkflowId, FileId),
    GetFolderPermission.Response
  ](
    dataRoomIdPath / FilePath / fileIdPath / PermissionPath,
    Method.GET,
    TapirEndpointInfo(
      name = "Get file permission",
      summary = "Get file permission",
      description = "Get detailed permission of a specific file",
      tags = List(FileTag)
    )
  )

  val getFileDownloadUrl = emptyBodyPublicApiEndpoint[
    (DataRoomWorkflowId, FileId),
    GetFileDownloadUrl.Response
  ](
    dataRoomIdPath / FilePath / fileIdPath / DownloadPath,
    Method.GET,
    TapirEndpointInfo(
      name = "Get file download URL",
      summary = "Get file download URL",
      description = "Get the download URL of a file",
      tags = List(FileTag)
    )
  ).in(GetFileDownloadUrl.QueryParams.queryInput)

  val uploadSingleFile = publicApiEndpoint[
    (DataRoomWorkflowId, FolderId),
    UploadSingleFile.BodyParams,
    UploadSingleFile.Response
  ](
    dataRoomIdPath / FilePath / folderIdPath / UploadPath,
    Method.POST,
    TapirEndpointInfo(
      name = "Upload single file",
      summary = "Upload single file",
      description = "Upload a single file to a specific folder",
      tags = List(FileTag)
    )
  )

  val uploadNewVersion = publicApiEndpoint[
    (DataRoomWorkflowId, FileId),
    UploadNewFileVersion.BodyParams,
    UploadNewFileVersion.Response
  ](
    dataRoomIdPath / FilePath / fileIdPath / UploadNewVersionPath,
    Method.POST,
    TapirEndpointInfo(
      name = "Upload new version",
      summary = "Upload new version",
      description = "Upload a new version of a file",
      tags = List(FileTag)
    )
  )

  val deleteSingleFile = publicApiEndpoint[
    (DataRoomWorkflowId, FileId),
    DeleteSingleFile.BodyParams,
    DeleteSingleFile.Response
  ](
    dataRoomIdPath / FilePath / fileIdPath,
    Method.DELETE,
    TapirEndpointInfo(
      name = "Delete single file",
      summary = "Delete single file",
      description = "Delete a single file",
      tags = List(FileTag)
    )
  )

}
