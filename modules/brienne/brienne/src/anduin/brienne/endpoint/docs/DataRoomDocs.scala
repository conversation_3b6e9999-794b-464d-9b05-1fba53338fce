// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.brienne.endpoint.docs

private[endpoint] enum DataRoomDocs(override val fileName: String) extends PublicApiDocs {
  override def appFolder: String = "dataroom"

  case ListDataRooms extends DataRoomDocs("ListDataRooms.md")
  case ListDataRoomsByEmail extends DataRoomDocs("ListDataRoomsByEmail.md")
  case ListDataRoomUsers extends DataRoomDocs("ListDataRoomUsers.md")
  case ListDataRoomGroups extends DataRoomDocs("ListDataRoomGroups.md")
  case InviteUserSync extends DataRoomDocs("InviteUserSync.md")
  case AddUserToGroupSync extends DataRoomDocs("AddUserToGroupSync.md")
  case RemoveUserFromGroupSync extends DataRoomDocs("RemoveUserFromGroupSync.md")
  case RemoveUserSync extends DataRoomDocs("RemoveUserSync.md")

  case ExportActivitiesAsync extends DataRoomDocs("ExportActivitiesAsync.md")
  case GetFileInsights extends DataRoomDocs("GetFileInsights.md")
  case GetUserInsights extends DataRoomDocs("GetUserInsights.md")
  case GetGroupInsights extends DataRoomDocs("GetGroupInsights.md")

  case GetFolderStructure extends DataRoomDocs("GetFolderStructure.md")
  case BatchDownloadContent extends DataRoomDocs("BatchDownloadContent.md")
  case BatchDeleteContent extends DataRoomDocs("BatchDeleteContent.md")
}
