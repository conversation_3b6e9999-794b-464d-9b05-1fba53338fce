// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.brienne.dataroom.service

import java.time.LocalDate
import java.util.UUID
import anduin.id.entity.EntityId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.module.DataRoomApiWorkflowModule
import anduin.testing.{GondorCoreIntegUtils, PublicApiBaseInteg, TemporalFixture}
import anduin.workflow.{ActivityImpl, WorkflowImpl}
import zio.test.*
import anduin.brienne.dataroom.api.external.{
  AddUserToGroupSync,
  InviteUserSync,
  ListDataRoomGroups,
  ListDataRoomUsers,
  RemoveUserFromGroupSync,
  RemoveUserSync
}
import anduin.brienne.dataroom.api.external.ListDataRoomUsers.DataRoomGroupUserInfo
import anduin.dataroom.group.CreateDataRoomGroupParams
import anduin.dataroom.role.{Guest, Member}
import anduin.entity.EntityTestUtils
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.common.user.{UserAttributes, UserId, UserInfo}
import anduin.orgbilling.model.plan.DataRoomPlan
import anduin.service.{AuthenticatedRequestContext, PublicApiContext, ServiceActor}
import anduin.stargazer.service.dataroom.{
  AcceptInvitationToDataRoomParams,
  CreateDataRoomParams,
  DataRoomPermissionChanges,
  InviteUsersToDataRoomParams
}
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import com.anduin.stargazer.service.orgbilling.storage.OrgBillingStoreOperations

object DataRoomPublicApiUserServiceInteg extends PublicApiBaseInteg with GondorCoreIntegUtils with TemporalFixture {
  override def testWorkflows: List[WorkflowImpl[?, ?]] = DataRoomApiWorkflowModule.workflows

  override def testActivities: List[ActivityImpl[?, ?]] = dataRoomApiActivities

  // scalafix:off DisableSyntax.var
  private var entityId: EntityId = scala.compiletime.uninitialized
  private var dataRoomId: DataRoomWorkflowId = scala.compiletime.uninitialized
  private var actor: UserId = scala.compiletime.uninitialized
  private var dataRoomGroupId: DataRoomGroupId = scala.compiletime.uninitialized
  // scalafix:on

  private val dataRoomName: String = "Go Go Boukenger"
  private val groupName: String = "Grhhhh"

  private val serviceAccountEmail = s"magic+${UUID.randomUUID().toString}@anduin-integ.com"

  private val joinGroupUserEmail = s"red+${UUID.randomUUID().toString}@example.com"
  private val individualUserEmail = s"yellow+${UUID.randomUUID().toString}@example.com"

  override def spec = suite("DataRoomPublicApiUserServiceInteg")(
    setupTest,
    userGroupFlowTest
  ) @@ TestAspect.sequential

  private def setupTest = suite("Setup Data Room")(
    test("Create seed Entity") {
      for {
        testUser <- createTestUser(serviceAccountEmail)
        seedEntityId <- EntityTestUtils.createEntity(
          name = "Fire Dance 101",
          alias = "fd101",
          creator = testUser
        )
      } yield {
        actor = testUser
        entityId = seedEntityId
        assertCompletes
      }
    },
    test("Set premium package for entity") {
      for {
        anduinAdmin <- executiveAdmin.userId
        _ <- orgBillingService.changeDataRoomPlan(
          entityId,
          OrgBillingStoreOperations.toDataRoomPackageProto(DataRoomPlan.DataRoomBusinessPlan(LocalDate.now().plusDays(15))),
          anduinAdmin
        )
      } yield assertCompletes
    },
    test("Create and join data room") {
      val requestCtx = AuthenticatedRequestContext(
        ServiceActor(
          userId = actor,
          userInfo = UserInfo.DEFAULT,
          userAttributes = UserAttributes.DEFAULT,
          userSessionIdOpt = None,
          environmentIdOpt = None
        ),
        Seq.empty,
        Seq.empty
      )
      for {
        testDataRoomId <- dataRoomService
          .createDataRoom(
            CreateDataRoomParams(
              dataRoomName,
              entityId
            ),
            requestCtx
          )
          .map(_.dataRoomWorkflowId)
      } yield {
        dataRoomId = testDataRoomId
        assertCompletes
      }
    }
  )

  private def userGroupFlowTest = suite("User group flow test")(
    test("Create test group") {
      // TODO: @dungdq this test should use Create Group API
      for {
        resp <- dataRoomGroupService.createGroup(
          CreateDataRoomGroupParams(
            dataRoomWorkflowId = dataRoomId,
            name = groupName,
            role = Member(),
            assetPermissions = AssetPermissionChanges.allFoldersWithRootChannel(dataRoomId)
          ),
          actor,
          ctx = None
        )
      } yield {
        dataRoomGroupId = resp.groupId
        assertCompletes
      }
    },
    test("Invite 2 users, one join as individual and another join as group") {
      for {
        joinGroupUserId <- createTestUser(joinGroupUserEmail)
        individualUserId <- createTestUser(individualUserEmail)
        _ <- dataRoomPublicApiService.inviteUserSync(
          InviteUserSync.Params(
            pathParams = InviteUserSync.PathParams(dataRoomId),
            bodyParams = InviteUserSync.BodyParams(
              email = joinGroupUserEmail,
              groupIds = Seq(dataRoomGroupId)
            )
          ),
          publicApiCtx
        )
        // Currently, Public API does not support to invite users without a target group
        _ <- dataRoomParticipantService.inviteUsers(
          InviteUsersToDataRoomParams(
            dataRoomId,
            individualPermissionMap = Map(
              individualUserEmail -> DataRoomPermissionChanges(
                roleSet = Some(Guest()),
                assetPermissions = AssetPermissionChanges()
              )
            ),
            isToaRequired = false,
            subject = "",
            message = "",
            buttonLabel = ""
          ),
          serviceActor
        )
        authCtxGroupUser <- getAuthenticatedContext(joinGroupUserId)
        authCtxIndividualUser <- getAuthenticatedContext(individualUserId)
        _ <- dataRoomParticipantService.acceptInvitation(
          AcceptInvitationToDataRoomParams(
            dataRoomId,
            toaFileIdOpt = None
          ),
          authCtxGroupUser
        )
        _ <- dataRoomParticipantService.acceptInvitation(
          AcceptInvitationToDataRoomParams(
            dataRoomId,
            toaFileIdOpt = None
          ),
          authCtxIndividualUser
        )
      } yield assertCompletes
    },
    test("List users API") {
      for {
        resp <- dataRoomPublicApiService.listDataRoomUsers(
          ListDataRoomUsers.PathParams(dataRoomId),
          publicApiCtx
        )
      } yield {
        val usersResp = resp.toOption.get
        assertTrue(
          usersResp.groups.size == 1,
          usersResp.groups.exists { group =>
            group.id == dataRoomGroupId
            && group.name == groupName
            && group.users.size == 1
          },
          usersResp.unassigned.size == 2, // with service account also
          usersResp.unassigned.exists { user =>
            user.email == individualUserEmail
          },
          usersResp.unassigned.exists { user =>
            user.email == serviceAccountEmail
          }
        )
      }
    },
    test("List groups API") {
      for {
        resp <- dataRoomPublicApiService.listDataRoomGroups(
          ListDataRoomGroups.PathParams(dataRoomId),
          publicApiCtx
        )
      } yield {
        val groupsResp = resp.toOption.get
        assertTrue(
          groupsResp.groups.size == 1,
          groupsResp.groups.exists { group =>
            group.id == dataRoomGroupId
            && group.name == groupName
            && group.totalUsers == 1
            && group.createdBy.email == serviceAccountEmail
          }
        )
      }
    },
    test("Move random user to group then remove from Data Room") {
      val randomUserEmail = s"random+${UUID.randomUUID().toString}@example.com"
      for {
        // Invite random user
        randomUser <- createTestUser(randomUserEmail)
        _ <- dataRoomParticipantService.inviteUsers(
          InviteUsersToDataRoomParams(
            dataRoomId,
            individualPermissionMap = Map(
              randomUserEmail -> DataRoomPermissionChanges(
                roleSet = Some(Guest()),
                assetPermissions = AssetPermissionChanges()
              )
            ),
            isToaRequired = false,
            subject = "",
            message = "",
            buttonLabel = ""
          ),
          serviceActor
        )
        authCtx <- getAuthenticatedContext(randomUser)
        _ <- dataRoomParticipantService.acceptInvitation(
          AcceptInvitationToDataRoomParams(
            dataRoomId,
            toaFileIdOpt = None
          ),
          authCtx
        )
        beforeMoveUsersResp <- dataRoomPublicApiService.listDataRoomUsers(
          ListDataRoomUsers.PathParams(dataRoomId),
          publicApiCtx
        )
        // Add to group
        _ <- dataRoomPublicApiService.addUserToGroupSync(
          AddUserToGroupSync.Params(
            pathParams = AddUserToGroupSync.PathParams(dataRoomId),
            bodyParams = AddUserToGroupSync.BodyParams(
              email = randomUserEmail,
              groupId = dataRoomGroupId
            )
          ),
          publicApiCtx
        )
        afterMoveUsersResp <- dataRoomPublicApiService.listDataRoomUsers(
          ListDataRoomUsers.PathParams(dataRoomId),
          publicApiCtx
        )
        // Remove from group
        _ <- dataRoomPublicApiService.removeUserFromGroupSync(
          RemoveUserFromGroupSync.Params(
            pathParams = RemoveUserFromGroupSync.PathParams(dataRoomId),
            bodyParams = RemoveUserFromGroupSync.BodyParams(
              email = randomUserEmail,
              groupId = dataRoomGroupId
            )
          ),
          publicApiCtx
        )
        // Remove from Data Room
        _ <- dataRoomPublicApiService.removeUserSync(
          RemoveUserSync.Params(
            pathParams = RemoveUserSync.PathParams(dataRoomId),
            bodyParams = RemoveUserSync.BodyParams(
              email = randomUserEmail
            )
          ),
          publicApiCtx
        )
        afterRemoveUsersResp <- dataRoomPublicApiService.listDataRoomUsers(
          ListDataRoomUsers.PathParams(dataRoomId),
          publicApiCtx
        )
      } yield {
        val beforeMoveUser = beforeMoveUsersResp.toOption.get
        val afterMoveUser = afterMoveUsersResp.toOption.get
        val afterRemoveUser = afterRemoveUsersResp.toOption.get
        assertTrue(
          // Move user check
          beforeMoveUser.unassigned.size == afterMoveUser.unassigned.size + 1,
          afterMoveUser.groups.size == 1,
          beforeMoveUser.groups.head.users.size == afterMoveUser.groups.head.users.size - 1,
          // Remove user check
          afterMoveUser.unassigned.size == afterRemoveUser.unassigned.size,
          afterRemoveUser.groups.size == 1,
          afterMoveUser.groups.head.users.size == afterRemoveUser.groups.head.users.size + 1
        )
      }
    }
  )

  def publicApiCtx: PublicApiContext = PublicApiContext(
    serviceAccount = actor
  )

  def serviceActor: ServiceActor = ServiceActor.defaultServiceActor.copy(
    userId = actor
  )

}
