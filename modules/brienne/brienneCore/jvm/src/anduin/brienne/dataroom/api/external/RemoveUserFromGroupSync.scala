// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.brienne.dataroom.api.external

import zio.{ZIO, Task}
import sttp.tapir.Schema
import anduin.brienne.dataroom.workflow.codec.DataRoomGenericSyncApiCodec

import anduin.brienne.dataroom.workflow.{RemoveUserFromGroupSyncInputMessage, RemoveUserFromGroupSyncOutputMessage}
import anduin.circe.generic.semiauto.CirceCodec
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.id.stage.DataRoomWorkflowId

object RemoveUserFromGroupSync
    extends DataRoomApiEndpoint
    with DataRoomGenericSyncApiCodec[RemoveUserFromGroupSyncInputMessage, RemoveUserFromGroupSyncOutputMessage] {

  final case class Params(
    pathParams: PathParams,
    bodyParams: BodyParams
  )

  final case class PathParams(
    dataRoomId: DataRoomWorkflowId
  )

  final case class BodyParams(
    email: String,
    groupId: DataRoomGroupId
  ) derives CirceCodec.WithDefaults

  object BodyParams {

    given Schema[BodyParams] = Schema
      .derived[BodyParams]
      .modify(_.email)(_ => emailSchema)

  }

  final case class Response() derives CirceCodec.WithDefaults, Schema

  override def transformParams(params: Params): Task[RemoveUserFromGroupSyncInputMessage] = {
    ZIO.attempt(
      RemoveUserFromGroupSyncInputMessage(
        dataRoomId = params.pathParams.dataRoomId,
        email = params.bodyParams.email,
        groupId = params.bodyParams.groupId
      )
    )
  }

  override def transformResponse(output: RemoveUserFromGroupSyncOutputMessage): Task[Response] =
    ZIO.attempt(Response())

}
