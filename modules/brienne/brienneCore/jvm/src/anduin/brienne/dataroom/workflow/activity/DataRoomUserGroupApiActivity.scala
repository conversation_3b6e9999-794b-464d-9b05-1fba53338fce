// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.brienne.dataroom.workflow.activity

import anduin.brienne.dataroom.workflow.*
import anduin.brienne.workflow.core.RequestError
import anduin.service.PublicApiContext
import anduin.workflow.ActivityQueue
import anduin.workflow.common.TemporalActivityCompanion
import zio.temporal.activityInterface

@activityInterface(namePrefix = "DataRoomUserGroupApiActivity")
trait DataRoomUserGroupApiActivity extends DataRoomPublicApiActivity {

  def listUsers(
    params: ListDataRoomUsersInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, ListDataRoomUsersOutputMessage]

  def inviteUser(
    params: InviteUserSyncInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, InviteUserSyncOutputMessage]

  def addUserToGroup(
    params: AddUserToGroupSyncInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, AddUserToGroupSyncOutputMessage]

  def removeUserFromGroup(
    params: RemoveUserFromGroupSyncInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, RemoveUserFromGroupSyncOutputMessage]

  def removeUser(
    params: RemoveUserSyncInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, RemoveUserSyncOutputMessage]

  def createGroup(
    params: CreateGroupInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, CreateGroupOutputMessage]

  def renameGroup(
    params: RenameGroupInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, EmptyResponseOutputMessage]

  def deleteGroup(
    params: DeleteGroupInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, EmptyResponseOutputMessage]

  def updateGroupPermission(
    params: UpdateGroupPermissionInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, EmptyResponseOutputMessage]

  def getGroupFolderPermission(
    params: GetGroupFolderPermissionInputMessage,
    ctx: PublicApiContext
  ): Either[RequestError, GetGroupFolderPermissionOutputMessage]

}

object DataRoomUserGroupApiActivity
    extends TemporalActivityCompanion[DataRoomUserGroupApiActivity](
      ActivityQueue.PublicApiManageUserGroup
    )
