// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.brienne.dataroom.api.external

import zio.{Task, ZIO}
import sttp.tapir.{Schema, Validator}
import anduin.brienne.dataroom.workflow.codec.DataRoomGenericSyncApiCodec
import anduin.brienne.dataroom.workflow.{InviteUserSyncInputMessage, InviteUserSyncOutputMessage}
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.circe.generic.semiauto.CirceCodec

object InviteUserSync
    extends DataRoomApiEndpoint
    with DataRoomGenericSyncApiCodec[
      InviteUserSyncInputMessage,
      InviteUserSyncOutputMessage
    ] {

  final case class Params(
    pathParams: PathParams,
    bodyParams: BodyParams
  )

  final case class PathParams(
    dataRoomId: DataRoomWorkflowId
  )

  final case class BodyParams(
    email: String,
    groupIds: Seq[DataRoomGroupId],
    canInvite: Boolean = false,
    requireTermsOfAccess: Boolean = true,
    skipInvitationEmail: Boolean = false
  ) derives CirceCodec.WithDefaults

  object BodyParams {

    given Schema[BodyParams] = Schema
      .derived[BodyParams]
      .modify(_.groupIds)(_.validate(Validator.nonEmpty))
      .modify(_.email)(_ => emailSchema)
      .modify(_.canInvite)(_ => canInviteSchema)
      .modify(_.requireTermsOfAccess)(
        _.description(
          "Whether to require the user to accept the terms of access (if this Data Room enables terms of access requirement for newly invited members)"
        ).default(true)
      )
      .modify(_.skipInvitationEmail)(
        _.description(
          "Whether to skip sending email invitation to the user"
        ).default(false)
      )

  }

  final case class Response() derives CirceCodec.WithDefaults, Schema

  override def transformParams(params: Params): Task[InviteUserSyncInputMessage] = {
    ZIO.attempt(
      InviteUserSyncInputMessage(
        dataRoomId = params.pathParams.dataRoomId,
        email = params.bodyParams.email,
        groupIds = params.bodyParams.groupIds,
        canInvite = params.bodyParams.canInvite,
        requireTermsOfAccess = params.bodyParams.requireTermsOfAccess,
        skipInvitationEmail = params.bodyParams.skipInvitationEmail
      )
    )
  }

  override def transformResponse(output: InviteUserSyncOutputMessage): Task[Response] =
    ZIO.attempt(Response())

}
