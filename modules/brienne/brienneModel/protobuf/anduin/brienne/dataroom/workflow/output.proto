syntax = "proto3";

package anduin.brienne.dataroom.workflow;

import "date_time.proto";
import "scalapb/scalapb.proto";
import "flow/file/file_folder_permissions.proto";
import "anduin/brienne/dataroom/workflow/models.proto";
import "anduin/brienne/workflow/core/protocols.proto";

option (scalapb.options) = {
  package_name: "anduin.brienne.dataroom.workflow"
  single_file: true
  preserve_unknown_fields: false
  no_default_values_in_constructor: true
  import: "java.time.Instant"
  import: "anduin.model.id.FolderId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.dataroom.DataRoomGroupId"
  import: "anduin.model.id.stage.DataRoomWorkflowId"
};

// -------------------- Sync API --------------------

message ListDataRoomsOutputMessage {
  repeated DataRoomInfoMessage data_rooms = 1;
}

message ListDataRoomUsersOutputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  repeated DataRoomUserInfo users = 2;
  repeated DataRoomGroupInfo groups = 3;
}

message InviteUserSyncOutputMessage {}

message AddUserToGroupSyncOutputMessage {}

message RemoveUserFromGroupSyncOutputMessage {}

message RemoveUserSyncOutputMessage {}

message GetFileInsightsOutputMessage {
  string file_name = 3;
  repeated FileVersionInsightsSummary overview = 1;
  repeated FileWithUsersInsights user_insights = 2;
}

message GetUserInsightsOutputMessage {
  ParticipantsInsightsSummary overview = 1 [(scalapb.field).no_box = true];
  repeated ParticipantsWithFileInsights file_insights = 2;
}

message GetGroupInsightsOutputMessage {
  ParticipantsInsightsSummary overview = 1 [(scalapb.field).no_box = true];
  repeated ParticipantsWithFileInsights file_insights = 2;
}

message GetFolderStructureOutputMessage {
  string folder_name = 1;
  string creator = 2;
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
  InstantMessage last_updated = 4 [(scalapb.field).type = "Instant"];
  repeated FolderBasicInfo sub_folders = 5;
  repeated FileBasicInfo files = 6;
}

message GetFileFolderPermissionOutputMessage {
  map <string, GroupContentPermission> group_permissions = 1 [(scalapb.field).key_type = "DataRoomGroupId"];
  map <string, UserContentPermission> user_permissions = 2 [(scalapb.field).key_type = "UserId"];
}

message CreateFolderOutputMessage {
  string folder_id = 1 [(scalapb.field).type = "FolderId"];
}

message UpdateFileFolderPermissionOutputMessage {}

message GetFileInfoOutputMessage {
  string file_name = 1;
  UserBasicInfo creator = 2;
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
  InstantMessage last_updated = 4 [(scalapb.field).type = "Instant"];
  FileVersionInfo version = 5;
  int64 size = 6;
}

message GetFileDownloadUrlOutputMessage {
  string download_url = 1;
}

message UploadNewVersionOutputMessage {
  int32 version_index = 1;
}

message CreateGroupOutputMessage {
  string group_id = 1 [(scalapb.field).type = "DataRoomGroupId"];
}

message EmptyResponseOutputMessage {}

message GetGroupFolderPermissionOutputMessage {
  optional flow.file.FileFolderPermission permission = 1;
  repeated FolderUserGroupPermission sub_folders = 2;
  repeated FileUserGroupPermission files = 3;
}

message ListDataRoomsByEmailOutputMessage {
  repeated DataRoomInfoMessage data_rooms = 1;
}

message DataRoomGenericSyncResponse {
  oneof sealed_value {
    ListDataRoomsOutputMessage list_data_rooms_output_message = 1;
    ListDataRoomUsersOutputMessage list_data_room_users_output_message = 2;
    InviteUserSyncOutputMessage invite_user_sync_output_message = 3;
    AddUserToGroupSyncOutputMessage add_user_to_group_sync_output_message = 4;
    RemoveUserSyncOutputMessage remove_user_sync_output_message = 5;
    GetFileInsightsOutputMessage get_file_insights_output_message = 6;
    GetUserInsightsOutputMessage get_user_insights_output_message = 7;
    GetGroupInsightsOutputMessage get_group_insights_output_message = 8;
    GetFolderStructureOutputMessage get_folder_structure_output_message = 9;
    GetFileFolderPermissionOutputMessage get_file_folder_permission_output_message = 10;
    CreateFolderOutputMessage create_folder_output_message = 11;
    UpdateFileFolderPermissionOutputMessage update_file_folder_permission_output_message = 12;
    GetFileInfoOutputMessage get_file_info_output_message = 13;
    GetFileDownloadUrlOutputMessage get_file_download_url_output_message = 14;
    UploadNewVersionOutputMessage upload_new_version_output_message = 15;
    CreateGroupOutputMessage create_group_output_message = 16;
    EmptyResponseOutputMessage empty_response_output_message = 17;
    GetGroupFolderPermissionOutputMessage get_group_folder_permission_output_message = 18;
    ListDataRoomsByEmailOutputMessage list_data_rooms_by_email_output_message = 19;
    RemoveUserFromGroupSyncOutputMessage remove_user_from_group_sync_output_message = 20;
  }
}

message DataRoomGenericSyncApiOutput {
  oneof value {
    DataRoomGenericSyncResponse success = 1;
    anduin.brienne.workflow.core.RequestError error = 2;
  }
}

// -------------------- Async API --------------------

message DataRoomExportActivitiesWorkflowOutput {
  string download_url = 1;
}

message DataRoomUploadFilesWorkflowOutput {
  repeated FileBasicInfo files = 1;
}

message DataRoomBatchDownloadContentWorkflowOutput {
  string download_url = 1;
}

message DataRoomDeleteFileFolderWorkflowOutput {}