syntax = "proto3";

package anduin.brienne.dataroom.workflow;

import "date_time.proto";
import "scalapb/scalapb.proto";
import "anduin/service/context.proto";
import "anduin/dataroom/role.proto";
import "anduin/brienne/dataroom/workflow/models.proto";
import "com/anduin/stargazer/endpoints/asset_permission_changes.proto";

option (scalapb.options) = {
  package_name: "anduin.brienne.dataroom.workflow"
  single_file: true
  preserve_unknown_fields: false
  no_default_values_in_constructor: true
  import: "anduin.model.id.{FileId, FolderId}"
  import: "anduin.model.id.stage.DataRoomWorkflowId"
  import: "anduin.id.dataroom.DataRoomGroupId"
  import: "anduin.brienne.dataroom.api.external.activity.*"
  import: "anduin.brienne.id.PublicApiFileId"
  import: "anduin.dataroom.role.DataRoomRole"
  import: "java.time.{ZoneId, Instant}"
};

// -------------------- Sync API --------------------

message ListDataRoomsInputMessage {
  bool is_archived = 1;
}

message ListDataRoomUsersInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message InviteUserSyncInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string email = 2;
  repeated string group_ids = 3 [(scalapb.field).type = "DataRoomGroupId"];
  bool can_invite = 4;
  bool require_terms_of_access = 5;
  bool skip_invitation_email = 6;
}

message AddUserToGroupSyncInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string email = 2;
  string group_id = 3 [(scalapb.field).type = "DataRoomGroupId"];
}

message RemoveUserFromGroupSyncInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string email = 2;
  string group_id = 3 [(scalapb.field).type = "DataRoomGroupId"];
}

message RemoveUserSyncInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string email = 2;
  bool notify_email = 3;
}

message GetFileInsightsInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string file_id = 2 [(scalapb.field).type = "FileId"];
  InstantMessage from_timestamp = 3 [(scalapb.field).type = "Instant"];
  InstantMessage to_timestamp = 4 [(scalapb.field).type = "Instant"];
}

message GetUserInsightsInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string email = 2;
  InstantMessage from_timestamp = 3 [(scalapb.field).type = "Instant"];
  InstantMessage to_timestamp = 4 [(scalapb.field).type = "Instant"];
}

message GetGroupInsightsInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string group_id = 2 [(scalapb.field).type = "DataRoomGroupId"];
  InstantMessage from_timestamp = 3 [(scalapb.field).type = "Instant"];
  InstantMessage to_timestamp = 4 [(scalapb.field).type = "Instant"];
}

message GetFolderStructureInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string folder_id = 2 [(scalapb.field).type = "FolderId"];
  bool include_files = 3;
  bool include_sub_folders = 4;
}

message GetFolderPermissionInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string folder_id = 2 [(scalapb.field).type = "FolderId"];
}

message ContentWithGroupPermissionWrapper {
  repeated ContentWithGroupPermission permissions = 1;
}

message CreateFolderInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string parent_folder_id = 2 [(scalapb.field).type = "FolderId"];
  string folder_name = 3;
  ContentWithGroupPermissionWrapper group_permissions_opt = 4;
}

message UpdateFileFolderPermissionInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  map <string, com.anduin.stargazer.endpoints.AssetPermissionChanges> groups_change = 2 [(scalapb.field).key_type = "DataRoomGroupId"];
}

message GetFileInfoInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string file_id = 2 [(scalapb.field).type = "FileId"];
  bool include_versions = 3;
}

message GetFilePermissionInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string file_id = 2 [(scalapb.field).type = "FileId"];
}

message GetFileDownloadUrlInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string file_id = 2 [(scalapb.field).type = "FileId"];
  bool with_watermark = 3;
  optional int32 version = 4;
}

message UploadNewVersionInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string file_id = 2 [(scalapb.field).type = "FileId"];
  string new_content_file_id = 3 [(scalapb.field).type = "PublicApiFileId"];
  bool send_notification = 4;
}

message CreateGroupInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string group_name = 2;
  anduin.dataroom.role.DataRoomRole role = 3;
  com.anduin.stargazer.endpoints.AssetPermissionChanges content_permission = 4 [(scalapb.field).no_box = true];
}

message RenameGroupInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string group_id = 2 [(scalapb.field).type = "DataRoomGroupId"];
  string new_group_name = 3;
}

message DeleteGroupInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string group_id = 2 [(scalapb.field).type = "DataRoomGroupId"];
  bool should_remove_users = 3;
  bool should_notify_email = 4;
}

message UpdateGroupPermissionInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string group_id = 2 [(scalapb.field).type = "DataRoomGroupId"];
  anduin.dataroom.role.DataRoomRoleWrapper role = 3;
  com.anduin.stargazer.endpoints.AssetPermissionChanges permission_change = 4 [(scalapb.field).no_box = true];
}

message GetGroupFolderPermissionInputMessage {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string group_id = 2 [(scalapb.field).type = "DataRoomGroupId"];
  string folder_id = 3 [(scalapb.field).type = "FolderId"];
}

message ListDataRoomsByEmailInputMessage {
  string email = 1;
  bool is_archived = 2;
}

message DataRoomGenericSyncParams {
  oneof sealed_value {
    ListDataRoomsInputMessage list_data_rooms_input_message = 1;
    ListDataRoomUsersInputMessage list_data_room_users_input_message = 2;
    InviteUserSyncInputMessage invite_user_sync_input_message = 3;
    AddUserToGroupSyncInputMessage add_user_to_group_sync_input_message = 4;
    RemoveUserSyncInputMessage remove_user_sync_input_message = 5;
    GetFileInsightsInputMessage get_file_insights_input_message = 6;
    GetUserInsightsInputMessage get_user_insights_input_message = 7;
    GetGroupInsightsInputMessage get_group_insights_input_message = 8;
    GetFolderStructureInputMessage get_folder_structure_input_message = 9;
    GetFolderPermissionInputMessage get_folder_permission_input_message = 10;
    CreateFolderInputMessage create_folder_input_message = 11;
    UpdateFileFolderPermissionInputMessage update_file_folder_permission_input_message = 12;
    GetFileInfoInputMessage get_file_info_input_message = 13;
    GetFilePermissionInputMessage get_file_permission_input_message = 14;
    GetFileDownloadUrlInputMessage get_file_download_url_input_message = 15;
    UploadNewVersionInputMessage upload_new_version_input_message = 16;
    CreateGroupInputMessage create_group_input_message = 17;
    RenameGroupInputMessage rename_group_input_message = 18;
    DeleteGroupInputMessage delete_group_input_message = 19;
    UpdateGroupPermissionInputMessage update_group_permission_input_message = 20;
    GetGroupFolderPermissionInputMessage get_group_folder_permission_input_message = 21;
    ListDataRoomsByEmailInputMessage list_data_rooms_by_email_input_message = 22;
    RemoveUserFromGroupSyncInputMessage remove_user_from_group_sync_input_message = 23;
  }
}

message DataRoomGenericSyncApiInput {
  anduin.service.PublicApiContext ctx = 1 [(scalapb.field).no_box = true];
  DataRoomGenericSyncParams params = 2;
}

// -------------------- Async API --------------------

message FilterUsersInput {
  repeated string filter = 1 [(scalapb.field).collection_type = "Set"];
}

message FilterGroupIdsInput {
  repeated string filter = 1 [(scalapb.field).type = "DataRoomGroupId", (scalapb.field).collection_type = "Set"];
}

message FilterRolesInput {
  repeated anduin.dataroom.role.DataRoomRole filter = 1 [(scalapb.field).collection_type = "Set"];
}

message FilterFileIdsInput {
  repeated string filter = 1 [(scalapb.field).type = "FileId", (scalapb.field).collection_type = "Set"];
}

message ExportActivitiesFilter {
  FilterUsersInput filtered_users = 1;
  FilterGroupIdsInput filtered_group_ids = 2;
  FilterRolesInput filtered_roles = 3;
  FilterFileIdsInput filtered_file_ids = 4;
}

message ExportedFieldsWrapper {
  repeated string fields = 1 [(scalapb.field).type = "DataRoomActivityExportField"];
}

message DataRoomExportActivitiesWorkflowInput {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  InstantMessage from_timestamp = 2 [(scalapb.field).type = "Instant"];
  InstantMessage to_timestamp = 3 [(scalapb.field).type = "Instant"];
  repeated string data_room_activities = 4 [(scalapb.field).type = "DataRoomActivity", (scalapb.field).collection_type = "Set"];
  repeated string content_activities = 5 [(scalapb.field).type = "ContentActivity", (scalapb.field).collection_type = "Set"];
  ExportedFieldsWrapper export_fields = 8;
  ExportActivitiesFilter filter = 6 [(scalapb.field).no_box = true];
  ZoneIdMessage zone_id = 7 [(scalapb.field).type = "ZoneId"];
}

message DataRoomUploadFilesWorkflowInput {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string folder_id = 2 [(scalapb.field).type = "FolderId"];
  repeated string file_ids = 3 [(scalapb.field).type = "FileId"];
  bool send_notification = 4;
}

message DataRoomBatchDownloadContentWorkflowInput {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  repeated string file_ids = 2 [(scalapb.field).type = "FileId"];
  repeated string folder_ids = 3 [(scalapb.field).type = "FolderId"];
  optional string zip_file_name = 4;
  bool with_watermark = 5;
}

message DataRoomDeleteFileFolderWorkflowInput {
  string data_room_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  repeated string file_ids = 2 [(scalapb.field).type = "FileId"];
  repeated string folder_ids = 3 [(scalapb.field).type = "FolderId"];
  bool is_permanent = 4;
}
