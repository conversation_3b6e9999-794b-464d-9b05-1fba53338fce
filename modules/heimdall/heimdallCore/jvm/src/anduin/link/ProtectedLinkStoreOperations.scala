// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.link

import anduin.encryption.hash.Bcrypt
import anduin.fdb.record.FDBOperations
import anduin.fdb.record.model.{RecordIO, RecordTask}
import anduin.id.account.EnterpriseLoginLinkId
import anduin.id.authwhitelabel.AuthenticationWhitelabelId
import anduin.id.link.ProtectedLinkId
import anduin.link.ProtectedLinkStoreOperations.{CheckPassword, InternalCheckResult, ProtectedLinkModel}
import anduin.model.common.user.UserId
import anduin.model.id.ProtectedLinkIdFactory
import java.time.Instant

import anduin.link.ProtectedLinkStoreProvider.{*, given}

final case class ProtectedLinkStoreOperations(store: Store) {

  private def when(condition: Boolean)(op: RecordTask[Unit]) = if (condition) op else RecordIO.unit

  private def throwWhen(condition: Boolean)(error: Throwable) = when(condition)(RecordIO.fail(error))

  private def traverseO[A, B](opt: Option[A])(task: A => RecordTask[B]) = {
    opt.fold[RecordTask[Option[B]]](RecordIO.succeed(Option.empty[B]))(task.andThen(_.map(Option(_))))
  }

  def create(
    passwordOpt: Option[String],
    expiryDate: Option[Instant],
    whitelistedDomains: Set[String],
    asset: String,
    authenticationWhitelabelId: Option[AuthenticationWhitelabelId],
    isRequiredApproval: Boolean
  ): RecordTask[(ProtectedLinkId, ProtectedLinkModel)] = {
    for {
      linkId <- RecordIO.succeed(ProtectedLinkIdFactory.unsafeRandomId)
      linkModel = ProtectedLink(
        linkId = linkId,
        passwordHash = passwordOpt.map(Bcrypt.hash),
        expiryDate = expiryDate,
        whitelistedDomains = whitelistedDomains,
        asset = asset,
        authenticationWhitelabelId = authenticationWhitelabelId,
        isRequiredApproval = isRequiredApproval
      )
      _ <- store.create(linkModel)
    } yield linkId -> new ProtectedLinkModel(linkModel)
  }

  private def get(linkId: ProtectedLinkId) = {
    store.get(linkId).map(new ProtectedLinkModel(_))
  }

  private def update(linkModel: ProtectedLinkModel)(func: ProtectedLink => ProtectedLink) = {
    val newLinkModel = linkModel.update(func)
    store.update(newLinkModel).map(_ => newLinkModel)
  }

  private def getLinkState(linkModel: ProtectedLinkModel, now: Instant) = {
    ProtectedLinkState(
      linkId = linkModel.getLinkId,
      isDisabled = linkModel.checkIsDisabled,
      hasPassword = linkModel.hasPassword,
      isExpired = linkModel.checkIsExpired(now),
      whitelistedDomains = linkModel.getWhitelistedDomains,
      expiryDate = linkModel.getExpiryDate,
      isRequiredApproval = linkModel.getIsRequiredApproval,
      enterpriseLoginLinkId = linkModel.getEnterpriseLoginLinkId
    )
  }

  private[link] def getLinkState(linkId: ProtectedLinkId, now: Instant): RecordTask[ProtectedLinkState] = {
    for {
      linkModel <- get(linkId)
    } yield getLinkState(linkModel, now)
  }

  def getLinkAsset(linkId: ProtectedLinkId): RecordTask[Option[String]] = {
    store.getOpt(linkId).map(_.map(_.asset))
  }

  def getUserLinkState(
    linkId: ProtectedLinkId,
    userId: UserId,
    emailDomain: String,
    now: Instant
  ): RecordTask[UserProtectedLinkState] = {
    for {
      linkModel <- get(linkId)
    } yield {
      UserProtectedLinkState(
        linkState = getLinkState(linkModel, now),
        isDomainWhitelisted = linkModel.checkDomain(emailDomain),
        isPasswordPassed = linkModel.checkIsPasswordPassed(userId),
        isAccessGranted = linkModel.checkIsAccessGranted(userId),
        assetOpt = linkModel.getAssetOpt(
          userId,
          emailDomain,
          now
        )
      )
    }
  }

  private def check(
    linkId: ProtectedLinkId,
    userIdOpt: Option[UserId],
    checkIsDisabledAndExpired: Option[Instant],
    checkDomain: Option[String],
    checkPassword: Option[CheckPassword]
  ) = {
    for {
      linkModel <- get(linkId)
      isAccessGranted = userIdOpt.exists(linkModel.checkIsAccessGranted)
      _ <- when(!isAccessGranted) {
        for {
          _ <- traverseO(checkIsDisabledAndExpired) { now =>
            for {
              _ <- throwWhen(linkModel.checkIsDisabled) {
                CheckValidityException.LinkDisabled(linkModel.getLinkId)
              }
              _ <- throwWhen(linkModel.checkIsExpired(now)) {
                CheckValidityException.LinkExpired(linkModel.getLinkId)
              }
            } yield ()
          }
          _ <- traverseO(checkDomain) { domain =>
            throwWhen(!linkModel.checkDomain(domain)) {
              CheckValidityException.DomainNotWhiteListed(linkModel.getLinkId, domain)
            }
          }
          _ <- traverseO(checkPassword) { case ProtectedLinkStoreOperations.CheckPassword(userPasswordInput) =>
            when(!userIdOpt.exists(linkModel.checkIsPasswordPassed)) {
              for {
                _ <- throwWhen(!linkModel.checkPassword(userPasswordInput)) {
                  CheckValidityException.InvalidPassword(linkModel.getLinkId)
                }
                _ <- traverseO(userIdOpt) {
                  markAsPasswordPassed(linkId, _)
                }
              } yield ()
            }
          }
        } yield ()
      }
    } yield InternalCheckResult(isAccessGranted)
  }

  def checkValidity(
    linkId: ProtectedLinkId,
    userId: UserId,
    emailDomain: String,
    passwordOpt: Option[String],
    now: Instant
  ): RecordTask[Unit] = {
    check(
      linkId,
      Some(userId),
      Some(now),
      Some(emailDomain),
      Some(CheckPassword(passwordOpt))
    ).unit
  }

  def checkValidityPublic(
    linkId: ProtectedLinkId,
    passwordOpt: Option[String],
    now: Instant
  ): RecordTask[Unit] = {
    check(
      linkId,
      None,
      Some(now),
      None,
      Some(CheckPassword(passwordOpt))
    ).unit
  }

  def checkExpiredAndDomain(
    linkId: ProtectedLinkId,
    now: Instant,
    emailDomain: String
  ): RecordTask[Unit] = {
    check(
      linkId,
      None,
      Some(now),
      Some(emailDomain),
      None
    ).unit
  }

  def checkSignUpEligibility(linkId: ProtectedLinkId, emailDomain: String): RecordTask[Unit] = {
    check(
      linkId,
      None,
      None,
      Some(emailDomain),
      None
    ).map(_ => ())
  }

  def markAsPasswordPassed(linkId: ProtectedLinkId, userId: UserId): RecordTask[Unit] = {
    for {
      linkModel <- get(linkId)
      _ <- when(!linkModel.checkIsPasswordPassed(userId)) {
        update(linkModel)(_.addPasswordPassedUsers(userId)).map(_ => ())
      }
    } yield ()
  }

  def markAsPendingEmailVerification(linkId: ProtectedLinkId, userId: UserId): RecordTask[Unit] = {
    for {
      linkModel <- get(linkId)
      _ <- when(!linkModel.checkIsPendingEmailVerification(userId)) {
        update(linkModel)(_.addPendingEmailVerificationUsers(userId)).map(_ => ())
      }
    } yield ()
  }

  def getPendingEmailVerification(linkId: ProtectedLinkId, userId: UserId): RecordTask[Boolean] = {
    for {
      linkModel <- get(linkId)
    } yield linkModel.checkIsPendingEmailVerification(userId)
  }

  def grantAccess(
    linkId: ProtectedLinkId,
    userId: UserId,
    emailDomain: String,
    now: Instant
  ): RecordTask[Unit] = {
    for {
      res <- check(
        linkId,
        Some(userId),
        Some(now),
        Some(emailDomain),
        Some(CheckPassword(None))
      )
      _ <- when(!res.isAccessGranted) {
        for {
          linkModel <- get(linkId)
          _ <- update(linkModel) { protectedLink =>
            protectedLink
              .withPendingEmailVerificationUsers(protectedLink.pendingEmailVerificationUsers.diff(Set(userId)))
              .addAccessGrantedUsers(userId)
          }
        } yield ()
      }
    } yield ()
  }

  def revokeAccess(
    linkId: ProtectedLinkId,
    toRevokeUsers: Set[UserId]
  ): RecordTask[Unit] = {
    for {
      linkModel <- get(linkId)
      _ <- update(linkModel) { protectedLink =>
        protectedLink
          .withPasswordPassedUsers(protectedLink.passwordPassedUsers.diff(toRevokeUsers))
          .withPendingEmailVerificationUsers(protectedLink.pendingEmailVerificationUsers.diff(toRevokeUsers))
          .withAccessGrantedUsers(protectedLink.accessGrantedUsers.diff(toRevokeUsers))
      }
    } yield ()
  }

  private def getUpdateFunc[A](change: Option[A])(func: A => ProtectedLink => ProtectedLink) = {
    change.fold[ProtectedLink => ProtectedLink](identity)(func)
  }

  def update(
    linkId: ProtectedLinkId,
    passwordChange: Option[Option[String]],
    expiryDateChange: Option[Option[Instant]],
    isDisabledChange: Option[Boolean],
    whitelistDomainsChange: Option[Set[String]],
    isRequireApprovalChange: Option[Boolean]
  ): RecordTask[ProtectedLinkModel] = {
    val func = Function.chain(
      Seq(
        getUpdateFunc(passwordChange) { passwordOpt =>
          _.copy(passwordHash = passwordOpt.map(Bcrypt.hash), passwordPassedUsers = Set())
        },
        getUpdateFunc(expiryDateChange)(expiryDate => _.copy(expiryDate = expiryDate)),
        getUpdateFunc(isDisabledChange)(isDisabled => _.withIsDisabled(isDisabled)),
        getUpdateFunc(whitelistDomainsChange)(whitelistDomains => _.withWhitelistedDomains(whitelistDomains)),
        getUpdateFunc(isRequireApprovalChange)(isRequireApproval => _.withIsRequiredApproval(isRequireApproval))
      )
    )
    for {
      linkModel <- get(linkId)
      newLinkModel <- update(linkModel)(func)
    } yield new ProtectedLinkModel(newLinkModel)
  }

  def getWhitelabelState(linkId: ProtectedLinkId): RecordTask[Option[ProtectedLinkWhitelabel]] = {
    for {
      linkModel <- get(linkId)
    } yield linkModel.getWhitelabel
  }

  def getAuthenticationWhitelabelId(linkId: ProtectedLinkId): RecordTask[Option[AuthenticationWhitelabelId]] = {
    for {
      linkModel <- store.get(linkId)
    } yield linkModel.authenticationWhitelabelId
  }

  def updateWhitelabel(
    linkId: ProtectedLinkId,
    whitelabel: ProtectedLinkWhitelabel
  ): RecordTask[ProtectedLinkModel] = {
    for {
      linkModel <- get(linkId)
      newLinkModel <- update(linkModel)(_.withWhitelabel(whitelabel))
    } yield new ProtectedLinkModel(newLinkModel)
  }

  def updateEnterpriseLoginLink(
    linkId: ProtectedLinkId,
    enterpriseLoginLinkId: Option[EnterpriseLoginLinkId]
  ): RecordTask[ProtectedLinkModel] = {
    for {
      linkModel <- get(linkId)
      newLinkModel <- update(linkModel)(_.copy(enterpriseLoginLinkId = enterpriseLoginLinkId))
    } yield new ProtectedLinkModel(newLinkModel)
  }

  def updateAuthenticationWhitelabel(
    linkId: ProtectedLinkId,
    authenticationWhitelabelId: Option[AuthenticationWhitelabelId]
  ): RecordTask[ProtectedLinkModel] = {
    for {
      linkModel <- get(linkId)
      newLinkModel <- update(linkModel)(_.withAuthenticationWhitelabelId(authenticationWhitelabelId))
    } yield new ProtectedLinkModel(newLinkModel)
  }

}

object ProtectedLinkStoreOperations
    extends FDBOperations.Single[RecordEnum, ProtectedLinkStoreOperations](ProtectedLinkStoreProvider) {

  final case class CheckPassword(userPasswordInput: Option[String])

  private final case class InternalCheckResult(isAccessGranted: Boolean)

  final class ProtectedLinkModel(linkModel: ProtectedLink) {

    def getLinkId: ProtectedLinkId = linkModel.linkId

    def getExpiryDate: Option[Instant] = linkModel.expiryDate

    def getAssetOpt(userId: UserId, emailDomain: String, now: Instant): Option[String] = {
      val isDomainWhitelisted = checkDomain(emailDomain)
      val isPasswordPassed = checkIsPasswordPassed(userId)
      val isAccessGranted = checkIsAccessGranted(userId)
      val shouldShowAsset = isAccessGranted ||
        isPasswordPassed && isDomainWhitelisted && !checkIsDisabled && !checkIsExpired(now)
      Option.when(shouldShowAsset)(linkModel.asset)
    }

    def hasPassword: Boolean = linkModel.passwordHash.nonEmpty

    def getPasswordHash: Option[String] = linkModel.passwordHash

    def checkIsPasswordPassed(userId: UserId): Boolean = !hasPassword || linkModel.passwordPassedUsers.contains(userId)

    def checkIsPendingEmailVerification(userId: UserId): Boolean =
      linkModel.pendingEmailVerificationUsers.contains(userId)

    def checkPassword(userPasswordInput: Option[String]): Boolean = {
      linkModel.passwordHash.forall { storedPasswordHash =>
        userPasswordInput.exists(Bcrypt.validate(_, storedPasswordHash))
      }
    }

    def checkIsDisabled: Boolean = linkModel.isDisabled

    def checkIsExpired(now: Instant): Boolean = linkModel.expiryDate.exists(_.isBefore(now))

    def checkDomain(emailDomain: String): Boolean = {
      linkModel.whitelistedDomains.isEmpty || linkModel.whitelistedDomains.contains(emailDomain)
    }

    def getWhitelistedDomains: Set[String] = linkModel.whitelistedDomains

    def checkIsAccessGranted(userId: UserId): Boolean = linkModel.accessGrantedUsers.contains(userId)

    def getWhitelabel: Option[ProtectedLinkWhitelabel] = linkModel.whitelabel

    def getIsRequiredApproval: Boolean = linkModel.isRequiredApproval

    def getEnterpriseLoginLinkId: Option[EnterpriseLoginLinkId] = linkModel.enterpriseLoginLinkId

    def update(func: ProtectedLink => ProtectedLink): ProtectedLink = func(linkModel)
  }

}
