// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.account.protocol

import anduin.account.protocol.BifrostAuthenticationProtocol.EnterpriseLoginUserLinkedConfigInfo
import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.environment.EnvironmentPolicyCommonProtocols.EnvironmentAuthenticationDecision
import anduin.id.authwhitelabel.AuthenticationWhitelabelId
import io.circe.Codec
import anduin.model.codec.ProtoCodecs.given

import anduin.protobuf.authwhitelabel.AuthenticationWhitelabelPolicy

object BifrostCommonProtocol {

  sealed trait CaptchaResponse derives CanEqual

  object CaptchaResponse {
    given Codec.AsObject[CaptchaResponse] = deriveCodecWithDefaults

    case object NoResponse extends CaptchaResponse

    given Codec.AsObject[NoResponse.type] = deriveCodecWithDefaults

    final case class TurnstileInvisibleResponse(
      response: String
    ) extends CaptchaResponse

    object TurnstileInvisibleResponse {
      given Codec.AsObject[TurnstileInvisibleResponse] = deriveCodecWithDefaults
    }

    final case class TurnstileVisibleResponse(
      response: String
    ) extends CaptchaResponse

    object TurnstileVisibleResponse {
      given Codec.AsObject[TurnstileVisibleResponse] = deriveCodecWithDefaults
    }

    final case class BackdoorResponse(
      token: String
    ) extends CaptchaResponse

    object BackdoorResponse {
      given Codec.AsObject[BackdoorResponse] = deriveCodecWithDefaults
    }

  }

  sealed trait RecaptchaResponse derives CanEqual

  object RecaptchaResponse {

    given Codec.AsObject[RecaptchaResponse] = deriveCodecWithDefaults

    final case class AccountRecaptchaV2Response(
      response: Option[String]
    ) extends RecaptchaResponse

    object AccountRecaptchaV2Response {
      given Codec.AsObject[AccountRecaptchaV2Response] = deriveCodecWithDefaults
    }

    final case class AccountRecaptchaV3Response(
      response: Option[String]
    ) extends RecaptchaResponse

    object AccountRecaptchaV3Response {
      given Codec.AsObject[AccountRecaptchaV3Response] = deriveCodecWithDefaults
    }

    final case class RecaptchaBackdoorToken(
      token: String
    ) extends RecaptchaResponse

    object RecaptchaBackdoorToken {
      given Codec.AsObject[RecaptchaBackdoorToken] = deriveCodecWithDefaults
    }

  }

  final case class VerifyCookieParams(
    redirectUrl: Option[String]
  )

  object VerifyCookieParams {
    given Codec.AsObject[VerifyCookieParams] = deriveCodecWithDefaults
  }

  final case class VerifyCookieResponse(
    token: String
  )

  object VerifyCookieResponse {
    given Codec.AsObject[VerifyCookieResponse] = deriveCodecWithDefaults
  }

  sealed trait VerifyCookieException extends Exception

  object VerifyCookieException {
    given Codec.AsObject[VerifyCookieException] = deriveCodecWithDefaults
    case object CookieNotFound extends VerifyCookieException
    case object InvalidCookie extends VerifyCookieException

    final case class EnvironmentAuthenticationRequired(token: String) extends VerifyCookieException
    final case class EnvironmentAuthenticationGlobalEnforced(loginUrl: String) extends VerifyCookieException

    given Codec.AsObject[CookieNotFound.type] = deriveCodecWithDefaults
    given Codec.AsObject[InvalidCookie.type] = deriveCodecWithDefaults
    given Codec.AsObject[EnvironmentAuthenticationRequired] = deriveCodecWithDefaults
    given Codec.AsObject[EnvironmentAuthenticationGlobalEnforced] = deriveCodecWithDefaults
  }

  final case class VerifyCookieTokenExchangeParams(
    token: String
  )

  object VerifyCookieTokenExchangeParams {
    given Codec.AsObject[VerifyCookieTokenExchangeParams] = deriveCodecWithDefaults
  }

  final case class VerifyCookieTokenExchangeResponse(
    accountEnforcementToken: String
  )

  object VerifyCookieTokenExchangeResponse {
    given Codec.AsObject[VerifyCookieTokenExchangeResponse] = deriveCodecWithDefaults
  }

  sealed trait VerifyCookieTokenExchangeException extends Exception

  object VerifyCookieTokenExchangeException {
    given Codec.AsObject[VerifyCookieTokenExchangeException] = deriveCodecWithDefaults
    case object InvalidToken extends VerifyCookieTokenExchangeException
    case object ServerError extends VerifyCookieTokenExchangeException

    given Codec.AsObject[InvalidToken.type] = deriveCodecWithDefaults
    given Codec.AsObject[ServerError.type] = deriveCodecWithDefaults
  }

  final case class WhitelabelData(
    authenticationWhitelabelId: AuthenticationWhitelabelId,
    backgroundColor: String,
    providerLogo: Option[WhitelabelData.Logo],
    clientLogo: Option[WhitelabelData.Logo],
    anduinLogoDisplay: WhitelabelData.AnduinLogoDisplay,
    environmentDomain: Option[String],
    authenticationWhitelabelPolicy: Option[AuthenticationWhitelabelPolicy]
  )

  object WhitelabelData {

    given Codec.AsObject[WhitelabelData] = deriveCodecWithDefaults
    final case class Logo(url: String, height: Int)

    object Logo {
      given Codec.AsObject[Logo] = deriveCodecWithDefaults
    }

    sealed trait AnduinLogoDisplay derives CanEqual

    object AnduinLogoDisplay {
      given Codec.AsObject[AnduinLogoDisplay] = deriveCodecWithDefaults
      case object LogoHidden extends AnduinLogoDisplay
      case object LogoLight extends AnduinLogoDisplay
      case object LogoDark extends AnduinLogoDisplay

      given Codec.AsObject[LogoHidden.type] = deriveCodecWithDefaults
      given Codec.AsObject[LogoLight.type] = deriveCodecWithDefaults
      given Codec.AsObject[LogoDark.type] = deriveCodecWithDefaults
    }

  }

  sealed trait LoginAuthenticator derives CanEqual

  object LoginAuthenticator {

    given Codec.AsObject[LoginAuthenticator] = deriveCodecWithDefaults

    final case class Normal(userIdToken: String) extends LoginAuthenticator

    object Normal {
      given Codec.AsObject[Normal] = deriveCodecWithDefaults
    }

    final case class SSOEnforced(
      userIdTokenOpt: Option[String],
      domain: String,
      ssoConfig: EnterpriseLoginUserLinkedConfigInfo
    ) extends LoginAuthenticator

    object SSOEnforced {
      given Codec.AsObject[SSOEnforced] = deriveCodecWithDefaults
    }

    final case class AuthenticateByEnvironment(
      userIdTokenOpt: Option[String],
      environmentAuthentication: EnvironmentAuthenticationDecision,
      anduinAuthenticator: Option[LoginAuthenticator],
      immediateRedirectToSSO: Boolean
    ) extends LoginAuthenticator

    object AuthenticateByEnvironment {
      given Codec.AsObject[AuthenticateByEnvironment] = deriveCodecWithDefaults
    }

  }

}
