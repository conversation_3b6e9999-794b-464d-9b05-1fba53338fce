// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.account.component.auth

import anduin.account.component.layout.AccountLayoutComponents
import anduin.account.component.misc.{AccountCookieUtils, UserChangeDisplay}
import anduin.account.protocol.BifrostAuthenticationProtocol.EnterpriseLoginUserLinkedConfigInfo
import anduin.account.protocol.BifrostCommonProtocol.LoginAuthenticator
import anduin.environment.EnvironmentPolicyCommonProtocols.*
import anduin.environment.EnvironmentPolicyCommonProtocols.EnvironmentAuthenticationDecision.Decision
import anduin.environment.EnvironmentPolicyCommonProtocols.EnvironmentAuthenticationDecision.Decision.FallbackMethod
import anduin.environment.{EnvironmentConstants, EnvironmentPolicyCommonProtocols}
import anduin.id.authwhitelabel.AuthenticationWhitelabelId
import anduin.id.offering.GlobalOfferingId
import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.account.protocol.{BifrostCustomSSOClient, BifrostCustomSSOProtocol}
import anduin.protobuf.authwhitelabel.AuthenticationWhitelabelPolicy
import anduin.utils.RedirectorUtils
import com.anduin.stargazer.client.utils.ZIOUtils
import stargazer.model.routing.Page

final case class EnvironmentAuthenticationForm(
  userIdTokenOpt: Option[String],
  redirectUrl: Option[String],
  globalOfferingId: Option[GlobalOfferingId],
  email: String,
  environmentAuthenticationDecision: EnvironmentAuthenticationDecision,
  immediateRedirectToSSO: Boolean,
  anduinAuthenticator: Option[LoginAuthenticator],
  hasPassword: Boolean,
  enablePrimaryEmailOTP: Boolean,
  forceResetPassword: Boolean,
  ssoConfigs: Seq[EnterpriseLoginUserLinkedConfigInfo],
  authenticationWhitelabelId: Option[AuthenticationWhitelabelId],
  authenticationWhitelabelPolicy: Option[AuthenticationWhitelabelPolicy],
  environmentDomainOpt: Option[String],
  routerCtl: RouterCtl[Page],
  resetPasswordUrlOpt: Option[String],
  goback: Option[Callback],
  gotoFactor: AuthenticationForm.Factor => Callback
) {
  def apply(): VdomElement = EnvironmentAuthenticationForm.component(this)
}

object EnvironmentAuthenticationForm {

  private type Props = EnvironmentAuthenticationForm

  sealed trait RenderType derives CanEqual

  object RenderType {
    case object Waiting extends RenderType
    case object Normal extends RenderType
    final case class Redirect(ssoConfig: SSOConfig) extends RenderType
  }

  private final case class State(
    renderType: RenderType
  )

  private case class Backend(scope: BackendScope[Props, State]) {

    private def renderBlocked(props: Props, title: String, body: String): VdomElement = {
      <.div(
        <.div(
          tw.textDanger3.flex.justifyCenter,
          IconR(name = Icon.Glyph.CrossCircle, size = Icon.Size.Custom(64))()
        ),
        <.div(
          tw.mt16.text17.leading28.fontSemiBold.textCenter,
          title
        ),
        <.div(
          tw.mt8.text13.leading20,
          body
        ),
        props.goback.whenDefined(
          using { goback =>
            <.div(
              tw.mt24.flex.justifyCenter,
              Button(
                tpe = Button.Tpe.Plain(),
                style = Button.Style.Text(),
                onClick = AccountCookieUtils.removeRememberEmailToken() >> goback
              )("Go back")
            )
          }
        )
      )
    }

    private def renderAnduinAuth(props: Props, decision: EnvironmentAuthenticationDecision, userIdToken: String)
      : VdomElement = {
      val firstFactorForm = FirstFactorForm(
        props.redirectUrl,
        props.globalOfferingId,
        props.email,
        userIdToken,
        props.hasPassword,
        props.enablePrimaryEmailOTP,
        props.forceResetPassword,
        props.ssoConfigs,
        props.authenticationWhitelabelId,
        props.authenticationWhitelabelPolicy,
        props.environmentDomainOpt,
        props.routerCtl,
        props.resetPasswordUrlOpt,
        Some(decision),
        props.goback,
        props.gotoFactor
      )()
      props.anduinAuthenticator.fold(firstFactorForm) {
        case _: LoginAuthenticator.Normal => firstFactorForm
        case LoginAuthenticator.SSOEnforced(_, domain, ssoConfig) =>
          SSOEnforcedForm(
            email = props.email,
            domain = domain,
            ssoConfig = ssoConfig,
            goback = props.goback,
            getSSOLoginUrlTask = SSOButton.defaultGetSSOLoginUrlTask(
              BifrostCustomSSOProtocol.GetSSOLoginUrlParams(
                ssoConfig.configId,
                props.redirectUrl,
                Some(props.email),
                triggeredByEnvironment = false
              )
            )
          )()
        case _: LoginAuthenticator.AuthenticateByEnvironment => firstFactorForm
      }
    }

    private def renderProviders(props: Props, ssoConfigs: Seq[SSOConfig]): VdomElement = {
      <.div(
        AccountLayoutComponents.renderPageHeader("Welcome!"),
        UserChangeDisplay(props.email, props.goback)(),
        AccountLayoutComponents.renderForm(
          if (ssoConfigs.size > 1) {
            <.div("To continue, please select one of the following options:")
          } else {
            EmptyVdom
          },
          <.div(
            tw.mt4,
            ssoConfigs.toTagMod(
              using { ssoConfig =>
                <.div(
                  tw.mt12,
                  SSOButton(
                    providerName = ssoConfig.providerName,
                    providerLogoUrl = ssoConfig.providerLogoUrl,
                    getSSOLoginUrlTask = SSOButton.defaultGetSSOLoginUrlTask(
                      BifrostCustomSSOProtocol.GetSSOLoginUrlParams(
                        ssoConfig.configId,
                        props.redirectUrl,
                        Some(props.email),
                        true
                      )
                    )
                  )()
                )
              }
            )
          )
        )
      )
    }

    private def renderEmailBound(
      props: Props,
      decision: EnvironmentAuthenticationDecision,
      policy: EnvironmentAuthenticationPolicy
    ): VdomElement = {
      policy match {
        case EnvironmentAuthenticationPolicy.Fallback =>
          renderBlocked(
            props,
            EnvironmentConstants.defaultNoAccessMessageTitle,
            EnvironmentConstants.defaultNoAccessMessageBodyText
          )
        case EnvironmentAuthenticationPolicy.AnduinAuthentication =>
          props.userIdTokenOpt.fold(
            renderBlocked(
              props,
              EnvironmentConstants.defaultNoAccessMessageTitle,
              EnvironmentConstants.defaultNoAccessMessageBodyText
            )
          ) { userIdToken =>
            renderAnduinAuth(props, decision, userIdToken)
          }
        case EnvironmentAuthenticationPolicy.UseProvider(ssoConfig) => renderProviders(props, Seq(ssoConfig))
      }
    }

    private def renderUserBound(
      props: Props,
      decision: EnvironmentAuthenticationDecision,
      policy: EnvironmentAuthenticationPolicy,
      fallback: Decision.Fallback
    ): VdomElement = {
      policy match {
        case EnvironmentAuthenticationPolicy.Fallback => renderFallback(props, decision, fallback.fallbackMethod)
        case EnvironmentAuthenticationPolicy.AnduinAuthentication =>
          props.userIdTokenOpt.fold(
            renderBlocked(
              props,
              EnvironmentConstants.defaultNoAccessMessageTitle,
              EnvironmentConstants.defaultNoAccessMessageBodyText
            )
          ) { userIdToken =>
            renderAnduinAuth(props, decision, userIdToken)
          }
        case EnvironmentAuthenticationPolicy.UseProvider(ssoConfig) => renderProviders(props, Seq(ssoConfig))
      }
    }

    private def renderFallback(
      props: Props,
      decision: EnvironmentAuthenticationDecision,
      fallbackMethod: FallbackMethod
    ): VdomElement = {
      fallbackMethod match {
        case FallbackMethod.Blocked(title, body) => renderBlocked(props, title, body)
        case FallbackMethod.AnduinAuthentication =>
          props.userIdTokenOpt.fold(
            renderBlocked(
              props,
              EnvironmentConstants.defaultNoAccessMessageTitle,
              EnvironmentConstants.defaultNoAccessMessageBodyText
            )
          ) { userIdToken =>
            renderAnduinAuth(props, decision, userIdToken)
          }
        case FallbackMethod.ListProviders(ssoConfigs)     => renderProviders(props, ssoConfigs)
        case FallbackMethod.UseDefaultProvider(ssoConfig) => renderProviders(props, Seq(ssoConfig))
      }
    }

    private def renderWaiting(): VdomElement = {
      BlockIndicatorR()()
    }

    private def renderNormal(props: Props): VdomElement = {
      props.environmentAuthenticationDecision.decision match {
        case Decision.EmailBound(domain, bindingId, policy) =>
          renderEmailBound(props, props.environmentAuthenticationDecision, policy)
        case Decision.UserBound(bindingId, policy, fallback) =>
          renderUserBound(props, props.environmentAuthenticationDecision, policy, fallback)
        case Decision.Fallback(fallbackMethod) =>
          renderFallback(props, props.environmentAuthenticationDecision, fallbackMethod)
      }
    }

    private def renderRedirect(ssoConfig: SSOConfig): VdomElement = {
      <.div(
        tw.flex.flexCol.itemsCenter.justifyCenter,
        <.div(
          tw.mb16.flex.justifyCenter,
          BlockIndicatorR()()
        ),
        <.div(
          tw.text17.leading28.fontSemiBold.textCenter.textGray7,
          s"Redirecting to ${ssoConfig.providerName}"
        ),
        <.div(
          tw.mt8.text13.leading20.textCenter.textGray5,
          "Please wait..."
        )
      )
    }

    def render(props: Props, state: State): VdomElement = {
      state.renderType match {
        case RenderType.Waiting             => renderWaiting()
        case RenderType.Normal              => renderNormal(props)
        case RenderType.Redirect(ssoConfig) => renderRedirect(ssoConfig)
      }

    }

    def didMount(props: Props): Callback = {
      if (!props.immediateRedirectToSSO) {
        scope.modState(_.copy(renderType = RenderType.Normal))
      } else {
        val requiredSsoConfigs = props.environmentAuthenticationDecision.decision match {
          case Decision.EmailBound(_, _, policy) =>
            policy match {
              case EnvironmentAuthenticationPolicy.Fallback               => Seq.empty
              case EnvironmentAuthenticationPolicy.AnduinAuthentication   => Seq.empty
              case EnvironmentAuthenticationPolicy.UseProvider(ssoConfig) => Seq(ssoConfig)
            }
          case Decision.UserBound(bindingId, policy, fallback) =>
            policy match {
              case EnvironmentAuthenticationPolicy.Fallback =>
                fallback.fallbackMethod match {
                  case FallbackMethod.Blocked(_, _)                 => Seq.empty
                  case FallbackMethod.AnduinAuthentication          => Seq.empty
                  case FallbackMethod.ListProviders(ssoConfigs)     => ssoConfigs
                  case FallbackMethod.UseDefaultProvider(ssoConfig) => Seq(ssoConfig)
                }
              case EnvironmentAuthenticationPolicy.AnduinAuthentication   => Seq.empty
              case EnvironmentAuthenticationPolicy.UseProvider(ssoConfig) => Seq(ssoConfig)
            }
          case Decision.Fallback(fallbackMethod) =>
            fallbackMethod match {
              case FallbackMethod.Blocked(_, _)                 => Seq.empty
              case FallbackMethod.AnduinAuthentication          => Seq.empty
              case FallbackMethod.ListProviders(ssoConfigs)     => ssoConfigs
              case FallbackMethod.UseDefaultProvider(ssoConfig) => Seq(ssoConfig)
            }
        }
        if (requiredSsoConfigs.size != 1) {
          scope.modState(_.copy(renderType = RenderType.Normal))
        } else {
          requiredSsoConfigs.headOption.fold(scope.modState(_.copy(renderType = RenderType.Normal))) { ssoConfig =>
            ZIOUtils.toReactCallbackWithErrorHandler(
              BifrostCustomSSOClient
                .getSSOLoginUrl(
                  BifrostCustomSSOProtocol.GetSSOLoginUrlParams(
                    configId = ssoConfig.configId,
                    redirectUrl = props.redirectUrl,
                    email = Some(props.email),
                    triggeredByEnvironment = true
                  )
                )
                .map(
                  _.fold(
                    _ => scope.modState(_.copy(renderType = RenderType.Normal)),
                    response =>
                      scope.modState(
                        _.copy(renderType = RenderType.Redirect(ssoConfig)),
                        Callback {
                          RedirectorUtils.redirectToUrlUnsafe(response.url, 1000)
                        }
                      )
                  )
                ),
              _ => scope.modState(_.copy(renderType = RenderType.Normal))
            )
          }
        }
      }
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State(renderType = RenderType.Waiting))
    .renderBackend[Backend]
    .componentDidMount(scope => scope.backend.didMount(scope.props))
    .build

}
