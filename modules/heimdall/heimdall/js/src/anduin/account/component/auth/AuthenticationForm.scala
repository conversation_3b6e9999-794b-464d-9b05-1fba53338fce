// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.account.component.auth

import anduin.account.component.consent.CustomDisclaimerForm
import anduin.account.protocol.BifrostAuthenticationProtocol.EnterpriseLoginUserLinkedConfigInfo
import anduin.account.protocol.{BifrostCommonProtocol, BifrostCustomSSOProtocol}
import anduin.account.protocol.BifrostCommonProtocol.LoginAuthenticator
import anduin.disclaimer.DisclaimerSettingClient
import anduin.environment.EnvironmentPolicyCommonProtocols.EnvironmentAuthenticationDecision
import anduin.id.authwhitelabel.AuthenticationWhitelabelId
import anduin.id.offering.GlobalOfferingId
import anduin.mfa.MFAProtocol
import japgolly.scalajs.react.extra.router.RouterCtl

import stargazer.model.routing.Page
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.protobuf.authwhitelabel.AuthenticationWhitelabelPolicy

final case class AuthenticationForm(
  redirectUrl: Option[String],
  globalOfferingId: Option[GlobalOfferingId],
  email: String,
  authenticator: BifrostCommonProtocol.LoginAuthenticator,
  hasPassword: Boolean,
  enablePrimaryEmailOTP: Boolean,
  forceResetPassword: Boolean,
  ssoConfigs: Seq[EnterpriseLoginUserLinkedConfigInfo],
  authenticationWhitelabelId: Option[AuthenticationWhitelabelId],
  authenticationWhitelabelPolicy: Option[AuthenticationWhitelabelPolicy],
  environmentDomainOpt: Option[String],
  requiredDisclaimer: Option[DisclaimerSettingClient],
  routerCtl: RouterCtl[Page],
  resetPasswordUrlOpt: Option[String],
  goback: Option[Callback],
  toggleDisplayRegionInfo: Boolean => Callback = _ => Callback.empty
) {
  def apply(): VdomElement = AuthenticationForm.component(this)
}

object AuthenticationForm {

  type Props = AuthenticationForm

  sealed trait Factor derives CanEqual

  object Factor {
    final case class Disclaimer(disclaimer: DisclaimerSettingClient) extends Factor
    case object First extends Factor

    final case class Second(
      mfaToken: String,
      userMFAConfig: MFAProtocol.UserMFAConfig,
      environmentAuthenticationDecision: Option[EnvironmentAuthenticationDecision]
    ) extends Factor

  }

  private case class State(
    disclaimerAcceptanceToken: Option[String] = None,
    factor: Factor
  )

  private class Backend(scope: BackendScope[Props, State]) {

    private def gotoFactor(factor: Factor): Callback = {
      scope.modState(_.copy(factor = factor))
    }

    def render(props: Props, state: State): VdomNode = {
      state.factor match {
        case Factor.Disclaimer(disclaimer) =>
          props.authenticator match {
            case LoginAuthenticator.Normal(userIdToken) =>
              CustomDisclaimerForm(
                disclaimer,
                userIdToken,
                onAccept = acceptanceTokenOpt => {
                  for {
                    _ <- scope.modState(_.copy(disclaimerAcceptanceToken = acceptanceTokenOpt))
                    _ <- gotoFactor(Factor.First)
                    _ <- props.toggleDisplayRegionInfo(true)
                  } yield ()
                }
              )()
            case LoginAuthenticator.SSOEnforced(userIdTokenOpt, domain, ssoConfig) =>
              userIdTokenOpt.fold {
                SSOEnforcedForm(
                  email = props.email,
                  domain = domain,
                  ssoConfig = ssoConfig,
                  goback = props.goback,
                  getSSOLoginUrlTask = SSOButton.defaultGetSSOLoginUrlTask(
                    BifrostCustomSSOProtocol.GetSSOLoginUrlParams(
                      ssoConfig.configId,
                      props.redirectUrl,
                      Some(props.email),
                      triggeredByEnvironment = false
                    )
                  )
                )()
              } { userIdToken =>
                CustomDisclaimerForm(
                  disclaimer,
                  userIdToken,
                  onAccept = acceptanceTokenOpt => {
                    for {
                      _ <- scope.modState(_.copy(disclaimerAcceptanceToken = acceptanceTokenOpt))
                      _ <- gotoFactor(Factor.First)
                      _ <- props.toggleDisplayRegionInfo(true)
                    } yield ()
                  }
                )()
              }
            case LoginAuthenticator
                  .AuthenticateByEnvironment(
                    userIdTokenOpt,
                    environmentAuthenticationDecision,
                    anduinAuthenticator,
                    immediateRedirectToSSO
                  ) =>
              userIdTokenOpt.fold {
                EnvironmentAuthenticationForm(
                  userIdTokenOpt = userIdTokenOpt,
                  redirectUrl = props.redirectUrl,
                  globalOfferingId = props.globalOfferingId,
                  email = props.email,
                  environmentAuthenticationDecision = environmentAuthenticationDecision,
                  immediateRedirectToSSO = immediateRedirectToSSO,
                  anduinAuthenticator = anduinAuthenticator,
                  hasPassword = props.hasPassword,
                  enablePrimaryEmailOTP = props.enablePrimaryEmailOTP,
                  forceResetPassword = props.forceResetPassword,
                  ssoConfigs = props.ssoConfigs,
                  authenticationWhitelabelId = props.authenticationWhitelabelId,
                  authenticationWhitelabelPolicy = props.authenticationWhitelabelPolicy,
                  environmentDomainOpt = props.environmentDomainOpt,
                  routerCtl = props.routerCtl,
                  resetPasswordUrlOpt = props.resetPasswordUrlOpt,
                  goback = props.goback,
                  gotoFactor = gotoFactor
                )()
              } { userIdToken =>
                CustomDisclaimerForm(
                  disclaimer,
                  userIdToken,
                  onAccept = acceptanceTokenOpt => {
                    for {
                      _ <- scope.modState(_.copy(disclaimerAcceptanceToken = acceptanceTokenOpt))
                      _ <- gotoFactor(Factor.First)
                      _ <- props.toggleDisplayRegionInfo(true)
                    } yield ()
                  }
                )()
              }
          }

        case Factor.First =>
          props.authenticator match {
            case LoginAuthenticator.Normal(userIdToken) =>
              FirstFactorForm(
                props.redirectUrl,
                props.globalOfferingId,
                props.email,
                userIdToken,
                props.hasPassword,
                props.enablePrimaryEmailOTP,
                props.forceResetPassword,
                props.ssoConfigs,
                props.authenticationWhitelabelId,
                props.authenticationWhitelabelPolicy,
                props.environmentDomainOpt,
                props.routerCtl,
                props.resetPasswordUrlOpt,
                None,
                props.goback,
                gotoFactor
              )()
            case LoginAuthenticator.SSOEnforced(_, domain, ssoConfig) =>
              SSOEnforcedForm(
                email = props.email,
                domain = domain,
                ssoConfig = ssoConfig,
                goback = props.goback,
                getSSOLoginUrlTask = SSOButton.defaultGetSSOLoginUrlTask(
                  BifrostCustomSSOProtocol.GetSSOLoginUrlParams(
                    ssoConfig.configId,
                    props.redirectUrl,
                    Some(props.email),
                    triggeredByEnvironment = false
                  )
                )
              )()
            case LoginAuthenticator
                  .AuthenticateByEnvironment(
                    userIdTokenOpt,
                    environmentAuthentication,
                    anduinAuthenticator,
                    immediateRedirectToSSO
                  ) =>
              EnvironmentAuthenticationForm(
                userIdTokenOpt = userIdTokenOpt,
                redirectUrl = props.redirectUrl,
                globalOfferingId = props.globalOfferingId,
                email = props.email,
                environmentAuthenticationDecision = environmentAuthentication,
                immediateRedirectToSSO = immediateRedirectToSSO,
                anduinAuthenticator = anduinAuthenticator,
                hasPassword = props.hasPassword,
                enablePrimaryEmailOTP = props.enablePrimaryEmailOTP,
                forceResetPassword = props.forceResetPassword,
                ssoConfigs = props.ssoConfigs,
                authenticationWhitelabelId = props.authenticationWhitelabelId,
                authenticationWhitelabelPolicy = props.authenticationWhitelabelPolicy,
                environmentDomainOpt = props.environmentDomainOpt,
                routerCtl = props.routerCtl,
                resetPasswordUrlOpt = props.resetPasswordUrlOpt,
                goback = props.goback,
                gotoFactor = gotoFactor
              )()
          }

        case Factor.Second(mfaToken, userMFAConfig, environmentAuthenticationDecision) =>
          SecondFactorForm(
            mfaToken,
            userMFAConfig,
            props.redirectUrl,
            props.globalOfferingId,
            props.email,
            props.authenticationWhitelabelId,
            props.environmentDomainOpt,
            environmentAuthenticationDecision,
            props.routerCtl,
            gotoFactor
          )()
      }
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps { props =>
      val factor = props.requiredDisclaimer.fold[Factor](Factor.First)(Factor.Disclaimer.apply)
      State(factor = factor)
    }
    .renderBackend[Backend]
    .build

}
