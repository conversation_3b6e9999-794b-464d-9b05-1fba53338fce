// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.account.authentication

import anduin.account.common.AccountCommonService
import anduin.account.enforcement.AccountEnforcementCoreService
import anduin.account.enterprise.EnterpriseService
import anduin.account.mfa.MFACoreService
import anduin.account.otpauth.{OTPAuthenticationService, RevertOTPAuthenticationService}
import anduin.account.profile.UserProfileService
import anduin.account.protocol.BifrostAuthenticationProtocol.*
import anduin.account.protocol.BifrostCommonProtocol.*
import anduin.account.session.UserSessionService
import anduin.account.signup.CompleteSignupService
import anduin.account.store.KeycloakLoginService
import anduin.account.store.KeycloakLoginService.LoginWithPasswordResponse
import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.environment.{EnvironmentAuthenticationIntegrationService, EnvironmentAuthenticationService}
import anduin.environment.EnvironmentPolicyCommonProtocols.EnvironmentAuthenticationAction.ActionMethod
import anduin.environment.EnvironmentPolicyCommonProtocols.{
  EnvironmentAuthenticationAction,
  EnvironmentAuthenticationDecision
}
import anduin.id.authwhitelabel.AuthenticationWhitelabelId
import anduin.id.environment.EnvironmentId
import anduin.id.offering.GlobalOfferingId
import anduin.id.token.TokenClaim
import anduin.jwt.Jwt
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.multiregion.MultiRegionLoginProtocols.{CreateUserLoginTokenParams, VerifyUserLoginTokenParams}
import anduin.multiregion.{MultiRegionLoginClient, MultiRegionLoginProtocols}
import anduin.protobuf.account.{EnterpriseLoginConfig, EnterpriseLoginEmailDomainBinding}
import anduin.protobuf.account.token.UserSessionMfaMethod
import com.anduin.stargazer.service.GondorConfig
import com.anduin.stargazer.service.account.{ReverifyPasswordException, ReverifyPasswordParams, ReverifyPasswordResponse}
import com.anduin.stargazer.service.utils.ZIOUtils
import io.circe.Codec
import zio.implicits.*
import zio.{Task, ZIO}
import java.util.concurrent.TimeUnit
import scala.concurrent.duration.Duration

import anduin.id.link.ProtectedLinkId
import anduin.link.ProtectedLinkCoreService

final case class LoginService(
  gondorConfig: GondorConfig,
  accountCommonService: AccountCommonService,
  userProfileService: UserProfileService,
  userSessionService: UserSessionService,
  keycloakLoginService: KeycloakLoginService,
  captchaService: CaptchaService,
  otpAuthenticationService: OTPAuthenticationService,
  enterpriseService: EnterpriseService,
  mfaCoreService: MFACoreService,
  accountEnforcementCoreService: AccountEnforcementCoreService,
  multiRegionLoginClient: MultiRegionLoginClient,
  environmentAuthenticationService: EnvironmentAuthenticationService,
  environmentAuthenticationIntegrationService: EnvironmentAuthenticationIntegrationService,
  protectedLinkCoreService: ProtectedLinkCoreService,
  revertOTPAuthenticationService: RevertOTPAuthenticationService,
  jwt: Jwt
) {

  private val userIdTokenTimeout = Duration(30, TimeUnit.MINUTES)
  private val loginTokenTimeout = Duration(10, TimeUnit.MINUTES)
  private val reauthenticationTokenTimeout = Duration(10, TimeUnit.MINUTES)
  val recaptchaBackdoorTokenTimeout: Duration = Duration(1, TimeUnit.DAYS)

  val completeSignupService: CompleteSignupService = CompleteSignupService(
    accountCommonService,
    accountEnforcementCoreService,
    jwt
  )

  val cookieAuthenticationService: CookieAuthenticationService = CookieAuthenticationService(
    gondorConfig,
    accountCommonService,
    userSessionService,
    accountEnforcementCoreService,
    environmentAuthenticationService,
    jwt
  )

  val passwordAuthenticationService: PasswordAuthenticationService = PasswordAuthenticationService(
    accountCommonService,
    jwt
  )

  val mfaAuthenticationService: MFAAuthenticationService = MFAAuthenticationService(
    accountCommonService,
    keycloakLoginService,
    otpAuthenticationService,
    userProfileService,
    accountEnforcementCoreService,
    environmentAuthenticationService,
    environmentAuthenticationIntegrationService,
    protectedLinkCoreService,
    revertOTPAuthenticationService,
    jwt
  )

  def createUserIdToken(userId: UserId, loginFromProtectedLink: Option[ProtectedLinkId]): Task[String] = {
    if (gondorConfig.commonConfig.multiRegionConfig.isSecondaryRegion) {
      multiRegionLoginClient
        .createUserIdToken(MultiRegionLoginProtocols.CreateUserIdTokenParams(userId, loginFromProtectedLink))
        .flatMap(ZIO.fromEither)
        .map(_.token)
    } else {
      ZIO.succeed(
        jwt.encodeTemporaryToken(
          LoginService.UserIdTokenClaim(userId, loginFromProtectedLink),
          accountCommonService.loginJwtService,
          userIdTokenTimeout
        )
      )
    }
  }

  def verifyUserIdToken(token: String): Task[LoginService.UserIdTokenClaim] = {
    if (gondorConfig.commonConfig.multiRegionConfig.isSecondaryRegion) {
      multiRegionLoginClient
        .verifyUserIdToken(MultiRegionLoginProtocols.VerifyUserIdTokenParams(token))
        .flatMap(ZIO.fromEither)
        .map { response =>
          LoginService.UserIdTokenClaim(response.userId, response.loginFromProtectedLink)
        }
    } else {
      ZIO.fromEither(jwt.decodeTemporaryToken[LoginService.UserIdTokenClaim](token, accountCommonService.loginJwtService))
    }
  }

  private def createLoginTokenForUserLocal(
    userId: UserId,
    authenticationWhitelabelId: Option[AuthenticationWhitelabelId],
    zombiePassthrough: Boolean,
    remoteSignup: Boolean,
    environmentAuthenticationAction: Option[EnvironmentAuthenticationAction],
    remoteLogin: Boolean
  ): Task[String] = {
    ZIO.attempt {
      jwt.encodeTemporaryToken(
        LoginService.LoginTokenClaim(
          userId,
          authenticationWhitelabelId,
          zombiePassthrough,
          remoteSignup,
          environmentAuthenticationAction,
          remoteLogin
        ),
        accountCommonService.loginJwtService,
        loginTokenTimeout
      )
    }
  }

  private def createLoginTokenForUserRemote(
    userId: UserId,
    authenticationWhitelabelId: Option[AuthenticationWhitelabelId],
    zombiePassthrough: Boolean,
    environmentAuthenticationAction: Option[EnvironmentAuthenticationAction]
  ): Task[String] = {
    multiRegionLoginClient
      .createUserLoginToken(
        CreateUserLoginTokenParams(
          userId,
          authenticationWhitelabelId,
          zombiePassthrough,
          environmentAuthenticationAction
        )
      )
      .flatMap(ZIO.fromEither)
      .map(_.token)
  }

  def createLoginTokenForUser(
    userId: UserId,
    authenticationWhitelabelId: Option[AuthenticationWhitelabelId],
    zombiePassthrough: Boolean,
    remoteSignup: Boolean,
    environmentAuthenticationAction: Option[EnvironmentAuthenticationAction],
    remoteLogin: Boolean
  ): Task[String] = {
    if (gondorConfig.commonConfig.multiRegionConfig.isSecondaryRegion) {
      createLoginTokenForUserRemote(
        userId,
        authenticationWhitelabelId,
        zombiePassthrough,
        environmentAuthenticationAction
      )
    } else {
      createLoginTokenForUserLocal(
        userId,
        authenticationWhitelabelId,
        zombiePassthrough,
        remoteSignup,
        environmentAuthenticationAction,
        remoteLogin
      )
    }
  }

  private def decodeLoginTokenLocal(loginToken: String): Task[LoginService.LoginTokenClaim] = {
    ZIO.fromEither(
      jwt.decodeTemporaryToken[LoginService.LoginTokenClaim](loginToken, accountCommonService.loginJwtService)
    )
  }

  private def decodeLoginTokenRemote(loginToken: String): Task[LoginService.LoginTokenClaim] = {
    multiRegionLoginClient.verifyUserLoginToken(VerifyUserLoginTokenParams(loginToken)).flatMap(ZIO.fromEither).map {
      response =>
        LoginService.LoginTokenClaim(
          userId = response.userId,
          wid = response.authenticationWhitelabelId,
          zp = response.zombiePassthrough,
          rs = response.remoteSignup,
          eaa = response.eaa,
          rl = response.remoteLogin
        )
    }
  }

  def decodeLoginToken(loginToken: String): Task[LoginService.LoginTokenClaim] = {
    if (gondorConfig.commonConfig.multiRegionConfig.isSecondaryRegion) {
      decodeLoginTokenRemote(loginToken)
    } else {
      decodeLoginTokenLocal(loginToken)
    }
  }

  def decodeVerifyRememberEmailToken(token: String): Task[LoginService.RememberEmailToken] = {
    ZIO
      .fromEither(
        jwt.decodePermanentToken[LoginService.RememberEmailToken](token, accountCommonService.loginJwtService)
      )
  }

  def verifyRememberEmailToken(token: String, redirectUrlOpt: Option[String]): Task[VerifyRememberEmailResponse] = {
    for {
      rememberEmailTokenClaim <- decodeVerifyRememberEmailToken(token)
        .onErrorHandleWith { _ =>
          ZIO.fail(VerifyRememberEmailException.InvalidToken)
        }
      response <- verifyLoginEmail(
        email = rememberEmailTokenClaim.email,
        rememberEmail = true,
        captchaResponseOpt = None,
        redirectUrlOpt = redirectUrlOpt
      ).onErrorHandleWith {
        case VerifyLoginEmailException.InvalidUser =>
          ZIO.fail(VerifyRememberEmailException.InvalidUser)
        case VerifyLoginEmailException.BotUser =>
          ZIO.fail(VerifyRememberEmailException.BotUser)
        case error: Throwable => ZIO.fail(error)
      }
    } yield VerifyRememberEmailResponse(
      email = rememberEmailTokenClaim.email,
      authenticator = response.authenticator,
      hasPassword = response.hasPassword,
      enablePrimaryEmailOTP = response.enablePrimaryEmailOTP,
      rememberEmailToken = response.rememberEmailToken,
      linkedSsoConfigs = response.linkedSsoConfigs
    )
  }

  def verifyEnvironmentAuthenticationEmail(token: String, redirectUrlOpt: Option[String])
    : Task[VerifyEnvironmentAuthenticationEmailResponse] = {
    for {
      tokenClaim <- ZIO
        .fromEither(
          jwt.decodeTemporaryToken[LoginService.EnvironmentAuthenticationEmailToken](
            token,
            accountCommonService.loginJwtService
          )
        )
        .onErrorHandleWith { _ =>
          ZIO.fail(VerifyEnvironmentAuthenticationEmailException.InvalidToken)
        }
      response <- verifyLoginEmail(
        email = tokenClaim.email.address,
        rememberEmail = false,
        captchaResponseOpt = None,
        redirectUrlOpt = redirectUrlOpt
      ).onErrorHandleWith {
        case VerifyLoginEmailException.InvalidUser =>
          ZIO.fail(VerifyEnvironmentAuthenticationEmailException.InvalidUser)
        case VerifyLoginEmailException.BotUser =>
          ZIO.fail(VerifyEnvironmentAuthenticationEmailException.BotUser)
        case error: Throwable => ZIO.fail(error)
      }
    } yield VerifyEnvironmentAuthenticationEmailResponse(
      email = tokenClaim.email.address,
      authenticator = response.authenticator,
      hasPassword = response.hasPassword,
      enablePrimaryEmailOTP = response.enablePrimaryEmailOTP,
      linkedSsoConfigs = response.linkedSsoConfigs
    )
  }

  def createEnvironmentAuthenticationEmailToken(emailAddress: EmailAddress): String = {
    jwt.encodeTemporaryToken(
      LoginService.EnvironmentAuthenticationEmailToken(emailAddress),
      accountCommonService.loginJwtService,
      loginTokenTimeout
    )
  }

  private def verifyLoginEmailWithoutSSOBinding(
    userIdOpt: Option[UserId],
    loginFromProtectedLink: Option[ProtectedLinkId]
  ): Task[LoginAuthenticator] = {
    for {
      userId <- ZIOUtils.optionToTask(userIdOpt, VerifyLoginEmailException.InvalidUser)
      isZombie <- userProfileService.isZombieUser(userId)
      _ <- ZIOUtils.when(isZombie) {
        ZIO.fail(VerifyLoginEmailException.InvalidUser)
      }
      userIdToken <- createUserIdToken(userId, loginFromProtectedLink)
    } yield LoginAuthenticator.Normal(userIdToken)
  }

  private def verifyLoginEmailWithSsoBinding(
    ssoConfig: EnterpriseLoginConfig,
    userIdOpt: Option[UserId],
    emailAddress: EmailAddress,
    loginFromProtectedLink: Option[ProtectedLinkId]
  ): Task[LoginAuthenticator] = {
    for {
      userIdTokenOpt <- ZIOUtils.traverseOption(userIdOpt)(userId => createUserIdToken(userId, loginFromProtectedLink))
    } yield LoginAuthenticator.SSOEnforced(
      userIdTokenOpt = userIdTokenOpt,
      domain = emailAddress.domain.value,
      ssoConfig = EnterpriseLoginUserLinkedConfigInfo(
        configId = ssoConfig.id,
        providerName = ssoConfig.providerName,
        providerLogoUrl = ssoConfig.providerLogoUrl
      )
    )
  }

  private def verifyLoginEmailWithoutEnvironmentAuthentication(
    ssoBindingOpt: Option[EnterpriseLoginEmailDomainBinding],
    userIdOpt: Option[UserId],
    emailAddress: EmailAddress,
    loginFromProtectedLink: Option[ProtectedLinkId]
  ): Task[LoginAuthenticator] = {
    ssoBindingOpt.fold {
      verifyLoginEmailWithoutSSOBinding(userIdOpt, loginFromProtectedLink)
    } { ssoBinding =>
      for {
        ssoConfigOpt <- enterpriseService.getOptEnterpriseLoginConfig(ssoBinding.configId)
        response <- ssoConfigOpt.fold {
          verifyLoginEmailWithoutSSOBinding(userIdOpt, loginFromProtectedLink)
        } { ssoConfig =>
          verifyLoginEmailWithSsoBinding(ssoConfig, userIdOpt, emailAddress, loginFromProtectedLink)
        }
      } yield response
    }
  }

  private def verifyLoginEmailWithEnvironmentAuthentication(
    environmentAuthenticationDecision: EnvironmentAuthenticationDecision,
    ssoBindingOpt: Option[EnterpriseLoginEmailDomainBinding],
    userIdOpt: Option[UserId],
    emailAddress: EmailAddress,
    loginFromProtectedLink: Option[ProtectedLinkId]
  ): Task[LoginAuthenticator] = {
    for {
      userIdTokenOpt <- ZIOUtils.traverseOption(userIdOpt)(userId => createUserIdToken(userId, None))
      anduinAuthenticatorOpt <- environmentAuthenticationService
        .decisionToActionMethod(environmentAuthenticationDecision)
        .fold(ZIO.none) {
          case _: ActionMethod.AnduinAuthentication =>
            verifyLoginEmailWithoutEnvironmentAuthentication(
              ssoBindingOpt,
              userIdOpt,
              emailAddress,
              loginFromProtectedLink
            ).map(Some(_))
          case ActionMethod.UseProvider(_) => ZIO.none
        }
    } yield LoginAuthenticator.AuthenticateByEnvironment(
      userIdTokenOpt = userIdTokenOpt,
      environmentAuthentication = environmentAuthenticationDecision,
      anduinAuthenticator = anduinAuthenticatorOpt,
      immediateRedirectToSSO = true // TODO: make this configurable
    )
  }

  def getLoginResponseForEmail(
    email: String,
    environmentIdOpt: Option[EnvironmentId],
    offeringIdOpt: Option[GlobalOfferingId],
    blockBot: Boolean,
    loginFromProtectedLink: Option[ProtectedLinkId]
  ): Task[LoginService.LoginEmailResponse] = {
    for {
      emailAddress <- ZIOUtils.optionToTask(EmailAddress.unapply(email), VerifyLoginEmailException.InvalidUser)
      ssoBindingOpt <- enterpriseService.getOptEmailDomainBinding(emailAddress.domain.value)
      userIdOpt <- userProfileService.getUserIdFromEmailAddress(email).map(Some(_)).onErrorHandleWith {
        case _: UserProfileService.EmailNotFound => ZIO.attempt(None)
        case error: Throwable                    => ZIO.fail(error)
      }
      isBotUser <- ZIOUtils
        .traverseOption(userIdOpt)(userId => userProfileService.isBotUser(userId, useCache = false))
        .map(_.getOrElse(false))
      _ <- ZIOUtils.when(isBotUser && blockBot) {
        ZIO.fail(VerifyLoginEmailException.BotUser)
      }
      hasPassword <- ZIOUtils.traverseOption(userIdOpt)(userProfileService.hasPassword).map(_.getOrElse(false))
      hasMFA <- ZIOUtils.traverseOption(userIdOpt)(mfaCoreService.hasMFA).map(_.getOrElse(false))
      linkedSSOs <- ZIOUtils
        .traverseOption(userIdOpt)(userId =>
          enterpriseService.getLinkedConfigInfosForUser(userId, filterOutEnvironment = true)
        )
        .map(_.getOrElse(Seq.empty))
      environmentAuthenticationDecisionOpt <- environmentAuthenticationService.getEnvironmentAuthenticationDecision(
        emailAddress,
        userIdOpt,
        environmentIdOpt,
        offeringIdOpt
      )
      authenticatorOpt <- environmentAuthenticationDecisionOpt.fold {
        verifyLoginEmailWithoutEnvironmentAuthentication(ssoBindingOpt, userIdOpt, emailAddress, loginFromProtectedLink)
          .map(Some(_))
          .catchSome { case VerifyLoginEmailException.InvalidUser =>
            ZIO.succeed(None)
          }
      } { environmentAuthenticationDecision =>
        if (loginFromProtectedLink.nonEmpty && !environmentAuthenticationDecision.decision.isEmailBound) {
          verifyLoginEmailWithoutEnvironmentAuthentication(
            ssoBindingOpt,
            userIdOpt,
            emailAddress,
            loginFromProtectedLink
          )
            .map { authenticator =>
              val userIdTokenOpt = authenticator match {
                case LoginAuthenticator.Normal(userIdToken)                                => Some(userIdToken)
                case LoginAuthenticator.SSOEnforced(userIdTokenOpt, _, _)                  => userIdTokenOpt
                case LoginAuthenticator.AuthenticateByEnvironment(userIdTokenOpt, _, _, _) => userIdTokenOpt
              }
              Some(
                LoginAuthenticator.AuthenticateByEnvironment(
                  userIdTokenOpt = userIdTokenOpt,
                  environmentAuthentication = environmentAuthenticationDecision.copy(decision =
                    if (environmentAuthenticationDecision.decision.isFallback) {
                      EnvironmentAuthenticationDecision.Decision
                        .Fallback(EnvironmentAuthenticationDecision.Decision.FallbackMethod.AnduinAuthentication)
                    } else {
                      environmentAuthenticationDecision.decision
                    }
                  ),
                  anduinAuthenticator = Some(authenticator),
                  immediateRedirectToSSO = true // TODO: make this configurable
                )
              )
            }
            .catchSome { case VerifyLoginEmailException.InvalidUser =>
              ZIO.succeed(None)
            }
        } else {
          verifyLoginEmailWithEnvironmentAuthentication(
            environmentAuthenticationDecision,
            ssoBindingOpt,
            userIdOpt,
            emailAddress,
            loginFromProtectedLink
          ).map(Some(_))
        }
      }
    } yield LoginService.LoginEmailResponse(
      userIdOpt = userIdOpt,
      authenticator = authenticatorOpt,
      hasPassword = hasPassword,
      enablePrimaryEmailOTP = !hasMFA,
      linkedSsoConfigs = linkedSSOs
    )
  }

  def verifyLoginEmail(
    email: String,
    rememberEmail: Boolean,
    captchaResponseOpt: Option[CaptchaResponse],
    redirectUrlOpt: Option[String]
  ): Task[VerifyLoginEmailResponse] = {
    for {
      _ <- ZIO.foreach(captchaResponseOpt) { captchaResponse =>
        captchaService.verifyCaptchaResponse(captchaResponse).onErrorHandleWith {
          case _: CaptchaService.CaptchaException => ZIO.fail(VerifyLoginEmailException.NonHuman)
          case error: Throwable                   => ZIO.fail(error)
        }
      }
      environmentDataOpt <- ZIOUtils.traverseOption2(redirectUrlOpt)(redirectUrl =>
        environmentAuthenticationService.environmentService.getEnvironmentFromRedirectUrl(redirectUrl)
      )
      loginResponse <- getLoginResponseForEmail(
        email = email,
        environmentIdOpt = environmentDataOpt.map(_._1.id),
        offeringIdOpt = environmentDataOpt.flatMap(_._2.offeringId),
        blockBot = true,
        loginFromProtectedLink = None
      )
      authenticator <- ZIOUtils.optionToTask(loginResponse.authenticator, VerifyLoginEmailException.InvalidUser)
      rememberEmailToken =
        if (rememberEmail) {
          Some(
            jwt.encodePermanentToken(
              LoginService.RememberEmailToken(email, "tr"),
              accountCommonService.loginJwtService,
              gondorConfig.commonConfig.rememberEmailCookieConfig.expire
            )
          )
        } else {
          Option.empty[String]
        }
    } yield VerifyLoginEmailResponse(
      authenticator = authenticator,
      hasPassword = loginResponse.hasPassword,
      enablePrimaryEmailOTP = loginResponse.enablePrimaryEmailOTP,
      rememberEmailToken = rememberEmailToken,
      linkedSsoConfigs = loginResponse.linkedSsoConfigs
    )
  }

  def loginWithToken(
    loginToken: String,
    oldCookieOpt: Option[String],
    ip: Option[String],
    userAgent: Option[String],
    mfaMethod: UserSessionMfaMethod,
    environmentIdOpt: Option[EnvironmentId],
    offeringIdOpt: Option[GlobalOfferingId]
  ): Task[CookieAuthenticationService.Cookie] = {
    for {
      loginTokenClaim <- decodeLoginToken(loginToken)
      _ <- userProfileService.invalidCache(loginTokenClaim.userId)
      cookie <- cookieAuthenticationService.loginWithToken(
        loginTokenClaim,
        oldCookieOpt,
        ip,
        userAgent,
        mfaMethod,
        environmentIdOpt,
        offeringIdOpt
      )
    } yield cookie
  }

  def reverifyUserPassword(userId: UserId, params: ReverifyPasswordParams): Task[ReverifyPasswordResponse] = {
    for {
      emailAddress <- userProfileService.getEmailAddress(userId)
      email = emailAddress.address
      loginResponse <- keycloakLoginService.loginWithPassword(email, params.password)
      token <- loginResponse match {
        case LoginWithPasswordResponse.Success | LoginWithPasswordResponse.MultiFactorRequired =>
          ZIO.attempt(createReauthenticationToken(userId))
        case LoginWithPasswordResponse.UserNotFound | LoginWithPasswordResponse.UserDisabled =>
          ZIO.fail(ReverifyPasswordException.ServerError)
        case LoginWithPasswordResponse.WrongPassword =>
          ZIO.fail(ReverifyPasswordException.WrongPassword)
        case LoginWithPasswordResponse.BruteforceDetected =>
          ZIO.fail(ReverifyPasswordException.BruteforceDetected)
      }
    } yield ReverifyPasswordResponse(token)
  }

  def createReauthenticationToken(userId: UserId): String = {
    jwt.encodeTemporaryToken(
      LoginService.AccountReauthenticationClaim(userId),
      accountCommonService.jwtService,
      reauthenticationTokenTimeout
    )
  }

  def verifyReauthenticationToken(token: String): Task[LoginService.AccountReauthenticationClaim] = {
    ZIO.fromEither(
      jwt.decodeTemporaryToken[LoginService.AccountReauthenticationClaim](token, accountCommonService.jwtService)
    )
  }

}

object LoginService {

  final case class UserIdTokenClaim(
    userId: UserId,
    loginFromProtectedLink: Option[ProtectedLinkId]
  ) extends TokenClaim.TemporaryToken

  object UserIdTokenClaim {
    given Codec.AsObject[UserIdTokenClaim] = deriveCodecWithDefaults
  }

  final case class LoginTokenClaim(
    userId: UserId,
    wid: Option[AuthenticationWhitelabelId],
    zp: Boolean, // zombie passthrough
    rs: Boolean, // remote signup
    eaa: Option[EnvironmentAuthenticationAction],
    rl: Boolean // remote login
  ) extends TokenClaim.TemporaryToken

  object LoginTokenClaim {
    given Codec.AsObject[LoginTokenClaim] = deriveCodecWithDefaults
  }

  final case class RememberEmailToken(
    email: String,
    tr: String // See above
  ) extends TokenClaim.PermanentToken

  object RememberEmailToken {
    given Codec.AsObject[RememberEmailToken] = deriveCodecWithDefaults
  }

  final case class LoginToken(
    userId: UserId,
    accountEnforcementToken: String
  )

  final case class AccountReauthenticationClaim(
    userId: UserId
  ) extends TokenClaim.TemporaryToken

  object AccountReauthenticationClaim {
    given Codec.AsObject[AccountReauthenticationClaim] = deriveCodecWithDefaults
  }

  final case class EnvironmentAuthenticationEmailToken(
    email: EmailAddress
  ) extends TokenClaim.TemporaryToken

  object EnvironmentAuthenticationEmailToken {
    given Codec.AsObject[EnvironmentAuthenticationEmailToken] = deriveCodecWithDefaults
  }

  final case class LoginEmailResponse(
    userIdOpt: Option[UserId],
    authenticator: Option[LoginAuthenticator],
    hasPassword: Boolean,
    enablePrimaryEmailOTP: Boolean,
    linkedSsoConfigs: Seq[EnterpriseLoginUserLinkedConfigInfo]
  )

}
