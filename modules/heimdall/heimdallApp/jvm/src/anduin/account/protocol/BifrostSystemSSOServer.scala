// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.account.protocol

import anduin.account.{BifrostService, LinkAccountService, Oauth2Service}
import anduin.tapir.server.EndpointServer.TapirServerService
import anduin.tapir.server.PublicEndpointServer
import anduin.telemetry.TelemetryEnvironment
import com.anduin.stargazer.service.{CommonConfig, GondorBackendConfig}
import com.anduin.stargazer.service.authorization.AuthorizationService
import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter
import zio.ZIO

import anduin.account.protocol.BifrostSystemSSOEndpoints.*
import anduin.account.protocol.BifrostSystemSSOProtocol.*
import com.anduin.stargazer.service.utils.{ZIOTelemetryUtils, ZIOUtils}

final class BifrostSystemSSOServer(
  protected val backendConfig: GondorBackendConfig,
  protected val commonConfig: CommonConfig,
  protected val authorizationService: AuthorizationService,
  bifrostService: BifrostService,
  override protected val interpreter: ArmeriaZioServerInterpreter[Any],
  override protected val tracingEnvironment: TelemetryEnvironment.Tracing
) extends PublicEndpointServer {

  val services: List[TapirServerService] = List(
    publicRoute(verifyLinkAccountToken) { (param, _) =>
      val task =
        for {
          linkAccountTokenClaim <- bifrostService.oauth2Service
            .verifyLinkAccountToken(param.token)
          _ <- ZIOTelemetryUtils.traceUserId(linkAccountTokenClaim.userId)
        } yield VerifyLinkAccountTokenResponse(
          linkAccountTokenClaim.identityUsername,
          linkAccountTokenClaim.identityProvider,
          linkAccountTokenClaim.redirectUrl
        )
      ZIOUtils.toTaskEitherZIO(task.provideEnvironment(tracingEnvironment.environment)) {
        case exception: Oauth2Service.VerifyLinkAccountTokenException =>
          ZIO.logWarningCause("verify link account token exception", exception.toCause).as {
            exception match {
              case Oauth2Service.VerifyLinkAccountTokenException.InvalidToken =>
                VerifyLinkAccountTokenException.InvalidToken
            }
          }
        case error: Throwable =>
          ZIO
            .logWarningCause("verify link account token exception", error.toCause)
            .as(VerifyLinkAccountTokenException.ServerError)
      }
    },
    publicRoute(startLinkAccount) { (param, _) =>
      val task = bifrostService.linkAccountService
        .startLinkAccountFlow(param.token, param.authenticationWhitelabelId)
        .map { case (_, resendEmailToken) => StartLinkAccountResponse(resendEmailToken) }
      ZIOUtils.toTaskEitherZIO(task) {
        case exception: LinkAccountService.StartLinkAccountFlowException =>
          ZIO.logWarningCause("start link account exception", exception.toCause).as {
            exception match {
              case LinkAccountService.LinkAccountServiceException.InvalidToken => StartLinkAccountException.InvalidToken
              case _: LinkAccountService.LinkAccountServiceException.ProviderNotFound =>
                StartLinkAccountException.ProviderNotFound
              case LinkAccountService.LinkAccountServiceException.UserNotFound => StartLinkAccountException.UserNotFound
            }
          }
        case error: Throwable =>
          ZIO.logWarningCause("start link account exception", error.toCause).as(StartLinkAccountException.ServerError)
      }
    },
    publicRoute(resendLinkAccountEmail) { (param, _) =>
      val task = bifrostService.linkAccountService
        .resentLinkAccountEmail(param.token, param.authenticationWhitelabelId)
        .map { case (_, resendEmailToken) => ResendLinkAccountEmailResponse(resendEmailToken) }
      ZIOUtils.toTaskEitherZIO(task) {
        case exception: LinkAccountService.ResendEmailException =>
          ZIO.logWarningCause("resend link account email exception", exception.toCause).as {
            exception match {
              case LinkAccountService.LinkAccountServiceException.InvalidToken =>
                ResendLinkAccountEmailException.InvalidToken
              case _: LinkAccountService.LinkAccountServiceException.ProviderNotFound =>
                ResendLinkAccountEmailException.ProviderNotFound
              case LinkAccountService.LinkAccountServiceException.UserNotFound =>
                ResendLinkAccountEmailException.UserNotFound
            }
          }
        case error: Throwable =>
          ZIO
            .logWarningCause("resend link account email exception", error.toCause)
            .as(ResendLinkAccountEmailException.ServerError)
      }
    },
    publicRoute(verifyRedirectUrl) { (param, _) =>
      val task = ZIO.fromEither(
        bifrostService.accountCommonService
          .verifyRedirectUrlClaim(
            param.jwtString
          )
          .map(claim => VerifyRedirectUrlResponse(claim.redirectUrl))
      )
      ZIOUtils.toTaskEitherZIO(task) { case error: Throwable =>
        ZIO.logWarningCause("Verify redirect url error", error.toCause).as(VerifyRedirectUrlException.InvalidJwt)
      }
    }
  )

}
