// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings

import design.anduin.components.toast.Toast
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.reusesignature.UpdateAllowFormEditPostSigningParams
import anduin.fundsub.signature.AllowFormEditPostSigningUtils
import anduin.id.fundsub.FundSubId
import anduin.protobuf.fundsub.FeatureSwitch
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

private[settings] final case class AllowFormEditPostSigningSwitcher(
  fundSubId: FundSubId,
  featureSwitch: FeatureSwitch
) {
  def apply(): VdomElement = AllowFormEditPostSigningSwitcher.component(this)
}

private[settings] object AllowFormEditPostSigningSwitcher {
  private type Props = AllowFormEditPostSigningSwitcher

  private final case class State(
    isUpdating: Boolean = false,
    isEnabled: Option[Boolean] = None
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      val isEnabled =
        state.isEnabled.getOrElse(props.featureSwitch.allowFormEditPostSigning)
      FundSubSettingSwitch(
        enabled = isEnabled,
        onChange = checked => Callback.unless(state.isUpdating)(update(props, checked)),
        isDisabled = state.isUpdating,
        title = AllowFormEditPostSigningUtils.FundSettingFeatureLabel,
        description = () =>
          Option(
            <.div(
              <.div(
                s"Investors can update their ${FundSubCopyUtils.getFlowTerm.termInDocumentContext} after signing without requiring new signatures.",
                " Only allowed if signature page content remains unchanged."
              )
            )
          )
      )()
    }

    def update(props: Props, isChecked: Boolean): Callback = {
      val cb = for {
        _ <- ZIOUtils.toReactCallback {
          FundSubEndpointClient
            .updateAllowFormEditPostSigning(
              UpdateAllowFormEditPostSigningParams(isChecked, props.fundSubId)
            )
            .map(
              _.fold(
                _ => Toast.errorCallback(s"Failed to update config"),
                _ => Toast.successCallback(s"Fund setting update")
              )
            )
        }
        _ <- scope.modState(_.copy(isUpdating = false))
      } yield ()

      scope.modState(_.copy(isUpdating = true, isEnabled = Option(isChecked)), cb)
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
