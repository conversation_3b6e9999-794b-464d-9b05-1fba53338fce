// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings

import anduin.actionlogger.ActionEventLoggerJs
import anduin.fundsub.endpoint.common.UpdateUserTrackingParams
import anduin.fundsub.endpoint.dashboard.GetFundSubAdminNotificationPreferenceParams
import anduin.fundsub.endpoint.graphql.FundAdminInfoWithInvitationStatus
import anduin.fundsub.endpoint.group.FundSubGroupRoleType
import anduin.fundsub.endpoint.subscriptiondoc.review.FundSubSubscriptionDocReviewType
import anduin.fundsub.rebac.FundSubRebacModel.FundManagerRelation
import anduin.id.entity.EntityId
import anduin.id.fundsub.FundSubId
import anduin.model.common.user.UserId
import anduin.protobuf.fundsub.models.InactiveNotificationSetting
import anduin.protobuf.fundsub.notification.FundAdminNotificationPreference
import anduin.protobuf.fundsub.{CustomFundIdSetting, FeatureSwitch, LpFlowType}
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.investor.settings.auditlog.AuditLogTable
import com.anduin.stargazer.fundsub.module.investor.settings.customfundid.ManageCustomFundIdSetting
import com.anduin.stargazer.fundsub.module.investor.settings.dataroomlink.ManageShareDataRoomLink
import com.anduin.stargazer.fundsub.module.investor.settings.documents.{
  FundReferenceDocumentDetails,
  ViewSubscriptionDocument
}
import com.anduin.stargazer.fundsub.module.investor.settings.emaillog.EmailLogTable
import com.anduin.stargazer.fundsub.module.investor.settings.emailtemplate.ManageFundSubEmailTemplateSetting
import com.anduin.stargazer.fundsub.module.investor.settings.notifications.NewNotificationsSettings
import com.anduin.stargazer.fundsub.module.investor.settings.pointsofcontact.PointOfContactSetting
import com.anduin.stargazer.fundsub.module.investor.settings.review.ManageReviewDocumentSetting
import com.anduin.stargazer.fundsub.module.investor.settings.review.subdoc.ManageReviewSubscriptionDocSetting
import com.anduin.stargazer.fundsub.module.investor.settings.review.supportingdoc.{
  AdminGroupDetailProvider,
  ManageSupportingDocReviewSetting,
  PreventLpUploadSupportingDocAfterCountersignedSwitcher
}
import com.anduin.stargazer.fundsub.module.model.FundSubClientModel.{
  FundSubAdminRestrictedClientModel,
  FundSubPublicClientModel
}
import design.anduin.components.divider.react.DividerR
import design.anduin.components.icon.Icon
import design.anduin.components.layout.HStack
import design.anduin.components.list.react.ListR
import design.anduin.components.portal.PortalPosition
import design.anduin.components.responsive.{ContainerWidth, WithContainerWidth}
import design.anduin.components.toast.Toast
import design.anduin.components.tour.TourAppearance
import design.anduin.components.tour.react.{TourR, TourStepR, TourTargetR}
import design.anduin.components.util.ComponentUtils
import design.anduin.components.wrapper.react.WrapperR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*
import org.scalajs.dom.HTMLElement

import com.anduin.stargazer.fundsub.module.investor.settings.comment.FormCommentSettingSwitch
import com.anduin.stargazer.fundsub.module.investor.settings.integrations.FundSubIntegrationsSetting
import com.anduin.stargazer.fundsub.module.investor.settings.mailserver.MailServerSettingsPage
import stargazer.model.routing.DynamicAuthPage.FundSubAdminPage
import stargazer.model.routing.Page

private[investor] final case class FundSubSettingTab(
  page: FundSubAdminPage,
  router: RouterCtl[Page],
  entityId: EntityId,
  currentUserId: UserId,
  fundSubPublicClientModel: FundSubPublicClientModel,
  fundSubAdminRestrictedClientModel: FundSubAdminRestrictedClientModel,
  adminInfos: Seq[FundAdminInfoWithInvitationStatus],
  userGroupRole: FundSubGroupRoleType,
  onAdminChanged: Callback,
  refetchFsPublicModelCb: Callback,
  refetchFsResModelCb: Callback
) {

  def apply(): VdomElement = FundSubSettingTab.component(this)

  val fundSubId: FundSubId = page.fundSubId
}

private[investor] object FundSubSettingTab {

  private type Props = FundSubSettingTab

  private[settings] sealed trait SettingsGroup derives CanEqual {
    def icon: Icon.Name
    def label: String
    def description: Option[String] = None
    def urlParamsName: String
    def actionTypeOpt: Option[FundManagerRelation] = None

    def hasPermission(userGroupRole: FundSubGroupRoleType): Boolean = {
      this match {
        case Review | EmailLog | AuditLog => userGroupRole.isAdmin
        case _ =>
          userGroupRole match {
            case FundSubGroupRoleType.FundSubAdminRole => true
            case FundSubGroupRoleType.FundSubCustomRole(customRoleRelations) =>
              actionTypeOpt.forall(customRoleRelations.contains)
          }
      }

    }

  }

  private[settings] case object General extends SettingsGroup {
    override def icon: Icon.Name = Icon.Glyph.Cog

    override def label: String = "General"

    override def urlParamsName: String = "general"

    override def actionTypeOpt: Option[FundManagerRelation] = Some(FundManagerRelation.ManageFundSetting)

  }

  private[settings] case object Documents extends SettingsGroup {
    override def icon: Icon.Name = Icon.Glyph.FileText

    override def label: String = "Documents"

    override def urlParamsName: String = "documents"

  }

  private[settings] case object Review extends SettingsGroup {
    override def icon: Icon.Name = Icon.Glyph.FileCheck

    override def label: String = "Review and approval"

    override def urlParamsName: String = "review"

  }

  private[settings] case object PointsOfContact extends SettingsGroup {
    override def icon: Icon.Name = Icon.Glyph.UserStar
    override def label: String = "Points of contact"
    override def urlParamsName: String = "contact"

    override def actionTypeOpt: Option[FundManagerRelation] = Some(FundManagerRelation.ManageFundSetting)

  }

  private[settings] case object EmailTemplate extends SettingsGroup {
    override def icon: Icon.Name = Icon.Glyph.Envelope
    override def label: String = "Email templates"
    override def description: Option[String] = Option("Manage the default content for investor email notifications")
    override def urlParamsName: String = "emailtemplate"

    override def actionTypeOpt: Option[FundManagerRelation] = Some(FundManagerRelation.ManageFundSetting)

  }

  private[settings] case object MailServer extends SettingsGroup {
    override def icon: Icon.Name = Icon.Glyph.At

    override def label: String = "Outgoing email server"

    override def description: Option[String] = Option("Manage outgoing email server and its settings")

    override def urlParamsName: String = "mailserver"

    override def actionTypeOpt: Option[FundManagerRelation] = Some(FundManagerRelation.ManageFundSetting)

  }

  private[settings] case object Notifications extends SettingsGroup {
    override def icon: Icon.Name = Icon.Glyph.Bell
    override def label: String = "Email notifications"
    override def urlParamsName: String = "notifications"
  }

  private[settings] case object Customizations extends SettingsGroup {
    override def icon: Icon.Name = Icon.Glyph.Edit
    override def label: String = "Customizations"
    override def urlParamsName: String = "customizations"

    override def actionTypeOpt: Option[FundManagerRelation] = Some(FundManagerRelation.ManageFundSetting)

  }

  private[settings] case object Integrations extends SettingsGroup {
    override def icon: Icon.Name = Icon.Glyph.GridAdd
    override def label: String = "Integrations"
    override def urlParamsName: String = "integrations"

    override def actionTypeOpt: Option[FundManagerRelation] = Some(FundManagerRelation.ManageFundSetting)
  }

  private[settings] case object AuditLog extends SettingsGroup {
    override def icon: Icon.Name = Icon.Glyph.SearchInline
    override def label: String = "Audit log"
    override def urlParamsName: String = "audit"

  }

  private[settings] case object EmailLog extends SettingsGroup {
    override def icon: Icon.Name = Icon.Glyph.EnvelopeClock
    override def label: String = "Email log"
    override def urlParamsName: String = "email"

  }

  private val AllSettingGroups = List[SettingsGroup](
    General,
    Documents,
    Review,
    PointsOfContact,
    EmailTemplate,
    Notifications,
    Customizations,
    Integrations,
    AuditLog,
    EmailLog
  )

  private final case class State(
    selectedSettingGroups: SettingsGroup,
    notificationPreferenceOpt: Option[FundAdminNotificationPreference] = None,
    showingMultipleEmailTemplateTour: Boolean = false
  )

  private val MultipleEmailTemplateTourId = "metg"
  private val MultipleEmailTemplateTourStepId = "mets1"

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def renderSettingGroup(
      items: List[ListR.Item],
      shouldRenderDividerAtTheBeginning: Boolean,
      groupTitle: String
    ) = {
      if (items.nonEmpty) {
        <.div(
          if (shouldRenderDividerAtTheBeginning) {
            DividerR()()
          } else {
            EmptyVdom
          },
          <.div(
            ^.height := 36.px,
            tw.pl12.fontSemiBold.flex.itemsCenter,
            groupTitle
          ),
          ListR(
            items = items
          )()
        )
      } else {
        EmptyVdom
      }
    }

    private def getListItemFromSettingGroup(settingGroup: SettingsGroup, state: State): ListR.Item = {
      ListR.Item(
        icon = Option(settingGroup.icon),
        isSelected = Option(state.selectedSettingGroups == settingGroup),
        testId = s"FundSubNameSetting-${settingGroup.label}",
        renderContent = () =>
          settingGroup match {
            case EmailTemplate =>
              TourTargetR(
                target = MultipleEmailTemplateTourStepId,
                tourIds = List(MultipleEmailTemplateTourId)
              )(settingGroup.label)
            case _ => settingGroup.label
          },
        onClick = onMoveToSettingsGroup(settingGroup)
      )
    }

    private def renderSettingSelection(props: Props, state: State) = {
      val generalItems = List(
        General,
        Documents,
        Review,
        PointsOfContact,
        Customizations,
        Integrations
      ).filter(_.hasPermission(props.userGroupRole)).map(getListItemFromSettingGroup(_, state))

      val emailItems = List(
        EmailTemplate,
        MailServer
      ).filter(_.hasPermission(props.userGroupRole)).map(getListItemFromSettingGroup(_, state))

      val logItems = List(
        AuditLog,
        EmailLog
      ).filter(_.hasPermission(props.userGroupRole)).map(getListItemFromSettingGroup(_, state))

      val personalSettingItems = List(
        Notifications
      ).map(getListItemFromSettingGroup(_, state))

      <.div(
        renderSettingGroup(
          items = generalItems,
          shouldRenderDividerAtTheBeginning = false,
          groupTitle = "Fund settings"
        ),
        renderSettingGroup(
          items = emailItems,
          shouldRenderDividerAtTheBeginning = generalItems.nonEmpty,
          groupTitle = "Email settings"
        ),
        renderSettingGroup(
          items = logItems,
          shouldRenderDividerAtTheBeginning = generalItems.nonEmpty && emailItems.nonEmpty,
          groupTitle = "Logs"
        ),
        renderSettingGroup(
          items = personalSettingItems,
          shouldRenderDividerAtTheBeginning = generalItems.nonEmpty && logItems.nonEmpty && emailItems.nonEmpty,
          groupTitle = "Personal settings"
        )
      )
    }

    private def onMoveToSettingsGroup(settingsGroup: SettingsGroup) = {
      for {
        props <- scope.props
        page = props.page
        router = props.router
        nextPage = page.copy(
          params = page.params + (FundSubAdminPage.settingsGroup -> settingsGroup.urlParamsName)
        )
        _ <- ActionEventLoggerJs.logPageViewCb(subPage = Some(s"Settings - ${settingsGroup.label}"))
        _ <- scope.modState(
          _.copy(selectedSettingGroups = settingsGroup),
          router.set(nextPage)
        )
      } yield ()
    }

    private def renderGeneral(props: Props) = {
      val featureSwitch = props.fundSubPublicClientModel.featureSwitch.getOrElse(FeatureSwitch())
      <.div(
        tw.spaceY32,
        FundSubNameSetting(
          fundSubId = props.fundSubId,
          initialFundSubName = props.fundSubPublicClientModel.fundName
        )(),
        FundAndCloseIdsSection(
          props.fundSubId
        )(),
        DividerR()(),
        TagMod.unless(featureSwitch.enableRia) {
          RiaBannerSetting(
            fundSubId = props.fundSubId,
            enabled = !featureSwitch.disableRiaBanner
          )()
        },
        ManageProtectedLink(
          props.fundSubId,
          props.fundSubAdminRestrictedClientModel.protectedLink
        )(),
        ManageShareDataRoomLink(
          fundSubId = props.fundSubId,
          initialSharedDataRoomLink = props.fundSubPublicClientModel.sharedDataRoomLink,
          initialSharedDataRoomLinkDisplayName = props.fundSubPublicClientModel.sharedDataRoomLinkDisplayName
        )(),
        CustomLpIdSetting(
          fundSubId = props.fundSubId,
          enabled = featureSwitch.enabledCustomLpId
        )(),
        DividerR()(),
        TagMod.when(featureSwitch.formCommentSwitch) {
          FormCommentSettingSwitch(
            props.fundSubId,
            props.fundSubPublicClientModel.disabledFormComment,
            props.fundSubPublicClientModel.suppressSendingFormCommentDigestEmail,
            props.fundSubPublicClientModel.formCommentDigestEmailExceptionLps,
            props.fundSubPublicClientModel.featureSwitch.exists(_.disableLpResolvingComment),
            props.fundSubPublicClientModel.inactiveCommentSetting
          )()
        },
        DividerR()(),
        InvestFromAdditionalEntitySetting(
          fundId = props.fundSubId,
          enabled = !featureSwitch.disableInvestFromAdditionalEntity
        )(),
        DownloadSubscriptionDocumentSettingSwitch(
          fundId = props.fundSubId,
          isEnabled = !featureSwitch.disableDownloadSubscriptionDocument
        )(),
        MarkAsNotApplicableSetting(
          props.fundSubId,
          allowMarkSupportingDocAsNotApplicable = !props.fundSubPublicClientModel.featureSwitch
            .exists(_.disabledMarkAsNotApplicable)
        )(),
        PreventLpUploadSupportingDocAfterCountersignedSwitcher(
          fundSubId = props.fundSubId,
          featureSwitch = featureSwitch
        )(),
        if (featureSwitch.showSwitchAllowFormEditPostSigning) {
          AllowFormEditPostSigningSwitcher(props.fundSubId, featureSwitch)()
        } else {
          EmptyVdom
        }
      )
    }

    private def renderNotificationSettings(props: Props) = {
      NewNotificationsSettings(
        props.fundSubId,
        props.fundSubPublicClientModel
      )()
    }

    private def renderDocuments(props: Props) = {
      <.div(
        tw.spaceY32,
        ViewSubscriptionDocument(
          fundSubId = props.fundSubId,
          enableFormDiff = props.fundSubPublicClientModel.featureSwitch.exists(_.enableFormDiff),
          enableEmbedInvestmentEntityOnSubdoc = props.fundSubPublicClientModel.lpGeneralConfig
            .flatMap(_.embedInvestorDataOnSubDocConfig)
            .nonEmpty,
          props.userGroupRole.canManageFundSetting
        )(),
        FundReferenceDocumentDetails(
          props.fundSubId,
          props.userGroupRole.canManageFundSetting
        )()
      )
    }

    private def renderReview(props: Props) = {
      val featureSwitch = props.fundSubPublicClientModel.featureSwitch.getOrElse(FeatureSwitch())
      <.div(
        tw.spaceY32,
        if (props.fundSubPublicClientModel.lpFlowType == LpFlowType.Flexible) {
          AdminGroupDetailProvider(
            props.fundSubId,
            adminAndInvestorGroupInfo =>
              <.div(
                ManageReviewSubscriptionDocSetting(
                  fundId = props.fundSubId,
                  reviewType = FundSubSubscriptionDocReviewType.UnsignedSubscription,
                  entityId = props.entityId,
                  featureSwitch = featureSwitch,
                  refetchFsPublicModelCb = props.refetchFsPublicModelCb,
                  adminAndInvestorGroupInfo = adminAndInvestorGroupInfo
                )(),
                ManageReviewSubscriptionDocSetting(
                  fundId = props.fundSubId,
                  reviewType = FundSubSubscriptionDocReviewType.SignedSubscription,
                  entityId = props.entityId,
                  featureSwitch = featureSwitch,
                  refetchFsPublicModelCb = props.refetchFsPublicModelCb,
                  adminAndInvestorGroupInfo = adminAndInvestorGroupInfo
                )(),
                if (featureSwitch.enableSupportingDocReview) {
                  ManageSupportingDocReviewSetting(
                    fundId = props.fundSubId,
                    adminAndInvestorGroupInfo = adminAndInvestorGroupInfo
                  )()
                } else {
                  EmptyVdom
                }
              )
          )()
        } else {
          ManageReviewDocumentSetting(
            props.fundSubId,
            entityId = props.entityId,
            adminInfos = props.adminInfos,
            reviewPackageData = props.fundSubAdminRestrictedClientModel.reviewPackageData,
            onNavigateSettingGroup = onMoveToSettingsGroup,
            lpFlowType = props.fundSubPublicClientModel.lpFlowType
          )()
        }
      )
    }

    private def renderCustomizations(props: Props) = {
      <.div(
        tw.spaceY32,
        ManageCustomFundIdSetting(
          props.fundSubId,
          props.fundSubAdminRestrictedClientModel.customFundIdSetting.getOrElse(CustomFundIdSetting())
        )(),
        ManageInactiveLpSetting(
          props.fundSubId,
          props.fundSubAdminRestrictedClientModel.inactiveNotificationSetting.getOrElse(InactiveNotificationSetting())
        )(),
        props.fundSubPublicClientModel.signatureConfig.fold(EmptyVdom) { config =>
          React.Fragment(
            DividerR()(),
            FundSignatureDateSetting(
              fundSubId = props.fundSubId,
              fundSubSignatureConfig = config,
              refetchFundSubPublicModel = props.refetchFsPublicModelCb
            )()
          )
        }
      )
    }

    private def renderAuditLog(props: Props) = {
      <.div(
        tw.flex1.flex.flexCol,
        <.div(
          tw.text20.fontSemiBold.textGray8.mb32,
          AuditLog.label
        ),
        <.div(
          tw.flex1.wPc100,
          AuditLogTable(props.fundSubId)()
        )
      )
    }

    private def renderEmailLog(props: Props) = {
      <.div(
        tw.flex1.flex.flexCol,
        <.div(
          tw.text20.fontSemiBold.textGray8.mb32,
          EmailLog.label
        ),
        <.div(
          tw.flex1.wPc100,
          EmailLogTable(props.fundSubId)()
        )
      )
    }

    private def renderEmailTemplateSetting(props: Props) = {
      WrapperR(ManageFundSubEmailTemplateSetting(props.fundSubId)())()
    }

    private def renderMailServerSetting(props: Props) = {
      MailServerSettingsPage(
        fundSubId = props.fundSubId
      )()
    }

    private def renderIntegrationsSetting(props: Props) = {
      val showAddIntegration = props.page.params.get("add").contains("true")
      WrapperR(
        FundSubIntegrationsSetting(
          fundSubId = props.fundSubId,
          userGroupRole = props.userGroupRole,
          showAddIntegration = showAddIntegration
        )()
      )()
    }

    private def renderPointsOfContact(props: Props) = {
      val featureSwitch = props.fundSubPublicClientModel.featureSwitch.getOrElse(FeatureSwitch())
      <.div(
        PointOfContactSetting(
          props.fundSubId,
          featureSwitch,
          props.refetchFsPublicModelCb
        )()
      )
    }

    def render(props: Props, state: State): VdomElement = {
      <.div(
        TourR(
          steps = Seq(
            TourStepR(
              target = MultipleEmailTemplateTourStepId,
              title = "Save time with email templates",
              content = <.div(
                "You can now create multiple email templates and set default ones for investor notifications."
              ),
              popoverPosition = PortalPosition.RightCenter
            )
          ),
          appearance = TourAppearance.Dark,
          tourId = MultipleEmailTemplateTourId,
          isRunning = state.showingMultipleEmailTemplateTour,
          onFinish = userSeenMultipleEmailTemplateTourGuide,
          onSkip = userSeenMultipleEmailTemplateTourGuide
        )(),
        HStack(
          modifier = HStack
            .Modifier()
            .main((ele: VdomTagOf[HTMLElement]) =>
              ele(
                ComponentUtils.testId(FundSubSettingTab, "Container"),
                tw.pb20
              )
            )
            .content((ele: VdomTagOf[HTMLElement]) => ele(tw.flex.justifyCenter.p16))
            .toggleButton((ele: VdomTagOf[HTMLElement]) => ele(tw.p16))
            .withLeftSidebar()
            .withStickySidebar(width = Option(250))
        )(
          HStack.Slots.Sidebar(
            <.div(
              tw.p16,
              renderSettingSelection(props, state)
            )
          ),
          HStack.Slots.Content(
            state.selectedSettingGroups match {
              case AuditLog => renderAuditLog(props)
              case EmailLog => renderEmailLog(props)
              case _ =>
                WithContainerWidth() { ele => containerWidthOpt =>
                  {
                    ele(
                      tw.flex.flexCol,
                      if (containerWidthOpt.exists(_.minWidth < ContainerWidth.Medium.minWidth)) {
                        tw.wPc100
                      } else {
                        ^.width := ContainerWidth.Medium.minWidth.px
                      },
                      <.div(
                        tw.mb32,
                        <.div(
                          tw.text20.fontSemiBold.leading32,
                          state.selectedSettingGroups.label
                        ),
                        state.selectedSettingGroups.description.fold(EmptyVdom) { description =>
                          <.div(
                            tw.textGray7.mt4,
                            description
                          )
                        }
                      ),
                      state.selectedSettingGroups match {
                        case Documents       => renderDocuments(props)
                        case Review          => renderReview(props)
                        case General         => renderGeneral(props)
                        case PointsOfContact => renderPointsOfContact(props)
                        case Notifications   => renderNotificationSettings(props)
                        case Customizations  => renderCustomizations(props)
                        case EmailTemplate   => renderEmailTemplateSetting(props)
                        case MailServer      => renderMailServerSetting(props)
                        case Integrations    => renderIntegrationsSetting(props)
                        case _               => <.div()
                      }
                    )
                  }
                }
            }
          )
        )
      )
    }

    def fetchNotificationPreference: Callback = {
      for {
        props <- scope.props
        fundSubId = props.fundSubId
        _ <- ZIOUtils.toReactCallback {
          FundSubEndpointClient
            .getFundAdminNotificationPreference(
              GetFundSubAdminNotificationPreferenceParams(
                fundSubId
              )
            )
            .map(
              _.fold(
                _ => Toast.errorCallback("Failed to load notification preference"),
                res =>
                  scope.modState(
                    _.copy(
                      notificationPreferenceOpt = Option(res.setting)
                    )
                  )
              )
            )
        }
      } yield ()
    }

    private def userSeenMultipleEmailTemplateTourGuide: Callback = {
      scope.modState(
        _.copy(showingMultipleEmailTemplateTour = false),
        ZIOUtils.toReactCallback(
          FundSubEndpointClient
            .updateUserTracking(
              UpdateUserTrackingParams(
                seenMultipleEmailTemplateGuideTourOpt = Option(true)
              )
            )
            .map(
              _.fold(
                _ => Toast.errorCallback("Failed to update user tracking info, try again"),
                _ => Callback.empty
              )
            )
        )
      )
    }

    def getUserSeenStatus: Callback = {
      ZIOUtils.toReactCallback {
        FundSubEndpointClient
          .getUserTracking(())
          .map(
            _.fold(
              _ => Toast.errorCallback("Failed to get user tracking info, try again"),
              res =>
                scope.modState(
                  _.copy(
                    showingMultipleEmailTemplateTour = !res.userTrackingModel.seenMultipleEmailTemplateGuideTour
                  )
                )
            )
          )
      }
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialStateFromProps { props =>
      val defaultSettingGroup =
        if (General.hasPermission(props.userGroupRole)) {
          General
        } else {
          Notifications
        }

      val selectedSettingGroupsOpt = AllSettingGroups.find { settingGroup =>
        val urlParamsName = props.page.params.getOrElse(
          FundSubAdminPage.settingsGroup,
          ""
        )
        settingGroup.hasPermission(props.userGroupRole) && settingGroup.urlParamsName == urlParamsName
      }
      State(selectedSettingGroups = selectedSettingGroupsOpt.getOrElse(defaultSettingGroup))
    }
    .renderBackend[Backend]
    .componentDidMount { scope =>
      for {
        _ <- scope.backend.fetchNotificationPreference
        _ <- scope.backend.getUserSeenStatus
        page = scope.props.page
        settingGroupUrlParamsName = scope.state.selectedSettingGroups.urlParamsName
        params = page.params + (FundSubAdminPage.settingsGroup -> settingGroupUrlParamsName)
        // update the page params if necessary
        _ <- Callback.when(params != page.params) {
          scope.props.router.set(page.copy(params = params))
        }
      } yield ()
    }
    .build

}
