// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.dashboard.v2.renderer

import com.raquo.airstream.core.Observer
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.divider.Divider
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL, ModalL}
import design.anduin.components.portal.{PortalPosition, PortalUtils}
import design.anduin.components.tag.Tag
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import zio.ZIO

import anduin.component.util.JsDateFormatterUtils
import anduin.dashboard.data.DataExtractionRequestDashboardSchema.{
  DataExtractionDashboardStatus,
  DataExtractionRequestDashboardInfo
}
import anduin.dashboard.data.{DashboardMetadata, DataExtractionCell}
import anduin.dashboard.model.{DashboardColumnRendererEnum, DataExtractionColumn}
import anduin.frontend.AirStreamUtils
import anduin.fundsub.dataextract.GetDataExtractRequestFormInfoResp.ViewOnlyModeData
import anduin.fundsub.dataextract.{
  CloseDataExtractFormViewParams,
  FetchDataExtractRequestForDashboard,
  FetchDataExtractRequestFormInfo,
  ImportExtractedDataIntoSubscriptionParams,
  PreviewExtractedFormDataModal
}
import anduin.fundsub.endpoint.common.UpdateUserTrackingParams
import anduin.fundsub.endpoint.subscriptiondoc.{GetAllSubscriptionVersionBasicInfoParams, SubscriptionVersionBasicInfo}
import anduin.id.fundsub.FundSubLpId
import anduin.id.fundsub.dataextract.FundSubDataExtractRequestId
import anduin.scalajs.pluralize.Pluralize
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.{FundSubDataExtractClient, FundSubEndpointClient}
import com.anduin.stargazer.fundsub.module.dataextract.DataExtractStatusRendererUtils
import com.anduin.stargazer.fundsub.module.investor.dashboard.v2.CellData

object DataExtractionRequestRenderer
    extends DashboardRenderer[DataExtractionColumn, DataExtractionCell](DashboardColumnRendererEnum.DataExtraction) {

  private val isCompletingExtractionVar = Var(false)

  private val completeExtractionEventBus = new EventBus[Unit]

  private val userClosedCompleteDataExtractionGuidelineCalloutOptVar: Var[Option[Boolean]] = Var(None)

  private val closeCompleteDataExtractionGuidelineEventBus = new EventBus[Unit]

  private val lpVersionsVar = Var(Seq.empty[SubscriptionVersionBasicInfo])
  private val lpVersionsSignal = lpVersionsVar.signal

  override def render(
    context: DashboardContext,
    metadata: DashboardMetadata,
    cellData: CellData[DataExtractionCell],
    onClick: Observer[Unit] = Observer.empty
  ): HtmlElement = {
    // this is intentionally put here to make sure the event bus is tied to each cell instead of the entire column
    val closeFormViewEventBus = new EventBus[FundSubDataExtractRequestId]
    div(
      cellData.cellData.dataExtractionRequestOpt.fold(emptyNode) { requestInfo =>
        div(
          closeFormViewEventBus.events.flatMapSwitch(onCloseFormView) --> Observer.empty,
          div(
            tw.flex.itemsCenter,
            div(
              tw.mr4,
              TagL(
                label = Val(DataExtractStatusRendererUtils.getTagLabelFromDashboardStatus(requestInfo.status)),
                color = Val(DataExtractStatusRendererUtils.getTagColorFromDashboardStatus(requestInfo.status))
              )()
            ),
            requestInfo.status match {
              case DataExtractionDashboardStatus.ReadyForReview
                  if requestInfo.missingRecommendedFields > 0 || requestInfo.missingRequiredFields > 0 =>
                TooltipL(
                  renderTarget = if (requestInfo.missingRequiredFields > 0) {
                    div(
                      tw.textDanger4,
                      IconL(name = Val(Icon.Glyph.Error), size = Icon.Size.Custom(12))()
                    )
                  } else {
                    div(
                      tw.textWarning4,
                      IconL(name = Val(Icon.Glyph.Warning), size = Icon.Size.Custom(12))()
                    )
                  },
                  renderContent = _.amend(
                    renderFormValidationTooltipContent(requestInfo)
                  )
                )()
              case _ => emptyNode
            }
          ),
          requestInfo.status match {
            case DataExtractionDashboardStatus.ReadyForReview =>
              ModalL(
                renderTarget = open =>
                  div(
                    tw.mt4,
                    ButtonL(
                      style = ButtonL.Style.Full(
                        color = ButtonL.Color.Primary,
                        icon = Some(Icon.Glyph.FileEye),
                        height = ButtonL.Height.Fix24
                      ),
                      onClick = open.contramap(_ => ())
                    )("Review")
                  ),
                renderContent = close =>
                  FetchDataExtractRequestFormInfo(
                    requestId = requestInfo.requestId,
                    renderChildren = (renderPropsSignal, _) =>
                      FetchDataExtractRequestForDashboard(
                        requestId = requestInfo.requestId,
                        renderChildren = dataExtractRequestRenderPropsSignal =>
                          PreviewExtractedFormDataModal(
                            requestId = requestInfo.requestId,
                            formDataOptSignal = renderPropsSignal.map(_.formInfoOpt.map(_.formData)),
                            initialGaiaStateOptSignal =
                              renderPropsSignal.map(_.formInfoOpt.map(_.extractedData.gaiaState)),
                            onCloseFormModal = close,
                            submittedDocsSignal = dataExtractRequestRenderPropsSignal.map(
                              _.dataExtractRequestOpt.map(_.dataExtractRequest.submittedDocs).getOrElse(Seq.empty)
                            ),
                            renderHeader = renderHeader(
                              _,
                              renderPropsSignal.map(_.isFetching),
                              renderPropsSignal.map(_.viewOnlyModeDataOpt),
                              close,
                              requestInfo.requestId,
                              cellData.metadata.lpRepresentativeName,
                              dataExtractRequestRenderPropsSignal.map(
                                _.dataExtractRequestOpt.map(_.userClosedCompleteDataExtractGuidelineCallout)
                              ),
                              cellData.metadata.lpId
                            ),
                            initialFormDataLastUpdateAtSignal =
                              renderPropsSignal.map(_.formInfoOpt.flatMap(_.extractedDataLastUpdatedAt)),
                            viewOnlyModeDataOptSignal = renderPropsSignal.map(_.viewOnlyModeDataOpt)
                          )()
                      )()
                  )(),
                size = ModalL.Size(width = ModalL.Width.Full, height = ModalL.Height.Full),
                isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false)),
                afterUserClose = closeFormViewEventBus.writer.contramap(_ => requestInfo.requestId)
              )()
            case _ => emptyNode
          }
        )
      }
    )
  }

  private def renderHeader(
    renderHeaderProps: PreviewExtractedFormDataModal.RenderHeaderProps,
    isFetchingSignal: Signal[Boolean],
    viewOnlyModeDataOptSignal: Signal[Option[ViewOnlyModeData]],
    closeModal: Observer[Unit],
    requestId: FundSubDataExtractRequestId,
    lpRepresentativeName: String,
    userClosedCompleteDataExtractGuidelineCalloutSignal: Signal[Option[Boolean]],
    lpId: FundSubLpId
  ) = {
    div(
      div(
        tw.flex.itemsCenter.py12.px20.shadow1.z1,
        ButtonL(
          style = ButtonL.Style.Ghost(isBusy = renderHeaderProps.isClosingAndSaving),
          onClick = renderHeaderProps.onCloseModal.contramap(_ => ())
        )(IconL(Val(Icon.Glyph.Cross))()),
        renderFormTitle(
          lpRepresentativeName,
          renderHeaderProps,
          viewOnlyModeDataOptSignal
        ),
        renderSavedBlock,
        renderActions(
          renderHeaderProps,
          isFetchingSignal,
          viewOnlyModeDataOptSignal.map(_.nonEmpty),
          closeModal,
          requestId,
          lpId
        )
      ),
      child.maybe <-- viewOnlyModeDataOptSignal
        .combineWith(userClosedCompleteDataExtractionGuidelineCalloutOptVar.signal)
        .combineWith(isFetchingSignal)
        .distinct
        .map { case (viewOnlyModeDataOpt, userClosedCompleteDataExtractionGuidelineCalloutOpt, isFetching) =>
          if (isFetching) {
            None
          } else {
            viewOnlyModeDataOpt.fold {
              Option.when(userClosedCompleteDataExtractionGuidelineCalloutOpt.contains(false)) {
                renderCompleteDataExtractionGuidelineCallout
              }
            } { viewOnlyDataOpt =>
              Some(renderViewOnlyModeCallout(viewOnlyDataOpt))
            }
          }
        },
      userClosedCompleteDataExtractGuidelineCalloutSignal --> userClosedCompleteDataExtractionGuidelineCalloutOptVar,
      closeCompleteDataExtractionGuidelineEventBus.events.flatMapSwitch { _ =>
        onCloseCompleteDataExtractionGuidelineCallout
      } --> Observer.empty,
      fetchLpVersions(lpId) --> Observer.empty
    )
  }

  private def onCloseCompleteDataExtractionGuidelineCallout: EventStream[Unit] = {
    val task = for {
      _ <- ZIO.succeed(userClosedCompleteDataExtractionGuidelineCalloutOptVar.set(Some(true)))
      _ <- FundSubEndpointClient.updateUserTracking(
        UpdateUserTrackingParams(
          closedCompleteDataExtractionGuidelineCallout = Some(true)
        )
      )
    } yield ()
    AirStreamUtils.taskToStream(task)
  }

  private def fetchLpVersions(lpId: FundSubLpId) = {
    AirStreamUtils.taskToStream {
      val task = for {
        respEither <- FundSubEndpointClient.getAllSubscriptionVersionsBasicInfo(
          GetAllSubscriptionVersionBasicInfoParams(lpId)
        )
        resp <- ZIO.fromEither(respEither)
        _ <- ZIO.attempt(lpVersionsVar.set(resp.versions))
      } yield ()
      task.catchAll { error => ZIO.attempt(s"Unable to load subscription versions. Error: ${error.getMessage}") }
    }
  }

  private def renderCompleteDataExtractionGuidelineCallout = {
    div(
      tw.flex.itemsCenter.justifyCenter.spaceX8.py8,
      tw.bgPrimary1,
      div(
        tw.textPrimary4,
        IconL(name = Val(Icon.Glyph.Info))()
      ),
      div(
        tw.textPrimary5,
        "Review and complete the extraction to ensure the data is included during export"
      ),
      TooltipL(
        renderTarget = ButtonL(
          style = ButtonL.Style.Minimal(
            color = ButtonL.Color.Primary,
            height = ButtonL.Height.Fix24,
            icon = Some(Icon.Glyph.Cross)
          ),
          onClick = closeCompleteDataExtractionGuidelineEventBus.writer.contramap(_ => ())
        )(),
        renderContent = _.amend("Don't show again")
      )()
    )
  }

  private def renderViewOnlyModeCallout(viewOnlyModeData: ViewOnlyModeData): HtmlElement = {
    div(
      tw.flex.itemsCenter.justifyCenter.spaceX8.py8.bgWarning3,
      IconL(name = Val(Icon.Glyph.Info))(),
      div(
        tw.flex.itemsCenter,
        "Extracted data is being reviewed by",
        span(tw.fontSemiBold.mx4, viewOnlyModeData.userName),
        s"(${viewOnlyModeData.userEmail}). Editing will be available when ",
        span(tw.fontSemiBold.mx4, viewOnlyModeData.userName),
        " exits the review view."
      )
    )
  }

  private def renderFormTitle(
    lpRepresentativeName: String,
    renderHeaderProps: PreviewExtractedFormDataModal.RenderHeaderProps,
    viewOnlyModeDataOptSignal: Signal[Option[ViewOnlyModeData]]
  ) = {
    div(
      tw.ml16,
      div(
        tw.flex.itemsCenter.spaceX4,
        div(
          tw.text15.leading20.fontSemiBold.flexNone,
          s"Data extraction for $lpRepresentativeName"
        ),
        TooltipL(
          renderTarget = TagL(
            color = Val(Tag.Light.Gray),
            label = Val("Draft")
          )(),
          renderContent = _.amend("Drafts are for review purpose only. Complete the extraction for an official version.")
        )(),
        child.maybe <-- viewOnlyModeDataOptSignal.map(_.nonEmpty).map {
          Option.when(_) {
            TagL(
              color = Val(Tag.Bold.Primary),
              icon = Some(Icon.Glyph.Eye),
              label = Val("View only")
            )()
          }
        }
      ),
      child.maybe <-- renderHeaderProps.formDataLastUpdateAtSignal.map(_.map { lastUpdatedAt =>
        val formattedTime =
          JsDateFormatterUtils.format(lastUpdatedAt, JsDateFormatterUtils.JsDateFormat.MonthDateYearTime3)
        div(
          tw.textGray7,
          s"Last save: $formattedTime"
        )
      })
    )
  }

  private def renderSavedBlock = {
    div(
      tw.ml16,
      TooltipL(
        renderTarget = span(
          tw.flex.itemsCenter,
          tw.textBody.textGray6,
          IconL(name = Val(Icon.Glyph.Check))(),
          span(tw.ml8.textGray7, "Saved")
        ),
        renderContent = _.amend(
          "All changes save automatically"
        ),
        position = PortalPosition.BottomCenter
      )()
    )
  }

  private def renderFormValidationTooltipContent(requestInfo: DataExtractionRequestDashboardInfo) = {
    val numberOfMissingRequired = requestInfo.missingRequiredFields
    val numberOfMissingRecommended = requestInfo.missingRecommendedFields
    div(
      if (numberOfMissingRequired > 0) {
        val fieldStr = Pluralize("field", numberOfMissingRequired, inclusive = false)
        val inputStr = Pluralize("input", numberOfMissingRequired, inclusive = false)
        p(s"$numberOfMissingRequired missing required $fieldStr or invalid $inputStr")
      } else if (numberOfMissingRecommended > 0) {
        val fieldStr = Pluralize("field", numberOfMissingRecommended, inclusive = false)
        val warningStr = Pluralize("warning", numberOfMissingRecommended, inclusive = false)
        p(s"$numberOfMissingRecommended empty recommended $fieldStr or $fieldStr with $warningStr")
      } else {
        emptyNode
      }
    )
  }

  private def renderActions(
    renderHeaderProps: PreviewExtractedFormDataModal.RenderHeaderProps,
    isFetchingSignal: Signal[Boolean],
    isViewOnlySignal: Signal[Boolean],
    closeFormModal: Observer[Unit],
    requestId: FundSubDataExtractRequestId,
    lpId: FundSubLpId
  ) = {
    div(
      tw.mlAuto.flex.itemsCenter,
      ButtonL(
        style = ButtonL.Style.Full(icon = Some(Icon.Glyph.Comparison)),
        onClick = renderHeaderProps.openFormCompareModalObserver.contramap { _ => lpId -> lpVersionsVar.now() },
        isDisabled =
          (renderHeaderProps.isDisabledOpenFormCompareModalSignal || lpVersionsSignal.map(_.isEmpty)).distinct
      )("Compare form versions"),
      DividerL(Divider.Direction.Vertical)(),
      ModalL(
        renderTitle = _ => "Complete extraction",
        renderTarget = open =>
          TooltipL(
            renderTarget = ButtonL(
              style = ButtonL.Style.Full(
                color = ButtonL.Color.Primary,
                icon = Some(Icon.Glyph.FileCheck),
                isBusy = isCompletingExtractionVar.signal
              ),
              isDisabled = isViewOnlySignal || isFetchingSignal,
              onClick = open.contramap(_ => ())
            )("Complete data extraction"),
            renderContent = _.amend("This action is disabled in view-only mode"),
            isDisabled = isViewOnlySignal.map(!_) || isFetchingSignal
          )(),
        renderContent = closeConfirmModal =>
          renderCompleteExtractionConfirmModal(
            closeConfirmModal,
            closeFormModal,
            renderHeaderProps,
            requestId
          )
      )()
    )
  }

  private def renderCompleteExtractionConfirmModal(
    onCloseConfirmModal: Observer[Unit],
    closeFormModal: Observer[Unit],
    renderHeaderProps: PreviewExtractedFormDataModal.RenderHeaderProps,
    requestId: FundSubDataExtractRequestId
  ) = {
    div(
      completeExtractionEventBus.events.flatMapSwitch { _ =>
        onCompleteExtraction(
          requestId,
          onCloseConfirmModal,
          closeFormModal
        )
      } --> Observer.empty,
      ModalBodyL(
        div(
          "We'll create a new form version if you complete the data extraction. You can compare versions afterward.",
          " The data from completed data extraction will be included in your spreadsheet during export."
        ),
        div(
          tw.mt8,
          "Are you sure you want to continue?"
        )
      ),
      ModalFooterWCancelL(cancel = onCloseConfirmModal)(
        ButtonL(
          style = ButtonL.Style.Full(
            color = ButtonL.Color.Primary,
            isBusy = (renderHeaderProps.isClosingAndSaving || isCompletingExtractionVar.signal).distinct
          ),
          onClick = Observer { _ =>
            renderHeaderProps.onSaveData.onNext(
              PreviewExtractedFormDataModal.SaveExtractedDataProps(
                onSuccess = completeExtractionEventBus.writer,
                isClosingAfterSaving = true
              )
            )
          }
        )("Complete extraction")
      )
    )
  }

  private def onCompleteExtraction(
    requestId: FundSubDataExtractRequestId,
    onCloseConfirmModal: Observer[Unit],
    closeFormModal: Observer[Unit]
  ): EventStream[Unit] = {
    val completeExtractionTask = for {
      _ <- ZIO.succeed(isCompletingExtractionVar.set(true))
      _ <- FundSubDataExtractClient
        .importExtractedDataIntoSubscription(ImportExtractedDataIntoSubscriptionParams(requestId))
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold(
            _ => {
              isCompletingExtractionVar.set(false)
              Toast.error("Failed to complete extraction, please try again")
            },
            _ => {
              isCompletingExtractionVar.set(false)
              onCloseConfirmModal.onNext(())
              closeFormModal.onNext(())
            }
          )
        )
    } yield ()

    ZIOUtils.toEventStreamUnsafe(completeExtractionTask)
  }

  private def onCloseFormView(requestId: FundSubDataExtractRequestId): EventStream[Unit] = {
    ZIOUtils.toEventStreamUnsafe {
      FundSubDataExtractClient.closeDataExtractFormView(CloseDataExtractFormViewParams(requestId)).map(_ => ())
    }
  }

}
