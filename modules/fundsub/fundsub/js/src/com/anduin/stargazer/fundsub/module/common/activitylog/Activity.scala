// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.common.activitylog

import java.time.{Instant, ZonedDateTime}

import com.raquo.laminar.api.L.*
import design.anduin.components.badge.Badge
import design.anduin.components.badge.laminar.DotL
import design.anduin.style
import design.anduin.style.tw.*

import anduin.copy.Bundle.I18N
import anduin.flow.FundSubFlowTermUtils.FlowTermCollection
import anduin.fundsub.activitylog.FaActivityLogHelper
import anduin.fundsub.activitylog.FaActivityLogHelper.{getParticipantName, renderFundAdminActivityDescription}
import anduin.fundsub.endpoint.admin.LpBasicInfo
import anduin.fundsub.endpoint.lp.NameEmailInfo
import anduin.id.fundsub.FundSubLpId
import anduin.model.common.user.UserId
import anduin.model.id.TeamId
import anduin.protobuf.activitylog.ActivityModel
import anduin.protobuf.activitylog.fundsub.admin.*
import anduin.protobuf.activitylog.fundsub.admin.FundAdminActivity.Empty
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.fundsub.module.common.activitylog.laminar.ActivityRelatedFileInfoRenderer
import com.anduin.stargazer.fundsub.module.common.activitylog.render.{BasicActivityRenderer, CustomActivityRenderer}
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

private[fundsub] final case class Activity(
  actorName: String,
  activityDescription: Node,
  activityDetail: ActivityDetail,
  seen: Boolean,
  relatedFileInfo: ActivityRelatedFileInfo = NoFile,
  at: Option[Instant] = None,
  timeString: String = "" // eg. 08:00 AM
)

object Activity {

  def fromActivityModel(
    currentUserId: UserId,
    activityModel: ActivityModel,
    userInfoMap: Map[UserId, NameEmailInfo],
    gpTeamNameMap: Map[TeamId, String],
    lpInfoMap: Map[FundSubLpId, LpBasicInfo],
    copyConfig: I18N,
    today: ZonedDateTime,
    flowTermCollection: FlowTermCollection
  ): Activity = {
    val faActivity = activityModel.detail.flatMap(_.activity.fundAdminActivity).getOrElse(Empty)
    val relatedLpIdOpt = faActivity match {
      case _: AdminInvited | _: AdminJoined | _: AdminRemoved | _: OfflineOrderAdded |
          _: SubscriptionDocumentReviewEnabled | _: SubscriptionDocumentReviewDisabled |
          _: SubscriptionDocumentReviewerAdded | _: SubscriptionDocumentReviewerRemoved | _: BatchLpInvited |
          _: BatchOfflineOrderAdded | _: SentEmail | _: NewAdditionalDocumentUploadReport | _: NewLpReport |
          _: NewFormCommentReport | _: NewCommentAssignmentReport | _: SignatureRequestReassign | _: GroupMembersMoved |
          _: GroupCreated | _: GroupRenamed | _: GroupPermissionUpdated | _: GroupVisibilityUpdated | _: GroupDeleted |
          _: GroupAccessToViewUpdated | _: ViewRenamed | _: ViewCreated | _: DashboardColumnsUpdated | _: ViewDeleted |
          _: PrivateViewShared | _: InvestorGroupCreated | _: InvestorGroupRenamed | _: InvestorGroupDeleted |
          _: InvestorAssignedToGroup | _: InvestorMovedToAnotherGroup | _: InvestorUnassignedFromGroup |
          _: InvestorGroupAccessibilityGranted | _: InvestorGroupAccessibilityWithdrawn |
          _: SignedSubscriptionDocReviewConfigUpdated | _: UnsignedSubscriptionDocReviewConfigUpdated |
          _: SignedSubscriptionDocumentMarkedAsApproved | _: UnsignedSubscriptionDocumentMarkedAsApproved |
          _: CreateClose | _: UpdateClose | _: DeleteClose | _: EmailTemplateCreated | _: EmailTemplateUpdated |
          _: EmailTemplateDeleted | _: EmailTemplateRenamed | _: EmailTemplateSetAsDefault | _: AdvisorEntityJoined |
          _: AdvisorEntityNameUpdated | _: AdvisorJoined | _: AdvisorInvited | _: ResendAdvisorInvitation |
          _: RevokeAdvisorInvitation | _: DisableAdvisorEntityCreateNewSubscription |
          _: EnableAdvisorEntityCreateNewSubscription =>
        None
      case a: ConvertOfflineOrderToNormal          => Option(a.lpId)
      case a: AdminAccessedSubscription            => Option(a.lpId)
      case a: LpInvited                            => Option(a.lpId)
      case a: LpJoined                             => Option(a.lpId)
      case a: LpInvestedInAdditionalFund           => Option(a.originalLpId)
      case a: LpJoinedViaInvitationLink            => Option(a.lpId)
      case a: LpRemoved                            => Option(a.lpId)
      case a: LpRestored                           => Option(a.lpId)
      case a: CollaboratorAdded                    => Option(a.lpId)
      case a: CollaboratorJoined                   => Option(a.lpId)
      case a: CollaboratorRemoved                  => Option(a.lpId)
      case a: CollaboratorPromoted                 => Option(a.lpId)
      case a: UploadedExecutedDocument             => Option(a.lpId)
      case a: UploadedDocOnBehalf                  => Option(a.lpId)
      case a: SentExecutedDocument                 => Option(a.lpId)
      case a: UpdatedExecutedDocument              => Option(a.lpId)
      case a: SignedExecutedDocument               => Option(a.lpId)
      case a: SubmittedSubscriptionPackage         => Option(a.lpId)
      case a: UndidSubscriptionPackage             => Option(a.lpId)
      case a: MarkedSubscriptionAsComplete         => Option(a.lpId)
      case a: RequestChange                        => Option(a.lpId)
      case a: SubscriptionDocumentMarkedAsReviewed => Option(a.lpId)
      case a: SubscriptionDocumentReviewSkipped    => Option(a.lpId)
      case a: RemovedUploadedExecutedDocument      => Option(a.lpId)
      case a: RemindLpToSignAgain                  => Option(a.lpId)
      case a: SentReminderToUploadSupportingDoc    => Option(a.lpId)
      case a: LpFilledForm                         => Option(a.lpId)
      case _: ManualOrderActivatedByInvestor       => None
      case _: AmlKycReviewConfigUpdated            => None
      case a: NewFormCommentNotificationToInvestor => Option(a.lpId)
      case a: AmendmentAdded                       => Option(a.lpId)
      case a: AmendmentEdited                      => Option(a.lpId)
      case a: AmendmentRemoved                     => Option(a.lpId)
      case a: UpdateInvestorsValues                => Option(a.lpId)
      case a: CustomLpIdUpdated                    => Option(a.lpId)
      case _: CustomFundIdUpdated                  => None
      case a: CommentActivity                      => Option(a.lpId)
      case a: AnchorPointActivity                  => Option(a.lpId)
      case _: CommentExported                      => None
      case _: CommentSettingUpdateActivity         => None
      case a: DataExtractionStarted                => Some(a.requestInfo.lpId)
      case _: ExtractedDataReadyForReview          => None
      case a: ExtractedDataMarkedAsComplete        => Some(a.requestInfo.lpId)
      case a: ExtractedDataEdited                  => Some(a.requestInfo.lpId)
      case a: MoveLpToNewClose                     => Some(a.lpId)
      case _: SelfServiceExportTemplateCreated | _: SelfServiceExportTemplateUpdated |
          _: SelfServiceExportTemplateRenamed | _: SelfServiceExportTemplateDeleted | _: InvestorDataExported =>
        None
      case _: SignatureDateFormatUpdated                                            => None
      case a: RiaOrderCreated                                                       => Some(a.lpId)
      case a: ConvertToRiaOrder                                                     => Some(a.lpId)
      case a: UnlinkRiaOrder                                                        => Some(a.lpId)
      case a: SideLetterVersionCreated                                              => Some(a.lpId)
      case a: SideLetterFilesUploaded                                               => Some(a.lpId)
      case a: SideLetterFilesRemoved                                                => Some(a.lpId)
      case a: MarkSideLetterAsAgreed                                                => Some(a.lpId)
      case a: MarkSideLetterCompleted                                               => Some(a.lpId)
      case _: CustomColumnCreated | _: CustomColumnDeleted | _: CustomColumnRenamed => None
      case a: CustomColumnValueUpdated                                              => Some(a.lpId)
      case a: TagListUpdated                                                        => Some(a.lpId)
      case _: AdminUpdateAllowFormEditPostSigning                                   => None

      case FundAdminActivity.Empty => None
    }

    val actorName = activityModel.actorOpt.fold[String]("") { actor =>
      getParticipantName(
        actor,
        userInfoMap,
        relatedLpIdOpt,
        lpInfoMap
      )
    }

    val date = activityModel.at.getOrElse(today.toInstant).atZone(today.getZone)
    val timeString = DateTimeUtils.formatZonedDateTime(
      date,
      DateTimeUtils.Time12hourFormatter
    )

    val seen = activityModel.seenBy.contains(currentUserId) || activityModel.actorOpt.contains(currentUserId)

    val activityDescription = renderFundAdminActivityDescription(
      faActivity,
      userInfoMap,
      lpInfoMap,
      copyConfig,
      flowTermCollection
    )

    val activityDetail = getActivityDetail(
      faActivity,
      userInfoMap,
      lpInfoMap,
      gpTeamNameMap,
      actorName,
      activityDescription
    )

    Activity(
      actorName = actorName,
      activityDescription = activityDescription,
      timeString = timeString,
      seen = seen,
      activityDetail = activityDetail,
      at = activityModel.at
    )
  }

  def buildActivityFromFundAdminActivity(
    actorName: String,
    fundAdminActivity: FundAdminActivity,
    userInfoMap: Map[UserId, NameEmailInfo],
    lpInfoMap: Map[FundSubLpId, LpBasicInfo],
    gpTeamNameMap: Map[TeamId, String],
    copyConfig: I18N,
    flowTermCollection: FlowTermCollection
  ): Activity = {
    val activityDescription = renderFundAdminActivityDescription(
      fundAdminActivity,
      userInfoMap,
      lpInfoMap,
      copyConfig,
      flowTermCollection
    )

    val activityDetail = getActivityDetail(
      fundAdminActivity,
      userInfoMap,
      lpInfoMap,
      gpTeamNameMap,
      actorName,
      activityDescription
    )

    Activity(
      actorName = actorName,
      activityDescription = activityDescription,
      activityDetail = activityDetail,
      seen = true
    )
  }

  def renderActivityContent(activity: Activity): HtmlElement = {
    div(
      tw.flex,
      activity.activityDetail match {
        case basicActivity: BasicActivity =>
          BasicActivityRenderer(
            actorName = basicActivity.actorName,
            description = basicActivity.description
          )()
        case activityRelatedFileInfo: ActivityRelatedFileInfo =>
          ActivityRelatedFileInfoRenderer(
            fileInfo = activityRelatedFileInfo,
            actorName = activity.actorName
          )()
        case customActivity: CustomActivity =>
          CustomActivityRenderer(customActivity = customActivity, actorName = activity.actorName)()
      },
      Option.unless(activity.seen) {
        div(
          tw.flex1.flex.justifyEnd.ml8.mt6,
          DotL(color = Badge.Color.Primary)()
        )
      }
    )
  }

  def getActivityDetail(
    activity: FundAdminActivity,
    userInfoMap: Map[UserId, NameEmailInfo],
    lpInfoMap: Map[FundSubLpId, LpBasicInfo],
    gpTeamNameMap: Map[TeamId, String],
    actorName: String,
    description: String
  ): ActivityDetail = {
    val activityDetail = activity match {
      case submitted: SubmittedSubscriptionPackage =>
        LpSubmittedSubscriptionDocuments(submitted.files.toList)
      case sentExecutedDocument: SentExecutedDocument =>
        FundAdminDistributedCounterSignedDocuments(
          sentExecutedDocument.files.toList,
          getParticipantName(
            sentExecutedDocument.investorUserId,
            userInfoMap,
            Option(sentExecutedDocument.lpId),
            lpInfoMap
          )
        )
      case updatedExecutedDocument: UpdatedExecutedDocument =>
        FundAdminUpdatedCounterSignedDocuments(
          updatedExecutedDocument.files.toList,
          getParticipantName(
            updatedExecutedDocument.investorUserId,
            userInfoMap,
            Option(updatedExecutedDocument.lpId),
            lpInfoMap
          )
        )
      case uploadedExecutedDocument: UploadedExecutedDocument =>
        FundAdminUploadedCounterSignedDocuments(
          uploadedExecutedDocument.files.toList,
          getParticipantName(
            uploadedExecutedDocument.investorUserId,
            userInfoMap,
            Option(uploadedExecutedDocument.lpId),
            lpInfoMap
          )
        )
      case uploadedDocOnBehalf: UploadedDocOnBehalf =>
        val onBehalfOf =
          getParticipantName(
            uploadedDocOnBehalf.investorUserId,
            userInfoMap,
            Option(uploadedDocOnBehalf.lpId),
            lpInfoMap
          )
        FundAdminUploadedDocumentsOnBehalf(
          files = uploadedDocOnBehalf.files.toList,
          onBehalfOf = onBehalfOf
        )
      case removedUploadedExecutedDocument: RemovedUploadedExecutedDocument =>
        FundAdminRemovedCountersignedDocuments(
          files = removedUploadedExecutedDocument.files.toList,
          lpName = getParticipantName(
            removedUploadedExecutedDocument.investorUserId,
            userInfoMap,
            Option(removedUploadedExecutedDocument.lpId),
            lpInfoMap
          )
        )

      case batchLpInvited: BatchLpInvited =>
        FundAdminBatchInviteLp(
          batchLpInvited.batchInvitationId,
          batchLpInvited.successLp,
          batchLpInvited.failedLp,
          batchLpInvited.cancelledLp,
          isOfflineOrder = false,
          actorName = actorName
        )

      case batchOfflineOrderAdded: BatchOfflineOrderAdded =>
        FundAdminBatchInviteLp(
          batchOfflineOrderAdded.batchInvitationId,
          batchOfflineOrderAdded.successLp,
          batchOfflineOrderAdded.failedLp,
          batchOfflineOrderAdded.cancelledLp,
          isOfflineOrder = true,
          actorName = actorName
        )

      case signedCountersignRequest: SignedExecutedDocument =>
        FinishSignExecutedDocument(
          signedCountersignRequest.files.toList,
          getParticipantName(
            signedCountersignRequest.investorUserId,
            userInfoMap,
            Some(signedCountersignRequest.lpId),
            lpInfoMap
          ),
          signedCountersignRequest.signerIds.size
        )

      case newFormCommentNotificationToInvestor: NewFormCommentNotificationToInvestor =>
        FormCommentNotificationToInvestorActivity(
          getParticipantName(
            newFormCommentNotificationToInvestor.investorUserId,
            userInfoMap,
            Some(newFormCommentNotificationToInvestor.lpId),
            lpInfoMap
          )
        )
      case amendmentAdded: AmendmentAdded =>
        amendmentAdded.amendment.fold[ActivityDetail] {
          BasicActivity(
            actorName = actorName,
            description = description
          )
        } { amendment =>
          val lpName = getParticipantName(
            amendmentAdded.investorUserId,
            userInfoMap,
            Some(amendmentAdded.lpId),
            lpInfoMap
          )
          AddedAmendment(
            amendmentDetail = amendment,
            lpName = lpName
          )
        }
      case amendmentEdited: AmendmentEdited =>
        amendmentEdited.amendment.fold[ActivityDetail] {
          BasicActivity(
            actorName = actorName,
            description = description
          )
        } { amendment =>
          val lpName = getParticipantName(
            amendmentEdited.investorUserId,
            userInfoMap,
            Some(amendmentEdited.lpId),
            lpInfoMap
          )
          EditedAmendment(
            amendmentDetail = amendment,
            lpName = lpName
          )
        }

      case amendmentRemoved: AmendmentRemoved =>
        BasicActivity(
          actorName = actorName,
          s"removed the amendment from ${getParticipantName(
              amendmentRemoved.investorUserId,
              userInfoMap,
              Some(amendmentRemoved.lpId),
              lpInfoMap
            )}'s ${FundSubCopyUtils.getFlowTerm.termInDocumentContext}"
        )

      case a: UnsignedSubscriptionDocumentMarkedAsApproved =>
        val lpName = getParticipantName(
          a.investorUserId,
          userInfoMap,
          Some(a.lpId),
          lpInfoMap
        )
        SoftReviewApproved(
          actorName: String,
          lpName: String
        )

      case a: CommentActivity =>
        val lpName = lpInfoMap
          .get(a.lpId)
          .map(_.firmName)
          .filter(_.nonEmpty)
          .orElse(
            lpInfoMap
              .get(a.lpId)
              .map(_.mainLp)
              .map { userInfo =>
                s"${userInfo.firstName} ${userInfo.lastName}".trim
              }
          )
          .getOrElse("investor")
        CommentActivityDetail(
          actorName = actorName,
          lpName = lpName,
          a
        )
      case a: AnchorPointActivity =>
        val lpName = lpInfoMap
          .get(a.lpId)
          .map(_.firmName)
          .filter(_.nonEmpty)
          .orElse(
            lpInfoMap
              .get(a.lpId)
              .map(_.mainLp)
              .map { userInfo =>
                s"${userInfo.firstName} ${userInfo.lastName}".trim
              }
          )
          .getOrElse("investor")
        val assigneeGPName = a.assignedGpUserIdOpt
          .flatMap(gpUserId => userInfoMap.get(gpUserId).map(_.fullName))
          .getOrElse(a.assignedGpTeamIdOpt.flatMap(gpTeamId => gpTeamNameMap.get(gpTeamId)).getOrElse("GP entity"))

        AnchorPointActivityDetail(
          actorName = actorName,
          lpName = lpName,
          a,
          assigneeGPName = assigneeGPName
        )

      case a: CommentSettingUpdateActivity =>
        BasicActivity(
          actorName = actorName,
          description = span(
            if (a.action.isDisableLpResolvingComment) {
              "disabled"
            } else {
              "enabled"
            },
            span(
              tw.ml4.fontSemiBold,
              "Allow investors to mark comment threads as resolved"
            )
          )
        )
      case dataExtractionStarted: DataExtractionStarted =>
        DataExtractionStartedDetail(dataExtractionStarted.submittedFiles.toList)
      case selfServiceExportTemplateCreated: SelfServiceExportTemplateCreated =>
        BasicActivity(
          actorName = actorName,
          description = span(
            "created ",
            span(
              tw.fontSemiBold,
              selfServiceExportTemplateCreated.templateName
            )
          )
        )
      case selfServiceExportTemplateRenamed: SelfServiceExportTemplateRenamed =>
        BasicActivity(
          actorName = actorName,
          description = span(
            "renamed ",
            span(
              tw.fontSemiBold,
              selfServiceExportTemplateRenamed.oldTemplateName
            ),
            " to ",
            span(
              tw.fontSemiBold,
              selfServiceExportTemplateRenamed.newTemplateName
            )
          )
        )
      case selfServiceExportTemplateUpdated: SelfServiceExportTemplateUpdated =>
        BasicActivity(
          actorName = actorName,
          description = span(
            "updated ",
            span(
              tw.fontSemiBold,
              selfServiceExportTemplateUpdated.templateName
            )
          )
        )

      case investorDataExported: InvestorDataExported =>
        BasicActivity(
          actorName = actorName,
          description = span(
            "exported investor data using ",
            span(
              tw.fontSemiBold,
              investorDataExported.templateName
            )
          )
        )

      case selfServiceExportTemplateDeleted: SelfServiceExportTemplateDeleted =>
        BasicActivity(
          actorName = actorName,
          description = span(
            "deleted ",
            span(
              tw.fontSemiBold,
              selfServiceExportTemplateDeleted.templateName
            )
          )
        )

      case emailTemplateCreated: EmailTemplateCreated =>
        BasicActivity(
          actorName = actorName,
          description = span(
            s"created ${FaActivityLogHelper.emailTemplateName(emailTemplateCreated.emailEvent, FundSubCopyUtils.getFlowTerm)} ",
            span(
              tw.fontSemiBold,
              emailTemplateCreated.templateName
            )
          )
        )
      case emailTemplateRenamed: EmailTemplateRenamed =>
        BasicActivity(
          actorName = actorName,
          description = span(
            s"renamed ${FaActivityLogHelper.emailTemplateName(emailTemplateRenamed.emailEvent, FundSubCopyUtils.getFlowTerm)} ",
            span(
              tw.fontSemiBold,
              emailTemplateRenamed.oldTemplateName
            ),
            " to ",
            span(
              tw.fontSemiBold,
              emailTemplateRenamed.newTemplateName
            )
          )
        )
      case emailTemplateUpdated: EmailTemplateUpdated =>
        BasicActivity(
          actorName = actorName,
          description = span(
            s"updated ${FaActivityLogHelper.emailTemplateName(emailTemplateUpdated.emailEvent, FundSubCopyUtils.getFlowTerm)} ",
            span(
              tw.fontSemiBold,
              emailTemplateUpdated.templateName
            )
          )
        )

      case emailTemplateSetAsDefault: EmailTemplateSetAsDefault =>
        BasicActivity(
          actorName = actorName,
          description = span(
            s"set ${FaActivityLogHelper.emailTemplateName(emailTemplateSetAsDefault.emailEvent, FundSubCopyUtils.getFlowTerm)} ",
            span(
              tw.fontSemiBold,
              emailTemplateSetAsDefault.templateName
            ),
            " as default"
          )
        )

      case emailTemplateDeleted: EmailTemplateDeleted =>
        BasicActivity(
          actorName = actorName,
          description = span(
            s"deleted ${FaActivityLogHelper.emailTemplateName(emailTemplateDeleted.emailEvent, FundSubCopyUtils.getFlowTerm)} ",
            span(
              tw.fontSemiBold,
              emailTemplateDeleted.templateName
            )
          )
        )

      case sideLetterVersionCreated: SideLetterVersionCreated =>
        SideLetterNewVersionDetail(sideLetterVersionCreated.files.toList, sideLetterVersionCreated.version)

      case sideLetterFilesUploaded: SideLetterFilesUploaded =>
        SideLetterFilesUploadDetail(sideLetterFilesUploaded.files.toList, sideLetterFilesUploaded.version)

      case sideLetterFilesRemoved: SideLetterFilesRemoved =>
        SideLetterFilesRemoveDetail(sideLetterFilesRemoved.files.toList, sideLetterFilesRemoved.version)
      case activityModel: AdminUpdateAllowFormEditPostSigning =>
        AdminUpdateAllowFormEditPostSigningInfo(activityModel.isEnabled)

      case _: FundAdminActivity =>
        BasicActivity(
          actorName = actorName,
          description = description
        )
    }

    activityDetail match {
      case activityRelatedFileInfo: ActivityRelatedFileInfo if activityRelatedFileInfo.files.isEmpty =>
        BasicActivity(
          actorName = actorName,
          description = description
        )
      case _ => activityDetail
    }
  }

}
