// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.common.activitylog.render

import com.raquo.laminar.api.L.*
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*

import anduin.fundsub.signature.AllowFormEditPostSigningUtils
import com.anduin.stargazer.fundsub.module.common.activitylog.*
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

private[activitylog] final case class CustomActivityRenderer(
  customActivity: CustomActivity,
  actorName: String
) {

  def apply(): HtmlElement = {
    customActivity match {
      case batchInviteLp: FundAdminBatchInviteLp =>
        BatchInviteLpActivityRenderer(batchInviteLp)()
      case formCommentNotificationToInvestor: FormCommentNotificationToInvestorActivity =>
        renderFormCommentNotificationToInvestor(formCommentNotificationToInvestor.lpName)

      case softReviewApproved: SoftReviewApproved =>
        renderSoftReviewApproved(
          actorName = softReviewApproved.actorName,
          lpName = softReviewApproved.lpName
        )
      case commentActivityDetail: CommentActivityDetail =>
        CommentActivityRenderer(
          actorName = commentActivityDetail.actorName,
          firmName = commentActivityDetail.lpName,
          commentActivityDetail.activity
        )()
      case anchorPointActivityDetail: AnchorPointActivityDetail =>
        AnchorPointActivityRenderer(
          actorName = anchorPointActivityDetail.actorName,
          firmName = anchorPointActivityDetail.lpName,
          anchorPointActivityDetail.activity,
          assigneeGPName = anchorPointActivityDetail.assigneeGPName
        )()
      case commentSentLpActivityRenderer: CommentSentLpActivityDetail =>
        CommentSentLpActivityRenderer(
          actorName = commentSentLpActivityRenderer.actorName,
          recipientNames = commentSentLpActivityRenderer.recipientNames
        )()
      case reviewedDocDetail: FundAdminReviewedDocDetail =>
        AdminReviewedDocRenderer(
          actorName = actorName,
          docTypes = reviewedDocDetail.docTypes,
          action = reviewedDocDetail.action
        )()
      case actionOnSupportingDocDetail: ActionOnSupportingDocDetail =>
        ActionOnSupportingDocRenderer(
          actorName = actorName,
          docTypes = actionOnSupportingDocDetail.docTypes,
          action = actionOnSupportingDocDetail.action
        )()
      case activityInfo: AdminUpdateAllowFormEditPostSigningInfo =>
        renderAdminUpdateAllowFormEditPostSigning(actorName, activityInfo)
    }
  }

  private def renderFormCommentNotificationToInvestor(lpName: String) = {
    span(s"New comments on ${FundSubCopyUtils.getFlowTerm.termInDocumentContext} is sent to $lpName")
  }

  private def renderSoftReviewApproved(
    actorName: String,
    lpName: String
  ) = {
    div(
      div(
        tw.text13.leading20.flexFill,
        Option.when(actorName.nonEmpty) {
          span(
            ComponentUtils.testIdL(BasicActivityRenderer, "ActorName"),
            wordBreak := "break-all",
            tw.fontSemiBold,
            actorName
          )
        },
        span(
          ComponentUtils.testIdL(BasicActivityRenderer, "Description"),
          tw.ml4,
          s"marked unsigned ${FundSubCopyUtils.getFlowTerm.termInDocumentContext} from $lpName as approved"
        )
      )
    )
  }

  private def renderAdminUpdateAllowFormEditPostSigning(
    actorName: String,
    activityInfo: AdminUpdateAllowFormEditPostSigningInfo
  ) =
    span(
      actorName,
      span(
        tw.ml4,
        if (activityInfo.isEnabled) {
          "enabled"
        } else {
          "disabled"
        }
      ),
      span(
        tw.ml4.fontSemiBold,
        AllowFormEditPostSigningUtils.FundSettingFeatureLabel
      )
    )

}
