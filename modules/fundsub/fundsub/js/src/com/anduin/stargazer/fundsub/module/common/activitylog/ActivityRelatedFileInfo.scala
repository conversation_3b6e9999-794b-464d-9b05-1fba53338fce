// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.common.activitylog

import com.raquo.laminar.api.L.*

import anduin.id.fundsub.FundSubBatchInvitationId
import anduin.model.id.FileId
import anduin.protobuf.activitylog.fundsub.admin.{AmendmentDetail, AnchorPointActivity, CommentActivity}
import anduin.protobuf.fundsub.activitylog.lp.{ReviewActionLog, SupportingDocAction, SupportingDocsGroup}
import anduin.protobuf.fundsub.{FundSubDocType, FundSubFile}

private[activitylog] sealed trait ActivityDetail derives CanEqual

private[activitylog] final case class BasicActivity(
  actorName: String,
  description: Node
) extends ActivityDetail

private[activitylog] sealed trait ActivityRelatedFileInfo extends ActivityDetail {
  def files: List[FundSubFile]
}

private[activitylog] sealed trait CustomActivity extends ActivityDetail

private[activitylog] final case class LpSubmittedSubscriptionDocuments(
  files: List[FundSubFile]
) extends ActivityRelatedFileInfo

private[activitylog] final case class LpSubmittedSupportingDocuments(
  supportingDocsGroups: List[SupportingDocsGroup]
) extends ActivityRelatedFileInfo {

  override def files: List[FundSubFile] = {
    supportingDocsGroups.flatMap(_.fileIds).map { fileId =>
      FundSubFile(
        fileId
      )
    }
  }

}

private[activitylog] final case class FundAdminUpdatedCounterSignedDocuments(
  files: List[FundSubFile],
  lpName: String
) extends ActivityRelatedFileInfo

private[activitylog] final case class FundAdminDistributedCounterSignedDocuments(
  files: List[FundSubFile],
  lpName: String
) extends ActivityRelatedFileInfo

private[activitylog] final case class FundAdminUploadedCounterSignedDocuments(
  files: List[FundSubFile],
  lpName: String
) extends ActivityRelatedFileInfo

private[activitylog] final case class FundAdminUploadedDocumentsOnBehalf(
  files: List[FundSubFile],
  onBehalfOf: String
) extends ActivityRelatedFileInfo

private[activitylog] final case class FundAdminRemovedCountersignedDocuments(
  files: List[FundSubFile],
  lpName: String
) extends ActivityRelatedFileInfo

private[activitylog] case object NoFile extends ActivityRelatedFileInfo {
  override def files: List[FundSubFile] = List.empty
}

private[activitylog] final case class FundAdminRequestedSignatureOnDocuments(
  files: List[FundSubFile],
  signers: List[String]
) extends ActivityRelatedFileInfo

private[activitylog] final case class FundAdminCanceledSignatureOnSupportingDocRequest(
  files: List[FundSubFile],
  signers: List[String]
) extends ActivityRelatedFileInfo

private[activitylog] final case class FundAdminRemovedSupportingDoc(
  files: List[FundSubFile],
  name: String
) extends ActivityRelatedFileInfo

private[activitylog] final case class FundAdminAddedReferenceDoc(
  files: List[FundSubFile]
) extends ActivityRelatedFileInfo

private[activitylog] final case class FundAdminRemovedReferenceDoc(
  files: List[FundSubFile]
) extends ActivityRelatedFileInfo

private[activitylog] final case class RemovedSupportingDocs(
  files: List[FundSubFile]
) extends ActivityRelatedFileInfo

private[activitylog] final case class AddedAmendment(
  amendmentDetail: AmendmentDetail,
  lpName: String
) extends ActivityRelatedFileInfo {

  override val files: List[FundSubFile] = List(
    FundSubFile(
      fileId = amendmentDetail.amendmentFileId,
      docType = FundSubDocType.Others
    )
  )

}

private[activitylog] final case class EditedAmendment(
  amendmentDetail: AmendmentDetail,
  lpName: String
) extends ActivityRelatedFileInfo {

  override val files: List[FundSubFile] = List(
    FundSubFile(
      fileId = amendmentDetail.amendmentFileId,
      docType = FundSubDocType.Others
    )
  )

}

private[activitylog] final case class FundAdminBatchInviteLp(
  batchInvitationId: FundSubBatchInvitationId,
  successLp: Int,
  failedLp: Int,
  cancelledLp: Int,
  isOfflineOrder: Boolean,
  actorName: String
) extends CustomActivity {
  lazy val totalLp: Int = successLp + failedLp + cancelledLp
}

private[activitylog] final case class FinishSignExecutedDocument(
  files: List[FundSubFile],
  lpName: String,
  totalSignersCnt: Int
) extends ActivityRelatedFileInfo

private[activitylog] final case class FormCommentNotificationToInvestorActivity(
  lpName: String
) extends CustomActivity

private[activitylog] final case class SoftReviewApproved(
  actorName: String,
  lpName: String
) extends CustomActivity

private[activitylog] final case class CommentActivityDetail(
  actorName: String,
  lpName: String,
  activity: CommentActivity
) extends CustomActivity

private[activitylog] final case class CommentSentLpActivityDetail(
  actorName: String,
  recipientNames: Seq[String]
) extends CustomActivity

private[activitylog] final case class FundAdminReviewedDocDetail(
  docTypes: Seq[String],
  action: ReviewActionLog
) extends CustomActivity

private[activitylog] final case class ActionOnSupportingDocDetail(
  docTypes: Seq[String],
  action: SupportingDocAction
) extends CustomActivity

private[activitylog] final case class AnchorPointActivityDetail(
  actorName: String,
  lpName: String,
  activity: AnchorPointActivity,
  assigneeGPName: String = ""
) extends CustomActivity

private[activitylog] final case class DataExtractionStartedDetail(
  submittedFiles: List[FileId]
) extends ActivityRelatedFileInfo {

  override def files: List[FundSubFile] = submittedFiles.map { fileId =>
    FundSubFile(
      fileId = fileId,
      docType = FundSubDocType.Others
    )
  }

}

private[activitylog] final case class SideLetterNewVersionDetail(
  fileIds: List[FileId],
  version: Int
) extends ActivityRelatedFileInfo {

  override def files: List[FundSubFile] = fileIds.map { fileId =>
    FundSubFile(
      fileId = fileId,
      docType = FundSubDocType.Others
    )
  }

}

private[activitylog] final case class SideLetterFilesUploadDetail(
  fileIds: List[FileId],
  version: Int
) extends ActivityRelatedFileInfo {

  override def files: List[FundSubFile] = fileIds.map { fileId =>
    FundSubFile(
      fileId = fileId,
      docType = FundSubDocType.Others
    )
  }

}

private[activitylog] final case class SideLetterFilesRemoveDetail(
  fileIds: List[FileId],
  version: Int
) extends ActivityRelatedFileInfo {

  override def files: List[FundSubFile] = fileIds.map { fileId =>
    FundSubFile(
      fileId = fileId,
      docType = FundSubDocType.Others
    )
  }

}

private[activitylog] final case class CreateOneEnvelopeRequestActivityInfo(
  subscriptionFileIds: List[FileId],
  supportingFormFiles: List[(String, List[FileId])],
  uploadedFileIds: List[FileId],
  signersText: String
) extends ActivityRelatedFileInfo {

  override def files: List[FundSubFile] =
    (subscriptionFileIds ++ supportingFormFiles.flatMap(_._2) ++ uploadedFileIds).map { fileId =>
      FundSubFile(
        fileId = fileId,
        docType = FundSubDocType.Others
      )
    }

}

private[activitylog] final case class SignedOneEnvelopeRequestActivityInfo(
  fileIds: List[FileId]
) extends ActivityRelatedFileInfo {

  override def files: List[FundSubFile] =
    fileIds.map { fileId =>
      FundSubFile(
        fileId = fileId,
        docType = FundSubDocType.Others
      )
    }

}

private[activitylog] final case class AdminUpdateAllowFormEditPostSigningInfo(
  isEnabled: Boolean
) extends CustomActivity
