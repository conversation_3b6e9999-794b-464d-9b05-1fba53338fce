// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.formcomment

import anduin.forms.Form
import anduin.forms.engine.GaiaState
import anduin.id.fundsub.FundSubLpId
import anduin.id.issuetracker.IssueId
import anduin.model.common.user.{UserId, UserInfo}
import com.anduin.stargazer.dynamicform.core.FormProcessor

import java.time.Instant

case class CommentModel(
  isResolved: <PERSON><PERSON><PERSON>,
  creatorIdOpt: Option[UserId],
  creatorInfoOpt: Option[UserInfo],
  createdAtOpt: Option[Instant],
  content: String,
  isRootComment: Boolean,
  isNewContent: Boolean,
  remainingNewContentCnt: Int,
  totalNumComment: Int,
  lastEditedAt: Option[Instant],
  threadId: IssueId,
  lpId: FundSubLpId,
  commentId: String,
  hasMentions: Boolean
)

object CommentModel {

  case class DynamicFormDataForCommenting(
    formProcessor: FormProcessor
  )

  case class GaiaFormDataForCommenting(
    form: Form,
    gaiaState: GaiaState
  )

  type FormDataForCommenting = Either[DynamicFormDataForCommenting, GaiaFormDataForCommenting]
}
