// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.dashboard.lp.autoprefill.funddata

import com.raquo.laminar.api.L.*
import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.laminar.InitialAvatarL
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.laminar.PortalWrapperL
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.laminar.{CircleIndicatorL, SkeletonL}
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.CssVar
import design.anduin.style.tw.*
import design.anduin.table.laminar.{SelectionColumnL, TableL}
import io.circe.Codec
import org.scalajs.dom.{HTMLElement, KeyboardEvent, document}
import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.fundsub.endpoint.funddataintegration.{GetFirmFilterItemsResp, InvestmentEntityInfo, SupportingDocInfo}
import anduin.fundsub.endpoint.lp.{DynamicFormInfo, GaiaFormModel}
import anduin.id.funddata.FundDataInvestmentEntityId
import anduin.scalajs.pluralize.Pluralize
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils
import com.anduin.stargazer.fundsub.module.investor.dashboard.lp.autoprefill.funddata.ImportFromFundDataModalBody.{
  CustomContactInfo,
  InvestmentEntityReviewSetting,
  UpdateInvestmentEntityReviewSettingFunc,
  UpdateReviewedInvestmentEntityIdsFunc
}
import design.anduin.components.tag.TagColor
import design.anduin.components.tag.laminar.TagL

private final case class ReviewImportStep(
  investorInfosSignal: Signal[Map[FundDataInvestmentEntityId, InvestmentEntityInfo]],
  investmentEntityIdsSignal: Signal[Seq[FundDataInvestmentEntityId]],
  reviewedInvestmentEntityIdsSignal: Signal[Set[FundDataInvestmentEntityId]],
  onUpdateReviewedInvestmentEntityIds: Observer[UpdateReviewedInvestmentEntityIdsFunc],
  investmentEntityReviewSettingMapSignal: Signal[Map[FundDataInvestmentEntityId, InvestmentEntityReviewSetting]],
  onUpdateInvestmentEntityReviewSetting: Observer[(FundDataInvestmentEntityId, UpdateInvestmentEntityReviewSettingFunc)],
  fundSubFormOptSignal: Signal[Option[Either[DynamicFormInfo, GaiaFormModel]]],
  fundSubSupportingDocsSignal: Signal[Seq[SupportingDocInfo]],
  isFetchingSignal: Signal[Boolean],
  firmFilterItemsOptSignal: Signal[Option[GetFirmFilterItemsResp]]
) {
  private given Codec.AsObject[FundDataInvestmentEntityId] = deriveCodecWithDefaults

  private val searchKeyVar: Var[String] = Var("")
  private val tempSearchKeyVar: Var[String] = Var("")

  private val foundInvestmentEntityIdsSignal = investmentEntityIdsSignal
    .combineWith(investorInfosSignal, searchKeyVar.signal)
    .map { case (ieIds, ieInfoMap, searchKey) =>
      if (searchKey.isEmpty) {
        ieIds
      } else {
        ieIds.filter { ieId => ieInfoMap.get(ieId).exists(_.name.toLowerCase.contains(searchKey.toLowerCase)) }
      }
    }
    .distinct

  private val selectRowsEventBus: EventBus[List[FundDataInvestmentEntityId]] =
    new EventBus[List[FundDataInvestmentEntityId]]

  private def rowId(fundDataInvestmentEntityId: FundDataInvestmentEntityId): String = {
    s"row-${fundDataInvestmentEntityId.idString}"
  }

  def apply(): HtmlElement = {
    div(
      tw.flex.flexCol,
      div(tw.heading3.fontBold, "Review and finalize import"),
      div(
        tw.mt4,
        s"Review the list of users that will be added to this ${FundSubCopyUtils.getFlowTerm.fullTerm.split(' ').map(_.capitalize).mkString(" ")} as investors."
      ),
      div(
        tw.mb20,
        "Form completion, provided documents and contacts can be customized in the ",
        span(tw.fontSemiBold, "Settings"),
        "."
      ),
      renderSearchBoxAndBatchSettings,
      child <-- investorInfosSignal.distinct.map { renderInvestmentEntityTable(_).amend(tw.flexFill) }
    )
  }

  private def renderSearchBoxAndBatchSettings = {
    div(
      tw.flex.justifyBetween.mb12,
      TextBoxL(
        value = tempSearchKeyVar.signal,
        onChange = tempSearchKeyVar.writer,
        onClear = Some(Observer { _ =>
          Var.set(tempSearchKeyVar -> "", searchKeyVar -> "")
        }),
        onKeyDown = Observer[KeyboardEvent] { event =>
          if (event.key == "Enter") {
            searchKeyVar.set(tempSearchKeyVar.now())
          }
        },
        icon = Some(Icon.Glyph.Search),
        placeholder = "Search"
      )().amend(width.px(300)),
      // batch setting
      div()
    )
  }

  private def renderEmptyState = {
    NonIdealStateL(
      icon = img(src := "/web/gondor/images/funddata/magnifier-eye.svg"),
      title = div(tw.textGray8.textBodyLarge.fontSemiBold, "No results found"),
      description = div(tw.textGray7.textBody, "You can rephrase your search to try again")
    )().amend(height.px(400))
  }

  private def renderInvestmentEntityTable(investorInfos: Map[FundDataInvestmentEntityId, InvestmentEntityInfo]) = {
    val investorNameColumn = nameColumn(investorInfos)
    div(
      TableL[FundDataInvestmentEntityId](
        dataSignal = foundInvestmentEntityIdsSignal.map(_.toList),
        options = TableL.Options(
          layout = TableL.Layout.FitColumns,
          indexColumn = Option("value")
        ),
        columns = List(
          checkBoxColumn,
          investorNameColumn,
          formColumn,
          documentColumn,
          collaboratorColumn,
          actionColumn
        ),
        initialSortColumns = List(
          TableL.SortColumn(column = investorNameColumn, direction = TableL.ColumnSortDirection.Asc)
        ),
        onRowRendered = Observer[TableL.RowRenderedData[FundDataInvestmentEntityId]](handleRowRendered),
        placeholder = TableL.Placeholder(
          criteria = _.map(_.isEmpty),
          render = _ => renderEmptyState
        )
      ).amend(
        tw.hPc100,
        selectRowsEventBus.events
          .withCurrentValueOf(foundInvestmentEntityIdsSignal, investmentEntityReviewSettingMapSignal) --> Observer[
          (
            List[FundDataInvestmentEntityId],
            Seq[FundDataInvestmentEntityId],
            Map[FundDataInvestmentEntityId, InvestmentEntityReviewSetting]
          )
        ] { case (selectedRows, foundIeIds, settingMap) =>
          val validSelectedRows = selectedRows.filter(ieId => settingMap.get(ieId).exists(_.mainLp.nonEmpty))
          onUpdateReviewedInvestmentEntityIds.onNext(_ -- foundIeIds ++ validSelectedRows)
        }
      )
    )
  }

  private def handleDisabledRow(ieId: FundDataInvestmentEntityId): Observer[Option[Boolean]] = {
    Observer {
      _.foreach { isRowValid =>
        val elementOpt = document.getElementById(rowId(ieId)) match {
          case element: HTMLElement => Some(element)
          case _                    => Option.empty
        }
        elementOpt.foreach { element =>
          if (isRowValid) {
            element.style.backgroundColor = ""
          } else {
            element.style.backgroundColor = CssVar.toVar(CssVar.Color.Gray1)
          }
        }
        if (!isRowValid) {
          onUpdateReviewedInvestmentEntityIds.onNext(_.filterNot(_ == ieId))
        }
      }
    }
  }

  private def handleRowRendered(rowRenderedData: TableL.RowRenderedData[FundDataInvestmentEntityId]): Unit = {
    rowRenderedData.rowComponent.getElement().classList.add(tw.group.css)
    rowRenderedData.getData().foreach(id => rowRenderedData.rowComponent.getElement().id = rowId(id))
  }

  private val checkBoxColumn = {
    SelectionColumnL[FundDataInvestmentEntityId](
      dataSignal = foundInvestmentEntityIdsSignal
        .map(_.toList)
        .combineWith(investmentEntityReviewSettingMapSignal, isFetchingSignal)
        .mapN { (foundInvestmentEntityIds, settingMap, isFetching) =>
          foundInvestmentEntityIds.map { entityId =>
            SelectionColumnL.Row(
              data = entityId,
              isDisabled = !settingMap.get(entityId).exists(_.mainLp.nonEmpty) || isFetching
            )
          }
        },
      selectedRowsSignal = reviewedInvestmentEntityIdsSignal.map(_.toList),
      onRowsSelected = selectRowsEventBus.writer,
      renderCell = renderProps => {
        div(
          child <-- isFetchingSignal.distinct.map {
            if (_) {
              TooltipL(
                renderTarget = div(tw.textPrimary5, CircleIndicatorL()()),
                renderContent = _.amend("Loading data...")
              )()
            } else {
              TooltipL(
                renderTarget = renderProps.cell,
                renderContent = _.amend("Need at least 1 contact selected as an investor to select"),
                isDisabled = investmentEntityReviewSettingMapSignal.map { settingMap =>
                  settingMap.get(renderProps.data).forall(_.mainLp.nonEmpty)
                }.distinct
              )()
            }
          }
        )
      }
    )()
  }

  private def nameColumn(investorInfos: Map[FundDataInvestmentEntityId, InvestmentEntityInfo]) = {
    TableL.Column[FundDataInvestmentEntityId](
      title = "Investor",
      field = "investor",
      renderCell = renderProps => {
        val ieName = investorInfos.get(renderProps.data).fold("")(_.name)
        val settingOptSignal = investmentEntityReviewSettingMapSignal.map(_.get(renderProps.data))
        div(
          tw.wPc100,
          TooltipL(
            renderTarget = div(tw.truncate.fontSemiBold, ieName),
            renderContent = _.amend(ieName),
            targetWrapper = PortalWrapperL.BlockContent
          )(),
          children <-- settingOptSignal.distinct.map {
            _.fold(
              Seq[Node](
                SkeletonL(height = "20px", width = "50%", shape = Skeleton.Shape.Rectangle)(),
                SkeletonL(height = "16px", width = "75%", shape = Skeleton.Shape.Rectangle)()
              )
            ) { setting =>
              val mainLpOpt = setting.mainLpInfoOpt
              val fullName = mainLpOpt.fold("--")(_.fullName)
              val email = mainLpOpt.fold("--")(_.email)
              Seq[Node](
                TooltipL(
                  renderTarget = div(tw.truncate, fullName),
                  renderContent = _.amend(fullName),
                  targetWrapper = PortalWrapperL.BlockContent,
                  isDisabled = Val(mainLpOpt.isEmpty)
                )(),
                TooltipL(
                  renderTarget = div(tw.truncate.textSmall.textGray7, email),
                  renderContent = _.amend(email),
                  targetWrapper = PortalWrapperL.BlockContent,
                  isDisabled = Val(mainLpOpt.isEmpty)
                )()
              )
            }
          },
          settingOptSignal.map(_.map(_.mainLp.nonEmpty)).distinct --> handleDisabledRow(renderProps.data)
        )
      },
      isSortable = true,
      sortWith = Option(sorter => {
        val sortResult = for {
          aId <- sorter.a
          a <- investorInfos.get(aId)
          bId <- sorter.b
          b <- investorInfos.get(bId)
        } yield {
          a.name.toLowerCase.compareTo(b.name).toDouble
        }
        sortResult.getOrElse(0)
      }),
      widthGrow = Some(9)
    )
  }

  private val formColumn = {
    TableL.Column[FundDataInvestmentEntityId](
      title = "Form completion",
      field = "form",
      renderCell = renderProps => {
        val settingOptSignal = investmentEntityReviewSettingMapSignal.map(_.get(renderProps.data))
        val isFetchingSignal = settingOptSignal
          .combineWith(fundSubFormOptSignal)
          .map { case (settingOpt, fundSubFormOpt) => settingOpt.isEmpty || fundSubFormOpt.isEmpty }
          .distinct
        val shouldPrefillSignal = settingOptSignal.map(_.exists(_.shouldPrefillFormData)).distinct
        val hasPrefilledDataSignal =
          settingOptSignal.map(_.exists(_.info.profileOpt.exists(_.hasPrefilledData))).distinct
        div(
          tw.wPc100,
          child <-- isFetchingSignal.combineWith(shouldPrefillSignal, hasPrefilledDataSignal).distinct.map {
            case (isFetching, shouldPrefill, hasPrefilledData) =>
              if (isFetching) {
                div(
                  SkeletonL(height = "20px", width = "85%", shape = Skeleton.Shape.Rectangle)(),
                  SkeletonL(height = "20px", width = "65%", shape = Skeleton.Shape.Rectangle)()
                )
              } else if (!hasPrefilledData) {
                div(tw.textGray5, "No data could be prefilled")
              } else if (!shouldPrefill) {
                div(tw.textGray5, s"Don't autofill ${FundSubCopyUtils.getFlowTerm.termInFormContext}")
              } else {
                val profileInfoOptSignal = settingOptSignal.map(_.flatMap(_.info.profileOpt))
                div(
                  child.maybe <-- profileInfoOptSignal
                    .combineWith(fundSubFormOptSignal)
                    .distinct
                    .map { case (profileInfoOpt, formModelOpt) =>
                      for {
                        profileInfo <- profileInfoOpt
                        formModel <- formModelOpt
                      } yield {
                        formModel match {
                          case Left(_) => div(tw.textGray5, "This form is not supported")
                          case Right(_) => {
                            val numHiddenData = profileInfo.numPrefilledHiddenFields
                            val numVisibleData = profileInfo.numPrefilledVisibleFields
                            div(
                              Option.when(numVisibleData > 0) {
                                div(
                                  span(tw.fontSemiBold, s"$numVisibleData "),
                                  if (numVisibleData >= 2) {
                                    "fields"
                                  } else {
                                    "field"
                                  },
                                  " prefilled"
                                )
                              },
                              Option.when(numHiddenData > 0) {
                                TooltipL(
                                  renderTarget = div(
                                    tw.textGray6.borderBottom.borderDashed.wFit,
                                    s"${Pluralize("field", numHiddenData, true)} currently hidden"
                                  ),
                                  renderContent = _.amend(
                                    "These fields are hidden due to the logic associated with them. ",
                                    "They will appear once the remaining fields are filled"
                                  ),
                                  targetWrapper = PortalWrapperL.BlockContent
                                )()
                              }
                            )
                          }
                        }
                      }
                    }
                )
              }
          }
        )
      },
      widthGrow = Some(6)
    )
  }

  private def renderDocumentPopoverContent(providedDocTypes: Seq[String]) = {
    div(
      tw.py4.flex.flexCol.spaceY4.overflowYAuto,
      width.px(312),
      maxHeight.px(440),
      div(tw.fontMedium.ml4, s"Provided ${Pluralize("document", providedDocTypes.length)}:"),
      providedDocTypes.map { docType =>
        div(
          tw.flex.spaceX16.px12.py8,
          div(tw.textGray7, IconL(name = Val(Icon.Glyph.FileText))()),
          div(
            tw.flexFill,
            TooltipL(
              renderTarget = div(tw.truncate.leading16, docType),
              renderContent = _.amend(docType),
              targetWrapper = PortalWrapperL.BlockContent
            )()
          )
        )
      }
    )
  }

  private val documentColumn = {
    TableL.Column[FundDataInvestmentEntityId](
      title = "Provided documents",
      field = "document",
      renderCell = renderProps => {
        val settingOptSignal = investmentEntityReviewSettingMapSignal.map(_.get(renderProps.data))
        div(
          tw.wPc100,
          child <-- settingOptSignal.distinct.map {
            _.fold(
              SkeletonL(height = "20px", width = "75%", shape = Skeleton.Shape.Rectangle)()
            ) { setting =>
              if (setting.providedDocTypes.isEmpty) {
                div(tw.textGray5, "No provided documents")
              } else {
                div(
                  tw.spaceY4,
                  Option.when(setting.shouldShareProvidedDocsWithLp) {
                    TooltipL(
                      renderTarget = TagL(label = Val("Shared"), color = Val(TagColor.Custom.YellowGreen))(),
                      renderContent =
                        _.amend("Your investors can view these documents and replace it with the latest version"),
                      targetWrapper = PortalWrapperL.BlockContent
                    )()
                  },
                  PopoverL(
                    renderTarget = (open, _) =>
                      ButtonL(style = ButtonL.Style.Text(), onClick = open.contramap(_ => ()))(
                        Pluralize("document", setting.providedDocTypes.size, true)
                      ),
                    renderContent = _ => renderDocumentPopoverContent(setting.providedDocTypes.keys.toSeq)
                  )()
                )
              }
            }
          }
        )
      },
      widthGrow = Some(6)
    )
  }

  private def renderCollaboratorPopoverContent(collaborators: Seq[CustomContactInfo]) = {
    div(
      tw.py4.flex.flexCol.spaceY4.overflowYAuto,
      width.px(312),
      maxHeight.px(440),
      div(tw.fontMedium.ml4, s"${Pluralize("Collaborator", collaborators.length)} will be added:"),
      collaborators.sortBy(_.fullName.toLowerCase).map { collab =>
        val fullName = collab.fullName
        div(
          tw.flex.spaceX16.px12.py4.itemsCenter,
          div(InitialAvatarL(id = fullName, initials = Val(fullName), size = InitialAvatar.Size.Px20)()),
          div(
            tw.flexFill,
            TooltipL(
              renderTarget = div(tw.truncate.fontSemiBold, fullName),
              renderContent = _.amend(fullName),
              targetWrapper = PortalWrapperL.BlockContent
            )(),
            TooltipL(
              renderTarget = div(tw.truncate.textSmall.textGray7, collab.email),
              renderContent = _.amend(collab.email),
              targetWrapper = PortalWrapperL.BlockContent
            )()
          )
        )
      }
    )
  }

  private val collaboratorColumn = {
    TableL.Column[FundDataInvestmentEntityId](
      title = "Collaborators",
      field = "collaborator",
      renderCell = renderProps => {
        val settingOptSignal = investmentEntityReviewSettingMapSignal.map(_.get(renderProps.data))
        div(
          tw.wPc100,
          child <-- settingOptSignal.distinct.map {
            _.fold(
              SkeletonL(height = "20px", width = "75%", shape = Skeleton.Shape.Rectangle)()
            ) { setting =>
              if (setting.collaborators.isEmpty) {
                div(tw.textGray5, "No collaborators")
              } else {
                val collaborators = setting.collaboratorsInfo
                PopoverL(
                  renderTarget = (open, _) =>
                    ButtonL(style = ButtonL.Style.Text(), onClick = open.contramap(_ => ()))(
                      Pluralize("collaborator", collaborators.length, true)
                    ),
                  renderContent = _ => renderCollaboratorPopoverContent(collaborators)
                )()
              }
            }
          }
        )
      },
      widthGrow = Some(4)
    )
  }

  private val actionColumn = {
    TableL.Column[FundDataInvestmentEntityId](
      title = "",
      field = "action",
      renderCell = renderProps => {
        val settingOptSignal = investmentEntityReviewSettingMapSignal.map(_.get(renderProps.data))
        div(
          tw.wPc100,
          child <-- settingOptSignal
            .combineWith(
              fundSubFormOptSignal,
              fundSubSupportingDocsSignal,
              firmFilterItemsOptSignal
            )
            .distinct
            .map { case (settingOpt, fundSubFormOpt, fundSubSupportingDocs, firmFilterItemsOpt) =>
              settingOpt
                .zip(fundSubFormOpt)
                .zip(firmFilterItemsOpt)
                .fold(
                  SkeletonL(height = "20px", width = "100%", shape = Skeleton.Shape.Rectangle)()
                ) { case ((setting, fundSubForm), firmFilterItems) =>
                  SingleInvestorSettingModal(
                    renderTarget = open =>
                      ButtonL(
                        onClick = open.contramap(_ => ()),
                        style = ButtonL.Style.Full(height = ButtonL.Height.Fix24)
                      )("Settings"),
                    initialSetting = setting,
                    onChangeSetting = Observer { updateFunc =>
                      onUpdateInvestmentEntityReviewSetting.onNext((renderProps.data, updateFunc))
                    },
                    fundSubForm = fundSubForm,
                    fundSubSupportingDocs = fundSubSupportingDocs,
                    firmFilterItems = firmFilterItems
                  )()
                }
            }
        )
      },
      width = Some(86)
    )
  }

}
