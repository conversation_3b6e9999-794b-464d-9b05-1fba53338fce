// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.auditlog

import anduin.copy.Bundle.I18N
import anduin.flow.FundSubFlowTermUtils.FlowTermCollection
import anduin.fundsub.signature.AllowFormEditPostSigningUtils
import anduin.protobuf.fundsub.FundSubEvent

object FundSubAuditLogUtils {

  def getDescriptionForFundSubAuditLogEventType(
    eventType: AuditLogEventType,
    copyConfig: I18N,
    flowTermCollection: FlowTermCollection
  ): String = {
    eventType match {
      case AuditLogEventType.CUSTOM_LP_ID_UPDATED => "Custom ID updated"
      case AuditLogEventType.CUSTOM_FUND_ID_UPDATED =>
        val customFundIdWording = copyConfig.fundManagementSide.settingsTab.customFundIdWording.value
        s"$customFundIdWording updated"
      case AuditLogEventType.LP_RESOLVING_COMMENT_ENABLED =>
        "Allow investors to mark comment threads as resolved enabled"
      case AuditLogEventType.LP_RESOLVING_COMMENT_DISABLED =>
        "Allow investors to mark comment threads as resolved disabled"
      case _ =>
        // These magic words bellow follow this guide https://www.notion.so/anduin/FS-deep-copy-customization-7e2cca3ba2374ecf8248dd36bdad1d36
        if (eventType == AuditLogEventType.ALLOW_POST_SIGNING_UPDATES_WITHOUT_RE_SIGNATURE_UPDATED) {
          s"${AllowFormEditPostSigningUtils.FundSettingFeatureLabel} updated"
        } else {
          eventType.name
            .toLowerCase()
            .replace("_", " ")
            .replace("subscription package", flowTermCollection.termInPackageContext)
            .replace("subscription form", flowTermCollection.termInFormContext)
            .replace("subscription document", flowTermCollection.termInDocumentContext)
            .replace("fund subscription", flowTermCollection.fullTerm)
            .replace("subscription", flowTermCollection.standAloneTerm)
            .capitalize
        }
    }
  }

  def getEmailDescriptionForFundSubEvent(event: FundSubEvent, flowTermCollection: FlowTermCollection): String = {
    event match {
      case FundSubEvent.notifyEmailBounced               => "Invitation email bounced notification"
      case FundSubEvent.inviteAdmin                      => "Invitation to fund manager"
      case FundSubEvent.removeAdmin                      => "Remove notification to fund manager"
      case FundSubEvent.revokeAdminInvitation            => "Invitation withdrawn notification to fund manager"
      case FundSubEvent.inviteLp                         => "Invitation to investor"
      case FundSubEvent.inviteMultipleLpsPerMainLp       => "Invitation to investor"
      case FundSubEvent.removeLp                         => "Remove notification to investor"
      case FundSubEvent.revokeLpInvitation               => "Invitation withdrawn notification to investor"
      case FundSubEvent.inviteLpCollaborator             => "Invitation to collaborator"
      case FundSubEvent.inviteMultipleLpsPerCollaborator => "Invitation to collaborator"
      case FundSubEvent.subscribeBlankOrderViaInvitation => "Subscribe blank order via invitation email"
      case FundSubEvent.removeLpCollaborator             => "Remove notification to collaborator"
      case FundSubEvent.approveSoftReview =>
        s"Unsigned ${flowTermCollection.termInDocumentContext} review approved by fund notification to investor"
      case FundSubEvent.cancelSoftReview =>
        s"Unsigned ${flowTermCollection.standAloneTerm} review canceled notification to fund manager"
      case FundSubEvent.lpDuplicatedOrderParticipantNotification =>
        "Notification email for LP team when order is duplicated"
      case FundSubEvent.requestLpChange => "Change request notification to investor"
      case FundSubEvent.remindLpSignAgain =>
        s"Sign edited ${flowTermCollection.termInDocumentContext} reminder to investor"
      case FundSubEvent.lpSubmitted =>
        s"${flowTermCollection.termInDocumentContext.capitalize} submitted notification to fund manager"
      case FundSubEvent.lpSubmittedConfirmation =>
        s"${flowTermCollection.termInDocumentContext.capitalize} submitted notification to investor"
      case FundSubEvent.distributedDoc =>
        s"${flowTermCollection.termInPackageContext.capitalize} package distributed notification to fund manager"
      case FundSubEvent.countersigned =>
        s"${flowTermCollection.termInPackageContext.capitalize} package approved notification to investor"
      case FundSubEvent.countersignedDocsUpdated =>
        s"Countersigned ${flowTermCollection.termInDocumentContext} updated notification to investor"
      case FundSubEvent.markSubscriptionAsComplete =>
        s"${flowTermCollection.termInPackageContext.capitalize} was marked as complete"
      case FundSubEvent.customEmailFromFundManager => "Email to investor"
      case FundSubEvent.remindLpCompleteForm =>
        s"Complete ${flowTermCollection.termInDocumentContext} reminder to investor"
      case FundSubEvent.remindSupportingDoc => "Submit additional document reminder to investor"
      case FundSubEvent.requestSupportingDoc =>
        "Additional document request notification to investor"
      case FundSubEvent.uploadReferenceDoc => "Upload Reference Doc Template"
      case FundSubEvent.commentsDigestToInvestor | FundSubEvent.notifyCommentsToInvestor =>
        "New comment notification to investor"
      case FundSubEvent.commentsDigestToFundManager           => "New comment notification to fund manager"
      case FundSubEvent.commentAssignmentsDigestToFundManager => "New comment assignment notification to fund manager"
      case FundSubEvent.documentReadyForReview =>
        s"Signed ${flowTermCollection.termInDocumentContext} submitted for approval notification to fund manager"
      case FundSubEvent.markSubDocRequestCompleteRequester =>
        s"Signature request on ${flowTermCollection.termInDocumentContext} marked as complete notification to requester"
      case FundSubEvent.markSubDocRequestCompleteSigner =>
        s"Signature request on ${flowTermCollection.termInDocumentContext} marked as complete notification to signer"
      case FundSubEvent.unsignedDocumentReadyForReview =>
        s"Unsigned ${flowTermCollection.termInDocumentContext} submitted for review notification to fund manager"
      case FundSubEvent.lpFilledForm =>
        s"${flowTermCollection.termInDocumentContext.capitalize} filled notification to fund manager"
      case FundSubEvent.lpSendSignatureRequest =>
        s"Signature request on ${flowTermCollection.termInDocumentContext} notification"
      case FundSubEvent.lpRemindSignatureRequest => "Remind Signature Request Template"
      case FundSubEvent.lpCancelSignatureRequest =>
        s"Signature request on ${flowTermCollection.termInDocumentContext} canceled notification"
      case FundSubEvent.signerDoneSignatureRequestToLp    => "Signature request complete notification to investor"
      case FundSubEvent.signerDoneSignatureRequest        => "Signature request complete notification to signer"
      case FundSubEvent.sendSupportingDocSignatureRequest => "Signature request on additional document notification"
      case FundSubEvent.remindSupportingDocSignatureRequest =>
        "Signature request on additional document reminder notification"
      case FundSubEvent.cancelSupportingDocSignatureRequest =>
        "Signature request on additional document canceled notification"
      case FundSubEvent.doneSupportingDocSignatureRequest =>
        "Signature request on additional document complete notification to signer"
      case FundSubEvent.doneSupportingDocSignatureRequestToAdmin =>
        "Signature request on additional document complete notification to requester"
      case FundSubEvent.sendFormSubmissionSignatureRequest => "Signature request on additional document notification"
      case FundSubEvent.remindFormSubmissionSignatureRequest =>
        "Signature request on additional document reminder notification"
      case FundSubEvent.cancelFormSubmissionSignatureRequest =>
        "Signature request on additional document canceled notification"
      case FundSubEvent.doneFormSubmissionSignatureRequest =>
        "Signature request on additional document complete notification to requester"
      case FundSubEvent.sendTaxFormSignatureRequest   => "Signature request on tax form notification"
      case FundSubEvent.remindTaxFormSignatureRequest => "Signature request on tax form reminder notification"
      case FundSubEvent.cancelTaxFormSignatureRequest => "Signature request on tax form canceled notification"
      case FundSubEvent.doneTaxFormSignatureRequest   => "Signature request on tax form complete notification to signer"
      case FundSubEvent.doneTaxFormSignatureRequestToRequester =>
        "Signature request on tax form complete notification to requester"
      case FundSubEvent.adminSendCounterSignRequest      => "Countersignature request sent"
      case FundSubEvent.adminSendBatchCounterSignRequest => "Countersignature request sent"
      case FundSubEvent.cancelBatchCountersignRequest    => "Countersignature request canceled notification"
      case FundSubEvent.adminRemindCounterSignRequest    => "Countersignature request reminder notification"
      case FundSubEvent.adminCancelCounterSignRequest    => "Countersignature request canceled notification"
      case FundSubEvent.signerDoneCounterSignRequest     => "Countersignature request completed notification to signer"
      case FundSubEvent.signerDoneCounterSignRequestToAdmin =>
        "Countersignature request completed notification to requester"
      case FundSubEvent.uploadedCountersignedDoc =>
        s"Countersigned ${flowTermCollection.termInDocumentContext} uploaded notification to fund manager"
      case FundSubEvent.countersignedDoc =>
        s"Countersigned ${flowTermCollection.termInDocumentContext} uploaded notification to fund manager"
      case FundSubEvent.lpRequestReassignRequester => "Investor request reassign requester notification"
      case FundSubEvent.lpRequestReassignSigner    => "Investor request reassign signer notification"
      case FundSubEvent.lpRequestReassignActor     => "Investor request reassign actor notification"
      case FundSubEvent.newDocumentUploadDigestToFundManager =>
        "New additional document upload notification to fund manager"
      case FundSubEvent.newLpDigestToFundManager   => "Daily investor report notification to fund manager"
      case FundSubEvent.documentReviewAssignment   => "Fund manager assigned as reviewer notification"
      case FundSubEvent.documentReviewUnassignment => "Fund manager unassigned as reviewer notification"
      case FundSubEvent.cancelSupportingDocChangeRequest =>
        "Change request to additional document canceled notification"
      case FundSubEvent.requestChangeOnSupportingDoc => "Change request to additional document notification"
      case FundSubEvent.amlKycDocumentReviewAssignment | FundSubEvent.documentReviewAssignment =>
        "Fund manager assigned as reviewer notification"
      case FundSubEvent.amlKycDocumentReviewUnassignment | FundSubEvent.documentReviewUnassignment =>
        "Fund manager unassigned as reviewer notification"
      case FundSubEvent.documentHasNoReviewer => "Documents Missing Reviewers"
      case FundSubEvent.dataExtractRequestReadyForReview =>
        "Data extraction ready for review notification to fund managers"
      case FundSubEvent.inviteAdvisor                    => "Advisor invitation"
      case FundSubEvent.revokeAdvisorInvitation          => "Revoke advisor email"
      case FundSubEvent.riaEntityLinked                  => "RIA entity linked notification"
      case FundSubEvent.sendSideLetterSignatureRequest   => "Side letter signature request sent"
      case FundSubEvent.remindSideLetterSignatureRequest => "Side letter request reminder notification"
      case FundSubEvent.cancelSideLetterSignatureRequest => "Side letter request canceled notification"
      case FundSubEvent.doneSideLetterSignatureRequest   => "Side letter request completed notification"
      case FundSubEvent.gpSharedFirstSideLetterVersion   => "Side letter first version created"
      case FundSubEvent.gpCreatedNewSideLetterVersion    => "Fund manager created new side letter version"
      case FundSubEvent.gpAddedSideLetterFile            => "Fund manager added side letter files"
      case FundSubEvent.gpMarkedSideLetterAsAgreed       => "Fund manager mark side letter as agreed"
      case FundSubEvent.gpMarkedSideLetterAsCompleted    => "Fund manager mark side letter as complete"
      case FundSubEvent.lpCreatedSideLetterVersion       => "Investor created new side letter version"
      case FundSubEvent.lpAddedSideLetterSignedFile      => "Investor added side letter signed files"
      case FundSubEvent.lpAddedSideLetterFile            => "Investor added side letter files"
      case FundSubEvent.fundSubTestEmail                 => "Test Email"

      case FundSubEvent.lpSubmittedSupportingDoc | FundSubEvent.documentReadyForReviewDigest |
          FundSubEvent.batchSignCountersignRequestToSignerAndRequester |
          FundSubEvent.batchSignCountersignRequestToFundAdmin | FundSubEvent.fundActivitiesDigest |
          FundSubEvent.signerFailedDocusignAuthentication | FundSubEvent.Unrecognized(_) =>
        "???"
    }
  }

}
