// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.activitylog

import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

import anduin.copy.Bundle.I18N
import anduin.dashboard.data.CustomColumnData
import anduin.flow.FundSubFlowTermUtils.FlowTermCollection
import anduin.fundsub.admin.FundSubAdminUtils
import anduin.fundsub.constants.Terms
import anduin.fundsub.endpoint.admin.LpBasicInfo
import anduin.fundsub.endpoint.lp.NameEmailInfo
import anduin.fundsub.signature.AllowFormEditPostSigningUtils
import anduin.id.fundsub.FundSubLpId
import anduin.model.common.user.UserId
import anduin.protobuf.activitylog.fundsub.admin.*
import anduin.protobuf.activitylog.fundsub.admin.UpdateInvestorValueActionMessage.SealedValue.{
  UpdateInvestorValueActionAdd,
  UpdateInvestorValueActionRemove
}
import anduin.protobuf.activitylog.fundsub.admin.UpdateInvestorValueTypeMessage.SealedValue.{
  UpdateInvestorValueTypeMultipleSelect,
  UpdateInvestorValueTypeSingleSelect,
  UpdateInvestorValueTypeTag
}
import anduin.protobuf.fundsub.models.customdata.item.CustomData
import anduin.protobuf.fundsub.models.customdata.item.CustomDataMessage.SealedValue
import anduin.protobuf.fundsub.{FundSubAdminRole, FundSubEvent}
import anduin.util.CurrencyUtils
import anduin.utils.StringUtils

object FaActivityLogHelper {

  private def getLpFirmName(
    userId: UserId,
    relatedLpIdOpt: Option[FundSubLpId],
    lpInfoMap: Map[FundSubLpId, LpBasicInfo],
    skipRelatedLpCheck: Boolean = false
  ) = {
    val lpFirmName = relatedLpIdOpt.fold[String]("") { lpId =>
      val lpInfoOpt = lpInfoMap.get(lpId)
      if (
        skipRelatedLpCheck || lpInfoOpt.exists { lpInfo =>
          lpInfo.mainLp.userId == userId || lpInfo.collaborators.contains(userId)
        }
      ) {
        lpInfoOpt.map(_.firmName.trim).getOrElse("")
      } else {
        ""
      }
    }

    lpFirmName
  }

  private def getLpName(
    lpId: FundSubLpId,
    lpInfoMap: Map[FundSubLpId, LpBasicInfo] = Map.empty
  ) = {
    lpInfoMap.get(lpId).fold("")(_.lpFirmNameOrFullName)
  }

  def getParticipantName(
    userId: UserId,
    userInfoMap: Map[UserId, NameEmailInfo],
    relatedLpIdOpt: Option[FundSubLpId] = None,
    lpInfoMap: Map[FundSubLpId, LpBasicInfo] = Map.empty,
    skipRelatedLpCheck: Boolean = false
  ): String = {
    val nameOrEmail = userInfoMap
      .get(userId)
      .map { userInfo =>
        if (userInfo.fullName.isEmpty) userInfo.email else userInfo.fullName
      }
      .getOrElse("")

    val lpFirmName = getLpFirmName(
      userId,
      relatedLpIdOpt,
      lpInfoMap,
      skipRelatedLpCheck
    )

    if (lpFirmName.nonEmpty) {
      s"$nameOrEmail ($lpFirmName)"
    } else {
      nameOrEmail
    }
  }

  private def getFirmNameOrName(
    userId: UserId,
    userInfoMap: Map[UserId, NameEmailInfo],
    relatedLpIdOpt: Option[FundSubLpId],
    lpInfoMap: Map[FundSubLpId, LpBasicInfo],
    skipRelatedLpCheck: Boolean = false
  ): String = {
    val nameOrEmail = userInfoMap
      .get(userId)
      .map { userInfo =>
        if (userInfo.fullName.isEmpty) userInfo.email else userInfo.fullName
      }
      .getOrElse("")

    val lpFirmName = getLpFirmName(
      userId,
      relatedLpIdOpt,
      lpInfoMap,
      skipRelatedLpCheck
    )

    if (lpFirmName.nonEmpty) {
      lpFirmName
    } else {
      nameOrEmail
    }
  }

  private def getFundAdminRole(role: FundSubAdminRole) = {
    FundSubAdminUtils.adminRoleToString(role)
  }

  // Note: This function return the description of activity in plain text,
  // which used for audit log summary (both on frontend and exported csv) and activity log content.
  // If you want a custom renderer to render the description for frontend, please define
  // your own in `Activity.getActivityDetail` but you still have to
  // define a plaintext version of the event in this function for audit log export content
  def renderFundAdminActivityDescription(
    activity: FundAdminActivity,
    userInfoMap: Map[UserId, NameEmailInfo],
    lpInfoMap: Map[FundSubLpId, LpBasicInfo],
    copyConfig: I18N,
    flowTermCollection: FlowTermCollection
  ): String = {
    activity match {
      case a: AdminInvited =>
        if (a.groupName.nonEmpty) {
          s"invited ${getParticipantName(a.userId, userInfoMap)} to the ${a.groupName} group of the fund"
        } else {
          val role = getFundAdminRole(a.role)
          s"invited ${getParticipantName(a.userId, userInfoMap)} as ${StringUtils.getDeterminer(role)} $role"
        }
      case a: AdminJoined =>
        if (a.groupName.nonEmpty) {
          s"joined to the ${a.groupName} group of the fund"
        } else {
          val role = getFundAdminRole(a.role)
          s"joined as ${StringUtils.getDeterminer(role)} $role"
        }

      case a: AdminRemoved =>
        s"removed ${getParticipantName(a.userId, userInfoMap)} from the fund"
      case a: LpInvited =>
        val collaboratorDescription = if (a.collaborators.nonEmpty) {
          s", and ${
              if (a.collaborators.length > 1) {
                s"${a.collaborators.length} others as collaborators"
              } else {
                s"${getParticipantName(
                    a.collaborators.head,
                    userInfoMap,
                    None,
                    lpInfoMap
                  )} as a collaborator"
              }
            }"
        } else {
          ""
        }
        s"invited ${getParticipantName(
            a.userId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )} as an investor$collaboratorDescription"
      case _: LpJoined => "joined as an investor"
      case a: OfflineOrderAdded =>
        s"added an offline order to track investment from ${getParticipantName(
            a.userId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )}"
      case a: ConvertOfflineOrderToNormal =>
        s"invited ${getParticipantName(
            a.userId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )} to the ${flowTermCollection.standAloneTerm} previously tracked as an offline order"
      case a: AdminAccessedSubscription =>
        val lpUserIdOpt = lpInfoMap.get(a.lpId)
        val subscriptionName = lpUserIdOpt.fold("investor") { lpUserId =>
          getParticipantName(
            lpUserId.mainLp.userId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )
        }
        s"accessed $subscriptionName's ${flowTermCollection.standAloneTerm}"
      case a: LpInvestedInAdditionalFund =>
        val newFirmName = lpInfoMap.get(a.lpId).map(_.firmName.trim).getOrElse("")
        val suffix = if (newFirmName.nonEmpty) s" ($newFirmName)" else ""
        s"started investing from additional entity" + suffix
      case _: LpJoinedViaInvitationLink =>
        s"joined via invitation link as an investor"
      case a: LpRemoved =>
        s"removed ${getParticipantName(
            a.userId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )} from the fund"
      case a: LpRestored =>
        s"restored investor ${getParticipantName(
            a.userId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )}"
      case a: CollaboratorAdded =>
        s"added ${getParticipantName(
            a.userId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )} as a collaborator"
      case _: CollaboratorJoined =>
        s"joined as a collaborator"
      case a: CollaboratorRemoved =>
        s"removed ${getParticipantName(
            a.userId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap,
            skipRelatedLpCheck = true
          )} from the fund"
      case a: CollaboratorPromoted =>
        s"changed the investor to ${getParticipantName(
            a.userId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap,
            skipRelatedLpCheck = true
          )} for this ${flowTermCollection.standAloneTerm}"
      case a: UploadedExecutedDocument =>
        s"uploaded countersigned documents for ${getParticipantName(
            a.investorUserId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )}"
      case a: UploadedDocOnBehalf =>
        s"uploaded documents on behalf of ${getParticipantName(
            a.investorUserId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )}"
      case a: SentExecutedDocument =>
        s"distributed countersigned documents to ${getParticipantName(
            a.investorUserId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )}"
      case a: UpdatedExecutedDocument =>
        s"updated countersigned documents for ${getParticipantName(
            a.investorUserId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )}"
      case a: SignedExecutedDocument =>
        s"countersigned the ${flowTermCollection.termInDocumentContext}s for ${getParticipantName(
            a.investorUserId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )}"
      case _: SubmittedSubscriptionPackage =>
        s"submitted the ${flowTermCollection.termInPackageContext}"
      case _: UndidSubscriptionPackage =>
        s"withdrew the ${flowTermCollection.termInPackageContext}"
      case a: MarkedSubscriptionAsComplete =>
        val name = getParticipantName(
          a.lpUserId,
          userInfoMap,
          Option(a.lpId),
          lpInfoMap
        )
        s"marked ${StringUtils.addApostrophe(name)} ${flowTermCollection.standAloneTerm} as complete"
      case a: RequestChange =>
        val action = if (a.refillFormRequired) {
          s"make change to the ${flowTermCollection.standAloneTerm} form"
        } else {
          "sign the form again"
        }
        s"requested ${getParticipantName(
            a.investorUserId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )} to $action"

      case _: SubscriptionDocumentReviewEnabled  => "enabled document review"
      case _: SubscriptionDocumentReviewDisabled => "disabled document review"
      case a: SubscriptionDocumentReviewerAdded =>
        s"assigned ${getParticipantName(
            a.userId,
            userInfoMap
          )} as document reviewer"
      case a: SubscriptionDocumentReviewerRemoved =>
        s"unassigned ${getParticipantName(
            a.userId,
            userInfoMap
          )} as document reviewer"
      case a: SubscriptionDocumentMarkedAsReviewed =>
        s"marked ${flowTermCollection.termInDocumentContext}s from ${getParticipantName(
            a.investorUserId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )} as approved"
      case a: SignedSubscriptionDocumentMarkedAsApproved =>
        s"marked signed ${flowTermCollection.termInDocumentContext} from ${getParticipantName(
            a.investorUserId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )} as approved"
      case a: UnsignedSubscriptionDocumentMarkedAsApproved =>
        s"marked unsigned ${flowTermCollection.termInDocumentContext} from ${getParticipantName(
            a.investorUserId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )} as approved"
      case a: SubscriptionDocumentReviewSkipped =>
        s"skipped ${flowTermCollection.termInDocumentContext}s review for ${getParticipantName(
            a.investorUserId,
            userInfoMap,
            Option(a.lpId),
            lpInfoMap
          )}"
      case a: ManualOrderActivatedByInvestor =>
        val firmName = getLpFirmName(
          a.investorUserId,
          Option(a.lpId),
          lpInfoMap
        )
        s"accessed the offline order prepared for their ${Terms.InvesteeEntityLabel} $firmName"
      case a: SentEmail =>
        val recipientFullNames = a.userIds.map(userId => getParticipantName(userId, userInfoMap))
        val recipientString =
          s"${recipientFullNames.headOption.map(recipientFullName => recipientFullName).getOrElse("")} ${Option
              .when(recipientFullNames.length > 1)(" and collaborators")
              .getOrElse("")}"
        s"sent an email to $recipientString"
      case a: RemindLpToSignAgain =>
        val name = getParticipantName(
          a.investorUserId,
          userInfoMap,
          Option(a.lpId),
          lpInfoMap
        )
        s"Reminder sent to $name"
      case a: SentReminderToUploadSupportingDoc =>
        val name = getParticipantName(
          a.investorUserId,
          userInfoMap,
          Option(a.lpId),
          lpInfoMap
        )
        s"reminded $name to submit all AML/KYC and other requested documents"
      case a: LpFilledForm =>
        val name = getParticipantName(
          a.investorUserId,
          userInfoMap,
          Option(a.lpId),
          lpInfoMap
        )
        s"$name filled ${flowTermCollection.termInDocumentContext}"
      case a: NewAdditionalDocumentUploadReport =>
        s"New additional documents uploaded in the past ${a.frequencyInSecond / 60} minutes"
      case a: SignatureRequestReassign =>
        val oldSignerName = getParticipantName(
          a.oldSigner,
          userInfoMap,
          None,
          lpInfoMap
        )
        val newSignerName = getParticipantName(
          a.newSigner,
          userInfoMap,
          None,
          lpInfoMap
        )
        s"$oldSignerName's signature request is assigned to $newSignerName"
      case _: NewLpReport                => "Newly joined investors in the past 24 hours"
      case _: NewFormCommentReport       => s"New comments on ${flowTermCollection.termInDocumentContext}"
      case _: NewCommentAssignmentReport => s"New comment assignments on ${flowTermCollection.termInDocumentContext}"
      case _: RemovedUploadedExecutedDocument                                      => "removed countersigned documents"
      case _: BatchLpInvited | _: BatchOfflineOrderAdded | FundAdminActivity.Empty => ""
      case a: GroupMembersMoved =>
        if (a.groupMembers.size == 1) {
          s"moved ${getParticipantName(a.groupMembers.head, userInfoMap)} to group ${a.destGroupName}"
        } else {
          s"moved ${a.groupMembers.size} members to group ${a.destGroupName}"
        }
      case a: GroupCreated             => s"created group ${a.groupName}"
      case a: GroupRenamed             => s"updated group ${a.oldName}'s name to ${a.newName}"
      case a: GroupPermissionUpdated   => s"updated group ${a.groupName}'s permission"
      case a: GroupVisibilityUpdated   => s"updated group ${a.groupName}'s visibility"
      case a: GroupDeleted             => s"deleted group ${a.groupName}"
      case a: GroupAccessToViewUpdated => s"updated group access to dashboard ${a.viewName}"
      case a: ViewRenamed              => s"updated dashboard ${a.oldName}'s name to ${a.newName}"
      case a: ViewCreated              => s"created dashboard ${a.viewName}"
      case a: DashboardColumnsUpdated  => s"updated dashboard ${a.viewName}'s layout"
      case a: ViewDeleted              => s"deleted dashboard ${a.viewName}"
      case a: PrivateViewShared        => s"created dashboard ${a.viewName}"
      case a: InvestorGroupCreated     => s"created a new investor group ${a.groupName}"
      case a: InvestorGroupRenamed     => s"renamed ${a.oldName} to ${a.newName}"
      case a: InvestorGroupDeleted     => s"deleted the investor group ${a.groupName}"
      case a: InvestorAssignedToGroup =>
        val assignedLpInfo = if (a.lpIds.size == 1) {
          getLpName(a.lpIds.head, lpInfoMap)
        } else {
          s"${a.lpIds.size} investors"
        }
        s"assigned $assignedLpInfo to the investor group ${a.groupName}"
      case a: InvestorMovedToAnotherGroup =>
        val movedLpInfo = if (a.lpIds.size == 1) {
          getLpName(a.lpIds.head, lpInfoMap)
        } else {
          s"${a.lpIds.size} investors"
        }
        s"moved $movedLpInfo to the investor group ${a.destGroupName}"
      case a: InvestorUnassignedFromGroup =>
        val unassignedLpInfo = if (a.lpIds.size == 1) {
          getLpName(a.lpIds.head, lpInfoMap)
        } else {
          s"${a.lpIds.size} investors"
        }
        s"unassigned $unassignedLpInfo out of the investor group ${a.groupName}"
      case a: InvestorGroupAccessibilityGranted   => s"granted ${a.adminGroupName} access to ${a.investorGroupName}"
      case a: InvestorGroupAccessibilityWithdrawn => s"removed ${a.adminGroupName} access from ${a.investorGroupName}"
      case AmlKycReviewConfigUpdated(action, _) =>
        action match {
          case _: AmlKycReviewTurnedOff => "disabled AML/KYC document review"
          case _: AmlKycReviewTurnedOn  => "enabled AML/KYC document review"
          case a: AmlKycReviewUpdated =>
            val docGroupStatusInfo = if (a.wasDocGroupEnabled != a.isDocGroupEnabled) {
              val action = if (a.isDocGroupEnabled) {
                "enabling"
              } else {
                "disabling"
              }
              s" by $action supporting document groups"
            } else {
              ""
            }
            "updated AML/KYC document review settings" + docGroupStatusInfo
          case AmlKycReviewConfigUpdateAction.Empty => "N.A"
        }
      case SignedSubscriptionDocReviewConfigUpdated(action, _) =>
        action match {
          case _: SignedSubscriptionDocReviewTurnedOff =>
            s"disabled signed ${flowTermCollection.termInDocumentContext} review"
          case _: SignedSubscriptionDocReviewTurnedOn =>
            s"enabled signed ${flowTermCollection.termInDocumentContext} review"
          case _: SignedSubscriptionDocReviewUpdated =>
            s"updated signed ${flowTermCollection.termInDocumentContext} review settings"
          case SignedSubscriptionDocReviewConfigUpdateAction.Empty => "N.A"
        }
      case UnsignedSubscriptionDocReviewConfigUpdated(action, _) =>
        action match {
          case _: UnsignedSubscriptionDocReviewTurnedOff =>
            s"disabled unsigned ${flowTermCollection.termInDocumentContext} review"
          case _: UnsignedSubscriptionDocReviewTurnedOn =>
            s"enabled unsigned ${flowTermCollection.termInDocumentContext} review"
          case _: UnsignedSubscriptionDocReviewUpdated =>
            s"updated unsigned ${flowTermCollection.termInDocumentContext} review settings"
          case UnsignedSubscriptionDocReviewConfigUpdateAction.Empty => "N.A"
        }
      case a: NewFormCommentNotificationToInvestor =>
        s"sent new comments on ${flowTermCollection.termInDocumentContext} to ${getParticipantName(
            a.investorUserId,
            userInfoMap,
            Some(a.lpId),
            lpInfoMap
          )}"
      case _: AmendmentAdded =>
        s"Amendment added to ${flowTermCollection.termInDocumentContext}"
      case _: AmendmentEdited =>
        "Amendment edited"
      case _: AmendmentRemoved =>
        "Amendment removed"
      case a: UpdateInvestorsValues =>
        val name = getFirmNameOrName(
          a.investorUserId,
          userInfoMap,
          Option(a.lpId),
          lpInfoMap
        )
        a.updateInvestorValueType.asMessage.sealedValue match {
          case UpdateInvestorValueTypeTag(_) =>
            a.updateInvestorValueAction.asMessage.sealedValue match {
              case UpdateInvestorValueActionAdd(_)    => s"added ${a.updatedValues.mkString(", ")} to $name's Tags"
              case UpdateInvestorValueActionRemove(_) => s"removed ${a.updatedValues.mkString(", ")} from $name's Tags"
              case _                                  => "N.A"
            }
          case UpdateInvestorValueTypeSingleSelect(value) =>
            a.updateInvestorValueAction.asMessage.sealedValue match {
              case UpdateInvestorValueActionAdd(_) =>
                s"updated $name's ${value.columnName} to ${a.updatedValues.mkString(", ")}"
              case UpdateInvestorValueActionRemove(_) =>
                s"cleared ${a.updatedValues.mkString(", ")} from $name's ${value.columnName}"
              case _ => "N.A"
            }
          case UpdateInvestorValueTypeMultipleSelect(value) =>
            a.updateInvestorValueAction.asMessage.sealedValue match {
              case UpdateInvestorValueActionAdd(_) =>
                s"added ${a.updatedValues.mkString(", ")} to $name's ${value.columnName}"
              case UpdateInvestorValueActionRemove(_) =>
                s"removed ${a.updatedValues.mkString(", ")} from $name's ${value.columnName}"
              case _ => "N.A"
            }
          case _ => "N.A"
        }
      case a: CustomLpIdUpdated =>
        val name = lpInfoMap.get(a.lpId).fold("") { lpInfo =>
          if (lpInfo.lpFirmNameOrFullName.nonEmpty) {
            lpInfo.lpFirmNameOrFullName
          } else {
            lpInfo.mainLp.email
          }
        }
        if (a.currentCustomLpId.trim.isEmpty) {
          s"added a Custom ID for $name (${a.newCustomLpId})"
        } else if (a.newCustomLpId.trim.isEmpty) {
          s"removed $name's Custom ID (${a.currentCustomLpId})"
        } else {
          s"edited $name's Custom ID from ${a.currentCustomLpId} to ${a.newCustomLpId}"
        }
      case a: CustomFundIdUpdated =>
        val customFundIdWording = copyConfig.fundManagementSide.settingsTab.customFundIdWording.value
        // Turning off custom fund ID means deleting a custom fund id, turning on means creating a new one.
        if (!a.wasEnabled && a.isEnabled) {
          s"added $customFundIdWording (${a.newCustomFundId})"
        } else if (a.wasEnabled && !a.isEnabled) {
          s"removed $customFundIdWording (${a.oldCustomFundId})"
        } else {
          s"edited $customFundIdWording from ${a.oldCustomFundId} to ${a.newCustomFundId}"
        }
      case a: CommentActivity     => commentActivityDescription(a)
      case a: AnchorPointActivity => anchorPointActivityDescription(a)
      case a: CommentExported =>
        if (a.includingInternalComment) {
          "exported internal and share comments"
        } else {
          "exported shared comments"
        }
      case a: CommentSettingUpdateActivity =>
        val action = if (a.action.isDisableLpResolvingComment) {
          "disabled"
        } else {
          "enabled"
        }
        s"$action Allow investors to mark comment threads as resolved"
      case activity: DataExtractionStarted =>
        val lpDisplayName = getFirmNameOrName(
          userId = activity.requestInfo.lpUserId,
          userInfoMap = userInfoMap,
          relatedLpIdOpt = Some(activity.requestInfo.lpId),
          lpInfoMap = lpInfoMap
        )
        s"started data extraction using submitted documents of $lpDisplayName"
      case extractedDataReadyForReview: ExtractedDataReadyForReview =>
        val subscriptionWordingPlural =
          StringUtils.pluralItem(extractedDataReadyForReview.requestInfos.size, flowTermCollection.standAloneTerm)
        "Data extraction for " + subscriptionWordingPlural + " is ready for review"
      case activity: ExtractedDataMarkedAsComplete =>
        val lpDisplayName = getFirmNameOrName(
          userId = activity.requestInfo.lpUserId,
          userInfoMap = userInfoMap,
          relatedLpIdOpt = Some(activity.requestInfo.lpId),
          lpInfoMap = lpInfoMap
        )
        s"marked a data extraction for $lpDisplayName as complete"
      case activity: ExtractedDataEdited =>
        val lpDisplayName = getFirmNameOrName(
          userId = activity.requestInfo.lpUserId,
          userInfoMap = userInfoMap,
          relatedLpIdOpt = Some(activity.requestInfo.lpId),
          lpInfoMap = lpInfoMap
        )
        s"made changes to data extraction form of $lpDisplayName"
      case activity: CreateClose => s"created ${activity.name}"
      case activity: UpdateClose => s"updated ${activity.name}"
      case activity: DeleteClose => s"deleted ${activity.name}"
      case activity: MoveLpToNewClose =>
        s"assigned ${getLpName(activity.lpId, lpInfoMap)} from ${activity.oldFundSubCloseName} to ${activity.newFundSubCloseName}"
      case activity: SelfServiceExportTemplateCreated =>
        s"created ${activity.templateName}"
      case activity: SelfServiceExportTemplateUpdated =>
        s"edited ${activity.templateName}"
      case activity: SelfServiceExportTemplateRenamed =>
        s"renamed ${activity.oldTemplateName} to ${activity.newTemplateName}"
      case activity: SelfServiceExportTemplateDeleted =>
        s"deleted ${activity.templateName}"
      case activity: InvestorDataExported =>
        s"exported investor data using ${activity.templateName}"
      case activity: EmailTemplateCreated =>
        s"created ${emailTemplateName(activity.emailEvent, flowTermCollection)} template ${activity.templateName}"
      case activity: EmailTemplateUpdated =>
        s"updated ${emailTemplateName(activity.emailEvent, flowTermCollection)} template ${activity.templateName}"
      case activity: EmailTemplateDeleted =>
        s"deleted ${emailTemplateName(activity.emailEvent, flowTermCollection)} template ${activity.templateName}"
      case activity: EmailTemplateSetAsDefault =>
        s"set ${emailTemplateName(activity.emailEvent, flowTermCollection)} template ${activity.templateName} as default"
      case activity: EmailTemplateRenamed =>
        s"renamed ${emailTemplateName(activity.emailEvent, flowTermCollection)} template ${activity.oldTemplateName} to ${activity.newTemplateName}"
      case _: SignatureDateFormatUpdated =>
        "updated signature date format"
      case activity: AdvisorJoined => s"from ${activity.entityName} advisor entity has joined your fund."
      case activity: AdvisorInvited =>
        s"invited ${getParticipantName(activity.advisor, userInfoMap)} to ${activity.entityName}"
      case activity: ResendAdvisorInvitation =>
        s"has resend invitation to ${getParticipantName(activity.advisor, userInfoMap)} in advisor entity ${activity.entityName}"
      case activity: RevokeAdvisorInvitation =>
        s"has revoked invitation of ${getParticipantName(activity.advisor, userInfoMap)} in advisor entity ${activity.entityName}"
      case activity: DisableAdvisorEntityCreateNewSubscription =>
        s"disabled new subscription creation for ${activity.entityName} advisor entity"
      case activity: EnableAdvisorEntityCreateNewSubscription =>
        s"enabled new subscription creation for ${activity.entityName} advisor entity"
      case activity: AdvisorEntityJoined =>
        s"${activity.entityName} advisor entity has joined your fund through advisor ${getParticipantName(activity.linker, userInfoMap)}'s acceptance to join fund"
      case activity: AdvisorEntityNameUpdated =>
        s"has updated advisor entity's name of ${activity.oldName} to ${activity.newName}"
      case activity: RiaOrderCreated =>
        s"from ${activity.entityName} invited ${getLpName(activity.lpId, lpInfoMap)} to the fund subscription"
      case activity: ConvertToRiaOrder =>
        s"synced ${flowTermCollection.termInPackageContext} of ${getLpName(activity.lpId, lpInfoMap)} to ${activity.entityName} (advisor entity)"
      case activity: UnlinkRiaOrder =>
        s"unlinked ${flowTermCollection.termInPackageContext} of ${getLpName(activity.lpId, lpInfoMap)} from ${activity.entityName} (advisor entity)"
      case activity: SideLetterVersionCreated =>
        if (activity.version == 1) {
          "created a first version of the Side Letter"
        } else {
          s"created a new version (${activity.version}) of the Side Letter"
        }
      case activity: SideLetterFilesUploaded =>
        s"uploaded ${StringUtils.pluralItem(activity.files.size, "document")} to Side Letter (version ${activity.version})"
      case activity: SideLetterFilesRemoved =>
        s"removed ${StringUtils.pluralItem(activity.files.size, "document")} from Side Letter (version ${activity.version})"
      case activity: MarkSideLetterAsAgreed =>
        s"mark the Side Letter (version ${activity.version}) as agreed"
      case activity: MarkSideLetterCompleted =>
        s"mark the Side Letter (version ${activity.version}) as complete"
      case activity: CustomColumnCreated =>
        val columnType = CustomColumnData.fromCustomDataType(activity.columnType).name.toLowerCase
        s"created a new $columnType column named ${activity.columnName}"
      case activity: CustomColumnDeleted =>
        val columnType = CustomColumnData.fromCustomDataType(activity.columnType).name.toLowerCase
        s"deleted a $columnType column named ${activity.columnName}"
      case activity: CustomColumnRenamed =>
        val columnType = CustomColumnData.fromCustomDataType(activity.columnType).name.toLowerCase
        s"renamed a $columnType column from ${activity.oldColumnName} to ${activity.newColumnName}"
      case activity: CustomColumnValueUpdated => renderCustomColumnValueUpdatedDescription(activity)
      case activity: TagListUpdated           => renderTagListUpdatedDescription(activity)
      case activity: AdminUpdateAllowFormEditPostSigning =>
        val updateKeyWord = if (activity.isEnabled) "enabled" else "disabled"
        s"$updateKeyWord ${AllowFormEditPostSigningUtils.FundSettingFeatureLabel}"
    }
  }

  def emailTemplateName(emailType: FundSubEvent, flowTermCollection: FlowTermCollection): String = {
    emailType match {
      case FundSubEvent.inviteLp             => "investor invitation"
      case FundSubEvent.remindLpCompleteForm => s"${flowTermCollection.termInDocumentContext} reminder"
      case FundSubEvent.countersigned        => "executed document distribution"
      case _                                 => "???"
    }
  }

  private def renderTagListUpdatedDescription(activity: TagListUpdated) = {
    val addedTagsString = if (activity.addedTags.nonEmpty) {
      s"added ${activity.addedTags.map(_.name).mkString(", ")}"
    } else {
      ""
    }
    val removedTagsString = if (activity.removedTags.nonEmpty) {
      s"removed ${activity.removedTags.map(_.name).mkString(", ")}"
    } else {
      ""
    }
    if (removedTagsString.isEmpty) {
      s"$addedTagsString to the tag list"
    } else if (addedTagsString.isEmpty) {
      s"$removedTagsString from the tag list"
    } else {
      s"$addedTagsString and $removedTagsString from the tag list"
    }
  }

  private def renderCustomColumnValueUpdatedDescription(activity: CustomColumnValueUpdated) = {
    val columnType = CustomColumnData.fromCustomDataType(activity.newData)
    val oldData = convertCustomDataToString(activity.oldData)
    val newData = convertCustomDataToString(activity.newData)
    columnType match {
      case CustomColumnData.DatetimeColumn | CustomColumnData.CurrencyColumn | CustomColumnData.SingleChoiceColumn |
          CustomColumnData.MetadataColumn =>
        val oldDataString = oldData.headOption.getOrElse("")
        val newDataString = newData.headOption.getOrElse("")
        if (oldDataString.isEmpty) {
          s"added $newDataString to ${activity.columnName} column"
        } else if (newDataString.isEmpty) {
          s"removed $oldDataString from ${activity.columnName} column"
        } else {
          s"updated value in ${activity.columnName} column from $oldDataString to $newDataString"
        }
      case CustomColumnData.MultipleChoiceColumn | CustomColumnData.ChecklistColumn =>
        val addedOptions = newData.diff(oldData)
        val removedOptions = oldData.diff(newData)
        val addedOptionsString = if (addedOptions.nonEmpty) {
          s"added ${addedOptions.mkString(", ")}"
        } else {
          ""
        }
        val removedOptionsString = if (removedOptions.nonEmpty) {
          s"removed ${removedOptions.mkString(", ")}"
        } else {
          ""
        }
        if (removedOptionsString.isEmpty) {
          s"$addedOptionsString to ${activity.columnName} column"
        } else if (addedOptionsString.isEmpty) {
          s"$removedOptionsString from ${activity.columnName} column"
        } else {
          s"$addedOptionsString and $removedOptionsString from ${activity.columnName} column"
        }
      case CustomColumnData.NoteColumn =>
        s"updated value in ${activity.columnName} column to ${newData.headOption.getOrElse("")}"
      case CustomColumnData.Unrecognized => ""
    }
  }

  private def convertCustomDataToString(customData: CustomData): Seq[String] = {
    customData.asMessage.sealedValue match {
      case data: SealedValue.DateTimeValue =>
        // Use UTC to format date same as in UI
        data.value.value.map(_.atOffset(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("MM/dd/yyyy"))).toSeq
      case data: SealedValue.CurrencyValue =>
        Seq(CurrencyUtils.convertToMoneyString(data.value.amount, data.value.currency))
      case data: SealedValue.SingleStringValue   => data.value.valueWithColor.map(_.content)
      case data: SealedValue.MultipleStringValue => data.value.valueWithColor.map(_.content)
      case data: SealedValue.StringValue         => Seq(data.value.value)
      case data: SealedValue.ChecklistValue      => data.value.value
      case data: SealedValue.MetadataValue       => Seq(data.value.value)
      case SealedValue.Empty                     => Seq.empty
    }
  }

  private def commentActivityDescription(a: CommentActivity) = {
    a.action match {
      case CommentAuditLogAction.COMMENT_AUDIT_LOG_ACTION_ADD           => "added a comment"
      case CommentAuditLogAction.COMMENT_AUDIT_LOG_ACTION_DELETE_REPLY  => "deleted a comment"
      case CommentAuditLogAction.COMMENT_AUDIT_LOG_ACTION_DELETE_THREAD => "delete a comment thread"
      case CommentAuditLogAction.COMMENT_AUDIT_LOG_ACTION_EDIT          => "edit a comment"
      case CommentAuditLogAction.COMMENT_AUDIT_LOG_ACTION_RESOLVE       => "marked a comment thread as resolved"
      case CommentAuditLogAction.COMMENT_AUDIT_LOG_ACTION_REOPEN        => "reopened a comment thread"
      case _: CommentAuditLogAction.Unrecognized                        => ""
    }
  }

  private def anchorPointActivityDescription(a: AnchorPointActivity) = {
    a.action match {
      case AnchorPointAuditLogAction.ANCHOR_POINT_AUDIT_LOG_ACTION_ASSIGN   => "assigned a comment"
      case AnchorPointAuditLogAction.ANCHOR_POINT_AUDIT_LOG_ACTION_REASSIGN => "reassigned a comment"
      case AnchorPointAuditLogAction.ANCHOR_POINT_AUDIT_LOG_ACTION_UNASSIGN => "unassigned a comment"
      case _: AnchorPointAuditLogAction.Unrecognized                        => ""
    }
  }

}
