// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service.fundsub.operation

import zio.{Task, ZIO}

import anduin.dataextract.models.DataExtractEventMessage
import anduin.fundsub.dataextract.FundSubDataExtractSchema.FundSubDataExtractRequestStatus
import anduin.fundsub.dataextract.service.FundSubSubdocDataExtractService
import anduin.id.dataextract.DataExtractProjectItemId
import anduin.kafka.KafkaService.RetryOptions
import anduin.kafka.{KafkaFiber, KafkaService, KafkaSimpleConsumer, Topic}
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FundSubOperationDataExtractWebhookEventConsumer(
  kafkaService: KafkaService,
  gondorBackendConfig: GondorBackendConfig,
  fundSubOperationDataExtractService: FundSubOperationDataExtractService,
  fundSubSubdocDataExtractService: FundSubSubdocDataExtractService
) extends KafkaFiber {

  private val dataExtractStatusChangeEventTopic = Topic[DataExtractProjectItemId, DataExtractEventMessage](
    gondorBackendConfig.dataExtractStatusChangeEventConfig.topic
  )

  private val dataExtractStatusChangeEventConsumer =
    KafkaSimpleConsumer[DataExtractProjectItemId, DataExtractEventMessage](
      kafkaService = kafkaService,
      topic = dataExtractStatusChangeEventTopic,
      consumerGroupName = s"${dataExtractStatusChangeEventTopic.name}-fundsub-operation",
      handler = (_, message) => dataExtractStatusChangeHandler(message),
      retryOpt = Some(RetryOptions.default)
    )

  override def start(): Task[Unit] = dataExtractStatusChangeEventConsumer.start()

  override def close(): Task[Unit] = dataExtractStatusChangeEventConsumer.close()

  private def dataExtractStatusChangeHandler(message: DataExtractEventMessage) = {
    message match {
      case projectItemDoneMessage: DataExtractEventMessage.ProjectItemDone =>
        for {
          _ <- ZIO.logInfo(
            s"Handling project item ${projectItemDoneMessage.projectItemId.idString} status changing to Done"
          )
          requests <- fundSubSubdocDataExtractService.getAllRequestsByDataExtractProjectItemId(
            projectItemDoneMessage.projectItemId
          )
          _ <- ZIOUtils.when(requests.isEmpty) {
            ZIO.logInfo(s"Ignored: no request found for project item ${projectItemDoneMessage.projectItemId.idString}")
          }
          _ <- ZIO.foreachDiscard(requests) { request =>
            for {
              latestLpRequestOpt <- fundSubSubdocDataExtractService.getRequestOptOfLpUnsafe(request.lpId)
              _ <-
                if (latestLpRequestOpt.forall(_.id != request.id)) {
                  ZIO.logInfo(s"Ignored: request ${request.id.idString} does not match LP's latest request")
                } else if (request.status != FundSubDataExtractRequestStatus.InProgress) {
                  ZIO.logInfo(s"Ignored: request ${request.id.idString} status is not InProgress")
                } else {
                  for {
                    _ <- ZIO.logInfo(s"Proceeded: marking request ${request.id.idString} as ready for review")
                    _ <- fundSubOperationDataExtractService.markRequestAsReadyForGpReview(
                      request.id,
                      projectItemDoneMessage.actor
                    )
                  } yield ()
                }
            } yield ()
          }
        } yield ()
    }
  }

}
