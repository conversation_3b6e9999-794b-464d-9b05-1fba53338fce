// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service.fundsub.operation

import java.time.Instant
import java.util.UUID
import scala.jdk.DurationConverters.*

import cats.data.Chain
import diffson.circe.*
import diffson.jsonpatch.Add
import diffson.jsonpointer.Pointer
import io.circe.Json
import io.circe.syntax.*
import io.github.arainko.ducktape.*
import sttp.model.MediaType
import zio.implicits.*
import zio.temporal.workflow.ZWorkflowStub
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.batchaction.{BatchActionFrontendTracking, BatchActionService, BatchActionType}
import anduin.dashboard.model.DashboardColumnData
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.docrequest.service.{DocSubmissionService, FormSubmissionService}
import anduin.documentcontent.spreadsheet.SpreadsheetUtils
import anduin.email.provider.{EmailProviderModel, EmailProviderService}
import anduin.encryption.StreamingEncryptionService
import anduin.fdb.client.FDBClient
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{FDBCluster, DefaultCluster, FDBCommonDatabase, FDBOperations, FDBRecordDatabase}
import anduin.forms.`import`.PredefinedAlias
import anduin.forms.endpoint.integration.GetFormVersionIntegrationConfigParams
import anduin.forms.engine.GaiaEngine.{EngineConfiguration, EngineContext}
import anduin.forms.engine.{GaiaEngine, GaiaState}
import anduin.forms.event.{FormEvent, Patches}
import anduin.forms.event.FormEvent.FormEventType
import anduin.forms.integration.{FundConfigData, SubFundConfig, WithFundSelection, WithoutFundSelection}
import anduin.forms.model.mapping.FormTemplateMappingModels.FormMappingType
import anduin.forms.model.mapping.FormTemplateMappingModels.MappingTarget.DataTemplateTarget
import anduin.forms.service.{DataTemplateService, FormIntegrationService, FormService, FormTemplateMappingService}
import anduin.forms.storage.{FormModelStoreOperations, FormTemplateMappingStoreOperations, FormVersionStoreOperations}
import anduin.forms.ui.types.SupportingFileGroupType
import anduin.forms.ui.{UIKey, WidgetType}
import anduin.forms.utils.StandardAliasUtils.IaCoverage
import anduin.forms.utils.{FormDataUtils, StandardAliasUtils}
import anduin.forms.version.FormVersionModel.FormType
import anduin.fundsub.amlkyc.amlcheck.FundSubAmlCheckService
import anduin.fundsub.comment.CommentServiceUtils
import anduin.fundsub.commitment.FundSubCommitmentHelper
import anduin.fundsub.copy.FundSubCopyConfigService
import anduin.fundsub.customdata.CustomDataService
import anduin.fundsub.dashboard.{FundSubDashboardService, LpInfoOperations, LpInfoStoreProvider}
import anduin.fundsub.data.lp.LpData
import anduin.fundsub.dataexport.{FundSubExportHelper, FundSubFileDownloadService, FundSubLpFileSettingOperations}
import anduin.fundsub.datalakeingestion.FundSubDataLakeIngestionService
import anduin.fundsub.datalakeingestion.model.UpdateOrAddCloseParams
import anduin.fundsub.email.{FundSubCustomSmtpServerConfig, FundSubEmailConstants}
import anduin.fundsub.endpoint.dashboard.{LpDashboardQueryParams, SortingCategory, SortingOrder}
import anduin.fundsub.endpoint.group.GetGroupMemberMode
import anduin.fundsub.endpoint.lp.NameEmailInfo
import anduin.fundsub.endpoint.operation.*
import anduin.fundsub.endpoint.operation.GetFormVersionDetailParams.FormFieldMode
import anduin.fundsub.endpoint.subscriptiondoc.{GetAllSubscriptionVersionBasicInfoParams, SaveSubscriptionFormDataParams}
import anduin.fundsub.form.FundSubFormFileOperations
import anduin.fundsub.form.utils.FundSubCommonUtils
import anduin.fundsub.fundclose.FundSubCloseService
import anduin.fundsub.group.{FundSubGroupMemberService, FundSubGroupService}
import anduin.fundsub.models.signature.FundSubSignatureRequestModels.FundSubSignatureRequestStatus
import anduin.fundsub.models.{FundSubLpModelStoreOperations, FundSubModelStoreOperations, FundSubSgwModelUtils}
import anduin.fundsub.rebac.PortalFundsubModel
import anduin.fundsub.service.*
import anduin.fundsub.signature.integration.FundSubSignatureIntegrationService
import anduin.fundsub.storageintegration.FundSubStorageIntegrationSubspace
import anduin.fundsub.subscriptiondoc.FundSubSubscriptionDocService
import anduin.fundsub.supportingdoc.FundSubSupportingDocInfoOperations
import anduin.fundsub.taxform.FundSubTaxFormConfig
import anduin.fundsub.updatelog.InvestorFormUpdateLogStoreOperations
import anduin.fundsub.user.FundSubUserService
import anduin.fundsub.view.FundSubViewService
import anduin.fundsub.{FundSubEmailDefaultTemplates, LpFormDataOperations}
import anduin.greylin.GreylinDataService
import anduin.greylin.operation.FundSubscriptionOperations
import anduin.id.environment.EnvironmentId
import anduin.id.form.{FormId, FormTemplateMappingVersionId, FormVersionId}
import anduin.id.fundsub.*
import anduin.id.role.portal.PortalSectionId
import anduin.linkrestriction.LinkRestrictionService
import anduin.lpprofile.service.LpProfileService
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.model.id.protobuf.DateTimeMappersInstance
import anduin.model.id.{DynamicFormId, FolderId, FundSubItoolLpLockIdFactory, TemporalWorkflowId}
import anduin.portaluser.PortalUserService
import anduin.protobuf.actionlogger.event.ActionEventFundSubIAConfigUpsert
import anduin.protobuf.external.squants.CurrencyMessage
import anduin.protobuf.fundsub.*
import anduin.protobuf.fundsub.models.*
import anduin.protobuf.fundsub.models.FundSubPublicModel.FundType
import anduin.protobuf.fundsub.models.lp.{FundSubLpLockStatusModel, FundSubLpModel, FundSubLpRestrictedModel}
import anduin.rebac.RebacStoreOperation
import anduin.service.{AuthenticatedRequestContext, GeneralServiceException}
import anduin.signature.integration.SignatureIntegrationService
import anduin.storageservice.common.FileContentOrigin
import anduin.temporal.TemporalEnvironment
import anduin.user.UserService
import anduin.utils.{ScalaUtils, StringUtils}
import anduin.workflow.fundsub.dashboard.SyncDashboardDataParams
import anduin.workflow.fundsub.dashboard.impl.FundSubSyncDashboardWorkflowImpl
import anduin.workflow.fundsub.formcomment.ExportCommentParams
import anduin.workflow.fundsub.formcomment.impl.CommentExportWorkflowImpl
import com.anduin.stargazer.dynamicform.logic.FormLogicInstance
import com.anduin.stargazer.external.base64.Base64
import com.anduin.stargazer.service.actionlogger.ActionLoggerService
import com.anduin.stargazer.service.api.DynamicFormService
import com.anduin.stargazer.service.dynamicform.DynamicFormStorageService
import com.anduin.stargazer.service.fundsub.FundSubContactService
import com.anduin.stargazer.service.fundsub.free.module.{ManageFundSubAdminM, ManageFundSubLpM}
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.ses.SesOperationService
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FundSubOperationService(
  portalUserService: PortalUserService,
  fundSubPortalPermissionService: FundSubPortalPermissionService,
  manageFundSubAdminM: ManageFundSubAdminM,
  dynamicFormService: DynamicFormService,
  fundSubLpTagUtilService: FundSubLpTagUtilService,
  docSubmissionService: DocSubmissionService,
  formSubmissionService: FormSubmissionService,
  formIntegrationService: FormIntegrationService,
  fundSubDashboardService: FundSubDashboardService,
  fundSubUserService: FundSubUserService,
  fundSubContactService: FundSubContactService,
  fundSubCloseService: FundSubCloseService,
  fundSubFileDownloadService: FundSubFileDownloadService,
  fundSubPermissionService: FundSubPermissionService,
  fundSubGroupMemberService: FundSubGroupMemberService,
  fundSubGroupService: FundSubGroupService,
  customDataService: CustomDataService,
  fundSubViewService: FundSubViewService,
  formTemplateMappingService: FormTemplateMappingService,
  dataTemplateService: DataTemplateService,
  linkRestrictionService: LinkRestrictionService,
  lpProfileService: LpProfileService,
  natsNotificationService: NatsNotificationService,
  fundSubCopyConfigService: FundSubCopyConfigService,
  commentServiceUtils: CommentServiceUtils,
  fundSubAmlCheckService: FundSubAmlCheckService,
  temporalEnvironment: TemporalEnvironment,
  greylinDataService: GreylinDataService,
  actionLoggerService: ActionLoggerService,
  batchActionService: BatchActionService,
  encryptionService: StreamingEncryptionService,
  sesOperationService: SesOperationService,
  emailProviderService: EmailProviderService
)(
  using val userProfileService: UserProfileService,
  val formService: FormService,
  val fileService: FileService,
  val fundSubFormIntegrationService: FundSubFormIntegrationService,
  val signatureIntegrationService: SignatureIntegrationService,
  val userService: UserService,
  val fundSubSignatureIntegrationService: FundSubSignatureIntegrationService,
  val fundSubSubscriptionDocService: FundSubSubscriptionDocService,
  val lpModule: ManageFundSubLpM,
  val fundSubDataLakeIngestionService: FundSubDataLakeIngestionService,
  val dynamicFormStorageService: DynamicFormStorageService,
  val fundSubGreylinDataService: FundSubGreylinDataService
) extends FundSubFormFileOperations {

  def createFundSub(
    params: CreateFundSubParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext] = None
  ): Task[CreateFundSubResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is creating a new fund sub for entity ${params.entityId}")
      _ <- portalUserService.validateWritePermission(actor, PortalSectionId.FundSub)
      _ <- validateFeatureSwitch(params.featureSwitch)
      fundSubId <- manageFundSubAdminM.createFundSub(
        params,
        actor,
        httpContext
      )
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      userIds <- ZIO.foreach(params.initialAdmins.toList) { adminInfo =>
        for {
          groupIdOpt <- fundSubGroupService.getGroupByOldRoleUnsafe(
            fundSubId = fundSubId,
            adminInfo.role
          )
          userId <- fundSubUserService.createFundManagerUserIfNeeded(
            fundSubId = fundSubId,
            email = adminInfo.email,
            firstName = adminInfo.firstName,
            lastName = adminInfo.lastName,
            inviterOpt = httpContext.map(_.actor.userInfo.emailAddressStr)
          )
          _ <- ZIOUtils.traverseOptionUnit(groupIdOpt) { groupId =>
            fundSubGroupMemberService.inviteGroupMemberUnsafe(
              groupId,
              userId,
              actor,
              httpContext
            )
          }
        } yield userId
      }
      // Create fund contact group
      _ <- ZIOUtils.traverseOption(userIds.headOption) { userId =>
        fundSubContactService.createFundGroup(
          fundSubId,
          userId,
          params.fundName
        )
      }
      fsRestrictedModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
      }
      allTemplateVersions <- formTemplateMappingService.getAllTemplateInfoUnsafe(params.formMappingVersionIds)
      _ <- FundSubOperationUtils.addNewFundDataToDataLake(
        params = params,
        fundSubId = fundSubId,
        // pass this explicitly because we might have generated the sub-fund IDs in manageFundSubAdminM.createFundSub
        investmentFunds = fsRestrictedModel.investmentFunds,
        inactiveNotificationSettingOpt = fsRestrictedModel.inactiveNotificationSetting,
        admins = userIds,
        dataTemplateVersions = allTemplateVersions
      )
      _ <- FundSubOperationUtils.addNewFundDataToGreylin(
        params = params,
        fundSubId = fundSubId,
        // pass this explicitly because we might have generated the sub-fund IDs in manageFundSubAdminM.createFundSub
        investmentFunds = fsRestrictedModel.investmentFunds,
        inactiveNotificationSettingOpt = fsRestrictedModel.inactiveNotificationSetting,
        admins = userIds,
        dataTemplateVersions = allTemplateVersions
      )
      _ <- ZIOUtils.when(params.enableAdvancedDashboard) {
        fundSubCloseService.getFundSubCloseDataInternal(fundSubId).flatMap { closeInfo =>
          ZIO.foreachDiscard(closeInfo.closeInfo) { close =>
            val params = UpdateOrAddCloseParams(
              id = close.fundSubCloseId,
              name = close.name,
              customCloseId = close.customCloseId,
              targetClosingDate = close.closingDate.map(_.toLocalDate)
            )
            fundSubDataLakeIngestionService.sendNewUpdate(params)
          }
        }
      }

      // Update aml check column
      _ <- fundSubAmlCheckService.modifyAmlCheckDashboardColumn(fundSubId, params.featureSwitch.enableAmlCheck)

      // Check with IA template
      _ <- checkIaTemplateCoverage(
        actor,
        params.featureSwitch.enableLpProfile,
        fundSubId,
        params.fundName,
        params.formVersionOpt
      )
      _ <- sendFundSubIAConfigActionEventLog(
        actor,
        fundSubId,
        params.featureSwitch,
        params.formVersionOpt,
        httpContext
      )
      _ <- ZIO.logInfo(s"$fundSubId created successfully for ${params.entityId}")
    } yield CreateFundSubResponse(fundSubId)
  }

  def editFundSub(
    params: EditFundSubParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor is editing fund sub ${params.fundSubId}")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        params.fundSubId,
        PortalFundsubModel.Permission.Write
      )
      _ <- validateFeatureSwitch(params.featureSwitch)
      oldFormVersionOpt <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
        _.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(params.fundSubId)).map(_.formVersions.headOption)
      }
      oldFeatureSwitchOpt <- FDBRecordDatabase
        .transact(FundSubModelStoreOperations.Production) {
          _.getFundSubPublicModel(params.fundSubId)
        }
        .map(_.featureSwitch)
      _ <- manageFundSubAdminM.editFundSub(params, actor)

      // Update aml check config
      _ <- fundSubAmlCheckService.modifyAmlCheckDashboardColumn(params.fundSubId, params.featureSwitch.enableAmlCheck)

      // Update advisor group column
      riaFeatureSwitchUpdated = oldFeatureSwitchOpt.exists(_.enableRia != params.featureSwitch.enableRia)
      _ <- ZIO.whenDiscard(riaFeatureSwitchUpdated) {
        fundSubViewService.updateAdvisorGroupDashboardColumn(
          fundSubId = params.fundSubId,
          isEnabled = params.featureSwitch.enableRia
        )
      }

      // Check with IA template
      _ <- ZIO.when(
        !oldFeatureSwitchOpt.map(_.enableLpProfile).contains(params.featureSwitch.enableLpProfile)
          || params.formVersionOpt.map(_.id) != oldFormVersionOpt.map(_.id)
      ) {
        checkIaTemplateCoverage(
          actor,
          params.featureSwitch.enableLpProfile,
          params.fundSubId,
          params.fundName,
          params.formVersionOpt
        )
      }
      // Send activity log
      _ <- ZIO.when(
        !oldFeatureSwitchOpt.map(_.enableLpProfile).contains(params.featureSwitch.enableLpProfile) ||
          !oldFeatureSwitchOpt.map(_.enableAutoPrefillForLp).contains(params.featureSwitch.enableAutoPrefillForLp)
          || params.formVersionOpt.map(_.id) != oldFormVersionOpt.map(_.id)
      ) {
        sendFundSubIAConfigActionEventLog(
          actor,
          params.fundSubId,
          params.featureSwitch,
          params.formVersionOpt,
          None
        )
      }
    } yield ()
  }

  def archiveFundSub(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.id} is archiving fundsub $fundSubId")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Write
      )
      _ <- manageFundSubAdminM.archiveFundSub(fundSubId, actor)
    } yield ()
  }

  def editFundType(
    fundSubId: FundSubId,
    fundType: FundType,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is updating fund type of $fundSubId to $fundType")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Write
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubPublicModel(fundSubId)(_.copy(fundType = fundType))
      }
    } yield ()
  }

  def getFundSubLpInfo(
    fundSubId: FundSubId,
    actor: UserId,
    includeImportIdInfo: Boolean = false
  ): Task[GetFundSubLpInfoResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting LP info for fund ${fundSubId.idString}")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Read
      )
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      isFundAdmin <- fundSubPermissionService.checkIfUserHasAdminRoleR(fundSubId, actor)
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
      }
      isUsingNewForm = adminResModel.formVersions.nonEmpty
      formIdOpt = adminResModel.formIds.headOption
      activeOldFormInfoOpt <- formIdOpt.map { formId =>
        dynamicFormStorageService.getForm(formId).map(_.s3FormInfoOpt)
      } getOrElse ZIO.attempt(None)
      lpIdsToGet = adminResModel.lpIds.toSet
      formVersionMap <-
        if (isUsingNewForm) {
          FundSubOperationUtils
            .getAllActiveFormVersions(
              adminResModel.formVersions.headOption.map(_.id),
              lpIdsToGet.toSeq
            )
            .map(_._2.toMap)
        } else {
          ZIO.attempt(Map.empty[FundSubLpId, FormVersionForFundSetup])
        }
      defaultLpLockMap = getDefaultLpLockMap(formVersionMap)
      rawLps <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        for {
          lps <- ops.getAllFundSubLpModels(fundSubId)
          lpIds = lps.map(_.fundSubLpId)
          lpNotes <- ops.getFundSubLpNoteModels(lpIds)
          existingLpLocks <- ops.getFundSubLpLockStatusModels(lpIds)
          defaultLpLocks <- ops.updateFundSubLpLockStatusModelsWithDefaults(defaultLpLockMap -- existingLpLocks.keySet)
          lpLocks = existingLpLocks ++ defaultLpLocks
        } yield lps.map { lp =>
          val lpLockOpt = lpLocks.get(lp.fundSubLpId)
          val lpNoteOpt = lpNotes.get(lp.fundSubLpId)

          (lp, lpLockOpt, lpNoteOpt)
        }
      }
      lpImportInvestorIdMap <-
        if (includeImportIdInfo) {
          FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
            ops
              .getAllFundSubLpRestrictedModels(fundSubId)
              .map(
                _.map(lpRestrictedModel =>
                  lpRestrictedModel.fundSubLpRestrictedId.parent -> lpRestrictedModel.importInvestorId
                ).toMap
              )
          }
        } else {
          ZIO.attempt(Map.empty[FundSubLpId, String])
        }
      userInfoMap <- {
        val allUserIds = rawLps.flatMap { case (lpModel, lpLockOpt, lpNoteOpt) =>
          (lpModel.collaborators :+ lpModel.mainLp) ++ Seq(
            lpLockOpt.flatMap(_.lastUpdatedBy),
            lpNoteOpt.map(_.lastUpdatedBy)
          ).flatten
        }.toSet
        ZIO
          .foreach(allUserIds.sliding(1000, 1000).toSeq) { userIds =>
            userProfileService.batchGetUserInfos(userIds)
          }
          .map { userInfoMaps =>
            userInfoMaps.reduceOption(_ ++ _).getOrElse(Map.empty)
          }
      }
    } yield {
      val filteredRawLps = rawLps.filter { case (lpModel, _, _) =>
        lpIdsToGet.contains(lpModel.fundSubLpId)
      }
      val lpInfos = for {
        (lpModel, lpLockOpt, lpNoteOpt) <- filteredRawLps
        flowState <- lpModel.lpState
        mainLpInfo <- userInfoMap.get(lpModel.mainLp)
      } yield {
        val name = if (!StringUtils.isEmptyOrWhitespace(lpModel.firmName)) {
          lpModel.firmName
        } else if (!StringUtils.isEmptyOrWhitespace(mainLpInfo.fullNameString)) {
          mainLpInfo.fullNameString
        } else {
          mainLpInfo.emailAddressStr
        }
        val contact = if (!isFundAdmin) {
          LpContact(
            name = DeidentificationUtils.deidentifyName(name),
            mainLpName = DeidentificationUtils.deidentifyName(mainLpInfo.fullNameString),
            email = DeidentificationUtils.deidentifyEmail(mainLpInfo.emailAddressStr)
          )
        } else {
          LpContact(
            name = name,
            mainLpName = mainLpInfo.fullNameString,
            email = mainLpInfo.emailAddressStr
          )
        }
        val collaborators = lpModel.collaborators.flatMap { userId =>
          userInfoMap.get(userId).map { userInfo =>
            val info = if (!isFundAdmin) {
              NameEmailInfo(
                DeidentificationUtils.deidentifyEmail(userInfo.emailAddressStr),
                DeidentificationUtils.deidentifyName(userInfo.firstName),
                DeidentificationUtils.deidentifyName(userInfo.lastName)
              )
            } else {
              NameEmailInfo(
                userInfo.emailAddressStr,
                userInfo.firstName,
                userInfo.lastName
              )
            }
            userId -> info
          }
        }
        val lockInfoOpt = FundSubOperationUtils.toLpLockStatusInfo(lpLockOpt, userInfoMap)
        val noteInfoOpt = FundSubOperationUtils.toLpNoteInfo(lpNoteOpt, userInfoMap)

        LpInfo(
          contact,
          lpModel.mainLp,
          flowState.invitedAt,
          flowState.getLpStatus,
          flowState.lp,
          lpModel.formChangeInfo,
          formVersionMap.get(lpModel.fundSubLpId),
          collaborators,
          lpImportInvestorIdMap.getOrElse(lpModel.fundSubLpId, ""),
          lockInfoOpt,
          noteInfoOpt,
          lpModel.orderType
        )
      }
      GetFundSubLpInfoResponse(
        lpInfos,
        adminResModel.formVersions.headOption.map(_.id),
        activeOldFormInfoOpt
      )
    }
  }

  private def getDefaultLpLockMap(formVersionMap: Map[FundSubLpId, FormVersionForFundSetup]) = {
    for {
      (lpId, formVersion) <- formVersionMap
      defaultLock <- formVersion.formType match {
        case FormType.Validation =>
          Some(
            FundSubLpLockStatusModel(
              fsLpLockId = FundSubItoolLpLockIdFactory.unsafeRandomId(lpId),
              lastUpdatedAt = Some(Instant.now()),
              isLocked = true
            )
          )
        case _ => None
      }
    } yield lpId -> defaultLock
  }

  def getFundSubTemplateMappingsInfo(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[GetFundSubTemplateMappingsInfoResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting template mappings info for fund ${fundSubId.idString}")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Read
      )
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
      }
      lpIds = adminResModel.lpIds
      isUsingNewForm = adminResModel.formVersions.nonEmpty
      resp <-
        if (isUsingNewForm) {
          for {
            (activeVersion, lpsWithVersion) <- FundSubOperationUtils.getAllActiveFormVersions(
              adminResModel.formVersions.headOption.map(_.id),
              lpIds
            )
            activeVersions = (lpsWithVersion.map(_._2) :+ activeVersion)
              .distinctBy(_.formVersionId)
              .sortBy(_.versionNumber)
              .reverse
            // Note: because formVersions can have duplicate versions, we combine all the settings of the same versions together
            // This is a tech debt and will be remove in the future when we separate the mapping settings from the form versions list
            versionMappings = adminResModel.formVersions.groupMapReduce(_.id)(v =>
              v.importMappingIds -> v.exportMappingIds
            ) { case ((i, e), (ni, nx)) => (i ++ ni).distinct -> (e ++ nx).distinct }
            versionsWithMappingSetting <- ZIO
              .foreachPar(activeVersions) { version =>
                formTemplateMappingService
                  .getAllFormVersionMappingsUnsafe(version.formVersionId, includeVersions = true)
                  .map { mappings =>
                    val importMappings = mappings
                      .filter { case (mapping, _) =>
                        mapping.mappingType == FormMappingType.Import
                        && ScalaUtils.isMatch[DataTemplateTarget](mapping.mappingTarget)
                      }
                      .map { case (mapping, mappingVersions) =>
                        DataTemplateMappingWithVersions(mapping, mappingVersions)
                      }
                    val exportMappings = mappings
                      .filter { case (mapping, _) =>
                        mapping.mappingType == FormMappingType.Export
                        && ScalaUtils.isMatch[DataTemplateTarget](mapping.mappingTarget)
                      }
                      .map { case (mapping, mappingVersions) =>
                        DataTemplateMappingWithVersions(mapping, mappingVersions)
                      }
                    FormVersionWithMappingSettings(
                      formVersion = version,
                      lpIds = lpsWithVersion.filter(_._2.formVersionId == version.formVersionId).map(_._1),
                      enabledImportMappings = versionMappings.get(version.formVersionId).map(_._1).getOrElse(Seq.empty),
                      enabledExportMappings = versionMappings.get(version.formVersionId).map(_._2).getOrElse(Seq.empty),
                      importMappings = importMappings,
                      exportMappings = exportMappings
                    )
                  }
              }
              .withParallelism(4)
            relatedTemplateIds <- ZIO.attempt(versionsWithMappingSetting.flatMap { versionSetting =>
              (versionSetting.importMappings ++ versionSetting.exportMappings).map(_.mapping.templateIdUnsafe)
            }.distinct)
            dataTemplates <- ZIO
              .foreachPar(relatedTemplateIds) { templateId =>
                dataTemplateService.getTemplateVersionsUnsafe(templateId).map { case (model, versions) =>
                  DataTemplateWithVersions(model, versions.map(_.to[DataTemplateVersionMetadata]))
                }
              }
              .withParallelism(4)
          } yield {
            GetFundSubTemplateMappingsInfoResponse(
              activeVersionOpt = Some(activeVersion.formVersionId),
              versions = versionsWithMappingSetting,
              relatedDataTemplates = dataTemplates
            )
          }
        } else {
          ZIO.succeed(GetFundSubTemplateMappingsInfoResponse())
        }
    } yield resp
  }

  def updateFormVersionTemplateMappings(
    fundSubId: FundSubId,
    formVersionId: FormVersionId,
    importMappingVersionIds: Seq[FormTemplateMappingVersionId],
    exportMappingVersionIds: Seq[FormTemplateMappingVersionId],
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"${actor.id} is update template mappings info for fund ${fundSubId.idString}, version ${formVersionId.idString}"
      )
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Write
      )
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
      }
      activeVersion <- ZIOUtils.optionToTask(
        adminResModel.formVersions.headOption,
        GeneralServiceException("This operation supports new form only")
      )
      _ <- ZIOUtils.validate(formVersionId.parent == activeVersion.id.parent)(
        GeneralServiceException(
          s"Form version ${formVersionId.idString} doesn't belong to the fund's form ${activeVersion.id.parent.idString}"
        )
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.updateFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))(resModel =>
          resModel.copy(formVersions = resModel.formVersions.map { version =>
            if (version.id == formVersionId) {
              version.copy(importMappingIds = importMappingVersionIds, exportMappingIds = exportMappingVersionIds)
            } else {
              version
            }
          })
        )
      )
      _ <- ZIO.when(formVersionId == activeVersion.id) {
        for {
          _ <- FundSubOperationUtils.updateMappingTemplatesInDataLake(
            fundId = fundSubId,
            formMappingVersionIds = importMappingVersionIds ++ exportMappingVersionIds,
            existingMappingVersionIds = activeVersion.importMappingIds ++ activeVersion.importMappingIds
          )(
            formTemplateMappingService,
            fundSubDataLakeIngestionService,
            userProfileService,
            fileService
          )
          _ <- FundSubOperationUtils.updateMappingTemplatesInGreylin(
            fundId = fundSubId,
            formMappingVersionIds = importMappingVersionIds ++ exportMappingVersionIds,
            existingMappingVersionIds = activeVersion.importMappingIds ++ activeVersion.importMappingIds
          )(
            using formTemplateMappingService,
            fundSubGreylinDataService
          )
        } yield ()
      }
    } yield ()
  }

  def getRemovedInvestors(fundSubId: FundSubId, actor: UserId): Task[GetRemovedInvestorResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting removed investors of ${fundSubId.idString}")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Read
      )
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
      }
      // We don't do batch query here because we assume there are not a lot of removed investors
      removedInvestors <- ZIO.foreach(adminResModel.removedLps) { lpId =>
        for {
          lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
            ops.getFundSubLpModel(lpId)
          }
          userInfo <- userProfileService.getUserInfo(lpModel.mainLp)
        } yield {
          val name = if (!StringUtils.isEmptyOrWhitespace(lpModel.firmName)) {
            lpModel.firmName
          } else if (!StringUtils.isEmptyOrWhitespace(userInfo.fullNameString)) {
            userInfo.fullNameString
          } else {
            userInfo.emailAddressStr
          }
          val contact = LpContact(
            name = name,
            mainLpName = userInfo.fullNameString,
            email = userInfo.emailAddressStr
          )
          lpId -> contact
        }
      }
    } yield GetRemovedInvestorResponse(removedInvestors.toMap)
  }

  def getDashboardConfig(
    params: GetDashboardConfigParams,
    actor: UserId
  ): Task[GetDashboardConfigResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting dashboard config of fund ${params.fundSubId.idString}")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        params.fundSubId,
        PortalFundsubModel.Permission.Read
      )
      given FDBCluster = anduin.fdb.record.ReadOnlyCluster
      fundSubModel <- FDBCommonDatabase().read(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(params.fundSubId)
      )
    } yield GetDashboardConfigResponse(
      config = fundSubModel.dashboardConfig.getOrElse(DashboardConfig()),
      fundType = fundSubModel.fundType
    )
  }

  def saveDashboardConfig(
    params: SaveDashboardConfigParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is saving dashboard config of fund ${params.fundSubId.idString}")
      _ <- ZIOUtils.validate(params.config.enableStandardDashboard || params.config.enableAdvancedDashboard) {
        new RuntimeException("No dashboard is turned on")
      }
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        params.fundSubId,
        PortalFundsubModel.Permission.Write
      )
      fsPublicModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
        _.updateFundSubPublicModel(params.fundSubId) { model =>
          model.copy(dashboardConfig = Some(params.config))
        }
      }
      fsAdminRes <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(params.fundSubId))
      )
      masterDashboardId <- fsAdminRes.dashboardId.fold(
        for {
          dashboardId <- fundSubViewService.createMasterViewUnsafe(
            fundSubId = params.fundSubId,
            excludeColumnIds = List(
              Option.unless(fsPublicModel.featureSwitch.exists(_.enabledCustomLpId))(
                DashboardColumnData.customLpIdColumn.id
              ),
              Option.unless(fsPublicModel.featureSwitch.exists(_.enableImportFromFundData))(
                DashboardColumnData.clientColumn.id
              ),
              Option.unless(fsPublicModel.featureSwitch.exists(_.enableRia))(
                DashboardColumnData.advisorGroupColumn.id
              ),
              Option.when(fsPublicModel.dataExtractionConfig.exists(_.isEnabled))(
                DashboardColumnData.dataExtractionColumn.id
              )
            ).flatten,
            investmentFunds = fsAdminRes.investmentFunds,
            actor = actor
          )
          _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
            _.updateFundSubAdminRestrictedModel(FundSubAdminRestrictedId(params.fundSubId))(
              _.copy(dashboardId = Some(dashboardId))
            )
          )
        } yield dashboardId
      )(ZIO.succeed(_))
      _ <- fundSubDashboardService.updateDashboardColumnFromConfigs(
        masterDashboardId,
        getFormFieldConfigsTask = ZIO.attempt(params.config.formFieldsConfigs),
        getCustomDataConfigsTask = customDataService.getAllCustomDataColumns(params.fundSubId),
        reorderAvailableFormFieldColumnId = true,
        investmentFunds = fsAdminRes.investmentFunds
      )
    } yield ()
  }

  def getInvestorDebugData(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[GetInvestorDebugDataResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting debug data for investor ${lpId.idString}")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        lpId.parent,
        PortalFundsubModel.Permission.Read
      )
      (fsModel: FundSubPublicModel, lpResModel: FundSubLpRestrictedModel) <- FDBRecordDatabase.transact(
        FDBOperations[(FundSubLpModelStoreOperations, FundSubModelStoreOperations)].Production
      ) { case (lpOps, fsOps) =>
        for {
          fsModel <- fsOps.getFundSubPublicModel(lpId.parent)
          lpModel <- lpOps.getFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId))
        } yield (fsModel, lpModel)
      }
      getVersionsResp <- fundSubSubscriptionDocService.getAllSubscriptionVersionBasicInfo(
        GetAllSubscriptionVersionBasicInfoParams(lpId),
        actor
      )
      versionsWithRequestId <- ZIO.foreach(getVersionsResp.versions) { version =>
        for {
          requests <- fundSubSignatureIntegrationService.getSubscriptionDocSignatureRequestsMetadata(
            lpId,
            statuses = Set(FundSubSignatureRequestStatus.InProgress, FundSubSignatureRequestStatus.Completed),
            version.versionIndex
          )
        } yield SubscriptionVersionWithSignatureRequestId(
          version,
          requests.map(_.requestId)
        )
      }
    } yield {
      GetInvestorDebugDataResponse(
        lpFlowType = fsModel.lpFlowType,
        versions = versionsWithRequestId,
        subscriptionDocSignatureRequestIds = lpResModel.subscriptionDocSignatureRequestIds,
        counterSignatureRequestIds = lpResModel.counterSignatureRequestIds,
        additionalSignatureRequestIds = lpResModel.additionalSignatureRequestIds,
        completedSignatureRequestIds = lpResModel.completedSignatureRequestIds,
        gpSignatureFields = lpResModel.gpSignatureBlocks,
        uniqueEmailSigningRoles = lpResModel.uniqueEmailSigningRoles
      )
    }
  }

  def syncFormDataToDashboard(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is syncing form data to fund sub for investor ${lpId.idString}")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        lpId.parent,
        PortalFundsubModel.Permission.Write
      )
      lpFormInfo <- FundSubCommonUtils.getLpFormInfo(lpId, actor)
      _ <- lpModule.syncFormDataToDashboard(
        lpId,
        lpFormInfo,
        actor
      )
      _ <- ZIO.logInfo(s"${actor.id} finish syncing form data to fund sub for investor ${lpId.idString}")
    } yield ()
  }

  def getInvestorFormUpdateLogs(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[GetInvestorFormUpdateLogResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting investor form update logs for ${fundSubId.idString}")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Read
      )
      logs <- FDBRecordDatabase.transact(InvestorFormUpdateLogStoreOperations.Production) { ops =>
        ops.getByFundSubId(fundSubId)
      }
      formIdOpt = logs.flatMap(_.formVersionId).map(_.parent).headOption
      formVersions <- formIdOpt.fold(ZIO.attempt(Seq.empty[FormVersionForFundSetup])) { formId =>
        FundSubOperationUtils.getAllFormVersions(formId)
      }
      userIds = logs.map(_.actor).distinct
      userInfos <- ZIO.foreach(userIds.grouped(1000).toSeq) { ids =>
        userProfileService.batchGetUserInfos(ids.toSet)
      }
    } yield {
      val refinedLogs = logs.map { log =>
        val formVersionOpt = log.formVersionId.flatMap { versionId =>
          formVersions.find(_.formVersionId == versionId)
        }
        InvestorFormUpdateLog(
          log.investorFormUpdateLogId,
          log.actor,
          log.at,
          log.lpIds,
          log.s3FormChangeInfo,
          formVersionOpt
        )
      }
      GetInvestorFormUpdateLogResponse(
        updateLogs = refinedLogs,
        userInfoMap = userInfos.flatten.toMap
      )
    }
  }

  def getInvestorFirstFormVersion(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[GetInvestorFirstFormVersionResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting investor first form version for ${lpId.idString}")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        lpId.parent,
        PortalFundsubModel.Permission.Read
      )
      lpRes <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.getFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId))
      }
      versionId <- ZIOUtils.optionToTask(
        lpRes.formIds.headOption.flatMap(_.asNewLpFormId),
        GeneralServiceException("No form version found")
      )
      version <- FDBRecordDatabase.transact(FormVersionStoreOperations.Production) { ops =>
        ops.getVersion(versionId.parent)
      }
    } yield {
      val formVersion = FormVersionForFundSetup(
        formVersionId = versionId.parent,
        formName = "",
        versionName = version.name,
        createdAt = version.createdAt,
        author = version.author,
        versionNumber = version.versionNumber,
        formType = version.formType
      )
      GetInvestorFirstFormVersionResponse(formVersion)
    }
  }

  def getFormForFundSetup(actor: UserId): Task[GetFormForFundSetupResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting forms for fund setup")
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      forms <- FDBRecordDatabase.transact(FormModelStoreOperations.Production) { ops =>
        ops.getAll
      }
    } yield {
      val allForms = forms
        .filter(!_._2.trackingStatus.isArchived)
        .sortBy(_._2.createdAt.map(_.toEpochMilli).getOrElse(0L))
        .reverse
        .map { case (formId, formModel) =>
          formId -> formModel.name
        }
      GetFormForFundSetupResponse(allForms)
    }
  }

  def getFormVersionForFundSetup(formId: FormId, actor: UserId): Task[GetFormVersionForFundSetupResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting versions of form ${formId.idString} for fund setup")
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      versions <- FundSubOperationUtils.getAllFormVersions(formId)
    } yield GetFormVersionForFundSetupResponse(versions)
  }

  def getFormVersionDetail(
    formVersionId: FormVersionId,
    formFieldMode: FormFieldMode,
    actor: UserId
  ): Task[GetFormVersionDetailResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting version detail of ${formVersionId.idString}")
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      (formModel, formVersionModel) <- FDBRecordDatabase.transact(
        FDBOperations[(FormModelStoreOperations, FormVersionStoreOperations)].Production
      ) { case (formOps, versionOps) =>
        for {
          formModel <- formOps.get(formVersionId.parent)
          formVersionModel <- versionOps.getVersion(formVersionId)
        } yield formModel -> formVersionModel
      }

      (subFundConfigs, isAsaTemplateAvailable) <- formFieldMode match {
        case GetFormVersionDetailParams.MoneyFields =>
          for {
            resp <- formIntegrationService.getFormVersionIntegrationConfig(
              GetFormVersionIntegrationConfigParams(formVersionId),
              actor
            )
            isAsaTemplateAvailable <- formService.checkAsaAvailability(formVersionId)
          } yield {
            val subFundConfigs = resp.config.fundEnvironment.fold(Seq.empty[SubFundConfig]) { configData =>
              configData.fundConfig match {
                case FundConfigData.Empty           => Seq.empty
                case WithoutFundSelection(subFunds) => subFunds
                case WithFundSelection(selections)  => selections.flatMap(_.subFunds)
              }
            }
            subFundConfigs -> isAsaTemplateAvailable
          }

        case _ => ZIO.succeed(Seq.empty -> false)
      }
    } yield {
      val version = FormVersionForFundSetup(
        formVersionId = formVersionId,
        formName = formModel.name,
        versionName = formVersionModel.name,
        createdAt = formVersionModel.createdAt,
        author = formVersionModel.author,
        versionNumber = formVersionModel.versionNumber,
        formType = formVersionModel.formType
      )
      GetFormVersionDetailResponse(
        version = version,
        subFundConfigs = subFundConfigs,
        isAsaTemplateAvailable = isAsaTemplateAvailable
      )
    }
  }

  def getFormVersionForDashboardConfig(
    formVersionId: FormVersionId,
    actor: UserId
  ): Task[GetFormVersionForDashboardConfigResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting form version ${formVersionId.idString} for dashboard config")
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      formData <- formService.getFormDataUnsafe(formVersionId)
    } yield {
      GetFormVersionForDashboardConfigResponse(
        formUiSchema = formData.form.defaultUiSchema,
        allFields = FormDataUtils.traverseAndExtract(formData.form, (_, _) => true).map(_._1.name)
      )
    }
  }

  def getTaxFormConfig(
    actor: UserId
  ): Task[GetTaxFormConfigResponse] = {
    for {
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      config <- FundSubTaxFormConfig.getTaxFormConfig
      taxForms <- ZIO.foreach(config.formIds) { formId =>
        dynamicFormStorageService.getForm(formId).map { formModel =>
          FundTaxForm(
            formId,
            formModel.formName,
            formModel.s3FormInfoOpt
          )
        }
      }
    } yield GetTaxFormConfigResponse(taxForms)
  }

  def getGlobalTaxFormVersions(actor: UserId): Task[GetGlobalTaxFormVersionsResponse] = {
    for {
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      config <- FundSubTaxFormConfig.getTaxFormConfig
      versionModels <- ZIO.foreach {
        config.formVersionIds.groupBy(_.parent).toSeq
      } { case (formId, versionIds) =>
        FundSubOperationUtils
          .getAllFormVersions(formId)
          .map {
            _.filter { version =>
              versionIds.contains(version.formVersionId)
            }
          }
      }
    } yield GetGlobalTaxFormVersionsResponse(versionModels.flatten)
  }

  def updateTaxFormConfig(formIds: Seq[DynamicFormId], actor: UserId): Task[Unit] = {
    for {
      _ <- portalUserService.validateWritePermission(actor, PortalSectionId.FundSub)
      _ <- ZIO.logInfo(s"User $actor is updating tax form list")
      _ <- FundSubTaxFormConfig.updateDynamicTaxFormConfig(formIds.toList, Option(actor))
    } yield ()
  }

  def updateTaxFormVersionConfig(formVersionIds: Seq[FormVersionId], actor: UserId): Task[Unit] = {
    for {
      _ <- portalUserService.validateWritePermission(actor, PortalSectionId.FundSub)
      _ <- ZIO.logInfo(s"User $actor is updating tax form version list")
      _ <- FundSubTaxFormConfig.updateTaxFormVersionsConfig(formVersionIds, Option(actor))
    } yield ()
  }

  def getFundSupportingForms(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[GetFundSupportingFormsResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting supporting forms for fund $fundSubId")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Read
      )
      fundSubPublicModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(fundSubId)
      }
      adminRes <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
      }
      isUsingNewForm = adminRes.formVersions.nonEmpty
      taxFormConfig <- FundSubTaxFormConfig.getTaxFormConfig
      // old form
      taxForms <-
        if (fundSubPublicModel.featureSwitch.exists(_.enabledAddAdditionalTaxForms)) {
          FundSubOperationUtils.getFundAdditionalTaxForms(adminRes, taxFormConfig)
        } else {
          ZIO.attempt(Seq.empty)
        }
      // new form
      taxFormVersions <- FundSubOperationUtils.getFundSupportingFormVersions(
        fundSubPublicModel,
        adminRes,
        taxFormConfig
      )
    } yield {
      GetFundSupportingFormsResponse(
        isUsingNewForm,
        taxForms,
        taxFormVersions
      )
    }
  }

  def anduinAdminPreviewLpFormOutputAndDocuments(
    lpId: FundSubLpId,
    actor: UserId,
    shouldGenerateNewDocs: Boolean
  ): Task[AnduinAdminPreviewLpFormOutputAndDocumentsResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor replay lp flow to preview for lp $lpId")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        lpId.parent,
        PortalFundsubModel.Permission.Read
      )

      (fsModel, lpModel, lpRestrictedModel, lpSubDocData) <- FDBRecordDatabase.transact(
        FDBOperations[((FundSubModelStoreOperations, FundSubLpModelStoreOperations), LPDataOperations)].Production
      ) { case ((fsModelOps, lpModelOps), lpDataOps) =>
        for {
          fsModel <- fsModelOps.getFundSubPublicModel(lpId.parent)
          lpModel <- lpModelOps.getFundSubLpModel(lpId)
          lpRestrictedModel <- lpModelOps.getFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId))
          lpSubDocData <- lpDataOps.getLPSubscriptionDocData(lpId, lpRestrictedModel)
        } yield (fsModel, lpModel, lpRestrictedModel, lpSubDocData)
      }

      curLpCleanFiles <- FundSubOperationUtils.sortFilesByName(
        lpSubDocData.formFiles.toSeq,
        actor
      )

      curLpCleanInfoOpt = Option.when(curLpCleanFiles.nonEmpty)(
        LpCleanInfo(
          cleanFiles = curLpCleanFiles,
          lpLocations = lpSubDocData.lpSignatureBlocks,
          gpLocations = lpSubDocData.gpSignatureBlocks,
          uniqueEmailSigningRoles = lpSubDocData.uniqueEmailSigningRoles
        )
      )

      curLpSignedInfoOpt <- FundSubOperationUtils.getCurrentLpSignedInfoOpt(
        fsModel,
        lpModel,
        lpRestrictedModel,
        actor
      )

      newLpCleanInfoOpt <-
        if (curLpCleanInfoOpt.nonEmpty && shouldGenerateNewDocs) {
          FundSubOperationUtils.generateNewLpCleanInfo(lpId, actor).map(Some.apply)
        } else {
          ZIO.attempt(None)
        }

      newLpSignedInfoOpt <-
        if (shouldGenerateNewDocs) {
          ZIOUtils.traverseOption(
            newLpCleanInfoOpt.zip(
              curLpSignedInfoOpt.filter { signedInfo =>
                val signatureType = signedInfo.signatureType
                signatureType == FundSubSignatureType.ESignature || signatureType == FundSubSignatureType.RequestedSignature
              }
            )
          ) { case (newLpCleanInfo, curLpSignedInfo) =>
            FundSubOperationUtils.generateNewLpSignedInfo(
              actor,
              newLpCleanInfo,
              curLpSignedInfo
            )
          }
        } else {
          ZIO.attempt(None)
        }

      curGpCountersignedInfoOpt <- FundSubOperationUtils.getCurrentGpCounterSignedInfoOpt(
        lpRestrictedModel,
        lpModel,
        actor
      )

      newGpCountersignedInfoOpt <- ZIOUtils.traverseOption(
        newLpSignedInfoOpt.zip(curGpCountersignedInfoOpt)
      ) { case (newLpSignedInfo, curGpCountersignedInfo) =>
        FundSubOperationUtils.generateNewGpCounterSignedInfo(
          actor,
          lpModel,
          newLpSignedInfo,
          curGpCountersignedInfo
        )
      }
    } yield AnduinAdminPreviewLpFormOutputAndDocumentsResponse(
      curLpCleanInfoOpt = curLpCleanInfoOpt,
      newLpCleanInfoOpt = newLpCleanInfoOpt,
      curLpSignedInfoOpt = curLpSignedInfoOpt,
      newLpSignedInfoOpt = newLpSignedInfoOpt,
      curGpCounterSignedInfoOpt = curGpCountersignedInfoOpt,
      newGpCounterSignedInfoOpt = newGpCountersignedInfoOpt
    )
  }

  def anduinAdminUpdateLpFormOutputAndDocuments(
    lpId: FundSubLpId,
    cleanInfoToUpdateOpt: Option[LpCleanInfoToUpdate],
    signedInfoToUpdateOpt: Option[LpSignedInfoToUpdate],
    countersignedInfoToUpdateOpt: Option[GpCountersignedInfoToUpdate],
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor update lp form output and documents for $lpId")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        lpId.parent,
        PortalFundsubModel.Permission.Write
      )
      _ <- ZIOUtils.traverseOption(cleanInfoToUpdateOpt) { cleanInfoToUpdate =>
        for {
          _ <- ZIO.foreach(cleanInfoToUpdate.cleanFilesToSwap.toSeq) { case (oldFile, newFile) =>
            fileService.uploadNewVersionFromFile(
              fileId = oldFile,
              fromFileId = newFile,
              actor,
              None
            )
          }
          _ <- FundSubOperationUtils.updateLpCleanData(
            lpId,
            actor,
            cleanInfoToUpdate.lpLocations,
            cleanInfoToUpdate.gpLocations,
            cleanInfoToUpdate.uniqueEmailSigningRoles
          )
        } yield ()
      }
      _ <- ZIOUtils.traverseOption(signedInfoToUpdateOpt) { signedInfoToUpdate =>
        ZIO.foreach(signedInfoToUpdate.signedFilesToSwap.toSeq) { case (oldFile, newFile) =>
          fileService.uploadNewVersionFromFile(
            fileId = oldFile,
            fromFileId = newFile,
            actor,
            None
          )
        }
      }
      _ <- ZIOUtils.traverseOption(countersignedInfoToUpdateOpt) { countersignedInfoToUpdate =>
        ZIO.foreach(countersignedInfoToUpdate.countersignedFilesToSwap.toSeq) { case (oldFile, newFile) =>
          fileService.uploadNewVersionFromFile(
            fileId = oldFile,
            fromFileId = newFile,
            actor,
            None
          )
        }
      }
    } yield ()
  }

  def exportLpsTagData(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[ExportLpsTagDataResponse] = {
    for {
      _ <- ZIO.logInfo(s"Admin $actor export lps tag data")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Write
      )
      lps <- FDBRecordDatabase
        .transact(LpInfoStoreProvider.Production) { store =>
          LpInfoOperations(store).getLpInfoRecords(
            LpDashboardQueryParams(
              fundSubId = fundSubId,
              sortingOrder = SortingOrder.Ascending,
              sortingCategory = SortingCategory.FullName
            )
          )
        }
      allTags <- fundSubLpTagUtilService.getTagByFundSubId(fundSubId)
      lpsData = lps.items.map { lpRecordInfo =>
        LpData(
          firstName = lpRecordInfo.firstName,
          lastName = lpRecordInfo.lastName,
          email = lpRecordInfo.email,
          firmName = lpRecordInfo.firmName,
          lpId = lpRecordInfo.lpId,
          customId = lpRecordInfo.customId,
          tags = lpRecordInfo.tags.flatMap(tagId => allTags.find(_.id == tagId)).map(_.name)
        )
      }
      data = FundSubExportHelper.computeFillSheetDataForPredefinedAliasInclusive(
        lpsData,
        Seq(
          PredefinedAlias.InvestorLpIdAlias,
          PredefinedAlias.CustomLpIdAlias,
          PredefinedAlias.FirmNameAlias,
          PredefinedAlias.InvestorEmailAlias,
          PredefinedAlias.TagAlias
        ),
        mainFormInfo = None,
        taxFormFileIds = Seq.empty
      )
      result <- SpreadsheetUtils.createAndFillSpreadsheet(data)
      userFolderId <- fileService.createUserTemporaryFolderIfNeeded(actor)
      generatedFileId <- fileService.uploadFile(
        userFolderId,
        "lp_tags.xlsx",
        FileContentOrigin.FromSource(
          result,
          MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        ),
        actor
      )
    } yield ExportLpsTagDataResponse(generatedFileId)
  }

  def getLpsTagData(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[GetLpsTagDataResponse] = {
    for {
      _ <- ZIO.logInfo(s"Admin $actor get lps tag data")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Read
      )
      lps <- FDBRecordDatabase
        .transact(LpInfoStoreProvider.Production) { store =>
          LpInfoOperations(store).getLpInfoRecords(
            LpDashboardQueryParams(
              fundSubId = fundSubId,
              sortingOrder = SortingOrder.Ascending,
              sortingCategory = SortingCategory.FullName
            )
          )
        }
      allTags <- fundSubLpTagUtilService.getTagByFundSubId(fundSubId)
      lpsTagData = lps.items.map { lpRecordInfo =>
        LpTagData(
          lpId = lpRecordInfo.lpId,
          tagInfos =
            lpRecordInfo.tags.flatMap(tagId => allTags.find(_.id == tagId)).map(tag => TagInfo(tag.name, tag.color))
        )
      }
    } yield GetLpsTagDataResponse(lpsTagData)
  }

  def updateLpsTagData(
    fundSubId: FundSubId,
    lpsTagDataToUpdate: Seq[LpTagData],
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Admin $actor update lps tag data")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Write
      )
      _ <- ZIO.foreach(lpsTagDataToUpdate.filter(_.lpId.parent == fundSubId)) { lpTagData =>
        fundSubLpTagUtilService.updateTagsOfLp(
          lpTagData.lpId,
          _ => lpTagData.tagInfos.toSet,
          actor
        )
      }
    } yield ()
  }

  def getFundsUsingTaxForm(
    formIdEither: Either[DynamicFormId, FormId],
    actor: UserId
  ): Task[GetFundsUsingTaxFormResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is getting funds using $formIdEither")
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      allFundIds <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(_.getAllFundSubIds)
      fundsUsingTaxFormInSettings <- ZIO
        .foreach(allFundIds) { fundId =>
          for {
            adminResModelOpt <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
              ops.getOptFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundId))
            }
          } yield {
            adminResModelOpt
              .filter(_.taxFormGroups.values.exists { taxFormGroup =>
                formIdEither.fold(
                  taxFormGroup.formIds.contains,
                  formId => taxFormGroup.formVersionIds.exists(_.parent == formId)
                )
              })
              .map(_.fundSubAdminRestrictedId.parent)
          }
        }
        .map(_.flatten)
      supDocInfoList <- FDBRecordDatabase.transact(FundSubSupportingDocInfoOperations.Production) { ops =>
        ops.getByFormId(formIdEither.fold(_.idString, _.idString))
      }
      fundsUsingTaxFormInAdditionalTaxForms = supDocInfoList.map(_.id.parent.parent)
      uniqueFundIds = (fundsUsingTaxFormInSettings ++ fundsUsingTaxFormInAdditionalTaxForms).distinct
      fundsUsingTaxForm <- ZIO.foreach(uniqueFundIds) { fundId =>
        for {
          (fundSubModel, adminResModelOpt) <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
            ops =>
              for {
                fundSubModel <- ops.getFundSubPublicModel(fundId)
                adminResModelOpt <- ops.getOptFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundId))
              } yield fundSubModel -> adminResModelOpt
          }
          taxFormGroups = adminResModelOpt.map(_.taxFormGroups).getOrElse(Map.empty)
          additionalTaxFormVersions = adminResModelOpt.map(_.additionalTaxFormVersions).getOrElse(List.empty)
          allVersionIds = (taxFormGroups.values.toSeq.flatMap(_.formVersionIds) ++ additionalTaxFormVersions).distinct
          taxFormVersionIdOpt = allVersionIds.find { formVersionId =>
            // assume that all versions of this form are the same for this fund
            formIdEither.contains(formVersionId.parent)
          }
          taxFormVersionOpt <- taxFormVersionIdOpt.fold[Task[Option[FormVersionForFundSetup]]] {
            ZIO.attempt(None)
          } { versionId =>
            FDBRecordDatabase.transact(FormVersionStoreOperations.Production) { ops =>
              ops.getVersion(versionId).map { version =>
                Some(
                  FormVersionForFundSetup(
                    formVersionId = version.formVersionId,
                    formName = "", // not required
                    versionName = version.name,
                    createdAt = version.createdAt,
                    author = version.author,
                    versionNumber = version.versionNumber,
                    formType = version.formType
                  )
                )
              }
            }
          }
        } yield {
          FundUsingTaxForm(
            fundSubId = fundId,
            fundName = fundSubModel.fundName,
            taxFormVersionOpt = taxFormVersionOpt
          )
        }
      }
    } yield GetFundsUsingTaxFormResponse(fundsUsingTaxForm)
  }

  def updateTaxFormForFunds(
    formVersionId: FormVersionId,
    fundSubIds: Seq[FundSubId],
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor is updating tax form $formVersionId for ${fundSubIds.size} fund(s)")
      _ <- portalUserService.validateManagePermission(actor, PortalSectionId.FundSub)
      _ <- ZIO.foreach(fundSubIds) { fundSubId =>
        FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
          ops.updateFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId)) { adminResModel =>
            val newTaxFormGroups = adminResModel.taxFormGroups.view.mapValues { taxFormGroup =>
              val newFormVersionIds = taxFormGroup.formVersionIds.map { versionId =>
                if (versionId.parent == formVersionId.parent) formVersionId else versionId
              }
              taxFormGroup.copy(formVersionIds = newFormVersionIds)
            }
            val newAdditionalTaxFormVersions = adminResModel.additionalTaxFormVersions.map { versionId =>
              if (versionId.parent == formVersionId.parent) formVersionId else versionId
            }
            adminResModel.copy(
              taxFormGroups = newTaxFormGroups.toMap,
              additionalTaxFormVersions = newAdditionalTaxFormVersions
            )
          }
        }
      }
    } yield ()
  }

  def getInvestorsUsingTaxForm(
    formIdEither: Either[DynamicFormId, FormId],
    fundId: FundSubId,
    actor: UserId
  ): Task[GetInvestorsUsingSupportingFormResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is getting investors of $fundId using $formIdEither")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundId,
        PortalFundsubModel.Permission.Read
      )
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundId)
      isFundAdmin <- fundSubPermissionService.checkIfUserHasAdminRoleR(fundId, actor)
      investorForms <- FundSubOperationFormUtils.getInvestorsUsingTaxForm(
        formIdEither,
        fundId
      )(
        userProfileService,
        docSubmissionService,
        formSubmissionService
      )
    } yield {
      val refinedInvestorForms = if (isFundAdmin) {
        investorForms
      } else {
        investorForms.map { investorForm =>
          investorForm.copy(
            name = DeidentificationUtils.deidentifyName(investorForm.name),
            mainLpName = DeidentificationUtils.deidentifyName(investorForm.mainLpName),
            email = DeidentificationUtils.deidentifyEmail(investorForm.email)
          )
        }
      }
      GetInvestorsUsingSupportingFormResponse(refinedInvestorForms)
    }
  }

  def getStandardAliasSupportOfForm(
    formVersionId: FormVersionId,
    actor: UserId
  ): Task[Map[String, String]] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is getting standard alias support of form version $formVersionId")
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      standardAliasMapping <- formService.getFormStandardAliasMapping(formVersionId)
    } yield standardAliasMapping
  }

  def setCloseFundSub(
    params: SetCloseFundSubParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor set close for fund ${params.fundSubId} to isClosed ${params.isClosed}")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        params.fundSubId,
        PortalFundsubModel.Permission.Write
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.updateFundSubPublicModel(params.fundSubId)(
          _.copy(
            closingConfig = Some(
              FundSubClosingConfig(
                isClosed = params.isClosed,
                updatedBy = actor,
                updatedAt = Some(Instant.now)
              )
            )
          )
        )
      )
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(params.fundSubId)(natsNotificationService)
      _ <- greylinDataService.runUnit(
        FundSubscriptionOperations.update(params.fundSubId)(_.copy(isClosed = params.isClosed))
      )
    } yield ()
  }

  def batchMarkAllPagesAsViewed(
    params: BatchMarkAllPagesAsViewedParams,
    actor: UserId
  ): Task[BatchMarkAllPagesAsViewedResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is setting all pages as viewed for ${params.lpIds.size} lps")
      _ <- portalUserService.validateManagePermission(actor, PortalSectionId.FundSub)
      _ <- ZIOUtils.validate(params.lpIds.forall(_.parent == params.fundSubId))(
        GeneralServiceException("Some LP id doesn't match with the fund id")
      )
      fundModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(params.fundSubId)
      )
      _ <- ZIOUtils.validate(fundModel.lpFlowType.isFlexible)(
        GeneralServiceException("This method only support flexible flow")
      )
      lpFormIds <- FDBRecordDatabase.transact(LPDataOperations.Production) { ops =>
        RecordIO.parTraverseN(4)(params.lpIds) { lpId =>
          ops.getLastFormIdOpt(lpId).map { idOpt =>
            lpId -> idOpt.map(_.asNewLpFormIdUnsafe())
          }
        }
      }
      allFormsInfo <- ZIOUtils
        .foreachParN(4)(lpFormIds.flatMap(_._2).map(_.parent)) { formVersionId =>
          formService
            .getForm(
              formId = formVersionId.parent,
              versionIdOpt = Some(formVersionId),
              actor = actor,
              shouldCheckPermission = false
            )
            .map { resp =>
              val visiblePages = FormDataUtils.findAllVisiblePages(resp.formData.form).map(_._1)
              val engine = GaiaEngine.make(
                resp.formData.form,
                EngineConfiguration.default,
                EngineContext.default
              )
              formVersionId -> (visiblePages, engine)
            }
        }
        .map(_.toMap)
      results <- ZIO.foreach(lpFormIds) { case (lpId, lpFormIdOpt) =>
        lpFormIdOpt.fold[Task[(FundSubLpId, Either[String, Unit])]](
          ZIO.succeed(lpId -> Left[String, Unit]("This lp doesn't have any data"))
        ) { lpFormId =>
          for {
            lpFormDataOpt <- FDBRecordDatabase.transact(LpFormDataOperations.Production)(_.getOpt(lpFormId))
            state <- ZIOUtils.optionToTask(
              lpFormDataOpt.map(_.gaiaState),
              new RuntimeException(s"Form data for ${lpFormId.idString} of lp ${lpId.idString} not found")
            )
            newState = markAllPagesAsViewed(
              lpFormId.parent,
              state,
              allFormsInfo
            )
            result <- ZIOUtils
              .when(state.events.size != newState.events.size) {
                fundSubSubscriptionDocService
                  .saveSubscriptionFormData(
                    params = SaveSubscriptionFormDataParams(
                      fundSubLpId = lpId,
                      gaiaState = newState,
                      formVersionId = lpFormId.parent
                    ),
                    actor = actor
                  )
                  .map(_ => ())
              }
              .map(_ => Right[String, Unit](()))
              .onErrorHandleWith(e => ZIO.attempt(Left(e.getMessage)))
          } yield lpId -> result
        }
      }
    } yield BatchMarkAllPagesAsViewedResponse(
      results = results.toMap
    )
  }

  private def markAllPagesAsViewed(
    formVersionId: FormVersionId,
    state: GaiaState,
    formsInfo: Map[FormVersionId, (Seq[String], Either[String, GaiaEngine])]
  ) = {
    val newStateOpt = for {
      (pages, engineEither) <- formsInfo.get(formVersionId)
      engine <- engineEither.toOption
      newState <- {
        val unSeenPages = pages.filterNot { page =>
          state.get(page).exists(_.isTouched)
        }
        val events = unSeenPages.map { page =>
          Patches(
            trigger = FormEvent(
              page,
              Add[Json](Pointer.Root / UIKey.touched.entryName, Json.fromBoolean(true)),
              FormEventType.Import
            ),
            effect = Chain.empty
          )
        }
        engine.replay(events = events, initialStateOpt = Some(state)).map(_._1).toOption
      }
    } yield newState
    newStateOpt.getOrElse(state)
  }

  def removeFundSubEnvironment(
    fundSubId: FundSubId,
    actorId: UserId
  ): Task[RemoveFundSubEnvironmentResponse] = {
    for {
      _ <- ZIO.logInfo(s"user $actorId is removing fundsub $fundSubId 's environment")
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actorId,
        fundSubId,
        PortalFundsubModel.Permission.Write
      )
      _ <- portalUserService.validateWritePermission(actorId, PortalSectionId.Environment)
      oldEnvironmentIdOpt <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(op =>
        for {
          oldEnvironmentIdOpt <- op.getFundSubPublicModel(fundSubId).map(_.environmentIdOpt)
          _ <- op.updateFundSubPublicModel(fundSubId)(_.copy(environmentIdOpt = None))
        } yield oldEnvironmentIdOpt
      )
      _ <- greylinDataService.runUnit(
        FundSubscriptionOperations.update(fundSubId)(_.copy(environmentId = None))
      )
      batchActionIdOpt <- ZIO.foreach(oldEnvironmentIdOpt) { environmentId =>
        batchActionService.startBatchActionInternal(
          environmentId,
          actorId,
          actionType = BatchActionType.FundSubOperationRemoveEnvironment,
          batchActionItemsData =
            List(FundSubOperationRemoveEnvironmentActiviesImpl.Params(fundSubId, environmentId).asJson),
          frontendTracking = BatchActionFrontendTracking.ACTOR_TRACKING,
          startWorkflow = workflowParams => {
            FundSubOperationRemoveEnvironmentWorkflowImpl.instance
              .getWorkflowStub()
              .provideEnvironment(temporalEnvironment.workflowClient)
              .flatMap(workflowStub => ZWorkflowStub.start(workflowStub.execute(workflowParams)))
          }
        )
      }
    } yield RemoveFundSubEnvironmentResponse(batchActionIdOpt)
  }

  def syncAllDashboards(
    actor: UserId
  ): Task[SyncAllDashboardsResponse] = {
    for {
      _ <- ZIO.logInfo(s"user ${actor.idString} is syncing all fundsub dashboards")
      _ <- portalUserService.validateWritePermission(actor, PortalSectionId.FundSub)
      workflowId = TemporalWorkflowId.unsafeFromSuffix(s"SyncAllDashboards-${UUID.randomUUID}")
      workflowStub <- FundSubSyncDashboardWorkflowImpl.instance
        .getWorkflowStub(
          workflowId,
          _.withWorkflowRunTimeout(FundSubSyncDashboardWorkflowImpl.workflowRunTimeout.toJava)
        )
        .provideEnvironment(temporalEnvironment.workflowClient)
      _ <- ZWorkflowStub.start(workflowStub.syncDashboardData(SyncDashboardDataParams()))
    } yield SyncAllDashboardsResponse(workflowId.idString)
  }

  def bulkAddFundsToEnvironment(
    fundSubIds: Seq[FundSubId],
    environmentId: EnvironmentId,
    actorId: UserId
  ): Task[BulkAddFundsToEnvironmentResp] = {
    for {
      _ <- ZIO.logInfo(s"user $actorId is adding these funds ${fundSubIds} to environment ${environmentId} ")
      _ <- portalUserService.validateWritePermission(actorId, PortalSectionId.Environment)
      succeededFunds <- ZIO
        .foreach(fundSubIds) { fundSubId =>
          val task = for {
            _ <- fundSubPortalPermissionService.validateFundSubPermission(
              actorId,
              fundSubId,
              PortalFundsubModel.Permission.Write
            )
            _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
              _.updateFundSubPublicModel(fundSubId)(_.copy(environmentIdOpt = Some(environmentId)))
            )
          } yield Some(fundSubId)
          task
            .catchAllCause { err =>
              ZIO.logWarningCause(s"Error when add fund $fundSubId to environment $environmentId: ", err).as(None)
            }
        }
        .map(_.flatten)
      _ <- ZIO.foreach(succeededFunds) { fundSubId =>
        greylinDataService.runUnit(
          FundSubscriptionOperations.update(fundSubId)(_.copy(environmentId = Some(environmentId)))
        )
      }
      batchActionId <- batchActionService.startBatchActionInternal(
        environmentId,
        actorId,
        actionType = BatchActionType.FundSubOperationBulkAddEnvironment,
        batchActionItemsData =
          succeededFunds.map(FundSubOperationBulkAddEnvironmentActiviesImpl.Params(_, environmentId).asJson).toList,
        frontendTracking = BatchActionFrontendTracking.ACTOR_TRACKING,
        startWorkflow = workflowParams => {
          FundSubOperationBulkAddEnvironmentWorkflowImpl.instance
            .getWorkflowStub()
            .provideEnvironment(temporalEnvironment.workflowClient)
            .flatMap(workflowStub => ZWorkflowStub.start(workflowStub.execute(workflowParams)))
        }
      )
    } yield BulkAddFundsToEnvironmentResp(succeededFunds, batchActionId)
  }

  def unlinkFundsAssociatedWithEnvironmentInternal(environmentId: EnvironmentId): Task[List[FundSubId]] = {
    for {
      fundIds <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(ops =>
        for {
          fundIds <- ops.queryFundsByEnvironment(Some(environmentId)).map(_.map(_.fundSubId))
          _ <- RecordIO.traverse(fundIds)(ops.updateFundSubPublicModel(_)(_.copy(environmentIdOpt = None)))
        } yield fundIds
      )
      _ <- ZIO.foreach(fundIds) { fundSubId =>
        greylinDataService.runUnit(
          FundSubscriptionOperations.update(fundSubId)(_.copy(environmentId = None))
        )
      }
    } yield fundIds

  }

  def getEmailDefaultTemplates(
    params: GetEmailDefaultTemplatesParams,
    actorId: UserId
  ): Task[GetEmailDefaultTemplatesResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actorId is getting email default templates")
      flowTermCollection <- fundSubCopyConfigService.getFlowTermCollection(params.fundSubId)
      _ <- portalUserService.validateReadPermission(actorId, PortalSectionId.FundSub)
      emailTemplates = params.events.map(FundSubEmailDefaultTemplates.getEventTemplate(_, flowTermCollection))
    } yield GetEmailDefaultTemplatesResponse(emailTemplates)
  }

  def getFundAdminsNotificationSettings(
    fundId: FundSubId,
    actor: UserId
  ): Task[GetFundAdminsNotificationSettingsResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is getting notification settings of fund ${fundId.idString}")
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundId,
        PortalFundsubModel.Permission.Read
      )
      admins <- fundSubDashboardService.permissionService.getAllFundManagers(fundId)
      adminEmailToNotificationSettings <- ZIO.foreach(admins.toSet) { admin =>
        for {
          emailAddress <- userProfileService.getEmailAddress(admin)
          settings <- FundAdminNotificationUtils.getFundNotificationPreference(admin, fundId)
        } yield emailAddress.address -> settings
      }
    } yield GetFundAdminsNotificationSettingsResponse(
      settings = adminEmailToNotificationSettings.toMap
    )
  }

  def getFundSubPortalData(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[GetFundSubPortalDataResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is getting fundsub portal data for ${fundSubId.idString}")
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      hasPermission <- fundSubPortalPermissionService.hasFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Read
      )
      _ <- ZIO.unless(hasPermission) {
        ZIO.fail(GeneralServiceException(GetFundSubPortalDataResponse.NoFundPermissionMessage))
      }
      fundSubGeneralInfo <- getFundSubGeneralInfo(fundSubId)
      fundSubLpSummary <- getFundSubLpSummary(fundSubId)
      _ <- fundSubGroupMemberService.getGroupsMembers(
        fundSubId = fundSubId,
        getMemberMode = GetGroupMemberMode.All,
        actor = actor
      )
    } yield {
      GetFundSubPortalDataResponse(
        fundSubGeneralInfo = fundSubGeneralInfo,
        fundSubLpSummary = fundSubLpSummary
      )
    }
  }

  private def getFundSubGeneralInfo(fundSubId: FundSubId): Task[FundSubGeneralInfo] = {
    for {
      (fundSubModel, adminResModel, reportingOpt) <- FDBRecordDatabase.transact(
        FundSubModelStoreOperations.Production
      ) { ops =>
        for {
          fundSubModel <- ops.getFundSubPublicModel(fundSubId)
          adminResModel <- ops.getFundSubAdminRestrictedModel(fundSubId)
          reportingModelOpt <- ops.getOptFundSubReportingModel(fundSubId)
        } yield (fundSubModel, adminResModel, reportingModelOpt)
      }
      fundManagers <- fundSubPermissionService.getAllFundManagers(fundSubId)
      userInfoMap <- userProfileService.batchGetUserInfos(fundManagers.toSet)
      fundAmounts <- FundSubCommitmentHelper.getCommitmentForReporting(
        fundSubId = fundSubId,
        investmentFunds = adminResModel.investmentFunds,
        reportingModelOpt = reportingOpt
      )
      versionSettings <- ZIO.foreach(adminResModel.formVersions) { version =>
        for {
          editorName <- version.editor.fold(ZIO.attempt(""))(userId =>
            userProfileService.getUserInfo(userId).map(_.fullNameString)
          )
          internalVersionNumber <- FDBRecordDatabase.transact(FormVersionStoreOperations.Production) { ops =>
            ops.getVersion(version.id).map { version =>
              version.versionNumber
            }
          }
          allMappings <- ZIO
            .foreach(version.importMappingIds ++ version.exportMappingIds) { mappingVersionId =>
              FDBRecordDatabase
                .transact(FormTemplateMappingStoreOperations.Production) { ops =>
                  ops.getMappingVersionModel(
                    mappingId = mappingVersionId.parent,
                    versionOpt = Some(mappingVersionId)
                  )
                }
                .map(_.map { case (mapping, mappingVersion) =>
                  mappingVersionId -> FundSubFormMappingVersionInfo(
                    id = mappingVersionId,
                    mappingType = mapping.mappingType.value,
                    name = mapping.name,
                    versionName = mappingVersion.name,
                    versionIndex = mappingVersion.versionIndex
                  )
                })
            }
            .map(_.flatten.toMap)
        } yield FundSubFormVersionSettings(
          fundSubFormVersion = FundSubFormVersionSetting(
            version.id,
            version.createdAt.map(DateTimeMappersInstance.instantMapper.toBase),
            version.updatedAt.map(DateTimeMappersInstance.instantMapper.toBase),
            version.name,
            version.description,
            editorName,
            internalVersionNumber
          ),
          importMappings = version.importMappingIds.flatMap(allMappings.get),
          exportMappings = version.exportMappingIds.flatMap(allMappings.get)
        )
      }
      linkRestrictionConfig <- getFundsubLinkRestriction(fundSubModel)
    } yield {
      val authenticationConfig = fundSubModel.authenticationConfig.map { config =>
        FundSubAuthenticationConfig(
          enforcePasswordForLP = config.enforcePasswordLp,
          enforceMFAForLP = config.enforceMfaLp,
          enforceMFAForGP = config.enforceMfaGp,
          enforceShortSessionForLP = config.enforceShortSessionLp,
          enforceShortSessionForGP = config.enforceShortSessionGp
        )
      }
      val currencyOpt = if (adminResModel.investmentFunds.size == 1) {
        adminResModel.investmentFunds.headOption.map(_.currency)
      } else {
        reportingOpt.map(_.mainCurrency)
      }
      val fundManagerEmails = fundManagers.flatMap { userId =>
        userInfoMap.get(userId).map(_.emailAddressStr)
      }

      FundSubGeneralInfo(
        fundName = fundSubModel.fundName,
        fundManagerEmails = fundManagerEmails,
        startDate = fundSubModel.startDate.map(DateTimeMappersInstance.instantMapper.toBase),
        investorEntity = fundSubModel.investorEntity,
        creator = fundSubModel.creator,
        fundType = fundSubModel.fundType,
        supportingContacts = fundSubModel.supportingContacts,
        formIds = adminResModel.formIds,
        settings = adminResModel.exportSettings.toSeq.map(data => FundSubFormSettings(data._1, data._2)),
        versionSettings = versionSettings,
        investmentFunds = adminResModel.investmentFunds,
        approvedAmount = fundAmounts.approvedAmount,
        underReviewAmount = fundAmounts.underReviewAmount,
        featureSwitch = fundSubModel.featureSwitch.getOrElse(FeatureSwitch()),
        lpGeneralConfig = fundSubModel.lpGeneralConfig,
        isArchived = fundSubModel.status.isFundSubArchived,
        currency = currencyOpt.getOrElse(CurrencyMessage.USD),
        signatureConfig = fundSubModel.signatureConfig,
        lpFlowType = fundSubModel.lpFlowType,
        packageType = fundSubModel.packageType,
        linkRestrictionConfig = linkRestrictionConfig,
        pdfTextColorOpt = fundSubModel.pdfTextColorOpt,
        closingConfig = fundSubModel.closingConfig,
        authenticationConfig = authenticationConfig,
        environmentIdOpt = fundSubModel.environmentIdOpt,
        flowType = fundSubModel.flowType
      )
    }
  }

  def getFundsubLinkRestriction(
    model: FundSubPublicModel
  ): Task[Option[FundSubLinkRestrictionConfig]] = {
    val linkRestrictionListIdOpt = model.restrictionConfig.flatMap(_.linkRestrictionListId)
    linkRestrictionListIdOpt.fold(ZIO.attempt(Option.empty[FundSubLinkRestrictionConfig])) { linkRestrictionListId =>
      for {
        linkRestrictionListDataOpt <- manageFundSubAdminM.linkRestrictionService.getOptRestrictionList(
          linkRestrictionListId
        )
      } yield linkRestrictionListDataOpt.map { linkRestrictionListData =>
        FundSubLinkRestrictionConfig(
          enableAutoLoginRestriction = linkRestrictionListData.restrictions.exists(_.restrictionType.isAutoLogin)
        )
      }
    }
  }

  private def getFundSubLpSummary(fundSubId: FundSubId): Task[FundSubLpSummary] = {
    for {
      (adminResModel, lpModels) <- FDBRecordDatabase.transact(
        FDBOperations[(FundSubModelStoreOperations, FundSubLpModelStoreOperations)].Production
      ) { case (fundOps, lpOps) =>
        for {
          adminResModel <- fundOps.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
          lpModels <- lpOps.getAllFundSubLpModels(fundSubId)
        } yield adminResModel -> lpModels
      }
    } yield {
      val allLpIds = adminResModel.lpIds.toSet
      val lpStatuses = lpModels.collect {
        case lpModel: FundSubLpModel if allLpIds.contains(lpModel.fundSubLpId) => lpModel.lpState.map(_.getLpStatus)
      }.flatten
      FundSubLpSummary(
        statusCount = lpStatuses.groupMapReduce(identity)(_ => 1)(_ + _)
      )
    }
  }

  def getFundSubEmailConfig(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[GetFundSubEmailConfigResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is getting fundsub email config for ${fundSubId.idString}")
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Read
      )
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
      }
      customizedDomainParts <- sesOperationService.getCustomizedIdentities
      customEmailProviderIdOpt = adminResModel.customSmtpServerConfig.flatMap(_.providerId)
      customEmailProviderOpt <- ZIOUtils.traverseOption2(customEmailProviderIdOpt) { providerId =>
        emailProviderService.getOptEmailProvider(providerId)
      }
      emailProviderConfig = adminResModel.customSmtpServerConfig.map(config =>
        FundSubCustomEmailProviderConfig(
          enabled = config.enabled,
          emailProvider = customEmailProviderOpt.map(
            _.copy(encryptedPassword = "")
          ) // Remove the encrypted password before returning to the front-end
        )
      )
    } yield {
      val unusedTemplates = FundSubEmailConstants.unusedEmailTemplate
      val refinedTemplates = adminResModel.emailTemplates.filterNot(template => unusedTemplates.contains(template.event))
      val refinedDisableEmails = adminResModel.disabledEmails.diff(unusedTemplates)
      GetFundSubEmailConfigResponse(
        emailTemplates = refinedTemplates,
        disabledEmails = refinedDisableEmails,
        customSenderEmailAddress = adminResModel.customSenderEmailAddress,
        customEmailReply = adminResModel.customEmailReply,
        emailReplyToSenderConfig = adminResModel.emailReplyToSenderConfig,
        customEmailCcConfig = adminResModel.customEmailCcConfig,
        senderCcConfig = adminResModel.senderCcConfig,
        customEmailBccConfig = adminResModel.customEmailBccConfig,
        senderBccConfig = adminResModel.senderBccConfig,
        emailProviderConfig = emailProviderConfig,
        customizedDomainParts = customizedDomainParts
      )
    }
  }

  def updateFundSubEmailConfig(
    params: UpdateFundSubEmailConfigParams,
    actor: UserId
  ): Task[Unit] = {
    val fundSubId = params.fundSubId
    for {
      _ <- ZIO.logInfo(s"$actor is updating fundsub email config for ${fundSubId.idString}")
      _ <- portalUserService.validateWritePermission(actor, PortalSectionId.FundSub)
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Write
      )
      _ <- ZIOUtils.validate(
        !params.customSenderEmailAddress.exists { addressConfig =>
          addressConfig.enabled && !EmailAddress.isValid(s"${addressConfig.emailNamePart}@email.com")
        }
      )(GeneralServiceException("Invalid sender email name part config"))

      // Authentication white label
      oldAuthenticationWhitelabelIdOpt <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId)).map(_.authenticationWhitelabelId)
      }
      newAuthenticationWhitelabelId <- manageFundSubAdminM.createAuthenticationWhitelabel(
        oldAuthenticationWhitelabelIdOpt,
        params.customSenderEmailAddress
      )
      publicModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(fundSubId)
      }
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
      }
      currentProviderIdOpt = adminResModel.customSmtpServerConfig.flatMap(_.providerId)
      emailProviderIdOpt <- ZIOUtils.traverseOption2(params.customSmtpServerConfig) { config =>
        if (config.edited) {
          for {
            encryptedPasswordBinary <- encryptionService.encrypt(config.config.rawPassword.getBytes)
            providerId <- currentProviderIdOpt.fold {
              emailProviderService
                .createEmailProvider(
                  EmailProviderModel(
                    name = publicModel.fundName,
                    fromName = config.config.from.name,
                    fromAddress = config.config.from.address,
                    host = config.config.host,
                    port = config.config.port,
                    userName = config.config.userName,
                    encryptedPassword = Base64.toBase64(encryptedPasswordBinary),
                    tls = config.config.tls
                  )
                )
                .map(Some(_))
            } { currentProviderId =>
              emailProviderService
                .updateEmailProvider(
                  currentProviderId,
                  EmailProviderModel(
                    name = publicModel.fundName,
                    fromName = config.config.from.name,
                    fromAddress = config.config.from.address,
                    host = config.config.host,
                    port = config.config.port,
                    userName = config.config.userName,
                    encryptedPassword = Base64.toBase64(encryptedPasswordBinary),
                    tls = config.config.tls
                  )
                )
                .as(Some(currentProviderId))
            }
            _ <- ZIO.foreachDiscard(providerId) { providerId =>
              emailProviderService.createEmailProviderBinding(providerId, params.fundSubId)
            }
          } yield providerId
        } else {
          ZIO.succeed(currentProviderIdOpt)
        }
      }
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId)) { model =>
          val smtpServerConfig = params.customSmtpServerConfig.flatMap { configParam =>
            if (configParam.edited) {
              Some(FundSubCustomSmtpServerConfig(enabled = configParam.enabled, providerId = emailProviderIdOpt))
            } else {
              model.customSmtpServerConfig.map(_.copy(enabled = configParam.enabled))
            }
          }
          // Disable custom sender email address if custom smtp server is enabled
          val customSenderConfig = if (smtpServerConfig.exists(_.enabled)) {
            model.customSenderEmailAddress.map(_.copy(enabled = false))
          } else {
            params.customSenderEmailAddress
          }
          model.copy(
            emailTemplates = params.emailTemplates,
            disabledEmails = params.disabledEmails,
            customSenderEmailAddress = customSenderConfig,
            customEmailReply = params.customEmailReply,
            emailReplyToSenderConfig = params.emailReplyToSenderConfig,
            customEmailCcConfig = params.customEmailCcConfig,
            senderCcConfig = params.senderCcConfig,
            customEmailBccConfig = params.customEmailBccConfig,
            senderBccConfig = params.senderBccConfig,
            authenticationWhitelabelId = Some(newAuthenticationWhitelabelId),
            customSmtpServerConfig = smtpServerConfig
          )
        }
      }
    } yield ()
  }

  def getFundSubStorageIntegrationConfig(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[GetFundSubStorageIntegrationConfigResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is getting fundsub storage integration config for ${fundSubId.idString}")
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Read
      )
      fundSubStorageIntegrationOpt <- FDBClient.read(
        FundSubStorageIntegrationSubspace.instance.getOpt(FundSubStorageIntegrationId(fundSubId))
      )
      lpFileDownloadConfigOpt <- FDBRecordDatabase.transact(FundSubLpFileSettingOperations.Production)(
        _.getOpt(fundSubId).map(_.map { setting =>
          LpFileDownloadConfig(
            folderNameFormat = setting.lpFolderNameDownloadFormat,
            fileNameFormat = setting.lpFileNameDownloadFormat
          )
        })
      )
    } yield {
      GetFundSubStorageIntegrationConfigResponse(
        dataRoomIntegrationConfig = fundSubStorageIntegrationOpt
          .flatMap(_.dataRoomIntegrationConfig)
          .getOrElse(DataRoomIntegrationConfig()),
        lpFileDownloadConfig = lpFileDownloadConfigOpt.getOrElse(LpFileDownloadConfig())
      )
    }
  }

  def updateFundSubStorageIntegrationConfig(
    params: UpdateFundSubStorageIntegrationConfigParams,
    actor: UserId
  ): Task[Unit] = {
    val fundSubId = params.fundSubId
    for {
      _ <- ZIO.logInfo(s"$actor is updating fundsub storage integration config for ${fundSubId.idString}")
      _ <- portalUserService.validateWritePermission(actor, PortalSectionId.FundSub)
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Write
      )
      _ <- manageFundSubAdminM.fundSubStorageIntegrationService.updateOrAddStorageIntegrationConfigs(
        fundSubId,
        params.dataRoomIntegrationConfig
      )
      _ <- fundSubFileDownloadService.updateLpFileDownloadSettingUnsafe(
        fundSubId,
        params.lpFileDownloadConfig.folderNameFormat,
        params.lpFileDownloadConfig.fileNameFormat
      )
    } yield ()
  }

  def getSupportingFormConfig(
    params: GetSupportingFormConfigParams,
    actor: UserId
  ): Task[GetSupportingFormConfigResponse] = {
    val fundSubId = params.fundSubId
    for {
      _ <- ZIO.logInfo(s"$actor is getting fundsub supporting form config for ${fundSubId.idString}")
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Read
      )
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
        _.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
      }
      supportingDocs <- params.formVersionIdOpt.fold {
        // old form
        for {
          dynamicFormId <- ZIOUtils.optionToTask(params.dynamicFormIdOpt, GeneralServiceException("No form ID"))
          getFormResp <- dynamicFormService.getDynamicFormModel(actor, dynamicFormId)
        } yield {
          getFormResp.formModel.form.toSeq.flatMap { section =>
            FormLogicInstance.traverseAndExtractSection(
              section,
              _.formDescription.filter(_.inputType.isFile).map(_.label).toSeq
            )
          }.distinct
        }

      } { formVersionId =>
        // new form
        for {
          formData <- formService.getFormDataUnsafe(formVersionId)
        } yield {
          formData.form.defaultUiSchema
            .collect {
              case (_, widget) if widget.widgetType == WidgetType.FileGroup =>
                widget.uiOptions
                  .getOrElse(UIKey.supportingFileGroup, SupportingFileGroupType.Default)
                  .files
                  .map(_._2.description)
            }
            .flatten
            .toSeq
            .distinct
        }
      }
      // for new forms
      formVersionIds = (adminResModel.taxFormGroups.flatMap(_._2.formVersionIds).toSeq ++
        adminResModel.additionalTaxFormVersions).distinct
      formNames <- formService.getFormNames(formVersionIds.map(_.parent).distinct)
      formVersions <- ZIO.foreach(formVersionIds) { formVersionId =>
        FDBRecordDatabase.transact(FormVersionStoreOperations.Production) { ops =>
          ops.getVersion(formVersionId).map { version =>
            FormVersionForFundSetup(
              formVersionId = formVersionId,
              formName = formNames.find(_._1 == formVersionId.parent).map(_._2).getOrElse(""),
              versionName = version.name,
              createdAt = version.createdAt,
              author = version.author,
              versionNumber = version.versionNumber,
              formType = version.formType
            )
          }
        }
      }
    } yield {
      GetSupportingFormConfigResponse(
        supportingDocs = supportingDocs,
        taxFormGroups = adminResModel.taxFormGroups,
        additionalTaxForms = adminResModel.additionalTaxForms,
        additionalTaxFormVersions = adminResModel.additionalTaxFormVersions,
        formVersions = formVersions
      )
    }
  }

  def updateSupportingFormConfig(
    params: UpdateSupportingFormConfigParams,
    actor: UserId
  ): Task[Unit] = {
    val fundSubId = params.fundSubId
    for {
      _ <- ZIO.logInfo(s"$actor is updating fundsub supporting form config for ${fundSubId.idString}")
      _ <- portalUserService.validateWritePermission(actor, PortalSectionId.FundSub)
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Write
      )
      adminTeamId <- fundSubPermissionService.getAdminTeamId(fundSubId)
      initialFormVersionIds <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId)).map { model =>
          (model.taxFormGroups.values.flatMap(_.formVersionIds) ++ model.additionalTaxFormVersions).toSet
        }
      }
      newFormVersionIds =
        (params.taxFormGroups.values.flatMap(_.formVersionIds) ++ params.additionalTaxFormVersions).toSet
      _ <- ZIO.foreachDiscard(newFormVersionIds.diff(initialFormVersionIds)) { formVersionId =>
        formService.updatePermission(
          versionId = formVersionId,
          grantedTeams = Set(adminTeamId)
        )
      }
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId)) {
          _.copy(
            taxFormGroups = params.taxFormGroups,
            additionalTaxForms = params.additionalTaxForms,
            additionalTaxFormVersions = params.additionalTaxFormVersions
          )
        }
      }
    } yield ()
  }

  def updateInvestorImportId(
    params: UpdateInvestorImportIdParams,
    actor: UserId
  ): Task[Unit] = {
    val fundId = params.lpId.parent
    for {
      _ <- ZIO.logInfo(s"$actor is updating import id for investor ${params.lpId.idString}")
      _ <- portalUserService.validateWritePermission(actor, PortalSectionId.FundSub)
      _ <- fundSubPortalPermissionService.validateFundSubPermission(
        actor,
        fundId,
        PortalFundsubModel.Permission.Write
      )
      _ <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.updateFundSubLpRestrictedModel(FundSubLpRestrictedId(params.lpId)) {
          _.copy(
            importInvestorId = params.importInvestorId
          )
        }
      }
    } yield ()
  }

  def computeIaTemplateCoverageForForm(
    actor: UserId,
    formVersionId: FormVersionId
  ): Task[IaCoverage] = {
    for {
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.FundSub)
      iaCoverage <- lpProfileService.computeIaTemplateCoverageForForm(actor, formVersionId)
    } yield iaCoverage
  }

  private def getFundSubIAConfigActionEventLog(
    actor: UserId,
    fundSubId: FundSubId,
    formVersionIdOpt: Option[FormVersionId],
    featureSwitchOpt: Option[FeatureSwitch]
  ) = {
    for {
      coverage <-
        formVersionIdOpt.fold(ZIO.attempt(IaCoverage(-1, -1))) { formVersionId =>
          computeIaTemplateCoverageForForm(actor, formVersionId).catchAllCause { err =>
            ZIO.logWarningCause(s"Fail to compute coverage, due to: ", err).as(IaCoverage(-1, -1))
          }
        }
    } yield ActionEventFundSubIAConfigUpsert(
      fundSubId = fundSubId,
      formVersionIdOpt = formVersionIdOpt,
      iaCoverageNominator = coverage.totalFieldsIaCanMap,
      iaCoverageDenominator = coverage.fieldsWithPdfMappings,
      isInvestorAccessEnabled = featureSwitchOpt.exists(_.enableLpProfile),
      isAutofillPastSubEnabled = featureSwitchOpt.exists(_.enableAutoPrefillForLp)
    )
  }

  def getFundSubIAConfigActionEventLog(
    actor: UserId,
    fundSubId: FundSubId
  ): Task[ActionEventFundSubIAConfigUpsert] = {
    for {
      _ <- portalUserService.validateWritePermission(actor, PortalSectionId.FundSub)
      formVersionOpt <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
        _.getOptFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId)).map(_.flatMap(_.formVersions.headOption))
      }
      featureSwitchOpt <- FDBRecordDatabase
        .transact(FundSubModelStoreOperations.Production) {
          _.getOptFundSubPublicModel(fundSubId)
        }
        .map(_.flatMap(_.featureSwitch))
      event <- getFundSubIAConfigActionEventLog(actor, fundSubId, formVersionOpt.map(_.id), featureSwitchOpt)
    } yield event
  }

  private def sendFundSubIAConfigActionEventLog(
    actor: UserId,
    fundSubId: FundSubId,
    featureSwitch: FeatureSwitch,
    formVersionOpt: Option[FundSubFormVersion],
    httpContext: Option[AuthenticatedRequestContext]
  ) = {
    // Only send when there is a form version
    for {
      _ <- ZIO.logInfo("Send Action Log Fund Sub IA Config")
      eventLog <- getFundSubIAConfigActionEventLog(actor, fundSubId, formVersionOpt.map(_.id), Some(featureSwitch))
      _ <- actionLoggerService.addEventLog(
        actor,
        Seq(eventLog),
        httpContext
      )
    } yield ()
  }

  def checkIaTemplateCoverage(
    actor: UserId,
    isEnableLpProfile: Boolean,
    fundSubId: FundSubId,
    fundName: String,
    formVersionOpt: Option[FundSubFormVersion]
  ): Task[Unit] = {
    ZIOUtils
      .when(isEnableLpProfile) {
        ZIOUtils.traverseOptionUnit(formVersionOpt) { formVersion =>
          for {
            coverage <- computeIaTemplateCoverageForForm(actor, formVersion.id)
            _ <- ZIO.when(coverage.value * 100 < StandardAliasUtils.ValidIaAsaCoverageThreshold) {
              lpProfileService.sendWarningEmailFundSubLowIaCoverage(
                coverage = coverage.value,
                fundSubId = fundSubId,
                fundName = fundName,
                formVersionId = formVersion.id,
                formName = formVersion.name,
                actor = actor
              )
            }
          } yield ()
        }
      }
      .catchAllCause(ex => ZIO.logWarningCause(s"Error happen when compute IA template coverage. Reason: ", ex))
  }

  def exportFundComment(
    params: ExportFundCommentParams,
    actor: UserId
  ): Task[FolderId] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.id} is exporting comment data of fund ${params.fundId.idString}")
      _ <- portalUserService.validateWritePermission(actor, PortalSectionId.FundSub)
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(
        params.fundId,
        actor
      )
      workflowId = TemporalWorkflowId.unsafeFromSuffix(
        s"export-comment-${params.fundId.idString}-${UUID.randomUUID}"
      )
      workflowStub <- CommentExportWorkflowImpl.instance
        .getWorkflowStub(
          workflowId,
          _.withWorkflowRunTimeout(CommentExportWorkflowImpl.workflowRunTimeout.toJava)
        )
        .provideEnvironment(temporalEnvironment.workflowClient)
      // TODO @tuananhtd: Update this to async
      response <- ZWorkflowStub.execute(
        workflowStub.exportComment(
          ExportCommentParams(
            actor = actor,
            fundId = params.fundId,
            includingInternalComment = params.includingInternalComments
          )
        )
      )
      folderId <- ZIOUtils.optionToTask(response.folderIdOpt, new RuntimeException(response.errorMessage))
    } yield folderId
  }

  def syncCommentTiDbData(
    fundId: FundSubId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.id} is syncing comment TiDB data of fund ${fundId.idString}")
      _ <- portalUserService.validateWritePermission(actor, PortalSectionId.FundSub)
      threads <- commentServiceUtils.getFundCommentTiDbData(
        fundId
      )
      _ <- commentServiceUtils.syncCommentTiDbData(
        threads
      )
    } yield ()
  }

  private def validateFeatureSwitch(featureSwitch: FeatureSwitch): Task[Unit] = {
    val invalidReusePreviousSignedVersion =
      (featureSwitch.showSwitchAllowFormEditPostSigning || featureSwitch.allowFormEditPostSigning) && featureSwitch.enableLpReusePreviousSignedVersion
    ZIOUtils.failWhen(invalidReusePreviousSignedVersion) {
      GeneralServiceException(
        s""""Allow form edit post signing" and "Reuse previous signed version" cannot be both enabled"""
      )
    }
  }

}
