// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service.fundsub.free.module

import java.time.Instant
import java.util.UUID

import io.circe.syntax.*
import io.github.arainko.ducktape.*
import zio.temporal.workflow.ZWorkflowStub
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.batchaction.*
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.fundsub.batchaction.invitation.FundSubBatchInvitationOperations
import anduin.fundsub.endpoint.lp.*
import anduin.fundsub.endpoint.lp.ImportFromFundData.AutoPrefillDocsOption
import anduin.fundsub.models.FundSubSgwModelUtils
import anduin.id.batchaction.BatchActionId
import anduin.id.fundsub.*
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.id.role.signature.SignatureModuleRoleId
import anduin.model.common.user.UserId
import anduin.model.id.*
import anduin.protobuf.fundsub.*
import anduin.refined.Refined
import anduin.temporal.TemporalEnvironment
import anduin.workflow.fundsub.invitation.AutoPrefillDocuments.{
  AutoMarkDocsAsProvided,
  AutoShareDocsWithLp,
  DoNotAutoPrefill
}
import anduin.workflow.fundsub.invitation.impl.FundSubMultipleInvitationWorkflowImpl
import anduin.workflow.fundsub.invitation.{
  ImportFromFundData as ImportFromFundDataModel,
  ImportFromFundDataFirm as ImportFromFundDataFirmModel,
  *
}
import anduin.workflow.fundsub.UserContact
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils

object FundSubInvitationHelper {

  val SignatureModuleOwnerTeamPermissions: Set[Refined[SignatureModuleRoleId]] =
    Set(SignatureModuleRoleId.ViewIdValue, SignatureModuleRoleId.CreateRequestIdValue)

  val SignatureModuleViewOnlyTeamPermissions: Set[Refined[SignatureModuleRoleId]] = Set(
    SignatureModuleRoleId.ViewIdValue
  )

  private def toImportFromFundDataFirmModel(
    importFromFundDataFirm: ImportFromFundDataFirm
  ): ImportFromFundDataFirmModel = ImportFromFundDataFirmModel(
    firmId = importFromFundDataFirm.firmId,
    lpEmailTemplate = importFromFundDataFirm.lpEmailTemplate.map(_.toEmailTemplateMessage),
    collaboratorEmailTemplate = importFromFundDataFirm.collaboratorEmailTemplate.map(_.toEmailTemplateMessage)
  )

  private def setUpFundSubBatchInvitation(
    invitationModel: FundSubBatchInvitationInfo,
    realtimeUpdate: Boolean
  )(
    batchActionService: BatchActionService,
    natsNotificationService: NatsNotificationService
  ): Task[FundSubBatchInvitationModel] = {
    val actor = invitationModel.actor
    for {
      longTaskId <- ZIO.attempt(LongTaskIdFactory.unsafeRandomId(invitationModel.fundSubId))
      fsBatchInvitationId <- ZIO.attempt(FundSubBatchInvitationId(longTaskId))
      batchActionId <- ZIO.attempt(BatchActionId(longTaskId))

      // Create backend model
      batchInvitationModel <- FDBRecordDatabase.transact(
        FDBOperations[(FundSubBatchInvitationOperations, BatchActionStoreOperations)].Production
      ) { (invitationOps, batchActionOps) =>
        for {
          batchInvitationItems <- RecordIO.parTraverseN(4)(invitationModel.invitationItems.zipWithIndex) {
            case (item, order) =>
              val batchInvitationItemId = FundSubBatchInvitationItemIdFactory.unsafeRandomId(fsBatchInvitationId)
              val batchActionItemId = BatchActionItemIdFactory.unsafeRandomId(batchActionId)
              val batchInvitationItem = item
                .into[FundSubBatchInvitationItem]
                .transform(
                  Field.const(_.batchInvitationItemId, batchInvitationItemId),
                  Field.const(_.batchActionItemId, batchActionItemId),
                  Field.const(_.lpContact, item.lpContact.map(convertContact)),
                  Field.const(_.collaboratorContacts, item.collaboratorContacts.map(convertContact)),
                  Field.const(_.closeId, item.closeIdOpt),
                  Field.const(_.investorGroupId, item.investorGroupIdOpt),
                  Field.const(_.lpEmailTemplate, item.lpEmailTemplate.map(_.toEmailTemplateMessage)),
                  Field.const(_.workflowId, None),
                  Field.const(_.status, FundSubBatchInvitationStatusWaiting(Some(Instant.now))),
                  Field.const(_.importItemId, item.dataImportItemId),
                  Field.const(
                    _.sharedDocumentsPerDocType,
                    item.sharedDocumentsPerDocType.map { case (docType, fileIds) =>
                      docType -> FileList(fileIds)
                    }
                  ),
                  Field.const(
                    _.importFromFundData,
                    item.importFromFundData.map { importData =>
                      ImportFromFundDataModel(
                        investmentEntityId = importData.investmentEntityId,
                        prefillFormData = importData.prefillFormData,
                        autoPrefillDocuments = importData.autoPrefillDocsOption match {
                          case AutoPrefillDocsOption.DoNotAutoPrefill       => DoNotAutoPrefill
                          case AutoPrefillDocsOption.AutoMarkDocsAsProvided => AutoMarkDocsAsProvided
                          case AutoPrefillDocsOption.AutoShareDocsWithLp    => AutoShareDocsWithLp
                        }
                      )
                    }
                  ),
                  Field.default(_.unknownFields)
                )
              for {
                _ <- invitationOps.createItem(batchInvitationItem)
                _ <- batchActionOps.createItem(
                  BatchActionItemModel(
                    batchActionItemId = batchActionItemId,
                    status = BatchActionItemStatusWaiting(Some(Instant.now)),
                    data = Some(item.asJson),
                    workflowId = None,
                    order = order
                  )
                )
              } yield batchInvitationItem
          }
          batchInvitationModel = invitationModel
            .into[FundSubBatchInvitationModel]
            .transform(
              Field.const(_.fundSubBatchInvitationId, fsBatchInvitationId),
              Field.const(_.batchActionId, batchActionId),
              Field.const(_.itemIds, batchInvitationItems.map(_.batchInvitationItemId)),
              Field.const(_.investorGroupId, invitationModel.investorGroupIdOpt),
              Field.const(_.createdAt, Some(Instant.now)),
              Field.const(_.workflowId, None),
              Field.const(
                _.importFromFundDataFirm,
                invitationModel.importFromFundDataFirm.map(toImportFromFundDataFirmModel)
              ),
              Field.default(_.unknownFields)
            )
          _ <- invitationOps.create(batchInvitationModel)
          _ <- batchActionOps.create(
            BatchActionModel(
              batchActionId = batchActionId,
              createdBy = actor,
              createdAt = Some(Instant.now),
              workflowId = None,
              frontendTracking = BatchActionFrontendTracking.ACTOR_TRACKING,
              actionType = invitationModel.actionType,
              postExecuteStatus = BatchActionItemStatusWaiting(Some(Instant.now))
            )
          )
          _ <- batchActionOps.addUserBatchAction(actor, batchActionId)
        } yield batchInvitationModel
      }
      _ <- batchActionService.notifyFrontendTracking(batchActionId)
      _ <- ZIOUtils.when(realtimeUpdate) {
        for {
          _ <- FDBRecordDatabase.transact(FundSubBatchInvitationOperations.Production) { op =>
            op.addUserBatchInvitation(actor, fsBatchInvitationId)
          }
          _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubBatchInvitationModel(
            actor,
            fsBatchInvitationId
          )(
            natsNotificationService
          )
        } yield ()
      }
    } yield batchInvitationModel
  }

  def setUpFundSubBatchInvitation(
    fundSubId: FundSubId,
    lpsToInvite: Seq[FundSubLpInfo],
    closeId: Option[FundSubCloseId],
    attachedDocs: Seq[FileId],
    lpOrderType: LpOrderType,
    investorGroupIdOpt: Option[FundSubInvestorGroupId],
    actor: UserId,
    realtimeUpdate: Boolean,
    importFromFundDataFirm: Option[ImportFromFundDataFirm] = None
  )(
    batchActionService: BatchActionService,
    natsNotificationService: NatsNotificationService
  ): Task[FundSubBatchInvitationModel] = {
    setUpFundSubBatchInvitation(
      invitationModel = FundSubBatchInvitationInfo(
        fundSubId = fundSubId,
        invitationItems = lpsToInvite,
        closeId = closeId,
        attachedDocs = attachedDocs,
        orderType = lpOrderType,
        investorGroupIdOpt = investorGroupIdOpt,
        actor = actor,
        importFromFundDataFirm = importFromFundDataFirm,
        actionType = BatchActionType.FundSubInviteLp
      ),
      realtimeUpdate = realtimeUpdate
    )(batchActionService, natsNotificationService)
  }

  def setUpFundSubBatchInvitationForAdvisor(
    ordersToAdd: Seq[AdvisorCreateOrderInfo],
    advisorGroupId: FundSubRiaGroupId,
    realtimeUpdate: Boolean,
    actor: UserId
  )(
    userProfileService: UserProfileService,
    batchActionService: BatchActionService,
    natsNotificationService: NatsNotificationService
  ): Task[FundSubBatchInvitationModel] = {
    for {
      advisorInfoMap <- ZIO
        .foreach(ordersToAdd.map(_.advisorUserId).distinct.grouped(1000).toList) { batchUserIds =>
          userProfileService.batchGetUserInfos(batchUserIds.toSet)
        }
        .map(_.flatten.toMap)
      invitationModel <- setUpFundSubBatchInvitation(
        invitationModel = FundSubBatchInvitationInfo(
          fundSubId = advisorGroupId.parent,
          invitationItems = ordersToAdd.map { orderInfo =>
            FundSubBatchInvitationItemInfo(
              lpContact = advisorInfoMap.get(orderInfo.advisorUserId).map { advisorInfo =>
                NameEmailInfo(
                  email = advisorInfo.emailAddressStr,
                  firstName = advisorInfo.firstName,
                  lastName = advisorInfo.lastName
                )
              },
              firmName = orderInfo.investmentEntityName,
              advisorGroupIdOpt = Some(advisorGroupId)
            )
          },
          actionType = BatchActionType.RiaCreateOrder,
          actor = actor
        ),
        realtimeUpdate = realtimeUpdate
      )(batchActionService, natsNotificationService)
    } yield invitationModel

  }

  def setUpFundSubBatchInvitationWithoutJointInfo(
    fundSubId: FundSubId,
    lpsToInvite: Seq[FundSubLpInvitationInfo],
    sharedAttachedDocs: Seq[FileId],
    lpOrderType: LpOrderType,
    actor: UserId,
    realtimeUpdate: Boolean,
    importFromFundDataFirm: Option[ImportFromFundDataFirm] = None
  )(
    batchActionService: BatchActionService,
    natsNotificationService: NatsNotificationService
  ): Task[FundSubBatchInvitationModel] = {
    setUpFundSubBatchInvitation(
      invitationModel = FundSubBatchInvitationInfo(
        fundSubId = fundSubId,
        invitationItems = lpsToInvite,
        attachedDocs = sharedAttachedDocs,
        orderType = lpOrderType,
        actor = actor,
        importFromFundDataFirm = importFromFundDataFirm,
        actionType = BatchActionType.FundSubInviteLp
      ),
      realtimeUpdate = realtimeUpdate
    )(batchActionService, natsNotificationService)
  }

  def storeBatchWorkflowId(
    fundSubBatchInvitationId: FundSubBatchInvitationId,
    workflowId: String
  ): Task[Unit] = {
    for {
      invitationModel <- FDBRecordDatabase.transact(FundSubBatchInvitationOperations.Production)(
        _.update(fundSubBatchInvitationId)(
          _.copy(workflowId = Some(workflowId))
        )
      )
      _ <- FDBRecordDatabase.transact(BatchActionStoreOperations.Production)(
        _.update(invitationModel.batchActionId)(
          _.copy(workflowId = Some(workflowId))
        )
      )
    } yield ()
  }

  def setUpInvitationWorkflow(
    batchInvitationId: FundSubBatchInvitationId,
    batchInvitationItemIds: Seq[FundSubBatchInvitationItemId],
    actorIpAddress: Option[String]
  )(
    using temporalEnvironment: TemporalEnvironment
  ): Task[Unit] = {
    val workflowId = TemporalWorkflowId.unsafeFromSuffix(s"FundSubMultipleInvitation-${UUID.randomUUID}")
    for {
      workflowStub <- FundSubMultipleInvitationWorkflowImpl.instance
        .getWorkflowStub(workflowId)
        .provideEnvironment(temporalEnvironment.workflowClient)
      _ <- ZWorkflowStub.start(
        workflowStub.inviteMultiple(
          MultipleInvitationParam(
            batchInvitationItemIds.map { batchInvitationItemId =>
              SingleInvitationParam(
                batchInvitationItemId = batchInvitationItemId,
                actorIpAddress = actorIpAddress.getOrElse(""),
                syncGatewayUpdate = true
              )
            },
            batchInvitationId
          )
        )
      )
      _ <- ZIO.logInfo(s"Started workflowId ${workflowId.idString}")
      _ <- storeBatchWorkflowId(
        batchInvitationId,
        workflowId.idString
      )
    } yield ()
  }

  def convertContact(contact: NameEmailInfo): UserContact = UserContact(
    email = contact.email,
    firstName = contact.firstName,
    lastName = contact.lastName,
    skipInvitationEmail = contact.skipInvitationEmail,
    enableSso = contact.enableSSO
  )

  def convertContact(contact: UserContact): NameEmailInfo = NameEmailInfo(
    email = contact.email,
    firstName = contact.firstName,
    lastName = contact.lastName,
    skipInvitationEmail = contact.skipInvitationEmail,
    enableSSO = contact.enableSso
  )

  private given Conversion[FundSubLpInfo, FundSubBatchInvitationItemInfo] = { lpInfo =>
    lpInfo
      .into[FundSubBatchInvitationItemInfo]
      .transform(
        Field.const(_.advisorGroupIdOpt, None),
        Field.const(_.lpAttachedDocs, Seq[FileId]()),
        Field.const(_.investorGroupIdOpt, None),
        Field.const(_.closeIdOpt, None)
      )
  }

  private given Conversion[FundSubLpInvitationInfo, FundSubBatchInvitationItemInfo] = { lpInvitationInfo =>
    lpInvitationInfo.lpInfo
      .into[FundSubBatchInvitationItemInfo]
      .transform(
        Field.const(_.advisorGroupIdOpt, None),
        Field.const(_.lpAttachedDocs, lpInvitationInfo.lpAttachedDocs),
        Field.const(_.investorGroupIdOpt, lpInvitationInfo.investorGroupIdOpt),
        Field.const(_.closeIdOpt, lpInvitationInfo.closeIdOpt)
      )
  }

  private given [A, B] => (conv: Conversion[A, B]) => Conversion[Seq[A], Seq[B]] = { seqA =>
    seqA.map(conv)
  }

}
