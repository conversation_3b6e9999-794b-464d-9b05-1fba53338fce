// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service.fundsub.free.module

import java.time.Instant

import io.circe.Json
import io.circe.syntax.*
import zio.implicits.*
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.batchaction.*
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.fdb.client.FDBClient
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{DefaultCluster, FDBOperations, FDBRecordDatabase}
import anduin.forms.engine.GaiaState
import anduin.forms.model.FormDataSource
import anduin.forms.service.FormService
import anduin.fundsub.activitylog.{ActivityLogService, FundSubAdminActivityLogUtils}
import anduin.fundsub.auditlog.FundSubAuditLogService.{AddEventEmailParam, AddEventParam}
import anduin.fundsub.auditlog.{AuditLogActorType, AuditLogEventType, FundSubAuditLogService}
import anduin.fundsub.batchaction.invitation.FundSubBatchInvitationOperations
import anduin.fundsub.comment.FormCommentService
import anduin.fundsub.commitment.FundSubCommitmentHelper
import anduin.fundsub.dashboard.{LpInfoOperations, LpInfoStoreProvider}
import anduin.fundsub.datalakeingestion.FundSubDataLakeIngestionService
import anduin.fundsub.datalakeingestion.model.{
  AddOrderParams,
  FormFieldValue,
  SubFundCommitmentAmountMessage,
  UserBasicInfo
}
import anduin.fundsub.endpoint.lp.*
import anduin.fundsub.endpoint.operation.TagInfo
import anduin.fundsub.environment.FundSubEnvironmentPolicyService
import anduin.fundsub.exception.FundSubException.{FundSubRiaGroupHasNotBeenLinked, NoPermissionToStartInvitation}
import anduin.fundsub.flow.FundSubLpFlowStatus.toGreylinSubscriptionOrderStatus
import anduin.fundsub.flow.FundSubLpFlowTaskHandler
import anduin.fundsub.form.utils.FundSubCommonUtils
import anduin.fundsub.form.{FundSubFormService, LpForm}
import anduin.fundsub.fundclose.{FundSubCloseOperations, FundSubCloseSgwUtils}
import anduin.fundsub.group.FundSubGroupTrackingUtils
import anduin.fundsub.investorgroup.{FundSubInvestorGroupService, FundSubInvestorGroupUtils}
import anduin.fundsub.invitation.FundSubSingleUserInvitationLinkStoreOperations
import anduin.fundsub.models.{FundSubLpModelStoreOperations, FundSubModelStoreOperations, FundSubSgwModelUtils}
import anduin.fundsub.ria.group.FundSubRiaGroupStoreOperations
import anduin.fundsub.service.*
import anduin.fundsub.signature.FundSubSignatureModel
import anduin.fundsub.signature.integration.FundSubSignatureStoreOperations
import anduin.fundsub.status.FundSubLpStatusHistoryService
import anduin.fundsub.submission.version.event.FormProgress
import anduin.fundsub.subscriptiondoc.FundSubSubscriptionDocService
import anduin.fundsub.subscriptiondoc.FundSubSubscriptionDocService.CreateFirstVersionResponse
import anduin.fundsub.user.FundSubUserService
import anduin.fundsub.utils.{FundSubDashboardDataUtils, FundSubDataLakeUtils, FundSubRiaPermissionUtils}
import anduin.fundsub.{FundSubLoggingService, LpFormDataOperations}
import anduin.greylin.GreylinDataService
import anduin.greylin.modelti.{SubscriptionOrder, SubscriptionOrderMember}
import anduin.greylin.operation.fundsub.SubscriptionOrderSubFundInvestmentOperations
import anduin.greylin.operation.{SubscriptionOrderMemberOperations, SubscriptionOrderOperations}
import anduin.id.form.FormVersionId
import anduin.id.fundsub.*
import anduin.id.fundsub.FundSubLpFolderTypeId.FolderType
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.id.signature.SignatureModuleId
import anduin.model.common.user.UserId
import anduin.model.id.*
import anduin.model.id.email.InternalEmailId
import anduin.model.optics.iso.SquantsIso
import anduin.portaluser.ExecutiveAdmin
import anduin.protobuf.activitylog.fundsub.admin.*
import anduin.protobuf.dynamicform.S3FormChangeInfo
import anduin.protobuf.external.squants.{CurrencyMessage, MoneyMessage}
import anduin.protobuf.flow.file.{FileFolderPermission, FileFolderPermissionMap}
import anduin.protobuf.flow.fundsub.admin.lpdashboard.{LpInfoRecord, LpInvitationType, LpStatus}
import anduin.protobuf.fundsub.*
import anduin.protobuf.fundsub.activitylog.lp.{CollaboratorInvited, LpActivityInfo}
import anduin.protobuf.fundsub.close.FundSubCloseDataModel
import anduin.protobuf.fundsub.commitment.LpCommitment
import anduin.protobuf.fundsub.environment.policy.FundSubLpEnvironmentSSOType
import anduin.protobuf.fundsub.invitation.FundSubSingleUserInvitationLink
import anduin.protobuf.fundsub.models.{FundSubAdminRestrictedModel, InvestmentFundModel}
import anduin.protobuf.fundsub.models.lp.{FundSubLpModel, FundSubLpRestrictedModel}
import anduin.rebac.RebacStoreOperation
import anduin.ria.integration.RiaExternalIntegrationService
import anduin.service.entity.EntityServiceUtils
import anduin.service.{AuthenticatedRequestContext, GeneralServiceException, RequestContext}
import anduin.signature.integration.SignatureIntegrationService
import anduin.signature.model.{SignatureModuleClient, SignatureRecipientFeature, WorkflowClient}
import anduin.team.TeamServiceParams.{AddSubTeamsParams, CreateNewTeamParams}
import anduin.team.{TeamService, TeamServiceParams}
import anduin.temporal.TemporalEnvironment
import anduin.util.CurrencyUtils
import anduin.utils.ScalaUtils
import anduin.workflow.fundsub.UserContact
import anduin.workflow.fundsub.invitation.*
import anduin.workflow.fundsub.invitation.SendEmailResponse.SendEmailResult
import com.anduin.stargazer.endpoints.PermissionChanges
import com.anduin.stargazer.service.fundsub.FundSubContactService
import com.anduin.stargazer.service.fundsub.free.module.FundSubLpInvitationService.{
  FundSubFormWorkflowData,
  SetUpLpModelResp
}
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.util.date.DateCalculator

final case class FundSubLpInvitationService(
  userProfileService: UserProfileService,
  teamService: TeamService,
  batchActionService: BatchActionService,
  natsNotificationService: NatsNotificationService,
  formCommentService: FormCommentService,
  signatureIntegrationService: SignatureIntegrationService,
  fundSubUserService: FundSubUserService,
  fundSubLpActivityLogService: FundSubLpActivityLogService,
  fundSubEmailService: FundSubEmailService,
  riaExternalIntegrationService: RiaExternalIntegrationService,
  fundSubPermissionService: FundSubPermissionService,
  fundSubLpFlowTaskHandler: FundSubLpFlowTaskHandler,
  newSupportingDocService: NewSupportingDocService,
  fundSubLpStatusHistoryService: FundSubLpStatusHistoryService,
  fundSubInvestorGroupService: FundSubInvestorGroupService,
  fundSubEnvironmentPolicyService: FundSubEnvironmentPolicyService,
  fundSubFormService: FundSubFormService,
  fundSubLpTagUtilService: FundSubLpTagUtilService,
  fundSubContactService: FundSubContactService,
  fundSubAuditLogService: FundSubAuditLogService,
  fundSubLoggingService: FundSubLoggingService,
  fundSubZapierService: FundSubZapierService,
  fundSubSubscriptionDocService: FundSubSubscriptionDocService
)(
  using executiveAdmin: ExecutiveAdmin,
  temporalEnvironment: TemporalEnvironment,
  fileService: FileService,
  formService: FormService,
  greylinDataService: GreylinDataService,
  activityLogService: ActivityLogService,
  fundSubLpDashboardService: FundSubLpDashboardService,
  dataLakeIngestionService: FundSubDataLakeIngestionService
) {

  private given Conversion[BatchActionItemError, FundSubInvitationError] = {
    case e: BatchActionItemNoPermissionError => FundSubInvitationNoPermissionError(e.detail)
    case e: BatchActionItemServerError       => FundSubInvitationServerError(e.reason)
    case BatchActionItemError.Empty          => FundSubInvitationError.Empty
  }

  private given Conversion[BatchActionItemStatusCancelledReason, FundSubBatchInvitationStatusCancelledReason] = {
    case BatchActionItemStatusCancelledReason.UserTrigger => FundSubBatchInvitationStatusCancelledReason.UserTrigger
    case BatchActionItemStatusCancelledReason.WorkflowTerminated =>
      FundSubBatchInvitationStatusCancelledReason.WorkflowTerminated
    case r: BatchActionItemStatusCancelledReason.Unrecognized =>
      FundSubBatchInvitationStatusCancelledReason.Unrecognized(r.unrecognizedValue)
  }

  private given Conversion[BatchActionItemStatus, FundSubBatchInvitationStatus] = {
    case s: BatchActionItemStatusWaiting => FundSubBatchInvitationStatusWaiting(s.createdAt)
    case s: BatchActionItemStatusRunning => FundSubBatchInvitationStatusRunning(s.startedAt)
    case s: BatchActionItemStatusSucceeded =>
      s.data
        .flatMap(_.as[SucceededLp].toOption)
        .map(succeededLp => FundSubBatchInvitationStatusSucceeded(s.succeededAt, succeededLp.fundSubLpId))
        .getOrElse(
          FundSubBatchInvitationStatusFailed(
            s.succeededAt,
            FundSubInvitationServerError(s"Cannot decode FundSubLpId from ${s.data}")
          )
        )
    case s: BatchActionItemStatusFailed    => FundSubBatchInvitationStatusFailed(s.failedAt, s.error)
    case s: BatchActionItemStatusCancelled => FundSubBatchInvitationStatusCancelled(s.cancelledAt, s.reason)
    case BatchActionItemStatus.Empty       => FundSubBatchInvitationStatus.Empty
  }

  private def validateUserCanStartInvitation(
    invitationItem: FundSubBatchInvitationItem,
    fundSubId: FundSubId,
    actor: UserId
  ): Task[Unit] = {
    val task = for {
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- fundSubPermissionService.validateUserCanInviteInvestorR(fundSubId, actor)
      _ <- ZIOUtils.traverseOptionUnit(invitationItem.advisorGroupIdOpt.zip(invitationItem.lpContact)) {
        (fundSubRiaGroupId, lpContact) =>
          for {
            _ <- fundSubPermissionService.validateUserCanCreateOrderInAdvisorGroupR(fundSubRiaGroupId, actor)
            linkedRiaFundGroupIdOpt <- FDBRecordDatabase.transact(FundSubRiaGroupStoreOperations.Production) { ops =>
              ops.getOpt(fundSubRiaGroupId).map(_.flatMap(_.linkedRiaFundGroupId))
            }
            linkedRiaFundGroupId <- ZIOUtils.optionToTask(
              linkedRiaFundGroupIdOpt,
              FundSubRiaGroupHasNotBeenLinked(fundSubRiaGroupId)
            )
            lpUserId <- userProfileService.getUserIdFromEmailAddress(lpContact.email)
            _ <- riaExternalIntegrationService.validateUserCanCreateRiaOrder(
              linkedRiaFundGroupId,
              lpUserId,
              actor
            )
          } yield ()
      }
    } yield ()
    task.mapError(err => NoPermissionToStartInvitation(err.getMessage))
  }

  def startSingleInvitation(
    fundSubBatchInvitationItemId: FundSubBatchInvitationItemId,
    workflowId: Option[String],
    syncGatewayUpdate: Boolean
  )(
    batchActionService: BatchActionService,
    natsNotificationService: NatsNotificationService
  ): Task[(FundSubBatchInvitationModel, FundSubBatchInvitationItem)] = {
    for {
      fundSubBatchInvitationId <- ZIO.attempt(fundSubBatchInvitationItemId.parent)
      fundSubId <- ZIO.getOrFail {
        fundSubBatchInvitationId.parent.parent match {
          case id: FundSubId => Some(id)
          case _             => None
        }
      }
      (batchInvitationModel, invitationItem) <- FDBRecordDatabase.transact(
        FundSubBatchInvitationOperations.Production
      ) { ops =>
        for {
          model <- ops.get(fundSubBatchInvitationId)
          item <- ops.getItem(fundSubBatchInvitationItemId)
        } yield (model, item)
      }
      _ <- validateUserCanStartInvitation(invitationItem, fundSubId, batchInvitationModel.actor)
      newStatus <- batchActionService
        .updateSingleActionStatusInternal(
          invitationItem.batchActionItemId,
          curStatus =>
            if (ScalaUtils.isMatch[BatchActionItemStatusWaiting](curStatus)) {
              BatchActionItemStatusRunning(Some(Instant.now))
            } else {
              curStatus
            }
        )
        .map(_._1.status)
      updatedInvitationItem <- FDBRecordDatabase.transact(FundSubBatchInvitationOperations.Production)(
        _.updateItem(fundSubBatchInvitationItemId) { model =>
          model.copy(
            status = newStatus,
            workflowId = workflowId
          )
        }
      )
      _ <- ZIOUtils.when(syncGatewayUpdate) {
        for {
          invitationOpt <- FDBRecordDatabase.transact(FundSubBatchInvitationOperations.Production) { op =>
            op.getOpt(fundSubBatchInvitationId)
          }
          _ <- ZIOUtils.traverseOptionUnit(invitationOpt) { invitation =>
            FundSubSgwModelUtils.modifyLastUpdateFundSubBatchInvitationModel(
              invitation.actor,
              fundSubBatchInvitationId
            )(
              natsNotificationService
            )
          }
        } yield ()
      }
    } yield batchInvitationModel -> updatedInvitationItem
  }

  def setUpUserIfNeeded(
    fundSubId: FundSubId,
    contact: NameEmailInfo,
    actor: UserId
  ): Task[UserId] = setUpUserIfNeeded(
    fundSubId,
    FundSubInvitationHelper.convertContact(contact),
    actor
  )

  def setUpUserIfNeeded(
    fundSubId: FundSubId,
    contact: UserContact,
    actor: UserId
  ): Task[UserId] = {
    for {
      inviterEmail <- userProfileService.getEmailAddress(actor).map(_.address)
      userId <- fundSubUserService.createInvestorUserIfNeeded(
        email = contact.email,
        firstName = contact.firstName,
        lastName = contact.lastName,
        inviterOpt = Some(inviterEmail),
        fundSubId = fundSubId
      )
    } yield userId
  }

  def setUpLpTeam(
    fundSubId: FundSubId,
    actor: UserId,
    initialUserIds: Seq[UserId]
  ): Task[TeamId] = {
    for {
      _ <- ZIO.logInfo(s"Create team for $fundSubId with initial users $initialUserIds")
      lpTeamId <- teamService.createNewTeam(CreateNewTeamParams(fundSubId.parent, None))
      // Add initial members
      _ <- ZIO.foreach(initialUserIds.distinct) { userId =>
        teamService.addMember(
          TeamServiceParams.AddMemberParams(
            actor,
            userId,
            lpTeamId,
            skipPermission = true
          )
        )
      }
    } yield lpTeamId
  }

  def initializeLpFolders(
    lpId: FundSubLpId,
    actor: UserId,
    teamIdsFromLpSide: Set[TeamId],
    investorGroupIdOpt: Option[FundSubInvestorGroupId]
  )(
    using fileService: FileService
  ): Task[Seq[FolderId]] = {
    for {
      allInvestorGroupModel <- FundSubInvestorGroupUtils.getInvestorGroupModel(
        FundSubInvestorGroupId.PredefinedId.AllInvestorGroup(lpId.parent)
      )
      investorGroupModelOpt <- ZIOUtils.traverseOption(investorGroupIdOpt)(
        FundSubInvestorGroupUtils.getInvestorGroupModel
      )
      // LP's main folders shared between LP and authorized GPs
      lpFolderTypes = FolderType.values.toList
      lpFolderIds <- ZIO.foreach(lpFolderTypes) { lpFolderType =>
        val lpFolderTypeId = lpFolderType(lpId)
        val lpFolderId = FolderId.channelSystemFolderId(lpFolderTypeId)
        for {
          lpFolderExisted <- fileService.existFolder(actor)(lpFolderId)
          folderId <-
            if (lpFolderExisted) {
              ZIO.attempt(lpFolderId)
            } else {
              val authorizedTeamIds = lpFolderType match {
                case FolderType.SubscriptionDoc | FolderType.CountersignedDoc =>
                  Set(allInvestorGroupModel.subscriptionDocAuthorizedTeamId)
                    ++ investorGroupModelOpt.map(_.subscriptionDocAuthorizedTeamId)
                case FolderType.SupportingDoc =>
                  Set(allInvestorGroupModel.supportingDocAuthorizedTeamId)
                    ++ investorGroupModelOpt.map(_.supportingDocAuthorizedTeamId)
                case FolderType.ReferenceDoc =>
                  Set(allInvestorGroupModel.referenceDocAuthorizedTeamId)
                    ++ investorGroupModelOpt.map(_.referenceDocAuthorizedTeamId)
                case FolderType.SideLetter =>
                  Set(allInvestorGroupModel.sideLetterAuthorizedTeamId)
                    ++ investorGroupModelOpt.map(_.sideLetterAuthorizedTeamId)
              }

              val folderPermission = lpFolderType match {
                case FolderType.SubscriptionDoc | FolderType.SupportingDoc | FolderType.SideLetter =>
                  FileFolderPermissionMap(
                    teamPermissions = (authorizedTeamIds ++ teamIdsFromLpSide).map(_ -> FileFolderPermission.Own).toMap
                  )
                case FolderType.CountersignedDoc =>
                  FileFolderPermissionMap(
                    teamPermissions = authorizedTeamIds.map(_ -> FileFolderPermission.Own).toMap
                  )
                case FolderType.ReferenceDoc =>
                  FileFolderPermissionMap(
                    teamPermissions = authorizedTeamIds.map(_ -> FileFolderPermission.Own).toMap
                      ++ teamIdsFromLpSide.map(_ -> FileFolderPermission.Read)
                  )
              }
              fileService.createSystemFolderForChannel(
                lpFolderTypeId,
                lpFolderTypeId.value.value,
                actor,
                folderPermission
              )
            }
        } yield folderId
      }

      // FundSub folder shared among all LPs and GPs
      addedPermissionToSharedFolder = FileFolderPermissionMap(
        teamPermissions = teamIdsFromLpSide.map(_ -> FileFolderPermission.Read).toMap
      )
      _ <- fileService.modifyPermissionsRecursively(
        actor,
        FolderId.channelSystemFolderId(lpId.parent),
        PermissionChanges(addedPermissionToSharedFolder),
        mode = FileService.PermissionModMode.Unsafe
      )
    } yield lpFolderIds
  }

  def setUpSignatureModule(
    lpId: FundSubLpId,
    ownerTeamIds: Seq[TeamId],
    actor: UserId,
    viewOnlyTeamIds: Seq[TeamId] = Seq.empty
  ): Task[SignatureModuleId] = {
    val fundSubId = lpId.parent
    val config = signatureIntegrationService.customSignatureModuleConfig(
      client = SignatureModuleClient().withWorkflowClient(WorkflowClient(fundSubId)),
      recipientFeature = SignatureRecipientFeature(canUploadSignedCopy = true)
    )
    val ownerTeamPermissions = ownerTeamIds.map(_ -> FundSubInvitationHelper.SignatureModuleOwnerTeamPermissions).toMap
    val viewOnlyTeamPermissions =
      viewOnlyTeamIds.map(_ -> FundSubInvitationHelper.SignatureModuleViewOnlyTeamPermissions).toMap
    signatureIntegrationService.createModule(
      Some(fundSubId.parent),
      config,
      actor,
      Map.empty,
      ownerTeamPermissions ++ viewOnlyTeamPermissions
    )
  }

  def setUpFundSubSignatureModelForLp(
    creatingLpId: FundSubLpId,
    teamIdsFromLpSide: Set[TeamId],
    investorGroupIdOpt: Option[FundSubInvestorGroupId],
    actor: UserId
  ): Task[FundSubSignatureModel] = {
    for {
      _ <- ZIO.logInfo(s"$actor is setting up signature modules for investor $creatingLpId")
      allInvestorGroupModel <- FundSubInvestorGroupUtils.getInvestorGroupModel(
        FundSubInvestorGroupId.PredefinedId.AllInvestorGroup(creatingLpId.parent)
      )
      investorGroupModelOpt <- ZIOUtils.traverseOption(investorGroupIdOpt) { investorGroupId =>
        FundSubInvestorGroupUtils.getInvestorGroupModel(investorGroupId)
      }
      subscriptionDocAuthorizedTeamIds = allInvestorGroupModel.subscriptionDocAuthorizedTeamId
        +: investorGroupModelOpt.map(_.subscriptionDocAuthorizedTeamId).toSeq
      supportingDocAuthorizedTeamIds = allInvestorGroupModel.supportingDocAuthorizedTeamId
        +: investorGroupModelOpt.map(_.supportingDocAuthorizedTeamId).toSeq
      sideLetterAuthorizedTeamIds = allInvestorGroupModel.sideLetterAuthorizedTeamId
        +: investorGroupModelOpt.map(_.sideLetterAuthorizedTeamId).toSeq

      subscriptionDocSignatureModuleId <- setUpSignatureModule(
        creatingLpId,
        ownerTeamIds = subscriptionDocAuthorizedTeamIds ++ teamIdsFromLpSide,
        actor
      )
      countersignatureModuleId <- setUpSignatureModule(
        creatingLpId,
        ownerTeamIds = subscriptionDocAuthorizedTeamIds,
        actor
      )
      supportingFormSignatureModuleId <- setUpSignatureModule(
        creatingLpId,
        ownerTeamIds = supportingDocAuthorizedTeamIds ++ teamIdsFromLpSide,
        actor
      )
      additionalSignatureModuleId <- setUpSignatureModule(
        creatingLpId,
        ownerTeamIds = supportingDocAuthorizedTeamIds,
        actor,
        viewOnlyTeamIds = teamIdsFromLpSide.toSeq
      )
      sideLetterSignatureModuleId <- setUpSignatureModule(
        creatingLpId,
        ownerTeamIds = sideLetterAuthorizedTeamIds ++ teamIdsFromLpSide,
        actor
      )
      model = FundSubSignatureModel(
        fundSubLpId = creatingLpId,
        subscriptionDocSignatureModuleId = subscriptionDocSignatureModuleId,
        countersignatureModuleId = countersignatureModuleId,
        supportingFormSignatureModuleId = supportingFormSignatureModuleId,
        additionalSignatureModuleId = additionalSignatureModuleId,
        sideLetterSignatureModuleId = sideLetterSignatureModuleId
      )
      _ <- FDBRecordDatabase.transact(FundSubSignatureStoreOperations.Production) { ops =>
        ops.create(model)
      }
    } yield model
  }

  def compensateSetUpLpForm(
    lpFormId: FundSubLpFormIdTrait,
    teamIds: Set[TeamId]
  ): Task[Unit] = {
    for {
      _ <- lpFormId match {
        case fundSubLpFormId: FundSubLpFormId =>
          FDBClient.transact(LpForm.lpFormSubspace.clearKey(fundSubLpFormId))
        case fundSubFormVersionId: FundSubLpFormVersionId =>
          formService.updatePermission(
            versionId = fundSubFormVersionId.parent,
            grantedTeams = teamIds
          )
      }
      _ <- FDBRecordDatabase.transact(LpFormDataOperations.Production) { ops =>
        ops.delete(lpFormId)
      }
    } yield ()
  }

  def setUpFundSubLpModel(
    lpId: FundSubLpId,
    lpUserId: UserId,
    collaboratorUserIds: Seq[UserId],
    lpTeamId: TeamId,
    sharedDocs: Seq[FileId],
    lpOrderType: LpOrderType,
    firmName: String,
    customId: String,
    s3FormInfoOpt: Option[S3FormChangeInfo],
    expectedCommitment: String,
    lpFormId: FundSubLpFormIdTrait,
    formVersionIdOpt: Option[FormVersionId],
    closeId: Option[FundSubCloseId],
    investorGroupIdOpt: Option[FundSubInvestorGroupId],
    actor: UserId,
    invitationType: LpInvitationType,
    initialFormDataOpt: Option[Either[Map[AliasId, Json], GaiaState]],
    importInvestorId: String = "",
    useInitialGaiaStateDirectly: Boolean = false,
    initialFormDataSourceOpt: Option[FormDataSource] = None,
    duplicatedFromLpIdOpt: Option[FundSubLpId],
    metadata: Map[String, String] = Map.empty,
    advisorGroupIdOpt: Option[FundSubRiaGroupId]
  )(
    natsNotificationService: NatsNotificationService
  ): Task[SetUpLpModelResp] = {
    val fundSubId = lpId.parent
    for {
      (fundSubModel, adminResModel) <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        for {
          fundSubModel <- ops.getFundSubPublicModel(fundSubId)
          adminResModel <- ops.getFundSubAdminRestrictedModel(fundSubId)
        } yield fundSubModel -> adminResModel
      }

      lpFlowState <- ZIO.attempt(
        fundSubLpFlowTaskHandler.createLpFlowState(
          actor,
          lpId,
          lpOrderType
        )
      )

      trimmedExpectedCommitment = expectedCommitment.trim.filter(char => char.isDigit || char == '.')
      expectedCommitmentOpt = Option.when(trimmedExpectedCommitment.nonEmpty) {
        MoneyMessage(
          unit = adminResModel.investmentFunds.headOption.map(_.currency).getOrElse(CurrencyMessage.USD),
          trimmedExpectedCommitment
        )
      }
      commitments = expectedCommitmentOpt
        .map { expectedCommitment =>
          // assume that there must be one investment fund
          adminResModel.investmentFunds.headOption.map { investmentFund =>
            investmentFund.fundId -> LpCommitment(expectedCommitment = Some(expectedCommitment))
          }.toMap
        }
        .getOrElse(Map.empty)

      docRequestIdOpt <- newSupportingDocService.prepareDocRequestIdIfNecessary(
        lpId,
        Set(lpTeamId),
        investorGroupIdOpt
      )

      lpModel <- ZIO.attempt(
        FundSubLpModel(
          fundSubLpId = lpId,
          mainLp = lpUserId,
          collaborators = collaboratorUserIds,
          teamId = Some(lpTeamId),
          docRequestIdOpt = docRequestIdOpt,
          attachedDocs = sharedDocs,
          orderType = lpOrderType,
          firmName = firmName,
          customId = customId,
          addedAt = Option(Instant.now),
          lpState = Some(lpFlowState),
          formChangeInfo = s3FormInfoOpt,
          commitments = commitments,
          duplicatedFromLpIdOpt = duplicatedFromLpIdOpt,
          creatorOpt = Option(actor),
          metadata = metadata,
          advisorGroupIdOpt = advisorGroupIdOpt
        )
      )

      lpRestrictedModel <- ZIO.attempt(
        FundSubLpRestrictedModel(
          fundSubLpRestrictedId = FundSubLpRestrictedId(lpId),
          formIds = Seq(lpFormId),
          formVersionIdOpt = formVersionIdOpt,
          importInvestorId = importInvestorId,
          invitationTypeOpt = Some(invitationType)
        )
      )

      defaultCloseId = adminResModel.defaultCloseId

      closeData <- FDBRecordDatabase.transact(
        FDBOperations[((FundSubLpModelStoreOperations, FundSubModelStoreOperations), FundSubCloseOperations)].Production
      ) { case ((lpModelOps, adminModelOpt), closeOps) =>
        for {
          _ <- lpModelOps.updateOrCreateFundSubLpModel(
            id = lpModel.fundSubLpId,
            initialModel = lpModel,
            updateFn = _ => lpModel
          )
          _ <- lpModelOps.updateOrCreateFundSubLpRestrictedModel(
            id = lpRestrictedModel.fundSubLpRestrictedId,
            initialModel = lpRestrictedModel,
            updateFn = _ => lpRestrictedModel
          )
          _ <- adminModelOpt.updateFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId)) {
            (model: FundSubAdminRestrictedModel) =>
              model.copy(
                lpIds = (Seq(lpId) ++ model.lpIds).distinct
              )
          }
          closeData <- closeOps.update(FundSubCloseDataId(fundSubId)) { (model: FundSubCloseDataModel) =>
            val closeIdForNewLp = Some(closeId.getOrElse(defaultCloseId))
            model.copy(
              lpToCloseId = model.lpToCloseId ++ closeIdForNewLp.map(lpId -> _).toMap
            )
          }
        } yield closeData
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubLpModel(lpId)(natsNotificationService)
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubLpRestrictedModel(lpId)(natsNotificationService)

      _ <- fundSubEnvironmentPolicyService.handleMainInvestorAddedToFundSub(
        lpId,
        lpUserId
      )
      _ <- ZIO.foreach(collaboratorUserIds) {
        fundSubEnvironmentPolicyService.handleCollaboratorAddedToFundSub(
          lpId,
          _,
          FundSubLpEnvironmentSSOType.CollaboratorInvitedByGp
        )
      }

      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService.createFundInvestorRelationR(fundSubId, lpId)
      _ <- fundSubPermissionService.createInvestorMemberRelationsR(lpId, Set(lpUserId) ++ collaboratorUserIds)
      _ <- ZIOUtils.traverseOptionUnit(advisorGroupIdOpt) { fundSubRiaGroupId =>
        for {
          _ <- FundSubRiaPermissionUtils.createRiaFundGroupOrderRelationsR(fundSubRiaGroupId, lpId)
          linkedRiaFundGroupIdOpt <- FDBRecordDatabase.transact(FundSubRiaGroupStoreOperations.Production) { ops =>
            ops.getOpt(fundSubRiaGroupId).map(_.flatMap(_.linkedRiaFundGroupId))
          }
          linkedRiaFundGroupId <- ZIOUtils.optionToTask(
            linkedRiaFundGroupIdOpt,
            FundSubRiaGroupHasNotBeenLinked(fundSubRiaGroupId)
          )
          _ <- riaExternalIntegrationService.createRiaOrderRelations(
            lpId,
            linkedRiaFundGroupId,
            lpUserId,
            actor
          )
        } yield ()
      }

      // Create first LP submission version for LP if initialFormDataOpt is nonEmpty (only apply for LP Flexible Workflow)
      createFirstVersionResp <- ZIOUtils
        .whenOption(fundSubModel.lpFlowType == LpFlowType.Flexible)(
          ZIOUtils.traverseOption(initialFormDataOpt) { initialFormData =>
            fundSubSubscriptionDocService.createFirstVersion(
              lpId,
              actor,
              initialFormData.toOption,
              useInitialGaiaStateDirectly,
              initialFormDataSourceOpt
            )
          }
        )
        .map(_.flatten)
      // TODO: make sure creation of LpInfoRecord is idempotent
      lpInfoRecord <- fundSubLpDashboardService.createLpInfoRecord(
        lpId = lpId,
        lpUserId = lpUserId,
        collaboratorUserIds = collaboratorUserIds,
        fundSubCloseIdOpt = closeData.lpToCloseId.get(lpId),
        customId = customId,
        commitments = lpModel.commitments,
        invitationType = invitationType,
        orderType = lpOrderType,
        firmName = firmName
      )
      (updatedLpInfoRecord, _) <- ZIOUtils
        .traverseOption(createFirstVersionResp) { createFirstVersionResp =>
          updateFundSubFormWorkflowData(lpId, createFirstVersionResp, adminResModel.investmentFunds)
        }
        .map(_.flatten.unzip)
      finalLpInfoRecord = updatedLpInfoRecord.getOrElse(lpInfoRecord)

      // Always add investor into All Investors group. Optional add investor to custom group if any
      // LP folders' permission has been set up during initialization process
      _ <- fundSubInvestorGroupService.addInvestorToGroupPure(
        FundSubInvestorGroupId.PredefinedId.AllInvestorGroup(fundSubId),
        lpId,
        actor,
        shouldSkipFolderPermissionUpdate = true
      )
      _ <- ZIOUtils.traverseOptionUnit(
        investorGroupIdOpt.filterNot(FundSubInvestorGroupId.PredefinedId.AllInvestorGroup.isMatch)
      ) { investorGroupId =>
        fundSubInvestorGroupService.addInvestorToGroupPure(
          investorGroupId,
          lpId,
          actor,
          shouldSkipFolderPermissionUpdate = true
        )
      }
      _ <- fundSubLpStatusHistoryService.createLpStatusActivityHistory(lpId)
      _ <- fundSubLpStatusHistoryService.addLpStatusActivity(
        id = Left(lpId),
        lpStatus = lpFlowState.getLpStatus
      )
    } yield SetUpLpModelResp(
      lpFormIdOpt = createFirstVersionResp.map(_.submissionVersionModel.lpFormId),
      firmName = finalLpInfoRecord.firmName,
      lpStatus = lpFlowState.getLpStatus,
      closeId = closeData.lpToCloseId.get(lpId),
      expectedCommitment = expectedCommitmentOpt,
      commitments = finalLpInfoRecord.commitments
    )
  }

  def compensateCreateFundSubModel(
    lpId: FundSubLpId,
    lpUserId: UserId,
    collaboratorUserIds: Seq[UserId],
    actor: UserId
  ): Task[Unit] = {
    val fundId = lpId.parent
    for {
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundId)
      _ <- fundSubInvestorGroupService.completelyRemoveInvestorFromAllGroupsUnsafe(
        fundId,
        lpId,
        actor
      )
      _ <- FDBRecordDatabase.transact(LpInfoStoreProvider.Production) { store =>
        LpInfoOperations(store).delete(lpId)
      }
      _ <- fundSubPermissionService.removeFundInvestorRelationR(lpId)
      _ <- fundSubPermissionService.removeInvestorMemberRelationsR(lpId, Set(lpUserId) ++ collaboratorUserIds)

      // The FundSubRiaGroupId might have already been deleted at this point.
      // If it has been deleted, retrieving the RiaFundGroupId from it will be impossible.
      // Therefore, we should avoid passing the FundSubRiaGroupId into this function.
      _ <- FundSubRiaPermissionUtils.removeRiaFundGroupOrderRelationsR(lpId)
      _ <- riaExternalIntegrationService.compensateCreateRiaOrderRelations(lpId, actor)

      _ <- FDBRecordDatabase.transact(
        FDBOperations[((FundSubLpModelStoreOperations, FundSubModelStoreOperations), FundSubCloseOperations)].Production
      ) { case ((lpModelOps, adminModelOps), closeOps) =>
        for {
          _ <- lpModelOps.removeFundSubLpModel(lpId)
          _ <- lpModelOps.removeFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId))
          _ <- adminModelOps.updateFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundId)) {
            (model: FundSubAdminRestrictedModel) =>
              model
                .copy(lpIds = model.lpIds.filterNot(_ == lpId))
          }
          _ <- closeOps.update(FundSubCloseDataId(fundId)) { (model: FundSubCloseDataModel) =>
            model.copy(
              lpToCloseId = model.lpToCloseId - lpId
            )
          }
        } yield ()
      }
      _ <- greylinDataService.runUnit(
        SubscriptionOrderOperations.delete(lpId)
      )
      _ <- greylinDataService.runUnit(
        SubscriptionOrderMemberOperations.delete(lpId)
      )
      _ <- fundSubEnvironmentPolicyService.handleMainInvestorRemovedFromFundSub(lpId, lpUserId)
      _ <- ZIO.foreach(collaboratorUserIds) {
        fundSubEnvironmentPolicyService.handleCollaboratorRemovedFromFundSub(lpId, _, saveTombstone = false)
      }

    } yield ()
  }

  def setUpLpTags(
    lpId: FundSubLpId,
    actor: UserId,
    tagNames: Seq[String]
  ): Task[Unit] = {
    ZIOUtils.when(tagNames.nonEmpty) {
      fundSubLpTagUtilService.updateTagsOfLp(
        lpId = lpId,
        updateTagsFnc = _ => tagNames.map(tagName => TagInfo(tagName.trim, LpTagColor.Gray1)).toSet,
        actor = actor
      )
    }
  }

  def setUpContact(
    lpId: FundSubLpId,
    actor: UserId,
    invitationType: LpInvitationType
  ): Task[Unit] = {
    invitationType match {
      case LpInvitationType.Normal | LpInvitationType.OfflineOrder =>
        addLpAndCollaboratorsToContact(lpId, actor, skipPermissionCheck = true)
      case LpInvitationType.InvestedInAdditionalFund =>
        fundSubContactService.syncLpContactToGroup(lpId, actor)
      case LpInvitationType.ViaInvitationLink =>
        fundSubContactService.selfAddLpContact(actor, lpId).map(_ => ())
      case _ => ZIO.unit
    }
  }

  private def addLpAndCollaboratorsToContact(
    lpId: FundSubLpId,
    actor: UserId,
    skipPermissionCheck: Boolean // TODO: @huyha to remove this param when creating a separate invitation function for RIA
  ) = {
    for {
      fundSubLpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.getFundSubLpModel(lpId)
      }
      lpContact <- fundSubContactService.getUserContactData(fundSubLpModel.mainLp)
      collaboratorContacts <- ZIO.foreach(fundSubLpModel.collaborators)(
        fundSubContactService.getUserContactData
      )
      _ <-
        if (fundSubLpModel.firmName.isEmpty && fundSubLpModel.collaborators.isEmpty) {
          fundSubContactService
            .addLpContact(
              actor,
              lpId.parent,
              Seq(lpContact),
              skipPermissionCheck = skipPermissionCheck
            )
            .map(_ => ())
        } else {
          val groupName = if (fundSubLpModel.firmName.isEmpty) {
            s"${lpContact.firstName} ${lpContact.lastName}"
          } else {
            fundSubLpModel.firmName
          }
          fundSubContactService
            .addContactsToGroup(
              fundSubId = lpId.parent,
              actor = actor,
              groupName = groupName,
              customId = fundSubLpModel.customId.trim,
              mainLpEmailOpt = Some(lpContact.email),
              contacts = lpContact +: collaboratorContacts,
              skipPermissionCheck = skipPermissionCheck
            )
            .map(_ => ())
        }
    } yield ()
  }

  def sendEmailForAddingLp(
    lpId: FundSubLpId,
    actor: UserId,
    invitationType: LpInvitationType,
    inviteLpCustomEmailTemplate: Option[EmailTemplateMessage],
    newFirmName: String,
    originalLpIdOpt: Option[FundSubLpId] = None,
    skipInvitationEmailContacts: List[String] = List.empty,
    enableSSOContacts: List[String] = List.empty
  ): Task[SendEmailResult] = {
    for {
      lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.getFundSubLpModel(lpId)
      }
      lpUserId = lpModel.mainLp
      lpUserEmail <- userProfileService.getEmailAddress(lpUserId).map(_.address)
      result <- invitationType match {
        case LpInvitationType.Normal =>
          for {
            mainLpEmailIdOpt <-
              if (skipInvitationEmailContacts.exists(_.equalsIgnoreCase(lpUserEmail))) {
                ZIO.attempt[Option[InternalEmailId]](None)
              } else {
                val enableSSO = enableSSOContacts.exists(_.equalsIgnoreCase(lpUserEmail))
                fundSubEmailService
                  .sendInviteLpEmail(
                    actor,
                    lpUserId,
                    lpId,
                    attachedDocs = lpModel.attachedDocs,
                    userCustomTemplateOpt = inviteLpCustomEmailTemplate,
                    enableSSO = enableSSO
                  )
                  .map(_.flatMap(_.internalIdOpt))
              }
            collaboratorEmailIds <- ZIO
              .foreach(lpModel.collaborators) { collaborator =>
                for {
                  email <- userProfileService.getEmailAddress(collaborator).map(_.address)
                  internalEmailIdOpt <-
                    if (skipInvitationEmailContacts.exists(_.equalsIgnoreCase(email))) {
                      ZIO.attempt[Option[InternalEmailId]](None)
                    } else {
                      val enableSSO = enableSSOContacts.exists(_.equalsIgnoreCase(email))
                      fundSubEmailService
                        .sendInviteCollaboratorEmail(
                          actor,
                          collaborator,
                          lpId,
                          lpModel.attachedDocs,
                          userCustomTemplateOpt = inviteLpCustomEmailTemplate,
                          enableSSO = enableSSO
                        )
                        .map(_.flatMap(_.internalIdOpt))
                    }
                } yield internalEmailIdOpt
              }
              .map(_.flatten)
          } yield SendEmailResult.NormalInviteEmailResult(NormalInviteEmailResult(mainLpEmailIdOpt, collaboratorEmailIds))

        case LpInvitationType.InvestedInAdditionalFund =>
          for {
            lpTeamEmailIds <- originalLpIdOpt.fold[Task[Seq[InternalEmailId]]] {
              ZIO.attempt(Seq.empty)
            } { originalLpId =>
              ZIO
                .foreach(lpUserId +: lpModel.collaborators) { userId =>
                  if (actor != userId) {
                    fundSubEmailService.sendLpDuplicatedOrderLpEmail(
                      fundSubLpId = lpId,
                      lpUserId = lpUserId,
                      actorUserId = actor,
                      receiverUserId = userId,
                      originalLpId = originalLpId,
                      newFirmName = newFirmName
                    )
                  } else {
                    ZIO.attempt(None)
                  }
                }
                .map(_.flatMap(_.flatMap(_.internalIdOpt)))
            }
          } yield SendEmailResult.InAdditionalFundEmailResult(
            InvestedInAdditionalFundEmailResult(lpTeamEmailIds = lpTeamEmailIds)
          )
        case LpInvitationType.OfflineOrder =>
          ZIO.attempt(SendEmailResult.AddOfflineOrderEmailResult(AddOfflineOrderEmailResult()))
        case LpInvitationType.ViaInvitationLink =>
          ZIO.attempt(SendEmailResult.JoinViaInvitationLink(JoinViaInvitationLink()))
        case _ => ZIO.attempt(SendEmailResult.Empty)
      }
    } yield result
  }

  def sendEmailForAddingMultipleLps(
    lpIdsBatches: Seq[Seq[FundSubLpId]],
    actor: UserId,
    invitationType: LpInvitationType,
    inviteLpCustomEmailTemplate: Option[EmailTemplateMessage],
    inviteCollaboratorCustomEmailTemplate: Option[EmailTemplateMessage],
    skipInvitationEmailsMap: Map[FundSubLpId, Seq[String]]
  ): Task[Map[FundSubLpId, Seq[FundSubAuditLogService.AddEventEmailParam]]] = {
    invitationType match {
      case LpInvitationType.Normal =>
        for {
          skipInvitationUserIdsMap <- ZIO
            .foreach(skipInvitationEmailsMap.toList) { case (lpId, emails) =>
              for {
                userIds <- ZIOUtils
                  .foreachParN(4)(emails) { email =>
                    userProfileService
                      .getUserIdFromEmailAddress(email)
                      .map(Some(_))
                      .catchSome { case _: UserProfileService.EmailNotFound =>
                        ZIO.succeed(None)
                      }
                  }
                  .map(_.flatten)
              } yield Option.when(userIds.nonEmpty) { lpId -> userIds }
            }
            .map(_.flatten.toMap)
          lpIdAndEmailEventParamOpts <- ZIO
            .foreach(lpIdsBatches) { lpIds =>
              for {
                lpModels <- FDBRecordDatabase
                  .transact(FundSubLpModelStoreOperations.Production) { ops =>
                    RecordIO.parTraverseN(4)(lpIds) { lpId =>
                      ops.getFundSubLpModel(lpId)
                    }
                  }
                userIdAndLpModels = lpModels
                  .flatMap { model =>
                    val skipInvitationUserIds = skipInvitationUserIdsMap.getOrElse(model.fundSubLpId, List.empty)
                    val userIdLpModelTuples = (model.mainLp -> model) +: model.collaborators.map(_ -> model)
                    userIdLpModelTuples.filterNot { case (userId, _) => skipInvitationUserIds.contains(userId) }
                  }
                  .groupMap(_._1)(_._2)
                  .toSeq
                resp <- ZIO
                  .foreach(userIdAndLpModels) { case (invitee, lpModels) =>
                    sendEmailPerUserForAddingMultipleLps(
                      lpModels,
                      actor,
                      invitee,
                      invitationType,
                      inviteLpCustomEmailTemplate,
                      inviteCollaboratorCustomEmailTemplate
                    )
                  }
                  .map(_.flatten)
              } yield resp
            }
            .map(_.flatten)
        } yield {
          val lpIdAndEmailEventParamOptsMap = lpIdAndEmailEventParamOpts.groupMap(_._1)(_._2)
          val lpIdAndEmailEventParamsMap = lpIdAndEmailEventParamOptsMap.map { case (k, v) =>
            k -> v.flatten
          }
          lpIdAndEmailEventParamsMap
        }
      case LpInvitationType.OfflineOrder =>
        ZIO.succeed(lpIdsBatches.flatten.map { _ -> Seq.empty }.toMap)
      case _ => ZIO.succeed(Map.empty)
    }
  }

  def createLinkForInvestInAdditionalEntityFromMultipleLps(
    lpIds: Seq[FundSubLpId],
    userId: UserId
  ): Task[Option[FundSubSingleUserInvitationLinkId]] = {
    val unsafeLinkId = FundSubSingleUserInvitationLinkIdFactory.unsafeRandomId
    for {
      fundSubId <- ZIOUtils.uniqueSeqToTask(
        lpIds.map(_.parent),
        GeneralServiceException("All lps must be from one fundsub")
      )
      allowInvestFromAdditionalEntity <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
        fundSubOps =>
          fundSubOps
            .getFundSubPublicModel(fundSubId)
            .map(_.featureSwitch.exists(!_.disableInvestFromAdditionalEntity))
      }
      linkOpt <- ZIO.when(allowInvestFromAdditionalEntity) {
        FDBRecordDatabase.transact(FundSubSingleUserInvitationLinkStoreOperations.Production) { ops =>
          ops.create(
            FundSubSingleUserInvitationLink(
              singleUserLinkId = unsafeLinkId,
              userId = userId,
              lpIds = lpIds
            )
          )
        }
      }
    } yield linkOpt.map(_.singleUserLinkId)
  }

  private def sendEmailPerUserForAddingMultipleLps(
    lpModels: Seq[FundSubLpModel],
    actor: UserId,
    invitee: UserId,
    invitationType: LpInvitationType,
    inviteLpCustomEmailTemplate: Option[EmailTemplateMessage],
    inviteCollaboratorCustomEmailTemplate: Option[EmailTemplateMessage]
  ): Task[Seq[(FundSubLpId, Option[FundSubAuditLogService.AddEventEmailParam])]] = {
    invitationType match {
      case LpInvitationType.Normal =>
        val shouldUseLpEmailTemplate = lpModels.exists(_.mainLp == invitee)
        for {
          linkIdOpt <- createLinkForInvestInAdditionalEntityFromMultipleLps(
            lpModels.map(_.fundSubLpId),
            invitee
          )
          emailParamOpt <-
            if (shouldUseLpEmailTemplate) {
              fundSubEmailService
                .sendInviteMultipleLpsPerMainLpEmail(
                  inviter = actor,
                  invitee = invitee,
                  lpIds = lpModels.map(_.fundSubLpId),
                  investAdditionalLinkIdOpt = linkIdOpt,
                  userCustomTemplateOpt = inviteLpCustomEmailTemplate
                )
                .map(
                  _.flatMap(
                    _.internalIdOpt
                      .map(id =>
                        FundSubAuditLogService.AddEventEmailParam(FundSubEvent.inviteMultipleLpsPerMainLp, Seq(id))
                      )
                  )
                )
            } else {
              fundSubEmailService
                .sendInviteMultipleLpsPerCollaboratorLpEmail(
                  inviter = actor,
                  invitee = invitee,
                  lpIds = lpModels.map(_.fundSubLpId),
                  investAdditionalLinkIdOpt = linkIdOpt,
                  userCustomTemplateOpt = inviteCollaboratorCustomEmailTemplate
                )
                .map(
                  _.flatMap(
                    _.internalIdOpt
                      .map(id =>
                        FundSubAuditLogService.AddEventEmailParam(FundSubEvent.inviteMultipleLpsPerCollaborator, Seq(id))
                      )
                  )
                )
            }
        } yield {
          lpModels.map(model => (model.fundSubLpId, emailParamOpt))
        }
      case _ => ZIO.attempt(Seq.empty)
    }
  }

  def createFundSubActivityLog(
    lpId: FundSubLpId,
    lpUserId: UserId,
    collaboratorUserIds: Seq[UserId],
    invitationType: LpInvitationType,
    actor: UserId,
    originalLpIdOpt: Option[FundSubLpId],
    sendEmailResult: Option[SendEmailResult],
    advisorGroupIdOpt: Option[FundSubRiaGroupId],
    ipAddress: Option[String],
    skipFundAdminActivityLog: Boolean = false,
    investmentEntity: String = "",
    skipFundSubAuditLog: Boolean = false
  ): Task[Unit] = {
    for {
      _ <- fundSubLpActivityLogService.logActivity(
        lpId = lpId,
        actorOpt = Option(actor),
        detail = anduin.protobuf.fundsub.activitylog.lp.LpInvited(
          viaProtectedLink = invitationType.isViaInvitationLink,
          selfDuplicated = invitationType.isInvestedInAdditionalFund,
          isOfflineOrder = invitationType.isOfflineOrder,
          investmentEntity = investmentEntity
        )
      )
      _ <- ZIO.foreach(collaboratorUserIds) { collaborator =>
        fundSubLpActivityLogService.logActivity(
          lpId = lpId,
          actorOpt = Option(actor),
          at = Option(DateCalculator.instantNow),
          detail = CollaboratorInvited(
            collaborator,
            isOfflineOrder = invitationType.isOfflineOrder
          )
        )
      }
      _ <- ZIOUtils.unless(skipFundAdminActivityLog)(
        createFundAdminActivityLog(
          lpId = lpId,
          lpUserId = lpUserId,
          collaboratorUserIds = collaboratorUserIds,
          actor = actor,
          originalLpIdOpt = originalLpIdOpt,
          invitationType = invitationType
        )
      )
      _ <- ZIOUtils.unless(skipFundSubAuditLog)(
        createFundSubAuditLogEvent(
          lpId = lpId,
          actor = actor,
          ipAddress = ipAddress,
          sendEmailResultOpt = sendEmailResult,
          investmentEntity = investmentEntity,
          advisorGroupIdOpt = advisorGroupIdOpt
        )
      )
    } yield ()
  }

  private def createFundSubAuditLogEvent(
    ipAddress: Option[String],
    sendEmailResultOpt: Option[SendEmailResult],
    lpId: FundSubLpId,
    actor: UserId,
    investmentEntity: String,
    advisorGroupIdOpt: Option[FundSubRiaGroupId]
  ) = {
    val sendEmailResult = sendEmailResultOpt.get

    val (activityDetail, auditLogActivityType, eventEmails, actorType) = sendEmailResult match {
      case SendEmailResult.JoinViaInvitationLink(_) =>
        (
          anduin.protobuf.fundsub.activitylog.lp.LpInvited(viaProtectedLink = true),
          AuditLogEventType.INVESTOR_JOINED,
          Seq.empty,
          AuditLogActorType.InvestorSide
        )
      case SendEmailResult.AddOfflineOrderEmailResult(_) =>
        (
          anduin.protobuf.fundsub.activitylog.lp.LpInvited(isOfflineOrder = true),
          AuditLogEventType.OFFLINE_SUBSCRIPTION_ADDED,
          Seq.empty,
          AuditLogActorType.FundSide
        )
      case SendEmailResult.NormalInviteEmailResult(value) =>
        val collaboratorEventEmail = if (value.collaboratorEmailIds.nonEmpty) {
          Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.inviteLpCollaborator,
              emailIds = value.collaboratorEmailIds
            )
          )
        } else { Seq.empty }
        (
          anduin.protobuf.fundsub.activitylog.lp.LpInvited(),
          AuditLogEventType.INVESTOR_INVITED,
          Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.inviteLp,
              emailIds = Seq(value.lpEmailId).flatten
            )
          ) ++ collaboratorEventEmail,
          advisorGroupIdOpt.map(_ => AuditLogActorType.Ria).getOrElse(AuditLogActorType.FundSide)
        )
      case SendEmailResult.InAdditionalFundEmailResult(value) =>
        (
          anduin.protobuf.fundsub.activitylog.lp.LpInvited(selfDuplicated = true, investmentEntity = investmentEntity),
          AuditLogEventType.SUBSCRIPTION_DUPLICATED,
          Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.lpDuplicatedOrderParticipantNotification,
              emailIds = value.lpTeamEmailIds
            )
          ),
          AuditLogActorType.InvestorSide
        )
      case SendEmailResult.Empty =>
        (LpActivityInfo.Empty, AuditLogEventType.NULL, Seq.empty, AuditLogActorType.System)
    }

    fundSubAuditLogService.addEvent(
      fundSubId = lpId.parent,
      params = AddEventParam(
        actor = Some(actor),
        orderId = Option(lpId),
        actorType = actorType,
        eventType = auditLogActivityType,
        eventEmail = eventEmails,
        activityDetail = activityDetail,
        ipAddress = ipAddress
      )
    )
  }

  private def createFundAdminActivityLog(
    lpId: FundSubLpId,
    lpUserId: UserId,
    collaboratorUserIds: Seq[UserId],
    actor: UserId,
    originalLpIdOpt: Option[FundSubLpId],
    invitationType: LpInvitationType
  ) = {

    for {
      _ <- FundSubAdminActivityLogUtils.addActivity(
        fundSubId = lpId.parent,
        actor = actor,
        fundAdminActivity = invitationType match {
          case LpInvitationType.OfflineOrder      => OfflineOrderAdded(lpUserId, lpId)
          case LpInvitationType.ViaInvitationLink => LpJoinedViaInvitationLink(lpUserId, lpId)
          case LpInvitationType.InvestedInAdditionalFund =>
            LpInvestedInAdditionalFund(
              actor,
              originalLpId = originalLpIdOpt.getOrElse(lpId),
              lpId = lpId
            )
          case _ => anduin.protobuf.activitylog.fundsub.admin.LpInvited(lpUserId, lpId)
        }
      )
      _ <- ZIO.foreach(collaboratorUserIds) { collaborator =>
        FundSubAdminActivityLogUtils.addActivity(
          lpId.parent,
          actor,
          CollaboratorAdded(
            collaborator,
            lpId
          )
        )
      }
    } yield ()
  }

  def sendAmplitudeAndZapier(
    lpId: FundSubLpId,
    collaboratorUserIds: Seq[UserId],
    actor: UserId,
    firmName: String,
    invitationType: LpInvitationType,
    lpContact: NameEmailInfo,
    collaboratorContacts: Seq[NameEmailInfo],
    prefilledFromPastLp: Option[FundSubLpId],
    fromDataImport: Boolean,
    formDataSource: String,
    httpContext: Option[RequestContext]
  ): Task[Unit] = {
    for {
      fundSubPublicModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(lpId.parent)
      }

      entityModel <- EntityServiceUtils.execute(_.getEntityModel(fundSubPublicModel.investorEntity))
      _ <- fundSubLoggingService.logEventOrderCreated(
        actor,
        lpId,
        fundSubPublicModel.fundName,
        fundSubPublicModel.investorEntity,
        entityModel,
        viaProtectedLink = invitationType.isViaInvitationLink,
        selfDuplicated = invitationType.isInvestedInAdditionalFund,
        prefilledFromPastLp = prefilledFromPastLp,
        fromDataImport = fromDataImport,
        formDataSource = formDataSource,
        httpContext
      )
      _ <- ZIO.foreach(collaboratorUserIds) { collaborator =>
        fundSubLoggingService.logEventAddCollaborator(
          actor = actor,
          fundSubLpId = lpId,
          fundName = fundSubPublicModel.fundName,
          collaboratorId = collaborator,
          httpContext = httpContext
        )
      }
      _ <- fundSubZapierService.sendNewInvestorZapEvent(
        lpId,
        actor,
        fundSubPublicModel.fundName,
        firmName,
        lpContact,
        collaboratorContacts,
        invitationType
      )
    } yield ()
  }

  def completeSingleInvitation(
    fundSubBatchInvitationItemId: FundSubBatchInvitationItemId,
    fundSubLpId: FundSubLpId,
    syncGatewayUpdate: Boolean
  )(
    batchActionService: BatchActionService,
    natsNotificationService: NatsNotificationService
  ): Task[FundSubBatchInvitationItem] = {
    val newStatus = BatchActionItemStatusSucceeded(Some(Instant.now), Some(SucceededLp(fundSubLpId).asJson))
    val fundSubBatchInvitationId = fundSubBatchInvitationItemId.parent
    for {
      invitationItem <- FDBRecordDatabase.transact(FundSubBatchInvitationOperations.Production) { ops =>
        ops.updateItem(fundSubBatchInvitationItemId) { model =>
          model.copy(status = newStatus)
        }
      }
      _ <- batchActionService.updateSingleActionStatusInternal(
        invitationItem.batchActionItemId,
        _ => newStatus
      )
      _ <- ZIOUtils.when(syncGatewayUpdate) {
        for {
          invitationOpt <- FDBRecordDatabase.transact(FundSubBatchInvitationOperations.Production) { op =>
            op.getOpt(fundSubBatchInvitationId)
          }
          _ <- ZIOUtils.traverseOptionUnit(invitationOpt) { invitation =>
            FundSubSgwModelUtils.modifyLastUpdateFundSubBatchInvitationModel(
              invitation.actor,
              fundSubBatchInvitationId
            )(
              natsNotificationService
            )
          }
          _ <- FundSubCloseSgwUtils.updateFundSubCloseDataSgw(FundSubCloseDataId(fundSubLpId.parent))(
            natsNotificationService
          )
        } yield ()
      }
    } yield invitationItem
  }

  def markSingleInvitationFailed(
    fundSubBatchInvitationItemId: FundSubBatchInvitationItemId,
    error: BatchActionItemError,
    syncGatewayUpdate: Boolean
  )(
    batchActionService: BatchActionService,
    natsNotificationService: NatsNotificationService
  ): Task[FundSubBatchInvitationItem] = {
    val newStatus = BatchActionItemStatusFailed(Some(Instant.now), error)
    val fundSubBatchInvitationId = fundSubBatchInvitationItemId.parent
    for {
      invitationItem <- FDBRecordDatabase.transact(FundSubBatchInvitationOperations.Production) { ops =>
        ops.updateItem(fundSubBatchInvitationItemId) { model =>
          model.copy(status = newStatus)
        }
      }
      _ <- batchActionService.updateSingleActionStatusInternal(
        invitationItem.batchActionItemId,
        _ => newStatus
      )
      _ <- ZIOUtils.when(syncGatewayUpdate) {
        for {
          invitationOpt <- FDBRecordDatabase.transact(FundSubBatchInvitationOperations.Production) { op =>
            op.getOpt(fundSubBatchInvitationId)
          }
          _ <- ZIOUtils.traverseOptionUnit(invitationOpt) { invitation =>
            FundSubSgwModelUtils.modifyLastUpdateFundSubBatchInvitationModel(
              invitation.actor,
              fundSubBatchInvitationId
            )(
              natsNotificationService
            )
          }
        } yield ()
      }
    } yield invitationItem
  }

  def addOrderDataToDataPipeline(
    lpId: FundSubLpId,
    lpUserId: UserId,
    collaboratorUserIds: Seq[UserId],
    refinedFirmName: String,
    lpStatus: LpStatus,
    closeId: Option[FundSubCloseId],
    metadata: Map[String, String],
    expectedCommitmentOpt: Option[MoneyMessage],
    commitments: Map[InvestmentFundId, LpCommitment]
  ): Task[Unit] = {
    for {
      _ <- greylinDataService.runUnit(
        SubscriptionOrderOperations.upsert(
          SubscriptionOrder(
            id = lpId,
            fundSubscriptionId = lpId.parent,
            mainUserId = lpUserId,
            investmentEntityName = refinedFirmName,
            status = lpStatus,
            lastActivityAt = Some(Instant.now),
            metaData = Some(metadata.asJson.noSpaces),
            closeId = closeId
          ).withExpectedCommitment(
            expectedCommitmentOpt.map(SquantsIso.moneyIso.reverseGet)
          )
        )
      )

      _ <- greylinDataService.runUnit(
        SubscriptionOrderMemberOperations.insert(
          (lpUserId +: collaboratorUserIds).map { userId =>
            SubscriptionOrderMember(
              subscriptionOrderId = lpId,
              userId = userId
            )
          }.toList
        )
      )
      _ <- greylinDataService.runUnit(
        SubscriptionOrderSubFundInvestmentOperations.Default.updateBySubscriptionOrder(
          subscriptionOrderId = lpId,
          investments = FundSubCommitmentHelper.commitmentsToGreylinInvestment(lpId, commitments)
        )
      )
    } yield ()
  }

  def addOrderDataToDataLake(
    lpId: FundSubLpId,
    referenceDocs: Seq[FileId],
    metadata: Map[String, String],
    lpFormIdOpt: Option[FundSubLpFormIdTrait],
    advisorGroupIdOpt: Option[FundSubRiaGroupId],
    investorGroupIdOpt: Option[FundSubInvestorGroupId],
    actorId: UserId
  ): Task[Unit] = {
    val getParamsTask = for {
      lpRecord <- FDBRecordDatabase.transact(LpInfoOperations.Production) { ops =>
        ops.get(lpId)
      }
      mainLpInfo <- ZIO.attempt(
        UserBasicInfo(
          id = lpRecord.userId,
          email = lpRecord.email,
          firstName = lpRecord.firstName,
          lastName = lpRecord.lastName
        )
      )
      collaborators <- ZIO.attempt(
        lpRecord.collaborators.map { user =>
          UserBasicInfo(
            id = user.userId,
            email = user.email,
            firstName = user.firstName,
            lastName = user.lastName
          )
        }
      )
      referenceDocInfos <- FundSubDataLakeUtils.getFileInfoList(
        referenceDocs.toList,
        fileService,
        userProfileService
      )
      initialFormFieldValues <- lpFormIdOpt
        .flatMap(_.asNewLpFormId)
        .fold[Task[Seq[FormFieldValue]]](
          ZIO.succeed(Seq.empty)
        ) { lpFormVersionId =>
          getInitialFormFieldValues(lpId, lpFormVersionId, actorId)(formService)
        }
    } yield {
      val commitmentAmounts = lpRecord.commitments.toSeq.map { case (fundId, commitment) =>
        SubFundCommitmentAmountMessage(
          id = fundId,
          expectedCommitment = commitment.expectedCommitment
            .map(_.value)
            .map(CurrencyUtils.convertMoneyStringToFloatString(_).toDouble),
          submittedCommitment = commitment.submittedCommitment
            .map(_.value)
            .map(CurrencyUtils.convertMoneyStringToFloatString(_).toDouble),
          acceptedCommitment = commitment.acceptedCommitment
            .map(_.value)
            .map(CurrencyUtils.convertMoneyStringToFloatString(_).toDouble)
        )
      }
      AddOrderParams(
        lpIdOpt = Option(lpId),
        mainLp = Option(mainLpInfo),
        collaborators = collaborators,
        customId = lpRecord.customId,
        investmentEntity = lpRecord.firmName,
        commitmentAmounts = commitmentAmounts,
        closeIdOpt = lpRecord.fundSubCloseIdOpt,
        lpOrderType = lpRecord.orderType,
        referenceDocs = referenceDocInfos,
        createdAt = lpRecord.createdAt,
        lastActivityAt = lpRecord.createdAt,
        tags = lpRecord.tags,
        status = Option(lpRecord.status),
        initialFormProgress = lpRecord.formFillingProgress.toDouble,
        metadata = metadata,
        formFields = initialFormFieldValues,
        advisorGroupIdOpt = advisorGroupIdOpt,
        investorGroupIdOpt = investorGroupIdOpt.filterNot(FundSubInvestorGroupId.PredefinedId.AllInvestorGroup.isMatch)
      )
    }

    FundSubDataLakeUtils.sendUpdateParams(
      lpId.parent,
      getParamsTask,
      dataLakeIngestionService
    )
  }

  private def getInitialFormFieldValues(
    lpId: FundSubLpId,
    lpFormVersionId: FundSubLpFormVersionId,
    actor: UserId
  )(
    formService: FormService
  ): Task[Seq[FormFieldValue]] = {
    val formVersionId = lpFormVersionId.parent
    for {
      formDataOpt <- FDBRecordDatabase.transact(LpFormDataOperations.Production)(_.getOpt(lpFormVersionId))
      formFieldValues <- ZIOUtils
        .traverseOption(formDataOpt) { formData =>
          val state = formData.gaiaState
          for {
            getFormResp <- formService.getForm(
              formId = formVersionId.parent,
              versionIdOpt = Some(formVersionId),
              actor = actor,
              shouldCheckPermission = false
            )
            dashboardFormFieldsConfig <- FDBRecordDatabase
              .transact(
                FundSubModelStoreOperations.Production
              )(_.getFundSubPublicModel(lpId.parent))
              .map(_.dashboardConfig.map(_.formFieldsConfigs).getOrElse(Seq.empty))
            formFieldValues = FundSubDashboardDataUtils.getFormFieldsData(
              state = state,
              form = getFormResp.formData.form,
              fields = dashboardFormFieldsConfig
            )
          } yield formFieldValues
        }
        .map(_.getOrElse(Seq.empty))
    } yield formFieldValues
  }

  def updateSupportingDocsIfNecessary(
    lpId: FundSubLpId,
    actor: UserId
  )(
    natsNotificationService: NatsNotificationService
  ): Task[Unit] = {
    for {
      isFlexible <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops
          .getFundSubPublicModel(lpId.parent)
          .map(_.lpFlowType.isFlexible)
      }
      formInfo <- FundSubCommonUtils.getLpFormInfo(lpId: FundSubLpId, actor: UserId)
      requiredFiles = fundSubFormService.getFormRequiredDocs(formInfo)
      formIsCompleted = formInfo match {
        case gaiaFormInfo: FundSubFormIntegrationService.GaiaFormInfo       => gaiaFormInfo.missingRequiredFields == 0
        case dynamicFormInfo: FundSubFormIntegrationService.DynamicFormInfo => dynamicFormInfo.isCompleted
      }
      _ <- ZIOUtils.when(formIsCompleted) {
        if (isFlexible) {
          newSupportingDocService.updateFormRequiredDocs(lpId, requiredFiles)
        } else {
          SupportingDocUtils.updateFormRequiredSupportingDocsForLp(lpId, requiredFiles.files.toList)(
            natsNotificationService
          )
        }
      }
    } yield ()
  }

  def addMainLp(
    fundSubId: FundSubId,
    actor: UserId,
    toAddLp: UserId,
    lpInfo: FundSubLpInfo,
    toAddCollaborators: Seq[UserId] = Seq.empty,
    initialFormData: Option[Either[Map[AliasId, Json], GaiaState]] = None,
    fundSubCloseIdToAdd: Option[FundSubCloseId] = None,
    attachedDocs: Seq[FileId] = Seq.empty,
    lpOrderType: LpOrderType = LpOrderType.NormalOrder,
    investorGroupIdOpt: Option[FundSubInvestorGroupId] = None,
    httpContext: Option[RequestContext] = None,
    invitationType: LpInvitationType = LpInvitationType.Normal,
    originalLpIdOpt: Option[FundSubLpId] = None, // Use for LpInvitationType.InvestedInAdditionalFund
    advisorGroupIdOpt: Option[FundSubRiaGroupId] = None
  ): Task[FundSubLpId] = {
    val refinedToAddCollaborators = toAddCollaborators.distinct.filter(_ != toAddLp)
    for {
      (lpId, setUpOrderRes) <- initCoreServicesForAddingMainLp(
        fundSubId = fundSubId,
        lpUserId = toAddLp,
        collaboratorUserIds = refinedToAddCollaborators,
        actorId = actor,
        invitationType = invitationType,
        attachedDocs = attachedDocs,
        initialFormData = initialFormData,
        expectedCommitment = lpInfo.expectedCommitment,
        firmName = lpInfo.firmName,
        customId = lpInfo.customId,
        importInvestorId = lpInfo.importInvestorId,
        closeIdOpt = fundSubCloseIdToAdd,
        lpOrderType = lpOrderType,
        investorGroupIdOpt = investorGroupIdOpt,
        httpContext = httpContext,
        tagNames = lpInfo.tagNames,
        duplicatedFromLpIdOpt = originalLpIdOpt,
        metadata = lpInfo.metadata,
        advisorGroupIdOpt = advisorGroupIdOpt
      )
      _ <- setUpContact(
        lpId,
        actor,
        invitationType
      )
      sendEmailResult <- sendEmailForAddingLp(
        lpId = lpId,
        actor = actor,
        invitationType = invitationType,
        inviteLpCustomEmailTemplate = lpInfo.lpEmailTemplate.map(_.toEmailTemplateMessage),
        newFirmName = lpInfo.firmName,
        originalLpIdOpt = originalLpIdOpt
      )
      _ <- createFundSubActivityLog(
        lpId,
        lpUserId = toAddLp,
        collaboratorUserIds = refinedToAddCollaborators,
        invitationType,
        actor,
        originalLpIdOpt,
        advisorGroupIdOpt = advisorGroupIdOpt,
        investmentEntity = lpInfo.firmName,
        ipAddress = httpContext.flatMap(_.getClientIP),
        sendEmailResult = Option(sendEmailResult)
      )
      _ <- sendAmplitudeAndZapier(
        lpId,
        collaboratorUserIds = refinedToAddCollaborators,
        actor,
        lpInfo.firmName,
        invitationType,
        lpInfo.lpContact,
        lpInfo.collaboratorContacts,
        lpInfo.prefillFromLp,
        fromDataImport = false,
        formDataSource = "",
        httpContext
      )
      _ <- addOrderDataToDataLake(
        lpId = lpId,
        referenceDocs = attachedDocs,
        metadata = lpInfo.metadata,
        lpFormIdOpt = setUpOrderRes.lpFormIdOpt,
        advisorGroupIdOpt = advisorGroupIdOpt,
        investorGroupIdOpt = investorGroupIdOpt,
        actorId = actor
      )

      _ <- addOrderDataToDataPipeline(
        lpId = lpId,
        lpUserId = toAddLp,
        collaboratorUserIds = refinedToAddCollaborators,
        refinedFirmName = setUpOrderRes.firmName,
        lpStatus = setUpOrderRes.lpStatus,
        closeId = fundSubCloseIdToAdd,
        metadata = lpInfo.metadata,
        expectedCommitmentOpt = setUpOrderRes.expectedCommitment,
        commitments = setUpOrderRes.commitments
      )

      _ <- FundSubGroupTrackingUtils.updateInvestorGroupTracking(fundSubId, natsNotificationService)
      // Must put it here after dashboard data is initialized
      _ <- ZIOUtils.when(initialFormData.nonEmpty) {
        for {
          _ <- updateSupportingDocsIfNecessary(lpId, actor)(natsNotificationService)
          formInfo <- FundSubCommonUtils.getLpFormInfo(lpId, actor)
          adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
            ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(lpId.parent))
          }
          _ <- FundSubCommitmentHelper.lpUpdateSubmittedCommitment(
            lpId = lpId,
            subFundAmounts = fundSubFormService.getSubmittedAmounts(adminResModel.investmentFunds, formInfo)
          )
        } yield ()
      }
    } yield lpId
  }

  private def initCoreServicesForAddingMainLp(
    fundSubId: FundSubId,
    lpUserId: UserId,
    collaboratorUserIds: Seq[UserId],
    actorId: UserId,
    invitationType: LpInvitationType,
    attachedDocs: Seq[FileId],
    initialFormData: Option[Either[Map[AliasId, Json], GaiaState]],
    expectedCommitment: String,
    firmName: String,
    customId: String,
    importInvestorId: String,
    closeIdOpt: Option[FundSubCloseId],
    lpOrderType: LpOrderType,
    investorGroupIdOpt: Option[FundSubInvestorGroupId],
    httpContext: Option[RequestContext],
    tagNames: Seq[String],
    duplicatedFromLpIdOpt: Option[FundSubLpId],
    metadata: Map[String, String],
    advisorGroupIdOpt: Option[FundSubRiaGroupId]
  ): Task[(FundSubLpId, SetUpLpModelResp)] = {
    for {
      lpTeamId <- setUpLpTeam(
        fundSubId,
        actorId,
        lpUserId +: collaboratorUserIds
      )

      advisorGroupAdminTeamIdOpt <- ZIOUtils.traverseOption(advisorGroupIdOpt) { advisorGroupId =>
        FDBRecordDatabase.transact(FundSubRiaGroupStoreOperations.Production) { ops =>
          ops.get(advisorGroupId).map(_.adminTeamId)
        }
      }

      _ <- ZIOUtils.traverseOption(advisorGroupAdminTeamIdOpt) { advisorGroupAdminTeamId =>
        teamService.addSubTeams(
          AddSubTeamsParams(
            teamId = lpTeamId,
            teamIdsToAdd = Seq(advisorGroupAdminTeamId)
          )
        )
      }

      lpId <- ZIO.succeed(FundSubLpIdFactory.unsafeRandomId(fundSubId))
      _ <- initializeLpFolders(
        lpId,
        actorId,
        Set(lpTeamId),
        investorGroupIdOpt
      )
      // Share attached docs to lp's folder
      sharedDocs <- ZIO.foreach(attachedDocs) { fileId =>
        fileService.copyFileInSameFeature(
          actorId,
          fileId,
          FolderId.channelSystemFolderId(FolderType.ReferenceDoc(lpId)),
          httpContext
        )
      }
      _ <- setUpFundSubSignatureModelForLp(
        lpId,
        Set(lpTeamId),
        investorGroupIdOpt,
        actorId
      )
      (s3FormInfoOpt, lpFormId, formVersionIdOpt) <- fundSubFormService.setUpLpForm(
        fundSubId = lpId.parent,
        teamIds = Set(lpTeamId),
        initialFormData = initialFormData
      )
      _ <- formCommentService.initializeLpComment(
        lpId,
        Set(lpTeamId),
        actorId
      )
      setUpFundSubResp <- setUpFundSubLpModel(
        lpId,
        lpUserId,
        collaboratorUserIds,
        lpTeamId,
        sharedDocs,
        lpOrderType,
        firmName,
        customId,
        s3FormInfoOpt,
        expectedCommitment,
        lpFormId,
        formVersionIdOpt,
        closeIdOpt,
        investorGroupIdOpt,
        actorId,
        invitationType,
        initialFormData,
        importInvestorId,
        duplicatedFromLpIdOpt = duplicatedFromLpIdOpt,
        metadata = metadata,
        advisorGroupIdOpt = advisorGroupIdOpt
      )(natsNotificationService)
      _ <- setUpLpTags(
        lpId,
        actorId,
        tagNames
      )
    } yield lpId -> setUpFundSubResp
  }

  def addMainLpsForAdvisorCreatedOrders(
    ordersToAdd: Seq[AdvisorCreateOrderInfo],
    advisorGroupId: FundSubRiaGroupId,
    actor: UserId,
    httpContextOpt: Option[AuthenticatedRequestContext] = None
  ): Task[AddFundSubLpsResponse] = {
    val fundSubId = advisorGroupId.parent
    for {
      _ <- ZIO.logInfo(s"${actor.idString} is adding ${ordersToAdd.size} RIA order(s) to fund ${fundSubId.idString}")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- fundSubPermissionService.validateUserCanCreateOrderInAdvisorGroupR(advisorGroupId, actor)
      linkedRiaFundGroupIdOpt <- FDBRecordDatabase.transact(FundSubRiaGroupStoreOperations.Production) { ops =>
        ops.getOpt(advisorGroupId).map(_.flatMap(_.linkedRiaFundGroupId))
      }
      linkedRiaFundGroupId <- ZIOUtils.optionToTask(
        linkedRiaFundGroupIdOpt,
        FundSubRiaGroupHasNotBeenLinked(advisorGroupId)
      )
      inviteResults <- ZIO.foreach(ordersToAdd) { orderInfo =>
        val inviteTask = for {
          _ <- riaExternalIntegrationService.validateUserCanCreateRiaOrder(
            linkedRiaFundGroupId,
            orderInfo.advisorUserId,
            actor
          )
          advisorInfo <- userProfileService.getUserInfo(orderInfo.advisorUserId)
          lpInfo = FundSubLpInfo(
            lpInfoId = orderInfo.orderInfoId,
            lpContact = NameEmailInfo(
              email = advisorInfo.emailAddressStr,
              firstName = advisorInfo.firstName,
              lastName = advisorInfo.lastName
            ),
            firmName = orderInfo.investmentEntityName
          )
          lpId <- addMainLp(
            fundSubId = fundSubId,
            toAddLp = orderInfo.advisorUserId,
            lpInfo = lpInfo,
            advisorGroupIdOpt = Some(advisorGroupId),
            actor = actor,
            httpContext = httpContextOpt
          )
        } yield orderInfo.orderInfoId -> AddFundSubLpStatus.Succeed(lpId)

        inviteTask.onErrorHandleWith { ex =>
          for {
            _ <- ZIO.logErrorCause(s"Error creating RIA order $orderInfo", ex.toCause)
          } yield orderInfo.orderInfoId -> AddFundSubLpStatus.Failed(ex.getMessage)
        }
      }
    } yield AddFundSubLpsResponse(inviteResults)
  }

  def addMainLpsWorkflowForAdvisorCreatedOrders(
    ordersToAdd: Seq[AdvisorCreateOrderInfo],
    advisorGroupId: FundSubRiaGroupId,
    actor: UserId,
    httpContextOpt: Option[AuthenticatedRequestContext] = None
  ): Task[FundSubBatchInvitationId] = {
    val fundSubId = advisorGroupId.parent
    for {
      _ <- ZIO.logInfo(s"Advisor ${actor.idString} is creating new subscription in fund ${fundSubId.idString}")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- fundSubPermissionService.validateUserCanCreateOrderInAdvisorGroupR(advisorGroupId, actor)
      fundSubBatchInvitationModel <- FundSubInvitationHelper.setUpFundSubBatchInvitationForAdvisor(
        ordersToAdd,
        advisorGroupId,
        realtimeUpdate = true,
        actor
      )(userProfileService, batchActionService, natsNotificationService)
      _ <- FundSubInvitationHelper.setUpInvitationWorkflow(
        fundSubBatchInvitationModel.fundSubBatchInvitationId,
        fundSubBatchInvitationModel.itemIds,
        httpContextOpt.flatMap(_.getClientIP)
      )
    } yield fundSubBatchInvitationModel.fundSubBatchInvitationId
  }

  private def computeFundSubFormWorkflowData(
    createFirstVersionResp: CreateFirstVersionResponse,
    investmentFunds: Seq[InvestmentFundModel]
  ): Task[FundSubFormWorkflowData] = {
    for {
      investmentEntityOpt <- fundSubFormService
        .getInvestmentEntity(createFirstVersionResp.gaiaFormInfo)
        .map(_.filter(_.nonEmpty))
      submittedAmount = fundSubFormService.getSubmittedAmounts(investmentFunds, createFirstVersionResp.gaiaFormInfo)
    } yield FundSubFormWorkflowData(
      firmNameOpt = investmentEntityOpt,
      submittedAmount = submittedAmount,
      formProgress = createFirstVersionResp.submissionVersionModel.formProgress
    )
  }

  private def updateFundSubFormWorkflowData(
    lpId: FundSubLpId,
    createFirstVersionResp: CreateFirstVersionResponse,
    investmentFunds: Seq[InvestmentFundModel]
  ): Task[Option[(LpInfoRecord, FundSubLpModel)]] = {
    for {
      fundSubFormWorkflowData <- computeFundSubFormWorkflowData(
        createFirstVersionResp,
        investmentFunds
      )
      resp <- ZIO.when(fundSubFormWorkflowData != FundSubFormWorkflowData()) {
        val submittedCommitmentFromForm = fundSubFormWorkflowData.submittedAmount
        for {
          updatedModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(
            _.updateFundSubLpModel(
              lpId
            ) { prevModel =>
              val newLpCommitment = submittedCommitmentFromForm.map { case (investmentFundId, newSubmittedAmount) =>
                investmentFundId -> prevModel.commitments
                  .getOrElse(investmentFundId, LpCommitment())
                  .copy(submittedCommitment = newSubmittedAmount)
              }
              prevModel.copy(
                firmName = fundSubFormWorkflowData.firmNameOpt.getOrElse(prevModel.firmName),
                syncedFirmNameFromForm =
                  fundSubFormWorkflowData.firmNameOpt.nonEmpty || prevModel.syncedFirmNameFromForm,
                commitments = prevModel.commitments ++ newLpCommitment
              )
            }
          )
          updatedLpInfoRecord <- FDBRecordDatabase.transact(LpInfoOperations.Production)(
            _.update(
              lpId
            ) { prevRecord =>
              prevRecord.copy(
                formFillingProgress =
                  createFirstVersionResp.submissionVersionModel.formProgress.map(_.formFillingProgress).getOrElse(0.0),
                firmName = updatedModel.firmName,
                commitments = updatedModel.commitments
              )
            }
          )
        } yield updatedLpInfoRecord -> updatedModel
      }
    } yield resp
  }

}

object FundSubLpInvitationService {

  final case class SetUpLpModelResp(
    lpFormIdOpt: Option[FundSubLpFormIdTrait],
    firmName: String,
    lpStatus: LpStatus,
    closeId: Option[FundSubCloseId],
    expectedCommitment: Option[MoneyMessage],
    commitments: Map[InvestmentFundId, LpCommitment]
  )

  case class FundSubFormWorkflowData(
    firmNameOpt: Option[String] = None,
    submittedAmount: Map[InvestmentFundId, Option[MoneyMessage]] = Map.empty,
    formProgress: Option[FormProgress] = None
  ) derives CanEqual

}
