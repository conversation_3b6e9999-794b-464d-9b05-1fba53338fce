// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service.fundsub

import java.time.Instant
import java.util.UUID
import scala.jdk.DurationConverters.ScalaDurationOps

import sttp.model.MediaType
import zio.implicits.*
import zio.temporal.workflow.ZWorkflowStub
import zio.{Task, ZIO}

import anduin.account.enterprise.EnterpriseService
import anduin.account.profile.UserProfileService
import anduin.customdomain.CustomDomainService
import anduin.dashboard.data.{ColumnHeader, DashboardRow, DocumentRequestCell, RowConvert}
import anduin.dashboard.model.DashboardColumn
import anduin.dashboard.query.RequestedDocumentsQuery
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.documentcontent.spreadsheet.FillSheetData.{FillSheetCell, FillSheetRow}
import anduin.documentcontent.spreadsheet.{FillSheetData, SpreadsheetUtils}
import anduin.email.provider.{EmailProviderModel, EmailProviderService}
import anduin.email.{CustomSmtpServerConfig, CustomSmtpServerConfigParams}
import anduin.encryption.StreamingEncryptionService
import anduin.endpoint.signature.DocusignRecipientAuthData
import anduin.endpoint.signature.SignatureSharedModels.{SignatureRecipientStatus, SignerAuthTypeData}
import anduin.environment.{
  EnvironmentAuthenticationIntegrationService,
  EnvironmentReauthenticationPolicyService,
  EnvironmentService
}
import anduin.evendim.client.EvendimClient
import anduin.evendim.model.datalake
import anduin.fdb.client.FDBClient
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{DefaultCluster, FDBOperations, FDBRecordDatabase}
import anduin.forms.engine.GaiaState
import anduin.forms.model.formdiff.FormDiffMode
import anduin.forms.service.FormService
import anduin.forms.utils.FormValidationUtils.SectionResult
import anduin.forms.utils.{FormDataConverters, FormValidationUtils}
import anduin.fundsub.activitylog.{ActivityLogService, FundSubAdminActivityLogUtils}
import anduin.fundsub.admin.FundSubAdminGeneral
import anduin.fundsub.auditlog.FundSubAuditLogService.{AddEventEmailParam, AddEventParam}
import anduin.fundsub.auditlog.{AuditLogActorType, AuditLogEventType, FundSubAuditLogService}
import anduin.fundsub.batchaction.FundSubBatchActionService
import anduin.fundsub.comment.database.{CommentExportTaskOperations, CommentExportTaskStoreProvider}
import anduin.fundsub.comment.{FormCommentEmailService, FormCommentService}
import anduin.fundsub.commitment.FundSubCommitmentHelper
import anduin.fundsub.customdata.CustomDataService
import anduin.fundsub.dashboard.{FundSubDashboardService, LpInfoOperations}
import anduin.fundsub.dataexport.FundSubAnalysisUtils
import anduin.fundsub.datalakeingestion.FundSubDataLakeIngestionService
import anduin.fundsub.datalakeingestion.model.{AddOrUpdateFundParams, UpdateOrderBasicInfoParams}
import anduin.fundsub.email.generate.FundSubEmailUtils
import anduin.fundsub.email.generate.test.FundSubTestEmailGenerate
import anduin.fundsub.email.{FundSubCustomSmtpServerConfig, FundSubDeprecatedSignatureEmailService, generate}
import anduin.fundsub.endpoint.admin.*
import anduin.fundsub.endpoint.dashboard.*
import anduin.fundsub.endpoint.dashboard.v2.GetDashboardGeneralInfoResponse
import anduin.fundsub.endpoint.formcomment.{
  CancelCommentExportTaskParams,
  GetCommentExportTaskStatusResponse,
  MarkFormCommentsAsSentNotificationParams
}
import anduin.fundsub.endpoint.lp.*
import anduin.fundsub.endpoint.operation.{ExportFundCommentParams, SendDemoFundActivityEmailParams, TagInfo}
import anduin.fundsub.endpoint.reviewpackage.*
import anduin.fundsub.endpoint.subscriptiondoc.SubscriptionVersionIndex
import anduin.fundsub.endpoint.subscriptiondoc.review.FundSubSubscriptionDocReviewType
import anduin.fundsub.endpoint.whitelabel.UpdateSharedDataRoomLinkParams
import anduin.fundsub.form.utils.FundSubCommonUtils
import anduin.fundsub.form.{FundSubFormComparisonService, FundSubFormFileOperations, FundSubFormService, LpForm}
import anduin.fundsub.fundclose.FundSubCloseService
import anduin.fundsub.group.{FundSubGroupStoreOperations, FundSubGroupUtils}
import anduin.fundsub.model.*
import anduin.fundsub.model.FundSubSharedClientModel.ParticipantInfo
import anduin.fundsub.model.FundSubSignatureRequestDocType.*
import anduin.fundsub.models.signature.FundSubSignatureRequestModels.DocType
import anduin.fundsub.models.{FundSubLpModelStoreOperations, FundSubModelStoreOperations, FundSubSgwModelUtils}
import anduin.fundsub.rebac.FundSubRebacModel.{DashboardPermission, FundPermission, InvestorPermission, Type}
import anduin.fundsub.rebac.PortalFundsubModel
import anduin.fundsub.reviewpackage.ReviewPackageService
import anduin.fundsub.service.*
import anduin.fundsub.signature.FundSubSignatureJvmUtils
import anduin.fundsub.signature.integration.FundSubSignatureIntegrationService
import anduin.fundsub.storageintegration.FundSubStorageIntegrationService
import anduin.fundsub.subscriptiondoc.review.FundSubSubscriptionDocReviewService
import anduin.fundsub.supportingdoc.SupportingDocReviewConfigOperations
import anduin.fundsub.user.{FundSubUserService, FundSubUserTrackingService}
import anduin.fundsub.utils.*
import anduin.fundsub.view.FundSubViewService
import anduin.fundsub.{FundSubLoggingService, LpFormDataOperations}
import anduin.greylin.GreylinDataService
import anduin.greylin.operation.FundSubscriptionOperations
import anduin.id.ModelIdRegistry
import anduin.id.fundsub.*
import anduin.id.offering.OfferingId
import anduin.id.review.SupportingDocReviewConfigId
import anduin.id.signature.SignatureRequestId
import anduin.link.LinkGeneratorService
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.model.id.email.InternalEmailId
import anduin.model.id.{FileId, FolderId, GlobalOfferingIdFactory, TemporalWorkflowId}
import anduin.model.optics.iso.RequestContextIso.authenticatedRequestContextIso
import anduin.model.user.FullName
import anduin.portaluser.{ExecutiveAdmin, PortalUserService}
import anduin.protobuf.account.EnterpriseLoginConfigEntityBinding
import anduin.protobuf.actionlogger.event.ActionEventExportInvestorDashboardData
import anduin.protobuf.activitylog.GeneralActivity
import anduin.protobuf.activitylog.GeneralActivity.Value
import anduin.protobuf.activitylog.fundsub.admin.*
import anduin.protobuf.activitylog.fundsub.admin.CommentSettingUpdateActivity.CommentSettingUpdateAction
import anduin.protobuf.dynamicform.DynamicFormSection
import anduin.protobuf.flow.fundsub.admin.lpdashboard.{LpInfoRecord, LpStatus, UserBasicInfo}
import anduin.protobuf.fundsub.*
import anduin.protobuf.fundsub.activitylog.lp.{
  BouncedInvitationEmailResent,
  LpCommentNotificationSent,
  LpRequestedToSignOnSupportingDoc
}
import anduin.protobuf.fundsub.models.FundSubPublicModel
import anduin.protobuf.fundsub.models.customdata.item.CustomDataMessage.SealedValue
import anduin.protobuf.fundsub.models.customdata.item.{MultipleStringValue, SingleStringValue, StringWithColor}
import anduin.protobuf.fundsub.models.lp.FundSubLpModel
import anduin.rebac.RebacModel.implicits.given
import anduin.rebac.RebacStoreOperation
import anduin.service.*
import anduin.signature.integration.SignatureIntegrationService
import anduin.stargazer.service.formcomment.FormCommentCommons.NewFormCommentEmailDataForLp
import anduin.storageservice.common.FileContentOrigin
import anduin.temporal.TemporalEnvironment
import anduin.user.CommonUserUtils
import anduin.utils.UrlValidatorUtils
import anduin.workflow.fundsub.batchaction.UpdateInvestorValueItemData.{
  MultipleSelectColumn,
  SingleSelectColumn,
  TagColumn
}
import anduin.workflow.fundsub.batchaction.{
  ConvertOfflineToNormalOrderItemData,
  FundSubBatchActionFrontendTracking,
  SignCountersignRequestData,
  UpdateInvestorValueItemData
}
import anduin.workflow.fundsub.formcomment.ExportCommentParams
import anduin.workflow.fundsub.formcomment.impl.CommentExportWorkflowImpl
import com.anduin.stargazer.dynamicform.DynamicFormFillingProgressCalculator
import com.anduin.stargazer.dynamicform.DynamicFormFillingProgressCalculator.FormTableOfContent
import com.anduin.stargazer.dynamicform.core.{FormProcessor, FormState}
import com.anduin.stargazer.dynamicform.validation.FormValidation
import com.anduin.stargazer.external.base64.Base64
import com.anduin.stargazer.service.actionlogger.ActionLoggerService
import com.anduin.stargazer.service.api.FileDownloadService
import com.anduin.stargazer.service.dynamicform.DynamicFormStorageService
import com.anduin.stargazer.service.email.EmailSenderService
import com.anduin.stargazer.service.file.BatchDownloadRequest
import com.anduin.stargazer.service.fundsub.FundSubAdminService.GetDashboardDataForExport
import com.anduin.stargazer.service.fundsub.free.module.{FundSubCountersignHelper, ManageFundSubAdminM, ManageFundSubLpM}
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils
import stargazer.model.routing.DynamicAuthPage

final case class NotAnOfflineOrderException(message: String) extends RuntimeException {
  override def getMessage: String = message
}

final case class FundSubAdminService(
  fundSubUserTrackingService: FundSubUserTrackingService,
  userProfileService: UserProfileService,
  fundSubStorageIntegrationService: FundSubStorageIntegrationService,
  fundSubUserService: FundSubUserService,
  fundSubLoggingService: FundSubLoggingService,
  emailSenderServiceA: EmailSenderService,
  fundSubLpActivityLogService: FundSubLpActivityLogService,
  fundSubPermissionService: FundSubPermissionService,
  fundSubEmailUtils: FundSubEmailUtils,
  reviewPackageService: ReviewPackageService,
  documentReviewEmailUtils: DocumentReviewEmailUtils,
  formCommentService: FormCommentService,
  formCommentEmailService: FormCommentEmailService,
  fundSubFormComparisonService: FundSubFormComparisonService,
  fundSubFormService: FundSubFormService,
  fundSubEmailService: FundSubEmailService,
  enterpriseService: EnterpriseService,
  newSupportingDocService: NewSupportingDocService,
  fundSubDashboardService: FundSubDashboardService,
  fundSubAuditLogService: FundSubAuditLogService,
  fundSubBatchActionService: FundSubBatchActionService,
  fundSubSubscriptionDocReviewService: FundSubSubscriptionDocReviewService,
  signatureIntegrationService: SignatureIntegrationService,
  fundSubLpTagUtilService: FundSubLpTagUtilService,
  customDataService: CustomDataService,
  fundSubSignatureIntegrationService: FundSubSignatureIntegrationService,
  evendimClient: EvendimClient,
  dynamicFormStorageService: DynamicFormStorageService,
  natsNotificationService: NatsNotificationService,
  temporalEnvironment: TemporalEnvironment,
  fundSubViewService: FundSubViewService,
  environmentAuthenticationIntegrationService: EnvironmentAuthenticationIntegrationService,
  environmentPolicyService: EnvironmentReauthenticationPolicyService,
  actionLoggerService: ActionLoggerService,
  fundSubDeprecatedEmailService: FundSubDeprecatedSignatureEmailService,
  emailProviderService: EmailProviderService
)(
  using val fundSubLpDashboardService: FundSubLpDashboardService,
  val fileService: FileService,
  val fileDownloadService: FileDownloadService,
  val customDomainService: CustomDomainService,
  val linkGeneratorService: LinkGeneratorService,
  val executiveAdmin: ExecutiveAdmin,
  val activityLogService: ActivityLogService,
  val formService: FormService,
  val portalUserService: PortalUserService,
  val fundSubCloseService: FundSubCloseService,
  val fundSubDataLakeIngestionService: FundSubDataLakeIngestionService,
  val environmentService: EnvironmentService,
  val greylinDataService: GreylinDataService,
  val encryptionService: StreamingEncryptionService
) extends FundSubFormFileOperations {
  private given userProfileServiceIm: UserProfileService = userProfileService

  def uploadRefDoc(
    params: UploadDocumentsParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  )(
    using fsModule: ManageFundSubAdminM
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is uploading reference documents ${params.files} for fundsub ${params.fundSubId}")
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      fundSubFolderId = FolderId.channelSystemFolderId(params.fundSubId)
      newFilesMap <- FileServiceHelper
        .shareFilesToFolder(
          params.files,
          fundSubFolderId,
          actor,
          ctx
        )
      _ <- fsModule.uploadRefDoc(
        params.fundSubId,
        params.files.flatMap(newFilesMap.get), // preserve order
        params.isOverride
      )
    } yield ()
  }

  def renameRefDoc(
    params: RenameDocumentsParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is renaming reference documents ${params.file} for fundsub ${params.fundSubId}")
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      _ <- fileService.updateFileName(
        actor,
        params.file,
        params.name
      )
    } yield ()
  }

  def getFundSharedRefDocs(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[GetFundSharedReferenceDocsResp] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is getting reference documents for fundsub $fundSubId")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- fundSubPermissionService.validateUserIsFundUserR(fundSubId, actor)
      fundSubModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(fundSubId)
      )
      refDocIds = fundSubModel.referenceFiles
      refDocInfos <- ZIOUtils.foreachParN(4)(refDocIds) { fileId =>
        for {
          fileName <- fileService.getFileNameUnsafe(fileId)
          uploadedAtOpt <- fileService.getFileCreatedAt(actor)(fileId)
        } yield FundReferenceDocInfo(
          fileId = fileId,
          fileName = fileName,
          uploadedAtOpt = uploadedAtOpt
        )
      }
    } yield GetFundSharedReferenceDocsResp(refDocInfos)
  }

  def removeRefDoc(
    params: RemoveDocumentsParams,
    actor: UserId
  )(
    using fsModule: ManageFundSubAdminM
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is removing reference documents ${params.files} for fundsub ${params.fundSubId}")
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      _ <- fsModule.removeRefDoc(params.fundSubId, params.files)
    } yield ()
  }

  def saveSubscriptionDocsOrder(
    params: SaveSubscriptionDocsOrderParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is saving subscription documents order for fund ${params.fundSubId.idString}")
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      sanitizedFileNames = params.fileNames
        .map(
          _.trim
        )
        .filter { fileName =>
          fileName.nonEmpty && fileName.length <= 500
        }
        .take(50)
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.updateFundSubPublicModel(params.fundSubId)(
          _.copy(
            subscriptionDocsOrder = sanitizedFileNames
          )
        )
      )
    } yield ()
  }

  def requestCountersignSignature(
    params: RequestCountersignSignatureParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  )(
    using fsModule: ManageFundSubAdminM
  ): Task[RequestCountersignSignatureResp] = {
    val lpId = params.lpId
    val fundId = lpId.parent
    for {
      _ <- {
        val text = if (params.isBatchAction) {
          s"$actor is batch requesting counter-signature for $lpId"
        } else {
          s"$actor is requesting counter-signature for $lpId"
        }
        ZIO.logInfo(text)
      }
      _ <- FundSubSignatureJvmUtils.validateShouldCounterSign(fundId)
      _ <- validateLpWhenCountersign(lpId, actor)
      userIdMap <- fundSubUserService.createSignatureUserIfNeeded(
        fundSubId = fundId,
        signers = params.signers.map(signer => signer.email -> signer.name),
        inviterOpt = httpContext.map(_.actor.userInfo.emailAddressStr)
      )
      requestId <- fsModule.requestCountersignSignature(
        params.lpId,
        params.signers,
        userIdMap,
        params.fileIds,
        params.message,
        params.isBatchAction,
        params.refDocs,
        params.docusignESignatureOptionParamsOpt,
        actor,
        httpContext
      )
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundId)
      _ <- fundSubPermissionService.createFundSignatureRequestRelationR(
        fundId = fundId,
        signatureRequestId = requestId
      )
      _ <- fundSubPermissionService.createSignatureRequestSignerRelationsR(
        signatureRequestId = requestId,
        signerUserIds = userIdMap.values.toSet
      )
      _ <- fundSubLpDashboardService.updateLpInfoRecord(params.lpId, identity)
    } yield RequestCountersignSignatureResp(requestId)
  }

  def validateLpWhenCountersign(lpId: FundSubLpId, actor: UserId): Task[Unit] = {
    for {
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = lpId.parent,
        userId = actor,
        permission = InvestorPermission.ManageCountersignatures,
        obj = Type.Investor(lpId)
      )
      _ <- fundSubPermissionService.validateLpStatus(lpId, Set(LpStatus.LPSubmitted, LpStatus.LPCompleted))
    } yield ()
  }

  def remindSigningRequestRestrictedFlow(
    params: RemindSigningRequestParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor send remind signing request for lp ${params.lpId}, signer ${params.signer}")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.lpId.parent)
      _ <- params.requestType match {
        case SubscriptionDoc | TaxFormDeprecated =>
          fundSubPermissionService.validateUserCanAccessLpViewR(params.lpId, actor)
        case CounterSignDoc =>
          fundSubPermissionService.validateFundManagerCanAccessLpSubscriptionDocsR(params.lpId, actor)
        case GpAdditionalRequestedDoc | FormSubmissionDoc =>
          fundSubPermissionService.validateFundManagerCanAccessLpSupportingDocsR(params.lpId, actor)
        case SideLetterDoc | LpUploadedDoc =>
          ZIO.fail(GeneralServiceException(s"Request type ${params.requestType} is not supported"))
      }
      _ <- ZIOUtils.when(params.requestType == SubscriptionDoc)(
        fundSubPermissionService.validateLpStatus(params.lpId, Set(LpStatus.LPRequestedSignature))
      )

      requests <- ZIO.foreachPar(params.requestIds) { requestId =>
        signatureIntegrationService.fetchRequestBasic(requestId, actor)
      }
      pendingSigner = requests.flatMap(
        _.recipients.filterNot(_.status == SignatureRecipientStatus.Signed)
      )
      _ <- ZIOUtils.validate(pendingSigner.exists(_.userId == params.signer))(
        GeneralServiceException(s"User ${params.signer} is not one of pending signers")
      )

      emailIdOpt <- fundSubDeprecatedEmailService
        .sendFundSubRemindSignatureRequestEmailDeprecated(
          params.lpId,
          params.signer,
          actor,
          params.requestType,
          params.requestIds
        )
        .map(_.flatMap(_.internalIdOpt))
      _ <- params.requestType match {
        case SubscriptionDoc =>
          fundSubAuditLogService.addEvent(
            fundSubId = params.lpId.parent,
            params = AddEventParam(
              actor = Some(actor),
              actorType = AuditLogActorType.FundSide,
              eventType = AuditLogEventType.REMINDER_TO_COMPLETE_SIGNATURE_REQUEST_ON_SUBSCRIPTION_DOCUMENT_SENT,
              orderId = Option(params.lpId),
              eventEmail = Seq(
                AddEventEmailParam(
                  fundSubEventType = FundSubEvent.lpRemindSignatureRequest,
                  emailIds = Seq(emailIdOpt).flatten
                )
              ),
              activityDetail = GeneralActivity(Value.Empty)
            )
          )
        case TaxFormDeprecated =>
          fundSubAuditLogService.addEvent(
            fundSubId = params.lpId.parent,
            params = AddEventParam(
              actor = Some(actor),
              actorType = AuditLogActorType.FundSide,
              eventType = AuditLogEventType.REMINDER_TO_COMPLETE_SIGNATURE_REQUEST_ON_TAX_FORM_SENT,
              orderId = Option(params.lpId),
              eventEmail = Seq(
                AddEventEmailParam(
                  fundSubEventType = FundSubEvent.remindTaxFormSignatureRequest,
                  emailIds = Seq(emailIdOpt).flatten
                )
              ),
              activityDetail = GeneralActivity(Value.Empty)
            )
          )
        case CounterSignDoc =>
          fundSubAuditLogService.addEvent(
            fundSubId = params.lpId.parent,
            params = AddEventParam(
              actor = Some(actor),
              actorType = AuditLogActorType.FundSide,
              eventType = AuditLogEventType.REMINDER_TO_COUNTERSIGN_SUBSCRIPTION_DOCUMENT_SENT,
              orderId = Option(params.lpId),
              eventEmail = Seq(
                AddEventEmailParam(
                  fundSubEventType = FundSubEvent.adminRemindCounterSignRequest,
                  emailIds = Seq(emailIdOpt).flatten
                )
              ),
              activityDetail = GeneralActivity(Value.Empty)
            )
          )
        case GpAdditionalRequestedDoc =>
          fundSubAuditLogService.addEvent(
            fundSubId = params.lpId.parent,
            params = AddEventParam(
              actor = Some(actor),
              actorType = AuditLogActorType.FundSide,
              eventType = AuditLogEventType.REMINDER_TO_COMPLETE_SIGNATURE_REQUEST_ON_ADDITIONAL_DOCUMENT_SENT,
              orderId = Option(params.lpId),
              eventEmail = Seq(
                AddEventEmailParam(
                  fundSubEventType = FundSubEvent.remindSupportingDocSignatureRequest,
                  emailIds = Seq(emailIdOpt).flatten
                )
              ),
              activityDetail = GeneralActivity(Value.Empty)
            )
          )
        case FormSubmissionDoc =>
          fundSubAuditLogService.addEvent(
            fundSubId = params.lpId.parent,
            params = AddEventParam(
              actor = Some(actor),
              actorType = AuditLogActorType.FundSide,
              eventType = AuditLogEventType.REMINDER_TO_COMPLETE_SIGNATURE_REQUEST_ON_ADDITIONAL_DOCUMENT_SENT,
              orderId = Option(params.lpId),
              eventEmail = Seq(
                AddEventEmailParam(
                  fundSubEventType = FundSubEvent.remindSupportingDocSignatureRequest,
                  emailIds = Seq(emailIdOpt).flatten
                )
              ),
              activityDetail = GeneralActivity(Value.Empty)
            )
          )
        case SideLetterDoc | LpUploadedDoc => ZIO.unit
      }
    } yield ()
  }

  def adminReassignSignatureRequest(
    lpId: FundSubLpId,
    requestId: SignatureRequestId,
    docType: DocType,
    oldSigner: UserId,
    newSignerName: String,
    newSignerEmail: String,
    reason: String,
    signerAuthTypeData: SignerAuthTypeData,
    docusignRecipientAuthDataOpt: Option[DocusignRecipientAuthData] = None,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"$actor is reassigning signature request $requestId to $newSignerEmail for $lpId"
      )
      _ <- ZIO.when(actor != oldSigner) {
        for {
          given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
          _ <- docType match {
            case DocType.CountersignDoc =>
              fundSubPermissionService.validateUserHasPermissionR(
                userId = actor,
                permission = InvestorPermission.ManageCountersignatures,
                obj = Type.Investor(lpId)
              )
            case DocType.GpAdditionalRequestedDoc =>
              fundSubPermissionService.validateFundManagerCanManageAdditionalSignatureRequestsR(lpId, actor)
            case _ =>
              ZIO.fail(GeneralServiceException("Only allow admin reassign countersign and supporting doc request"))
          }
        } yield ()
      }
      newSigners <- fundSubUserService.createSignatureUserIfNeeded(
        fundSubId = lpId.parent,
        signers = Seq(newSignerEmail -> newSignerName),
        inviterOpt = None
      )
      newSigner <- ZIOUtils.optionToTask(newSigners.headOption.map(_._2), GeneralServiceException("No user"))
      _ <- fundSubSignatureIntegrationService.reassignSignatureRequest(
        fundSubId = lpId.parent,
        requestId = requestId,
        lpIds = Seq(lpId),
        oldSigner = oldSigner,
        newSigner = newSigner,
        signerAuthTypeData = signerAuthTypeData,
        docusignRecipientAuthDataOpt = docusignRecipientAuthDataOpt,
        actor = actor
      )
      _ <- fundSubLpDashboardService.updateLpInfoRecord(lpId, identity)
      request <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasic(
        requestId,
        lpId,
        if (docType == DocType.CountersignDoc) {
          FundSubSignatureRequestDocType.CounterSignDoc
        } else {
          FundSubSignatureRequestDocType.GpAdditionalRequestedDoc
        }
      )
      unsignedFileIds = request
        .getCountersignPackage(lpId)
        .map(_.getUnsignedSignatureFileIds)
        .getOrElse(Seq.empty)
      newSignerEmailIdOpt <- fundSubEmailService
        .sendFundSubSendSignatureRequestSignerEmail(
          fundSubLpId = lpId,
          fileIds = unsignedFileIds,
          signer = newSigner,
          actor = actor,
          message = reason,
          envelopeType = request.envelopeType,
          signatureRequestId = requestId
        )
        .map(_.flatMap(_.internalIdOpt))
      requesterEmailIdOpt <-
        if (actor != request.requester && newSigner != request.requester) {
          fundSubEmailService
            .sendFundSubReassignRequesterEmail(
              fundSubLpId = lpId,
              requester = request.requester,
              fileIds = unsignedFileIds,
              newSignerName = newSignerName,
              newSignerEmail = newSignerEmail,
              reason = reason,
              actor = actor
            )
            .map(_.flatMap(_.internalIdOpt))
        } else {
          ZIO.attempt(None)
        }
      oldSignerEmailIdOpt <-
        if (actor != oldSigner) {
          fundSubEmailService
            .sendCancelSignatureRequestEmail(
              lpId = lpId,
              fileIds = unsignedFileIds,
              receiver = oldSigner,
              actor = actor,
              envelopeType = request.envelopeType,
              requestId = requestId
            )
            .map(_.flatMap(_.internalIdOpt))
        } else {
          ZIO.attempt(None)
        }
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = lpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.SIGNATURE_REQUEST_REASSIGNED,
          orderId = Option(lpId),
          eventEmail = Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.adminSendCounterSignRequest,
              emailIds = newSignerEmailIdOpt.toSeq
            )
          ) ++ requesterEmailIdOpt.map { emailId =>
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.lpRequestReassignRequester,
              emailIds = Seq(emailId)
            )
          } ++ oldSignerEmailIdOpt.map { emailId =>
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.adminCancelCounterSignRequest,
              emailIds = Seq(emailId)
            )
          },
          activityDetail = SignatureRequestReassign(actor, newSigner)
        )
      )
    } yield ()
  }

  def signCountersignRequest(
    params: SignCountersignRequestParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  )(
    using adminModule: ManageFundSubAdminM
  ): Task[Unit] = {
    for {
      _ <- {
        val text = if (params.isBatchAction) {
          s"$actor is batch signing countersign request for ${params.lpId}"
        } else {
          s"$actor is signing countersign request for ${params.lpId}"
        }
        ZIO.logInfo(text)
      }
      _ <- fundSubPermissionService.validateLpStatus(params.lpId, Set(LpStatus.LPSubmitted, LpStatus.LPCompleted))
      _ <- adminModule.signCountersignRequest(
        params.lpId,
        params.signData,
        params.isBatchAction,
        actor,
        httpContext,
        params.signingAuthData
      )
      _ <- fundSubLpDashboardService.updateLpInfoRecord(params.lpId, identity)
    } yield ()
  }

  def batchSignCountersignRequest(
    singleSignCountersignReqParams: Seq[SingleSignCountersignRequestParams],
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[BatchSignCountersignRequestResp] = {
    for {
      fundSubId <- FundSubLpUtils.validateLpListNonEmptyAndBelongToTheSameFund(singleSignCountersignReqParams.map(_.lpId))
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      canAccessFundSignerView <- fundSubPermissionService.checkIfUserCanAccessFundSubSignerViewR(fundSubId, actor)
      _ <- ZIO.unless(canAccessFundSignerView) {
        ZIO.fail(GeneralServiceException(s"$actor does not have permission to countersign"))
      }
      batchActionId <- fundSubBatchActionService.startBatchAction[SignCountersignRequestData](
        fundSubId,
        actor,
        singleSignCountersignReqParams.map { param =>
          SignCountersignRequestData(
            fundSubLpId = param.lpId,
            lpName = param.lpName,
            signData = param.signData.map { data =>
              SignCountersignRequestData.SignedFile(
                data.fileId,
                data.docSig,
                data.requestId
              )
            },
            actorId = actor,
            httpContext = httpContext.map(authenticatedRequestContextIso.get)
          )
        },
        FundSubBatchActionFrontendTracking.ACTOR_TRACKING,
        FundSubCountersignHelper.batchSignCountersignRequestWorkflow(temporalEnvironment)
      )
    } yield BatchSignCountersignRequestResp(batchActionId)
  }

  def getLpUnsignedDoc(
    params: GetLpUnsignedDocParams,
    actor: UserId
  ): Task[GetLpUnsignedDocResponse] = {
    val lpId = params.fundSubLpId
    for {
      _ <- ZIO.logInfo(s"User $actor is getting unsigned doc for lp $lpId")
      _ <- fundSubPermissionService.validateFundManagerCanAccessLpSubscriptionDocs(lpId, actor)
      _ <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(
          LpStatus.LPPendingReview,
          LpStatus.LPSubmitted,
          LpStatus.LPCompleted
        )
      )
      formFiles <- FDBRecordDatabase.transact(LPDataOperations.Production)(_.getFormFiles(lpId))
    } yield GetLpUnsignedDocResponse(
      fileIds = formFiles.values.toSeq
    )
  }

  def getLpFormAndValue(
    params: GetLpFormAndValueParams,
    actor: UserId
  )(
    using fsModule: ManageFundSubAdminM
  ): Task[GetLpFormAndValueResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is getting form and value from lp ${params.fundSubLpId}")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.fundSubLpId.parent)
      _ <- fundSubPermissionService
        .validateFundManagerCanAccessLpSubscriptionDocsR(params.fundSubLpId, actor)
        .orElse(fundSubPermissionService.validateUserCanServeOnInvestorSideR(params.fundSubLpId, actor))
      resp <- fsModule.getLpFormAndValue(params.fundSubLpId, actor)
    } yield resp
  }

  /** Export LPs and Collaborators contact for a fund into a csv file. Exported file can be used to import for
    * invitation.
    *
    * @return
    *   file url for download
    */
  def exportInvestorDashboardData(
    params: ExportInvestorDashboardDataParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  )(
    using fileService: FileService,
    fundSubLpTagUtilService: FundSubLpTagUtilService
  ): Task[ExportInvestorDashboardDataResp] = {
    for {
      _ <- ZIO.logInfo(
        s"User $actor is exporting lp contact for fund ${params.fundSubId}, dashboardType = ${params.dashboardType.name}"
      )

      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.fundSubId)
      _ <- params.lpFilter.fold(
        lpIds =>
          ZIOUtils.foreachParN(10)(lpIds) { lpId =>
            fundSubPermissionService.validateUserHasPermissionR(
              userId = actor,
              permission = InvestorPermission.ExportInvestorDashboardData,
              obj = Type.Investor(lpId)
            )
          },
        _ =>
          fundSubPermissionService.validateUserHasPermissionR(
            userId = actor,
            permission = FundPermission.ExportInvestorDashboardData,
            obj = Type.FundSub(params.fundSubId)
          )
      )

      fundSubModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(params.fundSubId)
      }
      enabledCustomId = fundSubModel.featureSwitch.exists(_.enabledCustomLpId)
      exportTagsAsColumns = fundSubModel.featureSwitch.exists(_.exportInvestorDashboardTagsAsColumns)
      data <- params.dashboardType match {
        case FundSubDashboardType.NormalDashboard =>
          FundSubAnalysisUtils
            .getLpContacts(params.fundSubId, params.lpFilter, actor)
            .map(lpContacts =>
              FundSubAnalysisUtils.buildFileDataFromLpContactList(
                lpContacts,
                enabledCustomId,
                exportTagsAsColumns
              )
            )
        case advancedDashboardType: FundSubDashboardType.AdvancedDashboard =>
          exportInvestorAdvancedDashboardData(
            params,
            exportTagsAsColumns,
            actor,
            advancedDashboardType
          )
      }
      spreadSheetData <- SpreadsheetUtils.createAndFillSpreadsheet(
        FillSheetData(
          startCol = 0,
          startRow = 0,
          rows = data.map { row =>
            FillSheetRow(
              cells = row.map { cell =>
                FillSheetCell(
                  value = cell
                )
              }
            )
          },
          defaultColumnWidth = Some(20)
        )
      )
      folderId <- fileService.createUserTemporaryFolderIfNeeded(actor)
      fileName <- getExportLpContactFileName(
        fundSubModel.fundName,
        params.lpFilter,
        params.dateSuffix
      )
      fileId <- fileService.uploadFile(
        folderId,
        fileName,
        FileContentOrigin.FromSource(
          spreadSheetData,
          MediaType(
            "text",
            "xlsx",
            Some("UTF8")
          )
        ),
        actor
      )
      downloadUrlRes <- fileDownloadService.getBatchDownloadData(
        actor,
        BatchDownloadRequest(
          "",
          Seq(),
          Seq(fileId),
          DmsTrackingActivityType.Download
        ),
        httpContext
      )
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = params.fundSubId,
        params = AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.INVESTOR_DASHBOARD_DATA_EXPORTED,
          activityDetail = GeneralActivity(Value.Empty)
        )
      )
      _ <- actionLoggerService.addEventLog(
        actor = actor,
        events = Seq(
          ActionEventExportInvestorDashboardData(
            fundSubId = params.fundSubId,
            investorCount = data.size
          )
        ),
        httpContextOpt = httpContext
      )
    } yield ExportInvestorDashboardDataResp(downloadUrlRes.url)
  }

  private def getInvestorsHasIncompleteAmlKycDocsWithReviewFlow(fundId: FundSubId) = {
    for {
      lpIds <- evendimClient
        .query(
          datalake.Query.getFundSubscription(
            id = fundId.idString
          )(
            datalake.FundSubscription
              .orders()(
                (datalake.Order.id ~ RequestedDocumentsQuery.query)
                  .map { (idStr, cell) =>
                    idStr -> DocumentRequestCell
                      .getDocRequestStatusMap(cell, true)
                      .exists { case (_, status) =>
                        status == DocumentRequestCell.PendingSubmissionState ||
                        status == DocumentRequestCell.ChangesInProgressState
                      }
                  }
              )
              .map(_.flatten.filter(_._2).map(_._1))
          )
        )
        .map(_.map(_.flatMap { idStr =>
          ModelIdRegistry.parser.parseAs[FundSubLpId](idStr)
        }).getOrElse(List.empty))
    } yield lpIds.toSet
  }

  def getLpsBasicInfo(
    params: GetLpsBasicInfoParams,
    actor: UserId
  ): Task[Seq[LpBasicInfo]] = {
    for {
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.fundSubId)
      actorGroupRole <- FundSubGroupUtils.getUserFundManagerGroupRole(params.fundSubId, actor)
      lpIds <-
        if (params.fundSubLpIds.nonEmpty) {
          fundSubPermissionService
            .validateUserCanManageInvestorsInFundR(
              lpIds = params.fundSubLpIds.toSet,
              userId = actor
            )
            .map(_ => params.fundSubLpIds)
        } else {
          FundSubPermissionUtils.getAccessibleLpIdsR(params.fundSubId, actor).map(_.toSeq)
        }
      tags <- fundSubLpTagUtilService.getTagByFundSubId(params.fundSubId)
      tagsMap = tags.map { tag =>
        tag.id -> tag
      }.toMap
      closeData <- fundSubCloseService.getFundSubCloseDataInternal(params.fundSubId)
      lpRecords <- FundSubLpUtils.getLpInfoRecordsForFundManagerR(lpIds, actorGroupRole)
      lpFlowType <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(params.fundSubId).map(_.lpFlowType)
      )
      amlKycReviewEnabled <- SupportingDocReviewUtils.checkIfSupportingDocReviewEnabledInBothSides(params.fundSubId)
      lpMissingAmlKycDocs <-
        if (actorGroupRole.canAccessLpSupportingDocs && amlKycReviewEnabled && lpFlowType.isFlexible) {
          getInvestorsHasIncompleteAmlKycDocsWithReviewFlow(params.fundSubId)
        } else {
          ZIO.attempt(Set.empty[FundSubLpId])
        }
    } yield lpRecords.map { lpRecord =>
      LpBasicInfo(
        lpId = lpRecord.lpId,
        mainLp = UserBasicInfo(
          userId = lpRecord.userId,
          firstName = lpRecord.firstName,
          lastName = lpRecord.lastName,
          email = lpRecord.email
        ),
        collaborators = lpRecord.collaborators.toList,
        firmName = lpRecord.firmName,
        close = lpRecord.fundSubCloseIdOpt.flatMap(closeId => closeData.closeInfo.find(_.fundSubCloseId == closeId)),
        tags = lpRecord.tags.flatMap(tagsMap.get),
        statusOpt = Option.when(actorGroupRole.canAccessLpSubscriptionDocs)(lpRecord.status),
        orderType = lpRecord.orderType,
        hasPendingRequiredSupportingDoc = lpRecord.hasPendingRequiredSupportingDoc
          || (lpFlowType.isFlexible && amlKycReviewEnabled && lpMissingAmlKycDocs.contains(lpRecord.lpId))
      )
    }
  }

  def batchUpdateInvestorData(
    params: BatchUpdateInvestorDataParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.fundSubId)
      _ <- ZIOUtils.validate(params.values.nonEmpty) {
        new RuntimeException("values must not be empty")
      }
      _ <- params.columnType match {
        case BatchUpdateInvestorData.SingleSelectColumn(id) =>
          val newValueWithColor = StringWithColor(params.values.toSeq.head, Option(LpTagColor.Gray1))
          for {
            _ <- ZIOUtils.foreachParN(10)(params.lpIds) { lpId =>
              fundSubPermissionService.validateUserHasPermissionR(
                userId = actor,
                permission = InvestorPermission.UpdateInvestorCustomData,
                obj = Type.Investor(lpId)
              )
            }
            _ <- customDataService.updateCustomDataColumnInfo[SealedValue.SingleStringValue](
              columnId = id,
              updateDataFnc = currentData => {
                val currentStringValues = currentData.data.value.valueWithColor.toSet
                params.actionType match {
                  case BatchUpdateInvestorData.AddValue() =>
                    Option(
                      currentData.copy(data =
                        currentData.data
                          .copy(value = currentData.data.value.copy((currentStringValues + newValueWithColor).toSeq))
                      )
                    )
                  case BatchUpdateInvestorData.RemoveValue() => None
                }
              },
              actor = actor
            )
            _ <- ZIOUtils.foreachParN(5)(params.lpIds) { lpId =>
              customDataService.updateLpCustomData[SealedValue.SingleStringValue](
                columnId = id,
                lpId = lpId,
                updateDataFnc = _.fold(
                  SealedValue.SingleStringValue(SingleStringValue(Seq(newValueWithColor)))
                ) { currentValue =>
                  val updatedStringValues =
                    params.actionType match {
                      case BatchUpdateInvestorData.AddValue()    => Set(newValueWithColor)
                      case BatchUpdateInvestorData.RemoveValue() => Set.empty
                    }
                  currentValue.copy(value = currentValue.value.copy(valueWithColor = updatedStringValues.toSeq))
                },
                actor = actor
              )
            }
          } yield ()
        case BatchUpdateInvestorData.MultipleSelectColumn(id) =>
          val newValueWithColor = params.values.map(StringWithColor(_, Option(LpTagColor.Gray1)))
          for {
            _ <- ZIOUtils.foreachParN(10)(params.lpIds) { lpId =>
              fundSubPermissionService.validateUserHasPermissionR(
                userId = actor,
                permission = InvestorPermission.UpdateInvestorCustomData,
                obj = Type.Investor(lpId)
              )
            }
            _ <- customDataService.updateCustomDataColumnInfo[SealedValue.MultipleStringValue](
              columnId = id,
              updateDataFnc = currentData => {
                val currentStringValues = currentData.data.value.valueWithColor.toSet
                params.actionType match {
                  case BatchUpdateInvestorData.AddValue() =>
                    Option(
                      currentData.copy(data =
                        currentData.data
                          .copy(value = currentData.data.value.copy((currentStringValues ++ newValueWithColor).toSeq))
                      )
                    )
                  case BatchUpdateInvestorData.RemoveValue() => None
                }
              },
              actor = actor
            )
            _ <- ZIOUtils.foreachParN(5)(params.lpIds) { lpId =>
              customDataService.updateLpCustomData[SealedValue.MultipleStringValue](
                columnId = id,
                lpId = lpId,
                updateDataFnc = _.fold(SealedValue.MultipleStringValue(MultipleStringValue(newValueWithColor.toSeq))) {
                  currentValue =>
                    val currentStringValues = currentValue.value.valueWithColor.toSet
                    val updatedStringValues =
                      params.actionType match {
                        case BatchUpdateInvestorData.AddValue() => currentStringValues ++ newValueWithColor
                        case BatchUpdateInvestorData.RemoveValue() =>
                          currentStringValues.filterNot(value => params.values.contains(value.content))
                      }
                    currentValue.copy(value = currentValue.value.copy(valueWithColor = updatedStringValues.toSeq))
                },
                actor = actor
              )
            }
          } yield ()
        case BatchUpdateInvestorData.TagColumn() =>
          for {
            _ <- ZIOUtils.foreachParN(10)(params.lpIds) { lpId =>
              fundSubPermissionService.validateUserHasPermissionR(
                userId = actor,
                permission = InvestorPermission.ManageLpAdditionalInfo,
                obj = Type.Investor(lpId)
              )
            }
            _ <- ZIOUtils.foreachParN(5)(params.values.toSeq) { tag =>
              fundSubLpTagUtilService.createTag(
                fundSubId = params.fundSubId,
                tagName = tag,
                color = LpTagColor.Gray1,
                actor = actor
              )
            }
            _ <- ZIOUtils.foreachParN(5)(params.lpIds) { lpId =>
              fundSubLpTagUtilService.updateTagsOfLp(
                lpId = lpId,
                updateTagsFnc = existingTags =>
                  params.actionType match {
                    case BatchUpdateInvestorData.AddValue() =>
                      existingTags ++ params.values.map(value => TagInfo(name = value, color = LpTagColor.Gray1))
                    case BatchUpdateInvestorData.RemoveValue() =>
                      existingTags.filterNot(tagInfo => params.values.contains(tagInfo.name))
                  },
                actor = actor
              )
            }
          } yield ()
      }
    } yield ()
  }

  def batchUpdateInvestorDataWithWorkflow(
    params: BatchUpdateInvestorDataParams,
    actor: UserId
  ): Task[Unit] = {
    val batchSize = 10
    for {
      _ <- ZIO.logInfo(s"$actor start resend invitations with workflow")
      fundSubId <- FundSubLpUtils.validateLpListNonEmptyAndBelongToTheSameFund(params.lpIds)
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.fundSubId)
      _ <- params.columnType match {
        case _: BatchUpdateInvestorData.SingleSelectColumn =>
          ZIOUtils.foreachParN(10)(params.lpIds) { lpId =>
            fundSubPermissionService.validateUserHasPermissionR(
              userId = actor,
              permission = InvestorPermission.UpdateInvestorCustomData,
              obj = Type.Investor(lpId)
            )
          }
        case _: BatchUpdateInvestorData.MultipleSelectColumn =>
          ZIOUtils.foreachParN(10)(params.lpIds) { lpId =>
            fundSubPermissionService.validateUserHasPermissionR(
              userId = actor,
              permission = InvestorPermission.UpdateInvestorCustomData,
              obj = Type.Investor(lpId)
            )
          }
        case _: BatchUpdateInvestorData.TagColumn =>
          ZIOUtils.foreachParN(10)(params.lpIds) { lpId =>
            fundSubPermissionService.validateUserHasPermissionR(
              userId = actor,
              permission = InvestorPermission.ManageLpAdditionalInfo,
              obj = Type.Investor(lpId)
            )
          }
      }
      _ <- fundSubBatchActionService.startBatchAction[UpdateInvestorValueItemData](
        fundSubId = fundSubId,
        actor = actor,
        frontendTracking = FundSubBatchActionFrontendTracking.ACTOR_TRACKING,
        batchActionItemData = params.lpIds
          .grouped(batchSize)
          .map { lpIds =>
            UpdateInvestorValueItemData(
              fundSubId = params.fundSubId,
              lpIds = lpIds,
              actor = actor,
              columnType = params.columnType match {
                case BatchUpdateInvestorData.SingleSelectColumn(id)   => SingleSelectColumn(id)
                case BatchUpdateInvestorData.MultipleSelectColumn(id) => MultipleSelectColumn(id)
                case BatchUpdateInvestorData.TagColumn()              => TagColumn()
              },
              actionType = params.actionType match {
                case BatchUpdateInvestorData.AddValue()    => UpdateInvestorValueItemData.ActionType.ADD
                case BatchUpdateInvestorData.RemoveValue() => UpdateInvestorValueItemData.ActionType.REMOVE
              },
              values = params.values.toSeq
            )
          }
          .toSeq
      )
    } yield ()
  }

  private def exportInvestorAdvancedDashboardData(
    params: ExportInvestorDashboardDataParams,
    exportTagsAsColumns: Boolean,
    actor: UserId,
    advancedDashboardType: FundSubDashboardType.AdvancedDashboard
  ) = {
    for {
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.fundSubId)
      _ <- fundSubPermissionService.validateUserHasPermissionR(
        userId = actor,
        permission = DashboardPermission.ViewDashboard,
        obj = Type.Dashboard(advancedDashboardType.dashboardId)
      )
      supportingDocReviewConfigOpt <- FDBRecordDatabase.transact(SupportingDocReviewConfigOperations.Production)(
        _.getOpt(SupportingDocReviewConfigId(params.fundSubId))
      )
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
        _.getFundSubAdminRestrictedModel(params.fundSubId)
      }
      dashboardConfig <- fundSubDashboardService.getDashboardGeneralInfo(
        params.fundSubId,
        Some(advancedDashboardType.dashboardId),
        actor
      )
      dashboardDataForExport <- getDashboardDataForExport(
        params.lpFilter,
        dashboardConfig,
        advancedDashboardType,
        params.fundSubId,
        actor
      )
    } yield FundSubAnalysisUtils.buildAdvancedDashboardLpContactList(
      columns = dashboardDataForExport.columns,
      rows = dashboardDataForExport.rows,
      columnHeaders = dashboardDataForExport.columnHeaders,
      investmentFunds = adminResModel.investmentFunds,
      formFieldsConfig = dashboardConfig.formFieldConfigs,
      exportTagsAsColumns = exportTagsAsColumns,
      isSupportingDocReviewEnabled = supportingDocReviewConfigOpt.exists(!_.mode.isSupportingDocReviewConfigModeDisabled)
    )
  }

  private def getDashboardDataForExport(
    lpFilterEither: Either[Seq[FundSubLpId], LpFilter],
    dashboardGeneralInfos: GetDashboardGeneralInfoResponse,
    advancedDashboardType: FundSubDashboardType.AdvancedDashboard,
    fundSubId: FundSubId,
    actor: UserId
  )(
    using RebacStoreOperation
  ): Task[GetDashboardDataForExport] = {
    lpFilterEither.fold(
      lpIds =>
        getDashboardDataForExportFromLpIds(
          lpIds,
          dashboardGeneralInfos,
          advancedDashboardType,
          fundSubId,
          actor
        ),
      lpFilter =>
        getDashboardDataForExportFromFilter(
          lpFilter,
          advancedDashboardType,
          fundSubId,
          actor
        )
    )
  }

  private def getDashboardDataForExportFromFilter(
    lpFilter: LpFilter,
    advancedDashboardType: FundSubDashboardType.AdvancedDashboard,
    fundSubId: FundSubId,
    actor: UserId
  ): Task[GetDashboardDataForExport] = {
    for {
      getDashboardDataResp <- fundSubDashboardService
        .getDashboardDataUnsafe(
          fundSubId = fundSubId,
          dashboardId = advancedDashboardType.dashboardId,
          queryParams = lpFilter.toAdvancedDashboardQueryParams,
          actor
        )
      dashboardRows <- ZIOUtils.eitherToTask(
        RowConvert
          .toData(
            getDashboardDataResp.dashboardData.headers.map(_.model),
            getDashboardDataResp.dashboardData.rows
          )
      )
    } yield GetDashboardDataForExport(
      columns = getDashboardDataResp.dashboardData.headers.map(_.model),
      rows = dashboardRows,
      columnHeaders = getDashboardDataResp.dashboardData.headers.map(_.header)
    )

  }

  private def getDashboardDataForExportFromLpIds(
    lpIds: Seq[FundSubLpId],
    dashboardGeneralInfos: GetDashboardGeneralInfoResponse,
    advancedDashboardType: FundSubDashboardType.AdvancedDashboard,
    fundSubId: FundSubId,
    actor: UserId
  )(
    using RebacStoreOperation
  ) = {
    for {
      inaccessibleColumnCheck <- fundSubDashboardService.getInaccessibleColumnCheckForUser(fundSubId, actor)
      accessibleColumns = dashboardGeneralInfos.columns.filterNot(inaccessibleColumnCheck).toList
      rowDataResp <- fundSubDashboardService
        .getRowsData(
          fundSubId = fundSubId,
          dashboardIdOpt = Some(advancedDashboardType.dashboardId),
          lpIds = lpIds.toList,
          actor = actor
        )
      dashboardRows <- ZIOUtils.eitherToTask(
        RowConvert
          .toData(
            accessibleColumns,
            rowDataResp.rowsData
          )
      )
      columnHeaders <-
        fundSubDashboardService
          .getDashboardMetadataOptUnsafe(fundSubId, accessibleColumns)
          .map(
            _.map(_.headers.map(_.header))
              .getOrElse(List.empty)
          )
    } yield {
      GetDashboardDataForExport(
        columns = accessibleColumns,
        rows = dashboardRows,
        columnHeaders = columnHeaders
      )
    }
  }

  private def getExportLpContactFileName(
    fundName: String,
    lpFilter: Either[Seq[FundSubLpId], LpFilter],
    dateSuffix: String
  ) = {
    val singleLpId = lpFilter
      .fold(
        {
          case Seq(id) => Some(id)
          case _       => None
        },
        _ => None
      )
    singleLpId
      .fold[Task[String]](
        ZIO.succeed("Investor")
      ) { lpId =>
        for {
          lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
            ops.getFundSubLpModel(lpId)
          }
          investmentEntity = lpModel.firmName
          mainLpInfo <- userProfileService.getUserInfo(lpModel.mainLp)
        } yield
          if (investmentEntity.nonEmpty) {
            investmentEntity
          } else {
            mainLpInfo.fullNameString
          }
      }
      .map { lpInfo =>
        s"${fundName}_${lpInfo}_dashboard_data_$dateSuffix.xlsx"
      }
  }

  def getDataRoomIntegrationInfo(
    params: GetDataRoomIntegrationInfoParams,
    actor: UserId
  ): Task[GetDataRoomIntegrationInfoResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is getting dataroom integration info for ${params.fundSubId}")
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(params.fundSubId, actor)
      dataRoomFolderLink <- fundSubStorageIntegrationService.getDataRoomFolderPageLink(params.fundSubId)
    } yield GetDataRoomIntegrationInfoResponse(dataRoomPageLink = dataRoomFolderLink)
  }

  def getFormFilesInfoForFundAdmin(
    params: GetFormFilesForFundAdminParams,
    actorUserId: UserId
  ): Task[GetFormFilesForFundAdminResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actorUserId is getting form files info for ${params.fundSubId}")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.fundSubId)
      _ <- fundSubPermissionService.validateUserHasFundManagerRoleR(params.fundSubId, actorUserId)
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(params.fundSubId))
      }
      formFiles <- {
        (adminResModel.formIds.headOption, adminResModel.formVersions.headOption.map(_.id)) match {
          case (Some(dynamicFormId), None) =>
            for {
              fileIds <- dynamicFormStorageService.getForm(dynamicFormId).map(_.formFiles)
              sharedFormFiles <- shareFormFilesToCommonFolder(params.fundSubId, fileIds)
            } yield sharedFormFiles.values.toSeq
          case (None, Some(formVersionId)) =>
            formService
              .getForm(
                formVersionId.parent,
                Some(formVersionId),
                actorUserId,
                shouldCheckPermission = false
              )
              .map(_.formData.uploadedPdf.keys.toSeq.flatMap(FormDataConverters.fileIdTypeToFileId))
          case _ => ZIO.attempt(Nil)
        }
      }
      fileInfos <- ZIO.foreachPar(formFiles) { fileId =>
        for {
          portalUser <- executiveAdmin.userId
          fileName <- fileService.getFileName(actorUserId)(fileId)
          createdAtOpt <- fileService.getFileCreatedAt(portalUser)(fileId)
        } yield FormFileInfo(
          fileName,
          fileId,
          createdAtOpt
        )
      }
    } yield GetFormFilesForFundAdminResponse(fileInfos.toList, adminResModel.formVersions)
  }

  def saveFundSubAdminTestFormData(
    params: SaveAdminTestFormDataParams,
    actor: UserId
  )(
    using fsModule: ManageFundSubAdminM
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is saving test form data in ${params.fundSubId}")
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      _ <- fsModule.saveAdminTestFormFillData(
        actor,
        params.fundSubId,
        params.values,
        params.viewedSections
      )
    } yield ()
  }

  def getFundSubAdminTestFormData(
    params: GetAdminTestFormDataParams,
    actor: UserId
  )(
    using fsModule: ManageFundSubAdminM
  ): Task[GetAdminTestFormDataResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is getting test form data in ${params.fundSubId}")
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      res <- fsModule.getAdminTestFormAndValue(params.fundSubId, actor)
    } yield res
  }

  /** Fund managers change bounced emails and resend invitations. `Note`
    *   - Only fund managers can resend a bounced invitation because only fund managers can invite new investors or
    *     collaborators.
    *   - New emails cannot duplicate
    */
  def resendBounceInvitation(
    params: ResendBounceInvitationParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  )(
    using lpModule: ManageFundSubLpM
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is resending bounced invitation for ${params.invitationInfos
          .map(_.ofUserId)} of lp order ${params.fundSubLpId}")
      _ <- fundSubPermissionService.validateUserCanManageInvestor(params.fundSubLpId, actor)
      _ <- {
        val newEmails = params.invitationInfos.map(_.toEmail)
        ZIOUtils.validate(newEmails.distinct.size == newEmails.size)(
          new RuntimeException("New email cannot be duplicated")
        )
      }

      toMainLps <- ZIO.foreach(params.invitationInfos.filter(_.role == FundSubLpRole.LpMain)) { invitationInfo =>
        resendBouncedInvitationForLp(
          params.fundSubLpId,
          actor,
          invitationInfo,
          params.lpEmailTemplate
        )
      }

      toCollaborators <- ZIO.foreach(params.invitationInfos.filter(_.role == FundSubLpRole.LpCollaborator)) {
        invitationInfo =>
          resendBouncedInvitationForCollaborator(
            params.fundSubLpId,
            actor,
            invitationInfo,
            params.lpEmailTemplate,
            httpContext
          )
      }

      userIds = toMainLps.map(_._1) ++ toCollaborators.map(_._1)
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = params.fundSubLpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          orderId = Option(params.fundSubLpId),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.RESEND_INVESTOR_INVITATION,
          eventEmail = Seq(
            Option.when(toMainLps.nonEmpty) {
              AddEventEmailParam(
                fundSubEventType = FundSubEvent.inviteLp,
                emailIds = toMainLps.flatMap(_._2)
              )
            },
            Option.when(toCollaborators.nonEmpty) {
              AddEventEmailParam(
                fundSubEventType = FundSubEvent.inviteLpCollaborator,
                emailIds = toCollaborators.flatMap(_._2)
              )
            }
          ).flatten,
          activityDetail = BouncedInvitationEmailResent(userIds)
        )
      )
      _ <- fundSubLpActivityLogService.logActivity(
        params.fundSubLpId,
        Some(actor),
        detail = BouncedInvitationEmailResent(userIds)
      )
    } yield ()
  }

  def getFundActivityLog(
    params: GetFundSubAdminActivityLogParams,
    actor: ServiceActor
  ): Task[GetFundSubAdminActivityLogResponse] = {
    for {
      _ <- fundSubPermissionService.validateUserHasFundAdminRole(params.fundSubId, actor.userId)
      activities <- params.offset.fold(
        FundSubAdminActivityLogUtils.getLatestActivities(params.fundSubId, params.limit)
      ) { offset =>
        FundSubAdminActivityLogUtils.getActivities(
          params.fundSubId,
          offset = offset,
          limit = params.limit
        )
      }
      participantInfo <- FundSubAdminActivityLogUtils
        .extractParticipantInfoFromFundAdminActivity(
          activities.flatMap(_.detail.flatMap(_.activity.fundAdminActivity))
        )
        .map {
          _ ++ Map(
            actor.userId -> NameEmailInfo(
              firstName = actor.userInfo.firstName,
              lastName = actor.userInfo.lastName,
              email = actor.userInfo.emailAddressStr
            )
          )
        }
      actorInfo <- userProfileService
        .batchGetUserInfos(activities.flatMap(_.actorOpt).toSet)
        .map(_.map { case (userId, userInfo) =>
          userId -> NameEmailInfo(
            firstName = userInfo.firstName,
            lastName = userInfo.lastName,
            email = userInfo.emailAddressStr
          )
        })
      lpInfo <- FundSubAdminActivityLogUtils.extractLpInfoFromFundAdminActivity(
        activities.flatMap(_.detail.flatMap(_.activity.fundAdminActivity))
      )

      (_, gpTeamIds) = FundSubAdminActivityLogUtils.extractGpEntityIdsFromFundAdminActivity(
        activities.flatMap(_.detail.flatMap(_.activity.fundAdminActivity))
      )

      gpTeamNameMap <- ZIO
        .collectAllPar {
          gpTeamIds.map { gpTeamId =>
            FDBRecordDatabase.transact(FundSubGroupStoreOperations.Production)(_.getGroup(gpTeamId).map { teamInfo =>
              gpTeamId -> teamInfo.name
            })
          }
        }
        .map(_.toMap)

    } yield GetFundSubAdminActivityLogResponse(
      activities = activities,
      userInfoMap = participantInfo ++ actorInfo,
      lpInfoMap = lpInfo,
      gpTeamNameMap = gpTeamNameMap
    )
  }

  def markActivitiesAsSeen(
    params: MarkAsSeenFaActivityLogParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(params.fundSubId, actor)
      _ <- activityLogService.markAsSeenActivities(
        params.activityIds,
        params.logId,
        actor
      )
    } yield ()
  }

  /** Replace main lp user with new user id
    *
    * @return
    *   new main lp user id
    */
  private def resendBouncedInvitationForLp(
    fundSubLpId: FundSubLpId,
    actor: UserId,
    invitationInfo: ResendBounceInvitationInfo,
    lpEmailTemplate: Option[UserEmailTemplate]
  )(
    using lpModule: ManageFundSubLpM
  ): Task[(UserId, Option[InternalEmailId])] = {
    for {
      toUserId <- fundSubUserService.createInvestorUserIfNeeded(
        email = invitationInfo.toEmail,
        firstName = invitationInfo.firstName,
        lastName = invitationInfo.lastName,
        fundSubId = fundSubLpId.parent
      )
      fundSubLpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.getFundSubLpModel(fundSubLpId)
      }
      emailId <-
        if (
          fundSubLpModel.mainLp == invitationInfo.ofUserId &&
          fundSubLpModel.bouncedEmailUsers.contains(invitationInfo.ofUserId)
        ) {
          for {
            // If change to new user, then we need to change lp main user
            _ <- ZIOUtils.when(fundSubLpModel.mainLp != toUserId)(
              lpModule.changeLpMainUser(
                lpId = fundSubLpId,
                toLpMainUser = toUserId,
                actor = actor
              )
            )

            // Send new invitation email
            emailIdOpt <- fundSubEmailService
              .sendInviteLpEmail(
                actor,
                toUserId,
                fundSubLpId,
                attachedDocs = fundSubLpModel.attachedDocs,
                userCustomTemplateOpt = lpEmailTemplate.map(_.toEmailTemplateMessage)
              )
              .map(_.flatMap(_.internalIdOpt))
            _ <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
              ops.updateFundSubLpModel(fundSubLpId) { (model: FundSubLpModel) =>
                model.copy(bouncedEmailUsers = model.bouncedEmailUsers.filterNot(_ == invitationInfo.ofUserId))
              }
            }
            _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubLpModel(fundSubLpId)(natsNotificationService)
            _ <- FundSubDataLakeUtils.sendUpdateParams(
              fundSubLpId.parent,
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(fundSubLpId),
                removeEmailBouncedUsers = Seq(invitationInfo.ofUserId)
              ),
              fundSubDataLakeIngestionService
            )
          } yield emailIdOpt
        } else {
          ZIO.attempt(None)
        }
    } yield (toUserId, emailId)
  }

  /** Remove bounced user id and add a new collaborator
    *
    * @return
    *   new collaborator user id
    */
  private def resendBouncedInvitationForCollaborator(
    fundSubLpId: FundSubLpId,
    actor: UserId,
    invitationInfo: ResendBounceInvitationInfo,
    collaboratorEmailTemplate: Option[UserEmailTemplate],
    httpContext: Option[AuthenticatedRequestContext]
  )(
    using lpModule: ManageFundSubLpM
  ): Task[(UserId, Option[InternalEmailId])] = {
    for {
      toUserId <- fundSubUserService.createInvestorUserIfNeeded(
        email = invitationInfo.toEmail,
        firstName = invitationInfo.firstName,
        lastName = invitationInfo.lastName,
        fundSubId = fundSubLpId.parent
      )
      fundSubLpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.getFundSubLpModel(fundSubLpId)
      }
      emailId <-
        if (
          fundSubLpModel.collaborators.contains(invitationInfo.ofUserId) &&
          fundSubLpModel.bouncedEmailUsers.contains(invitationInfo.ofUserId)
        ) {
          for {
            // If change to new user, then we remove current user
            _ <- ZIOUtils.when(invitationInfo.ofUserId != toUserId)(
              lpModule.removeLpCollaborator(
                fundSubLpId = fundSubLpId,
                actor = actor,
                toRemoveCollaborator = invitationInfo.ofUserId,
                notifyRemovedCollaborator = false,
                actorIsFundManager = Some(true),
                httpContext = httpContext
              )
            )
            // If new user is not in the collaborator list, then add it and send invitation email
            emailId <-
              if (!fundSubLpModel.collaborators.contains(toUserId)) {
                lpModule.addLpCollaborator(
                  lpId = fundSubLpId,
                  actor = actor,
                  toAddLpCollaborator = toUserId,
                  collaboratorEmailTemplate = collaboratorEmailTemplate,
                  httpContext = httpContext,
                  notifyAddCollaborator = true,
                  actorIsFundManager = true
                )
              } else {
                // Else we only need to resend invitation email
                fundSubEmailService
                  .sendInviteCollaboratorEmail(
                    actor,
                    toUserId,
                    fundSubLpId,
                    fundSubLpModel.attachedDocs,
                    userCustomTemplateOpt = collaboratorEmailTemplate.map(_.toEmailTemplateMessage)
                  )
                  .map(_.flatMap(_.internalIdOpt))
              }
            _ <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
              ops.updateFundSubLpModel(fundSubLpId) { (model: FundSubLpModel) =>
                model.copy(bouncedEmailUsers = model.bouncedEmailUsers.filterNot(_ == invitationInfo.ofUserId))
              }
            }
            _ <- FundSubDataLakeUtils.sendUpdateParams(
              fundSubLpId.parent,
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(fundSubLpId),
                removeEmailBouncedUsers = Seq(invitationInfo.ofUserId)
              ),
              fundSubDataLakeIngestionService
            )
            _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubLpModel(fundSubLpId)(natsNotificationService)
          } yield emailId
        } else {
          ZIO.attempt(None)
        }
    } yield (toUserId, emailId)
  }

  private def getEnterpriseLoginBindingForFundSubUnsafe(
    fundSubId: FundSubId
  ): Task[Option[EnterpriseLoginConfigEntityBinding]] = {
    for {
      fundSubModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(fundSubId)
      }
      binding <- enterpriseService.getOptEntityBinding(fundSubModel.investorEntity)
    } yield binding
  }

  def isEnterpriseLoginEnabledForFundSub(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[GetFundSubEnvironmentStatusResponse] = {
    for {
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(fundSubId)
      )
      fundSubModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(fundSubId)
      }
      binding <- getEnterpriseLoginBindingForFundSubUnsafe(fundSubId)
      environmentHasSSOPolicy <- fundSubModel.environmentIdOpt.fold(ZIO.succeed(false)) { environmentId =>
        environmentPolicyService.hasReauthenticationPolicy(environmentId)
      }
      isEnterpriseLoginEnabledForFundSub = !environmentHasSSOPolicy && binding.exists(
        _.offeringIds.contains(GlobalOfferingIdFactory.generateId(OfferingId.FundSub))
      )
      isSharableLinkEnabled <- fundSubModel.environmentIdOpt.fold(ZIO.succeed(true)) { environmentId =>
        environmentAuthenticationIntegrationService.isSharableLinkEnabled(
          GlobalOfferingIdFactory.generateId(OfferingId.FundSub),
          environmentId
        )
      }
      ssoProviderForProtectedLinkOpt <- fundSubModel.environmentIdOpt.fold(ZIO.attempt(None)) { environmentId =>
        for {
          configIdOpt <- environmentAuthenticationIntegrationService.resolveSsoProviderForProtectedLink(
            GlobalOfferingIdFactory.generateId(OfferingId.FundSub),
            environmentId
          )
          configOpt <- ZIOUtils.traverseOption2(configIdOpt) { configId =>
            enterpriseService.getOptEnterpriseLoginConfig(configId)
          }
        } yield configOpt
      }
    } yield GetFundSubEnvironmentStatusResponse(
      isEnterpriseLoginEnabledForFundSub,
      isSharableLinkEnabled,
      ssoProviderForProtectedLinkOpt.map(_.providerName)
    )
  }

  def updateEmailTemplates(
    params: UpdateEmailTemplatesParams,
    actor: UserId
  )(
    using adminModule: ManageFundSubAdminM
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is updating the email template for ${params.fundSubId}")
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      _ <- adminModule.updateEmailTemplates(
        params.fundSubId,
        params.templates,
        actor
      )
    } yield ()
  }

  def sendNewInvestorsReportDemoEmail(
    params: SendNewInvestorsReportDemoEmailParams,
    httpContext: AuthenticatedRequestContext
  )(
    using fsModule: ManageFundSubAdminM
  ): Task[Unit] = {
    val actor = httpContext.actor.userId
    for {
      _ <- ZIO.logInfo(s"$actor is sending new investors report demo email")
      _ <- fundSubPermissionService.validateAnduinAdmin(
        actor,
        params.fundSubId,
        PortalFundsubModel.Permission.Write
      )
      _ <- fsModule.sendNewInvestorsReportDemoEmail(params, httpContext.actor.userId)
    } yield ()
  }

  def sendCommentsDigestDemoEmail(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- fundSubPermissionService.validateAnduinAdmin(
        actor,
        fundSubId,
        PortalFundsubModel.Permission.Write
      )
      _ <- ZIO.logInfo(s"$actor is sending comments digest demo email on fund $fundSubId")
      _ <- formCommentEmailService.sendNewFormCommentsDigestEmail(testFundIdOpt = Option(fundSubId))
    } yield ()
  }

  def sendFundActivitiesDemoEmail(
    params: SendDemoFundActivityEmailParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- fundSubPermissionService.validateAnduinAdmin(
        actor,
        params.fundId,
        PortalFundsubModel.Permission.Write
      )
      _ <- ZIO.logInfo(s"$actor is sending fund activities digest demo email on fund ${params.fundId.idString}")
      _ <- fundSubEmailService.sendFundActivitiesEmail(
        params.fundId,
        isDailyEmail = params.isDailyEmail,
        ignoreTimeCheckForTesting = true
      )
    } yield ()
  }

  def adminRemoveLpCollaborator(
    params: AdminRemoveLpCollaboratorParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  )(
    using lpModule: ManageFundSubLpM
  ): Task[Unit] = {
    for {
      _ <- fundSubPermissionService.validateAnduinAdmin(
        actor,
        params.fundSubLpId.parent,
        PortalFundsubModel.Permission.Write
      )
      lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.getFundSubLpModel(params.fundSubLpId)
      }
      _ <- ZIOUtils.validate(lpModel.collaborators.contains(params.collaboratorId)) {
        GeneralServiceException(s"${params.collaboratorId} is not a collaborator of this lp")
      }
      emailId <- lpModule.removeLpCollaborator(
        params.fundSubLpId,
        actor,
        toRemoveCollaborator = params.collaboratorId,
        notifyRemovedCollaborator = false,
        actorIsFundManager = None,
        httpContext = httpContext,
        addActivityLog = false
      )
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = params.fundSubLpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          orderId = Option(params.fundSubLpId),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.COLLABORATOR_REMOVED,
          eventEmail = Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.removeLpCollaborator,
              emailIds = Seq(emailId).flatten
            )
          ),
          ipAddress = httpContext.flatMap(_.getClientIP),
          activityDetail = CollaboratorRemoved(userId = params.collaboratorId, params.fundSubLpId)
        )
      )

    } yield ()
  }

  def requestSupportingDocSignature(
    params: RequestSupportingDocSignatureParams,
    context: AuthenticatedRequestContext
  )(
    using adminModule: ManageFundSubAdminM
  ): Task[RequestSupportingDocSignatureResponse] = {
    val actor = context.actor.userId
    for {
      _ <- ZIO.logInfo(s"${actor.id} is creating additional signature request for ${params.fundSubLpId.idString}")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.fundSubLpId.parent)
      _ <- fundSubPermissionService.validateFundManagerCanManageAdditionalSignatureRequestsR(params.fundSubLpId, actor)
      requestId <- adminModule.requestSupportingDocSignature(params, context)
      request <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(requestId)
      _ <- fundSubLpDashboardService.updateLpInfoRecord(params.fundSubLpId, identity)
      signers = request.signers.map(_.userId)
      additionalRequestedDocPackageOpt = request.getAdditionalRequestedDocPackage(params.fundSubLpId)
      unsignedFileIds = additionalRequestedDocPackageOpt.map(_.getUnsignedSignatureFileIds).getOrElse(Seq.empty)
      _ <- fundSubLpActivityLogService.logActivity(
        lpId = params.fundSubLpId,
        actorOpt = Option(actor),
        detail = LpRequestedToSignOnSupportingDoc(
          fileIds = unsignedFileIds,
          signers = signers,
          requestIds = List(requestId)
        )
      )
    } yield RequestSupportingDocSignatureResponse(requestId)
  }

  def signAdditionalSignatureRequest(
    params: SignAdditionalSignatureRequestParams,
    context: AuthenticatedRequestContext
  )(
    using adminModule: ManageFundSubAdminM
  ): Task[Unit] = {
    val userId = context.actor.userId
    for {
      _ <- ZIO.logInfo(s"${userId.id} is signing supporting doc request of ${params.fundSubLpId.idString}")
      _ <- adminModule.signAdditionalSignatureRequest(params, context)
      _ <- fundSubLpDashboardService.updateLpInfoRecord(params.fundSubLpId, identity)
    } yield ()
  }

  def getFundAdminNotificationPreference(
    params: GetFundSubAdminNotificationPreferenceParams,
    actor: UserId
  ): Task[GetFundSubAdminNotificationPreferenceResponse] = {
    for {
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(params.fundSubId, actor)
      setting <- FundAdminNotificationUtils.getFundNotificationPreference(actor, params.fundSubId)
    } yield GetFundSubAdminNotificationPreferenceResponse(setting)
  }

  def updateFundAdminNotificationPreference(
    params: UpdateFundSubAdminNotificationPreferenceParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(params.fundSubId, actor)
      _ <- ZIO.logInfo(
        s"fund admin $actor is updating notification preference for ${params.fundSubId}: ${params.setting}, setAsDefault = ${params.setAsDefault}"
      )
      _ <- FundAdminNotificationUtils.updateFundNotificationPreference(
        actor,
        params.fundSubId,
        params.setting,
        params.setAsDefault
      )
    } yield ()
  }

  def updateSharedDataRoomLink(
    params: UpdateSharedDataRoomLinkParams,
    actor: UserId
  ): Task[Unit] = {
    val url = params.url.trim
    val actionText = if (url.isEmpty) {
      "is removing the data room link"
    } else {
      s"is sharing the data room link $url"
    }

    for {
      _ <- ZIO.logInfo(s"$actor $actionText of fund ${params.fundSubId}")
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      urlIsValid = url.isEmpty || UrlValidatorUtils.SimpleWebLinkValidator.matches(url)
      _ <- ZIOUtils.validate(urlIsValid)(new RuntimeException(s"Invalid URL format: $url"))
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubPublicModel(params.fundSubId) { (model: FundSubPublicModel) =>
          {
            model.copy(
              sharedDataRoomLink = url,
              sharedDataRoomLinkDisplayName = params.displayName.trim
            )
          }
        }
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(params.fundSubId)(natsNotificationService)
    } yield ()
  }

  def accessFormCompare(
    params: AccessFormCompareParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is accessing form compare in fund ${params.fundSubId}")
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(params.fundSubId, actor)
      _ <- fundSubLoggingService.logEventFormCompareView(
        actor,
        params.fundSubId,
        httpContext
      )
    } yield ()
  }

  def compareForm(params: CompareFormParams, actor: UserId): Task[CompareFormResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is comparing form in fund ${params.fundSubId}")
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(params.fundSubId, actor)
      resModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(params.fundSubId))
      }
      allFormVersionIds = resModel.formVersions.map(_.id)
      _ <- ZIOUtils.validate(allFormVersionIds.contains(params.oldVersionId))(
        GeneralServiceException(s"Version ${params.oldVersionId} is not a valid version in fund ${params.fundSubId}")
      )
      _ <- ZIOUtils.validate(allFormVersionIds.contains(params.newVersionId))(
        GeneralServiceException(s"Version ${params.newVersionId} is not a valid version in fund ${params.fundSubId}")
      )
      resp <- fundSubFormComparisonService.compareForm(
        params.oldVersionId,
        params.newVersionId,
        FormDiffMode.FundSubDiff,
        actor
      )
    } yield resp
  }

  def getCompareResult(params: GetCompareResultParams, actor: UserId): Task[GetCompareResultResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is getting comparison result for ${params.jobId}")
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(params.fundSubId, actor)
      resp <- fundSubFormComparisonService.getCompareResult(params.jobId)
    } yield resp
  }

  def checkLpFormValidation(
    params: CheckLpFormValidationParams,
    actor: UserId
  ): Task[CheckLpFormValidationResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor check form validation for lp ${params.lpId}")
      _ <- fundSubPermissionService.validateAnduinAdmin(
        actor,
        params.lpId.parent,
        PortalFundsubModel.Permission.Read
      )
      formProcessor <- fundSubFormService.getLpFormProcessor(params.lpId)
      formWithValidation = convertFormSectionState(formProcessor, formProcessor.formState.formSectionStates)
    } yield CheckLpFormValidationResponse(formWithValidation)
  }

  def editFormVersionDescription(
    params: EditFormVersionDescriptionParams,
    actor: UserId
  ): Task[EditFormVersionDescriptionResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor editing version description for fund ${params.fundSubId}")
      _ <- fundSubPermissionService.validateAnduinAdmin(
        actor,
        params.fundSubId,
        PortalFundsubModel.Permission.Write
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubAdminRestrictedModel(FundSubAdminRestrictedId(params.fundSubId)) { model =>
          model.copy(
            formVersions = model.formVersions.patch(
              params.index,
              model.formVersions
                .lift(params.index)
                .map(
                  _.copy(
                    description = params.description,
                    editor = Some(actor),
                    updatedAt = Some(Instant.now())
                  )
                ),
              1
            )
          )
        }
      }
    } yield {
      EditFormVersionDescriptionResponse()
    }
  }

  private def convertFormSectionState(
    formProcessor: FormProcessor,
    sectionState: FormState.FormSectionState
  ): FormSectionWithValidation = {
    val section = formProcessor.getSection(sectionState.id).getOrElse(DynamicFormSection())
    FormSectionWithValidation(
      id = section.id,
      alias = section.alias,
      label = section.formDescription.map(_.label).getOrElse(""),
      selfValidation = FormValidation.getErrorMessage(sectionState.selfValidation),
      validation = FormValidation.getErrorMessage(sectionState.validation),
      isHidden = sectionState.isHidden,
      children = sectionState.children.map(convertFormSectionState(formProcessor, _))
    )
  }

  def updateInactiveLpSetting(
    params: UpdateInactiveLpSettingParams,
    actor: UserId
  )(
    using fsModule: ManageFundSubAdminM
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"User $actor update inactive lp setting for fund ${params.fundSubId}:" +
          s" enabled = ${params.enabled}, days = ${params.days}"
      )
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      _ <- fsModule.updateInactiveLpSetting(params)
    } yield ()
  }

  def updateFormCommentFundSettingSwitch(
    params: UpdateFormCommentFundSettingSwitchParams,
    actor: UserId
  ): Task[Unit] = {
    val actionStr = if (params.enableFormComment) "enabled" else "disabled"
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} $actionStr comment on fund ${params.fundSubId}"
      )
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubPublicModel(params.fundSubId) { model =>
          model.copy(
            disabledFormComment = !params.enableFormComment
          )
        }
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(params.fundSubId)(natsNotificationService)
    } yield ()
  }

  def updateFormCommentInvestorDigestEmailSwitch(
    params: UpdateFormCommentInvestorDigestEmailSwitchParams,
    actor: UserId
  ): Task[Unit] = {
    val actionStr = if (params.enableEmail) "enabled" else "disabled"
    for {
      _ <- ZIO.logInfo(
        s"User $actor $actionStr investor comment digest email on fund ${params.fundSubId}"
      )
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubPublicModel(params.fundSubId) { model =>
          model.copy(
            suppressSendingFormCommentDigestEmail = !params.enableEmail,
            formCommentDigestEmailExceptionLps = Seq.empty
          )
        }
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(params.fundSubId)(natsNotificationService)
    } yield ()
  }

  def updateFormCommentInvestorCanResolveSwitch(
    params: UpdateFormCommentInvestorCanResolveSwitchParams,
    actor: UserId
  ): Task[Unit] = {
    val actionStr = if (params.investorCanResolve) "allowed" else "disallowed"
    for {
      _ <- ZIO.logInfo(
        s"User $actor $actionStr investor to resolve comments on fund ${params.fundSubId}"
      )
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubPublicModel(params.fundSubId) { model =>
          model.copy(
            featureSwitch = model.featureSwitch.map { currentFeatureSwitch =>
              currentFeatureSwitch.copy(
                disableLpResolvingComment = !params.investorCanResolve
              )
            }
          )
        }
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(params.fundSubId)(natsNotificationService)
      _ <- actionLoggerService.addEventLog(
        actor,
        events = Seq(
          anduin.protobuf.actionlogger.event.ActionEventDisableLpResolvingComment(
            fundSubId = params.fundSubId,
            disableLpResolvingComment = params.investorCanResolve,
            actor = actor
          )
        ),
        httpContextOpt = None
      )
      _ <- fundSubAuditLogService.addEvent(
        params.fundSubId,
        AddEventParam(
          actor = Option(actor),
          actorType = AuditLogActorType.FundSide,
          eventType = if (params.investorCanResolve) {
            AuditLogEventType.LP_RESOLVING_COMMENT_ENABLED
          } else {
            AuditLogEventType.LP_RESOLVING_COMMENT_DISABLED
          },
          activityDetail = CommentSettingUpdateActivity(
            action = if (params.investorCanResolve) {
              CommentSettingUpdateAction.ENABLE_LP_RESOLVING_COMMENT
            } else {
              CommentSettingUpdateAction.DISABLE_LP_RESOLVING_COMMENT
            }
          )
        )
      )
    } yield ()
  }

  def updateInactiveCommentSetting(
    params: UpdateInactiveCommentSettingParams,
    actor: UserId
  ): Task[Unit] = {
    val actionStr = if (params.setting.enabled) "enabled" else "disabled"
    val days = params.setting.days
    val daysStr = if (params.setting.enabled) s" with $days-day threshold" else ""
    for {
      _ <- ZIO.logInfo(
        s"User $actor $actionStr inactive comment setting$daysStr on fund ${params.fundSubId}"
      )
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      _ <- ZIOUtils.validate(
        (!params.setting.enabled) || (
          params.setting.days > 0 && params.setting.days <= FormCommentUtils.MaxInactiveDaysThreshold
        )
      )(
        GeneralServiceException(s"Invalid value for days: ${params.setting.days}")
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubPublicModel(params.fundSubId) { model =>
          model.copy(
            inactiveCommentSetting = Option(params.setting)
          )
        }
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(params.fundSubId)(natsNotificationService)
      _ <- actionLoggerService.addEventLog(
        actor,
        events = Seq(
          anduin.protobuf.actionlogger.event.ActionEventUpdateInactiveCommentSetting(
            fundSubId = params.fundSubId,
            enabled = params.setting.enabled,
            days = params.setting.days
          )
        ),
        httpContextOpt = None
      )
    } yield ()
  }

  def updateFormCommentDigestEmailExceptionLps(
    params: UpdateFormCommentDigestEmailExceptionLpsParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor update form comment digest email exception Lps")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.fundSubId)
      _ <- formCommentService.commentServiceUtils.validateFormCommentEnabled(params.fundSubId)
      _ <- fundSubPermissionService.validateUserHasFundManagerRoleR(params.fundSubId, actor)
      _ <- fundSubPermissionService.validateUserCanManageInvestorsInFundR(
        (params.addedLps ++ params.removedLps).toSet,
        actor
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubPublicModel(params.fundSubId) { model =>
          model.copy(
            formCommentDigestEmailExceptionLps =
              (model.formCommentDigestEmailExceptionLps.toSet ++ params.addedLps -- params.removedLps).toSeq
          )
        }
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(params.fundSubId)(natsNotificationService)
    } yield ()
  }

  def getFormCommentDigestEmailExceptionInfo(
    params: GetFormCommentDigestEmailExceptionInfoParams,
    actor: UserId
  )(
    using userProfileService: UserProfileService
  ): Task[GetFormCommentDigestEmailExceptionInfoResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is getting form comment digest email exception info for fund ${params.fundSubId}")
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(params.fundSubId, actor)
      fsPublicModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
        _.getFundSubPublicModel(params.fundSubId)
      }
      exceptionLps = fsPublicModel.formCommentDigestEmailExceptionLps
      lpModels <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(
        _.getAllFundSubLpModels(params.fundSubId)
      )
      exceptionLpsInfo <- ZIO.foreachPar(lpModels.filter(lpModel => exceptionLps.contains(lpModel.fundSubLpId))) {
        lpInfo =>
          if (lpInfo.firmName.nonEmpty) {
            ZIO.attempt(
              LpSimpleInfo(
                fundSubLpId = lpInfo.fundSubLpId,
                displayName = lpInfo.firmName,
                mainLpId = lpInfo.mainLp,
                collaborators = lpInfo.collaborators
              )
            )
          } else {
            userProfileService
              .getUserInfo(lpInfo.mainLp)
              .map(CommonUserUtils.fullNameOrEmailString)
              .map(name =>
                LpSimpleInfo(
                  fundSubLpId = lpInfo.fundSubLpId,
                  displayName = name,
                  mainLpId = lpInfo.mainLp,
                  collaborators = lpInfo.collaborators
                )
              )
          }
      }
    } yield GetFormCommentDigestEmailExceptionInfoResponse(
      exceptionLps = exceptionLpsInfo
    )
  }

  def updateCustomFundIdSetting(
    params: UpdateCustomFundIdSettingParams,
    actor: UserId
  ): Task[Unit] = {
    val fundSubId = params.fundSubId
    for {
      _ <- ZIO.logInfo(s"User $actor update custom fund ID setting for $fundSubId")
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
      }
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId)) { model =>
          val customFundIdSetting = model.customFundIdSetting.getOrElse(CustomFundIdSetting())
          model.copy(
            customFundIdSetting = Some(
              customFundIdSetting.copy(
                isEnabled = params.isEnabled,
                customFundId = params.newCustomFundId
              )
            )
          )
        }
      }
      _ <- ZIOUtils.when(!adminResModel.customFundIdSetting.exists(_.customFundId == params.newCustomFundId)) {
        FundSubDataLakeUtils.sendUpdateParams(
          fundId = fundSubId,
          params = AddOrUpdateFundParams(
            id = fundSubId,
            setCustomFundId = Option(params.newCustomFundId)
          ),
          dataLakeService = fundSubDataLakeIngestionService
        )
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubAdminRestrictedModel(fundSubId)(natsNotificationService)
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = params.fundSubId,
        params = AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.CUSTOM_FUND_ID_UPDATED,
          activityDetail = CustomFundIdUpdated(
            wasEnabled = adminResModel.customFundIdSetting.exists(_.isEnabled),
            isEnabled = params.isEnabled,
            oldCustomFundId = adminResModel.customFundIdSetting.map(_.customFundId).getOrElse(""),
            newCustomFundId = params.newCustomFundId
          )
        )
      )
    } yield ()
  }

  def getTableOfContentLpProgress(
    params: GetTableOfContentLpProgressParams,
    actor: UserId
  ): Task[GetTableOfContentLpProgressResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor get table of content progress for ${params.lpId}")
      _ <- fundSubPermissionService.validateFundManagerCanAccessLpSubscriptionDocs(params.lpId, actor)
      (lpRes, lpFormId) <- FDBRecordDatabase.transact(
        FDBOperations[(FundSubLpModelStoreOperations, LPDataOperations)].Production
      ) { case (lpModelOps, lpDataOps) =>
        for {
          lpResModel <- lpModelOps.getFundSubLpRestrictedModel(FundSubLpRestrictedId(params.lpId))
          lpFormId <- lpDataOps.getLastFormId(params.lpId)
        } yield (lpResModel, lpFormId)
      }
      formDataOpt <- FDBRecordDatabase.transact(LpFormDataOperations.Production)(_.getOpt(lpFormId))
      formTableOfContent <- lpFormId match {
        case oldFormId: FundSubLpFormId =>
          for {
            lpFormValues <- formDataOpt.map(FundSubCommonUtils.convertToStringFormData).getOrElse(ZIO.attempt(None))
            formModel <- FDBClient.read(LpForm.lpFormSubspace.getValue(oldFormId))
            form <- ZIOUtils.optionToTask(formModel.form, new RuntimeException("No lp form"))
            formState = DynamicFormFillingProgressCalculator.calculateFormWithState(
              form,
              lpFormValues.map(_.values).getOrElse(Map.empty),
              lpRes.viewedSections.toSet
            )
            formProgress = DynamicFormFillingProgressCalculator.calculateFormProgressFromFormWithState(formState)
            formTableOfContent =
              DynamicFormFillingProgressCalculator.calculateFormTableOfContentFromFormWithState(formState, formProgress)
          } yield formTableOfContent.flatMap(
            getFormTableOfContentSection(
              _,
              0,
              2
            )
          )
        case lpFormVersionId: FundSubLpFormVersionId =>
          // TODO @trancuong81 Only handle default namespace ATM, revise after clarifying what we wanna do here
          val formData = GaiaState
            .fromJsonMap(formDataOpt.map(_.defaultValues.map { case (k, v) => k.rawAlias -> v }).getOrElse(Map.empty))
          for {
            form <- formService
              .getForm(
                lpFormVersionId.parent.parent,
                Some(lpFormVersionId.parent),
                actor,
                shouldCheckPermission = false
              )
              .map(_.formData.form)
          } yield {
            val formTocSections = FormValidationUtils
              .getFormValidationResult(
                // TODO @trancuong81 Only handle default namespace ATM, revise after clarifying what we wanna do here
                form.defaultSchema,
                form.defaultFormSchema,
                formData
              )
              .flatMap(
                getFormTableOfContentSection(
                  _,
                  0,
                  1
                )
              )
            Some(
              FormTableOfContentSection(
                id = "",
                label = "",
                completed = formTocSections.forall(_.completed),
                hasWarning = formTocSections.exists(_.hasWarning),
                children = formTocSections
              )
            )
          }
      }
    } yield GetTableOfContentLpProgressResponse(
      formTableOfContent
    )
  }

  def convertOfflineToNormalOrder(
    lpIds: Seq[FundSubLpId],
    lpEmailTemplate: Option[UserEmailTemplate] = None,
    actor: UserId,
    actorIpAddress: Option[String] = None
  ): Task[ConvertOfflineToNormalOrderResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor convert offline order $lpIds to online order")
      fundSubId <- FundSubLpUtils.validateLpListNonEmptyAndBelongToTheSameFund(lpIds)
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- ZIOUtils.foreachParN(10)(lpIds) { lpId =>
        fundSubPermissionService.validateUserHasPermissionR(
          userId = actor,
          permission = InvestorPermission.ConvertOfflineInvestor,
          obj = Type.Investor(lpId)
        )
      }
      res <- ZIO.foreach(lpIds.filter(_.parent == fundSubId)) { lpId =>
        convertOfflineToNormalOrderInternal(
          lpId,
          lpEmailTemplate,
          actor,
          actorIpAddress
        ).map[ConvertOfflineToNormalOrderStatus](_ => ConvertOfflineToNormalOrderStatus.Succeed)
          .onErrorHandleWith { ex =>
            for {
              _ <- ZIO.logErrorCause(s"Error convert offline to normal for $lpId", ex.toCause)
            } yield ex match {
              case _: NotAnOfflineOrderException => ConvertOfflineToNormalOrderStatus.Succeed
              case _                             => ConvertOfflineToNormalOrderStatus.Failed
            }
          }
          .map(lpId -> _)
      }
    } yield ConvertOfflineToNormalOrderResponse(res.toMap)
  }

  def convertOfflineToNormalOrderWithWorkflow(
    lpIds: Seq[FundSubLpId],
    lpEmailTemplate: Option[UserEmailTemplate] = None,
    actor: UserId
  ): Task[Unit] = {
    val batchSize = 10
    for {
      _ <- ZIO.logInfo(s"$actor start convert offline to normal order with workflow")
      fundSubId <- FundSubLpUtils.validateLpListNonEmptyAndBelongToTheSameFund(lpIds)
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- ZIOUtils.foreachParN(10)(lpIds) { lpId =>
        fundSubPermissionService.validateUserHasPermissionR(
          userId = actor,
          permission = InvestorPermission.ConvertOfflineInvestor,
          obj = Type.Investor(lpId)
        )
      }
      _ <- fundSubBatchActionService.startBatchAction[ConvertOfflineToNormalOrderItemData](
        fundSubId = fundSubId,
        actor = actor,
        frontendTracking = FundSubBatchActionFrontendTracking.ACTOR_TRACKING,
        batchActionItemData = lpIds
          .grouped(batchSize)
          .map { lpIds =>
            ConvertOfflineToNormalOrderItemData(
              lpIds = lpIds,
              actor = actor,
              lpEmailTemplate = lpEmailTemplate.map(_.toEmailTemplateMessage)
            )
          }
          .toSeq
      )
    } yield ()
  }

  /** Used after permission check
    */
  def convertOfflineToNormalOrderInternal(
    lpId: FundSubLpId,
    lpEmailTemplate: Option[UserEmailTemplate],
    actor: UserId,
    actorIpAddress: Option[String]
  ): Task[Unit] = {
    for {
      lpModel <- FDBRecordDatabase.transactC(
        FDBOperations[FundSubLpModelStoreOperations].Production
      ) { case (_, lpModelOps) =>
        for {
          lpModel <- lpModelOps.getFundSubLpModel(lpId)
          _ <- RecordIO.validate(lpModel.orderType.isOfflineOrder)(
            NotAnOfflineOrderException(s"Lp $lpId is not offline order")
          )
          newLpModel <- RecordIO.succeed(lpModel.copy(orderType = LpOrderType.NormalOrder))
          _ <- lpModelOps.updateFundSubLpModel(lpId)(_ => newLpModel)
        } yield lpModel
      }
      _ <- fundSubLpDashboardService.updateLpInfoRecord(
        lpId,
        _.withOrderType(LpOrderType.NormalOrder)
      )
      // Send email to main lp & collaborators
      internalEmailIdOpt <- fundSubEmailService
        .sendInviteLpEmail(
          inviter = actor,
          invitee = lpModel.mainLp,
          lpId = lpId,
          attachedDocs = lpModel.attachedDocs,
          userCustomTemplateOpt = lpEmailTemplate.map(_.toEmailTemplateMessage)
        )
        .map(_.flatMap(_.internalIdOpt))
      collaboratorEmailIds <- ZIO
        .foreach(lpModel.collaborators) { userId =>
          fundSubEmailService
            .sendInviteCollaboratorEmail(
              inviter = actor,
              invitee = userId,
              lpId = lpId,
              attachedDocs = lpModel.attachedDocs,
              userCustomTemplateOpt = lpEmailTemplate.map(_.toEmailTemplateMessage)
            )
        }
        .map(_.flatMap(_.flatMap(_.internalIdOpt)))

      collaboratorEventEmail =
        if (collaboratorEmailIds.nonEmpty) {
          Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.inviteLpCollaborator,
              emailIds = collaboratorEmailIds
            )
          )
        } else {
          Seq.empty
        }
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = lpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          orderId = Option(lpId),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.OFFLINE_INVESTOR_INVITED,
          eventEmail = Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.inviteLp,
              emailIds = Seq(internalEmailIdOpt).flatten
            )
          ) ++ collaboratorEventEmail,
          ipAddress = actorIpAddress,
          activityDetail = LpInvited(
            userId = lpModel.mainLp,
            lpId = lpId,
            collaborators = lpModel.collaborators
          )
        )
      )
      _ <- updateOrderTypeInDataLake(lpId, orderType = LpOrderType.NormalOrder)
      _ <- FundSubAdminActivityLogUtils.addActivity(
        lpId.parent,
        actor,
        ConvertOfflineOrderToNormal(
          lpModel.mainLp,
          lpId
        )
      )
      _ <- fundSubLpActivityLogService.logActivity(
        lpId,
        Option(actor),
        detail = anduin.protobuf.fundsub.activitylog.lp.LpInvited(investmentEntity = lpModel.firmName)
      )
      _ <- ZIO.foreach(lpModel.collaborators) { userId =>
        fundSubLpActivityLogService.logActivity(
          lpId,
          Option(actor),
          detail = anduin.protobuf.fundsub.activitylog.lp.CollaboratorInvited(userId)
        )
      }
    } yield ()
  }

  private def updateOrderTypeInDataLake(lpId: FundSubLpId, orderType: LpOrderType) = {
    FundSubDataLakeUtils.sendUpdateParams(
      lpId.parent,
      UpdateOrderBasicInfoParams(
        lpIdOpt = Option(lpId),
        setOrderType = Option(orderType)
      ),
      fundSubDataLakeIngestionService
    )
  }

  private def getFormTableOfContentSection(
    formTableOfContent: FormTableOfContent,
    level: Int,
    maxLevel: Int
  ): Option[FormTableOfContentSection] = {
    Option.when(maxLevel < 0 || level <= maxLevel)(
      FormTableOfContentSection(
        id = formTableOfContent.id,
        label = formTableOfContent.label,
        completed = formTableOfContent.completed,
        hasWarning = false, // Warning is not supported in dynamic form
        children = formTableOfContent.children.flatMap(
          getFormTableOfContentSection(
            _,
            level + 1,
            maxLevel
          )
        )
      )
    )
  }

  private def getFormTableOfContentSection(
    sectionResult: SectionResult,
    level: Int,
    maxLevel: Int
  ): Option[FormTableOfContentSection] = {
    if (maxLevel < 0 || level <= maxLevel) {
      Some(
        FormTableOfContentSection(
          id = sectionResult.key,
          label = sectionResult.label,
          completed = sectionResult.completed,
          hasWarning = sectionResult.hasWarning,
          children = sectionResult.children.flatMap(
            getFormTableOfContentSection(
              _,
              level + 1,
              maxLevel
            )
          )
        )
      )
    } else {
      None
    }
  }

  def updateInvestingFromAdditionalEntitySetting(
    params: UpdateInvestingFromAdditionalEntitySettingParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundId)
      )
      _ <- ZIO.logInfo(
        s"User $actor is updating the investing from additional entity setting for fund ${params.fundId}: enabled = ${params.enabled}"
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubPublicModel(params.fundId) { model =>
          model.copy(
            featureSwitch = model.featureSwitch.map(
              _.copy(disableInvestFromAdditionalEntity = !params.enabled)
            )
          )
        }
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(params.fundId)(natsNotificationService)
    } yield ()
  }

  def updateCustomLpIdSetting(
    params: UpdateCustomLpIdSettingParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      _ <- ZIO.logInfo(
        s"User $actor is updating enable custom LP ID setting for fund ${params.fundSubId}: enabled = ${params.enabled}"
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubPublicModel(params.fundSubId) { model =>
          model.copy(
            featureSwitch = model.featureSwitch.map(
              _.copy(enabledCustomLpId = params.enabled)
            )
          )
        }
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(params.fundSubId)(natsNotificationService)
      _ <- fundSubViewService.updateCustomIdDashboardColumn(params.fundSubId, params.enabled)
    } yield ()
  }

  def updateRiaBannerVisibility(
    params: UpdateRiaBannerVisibilityParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      _ <- ZIO.logInfo(
        s"User $actor is updating enable ria banner visibility setting for fund ${params.fundSubId}: enabled = ${params.enabled}"
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubPublicModel(params.fundSubId) { model =>
          model.copy(
            featureSwitch = model.featureSwitch.map(
              _.copy(disableRiaBanner = !params.enabled)
            )
          )
        }
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(params.fundSubId)(natsNotificationService)
    } yield ()
  }

  def updateDownloadSubscriptionDocumentSetting(
    params: UpdateDownloadSubscriptionDocumentSettingParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundId)
      )
      _ <- ZIO.logInfo(
        s"User $actor is updating download subscription document setting for fund ${params.fundId}: enabled = ${params.enabled}"
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubPublicModel(params.fundId) { model =>
          model.copy(
            featureSwitch = model.featureSwitch.map(
              _.copy(disableDownloadSubscriptionDocument = !params.enabled)
            )
          )
        }
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(params.fundId)(natsNotificationService)
    } yield ()
  }

  def updateMarkAsNotApplicableSetting(
    params: UpdateMarkAsNotApplicableParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundId)
      )
      _ <- ZIO.logInfo(
        s"User $actor is updating mark as not applicable setting - enabled = ${!params.disabledMarkAsNotApplicable}"
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubPublicModel(params.fundId) { model =>
          model.copy(
            featureSwitch = model.featureSwitch
              .map(
                _.copy(
                  disabledMarkAsNotApplicable = params.disabledMarkAsNotApplicable
                )
              )
              .orElse(
                Option(
                  FeatureSwitch(
                    disabledMarkAsNotApplicable = params.disabledMarkAsNotApplicable
                  )
                )
              )
          )
        }
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(params.fundId)(natsNotificationService)

    } yield ()
  }

  def updateDisableFundContactInInvestorWorkspaceSetting(
    fundSubId: FundSubId,
    disableFundContactInInvestorWorkspace: Boolean,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor updates disable fund contact in investor workspace")
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(fundSubId)
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.updateFundSubPublicModel(fundSubId) { model =>
          model.copy(
            featureSwitch = model.featureSwitch
              .map(_.copy(disableFundContactInInvestorWorkspace = disableFundContactInInvestorWorkspace))
              .orElse(Some(FeatureSwitch(disableFundContactInInvestorWorkspace = disableFundContactInInvestorWorkspace)))
          )
        }
      )
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(fundSubId)(natsNotificationService)
    } yield ()
  }

  def updateFundName(
    params: UpdateFundNameParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundId)
      )
      fundName = params.fundName.trim
      _ <- ZIOUtils.validate(fundName.nonEmpty)(
        new RuntimeException("Fund Name must not be empty")
      )
      _ <- ZIO.logInfo(s"User $actor is updating fund name of fund ${params.fundId} to $fundName")
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubPublicModel(params.fundId) {
          _.copy(
            fundName = fundName
          )
        }
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(params.fundId)(natsNotificationService)
      _ <- FundSubDataLakeUtils.sendUpdateParams(
        params.fundId,
        AddOrUpdateFundParams(
          id = params.fundId,
          setFundName = Option(params.fundName)
        ),
        fundSubDataLakeIngestionService
      )
      _ <- greylinDataService.runUnit(
        FundSubscriptionOperations.update(params.fundId)(
          _.copy(name = params.fundName)
        )
      )
    } yield ()
  }

  def checkReviewerPermission(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[CheckReviewerPermissionResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor checking reviewer permission for lp $lpId")
      _ <- fundSubPermissionService.validateUserCanManageInvestor(lpId, actor)
      fsModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(lpId.parent)
      )
      lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(
        _.getFundSubLpModel(lpId)
      )
      lpStatus = lpModel.getLpState.getLpStatus
      lpEmail <- userProfileService.getEmailAddress(lpModel.mainLp).map(_.address)

      isReviewer <- lpStatus match {
        case LpStatus.LPPendingUnsignedReview | LpStatus.LPPendingReview =>
          if (fsModel.lpFlowType == LpFlowType.Flexible) {
            for {
              reviewFlowOpt <- fundSubSubscriptionDocReviewService.getReviewFlowInternal(
                lpId,
                SubscriptionVersionIndex.Latest,
                reviewType = lpStatus match {
                  case LpStatus.LPPendingUnsignedReview => FundSubSubscriptionDocReviewType.UnsignedSubscription
                  case _                                => FundSubSubscriptionDocReviewType.SignedSubscription
                }
              )
            } yield reviewFlowOpt.flatMap(_._2.currentStepOpt).exists(_.reviewers.map(_.userId).contains(actor))
          } else {
            for {
              reviewPackageDataOpt <- reviewPackageService.getReviewPackageData(lpId.parent)
            } yield reviewPackageDataOpt.exists(config => config.isEnabled && config.reviewerUserIds.contains(actor))
          }

        case _ => ZIO.attempt(false)
      }
    } yield CheckReviewerPermissionResponse(
      isReviewer = isReviewer,
      lpEmail = lpEmail
    )
  }

  def enableReviewPackage(
    params: EnableReviewPackageParams,
    actor: UserId
  ): Task[GeneralServiceResponse] = {
    for {
      _ <- fundSubPermissionService.validateUserHasFundAdminRole(params.fundSubId, actor)
      response <- reviewPackageService.enableReviewPackage(params, actor)
      _ <- ZIOUtils.when(params.notifyReviewers) {
        for {
          emailIds <- ZIO
            .foreach(params.initialReviewers) { reviewer =>
              documentReviewEmailUtils.sendDocumentReviewerAssignmentEmail(
                params.fundSubId,
                actor = actor,
                assignee = reviewer
              )
            }
            .map(_.flatMap(_.flatMap(_.internalIdOpt)))
          _ <- fundSubAuditLogService.addEvent(
            fundSubId = params.fundSubId,
            params = AddEventParam(
              actor = Some(actor),
              actorType = AuditLogActorType.FundSide,
              eventType = AuditLogEventType.REVIEWER_ASSIGNED,
              eventEmail = Seq(
                AddEventEmailParam(
                  fundSubEventType = FundSubEvent.documentReviewAssignment,
                  emailIds = emailIds
                )
              ),
              activityDetail = SubscriptionDocumentReviewEnabled()
            )
          )
          _ <- FundSubAdminActivityLogUtils.addActivity(
            fundSubId = params.fundSubId,
            actor = actor,
            fundAdminActivity = SubscriptionDocumentReviewEnabled()
          )
        } yield ()
      }
    } yield response
  }

  def disableReviewPackage(
    params: DisableReviewPackageParams,
    actor: UserId
  ): Task[DisableReviewPackageResponse] = {
    for {
      _ <- fundSubPermissionService.validateUserHasFundAdminRole(params.fundSubId, actor)
      response <- reviewPackageService.disableReviewPackage(params, actor)
      _ <- ZIOUtils.when(response._2.isSucceeded) {
        for {
          emailIds <-
            if (params.notifyReviewers) {
              ZIO
                .foreach(response._1) { reviewer =>
                  documentReviewEmailUtils.sendDocumentReviewerUnassignmentEmail(
                    params.fundSubId,
                    actor = actor,
                    assignee = reviewer
                  )
                }
                .map(_.flatMap(_.flatMap(_.internalIdOpt)))
            } else {
              ZIO.attempt(Seq.empty)
            }
          _ <- fundSubAuditLogService.addEvent(
            fundSubId = params.fundSubId,
            params = AddEventParam(
              actor = Some(actor),
              actorType = AuditLogActorType.FundSide,
              eventType = AuditLogEventType.REVIEWER_UNASSIGNED,
              eventEmail = Seq(
                AddEventEmailParam(
                  fundSubEventType = FundSubEvent.documentReviewUnassignment,
                  emailIds = emailIds
                )
              ),
              activityDetail = SubscriptionDocumentReviewDisabled()
            )
          )
          _ <- FundSubAdminActivityLogUtils.addActivity(
            fundSubId = params.fundSubId,
            actor = actor,
            fundAdminActivity = SubscriptionDocumentReviewDisabled()
          )
        } yield ()
      }
    } yield response._2
  }

  def assignReviewers(
    params: AssignReviewersParams,
    actor: UserId
  ): Task[GeneralServiceResponse] = {
    for {
      _ <- fundSubPermissionService.validateUserHasFundAdminRole(params.fundSubId, actor)
      response <- reviewPackageService.assignReviewers(params, actor)
      _ <- ZIO.foreach(params.userIds) { reviewer =>
        for {
          emailIdOpt <- ZIO
            .when(params.notifyReviewers) {
              documentReviewEmailUtils
                .sendDocumentReviewerAssignmentEmail(
                  params.fundSubId,
                  actor = actor,
                  assignee = reviewer
                )

            }
            .map(_.flatMap(_.flatMap(_.internalIdOpt)))
          _ <- fundSubAuditLogService.addEvent(
            fundSubId = params.fundSubId,
            params = AddEventParam(
              actor = Some(actor),
              actorType = AuditLogActorType.FundSide,
              eventType = AuditLogEventType.REVIEWER_ASSIGNED,
              eventEmail = emailIdOpt.map { emailId =>
                AddEventEmailParam(
                  fundSubEventType = FundSubEvent.documentReviewAssignment,
                  emailIds = Seq(emailId)
                )
              }.toSeq,
              activityDetail = SubscriptionDocumentReviewerAdded(reviewer)
            )
          )
          _ <- FundSubAdminActivityLogUtils.addActivity(
            fundSubId = params.fundSubId,
            actor = actor,
            fundAdminActivity = SubscriptionDocumentReviewerAdded(
              reviewer
            )
          )
        } yield ()
      }
    } yield response
  }

  def removeReviewers(
    params: RemoveReviewersParams,
    actor: UserId
  ): Task[RemoveReviewersResponse] = {
    for {
      _ <- fundSubPermissionService.validateUserHasFundAdminRole(params.fundSubId, actor)
      response <- reviewPackageService.removeReviewers(params, actor)
      _ <- ZIOUtils.when(response.thereIsPendingLPsOrSucceeded.isRight) {
        for {
          emails <-
            if (params.notifyReviewers) {
              ZIO
                .foreach(params.userIds) { reviewer =>
                  documentReviewEmailUtils
                    .sendDocumentReviewerUnassignmentEmail(
                      params.fundSubId,
                      actor = actor,
                      assignee = reviewer
                    )
                    .map(emailOpt => emailOpt.map((reviewer, _)))
                }
                .map(_.flatten)
            } else {
              ZIO.attempt(Seq.empty)
            }
          emailMap = emails.toMap
          _ <-
            if (response.autoDisabled) {
              for {
                _ <- FundSubAdminActivityLogUtils.addActivity(
                  fundSubId = params.fundSubId,
                  actor = actor,
                  fundAdminActivity = SubscriptionDocumentReviewDisabled()
                )
                _ <- fundSubAuditLogService.addEvent(
                  fundSubId = params.fundSubId,
                  params = AddEventParam(
                    actor = Some(actor),
                    actorType = AuditLogActorType.FundSide,
                    eventType = AuditLogEventType.REVIEWER_UNASSIGNED,
                    eventEmail = Seq(
                      AddEventEmailParam(
                        fundSubEventType = FundSubEvent.documentReviewUnassignment,
                        emailIds = emails.flatMap(_._2.internalIdOpt)
                      )
                    ),
                    activityDetail = SubscriptionDocumentReviewDisabled()
                  )
                )
              } yield ()
            } else {
              ZIO.foreach(params.userIds) { reviewer =>
                for {
                  _ <- FundSubAdminActivityLogUtils.addActivity(
                    fundSubId = params.fundSubId,
                    actor = actor,
                    fundAdminActivity = SubscriptionDocumentReviewerRemoved(
                      reviewer
                    )
                  )
                  _ <- fundSubAuditLogService.addEvent(
                    fundSubId = params.fundSubId,
                    params = AddEventParam(
                      actor = Some(actor),
                      actorType = AuditLogActorType.FundSide,
                      eventType = AuditLogEventType.REVIEWER_UNASSIGNED,
                      eventEmail = Seq(
                        AddEventEmailParam(
                          fundSubEventType = FundSubEvent.documentReviewUnassignment,
                          emailIds = Seq(emailMap.get(reviewer).flatMap(_.internalIdOpt)).flatten
                        )
                      ),
                      activityDetail = SubscriptionDocumentReviewerRemoved(
                        reviewer
                      )
                    )
                  )
                } yield ()
              }
            }
        } yield ()
      }
    } yield response
  }

  def getLpParticipantInfos(
    fundSubId: FundSubId,
    lpFilter: LpFilter,
    actor: UserId
  ): Task[GetLpParticipantInfosResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor get lp participants infos for $fundSubId")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- fundSubPermissionService.validateUserHasFundManagerRoleR(fundSubId, actor)
      lpRecords <- FDBRecordDatabase.transact(LpInfoOperations.Production) { ops =>
        ops.getLpInfoRecords(lpFilter.toLpDashboardQueryParams(fundSubId))
      }
      accessibleLpIds <- FundSubPermissionUtils.getAccessibleLpIdsR(
        fundSubId,
        actor
      )
    } yield {
      val lpParticipants = lpRecords.items.collect {
        case record: LpInfoRecord if accessibleLpIds.contains(record.lpId) =>
          record.lpId -> LpParticipantInfo(
            firmName = record.firmName,
            mainLpInfo = ParticipantInfo(
              userId = record.userId,
              firstName = record.firstName,
              lastName = record.lastName,
              email = record.email
            ),
            collaboratorInfo = record.collaborators.map { info =>
              ParticipantInfo(
                userId = info.userId,
                firstName = info.firstName,
                lastName = info.lastName,
                email = info.email
              )
            }
          )
      }
      GetLpParticipantInfosResponse(lpParticipants)
    }
  }

  private def validateContactsBelongToAnLp(
    contacts: Seq[SingleContact],
    lpId: FundSubLpId
  ): Task[Unit] = {
    for {
      lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(_.getFundSubLpModel(lpId))
      allEmailContactsOfLp <- ZIO
        .foreach(lpModel.mainLp +: lpModel.collaborators) { lpContact =>
          userProfileService.getEmailAddress(lpContact).map(_.address)
        }
        .map(_.toSet)
      _ <- ZIOUtils.validate(
        contacts.forall { contact =>
          allEmailContactsOfLp.contains(contact.email)
        }
      )(GeneralServiceException("All contacts must belong to an LP"))
    } yield ()
  }

  def fundAdminSendCustomEmail(
    fundSubId: FundSubId,
    recipients: Seq[GroupContact],
    ccRecipients: Seq[EmailAddress],
    subject: String,
    message: String,
    attachments: Seq[FileId],
    sendMeCopy: Boolean,
    primaryButtonCTAOpt: Option[String], // None will hide the CTA button in the email content
    actor: UserId
  ): Task[GeneralServiceResponse] = {
    val _ = sendMeCopy
    for {
      _ <- ZIO.logInfo(s"User $actor send investor custom email in $fundSubId")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- fundSubPermissionService.validateUserHasPermissionR(
        userId = actor,
        permission = FundPermission.SendCustomEmail,
        obj = Type.FundSub(fundSubId)
      )
      emails <- ZIO.foreach(recipients) { groupContact =>
        for {
          _ <- validateContactsBelongToAnLp(
            groupContact.contacts,
            groupContact.lpId
          )
          _ <- fundSubPermissionService.validateUserHasPermissionR(
            userId = actor,
            permission = InvestorPermission.SendCustomEmail,
            obj = Type.Investor(groupContact.lpId)
          )
          receiverUserIds <- ZIO
            .foreach(groupContact.contacts) { contact =>
              userProfileService
                .maybeCreateZombieUser(
                  contact.email,
                  FullName(contact.firstName, contact.lastName)
                )
                .either
                .flatMap(
                  _.fold[Task[Option[UserId]]](
                    _ => {
                      ZIO
                        .logInfo(s"Failed to send custom email in $fundSubId to ${contact.email}")
                        .as(Option.empty[UserId])
                    },
                    res => ZIO.succeed(Some(res.userId))
                  )
                )
            }
            .map(_.flatten)
          _ <- FundSubAdminActivityLogUtils.addActivity(
            fundSubId,
            actor,
            SentEmail(
              receiverUserIds
            )
          )
          pageForCTA = DynamicAuthPage.FundSubLpPageV2(fundSubLpId = groupContact.lpId)
        } yield {
          generate.SendCustomEmailGenerate(
            fundSubId = fundSubId,
            actor = actor,
            receivers = receiverUserIds,
            additionalCcReceivers = ccRecipients,
            subject = subject,
            message = message,
            attachments = attachments,
            primaryCTAOpt = primaryButtonCTAOpt.map(_ -> pageForCTA)
          )(fundSubEmailUtils)
        }
      }
      emailCopyToActorOpt <- ZIO.when(sendMeCopy)(
        for {
          fundSubModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
            _.getFundSubPublicModel(fundSubId)
          )
          pageForCTAForActorOpt = primaryButtonCTAOpt.map { primaryButtonCTA =>
            primaryButtonCTA -> DynamicAuthPage
              .FundSubAdminPage(
                entityId = fundSubModel.investorEntity,
                fundSubId = fundSubId
              )
          }
        } yield generate.SendCustomEmailGenerate(
          fundSubId = fundSubId,
          actor = actor,
          receivers = Seq(actor),
          subject = subject,
          message = message,
          attachments = attachments,
          primaryCTAOpt = pageForCTAForActorOpt
        )(fundSubEmailUtils)
      )
      receivers = emails ++ emailCopyToActorOpt.toSeq
      emailIds <- ZIOUtils
        .foreachParN(FundSubAdminService.SendEmailParallelism)(receivers)(
          fundSubEmailService.sendEmail(
            fundSubId,
            _
          )
        )
        .map(_.flatMap(_.flatMap(_.internalIdOpt)))
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = fundSubId,
        params = AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.EMAIL_SENT,
          eventEmail = Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.customEmailFromFundManager,
              emailIds = emailIds
            )
          ),
          activityDetail = anduin.protobuf.activitylog.fundsub.admin.SentEmail(
            userIds = receivers.flatMap(_.receivers)
          )
        )
      )

    } yield GeneralServiceResponse("OK")
  }

  def updateLpCommitment(
    fundSubLpId: FundSubLpId,
    investmentFundIdOpt: Option[InvestmentFundId],
    expectedCommitment: UpdateLpCommitmentOperator,
    acceptedCommitment: UpdateLpCommitmentOperator,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor save lp commitment amount for $fundSubLpId")
      _ <- fundSubPermissionService.validateFundManagerCanAccessLpSubscriptionDocs(fundSubLpId, actor)
      _ <- ZIOUtils.failWhen(investmentFundIdOpt.exists(_.parent != fundSubLpId.parent)) {
        GeneralServiceException("Investment fund and investor do not belong to the same fund")
      }
      _ <- ZIOUtils.failUnless(expectedCommitment.isValid)(GeneralServiceException("Invalid expected commitment"))
      _ <- ZIOUtils.failUnless(acceptedCommitment.isValid)(GeneralServiceException("Invalid accepted commitment"))
      _ <- FundSubCommitmentHelper.fundManagerUpdateLpCommitment(
        lpId = fundSubLpId,
        investmentFundIdOpt = investmentFundIdOpt,
        expectedCommitment = expectedCommitment.toOptionOption,
        acceptedCommitment = acceptedCommitment.toOptionOption
      )
    } yield ()
  }

  def verifyInvestmentEntityIsFromFormData(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Boolean] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is verifying investment entity of lp $lpId is from form data")
      _ <- fundSubPermissionService.validateUserCanManageInvestor(lpId, actor)
      lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(_.getFundSubLpModel(lpId))
    } yield lpModel.firmName.nonEmpty && lpModel.syncedFirmNameFromForm
  }

  def checkSeenAutoSelectedCommentsBanner(actor: UserId): Task[Boolean] = {
    fundSubUserTrackingService
      .getUserTracking(actor)
      .map(_.seenAutoSelectedCommentsBanner)
  }

  def markSeenAutoSelectedCommentsBanner(actor: UserId): Task[Unit] = {
    fundSubUserTrackingService
      .updateUserTracking(actor, _.withSeenAutoSelectedCommentsBanner(true))
      .map(_ => ())
  }

  def checkIfFundManagerCanMakeShareComment(actor: UserId, fundId: FundSubId): Task[Boolean] = {
    fundSubPermissionService
      .checkIfUserHasPermission(
        fundId = fundId,
        userId = actor,
        permission = FundPermission.MakePublicComment,
        obj = Type.FundSub(fundId)
      )
  }

  def getRecentDashboardId(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[Option[FundSubDashboardId]] = {
    fundSubUserTrackingService
      .getUserTracking(actor)
      .map(_.fundSubRecentDashboardId.get(fundSubId))
  }

  def setRecentDashboardId(
    dashboardId: FundSubDashboardId,
    actor: UserId
  ): Task[Unit] = {
    fundSubUserTrackingService
      .updateUserTracking(actor, _.addFundSubRecentDashboardId((dashboardId.parent, dashboardId)))
      .map(_ => ())
  }

  def sendNotifyNewCommentsToInvestor(
    params: FundAdminSendNotifyNewCommentsToInvestorParams,
    actor: UserId
  ): Task[Unit] = {
    val lpId = params.fundSubLpId
    for {
      _ <- ZIO.logInfo(s"User $actor is sending notify new comments to ${params.receivers.size} investors")
      _ <- formCommentService.commentServiceUtils.validateFormCommentEnabled(params.fundSubLpId.parent)
      _ <- fundSubPermissionService.validateUserCanManageInvestor(params.fundSubLpId, actor)
      fundSubModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(lpId.parent)
      }
      lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.getFundSubLpModel(lpId)
      }
      mainLpName <- userProfileService
        .getUserInfo(lpModel.mainLp)
        .map(CommonUserUtils.fullNameOrEmailString)
      emails <- ZIO
        .foreach(params.receivers) { receiverId =>
          val emailData = NewFormCommentEmailDataForLp(
            receiverId = receiverId,
            fundName = fundSubModel.fundName,
            fundSubLpId = lpId,
            lpFirmName = lpModel.firmName,
            mainLpId = lpModel.mainLp,
            mainLpName = mainLpName,
            investorEntity = fundSubModel.investorEntity,
            threads = params.attachedComments,
            from = None,
            to = None
          )
          fundSubEmailService.sendFormCommentNotifEmailForLp(
            emailData,
            actor,
            params.emailTemplate.map(_.toEmailTemplateMessage)
          )
        }
        .map(_.flatten)
      _ <- formCommentService.markFormCommentsAsSentNotification(
        MarkFormCommentsAsSentNotificationParams(params.attachedComments.map(_.id)),
        actor
      )
      _ <- {
        val sentEmailIds = emails.flatMap(_.internalIdOpt)
        fundSubAuditLogService.addEvent(
          params.fundSubLpId.parent,
          AddEventParam(
            actor = Some(actor),
            actorType = AuditLogActorType.FundSide,
            eventType = AuditLogEventType.NEW_COMMENT_NOTIFICATION_SENT,
            orderId = Some(params.fundSubLpId),
            eventEmail = Seq(
              AddEventEmailParam(FundSubEvent.commentsDigestToInvestor, sentEmailIds)
            ),
            activityDetail = NewFormCommentNotificationToInvestor(params.fundSubLpId, lpModel.mainLp)
          )
        )
      }
      _ <- fundSubLpActivityLogService.logActivity(
        lpId = lpId,
        actorOpt = Option(actor),
        detail = LpCommentNotificationSent(
          actor = actor,
          recipients = params.receivers
        )
      )
    } yield ()
  }

  def getLpDocumentsPageData(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[GetLpDocumentsPageResponse] = {
    val fundSubId = lpId.parent
    for {
      _ <- fundSubPermissionService.validateUserCanManageInvestor(lpId, actor)
      lpDashboardItem <- fundSubLpDashboardService
        .getLpDashboardItem(GetLpDashboardItemParams(lpId), actor)
        .map(_.item)
      reviewPackageDataOpt <- reviewPackageService.getReviewPackageData(fundSubId)
      (fundSubModel, adminResModel) <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        for {
          fundSubModel <- ops.getFundSubPublicModel(fundSubId)
          adminResModel <- ops.getFundSubAdminRestrictedModel(fundSubId)
        } yield fundSubModel -> adminResModel
      }
      fundAdminIds <- FDBClient.read(FundSubAdminGeneral.getAdminGeneralModel(fundSubId)).map(_.adminInfo.keys.toSeq)
      fundAdmins <- ZIO.foreach(fundAdminIds.grouped(1000).toSeq) { ids =>
        userProfileService.batchGetUserInfos(ids.toSet)
      }
    } yield GetLpDocumentsPageResponse(
      lpDashboardItem = lpDashboardItem,
      reviewPackageDataOpt = reviewPackageDataOpt,
      fundName = fundSubModel.fundName,
      fundAdmins = fundAdmins.flatten.toMap,
      signatureConfigOpt = fundSubModel.signatureConfig,
      subscriptionDocsOrder = fundSubModel.subscriptionDocsOrder,
      investmentFunds = adminResModel.investmentFunds
    )
  }

  def getInviteLpData(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[GetInviteLpDataResp] = {
    for {
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = fundSubId,
        userId = actor,
        permission = FundPermission.InviteInvestor,
        obj = Type.FundSub(fundSubId)
      )
      closeData <- fundSubCloseService.getFundSubCloseData(fundSubId, actor)
      (fundSubModel, adminResModel) <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        for {
          fundSubModel <- ops.getFundSubPublicModel(fundSubId)
          adminResModel <- ops.getFundSubAdminRestrictedModel(fundSubId)
        } yield fundSubModel -> adminResModel
      }
    } yield GetInviteLpDataResp(
      closeInfo = closeData.closeInfo,
      lpToCloseIds = closeData.lpToCloseIds,
      currencyOpt = FundSubInvestmentFundModelUtils.getFundCurrencyForSingleInvestmentFund(adminResModel),
      featureSwitchOpt = fundSubModel.featureSwitch
    )
  }

  def exportFundComment(
    params: ExportFundCommentParams,
    actor: UserId
  ): Task[TemporalWorkflowId] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.id} is exporting comment data of fund ${params.fundId.idString}")
      _ <- formCommentService.commentServiceUtils.checkFormCommentEnabled(params.fundId)
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(
        params.fundId,
        actor
      )
      workflowId = TemporalWorkflowId.unsafeFromSuffix(
        s"export-comment-${params.fundId.idString}-${UUID.randomUUID}"
      )
      workflowStub <- CommentExportWorkflowImpl.instance
        .getWorkflowStub(
          workflowId,
          _.withWorkflowRunTimeout(CommentExportWorkflowImpl.workflowRunTimeout.toJava)
        )
        .provideEnvironment(temporalEnvironment.workflowClient)
      _ <- ZWorkflowStub.start(
        workflowStub.exportComment(
          ExportCommentParams(
            actor = actor,
            fundId = params.fundId,
            includingInternalComment = params.includingInternalComments
          )
        )
      )
      _ <- actionLoggerService.addEventLog(
        actor,
        events = Seq(
          anduin.protobuf.actionlogger.event.ActionEventFormCommentExportComment(
            fundSubId = params.fundId,
            actor = actor,
            includingInternalComment = params.includingInternalComments
          )
        ),
        httpContextOpt = None
      )
      _ <- fundSubAuditLogService.addEvent(
        params.fundId,
        AddEventParam(
          actor = Option(actor),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.COMMENT_EXPORT,
          activityDetail = CommentExported(
            includingInternalComment = params.includingInternalComments
          )
        )
      )
    } yield workflowId
  }

  private def cancelCommentExportWorkflowTask(workflowId: TemporalWorkflowId, actorOpt: Option[UserId]) = {
    for {
      workflowStub <- CommentExportWorkflowImpl.instance
        .getWorkflowStub(
          workflowId
        )
        .provideEnvironment(temporalEnvironment.workflowClient)
      terminationReasonOpt = actorOpt
        .map { actor =>
          s"Terminated by ${actor.idString}"
        }
        .orElse(Option("System triggered"))
      _ <- workflowStub.terminate(
        terminationReasonOpt
      )
    } yield ()
  }

  def cancelCommentExportTask(
    params: CancelCommentExportTaskParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo("Canceling comment export task")
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(
        params.fundId,
        actor
      )
      taskOpt <- FDBRecordDatabase.transact(CommentExportTaskStoreProvider.Production) { store =>
        CommentExportTaskOperations(store)
          .getOpt(actor, params.fundId)
      }
      workflowIdOpt = taskOpt.flatMap(_.workflowIdOpt)
      _ <- ZIOUtils.when(
        // no need to terminate if the workflow already completed
        taskOpt.flatMap(_.resultFolderIdOpt).nonEmpty
      ) {
        ZIOUtils.traverseOption(workflowIdOpt) { workflowId =>
          cancelCommentExportWorkflowTask(workflowId, Option(actor))
            .catchAllCause { ex =>
              ZIO.logErrorCause(s"Failed to terminate workflow ${workflowId.idString}", ex)
            }
        }
      }
      _ <- FDBRecordDatabase.transact(CommentExportTaskStoreProvider.Production) { store =>
        CommentExportTaskOperations(store)
          .update(
            actor,
            params.fundId,
            _.copy(
              workflowIdOpt = None
            )
          )
      }
    } yield ()
  }

  def getCommentExportTaskStatus(
    fundId: FundSubId,
    actor: UserId
  ): Task[GetCommentExportTaskStatusResponse] = {
    for {
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(
        fundId,
        actor
      )
      taskOpt <- FDBRecordDatabase.transact(CommentExportTaskStoreProvider.Production) { store =>
        CommentExportTaskOperations(store)
          .getOpt(actor, fundId)
      }
      workflowIdOpt = taskOpt.flatMap(_.workflowIdOpt)
    } yield GetCommentExportTaskStatusResponse(
      currentWorkflowIdOpt = workflowIdOpt,
      resultFolderIdOpt = taskOpt.flatMap(_.resultFolderIdOpt)
    )
  }

  def getSmtpConfig(fundSubId: FundSubId, actor: UserId): Task[CustomSmtpServerConfigParams] = {
    for {
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(
        fundSubId,
        actor
      )
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(fundSubId)
      }
      customEmailProviderIdOpt = adminResModel.customSmtpServerConfig.flatMap(_.providerId)
      customEmailProviderOpt <- ZIOUtils.traverseOption2(customEmailProviderIdOpt) { providerId =>
        emailProviderService.getOptEmailProvider(providerId)
      }
    } yield {
      val noPasswordConfig = customEmailProviderOpt
        .map(
          CustomSmtpServerConfig.from(_)
        )
        .map(
          _.copy(
            rawPassword = ""
          )
        )
        .getOrElse(CustomSmtpServerConfig.Default)
      CustomSmtpServerConfigParams(
        enabled = adminResModel.customSmtpServerConfig.exists(_.enabled),
        edited = false,
        config = noPasswordConfig
      )
    }
  }

  def updateSmtpConfig(params: UpdateSmtpConfigParams, actor: UserId): Task[Unit] = {
    for {
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(
        params.fundSubId,
        actor
      )
      publicModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(params.fundSubId)
      }
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(params.fundSubId))
      }
      config = params.smptConfig
      shouldUpdateHostConfig = config.edited && config.enabled
      currentProviderIdOpt = adminResModel.customSmtpServerConfig.flatMap(_.providerId)
      encryptedCustomSmtpConfig <-
        if (shouldUpdateHostConfig) {
          for {
            encryptedPasswordBinary <- encryptionService.encrypt(config.config.rawPassword.getBytes)
            providerId <- currentProviderIdOpt.fold {
              emailProviderService
                .createEmailProvider(
                  EmailProviderModel(
                    name = publicModel.fundName,
                    fromName = config.config.from.name,
                    fromAddress = config.config.from.address,
                    host = config.config.host,
                    port = config.config.port,
                    userName = config.config.userName,
                    encryptedPassword = Base64.toBase64(encryptedPasswordBinary),
                    tls = config.config.tls
                  )
                )
                .map(Some(_))
            } { currentProviderId =>
              emailProviderService
                .updateEmailProvider(
                  currentProviderId,
                  EmailProviderModel(
                    name = publicModel.fundName,
                    fromName = config.config.from.name,
                    fromAddress = config.config.from.address,
                    host = config.config.host,
                    port = config.config.port,
                    userName = config.config.userName,
                    encryptedPassword = Base64.toBase64(encryptedPasswordBinary),
                    tls = config.config.tls
                  )
                )
                .as(Some(currentProviderId))
            }
            _ <- ZIO.foreachDiscard(providerId) { providerId =>
              emailProviderService.createEmailProviderBinding(providerId, params.fundSubId)
            }
          } yield FundSubCustomSmtpServerConfig(
            enabled = config.enabled,
            providerId = providerId
          )
        } else {
          ZIO.succeed(FundSubCustomSmtpServerConfig())
        }
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubAdminRestrictedModel(FundSubAdminRestrictedId(params.fundSubId)) { model =>
          val smtpServerConfig = if (shouldUpdateHostConfig) {
            Option(encryptedCustomSmtpConfig)
          } else {
            model.customSmtpServerConfig.map(_.copy(enabled = config.enabled))
          }
          // Disable custom sender email address if custom smtp server is enabled
          val customSenderConfig = if (config.enabled) {
            model.customSenderEmailAddress.map(_.copy(enabled = false))
          } else {
            model.customSenderEmailAddress
          }
          model.copy(
            customSenderEmailAddress = customSenderConfig,
            customSmtpServerConfig = smtpServerConfig
          )
        }
      }
    } yield ()
  }

  def sendTestEmailUsingCustomSmtp(params: SendTestEmailUsingCustomSmtpParams, actor: UserId): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is sending a test email to ${params.email} using custom SMTP settings")
      _ <- fundSubPermissionService.validateUserHasFundManagerRole(params.fundSubId, actor)
      smtpConfigOpt <- fundSubEmailUtils.getEnabledCustomSmtpServerConfig(params.fundSubId)
      _ <- ZIOUtils.validate(smtpConfigOpt.isDefined)(
        GeneralServiceException("No enabled custom SMTP server configuration found")
      )
      _ <- fundSubEmailService.sendEmail(
        params.fundSubId,
        FundSubTestEmailGenerate(
          fundSubId = params.fundSubId,
          receiver = params.email,
          actor = actor
        )(fundSubEmailUtils)
      )
    } yield ()
  }

  def updateAllowFormEditPostSigning(
    fundSubId: FundSubId,
    isEnabled: Boolean,
    actor: UserId
  ): Task[Unit] =
    for {
      _ <- ZIO.logInfo(s"User $actor is updating allow form edit post signing for fund ${fundSubId.idString}")
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(fundSubId)
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(_.updateFundSubPublicModel(fundSubId) {
        prevModel =>
          prevModel.copy(featureSwitch = Some(prevModel.getFeatureSwitch.copy(allowFormEditPostSigning = isEnabled)))
      })
      _ <- fundSubAuditLogService.addEvent(
        fundSubId,
        AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.ALLOW_POST_SIGNING_UPDATES_WITHOUT_RE_SIGNATURE_UPDATED,
          activityDetail = AdminUpdateAllowFormEditPostSigning(isEnabled)
        )
      )
    } yield ()

}

object FundSubAdminService {

  private val SendEmailParallelism = 10

  private case class GetDashboardDataForExport(
    columns: Seq[DashboardColumn],
    rows: Seq[DashboardRow],
    columnHeaders: List[ColumnHeader]
  )

}
