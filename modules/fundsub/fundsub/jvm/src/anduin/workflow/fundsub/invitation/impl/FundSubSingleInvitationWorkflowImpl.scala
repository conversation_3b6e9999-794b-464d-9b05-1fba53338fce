// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.invitation.impl

import java.time.Duration

import zio.temporal.activity.{ZActivityOptions, ZActivityStub}
import zio.temporal.failure.{ActivityFailure, ApplicationFailure}
import zio.temporal.workflow.{ZSaga, ZWorkflow}
import zio.temporal.{JavaTypeTag, ZRetryOptions}

import anduin.batchaction.{BatchActionItemNoPermissionError, BatchActionItemServerError}
import anduin.forms.model.*
import anduin.fundsub.exception.FundSubException
import anduin.id.fundsub.FundSubBatchInvitationItemId
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpInvitationType
import anduin.protobuf.fundsub.LpOrderType
import anduin.utils.ScalaUtils
import anduin.workflow.*
import anduin.workflow.fundsub.invitation.*
import anduin.workflow.fundsub.invitation.AutoPrefillDocuments.{AutoMarkDocsAsProvided, AutoShareDocsWithLp}

class FundSubSingleInvitationWorkflowImpl extends FundSubSingleInvitationWorkflow {

  private inline def attemptSagaActivity[R](
    inline f: R
  )(
    using JavaTypeTag[R]
  ) = {
    ZSaga.attempt(ZActivityStub.execute(f))
  }

  private inline def compensateSagaActivity[R](
    inline f: R
  )(
    using JavaTypeTag[R]
  ) = {
    ZSaga.compensation {
      ZActivityStub.execute(f)
      ()
    }
  }

  private val activities = ZWorkflow
    .newActivityStub[FundSubInvitationActivities](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofSeconds(30))
        .withScheduleToCloseTimeout(Duration.ofMinutes(10))
        .withTaskQueue(ActivityQueue.FundSubInvitation.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(5))
    )

  override def inviteSingle(singleInviteParam: SingleInvitationParam): SingleInvitationResponse = {
    scribe.info(s"Start inviteSingle workflow for ${singleInviteParam.batchInvitationItemId}")

    try {
      // start invitation
      val startSingleInvitationParam = StartSingleInvitationParam(
        singleInviteParam.batchInvitationItemId,
        Some(ZWorkflow.info.workflowId),
        singleInviteParam.actorIpAddress,
        syncGatewayUpdate = singleInviteParam.syncGatewayUpdate
      )
      val inviteParam = ZActivityStub.execute(activities.startSingleInvitation(startSingleInvitationParam))

      if (ScalaUtils.isMatch[FundSubBatchInvitationStatusRunning](inviteParam.status)) {
        runSingleInvitation(
          singleInviteParam.batchInvitationItemId,
          singleInviteParam.syncGatewayUpdate,
          inviteParam
        ).runOrThrow(ZSaga.Options(parallelCompensation = true))
      } else {
        SingleInvitationResponse(
          None,
          inviteParam.status,
          singleInviteParam.batchInvitationItemId,
          skipInvitationEmails = Seq.empty,
          providedDocTypes = Seq.empty,
          initialFormDataSource = FormDataSource.Empty,
          lpUserId = None,
          collaboratorUserIds = Seq.empty
        )
      }
    } catch {
      case e: ActivityFailure =>
        scribe.info(s"Catch activity failure ${e.getCause}")
        val markFailedResp =
          ZActivityStub.execute(
            activities.markSingleInvitationFailed(
              MarkSingleInvitationFailedParam(
                batchInvitationItemId = singleInviteParam.batchInvitationItemId,
                error = e.getCause match {
                  case applicationFailure: ApplicationFailure
                      if applicationFailure.getType == FundSubException.NoPermissionToStartInvitation.getClass.getName =>
                    BatchActionItemNoPermissionError(applicationFailure.getOriginalMessage)
                  case _ => BatchActionItemServerError(e.getCause.getMessage)
                },
                singleInviteParam.syncGatewayUpdate
              )
            )
          )
        SingleInvitationResponse(
          None,
          markFailedResp.status,
          singleInviteParam.batchInvitationItemId,
          skipInvitationEmails = Seq.empty,
          providedDocTypes = Seq.empty,
          initialFormDataSource = FormDataSource.Empty,
          lpUserId = None,
          collaboratorUserIds = Seq.empty
        )
    }

  }

  private def runSingleInvitation(
    batchInvitationItemId: FundSubBatchInvitationItemId,
    syncGatewayUpdate: Boolean,
    inviteParam: StartSingleInvitationResponse
  ): ZSaga[SingleInvitationResponse] = {
    val fundSubId = inviteParam.fundSubId
    val actor = inviteParam.actor
    val invitationType = if (inviteParam.orderType == LpOrderType.NormalOrder) {
      LpInvitationType.Normal
    } else {
      LpInvitationType.OfflineOrder
    }

    // with up lp user and team
    val setupUserAndTeamParam = SetupUserAndTeamParam(
      fundSubId,
      inviteParam.lpContact,
      inviteParam.collaboratorContacts,
      inviteParam.advisorGroupIdOpt,
      actor
    )

    for {
      setupUserAndTeamResp <- attemptSagaActivity(activities.setupUserAndTeam(setupUserAndTeamParam))
      teamIdsFromLpSide = setupUserAndTeamResp.advisorAdminTeamIdOpt.toSet + setupUserAndTeamResp.lpTeamId
      _ <- compensateSagaActivity(
        activities.compensateSetupUserAndTeam(
          SetupUserAndTeamCompensateParam(fundSubId, teamIdsFromLpSide)
        )
      )
      // with up folder and documents
      setUpFolderAndDocumentsParam = SetUpFolderAndDocumentsParam(
        fundSubId,
        teamIdsFromLpSide,
        actor,
        inviteParam.attachedDocs,
        inviteParam.investorGroupIdOpt
      )
      setUpFolderAndDocumentsResp <- attemptSagaActivity(
        activities.setUpFolderAndDocuments(setUpFolderAndDocumentsParam)
      )
      lpId = setUpFolderAndDocumentsResp.fundSubLpId
      _ <- compensateSagaActivity(
        activities.compensateSetUpFolderAndDocuments(
          SetUpFolderAndDocumentsCompensateParam(
            fundSubLpId = lpId,
            lpFolderIds = setUpFolderAndDocumentsResp.lpFolderIds,
            sharedDocs = setUpFolderAndDocumentsResp.sharedDocs,
            actor = actor
          )
        )
      )

      // with up signature module
      setUpSignatureModuleParam = SetUpSignatureModuleParam(
        lpId,
        teamIdsFromLpSide,
        setupUserAndTeamResp.lpUserId,
        inviteParam.investorGroupIdOpt,
        actor
      )
      _ <- attemptSagaActivity(activities.setUpSignatureModule(setUpSignatureModuleParam))

      // with up lp form
      setUpFormParam = SetUpFormParam(
        lpId,
        teamIdsFromLpSide,
        inviteParam.prefillFromLp,
        actor,
        inviteParam.importItemId,
        inviteParam.importFromFundData
      )
      setUpFormResp <- attemptSagaActivity(activities.setUpForm(setUpFormParam))
      _ <- compensateSagaActivity(
        activities.compensateSetUpForm(
          SetUpFormCompensateParam(
            lpId,
            teamIdsFromLpSide,
            setUpFormResp.lpFormId
          )
        )
      )

      // with up form commenting
      setUpFormCommentingParam = SetUpFormCommentingParam(
        lpId,
        teamIdsFromLpSide,
        actor
      )
      _ <- attemptSagaActivity(activities.setUpFormCommenting(setUpFormCommentingParam))
      _ <- compensateSagaActivity(
        activities.compensateSetUpFormCommenting(
          SetUpFormCommentingCompensateParam(
            lpId,
            teamIdsFromLpSide,
            actor
          )
        )
      )

      // create fund sub models
      createModelParam = CreateFundSubModelParam(
        lpId,
        setupUserAndTeamResp.lpUserId,
        setupUserAndTeamResp.collaboratorUserIds,
        setupUserAndTeamResp.lpTeamId,
        setUpFolderAndDocumentsResp.sharedDocs,
        inviteParam.orderType,
        inviteParam.firmName,
        inviteParam.customId,
        setUpFormResp.s3FormInfo,
        inviteParam.expectedCommitment,
        setUpFormResp.lpFormId,
        setUpFormResp.formVersionId,
        inviteParam.closeId,
        inviteParam.investorGroupIdOpt,
        actor,
        invitationType,
        inviteParam.prefillFromLp,
        inviteParam.importItemId,
        inviteParam.importFromFundData,
        inviteParam.metadata,
        inviteParam.advisorGroupIdOpt
      )
      createModelResp <- attemptSagaActivity(activities.createFundSubModel(createModelParam))
      _ <- compensateSagaActivity(
        activities.compensateCreateFundSubModel(
          CreateFundSubModelCompensateParam(
            lpId,
            setupUserAndTeamResp.lpUserId,
            setupUserAndTeamResp.collaboratorUserIds,
            inviteParam.investorGroupIdOpt,
            inviteParam.actor
          )
        )
      )

      // add order data to datalake if necessary
      _ <- attemptSagaActivity(
        activities.addOrderDataToDataLake(
          AddOrderDataToDataLakeParam(
            lpId,
            setUpFolderAndDocumentsResp.sharedDocs,
            inviteParam.metadata,
            createModelResp.lpFormIdOpt,
            inviteParam.advisorGroupIdOpt,
            inviteParam.investorGroupIdOpt,
            actor
          )
        )
      )

      // set up tag
      _ <-
        if (inviteParam.tagNames.nonEmpty) {
          val setUpLpTagsParam = SetUpLpTagsParam(
            lpId,
            actor,
            inviteParam.tagNames
          )
          attemptSagaActivity(activities.setUpLpTags(setUpLpTagsParam))
        } else {
          ZSaga.succeed(ResponseUnit)
        }

      // set up provided document types
      setUpProvidedDocumentTypesParam = SetUpProvidedDocumentTypesParam(
        fundSubLpId = lpId,
        actor = actor,
        providedDocTypes = inviteParam.importFromFundData
          .withFilter(_.autoPrefillDocuments == AutoMarkDocsAsProvided)
          .map { importFromFundData =>
            AutoMarkImportFromFundData(
              investmentEntityId = importFromFundData.investmentEntityId,
              visibleSupportingDocs = createModelResp.visibleSupportingDocs
            )
          }
          .getOrElse(
            CustomProvidedDocumentTypes(docTypes = inviteParam.docTypesToMarkAsProvided)
          )
      )
      setUpProvidedDocTypesResp <- attemptSagaActivity(
        activities.setUpProvidedDocumentTypes(setUpProvidedDocumentTypesParam)
      )

      // set up shared supporting documents with lp
      setUpShareDocumentsWithLpParam = SetUpShareDocumentsWithLpParam(
        fundSubLpId = lpId,
        actor = actor,
        sharedDocTypes = inviteParam.importFromFundData
          .withFilter(_.autoPrefillDocuments == AutoShareDocsWithLp)
          .map { importFromFundData =>
            AutoShareImportFromFundData(
              investmentEntityId = importFromFundData.investmentEntityId,
              visibleSupportingDocs = createModelResp.visibleSupportingDocs
            )
          }
          .getOrElse(
            CustomSharedDocumentTypes(sharedDocumentsWithLp = inviteParam.sharedDocumentsPerDocType)
          )
      )
      setUpShareDocumentsWithLpResp <- attemptSagaActivity(
        activities.setUpShareDocumentsWithLp(setUpShareDocumentsWithLpParam)
      )

      // with up contact
      setUpContact = SetUpContactParam(
        lpId,
        actor,
        invitationType
      )
      _ <- attemptSagaActivity(activities.setUpContact(setUpContact))
      _ <- compensateSagaActivity(
        activities.compensateSetUpContact(
          SetUpContactCompensateParam(
            lpId,
            actor,
            invitationType
          )
        )
      )

      // set up import from fund data if necessary
      _ <- inviteParam.importFromFundData.fold(
        ZSaga.succeed(ResponseUnit)
      ) { importFromFundData =>
        for {
          _ <- attemptSagaActivity(
            activities.setUpImportFromFundData(
              SetUpImportFromFundDataParam(
                lpId,
                importFromFundData.investmentEntityId,
                actor
              )
            )
          )
          _ <- compensateSagaActivity(
            activities.compensateSetUpImportFromFundData(
              SetUpImportFromFundDataCompensateParam(
                lpId,
                importFromFundData.investmentEntityId,
                actor
              )
            )
          )
        } yield ()
      }

      skipInvitationEmailContacts = (inviteParam.lpContact.toSeq ++ inviteParam.collaboratorContacts)
        .filter(_.skipInvitationEmail)
        .map(_.email)
        .distinct
      enableSSOContacts = (inviteParam.lpContact.toSeq ++ inviteParam.collaboratorContacts)
        .filter(_.enableSso)
        .map(_.email)
        .distinct
      // send email
      sendEmailParam = SendEmailParam(
        lpId,
        actor,
        inviteParam.lpEmailTemplate,
        invitationType,
        inviteParam.firmName,
        skipInvitationEmailContacts,
        enableSSOContacts
      )
      sendEmailResponseOpt <-
        if (inviteParam.importFromFundData.isEmpty) {
          ZSaga.attempt(Option(ZActivityStub.execute(activities.sendEmail(sendEmailParam))))
        } else {
          ZSaga.succeed(None)
        }

      // create activity logs
      createFundSubActivityLogParam = CreateFundSubActivityLogParam(
        lpId,
        setupUserAndTeamResp.lpUserId,
        setupUserAndTeamResp.collaboratorUserIds,
        actor,
        invitationType,
        inviteParam.firmName,
        inviteParam.actorIpAddress,
        sendEmailResult = sendEmailResponseOpt,
        inviteParam.advisorGroupIdOpt
      )
      _ <- attemptSagaActivity(activities.createFundSubActivityLog(createFundSubActivityLogParam))
      _ <- compensateSagaActivity(
        activities.compensateCreateFundSubActivityLog(CreateFundSubActivityLogCompensateParam(lpId))
      )

      // send amplitude and zapier
      sendAmplitudeAndZapierParam = SendAmplitudeAndZapierParam(
        lpId,
        setupUserAndTeamResp.collaboratorUserIds,
        actor,
        inviteParam.lpContact,
        inviteParam.collaboratorContacts,
        inviteParam.firmName,
        invitationType,
        prefilledFromPastLp = inviteParam.prefillFromLp,
        fromDataImport = inviteParam.importItemId.nonEmpty,
        formDataSource = getFormDataSourceName(setUpFormResp.formDataSource)
      )
      _ <- attemptSagaActivity(activities.sendAmplitudeAndZapier(sendAmplitudeAndZapierParam))
      _ <-
        if (createModelResp.hasInitialFormData) {
          val updateSupportingDocsIfNecessaryParam = UpdateSupportingDocsParams(lpId, actor)
          attemptSagaActivity(activities.updateSupportingDocsIfNecessary(updateSupportingDocsIfNecessaryParam))
        } else {
          ZSaga.succeed(ResponseUnit())
        }

      // complete invitation
      completeSingleInvitationParam = CompleteSingleInvitationParam(
        batchInvitationItemId,
        lpId,
        actor,
        syncGatewayUpdate = syncGatewayUpdate
      )
      completeResp <- attemptSagaActivity(activities.completeSingleInvitation(completeSingleInvitationParam))

      addOrderDataToDataPipelineParam = AddOrderDataToDataPipelineParam(
        fundSubLpId = lpId,
        firmName = createModelResp.firmName,
        lpStatus = createModelResp.lpStatus,
        closeId = createModelResp.closeId,
        expectedCommitment = createModelResp.expectedCommitment,
        commitments = createModelResp.commitments,
        lpUserId = setupUserAndTeamResp.lpUserId,
        collaboratorUserIds = setupUserAndTeamResp.collaboratorUserIds,
        metadata = inviteParam.metadata
      )
      _ <- attemptSagaActivity(activities.addOrderDataToDataPipeline(addOrderDataToDataPipelineParam))

    } yield SingleInvitationResponse(
      lpIdOpt = Some(lpId),
      status = completeResp.status,
      batchInvitationItemId = batchInvitationItemId,
      skipInvitationEmails =
        (inviteParam.collaboratorContacts ++ inviteParam.lpContact).withFilter(_.skipInvitationEmail).map(_.email),
      providedDocTypes =
        (setUpProvidedDocTypesResp.providedDocTypes ++ setUpShareDocumentsWithLpResp.sharedDocumentsWithLp.keys.toSeq).distinct,
      initialFormDataSource = createModelResp.initialFormDataSource,
      lpUserId = Some(setupUserAndTeamResp.lpUserId),
      collaboratorUserIds = setupUserAndTeamResp.collaboratorUserIds
    )
  }

  private def getFormDataSourceName(formDataSource: FormDataSource): String = {
    formDataSource match {
      case _: FromGpAutofill               => "GP autofill"
      case _: FromLpAutofill               => "LP autofill"
      case _: FromLpProfile                => "LP profile"
      case _: FromFundDataInvestmentEntity => "Fund data investment entity"
      case _: FromDataExtractionService    => "Data extraction service"
      case FormDataSource.Empty            => ""
    }
  }

}

object FundSubSingleInvitationWorkflowImpl {

  lazy val instance: WorkflowImpl[FundSubSingleInvitationWorkflow, FundSubSingleInvitationWorkflowImpl] =
    WorkflowImpl[FundSubSingleInvitationWorkflow, FundSubSingleInvitationWorkflowImpl](WorkflowQueue.FundSubInvitation)

}
