// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.invitation.impl

import java.time.Instant

import io.circe.Json
import io.temporal.failure.ApplicationFailure
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.batchaction.{ResponseUnit, *}
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.fdb.record.FDBRecordDatabase
import anduin.forms.engine.GaiaEngine.{EngineConfiguration, EngineContext}
import anduin.forms.engine.{GaiaEngine, GaiaState}
import anduin.forms.model.{FormDataSource, FromFundDataInvestmentEntity, FromGpAutofill}
import anduin.forms.service.FormService
import anduin.forms.utils.{FormDataComparison, FormDataUtils, FormSupportingFileUtils}
import anduin.funddata.integration.FundDataIntegrationService
import anduin.fundsub.activitylog.{ActivityLogService, FundSubAdminActivityLogUtils}
import anduin.fundsub.auditlog.FundSubAuditLogService.AddEventParam
import anduin.fundsub.auditlog.{AuditLogActorType, AuditLogEventType, FundSubAuditLogService}
import anduin.fundsub.batchaction.invitation.FundSubBatchInvitationOperations
import anduin.fundsub.comment.FormCommentService
import anduin.fundsub.endpoint.funddataintegration
import anduin.fundsub.exception.FundSubException.NoPermissionToStartInvitation
import anduin.fundsub.form.FundSubFormService
import anduin.fundsub.funddata.FundSubFundDataIntegrationUtils
import anduin.fundsub.group.FundSubGroupTrackingUtils
import anduin.fundsub.integration.funddata.FundSubFundDataIntegrationService
import anduin.fundsub.models.FundSubLpModelStoreOperations
import anduin.fundsub.participant.FundSubParticipantService
import anduin.fundsub.ria.group.FundSubRiaGroupStoreOperations
import anduin.fundsub.service.*
import anduin.fundsub.supportingdoc.v2.{MarkDocAsProvidedParams, UploadSupportingDocFilesParams}
import anduin.greylin.GreylinDataService
import anduin.id.funddata.FundDataInvestmentEntityId
import anduin.id.fundsub.*
import anduin.id.fundsub.FundSubLpFolderTypeId.FolderType
import anduin.model.common.user.UserId
import anduin.model.id.{FolderId, FundSubLpIdFactory}
import anduin.portaluser.ExecutiveAdmin
import anduin.protobuf.activitylog.fundsub.admin.{BatchLpInvited, BatchOfflineOrderAdded}
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpInvitationType
import anduin.protobuf.fundsub.LpOrderType
import anduin.protobuf.fundsub.activitylog.lp.LpInvited
import anduin.protobuf.fundsub.models.SupportingDocInfo
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.fundsub.UserContact
import anduin.workflow.fundsub.invitation.*
import anduin.workflow.fundsub.invitation.FundSubInvitationActivities.DuplicatedLpIdException
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.fundsub.FundSubLpService
import com.anduin.stargazer.service.fundsub.free.module.{FundSubInvitationHelper, FundSubLpInvitationService}
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils

case class FundSubInvitationActivitiesImpl(
  backendConfig: GondorBackendConfig,
  natsNotificationService: NatsNotificationService,
  batchActionService: BatchActionService,
  greylinDataService: GreylinDataService
)(
  using val fileService: FileService,
  val formService: FormService,
  val fundSubImportExportService: FundSubImportExportService,
  val executiveAdmin: ExecutiveAdmin,
  val formCommentingService: FormCommentService,
  val activityLogService: ActivityLogService,
  val fundSubFormService: FundSubFormService,
  val newSupportingDocService: NewSupportingDocService,
  val fundSubParticipantService: FundSubParticipantService,
  val autoPrefillService: AutoPrefillService,
  val fundSubLpService: FundSubLpService,
  val fundSubAuditLogService: FundSubAuditLogService,
  val temporalWorkflowService: TemporalWorkflowService,
  val fundDataIntegrationService: FundDataIntegrationService,
  val fundSubFundDataIntegrationService: FundSubFundDataIntegrationService,
  val fundSubLpInvitationService: FundSubLpInvitationService
) extends FundSubInvitationActivities {

  override def startSingleInvitation(param: StartSingleInvitationParam): StartSingleInvitationResponse = {
    val task = {
      for {
        _ <- ZIO.logInfo(s"startSingleInvitation ${param.batchInvitationItemId}, workflow ${param.workflowId}")
        (batchModel, itemModel) <- fundSubLpInvitationService.startSingleInvitation(
          param.batchInvitationItemId,
          param.workflowId,
          param.syncGatewayUpdate
        )(batchActionService, natsNotificationService)
      } yield StartSingleInvitationResponse(
        fundSubId = batchModel.fundSubId,
        lpContact = itemModel.lpContact,
        collaboratorContacts = itemModel.collaboratorContacts,
        firmName = itemModel.firmName,
        customId = itemModel.customId,
        expectedCommitment = itemModel.expectedCommitment,
        lpEmailTemplate = itemModel.lpEmailTemplate,
        closeId = itemModel.closeId.orElse(batchModel.closeId),
        attachedDocs = itemModel.lpAttachedDocs ++ batchModel.attachedDocs,
        orderType = batchModel.orderType,
        investorGroupIdOpt = itemModel.investorGroupId.orElse(batchModel.investorGroupId),
        actor = batchModel.actor,
        status = itemModel.status,
        tagNames = itemModel.tagNames,
        prefillFromLp = itemModel.prefillFromLp,
        importItemId = itemModel.importItemId,
        actorIpAddress = param.actorIpAddress,
        docTypesToMarkAsProvided = itemModel.docTypesToMarkAsProvided,
        sharedDocumentsPerDocType = itemModel.sharedDocumentsPerDocType,
        importFromFundData = itemModel.importFromFundData,
        metadata = itemModel.metadata,
        advisorGroupIdOpt = itemModel.advisorGroupIdOpt
      )
    }.tapError {
      case err: NoPermissionToStartInvitation =>
        ZIO.fail(
          ApplicationFailure.newNonRetryableFailure(err.getMessage, NoPermissionToStartInvitation.getClass.getName)
        )
      case ex: Throwable =>
        ZIO.fail(ex)
    }
    temporalWorkflowService.executeTask(task, "startSingleInvitation")
  }

  override def setupUserAndTeam(param: SetupUserAndTeamParam): SetupUserAndTeamResponse = {
    val task = {
      for {
        _ <- ZIO.logInfo(s"SetupUserAndTeam for ${param.fundSubId} and lp ${param.lpContact}")

        lpContact <- ZIOUtils.optionToTask(param.lpContact, new RuntimeException("No lp contact"))

        lpUserId <- fundSubLpInvitationService.setUpUserIfNeeded(
          param.fundSubId,
          lpContact,
          param.actor
        )

        collaboratorUserIds <- ZIO
          .foreach(param.collaboratorContacts)(
            fundSubLpInvitationService.setUpUserIfNeeded(
              param.fundSubId,
              _,
              param.actor
            )
          )
          .map(_.distinct.filter(_ != lpUserId)) // Should be unique and different from LP user

        lpTeamId <- fundSubLpInvitationService.setUpLpTeam(
          param.fundSubId,
          param.actor,
          initialUserIds = lpUserId +: collaboratorUserIds
        )

        advisorGroupAdminTeamIdOpt <- ZIOUtils.traverseOption(param.advisorGroupIdOpt) { advisorGroupId =>
          FDBRecordDatabase.transact(FundSubRiaGroupStoreOperations.Production) { ops =>
            ops.get(advisorGroupId).map(_.adminTeamId)
          }
        }
      } yield SetupUserAndTeamResponse(
        lpUserId,
        collaboratorUserIds,
        lpTeamId,
        advisorGroupAdminTeamIdOpt
      )
    }.tapError {
      case UserProfileService.InvalidEmail(email) =>
        ZIO.fail(
          ApplicationFailure.newNonRetryableFailure(email, UserProfileService.InvalidEmail.getClass.getName)
        )
      case _ => ZIO.unit
    }

    temporalWorkflowService.executeTask(task, "setupUserAndTeam")
  }

  override def compensateSetupUserAndTeam(
    param: SetupUserAndTeamCompensateParam
  ): ResponseUnit = {
    scribe.info(s"compensateSetupUserAndTeam for ${param.fundSubId}, team ${param.teamIdsFromLpSide}")
    // TODO: Remove team with id
    ResponseUnit()
  }

  override def setUpFolderAndDocuments(param: SetUpFolderAndDocumentsParam): SetUpFolderAndDocumentsResponse = {
    val task = for {
      _ <- ZIO.logInfo(s"setUpFolderAndDocuments for ${param.fundSubId}, team ${param.teamIdsFromLpSide}")

      lpId <- ZIO.succeed(FundSubLpIdFactory.unsafeRandomId(param.fundSubId))
      lpModelOpt <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.getOptFundSubLpModel(lpId)
      }
      _ <- ZIOUtils.validate(lpModelOpt.isEmpty)(DuplicatedLpIdException(s"LpId $lpId is duplicated"))
      lpFolderIds <- fundSubLpInvitationService.initializeLpFolders(
        lpId,
        param.actor,
        param.teamIdsFromLpSide,
        param.investorGroupIdOpt
      )

      // Share attached docs to lp's folder
      sharedDocs <- ZIO.foreach(param.attachedDocs) { fileId =>
        fileService.copyFileInSameFeature(
          param.actor,
          fileId,
          FolderId.channelSystemFolderId(FolderType.ReferenceDoc(lpId)),
          None
        )
      }
    } yield SetUpFolderAndDocumentsResponse(
      fundSubLpId = lpId,
      lpFolderIds = lpFolderIds,
      sharedDocs = sharedDocs
    )

    temporalWorkflowService.executeTask(task, "setUpFolderAndDocuments")
  }

  override def compensateSetUpFolderAndDocuments(
    param: SetUpFolderAndDocumentsCompensateParam
  ): ResponseUnit = {
    val task = for {
      _ <- ZIO.logInfo(s"compensateSetUpFolderAndDocuments for ${param.fundSubLpId}, folders ${param.lpFolderIds}")
      _ <- fileService.deleteFilesAndFoldersRecursivelyUnsafe(
        actor = param.actor,
        folderIds = param.lpFolderIds,
        fileIds = param.sharedDocs
      )
    } yield ResponseUnit()

    temporalWorkflowService.executeTask(task, "compensateSetUpFolderAndDocuments")
  }

  override def setUpSignatureModule(param: SetUpSignatureModuleParam): ResponseUnit = {
    val task = for {
      _ <- ZIO.logInfo(s"setUpSignatureModule for LP ${param.fundSubLpId}")
      _ <- fundSubLpInvitationService.setUpFundSubSignatureModelForLp(
        param.fundSubLpId,
        param.teamIdsFromLpSide,
        param.investorGroupIdOpt,
        param.actor
      )
    } yield ResponseUnit()

    temporalWorkflowService.executeTask(task, "setUpSignatureModule")
  }

  private def getPrefillDataFromLp(
    curLpId: FundSubLpId,
    prefillFromLpIdOpt: Option[FundSubLpId],
    actorId: UserId
  ): Task[(Option[Either[Map[AliasId, Json], GaiaState]], Option[FormDataSource])] = {
    prefillFromLpIdOpt.map { pastLpId =>
      for {
        prefillData <- autoPrefillService.computePrefillDataFromPastLp(
          pastLpId,
          Left(curLpId.parent),
          actorId,
          isOverwritten = false
        )
        curFormModel <- autoPrefillService.getFundFormModel(curLpId.parent, actorId)
      } yield {
        val formData = curFormModel.left
          .map(_ => prefillData.formData)
          .map(gaiaModel => FormDataUtils.setAllPageTouched(prefillData.result.gaiaState, gaiaModel.formData.form))
        val source = FromGpAutofill(
          curLpId.parent,
          pastLpId,
          actorId,
          Option(Instant.now)
        )

        Some(
          formData.left
            .map(
              _.map { case (alias, value) => AliasId.generate(alias, "") -> value }
            )
        ) -> Some(source)
      }
    } getOrElse ZIO.attempt(None -> None)
  }

  private def getPrefillDataFromFDM(
    fundSubId: FundSubId,
    fundDataInvestmentEntityId: FundDataInvestmentEntityId,
    dataImportItemIdOpt: Option[FundSubDataImportItemId],
    actor: UserId
  ): Task[(GaiaState, FormDataSource)] = {
    for {
      resp <- fundDataIntegrationService.computeSubscriptionFormDataAppendWithInvestmentEntityProfile(
        fundDataInvestmentEntityId,
        fundSubId,
        actor
      )
      prefilledDataFromFDM = resp.computedState
      subscriptionForm = resp.subscriptionForm
      prefilledDataFromFDMWithAdditionalData <- dataImportItemIdOpt.fold(
        ZIO.attempt(prefilledDataFromFDM)
      ) { dataImportItemId =>
        fundSubImportExportService.upsertImportItem(
          fundSubId,
          currentState = prefilledDataFromFDM,
          dataImportItemId,
          actor
        )
      }
    } yield {
      val initialDefaultData = GaiaEngine
        .make(
          subscriptionForm.form,
          EngineConfiguration.default,
          EngineContext.default
        )
        .flatMap(_.replay(List.empty).map(_._1))
        .fold(_ => GaiaState.empty, identity)
      val formComparison = FormDataComparison(
        form = subscriptionForm,
        computedFormState = prefilledDataFromFDMWithAdditionalData,
        previousFormState = initialDefaultData
      )
      val source = FromFundDataInvestmentEntity(
        fundDataInvestmentEntityId = fundDataInvestmentEntityId,
        actorId = actor,
        occurAt = Some(Instant.now),
        prefilledVisibleFields = formComparison.changedVisibleMatchings.length,
        prefilledHiddenFields = formComparison.changedHiddenMatchings.length
      )
      prefilledDataFromFDMWithAdditionalData -> source
    }
  }

  private def extractInitialFormDataAndSourceOpt(
    fundSubLpId: FundSubLpId,
    importFromFundData: Option[ImportFromFundData],
    importItemId: Option[FundSubDataImportItemId],
    prefillFromLp: Option[FundSubLpId],
    actorId: UserId
  ): Task[(Option[Either[Map[AliasId, Json], GaiaState]], Option[FormDataSource])] = {
    importFromFundData
      .filter(_.prefillFormData)
      .fold(
        importItemId.fold(
          getPrefillDataFromLp(
            fundSubLpId,
            prefillFromLp,
            actorId
          )
        ) { importItemId =>
          fundSubImportExportService
            .getImportItemResult(importItemId, actorId)
            .map(_.gaiaStateOpt.map(Right.apply) -> Option.empty[FormDataSource])
        }
      ) { importFromFundData =>
        getPrefillDataFromFDM(
          fundSubId = fundSubLpId.parent,
          fundDataInvestmentEntityId = importFromFundData.investmentEntityId,
          dataImportItemIdOpt = importItemId,
          actor = actorId
        ).map { case (formData, source) => Some(Right(formData)) -> Some(source) }
      }
  }

  private def getVisibleSupportingDocumentsFromInitialFormData(
    initialFormDataOpt: Option[Either[Map[AliasId, Json], GaiaState]],
    fundSubId: FundSubId,
    actorId: UserId
  ): Task[Seq[SupportingDocInfo]] = {
    initialFormDataOpt match {
      case Some(formDataEither) =>
        formDataEither match {
          case Left(_) => ZIO.succeed(Seq.empty[SupportingDocInfo])
          case Right(gaiaState) =>
            for {
              fundFormModel <- autoPrefillService.getFundFormModel(
                fundId = fundSubId,
                actorId = actorId,
                ignoreRoleCheck = true
              )
              visibleSupportingDocuments = fundFormModel match {
                case Left(_) => Seq.empty[SupportingDocInfo]
                case Right(gaiaForm) =>
                  FormSupportingFileUtils
                    .getVisibleSupportingFileGroups(
                      form = gaiaForm.formData.form,
                      formStates = gaiaState.defaultStateMap
                    )
                    .flatMap(_.files.map(doc => SupportingDocInfo(doc.description, doc.helpText)))
              }
            } yield visibleSupportingDocuments
        }
      case None => ZIO.succeed(Seq.empty[SupportingDocInfo])
    }
  }

  override def setUpForm(param: SetUpFormParam): SetUpFormResponse = {
    val task = {
      for {
        _ <- ZIO.logInfo(s"setUpForm for ${param.fundSubLpId}, team ${param.teamIdsFromLpSide}")
        (initialFormDataOpt, initialFormDataSourceOpt) <- extractInitialFormDataAndSourceOpt(
          fundSubLpId = param.fundSubLpId,
          importFromFundData = param.importFromFundData,
          importItemId = param.importItemId,
          prefillFromLp = param.prefillFromLp,
          actorId = param.actorId
        )
        (s3FormInfoOpt, lpFormId, formVersionIdOpt) <- fundSubFormService.setUpLpForm(
          param.fundSubLpId.parent,
          param.teamIdsFromLpSide,
          initialFormData = initialFormDataOpt,
          useInitialGaiaStateDirectly =
            param.importFromFundData.exists(_.prefillFormData) || param.prefillFromLp.nonEmpty,
          initialFormDataSourceOpt = initialFormDataSourceOpt
        )
      } yield SetUpFormResponse(
        s3FormInfo = s3FormInfoOpt,
        lpFormId = lpFormId,
        formVersionId = formVersionIdOpt,
        formDataSource = initialFormDataSourceOpt.getOrElse(FormDataSource.Empty)
      )
    }

    temporalWorkflowService.executeTask(task, "setUpForm")
  }

  override def compensateSetUpForm(param: SetUpFormCompensateParam): ResponseUnit = {
    val task = for {
      _ <- ZIO.logInfo(s"compensateSetUpForm for ${param.fundSubLpId}, form ${param.lpFormId}")
      _ <- fundSubLpInvitationService.compensateSetUpLpForm(
        param.lpFormId,
        param.teamIdsFromLpSide
      )
    } yield ResponseUnit()

    temporalWorkflowService.executeTask(task, "compensateSetUpForm")
  }

  override def setUpFormCommenting(param: SetUpFormCommentingParam): ResponseUnit = {
    val task = {
      for {
        _ <- ZIO.logInfo(s"setUpFormCommenting for ${param.fundSubLpId}, team ${param.teamIdsFromLpSide}")
        // TODO: double check to make sure idempotent
        _ <- formCommentingService.initializeLpComment(
          param.fundSubLpId,
          param.teamIdsFromLpSide,
          param.actor
        )
      } yield ResponseUnit()
    }

    temporalWorkflowService.executeTask(task, "setUpFormCommenting")
  }

  override def compensateSetUpFormCommenting(
    param: SetUpFormCommentingCompensateParam
  ): ResponseUnit = {
    scribe.info(s"compensateSetUpFormCommenting for ${param.fundSubLpId}, team ${param.teamIdsFromLpSide}")
    // TODO: Cleanup form commenting data
    ResponseUnit()
  }

  override def createFundSubModel(param: CreateFundSubModelParam): CreateFundSubModelResponse = {
    val task = {
      for {
        _ <- ZIO.logInfo(s"createFundSubModel for ${param.fundSubLpId}, user ${param.lpUserId}")
        (initialFormDataOpt, initialFormDataSourceOpt) <- extractInitialFormDataAndSourceOpt(
          fundSubLpId = param.fundSubLpId,
          importFromFundData = param.importFromFundData,
          importItemId = param.importItemId,
          prefillFromLp = param.prefillFromLp,
          actorId = param.actor
        )
        setUpLpModelResp <- fundSubLpInvitationService.setUpFundSubLpModel(
          param.fundSubLpId,
          param.lpUserId,
          param.collaboratorUserIds,
          param.lpTeamId,
          param.sharedDocs,
          param.orderType,
          param.firmName,
          param.customId,
          param.s3FormInfo,
          param.expectedCommitment,
          param.lpFormId,
          param.formVersionId,
          param.closeId,
          param.investorGroupId,
          param.actor,
          param.invitationType,
          initialFormDataOpt = initialFormDataOpt,
          useInitialGaiaStateDirectly =
            param.importFromFundData.exists(_.prefillFormData) || param.prefillFromLp.nonEmpty,
          initialFormDataSourceOpt = initialFormDataSourceOpt,
          duplicatedFromLpIdOpt = None,
          metadata = param.metadata,
          advisorGroupIdOpt = param.advisorGroupIdOpt
        )(natsNotificationService)
        visibleSupportingDocuments <- getVisibleSupportingDocumentsFromInitialFormData(
          initialFormDataOpt = initialFormDataOpt,
          fundSubId = param.fundSubLpId.parent,
          actorId = param.actor
        )
      } yield CreateFundSubModelResponse(
        visibleSupportingDocs = visibleSupportingDocuments,
        hasInitialFormData = initialFormDataOpt.nonEmpty,
        initialFormDataSource = initialFormDataSourceOpt.getOrElse(FormDataSource.Empty),
        lpFormIdOpt = setUpLpModelResp.lpFormIdOpt,
        firmName = setUpLpModelResp.firmName,
        lpStatus = setUpLpModelResp.lpStatus,
        closeId = setUpLpModelResp.closeId,
        expectedCommitment = setUpLpModelResp.expectedCommitment,
        commitments = setUpLpModelResp.commitments
      )
    }

    temporalWorkflowService.executeTask(task, "createFundSubModel")
  }

  override def compensateCreateFundSubModel(
    param: CreateFundSubModelCompensateParam
  ): ResponseUnit = {
    val task = for {
      _ <- ZIO.logInfo(s"compensateCreateFundSubModel for ${param.fundSubLpId}, user ${param.lpUserId}")
      _ <- fundSubLpInvitationService.compensateCreateFundSubModel(
        param.fundSubLpId,
        param.lpUserId,
        param.collaboratorUserIds,
        param.actor
      )
    } yield ResponseUnit()

    temporalWorkflowService.executeTask(task, "compensateCreateFundSubModel")
  }

  override def addOrderDataToDataLake(
    param: AddOrderDataToDataLakeParam
  ): ResponseUnit = {
    val task = for {
      _ <- ZIO.logInfo(s"addOrderDataToDataLake for ${param.fundSubLpId}")
      _ <- fundSubLpInvitationService.addOrderDataToDataLake(
        param.fundSubLpId,
        param.sharedDocs,
        param.metadata,
        param.lpFormIdOpt,
        param.advisorGroupIdOpt,
        param.investorGroupIdOpt,
        param.actorId
      )
    } yield ResponseUnit()
    temporalWorkflowService.executeTask(task, "addOrderDataToDataLake")
  }

  override def addOrderDataToDataPipeline(
    param: AddOrderDataToDataPipelineParam
  ): ResponseUnit = {
    val task = for {
      _ <- ZIO.logInfo(s"addOrderDataToDataPipeline for ${param.fundSubLpId}")
      _ <- fundSubLpInvitationService.addOrderDataToDataPipeline(
        lpId = param.fundSubLpId,
        lpUserId = param.lpUserId,
        collaboratorUserIds = param.collaboratorUserIds,
        refinedFirmName = param.firmName,
        lpStatus = param.lpStatus,
        closeId = param.closeId,
        metadata = param.metadata,
        expectedCommitmentOpt = param.expectedCommitment,
        commitments = param.commitments
      )
    } yield ResponseUnit()
    temporalWorkflowService.executeTask(task, "addOrderDataToDataPipeline")
  }

  override def setUpLpTags(param: SetUpLpTagsParam): ResponseUnit = {
    val task = for {
      _ <- ZIO.logInfo(s"setUpLpTags for ${param.fundSubLpId}")
      _ <- fundSubLpInvitationService.setUpLpTags(
        param.fundSubLpId,
        param.actor,
        param.tagNames
      )
    } yield ResponseUnit()

    temporalWorkflowService.executeTask(task, "setUpLpTags")
  }

  override def setUpProvidedDocumentTypes(param: SetUpProvidedDocumentTypesParam)
    : SetUpProvidedDocumentTypesResponse = {
    val task = for {
      _ <- ZIO.logInfo(s"setUpProvidedDocumentTypes for ${param.fundSubLpId}")
      docTypes <- param.providedDocTypes match {
        case customProvidedDocumentTypes: CustomProvidedDocumentTypes =>
          ZIO.succeed(customProvidedDocumentTypes.docTypes)
        case autoMarkImportFromFundData: AutoMarkImportFromFundData =>
          for {
            documentInfos <- fundSubFundDataIntegrationService.getInvestmentEntityDocumentsUnsafe(
              investmentEntityId = autoMarkImportFromFundData.investmentEntityId,
              actor = param.actor
            )
            documentTypes <- fundSubFundDataIntegrationService.getFirmDocumentTypesUnsafe(
              autoMarkImportFromFundData.investmentEntityId.parent.parent
            )
            toMarkAsProvidedDocuments = FundSubFundDataIntegrationUtils
              .getMatchedSupportingDocWithInvestmentEntityDocuments(
                supportingDocs = autoMarkImportFromFundData.visibleSupportingDocs.map(doc =>
                  funddataintegration.SupportingDocInfo(doc.name, doc.description)
                ),
                investmentEntityDocuments = documentInfos,
                documentTypes = documentTypes,
                instNow = Instant.now
              )
          } yield toMarkAsProvidedDocuments.keys.toSeq
        case _ => ZIO.succeed(Seq.empty[String])
      }
      _ <- ZIO.when(docTypes.nonEmpty) {
        newSupportingDocService.markDocAsProvided(
          MarkDocAsProvidedParams(param.fundSubLpId, docTypes),
          actor = param.actor,
          shouldValidateUserHasPermission = false
        )
      }
    } yield SetUpProvidedDocumentTypesResponse(docTypes)

    temporalWorkflowService.executeTask(task, "setUpProvidedDocumentTypes")
  }

  override def setUpShareDocumentsWithLp(param: SetUpShareDocumentsWithLpParam): SetUpShareDocumentsWithLpResponse = {
    val task = for {
      _ <- ZIO.logInfo(s"setUpShareDocumentsWithLp for ${param.fundSubLpId}")
      sharedDocumentMap <- param.sharedDocTypes match {
        case customSharedDocumentTypes: CustomSharedDocumentTypes =>
          ZIO.succeed(customSharedDocumentTypes.sharedDocumentsWithLp)
        case autoShareImportFromFundData: AutoShareImportFromFundData =>
          for {
            documentInfos <- fundSubFundDataIntegrationService.getInvestmentEntityDocumentsUnsafe(
              investmentEntityId = autoShareImportFromFundData.investmentEntityId,
              actor = param.actor
            )
            documentTypes <- fundSubFundDataIntegrationService.getFirmDocumentTypesUnsafe(
              autoShareImportFromFundData.investmentEntityId.parent.parent
            )
            sharedDocTypesMap = FundSubFundDataIntegrationUtils
              .getMatchedSupportingDocWithInvestmentEntityDocuments(
                supportingDocs = autoShareImportFromFundData.visibleSupportingDocs.map(doc =>
                  funddataintegration.SupportingDocInfo(doc.name, doc.description)
                ),
                investmentEntityDocuments = documentInfos,
                documentTypes = documentTypes,
                instNow = Instant.now
              )
            sharedDocumentsPerDocTypeMap = sharedDocTypesMap.flatMap { case (docType, documentIds) =>
              val fileIds = documentInfos.filter(doc => documentIds.contains(doc.documentId)).map(_.fileId)
              Option.when(fileIds.nonEmpty)(docType -> FileList(fileIds))
            }
          } yield sharedDocumentsPerDocTypeMap
        case _ => ZIO.succeed(Map.empty[String, FileList])
      }
      _ <- ZIO.foreach(sharedDocumentMap.toSeq) { case (docType, fileList) =>
        for {
          sharedFileIds <- newSupportingDocService.uploadSupportingDocFiles(
            params = UploadSupportingDocFilesParams(
              lpId = param.fundSubLpId,
              fileIds = fileList.fileIds,
              docType = docType
            ),
            actor = param.actor,
            ctx = None
          )
          _ <- newSupportingDocService.supportingDocReviewService.attemptToSubmitForReviewWhenUploadDoc(
            lpId = param.fundSubLpId,
            docType = docType,
            actor = param.actor
          )
          _ <- newSupportingDocService.markSubmittedFilesAsFundShared(
            lpId = param.fundSubLpId,
            fileIds = sharedFileIds,
            actorOpt = Some(param.actor)
          )
        } yield ()
      }
    } yield SetUpShareDocumentsWithLpResponse(sharedDocumentMap)

    temporalWorkflowService.executeTask(task, "setUpShareDocumentsWithLp")
  }

  override def setUpContact(param: SetUpContactParam): ResponseUnit = {
    val task = for {
      _ <- ZIO.logInfo(s"setUpContact for ${param.fundSubLpId}")
      _ <- fundSubLpInvitationService.setUpContact(
        param.fundSubLpId,
        param.actor,
        param.invitationType
      )
    } yield ResponseUnit()

    temporalWorkflowService.executeTask(task, "setUpContact")
  }

  override def compensateSetUpContact(
    param: SetUpContactCompensateParam
  ): ResponseUnit = {
    scribe.info(s"compensateSetUpContact for ${param.fundSubLpId}")
    // TODO: Cleanup contact data
    ResponseUnit.defaultInstance
  }

  override def setUpImportFromFundData(param: SetUpImportFromFundDataParam): ResponseUnit = {
    val task = for {
      _ <- ZIO.logInfo(s"setUpImportFromFundData for ${param.fundSubLpId}")
      _ <- fundDataIntegrationService.linkSubscriptionToInvestmentEntity(
        param.investmentEntityId,
        param.fundSubLpId,
        param.actor
      )
    } yield ResponseUnit()
    temporalWorkflowService.executeTask(task, "setUpImportFromFundData")
  }

  override def compensateSetUpImportFromFundData(param: SetUpImportFromFundDataCompensateParam): ResponseUnit = {
    val task = for {
      _ <- ZIO.logInfo(s"compensateSetUpFromFundData for ${param.fundSubLpId}")
      _ <- fundDataIntegrationService.unlinkSubscriptionToInvestmentEntity(
        param.investmentEntityId,
        param.fundSubLpId,
        param.actor
      )
    } yield ResponseUnit()
    temporalWorkflowService.executeTask(task, "compensateSetUpFromFundData")
  }

  override def sendEmail(param: SendEmailParam): SendEmailResponse = {
    val task = for {
      _ <- ZIO.logInfo(s"sendEmail for ${param.fundSubLpId}")
      result <- fundSubLpInvitationService.sendEmailForAddingLp(
        lpId = param.fundSubLpId,
        actor = param.actor,
        invitationType = param.invitationType,
        param.lpEmailTemplate,
        newFirmName = param.firmName,
        skipInvitationEmailContacts = param.skipInvitationEmailContacts.toList,
        enableSSOContacts = param.enableSsoContacts.toList
      )
    } yield SendEmailResponse(
      sendEmailResult = result
    )

    temporalWorkflowService.executeTask(task, "sendEmail")
  }

  override def createFundSubActivityLog(param: CreateFundSubActivityLogParam): ResponseUnit = {
    val task = for {
      _ <- ZIO.logInfo(s"createFundSubActivityLog for ${param.fundSubLpId}")
      _ <- fundSubLpInvitationService.createFundSubActivityLog(
        param.fundSubLpId,
        param.lpUserId,
        param.collaboratorUserIds,
        param.invitationType,
        param.actor,
        advisorGroupIdOpt = param.advisorGroupIdOpt,
        originalLpIdOpt = None,
        skipFundAdminActivityLog = true,
        investmentEntity = param.investmentEntity,
        ipAddress = Option(param.actorIpAddress),
        sendEmailResult = param.sendEmailResult.map(_.sendEmailResult),
        skipFundSubAuditLog = param.sendEmailResult.isEmpty
      )
    } yield ResponseUnit()

    temporalWorkflowService.executeTask(task, "createFundSubActivityLog")
  }

  override def compensateCreateFundSubActivityLog(
    param: CreateFundSubActivityLogCompensateParam
  ): ResponseUnit = {
    scribe.info(s"compensateCreateFundSubActivityLog for ${param.fundSubLpId}")
    // TODO: Cleanup fund sub activity logs
    ResponseUnit()
  }

  override def sendAmplitudeAndZapier(param: SendAmplitudeAndZapierParam): ResponseUnit = {
    val task = for {
      _ <- ZIO.logInfo(s"sendAmplitudeAndZapier for ${param.fundSubLpId}")
      _ <- fundSubLpInvitationService.sendAmplitudeAndZapier(
        param.fundSubLpId,
        param.collaboratorUserIds,
        param.actor,
        param.firmName,
        param.invitationType,
        FundSubInvitationHelper.convertContact(param.lpContact.getOrElse(UserContact())),
        param.collaboratorContacts.map(FundSubInvitationHelper.convertContact),
        param.prefilledFromPastLp,
        param.fromDataImport,
        param.formDataSource,
        None
      )
    } yield ResponseUnit()

    temporalWorkflowService.executeTask(task, "sendAmplitudeAndZapier")
  }

  override def completeSingleInvitation(param: CompleteSingleInvitationParam): CompleteSingleInvitationResponse = {
    val task = for {
      _ <- ZIO.logInfo(s"completeSingleInvitation ${param.batchInvitationItemId}")
      invitationItem <- fundSubLpInvitationService.completeSingleInvitation(
        param.batchInvitationItemId,
        param.fundSubLpId,
        param.syncGatewayUpdate
      )(batchActionService, natsNotificationService)
      _ <- fundSubLpService.updateFormProgress(param.fundSubLpId, param.actorId)
      _ <- FundSubGroupTrackingUtils.updateInvestorGroupTracking(param.fundSubLpId.parent, natsNotificationService)
    } yield CompleteSingleInvitationResponse(invitationItem.status)

    temporalWorkflowService.executeTask(task, "completeSingleInvitation")
  }

  override def markSingleInvitationFailed(
    param: MarkSingleInvitationFailedParam
  ): MarkSingleInvitationFailedResponse = {
    val task = for {
      _ <- ZIO.logInfo(s"markSingleInvitationFailed ${param.batchInvitationItemId}, error ${param.error}")
      invitationItem <- fundSubLpInvitationService.markSingleInvitationFailed(
        param.batchInvitationItemId,
        param.error,
        param.syncGatewayUpdate
      )(batchActionService, natsNotificationService)
    } yield MarkSingleInvitationFailedResponse(invitationItem.status)
    temporalWorkflowService.executeTask(task, "markSingleInvitationFailed")
  }

  override def createBatchInviteInvestorActivityLog(param: CreateBatchInviteInvestorActivityLogParam): ResponseUnit = {
    val task = for {
      _ <- ZIO.logInfo(s"createBatchInviteInvestorActivityLog ${param.batchInvitationId}")
      fundSubId <- ZIO.getOrFail {
        param.batchInvitationId.parent.parent match {
          case id: FundSubId => Some(id)
          case _             => None
        }
      }
      invitationInfo <- fundSubParticipantService.getBatchInviteInvestorInfoUnsafe(param.batchInvitationId)
      _ <- invitationInfo.lpOrderType match {
        case LpOrderType.NormalOrder =>
          FundSubAdminActivityLogUtils.addActivity(
            fundSubId = fundSubId,
            actor = invitationInfo.actor,
            fundAdminActivity = BatchLpInvited(
              batchInvitationId = param.batchInvitationId,
              successLp = invitationInfo.successCount,
              failedLp = invitationInfo.failedCount,
              cancelledLp = invitationInfo.cancelledCount
            )
          )
        case LpOrderType.OfflineOrder =>
          FundSubAdminActivityLogUtils.addActivity(
            fundSubId = fundSubId,
            actor = invitationInfo.actor,
            fundAdminActivity = BatchOfflineOrderAdded(
              batchInvitationId = param.batchInvitationId,
              successLp = invitationInfo.successCount,
              failedLp = invitationInfo.failedCount,
              cancelledLp = invitationInfo.cancelledCount
            )
          )
        case _: LpOrderType.Unrecognized =>
          ZIO.unit
      }
    } yield ResponseUnit()
    temporalWorkflowService.executeTask(task, "createBatchInviteInvestorActivityLog")
  }

  override def sendBatchEmailAndCreateBatchFundSubAuditLog(param: SendBatchEmailAndCreateBatchFundSubAuditLogParam)
    : ResponseUnit = {
    val task = for {
      _ <- ZIO.logInfo(s"sendBatchEmailAndCreateBatchFundSubAuditLog ${param.batchInvitationId}")
      batchModel <- FDBRecordDatabase.transact(FundSubBatchInvitationOperations.Production) {
        _.get(param.batchInvitationId)
      }
      _ <- ZIO.when(batchModel.importFromFundDataFirm.nonEmpty) {
        for {
          itemModels <- FDBRecordDatabase.transact(FundSubBatchInvitationOperations.Production) {
            _.getItems(param.batchInvitationId)
          }
          lpIdInvestorIdPairs = itemModels.flatMap { singleInvitation =>
            val singleResp = param.singleInvitationResponses.find(
              _.batchInvitationItemId == singleInvitation.batchInvitationItemId
            )
            for {
              investorId <- singleInvitation.importFromFundData.map(_.investmentEntityId.parent)
              lpId <- singleResp.flatMap(_.lpIdOpt)
            } yield lpId -> investorId
          }
          lpIdGroups = lpIdInvestorIdPairs.groupMap(_._2)(_._1).values.toSeq
          lpAndEmailIdMap <- fundSubLpInvitationService.sendEmailForAddingMultipleLps(
            lpIdsBatches = lpIdGroups,
            actor = batchModel.actor,
            invitationType = if (batchModel.orderType.isNormalOrder) {
              LpInvitationType.Normal
            } else {
              LpInvitationType.OfflineOrder
            },
            inviteLpCustomEmailTemplate = batchModel.importFromFundDataFirm.flatMap(_.lpEmailTemplate),
            inviteCollaboratorCustomEmailTemplate =
              batchModel.importFromFundDataFirm.flatMap(_.collaboratorEmailTemplate),
            skipInvitationEmailsMap = param.singleInvitationResponses.flatMap { singleResp =>
              singleResp.lpIdOpt.map { _ -> singleResp.skipInvitationEmails }
            }.toMap
          )

          // add fund sub audit log
          _ <- ZIO.foreachDiscard(lpAndEmailIdMap.toSeq) { case (lpId, emailParams) =>
            val groupedEmailParams = emailParams.groupBy(_.fundSubEventType)
            val emailParamsByType = groupedEmailParams.map { case (eventType, params) =>
              FundSubAuditLogService.AddEventEmailParam(eventType, params.flatMap(_.emailIds))
            }.toSeq
            fundSubAuditLogService.addEvent(
              fundSubId = lpId.parent,
              params = AddEventParam(
                actor = Some(batchModel.actor),
                orderId = Option(lpId),
                actorType = AuditLogActorType.FundSide,
                eventType = if (batchModel.orderType.isNormalOrder) {
                  AuditLogEventType.INVESTOR_INVITED
                } else {
                  AuditLogEventType.OFFLINE_SUBSCRIPTION_ADDED
                },
                eventEmail = emailParamsByType,
                activityDetail = LpInvited(isOfflineOrder = batchModel.orderType.isOfflineOrder),
                ipAddress = param.actorIpAddress
              )
            )
          }
        } yield ()
      }
    } yield ResponseUnit()
    temporalWorkflowService.executeTask(task, "sendBatchEmailAndCreateBatchFundSubAuditLog")
  }

  override def updateSupportingDocsIfNecessary(params: UpdateSupportingDocsParams): ResponseUnit = {
    val task = fundSubLpInvitationService
      .updateSupportingDocsIfNecessary(params.lpId, params.actorId)(natsNotificationService)
      .as(ResponseUnit())
    temporalWorkflowService.executeTask(task, "updateSupportingDocsIfNecessary")
  }

  override def completeBatchInvitation(param: MultipleInvitationParam): ResponseUnit = {
    val task = for {
      batchActionId <- FDBRecordDatabase.transact(FundSubBatchInvitationOperations.Production) { ops =>
        ops.get(param.batchInvitationId).map(_.batchActionId)
      }
      _ <- batchActionService.updatePostExecuteActionStatus(
        batchActionId,
        _ => BatchActionItemStatusSucceeded(Some(Instant.now))
      )
    } yield ResponseUnit()
    temporalWorkflowService.executeTask(task, "postStartMultipleInvitation")
  }

}
