// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.dataextract.service

import java.time.{Duration, Instant}

import fundsub.webhook.{DataExtractionStatus, DataExtractionStatusChangedPayload, WebhookPayload}
import io.circe.Json
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.cue.storage.table.{CueTableDataStoreOperations, CueTableMetadataStoreOperations}
import anduin.cue.table.metadata.CueTableMetadataModel
import anduin.dataextract.integration.DataExtractIntegrationService
import anduin.dms.DmsFeature
import anduin.dms.service.FileService
import anduin.fdb.record.{DefaultCluster, FDBOperations, FDBRecordDatabase}
import anduin.forms.endpoint.GetLockResult
import anduin.forms.engine.GaiaState
import anduin.forms.service.{AssetLockService, FormService}
import anduin.forms.utils.FormDataUtils
import anduin.fundsub.auditlog.FundSubAuditLogService.AddEventParam
import anduin.fundsub.auditlog.{AuditLogActorType, AuditLogEventType, AuditLogFilter, FundSubAuditLogService}
import anduin.fundsub.dataextract.*
import anduin.fundsub.dataextract.FundSubDataExtractLogType.*
import anduin.fundsub.dataextract.FundSubDataExtractSchema.{
  DataExtractFormProgress,
  FundSubDataExtractRequest,
  FundSubDataExtractRequestStatus
}
import anduin.fundsub.dataextract.GetDataExtractRequestFormInfoResp.ViewOnlyModeData
import anduin.fundsub.dataextract.database.{
  FundSubDataExtractRequestStoreOperations,
  FundSubDataExtractTestProfileStoreOperations
}
import anduin.fundsub.dataextract.protocols.{PrepareDummyFormDataInput, PrepareDummyFormDataOutput}
import anduin.fundsub.dataextract.request.{
  FundSubDataExtractRequestApproved,
  FundSubDataExtractRequestInProgress,
  FundSubDataExtractRequestModel,
  FundSubDataExtractRequestReadyForReview
}
import anduin.fundsub.datalakeingestion.FundSubDataLakeIngestionService
import anduin.fundsub.datalakeingestion.model.{
  DataExtractRequestInfoForDataLakeIngestion,
  UpdateOrAddSubdocDataExtractRequestParams
}
import anduin.fundsub.form.utils.FundSubCommonUtils
import anduin.fundsub.models.{FundSubLpModelStoreOperations, FundSubModelStoreOperations}
import anduin.fundsub.service.FundSubFormIntegrationService.GaiaFormInfo
import anduin.fundsub.service.{
  FundSubLpActivityLogService,
  FundSubLpDashboardService,
  FundSubPermissionService,
  LPDataOperations
}
import anduin.fundsub.user.FundSubUserTrackingService
import anduin.fundsub.utils.{FundSubDataLakeUtils, FundSubFileOperations}
import anduin.fundsub.webhook.WebhookKafkaTopic
import anduin.id.cue.CueModuleVersionId
import anduin.id.dataextract.{DataExtractProjectId, DataExtractProjectItemId}
import anduin.id.form.FormVersionId
import anduin.id.fundsub.dataextract.{FundSubDataExtractRequestId, FundSubDataExtractTestProfileId}
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.kafka.{KafkaService, KafkaSimpleProducer}
import anduin.model.common.user.UserId
import anduin.model.id.{CueTableIdFactory, FileId, FundSubDataExtractRequestIdFactory}
import anduin.protobuf.activitylog.fundsub.admin.{
  DataExtractRequestInfo,
  DataExtractionStarted,
  ExtractedDataEdited,
  ExtractedDataMarkedAsComplete
}
import anduin.protobuf.fundsub.activitylog.lp.DataExtractResultImportedToForm
import anduin.protobuf.fundsub.models.FundSubPublicModel
import anduin.rebac.RebacStoreOperation
import anduin.service.GeneralServiceException
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.fundsub.dataextract.FundSubPrepareDummyDataForDataExtractWorkflow
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.util.date.DateCalculator

final case class FundSubSubdocDataExtractService(
  assetLockService: AssetLockService,
  dataExtractIntegrationService: DataExtractIntegrationService,
  fundSubPermissionService: FundSubPermissionService,
  fundSubDataLakeIngestionService: FundSubDataLakeIngestionService,
  fundSubLpDashboardService: FundSubLpDashboardService,
  formService: FormService,
  fundSubAuditLogService: FundSubAuditLogService,
  userProfileService: UserProfileService,
  fundSubLpActivityLogService: FundSubLpActivityLogService,
  kafkaService: KafkaService,
  userTrackingService: FundSubUserTrackingService,
  fileService: FileService,
  fundSubDataExtractLogService: FundSubDataExtractLogService,
  temporalWorkflowService: TemporalWorkflowService
) extends FundSubFileOperations {

  private val webhookEventProducer = KafkaSimpleProducer[FundSubId, WebhookPayload](
    kafkaService = kafkaService,
    topic = WebhookKafkaTopic.instance
  )

  def createSubscriptionDocDataExtractRequestUnsafe(
    lpId: FundSubLpId,
    sharedFiles: Seq[FileId],
    actorId: UserId
  ): Task[Unit] = {
    val fundSubId = lpId.parent
    for {
      _ <- validateAllFilesInLpDocFolder(lpId, sharedFiles)
      fundSubModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(fundSubId)
      )
      dataExtractionConfigOpt = fundSubModel.dataExtractionConfig
      dataExtractProjectId <- ZIOUtils.fromOption(
        dataExtractionConfigOpt.filter(_.isEnabled).flatMap(_.dataExtractProjectId),
        GeneralServiceException(s"Data extraction project is not found for fund $fundSubId")
      )
      lastFormVersionOfLpOpt <- FDBRecordDatabase
        .transact(LPDataOperations.Production)(_.getLastFormIdOpt(lpId))
        .map(_.flatMap(_.asNewLpFormId.map(_.parent)))
      fundSubMainFormIdOpt <- FundSubCommonUtils.getFundSubMainFormId(fundSubId).map(_.toOption)
      initialFormVersionId <- ZIOUtils.fromOption(
        lastFormVersionOfLpOpt.orElse(fundSubMainFormIdOpt),
        GeneralServiceException(s"Cannot find initial form version id for lp $lpId")
      )
      request <- createNewDataExtractProjectItem(
        lpId,
        dataExtractProjectId,
        actorId,
        sharedFiles,
        initialFormVersionId = initialFormVersionId,
        fundSubModel = fundSubModel
      )
      lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(_.getFundSubLpModel(lpId))
      _ <- fundSubAuditLogService.addEvent(
        lpId.parent,
        AddEventParam(
          actor = None,
          actorType = AuditLogActorType.System,
          orderId = Some(lpId),
          eventType = AuditLogEventType.DATA_EXTRACTION_STARTED,
          eventEmail = Seq.empty,
          activityDetail = DataExtractionStarted(
            requestInfo = DataExtractRequestInfo(
              dataExtractRequestId = request.id,
              lpId = lpId,
              lpUserId = lpModel.mainLp
            ),
            submittedFiles = sharedFiles
          )
        )
      )
      _ <- webhookEventProducer.send(
        WebhookKafkaTopic.instance.message(
          fundSubId,
          DataExtractionStatusChangedPayload(
            lpIdOpt = Some(lpId),
            dataExtractionStatus = DataExtractionStatus.STARTED,
            createdAt = Some(Instant.now)
          )
        )
      )
    } yield ()
  }

  private def getTestProfileOpt(
    fundSubId: FundSubId,
    fileNames: List[String]
  ) =
    for {
      testProfiles <- ZIOUtils
        .foreachParN(4)(fileNames) { fileName =>
          FDBRecordDatabase.transact(FundSubDataExtractTestProfileStoreOperations.Production)(
            _.getByFileName(fundSubId = fundSubId, fileName = fileName)
          )
        }
        .map(_.flatten)
      testProfileDataOpt = testProfiles.find(_.extractedFormData.nonEmpty).flatMap { testProfile =>
        testProfile.extractedFormData.map(
          testProfile.id -> FundSubDataExtractRequestConverterUtils.fromProtoToExtractedFormData(_)
        )
      }
    } yield testProfileDataOpt

  private def triggerTestProfile(
    request: FundSubDataExtractRequest,
    testProfileId: FundSubDataExtractTestProfileId,
    testExtractedFormData: FundSubDataExtractSchema.FundSubExtractedFormData,
    actorId: UserId
  ) = {
    for {
      _ <- ZIO.logInfo(s"Triggering test profile ${testProfileId.idString} for lp ${request.lpId.idString}")
      _ <- markRequestAsReadyForReviewUnsafe(
        request = request,
        newExtractedData = Left(
          (
            newFormVersionId = testExtractedFormData.formVersionId,
            newGaiaState = testExtractedFormData.gaiaState
          )
        ),
        actorId = actorId
      )
    } yield ()
  }

  private def addDummyDataForTesting(
    request: FundSubDataExtractRequest,
    initialFormVersionId: FormVersionId,
    actor: UserId
  ): Task[Unit] = {
    temporalWorkflowService.runAsync[
      PrepareDummyFormDataInput,
      PrepareDummyFormDataOutput,
      FundSubPrepareDummyDataForDataExtractWorkflow
    ](
      PrepareDummyFormDataInput(
        lpId = request.lpId,
        actor = actor,
        formVersionId = initialFormVersionId
      )
    )
  }.unit

  def saveFormDataOfDataExtract(
    requestId: FundSubDataExtractRequestId,
    gaiaState: GaiaState,
    actorId: UserId
  ): Task[SaveFormDataOfDataExtractResp] = {
    for {
      request <- getRequest(requestId)
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(request.lpId.parent)
      _ <- fundSubPermissionService.validateFundManagerCanAccessLpSubscriptionDocsR(request.lpId, actorId)
      isValidLock <- assetLockService.validateLock(requestId, actorId)
      updatedRequestOpt <-
        if (isValidLock) {
          saveRequestFormDataUnsafe(
            requestId = requestId,
            newGaiaState = gaiaState,
            actorId = actorId
          ).map(Some(_))
        } else {
          ZIO.succeed(None)
        }
    } yield SaveFormDataOfDataExtractResp(
      formFillingPercentage = updatedRequestOpt.flatMap(_.formProgressOpt).fold(0.0f)(_.formFillingProgress),
      formDataLastEditedAtOpt = updatedRequestOpt.flatMap(_.extractedDataLastUpdatedAt),
      isLocked = updatedRequestOpt.isEmpty
    )
  }

  def markRequestAsReadyForReviewUnsafe(
    request: FundSubDataExtractRequest,
    newExtractedData: Either[
      (newFormVersionId: FormVersionId, newGaiaState: GaiaState),
      (newCueModuleVersionId: CueModuleVersionId, newCueTableData: Json)
    ],
    actorId: UserId
  ): Task[FundSubDataExtractRequest] = {
    val requestId = request.id
    val lpId = request.lpId
    for {
      _ <- ZIO.logInfo(s"User $actorId marks data extract request of lp $lpId as ready for review")
      _ <- ZIOUtils.validate(request.status == FundSubDataExtractRequestStatus.InProgress)(
        GeneralServiceException(s"Status of request ${request.id.idString} is not InProgress.")
      )
      _ <- newExtractedData match {
        case Left((newFormVersionId, newGaiaState)) =>
          for {
            _ <- DataExtractRequestFormDataUtils.updateRequestFormVersion(request.id, newFormVersionId)
            _ <- updateRequestFormData(
              requestId = requestId,
              currentGaiaState = GaiaState.empty,
              formVersionId = newFormVersionId,
              gaiaState = newGaiaState,
              actorId = actorId,
              shouldAddAuditLogEvent = false,
              formChangeEventType = FundSubDataExtractLogType.OCR_DATA_SUBMISSION
            )
          } yield ()
        case Right((newCueModuleVersionId, newCueTableData)) =>
          updateRequestCueTableData(
            requestId = requestId,
            cueModuleVersionId = newCueModuleVersionId,
            cueTableData = newCueTableData,
            actorId = actorId,
            shouldAddAuditLogEvent = false
          )
      }
      _ <- markRequestAsReadyForReviewInternal(requestId)
      updatedRequest <- getRequest(requestId)
      _ <- updateDataExtractModelInDataLake(
        lpId = lpId,
        newRequestOpt = Some(updatedRequest)
      )
      _ <- fundSubLpDashboardService.updateLpInfoRecord(
        lpId,
        identity
      )
    } yield updatedRequest
  }

  private def saveRequestFormDataUnsafe(
    requestId: FundSubDataExtractRequestId,
    newGaiaState: GaiaState,
    actorId: UserId
  ): Task[FundSubDataExtractRequest] = {
    val lpId = requestId.parent
    for {
      _ <- ZIO.logInfo(s"User $actorId saves form data of data extract request of lp $lpId")
      formDataOpt <- DataExtractRequestFormDataUtils.getExtractedFormDataOpt(requestId)
      formData <- ZIOUtils.fromOption(
        formDataOpt,
        GeneralServiceException(s"Request ${requestId.idString} does not have extracted form data")
      )
      _ <- ZIO.when(formFieldValuesHasChanged(formData.gaiaState, newGaiaState)) {
        updateRequestFormData(
          requestId = requestId,
          currentGaiaState = formData.gaiaState,
          formVersionId = formData.formVersionId,
          gaiaState = newGaiaState,
          actorId = actorId,
          shouldAddAuditLogEvent = true,
          formChangeEventType = USER_MODIFICATION
        )
      }
      updatedRequest <- getRequest(requestId)
      _ <- updateDataExtractModelInDataLake(
        lpId = lpId,
        newRequestOpt = Some(updatedRequest)
      )
      _ <- fundSubLpDashboardService.updateLpInfoRecord(
        lpId,
        identity
      )
    } yield updatedRequest
  }

  def updateRequestFormVersionUnsafe(
    request: FundSubDataExtractRequest,
    newFormVersionId: FormVersionId,
    newGaiaState: GaiaState,
    actorId: UserId
  ): Task[FundSubDataExtractRequest] = {
    val requestId = request.id
    val lpId = request.lpId
    for {
      _ <- ZIO.logInfo(s"User $actorId update form version for data extract request of lp $lpId")
      _ <- ZIOUtils.validate(request.status == FundSubDataExtractRequestStatus.ReadyForReview)(
        GeneralServiceException(s"Status of request ${request.id.idString} is not ReadyForReview.")
      )
      formDataOpt <- DataExtractRequestFormDataUtils.getExtractedFormDataOpt(requestId)
      formData <- ZIOUtils.fromOption(
        formDataOpt,
        GeneralServiceException(s"Request ${request.id.idString} does not have extracted form data")
      )
      _ <- DataExtractRequestFormDataUtils.updateRequestFormVersion(request.id, newFormVersionId)
      _ <- updateRequestFormData(
        requestId = requestId,
        currentGaiaState = formData.gaiaState,
        formVersionId = newFormVersionId,
        gaiaState = newGaiaState,
        actorId = actorId,
        shouldAddAuditLogEvent = true,
        formChangeEventType = FundSubDataExtractLogType.FORM_UPDATE
      )
      updatedRequest <- getRequest(requestId)
      _ <- updateDataExtractModelInDataLake(
        lpId = lpId,
        newRequestOpt = Some(updatedRequest)
      )
      _ <- fundSubLpDashboardService.updateLpInfoRecord(
        lpId,
        identity
      )
    } yield updatedRequest
  }

  private def formFieldValuesHasChanged(oldFormData: GaiaState, newFormData: GaiaState): Boolean =
    oldFormData.defaultStateMap.view.mapValues(_.getValue).toMap != newFormData.defaultStateMap.view
      .mapValues(_.getValue)
      .toMap

  private def updateRequestFormData(
    requestId: FundSubDataExtractRequestId,
    currentGaiaState: GaiaState,
    formVersionId: FormVersionId,
    gaiaState: GaiaState,
    actorId: UserId,
    shouldAddAuditLogEvent: Boolean,
    formChangeEventType: FundSubDataExtractLogType
  ) = {
    val fundSubLpId = requestId.parent
    for {
      formData <- formService.getFormDataUnsafe(formVersionId)
      gaiaFormInfo = GaiaFormInfo(formData, gaiaState)
      dataExtractFormProgress = DataExtractFormProgress(
        gaiaFormInfo.missingRequiredFields,
        gaiaFormInfo.missingRecommendedFields,
        FormDataUtils.calculateProgress(formData.form, gaiaState.defaultStateMap)
      )
      _ <- DataExtractRequestFormDataUtils.importFormDataIntoDataExtractRequest(
        requestId,
        gaiaState,
        dataExtractFormProgress
      )
      _ <- ZIO.when(shouldAddAuditLogEvent) {
        addAuditLogForEditingExtractedData(
          requestId = requestId,
          actorId = actorId
        )
      }
      shouldLogFormChanges <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops
          .getFundSubPublicModel(fundSubLpId.parent)
          .map(_.featureSwitch)
          .map(_.exists(_.enableDataExtractDraftFormLog))
      }
      _ <- ZIO.when(shouldLogFormChanges) {
        fundSubDataExtractLogService.logFormChanges(
          requestId,
          formVersionId,
          currentGaiaState,
          gaiaState,
          formChangeEventType,
          actorId
        )
      }
    } yield ()
  }

  private def updateRequestCueTableData(
    requestId: FundSubDataExtractRequestId,
    cueModuleVersionId: CueModuleVersionId,
    cueTableData: Json,
    actorId: UserId,
    shouldAddAuditLogEvent: Boolean
  ): Task[Unit] = {
    for {
      _ <- FDBRecordDatabase.transact(
        FDBOperations[
          (FundSubDataExtractRequestStoreOperations, CueTableDataStoreOperations, CueTableMetadataStoreOperations)
        ].Production
      ) { case (requestOps, cueTableDataOps, cueTableMetadataOps) =>
        for {
          cueTableIdOpt <- requestOps.getOpt(requestId).map(_.flatMap(_.extractedCueTableMetadataOpt.map(_.cueTableId)))
          cueTableId = cueTableIdOpt.getOrElse(CueTableIdFactory.unsafeRandomId)
          _ <- cueTableMetadataOps.upsert(cueTableId, cueModuleVersionId)
          _ <- cueTableDataOps.upsert(cueTableId, cueTableData)
          _ <- requestOps.update(requestId)(
            _.copy(
              extractedCueTableMetadataOpt = Some(
                CueTableMetadataModel(
                  cueTableId = cueTableId,
                  cueModuleVersionId = cueModuleVersionId
                )
              )
            )
          )
        } yield ()
      }
      _ <- ZIO.when(shouldAddAuditLogEvent) {
        addAuditLogForEditingExtractedData(
          requestId = requestId,
          actorId = actorId
        )
      }
    } yield ()
  }

  private def addAuditLogForEditingExtractedData(
    requestId: FundSubDataExtractRequestId,
    actorId: UserId
  ): Task[Unit] = {
    val lpId = requestId.parent
    val fundSubId = lpId.parent
    val ThirtyMinuteDuration = Duration.ofMinutes(30)
    val now = Instant.now
    for {
      // We only record new ExtractedDataEdited if there are no ExtractedDataEdited events from the same person in the last 30 mins
      lastAuditLogEventByActorOpt <- fundSubAuditLogService
        .queryAuditLogEvents(
          fundSubId,
          filters = Seq(AuditLogFilter.ActorFilter(actorId)),
          timestampFilterOpt = Some(AuditLogFilter.TimestampFilter(now.minus(ThirtyMinuteDuration), now)),
          limitOpt = Some(1)
        )
        .map(_._1.headOption)
      lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(_.getFundSubLpModel(lpId))
      lpUserId = lpModel.mainLp
      _ <- lastAuditLogEventByActorOpt.fold[Task[Unit]](
        logEditExtractedData(requestId, lpId, lpUserId, actorId).unit
      ) { lastEvent =>
        val shouldNotCreateNewEvent = lastEvent.activityDetail.exists(_.value.faActivity.exists {
          case extractedDataEdited: ExtractedDataEdited =>
            extractedDataEdited.requestInfo.dataExtractRequestId == requestId && lastEvent.actor.contains(actorId)
          case _ => false
        })
        if (shouldNotCreateNewEvent) {
          ZIO.unit
        } else {
          logEditExtractedData(requestId, lpId, lpUserId, actorId).unit
        }
      }
    } yield ()
  }

  private def logEditExtractedData(
    requestId: FundSubDataExtractRequestId,
    lpId: FundSubLpId,
    lpUserId: UserId,
    actorId: UserId
  ) = {
    fundSubAuditLogService.addEvent(
      lpId.parent,
      AddEventParam(
        actor = Some(actorId),
        actorType = AuditLogActorType.FundSide,
        eventType = AuditLogEventType.DATA_EXTRACTION_EXTRACTED_DATA_EDITED,
        orderId = Some(lpId),
        eventEmail = Seq.empty,
        activityDetail = ExtractedDataEdited(
          requestInfo = DataExtractRequestInfo(
            lpId = lpId,
            lpUserId = lpUserId,
            dataExtractRequestId = requestId
          )
        )
      )
    )
  }

  private def updateDataExtractModelInDataLake(
    lpId: FundSubLpId,
    newRequestOpt: Option[FundSubDataExtractRequest],
    requestToRemoveOpt: Option[FundSubDataExtractRequestId] = None
  ) = {
    FundSubDataLakeUtils.sendUpdateParams(
      lpId.parent,
      UpdateOrAddSubdocDataExtractRequestParams(
        lpIdOpt = Some(lpId),
        newDataExtractRequestInfo = newRequestOpt.map { newRequest =>
          DataExtractRequestInfoForDataLakeIngestion(
            id = newRequest.id,
            status = Some(FundSubDataLakeUtils.dataExtractRequestStatusToDataLakeIngestion(newRequest.status)),
            missingRequiredFields = newRequest.formProgressOpt.map(_.missingRequiredFields),
            missingRecommendedFields = newRequest.formProgressOpt.map(_.missingRecommendedFields)
          )
        },
        requestToRemoveOpt = requestToRemoveOpt
      ),
      fundSubDataLakeIngestionService
    )
  }

  def getDataExtractRequestFormInfo(
    params: GetDataExtractRequestFormInfoParams,
    actorId: UserId
  ): Task[GetDataExtractRequestFormInfoResp] = {
    val lpId = params.requestId.parent
    for {
      request <- getRequest(params.requestId)
      _ <- ZIO.logInfo(s"User $actorId get form info of data extract request for lp $lpId")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(request.lpId.parent)
      _ <- fundSubPermissionService.validateFundManagerCanAccessLpSubscriptionDocsR(request.lpId, actorId)
      extractedDataOpt <- DataExtractRequestFormDataUtils.getExtractedFormDataOpt(params.requestId)
      formInfoOpt <- ZIOUtils.traverseOption(extractedDataOpt) { extractedData =>
        formService
          .getForm(
            extractedData.formVersionId.parent,
            Some(extractedData.formVersionId),
            actorId,
            shouldCheckPermission = false
          )
          .map { getFormResp =>
            DataExtractRequestFormInfo(
              getFormResp.formData,
              extractedData,
              request.extractedDataLastUpdatedAt
            )
          }
      }
      viewOnlyModeDataOpt <-
        if (!params.shouldGetLock) {
          ZIO.succeed(None)
        } else {
          assetLockService.getLock(params.requestId, actorId).map {
            case locked: GetLockResult.AssetLocked =>
              Some(
                ViewOnlyModeData(
                  userName = locked.lockedUserInfo.fullNameString,
                  userEmail = locked.lockedUserInfo.emailAddressStr
                )
              )

            case _ =>
              None
          }
        }
    } yield GetDataExtractRequestFormInfoResp(formInfoOpt, viewOnlyModeDataOpt)
  }

  def getRequest(
    requestId: FundSubDataExtractRequestId
  ): Task[FundSubDataExtractRequest] = {
    for {
      requestModel <- FDBRecordDatabase.read(FundSubDataExtractRequestStoreOperations.Production)(_.get(requestId))
    } yield FundSubDataExtractRequestConverterUtils.fromProtoToDataExtractRequest(requestModel)
  }

  def getAllRequestsByDataExtractProjectItemId(
    projectItemId: DataExtractProjectItemId
  ): Task[List[FundSubDataExtractRequest]] = {
    for {
      requestModels <- FDBRecordDatabase.read(FundSubDataExtractRequestStoreOperations.Production)(
        _.getByDataExtractProjectItemId(projectItemId)
      )
    } yield requestModels.map(FundSubDataExtractRequestConverterUtils.fromProtoToDataExtractRequest)
  }

  def getDataExtractRequest(
    requestId: FundSubDataExtractRequestId,
    actorId: UserId
  ): Task[GetDataExtractRequestResp] = {
    for {
      request <- getRequest(requestId)
      _ <- ZIO.logInfo(s"User $actorId get subscription doc data extract request of lp ${request.lpId}")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(request.lpId.parent)
      _ <- fundSubPermissionService.validateFundManagerCanAccessLpSubscriptionDocsR(request.lpId, actorId)
      userClosedCompleteDataExtractGuidelineCallout <- userTrackingService
        .getUserTracking(actorId)
        .map(_.closedCompleteDataExtractionGuidelineCallout)
    } yield GetDataExtractRequestResp(
      dataExtractRequest = request,
      userClosedCompleteDataExtractGuidelineCallout = userClosedCompleteDataExtractGuidelineCallout
    )
  }

  def getRequestOptOfLpUnsafe(
    lpId: FundSubLpId
  ): Task[Option[FundSubDataExtractRequest]] =
    for {
      requestModelOpt <- FDBRecordDatabase.read(FundSubDataExtractRequestStoreOperations.Production)(
        _.getCurrentRequestOptOfLp(lpId)
      )
    } yield requestModelOpt.map(FundSubDataExtractRequestConverterUtils.fromProtoToDataExtractRequest)

  private def createNewDataExtractProjectItem(
    lpId: FundSubLpId,
    projectId: DataExtractProjectId,
    actorId: UserId,
    submittedDocs: Seq[FileId],
    initialFormVersionId: FormVersionId,
    fundSubModel: FundSubPublicModel
  ): Task[FundSubDataExtractRequest] = {
    for {
      _ <- ZIO.logInfo(s"[Data Extract]: User $actorId is creating new data extract request for lpId $lpId")
      _ <- ZIOUtils.validate(submittedDocs.nonEmpty) {
        GeneralServiceException("Cannot create a data extract request with no PDF")
      }
      lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(_.getFundSubLpModel(lpId))
      lpUserInfo <- userProfileService.getUserInfo(lpModel.mainLp)
      submittedFileNames <- fileService
        .batchGetFileNamesUnsafe(submittedDocs)(
          using DmsFeature.Public
        )
        .map(_.values.toList)
      testProfileDataOpt <- ZIO
        .when(fundSubModel.dataExtractionConfig.exists(_.isTestProfileEnabled)) {
          getTestProfileOpt(lpId.parent, submittedFileNames)
        }
        .map(_.flatten)
      shouldAddDummyData =
        if (fundSubModel.fundType.isProduction) {
          false
        } else {
          submittedFileNames.exists(_.trim.toLowerCase.startsWith("[test_dummy_data]"))
        }
      shouldCreateProjectItem = testProfileDataOpt.isEmpty && !shouldAddDummyData
      projectItemOpt <- ZIO.when(shouldCreateProjectItem) {
        val projectItemName =
          Seq(
            lpModel.firmName,
            lpUserInfo.fullNameString,
            lpUserInfo.emailAddressStr
          ).filterNot(_.isEmpty).mkString(" - ")
        // TODO: @pikachu, @flashmt to create data extract project item with cue module here
        dataExtractIntegrationService.createNewDataExtractProjectItemUnsafe(
          projectId = projectId,
          projectItemName = projectItemName,
          actorId = actorId,
          submittedDocs = submittedDocs,
          initialFormVersionIdOpt = Some(initialFormVersionId)
        )
      }
      requestModel = FundSubDataExtractRequestModel(
        requestId = FundSubDataExtractRequestIdFactory.unsafeRandomId(lpId),
        projectItemIdOpt = projectItemOpt.map(_.itemId),
        submittedDocs = submittedDocs,
        dataExtractFileMap = projectItemOpt.fold(Map.empty[FileId, FileId])(_.fileIdMap),
        status = FundSubDataExtractRequestInProgress(),
        createdBy = actorId,
        createdAt = Instant.now
      )
      _ <- FDBRecordDatabase.transact(FundSubDataExtractRequestStoreOperations.Production)(
        _.create(requestModel)
      )
      request = FundSubDataExtractRequestConverterUtils.fromProtoToDataExtractRequest(requestModel)
      _ <- testProfileDataOpt.fold(
        if (shouldAddDummyData) {
          addDummyDataForTesting(
            request = request,
            initialFormVersionId = initialFormVersionId,
            actor = actorId
          )
        } else {
          updateDataExtractModelInDataLake(
            lpId = lpId,
            newRequestOpt = Some(request)
          )
        }
      ) { case (testProfileId, testExtractedFormData) =>
        triggerTestProfile(
          request = request,
          testProfileId = testProfileId,
          testExtractedFormData = testExtractedFormData,
          actorId = actorId
        )
      }
    } yield request
  }

  def getAllRequestsInFundUnsafe(fundSubId: FundSubId): Task[List[FundSubDataExtractRequest]] = {
    for {
      allRequestMessages <- FDBRecordDatabase.transact(FundSubDataExtractRequestStoreOperations.Production)(
        _.getAllRequestsInFund(fundSubId)
          .map(_.filterNot(_.isDiscarded))
      )
    } yield allRequestMessages.map(FundSubDataExtractRequestConverterUtils.fromProtoToDataExtractRequest)
  }

  def markRequestAsApprovedUnsafe(
    requestId: FundSubDataExtractRequestId,
    actorId: UserId,
    actorIpAddressOpt: Option[String]
  ): Task[Unit] = {
    for {
      updatedRequestModel <- FDBRecordDatabase.transact(FundSubDataExtractRequestStoreOperations.Production)(
        _.update(requestId)(
          _.copy(status = FundSubDataExtractRequestApproved(approvedBy = actorId, approvedAt = Some(Instant.now)))
        )
      )
      lpId = updatedRequestModel.requestId.parent
      lpUserId <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(
        _.getFundSubLpModel(lpId).map(_.mainLp)
      )
      updatedRequest = FundSubDataExtractRequestConverterUtils.fromProtoToDataExtractRequest(updatedRequestModel)
      _ <- updateDataExtractModelInDataLake(
        lpId = lpId,
        newRequestOpt = Some(updatedRequest)
      )
      _ <- fundSubAuditLogService.addEvent(
        lpId.parent,
        AddEventParam(
          actor = Some(actorId),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.DATA_EXTRACTION_MARKED_AS_COMPLETE,
          orderId = Some(lpId),
          eventEmail = Seq.empty,
          ipAddress = actorIpAddressOpt,
          activityDetail = ExtractedDataMarkedAsComplete(
            requestInfo = DataExtractRequestInfo(
              dataExtractRequestId = requestId,
              lpId = lpId,
              lpUserId = lpUserId
            )
          )
        )
      )
      _ <- webhookEventProducer.send(
        WebhookKafkaTopic.instance.message(
          lpId.parent,
          DataExtractionStatusChangedPayload(
            lpIdOpt = Some(lpId),
            dataExtractionStatus = DataExtractionStatus.COMPLETED,
            createdAt = Some(Instant.now)
          )
        )
      )
      _ <- fundSubLpActivityLogService.logActivity(
        lpId = lpId,
        actorOpt = Option(actorId),
        at = Option(DateCalculator.instantNow),
        detail = DataExtractResultImportedToForm(requestId)
      )
    } yield ()
  }

  def getDataExtractRequestsReadyForReview(
    fromTimestamp: Instant,
    toTimestamp: Instant
  ): Task[Seq[FundSubDataExtractRequestModel]] = {
    for {
      requestModels <- FDBRecordDatabase.transact(FundSubDataExtractRequestStoreOperations.Production)(
        _.getDataExtractRequestsReadyForReview(fromTimestamp, toTimestamp)
      )
    } yield requestModels.filter { requestModel =>
      requestModel.status match {
        case _: FundSubDataExtractRequestReadyForReview => true
        case _                                          => false
      }
    }
  }

  def getLpDataExtractionState(
    lpId: FundSubLpId,
    actorId: UserId
  ): Task[GetLpDataExtractionStateResp] = {
    for {
      _ <- ZIO.logInfo(s"User $actorId gets LP $lpId data extraction state")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService.validateFundManagerCanAccessLpSubscriptionDocsR(lpId, actorId)
      requestOfLpOpt <- getRequestOptOfLpUnsafe(lpId)
      dataExtractionConfigOpt <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(lpId.parent).map(_.dataExtractionConfig)
      )
    } yield GetLpDataExtractionStateResp(
      currentRequestOpt = requestOfLpOpt,
      isLiveDataExtractionEnabled = dataExtractionConfigOpt.exists { config =>
        config.isEnabled && config.dataExtractProjectId.isDefined && config.isLiveExtractionEnabled
      }
    )
  }

  def discardIncompleteRequestOfLp(
    lpId: FundSubLpId
  ): Task[Unit] = {
    for {
      discardedRequestOpt <- FDBRecordDatabase.transact(FundSubDataExtractRequestStoreOperations.Production)(
        _.discardExistingRequestOfLp(lpId, shouldKeepCompletedRequest = true)
      )
      _ <- ZIO.foreachDiscard(discardedRequestOpt) { discardedRequest =>
        ZIO.logInfo(
          s"Discarded incomplete data extract request ${discardedRequest.requestId} of lp $lpId"
        ) *> updateDataExtractModelInDataLake(
          lpId = lpId,
          newRequestOpt = None,
          requestToRemoveOpt = Option.when(discardedRequest.isDiscarded)(discardedRequest.requestId)
        )
      }
    } yield ()

  }

  private def markRequestAsReadyForReviewInternal(
    requestId: FundSubDataExtractRequestId
  ): Task[Unit] = {
    FDBRecordDatabase
      .transact(FundSubDataExtractRequestStoreOperations.Production)(
        _.markRequestReadyForReview(requestId)
      )
      .unit
  }

  def renewDataExtractFormLock(
    params: RenewDataExtractFormLockParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- assetLockService.renewLock(params.requestId, actor)
    } yield ()
  }

  def closeDataExtractFormView(
    params: CloseDataExtractFormViewParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- assetLockService.releaseLock(params.requestId, actor)
    } yield ()
  }

}
