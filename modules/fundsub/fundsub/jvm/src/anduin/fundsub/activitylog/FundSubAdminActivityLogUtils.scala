// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.activitylog

import fundsub.review.supportingdoc.SupportingDocReviewConfigMode
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.fdb.record.FDBRecordDatabase
import anduin.fundsub.auditlog.FundSubAuditLogService.{AddEventEmailParam, AddEventParam}
import anduin.fundsub.auditlog.{AuditLogActorType, AuditLogEventType, FundSubAuditLogService}
import anduin.fundsub.dashboard.LpInfoOperations
import anduin.fundsub.endpoint.admin.LpBasicInfo
import anduin.fundsub.endpoint.lp.NameEmailInfo
import anduin.fundsub.endpoint.subscriptiondoc.review.FundSubSubscriptionDocReviewConfigMode
import anduin.fundsub.models.FundSubModelStoreOperations
import anduin.id.fundsub.{FundSubAdminRestrictedId, FundSubId, FundSubLpId}
import anduin.model.common.user.UserId
import anduin.model.id.TeamId
import anduin.model.id.email.InternalEmailId
import anduin.protobuf.actionlogger.event.*
import anduin.protobuf.activitylog.ActivityDetail.Activity
import anduin.protobuf.activitylog.fundsub.admin.*
import anduin.protobuf.activitylog.{ActivityDetail, ActivityModel}
import anduin.protobuf.flow.fundsub.admin.lpdashboard.UserBasicInfo
import anduin.protobuf.fundsub.FundSubEvent
import anduin.service.RequestContext
import com.anduin.stargazer.service.actionlogger.*

object FundSubAdminActivityLogUtils {

  def addActivity(
    fundSubId: FundSubId,
    actor: UserId,
    fundAdminActivity: FundAdminActivity
  )(
    using activityLogService: ActivityLogService
  ): Task[Unit] = {
    for {
      logIdOpt <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId)).map(_.activityLogId)
      }
      _ <- logIdOpt.fold[Task[Unit]] {
        ZIO.unit
      } { logId =>
        activityLogService
          .addActivity(
            logId,
            Option(actor),
            ActivityDetail(
              Activity.FundAdminActivity(
                fundAdminActivity
              )
            )
          )
          .map(_ => ())
      }
    } yield ()
  }

  def addUpdateAmlKycConfigEvent(
    fundId: FundSubId,
    currentMode: SupportingDocReviewConfigMode,
    updatedMode: SupportingDocReviewConfigMode,
    wasDocGroupEnabled: Boolean,
    isDocGroupEnabled: Boolean,
    actor: UserId,
    unassignedEmailIds: Seq[InternalEmailId],
    assignedEmailIds: Seq[InternalEmailId]
  )(
    using activityLogService: ActivityLogService,
    fundSubAuditLogService: FundSubAuditLogService
  ): Task[Unit] = {
    val (action, eventType) =
      if (currentMode.isSupportingDocReviewConfigModeDisabled && !updatedMode.isSupportingDocReviewConfigModeDisabled) {
        AmlKycReviewTurnedOn() -> AuditLogEventType.SUPPORTING_DOC_REVIEW_WORKFLOW_ENABLED
      } else if (
        !currentMode.isSupportingDocReviewConfigModeDisabled && updatedMode.isSupportingDocReviewConfigModeDisabled
      ) {
        AmlKycReviewTurnedOff() -> AuditLogEventType.SUPPORTING_DOC_REVIEW_WORKFLOW_DISABLED
      } else {
        AmlKycReviewUpdated(
          wasDocGroupEnabled = wasDocGroupEnabled,
          isDocGroupEnabled = isDocGroupEnabled
        ) -> AuditLogEventType.SUPPORTING_DOC_REVIEW_WORKFLOW_UPDATED
      }
    for {
      _ <- addActivity(
        fundId,
        actor = actor,
        AmlKycReviewConfigUpdated(
          action
        )
      )
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = fundId,
        params = AddEventParam(
          actor = Option(actor),
          actorType = AuditLogActorType.FundSide,
          eventType = eventType,
          eventEmail = Seq(
            Option.when(assignedEmailIds.nonEmpty) {
              AddEventEmailParam(
                fundSubEventType = FundSubEvent.amlKycDocumentReviewAssignment,
                emailIds = assignedEmailIds
              )
            },
            Option.when(unassignedEmailIds.nonEmpty) {
              AddEventEmailParam(
                fundSubEventType = FundSubEvent.amlKycDocumentReviewUnassignment,
                emailIds = unassignedEmailIds
              )
            }
          ).flatten,
          activityDetail = AmlKycReviewConfigUpdated(
            action
          )
        )
      )
    } yield ()

  }

  def addUpdateUnsignedSubscriptionDocConfigEvent(
    fundId: FundSubId,
    currentMode: FundSubSubscriptionDocReviewConfigMode,
    updatedMode: FundSubSubscriptionDocReviewConfigMode,
    unassignedEmailIds: Seq[InternalEmailId],
    assignedEmailIds: Seq[InternalEmailId],
    actor: UserId,
    httpRequestCtxOpt: Option[RequestContext],
    isEnableRequireInvestorToSubmitDocumentBeforeSign: Boolean
  )(
    using activityLogService: ActivityLogService,
    fundSubAuditLogService: FundSubAuditLogService,
    actionLoggerService: ActionLoggerService
  ): Task[Unit] = {
    val (action, eventType, actionEvent) =
      if (
        currentMode == FundSubSubscriptionDocReviewConfigMode.Disabled && updatedMode != FundSubSubscriptionDocReviewConfigMode.Disabled
      ) {
        (
          UnsignedSubscriptionDocReviewTurnedOn(),
          AuditLogEventType.UNSIGNED_SUBSCRIPTION_DOC_REVIEW_WORKFLOW_ENABLED,
          ActionEventEnableUnsignedSubscriptionDocConfig(fundId)
        )
      } else if (
        currentMode != FundSubSubscriptionDocReviewConfigMode.Disabled && updatedMode == FundSubSubscriptionDocReviewConfigMode.Disabled
      ) {
        (
          UnsignedSubscriptionDocReviewTurnedOff(),
          AuditLogEventType.UNSIGNED_SUBSCRIPTION_DOC_REVIEW_WORKFLOW_DISABLED,
          ActionEventDisableUnsignedSubscriptionDocConfig(fundId)
        )
      } else {
        (
          UnsignedSubscriptionDocReviewUpdated(),
          AuditLogEventType.UNSIGNED_SUBSCRIPTION_DOC_REVIEW_WORKFLOW_UPDATED,
          if (isEnableRequireInvestorToSubmitDocumentBeforeSign) {
            ActionEventRequireInvestorToSubmitForReviewBeforeSign(fundId)
          } else {
            ActionEventUpdateUnsignedSubscriptionDocConfig(fundId)
          }
        )
      }

    for {
      _ <- addActivity(
        fundId,
        actor = actor,
        UnsignedSubscriptionDocReviewConfigUpdated(
          action
        )
      )
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = fundId,
        params = AddEventParam(
          actor = Option(actor),
          actorType = AuditLogActorType.FundSide,
          eventType = eventType,
          eventEmail = Seq(
            Option.when(assignedEmailIds.nonEmpty) {
              AddEventEmailParam(
                fundSubEventType = FundSubEvent.documentReviewAssignment,
                emailIds = assignedEmailIds
              )
            },
            Option.when(unassignedEmailIds.nonEmpty) {
              AddEventEmailParam(
                fundSubEventType = FundSubEvent.documentReviewUnassignment,
                emailIds = unassignedEmailIds
              )
            }
          ).flatten,
          activityDetail = UnsignedSubscriptionDocReviewConfigUpdated(
            action
          )
        )
      )
      _ <- actionLoggerService.addEventLog(
        actor = actor,
        events = Seq(actionEvent),
        httpContextOpt = httpRequestCtxOpt
      )
    } yield ()
  }

  def addUpdateSignedSubscriptionDocConfigEvent(
    fundId: FundSubId,
    currentMode: FundSubSubscriptionDocReviewConfigMode,
    updatedMode: FundSubSubscriptionDocReviewConfigMode,
    unassignedEmailIds: Seq[InternalEmailId],
    assignedEmailIds: Seq[InternalEmailId],
    actor: UserId,
    httpRequestCtxOpt: Option[RequestContext]
  )(
    using activityLogService: ActivityLogService,
    fundSubAuditLogService: FundSubAuditLogService,
    actionLoggerService: ActionLoggerService
  ): Task[Unit] = {
    val (action, eventType, actionEvent) =
      if (
        currentMode == FundSubSubscriptionDocReviewConfigMode.Disabled && updatedMode != FundSubSubscriptionDocReviewConfigMode.Disabled
      ) {
        (
          SignedSubscriptionDocReviewTurnedOn(),
          AuditLogEventType.SIGNED_SUBSCRIPTION_DOC_REVIEW_WORKFLOW_ENABLED,
          ActionEventEnableSignedSubscriptionDocConfig(fundId)
        )
      } else if (
        currentMode != FundSubSubscriptionDocReviewConfigMode.Disabled && updatedMode == FundSubSubscriptionDocReviewConfigMode.Disabled
      ) {
        (
          SignedSubscriptionDocReviewTurnedOff(),
          AuditLogEventType.SIGNED_SUBSCRIPTION_DOC_REVIEW_WORKFLOW_DISABLED,
          ActionEventDisableSignedSubscriptionDocConfig(fundId)
        )
      } else {
        (
          SignedSubscriptionDocReviewUpdated(),
          AuditLogEventType.SIGNED_SUBSCRIPTION_DOC_REVIEW_WORKFLOW_UPDATED,
          ActionEventUpdateSignedSubscriptionDocConfig(fundId)
        )
      }

    for {
      _ <- addActivity(
        fundId,
        actor = actor,
        SignedSubscriptionDocReviewConfigUpdated(
          action
        )
      )
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = fundId,
        params = AddEventParam(
          actor = Option(actor),
          actorType = AuditLogActorType.FundSide,
          eventType = eventType,
          eventEmail = Seq(
            Option.when(assignedEmailIds.nonEmpty) {
              AddEventEmailParam(
                fundSubEventType = FundSubEvent.documentReviewAssignment,
                emailIds = assignedEmailIds
              )
            },
            Option.when(unassignedEmailIds.nonEmpty) {
              AddEventEmailParam(
                fundSubEventType = FundSubEvent.documentReviewUnassignment,
                emailIds = unassignedEmailIds
              )
            }
          ).flatten,
          activityDetail = SignedSubscriptionDocReviewConfigUpdated(
            action
          )
        )
      )
      _ <- actionLoggerService.addEventLog(
        actor = actor,
        events = Seq(actionEvent),
        httpContextOpt = httpRequestCtxOpt
      )
    } yield ()

  }

  def getActivities(
    fundSubId: FundSubId,
    offset: Int,
    limit: Int
  )(
    using activityLogService: ActivityLogService
  ): Task[List[ActivityModel]] = {
    for {
      logIdOpt <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId)).map(_.activityLogId)
      }
      activities <- logIdOpt.fold[Task[List[ActivityModel]]] {
        ZIO.succeed(List.empty[ActivityModel])
      } { logId =>
        activityLogService.getActivities(
          logId,
          offset = offset,
          limit = limit
        )
      }
    } yield activities
  }

  def getLatestActivities(
    fundSubId: FundSubId,
    limit: Int
  )(
    using activityLogService: ActivityLogService
  ): Task[List[ActivityModel]] = {
    for {
      logIdOpt <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId)).map(_.activityLogId)
      }
      activities <- logIdOpt.fold[Task[List[ActivityModel]]] {
        ZIO.succeed(List.empty[ActivityModel])
      } { logId =>
        activityLogService.getLatestActivities(
          logId,
          limit = limit
        )
      }
    } yield activities
  }

  private def extractUserIdFromFundAdminActivity(activities: Seq[FundAdminActivity]): List[UserId] = {
    activities
      .flatMap[UserId] {
        case adminInvited: AdminInvited                             => List(adminInvited.userId)
        case adminJoined: AdminJoined                               => List(adminJoined.userId)
        case adminRemoved: AdminRemoved                             => List(adminRemoved.userId)
        case sentEmail: SentEmail                                   => sentEmail.userIds
        case lpInvited: LpInvited                                   => lpInvited.userId +: lpInvited.collaborators
        case _: BatchLpInvited                                      => List.empty
        case lpJoined: LpJoined                                     => List(lpJoined.userId)
        case adminAccessedLp: AdminAccessedSubscription             => List(adminAccessedLp.userId)
        case offlineOrderAdded: OfflineOrderAdded                   => List(offlineOrderAdded.userId)
        case _: BatchOfflineOrderAdded                              => List.empty
        case lpInvestedInAdditionalFund: LpInvestedInAdditionalFund => List(lpInvestedInAdditionalFund.userId)
        case lpJoinedViaInvitationLink: LpJoinedViaInvitationLink   => List(lpJoinedViaInvitationLink.userId)
        case lpRemoved: LpRemoved                                   => List(lpRemoved.userId)
        case lpRestored: LpRestored                                 => List(lpRestored.userId)
        case collaboratorAdded: CollaboratorAdded                   => List(collaboratorAdded.userId)
        case collaboratorJoined: CollaboratorJoined                 => List(collaboratorJoined.userId)
        case collaboratorRemoved: CollaboratorRemoved               => List(collaboratorRemoved.userId)
        case collaboratorPromoted: CollaboratorPromoted             => List(collaboratorPromoted.userId)
        case uploadedExecutedDocument: UploadedExecutedDocument     => List(uploadedExecutedDocument.investorUserId)
        case uploadedDocOnBehalf: UploadedDocOnBehalf               => List(uploadedDocOnBehalf.investorUserId)
        case sentExecutedDocument: SentExecutedDocument             => List(sentExecutedDocument.investorUserId)
        case signedExecutedDocument: SignedExecutedDocument =>
          signedExecutedDocument.investorUserId +: signedExecutedDocument.signerIds
        case submittedSubscriptionPackage: SubmittedSubscriptionPackage =>
          List(submittedSubscriptionPackage.investorUserId)
        case undidSubscriptionPackage: UndidSubscriptionPackage   => List(undidSubscriptionPackage.investorUserId)
        case requestChange: RequestChange                         => List(requestChange.investorUserId)
        case _: SubscriptionDocumentReviewEnabled                 => List.empty
        case _: SubscriptionDocumentReviewDisabled                => List.empty
        case reviewerAdded: SubscriptionDocumentReviewerAdded     => List(reviewerAdded.userId)
        case reviewerRemoved: SubscriptionDocumentReviewerRemoved => List(reviewerRemoved.userId)
        case subscriptionDocumentMarkedAsReviewed: SubscriptionDocumentMarkedAsReviewed =>
          List(subscriptionDocumentMarkedAsReviewed.investorUserId)
        case signedSubscriptionDocumentMarkedAsApproved: SignedSubscriptionDocumentMarkedAsApproved =>
          List(signedSubscriptionDocumentMarkedAsApproved.investorUserId)
        case unsignedSubscriptionDocumentMarkedAsApproved: UnsignedSubscriptionDocumentMarkedAsApproved =>
          List(unsignedSubscriptionDocumentMarkedAsApproved.investorUserId)
        case subscriptionDocumentReviewSkipped: SubscriptionDocumentReviewSkipped =>
          List(subscriptionDocumentReviewSkipped.investorUserId)
        case removedUploadedExecutedDocument: RemovedUploadedExecutedDocument =>
          List(removedUploadedExecutedDocument.investorUserId)
        case updatedExecutedDocument: UpdatedExecutedDocument           => List(updatedExecutedDocument.investorUserId)
        case markedSubscriptionAsComplete: MarkedSubscriptionAsComplete => List(markedSubscriptionAsComplete.lpUserId)
        case manualOrderActivatedByInvestor: ManualOrderActivatedByInvestor =>
          List(manualOrderActivatedByInvestor.investorUserId)
        case convertOfflineOrderToNormal: ConvertOfflineOrderToNormal => List(convertOfflineOrderToNormal.userId)
        case remindLPToSignAgain: RemindLpToSignAgain                 => List(remindLPToSignAgain.investorUserId)
        case sentReminderToUploadSupportingDoc: SentReminderToUploadSupportingDoc =>
          List(sentReminderToUploadSupportingDoc.investorUserId)
        case lpFilledForm: LpFilledForm => List(lpFilledForm.investorUserId)
        case signatureRequestReassign: SignatureRequestReassign =>
          List(signatureRequestReassign.newSigner, signatureRequestReassign.oldSigner)
        case _: NewAdditionalDocumentUploadReport => List.empty
        case _: NewFormCommentReport              => List.empty
        case _: NewCommentAssignmentReport        => List.empty
        case _: NewLpReport                       => List.empty
        case groupMemberMoved: GroupMembersMoved =>
          if (groupMemberMoved.groupMembers.size == 1) groupMemberMoved.groupMembers.headOption.toList else List.empty
        case _: GroupCreated | _: GroupRenamed | _: GroupPermissionUpdated | _: GroupVisibilityUpdated |
            _: GroupDeleted =>
          List.empty
        case _: GroupAccessToViewUpdated | _: ViewRenamed | _: ViewCreated | _: DashboardColumnsUpdated |
            _: ViewDeleted | _: PrivateViewShared =>
          List.empty
        case _: InvestorGroupCreated | _: InvestorGroupRenamed | _: InvestorGroupDeleted |
            _: InvestorGroupAccessibilityGranted | _: InvestorGroupAccessibilityWithdrawn =>
          List.empty
        case _: InvestorGroupCreated | _: InvestorGroupRenamed | _: InvestorGroupDeleted |
            _: InvestorGroupAccessibilityGranted | _: InvestorGroupAccessibilityWithdrawn | _: InvestorAssignedToGroup |
            _: InvestorMovedToAnotherGroup | _: InvestorUnassignedFromGroup =>
          List.empty
        case _: AmlKycReviewConfigUpdated                  => List.empty
        case _: SignedSubscriptionDocReviewConfigUpdated   => List.empty
        case _: UnsignedSubscriptionDocReviewConfigUpdated => List.empty
        case newFormCommentNotification: NewFormCommentNotificationToInvestor =>
          List(newFormCommentNotification.investorUserId)

        case a: AmendmentEdited       => List(a.investorUserId)
        case a: AmendmentAdded        => List(a.investorUserId)
        case a: AmendmentRemoved      => List(a.investorUserId)
        case a: UpdateInvestorsValues => List(a.investorUserId)
        case FundAdminActivity.Empty  => List.empty
        case _: CustomLpIdUpdated     => List.empty
        case _: CustomFundIdUpdated   => List.empty
        case _: CommentActivity       => List.empty
        case a: AnchorPointActivity   => a.assignedGpUserIdOpt.map(gpUserId => List(gpUserId)).getOrElse(List.empty)
        case _: CommentExported       => List.empty
        case _: CommentSettingUpdateActivity              => List.empty
        case dataExtractionStarted: DataExtractionStarted => List(dataExtractionStarted.requestInfo.lpUserId)
        case extractedDataReadyForReview: ExtractedDataReadyForReview =>
          extractedDataReadyForReview.requestInfos.map(_.lpUserId)
        case extractedDataMarkedAsComplete: ExtractedDataMarkedAsComplete =>
          List(extractedDataMarkedAsComplete.requestInfo.lpUserId)
        case extractedDataEdited: ExtractedDataEdited =>
          List(extractedDataEdited.requestInfo.lpUserId)
        case _: CreateClose | _: UpdateClose | _: DeleteClose | _: MoveLpToNewClose => List.empty
        case _: SelfServiceExportTemplateCreated | _: SelfServiceExportTemplateRenamed |
            _: SelfServiceExportTemplateUpdated | _: SelfServiceExportTemplateDeleted | _: InvestorDataExported |
            _: EmailTemplateCreated | _: EmailTemplateUpdated | _: EmailTemplateRenamed | _: EmailTemplateSetAsDefault |
            _: EmailTemplateDeleted | _: AdvisorEntityJoined | _: AdvisorEntityNameUpdated | _: AdvisorJoined |
            _: RiaOrderCreated | _: ConvertToRiaOrder | _: UnlinkRiaOrder |
            _: DisableAdvisorEntityCreateNewSubscription | _: EnableAdvisorEntityCreateNewSubscription |
            _: SideLetterVersionCreated | _: SideLetterFilesUploaded | _: SideLetterFilesRemoved |
            _: MarkSideLetterAsAgreed | _: MarkSideLetterCompleted | _: CustomColumnCreated | _: CustomColumnDeleted |
            _: CustomColumnRenamed | _: CustomColumnValueUpdated | _: TagListUpdated |
            _: AdminUpdateAllowFormEditPostSigning =>
          List.empty
        case a: AdvisorInvited                                      => List(a.advisor)
        case a: ResendAdvisorInvitation                             => List(a.advisor)
        case a: RevokeAdvisorInvitation                             => List(a.advisor)
        case signatureDateFormatUpdated: SignatureDateFormatUpdated => List(signatureDateFormatUpdated.userId)
      }
      .toList
  }

  def extractParticipantInfoFromFundAdminActivity(
    activities: List[FundAdminActivity]
  )(
    using userProfileService: UserProfileService
  ): Task[Map[UserId, NameEmailInfo]] = {
    val participants = extractUserIdFromFundAdminActivity(activities)
    ZIO
      .collectAllPar {
        participants.map { participant =>
          userProfileService.getUserInfo(participant).map { userInfo =>
            participant -> NameEmailInfo(
              email = userInfo.emailAddressStr,
              firstName = userInfo.firstName,
              lastName = userInfo.lastName
            )
          }
        }
      }
      .map(_.toMap)
  }

  def extractGpEntityIdsFromFundAdminActivity(
    activities: List[FundAdminActivity]
  ): (List[UserId], List[TeamId]) = {
    val gpUserIds = activities.flatMap {
      case a: AnchorPointActivity => a.assignedGpUserIdOpt
      case _                      => None
    }.distinct
    val gpTeamIds = activities.flatMap {
      case a: AnchorPointActivity => a.assignedGpTeamIdOpt
      case _                      => None
    }.distinct
    (gpUserIds, gpTeamIds)
  }

  private def extractLpIdFromFundAdminActivity(activities: Seq[FundAdminActivity]): List[FundSubLpId] = {
    activities.flatMap {
      case _: SentEmail                             => List.empty
      case _: AdminInvited                          => List.empty
      case _: AdminJoined                           => List.empty
      case _: AdminRemoved                          => List.empty
      case adminAccessed: AdminAccessedSubscription => List(adminAccessed.lpId)
      case lpInvited: LpInvited                     => List(lpInvited.lpId)
      case _: BatchLpInvited                        => List.empty
      case lpJoined: LpJoined                       => List(lpJoined.lpId)
      case offlineOrderAdded: OfflineOrderAdded     => List(offlineOrderAdded.lpId)
      case _: BatchOfflineOrderAdded                => List.empty
      case lpInvestedInAdditionalFund: LpInvestedInAdditionalFund =>
        List(lpInvestedInAdditionalFund.lpId, lpInvestedInAdditionalFund.originalLpId)
      case lpJoinedViaInvitationLink: LpJoinedViaInvitationLink       => List(lpJoinedViaInvitationLink.lpId)
      case lpRemoved: LpRemoved                                       => List(lpRemoved.lpId)
      case lpRestore: LpRestored                                      => List(lpRestore.lpId)
      case collaboratorAdded: CollaboratorAdded                       => List(collaboratorAdded.lpId)
      case collaboratorJoined: CollaboratorJoined                     => List(collaboratorJoined.lpId)
      case collaboratorRemoved: CollaboratorRemoved                   => List(collaboratorRemoved.lpId)
      case collaboratorPromoted: CollaboratorPromoted                 => List(collaboratorPromoted.lpId)
      case uploadedExecutedDocument: UploadedExecutedDocument         => List(uploadedExecutedDocument.lpId)
      case uploadedDocOnBehalf: UploadedDocOnBehalf                   => List(uploadedDocOnBehalf.lpId)
      case sentExecutedDocument: SentExecutedDocument                 => List(sentExecutedDocument.lpId)
      case signedExecutedDocument: SignedExecutedDocument             => List(signedExecutedDocument.lpId)
      case submittedSubscriptionPackage: SubmittedSubscriptionPackage => List(submittedSubscriptionPackage.lpId)
      case undidSubscriptionPackage: UndidSubscriptionPackage         => List(undidSubscriptionPackage.lpId)
      case requestChange: RequestChange                               => List(requestChange.lpId)
      case _: SubscriptionDocumentReviewEnabled                       => List.empty
      case _: SubscriptionDocumentReviewDisabled                      => List.empty
      case _: SubscriptionDocumentReviewerAdded                       => List.empty
      case _: SubscriptionDocumentReviewerRemoved                     => List.empty
      case subscriptionDocumentMarkedAsReviewed: SubscriptionDocumentMarkedAsReviewed =>
        List(subscriptionDocumentMarkedAsReviewed.lpId)
      case signedSubscriptionDocumentMarkedAsApproved: SignedSubscriptionDocumentMarkedAsApproved =>
        List(signedSubscriptionDocumentMarkedAsApproved.lpId)
      case unsignedSubscriptionDocumentMarkedAsApproved: UnsignedSubscriptionDocumentMarkedAsApproved =>
        List(unsignedSubscriptionDocumentMarkedAsApproved.lpId)
      case subscriptionDocumentReviewSkipped: SubscriptionDocumentReviewSkipped =>
        List(subscriptionDocumentReviewSkipped.lpId)
      case removedUploadedExecutedDocument: RemovedUploadedExecutedDocument =>
        List(removedUploadedExecutedDocument.lpId)
      case updatedExecutedDocument: UpdatedExecutedDocument               => List(updatedExecutedDocument.lpId)
      case markedSubscriptionAsComplete: MarkedSubscriptionAsComplete     => List(markedSubscriptionAsComplete.lpId)
      case manualOrderActivatedByInvestor: ManualOrderActivatedByInvestor => List(manualOrderActivatedByInvestor.lpId)
      case convertOfflineOrderToNormal: ConvertOfflineOrderToNormal       => List(convertOfflineOrderToNormal.lpId)
      case remindLPToSignAgain: RemindLpToSignAgain                       => List(remindLPToSignAgain.lpId)
      case sentReminderToUploadSupportingDoc: SentReminderToUploadSupportingDoc =>
        List(sentReminderToUploadSupportingDoc.lpId)
      case lpFilledForm: LpFilledForm           => List(lpFilledForm.lpId)
      case _: NewAdditionalDocumentUploadReport => List.empty
      case _: NewFormCommentReport              => List.empty
      case _: NewCommentAssignmentReport        => List.empty
      case _: NewLpReport                       => List.empty
      case _: SignatureRequestReassign          => List.empty
      case _: GroupMembersMoved | _: GroupCreated | _: GroupRenamed | _: GroupPermissionUpdated |
          _: GroupVisibilityUpdated | _: GroupDeleted =>
        List.empty
      case _: GroupAccessToViewUpdated | _: ViewRenamed | _: ViewCreated | _: DashboardColumnsUpdated | _: ViewDeleted |
          _: PrivateViewShared =>
        List.empty
      case _: InvestorGroupCreated | _: InvestorGroupRenamed | _: InvestorGroupDeleted |
          _: InvestorGroupAccessibilityGranted | _: InvestorGroupAccessibilityWithdrawn =>
        List.empty
      case investorAssigned: InvestorAssignedToGroup                        => investorAssigned.lpIds
      case investorMoved: InvestorMovedToAnotherGroup                       => investorMoved.lpIds
      case investorUnassigned: InvestorUnassignedFromGroup                  => investorUnassigned.lpIds
      case _: AmlKycReviewConfigUpdated                                     => List.empty
      case _: SignedSubscriptionDocReviewConfigUpdated                      => List.empty
      case _: UnsignedSubscriptionDocReviewConfigUpdated                    => List.empty
      case newFormCommentNotification: NewFormCommentNotificationToInvestor => List(newFormCommentNotification.lpId)
      case a: AmendmentEdited                                               => List(a.lpId)
      case a: AmendmentAdded                                                => List(a.lpId)
      case a: AmendmentRemoved                                              => List(a.lpId)
      case a: UpdateInvestorsValues                                         => List(a.lpId)
      case a: CustomLpIdUpdated                                             => List(a.lpId)
      case _: CustomFundIdUpdated                                           => List.empty
      case FundAdminActivity.Empty                                          => List.empty
      case a: CommentActivity                                               => List(a.lpId)
      case a: AnchorPointActivity                                           => List(a.lpId)
      case _: CommentExported                                               => List.empty
      case _: CommentSettingUpdateActivity                                  => List.empty
      case dataExtractionStarted: DataExtractionStarted => List(dataExtractionStarted.requestInfo.lpId)
      case extractedDataReadyForReview: ExtractedDataReadyForReview =>
        extractedDataReadyForReview.requestInfos.map(_.lpId)
      case extractedDataMarkedAsComplete: ExtractedDataMarkedAsComplete =>
        List(extractedDataMarkedAsComplete.requestInfo.lpId)
      case extractedDataEdited: ExtractedDataEdited =>
        List(extractedDataEdited.requestInfo.lpId)
      case _: CreateClose | _: UpdateClose | _: DeleteClose => List.empty
      case a: MoveLpToNewClose                              => List(a.lpId)
      case _: SelfServiceExportTemplateCreated | _: SelfServiceExportTemplateRenamed |
          _: SelfServiceExportTemplateUpdated | _: SelfServiceExportTemplateDeleted =>
        List.empty
      case investorDataExported: InvestorDataExported => investorDataExported.lpIds
      case _: EmailTemplateCreated | _: EmailTemplateUpdated | _: EmailTemplateRenamed | _: EmailTemplateSetAsDefault |
          _: EmailTemplateDeleted | _: AdvisorEntityJoined | _: AdvisorEntityNameUpdated | _: AdvisorJoined |
          _: AdvisorInvited | _: ResendAdvisorInvitation | _: RevokeAdvisorInvitation |
          _: DisableAdvisorEntityCreateNewSubscription | _: EnableAdvisorEntityCreateNewSubscription |
          _: AdminUpdateAllowFormEditPostSigning =>
        List.empty
      case a: RiaOrderCreated                                                       => List(a.lpId)
      case a: ConvertToRiaOrder                                                     => List(a.lpId)
      case a: UnlinkRiaOrder                                                        => List(a.lpId)
      case a: SideLetterVersionCreated                                              => List(a.lpId)
      case a: SideLetterFilesUploaded                                               => List(a.lpId)
      case a: SideLetterFilesRemoved                                                => List(a.lpId)
      case a: MarkSideLetterAsAgreed                                                => List(a.lpId)
      case a: MarkSideLetterCompleted                                               => List(a.lpId)
      case _: SignatureDateFormatUpdated                                            => List.empty
      case _: CustomColumnCreated | _: CustomColumnDeleted | _: CustomColumnRenamed => List.empty
      case a: CustomColumnValueUpdated                                              => List(a.lpId)
      case a: TagListUpdated                                                        => List(a.lpId)
    }.toList
  }

  def extractLpInfoFromFundAdminActivity(
    activities: List[FundAdminActivity]
  ): Task[Map[FundSubLpId, LpBasicInfo]] = {
    val lpIds = extractLpIdFromFundAdminActivity(activities)
    ZIO
      .foreach(lpIds) { lpId =>
        FDBRecordDatabase.transact(LpInfoOperations.Production)(_.getOpt(lpId).map(_.map { lpRecord =>
          lpId -> LpBasicInfo(
            lpId = lpRecord.lpId,
            mainLp = UserBasicInfo(
              userId = lpRecord.userId,
              firstName = lpRecord.firstName,
              lastName = lpRecord.lastName,
              email = lpRecord.email
            ),
            collaborators = lpRecord.collaborators.toList,
            firmName = lpRecord.firmName
          )
        }))
      }
      .map(_.flatten.toMap)
  }

}
