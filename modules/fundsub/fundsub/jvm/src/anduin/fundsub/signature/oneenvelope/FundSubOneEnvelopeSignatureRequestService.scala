// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.signature.oneenvelope

import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.dms.DmsFeature
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.docrequest.service.FormSubmissionService
import anduin.endpoint.signature.SignatureProviderParams
import anduin.endpoint.signature.SignatureSharedModels.SigningAuthData
import anduin.fdb.record.{DefaultCluster, FDBOperations, FDBRecordDatabase}
import anduin.forms.service.FormService
import anduin.fundsub.auditlog.FundSubAuditLogService.{AddEventEmailParam, AddEventParam}
import anduin.fundsub.auditlog.{AuditLogActorType, AuditLogEventType, FundSubAuditLogService}
import anduin.fundsub.endpoint.common.CreateRequestSignerData
import anduin.fundsub.endpoint.signature.DocusignESignatureOptionParams
import anduin.fundsub.endpoint.signature.oneenvelope.*
import anduin.fundsub.endpoint.subscriptiondoc.SubscriptionVersionIndex.Latest
import anduin.fundsub.endpoint.subscriptiondoc.{GetSubscriptionVersionBasicInfoParams, SubscriptionVersionIndex}
import anduin.fundsub.form.FundSubFormService
import anduin.fundsub.form.utils.FundSubCommonUtils
import anduin.fundsub.investorgroup.FundSubInvestorGroupUtils
import anduin.fundsub.models.{FundSubLpModelStoreOperations, FundSubModelStoreOperations}
import anduin.fundsub.models.signature.FundSubSignatureRequestModels
import anduin.fundsub.models.signature.FundSubSignatureRequestModels.{
  DocType,
  FundSubSignatureRequestBasic,
  FundSubSignatureRequestStatus
}
import anduin.fundsub.service.*
import anduin.fundsub.signature.integration.FundSubSignatureIntegrationService
import anduin.fundsub.signature.integration.FundSubSignatureIntegrationService.FundSubSignerParams
import anduin.fundsub.signature.oneenvelope.FundSubOneEnvelopeSignatureRequestService.DocTypeNotSupportedException
import anduin.fundsub.signature.{
  FundSubSignatureJvmUtils,
  FundSubSignaturePermissionUtils,
  FundSubSignatureService,
  SchwabTemplateUtils
}
import anduin.fundsub.submission.LPSubmissionOperations
import anduin.fundsub.submission.version.SubmissionVersionModel
import anduin.fundsub.submission.version.signature.LpSignatureType
import anduin.fundsub.subscriptiondoc.FundSubSubscriptionQueryService.GenerateCleanDocDataStrategy
import anduin.fundsub.subscriptiondoc.review.FundSubSubscriptionDocReviewService
import anduin.fundsub.subscriptiondoc.{FundSubSubscriptionDocService, FundSubSubscriptionQueryService}
import anduin.fundsub.uploadeddoc.LpUploadedDocSignatureRequestService
import anduin.fundsub.user.FundSubUserService
import anduin.id.docrequest.FormSubmissionId
import anduin.id.entity.EntityId
import anduin.id.fundsub.FundSubLpId
import anduin.id.signature.SignatureRequestId
import anduin.model.common.user.UserId
import anduin.model.id.FileId
import anduin.model.id.email.InternalEmailId
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus
import anduin.protobuf.fundsub.FundSubEvent
import anduin.protobuf.fundsub.activitylog.lp.{
  CreateOneEnvelopeRequest,
  MarkSubDocRequestComplete,
  OneEnvelopeSupportingFormFiles,
  SignedOneEnvelopeRequest
}
import anduin.protobuf.signature.DocumentSignatureMessage
import anduin.service.{AuthenticatedRequestContext, GeneralServiceException, ServiceException}
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.util.date.DateCalculator

case class FundSubOneEnvelopeSignatureRequestService(
  fundSubSubscriptionQueryService: FundSubSubscriptionQueryService,
  fundSubPermissionService: FundSubPermissionService,
  fundSubSubscriptionDocService: FundSubSubscriptionDocService,
  formService: FormService,
  newSupportingDocService: NewSupportingDocService,
  fundSubFormService: FundSubFormService,
  fileService: FileService,
  formSubmissionService: FormSubmissionService,
  fundSubSignatureService: FundSubSignatureService,
  fundSubUserService: FundSubUserService,
  backendConfig: GondorBackendConfig,
  userProfileService: UserProfileService,
  fundSubSignatureIntegrationService: FundSubSignatureIntegrationService,
  fundSubEmailService: FundSubEmailService,
  fundSubLpDashboardService: FundSubLpDashboardService,
  fundSubSubscriptionDocReviewService: FundSubSubscriptionDocReviewService,
  newSupportingDocLoggingService: NewSupportingDocLoggingService,
  lpUploadedDocService: LpUploadedDocSignatureRequestService,
  fundSubLpActivityLogService: FundSubLpActivityLogService,
  fundSubAuditLogService: FundSubAuditLogService
) {

  def getFormInfosToCreateOneEnvelope(
    lpId: FundSubLpId,
    actorId: UserId
  ): Task[FormInfosToCreateOneEnvelopeResp] = {
    for {
      _ <- ZIO.logInfo(s"User $actorId get form infos to create one envelope")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actorId)
      submissionVersionOpt <- fundSubSubscriptionQueryService.getSubscriptionVersionModelFromVersionIndex(
        lpId = lpId,
        versionIndex = Latest,
        actor = actorId,
        generateCleanDocDataStrategy = GenerateCleanDocDataStrategy.ForceNotGenerate
      )
      submissionVersionWithUpToDateSignatureStatusOpt <- getSubmissionVersionWithUpToDateSignatureStatus(
        submissionVersionOpt,
        actorId
      )
      fundSubModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(lpId.parent)
      )
      fundSubLpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(_.getFundSubLpModel(lpId))
      featureSwitchOpt = fundSubModel.featureSwitch
      subscriptionFormToCreateRequestOpt <- ZIOUtils
        .traverseOption(submissionVersionWithUpToDateSignatureStatusOpt) { submissionVersionModel =>
          FundSubOneEnvelopeServiceUtils.getSubscriptionFormInfoForOneEnvelope(
            fundSubLpModel,
            featureSwitchOpt,
            submissionVersionModel,
            actorId
          )(
            formService,
            fundSubSubscriptionDocService
          )
        }
        .map(_.flatten)
      supportingFormsToCreateRequest <- FundSubOneEnvelopeServiceUtils.getSupportingFormInfosForOneEnvelope(
        fundSubLpModel,
        featureSwitchOpt,
        submissionVersionWithUpToDateSignatureStatusOpt,
        actorId
      )(
        userProfileService,
        newSupportingDocService
      )
    } yield FormInfosToCreateOneEnvelopeResp(
      formInfos = (subscriptionFormToCreateRequestOpt ++ supportingFormsToCreateRequest).toSeq
    )
  }

  private def getSubmissionVersionWithUpToDateSignatureStatus(
    submissionVersionOpt: Option[SubmissionVersionModel],
    actor: UserId
  ): Task[Option[SubmissionVersionModel]] = {
    ZIOUtils
      .traverseOption(submissionVersionOpt) { submissionVersion =>
        val lpId = submissionVersion.fundSubLpId
        if (submissionVersion.signatureStatus.isEmpty) {
          for {
            enabledSoftReview <- fundSubSubscriptionDocReviewService.checkIfUnsignedReviewEnabled(lpId)
            _ <- fundSubSubscriptionQueryService.calculateAndUpdateSignatureStatusInternal(
              submissionVersion,
              enabledSoftReview,
              actor
            )
            updatedSubmissionVersionModelOpt <- fundSubSubscriptionQueryService
              .getSubscriptionVersionModelFromVersionIndex(
                lpId = lpId,
                versionIndex = SubscriptionVersionIndex.Latest,
                actor = actor,
                generateCleanDocDataStrategy = GenerateCleanDocDataStrategy.ForceNotGenerate
              )
          } yield updatedSubmissionVersionModelOpt
        } else {
          ZIO.some(submissionVersion)
        }
      }
      .map(_.flatten)
  }

  def getGeneratedFormFilesForOneEnvelopeRequest(
    lpId: FundSubLpId,
    formTypes: Seq[FormTypeForOneEnvelope],
    actorId: UserId
  ): Task[GetGeneratedFormFilesForOneEnvelopeRequestResp] =
    for {
      _ <- ZIO.logInfo(s"User $actorId get prepared signature locations for one envelope")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actorId)
      pdfTextColorOpt <- FundSubCommonUtils.getPDFTextColor(lpId.parent)
      formFilesWithSignatureBlocks <- ZIOUtils
        .foreachParN(2)(formTypes) { formType =>
          getFormFilesWithSignatureBlockInfos(formType, lpId, pdfTextColorOpt, actorId).map(formType -> _)
        }
        .map(_.toMap)
    } yield GetGeneratedFormFilesForOneEnvelopeRequestResp(formFilesWithSignatureBlocks)

  private def getFormFilesWithSignatureBlockInfos(
    formType: FormTypeForOneEnvelope,
    lpId: FundSubLpId,
    pdfTextColorOpt: Option[String],
    actorId: UserId
  ): Task[FormFilesWithSignatureBlockInfos] = {
    formType match {
      case FormTypeForOneEnvelope.SubscriptionForm =>
        for {
          getLpPreparedSignatureLocationResp <- fundSubSignatureService.getLpPreparedSignatureLocationsInternal(
            lpId,
            SubscriptionVersionIndex.Latest,
            actorId
          )
          generatedFileInfos <- fileService
            .batchGetFileNamesUnsafe(getLpPreparedSignatureLocationResp.generatedFiles)
            .map(_.toSeq)
        } yield FormFilesWithSignatureBlockInfos(
          signatureBlocks = getLpPreparedSignatureLocationResp.signatureBlocks,
          uniqueEmailSigningRoles = getLpPreparedSignatureLocationResp.uniqueEmailSigningRoles,
          generatedFileInfos = generatedFileInfos
        )
      case supportingForm: FormTypeForOneEnvelope.SupportingForm =>
        formSubmissionService
          .generateFilledFiles(supportingForm.formSubmissionId, actorId, None, pdfTextColorOpt)
          .map { filledFiles =>
            FormFilesWithSignatureBlockInfos(
              signatureBlocks = filledFiles.flatMap(_.signatureBlocks),
              uniqueEmailSigningRoles = Set.empty,
              generatedFileInfos = filledFiles.map(file => file.fileId -> file.name)
            )
          }
      case _: FormTypeForOneEnvelope.SupportingFormNotStarted =>
        ZIO.succeed(
          FormFilesWithSignatureBlockInfos(
            signatureBlocks = Seq.empty,
            uniqueEmailSigningRoles = Set.empty,
            generatedFileInfos = Seq.empty
          )
        )
    }
  }

  def createOneEnvelopeRequest(
    lpId: FundSubLpId,
    signers: Seq[CreateRequestSignerData],
    subscriptionFiles: Seq[FileId],
    supportingFormFiles: Seq[(FormSubmissionId, Seq[FileId])],
    lpUploadedFiles: Seq[FileId],
    message: String = "",
    refDocs: Seq[FileId] = Seq.empty,
    docusignESignatureOptionParamsOpt: Option[DocusignESignatureOptionParams] = None,
    actorId: UserId,
    httpContextOpt: Option[AuthenticatedRequestContext]
  ): Task[CreateOneEnvelopeRequestResp] = {
    val subscriptionFilesCount = subscriptionFiles.size
    val lpUploadedFilesCount = lpUploadedFiles.size
    val supportingFormFilesCount = supportingFormFiles.map(_._2.size).sum
    val docTypesCount = Seq(
      subscriptionFilesCount,
      supportingFormFilesCount,
      lpUploadedFilesCount
    ).count(_ > 0)
    val allFileIds = subscriptionFiles ++ supportingFormFiles.flatMap(_._2) ++ lpUploadedFiles
    val docsCount = allFileIds.size
    for {
      _ <- ZIO.logInfo(
        s"User $actorId create one envelope request for lp $lpId, doc type count: $docTypesCount, total file count: $docsCount"
      )
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actorId)
      _ <- ZIO.when(subscriptionFiles.nonEmpty) {
        fundSubPermissionService.validateLpStatus(
          lpId,
          Set(
            LpStatus.LPNotStarted,
            LpStatus.LPInProgress,
            LpStatus.LPFormReviewed,
            LpStatus.LPChangeInProgress,
            LpStatus.LPPendingUnsignedReview,
            LpStatus.LPPendingSubmission
          )
        )
      }
      _ <- fundSubPermissionService.validateFundActive(lpId.parent)
      _ <- ZIOUtils.failWhen(docsCount == 0 || signers.isEmpty) {
        GeneralServiceException("No files/signers found")
      }
      (fundSubModel, lpModel) <- FDBRecordDatabase.transact(
        FDBOperations[(FundSubModelStoreOperations, FundSubLpModelStoreOperations)].Production
      ) { case (fsOps, lpOps) =>
        for {
          fsModel <- fsOps.getFundSubPublicModel(lpId.parent)
          lpModel <- lpOps.getFundSubLpModel(lpId)
        } yield (fsModel, lpModel)
      }
      _ <- ZIO.when(subscriptionFiles.nonEmpty) {
        validateSubscriptionFormFiles(subscriptionFiles, lpId, actorId)
      }

      userIdMap <- fundSubUserService.createSignatureUserIfNeeded(
        fundSubId = lpId.parent,
        signers = signers.map(signer => signer.email -> signer.name),
        inviterOpt = httpContextOpt.map(_.actor.userInfo.emailAddressStr)
      )
      fundSubSigners <- ZIO.foreach(signers) { signer =>
        for {
          userId <- ZIOUtils.optionToTask(
            userIdMap.get(signer.email),
            GeneralServiceException(s"No userId for ${signer.email}")
          )
        } yield {
          FundSubSignerParams(
            userId = userId,
            blocks = signer.blocks,
            signatures = signer.signatures,
            authTypeData = signer.authTypeData,
            docusignRecipientAuthDataOpt = signer.docusignRecipientAuthDataOpt,
            canAccessSignatureRequiredDocOnly = signer.canAccessSignatureRequiredDocOnly
          )
        }
      }
      latestVersionIndex <-
        if (subscriptionFilesCount > 0) {
          fundSubSubscriptionDocService
            .prepareLatestVersionBeforeSignature(
              lpId,
              actorId,
              allSigners = fundSubSigners.map(_.userId)
            )
            .map(_.versionIndex)
        } else {
          FDBRecordDatabase.transact(LPSubmissionOperations.Production)(_.getLastVersionIndex(lpId))
        }
      signatureProviderParams <- FundSubSignatureJvmUtils.resolveSignatureProviderParams(
        fundSubModel,
        backendConfig.docusignIntegrationConfig,
        signatureRequestDocTypeOpt = None,
        docusignESignatureOptionParamsOpt,
        actorId
      )(
        using userProfileService
      )
      namesOfFilesToSign <- fileService.batchGetFileNamesUnsafe(allFileIds)(
        using DmsFeature.Public
      )
      useDocusign = signatureProviderParams match {
        case _: SignatureProviderParams.Docusign => true
        case _                                   => false
      }
      isSchwabSigning = SchwabTemplateUtils.shouldSignWithSchwabFlow(namesOfFilesToSign.values.toSeq) && useDocusign
      _ <- ZIO.when(isSchwabSigning) {
        ZIOUtils.failWhen(
          docusignESignatureOptionParamsOpt.forall(
            !SchwabTemplateUtils.isSignersDataValidForSchwabSigning(signers, _)
          )
        ) {
          GeneralServiceException("Signers data is not valid for Schwab signing")
        }
      }
      _ <- ZIOUtils.traverseOption(docusignESignatureOptionParamsOpt)(validateDocusignParams(_, signers))
      lpInvestorGroups <- FundSubInvestorGroupUtils
        .getAllInvestorGroupsOfLp(lpId)
      fundAuthorizedTeamIds <-
        if (subscriptionFilesCount > 0) {
          ZIO.succeed(lpInvestorGroups.map(_.subscriptionDocAuthorizedTeamId))
        } else if (supportingFormFilesCount > 0) {
          ZIO.succeed(lpInvestorGroups.map(_.supportingDocAuthorizedTeamId))
        } else {
          ZIO.succeed(lpInvestorGroups.map(_.supportingDocAuthorizedTeamId))
        }
      requestId <- fundSubSignatureIntegrationService.createMultiDocTypeSignatureRequest(
        lpId = lpId,
        subscriptionVersionIndex = latestVersionIndex,
        subscriptionFiles = subscriptionFiles,
        supportingFormFiles = supportingFormFiles,
        lpUploadedFiles = lpUploadedFiles,
        signers = fundSubSigners,
        message = message,
        teamIds = (lpModel.teamId ++ fundAuthorizedTeamIds).toSet,
        actor = actorId,
        signatureProviderParams = signatureProviderParams,
        httpContext = httpContextOpt,
        refDocs = refDocs,
        fundSubSignatureDateFormatOpt = fundSubModel.signatureConfig.flatMap(_.dateFormat),
        isLpSigningWithSchwabFlow = isSchwabSigning
      )
      requestBasic <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(requestId)
      _ <-
        if (requestBasic.doneSigning) {
          handleOneEnvelopeRequestComplete(
            lpId = lpId,
            requestBasic = requestBasic,
            actor = actorId,
            entityId = fundSubModel.investorEntity,
            ctx = httpContextOpt
          )
        } else {
          handleRequestOtherSignatures(
            lpId = lpId,
            requestBasic = requestBasic,
            actor = actorId,
            message = message,
            httpContext = httpContextOpt
          )
        }
    } yield CreateOneEnvelopeRequestResp(
      requestId = requestId
    )
  }

  def eSignOneEnvelopeRequest(
    lpId: FundSubLpId,
    requestId: SignatureRequestId,
    signingData: Seq[(FileId, DocumentSignatureMessage)],
    signingAuthData: SigningAuthData,
    actorId: UserId,
    httpContextOpt: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actorId is e-signing one envelope request $requestId of $lpId")
      _ <- fundSubPermissionService.validateFundActive(lpId.parent)
      _ <- ZIOUtils.failWhen(signingData.isEmpty)(GeneralServiceException("Signed data should be non empty"))
      _ <- validateEnvelopeDataBeforeESign(requestId, lpId)
      fundSubModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(lpId.parent)
      )
      _ <- fundSubSignatureIntegrationService.eSignSignatureRequest(
        lpId = lpId,
        requestId = requestId,
        signatures = signingData.toMap,
        actor = actorId,
        httpContext = httpContextOpt,
        signingAuthData = signingAuthData,
        enabledElectronicSeal = fundSubModel.signatureConfig.exists(_.anduinSignSignatureConfig.enabledElectronicSeal)
      )
      signedRequest <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(requestId)
      _ <- fundSubLpActivityLogService.logActivity(
        lpId = lpId,
        actorOpt = Some(actorId),
        detail = SignedOneEnvelopeRequest(
          requestId = signedRequest.requestId,
          fileIds = signedRequest.filePackages.flatMap(_._2.getSignedFileIds).toSeq
        )
      )
      _ <-
        if (signedRequest.doneSigning) {
          handleOneEnvelopeRequestComplete(
            lpId = lpId,
            requestBasic = signedRequest,
            actor = actorId,
            entityId = fundSubModel.investorEntity,
            ctx = httpContextOpt
          )
        } else {
          fundSubLpDashboardService.updateLpInfoRecord(lpId, identity)
        }
    } yield ()
  }

  private def validateEnvelopeDataBeforeESign(
    requestId: SignatureRequestId,
    lpId: FundSubLpId
  ) = {
    for {
      requestMetadata <- fundSubSignatureIntegrationService.getSignatureRequestMetadata(requestId)
      _ <- ZIO.foreach(requestMetadata.envelopeType.innerDocTypes) {
        case DocType.SubscriptionDoc =>
          fundSubPermissionService.validateLpStatus(
            lpId,
            Set(LpStatus.LPRequestedSignature)
          )
        case _ => ZIO.unit
      }
    } yield ()
  }

  def handleOneEnvelopeRequestComplete(
    lpId: FundSubLpId,
    requestBasic: FundSubSignatureRequestBasic,
    actor: UserId,
    entityId: EntityId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    for {
      _ <- distributeOneEnvelopeFilePackagesAfterComplete(requestBasic, lpId, actor, ctx)
      emailsToSigners <- ZIO
        .foreach(requestBasic.signers.filterNot(_.userId == requestBasic.requester)) { signer =>
          val applicableSignatureFileIds = signer.applicableSignatureFileIds.toSet
          val applicableSignedFileIds = requestBasic.filePackages.values
            .flatMap(_.files)
            .filter { file =>
              applicableSignatureFileIds.contains(file.originalSignatureFileId)
            }
            .flatMap(_.signedFileId)
            .toSeq
          fundSubEmailService
            .sendFundSubDoneSigningSignerEmail(
              lpId,
              applicableSignedFileIds,
              signer.userId,
              actor,
              requestBasic.envelopeType,
              requestBasic.requestId
            )
            .map(_.flatMap(_.internalIdOpt))
        }
        .map(_.flatten)
      emailToRequesterOpt <- ZIO
        .when(actor != requestBasic.requester) {
          fundSubEmailService
            .sendFundSubDoneSigningRequesterEmail(
              lpId,
              entityId,
              requestBasic.getAllUnsignedSignatureFileIds,
              requestBasic.requester,
              actor,
              requestBasic.envelopeType,
              requestBasic.requestId
            )
            .map(_.flatMap(_.internalIdOpt))
        }
        .map(_.flatten)
      _ <- ZIO.when(requestBasic.filePackages.exists(_._1.docType == DocType.SubscriptionDoc)) {
        fundSubAuditLogService.addEvent(
          fundSubId = lpId.parent,
          params = AddEventParam(
            actor = Some(actor),
            actorType = AuditLogActorType.InvestorSide,
            eventType = AuditLogEventType.SUBSCRIPTION_DOCUMENT_SIGNED,
            orderId = Some(lpId),
            eventEmail = Seq(
              AddEventEmailParam(
                fundSubEventType = FundSubEvent.signerDoneSignatureRequest,
                emailIds = emailsToSigners
              ),
              AddEventEmailParam(
                fundSubEventType = FundSubEvent.signerDoneSignatureRequestToLp,
                emailIds = emailToRequesterOpt.toSeq
              )
            ),
            activityDetail = SignedOneEnvelopeRequest(
              requestId = requestBasic.requestId,
              fileIds = requestBasic.filePackages.flatMap(_._2.getSignedFileIds).toSeq
            )
          )
        )
      }
    } yield ()
  }

  private def distributeOneEnvelopeFilePackagesAfterComplete(
    requestBasic: FundSubSignatureRequestModels.FundSubSignatureRequestBasic,
    lpId: FundSubLpId,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext] = None
  ) = {
    ZIO.foreachDiscard(requestBasic.filePackages) { case (fileGroupKey, _) =>
      val docType = fileGroupKey.docType
      docType match {
        case FundSubSignatureRequestModels.DocType.SubscriptionDoc =>
          ZIOUtils.traverseOptionUnit(fileGroupKey.subscriptionVersionIdxOpt) { requestSubscriptionVersionIndex =>
            handleSubscriptionDocSignatureComplete(
              lpId,
              requestBasic,
              requestSubscriptionVersionIndex
            )
          }
        case FundSubSignatureRequestModels.DocType.SupportingForm =>
          ZIOUtils.traverseOptionUnit(fileGroupKey.formSubmissionIdOpt) { formSubmissionId =>
            handleSupportingDocSignatureComplete(
              lpId,
              formSubmissionId,
              requestBasic,
              actor,
              ctx
            ).catchAllCause { cause =>
              ZIO.logErrorCause(
                s"Failed to handle supporting doc signature complete for $lpId, formSubmissionId: $formSubmissionId",
                cause
              )
            }
          }
        case FundSubSignatureRequestModels.DocType.LpUploadedDoc =>
          ZIO.unit
        case FundSubSignatureRequestModels.DocType.CountersignDoc |
            FundSubSignatureRequestModels.DocType.GpAdditionalRequestedDoc |
            FundSubSignatureRequestModels.DocType.SideLetterDoc =>
          ZIO.fail(DocTypeNotSupportedException(docType))
      }
    }
  }

  private def handleSubscriptionDocSignatureComplete(
    lpId: FundSubLpId,
    requestBasic: FundSubSignatureRequestBasic,
    requestSubscriptionVersionIndex: Int
  ): Task[Unit] = {
    for {
      lastVersionIndex <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(
        _.getLastVersionIndex(lpId)
      )
      _ <-
        if (lastVersionIndex == requestSubscriptionVersionIndex) {
          for {
            _ <- fundSubSubscriptionDocService.updateSignedDocumentAndAttemptToSubmitPackage(
              lpId,
              requestBasic,
              LpSignatureType.ESignature,
              versionIndex = requestSubscriptionVersionIndex,
              None
            )
          } yield ()
        } else {
          ZIO.fail(
            GeneralServiceException(
              s"Signature request subscription version $requestSubscriptionVersionIndex is outdated, latest version $lastVersionIndex"
            )
          )
        }
    } yield ()
  }

  private def handleSupportingDocSignatureComplete(
    lpId: FundSubLpId,
    formSubmissionId: FormSubmissionId,
    requestBasic: FundSubSignatureRequestBasic,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    for {
      _ <- formSubmissionService.afterFormSubmissionSignatureRequestCompleted(
        formSubmissionId = formSubmissionId,
        formSubmissionEnvelopePackageOpt = requestBasic.getSupportingFormPackage(formSubmissionId, lpId),
        actor = actor,
        sharedFolderIdOpt = None,
        ctx = ctx
      )
      _ <- newSupportingDocService.afterOneEnvelopeSupportingDocSignatureRequestCompleted(
        formSubmissionId = formSubmissionId,
        lpId = lpId,
        requestBasic = requestBasic,
        ctx = ctx
      )
    } yield ()
  }

  private def handleRequestOtherSignatures(
    lpId: FundSubLpId,
    requestBasic: FundSubSignatureRequestBasic,
    actor: UserId,
    message: String,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    for {
      emailIds <- ZIO
        .foreach(requestBasic.signers.filterNot(_.userId == actor)) { recipient =>
          fundSubEmailService.sendFundSubSendSignatureRequestSignerEmail(
            lpId,
            recipient.applicableSignatureFileIds,
            recipient.userId,
            actor,
            message,
            requestBasic.envelopeType,
            requestBasic.requestId
          )
        }
        .map(_.flatten.flatMap(_.internalIdOpt))
      _ <- logEventOneEnvelopeRequestCreated(
        lpId,
        actor,
        requestBasic,
        emailIds
      )
      _ <- ZIO.foreachDiscard(requestBasic.filePackages) { case (fileGroupKey, _) =>
        fileGroupKey.docType match {
          case FundSubSignatureRequestModels.DocType.SubscriptionDoc =>
            fundSubSubscriptionDocService.requestOthersSignaturesForMultiDocTypeEnvelope(
              lpId = lpId,
              actor = actor,
              signerUserIds = requestBasic.getAllSignerUserIds,
              httpContext = httpContext
            )
          case FundSubSignatureRequestModels.DocType.SupportingForm =>
            ZIO.unit
          case FundSubSignatureRequestModels.DocType.LpUploadedDoc =>
            ZIO.unit
          case FundSubSignatureRequestModels.DocType.CountersignDoc |
              FundSubSignatureRequestModels.DocType.GpAdditionalRequestedDoc |
              FundSubSignatureRequestModels.DocType.SideLetterDoc =>
            ZIO.fail(DocTypeNotSupportedException(fileGroupKey.docType))
        }
      }

    } yield ()
  }

  private def validateSubscriptionFormFiles(
    filesFromParams: Seq[FileId],
    lpId: FundSubLpId,
    actorId: UserId
  ): Task[Unit] =
    for {
      version <- fundSubSubscriptionDocService.getSubscriptionVersionBasicInfo(
        GetSubscriptionVersionBasicInfoParams(lpId, SubscriptionVersionIndex.Latest),
        actorId
      )
      formFileIds = version.versionInfoOpt.map(_.formFiles.values.toSet).getOrElse(Set.empty)
      _ <- ZIOUtils.failWhen(filesFromParams.toSet != formFileIds) {
        GeneralServiceException("Signature files do not match form files")
      }
    } yield ()

  def getSignatureRequestInfoWithFilePackages(
    requestId: SignatureRequestId,
    actor: UserId
  ): Task[SignatureRequestInfoWithFilePackages] = {
    for {
      _ <- ZIO.logInfo(s"User $actor gets signature request info with file packages for $requestId")
      requestBasic <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(requestId)
      lpId <- ZIOUtils.uniqueSeqToTask(
        requestBasic.envelopeType.lpIds,
        GeneralServiceException(s"Cannot find lpId from request $requestId")
      )
      fundSubId = lpId.parent
      rebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- FundSubSignaturePermissionUtils.validateActorSignatureRequestAccess(requestBasic.envelopeType, actor)(
        using rebacStoreOperation,
        fundSubPermissionService
      )
      signatureRequestInfo <- getSignatureRequestInfoWithFilePackagesInternal(requestBasic, actor, lpId)
    } yield signatureRequestInfo
  }

  private def getSignatureRequestInfoWithFilePackagesInternal(
    requestBasic: FundSubSignatureRequestBasic,
    actor: UserId,
    lpId: FundSubLpId
  ): Task[SignatureRequestInfoWithFilePackages] = {
    val filePackages = convertToSharedFilePackages(requestBasic)
    for {
      fileNameMap <- fileService.batchGetFileNamesUnsafe(filePackages.flatMap(_.fileIds))
      docSubmission <- newSupportingDocService.getDocSubmission(lpId, actor)
      sharedEnvelopeType <- ZIOUtils.fromOption(
        FundSubSignatureJvmUtils.convertToSharedEnvelopeType(requestBasic.envelopeType),
        GeneralServiceException(s"Cannot convert envelope type ${requestBasic.envelopeType} to shared envelope type")
      )
    } yield {
      SignatureRequestInfoWithFilePackages(
        requestId = requestBasic.requestId,
        filePackages = filePackages,
        envelopeType = sharedEnvelopeType,
        supportingDocTypeMap = docSubmission.formDocTypeMapping,
        fileNameMap = fileNameMap
      )
    }
  }

  def getPendingSubdocSignatureRequestInfoWithFilePackage(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[GetSubdocSignatureRequestInfoWithFilePackagesResp] =
    for {
      _ <- ZIO.logInfo(s"User $actor gets subdoc signature request info with file packages for $lpId")
      rebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- FundSubSignaturePermissionUtils.validateActorAccessOnSingleDocTypeRequest(
        lpId = lpId,
        docType = DocType.SubscriptionDoc,
        actor = actor
      )(
        using rebacStoreOperation,
        fundSubPermissionService
      )
      versionIndexOpt <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(_.getLastVersionIndexOpt(lpId))
      pendingRequestOpt <- ZIOUtils
        .traverseOption(versionIndexOpt) { versionIndex =>
          fundSubSignatureIntegrationService
            .getSubscriptionDocSignatureRequestsMetadata(
              lpId = lpId,
              statuses = Set(FundSubSignatureRequestStatus.InProgress),
              versionIndex = versionIndex
            )
            .map(_.headOption)
        }
        .map(_.flatten)
      requestInfoWithFilePackageOpt <- ZIOUtils.traverseOption(pendingRequestOpt) { requestMetadata =>
        for {
          requestBasic <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasic(requestMetadata)
          requestInfo <- getSignatureRequestInfoWithFilePackagesInternal(requestBasic, actor, lpId)
        } yield requestInfo
      }
    } yield GetSubdocSignatureRequestInfoWithFilePackagesResp(requestInfoWithFilePackageOpt)

  private def convertToSharedFilePackages(requestBasic: FundSubSignatureRequestBasic) = {
    for {
      (fileGroupKey, filePackage) <- requestBasic.filePackages.toSeq
      docType <- fileGroupKey.docType.toOldSignatureRequestTypeOpt
    } yield SignatureFilePackageInfo(
      fileIds = filePackage.files.map(file => file.signedFileId.getOrElse(file.originalSignatureFileId)),
      docType = docType,
      formSubmissionIdOpt = fileGroupKey.formSubmissionIdOpt
    )
  }

  // This api is designed for both multi and single doc type envelope signature request
  def markSignatureRequestAsComplete(
    requestId: SignatureRequestId,
    shouldNotifySignersAndRequester: Boolean,
    actorId: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actorId marks signature request $requestId as complete")
      requestBasic <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(requestId)
      lpId <- ZIOUtils.uniqueSeqToTask(
        requestBasic.envelopeType.lpIds,
        GeneralServiceException(s"Cannot find a single lpId from request $requestId")
      )
      rebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- FundSubSignaturePermissionUtils.validateActorSignatureRequestAccess(requestBasic.envelopeType, actorId)(
        using rebacStoreOperation,
        fundSubPermissionService
      )
      envelopeHasSubdoc = requestBasic.envelopeType.innerDocTypes.contains(DocType.SubscriptionDoc)
      _ <- ZIOUtils.failWhen(!envelopeHasSubdoc) {
        GeneralServiceException(s"Signature request $requestId does not contain subscription doc")
      }
      _ <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(LpStatus.LPRequestedSignature)
      )
      _ <- fundSubPermissionService.validateFundActive(lpId.parent)
      lastSubscriptionVersionIdx <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(
        _.getLastVersionIndex(lpId)
      )
      subdocPackageOpt = requestBasic.getSubdocPackage(lpId, lastSubscriptionVersionIdx)
      signedSubdocFileIds = subdocPackageOpt.map(_.getSignedFileIds).getOrElse(Seq.empty)
      _ <- ZIOUtils.failWhen(signedSubdocFileIds.isEmpty) {
        GeneralServiceException("No signed files found")
      }
      _ <- fundSubSignatureIntegrationService.markRequestComplete(
        requestId = requestBasic.requestId,
        actor = actorId
      )
      updatedRequest <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(requestId)
      _ <- distributeOneEnvelopeFilePackagesAfterComplete(updatedRequest, lpId, actorId, ctx)
      _ <- sendEmailAndAddActivityLogAfterMarkingSignatureRequestComplete(
        actorId,
        updatedRequest,
        lpId,
        shouldNotifySignersAndRequester
      )
    } yield ()
  }

  private def sendEmailAndAddActivityLogAfterMarkingSignatureRequestComplete(
    actorId: UserId,
    requestBasic: FundSubSignatureRequestBasic,
    lpId: FundSubLpId,
    shouldNotifySignersAndRequester: Boolean
  ): Task[Unit] = {
    val requestId = requestBasic.requestId
    for {
      _ <- fundSubLpActivityLogService.logActivity(
        lpId,
        Some(actorId),
        Some(DateCalculator.instantNow),
        MarkSubDocRequestComplete()
      )
      requesterEmailIdOpt <-
        if (shouldNotifySignersAndRequester && actorId != requestBasic.requester) {
          fundSubEmailService
            .sendMarkSubDocRequestCompleteRequesterEmail(
              lpId,
              requestBasic.requester,
              requestId,
              requestBasic.envelopeType,
              requestBasic.getAllUnsignedSignatureFileIds,
              actorId
            )
            .map(_.flatMap(_.internalIdOpt))
        } else {
          ZIO.attempt(None)
        }
      signerEmailIds <-
        ZIO
          .foreach(requestBasic.signers.filter(_.userId != requestBasic.requester && shouldNotifySignersAndRequester)) {
            signer =>
              fundSubEmailService.sendMarkSubDocRequestCompeteSignerEmail(
                lpId,
                signer.userId,
                requestId,
                requestBasic.envelopeType,
                signer.applicableSignatureFileIds,
                actorId
              )
          }
          .map(_.flatMap(_.flatMap(_.internalIdOpt)))
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = lpId.parent,
        params = AddEventParam(
          actor = Some(actorId),
          orderId = Option(lpId),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.SIGNATURE_REQUEST_ON_SUBSCRIPTION_DOCUMENT_MARKED_AS_COMPLETE,
          eventEmail = Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.markSubDocRequestCompleteRequester,
              emailIds = requesterEmailIdOpt.toSeq
            ),
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.markSubDocRequestCompleteSigner,
              emailIds = signerEmailIds
            )
          ),
          activityDetail = MarkSubDocRequestComplete()
        )
      )
    } yield ()
  }

  private def validateDocusignParams(
    docusignParams: DocusignESignatureOptionParams,
    signers: Seq[CreateRequestSignerData]
  ): Task[Unit] = {
    val isValidDocumentVisibilityParams =
      docusignParams.signersCanAccessSignatureRequiredDocsOnly == signers.forall(_.canAccessSignatureRequiredDocOnly)
    ZIOUtils.failWhen(!isValidDocumentVisibilityParams) {
      GeneralServiceException("Docusign document visibility is not set up correctly")
    }
  }

  private def logEventOneEnvelopeRequestCreated(
    lpId: FundSubLpId,
    actor: UserId,
    requestBasic: FundSubSignatureRequestBasic,
    emailIds: Seq[InternalEmailId]
  ) = {
    val requestId = requestBasic.requestId
    val signerUserIds = requestBasic.getAllSignerUserIds
    val supportingFormFileIds = requestBasic.filePackages.flatMap { case (fileGroupKey, filePackage) =>
      fileGroupKey.formSubmissionIdOpt.map(_ -> filePackage.getUnsignedSignatureFileIds)
    }.toSeq
    val uploadedFileIds =
      requestBasic.getLpUploadedDocsPackage(lpId).map(_.getUnsignedSignatureFileIds).getOrElse(Seq.empty)
    val subscriptionFileIds = requestBasic.filePackages
      .flatMap { case (fileGroupKey, filePackage) =>
        Option.when(fileGroupKey.docType == DocType.SubscriptionDoc) {
          filePackage.getUnsignedSignatureFileIds
        }
      }
      .flatten
      .toSeq
    for {
      docSubmission <- newSupportingDocService.getDocSubmission(lpId, actor)
      supportingFormFilesWithDocType <- ZIO.foreach(supportingFormFileIds) { case (formSubmissionId, fileIds) =>
        SupportingDocUtils
          .getFilesGroupName(
            files = fileIds,
            defaultName = docSubmission.formDocTypeMapping.getOrElse(formSubmissionId, ""),
            actor = actor
          )(fileService)
          .map { groupName =>
            OneEnvelopeSupportingFormFiles(
              docType = groupName,
              fileIds = fileIds
            )
          }
      }
      lpActivityDetail = CreateOneEnvelopeRequest(
        requestId = requestId,
        signerUserIds = signerUserIds,
        subscriptionFileIds = subscriptionFileIds,
        supportingFormFiles = supportingFormFilesWithDocType,
        uploadedFileIds = uploadedFileIds
      )
      _ <- fundSubLpActivityLogService.logActivity(
        lpId = lpId,
        actorOpt = Option(actor),
        detail = lpActivityDetail
      )
      (auditLogEventType, emailEventType) = requestBasic.envelopeType.primaryDocType match {
        case DocType.SubscriptionDoc =>
          (
            AuditLogEventType.SIGNATURE_REQUEST_OF_SUBSCRIPTION_DOCUMENT_SENT,
            FundSubEvent.lpSendSignatureRequest
          )
        case DocType.SupportingForm | DocType.LpUploadedDoc =>
          (
            AuditLogEventType.SIGNATURE_REQUEST_ON_ADDITIONAL_DOCUMENT_SENT,
            FundSubEvent.sendFormSubmissionSignatureRequest
          )
        case _ => throw DocTypeNotSupportedException(requestBasic.envelopeType.primaryDocType)
      }
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = lpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.InvestorSide,
          eventType = auditLogEventType,
          orderId = Some(lpId),
          eventEmail = Seq(
            AddEventEmailParam(
              fundSubEventType = emailEventType,
              emailIds = emailIds
            )
          ),
          activityDetail = lpActivityDetail
        )
      )
    } yield ()
  }

}

object FundSubOneEnvelopeSignatureRequestService {

  case class DocTypeNotSupportedException(
    docType: FundSubSignatureRequestModels.DocType
  ) extends ServiceException {
    override def message: String = s"DocType $docType is not supported in one envelope signature request"
  }

}
