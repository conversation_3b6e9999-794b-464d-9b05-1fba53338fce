// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.signature.oneenvelope

import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.fdb.record.FDBCluster
import anduin.forms.service.FormService
import anduin.fundsub.endpoint.signature.oneenvelope.{FormTypeForOneEnvelope, OneEnvelopeFormInfo, OneEnvelopeFormStatus}
import anduin.fundsub.endpoint.subscriptiondoc.GetSubscriptionDocSigningTypeParams
import anduin.fundsub.service.NewSupportingDocService
import anduin.fundsub.submission.version.SubmissionVersionModel
import anduin.fundsub.submission.version.signature.{
  SignatureBlockByReview,
  SignatureCompleted,
  SignatureRequestPending,
  SignatureRequired,
  SignatureReused,
  SignatureStatus
}
import anduin.fundsub.subscriptiondoc.{FundSubSubscriptionDocService, FundSubSubscriptionFetchingUtils}
import anduin.fundsub.supportingdoc.SupportingDocumentSharedUtils
import anduin.model.common.user.UserId
import anduin.protobuf.fundsub.FeatureSwitch
import anduin.protobuf.fundsub.models.lp.FundSubLpModel
import com.anduin.stargazer.service.utils.ZIOUtils

object FundSubOneEnvelopeServiceUtils {

  def getSubscriptionFormInfoForOneEnvelope(
    fundSubLpModel: FundSubLpModel,
    featureSwitchOpt: Option[FeatureSwitch],
    submissionVersionModel: SubmissionVersionModel,
    actorId: UserId
  )(
    formService: FormService,
    fundSubSubscriptionDocService: FundSubSubscriptionDocService
  ): Task[Option[OneEnvelopeFormInfo]] = {
    val lpId = fundSubLpModel.fundSubLpId
    val allowIncompleteFormSubmission = fundSubLpModel.formSetting.exists(_.allowIncompleteForm)
    val subscriptionFormVersionId = submissionVersionModel.lpFormId.asNewLpFormIdUnsafe().parent
    for {
      subscriptionFormName <- formService
        .getFormNames(
          Seq(subscriptionFormVersionId.parent)
        )(
          using FDBCluster.Default
        )
        .map(_.headOption.map(_._2).getOrElse("Form"))
      subdocRequiredWetSign <- fundSubSubscriptionDocService
        .getSubscriptionDocSigningType(
          GetSubscriptionDocSigningTypeParams(
            lpId = lpId,
            versionIndex = submissionVersionModel.versionIndex
          ),
          actorId
        )
        .map(_.requireWetSign)
      eSignSectionHidden = featureSwitchOpt.exists(_.hideFormFilesAndSigningButtons)
      statusForOneEnvelope =
        if (subdocRequiredWetSign || eSignSectionHidden) {
          None
        } else {
          getSubscriptionFormStatusFromVersionModel(
            submissionVersionModel,
            allowIncompleteFormSubmission
          )
        }
    } yield statusForOneEnvelope.map { status =>
      OneEnvelopeFormInfo(
        status = status,
        formName = subscriptionFormName,
        formType = FormTypeForOneEnvelope.SubscriptionForm,
        supportingDocType = None
      )
    }
  }

  private def getSubscriptionFormStatusFromVersionModel(
    submissionVersionModel: SubmissionVersionModel,
    allowIncompleteFormSubmission: Boolean
  ): Option[OneEnvelopeFormStatus] = {
    submissionVersionModel.signatureStatus match {
      case _: SignatureCompleted | _: SignatureReused | _: SignatureRequestPending =>
        None
      case _: SignatureBlockByReview => Some(OneEnvelopeFormStatus.PendingSoftReview)
      case _: SignatureRequired =>
        if (submissionVersionModel.formProgress.exists(_.missingRequiredFields == 0) || allowIncompleteFormSubmission) {
          Some(OneEnvelopeFormStatus.ReadyForSignature)
        } else {
          Some(OneEnvelopeFormStatus.InProgress)
        }
      case SignatureStatus.Empty =>
        Some(OneEnvelopeFormStatus.InProgress)
    }
  }

  def getSupportingFormInfosForOneEnvelope(
    fundSubLpModel: FundSubLpModel,
    featureSwitchOpt: Option[FeatureSwitch],
    submissionVersionModelOpt: Option[SubmissionVersionModel],
    actorId: UserId
  )(
    userProfileService: UserProfileService,
    newSupportingDocService: NewSupportingDocService
  ): Task[Seq[OneEnvelopeFormInfo]] = {
    val lpId = fundSubLpModel.fundSubLpId
    val enableTaxFormESignature =
      featureSwitchOpt.forall(!_.disableInvestorTaxFormElectronicSignature)
    if (!enableTaxFormESignature) {
      ZIO.succeed(Seq.empty)
    } else {
      for {
        subscriptionVersionBasicInfoOpt <- ZIOUtils.traverseOption(submissionVersionModelOpt) {
          submissionVersionModel =>
            FundSubSubscriptionFetchingUtils.convertToSubscriptionVersionBasicInfo(
              submissionVersionModel
            )(
              using userProfileService
            )
        }
        shouldShowFormSupportingDoc = subscriptionVersionBasicInfoOpt.exists { subscriptionVersionBasicInfo =>
          fundSubLpModel.lpState.map(_.getLpStatus).exists { lpStatus =>
            SupportingDocumentSharedUtils.shouldShowFormSupportingDoc(
              lpStatus = lpStatus,
              formProgress = subscriptionVersionBasicInfo.formProgress,
              reviewStatus = subscriptionVersionBasicInfo.reviewStatus,
              enableAmlKycListAfterRequiredFieldsCompleted =
                featureSwitchOpt.exists(_.enableAmlKycListAfterRequiredFieldsCompleted)
            )
          }
        }
        supportingFormInfos <- newSupportingDocService.getSupportingDocPendingSignatureData(
          lpId,
          actorId,
          shouldShowFormSupportingDoc
        )
      } yield {
        val inProgressFormSubmissionInfos = supportingFormInfos.inProgressFormSubmissionInfos.map {
          formSubmissionPendingSignature =>
            OneEnvelopeFormInfo(
              status = if (formSubmissionPendingSignature.readyForSignature) {
                OneEnvelopeFormStatus.ReadyForSignature
              } else {
                OneEnvelopeFormStatus.InProgress
              },
              formName = formSubmissionPendingSignature.formName,
              formType = FormTypeForOneEnvelope.SupportingForm(formSubmissionPendingSignature.formSubmissionId),
              supportingDocType = formSubmissionPendingSignature.docTypeOpt
            )
        }
        val notStartedSupportingFormInfos = supportingFormInfos.supportingFormsNotStarted.map { docType =>
          OneEnvelopeFormInfo(
            status = OneEnvelopeFormStatus.NotStarted,
            formName = "",
            formType = FormTypeForOneEnvelope.SupportingFormNotStarted(docType),
            supportingDocType = Some(docType)
          )
        }
        inProgressFormSubmissionInfos ++ notStartedSupportingFormInfos
      }
    }
  }

}
