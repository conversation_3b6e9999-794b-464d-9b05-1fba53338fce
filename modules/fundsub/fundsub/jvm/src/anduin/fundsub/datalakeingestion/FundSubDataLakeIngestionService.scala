// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion

import anduin.evendim.client.{EvendimAdminClient, EvendimClient}
import anduin.fdb.record.FDBRecordDatabase
import anduin.fundsub.datalakeingestion.handler.*
import anduin.fundsub.datalakeingestion.model.*
import anduin.fundsub.models.{FundSubLpModelStoreOperations, FundSubModelStoreOperations}
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.kafka.{KafkaAsyncExecutor, KafkaFiber, KafkaService, Topic}
import anduin.model.notichannel.FundSubNotificationChannels
import anduin.portaluser.ExecutiveAdmin
import com.anduin.stargazer.service.actionlogger.{
  ActionEventFailedDgraphMutationParams,
  ActionLoggerService,
  LogActionEventParams
}
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.service.{GondorBackendConfig, GondorConfig}
import com.anduin.stargazer.util.date.DateCalculator
import fundsub.webhook.WebhookPayload
import zio.implicits.*
import zio.{Task, UIO, ZIO}

import java.time.Instant
import scala.annotation.unused
import scala.util.Random
import anduin.fundsub.webhook.WebhookKafkaTopic

private[datalakeingestion] final case class FundAndLpIdInfo(
  fundIdOpt: Option[FundSubId] = None,
  lpIdOpt: Option[FundSubLpId] = None
)

final case class FundSubDataLakeIngestionService(
  gondorConfig: GondorConfig,
  actionLoggerService: ActionLoggerService,
  executiveAdmin: ExecutiveAdmin,
  kafkaService: KafkaService,
  natsNotificationService: NatsNotificationService
)(
  using val evendimClient: EvendimClient
) extends KafkaFiber {

  protected def backendConfig: GondorBackendConfig = gondorConfig.backendConfig

  private val dataIngestionTopic: Topic[String, FundSubDataLakeIngestionParams] =
    Topic[String, FundSubDataLakeIngestionParams](
      backendConfig.dataLakeConfig.fundSubDataLakeIngestionTopic
    )

  private val dataIngestAsyncExecutor = KafkaAsyncExecutor[String, FundSubDataLakeIngestionParams](
    kafkaService = kafkaService,
    topic = dataIngestionTopic,
    handler = updateDataHandler,
    errorHandlerOpt = Some(onUpdateDataError),
    retryOpt = Some(KafkaService.RetryOptions.default),
    onSendMessage = { message =>
      ZIO.logInfo(
        s"Send message ${message.value.getClass.getSimpleName} in lpOpt ${message.value.lpIdOpt} to Data Ingest Kafka handler"
      )
    }
  )

  given webhookPayloadAsyncExecutor: KafkaAsyncExecutor[FundSubId, WebhookPayload] =
    KafkaAsyncExecutor[FundSubId, WebhookPayload](
      kafkaService = kafkaService,
      topic = WebhookKafkaTopic.instance,
      handler = (_, _) => ZIO.unit,
      retryOpt = Some(KafkaService.RetryOptions.default),
      onSendMessage = { message =>
        ZIO.logInfo(
          s"Send message ${message.value.getClass.getSimpleName} in fund ${message.key} to Webhook Kafka handler"
        )
      }
    )

  private def updateDashboardTracking(lpId: FundSubLpId) = {
    for {
      isExist <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(
        _.getOptFundSubLpModel(lpId).map(_.flatMap(_.lpState).exists(!_.isRemoved))
      )
      _ <- ZIO.when(isExist) {
        natsNotificationService.publish(lpId, FundSubNotificationChannels.fundSubAdvancedDashboard(lpId.parent))
      }
    } yield ()
  }

  private def updateDataHandlerExecution[P <: FundSubDataLakeIngestionParams](
    params: P
  )(
    handler: DataLakeIngestionHandler[P]
  ): Task[Unit] = {
    for {
      now <- ZIO.attempt(DateCalculator.instantNow)
      _ <- handler.execute(params, Option(now))
      _ <- params.lpIdOpt.fold[Task[Unit]] {
        ZIO.unit
      } { lpId =>
        updateDashboardTracking(lpId)
      }
    } yield ()
  }

  private def extractFundAndLpId(params: FundSubDataLakeIngestionParams) = {
    params match {
      case FundSubDataLakeIngestionParams.Empty => FundAndLpIdInfo()
      case nonEmpty: FundSubDataLakeIngestionParams.NonEmpty =>
        nonEmpty match {
          case p: AddOrUpdateFundParams =>
            FundAndLpIdInfo(
              fundIdOpt = Option(p.id)
            )
          case p: UpdateOrAddCloseParams =>
            FundAndLpIdInfo(
              fundIdOpt = Option(p.id.parent)
            )
          case p: RemoveCloseParams =>
            FundAndLpIdInfo(
              fundIdOpt = Option(p.id.parent)
            )
          case p: UpdateOrAddTagParams =>
            FundAndLpIdInfo(
              fundIdOpt = Option(p.id.parent)
            )
          case p: RemoveTagParams =>
            FundAndLpIdInfo(
              fundIdOpt = Option(p.id.parent)
            )
          case p: RemoveCustomDataColumnParams =>
            FundAndLpIdInfo(
              fundIdOpt = Option(p.customDataColumnId.parent)
            )
          case _: UpdateOrAddTemplateParams =>
            FundAndLpIdInfo()
          case _: AddOrderParams | _: LogViewOrderEventParams | _: UpdateCommitmentAmountParams |
              _: UpdateCollaboratorsParams | _: UpdateFormDataParams | _: UpdateOrAddCustomDataParams |
              _: UpdateOrderBasicInfoParams | _: UpdateOrAddOrderSignatureRequestsParams | _: UpdateRequiredDocsParams |
              _: PromoteCollaboratorParams | _: UpdateOrAddAmlKycReviewParams | _: UpdateProvidedDocsParams |
              _: UpdateUserInfoParams | _: UpdateOrAddSubdocDataExtractRequestParams | _: UpdateOrAddAmlCheckParams |
              _: RemoveAmlCheckParams =>
            FundAndLpIdInfo(
              fundIdOpt = nonEmpty.lpIdOpt.map(_.parent),
              lpIdOpt = nonEmpty.lpIdOpt
            )
          case p: UpdateOrAddAdvisorGroupParams =>
            FundAndLpIdInfo(
              fundIdOpt = Option(p.id.parent)
            )
          case p: RemoveAdvisorGroupParams =>
            FundAndLpIdInfo(
              fundIdOpt = Option(p.id.parent)
            )
          case p: UpdateOrAddInvestorGroupParams =>
            FundAndLpIdInfo(
              fundIdOpt = Option(p.id.parent)
            )
          case p: RemoveInvestorGroupParams =>
            FundAndLpIdInfo(
              fundIdOpt = Option(p.id.parent)
            )
        }

    }
  }

  private def updateDataHandler(
    @unused key: String,
    params: FundSubDataLakeIngestionParams
  ): Task[Unit] = {

    params match {
      case FundSubDataLakeIngestionParams.Empty              => ZIO.unit
      case nonEmpty: FundSubDataLakeIngestionParams.NonEmpty =>
        // we need this to be able to take advantage of Scala sealed trait exhaustive match
        nonEmpty match {
          case p: AddOrUpdateFundParams        => updateDataHandlerExecution(p)(UpdateOrAddFundHandler)
          case p: AddOrderParams               => updateDataHandlerExecution(p)(AddOrderHandler)
          case p: LogViewOrderEventParams      => updateDataHandlerExecution(p)(LogViewOrderEventHandler)
          case p: UpdateCommitmentAmountParams => updateDataHandlerExecution(p)(UpdateCommitmentAmountHandler)
          case p: UpdateOrAddCloseParams       => updateDataHandlerExecution(p)(UpdateOrAddCloseHandler)
          case p: RemoveCloseParams            => updateDataHandlerExecution(p)(RemoveCloseHandler)
          case p: UpdateOrAddTagParams         => updateDataHandlerExecution(p)(UpdateOrAddTagHandler)
          case p: UpdateCollaboratorsParams    => updateDataHandlerExecution(p)(UpdateCollaboratorsHandler)
          case p: RemoveTagParams              => updateDataHandlerExecution(p)(RemoveTagHandler)
          case p: UpdateFormDataParams         => updateDataHandlerExecution(p)(UpdateFormDataHandler)
          case p: UpdateOrAddCustomDataParams  => updateDataHandlerExecution(p)(UpdateOrAddCustomDataHandler)
          case p: UpdateOrderBasicInfoParams   => updateDataHandlerExecution(p)(UpdateOrderBasicInfoHandler)
          case p: UpdateRequiredDocsParams     => updateDataHandlerExecution(p)(UpdateRequiredDocsHandler)
          case p: UpdateOrAddOrderSignatureRequestsParams =>
            updateDataHandlerExecution(p)(UpdateOrAddOrderSignatureRequestsHandler)
          case p: RemoveCustomDataColumnParams  => updateDataHandlerExecution(p)(RemoveCustomDataColumnHandler)
          case p: UpdateOrAddTemplateParams     => updateDataHandlerExecution(p)(UpdateOrAddTemplateHandler)
          case p: PromoteCollaboratorParams     => updateDataHandlerExecution(p)(CollaboratorPromotionHandler)
          case p: UpdateOrAddAmlKycReviewParams => updateDataHandlerExecution(p)(UpdateOrAddAmlKycReviewHandler)
          case p: UpdateProvidedDocsParams      => updateDataHandlerExecution(p)(UpdateProvidedDocsHandler)
          case p: UpdateUserInfoParams          => updateDataHandlerExecution(p)(UpdateUserInfoHandler)
          case p: UpdateOrAddSubdocDataExtractRequestParams =>
            updateDataHandlerExecution(p)(UpdateOrAddSubdocDataExtractRequestHandler)
          case p: UpdateOrAddAmlCheckParams =>
            updateDataHandlerExecution(p)(UpdateOrAddAmlCheckHandler)
          case p: RemoveAmlCheckParams =>
            updateDataHandlerExecution(p)(RemoveAmlCheckHandler)
          case p: UpdateOrAddAdvisorGroupParams  => updateDataHandlerExecution(p)(UpdateOrAddAdvisorGroupHandler)
          case p: RemoveAdvisorGroupParams       => updateDataHandlerExecution(p)(RemoveAdvisorGroupHandler)
          case p: UpdateOrAddInvestorGroupParams => updateDataHandlerExecution(p)(UpdateOrAddInvestorGroupHandler)
          case p: RemoveInvestorGroupParams      => updateDataHandlerExecution(p)(RemoveInvestorGroupHandler)
        }
    }

  }

  private def onUpdateDataError(
    @unused key: String,
    params: FundSubDataLakeIngestionParams,
    error: Throwable
  ): Task[Unit] = {
    for {
      _ <- ZIO.logError(
        s"Failed to execute mutation for $params"
      )
      fundAndLpId = extractFundAndLpId(params)
      admin <- executiveAdmin.userId
      _ <- actionLoggerService.logNewActionEvent(
        actor = admin,
        params = LogActionEventParams(
          ActionEventFailedDgraphMutationParams(
            fundSubIdOpt = fundAndLpId.fundIdOpt,
            fundsubLpIdOpt = fundAndLpId.lpIdOpt,
            mutation = params.getClass.getSimpleName,
            error = error.getMessage
          )
        ),
        httpRequestContextOpt = None
      )
    } yield ()
  }

  def sendNewUpdate(params: FundSubDataLakeIngestionParams): Task[Unit] = {
    for {
      _ <- dataIngestAsyncExecutor
        .send(dataIngestionTopic.message(Random.alphanumeric.take(32).mkString, params))
        .map(_ => ())
      fundIdOpt = params.lpIdOpt.map(_.parent)
      _ <- ZIOUtils.traverseOption(fundIdOpt) { fundId =>
        FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
          ops.updateFundSubPublicModel(fundId)(
            _.withLastDashboardMutationAt(
              Instant.now()
            )
          )
        }
      }
    } yield ()
  }

  override def start(): zio.Task[Unit] = {
    dataIngestAsyncExecutor.start()
  }

  override def close(): UIO[Unit] = {
    dataIngestAsyncExecutor.close()
  }

  def updateDgraphSchema(evendimAdminClient: EvendimAdminClient): Task[Unit] = {
    for {
      serverSchema <- evendimAdminClient
        .queryGeneratedSchema()
        .map(_.data.getGQLSchema.map(_.schema).getOrElse(""))
      inputSchema = evendimAdminClient.getSchemaString
      _ <-
        if (serverSchema != inputSchema) {
          for {
            _ <- ZIO.logInfo("Updating Dgraph Schema")
            _ <- evendimAdminClient
              .updateSchema()
              .onErrorHandleWith { ex =>
                ZIO.logInfo(s"Failed to update Dgraph schema: $ex")
              }
          } yield ()
        } else {
          ZIO.logInfo("There is no update for Dgraph schema")
        }
    } yield ()
  }

}
