// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import java.time.Instant

import fundsub.webhook.*
import zio.{Task, ZIO}

import anduin.dashboard.query.CustomDataQuery
import anduin.evendim.client.EvendimClient
import anduin.evendim.model.datalake.*
import anduin.fundsub.datalakeingestion.model.UpdateOrAddCustomDataParams
import anduin.fundsub.datalakeingestion.utils.DataConversionUtils
import anduin.fundsub.webhook.WebhookKafkaTopic
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.kafka.KafkaAsyncExecutor
import anduin.protobuf.fundsub.models.customdata.item.{CustomData as CustomDataProto, *}
import com.anduin.stargazer.service.utils.ZIOUtils

object UpdateOrAddCustomDataHandler extends DataLakeIngestionHandler[UpdateOrAddCustomDataParams] {

  private def getCustomDataQuery(params: UpdateOrAddCustomDataParams) = {
    val lpIdStr = params.lpIdOpt.map(_.idString).getOrElse("")
    val customDataColumnIdFilter = StringHashFilter(eq = Option(params.customDataColumnId.idString))
    Query
      .getOrder(id = lpIdStr)(
        Order.customData(filter = Option(CustomDataFilter(dataColumnId = Option(customDataColumnIdFilter))))(
          CustomData.id ~
            CustomData.customValue()(CustomDataQuery.queryCustomValueObj).map(_.getOrElse(CustomDataProto.Empty))
        )
      )
      .map(_.flatMap(_.headOption))
  }

  private def addCustomDataMutation(params: UpdateOrAddCustomDataParams) = {
    Mutation
      .addCustomData(
        DataConversionUtils
          .toCustomValueTypeRef(params.customData)
          .map { customValueTypeRef =>
            AddCustomDataInput(
              order = Some(OrderRef(id = params.lpIdOpt.map(_.idString))),
              dataColumnId = params.customDataColumnId.idString,
              columnName = Option(params.columnName),
              customValue = customValueTypeRef,
              lastEditedByOpt = params.creator.map(DataConversionUtils.toUserRef),
              lastEditedAtOpt = params.createdAt.map(DataConversionUtils.toEvendimDateTime)
            )
          }
          .toList
      )(
        AddCustomDataPayload.numUids
      )
  }

  private def updateCustomDataMutation(params: UpdateOrAddCustomDataParams, customDataId: String) = {
    Mutation
      .updateCustomData(
        UpdateCustomDataInput(
          filter = CustomDataFilter(id = Option(List(customDataId))),
          set = DataConversionUtils
            .toCustomValueTypeRef(params.customData)
            .map { customValue =>
              CustomDataPatch(
                customValue = Option(customValue),
                lastEditedByOpt = params.creator.map(DataConversionUtils.toUserRef),
                lastEditedAtOpt = params.createdAt.map(DataConversionUtils.toEvendimDateTime)
              )
            }
        )
      )(
        UpdateCustomDataPayload.numUids
      )
  }

  private def updateOrderLastUpdatedAt(lpIdOpt: Option[FundSubLpId], now: Option[Instant]) = {
    Mutation.updateOrder(
      UpdateOrderInput(
        filter = OrderFilter(
          id = Option(StringHashFilter(eq = lpIdOpt.map(_.idString)))
        ),
        set = Option(
          OrderPatch(
            lastUpdatedAt = now.map(DataConversionUtils.toEvendimDateTime)
          )
        )
      )
    )(
      UpdateOrderPayload.numUids
    )
  }

  override def execute(
    params: UpdateOrAddCustomDataParams,
    now: Option[Instant]
  )(
    using evendimClient: EvendimClient,
    webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    for {
      (oldCustomDataIdOpt, oldCustomDataOpt) <- evendimClient.query(getCustomDataQuery(params)).map(_.unzip)
      mutations = oldCustomDataIdOpt
        .map { customDataId =>
          updateCustomDataMutation(params, customDataId) ~ updateOrderLastUpdatedAt(params.lpIdOpt, now)
        }
        .getOrElse {
          addCustomDataMutation(params) ~ updateOrderLastUpdatedAt(params.lpIdOpt, now)
        }
      _ <- evendimClient.mutation(mutations)
      _ <- ZIOUtils.traverseOption(params.lpIdOpt) { lpId =>
        val oldValue = toWebhookCustomDataType(oldCustomDataOpt.getOrElse(CustomDataProto.Empty))
        val newValue = toWebhookCustomDataType(params.customData)
        ZIO.when(newValue != oldValue && !newValue.isEmpty) {
          webhookPayloadQueue.send(
            WebhookKafkaTopic.instance.message(
              lpId.parent,
              FundOrderCustomColumnValueUpdatedPayload(
                lpIdOpt = params.lpIdOpt,
                columnName = params.columnName,
                columnId = params.customDataColumnId,
                oldValue = oldValue,
                newValue = newValue
              )
            )
          )
        }
      }
    } yield ()
  }

  private def toWebhookCustomDataType(
    customData: CustomDataProto
  ): OrderCustomDataType = {
    customData match {
      case CustomDataProto.Empty                => OrderCustomDataType.Empty
      case DateTimeValue(value, _)              => DateTimeType(value)
      case CurrencyValue(amount, currency, _)   => CurrencyType(currency, amount)
      case SingleStringValue(valueWithColor, _) => StringType(valueWithColor.headOption.map(_.content).getOrElse(""))
      case MultipleStringValue(valueWithColor, _) =>
        StringArrayType(valueWithColor.map(_.content))
      case _: StringValue           => OrderCustomDataType.Empty
      case ChecklistValue(value, _) => StringArrayType(value)
      case MetadataValue(value, _)  => StringType(value)
    }
  }

}
