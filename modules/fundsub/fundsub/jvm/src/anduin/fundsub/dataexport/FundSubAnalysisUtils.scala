// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.dataexport

import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.dashboard.data.*
import anduin.dashboard.model.*
import anduin.fdb.record.{FDBCluster, FDBRecordDatabase}
import anduin.fundsub.commitment.FundSubCommitmentUtils
import anduin.fundsub.constants.Terms
import anduin.fundsub.endpoint.admin.LpFilter
import anduin.fundsub.endpoint.fundclose.FundSubCloseInfo
import anduin.fundsub.fundclose.FundSubCloseService
import anduin.fundsub.models.{FundSubLpModelStoreOperations, FundSubModelStoreOperations}
import anduin.fundsub.service.{FundSubLpTagUtilService, FundSubPermissionUtils}
import anduin.fundsub.status.LpStatusSharedUtils
import anduin.fundsub.utils.FundSubLpUtils
import anduin.id.fundsub.*
import anduin.model.common.user.UserId
import anduin.protobuf.external.squants.CurrencyMessage
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpInfoRecord
import anduin.protobuf.fundsub.models.InvestmentFundModel
import anduin.rebac.RebacStoreOperation
import anduin.util.CurrencyUtils
import anduin.utils.DateTimeUtils

object FundSubAnalysisUtils {

  def lpContactFileHeader(enabledCustomId: Boolean, allTagColumnsOpt: Option[List[String]] = None): List[String] = {
    val allTagColumns = allTagColumnsOpt.getOrElse(List.empty)
    List(
      List(Terms.InvesteeEntityLabelCamelCase),
      if (enabledCustomId) List("Custom ID") else List.empty,
      List("First Name"),
      List("Last Name"),
      List("Email"),
      List("Collaborator"),
      if (allTagColumnsOpt.isEmpty) List("Tags") else List.empty,
      List("Close"),
      List("Status"),
      List("Amount"),
      List("Notes"),
      if (allTagColumnsOpt.nonEmpty) allTagColumns else List.empty
    ).flatten
  }

  final case class LpContact(
    lpId: FundSubLpId,
    firmName: String,
    customId: String,
    lpFirstName: String,
    lpLastName: String,
    lpEmail: String,
    collaborators: List[(String, String, String)],
    tags: List[String],
    closeName: String,
    status: String,
    amount: String,
    notes: String
  ) derives CanEqual {

    def toRow(enabledCustomId: Boolean, allTagColumnsOpt: Option[List[String]] = None): List[String] = {
      val allTagColumns = allTagColumnsOpt.getOrElse(List.empty)

      List(
        List(firmName),
        if (enabledCustomId) List(customId) else List.empty,
        List(lpFirstName),
        List(lpLastName),
        List(lpEmail),
        List(
          collaborators
            .map { case (fistName, lastName, email) =>
              s"$fistName, $lastName <$email>"
            }
            .mkString("\n")
        ),
        if (allTagColumnsOpt.isEmpty) List(tags.mkString("; ")) else List.empty,
        List(closeName),
        List(status),
        List(amount),
        List(notes),
        if (allTagColumnsOpt.nonEmpty) {
          allTagColumns.map { tagColumnHeader =>
            if (tags.contains(tagColumnHeader)) "X" else ""
          }
        } else {
          List.empty
        }
      ).flatten
    }

  }

  private def lpRecordToContactTask(
    fundSubId: FundSubId,
    lpRecord: LpInfoRecord,
    tagNamesMap: Map[FundSubLpTagId, String],
    closeInfo: Seq[FundSubCloseInfo]
  )(
    using userProfileService: UserProfileService
  ) = {
    for {
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(fundSubId)
      }
      lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.getFundSubLpModel(lpRecord.lpId)
      }
      collaboratorInfos <- ZIO.foreach(lpModel.collaborators) { userId =>
        userProfileService.getUserInfo(userId)
      }
    } yield {
      val closeNameOpt = lpRecord.fundSubCloseIdOpt
        .flatMap(closeId => closeInfo.find(_.fundSubCloseId == closeId))
        .map(_.name)
      val commitmentAmountOpt = for {
        (investmentFundId, lpCommitment) <- lpRecord.commitments.headOption
        commitment <- FundSubCommitmentUtils.getInUseLpCommitment(lpCommitment).commitment
        investmentFund <- adminResModel.investmentFunds.find(_.fundId == investmentFundId)
      } yield s"${CurrencyUtils.getCurrencySymbol(investmentFund.currency)}${commitment.value}"
      LpContact(
        lpId = lpRecord.lpId,
        firmName = lpRecord.firmName,
        customId = lpRecord.customId,
        lpFirstName = lpRecord.firstName,
        lpLastName = lpRecord.lastName,
        lpEmail = lpRecord.email,
        status = LpStatusSharedUtils.getStatusName(lpRecord.status),
        collaborators = collaboratorInfos.map(info => (info.firstName, info.lastName, info.emailAddressStr)).toList,
        closeName = closeNameOpt.getOrElse(""),
        tags = lpRecord.tags.flatMap(tagNamesMap.get).toList,
        amount = commitmentAmountOpt.getOrElse(""),
        notes = lpRecord.lpNotes.map(_.notes).getOrElse("")
      )
    }
  }

  def getLpContacts(
    fundSubId: FundSubId,
    lpFilter: Either[Seq[FundSubLpId], LpFilter],
    actor: UserId
  )(
    using rebacStore: RebacStoreOperation,
    userProfileService: UserProfileService,
    fundSubLpTagUtilService: FundSubLpTagUtilService,
    fundSubCloseService: FundSubCloseService,
    fdbCluster: FDBCluster
  ): Task[List[LpContact]] = {
    for {
      accessibleLpIds <- FundSubPermissionUtils.getAccessibleLpIdsR(
        fundSubId,
        actor
      )
      tags <- fundSubLpTagUtilService.getTagByFundSubId(fundSubId)
      tagNamesMap = tags.map { tag =>
        tag.id -> tag.name
      }.toMap
      closeData <- fundSubCloseService.getFundSubCloseDataInternal(fundSubId)
      lpRecords <- FundSubLpUtils
        .getLpInfoRecordsFromLpFilterForFundManagerR(fundSubId, lpFilter, actor)
        .map(_.filter { lpRecord => accessibleLpIds.contains(lpRecord.lpId) })
      lpContacts <-
        ZIO
          .foreach(lpRecords) { lpRecord =>
            lpRecordToContactTask(
              fundSubId,
              lpRecord,
              tagNamesMap,
              closeData.closeInfo
            )
          }
          .map(_.toList)
    } yield lpContacts
  }

  def buildFileDataFromLpContactList(
    lpContacts: Seq[LpContact],
    enabledCustomId: Boolean,
    exportTagsAsColumns: Boolean = false
  ): List[List[String]] = {
    val allTagColumnsOpt = if (exportTagsAsColumns) {
      Option(lpContacts.flatMap(_.tags).distinct.sorted.toList)
    } else {
      None
    }

    List(lpContactFileHeader(enabledCustomId, allTagColumnsOpt)) ++
      lpContacts.map(_.toRow(enabledCustomId, allTagColumnsOpt)).toList
  }

  def buildAdvancedDashboardLpContactList(
    columns: Seq[DashboardColumn],
    rows: Seq[DashboardRow],
    columnHeaders: Seq[ColumnHeader],
    investmentFunds: Seq[InvestmentFundModel],
    formFieldsConfig: Seq[FormFieldConfig],
    exportTagsAsColumns: Boolean,
    isSupportingDocReviewEnabled: Boolean
  ): List[List[String]] = {
    val allTagColumns = if (exportTagsAsColumns) {
      rows
        .flatMap(
          _.data.flatMap {
            case tag: TagCell => tag.tags.map(_._2)
            case _            => List.empty
          }
        )
        .distinct
        .map(_.name)
        .sorted
    } else {
      Seq.empty
    }
    val headers = columns.flatMap {
      case _: ContactColumn =>
        Seq(
          Option(Terms.InvesteeEntityLabelCamelCase),
          Option("First Name"),
          Option("Last Name"),
          Option("Email"),
          Option("Collaborator")
        ).flatten
      case c: LastActivityColumn =>
        Seq(
          s"${c.title} (UTC)"
        )
      case c: TagColumn =>
        if (exportTagsAsColumns) {
          allTagColumns
        } else {
          Seq(c.title)
        }
      case c: DashboardColumn => Seq(c.title)
    }
    val content = rows.map { row =>
      row.data.zip(columns).flatMap { case (cell, column) =>
        cell match {
          case contact: ContactCell =>
            Seq(
              Option(contact.investmentEntity),
              Option(contact.firstName),
              Option(contact.lastName),
              Option(contact.emailAddress.address),
              Option(
                row.rowMetadata.collaborators
                  .map { user => s"${user.firstName}, ${user.lastName} <${user.emailStr}>" }
                  .mkString("\n")
              )
            ).flatten
          case customLpId: CustomLpIdCell => Seq(customLpId.customId)
          case close: CloseCell           => Seq(close.name)
          case customData: CustomDataCell =>
            Seq(customData.data.map(FundSubExportHelper.getCustomDataDisplayValue).getOrElse(""))
          case lastActivity: LastActivityCell =>
            lastActivity.time.map { time =>
              DateTimeUtils
                .formatInstant(
                  time,
                  DateTimeUtils.DatabaseDatetimeFormatter
                )(
                  using DateTimeUtils.utcTimezone
                )
            }.toSeq
          case invitationDate: InvitationDateCell =>
            Seq(
              invitationDate.value
                .map { time =>
                  DateTimeUtils
                    .formatInstant(
                      time,
                      DateTimeUtils.DatabaseDatetimeFormatter
                    )(
                      using DateTimeUtils.utcTimezone
                    )
                }
                .getOrElse("")
            )
          case submissionDate: SubmissionDateCell =>
            Seq(
              submissionDate.value
                .map { time =>
                  DateTimeUtils
                    .formatInstant(
                      time,
                      DateTimeUtils.DatabaseDatetimeFormatter
                    )(
                      using DateTimeUtils.utcTimezone
                    )
                }
                .getOrElse("")
            )
          case formProgress: FormProgressCell         => Seq(s"${Math.round(formProgress.value * 100)}%")
          case subscription: SubscriptionDocumentCell => Seq(LpStatusSharedUtils.getStatusName(subscription.status))
          case signature: SignatureRequestCell        => Seq(DataUtils.getRequestSignatureStatus(signature))
          case document: DocumentRequestCell =>
            Seq(DataUtils.getDocumentRequestProgress(document, isSupportingDocReviewEnabled))
          case tag: TagCell =>
            if (exportTagsAsColumns) {
              allTagColumns.map { tagColumnHeader =>
                if (tag.tags.map(_._2.name).contains(tagColumnHeader)) "X" else ""
              }
            } else {
              Seq(tag.tags.map(_._2.name).sorted.mkString("; "))
            }
          case referenceDoc: ReferenceDocumentCell => Seq(referenceDoc.files.map(_.name).mkString("\n"))
          case amount: AmountCell =>
            Seq(
              buildAmountCell(
                investmentFunds = investmentFunds,
                amountCell = amount,
                column = column
              )
            )

          case field: FormFieldCell =>
            column match {
              case col: FormFieldColumn =>
                Seq(
                  DataUtils
                    .formatFormField(
                      col,
                      field,
                      formFieldsConfig
                    )
                    ._1
                    .mkString("\n")
                )
              case _ => Seq.empty
            }
          case commitmentPercentage: CommitmentPercentageCell => {
            val lpCommitmentOpt = commitmentPercentage.value
            val commitmentPercentageHeaderOpt = columnHeaders
              .collectFirst { case header: CommitmentPercentageHeader =>
                header
              }
            val totalCommitmentOpt = commitmentPercentageHeaderOpt.map(_.totalCommitment)
            lpCommitmentOpt
              .zip(totalCommitmentOpt)
              .flatMap(getCommitmentPercentageString)
              .toSeq
          }
          case _: MoneyCell => Seq()
          case clientCell: ClientCell =>
            Seq(
              clientCell.clients.headOption
                .flatMap(_.clientRestrictedInfo)
                .fold("")(client => s"${client.clientName}${client.clientCustomId.map(" - " + _)}")
            )
          case dataExtractionCell: DataExtractionCell =>
            Seq(
              DataUtils.getDataExtractRequestDisplayName(dataExtractionCell)
            )
          case amlCheckCell: AmlCheckCell =>
            amlCheckCell.amlChecks.map(_.status.name)
          case advisorGroupCell: AdvisorGroupCell   => Seq(advisorGroupCell.advisorGroupOpt.map(_.name).getOrElse(""))
          case sideLetterCell: SideLetterCell       => Seq(sideLetterCell.status.label)
          case investorGroupCell: InvestorGroupCell => Seq(investorGroupCell.investorGroupOpt.map(_.name).getOrElse(""))
        }
      }
    }
    List(headers.toList) ++ content
  }

  private def buildAmountCell(
    investmentFunds: Seq[InvestmentFundModel],
    amountCell: AmountCell,
    column: DashboardColumn
  ): String = {
    amountCell.value
      .map { value =>
        val currency = investmentFunds
          .collectFirst {
            case fund: InvestmentFundModel if column.id.endsWith(fund.fundId.idString) =>
              fund.currency
          }
          .getOrElse(CurrencyMessage.USD)
        CurrencyUtils.getCurrencySymbol(currency) + s"%,.2f".format(value)
      }
      .getOrElse("")
  }

  private def getCommitmentPercentageString(commitment: Double, totalCommitment: Double): Option[String] = {
    Option.when(totalCommitment > 0) {
      val percentage = commitment / totalCommitment * 100
      "%.2f%%".format(percentage)
    }
  }

}
