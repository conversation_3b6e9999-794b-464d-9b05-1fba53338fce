// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.dataexport

import java.io.{ByteArrayInputStream, ByteArrayOutputStream, InputStream}
import java.time.format.DateTimeFormatter
import java.time.{Instant, ZoneOffset}

import com.github.tototoshi.csv.*
import io.circe.Json
import org.apache.poi.ss.usermodel.{CellType, Workbook}
import org.apache.poi.xssf.usermodel.{XSSFSheet, XSSFWorkbook}
import sttp.model.MediaType
import zio.implicits.*
import zio.stream.Stream
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.dashboard.data.DocumentRequestCell
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.documentcontent.spreadsheet.FillSheetData.{FillSheetCell, FillSheetRow}
import anduin.documentcontent.spreadsheet.FillSpreadsheet.MergeRegion
import anduin.documentcontent.spreadsheet.{FillSheetData, FillSpreadsheet, ReadSpreadsheet, SpreadsheetUtils}
import anduin.fdb.record.{FDBCluster, FDBRecordDatabase}
import anduin.forms.`import`.PredefinedAlias
import anduin.forms.model.Schema
import anduin.forms.service.FormService
import anduin.forms.ui.types.MultipleOptionType
import anduin.forms.ui.{UIKey, Widget}
import anduin.forms.util.{FormImportUtils, TextUtils}
import anduin.forms.utils.FormDataUtils
import anduin.forms.{Form, FormData, FormFieldState}
import anduin.fundsub.`export`.FundSubDataExportConstants
import anduin.fundsub.auditlog.FundSubAuditLogService.AddEventParam
import anduin.fundsub.auditlog.{AuditLogActorType, AuditLogEventType, FundSubAuditLogService}
import anduin.fundsub.constants.Terms
import anduin.fundsub.data.`export`.*
import anduin.fundsub.data.lp.LpData
import anduin.fundsub.data.protocols.*
import anduin.fundsub.data.workflow.GetLpExportDataWorkflow
import anduin.fundsub.endpoint.customdata.CustomDataColumnInfo
import anduin.fundsub.endpoint.lp.FormReferenceInfo
import anduin.fundsub.form.utils.FundSubCommonUtils
import anduin.fundsub.models.{FundSubLpModelStoreOperations, FundSubModelStoreOperations}
import anduin.fundsub.service.FundSubFormIntegrationService.*
import anduin.fundsub.service.{FundSubFormIntegrationService, FundSubLpTagUtilService}
import anduin.fundsub.status.LpStatusSharedUtils
import anduin.fundsub.utils.FundSubExporterAliasParser.ParsedExporterAlias
import anduin.fundsub.utils.{FundSubExporterAliasParser, FundSubLpUtils}
import anduin.id.fundsub.{FundSubAdminRestrictedId, FundSubExportTemplateId, FundSubId, FundSubLpId}
import anduin.model.common.user.UserId
import anduin.model.id.{FileId, FolderId, FundSubExportTemplateIdFactory}
import anduin.protobuf.activitylog.fundsub.admin.InvestorDataExported
import anduin.protobuf.dynamicform.{DynamicFormModel, DynamicFormSection, InputType}
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus
import anduin.protobuf.fundsub.FundSubSignatureType.{ESignature, RequestedSignature, UploadedSignature}
import anduin.protobuf.fundsub.exporter.{FundSubExportFieldConfig, FundSubExportTemplate}
import anduin.protobuf.fundsub.models.customdata.item.*
import anduin.protobuf.fundsub.{FormReference, FundSubSignatureType}
import anduin.rebac.RebacStoreOperation
import anduin.service.{AuthenticatedRequestContext, GeneralServiceException}
import anduin.storageservice.common.FileContentOrigin
import anduin.util.{CurrencyUtils, FilenameUtils}
import anduin.utils.stream.ZStreamIOUtils
import anduin.utils.{DateTimeUtils, ListUtils, StringUtils}
import anduin.workflow.TemporalWorkflowService
import com.anduin.stargazer.dynamicform.constants.DynamicFormConstants
import com.anduin.stargazer.dynamicform.core.{FormHelper, FormProperty}
import com.anduin.stargazer.dynamicform.misc.FormDuplication
import com.anduin.stargazer.dynamicform.{DynamicFormCheckboxUtils, DynamicFormPhoneNumberWithCountryCodeUtils}
import com.anduin.stargazer.service.dynamicform.DynamicFormStorageService
import com.anduin.stargazer.service.utils.ZIOUtils
import scala.jdk.CollectionConverters.*

object FundSubExportHelper {

  final case class LpDataByTemplate(
    lpData: LpData,
    mainFormInfo: Option[FormInfoWithSignDate] = None,
    taxFormInfos: Seq[FormInfoWithSignDate] = Seq.empty,
    taxFormFileIds: Seq[FileId] = Seq.empty
  )

  given SelfEqual[CellType] = CanEqual.derived

  private[dataexport] val ExportLabelLengthThreshold = FundSubDataExportConstants.DefaultMaxOptionLabelLength // chars
  private[dataexport] val ExportHeaderLengthThreshold = 300 // chars

  private[dataexport] def getTextFromHtml(strHtml: String, lengthThreshold: Int): String = {
    val res = TextUtils
      .getTextFromHtml(strHtml)
      // Replace non-breaking spaces with white spaces
      .replace('\u00A0', ' ')
      .trim

    if (lengthThreshold > 0) {
      StringUtils.truncateWithoutWordCutOff(res, lengthThreshold)
    } else {
      res
    }
  }

  def convertTimestamp(
    timestampOpt: Option[Instant],
    timezoneOffsetInMinutes: Int,
    format: DateTimeFormatter = DateTimeUtils.MonthDayTimeTimezoneFormatter2
  ): String = {
    timestampOpt.fold("") { timestamp =>
      DateTimeUtils.formatInstant(timestamp, format)(
        using ZoneOffset.ofTotalSeconds(timezoneOffsetInMinutes * 60)
      )
    }
  }

  def generateExcel(
    fundSubId: FundSubId,
    lpIds: Seq[FundSubLpId],
    accessibleLpIds: Set[FundSubLpId],
    enableTextWrapping: Boolean,
    actor: UserId,
    dateSuffix: String
  )(
    using rebacStoreOperation: RebacStoreOperation,
    formService: FormService,
    fundSubLpTagUtilService: FundSubLpTagUtilService,
    fileService: FileService,
    userProfileService: UserProfileService,
    fdbCluster: FDBCluster
  ): Task[FileId] = {
    val folderId = FolderId.channelSystemFolderId(FundSubAdminRestrictedId(fundSubId))
    for {
      filteredLps <- FundSubLpUtils.getLpInfoRecordsFromLpFilterForFundManagerR(fundSubId, Left(lpIds), actor)
      applicableLps = filteredLps.filter { record =>
        !record.status.isLpnotStarted && accessibleLpIds.contains(record.lpId)
      }
      excel <- ExcelWorkbookExporter.lpFormToExcel(
        fundSubId,
        applicableLps,
        actor,
        enableTextWrapping
      )
      fileName <- getFileNameForExportLpData(
        fundSubId,
        applicableLps.map(_.lpId),
        dateSuffix
      )
      fileId <- fileService.uploadFile(
        folderId,
        fileName,
        FileContentOrigin.FromSource(
          excel,
          MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        ),
        actor
      )
    } yield fileId
  }

  def uploadCsv(
    fileName: String,
    csv: Stream[Throwable, Byte],
    httpContext: AuthenticatedRequestContext
  )(
    using fileService: FileService
  ): Task[FileId] = {
    for {
      folderId <- fileService.createUserTemporaryFolderIfNeeded(httpContext.actor.userId)
      fileId <- fileService.uploadFile(
        folderId,
        s"$fileName.csv",
        FileContentOrigin.FromSource(
          csv,
          MediaType(
            "text",
            "csv",
            Some("UTF8")
          )
        ),
        httpContext.actor.userId
      )
    } yield fileId
  }

  private def getFileNameForExportLpData(
    fundSubId: FundSubId,
    lpIds: Seq[FundSubLpId],
    dateSuffix: String
  )(
    using userProfileService: UserProfileService
  ) = {
    for {
      fundName <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(fundSubId).map(_.fundName)
      }
      lpInfo <- lpIds match {
        case lpId :: Nil =>
          for {
            lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
              ops.getFundSubLpModel(lpId)
            }
            investmentEntity <- ZIO.attempt(lpModel.firmName)
            mainLpInfo <- userProfileService.getUserInfo(lpModel.mainLp)
          } yield
            if (investmentEntity.nonEmpty) {
              investmentEntity
            } else {
              mainLpInfo.fullNameString
            }
        case _ => ZIO.succeed("Investor")
      }
    } yield s"${fundName}_${lpInfo}_Data_$dateSuffix.xlsx"
  }

  def isXlsmFileName(fileName: String): Boolean = {
    FilenameUtils.getExtension(fileName).getOrElse("").toLowerCase == "xlsm"
  }

  def exportLpDataWithUsingTemplate(
    fundSubId: FundSubId,
    lpIds: Seq[FundSubLpId],
    template: FundSubExportTemplate,
    actorId: UserId,
    fileName: String,
    folderId: FolderId,
    forceCsvOutputOpt: Option[Boolean] = None,
    shouldBypassPermissionCheck: Boolean = false
  )(
    using temporalWorkflowService: TemporalWorkflowService,
    fundSubAuditLogService: FundSubAuditLogService,
    fileService: FileService
  ): Task[(FileId, Seq[FundSubLpId])] = {
    for {
      lpExportData <- ZIOUtils
        .foreachParN(50)(lpIds) { lpId =>
          temporalWorkflowService
            .runSync[
              GetLpExportDataInput,
              GetLpExportDataOutput,
              GetLpExportDataWorkflow
            ](
              GetLpExportDataInput(
                lpId = lpId,
                actor = actorId,
                template = Some(template),
                bypassPermission = shouldBypassPermissionCheck
              )
            )
            .map(res => Some(lpId -> res.lpExportData))
            .catchAllCause { cause =>
              ZIO.logWarningCause(s"Failed to export lp data ${lpId.idString}", cause).as(Option.empty)
            }
        }
        .map(_.flatten)
      exportedLpIds = lpExportData.map(_._1)
      generatedFileId <-
        if (exportedLpIds.nonEmpty) {
          fillAndUploadMultipleLpData(
            combineMultipleLpDataToFill(template, lpExportData.flatMap(_._2)),
            template,
            actorId,
            fileName,
            folderId,
            forceCsvOutputOpt
          ) <* logExportEvent(
            fundSubAuditLogService,
            fundSubId,
            actorId,
            Some(template.templateId),
            template.templateName,
            exportedLpIds
          )
        } else {
          ZIO.fail(new GeneralServiceException("No LP data exported"))
        }
    } yield (generatedFileId, exportedLpIds)

  }

  private def fillAndUploadMultipleLpData(
    multipleLpDataToFill: MultipleLpData,
    template: FundSubExportTemplate,
    actor: UserId,
    fileName: String,
    folderId: FolderId,
    forceCsvOutputOpt: Option[Boolean] = None
  )(
    using fileService: FileService
  ): Task[FileId] = {
    val csvOutput = forceCsvOutputOpt.getOrElse(template.shouldExportCsvOpt.getOrElse(false))
    for {
      resultSource <- useWorkbookFromFileOpt(actor, template.templateFileOpt) { workbook =>
        fillMultipleLpDataToWorkbookAndConvertStream(
          workbook,
          multipleLpDataToFill,
          template.sheet,
          if (csvOutput) Option(template) else None
        )
      }
      contentType =
        if (csvOutput) {
          MediaType(
            "text",
            "csv",
            Some("UTF8")
          )
        } else if (isXlsmFileName(fileName)) {
          MediaType("application", "vnd.ms-excel.sheet.macroEnabled.12")
        } else {
          MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        }
      revisedFileName =
        if (csvOutput) {
          val parts = fileName.split("\\.")
          val partsEndWithCsv = if (parts.size > 1) parts.dropRight(1) :+ "csv" else parts :+ "csv"
          partsEndWithCsv.mkString(".")
        } else {
          fileName
        }
      generatedFileId <- fileService.uploadFile(
        folderId,
        revisedFileName,
        FileContentOrigin.FromSource(
          resultSource,
          contentType
        ),
        actor
      )
    } yield generatedFileId
  }

  private[fundsub] def logExportEvent(
    fundSubAuditLogService: FundSubAuditLogService,
    fundSubId: FundSubId,
    actorId: UserId,
    templateIdOpt: Option[FundSubExportTemplateId],
    templateName: String,
    lpIds: Seq[FundSubLpId]
  ) = {
    fundSubAuditLogService.addEvent(
      fundSubId = fundSubId,
      params = AddEventParam(
        actor = Some(actorId),
        actorType = AuditLogActorType.FundSide,
        eventType = AuditLogEventType.INVESTOR_DATA_EXPORTED,
        activityDetail = InvestorDataExported(
          advanceExportTemplateIdOpt = templateIdOpt,
          templateName = templateName,
          lpIds = lpIds
        )
      )
    )
  }

  def computeMultipleLpDataToFill(
    lpDataByTemplateSeq: Seq[LpDataByTemplate],
    template: FundSubExportTemplate
  ): Task[MultipleLpData] = {
    for {
      lpDataToFills <- ZIO.foreach(lpDataByTemplateSeq) {
        case LpDataByTemplate(lpData, mainFormInfo, taxFormInfos, taxFormFileIds) =>
          computeSingleLpDataToFill(lpData, template, mainFormInfo, taxFormInfos, taxFormFileIds)
      }
    } yield combineMultipleLpDataToFill(template, lpDataToFills.flatten)
  }

  def combineMultipleLpDataToFill(
    template: FundSubExportTemplate,
    lpDataToFills: Seq[SingleLpData]
  ): MultipleLpData = {
    val processedLpDataToFills = lpDataToFills
      .scan(
        SingleLpData(
          template.startRow,
          0,
          Seq.empty
        )
      ) { (result, lpDataToFill) =>
        lpDataToFill.copy(startingRow = result.startingRow + result.numberOfRow)
      }
    MultipleLpData(lpData = processedLpDataToFills.drop(1))
  }

  private def computeSingleFieldData(
    fieldConfig: FundSubExportFieldConfig,
    formInfoWithSignDateOpt: Option[FormInfoWithSignDate],
    lpDataOpt: Option[LpData],
    shouldExportOptionLabel: Boolean,
    optionLabelLengthThresholdOpt: Option[Int]
  ): SingleFieldData = {
    val parsedAlias = FundSubExporterAliasParser.parseExporterAlias(fieldConfig.alias)

    val fieldDataToFill = parsedAlias.toPredefinedAlias.fold {
      if (parsedAlias.duplicateIndex.exists(_ < 0)) {
        computeFieldDataToFillForListAllDuplicatedAlias(
          formInfoWithSignDateOpt.map(_.formInfo),
          parsedAlias,
          shouldExportOptionLabel,
          optionLabelLengthThresholdOpt
        )
      } else {
        computeFieldDataToFillForNormalAlias(
          formInfoWithSignDateOpt.map(_.formInfo),
          parsedAlias,
          shouldExportOptionLabel,
          optionLabelLengthThresholdOpt
        )
      }
    } { predefinedAlias =>
      lpDataOpt.fold(SingleFieldData(dataValues = Seq(DataValue()))) { lpData =>
        SingleFieldData(
          dataValues = Seq(
            DataValue(
              Seq(
                computeDataForPredefinedAlias(
                  lpData,
                  predefinedAlias,
                  mainFormInfo = formInfoWithSignDateOpt.map(_.formInfo),
                  signDateOpt = formInfoWithSignDateOpt.flatMap(_.signDateOpt),
                  uploadedOnlyTaxDocs = Seq.empty[FileId],
                  rawParsedAliasOpt = Option(parsedAlias)
                )
              )
            )
          )
        )
      }
    }

    fieldDataToFill.copy(startingCol = fieldConfig.fromCol, repeatAllRows = parsedAlias.repeatAllRows)
  }

  private[fundsub] def computeSingleLpDataToFill(
    lpDataByTemplate: LpData,
    template: FundSubExportTemplate,
    mainFormInfo: Option[FormInfoWithSignDate],
    taxFormInfos: Seq[FormInfoWithSignDate],
    taxFormFileIds: Seq[FileId]
  ): Task[Seq[SingleLpData]] = {
    ZIO.attempt {
      val shouldExportOptionLabel = template.shouldExportOptionLabel.getOrElse(false)
      if (template.taxFormReferenceOpt.isEmpty) {
        // Main form template
        val fieldDataToFills = template.fieldConfigs.map(
          computeSingleFieldData(
            _,
            mainFormInfo,
            Option(lpDataByTemplate),
            shouldExportOptionLabel,
            template.maxOptionLabelLength
          )
        )
        Seq(
          SingleLpData(
            startingRow = template.startRow,
            numberOfRow = fieldDataToFills.map(_.dataValues.size).maxOption.getOrElse(0),
            fieldDatas = fieldDataToFills
          )
        )
      } else {
        // Tax form template
        val filledFormDataSeq = taxFormInfos.map { formInfo =>
          val fieldDataToFills = template.fieldConfigs.map(
            computeSingleFieldData(
              _,
              Some(formInfo),
              Some(lpDataByTemplate),
              shouldExportOptionLabel,
              template.maxOptionLabelLength
            )
          )
          SingleLpData(
            startingRow = template.startRow,
            numberOfRow = fieldDataToFills.map(_.dataValues.size).maxOption.getOrElse(0),
            fieldDatas = fieldDataToFills
          )
        }
        val uploadedFormDataSeq = taxFormFileIds.map { _ =>
          val fieldDataToFills = template.fieldConfigs.map(
            computeSingleFieldData(
              _,
              Some(
                FormInfoWithSignDate(
                  DynamicFormInfo(DynamicFormModel(), Map.empty)
                )
              ),
              Option(lpDataByTemplate),
              shouldExportOptionLabel,
              template.maxOptionLabelLength
            )
          )
          SingleLpData(
            startingRow = template.startRow,
            numberOfRow = fieldDataToFills.map(_.dataValues.size).maxOption.getOrElse(0),
            fieldDatas = fieldDataToFills
          )
        }

        filledFormDataSeq ++ uploadedFormDataSeq
      }
    }
  }

  private def getSignatureTypeStringValue(signatureType: FundSubSignatureType) = {
    signatureType match {
      case RequestedSignature | _: FundSubSignatureType.Unrecognized => ""
      case ESignature                                                => "E-signed"
      case UploadedSignature                                         => "Signed doc uploaded"
    }
  }

  def getCustomDataDisplayValue(value: CustomData): String = {
    value match {
      case dateTime: DateTimeValue =>
        dateTime.value
          .map(
            DateTimeUtils.formatInstant(_, DateTimeFormatter.ofPattern("MM/dd/yyyy"))(
              using DateTimeUtils.utcTimezone
            )
          )
          .getOrElse("")
      case currency: CurrencyValue =>
        val symbol = CurrencyUtils.getCurrencySymbol(currency.currency)
        val numberFormat = s"%,.${2}f"
        symbol + numberFormat.format(currency.amount)
      case singleChoice: SingleStringValue => singleChoice.valueWithColor.map(_.content).mkString(SemicolonSeparator)
      case multipleChoices: MultipleStringValue =>
        multipleChoices.valueWithColor.map(_.content).mkString(SemicolonSeparator)
      case checklist: ChecklistValue => checklist.value.mkString(SemicolonSeparator)
      case note: StringValue         => TextUtils.getTextFromHtmlKeepNewLine(note.value)
      case metadata: MetadataValue   => TextUtils.getTextFromHtmlKeepNewLine(metadata.value)
      case CustomData.Empty          => ""
    }
  }

  private def computeDataForCustomDashboardColumn(
    lpData: LpData,
    columnIds: List[String]
  ): String = {
    if (columnIds.nonEmpty) {
      val lpCustomDataStringMap = lpData.customColumnDataMap.view.mapValues(getCustomDataDisplayValue).toMap
      columnIds
        .map(
          lpCustomDataStringMap.getOrElse(_, "")
        )
        .filter(_.nonEmpty)
        // In case multiple column IDs are specified => separate their data in different lines
        .mkString(NewLineSeparator)
    } else {
      ""
    }
  }

  def computeDataForPredefinedAlias(
    lpData: LpData,
    predefinedAlias: PredefinedAlias,
    mainFormInfo: Option[FormInfo] = None,
    uploadedOnlyTaxDocs: Seq[FileId] = Seq.empty,
    rawParsedAliasOpt: Option[ParsedExporterAlias] = None,
    signDateOpt: Option[Instant] = None
  ): String = {
    predefinedAlias match {
      case PredefinedAlias.FundSubIdAlias         => lpData.lpId.parent.idString
      case PredefinedAlias.CustomFundIdAlias      => lpData.customFundId
      case PredefinedAlias.InvestorLpIdAlias      => lpData.lpId.value.value
      case PredefinedAlias.CustomLpIdAlias        => lpData.customId
      case PredefinedAlias.ImportInvestorIdAlias  => lpData.importInvestorId
      case PredefinedAlias.FirmNameAlias          => lpData.firmName
      case PredefinedAlias.InvestorStatusAlias    => LpStatusSharedUtils.getStatusName(lpData.status)
      case PredefinedAlias.TagAlias               => lpData.tags.mkString(CommaSeparator)
      case PredefinedAlias.CloseAlias             => lpData.close
      case PredefinedAlias.NoteAlias              => lpData.note
      case PredefinedAlias.InvestorFirstNameAlias => lpData.firstName
      case PredefinedAlias.InvestorLastNameAlias  => lpData.lastName
      case PredefinedAlias.InvestorEmailAlias     => lpData.email
      case PredefinedAlias.FormData =>
        mainFormInfo
          .map(_.jsonFormValues.noSpacesSortKeys)
          .getOrElse("")
      case PredefinedAlias.FormEvents =>
        mainFormInfo
          .flatMap(_.events.map(_.noSpacesSortKeys))
          .getOrElse("")
      case PredefinedAlias.CollaboratorsAlias =>
        lpData.collaborators
          .map { userInfo =>
            s"${userInfo.firstName}, ${userInfo.lastName} <${userInfo.email}>"
          }
          .mkString(NewLineSeparator)
      case PredefinedAlias.ClosingDateAlias =>
        lpData.closingDateOpt
          .map(date =>
            DateTimeUtils
              .formatZonedDateTime(
                date,
                DateTimeFormatter.ofPattern("yyyy-MM-dd")
              )
          )
          .getOrElse("")
      case PredefinedAlias.InvestorFormVersion =>
        mainFormInfo
          .map {
            case oldForm: DynamicFormInfo =>
              oldForm.lpFormIdOpt.map { lpFormId =>
                s"${oldForm.formModel.formName} (${lpFormId.parent.idString})"
              } getOrElse ""
            case newForm: GaiaFormInfo => newForm.lpFormIdOpt.map(_.parent.idString).getOrElse("")
          }
          .getOrElse("")
      case PredefinedAlias.ExpectedCommitment =>
        lpData.commitment
          .flatMap(_.expectedCommitment)
          .map(CurrencyUtils.convertMoneyMessageToMoneyString)
          .getOrElse("")
      case PredefinedAlias.SubmittedCommitment =>
        lpData.commitment
          .flatMap(_.submittedCommitment)
          .map(CurrencyUtils.convertMoneyMessageToMoneyString)
          .getOrElse("")
      case PredefinedAlias.AcceptedCommitment =>
        lpData.commitment
          .flatMap(_.acceptedCommitment)
          .map(CurrencyUtils.convertMoneyMessageToMoneyString)
          .getOrElse("")
      case PredefinedAlias.FilledOnOffAnduin =>
        lpData.status match {
          case LpStatus.LPNotStarted | LpStatus.LPInProgress => ""
          case LpStatus.LPChangeInProgress                   => "On Anduin"
          case _ =>
            mainFormInfo.fold("") { formInfo =>
              if (formInfo.isCompleted) "On Anduin" else "Off Anduin"
            }
        }
      case PredefinedAlias.HasTaxDataFilledOffAnduin =>
        if (uploadedOnlyTaxDocs.nonEmpty) "Yes" else "No"
      case PredefinedAlias.FirstDateSubmitted =>
        lpData.statusHistory
          .map(_.activities)
          .getOrElse(Seq.empty)
          .find { activity =>
            activity.lpStatus == LpStatus.LPPendingReview || activity.lpStatus == LpStatus.LPSubmitted
          }
          .flatMap(_.at)
          .map(
            DateTimeUtils.formatInstant(_, DateTimeUtils.MonthDayTimeTimezoneFormatter2)(
              using DateTimeUtils.utcTimezone
            )
          )
          .getOrElse("")
      case PredefinedAlias.DateSubmitted =>
        val lpSubmittedDates =
          lpData.statusHistory.map(_.activities).getOrElse(Seq.empty).sliding(2).map(_.toList).map {
            case previousEvent :: currentEvent :: Nil =>
              Option
                .when(isLpSubmittedStatus(currentEvent.lpStatus) && !isLpSubmittedStatus(previousEvent.lpStatus))(
                  currentEvent.at
                )
                .flatten
            case _ => None
          }
        val lastSubmittedDateOpt = lpSubmittedDates.flatten.toSeq.lastOption
        lastSubmittedDateOpt
          .map(
            DateTimeUtils.formatInstant(_, DateTimeUtils.MonthDayTimeTimezoneFormatter2)(
              using DateTimeUtils.utcTimezone
            )
          )
          .getOrElse("")
      case PredefinedAlias.InvestorSigningType =>
        getSignatureTypeStringValue(lpData.signatureType)
      case PredefinedAlias.LastActivityAt =>
        lpData.lastActivityAtOpt
          .map(date =>
            DateTimeUtils
              .formatZonedDateTime(
                date.atZone(DateTimeUtils.utcTimezone),
                DateTimeUtils.MonthDayTimeTimezoneFormatter2
              )
          )
          .getOrElse("")
      case PredefinedAlias.CustomDashboardColumn =>
        computeDataForCustomDashboardColumn(lpData, rawParsedAliasOpt.map(_.additionalFlags).getOrElse(List.empty))
      case PredefinedAlias.SignDate =>
        signDateOpt
          .map(
            DateTimeUtils.formatInstant(_, DateTimeFormatter.ofPattern("MM/dd/yyyy"))(
              using DateTimeUtils.utcTimezone
            )
          )
          .getOrElse("")

      case PredefinedAlias.AmlkycRequest =>
        DocumentRequestCell.getDocRequestProgressString(
          lpData.docRequestProgress.map { progress =>
            DocumentRequestCell.getDocRequestStatus(progress.statusType) -> progress.docCount
          }
        )

      case PredefinedAlias.InvestorGroup => lpData.investorGroup
    }
  }

  private def isLpSubmittedStatus(lpStatus: LpStatus) =
    lpStatus == LpStatus.LPPendingReview || lpStatus == LpStatus.LPSubmitted

  private def computeFieldDataToFillForListAllDuplicatedAlias(
    formInfoOpt: Option[FormInfo],
    parsedAlias: ParsedExporterAlias,
    shouldExportOptionLabel: Boolean,
    optionLabelLengthThresholdOpt: Option[Int]
  ): SingleFieldData = {
    formInfoOpt.fold(SingleFieldData()) {
      case dynamicFormInfo: FundSubFormIntegrationService.DynamicFormInfo =>
        computeFieldDataToFillForListAllDuplicatedAliasForDynamicForm(
          dynamicFormInfo,
          parsedAlias
        )
      case gaiaFormInfo: FundSubFormIntegrationService.GaiaFormInfo =>
        computeFieldDataForAllRepeatableItem(
          gaiaFormInfo,
          parsedAlias,
          shouldExportOptionLabel,
          optionLabelLengthThresholdOpt
        )
    }
  }

  private def computeFieldDataToFillForNormalAlias(
    formInfoOpt: Option[FormInfo],
    parsedAlias: ParsedExporterAlias,
    shouldExportOptionLabel: Boolean,
    optionLabelLengthThresholdOpt: Option[Int]
  ): SingleFieldData = {
    formInfoOpt.fold(SingleFieldData()) {
      case dynamicFormInfo: FundSubFormIntegrationService.DynamicFormInfo =>
        computeFieldDataToFillForNormalAliasForDynamicForm(
          dynamicFormInfo,
          parsedAlias
        )
      case gaiaFormInfo: FundSubFormIntegrationService.GaiaFormInfo =>
        parsedAlias.duplicateIndex.fold(
          computeFieldDataToFillForNormalAliasForGaiaForm(
            gaiaFormInfo,
            parsedAlias,
            shouldExportOptionLabel,
            optionLabelLengthThresholdOpt
          )
        ) { index =>
          computeFieldDataForSingleRepeatableItem(
            parsedAlias.alias,
            index,
            gaiaFormInfo,
            parsedAlias.optionAsColumns,
            shouldExportOptionLabel,
            optionLabelLengthThresholdOpt
          )
        }
    }
  }

  /*
    DYNAMIC FORM'S METHODS
   */

  private def computeFieldDataToFillForListAllDuplicatedAliasForDynamicForm(
    dynamicFormInfo: FundSubFormIntegrationService.DynamicFormInfo,
    parsedAlias: ParsedExporterAlias
  ): SingleFieldData = {
    val sectionIdOpt = getSectionIdToLookupOpt(dynamicFormInfo, parsedAlias.copy(duplicateIndex = None))
    sectionIdOpt
      .map { sectionId =>
        val (duplicatableSectionId, duplicateCount) = FormDuplication
          .findDuplicatableSectionIdOfAnInnerSection(dynamicFormInfo.formProcessor, sectionId)
          .getOrElse(sectionId -> 0) // Fallback to export the section value itself
        val allDuplicatedIds = FormDuplication.generateAllDuplicatedIdOfAnInnerSectionId(
          duplicatableSectionId,
          sectionId,
          duplicateCount
        )
        val fieldDataToFillEachRows = allDuplicatedIds.map { targetId =>
          computeFieldDataToFillForNormalAliasForDynamicForm(
            dynamicFormInfo,
            parsedAlias.copy(
              duplicateIndex = None,
              alias = targetId,
              isId = true
            )
          )
        }
        SingleFieldData(
          dataValues = fieldDataToFillEachRows.flatMap(_.dataValues)
        )
      }
      .getOrElse {
        // Section is not existed
        SingleFieldData()
      }
  }

  // Always using 1 row to fill
  private def computeFieldDataToFillForNormalAliasForDynamicForm(
    dynamicFormInfo: FundSubFormIntegrationService.DynamicFormInfo,
    parsedAlias: ParsedExporterAlias
  ): SingleFieldData = {
    val sectionIdOpt = getSectionIdToLookupOpt(dynamicFormInfo, parsedAlias)
    sectionIdOpt
      .map { sectionId =>
        val sectionDescOpt = dynamicFormInfo.formProcessor.getSection(sectionId).flatMap(_.formDescription)
        val inputTypeOpt = sectionDescOpt.map(_.inputType)
        val sectionValue = dynamicFormInfo.formProcessor.filteredFormValues.getOrElse(sectionId, "")
        val singleRowDataValues =
          if (
            parsedAlias.optionAsColumns && inputTypeOpt
              .exists(inputType => inputType.isCheckbox || inputType.isRadio || inputType.isMultipleCheckbox)
          ) {
            // Fill X or empty for corresponding column
            val selectedOptions = inputTypeOpt.fold(Set.empty[String]) {
              case InputType.MultipleCheckbox =>
                DynamicFormCheckboxUtils.decodeMultipleCheckboxValueAsSet(sectionValue)
              case _ => Set(sectionValue.trim)
            }
            val allOptions = sectionDescOpt.map(_.inputOptions.map(_.value.trim)).getOrElse(Seq.empty)
            generateSelectedOptionToFill(selectedOptions.toSeq, allOptions)
          } else {
            // Get raw data to fill
            inputTypeOpt.fold(Seq(sectionValue)) {
              case InputType.MultipleCheckbox =>
                Seq(DynamicFormCheckboxUtils.decodeMultipleCheckboxValueFromString(sectionValue).mkString(CommaSeparator))
              case InputType.PhoneNumberWithCountryCode =>
                Seq(
                  DynamicFormPhoneNumberWithCountryCodeUtils
                    .convertToCaseClass(sectionValue)
                    .map(_.toDisplayString)
                    .getOrElse("")
                )
              case _ => Seq(sectionValue)
            }
          }
        SingleFieldData(
          dataValues = Seq(DataValue(singleRowDataValues))
        )
      }
      .getOrElse(
        SingleFieldData()
      )
  }

  // Get sectionId from parsed alias
  // None if unable to find sectionId
  private def getSectionIdToLookupOpt(
    dynamicFormInfo: FundSubFormIntegrationService.DynamicFormInfo,
    parsedAlias: ParsedExporterAlias
  ): Option[String] = {
    if (parsedAlias.isId) {
      val rawSectionId = parsedAlias.alias
      val sectionIdToLookupOpt = parsedAlias.duplicateIndex.fold {
        Option(rawSectionId)
      } { duplicateIndex =>
        // To generate the section id in a duplicate, we have to find the duplicatable section containing this id
        FormDuplication.findDuplicatableSectionIdOfAnInnerSection(dynamicFormInfo.formProcessor, rawSectionId).flatMap {
          case (duplicatableSectionId, _) =>
            FormDuplication.duplicateInnerSectionId(
              duplicatableSectionId,
              rawSectionId,
              duplicateIndex
            )
        }
      }
      sectionIdToLookupOpt.filter(dynamicFormInfo.formProcessor.isSectionIdExisted)
    } else {
      val aliasToLookup = parsedAlias.duplicateIndex.fold {
        parsedAlias.alias
      } { duplicateIndex =>
        s"${parsedAlias.alias}${DynamicFormConstants.AliasDuplicateSeparator}$duplicateIndex"
      }
      dynamicFormInfo.formProcessor.formProperty.allAliases.get(aliasToLookup)
    }
  }

  /*
    GAIA FORM'S METHODS
   */

  private[dataexport] def computeFieldDataForAllRepeatableItem(
    gaiaFormInfo: FundSubFormIntegrationService.GaiaFormInfo,
    parsedAlias: ParsedExporterAlias,
    shouldExportOptionLabel: Boolean = true,
    optionLabelLengthThresholdOpt: Option[Int] = None
  ): SingleFieldData = {
    val alias = parsedAlias.alias
    val dataValues = for {
      (field, _) <- FormDataUtils
        .traverseAndExtract(
          gaiaFormInfo.formData.form,
          (field, _) =>
            field.tpe match {
              case Schema.array(obj: Schema.obj, _, _, _) => FormDataUtils.getContainedSections(obj).contains(alias)
              case _                                      => false
            }
        )
      containerValue <- gaiaFormInfo.getFieldValue(field.name).toSeq
      element <- containerValue.asArray.toSeq.flatten
      fieldState <- element.asObject.flatMap(_.toMap.get(alias))
      fieldValue = FormFieldState(fieldState).getValue
      schema <- field.tpe match {
        case Schema.array(obj: Schema.obj, _, _, _) =>
          FormDataUtils
            .traverseAndExtract(
              // TODO @trancuong81 Only handle default namespace ATM, revise after clarifying what we wanna do here
              gaiaFormInfo.formData.form.setDefaultFormSchema(obj),
              (field, _) => field.name == alias
            )
            .headOption
            .map(_._1.tpe)
        case _ => None
      }
    } yield DataValue(
      getSingleFieldData(
        schema,
        fieldValue,
        parsedAlias.optionAsColumns,
        gaiaFormInfo.formData.form.defaultUiSchema.get(alias),
        shouldExportOptionLabel,
        optionLabelLengthThresholdOpt
      )
    )

    SingleFieldData(dataValues = dataValues)
  }

  private[dataexport] def computeFieldDataForSingleRepeatableItem(
    alias: String,
    index: Int,
    gaiaFormInfo: FundSubFormIntegrationService.GaiaFormInfo,
    optionAsColumns: Boolean,
    shouldExportOptionLabel: Boolean = true,
    optionLabelLengthThresholdOpt: Option[Int] = None
  ) = {
    val valueOpt = for {
      (field, _) <- FormDataUtils
        .traverseAndExtract(
          gaiaFormInfo.formData.form,
          (field, _) =>
            field.tpe match {
              case Schema.array(obj: Schema.obj, _, _, _) => FormDataUtils.getContainedSections(obj).contains(alias)
              case _                                      => false
            }
        )
        .headOption
      containerValue <- gaiaFormInfo.getFieldValue(field.name)
      fieldState <- containerValue.asArray.flatMap(_.lift(index)).flatMap(_.asObject.flatMap(_.toMap.get(alias)))
      fieldValue = FormFieldState(fieldState).getValue
      schema <- field.tpe match {
        case Schema.array(obj: Schema.obj, _, _, _) =>
          FormDataUtils
            .traverseAndExtract(
              // TODO @trancuong81 Only handle default namespace ATM, revise after clarifying what we wanna do here
              gaiaFormInfo.formData.form.setDefaultFormSchema(obj),
              (field, _) => field.name == alias
            )
            .headOption
            .map(_._1.tpe)
        case _ => None
      }
    } yield getSingleFieldData(
      schema,
      fieldValue,
      optionAsColumns,
      gaiaFormInfo.formData.form.defaultUiSchema.get(alias),
      shouldExportOptionLabel,
      optionLabelLengthThresholdOpt
    )
    SingleFieldData(dataValues = Seq(DataValue(valueOpt.getOrElse(Nil))))
  }

  private[dataexport] def computeFieldDataToFillForNormalAliasForGaiaForm(
    gaiaFormInfo: FundSubFormIntegrationService.GaiaFormInfo,
    parsedAlias: ParsedExporterAlias,
    shouldExportOptionLabel: Boolean = true,
    optionLabelLengthThresholdOpt: Option[Int] = None
  ): SingleFieldData = {
    val singleFieldDataOpt = for {
      fieldValue <- gaiaFormInfo.getFieldValue(parsedAlias.alias)
      schema <- gaiaFormInfo.allFieldSchema.get(parsedAlias.alias)
    } yield SingleFieldData(dataValues =
      Seq(
        DataValue(
          getSingleFieldData(
            schema,
            fieldValue,
            parsedAlias.optionAsColumns,
            gaiaFormInfo.formData.form.defaultUiSchema.get(parsedAlias.alias),
            shouldExportOptionLabel,
            optionLabelLengthThresholdOpt
          )
        )
      )
    )
    singleFieldDataOpt.getOrElse(SingleFieldData(dataValues = Seq(DataValue(Seq()))))
  }

  private def getWidgetMultiOptsValueLabelMapping(widget: Widget, labelLengthThresholdOpt: Option[Int])
    : Map[String, String] = {
    widget.uiOptions
      .getOrElse(UIKey.multipleOption, MultipleOptionType.Default)
      .options
      .map { case (optionValue, singleOptType) =>
        val optionLabel =
          getTextFromHtml(singleOptType.formattedText, labelLengthThresholdOpt.getOrElse(ExportLabelLengthThreshold))
        if (optionLabel.isEmpty) {
          optionValue -> optionValue
        } else {
          optionValue -> optionLabel
        }
      }
  }

  private def getSingleFieldData(
    schema: Schema,
    fieldValue: Json,
    optionAsColumns: Boolean,
    uiSchemaOpt: Option[Widget],
    shouldExportOptionLabel: Boolean,
    optionLabelLengthThresholdOpt: Option[Int]
  ): Seq[String] = {
    schema match {
      case _: Schema.string => fieldValue.asString.toSeq
      case _: Schema.integer | _: Schema.number =>
        fieldValue.asNumber.map { number =>
          number.toBigInt.map(_.toString).getOrElse(number.toDouble.toString)
        }.toSeq
      case enumSchema: Schema.`enum` =>
        val selectedValues = fieldValue.asString.toSeq

        if (optionAsColumns) {
          val allOptions = enumSchema.values.flatMap(_.asString)
          generateSelectedOptionToFill(selectedValues, allOptions)
        } else {
          if (shouldExportOptionLabel) {
            val valueLabelMapping =
              uiSchemaOpt.map(getWidgetMultiOptsValueLabelMapping(_, optionLabelLengthThresholdOpt)).getOrElse(Map.empty)
            selectedValues.map(v => valueLabelMapping.getOrElse(v, v))
          } else {
            selectedValues
          }
        }
      case Schema.array(enumSchema: Schema.`enum`, _, _, _) =>
        val selectedValues = fieldValue.asArray.fold(Seq.empty[String])(_.flatMap(_.asString))

        if (optionAsColumns) {
          val allOptions = enumSchema.values.flatMap(_.asString)
          generateSelectedOptionToFill(selectedValues, allOptions)
        } else {
          if (shouldExportOptionLabel) {
            val valueLabelMapping =
              uiSchemaOpt.map(getWidgetMultiOptsValueLabelMapping(_, optionLabelLengthThresholdOpt)).getOrElse(Map.empty)
            val selectedLabels = selectedValues.map(v => valueLabelMapping.getOrElse(v, v))

            Seq(selectedLabels.mkString(SemicolonSeparator))
          } else {
            Seq(selectedValues.mkString(SemicolonSeparator))
          }
        }
      case _ => Nil
    }
  }

  private def generateSelectedOptionToFill(
    selectedOptions: Seq[String],
    allOptions: Seq[String]
  ) = {
    allOptions.map(optionValue => if (selectedOptions.contains(optionValue)) "X" else "")
  }

  def useWorkbookFromFileOpt[A](
    actor: UserId,
    fileIdOpt: Option[FileId]
  )(
    use: XSSFWorkbook => Task[A]
  )(
    using fileService: FileService
  ): Task[A] = {
    fileIdOpt.fold {
      for {
        workbook <- ZIO.attempt(new XSSFWorkbook())
        res <- use(workbook)
        _ <- ZIO.attempt(workbook.close())
      } yield res
    } { fileId =>
      for {
        res <- fileService.useFileInputStream(
          actor,
          fileId,
          None
        ) { is =>
          SpreadsheetUtils.useWorkbookInputStream(is) { workbook =>
            use(workbook)
          }
        }
      } yield res
    }
  }

  // This is for our current approach of using Excel formulas as needed & copy them to e.g., 1000 rows as placeholders
  // - Would need to clean up placeholder rows with no actual data since some of them might not show empty
  // - Only clean up needed cells with formula instead of the whole unfilled row since those numbers of cells could be
  // large & affect the performance
  private def cleanUpFormulaRowsWithNoData(
    sheet: XSSFSheet,
    multipleLpData: MultipleLpData
  ): Unit = {
    val lastRowIdx = sheet.getLastRowNum
    // The startingRow & numberOfRow in our multipleLpData should be calculated gradually -> could find last filled row
    // index this way (instead of having to find max)
    val noDataRowStartIdx = multipleLpData.lpData.lastOption.map(d => d.startingRow + d.numberOfRow).getOrElse(1)
    val startRowIdx = multipleLpData.lpData.headOption.map(_.startingRow).getOrElse(1)
    val startRow = sheet.getRow(startRowIdx)
    val cellIdxWithFormulaList = if (startRow != null) { // scalafix:ok DisableSyntax.null
      Range(0, startRow.getLastCellNum.toInt).toList
        .map { idx =>
          val hasFormula =
            try {
              startRow.getCell(idx).getCellFormula.nonEmpty
            } catch {
              case _: Throwable => false
            }
          idx -> hasFormula
        }
        .filter(_._2)
        .map(_._1)
    } else {
      List.empty
    }

    // Clean up
    if (cellIdxWithFormulaList.nonEmpty) {
      Range(noDataRowStartIdx, lastRowIdx + 1).foreach { rowIdx =>
        val row = sheet.getRow(rowIdx)
        if (row != null) { // scalafix:ok DisableSyntax.null
          cellIdxWithFormulaList.foreach { cellIdx =>
            try {
              val cell = row.getCell(cellIdx)
              cell.removeFormula()
              cell.setCellValue("")
            } catch {
              case _: Throwable => ()
            }
          }
        }
      }
    }
  }

  private def sheetDataToCsvStream(
    workbook: Workbook,
    sheet: XSSFSheet,
    endDataRowIdx: Int,
    fieldConfigs: Seq[FundSubExportFieldConfig],
    fieldAliasRowIndex: Int
  ): Task[Stream[Throwable, Byte]] = {
    zio.ZIO.attempt {
      val outputStream = new ByteArrayOutputStream()
      val writer = CSVWriter.open(outputStream)

      val evaluator = workbook.getCreationHelper.createFormulaEvaluator()
      val aliasRow = sheet.getRow(fieldAliasRowIndex)
      val hiddenColumns = if (aliasRow == null) { // scalafix:ok DisableSyntax.null
        Set.empty[Int]
      } else {
        aliasRow.asScala.toSeq.flatMap { cell =>
          val columnIndex = cell.getColumnIndex
          Option.when(sheet.isColumnHidden(columnIndex))(columnIndex)
        }.toSet
      }
      (0 to endDataRowIdx).foreach { rowIdx =>
        val row = sheet.getRow(rowIdx)
        if (row != null) { // scalafix:ok DisableSyntax.null
          val data = row.asScala.toSeq
            .filterNot(cell => hiddenColumns.contains(cell.getColumnIndex))
            .map { cell =>
              if (cell != null) { // scalafix:ok DisableSyntax.null
                cell.getCellType match {
                  case CellType.BOOLEAN => String.valueOf(cell.getBooleanCellValue)
                  case CellType.NUMERIC => String.valueOf(cell.getNumericCellValue)
                  case CellType.STRING  => cell.getStringCellValue
                  case CellType.FORMULA =>
                    // Since we already evaluateAll when computing the spreadsheet, may remove this re-evaluation if
                    // runtime performance of exporting CSV becomes a concern
                    val cellValue = evaluator.evaluate(cell)
                    cellValue.getCellType match {
                      case CellType.BOOLEAN => String.valueOf(cellValue.getBooleanValue)
                      case CellType.NUMERIC => String.valueOf(cellValue.getNumberValue)
                      case CellType.STRING  => cellValue.getStringValue
                      case _                => ""
                    }
                  case CellType.BLANK | CellType.ERROR | CellType._NONE => ""
                }
              } else {
                ""
              }
            }
          writer.writeRow(data)
        }
      }
      writer.close()
      ZStreamIOUtils.fromByteArray(outputStream.toByteArray)
    }
  }

  def fillMultipleLpDataToWorkbookAndConvertStream(
    workbook: XSSFWorkbook,
    multipleLpData: MultipleLpData,
    sheetIndex: Int,
    outputCsvTemplateOpt: Option[FundSubExportTemplate] = None
  ): Task[Stream[Throwable, Byte]] = {
    for {
      dataSheet <- zio.ZIO.attempt {
        val sheet = workbook.getSheetAt(sheetIndex)
        if (sheet != null) { // scalafix:ok DisableSyntax.null
          fillMultipleLpDataToSheet(sheet, multipleLpData)
          cleanUpFormulaRowsWithNoData(sheet, multipleLpData)

          // Explicitly evaluate formula cells since in some cases (e.g., xlsm file in Mac), formula cell values are
          // not updated until touched
          val evaluator = workbook.getCreationHelper.createFormulaEvaluator()
          workbook.iterator().forEachRemaining { excelSheet =>
            excelSheet.iterator().forEachRemaining { row =>
              row.iterator().forEachRemaining { cell =>
                if (cell.getCellType == CellType.FORMULA) {
                  try {
                    evaluator.evaluateFormulaCell(cell)
                    ()
                  } catch {
                    // Ignore error & continue to evaluate other formulas
                    case _: Throwable => ()
                  }
                }
              }
            }
          }
        }
        sheet
      }
      resultSource <- outputCsvTemplateOpt.fold(
        SpreadsheetUtils.workbookToStream(workbook)
      ) { template =>
        val fieldConfigs = template.fieldConfigs
        val endRowIdx = multipleLpData.lpData.lastOption.map(d => d.startingRow + d.numberOfRow - 1).getOrElse(0)
        sheetDataToCsvStream(
          workbook,
          dataSheet,
          endRowIdx,
          fieldConfigs,
          template.startRow
        )
      }
    } yield resultSource
  }

  private def fillMultipleLpDataToSheet(
    sheet: XSSFSheet,
    multipleLpData: MultipleLpData
  ): Unit = {
    multipleLpData.lpData.foreach { singleLpData =>
      val startRow = singleLpData.startingRow
      singleLpData.fieldDatas.foreach { singleFieldData =>
        val startCol = singleFieldData.startingCol
        if (singleFieldData.repeatAllRows && singleFieldData.dataValues.size == 1) {
          val firstRowDataValues = singleFieldData.dataValues.headOption.map(_.values).getOrElse(Seq.empty)
          Range(0, singleLpData.numberOfRow).foreach { offsetRow =>
            FillSpreadsheet.fillRow(
              sheet,
              startRow + offsetRow,
              startCol,
              firstRowDataValues.map(FillSheetCell(_))
            )
          }
        } else {
          singleFieldData.dataValues.zipWithIndex.foreach { case (rowValues, offsetRow) =>
            FillSpreadsheet.fillRow(
              sheet,
              startRow + offsetRow,
              startCol,
              rowValues.values.map(FillSheetCell(_))
            )
          }
        }
      }
    }
  }

  // Load workbook from source and compute template using 3 rows start from startRow
  // rows are 0-based index.
  def loadTemplateFromInputStream(
    inputStream: InputStream,
    sheet: Int,
    aliasRowIndex: Int,
    startCol: Int
  ): Task[FundSubExportTemplate] = {
    for {
      fieldConfigs <- SpreadsheetUtils.useWorkbookInputStream(inputStream) { workbook =>
        ZIO.attemptBlocking {
          loadFieldConfigsFromWorkbook(
            workbook,
            sheet,
            aliasRowIndex,
            startCol
          )
        }
      }
    } yield FundSubExportTemplate(
      sheet = sheet,
      startRow = aliasRowIndex,
      startCol = startCol,
      fieldConfigs = fieldConfigs
    )
  }

  private def loadFieldConfigsFromWorkbook(
    workbook: Workbook,
    sheetIndex: Int,
    aliasRowIndex: Int,
    startCol: Int
  ): Seq[FundSubExportFieldConfig] = {
    ReadSpreadsheet
      .readData(
        workbook = workbook,
        sheetIndex = sheetIndex,
        startRow = aliasRowIndex,
        startCol = startCol
      )
      .flatMap(_.rows.headOption)
      .filter(_.rowIndex == aliasRowIndex)
      .map(_.cells)
      .getOrElse(List.empty)
      .map { cell =>
        FundSubExportFieldConfig(
          alias = cell.value,
          fromCol = cell.colIndex
        )
      }
  }

  def generateDefaultExportTemplate(
    fundSubId: FundSubId,
    actorId: UserId,
    // taxFormInfoOpt is None -> main form, else -> a specific tax form
    taxFormInfoOpt: Option[FormReferenceInfo],
    customColumnInfoList: Seq[CustomDataColumnInfo] = Seq.empty
  )(
    using formService: FormService,
    fileService: FileService,
    dynamicFormStorageService: DynamicFormStorageService
  ): Task[FundSubExportTemplate] = {
    val (fileName, templateName) = taxFormInfoOpt.fold(
      "Investor_data.xlsx" -> defaultTemplateName
    ) { taxFormInfo =>
      s"Investor_${taxFormInfo.name}_data.xlsx" -> s"${taxFormInfo.name} - Anduin Default"
    }

    for {
      formIdEither <- taxFormInfoOpt.fold(
        FundSubCommonUtils.getFundSubMainFormId(fundSubId)
      )(taxFormInfo => ZIO.attempt(taxFormInfo.formId))
      formInfo <- FundSubCommonUtils.getFundSubFormInfoFromId(formIdEither, actorId)
      templateStream <- generateDefaultExportTemplateFileSource(
        formInfo,
        taxFormInfoOpt.nonEmpty,
        customColumnInfoList
      )
      templateBytes <- ZStreamIOUtils.toByteArray(templateStream)
      generatedFileId <- fileService.uploadFile(
        FolderId.channelSystemFolderId(FundSubAdminRestrictedId(fundSubId)),
        fileName,
        FileContentOrigin.FromSource(
          ZStreamIOUtils.fromByteArray(templateBytes),
          MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        ),
        actorId
      )
      templateId = FundSubExportTemplateIdFactory.unsafeRandomId(fundSubId)
      template <- FundSubExportHelper.loadTemplateFromInputStream(
        new ByteArrayInputStream(templateBytes),
        0,
        2,
        0
      )
    } yield template.copy(
      templateFileOpt = Some(generatedFileId),
      templateName = templateName,
      templateId = templateId,
      taxFormReferenceOpt = taxFormInfoOpt.map { taxFormInfo =>
        FormReference(
          taxFormInfo.name,
          taxFormInfo.formIdString,
          taxFormInfo.groupName
        )
      },
      shouldExportOptionLabel = Option(true),
      shouldExportCsvOpt = Option(false)
    )
  }

  def generateDefaultExportTemplateFile(
    fundSubId: FundSubId,
    actorId: UserId,
    // taxFormInfoOpt is None -> main form, else -> a specific tax form
    taxFormInfoOpt: Option[FormReferenceInfo],
    customColumnInfoList: Seq[CustomDataColumnInfo] = Seq.empty
  )(
    using formService: FormService,
    fileService: FileService,
    dynamicFormStorageService: DynamicFormStorageService
  ): Task[FileId] = {
    val fileName = taxFormInfoOpt.fold(
      "Investor_data.xlsx"
    ) { taxFormInfo =>
      s"Investor_${taxFormInfo.name}_data.xlsx"
    }

    for {
      formIdEither <- taxFormInfoOpt.fold(
        FundSubCommonUtils.getFundSubMainFormId(fundSubId)
      )(taxFormInfo => ZIO.attempt(taxFormInfo.formId))
      formInfo <- FundSubCommonUtils.getFundSubFormInfoFromId(formIdEither, actorId)
      templateSource <- generateDefaultExportTemplateFileSource(
        formInfo,
        taxFormInfoOpt.nonEmpty,
        customColumnInfoList
      )
      generatedFileId <- fileService.uploadFile(
        FolderId.channelSystemFolderId(FundSubAdminRestrictedId(fundSubId)),
        fileName,
        FileContentOrigin.FromSource(
          templateSource,
          MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        ),
        actorId
      )
    } yield generatedFileId
  }

  private def generateDefaultExportTemplateFileSource(
    form: Either[DynamicFormModel, FormData],
    forTaxForm: Boolean,
    customColumnInfoList: Seq[CustomDataColumnInfo]
  ): Task[Stream[Throwable, Byte]] = {
    for {
      (sheetData, mergedRegions) <- zio.ZIO.attempt {
        form match {
          case Left(dynaFormModel) =>
            computeDefaultExportTemplateForDynamicForm(dynaFormModel.form.getOrElse(DynamicFormSection()), forTaxForm)
          case Right(gaiaFormModel) =>
            computeDefaultExportTemplateForGaiaForm(
              gaiaFormModel.form,
              forTaxForm,
              customColumnInfoList
            )
        }
      }
      result <- zio.ZIO
        .attempt {
          new XSSFWorkbook()
        }
        .bracket { workbook =>
          val sheet = workbook.createSheet("Raw Data")
          for {
            _ <- zio.ZIO.attempt {
              FillSpreadsheet.fillSheet(sheet, sheetData)
              if (mergedRegions.nonEmpty) {
                val startColIdx = mergedRegions.head.startColIdx
                val endColIdx = mergedRegions.last.endColIdx + 1
                FillSpreadsheet.setVerticalAlignment(
                  workbook,
                  sheet,
                  0,
                  startColIdx,
                  endColIdx
                )
              }
              val mergeRegionsFilter = mergedRegions.filter(region => region.startColIdx != region.endColIdx)
              FillSpreadsheet.merge(sheet, mergeRegionsFilter)
              FillSpreadsheet.setBoldRow(
                workbook,
                sheet,
                1
              )
              FillSpreadsheet.setFitContent(sheet)
            }
            resultSource <- SpreadsheetUtils.workbookToStream(workbook)
          } yield resultSource
        } { workbook =>
          ZIO.succeed {
            workbook.close()
          }
        }
    } yield result
  }

  private[dataexport] def computeDefaultExportTemplateForDynamicForm(
    form: DynamicFormSection,
    forTaxForm: Boolean
  ): (FillSheetData, List[MergeRegion]) = {
    val fieldsLabelAndAlias = FormHelper.traverseAndExtractSection(
      FormProperty.buildFullDuplicate(form).formWithDuplicate,
      section => {
        val inputType = section.formDescription.map(_.inputType).getOrElse(InputType.None)
        Option
          .when(FundSubExportHelper.DefaultInputTypeToExportForDynamicForm.contains(inputType)) {
            val label = section.formDescription.map(_.label).getOrElse("")
            val aliasOrId = if (section.alias.trim.nonEmpty) section.alias else s"${section.id}:i"
            FillSheetCell(label) -> FillSheetCell(aliasOrId)
          }
          .toSeq
      }
    )
    val predefinedAliasTemplate = if (forTaxForm) {
      PredefinedAliasToExportForDefaultTemplateTaxForm
    } else {
      PredefinedAliasToExportForDefaultTemplateMainForm
    }

    (
      FillSheetData(
        startCol = 0,
        startRow = 0,
        rows = List(
          FillSheetRow(cells = List.empty),
          FillSheetRow(cells =
            predefinedAliasTemplate.map(_._1).map(FillSheetCell.apply(_)) ++ fieldsLabelAndAlias.map(_._1) ++
              PostfixPredefinedAliasToExportForDefaultTemplate.map(_._1).map(FillSheetCell.apply(_))
          ),
          FillSheetRow(cells =
            predefinedAliasTemplate.map(_._2.value).map(FillSheetCell.apply(_)) ++ fieldsLabelAndAlias.map(_._2) ++
              PostfixPredefinedAliasToExportForDefaultTemplate.map(_._2.value).map(FillSheetCell.apply(_))
          )
        )
      ),
      List()
    )
  }

  private[dataexport] def computeDefaultExportTemplateForGaiaForm(
    form: Form,
    forTaxForm: Boolean,
    customColumnInfoList: Seq[CustomDataColumnInfo]
  ): (FillSheetData, List[MergeRegion]) = {
    val fields = traverseFormForExportedSection(form)
    val predefinedAliasTemplate = if (forTaxForm) {
      PredefinedAliasToExportForDefaultTemplateTaxForm
    } else {
      PredefinedAliasToExportForDefaultTemplateMainForm
    }
    val prefixPredefinedData = predefinedAliasTemplate.map(_._1).map(FillSheetCell.apply(_)) ++ customColumnInfoList
      .map(_.name)
      .map(FillSheetCell.apply(_))
    val startFieldColumnIndex = prefixPredefinedData.size
    val prefixPadding = List.fill(startFieldColumnIndex)("").map(FillSheetCell.apply(_))

    val fieldSections = fields.map(_._1)
    val fieldSectionsRangeIndexes = ListUtils
      .getRangeIndexes(fieldSections.toList)
    val mergedRegions =
      fieldSectionsRangeIndexes.map(x =>
        MergeRegion(
          0,
          0,
          startFieldColumnIndex + x._1,
          startFieldColumnIndex + x._2
        )
      )

    (
      FillSheetData(
        startCol = 0,
        startRow = 0,
        rows = List(
          FillSheetRow(cells = prefixPadding ++ fieldSections),
          FillSheetRow(cells =
            prefixPredefinedData ++
              fields.map(_._2) ++ PostfixPredefinedAliasToExportForDefaultTemplate.map(_._1).map(FillSheetCell.apply(_))
          ),
          FillSheetRow(cells =
            predefinedAliasTemplate.map(_._2.value).map(FillSheetCell.apply(_)) ++
              customColumnInfoList
                .map(colInfo => s"${PredefinedAlias.CustomDashboardColumn.value}:${colInfo.id}")
                .map(FillSheetCell.apply(_)) ++
              fields.map(_._3) ++
              PostfixPredefinedAliasToExportForDefaultTemplate.map(_._2.value).map(FillSheetCell.apply(_))
          )
        )
      ),
      mergedRegions
    )
  }

  private def traverseFormForExportedSection(form: Form): Seq[(FillSheetCell, FillSheetCell, FillSheetCell)] = {
    val schema = form.defaultSchema
    val gaiaFieldsWithPageTitle = FormImportUtils
      .extractGaiaFieldsExpandingRepeatables(form)
      .filterNot(field =>
        field._1.hiddenByType || !schema.visibleBySetting.contains(field._1.field.name) // ignore invisible fields
      )

    gaiaFieldsWithPageTitle.map { case (gaiaField, pageTitle) =>
      val formattedTextOpt = gaiaField.widget.uiOptions
        .get(UIKey.formattedText)
        .filter(_.trim.nonEmpty)
        .map(
          // Sanitize HTML string & truncate too long header
          getTextFromHtml(_, ExportHeaderLengthThreshold)
        )
      val field = gaiaField.field
      val header = formattedTextOpt.getOrElse(
        field.tpe.title.getOrElse(gaiaField.alias)
      )
      (FillSheetCell(pageTitle), FillSheetCell(header), FillSheetCell(gaiaField.alias))
    }
  }

  def computeFillSheetDataForPredefinedAliasInclusive(
    lpsData: Seq[LpData],
    predefinedAliases: Seq[PredefinedAlias],
    mainFormInfo: Option[FormInfo],
    taxFormFileIds: Seq[FileId]
  ): FillSheetData = {
    val aliasesToExport = predefinedAliases

    val aliasRow = FillSheetRow(
      cells = aliasesToExport.map { alias => FillSheetCell(alias.value) }.toList
    )

    val lpDataRows = lpsData.map { lpData =>
      FillSheetRow(
        cells = aliasesToExport.map { alias =>
          FillSheetCell(computeDataForPredefinedAlias(lpData, alias, mainFormInfo, taxFormFileIds))
        }.toList
      )
    }

    FillSheetData(
      startCol = 0,
      startRow = 0,
      rows = List(aliasRow) ++ lpDataRows
    )
  }

  val DefaultInputTypeToExportForDynamicForm: Seq[InputType] = Seq(
    InputType.String,
    InputType.Integer,
    InputType.Float,
    InputType.Percentage,
    InputType.Email,
    InputType.Checkbox,
    InputType.MultipleCheckbox,
    InputType.Radio,
    InputType.Money,
    InputType.Dropdown,
    InputType.Date,
    InputType.DateTime,
    InputType.TextArea,
    InputType.CustomFormat,
    InputType.CurrentDate,
    InputType.PhoneNumber,
    InputType.USTIN,
    InputType.PhoneNumberWithCountryCode
  )

  val PredefinedAliasToExportForDefaultTemplateMainForm: List[(String, PredefinedAlias)] = List(
    "Investor email" -> PredefinedAlias.InvestorEmailAlias,
    Terms.InvesteeEntityLabel -> PredefinedAlias.FirmNameAlias,
    "Investor group" -> PredefinedAlias.InvestorGroup,
    "Status" -> PredefinedAlias.InvestorStatusAlias,
    "Estimated commitment" -> PredefinedAlias.ExpectedCommitment,
    "Capital commitment" -> PredefinedAlias.SubmittedCommitment,
    "Accepted commitment" -> PredefinedAlias.AcceptedCommitment,
    "Filled on/off Anduin" -> PredefinedAlias.FilledOnOffAnduin,
    "Signature" -> PredefinedAlias.InvestorSigningType,
    "Closing date" -> PredefinedAlias.ClosingDateAlias,
    "Date submitted" -> PredefinedAlias.DateSubmitted
  )

  val PredefinedAliasToExportForDefaultTemplateTaxForm: List[(String, PredefinedAlias)] = List(
    "Investor email" -> PredefinedAlias.InvestorEmailAlias,
    Terms.InvesteeEntityLabel -> PredefinedAlias.FirmNameAlias,
    "Investor group" -> PredefinedAlias.InvestorGroup,
    "Status" -> PredefinedAlias.InvestorStatusAlias,
    "Has data filled off Anduin" -> PredefinedAlias.HasTaxDataFilledOffAnduin
  )

  val PostfixPredefinedAliasToExportForDefaultTemplate: List[(String, PredefinedAlias)] = List(
    "Custom ID" -> PredefinedAlias.CustomLpIdAlias
  )

  private val CommaSeparator = ", "

  private val SemicolonSeparator = "; "

  private val NewLineSeparator = "\n"

  private val defaultTemplateName = "Anduin Default Template"

}
