// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.dataexport.selfservice

import java.time.Instant

import org.apache.poi.xssf.usermodel.XSSFWorkbook
import sttp.model.MediaType
import zio.implicits.*
import zio.prelude.NonEmptyList
import zio.{Scope, Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.annotation.service.AnnotationDocumentService
import anduin.dashboard.data.*
import anduin.dashboard.model.*
import anduin.dashboard.service.DashboardService
import anduin.dataexport.selfservice.SelfServiceExportTemplatePublicModels.FieldType.PdfFieldType
import anduin.dataexport.selfservice.SelfServiceExportTemplatePublicModels.{
  FieldInfo,
  FieldType,
  InvestorData,
  TemplateData
}
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.documentcontent.pdf.PdfBoxHelper
import anduin.documentcontent.spreadsheet.SpreadsheetUtils
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{DefaultCluster, FDBRecordDatabase}
import anduin.forms.FormData
import anduin.forms.annotation.*
import anduin.forms.endpoint.GetFormResponse
import anduin.forms.engine.GaiaState
import anduin.forms.service.FormService
import anduin.forms.ui.{UIKey, WidgetType}
import anduin.forms.util.annotation.PdfAnnotationUtils
import anduin.forms.utils.*
import anduin.fundsub.FundSubLoggingService
import anduin.fundsub.auditlog.{AuditLogActorType, AuditLogEventType, FundSubAuditLogService}
import anduin.fundsub.dataexport.selfservice.FundSubSelfServiceExportService.*
import anduin.fundsub.dataexport.{FundSubExportHelper, FundSubGaiaExportHelper}
import anduin.fundsub.endpoint.dataexport.*
import anduin.fundsub.exception.FundSubException.SelfServiceExport.{
  FeatureSwitchNotEnabledException,
  NoPermissionException,
  TemplateForApiExportOnlyException,
  TemplateRemovedException
}
import anduin.fundsub.exporttemplate.{
  FieldPageLocationModel,
  FieldTypeModel,
  FundSubSelfServiceExportTemplateModel,
  SelectedFieldModel
}
import anduin.fundsub.form.utils.FundSubCommonUtils
import anduin.fundsub.model.FundSubSharedClientModel.ParticipantInfo
import anduin.fundsub.models.FundSubModelStoreOperations
import anduin.fundsub.rebac.FundSubRebacModel
import anduin.fundsub.rebac.FundSubRebacModel.FundPermission
import anduin.fundsub.service.{FundSubPermissionService, FundSubPermissionUtils, LPDataOperations}
import anduin.fundsub.status.LpStatusSharedUtils
import anduin.fundsub.supportingdoc.SupportingDocReviewConfigOperations
import anduin.fundsub.user.FundSubUserTrackingService
import anduin.fundsub.view.FundSubViewService
import anduin.id.annotation.{AnnotationDocumentId, AnnotationDocumentVersionId}
import anduin.id.form.FormVersionId
import anduin.id.fundsub.*
import anduin.id.review.SupportingDocReviewConfigId
import anduin.model.common.user.UserId
import anduin.model.id.{FileId, FolderId, FundSubSelfServiceExportTemplateIdFactory}
import anduin.ontology.service.FormSaProfileMappingService
import anduin.portaluser.PortalAdmin
import anduin.protobuf.activitylog.fundsub.admin.*
import anduin.protobuf.external.squants.CurrencyMessage
import anduin.protobuf.fundsub.models.{FundSubAdminRestrictedModel, FundSubPublicModel, InvestmentFundModel}
import anduin.rebac.RebacModel.implicits.given
import anduin.rebac.{RebacModel, RebacStoreOperation}
import anduin.service.GeneralServiceException
import anduin.storageservice.common.FileContentOrigin
import anduin.util.CurrencyUtils
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.service.utils.ZIOUtils

case class FundSubSelfServiceExportService(
  formService: FormService,
  fileService: FileService,
  fundSubPermissionService: FundSubPermissionService,
  fundSubUserTrackingService: FundSubUserTrackingService,
  userProfileService: UserProfileService,
  fundSubAuditLogService: FundSubAuditLogService,
  fundSubViewService: FundSubViewService,
  dashboardService: DashboardService,
  fundSubLoggingService: FundSubLoggingService,
  annotationDocumentService: AnnotationDocumentService,
  formSaProfileMappingService: FormSaProfileMappingService,
  portalAdmin: PortalAdmin
) {

  private def checkIfUserCanExportFormAndDashboardData(
    fundSubId: FundSubId,
    actorId: UserId
  )(
    using RebacStoreOperation
  ): Task[Boolean] = {
    for {
      canExportInvestorFormData <- fundSubPermissionService
        .checkIfUserHasPermissionR(
          userId = actorId,
          permission = FundPermission.ExportInvestorFormData,
          obj = FundSubRebacModel.Type.FundSub(fundSubId)
        )
      canExportInvestorDashboardData <-
        fundSubPermissionService.checkIfUserHasPermissionR(
          userId = actorId,
          permission = FundPermission.ExportInvestorDashboardData,
          obj = FundSubRebacModel.Type.FundSub(fundSubId)
        )
    } yield canExportInvestorFormData && canExportInvestorDashboardData
  }

  private def validateUserCanExportFormDataAndDashboardData(
    fundSubId: FundSubId,
    actorId: UserId
  )(
    using RebacStoreOperation
  ): Task[Unit] = {
    for {
      canExportFormAndDashboardData <- checkIfUserCanExportFormAndDashboardData(fundSubId, actorId)
      _ <- ZIOUtils.failWhen(!canExportFormAndDashboardData)(
        NoPermissionException(fundSubId, actorId)
      )
    } yield ()
  }

  def getDataToCreateSelfServiceExport(
    fundSubId: FundSubId,
    templateIdOpt: Option[FundSubSelfServiceExportTemplateId],
    actorId: UserId
  ): Task[GetDataOfSelfServiceExportResp] = {
    for {
      _ <- ZIO.logInfo(s"User $actorId gets form data and files for self service export for fund $fundSubId")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- validateUserCanExportFormDataAndDashboardData(fundSubId, actorId)
      _ <- ZIOUtils.traverseOption(templateIdOpt) { templateId =>
        ZIOUtils.failWhen(templateId.parent != fundSubId)(
          GeneralServiceException(s"Template $templateId must be in $fundSubId")
        )
      }
      templateModelOpt <- ZIOUtils.traverseOption(templateIdOpt) { templateId =>
        FDBRecordDatabase.transact(FundSubSelfServiceExportOperations.Production)(_.get(templateId))
      }
      _ <- ZIOUtils.traverseOption(templateModelOpt) { templateModel =>
        ZIOUtils.failWhen(templateModel.isDeleted) {
          TemplateRemovedException(templateModel.templateId)
        }
      }
      // For simplicity, we enforce that the form_version_id of a template cannot be changed once a template is created
      formVersionId <- templateModelOpt.fold {
        for {
          adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
            ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
          }
          formVersionId <- ZIOUtils.optionToTask(
            adminResModel.formVersions.headOption.map(_.id),
            GeneralServiceException(
              s"Cannot find form version id for fund $fundSubId"
            )
          )
        } yield formVersionId
      } { templateModel =>
        ZIO.succeed(templateModel.formVersionId)
      }
      formData <- formService
        .getForm(
          formId = formVersionId.parent,
          versionIdOpt = Some(formVersionId),
          actor = actorId,
          shouldCheckPermission = false
        )
        .map(_.formData)
      userHasSeenSelfServiceExportGuideTour <- fundSubUserTrackingService
        .getUserTracking(actorId)
        .map(_.seenSelfServiceExportGuideTour)
    } yield {
      val fileInfos = SubscriptionDocumentUtils.getAllSubDocs(formData)
      val forApiExport = templateModelOpt.exists(_.forApiExport)
      val pdfFieldsLabelMap = if (forApiExport) {
        FundSubGaiaExportHelper.getPdfFieldsLabelMap(formData).flatMap { case (fieldName, labels) =>
          labels.find(_.nonEmpty).map(fieldName -> _)
        }
      } else {
        Map.empty
      }
      GetDataOfSelfServiceExportResp(
        formFiles = fileInfos.sortBy(_._2.toLowerCase),
        selectedFieldInfos = templateModelOpt.fold(Seq.empty) { templateModel =>
          templateModel.selectedFields.flatMap { selectedFieldModel =>
            val fieldIdOpt = if (selectedFieldModel.fieldType.isDashboardField) {
              Some(ExportTemplateFieldIdentifier.DashboardColumnField(selectedFieldModel.fieldId))
            } else {
              selectedFieldModel.locationOpt.map { location =>
                ExportTemplateFieldIdentifier.PdfField(location.fileId, selectedFieldModel.fieldId)
              }
            }
            fieldIdOpt.map { fieldId =>
              val fieldType = fromProtoToFieldType(selectedFieldModel.fieldType)
              val displayNameOpt = fieldType match {
                case PdfFieldType.RadioGroup => pdfFieldsLabelMap.get(fieldId.value)
                case _                       => None
              }
              val displayOptionNameMap = fieldType match {
                case PdfFieldType.RadioGroup =>
                  selectedFieldModel.revisedOptionNameMap.keys.flatMap { rawName =>
                    pdfFieldsLabelMap.get(rawName).map(rawName -> _)
                  }.toMap
                case _ => Map.empty
              }
              SelectedFieldOfSelfServiceExport(
                fieldId = fieldId,
                fieldType = fieldType,
                revisedName = selectedFieldModel.revisedName,
                revisedOptionNameMap = selectedFieldModel.revisedOptionNameMap,
                displayNameOpt = displayNameOpt,
                displayOptionNameMap = displayOptionNameMap,
                fieldPageLocationOpt = selectedFieldModel.locationOpt.map(buildFieldPageLocation)
              )
            }
          }
        },
        userHasSeenSelfServiceExportGuideTour = userHasSeenSelfServiceExportGuideTour,
        forApiExport = templateModelOpt.exists(_.forApiExport)
      )
    }
  }

  private def buildFieldPageLocation(locationModel: FieldPageLocationModel): FieldPageLocationInfo =
    FieldPageLocationInfo(
      fileId = locationModel.fileId,
      pageIndex = locationModel.pageIndex
    )

  private def convertPdfAnnotationToPdfFieldForSelfServiceExport(
    field: PdfField,
    pdfObjects: PdfObjects,
    fileId: FileId,
    pdfFieldsLabelMap: Map[String, String],
    excludedFields: Set[String]
  ): Option[PdfFieldForSelfServiceExport] = {
    for {
      fieldName <- field match {
        case checkbox: PdfCheckboxField     => Some(checkbox.name)
        case radioGroup: PdfRadioGroupField => Some(radioGroup.name)
        case text: PdfTextField             => Some(text.name)
        case paragraph: PdfParagraphField   => Some(paragraph.name)
        case _                              => Option.empty[String]
      }
      if !excludedFields.contains(fieldName)
      fieldType <- field match {
        case _: PdfCheckboxField   => Some(PdfFieldType.Checkbox)
        case _: PdfRadioGroupField => Some(PdfFieldType.RadioGroup)
        case _: PdfTextField       => Some(PdfFieldType.Text)
        case _: PdfParagraphField  => Some(PdfFieldType.Text)
        case _                     => Option.empty[PdfFieldType]
      }
      displayName = pdfFieldsLabelMap.getOrElse(fieldName, fieldName)
      displayOptionNameMap = field match {
        case radioGroup: PdfRadioGroupField =>
          radioGroup.children.flatMap { option =>
            pdfObjects.getName(option).map { rawName =>
              val displayName = pdfFieldsLabelMap.getOrElse(rawName, rawName)
              (rawName, displayName)
            }
          }.toMap
        case _ => Map.empty[String, String]
      }
      locations <- field match {
        case checkbox: PdfCheckboxField =>
          Some(
            NonEmptyList.Single(
              PdfAnnotationLocationForSelfServiceExport(
                area = checkbox.area,
                pageIndex = checkbox.pageIndex
              )
            )
          )
        case radioGroup: PdfRadioGroupField =>
          NonEmptyList.fromIterableOption(
            pdfObjects.getPageIndexAreas(radioGroup.id).groupMap(_._1)(_._2).flatMap { case (pageIndex, areaSeq) =>
              NonEmptyList.fromIterableOption(areaSeq).map { neAreaSeq =>
                PdfAnnotationLocationForSelfServiceExport(
                  area = Area2D.bounding(neAreaSeq),
                  pageIndex = pageIndex
                )
              }
            }
          )
        case text: PdfTextField =>
          Some(
            NonEmptyList.Single(
              PdfAnnotationLocationForSelfServiceExport(
                area = text.area,
                pageIndex = text.pageIndex
              )
            )
          )
        case paragraph: PdfParagraphField =>
          pdfObjects.getPageIndexArea(paragraph.id).map { (pageIndex, area) =>
            NonEmptyList.Single(
              PdfAnnotationLocationForSelfServiceExport(
                area = area,
                pageIndex = pageIndex
              )
            )
          }
        case _ => Option.empty[NonEmptyList[PdfAnnotationLocationForSelfServiceExport]]
      }
    } yield PdfFieldForSelfServiceExport(
      locations = locations,
      rawName = fieldName,
      displayName = displayName,
      fieldType = fieldType,
      fileId = fileId,
      displayOptionNameMap = displayOptionNameMap
    )
  }

  def getAnnotationDataForSelfServiceExport(
    fundSubId: FundSubId,
    fileId: FileId,
    actorId: UserId
  ): Task[GetAnnotationDataForSelfServiceExportResp] = {
    for {
      _ <- ZIO.logInfo(s"User $actorId gets annotation data for file $fileId in fund $fundSubId")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- validateUserCanExportFormDataAndDashboardData(fundSubId, actorId)
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
      }
      formVersionId <- ZIOUtils.optionToTask(
        adminResModel.formVersions.headOption.map(_.id),
        GeneralServiceException(
          s"Cannot find form version id for fund $fundSubId"
        )
      )
      getFormResp <- formService
        .getForm(
          formId = formVersionId.parent,
          versionIdOpt = Some(formVersionId),
          actor = actorId,
          shouldCheckPermission = false
        )
      formData = getFormResp.formData
      allSignatureMappings = FormSignatureUtils
        .getAllSignatureMappingInfo(formData.form)
        .flatMap(_.mappings.map(_.mapping))
        .toSet
      pdfFieldsLabelMap = FundSubGaiaExportHelper.getPdfFieldsLabelMap(formData).flatMap { case (fieldName, labels) =>
        labels.find(_.nonEmpty).map(fieldName -> _)
      }
      resp <- getAnnotationDataForSelfServiceExportInternal(
        fileId = fileId,
        actorId = actorId,
        annotationDocumentVersionIdOpt = getFormResp.systemMetadata.annotationMapping.get(fileId),
        pdfFieldsLabelMap = pdfFieldsLabelMap,
        excludedFields = allSignatureMappings
      )
    } yield resp
  }

  private def getAnnotationDataForSelfServiceExportInternal(
    fileId: FileId,
    actorId: UserId,
    annotationDocumentVersionIdOpt: Option[AnnotationDocumentVersionId],
    pdfFieldsLabelMap: Map[String, String],
    excludedFields: Set[String]
  ): Task[GetAnnotationDataForSelfServiceExportResp] = {
    ZIO.scoped {
      for {
        pdfObjects <- annotationDocumentVersionIdOpt.fold(
          extractPdfAnnotationsFromPdfInternals(
            fileId,
            actorId
          )
        ) { annotationDocumentVersionId =>
          for {
            portalAdminUserId <- portalAdmin.userId
            pdfObjects <- annotationDocumentService
              .getAnnotationDocument(
                annotationDocumentVersionId.parent,
                Some(annotationDocumentVersionId),
                portalAdminUserId
              )(
                using AnnotationDocumentService.KeySpace.Production
              )
              .map(_.annotationData.pdfObjects)
          } yield pdfObjects
        }
      } yield {
        val pdfFieldsForSelfServiceExport = for {
          pdfField <- pdfObjects.flatMap {
            case _: PdfTextBlock => Iterator.empty
            case _: PdfTextLabel => Iterator.empty
            case field: PdfField => Iterator.single(field)
          }.toList
          fieldInfo <- convertPdfAnnotationToPdfFieldForSelfServiceExport(
            field = pdfField,
            pdfObjects = pdfObjects,
            fileId = fileId,
            pdfFieldsLabelMap = pdfFieldsLabelMap,
            excludedFields = excludedFields
          )
        } yield fieldInfo
        val pdfFieldsSorted = pdfFieldsForSelfServiceExport.sorted
        GetAnnotationDataForSelfServiceExportResp(pdfFieldsSorted)
      }
    }
  }

  private def extractPdfAnnotationsFromPdfInternals(
    fileId: FileId,
    actorId: UserId
  ): ZIO[Any & Scope, Throwable, PdfObjects] = {
    for {
      fileSource <- fileService.getFileSource(actorId, fileId)
      pdDocument <- PdfBoxHelper.mkDocumentFromStream(fileSource, None)
      pdfObjects <- ZIO.attempt(PdfAnnotationUtils.loadPdfAnnotationData(pdDocument))
    } yield pdfObjects
  }

  def getDashboardColumnInfosForSelfServiceExport(
    fundSubId: FundSubId,
    actorId: UserId
  ): Task[GetDashboardColumnInfosForSelfServiceExportResp] = {
    for {
      _ <- ZIO.logInfo(s"User $actorId get dashboard column infos for self service export")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- validateUserCanExportFormDataAndDashboardData(fundSubId, actorId)
      allAvailableColumns <- fundSubViewService.getAvailableColumnsOfAccessibleViews(
        fundSubId,
        actorId
      )
    } yield GetDashboardColumnInfosForSelfServiceExportResp(
      availableColumns = allAvailableColumns
    )
  }

  def saveSelfServiceExportTemplate(
    fundSubId: FundSubId,
    templateName: String,
    selectedFields: Seq[SelectedFieldOfSelfServiceExport],
    fileIds: Seq[FileId],
    templateIdOpt: Option[FundSubSelfServiceExportTemplateId],
    forApiExport: Boolean = false,
    actorId: UserId
  ): Task[FundSubSelfServiceExportTemplateId] = {
    for {
      _ <- templateIdOpt.fold(
        ZIO.logInfo(s"User $actorId creates self service export template in fund $fundSubId")
      ) { templateId =>
        ZIO.logInfo(s"User $actorId edits template $templateId in fund $fundSubId")
      }
      fundSubModel <- validateFeatureSwitchEnabled(fundSubId)
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- validateUserCanExportFormDataAndDashboardData(fundSubId, actorId)
      fsAdminRestrictedModel <- FDBRecordDatabase
        .transact(FundSubModelStoreOperations.Production)(_.getFundSubAdminRestrictedModel(fundSubId))

      now = Instant.now
      // At the moment, we only support renaming radio field options for API export templates
      refinedSelectedFields = selectedFields.distinctBy(_.fieldId).map { field =>
        field.copy(
          revisedOptionNameMap = field.fieldType match {
            case PdfFieldType.RadioGroup => field.revisedOptionNameMap
            case _                       => Map.empty
          }
        )
      }
      oldTemplateOpt <- ZIOUtils
        .traverseOption(templateIdOpt) { templateId =>
          FDBRecordDatabase.transact(FundSubSelfServiceExportOperations.Production)(_.getOpt(templateId))
        }
        .map(_.flatten)
      templateFormVersionId <- ZIOUtils
        .fromOption(
          oldTemplateOpt
            .map(_.formVersionId)
            .orElse(fsAdminRestrictedModel.formVersions.headOption.map(_.id)),
          GeneralServiceException(
            s"Cannot find form version id for fund $fundSubId"
          )
        )
      selectedFieldModels <- buildSelectedFieldModels(
        refinedSelectedFields,
        fileIds,
        actorId,
        templateFormVersionId
      )
      newTemplate <- templateIdOpt
        .fold {
          FDBRecordDatabase.transact(FundSubSelfServiceExportOperations.Production) { ops =>
            val newTemplate = FundSubSelfServiceExportTemplateModel(
              templateId = FundSubSelfServiceExportTemplateIdFactory.unsafeRandomId(fundSubId),
              templateName = templateName,
              createdAt = Some(now),
              createdBy = actorId,
              lastUpdatedAt = Some(now),
              lastUpdatedBy = actorId,
              selectedFields = selectedFieldModels,
              formVersionId = templateFormVersionId,
              forApiExport = forApiExport
            )
            ops.create(newTemplate).as(newTemplate)
          }
        } { templateId =>
          FDBRecordDatabase.transact(FundSubSelfServiceExportOperations.Production) { ops =>
            for {
              oldModel <- RecordIO.fromOption(
                oldTemplateOpt,
                GeneralServiceException(s"Cannot find template with id $templateId")
              )
              _ <- RecordIO.when(oldModel.isDeleted) {
                RecordIO.fail(TemplateRemovedException(oldModel.templateId))
              }
              newModel <- ops.update(templateId)(
                _.copy(
                  templateName = templateName,
                  lastUpdatedBy = actorId,
                  lastUpdatedAt = Some(now),
                  selectedFields = selectedFieldModels,
                  forApiExport = forApiExport
                )
              )
            } yield newModel
          }
        }
      _ <- oldTemplateOpt.fold[Task[Unit]](
        fundSubAuditLogService
          .addEvent(
            fundSubId = fundSubId,
            params = FundSubAuditLogService.AddEventParam(
              actor = Some(actorId),
              actorType = AuditLogActorType.FundSide,
              eventType = AuditLogEventType.EXPORT_TEMPLATE_CREATED,
              activityDetail = SelfServiceExportTemplateCreated(
                templateId = newTemplate.templateId,
                templateName = newTemplate.templateName
              )
            )
          )
          .unit
      ) { oldTemplate =>
        for {
          _ <- ZIO.when(oldTemplate.templateName != newTemplate.templateName) {
            fundSubAuditLogService.addEvent(
              fundSubId = fundSubId,
              params = FundSubAuditLogService.AddEventParam(
                actor = Some(actorId),
                actorType = AuditLogActorType.FundSide,
                eventType = AuditLogEventType.EXPORT_TEMPLATE_RENAMED,
                activityDetail = SelfServiceExportTemplateRenamed(
                  templateId = newTemplate.templateId,
                  oldTemplateName = oldTemplate.templateName,
                  newTemplateName = newTemplate.templateName
                )
              )
            )
          }
          _ <- ZIO.when(oldTemplate.selectedFields != newTemplate.selectedFields) {
            fundSubAuditLogService.addEvent(
              fundSubId = fundSubId,
              params = FundSubAuditLogService.AddEventParam(
                actor = Some(actorId),
                actorType = AuditLogActorType.FundSide,
                eventType = AuditLogEventType.EXPORT_TEMPLATE_UPDATED,
                activityDetail = SelfServiceExportTemplateUpdated(
                  templateId = newTemplate.templateId,
                  templateName = newTemplate.templateName
                )
              )
            )
          }
        } yield ()
      }
      _ <- fundSubLoggingService.logEventSelfServiceTemplateSaved(
        userId = actorId,
        templateId = newTemplate.templateId,
        templateName = newTemplate.templateName,
        fundSubId = fundSubId,
        fundName = fundSubModel.fundName,
        fields = newTemplate.selectedFields,
        isCreatingNewTemplate = oldTemplateOpt.isEmpty,
        isApiTemplate = forApiExport
      )

    } yield newTemplate.templateId
  }

  private def buildSelectedFieldModels(
    fields: Seq[SelectedFieldOfSelfServiceExport],
    fileIds: Seq[FileId],
    actorId: UserId,
    formVersionId: FormVersionId
  ): Task[Seq[SelectedFieldModel]] = {
    for {
      getFormResp <- formService
        .getForm(
          formId = formVersionId.parent,
          versionIdOpt = Some(formVersionId),
          actor = actorId,
          shouldCheckPermission = false
        )
      formData = getFormResp.formData
      allSignatureMappings = FormSignatureUtils
        .getAllSignatureMappingInfo(formData.form)
        .flatMap(_.mappings.map(_.mapping))
        .toSet
      pdfFieldsLabelMap = FundSubGaiaExportHelper.getPdfFieldsLabelMap(formData).flatMap { case (fieldName, labels) =>
        labels.find(_.nonEmpty).map(fieldName -> _)
      }
      pdfAnnotations <- ZIOUtils
        .foreachParN(2)(fileIds) { fileId =>
          getAnnotationDataForSelfServiceExportInternal(
            fileId = fileId,
            actorId = actorId,
            annotationDocumentVersionIdOpt = getFormResp.systemMetadata.annotationMapping.get(fileId),
            pdfFieldsLabelMap = pdfFieldsLabelMap,
            excludedFields = allSignatureMappings
          ).map(
            _.pdfFields
          )
        }
        .map(_.flatten)
      pdfAnnotationsMap = pdfAnnotations.groupBy(_.pdfFieldIdentifier)
    } yield {
      fields.map(buildSingleSelectedFieldModel(_, pdfAnnotationsMap))
    }
  }

  private def buildSingleSelectedFieldModel(
    field: SelectedFieldOfSelfServiceExport,
    pdfAnnotationsMap: Map[ExportTemplateFieldIdentifier.PdfField, Seq[PdfFieldForSelfServiceExport]]
  ): SelectedFieldModel = {
    val fieldType = field.fieldType match {
      case FieldType.DashboardField => FieldTypeModel.DashboardField
      case PdfFieldType.RadioGroup  => FieldTypeModel.PdfFieldRadio
      case PdfFieldType.Text        => FieldTypeModel.PdfFieldText
      case PdfFieldType.Checkbox    => FieldTypeModel.PdfFieldCheckbox
    }
    val fieldAnnotationInfoOpt = field.fieldId match {
      case pdfFieldId: ExportTemplateFieldIdentifier.PdfField =>
        pdfAnnotationsMap.get(pdfFieldId).flatMap(_.headOption)
      case _: ExportTemplateFieldIdentifier.DashboardColumnField => None
    }
    val fieldOptions = fieldAnnotationInfoOpt.map(_.displayOptionNameMap.keySet).getOrElse(Set.empty)
    SelectedFieldModel(
      fieldType = fieldType,
      fieldId = field.fieldId.value,
      revisedName = field.revisedName,
      revisedOptionNameMap = field.revisedOptionNameMap.view.filterKeys(fieldOptions.contains).toMap,
      locationOpt = fieldAnnotationInfoOpt.map { annotationInfo =>
        FieldPageLocationModel(
          fileId = annotationInfo.fileId,
          pageIndex = annotationInfo.locations.min.pageIndex
        )
      }
    )
  }

  def getSelfServiceExportTemplatesForFundSetting(
    fundSubId: FundSubId,
    actorId: UserId
  ): Task[GetSelfServiceExportTemplatesDetailInfoResp] = {
    for {
      _ <- ZIO.logInfo(s"User $actorId get self service export templates for fund setting")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- validateUserCanExportFormDataAndDashboardData(fundSubId, actorId)
      templateInfos <- getSelfServiceExportTemplatesDetailInfoInternal(fundSubId)
      userIds = templateInfos.map(_.lastUpdatedBy)
      userInfosMap <- ZIO
        .foreach(userIds.grouped(500).toSeq) { userIds =>
          userProfileService.batchGetUserInfos(userIds.toSet)
        }
        .map(
          _.flatten
            .map { case (userId, userInfo) =>
              userId -> ParticipantInfo.convert(userId, userInfo)
            }
            .toMap
        )
      userSeenApiExportTemplateGuideTour <- fundSubUserTrackingService
        .getUserTracking(actorId)
        .map(_.seenApiExportTemplateGuideTour)
    } yield GetSelfServiceExportTemplatesDetailInfoResp(
      templateInfos,
      userInfosMap,
      userSeenApiExportTemplateGuideTour
    )
  }

  def getSelfServiceExportTemplatesDetailInfoInternal(
    fundSubId: FundSubId
  ): Task[List[SelfServiceExportTemplateDetailInfo]] = {
    FDBRecordDatabase.transact(FundSubSelfServiceExportOperations.Production)(
      _.getAllTemplatesInFund(fundSubId).map(_.map(buildSelfServiceExportTemplateDetailInfo))
    )
  }

  private def buildSelfServiceExportTemplateDetailInfo(
    model: FundSubSelfServiceExportTemplateModel
  ): SelfServiceExportTemplateDetailInfo =
    SelfServiceExportTemplateDetailInfo(
      templateId = model.templateId,
      templateName = model.templateName,
      forApiExport = model.forApiExport,
      fieldsCount = model.selectedFields.size,
      lastUpdatedAt = model.lastUpdatedAt,
      lastUpdatedBy = model.lastUpdatedBy
    )

  def exportInvestorsDataUsingSelfServiceTemplate(
    templateId: FundSubSelfServiceExportTemplateId,
    lpIds: Seq[FundSubLpId],
    actorId: UserId
  ): Task[FileId] = {
    val fundSubId = templateId.parent
    for {
      templateData <- exportSelfServiceTemplateData(templateId, lpIds, actorId)
      folderId = FolderId.channelSystemFolderId(FundSubAdminRestrictedId(fundSubId))
      fundName <- FDBRecordDatabase
        .transact(FundSubModelStoreOperations.Production)(_.getFundSubPublicModel(fundSubId))
        .map(_.fundName)
      fileId <- fillInvestorsDataToSpreadsheet(fundName, folderId, templateData, actorId)
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = templateId.parent,
        params = FundSubAuditLogService.AddEventParam(
          actor = Some(actorId),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.INVESTOR_DATA_EXPORTED,
          activityDetail = InvestorDataExported(
            selfServiceTemplateIdOpt = Some(templateId),
            templateName = templateData.templateName,
            lpIds = lpIds
          )
        )
      )
    } yield fileId
  }

  def exportSelfServiceTemplateData(
    templateId: FundSubSelfServiceExportTemplateId,
    lpIds: Seq[FundSubLpId],
    actorId: UserId
  ): Task[TemplateData] = {
    for {
      _ <- ZIO.logInfo(s"User $actorId exports data of ${lpIds.size} investors using self service template $templateId")
      templateModel <- FDBRecordDatabase.transact(FundSubSelfServiceExportOperations.Production)(_.get(templateId))
      fundSubId <- ZIOUtils.uniqueSeqToTask(
        lpIds.map(_.parent),
        GeneralServiceException("All LPs must be in the same fund")
      )
      _ <- ZIOUtils.failWhen(templateModel.isDeleted) {
        TemplateRemovedException(templateModel.templateId)
      }
      _ <- ZIOUtils.failWhen(templateModel.forApiExport) {
        TemplateForApiExportOnlyException(templateModel.templateId)
      }
      _ <- ZIOUtils.failWhen(templateId.parent != fundSubId) {
        GeneralServiceException(s"Template $templateId is not found in fund $fundSubId")
      }
      fsModel <- validateFeatureSwitchEnabled(fundSubId)
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      lpsToExportFormData <- FundSubPermissionUtils.getAccessibleLpIdsFundManagerCanTakeActionR(
        fundId = fundSubId,
        fundPermission = FundPermission.ExportInvestorFormData,
        fundManagerUserId = actorId
      )
      lpsToExportDashboardData <- FundSubPermissionUtils.getAccessibleLpIdsFundManagerCanTakeActionR(
        fundId = fundSubId,
        fundPermission = FundPermission.ExportInvestorDashboardData,
        fundManagerUserId = actorId
      )
      canExportDashboardData = lpIds.forall(lpId => lpsToExportDashboardData.contains(lpId))
      canExportFormData = lpIds.forall(lpId => lpsToExportFormData.contains(lpId))
      templateData <- getInvestorsDataUsingSelfServiceTemplateInternal(
        fundSubId,
        templateModel,
        lpIds,
        actorId,
        canExportDashboardData,
        canExportFormData
      )
      _ <- fundSubLoggingService.logEventInvestorsDataExported(
        userId = actorId,
        templateName = templateData.templateName,
        fundSubId = templateId.parent,
        fundName = fsModel.fundName,
        numberOfLps = lpIds.size,
        isApiExport = false,
        selfServiceTemplateIdOpt = Some(templateId)
      )
    } yield templateData
  }

  def getInvestorsDataToExportViaApi(
    templateId: FundSubSelfServiceExportTemplateId,
    lpIds: Seq[FundSubLpId],
    dropEmptyTemplateValues: Boolean,
    dropAsaValues: Boolean,
    includeSapValues: Boolean,
    serviceAccount: UserId
  ): Task[Seq[(FundSubLpId, Map[String, String])]] = {
    for {
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(templateId.parent)
      templateModel <- FDBRecordDatabase.transact(FundSubSelfServiceExportOperations.Production)(_.get(templateId))
      _ <- ZIOUtils.failWhen(templateModel.isDeleted) {
        TemplateRemovedException(templateModel.templateId)
      }
      templateData <- getInvestorsDataUsingSelfServiceTemplateInternal(
        fundSubId = templateId.parent,
        templateModel = templateModel,
        lpIds = lpIds,
        actorId = serviceAccount,
        actorCanExportFormData = true,
        actorCanExportDashboardData = true,
        shouldIncludeAsaData = !dropAsaValues,
        shouldIncludeEmptyFields = !dropEmptyTemplateValues,
        shouldIncludeSapData = includeSapValues
      )
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = templateId.parent,
        params = FundSubAuditLogService.AddEventParam(
          actor = Some(serviceAccount),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.INVESTOR_DATA_EXPORTED,
          activityDetail = InvestorDataExported(
            selfServiceTemplateIdOpt = Some(templateId),
            templateName = templateData.templateName,
            lpIds = lpIds
          )
        )
      )
      fsModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(templateId.parent)
      )
      _ <- fundSubLoggingService.logEventInvestorsDataExported(
        userId = serviceAccount,
        templateName = templateData.templateName,
        fundSubId = templateId.parent,
        fundName = fsModel.fundName,
        numberOfLps = lpIds.size,
        isApiExport = true,
        selfServiceTemplateIdOpt = Some(templateId)
      )
    } yield {
      val allFields = templateData.getFlattenedFields
      val fieldLabelMap = allFields.map(field => field.fieldId -> field.fieldName).toMap
      // Map[(fieldId, rawValue), exportValue]
      val fieldValueMap = allFields.flatMap { field =>
        field.fieldType match {
          case PdfFieldType.RadioGroup =>
            field.fieldOptionNameMap.map { case (rawName, revisedName) =>
              (field.fieldId, rawName) -> revisedName
            }
          case _ => Map.empty
        }
      }.toMap
      templateData.lpsData.map { lpData =>
        lpData.lpId -> lpData.fieldsData.map { case (fieldId, rawValue) =>
          val fieldLabel = fieldLabelMap.getOrElse(fieldId, fieldId)
          val fieldValue = fieldValueMap.getOrElse((fieldId, rawValue), rawValue)
          fieldLabel -> fieldValue
        }.toMap
      }
    }
  }

  private def getInvestorsDataUsingSelfServiceTemplateInternal(
    fundSubId: FundSubId,
    templateModel: FundSubSelfServiceExportTemplateModel,
    lpIds: Seq[FundSubLpId],
    actorId: UserId,
    actorCanExportDashboardData: Boolean,
    actorCanExportFormData: Boolean,
    shouldIncludeEmptyFields: Boolean = true,
    shouldIncludeAsaData: Boolean = false,
    shouldIncludeSapData: Boolean = false
  )(
    using RebacStoreOperation
  ): Task[TemplateData] = {
    for {
      _ <- ZIOUtils.failWhen(!actorCanExportDashboardData && !actorCanExportFormData)(
        NoPermissionException(fundSubId = fundSubId, actor = actorId)
      )
      (fsAdminRestrictedModel: FundSubAdminRestrictedModel, fsPublicModel: FundSubPublicModel) <- FDBRecordDatabase
        .transact(FundSubModelStoreOperations.Production) { ops =>
          for {
            fsAdminResModel <- ops.getFundSubAdminRestrictedModel(fundSubId)
            fsModel <- ops.getFundSubPublicModel(fundSubId)
          } yield fsAdminResModel -> fsModel
        }
      investmentFunds = fsAdminRestrictedModel.investmentFunds
      lpsFormVersionMap <- FDBRecordDatabase.transact(LPDataOperations.Production)(_.batchGetLpFormVersionIds(lpIds))
      formVersionIdsFromLps = lpsFormVersionMap.values.map(_.parent).toSet
      formDataMap <- ZIO
        .foreach(formVersionIdsFromLps + templateModel.formVersionId) { formVersionId =>
          formService
            .getForm(
              formId = formVersionId.parent,
              versionIdOpt = Some(formVersionId),
              actor = actorId,
              shouldCheckPermission = false
            )
            .map { resp =>
              formVersionId -> resp
            }
        }
        .map(_.toMap)
      asaMappingMap: Map[FormVersionId, Map[String, String]] <-
        if (shouldIncludeAsaData) {
          ZIO
            .foreach(formVersionIdsFromLps) { formVersionId =>
              formService
                .getFormStandardAliasMapping(
                  id = formVersionId,
                  checkValidFormFieldsAndOptions = false
                )
                .map(formVersionId -> _)
            }
            .map(_.toMap)
        } else {
          ZIO.succeed(Map.empty[FormVersionId, Map[String, String]])
        }
      sapMappingMapOpt <- ZIOUtils.whenOption(shouldIncludeSapData)(
        ZIO
          .foreach(formVersionIdsFromLps) { formVersionId =>
            formSaProfileMappingService
              .queryFormVersionSaMapping(actorId, formVersionId, useSaLocalValueIfExisted = true)
              .map(formVersionId -> _)
          }
          .map(_.toMap)
      )
      getLpsDashboardDataResp <-
        getLpsDashboardData(
          lpIds = lpIds.filter(_ => actorCanExportDashboardData),
          templateModel = templateModel,
          fundSubId = fundSubId,
          actorId = actorId
        )
      supportingDocReviewEnabled <- FDBRecordDatabase.transact(SupportingDocReviewConfigOperations.Production)(
        _.getOpt(SupportingDocReviewConfigId(fundSubId)).map(_.exists(!_.mode.isSupportingDocReviewConfigModeDisabled))
      )
      accessibleColumnIds = getLpsDashboardDataResp.accessibleColumns.map(_.id).toSet
      fieldInfos = buildTemplateFieldsStructure(
        selectedFields = templateModel.selectedFields,
        canExportDashboardData = actorCanExportDashboardData,
        canExportPdfData = actorCanExportFormData,
        accessibleColumnsCheck = columnId => accessibleColumnIds.contains(columnId),
        forApiExport = templateModel.forApiExport
      )
      singleFieldInfos = fieldInfos.flatMap {
        case singleField: FieldInfo.SingleFieldData => Seq(singleField)
        case groupField: FieldInfo.GroupFieldData   => groupField.subFields
      }
      templateFileInfosMap = formDataMap
        .get(templateModel.formVersionId)
        .fold[Seq[FormFileInfosToExport]](Seq.empty) { getTemplateFormResp =>
          getFormFileInfosToExport(
            formData = getTemplateFormResp.formData,
            fileAnnotationIdMap = getTemplateFormResp.systemMetadata.annotationMapping,
            formFieldFilter = _ => true
          )
        }
        .map(file => file.fileId -> file)
        .toMap
      lpsData <- ZIOUtils.foreachParN(36)(lpIds) { lpId =>
        val lpFormVersionIdOpt = lpsFormVersionMap.get(lpId)
        val lpDashboardDataOpt = getLpsDashboardDataResp.lpDashboardDataMap.get(lpId)
        val formVersionDataOpt = lpFormVersionIdOpt.flatMap(lpFormVersionId => formDataMap.get(lpFormVersionId.parent))
        val asaMappingOpt = lpFormVersionIdOpt.flatMap(lpFormVersionId => asaMappingMap.get(lpFormVersionId.parent))
        val sapMappingOpt =
          lpFormVersionIdOpt.flatMap(lpFormVersionId => sapMappingMapOpt.flatMap(_.get(lpFormVersionId.parent)))
        for {
          lpData <- buildSingleInvestorData(
            lpId = lpId,
            lpFormVersionIdOpt = lpFormVersionIdOpt,
            formVersionDataOpt = formVersionDataOpt,
            lpDashboardDataOpt = lpDashboardDataOpt,
            formFieldsConfig = fsPublicModel.dashboardConfig.map(_.formFieldsConfigs).getOrElse(Seq.empty),
            investmentFunds = investmentFunds,
            commitmentPercentageHeaderOpt = getLpsDashboardDataResp.commitmentPercentageHeaderOpt,
            templateFieldInfos = singleFieldInfos,
            canExportDashboardData = actorCanExportDashboardData,
            canExportFormData = actorCanExportFormData,
            supportingDocReviewEnabled = supportingDocReviewEnabled,
            shouldIncludeEmptyFields = shouldIncludeEmptyFields,
            asaMappingOpt = asaMappingOpt,
            sapMappingOpt = sapMappingOpt,
            templateFileInfosMap = templateFileInfosMap,
            forApiExportTemplate = templateModel.forApiExport
          )
        } yield lpData
      }
    } yield TemplateData(
      lpsData = lpsData,
      fieldInfos = fieldInfos,
      templateName = templateModel.templateName
    )
  }

  def getTemplateFieldsStructureUnsafe(
    templateId: FundSubSelfServiceExportTemplateId
  ): Task[Seq[FieldInfo]] = {
    for {
      templateModel <- FDBRecordDatabase.transact(FundSubSelfServiceExportOperations.Production)(_.get(templateId))
      templateFieldsStructure = buildTemplateFieldsStructure(
        selectedFields = templateModel.selectedFields,
        canExportDashboardData = true,
        canExportPdfData = true,
        accessibleColumnsCheck = _ => true,
        forApiExport = templateModel.forApiExport
      )
    } yield templateFieldsStructure
  }

  private def buildTemplateFieldsStructure(
    selectedFields: Seq[SelectedFieldModel],
    canExportDashboardData: Boolean,
    canExportPdfData: Boolean,
    accessibleColumnsCheck: String => Boolean,
    forApiExport: Boolean
  ): Seq[FieldInfo] = {
    selectedFields.map { field =>
      field.fieldType match {
        case FieldTypeModel.DashboardField if canExportDashboardData && accessibleColumnsCheck(field.fieldId) =>
          getStructureOfDashboardColumnField(
            field = field,
            forApiExport = forApiExport
          )
        case FieldTypeModel.PdfFieldRadio | FieldTypeModel.PdfFieldText | FieldTypeModel.PdfFieldCheckbox
            if canExportPdfData =>
          getStructureOfPdfField(
            pdfField = field
          )
        case _ => fieldStructureWithoutPermissionToAccess(field)
      }
    }
  }

  private def getSubfieldsStructureForContactColumn(forApiExport: Boolean): Seq[FieldInfo.SingleFieldData] = {
    Seq(
      FieldInfo.SingleFieldData(
        fieldId = InvestmentEntityColumnId,
        fieldName = if (forApiExport) "investment_entity" else "Investment entity",
        description = "",
        fieldType = FieldType.DashboardField,
        fileIdOpt = None
      ),
      FieldInfo.SingleFieldData(
        fieldId = FirstNameColumnId,
        fieldName = if (forApiExport) "first_name" else "First name",
        description = "",
        fieldType = FieldType.DashboardField,
        fileIdOpt = None
      ),
      FieldInfo.SingleFieldData(
        fieldId = LastNameColumnId,
        fieldName = if (forApiExport) "last_name" else "Last name",
        description = "",
        fieldType = FieldType.DashboardField,
        fileIdOpt = None
      ),
      FieldInfo.SingleFieldData(
        fieldId = EmailAddressColumnId,
        fieldName = if (forApiExport) "email_address" else "Email address",
        description = "",
        fieldType = FieldType.DashboardField,
        fileIdOpt = None
      ),
      FieldInfo.SingleFieldData(
        fieldId = CollaboratorsColumnId,
        fieldName = if (forApiExport) "collaborators" else "Collaborators",
        description = "",
        fieldType = FieldType.DashboardField,
        fileIdOpt = None
      )
    )
  }

  private def getStructureOfDashboardColumnField(
    field: SelectedFieldModel,
    forApiExport: Boolean
  ): FieldInfo = {
    field.fieldId match {
      case DashboardColumnData.contactColumn.id =>
        FieldInfo.GroupFieldData(
          fieldId = field.fieldId,
          fieldName = field.revisedName,
          fieldOptionNameMap = field.revisedOptionNameMap,
          description = field.description,
          subFields = getSubfieldsStructureForContactColumn(forApiExport),
          fieldType = FieldType.DashboardField
        )
      case _ =>
        FieldInfo.SingleFieldData(
          fieldId = field.fieldId,
          fieldName = field.revisedName,
          fieldOptionNameMap = field.revisedOptionNameMap,
          description = field.description,
          fieldType = FieldType.DashboardField,
          fileIdOpt = None
        )
    }
  }

  private def getStructureOfPdfField(
    pdfField: SelectedFieldModel
  ): FieldInfo = {
    pdfField.fieldType match {
      case FieldTypeModel.DashboardField =>
        throw RuntimeException(s"${pdfField.fieldId} is not a pdf field")

      case _ =>
        FieldInfo.SingleFieldData(
          fieldId = pdfField.fieldId,
          fieldName = pdfField.revisedName,
          fieldOptionNameMap = pdfField.revisedOptionNameMap,
          description = pdfField.description,
          fieldType = fromProtoToFieldType(pdfField.fieldType),
          fileIdOpt = pdfField.locationOpt.map(_.fileId)
        )
    }
  }

  private def fieldStructureWithoutPermissionToAccess(
    field: SelectedFieldModel
  ): FieldInfo =
    FieldInfo.SingleFieldData(
      fieldId = field.fieldId,
      fieldName = field.revisedName,
      fieldOptionNameMap = field.revisedOptionNameMap,
      description = field.description,
      fieldType = fromProtoToFieldType(field.fieldType),
      hasPermissionToExportData = false,
      fileIdOpt = None
    )

  private def getLpsDashboardData(
    lpIds: Seq[FundSubLpId],
    templateModel: FundSubSelfServiceExportTemplateModel,
    fundSubId: FundSubId,
    actorId: UserId
  )(
    using RebacStoreOperation
  ): Task[GetLpsDashboardDataResp] = {
    val dashboardIdsFromTemplate =
      templateModel.selectedFields.filter(_.fieldType.isDashboardField).map(_.fieldId).toSet
    for {
      accessibleColumns <- fundSubViewService
        .getAvailableColumnsOfAccessibleViews(fundSubId, actorId)
        .map(_.map(_.column))
      applicableColumns = accessibleColumns.filter(col => dashboardIdsFromTemplate.contains(col.id)).toList
      lpsDashboardDataMap <-
        if (applicableColumns.isEmpty || lpIds.isEmpty) {
          ZIO.succeed(Map.empty[FundSubLpId, LpDashboardData])
        } else {
          for {
            rowsData <- dashboardService.getRowsData(applicableColumns, lpIds.toList)
            dashboardRowsEither <- ZIO.attempt(
              RowConvert.toData(
                headers = applicableColumns,
                rows = rowsData
              )
            )
            dashboardRows <- ZIO.fromEither(dashboardRowsEither)
          } yield dashboardRows.map { row =>
            row.rowMetadata.lpId ->
              LpDashboardData(
                columnsDataMap = row.data
                  .zip(applicableColumns)
                  .map { case (cell, column) =>
                    column.id -> ColumnData(cell, column)
                  }
                  .toMap,
                rowMetadata = row.rowMetadata
              )
          }.toMap
        }
      commitmentPercentageHeaderOpt <- ZIO
        .when(applicableColumns.nonEmpty) {
          dashboardService
            .getDashboardMetadataAndHeaders(
              id = fundSubId,
              columns = applicableColumns.collectFirst { case column: CommitmentPercentageColumn =>
                column
              }.toList
            )
            .map { metadataAndHeaders =>
              metadataAndHeaders.headers.map(_.header).collectFirst {
                case commitmentPercentageHeader: CommitmentPercentageHeader => commitmentPercentageHeader
              }
            }
        }
        .map(_.flatten)
    } yield GetLpsDashboardDataResp(
      lpDashboardDataMap = lpsDashboardDataMap,
      commitmentPercentageHeaderOpt = commitmentPercentageHeaderOpt,
      accessibleColumns = accessibleColumns
    )
  }

  private def getSimpleCellContent(
    cell: ColumnCell,
    column: DashboardColumn,
    formFieldsConfig: Seq[FormFieldConfig],
    investmentFunds: Seq[InvestmentFundModel],
    commitmentPercentageHeaderOpt: Option[CommitmentPercentageHeader],
    supportingDocReviewEnabled: Boolean
  ): String = {
    cell match {
      case _: ContactCell             => ""
      case customLpId: CustomLpIdCell => customLpId.customId
      case close: CloseCell           => close.name
      case customData: CustomDataCell =>
        customData.data.map(FundSubExportHelper.getCustomDataDisplayValue).getOrElse("")
      case lastActivity: LastActivityCell =>
        lastActivity.time
          .map { time =>
            DateTimeUtils
              .formatInstant(
                time,
                DateTimeUtils.DatabaseDatetimeFormatter
              )(
                using DateTimeUtils.utcTimezone
              )
          }
          .getOrElse("")
      case invitationDate: InvitationDateCell =>
        invitationDate.value
          .map { time =>
            DateTimeUtils
              .formatInstant(
                time,
                DateTimeUtils.DatabaseDatetimeFormatter
              )(
                using DateTimeUtils.utcTimezone
              )
          }
          .getOrElse("")
      case submissionDate: SubmissionDateCell =>
        submissionDate.value
          .map { time =>
            DateTimeUtils
              .formatInstant(
                time,
                DateTimeUtils.DatabaseDatetimeFormatter
              )(
                using DateTimeUtils.utcTimezone
              )
          }
          .getOrElse("")
      case formProgress: FormProgressCell         => s"${Math.round(formProgress.value * 100)}%"
      case subscription: SubscriptionDocumentCell => LpStatusSharedUtils.getStatusName(subscription.status)
      case signature: SignatureRequestCell        => DataUtils.getRequestSignatureStatus(signature)
      case document: DocumentRequestCell =>
        DataUtils.getDocumentRequestProgress(document, supportingDocReviewEnabled)
      case tag: TagCell =>
        tag.tags.map(_._2.name).sorted.mkString("\n")
      case referenceDoc: ReferenceDocumentCell => referenceDoc.files.map(_.name).mkString("\n")
      case amount: AmountCell =>
        buildAmountCell(
          investmentFunds = investmentFunds,
          amountCell = amount,
          column = column
        )
      case field: FormFieldCell =>
        column match {
          case col: FormFieldColumn =>
            DataUtils
              .formatFormField(
                col,
                field,
                formFieldsConfig
              )
              ._1
              .mkString("\n")
          case _ => ""
        }
      case commitmentPercentage: CommitmentPercentageCell =>
        val lpCommitmentOpt = commitmentPercentage.value
        val totalCommitmentOpt = commitmentPercentageHeaderOpt.map(_.totalCommitment)
        lpCommitmentOpt.zip(totalCommitmentOpt).flatMap(getCommitmentPercentageString).getOrElse("")
      case _: MoneyCell => ""
      case clientCell: ClientCell =>
        clientCell.clients.headOption
          .flatMap(_.clientRestrictedInfo)
          .fold("")(client => s"${client.clientName}${client.clientCustomId.map(" - " + _)}")
      case dataExtractionCell: DataExtractionCell =>
        DataUtils.getDataExtractRequestDisplayName(dataExtractionCell)
      case amlCheckCell: AmlCheckCell =>
        amlCheckCell.amlChecks.map(_.status.name).mkString("\n")
      case advisorGroupCell: AdvisorGroupCell   => advisorGroupCell.advisorGroupOpt.map(_.name).getOrElse("")
      case sideLetterCell: SideLetterCell       => sideLetterCell.status.label
      case investorGroupCell: InvestorGroupCell => investorGroupCell.investorGroupOpt.map(_.name).getOrElse("")
    }
  }

  private def buildAmountCell(
    investmentFunds: Seq[InvestmentFundModel],
    amountCell: AmountCell,
    column: DashboardColumn
  ): String = {
    amountCell.value
      .map { value =>
        val currency = investmentFunds
          .collectFirst {
            case fund: InvestmentFundModel if column.id.endsWith(fund.fundId.idString) =>
              fund.currency
          }
          .getOrElse(CurrencyMessage.USD)
        CurrencyUtils.getCurrencySymbol(currency) + s"%,.2f".format(value)
      }
      .getOrElse("")
  }

  private def getFormFileInfosToExport(
    formData: FormData,
    fileAnnotationIdMap: Map[FileId, AnnotationDocumentVersionId],
    formFieldFilter: String => Boolean
  ): Seq[FormFileInfosToExport] = {
    FormDataUtils
      .traverseAndExtract(
        formData.form,
        (field, widget) => widget.widgetType == WidgetType.File && formFieldFilter(field.name)
      )
      .flatMap { case (field, widget) =>
        val fileNameOpt = widget.uiOptions.get(UIKey.fileName)
        val fileIdOpt = formData.uploadedPdf
          .find { case (_, extractedPdf) =>
            fileNameOpt.contains(extractedPdf.name)
          }
          .map(_._1)
        for {
          fileName <- fileNameOpt
          fileId <- fileIdOpt.flatMap(FormDataConverters.fileIdTypeToFileId)
        } yield {
          FormFileInfosToExport(
            fileId = fileId,
            fileName = fileName,
            formFileAlias = field.name,
            pdfAnnotationDocumentIdOpt = fileAnnotationIdMap.get(fileId).map(_.parent)
          )
        }
      }
  }

  private def getLpPdfAndFormDataForExport(
    gaiaState: GaiaState,
    getFormVersionResp: GetFormResponse
  ): LpPdfAndFormDataForExport = {
    val formVersionData = getFormVersionResp.formData
    val fillPdfItems = FormDataUtils.getPdfValueMappings(
      formVersionData.form,
      formVersionData.uploadedPdf,
      gaiaState.defaultStateMap
    )
    val pdfMappingAndValues = fillPdfItems.map(_.mappingAndValue).toMap

    val pdfOptionFormattedLabels = for {
      fillPdfItem <- fillPdfItems
      formField = fillPdfItem.sourceFormFieldId
      widget <- formVersionData.form.defaultUiSchema.get(formField.key)
      availableOptions <- widget.uiOptions.get(UIKey.multipleOption)
      selectedOption <- fillPdfItem.sourceFormFieldSelectedOptionOpt
      formattedTextOfSelectedOption <- availableOptions.options.get(selectedOption).map(_.formattedText)
    } yield fillPdfItem.pdfFieldValue -> FundSubGaiaExportHelper.sanitizedLabel(formattedTextOfSelectedOption)

    val visibleFileInfos = getFormFileInfosToExport(
      formData = formVersionData,
      fileAnnotationIdMap = getFormVersionResp.systemMetadata.annotationMapping,
      formFieldFilter = fileAlias => gaiaState.defaultStateMap.get(fileAlias).forall(!_.isHidden)
    )
    LpPdfAndFormDataForExport(
      pdfMappingAndValues = pdfMappingAndValues,
      pdfOptionFormatedLabelsMap = pdfOptionFormattedLabels.toMap,
      visibleFileInfos = visibleFileInfos
    )
  }

  private def buildSingleInvestorData(
    lpId: FundSubLpId,
    lpFormVersionIdOpt: Option[FundSubLpFormVersionId],
    formVersionDataOpt: Option[GetFormResponse],
    lpDashboardDataOpt: Option[LpDashboardData],
    formFieldsConfig: Seq[FormFieldConfig],
    investmentFunds: Seq[InvestmentFundModel],
    commitmentPercentageHeaderOpt: Option[CommitmentPercentageHeader],
    templateFieldInfos: Seq[FieldInfo.SingleFieldData],
    canExportDashboardData: Boolean,
    canExportFormData: Boolean,
    supportingDocReviewEnabled: Boolean,
    shouldIncludeEmptyFields: Boolean = true,
    asaMappingOpt: Option[Map[String, String]] = None,
    templateFileInfosMap: Map[FileId, FormFileInfosToExport],
    forApiExportTemplate: Boolean,
    sapMappingOpt: Option[Map[String, String]] = None
  ): Task[InvestorData] = {
    for {
      gaiaStateOpt <- ZIOUtils.traverseOption(lpFormVersionIdOpt)(FundSubCommonUtils.getNewFormState)
      pdfAndFormDataForExportOpt = for {
        gaiaState <- gaiaStateOpt
        getFormVersionResp <- formVersionDataOpt
      } yield getLpPdfAndFormDataForExport(gaiaState, getFormVersionResp)
      fieldsData = templateFieldInfos.map { field =>
        val exportedValue = field.fieldType match {
          case FieldType.DashboardField if canExportDashboardData =>
            getExportedValueOfDashboardField(
              lpDashboardDataOpt,
              field,
              formFieldsConfig,
              investmentFunds,
              commitmentPercentageHeaderOpt,
              supportingDocReviewEnabled
            )
          case _: FieldType.PdfFieldType if canExportFormData =>
            getExportedValueOfPdfField(
              pdfAndFormDataForExportOpt,
              templateFileInfosMap,
              field,
              forApiExportTemplate
            )
          case _ => ""
        }
        field.fieldId -> exportedValue
      }
      asaDataOpt = for {
        gaiaState <- gaiaStateOpt
        formVersionData <- formVersionDataOpt.map(_.formData)
        asaMapping <- asaMappingOpt
      } yield FormTemplateMappingUtils.exportSaData(
        form = formVersionData.form,
        saMapping = asaMapping,
        formState = gaiaState,
        includeEmptyValue = shouldIncludeEmptyFields
      )
      sapDataOpt = for {
        gaiaState <- gaiaStateOpt
        formVersionData <- formVersionDataOpt.map(_.formData)
        sapMapping <- sapMappingOpt
      } yield FormTemplateMappingUtils.exportSaData(
        form = formVersionData.form,
        saMapping = sapMapping,
        formState = gaiaState,
        includeEmptyValue = shouldIncludeEmptyFields
      )
      fieldsDataWithSaData = fieldsData ++ asaDataOpt.getOrElse(Seq.empty) ++ sapDataOpt.getOrElse(Seq.empty)
      fieldsDataDroppedEmpty =
        if (shouldIncludeEmptyFields) {
          fieldsDataWithSaData
        } else {
          fieldsDataWithSaData.filter(_._2.nonEmpty)
        }
    } yield InvestorData(
      lpId = lpId,
      fieldsData = fieldsDataDroppedEmpty
    )
  }

  private def getExportedValueOfDashboardField(
    lpDashboardDataOpt: Option[LpDashboardData],
    field: FieldInfo.SingleFieldData,
    formFieldsConfig: Seq[FormFieldConfig],
    investmentFunds: Seq[InvestmentFundModel],
    commitmentPercentageHeaderOpt: Option[CommitmentPercentageHeader],
    supportingDocReviewEnabled: Boolean
  ): String = {
    lpDashboardDataOpt
      .flatMap { lpDashboardData =>
        field.fieldId match {
          case InvestmentEntityColumnId => Some(lpDashboardData.rowMetadata.lpInvestmentEntity)
          case FirstNameColumnId        => Some(lpDashboardData.rowMetadata.lpInfo.firstName)
          case LastNameColumnId         => Some(lpDashboardData.rowMetadata.lpInfo.lastName)
          case EmailAddressColumnId     => Some(lpDashboardData.rowMetadata.lpInfo.emailStr)
          case CollaboratorsColumnId =>
            Some(
              lpDashboardData.rowMetadata.collaborators
                .map { user => s"${user.firstName}, ${user.lastName} <${user.emailStr}>" }
                .mkString("\n")
            )
          case _ =>
            lpDashboardData.columnsDataMap.get(field.fieldId).map { columnData =>
              getSimpleCellContent(
                columnData.cell,
                columnData.columnModel,
                formFieldsConfig,
                investmentFunds,
                commitmentPercentageHeaderOpt,
                supportingDocReviewEnabled
              )
            }
        }
      }
      .getOrElse("")
  }

  private def getExportedValueOfPdfField(
    lpPdfAndFormDataForExportOpt: Option[LpPdfAndFormDataForExport],
    templateFileInfosMap: Map[FileId, FormFileInfosToExport],
    field: FieldInfo.SingleFieldData,
    forApiExportTemplate: Boolean
  ): String = {
    val exportedValueOpt = for {
      fileId <- field.fileIdOpt
      pdfAndFormData <- lpPdfAndFormDataForExportOpt
      pdfMappingValue <- pdfAndFormData.pdfMappingAndValues.get(field.fieldId)
      if templateFileInfosMap.get(fileId).exists { fileOfSelectedField =>
        pdfAndFormData.visibleFileInfos.exists { visibleFile =>
          visibleFile.fileName == fileOfSelectedField.fileName ||
          visibleFile.formFileAlias == fileOfSelectedField.formFileAlias ||
          visibleFile.pdfAnnotationDocumentIdOpt == fileOfSelectedField.pdfAnnotationDocumentIdOpt && visibleFile.pdfAnnotationDocumentIdOpt.nonEmpty
        }
      }
    } yield {
      field.fieldType match {
        case FieldType.PdfFieldType.Checkbox => if (pdfMappingValue.trim.toLowerCase == "on") "Yes" else "No"
        case FieldType.PdfFieldType.RadioGroup =>
          if (forApiExportTemplate) {
            pdfMappingValue
          } else {
            pdfAndFormData.pdfOptionFormatedLabelsMap.getOrElse(pdfMappingValue, pdfMappingValue)
          }
        case FieldType.PdfFieldType.Text => pdfMappingValue
        case _                           => ""
      }
    }
    exportedValueOpt.getOrElse("")
  }

  private def fromProtoToFieldType(fieldType: FieldTypeModel): FieldType = {
    fieldType match {
      case FieldTypeModel.DashboardField   => FieldType.DashboardField
      case FieldTypeModel.PdfFieldCheckbox => FieldType.PdfFieldType.Checkbox
      case FieldTypeModel.PdfFieldRadio    => FieldType.PdfFieldType.RadioGroup
      case _                               => FieldType.PdfFieldType.Text
    }
  }

  private def fillInvestorsDataToSpreadsheet(
    fundName: String,
    folderId: FolderId,
    templateData: TemplateData,
    actorId: UserId
  ): Task[FileId] = {
    ZIO
      .attempt(new XSSFWorkbook())
      .bracket { workbook =>
        SelfServiceExportToSpreadsheetHelper.fillDataToSpreadSheet(workbook, templateData.templateName, templateData)
        SpreadsheetUtils.workbookToStream(workbook)
      } { workbook =>
        zio.ZIO.succeed {
          workbook.close()
        }
      }
      .flatMap { workbookStream =>
        val contentType = MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        fileService.uploadFile(
          parentFolderId = folderId,
          fileName = s"$fundName-${templateData.templateName}.xlsx",
          content = FileContentOrigin.FromSource(source = workbookStream, mediaType = contentType),
          uploader = actorId
        )
      }
  }

  def deleteSelfServiceExportTemplate(
    templateId: FundSubSelfServiceExportTemplateId,
    actorId: UserId
  ): Task[Unit] = {
    val fundSubId = templateId.parent
    for {
      _ <- ZIO.logInfo(s"User $actorId deletes self-service export template $templateId")
      fsModel <- validateFeatureSwitchEnabled(fundSubId)
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- validateUserCanExportFormDataAndDashboardData(fundSubId, actorId)
      updatedModel <- FDBRecordDatabase.transact(FundSubSelfServiceExportOperations.Production)(
        _.deleteTemplate(templateId)
      )
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = templateId.parent,
        params = FundSubAuditLogService.AddEventParam(
          actor = Some(actorId),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.EXPORT_TEMPLATE_DELETED,
          activityDetail =
            SelfServiceExportTemplateDeleted(templateId = templateId, templateName = updatedModel.templateName)
        )
      )
      _ <- fundSubLoggingService.logEventSelfServiceTemplateDeleted(
        userId = actorId,
        templateId = templateId,
        templateName = updatedModel.templateName,
        fundSubId = fundSubId,
        fundName = fsModel.fundName
      )
    } yield ()
  }

  private def getCommitmentPercentageString(commitment: Double, totalCommitment: Double): Option[String] = {
    Option.when(totalCommitment > 0) {
      val percentage = commitment / totalCommitment * 100
      "%.2f%%".format(percentage)
    }
  }

  private def validateFeatureSwitchEnabled(
    fundSubId: FundSubId
  ): Task[FundSubPublicModel] = {
    for {
      fundSubModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(fundSubId)
      )
      _ <- ZIOUtils.failWhen(fundSubModel.featureSwitch.exists(_.disableSelfServiceExport)) {
        FeatureSwitchNotEnabledException(fundSubId)
      }
    } yield fundSubModel
  }

}

object FundSubSelfServiceExportService {

  private case class LpDashboardData(
    columnsDataMap: Map[String, ColumnData],
    rowMetadata: RowMetadata
  )

  private case class ColumnData(
    cell: ColumnCell,
    columnModel: DashboardColumn
  )

  private case class GetLpsDashboardDataResp(
    lpDashboardDataMap: Map[FundSubLpId, LpDashboardData],
    commitmentPercentageHeaderOpt: Option[CommitmentPercentageHeader],
    accessibleColumns: Seq[DashboardColumn]
  )

  private case class LpPdfAndFormDataForExport(
    pdfMappingAndValues: Map[String, String],
    pdfOptionFormatedLabelsMap: Map[String, String],
    visibleFileInfos: Seq[FormFileInfosToExport]
  )

  private case class FormFileInfosToExport(
    fileId: FileId,
    fileName: String,
    formFileAlias: String,
    pdfAnnotationDocumentIdOpt: Option[AnnotationDocumentId]
  )

  val InvestmentEntityColumnId: String = "investmentEntityColumn"

  val FirstNameColumnId: String = "firstNameColumn"

  val LastNameColumnId: String = "lastNameColumn"

  val EmailAddressColumnId: String = "emailAddressColumn"

  val CollaboratorsColumnId: String = "collaboratorsColumn"

}
