// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.service

import java.util.UUID

import io.circe.Json
import org.apache.poi.ss.util.CellRangeAddress
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import sttp.model.MediaType
import zio.implicits.*
import zio.temporal.workflow.ZWorkflowStub
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.dashboard.data.DocumentRequestCell
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.docrequest.model.*
import anduin.docrequest.service.{
  DocRequestService,
  DocSubmissionService,
  FormSubmissionService,
  UpdateDocRequestItemsParams
}
import anduin.documentcontent.spreadsheet.FillSheetData.{FillSheetCell, FillSheetRow}
import anduin.documentcontent.spreadsheet.{FillSheetData, FillSpreadsheet, SpreadsheetUtils}
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{DefaultCluster, FDBCluster, FDBCommonDatabase, FDBRecordDatabase}
import anduin.forms.FormDataProtoConverter
import anduin.forms.endpoint.GetFormResponse
import anduin.forms.engine.GaiaState
import anduin.forms.service.FormService
import anduin.forms.utils.{FormSignatureUtils, FormValidationUtils}
import anduin.fundsub.LpFormDataOperations
import anduin.fundsub.auditlog.FundSubAuditLogService.{AddEventEmailParam, AddEventParam}
import anduin.fundsub.auditlog.{AuditLogActorType, AuditLogEventType, FundSubAuditLogService}
import anduin.fundsub.dashboard.LpInfoOperations
import anduin.fundsub.datalakeingestion.FundSubDataLakeIngestionService
import anduin.fundsub.datalakeingestion.model.{FileInfo as DatalakeFileInfo, UserBasicInfo as DatalakeUserBasicInfo, *}
import anduin.fundsub.digest.OrderActivityType
import anduin.fundsub.email.digest.newdocupload.NewSupportingDocUploadReportService
import anduin.fundsub.email.digest.orderactivity.OrderActivityUtils
import anduin.fundsub.endpoint.admin.LpBasicInfo
import anduin.fundsub.endpoint.lp.GaiaFormModel
import anduin.fundsub.form.utils.FundSubCommonUtils
import anduin.fundsub.importexport.CommonField
import anduin.fundsub.importexport.FundSubMappingUtils.ProvidedDocMapping
import anduin.fundsub.investorgroup.{FundSubInvestorGroupService, FundSubInvestorGroupUtils}
import anduin.fundsub.model.FundSubSignatureRequestDocType
import anduin.fundsub.models.signature.FundSubSignatureRequestModels.{
  FundSubSignatureEnvelopeType,
  FundSubSignatureRequestBasic
}
import anduin.fundsub.models.{FundSubLpModelStoreOperations, FundSubModelStoreOperations, FundSubSgwModelUtils}
import anduin.fundsub.rebac.FundSubRebacModel.{FundPermission, Type}
import anduin.fundsub.service.FundSubPermissionService.FundSubRole
import anduin.fundsub.service.NewSupportingDocService.{
  SupportingDocPendingSignatureResp,
  SupportingDocSummary,
  SupportingFormPendingSignatureInfo
}
import anduin.fundsub.signature.FundSubSignatureJvmUtils
import anduin.fundsub.signature.integration.FundSubSignatureIntegrationService
import anduin.fundsub.signature.integration.FundSubSignatureIntegrationService.FundSubSignerParams
import anduin.fundsub.storageintegration.FundSubStorageIntegrationService
import anduin.fundsub.supportingdoc.v2.*
import anduin.fundsub.taxform.FundSubTaxFormConfig
import anduin.fundsub.user.{FundSubUserService, FundSubUserTrackingService}
import anduin.fundsub.utils.{FundSubDataLakeUtils, FundSubLpUtils}
import anduin.greylin.GreylinDataService
import anduin.id.docrequest.{DocRequestId, DocSubmissionId, FormSubmissionId}
import anduin.id.form.{FormId, FormVersionId}
import anduin.id.fundsub.*
import anduin.id.fundsub.FundSubLpFolderTypeId.FolderType
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.signature.SignatureRequestId
import anduin.model.common.user.UserId
import anduin.model.id.{FileId, FolderId, TeamId, TemporalWorkflowId}
import anduin.protobuf.actionlogger.event.{ActionEventAmlKycItemAction, AmlKycAction}
import anduin.protobuf.activitylog.GeneralActivity
import anduin.protobuf.activitylog.GeneralActivity.Value
import anduin.protobuf.flow.fundsub.admin.lpdashboard.{LpStatus, UserBasicInfo}
import anduin.protobuf.fundsub.*
import anduin.protobuf.fundsub.activitylog.lp.{ActionOnSupportingDoc, RemovedAdditionalForm, SupportingDocAction}
import anduin.rebac.RebacModel.implicits.given
import anduin.rebac.RebacStoreOperation
import anduin.service.{AuthenticatedRequestContext, GeneralServiceException}
import anduin.storageservice.common.FileContentOrigin
import anduin.temporal.TemporalEnvironment
import anduin.workflow.fundsub.syncinvestoraccess.AutoSaveSubscriptionFormParams
import anduin.workflow.fundsub.syncinvestoraccess.impl.FundSubAutoSaveSubscriptionDataWorkflowImpl
import com.anduin.stargazer.endpoints.FileInfo
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.actionlogger.ActionLoggerService
import com.anduin.stargazer.service.api.FileDownloadService
import com.anduin.stargazer.service.dynamicform.DynamicFormStorageService
import com.anduin.stargazer.service.file.BatchDownloadRequest
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.util.date.DateCalculator

final case class NewSupportingDocService(
  dashboardService: FundSubLpDashboardService,
  docRequestService: DocRequestService,
  docSubmissionService: DocSubmissionService,
  fileService: FileService,
  formSubmissionService: FormSubmissionService,
  formService: FormService,
  fundSubEmailService: FundSubEmailService,
  fundSubPermissionService: FundSubPermissionService,
  fundSubSignatureIntegrationService: FundSubSignatureIntegrationService,
  fundSubStorageIntegrationService: FundSubStorageIntegrationService,
  fundSubUserService: FundSubUserService,
  fundSubInvestorGroupService: FundSubInvestorGroupService,
  newSupportingDocLoggingService: NewSupportingDocLoggingService,
  userProfileService: UserProfileService,
  fundSubDataLakeIngestionService: FundSubDataLakeIngestionService,
  newSupportingDocUploadReportService: NewSupportingDocUploadReportService,
  lpActivityLogService: FundSubLpActivityLogService,
  fundSubAuditLogService: FundSubAuditLogService,
  fundSubUserTrackingService: FundSubUserTrackingService,
  supportingDocReviewService: SupportingDocReviewService,
  actionLoggerService: ActionLoggerService,
  natsNotificationService: NatsNotificationService,
  fileDownloadService: FileDownloadService,
  dynamicFormStorageService: DynamicFormStorageService,
  auditLogService: FundSubAuditLogService,
  backendConfig: GondorBackendConfig,
  temporalEnvironment: TemporalEnvironment
)(
  using greylinDataService: GreylinDataService
) {

  given NewSupportingDocService = this

  private val parallelism = 5

  def uploadSupportingDocFiles(
    params: UploadSupportingDocFilesParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Seq[FileId]] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is upload a supporting doc (${params.fileIds}) for ${params.lpId.idString}"
      )
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.lpId.parent)
      role <- fundSubPermissionService
        .validateFundManagerCanUploadSupportingDocsOnBehalfR(params.lpId, actor)
        .orElse(
          for {
            _ <- fundSubPermissionService.validateFundActive(params.lpId.parent)
            role <- fundSubPermissionService.validateUserCanServeOnInvestorSideR(params.lpId, actor)
          } yield role
        )
      _ <- validateShouldUpdateSupportingDoc(params.lpId, actor)
      // TODO: prevent upload new files when review status of doc type is Approved
      docRequestId <- getDocRequestId(params.lpId)
      docRequest <- docRequestService.getDocRequest(docRequestId, None)
      _ <- ZIOUtils.when(docRequest.providedItems.exists(_.trim.equalsIgnoreCase(params.docType.trim))) {
        docRequestService.unmarkDocRequestItemAsProvided(
          docRequestId,
          Seq(params.docType),
          Option(actor)
        )
      }
      sharedFiles <- docSubmissionService.addFiles(
        DocSubmissionId(docRequestId),
        params.fileIds,
        params.docType,
        actor,
        ctx
      )
      _ <- afterSupportingDocsUploaded(
        lpId = params.lpId,
        actor = actor,
        ctx = ctx,
        shouldAttemptToSubmitForReview = role != FundSubRole.FundManager,
        sharedFiles = sharedFiles,
        docType = params.docType,
        shouldTriggerAutoSaveProfile = false
      )
    } yield sharedFiles
  }

  def createFormSubmission(
    params: CreateFormSubmissionParams,
    actor: UserId
  ): Task[FormSubmission] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is submitting a form (${params.formVersionId}) for ${params.lpId.idString}"
      )
      _ <- fundSubPermissionService.validateUserCanAccessLpView(params.lpId, actor)
      _ <- fundSubPermissionService.validateFundActive(params.lpId.parent)
      _ <- validateShouldUpdateSupportingDoc(params.lpId, actor)
      docRequestId <- getDocRequestId(params.lpId)
      initialValues <- getPrefilledValues(params.lpId)
      formName <- getFormName(params.formVersionId.parent)
      formSubmission <- formSubmissionService.createFormSubmission(
        docRequestId = docRequestId,
        formVersionId = params.formVersionId,
        name = formName,
        initialFormValues = initialValues,
        actor
      )
      _ <- ZIOUtils.when(params.docType.trim.nonEmpty) {
        docSubmissionService
          .mapFormToDocType(
            DocSubmissionId(docRequestId),
            formSubmission.id,
            params.docType,
            Option(actor)
          )
          .map(_ => ())
      }
      _ <- updateDocRequestStatus(params.lpId, newFileIds = List.empty)
      _ <- actionLoggerService.addEventLog(
        actor = actor,
        events = Seq(
          ActionEventAmlKycItemAction(
            params.lpId.parent,
            params.lpId,
            action = AmlKycAction.AML_KYC_ACTION_ADD_FORM,
            Seq(params.docType)
          )
        ),
        httpContextOpt = None,
        eventTime = None
      )
    } yield formSubmission
  }

  def requestAdditionalSupportingDocuments(
    params: RequestSupportingDocsParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is requesting additional docs (${params.docNames}) from lp ${params.lpId.idString}"
      )
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.lpId.parent)
      _ <- fundSubPermissionService.validateFundManagerCanManageAdditionalSupportingDocRequestsR(params.lpId, actor)
      docRequestId <- getDocRequestId(params.lpId)
      newRequestedDocs = params.docNames
        .map(_.trim)
        .filterNot(_.isEmpty)
        .groupBy(_.toLowerCase)
        .flatMap(_._2.headOption)
        .toSeq
      _ <- docRequestService.updateDocRequestItemsList(
        id = docRequestId,
        updateFnc = items => {
          val existingDocs = items.map(_.name)
          val toAdds = newRequestedDocs.filterNot { name =>
            existingDocs.exists(_.trim.equalsIgnoreCase(name))
          }
          UpdateDocRequestItemsParams(
            itemsToAdd = toAdds.map(name => DocRequestItemBasicInfo(name))
          )
        },
        actorOpt = Option(actor)
      )
      _ <- updateDocRequestStatus(params.lpId, newFileIds = List.empty)
      _ <- newSupportingDocLoggingService.docsRequested(
        params.lpId,
        newRequestedDocs,
        params.message,
        actor,
        params.ccRecipients
      )
      _ <- actionLoggerService.addEventLog(
        actor = actor,
        events = newRequestedDocs.map { docType =>
          ActionEventAmlKycItemAction(
            params.lpId.parent,
            params.lpId,
            action = AmlKycAction.AML_KYC_ACTION_REQUEST_ADDITIONAL_DOC,
            Seq(docType)
          )
        },
        httpContextOpt = None,
        eventTime = Option(DateCalculator.instantNow)
      )
    } yield ()
  }

  def getDocRequest(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[GetDocRequestResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is getting doc request for ${lpId.idString}")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService.validateUserCanAccessLpSupportingDocsOrServeOnLpSideR(lpId, actor)
      docRequestId <- getDocRequestId(lpId)
      docRequest <- docRequestService.getDocRequest(docRequestId, None)
      fundSubRestrictedModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops
          .getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(lpId.parent))
      }
      relatedFormIds = fundSubRestrictedModel.taxFormGroups.toSeq.flatMap(_._2.formVersionIds.map(_.parent))
      relatedFormNames <- formService.getFormNames(relatedFormIds).map(_.toMap)
      docTypeWithRelatedForms = fundSubRestrictedModel.taxFormGroups.toSeq.map { case (docType, formInfo) =>
        DocTypeWithRelatedForms(
          name = docType.trim,
          forms = formInfo.formVersionIds.map { formVersionId =>
            val name = relatedFormNames.getOrElse(formVersionId.parent, "")
            FormNameAndVersionId(
              name = name,
              formId = formVersionId
            )
          }
        )
      }
      docGroups <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops
          .getFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId))
          .map(_.requiredDocGroupInfo)
      }
    } yield GetDocRequestResponse(
      docRequest = docRequest,
      globalDocTypes = fundSubRestrictedModel.supportingDocs.map(doc => doc.copy(name = doc.name.trim)),
      docGroups = docGroups,
      docTypeWithRelatedForms = docTypeWithRelatedForms
    )
  }

  def getDocSubmission(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[GetDocSubmissionResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is getting doc submission for ${lpId.idString}")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService.validateUserCanAccessLpSupportingDocsOrServeOnLpSideR(lpId, actor)
      docRequestId <- getDocRequestId(lpId)
      docSubmission <- docSubmissionService.get(DocSubmissionId(docRequestId), None)
      formSubmissions <- ZIO.foreach(docSubmission.formSubmissions) { formSubmissionId =>
        formSubmissionService.getFormSubmission(formSubmissionId, None)
      }
      submittedFilesWithName <- ZIO.foreachPar(docSubmission.submittedFiles) { fileId =>
        fileService.getFileName(actor)(fileId).map(name => FileIdAndName(fileId, name))
      }
    } yield GetDocSubmissionResponse(
      submittedFiles = submittedFilesWithName,
      formSubmissions = formSubmissions,
      fileDocTypeMapping = docSubmission.fileDocTypeMapping,
      formDocTypeMapping = docSubmission.formDocTypeMapping,
      fundSharedFileIds = docSubmission.fundSharedFileIds
    )
  }

  def getProvidedSupportingDocs(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Seq[String]] = {
    for {
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService.validateFundManagerCanMarkSupportingDocAsProvidedR(lpId, actor)
      docRequestId <- getDocRequestId(lpId)
      docRequest <- docRequestService.getDocRequest(docRequestId, Option(actor))
    } yield docRequest.providedItems
  }

  def getFundSubProvidedDocStatusData(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[GetFundSubProvidedDocStatusResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor is getting LP provided supporting document data for $fundSubId")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- fundSubPermissionService.validateFundManagerCanAccessLpSupportingDocsInFundR(fundSubId, actor)
      accessibleInvestorGroups <- fundSubInvestorGroupService.getAccessibleInvestorGroupsWithDetailedInfo(
        fundSubId = fundSubId,
        actor = actor,
        shouldGatherMemberDetail = true
      )
      accessibleLpIds = accessibleInvestorGroups.flatMap(_.members.map(_.lpId)).toSet
      accessibleLpRecords <- FundSubLpUtils.getLpInfoRecordsForFundManagerR(fundSubId, accessibleLpIds, actor)
      accessibleInvestorInfos <- ZIOUtils.foreachParN(parallelism)(accessibleLpRecords) { lpRecord =>
        for {
          docRequestId <- getDocRequestId(lpRecord.lpId)
          docRequest <- docRequestService.getDocRequest(docRequestId, None)
        } yield {
          LpProvidedSupDocInfo(
            lpId = lpRecord.lpId,
            firmName = lpRecord.firmName,
            firstName = lpRecord.firstName,
            lastName = lpRecord.lastName,
            email = lpRecord.email,
            tags = lpRecord.tags,
            closeOpt = lpRecord.fundSubCloseIdOpt,
            status = lpRecord.status,
            providedDocs = docRequest.providedItems
          )
        }
      }
    } yield {
      GetFundSubProvidedDocStatusResponse(
        accessibleInvestorInfos,
        accessibleInvestorGroups
      )
    }
  }

  def markDocTypeAsNotApplicable(
    params: MarkDocTypeAsNotApplicableParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is marking doc ${params.docType} as NA")
      _ <- validateMarkAsNotApplicablePermission(params.lpId, actor)
      docType = params.docType.trim
      _ <- ZIOUtils.validate(docType.nonEmpty)(GeneralServiceException("DocType must not be empty"))
      docRequestId <- getDocRequestId(params.lpId)
      _ <- docRequestService.markedDocRequestItemAsNA(
        docRequestId,
        docType,
        Option(actor)
      )
      _ <- updateDocRequestStatus(params.lpId, newFileIds = List.empty)
      activityDetail = ActionOnSupportingDoc(
        docTypes = Seq(params.docType),
        action = SupportingDocAction.MarkAsNotApplicable
      )
      _ <- auditLogService.addEvent(
        fundSubId = params.lpId.parent,
        params = AddEventParam(
          actor = Option(actor),
          actorType = AuditLogActorType.FundSide,
          orderId = Option(params.lpId),
          eventType = AuditLogEventType.ADDITIONAL_DOCUMENT_MARK_AS_NOT_APPLICABLE,
          activityDetail = activityDetail
        )
      )
      _ <- lpActivityLogService.logActivity(
        lpId = params.lpId,
        actorOpt = Option(actor),
        detail = activityDetail
      )
      _ <- actionLoggerService.addEventLog(
        actor = actor,
        events = Seq(
          ActionEventAmlKycItemAction(
            params.lpId.parent,
            params.lpId,
            action = AmlKycAction.AML_KYC_ACTION_MARK_AS_NA,
            Seq(params.docType)
          )
        ),
        httpContextOpt = None,
        eventTime = Option(DateCalculator.instantNow)
      )
    } yield ()
  }

  def unmarkDocTypeAsNotApplicable(
    params: UnmarkDocTypeAsNotApplicableParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is un-marking doc ${params.docType} as NA")
      _ <- validateMarkAsNotApplicablePermission(params.lpId, actor)
      docType = params.docType.trim
      _ <- ZIOUtils.validate(docType.nonEmpty)(GeneralServiceException("DocType must not be empty"))
      docRequestId <- getDocRequestId(params.lpId)
      _ <- docRequestService.unmarkDocRequestItemAsNA(
        docRequestId,
        docType,
        Option(actor)
      )
      _ <- ZIOUtils.when(params.fileIds.nonEmpty)(
        supportingDocReviewService.attemptToSubmitForReviewWhenUploadDoc(
          params.lpId,
          params.docType,
          actor
        )
      )
      activityDetail = ActionOnSupportingDoc(
        docTypes = Seq(params.docType),
        action = SupportingDocAction.UnMarkAsNotApplicable
      )
      _ <- auditLogService.addEvent(
        fundSubId = params.lpId.parent,
        params = AddEventParam(
          actor = Option(actor),
          actorType = AuditLogActorType.FundSide,
          orderId = Option(params.lpId),
          eventType = AuditLogEventType.ADDITIONAL_DOCUMENT_MARK_AS_APPLICABLE,
          activityDetail = activityDetail
        )
      )
      _ <- lpActivityLogService.logActivity(
        lpId = params.lpId,
        actorOpt = Option(actor),
        detail = activityDetail
      )
      _ <- actionLoggerService.addEventLog(
        actor = actor,
        events = Seq(
          ActionEventAmlKycItemAction(
            params.lpId.parent,
            params.lpId,
            action = AmlKycAction.AML_KYC_ACTION_MARK_AS_APPLICABLE,
            Seq(params.docType)
          )
        ),
        httpContextOpt = None,
        eventTime = Option(DateCalculator.instantNow)
      )
      _ <- updateDocRequestStatus(params.lpId, newFileIds = List.empty)
    } yield ()
  }

  def markDocAsProvided(
    params: MarkDocAsProvidedParams,
    actor: UserId,
    shouldValidateUserHasPermission: Boolean = true
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is marking docs ${params.docNames} as provided")
      _ <- ZIOUtils.when(shouldValidateUserHasPermission)(
        for {
          given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.lpId.parent)
          _ <- fundSubPermissionService.validateFundManagerCanMarkSupportingDocAsProvidedR(params.lpId, actor)
        } yield ()
      )
      docNames = params.docNames.map(_.trim).filterNot(_.isEmpty)
      _ <- ZIOUtils.validate(docNames.nonEmpty)(GeneralServiceException("DocName must not be empty"))
      docRequestId <- getDocRequestId(params.lpId)
      docSubmission <- docSubmissionService.get(DocSubmissionId(docRequestId), actorOpt = None)
      mappedDocTypes = docSubmission.fileDocTypeMapping.values
      docsToMarkAsProvided = params.docNames.filterNot { docType =>
        mappedDocTypes.exists(_.trim.equalsIgnoreCase(docType.trim))
      }
      _ <- docRequestService.markDocRequestItemAsProvided(
        docRequestId,
        docsToMarkAsProvided,
        Option(actor)
      )
      _ <- updateDocRequestStatus(params.lpId, newFileIds = List.empty)
      latestProvidedDocs <- docRequestService.getDocRequest(docRequestId, None).map(_.providedItems)
      _ <- FundSubDataLakeUtils.sendUpdateParams(
        params.lpId.parent,
        UpdateProvidedDocsParams(
          Option(params.lpId),
          latestProvidedDocs
        ),
        dataLakeService = fundSubDataLakeIngestionService
      )
      activityDetail = ActionOnSupportingDoc(
        docTypes = params.docNames,
        action = SupportingDocAction.MarkAsProvided
      )
      _ <- auditLogService.addEvent(
        fundSubId = params.lpId.parent,
        params = AddEventParam(
          actor = Option(actor),
          actorType = AuditLogActorType.FundSide,
          orderId = Option(params.lpId),
          eventType = AuditLogEventType.ADDITIONAL_DOCUMENT_MARKED_AS_PROVIDED,
          activityDetail = activityDetail
        )
      )
      _ <- lpActivityLogService.logActivity(
        lpId = params.lpId,
        actorOpt = Option(actor),
        detail = activityDetail
      )
      _ <- actionLoggerService.addEventLog(
        actor = actor,
        events = docNames.map { docType =>
          ActionEventAmlKycItemAction(
            params.lpId.parent,
            params.lpId,
            action = AmlKycAction.AML_KYC_ACTION_MARK_AS_PROVIDED,
            Seq(docType)
          )
        },
        httpContextOpt = None,
        eventTime = Option(DateCalculator.instantNow)
      )
    } yield ()
  }

  def unmarkDocAsProvided(
    params: UnmarkDocAsProvidedParams,
    actor: UserId,
    shouldValidateUserHasPermission: Boolean = true
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is un-marking docs ${params.docNames} as provided")
      _ <- ZIOUtils.when(shouldValidateUserHasPermission)(
        for {
          given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.lpId.parent)
          _ <- fundSubPermissionService.validateFundManagerCanMarkSupportingDocAsProvidedR(params.lpId, actor)
        } yield ()
      )
      docNames = params.docNames.map(_.trim).filterNot(_.isEmpty)
      _ <- ZIOUtils.validate(docNames.nonEmpty)(GeneralServiceException("docNames must not be empty"))
      docRequestId <- getDocRequestId(params.lpId)
      _ <- docRequestService.unmarkDocRequestItemAsProvided(
        docRequestId,
        docNames,
        Option(actor)
      )
      _ <- updateDocRequestStatus(params.lpId, newFileIds = List.empty)
      currentProvidedDocs <- getProvidedSupportingDocs(params.lpId, actor)
      _ <- FundSubDataLakeUtils.sendUpdateParams(
        params.lpId.parent,
        UpdateProvidedDocsParams(
          Option(params.lpId),
          currentProvidedDocs
        ),
        dataLakeService = fundSubDataLakeIngestionService
      )
      _ <- updateDocRequestStatus(params.lpId, newFileIds = List.empty)
      activityDetail = ActionOnSupportingDoc(
        docTypes = params.docNames,
        action = SupportingDocAction.UnMarkAsProvided
      )
      _ <- auditLogService.addEvent(
        fundSubId = params.lpId.parent,
        params = AddEventParam(
          actor = Option(actor),
          actorType = AuditLogActorType.FundSide,
          orderId = Option(params.lpId),
          eventType = AuditLogEventType.ADDITIONAL_DOCUMENT_UNMARKED_AS_PROVIDED,
          activityDetail = activityDetail
        )
      )
      _ <- lpActivityLogService.logActivity(
        lpId = params.lpId,
        actorOpt = Option(actor),
        detail = activityDetail
      )
      _ <- actionLoggerService.addEventLog(
        actor = actor,
        events = docNames.map { docName =>
          ActionEventAmlKycItemAction(
            params.lpId.parent,
            params.lpId,
            action = AmlKycAction.AML_KYC_ACTION_UNMARK_AS_PROVIDED,
            Seq(docName)
          )
        },
        httpContextOpt = None,
        eventTime = Option(DateCalculator.instantNow)
      )
    } yield ()
  }

  def getReviewableSupportingDoc(
    params: GetReviewableSupportingDocParams,
    actor: UserId
  ): Task[GetReviewableSupportingDocResponse] = {
    for {
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.fundSubId)
      accessibleLps <- FundSubLpUtils
        .getAccessibleLpInfoRecordsForFundManagerR(
          fundSubId = params.fundSubId,
          actor = actor
        )
        .map(_.filterNot(_.isRemoved))
      filteredLps = params.lpIdsOpt.fold(accessibleLps)(lpIds =>
        accessibleLps.filter(lpInfo => lpIds.contains(lpInfo.lpId))
      )
      reviewableSupportingDocsByLpMap <- ZIOUtils.foreachParN(8)(filteredLps) { lpInfoRecord =>
        for {
          reviewDataResponse <- supportingDocReviewService.getLpSupportingDocReviewData(lpInfoRecord.lpId, actor)
          docRequestByAdmin <- getDocRequest(lpInfoRecord.lpId, actor)
            .map(_.docRequest.docRequestsByGp.map(_.name))
          fileInfoByDocType <- getDocSubmission(lpInfoRecord.lpId, actor)
            .map { docSubmissionRes =>
              docSubmissionRes.fileDocTypeMapping.groupMap(_._2)(_._1).map { case (docType, fileIds) =>
                docType -> fileIds
                  .flatMap(fileId =>
                    docSubmissionRes.submittedFiles.find(_.fileId == fileId).map { fileIdAndName =>
                      FileInfo(
                        itemId = fileIdAndName.fileId,
                        name = fileIdAndName.fileName
                      )
                    }
                  )
                  .toSeq
              }
            }
          pendingReviewDocTypes = reviewDataResponse.reviewData
            .filter { case (_, reviewData) =>
              reviewData.reviewStatus.isPendingReview && reviewData.reviewers.contains(actor)
            }
            .keys
            .toSeq
        } yield ReviewableSupportingDocsForAnInvestor(
          lpInfo = LpBasicInfo(
            lpId = lpInfoRecord.lpId,
            mainLp = UserBasicInfo(
              userId = lpInfoRecord.userId,
              firstName = lpInfoRecord.firstName,
              lastName = lpInfoRecord.lastName,
              email = lpInfoRecord.email
            ),
            collaborators = lpInfoRecord.collaborators.toList,
            firmName = lpInfoRecord.firmName
          ),
          reviewableSupportingDocs = pendingReviewDocTypes.map { docType =>
            ReviewableSupportingDoc(
              docType = docType,
              isRequestedByAdmin = docRequestByAdmin.contains(docType),
              filesInfo = fileInfoByDocType.get(docType).fold(Seq.empty[FileInfo])(identity)
            )
          }
        )
      }
    } yield GetReviewableSupportingDocResponse(reviewableSupportingDocsByLpMap)
  }

  def updateFundSubProvidedDocStatus(
    params: UpdateFundSubProvidedDocStatusParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      fundSubId <- FundSubLpUtils.validateLpListNonEmptyAndBelongToTheSameFund(params.investors.map(_.lpId))
      _ <- ZIO.logInfo(s"User ${actor.idString} is updating provided docs for fund ${fundSubId.idString}")
      _ <- ZIOUtils.validate(
        params.investors.exists { lp =>
          lp.docsToMarkAsProvided.nonEmpty || lp.docsToUnmarkAsProvided.nonEmpty
        }
      )(GeneralServiceException("Investor Provided Docs to update must not be empty"))
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- ZIO.foreach(params.investors) { lpInfo =>
        val task = for {
          _ <- fundSubPermissionService.validateFundManagerCanMarkSupportingDocAsProvidedR(lpInfo.lpId, actor)
          _ <- ZIOUtils.when(lpInfo.docsToMarkAsProvided.nonEmpty) {
            markDocAsProvided(
              params = MarkDocAsProvidedParams(lpInfo.lpId, lpInfo.docsToMarkAsProvided),
              actor = actor,
              shouldValidateUserHasPermission = false
            )
          }
          _ <- ZIOUtils.when(lpInfo.docsToUnmarkAsProvided.nonEmpty) {
            unmarkDocAsProvided(
              params = UnmarkDocAsProvidedParams(lpInfo.lpId, lpInfo.docsToUnmarkAsProvided),
              actor = actor,
              shouldValidateUserHasPermission = false
            )
          }
        } yield ()
        task.catchAllCause { error =>
          ZIO.logWarningCause(
            s"Cannot update provided supporting doc status for investor ${lpInfo.lpId}",
            error
          )
        }
      }
    } yield ()
  }

  def mapFileToDocType(
    params: MapFileToDocTypeParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is mapping file ${params.fileId.idString} to doc type ${params.docType}")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.lpId.parent)
      role <- fundSubPermissionService
        .validateFundManagerCanAccessLpSupportingDocsR(params.lpId, actor)
        .orElse(
          for {
            status <- fundSubPermissionService.validateLpStatusFn(params.lpId, _ => true)
            // when fund is closed, do not allow mapping to doc type after countersigned or when docType is nonempty
            _ <- ZIOUtils.when(status.isLpcountersigned || status.isLpcompleted || params.docType.nonEmpty)(
              fundSubPermissionService.validateFundActive(params.lpId.parent)
            )
            role <- fundSubPermissionService.validateUserCanServeOnInvestorSideR(params.lpId, actor)
          } yield role
        )
      _ <- validateShouldUpdateSupportingDoc(params.lpId, actor)
      originalDocType <- getMappedDoctype(
        params.lpId,
        params.fileId,
        Option(actor)
      )
      docRequestId <- getDocRequestId(params.lpId)
      docRequest <- docRequestService.getDocRequest(docRequestId, None)
      _ <- docSubmissionService.mapFileToDocType(
        DocSubmissionId(docRequestId),
        params.fileId,
        params.docType,
        Option(actor)
      )
      isSkippedDocType = docRequest.items.exists(item =>
        item.name.trim.equalsIgnoreCase(params.docType.trim) && item.markedAsNa
      ) ||
        docRequest.providedItems.exists(_.trim.equalsIgnoreCase(params.docType.trim))
      _ <- ZIOUtils.when(role != FundSubRole.FundManager && params.docType.nonEmpty && !isSkippedDocType)(
        supportingDocReviewService.attemptToSubmitForReviewWhenUploadDoc(
          params.lpId,
          params.docType,
          actor
        )
      )
      _ <- ZIOUtils.when(role != FundSubRole.FundManager && originalDocType.nonEmpty && params.docType.isEmpty) {
        for {
          allMappedFiles <- getAllMappedFilesOfDocType(
            params.lpId,
            originalDocType,
            Option(actor)
          )
          _ <- supportingDocReviewService.attemptToUpdateReviewWhenRemoveDoc(
            params.lpId,
            originalDocType,
            isLastFile = allMappedFiles.isEmpty
          )
        } yield ()
      }
      _ <- updateDocRequestStatus(params.lpId, newFileIds = List.empty)
      _ <- ZIOUtils.when(params.docType.isEmpty && originalDocType.nonEmpty) {
        actionLoggerService.addEventLog(
          actor = actor,
          events = Seq(
            ActionEventAmlKycItemAction(
              params.lpId.parent,
              params.lpId,
              action = AmlKycAction.AML_KYC_ACTION_REMOVE_FILE,
              Seq(originalDocType)
            )
          ),
          httpContextOpt = None,
          eventTime = Option(DateCalculator.instantNow)
        )
      }
    } yield ()
  }

  def uploadFilesToFormSubmission(
    params: UploadFilesToFormSubmissionParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[FormSubmission] = {
    for {
      _ <- ZIO.logInfo(s"${actor.idString} is uploading files to form submission ${params.formSubmissionId.idString}")
      lpId <- getLpIdFromFormSubmissionId(params.formSubmissionId)
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      _ <- validateShouldUpdateSupportingDoc(lpId, actor)
      formSubmission <- formSubmissionService.submitFiles(
        id = params.formSubmissionId,
        files = params.fileIds,
        actor = actor,
        httpContextOpt = ctx,
        isFormGeneratedDocs = false
      )
      docSubmission <- docSubmissionService.get(DocSubmissionId(params.formSubmissionId.parent), None)
      _ <- afterSupportingDocsUploaded(
        lpId,
        actor,
        ctx,
        shouldAttemptToSubmitForReview = true,
        sharedFiles = formSubmission.allFiles,
        docType = docSubmission.formDocTypeMapping.getOrElse(params.formSubmissionId, "")
      )
    } yield formSubmission
  }

  private def afterSupportingDocsUploaded(
    lpId: FundSubLpId,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext],
    shouldAttemptToSubmitForReview: Boolean,
    sharedFiles: Seq[FileId],
    docType: String,
    shouldTriggerAutoSaveProfile: Boolean = true
  ) = {
    for {
      _ <- fundSubStorageIntegrationService.syncFilesToDataRoomIntegration(
        fundSubLpId = lpId,
        sender = actor,
        files = sharedFiles.map(_ -> FundSubStorageWhenToSend.LP_UPLOAD_SUPPORTING),
        httpContext = ctx
      )
      _ <- ZIO.when(docType.nonEmpty && shouldAttemptToSubmitForReview) {
        supportingDocReviewService.attemptToSubmitForReviewWhenUploadDoc(
          lpId,
          docType,
          actor
        )
      }
      _ <- updateDocRequestStatus(lpId, newFileIds = sharedFiles.toList)
      _ <- newSupportingDocLoggingService.supportingDocFilesAdded(
        lpId = lpId,
        files = sharedFiles,
        docName = docType,
        actor = actor,
        httpContext = ctx
      )
      _ <- OrderActivityUtils.addActivity(
        lpId = lpId,
        eventType = OrderActivityType.ORDER_ACTIVITY_TYPE_SUPPORTING_DOCS_SUBMITTED,
        createdAt = None
      )
      // Trigger auto-save to profile
      _ <- ZIO.when(shouldTriggerAutoSaveProfile) {
        triggerAutoSaveProfileFromSubscription(actor, lpId)
      }
    } yield ()
  }

  private def removeSaveSupportingFormToProfileFlag(
    lpId: FundSubLpId,
    formSubmissionId: FormSubmissionId
  ) = {
    for {
      _ <- ZIO.logInfo(s"Remove form submission ${formSubmissionId.idString} save to profile event")
      _ <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.updateFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId))(model =>
          model.copy(
            saveProfileSupportingFormEvents =
              model.saveProfileSupportingFormEvents.filterNot(_.formSubmissionId == formSubmissionId)
          )
        )
      }
    } yield ()
  }

  def resubmitFormSubmission(
    formSubmissionId: FormSubmissionId,
    actor: UserId
  ): Task[FormSubmission] = {
    for {
      _ <- ZIO.logInfo(s"${actor.idString} is resubmit form submission ${formSubmissionId.idString}")
      lpId <- getLpIdFromFormSubmissionId(formSubmissionId)
      _ <- fundSubPermissionService.validateFundActive(lpId.parent)
      _ <- validateShouldUpdateSupportingDoc(lpId, actor)
      submittedFiles <- formSubmissionService
        .getFormSubmission(formSubmissionId, actorOpt = None)
        .map(_.allFiles)
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      docType <- docSubmissionService
        .get(DocSubmissionId(formSubmissionId.parent), actorOpt = None)
        .map(
          _.formDocTypeMapping
            .getOrElse(formSubmissionId, "")
            .trim
        )
      formSubmission <- formSubmissionService.resubmit(
        id = formSubmissionId,
        actorOpt = Option(actor)
      )
      _ <- updateDocRequestStatus(lpId, newFileIds = List.empty)
      _ <- newSupportingDocLoggingService.resubmitFiles(
        lpId = lpId,
        files = submittedFiles,
        actor = actor
      )
      _ <- ZIOUtils.when(docType.nonEmpty) {
        for {
          allMappedFiles <- getAllMappedFilesOfDocType(
            lpId,
            docType,
            Option(actor)
          )
          _ <- supportingDocReviewService.attemptToUpdateReviewWhenRemoveDoc(
            lpId,
            docType,
            isLastFile = allMappedFiles.isEmpty
          )
        } yield ()
      }
      // Remove flag so save to profile can be trigger again
      _ <- removeSaveSupportingFormToProfileFlag(lpId, formSubmissionId)
    } yield formSubmission
  }

  def removingFile(
    params: RemoveFileParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is removing supporting doc file ${params.fileId.idString} for lp ${params.lpId.idString}"
      )
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.lpId.parent)
      _ <- fundSubPermissionService
        .validateFundManagerCanDeleteSubmittedSupportingDocsR(params.lpId, actor)
        .orElse(
          for {
            status <- fundSubPermissionService.validateLpStatusFn(params.lpId, _ => true)
            // when fund is closed, do not allow removing file after countersigned
            _ <- ZIOUtils.when(status.isLpcountersigned || status.isLpcompleted)(
              fundSubPermissionService.validateFundActive(params.lpId.parent)
            )
            _ <- fundSubPermissionService.validateUserCanServeOnInvestorSideR(params.lpId, actor)
          } yield ()
        )
      _ <- validateShouldUpdateSupportingDoc(params.lpId, actor)
      docRequestId <- getDocRequestId(params.lpId)
      docSubmission <- docSubmissionService.get(DocSubmissionId(docRequestId), actorOpt = None)
      _ <- ZIOUtils.when(docSubmission.submittedFiles.contains(params.fileId)) {
        for {
          newDocSubmission <- docSubmissionService.removeFile(
            DocSubmissionId(docRequestId),
            params.fileId,
            Option(actor)
          )
          _ <- ZIOUtils.traverseOptionUnit(docSubmission.fileDocTypeMapping.get(params.fileId)) { docType =>
            supportingDocReviewService.attemptToUpdateReviewWhenRemoveDoc(
              params.lpId,
              docType,
              isLastFile = !newDocSubmission.fileDocTypeMapping.exists(_._2 == docType)
            )
          }
          _ <- updateDocRequestStatus(params.lpId, newFileIds = List.empty)
          _ <- newSupportingDocLoggingService.resubmitFiles(
            lpId = params.lpId,
            files = Seq(params.fileId),
            actor = actor
          )
        } yield ()
      }
    } yield ()
  }

  def removingForm(
    formSubmissionId: FormSubmissionId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is removing form submission ${formSubmissionId.idString}")
      lpId <- getLpIdFromFormSubmissionId(formSubmissionId)
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService
        .validateFundManagerCanDeleteSubmittedSupportingDocsR(lpId, actor)
        .orElse(
          for {
            status <- fundSubPermissionService.validateLpStatusFn(lpId, _ => true)
            // when fund is closed, only allow fund admin remove form after countersigned
            _ <- ZIOUtils.when(status.isLpcountersigned || status.isLpcompleted)(
              fundSubPermissionService.validateFundActive(lpId.parent)
            )
            _ <- fundSubPermissionService.validateUserCanServeOnInvestorSideR(lpId, actor)
          } yield ()
        )
      _ <- validateShouldUpdateSupportingDoc(lpId, actor)
      docRequestId <- getDocRequestId(lpId)
      formName <- formSubmissionService.getFormSubmission(formSubmissionId, actorOpt = None).map(_.name)
      _ <- removeFormSubmission(formSubmissionId, lpId, actor)
      _ <- docSubmissionService.removeForm(
        DocSubmissionId(docRequestId),
        formSubmissionId,
        Option(actor)
      )
      _ <- updateDocRequestStatus(lpId, newFileIds = List.empty)
      _ <- lpActivityLogService.logActivity(
        lpId = lpId,
        actorOpt = Option(actor),
        detail = RemovedAdditionalForm(formName)
      )
      _ <- actionLoggerService.addEventLog(
        actor = actor,
        events = Seq(
          ActionEventAmlKycItemAction(
            lpId.parent,
            lpId,
            action = AmlKycAction.AML_KYC_ACTION_REMOVE_FORM,
            Seq(formName)
          )
        ),
        httpContextOpt = None,
        eventTime = None
      )
    } yield ()
  }

  def removingDocRequestItem(
    params: RemoveDocRequestParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is removing doc request ${params.docType} for lp ${params.lpId.idString}"
      )
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.lpId.parent)
      _ <- fundSubPermissionService.validateFundManagerCanManageAdditionalSupportingDocRequestsR(params.lpId, actor)
      docRequestId <- getDocRequestId(params.lpId)
      docRequest <- docRequestService.getDocRequest(docRequestId, Option(actor))
      isAdminRequestedDoc = docRequest.items.exists { item =>
        item.name.trim.equalsIgnoreCase(params.docType.trim) && item.creatorOpt.nonEmpty
      }
      _ <- ZIOUtils.validate(isAdminRequestedDoc) {
        GeneralServiceException(s"User ${actor.idString} is trying to remove form required doc ${params.docType}")
      }
      docSubmission <- docSubmissionService.get(DocSubmissionId(docRequestId), Option(actor))
      mappedFiles = docSubmission.fileDocTypeMapping
        .filter(_._2.equalsIgnoreCase(params.docType.trim))
        .keys
      mappedForms = docSubmission.formDocTypeMapping
        .filter(_._2.equalsIgnoreCase(params.docType.trim))
        .keys
      _ <- ZIO.foreach(mappedFiles) { fileId =>
        docSubmissionService.mapFileToDocType(
          DocSubmissionId(docRequestId),
          fileId,
          docType = "",
          Option(actor)
        )
      }
      _ <- ZIO.foreach(mappedForms) { formSubmissionId =>
        docSubmissionService.mapFormToDocType(
          DocSubmissionId(docRequestId),
          formSubmissionId,
          docType = "",
          Option(actor)
        )
      }
      _ <- docRequestService.removeDocRequestItem(
        docRequestId,
        params.docType.trim,
        Option(actor)
      )
      _ <- updateDocRequestStatus(params.lpId, newFileIds = mappedFiles.toList)
      _ <- newSupportingDocLoggingService.removeRequestedDocItems(
        params.lpId,
        params.docType.trim,
        actor
      )
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = params.lpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          orderId = Option(params.lpId),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.ADDITIONAL_DOCUMENT_REQUEST_DELETED,
          activityDetail = GeneralActivity(Value.Empty)
        )
      )
      _ <- actionLoggerService.addEventLog(
        actor = actor,
        events = Seq(
          ActionEventAmlKycItemAction(
            params.lpId.parent,
            params.lpId,
            action = AmlKycAction.AML_KYC_ACTION_REMOVE_DOC_REQUEST,
            Seq(params.docType)
          )
        ),
        httpContextOpt = None,
        eventTime = Option(DateCalculator.instantNow)
      )
    } yield ()
  }

  def removeAllMappedFilesInDocType(
    params: RemoveAllMappedFilesInDocTypeParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is removing all files in ${params.docType} for lp ${params.lpId.idString}"
      )
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(params.lpId.parent)
      _ <- fundSubPermissionService.validateFundManagerCanDeleteSubmittedSupportingDocsR(params.lpId, actor)
      docRequestId <- getDocRequestId(params.lpId)
      docSubmission <- docSubmissionService.get(DocSubmissionId(docRequestId), Option(actor))
      mappedFiles = docSubmission.fileDocTypeMapping
        .filter(_._2.equalsIgnoreCase(params.docType.trim))
        .keys

      mappedForms = docSubmission.formDocTypeMapping
        .filter(_._2.equalsIgnoreCase(params.docType.trim))
        .keys
      _ <- ZIO.foreach(mappedFiles) { fileId =>
        docSubmissionService.removeFile(
          DocSubmissionId(docRequestId),
          fileId,
          Option(actor)
        )
      }
      _ <- ZIO.foreach(mappedForms) { formSubmissionId =>
        for {
          _ <- docSubmissionService.removeForm(
            DocSubmissionId(docRequestId),
            formSubmissionId,
            Option(actor)
          )
          _ <- removeFormSubmission(formSubmissionId, lpId = params.lpId, actor)
        } yield ()
      }
      _ <- supportingDocReviewService.attemptToUpdateReviewWhenRemoveDoc(
        params.lpId,
        params.docType,
        isLastFile = true
      )
      _ <- updateDocRequestStatus(params.lpId, newFileIds = List.empty)
      _ <- ZIOUtils.when(mappedFiles.nonEmpty) {
        newSupportingDocLoggingService.resubmitFiles(
          params.lpId,
          mappedFiles.toList,
          actor
        )
      }
    } yield ()
  }

  def getSupportingDocGroup(
    params: GetSupportingDocGroupParams,
    actor: UserId
  ): Task[GetSupportingDocGroupResponse] = {
    val lpId = params.fundSubLpId
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is getting supporting doc group for ${lpId.idString}")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService.validateUserCanAccessLpSupportingDocsOrServeOnLpSideR(lpId, actor)
      docGroups <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops
          .getFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId))
          .map(_.requiredDocGroupInfo)
      }
    } yield GetSupportingDocGroupResponse(docGroups)
  }

  def prepareDocRequestIdIfNecessary(
    creatingLpId: FundSubLpId,
    teamIdsFromLpSide: Set[TeamId],
    investorGroupIdOpt: Option[FundSubInvestorGroupId]
  ): Task[Option[DocRequestId]] = {
    for {
      shouldCreate <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops
          .getFundSubPublicModel(creatingLpId.parent)
          .map(_.lpFlowType.isFlexible)
      }
      docRequestIdOpt <-
        if (shouldCreate) {
          for {
            supportingDocAuthorizedTeamIds <- FundSubInvestorGroupUtils.getCreatingLpSupportingDocAuthorizedTeamIds(
              creatingLpId,
              investorGroupIdOpt
            )
            docRequest <- docRequestService.createDocRequest(
              channelId = creatingLpId,
              submitterTeamIds = teamIdsFromLpSide,
              receiverTeamIds = supportingDocAuthorizedTeamIds,
              docsList = Seq.empty,
              sharedFolderId = FolderId.channelSystemFolderId(FolderType.SupportingDoc(creatingLpId)),
              actorOpt = None
            )
          } yield Option(docRequest.id)
        } else {
          ZIO.succeed[Option[DocRequestId]](None)
        }
    } yield docRequestIdOpt
  }

  def getFormData(formSubmissionId: FormSubmissionId, actor: UserId): Task[GetFormDataResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.idString} is getting form data of form submission ${formSubmissionId.idString}")
      lpId <- getLpIdFromFormSubmissionId(formSubmissionId)
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      formSubmission <- formSubmissionService.getFormSubmission(formSubmissionId, Option(actor))
      formData <- formService.getForm(
        formId = formSubmission.formDataId.parent.parent,
        versionIdOpt = Option(formSubmission.formDataId.parent),
        actor = actor,
        shouldCheckPermission = false
      )
      gaiaState <- FDBRecordDatabase
        .transact(LpFormDataOperations.Production)(_.getOpt(formSubmission.formDataId))
        .map { formDataOpt =>
          GaiaState
            .fromJsonMap(
              states = formDataOpt.map(_.defaultValues.map { case (k, v) => k.rawAlias -> v }).getOrElse(Map.empty),
              events = FormDataProtoConverter.protoToPatchesList(formDataOpt.map(_.patchesList).getOrElse(Seq.empty))
            )
        }
    } yield GetFormDataResponse(
      formModel = GaiaFormModel(
        formName = formData.formModel.name,
        formData = formData.formData,
        formVersionId = formSubmission.formDataId.parent
      ),
      data = gaiaState
    )
  }

  def saveFormValues(params: SaveFormValuesParams, actor: UserId): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"${actor.idString} is saving form values of form submission ${params.formSubmissionId.idString}")
      lpId <- getLpIdFromFormSubmissionId(params.formSubmissionId)
      _ <- fundSubPermissionService.validateFundActive(lpId.parent)
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      _ <- validateShouldUpdateSupportingDoc(lpId, actor)
      _ <- formSubmissionService.saveFormData(
        params.formSubmissionId,
        params.data,
        actor
      )
    } yield ()
  }

  def generatePdfFile(
    formSubmissionId: FormSubmissionId,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[GenerateFormPdfResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.idString} is generating pdf files of form submission ${formSubmissionId.idString}")
      lpId <- getLpIdFromFormSubmissionId(formSubmissionId)
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      pdfTextColorOpt <- FundSubCommonUtils.getPDFTextColor(lpId.parent)
      pdfData <- formSubmissionService.generateFilledFiles(
        formSubmissionId,
        actor,
        ctx,
        pdfTextColorOpt
      )
    } yield GenerateFormPdfResponse(
      pdfData
    )
  }

  def signingForm(
    params: SigningFormParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[FormSubmission] = {
    for {
      _ <- ZIO.logInfo(s"${actor.idString} is signing form submission ${params.formSubmissionId.idString}")
      lpId <- getLpIdFromFormSubmissionId(params.formSubmissionId)
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      _ <- validateShouldUpdateSupportingDoc(lpId, actor)
      fundSubModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
        _.getFundSubPublicModel(lpId.parent)
      }
      eSignatureProviderParams <- FundSubSignatureJvmUtils
        .resolveSignatureProviderParams(
          fundSubModel,
          backendConfig.docusignIntegrationConfig,
          Some(FundSubSignatureRequestDocType.FormSubmissionDoc),
          params.docusignESignatureOptionParamsOpt,
          actor
        )(
          using userProfileService
        )
      requestBasic <- formSubmissionService.signForm(
        formSubmissionId = params.formSubmissionId,
        cleanFileIds = params.cleanFileIds,
        signers = Seq(
          FundSubSignerParams(
            userId = actor,
            signatures = params.signatures,
            blocks = params.preparedBlocks
          )
        ),
        actor = actor,
        httpContextOpt = ctx,
        eSignatureProviderParams = eSignatureProviderParams,
        fundSubSignatureDateFormatOpt = fundSubModel.signatureConfig.flatMap(_.dateFormat)
      )
      _ <- ZIO.when(requestBasic.doneSigning) {
        afterSingleSupportingDocSignatureRequestComplete(
          formSubmissionId = params.formSubmissionId,
          lpId = lpId,
          actor = actor,
          ctx = ctx,
          requestBasic = requestBasic
        )
      }
      formSubmission <- formSubmissionService.getFormSubmission(params.formSubmissionId, Some(actor))
    } yield formSubmission
  }

  def requestSignature(
    params: RequestFormSignatureParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[RequestFormSignatureResp] = {
    for {
      _ <- ZIO.logInfo(
        s"${actor.idString} is requesting signature for form submission ${params.formSubmissionId.idString}"
      )
      lpId <- getLpIdFromFormSubmissionId(params.formSubmissionId)
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      _ <- fundSubPermissionService.validateFundActive(lpId.parent)
      _ <- validateShouldUpdateSupportingDoc(lpId, actor)
      fundSubModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
        _.getFundSubPublicModel(lpId.parent)
      }
      userEmailToIdMap <- fundSubUserService.createSignatureUserIfNeeded(
        fundSubId = lpId.parent,
        signers = params.signers.map { signer =>
          signer.email -> signer.name
        },
        inviterOpt = None
      )
      signers = params.signers.flatMap { signer =>
        userEmailToIdMap
          .get(signer.email)
          .map { userId =>
            FundSubSignerParams(
              userId = userId,
              signatures = signer.signatures,
              blocks = signer.blocks,
              authTypeData = signer.authTypeData,
              docusignRecipientAuthDataOpt = signer.docusignRecipientAuthDataOpt,
              canAccessSignatureRequiredDocOnly = signer.canAccessSignatureRequiredDocOnly
            )
          }
      }
      eSignatureProviderParams <- FundSubSignatureJvmUtils
        .resolveSignatureProviderParams(
          fundSubModel,
          backendConfig.docusignIntegrationConfig,
          Some(FundSubSignatureRequestDocType.FormSubmissionDoc),
          params.docusignESignatureOptionParamsOpt,
          actor
        )(
          using userProfileService
        )
      signatureRequestId <- formSubmissionService.addSignatureRequest(
        id = params.formSubmissionId,
        cleanFileIds = params.cleanFileIds,
        signers = signers,
        message = params.message,
        actor = actor,
        httpContextOpt = ctx,
        eSignatureProviderParams = eSignatureProviderParams,
        refDocs = params.refDocs,
        fundSubSignatureDateFormatOpt = fundSubModel.signatureConfig.flatMap(_.dateFormat)
      )
      signatureRequest <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(signatureRequestId)
      _ <- newSupportingDocLoggingService.requestFormSignature(
        lpId = lpId,
        formSubmissionId = params.formSubmissionId,
        signatureRequestId = signatureRequestId,
        signers = signers.map(_.userId),
        files = signatureRequest
          .getSupportingFormPackage(params.formSubmissionId, lpId)
          .map(_.getUnsignedSignatureFileIds)
          .getOrElse(Seq.empty),
        message = params.message,
        actor = actor,
        envelopeType = signatureRequest.envelopeType
      )
    } yield RequestFormSignatureResp(signatureRequestId)
  }

  def getSignatureRequestInfo(
    signatureRequestId: SignatureRequestId,
    actor: UserId
  ): Task[SignatureRequestInfo] = {
    given userProfileServiceImpl: UserProfileService = userProfileService
    for {
      _ <- ZIO.logInfo(s"${actor.idString} is getting signature request info ${signatureRequestId.idString}")
      lpId <- fundSubSignatureIntegrationService.getLpIdFromSignatureRequest(signatureRequestId)
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      requestBasic <- fundSubSignatureIntegrationService.getFundSubSignatureRequestWithoutFilePackageInfo(
        signatureRequestId
      )
      requesterName <- userProfileService.getUserInfo(requestBasic.requester).map(_.fullNameString)
      signers <- FundSubSignatureJvmUtils.convertToSignatureRequestSigners(requestBasic.signers)
      requestInfo = SignatureRequestInfo(
        signatureRequestId = requestBasic.requestId,
        requester = requestBasic.requester,
        requesterName = requesterName,
        signers = signers,
        sentAt = requestBasic.createdAt.map(_.toInstant),
        enabledQesOpt = requestBasic.enabledQesOpt,
        eSignatureProvider = requestBasic.eSignatureProvider
      )
    } yield requestInfo
  }

  def signingSignatureRequest(
    params: SigningFormSignatureRequestParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    for {
      requestEnvelopeTypeOpt <- fundSubSignatureIntegrationService.getEnvelopeTypeOptOfRequest(params.signatureRequestId)
      formSubmissionId <- ZIOUtils.fromOption(
        requestEnvelopeTypeOpt match {
          case Some(singleDocType: FundSubSignatureEnvelopeType.SingleDocTypeEnvelope) =>
            singleDocType.formSubmissionIdOpt
          case _ => None
        },
        GeneralServiceException(s"Cannot derive form submission id from signature request ${params.signatureRequestId}")
      )
      _ <- ZIO.logInfo(
        s"${actor.idString} is signing signature request on form submission ${formSubmissionId.idString}"
      )
      lpId <- getLpIdFromFormSubmissionId(formSubmissionId)
      pendingSignatureRequestIdOpt <- fundSubSignatureIntegrationService
        .getFormSubmissionSignatureRequestsMetadata(
          lpId,
          formSubmissionId = formSubmissionId
        )
        .map(_.headOption.map(_.requestId))
      _ <- ZIOUtils.validate(pendingSignatureRequestIdOpt.contains(params.signatureRequestId))(
        GeneralServiceException(
          s"Signature requestId does not match. Expected: ${params.signatureRequestId.idString}" +
            s" but found: $pendingSignatureRequestIdOpt"
        )
      )
      requestBasic <- formSubmissionService.signingSignatureRequest(
        formSubmissionId = formSubmissionId,
        signatureRequestId = params.signatureRequestId,
        signatures = params.signatures,
        actor = actor,
        httpContextOpt = ctx
      )
      _ <- ZIOUtils.when(requestBasic.doneSigning) {
        afterSingleSupportingDocSignatureRequestComplete(
          formSubmissionId,
          lpId,
          actor,
          ctx,
          requestBasic
        )
      }
    } yield ()
  }

  def triggerAutoSaveProfileFromSubscription(actor: UserId, fundSubLpId: FundSubLpId)
    : Task[Unit] // Asynchronously check autosave criteria and autosave profile
  = {
    for {
      workflowId <- ZIO.attempt(
        TemporalWorkflowId.unsafeFromSuffix(s"FundSubAutoSaveSubscriptionData-${UUID.randomUUID.toString}")
      )
      workflowStub <- FundSubAutoSaveSubscriptionDataWorkflowImpl.instance
        .getWorkflowStub(workflowId)
        .provideEnvironment(temporalEnvironment.workflowClient)
      _ <- ZWorkflowStub.start(
        workflowStub.autoSaveSubscriptionForm(AutoSaveSubscriptionFormParams(actor = actor, fundSubLpId = fundSubLpId))
      )
    } yield ()
  }

  private def updateDocRequestInternalDataWhenRequestCompleted(
    formSubmission: FormSubmission,
    lpId: FundSubLpId,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    val formSubmissionId = formSubmission.id
    val docRequestId = formSubmissionId.parent
    val sharedFiles = formSubmission.allFiles
    for {
      _ <- fundSubStorageIntegrationService.syncFilesToDataRoomIntegration(
        fundSubLpId = lpId,
        sender = actor,
        files = sharedFiles.map(_ -> FundSubStorageWhenToSend.LP_UPLOAD_SUPPORTING),
        httpContext = ctx
      )
      docType <- docSubmissionService
        .get(DocSubmissionId(docRequestId), Option(actor))
        .map(
          _.formDocTypeMapping
            .getOrElse(formSubmissionId, "")
            .trim
        )
      _ <- ZIOUtils.when(docType.nonEmpty) {
        supportingDocReviewService.attemptToSubmitForReviewWhenUploadDoc(
          lpId,
          docType,
          actor
        )
      }
      _ <- updateDocRequestStatus(
        lpId = lpId,
        docRequestId = docRequestId,
        newFileIds = sharedFiles.toList
      )
    } yield ()
  }

  def afterOneEnvelopeSupportingDocSignatureRequestCompleted(
    formSubmissionId: FormSubmissionId,
    lpId: FundSubLpId,
    requestBasic: FundSubSignatureRequestBasic,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    for {
      actorToSubmit <- getActorToSubmitFormAfterSigning(lpId, requestBasic.requester)
      formSubmission <- formSubmissionService.getFormSubmission(formSubmissionId, Option(actorToSubmit))
      _ <- updateDocRequestInternalDataWhenRequestCompleted(
        formSubmission,
        lpId,
        actorToSubmit,
        ctx
      )
      _ <- newSupportingDocLoggingService.supportingDocFilesAdded(
        lpId = lpId,
        files = formSubmission.submittedFiles,
        docName = formSubmission.name,
        actorToSubmit,
        httpContext = ctx
      )
    } yield ()
  }

  def afterSingleSupportingDocSignatureRequestComplete(
    formSubmissionId: FormSubmissionId,
    lpId: FundSubLpId,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext],
    requestBasic: FundSubSignatureRequestBasic
  ): Task[Unit] = {
    for {
      actorToSubmit <- getActorToSubmitFormAfterSigning(lpId, requestBasic.requester)
      formSubmission <- formSubmissionService.getFormSubmission(formSubmissionId, Option(actorToSubmit))
      _ <- updateDocRequestInternalDataWhenRequestCompleted(
        formSubmission,
        lpId,
        actorToSubmit,
        ctx
      )
      _ <- newSupportingDocLoggingService.supportingDocFilesAdded(
        lpId = lpId,
        files = formSubmission.submittedFiles,
        docName = formSubmission.name,
        actor = actorToSubmit,
        httpContext = ctx
      )
      _ <- sendNotificationEmailOnSignatureRequestComplete(lpId, requestBasic, actor, formSubmissionId)
      _ <- ZIO.logInfo(s"${actorToSubmit.idString} SIGNATURE_REQUEST_ON_ADDITIONAL_DOCUMENT_COMPLETED")
      _ <- triggerAutoSaveProfileFromSubscription(actorToSubmit, lpId)
    } yield ()
  }

  private def getActorToSubmitFormAfterSigning(
    lpId: FundSubLpId,
    requester: UserId
  ): Task[UserId] =
    for {
      requesterHasPermission <- fundSubPermissionService.checkIfUserCanAccessLpView(
        lpId,
        requester
      )
      actorToSubmit <-
        if (requesterHasPermission) {
          ZIO.attempt(requester)
        } else {
          FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(
            _.getFundSubLpModel(lpId).map(_.mainLp)
          )
        }
    } yield actorToSubmit

  private def sendNotificationEmailOnSignatureRequestComplete(
    lpId: FundSubLpId,
    requestBasic: FundSubSignatureRequestBasic,
    actor: UserId,
    formSubmissionId: FormSubmissionId
  ): Task[Unit] = {
    for {
      emailIdOpt <- ZIO
        .when(requestBasic.requester != actor) {
          fundSubEmailService
            .sendFormSubmissionSignatureRequestCompleteToRequesterEmail(
              lpId = lpId,
              actor = actor,
              requester = requestBasic.requester,
              fileIds = requestBasic
                .getSupportingFormPackage(formSubmissionId, lpId)
                .map(_.getSignedFileIds)
                .getOrElse(Seq.empty),
              signatureRequestId = requestBasic.requestId,
              envelopeType = requestBasic.envelopeType
            )
            .map(_.flatMap(_.internalIdOpt))
        }
        .map(_.flatten)
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = lpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.InvestorSide,
          eventType = AuditLogEventType.SIGNATURE_REQUEST_ON_ADDITIONAL_DOCUMENT_COMPLETED,
          orderId = Option(lpId),
          eventEmail = emailIdOpt.map { emailId =>
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.doneFormSubmissionSignatureRequest,
              emailIds = Seq(emailId)
            )
          }.toSeq,
          activityDetail = GeneralActivity(Value.Empty)
        )
      )
    } yield ()
  }

  def cancelSignatureRequest(
    formSubmissionId: FormSubmissionId,
    signatureRequestId: SignatureRequestId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"${actor.idString} is canceling signature request on form submission ${formSubmissionId.idString}"
      )
      lpId <- getLpIdFromFormSubmissionId(formSubmissionId)
      _ <- fundSubPermissionService.validateUserCanAccessLpView(
        lpId,
        actor
      )
      _ <- validateShouldUpdateSupportingDoc(lpId, actor)
      pendingSignatureRequestIdOpt <- fundSubSignatureIntegrationService
        .getFormSubmissionSignatureRequestsMetadata(
          lpId,
          formSubmissionId
        )
        .map(_.headOption.map(_.requestId))
      _ <- ZIOUtils.validate(pendingSignatureRequestIdOpt.contains(signatureRequestId))(
        GeneralServiceException(
          s"Signature requestId does not match. Expected: ${signatureRequestId.idString}" +
            s" but found: $pendingSignatureRequestIdOpt"
        )
      )
      docName <- formSubmissionService.getFormSubmission(formSubmissionId, None).map(_.name)
      _ <- formSubmissionService.cancelSignatureRequest(
        id = formSubmissionId,
        signatureRequestId = signatureRequestId,
        actor = actor
      )
      requestBasic <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(signatureRequestId)
      _ <- newSupportingDocLoggingService.cancelFormSignatureRequest(
        lpId = lpId,
        docName = docName,
        actor = actor,
        requestBasic = requestBasic
      )
    } yield ()
  }

  def checkSeenDocMappingGuideMessageState(actor: UserId): Task[Boolean] = {
    fundSubUserTrackingService
      .getUserTracking(actor)
      .map(_.seenDocMappingGuideMessage)
      .orElse(ZIO.attempt(true))
  }

  def markAsSeenDocMappingGuideMessage(actor: UserId): Task[Unit] = {
    fundSubUserTrackingService
      .updateUserTracking(actor, _.withSeenDocMappingGuideMessage(true))
      .unit
      .catchAllCause { err =>
        ZIO.logErrorCause(
          s"Failed to markAsSeenDocMappingGuideMessageState for ${actor.idString}: ",
          err
        )
      }
  }

  def getAllLpSubmittedSupportingDocsUnsafe(
    lpId: FundSubLpId
  ): Task[Seq[FileId]] = {
    for {
      docRequestId <- getDocRequestId(lpId)
      docSubmission <- docSubmissionService.get(DocSubmissionId(docRequestId), None)
      formSubmissions <- formSubmissionService.getByDocRequestId(docRequestId, None)
      submittedFiles = docSubmission.submittedFiles ++ formSubmissions.flatMap { form =>
        form.allFiles
      }
    } yield submittedFiles
  }

  private def updateDocGroupsIfNecessary(
    lpId: FundSubLpId,
    fileGroups: Seq[RequiredSupportingDocGroupInfo]
  ) = {
    for {
      updated <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        for {
          currentGroups <- ops.getFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId)).map(_.requiredDocGroupInfo)
          shouldUpdate = currentGroups != fileGroups
          _ <- RecordIO.when(shouldUpdate) {
            ops.updateFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId))(lpRestrictedModel =>
              lpRestrictedModel.withRequiredDocGroupInfo(fileGroups)
            )
          }
        } yield shouldUpdate
      }
      _ <- ZIOUtils.when(updated) {
        FundSubSgwModelUtils.modifyLastUpdateFundSubLpRestrictedModel(lpId)(natsNotificationService).map(_ => ())
      }
    } yield ()
  }

  def updateFormRequiredDocs(
    lpId: FundSubLpId,
    requiredFiles: FundSubFormIntegrationService.RequiredFiles
  ): Task[Unit] = {
    val task = for {
      docRequestIdOpt <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops
          .getFundSubLpModel(lpId)
          .map(_.docRequestIdOpt)
      }
      _ <- docRequestIdOpt.fold[Task[Unit]] { ZIO.unit } { docRequestId =>
        val docGroups = requiredFiles.fileGroups
        val requiredDocs = requiredFiles.files
        for {
          currentRequest <- docRequestService.getDocRequest(docRequestId, actorOpt = None)
          updatedRequest <- docRequestService.updateDocRequestItemsList(
            id = docRequestId,
            updateFnc = items => {
              val toRemoves = items
                .filter { item =>
                  val itemName = item.name.trim
                  val isNotInNewRequiredDocsList = !requiredDocs.exists(_._1.trim.equalsIgnoreCase(itemName))
                  item.creatorOpt.isEmpty && isNotInNewRequiredDocsList
                }
                .map { item =>
                  DocRequestItemBasicInfo(
                    name = item.name,
                    instruction = item.instruction
                  )
                }
              val toAdds = requiredDocs
                .filterNot { case (name, _) =>
                  items.exists(_.name.trim.equalsIgnoreCase(name.trim))
                }
                .map { case (name, description) =>
                  DocRequestItemBasicInfo(
                    name = name,
                    instruction = description
                  )
                }
              val toUpdates = requiredDocs
                .filter { case (name, instruction) =>
                  items.exists(item =>
                    item.name.trim.equalsIgnoreCase(name.trim) && !item.instruction.equalsIgnoreCase(instruction)
                  )
                }
                .map { case (name, instruction) =>
                  DocRequestItemBasicInfo(
                    name = name,
                    instruction = instruction
                  )
                }
              UpdateDocRequestItemsParams(
                itemsToAdd = toAdds,
                itemsToRemove = toRemoves,
                itemsToUpdate = toUpdates
              )
            },
            actorOpt = None
          )
          _ <- ZIOUtils.when(currentRequest.items != updatedRequest.items) {
            updateDocRequestStatus(
              lpId,
              docRequestId,
              newFileIds = List.empty
            )
          }
          _ <- updateDocGroupsIfNecessary(lpId, docGroups)
        } yield ()
      }
    } yield ()

    task.orElse(ZIO.logError(s"Failed to update form required docs list for ${lpId.idString}"))
  }

  private def makeLpDocument(
    fileId: FileId,
    fileInfoOpt: Option[DatalakeFileInfo],
    docType: FundSubDocType,
    lpId: FundSubLpId
  ) = {
    LpDocument(
      id = "",
      file = Option(
        DatalakeFileInfo(
          id = fileId,
          name = fileInfoOpt.map(_.name).getOrElse(""),
          uploader = fileInfoOpt
            .flatMap(_.uploader)
            .map { uploader =>
              DatalakeUserBasicInfo(
                id = uploader.id,
                email = uploader.email,
                firstName = uploader.firstName,
                lastName = uploader.lastName
              )
            },
          uploadedAt = fileInfoOpt.flatMap(_.uploadedAt)
        )
      ),
      docType = docType,
      lpId = lpId
    )
  }

  private def makeNamedDocItems(
    docRequest: DocRequest,
    docSubmission: DocSubmission,
    formSubmissions: List[FormSubmission],
    fileInfoMap: Map[FileId, DatalakeFileInfo],
    submittedDocTypes: Set[String],
    lpId: FundSubLpId
  ) = {
    val requiredDocs = docRequest.items.map(_.name)
    val items = docRequest.items ++ submittedDocTypes.toList
      .filterNot { doctype =>
        requiredDocs.exists(_.trim.equalsIgnoreCase(doctype))
      }
      .map { doctype =>
        DocRequestItem(
          name = doctype,
          instruction = "",
          createdAt = None,
          creatorOpt = None,
          markedAsNa = false,
          markedAsNaAt = None,
          markedAsNaByOpt = None
        )
      }
    items.map { item =>
      val submittedFiles =
        docSubmission.fileDocTypeMapping.filter(_._2.trim.equalsIgnoreCase(item.name.trim)).keys.toList
      val submittedFormIds =
        docSubmission.formDocTypeMapping.filter(_._2.trim.equalsIgnoreCase(item.name.trim)).keys.toList
      val submittedForms = formSubmissions.filter { form =>
        submittedFormIds.contains(form.id) && form.submittedFiles.nonEmpty
      }
      val submittedFundSubFiles = (submittedFiles ++ submittedForms.flatMap(_.submittedFiles)).map { fileId =>
        makeLpDocument(
          fileId,
          fileInfoMap.get(fileId),
          FundSubDocType.SupportingForm,
          lpId
        )
      }

      val submittedCertificates = submittedForms.flatMap(_.submittedCertificates).map { fileId =>
        makeLpDocument(
          fileId,
          fileInfoMap.get(fileId),
          FundSubDocType.SigningCertificate,
          lpId
        )
      }

      RequiredDocInfo(
        id = "",
        name = item.name.trim,
        markedAsNa = item.markedAsNa,
        submitted = submittedDocTypes.exists(_.equalsIgnoreCase(item.name)),
        submittedDocs = submittedFundSubFiles ++ submittedCertificates
      )
    }
  }

  private def getUnmappedDocs(
    docSubmission: DocSubmission,
    formSubmissions: List[FormSubmission],
    fileInfoMap: Map[FileId, DatalakeFileInfo],
    namedDocType: List[String],
    lpId: FundSubLpId
  ) = {
    val nonRequiredFileIds = docSubmission.submittedFiles.filter { fileId =>
      val mappedDocType = docSubmission.fileDocTypeMapping.getOrElse(fileId, "")
      mappedDocType.trim.isEmpty || !namedDocType.exists(_.trim.equalsIgnoreCase(mappedDocType.trim))
    }

    val nonRequiredDocs = nonRequiredFileIds.map { fileId =>
      makeLpDocument(
        fileId,
        fileInfoMap.get(fileId),
        FundSubDocType.SupportingForm,
        lpId
      )
    }
    val nonRequiredFormIds = docSubmission.formSubmissions.filter { formId =>
      val mappedDocType = docSubmission.formDocTypeMapping.getOrElse(formId, "")
      mappedDocType.trim.isEmpty || !namedDocType.exists(_.trim.equalsIgnoreCase(mappedDocType.trim))
    }
    val nonRequiredFormFiles = nonRequiredFormIds
      .flatMap { formId =>
        formSubmissions.find(_.id == formId)
      }
      .flatMap { form =>
        val signedFiles = form.submittedFiles.map { fileId =>
          makeLpDocument(
            fileId,
            fileInfoMap.get(fileId),
            FundSubDocType.SigningCertificate,
            lpId
          )
        }
        val certificates = form.submittedCertificates.map { fileId =>
          makeLpDocument(
            fileId,
            fileInfoMap.get(fileId),
            FundSubDocType.SigningCertificate,
            lpId
          )
        }
        signedFiles ++ certificates
      }

    nonRequiredDocs ++ nonRequiredFormFiles
  }

  private def getSignatureRequestFiles(
    lpId: FundSubLpId,
    fileInfoMap: Map[FileId, DatalakeFileInfo]
  )(
    using FDBCluster
  ): Task[List[LpDocument]] = {
    for {
      requestIds <- FDBCommonDatabase().read(FundSubLpModelStoreOperations.Production) { ops =>
        for {
          requestIds <- ops
            .getFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId))
            .map(_.additionalSignatureRequestIds)
        } yield requestIds
      }
      requests <- ZIO.foreach(requestIds) { requestId =>
        fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(requestId)
      }
      completedRequests = requests.filter(_.doneSigning)
      signedFilesAndCertificates =
        for {
          request <- completedRequests
          signedPackage <- request.getAdditionalRequestedDocPackage(lpId).toSeq
          file <- signedPackage.files
        } yield file.signedFileId.toSeq.map { fileId =>
          makeLpDocument(fileId, fileInfoMap.get(fileId), FundSubDocType.LpSignedDoc, lpId)
        } ++ file.certificateOpt.toSeq.map { fileId =>
          makeLpDocument(
            fileId,
            fileInfoMap.get(fileId),
            FundSubDocType.SigningCertificate,
            lpId
          )
        }
    } yield signedFilesAndCertificates.flatten.toList
  }

  def getSupportingDocsSummary(
    lpId: FundSubLpId,
    docRequestId: DocRequestId,
    newFiles: List[FileId] = List.empty
  )(
    using FDBCluster
  ): Task[SupportingDocSummary] = {
    for {
      docRequest <- docRequestService.getDocRequest(docRequestId, None)
      docSubmission <- docSubmissionService.get(DocSubmissionId(docRequestId), None)
      formSubmissions <- formSubmissionService.getByDocRequestId(docRequestId, None)
      submittedFormIds = formSubmissions.filter(_.submittedFiles.nonEmpty).map(_.id)

      submittedDocTypes = (docSubmission.fileDocTypeMapping.values ++ docSubmission.formDocTypeMapping.filter {
        case (key, _) =>
          submittedFormIds.contains(key)
      }.values).map(_.toLowerCase.trim).toSet
      fileInfoMap <- FundSubDataLakeUtils
        .getFileInfoList(
          newFiles,
          fileService,
          userProfileService
        )
        .map(
          _.map { fileInfo =>
            fileInfo.id -> fileInfo
          }.toMap
        )
      _ = docRequest.items.map(_.name).map(_.toLowerCase.trim)
      namedDocs = makeNamedDocItems(
        docRequest = docRequest,
        docSubmission = docSubmission,
        formSubmissions = formSubmissions.toList,
        fileInfoMap = fileInfoMap,
        submittedDocTypes = submittedDocTypes,
        lpId = lpId
      )
      unmappedDocs = getUnmappedDocs(
        docSubmission = docSubmission,
        formSubmissions = formSubmissions.toList,
        fileInfoMap = fileInfoMap,
        namedDocType = namedDocs.map(_.name).toList,
        lpId = lpId
      )
      additionalSignatureRequestFiles <- getSignatureRequestFiles(
        lpId,
        fileInfoMap
      )
    } yield SupportingDocSummary(
      namedDocs = namedDocs.toList,
      otherDocs = additionalSignatureRequestFiles ++ unmappedDocs
    )
  }

  def getAllLpSupportingDocs(
    lpId: FundSubLpId,
    docRequestId: DocRequestId,
    includingAdditionalSignatureRequestFiles: Boolean
  )(
    using FDBCluster
  ): Task[List[FundSubFile]] = {
    for {
      docSubmission <- docSubmissionService.get(DocSubmissionId(docRequestId), actorOpt = None)
      submittedFiles = docSubmission.submittedFiles.map(FundSubFile(_, FundSubDocType.SupportingForm))
      formSubmissionIds = docSubmission.formSubmissions
      formSubmissions <- ZIOUtils
        .foreachParN(parallelism)(formSubmissionIds) { formSubmissionId =>
          val task = formSubmissionService
            .getFormSubmission(formSubmissionId, actorOpt = None)
            .map[Seq[FundSubFile]] { formSubmission =>
              formSubmission.submittedFiles.map(
                FundSubFile(_, FundSubDocType.SupportingForm)
              ) ++ formSubmission.submittedCertificates.map(FundSubFile(_, FundSubDocType.SigningCertificate))
            }
          task.map(_.toList)
        }
        .map(_.flatten.toList)
      additionalSignatureRequestFiles <-
        if (includingAdditionalSignatureRequestFiles) {
          getSignatureRequestFiles(
            lpId,
            Map.empty
          ).map(_.flatMap { doc =>
            doc.file.map { file =>
              file -> doc.docType
            }
          }.map { case (file, docType) =>
            FundSubFile(
              fileId = file.id,
              docType = docType
            )
          })
        } else {
          ZIO.succeed(List.empty[FundSubFile])
        }
    } yield submittedFiles.toList ++ formSubmissions ++ additionalSignatureRequestFiles
  }

  private def checkFormCompletionStatus(formSubmissionId: FormSubmissionId, actor: UserId): Task[FormCompletionStatus] = {
    val task = for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is checking form completion status of form submission $formSubmissionId")
      lpId <- getLpIdFromFormSubmissionId(formSubmissionId)
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      formSubmission <- formSubmissionService.getFormSubmission(formSubmissionId, Option(actor))
      form <- formService.getForm(
        formSubmission.formDataId.parent.parent,
        Option(formSubmission.formDataId.parent),
        actor,
        shouldCheckPermission = false
      )
      gaiaStateOpt <- FDBRecordDatabase
        .transact(LpFormDataOperations.Production)(_.getOpt(formSubmission.formDataId))
        .map(_.map(_.gaiaState))
      formCompleted = gaiaStateOpt.fold[Boolean] {
        false
      } { gaiaState =>
        FormValidationUtils
          .findAllErrorFields(
            form.formData.form,
            gaiaState
          )
          .forall(_._2.isEmpty)
      }
      hasSignatureField = gaiaStateOpt.exists { gaiaState =>
        FormSignatureUtils
          .getAllSignatureMappingInfo(
            form.formData.form,
            gaiaState.getNamespaceStateMap(form.formData.form.defaultNamespace)
          )
          .nonEmpty
      }
    } yield FormCompletionStatus(formCompleted, hasSignatureField)

    task.orElse(
      ZIO
        .logError(s"Failed to calculate form completion status on $formSubmissionId")
        .as(FormCompletionStatus(false, false))
    )
  }

  def checkMultipleFormCompletionStatus(
    formSubmissionIds: List[FormSubmissionId],
    actor: UserId
  ): Task[Map[FormSubmissionId, FormCompletionStatus]] = {
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is checking form completion status of multiple form submissions $formSubmissionIds"
      )
      res <- ZIO
        .foreach(formSubmissionIds) { formId =>
          checkFormCompletionStatus(formId, actor).map { status =>
            formId -> status
          }
        }
        .map(_.toMap)
    } yield res
  }

  def updatePreventInvestorUploadSupportingDocAfterCountersigned(
    params: UpdatePreventInvestorUploadSupportingDocAfterCountersignedParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = params.fundSubId,
        userId = actor,
        permission = FundPermission.ManageFundSetting,
        obj = Type.FundSub(params.fundSubId)
      )
      _ <- ZIO.logInfo(
        s"$actor update prevent investor upload supporting doc after countersigned to ${params.shouldPrevent}"
      )
      _ <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
        _.updateFundSubPublicModel(params.fundSubId) { model =>
          model.copy(featureSwitch =
            model.featureSwitch.map(_.copy(preventLpUploadSupportingDocAfterCountersigned = params.shouldPrevent))
          )
        }
      }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(params.fundSubId)(natsNotificationService)
    } yield ()
  }

  def markSubmittedFilesAsFundShared(
    lpId: FundSubLpId,
    fileIds: Seq[FileId],
    actorOpt: Option[UserId]
  ): Task[DocSubmission] = {
    for {
      docRequestId <- getDocRequestId(lpId)
      docSubmissionId = DocSubmissionId(docRequestId)
      docSubmission <- docSubmissionService.markFilesAsFundShared(docSubmissionId, fileIds, actorOpt)
    } yield docSubmission
  }

  private def removeFormSubmission(formId: FormSubmissionId, lpId: FundSubLpId, actor: UserId) = {
    for {
      formSubmission <- formSubmissionService.getFormSubmission(formId, actorOpt = None)
      _ <- ZIOUtils.failWhen(formSubmission.pendingSignatureRequestIdOpt.nonEmpty) {
        GeneralServiceException(
          s"Cannot remove form submission ${formId.idString} because it has pending signature request"
        )
      }
      _ <- formSubmissionService.remove(formId, actorOpt = None)
      // Remove flag so save to profile can be trigger again
      _ <- removeSaveSupportingFormToProfileFlag(lpId, formId)
      _ <- triggerAutoSaveProfileFromSubscription(actor, lpId)
    } yield ()
  }

  private def updateDocRequestStatus(
    lpId: FundSubLpId,
    docRequestId: DocRequestId,
    newFileIds: List[FileId]
  ) = {
    for {
      requiredDocsSummary <- getSupportingDocsSummary(
        lpId,
        docRequestId,
        newFileIds
      )
      providedDocs <- docRequestService.getDocRequest(docRequestId, None).map(_.providedItems)
      missingRequiredDocs = requiredDocsSummary.namedDocs
        .filterNot { doc =>
          doc.markedAsNa || doc.submitted ||
          providedDocs.exists(_.equalsIgnoreCase(doc.name))
        }
        .map(_.name)
        .toSet
      _ <- dashboardService.updateLpInfoRecord(
        lpId,
        _.copy(hasPendingRequiredSupportingDoc = missingRequiredDocs.nonEmpty)
      )
      params = UpdateRequiredDocsParams(
        lpIdOpt = Option(lpId),
        docs = requiredDocsSummary.createSupportingDocItems
      )
      _ <- FundSubDataLakeUtils.sendUpdateParams(
        lpId.parent,
        params,
        fundSubDataLakeIngestionService
      )
      _ <- FundSubGreylinDataService.updateSupportingDocStatus(lpId)
    } yield ()
  }

  private def updateDocRequestStatus(
    lpId: FundSubLpId,
    newFileIds: List[FileId]
  ): Task[Unit] = {
    for {
      docRequestIdOpt <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops
          .getFundSubLpModel(lpId)
          .map(_.docRequestIdOpt)
      }
      _ <- ZIOUtils.traverseOptionUnit(docRequestIdOpt) { docRequestId =>
        updateDocRequestStatus(
          lpId,
          docRequestId,
          newFileIds
        )
      }
    } yield ()
  }

  private def getDocRequestId(lpId: FundSubLpId) = {
    for {
      idOpt <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops
          .getFundSubLpModel(lpId)
          .map(_.docRequestIdOpt)
      }
      id <- ZIOUtils.optionToTask(idOpt, GeneralServiceException(s"Not found doc request id for ${lpId.idString}"))
    } yield id
  }

  private def getLpIdFromFormSubmissionId(formSubmissionId: FormSubmissionId) = {
    val lpIdOpt = formSubmissionId.parent.parent match {
      case lpId: FundSubLpId => Option(lpId)
      case _                 => None
    }
    for {
      lpId <- ZIOUtils.optionToTask(
        lpIdOpt,
        GeneralServiceException(s"Cannot get lpId from form submission id ${formSubmissionId.idString}")
      )
    } yield lpId
  }

  private def getFormName(formId: FormId) = {
    formService
      .getFormNames(Seq(formId))
      .map(_.headOption)
      .map(_.map(_._2).getOrElse(""))
  }

  private def validateMarkAsNotApplicablePermission(lpId: FundSubLpId, actor: UserId) = {
    for {
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService
        .validateFundManagerCanMarkSupportingDocAsNotApplicableR(lpId, actor)
        .orElse(
          for {
            _ <- fundSubPermissionService.validateFeatureSwitch(
              lpId.parent,
              !_.exists(_.disabledMarkAsNotApplicable)
            )
            _ <- fundSubPermissionService.validateFundActive(lpId.parent)
            _ <- fundSubPermissionService.validateUserCanServeOnInvestorSideR(lpId, actor)
          } yield ()
        )
      _ <- validateShouldUpdateSupportingDoc(lpId, actor)
    } yield ()
  }

  private def getPrefilledValues(lpId: FundSubLpId) = {
    for {
      lpFormIdOpt <- FDBRecordDatabase.transact(LPDataOperations.Production)(
        _.getLastFormIdOpt(lpId).map(_.map(_.asNewLpFormIdUnsafe()))
      )
      prefilledValues <- lpFormIdOpt.fold[Task[Map[String, Json]]] {
        ZIO.succeed(Map.empty)
      } { lpFormId =>
        for {
          lpDataOpt <- FDBRecordDatabase.transact(LpFormDataOperations.Production)(_.getOpt(lpFormId))
        } yield {
          lpDataOpt.map(_.stringKeyValues).getOrElse(Map.empty)
        }
      }
    } yield prefilledValues
  }

  private def getAllMappedFilesOfDocType(lpId: FundSubLpId, docType: String, actorOpt: Option[UserId]) = {
    for {
      docRequestId <- getDocRequestId(lpId)
      docSubmission <- docSubmissionService.get(DocSubmissionId(docRequestId), actorOpt)
      mappedFiles = docSubmission.fileDocTypeMapping
        .filter(_._2.equalsIgnoreCase(docType.trim))
        .keys
        .toList

      mappedForms = docSubmission.formDocTypeMapping
        .filter(_._2.equalsIgnoreCase(docType.trim))
        .keys

      submittedFormFiles <- ZIO
        .foreach(mappedForms) { formSubmissionId =>
          formSubmissionService
            .getFormSubmission(formSubmissionId, actorOpt)
            .map(_.allFiles)
        }
        .map(_.flatten.toList)
    } yield mappedFiles ++ submittedFormFiles
  }

  private def getMappedDoctype(
    lpId: FundSubLpId,
    fileId: FileId,
    actorOpt: Option[UserId]
  ): Task[String] = {
    for {
      docRequestId <- getDocRequestId(lpId)
      docSubmission <- docSubmissionService.get(DocSubmissionId(docRequestId), actorOpt)
      docType = docSubmission.fileDocTypeMapping
        .getOrElse(fileId, "")
    } yield docType
  }

  private def validateShouldUpdateSupportingDoc(fundSubLpId: FundSubLpId, actor: UserId): Task[Unit] = {
    for {
      featureSwitch <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(fundSubLpId.parent).map(_.featureSwitch)
      }
      _ <- ZIOUtils.when(featureSwitch.exists(_.preventLpUploadSupportingDocAfterCountersigned)) {
        for {
          lpInfo <- FDBRecordDatabase.transact(LpInfoOperations.Production) { ops =>
            ops.get(fundSubLpId)
          }
          isFundManager <- fundSubPermissionService.checkIfUserHasFundManagerRole(fundSubLpId.parent, actor)
          _ <- ZIOUtils.validate(
            isFundManager || (lpInfo.status != LpStatus.LPCountersigned && lpInfo.status != LpStatus.LPCompleted)
          ) {
            GeneralServiceException(
              "Cannot make any change to supporting docs after countersigned or distributed document"
            )
          }
        } yield ()
      }
    } yield ()
  }

  def getAllFundSubSupportingDocs(
    fundId: FundSubId,
    actor: UserId
  ): Task[GetAllFundSubSupportingDocsResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is getting all supporting documents for $fundId")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundId)
      _ <- fundSubPermissionService.validateFundManagerCanAccessLpSupportingDocsInFundR(fundId, actor)
      allSupportingDocs <- SupportingDocUtils.getAllFundSupportingDocs(
        fundId = fundId,
        actor = actor,
        formService = formService,
        dynamicFormStorageService = dynamicFormStorageService
      )
    } yield GetAllFundSubSupportingDocsResponse(supportingDocs = allSupportingDocs)
  }

  def getFundSupportingDocGroupsFromForm(
    fundId: FundSubId,
    actor: UserId
  ): Task[GetFundSupportingDocGroupsFromFormResponse] = {
    for {
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundId)
      _ <- fundSubPermissionService.validateFundManagerCanAccessLpSupportingDocsInFundR(fundId, actor)
      docGroups <- SupportingDocUtils.getUnsanitizedSupportingDocGroupsFromForm(
        fundId = fundId,
        actor = actor,
        formService = formService,
        dynamicFormStorageService = dynamicFormStorageService
      )
    } yield {
      val sanitizedDocs = docGroups.flatMap(_.docTypes).map(_.trim).distinctBy(_.toLowerCase).sorted
      GetFundSupportingDocGroupsFromFormResponse(
        allSupportingDocs = sanitizedDocs,
        supportingDocGroups = SupportingDocReviewUtils.sanitizeDocGroups(
          allSupportingDocs = sanitizedDocs,
          docGroups = docGroups
        )
      )
    }
  }

  def exportFundSubProvidedDocStatus(
    fundSubId: FundSubId,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[ExportFundSubProvidedDocStatusResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is exporting lp provided doc status for fund ${fundSubId.idString}")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- fundSubPermissionService.validateFundManagerCanAccessLpSupportingDocsInFundR(fundSubId, actor)
      providedDocStatusInfos <- getFundSubProvidedDocStatusData(fundSubId, actor)
      investors = providedDocStatusInfos.investors
      allSupportingDocs <- SupportingDocUtils.getAllFundSupportingDocs(
        fundId = fundSubId,
        actor = actor,
        formService = formService,
        dynamicFormStorageService = dynamicFormStorageService
      )
      lpInfoColumns = List(
        FillSheetCell(CommonField.FirstName.label),
        FillSheetCell(CommonField.LastName.label),
        FillSheetCell(CommonField.Email.label),
        FillSheetCell(CommonField.FirmName.label),
        FillSheetCell("Investor ID")
      )
      headerCells = lpInfoColumns ++ allSupportingDocs
        .map(doc => FillSheetCell(doc.name))
        .toList
      instructionRow = FillSheetRow(List(FillSheetCell(ProvidedDocMapping.mappingInstruction)))
      headerRow = FillSheetRow(headerCells)
      investorIdColumnIndex = headerCells.indexOf(FillSheetCell("Investor ID"))
      dataRows = investors
        .map(investor =>
          FillSheetRow(
            List(
              FillSheetCell(investor.firstName),
              FillSheetCell(investor.lastName),
              FillSheetCell(investor.email),
              FillSheetCell(investor.firmName),
              FillSheetCell(investor.lpId.idString)
            ) ++ allSupportingDocs.map { doc =>
              if (investor.providedDocs.exists(_.trim.equalsIgnoreCase(doc.name.trim))) {
                FillSheetCell(ProvidedDocMapping.mappingKey)
              } else {
                FillSheetCell("")
              }
            }.toList
          )
        )
        .toList
      fileData = FillSheetData(
        startRow = 0,
        startCol = 0,
        rows = List(instructionRow, headerRow) ++ dataRows
      )
      result <- zio.ZIO
        .attempt {
          new XSSFWorkbook()
        }
        .bracket { workbook =>
          val sheet = workbook.createSheet("Provided Supporting Document Status")
          val styleForNotLockedRows = workbook.createCellStyle()
          styleForNotLockedRows.setLocked(false)
          val font = workbook.createFont()
          font.setBold(true)
          val styleForBoldRows = workbook.createCellStyle()
          styleForBoldRows.setFont(font)
          for {
            _ <- zio.ZIO.attempt {
              List.range(0, headerRow.cells.size).foreach(index => sheet.setColumnWidth(index, 20 * 256))
              FillSpreadsheet.fillSheet(sheet, fileData)
              sheet.getRow(1).forEach(cell => cell.setCellStyle(styleForBoldRows))
              List
                .range(1, dataRows.size)
                .foreach { index =>
                  sheet.getRow(index + 1).forEach { cell =>
                    if (cell.getColumnIndex != investorIdColumnIndex) {
                      cell.setCellStyle(styleForNotLockedRows)
                    }
                  }
                }
              sheet.protectSheet("")
              sheet.createFreezePane(0, 1)
              sheet.addMergedRegion(
                new CellRangeAddress(
                  0,
                  0,
                  0,
                  5
                )
              )
            }
            resultSource <- SpreadsheetUtils.workbookToStream(workbook)
          } yield resultSource
        } { workbook =>
          zio.ZIO.succeed {
            workbook.close()
          }
        }
      folderId <- fileService.createUserTemporaryFolderIfNeeded(actor)
      fileName = "lp_provided_doc_status_data.xlsx"
      fileId <- fileService.uploadFile(
        folderId,
        fileName,
        FileContentOrigin.FromSource(
          result,
          MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        ),
        actor
      )
      downloadUrlRes <- fileDownloadService.getBatchDownloadData(
        actor,
        BatchDownloadRequest(
          "",
          Seq(),
          Seq(fileId),
          DmsTrackingActivityType.Download
        ),
        httpContext
      )
    } yield ExportFundSubProvidedDocStatusResponse(downloadUrlRes.url)
  }

  def checkIsSupportingFormTaxForm(formSubmissionId: FormSubmissionId, actorId: UserId): Task[Boolean] = {
    for {
      taxFormVersionIds <- FundSubTaxFormConfig.getTaxFormConfig.map(_.formVersionIds)
      supportingFormVersionId <- formSubmissionService
        .getFormSubmission(formSubmissionId, Option(actorId))
        .map(_.formDataId.parent)
    } yield taxFormVersionIds.contains(supportingFormVersionId)
  }

  def submitForm(
    formSubmissionId: FormSubmissionId,
    ctx: Option[AuthenticatedRequestContext],
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor is submitting form $formSubmissionId")
      lpId <- getLpIdFromFormSubmissionId(formSubmissionId)
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      _ <- validateShouldUpdateSupportingDoc(lpId, actor)
      pdfTextColorOpt <- FundSubCommonUtils.getPDFTextColor(lpId.parent)
      pdfData <- formSubmissionService.generateFilledFiles(
        formSubmissionId,
        actor,
        ctx,
        pdfTextColorOpt
      )
      formSubmission <- formSubmissionService.submitFiles(
        id = formSubmissionId,
        files = pdfData.map(_.fileId),
        actor = actor,
        httpContextOpt = ctx,
        isFormGeneratedDocs = true
      )
      docSubmission <- docSubmissionService.get(DocSubmissionId(formSubmissionId.parent), Some(actor))
      _ <- afterSupportingDocsUploaded(
        lpId,
        actor,
        ctx,
        shouldAttemptToSubmitForReview = true,
        sharedFiles = formSubmission.allFiles,
        docType = docSubmission.formDocTypeMapping.getOrElse(formSubmissionId, "")
      )
    } yield ()
  }

  def getSupportingDocPendingSignatureData(lpId: FundSubLpId, actorId: UserId, shouldShowFormSupportingDoc: Boolean)
    : Task[SupportingDocPendingSignatureResp] =
    for {
      getDocRequestResp <- getDocRequest(lpId, actorId)
      getDocSubmissionResp <- getDocSubmission(lpId, actorId)
      applicableFormSubmissions = getDocSubmissionResp.formSubmissions.filter {
        formSubmission =>
          val submittedOrHasPendingRequest =
            formSubmission.submittedFiles.nonEmpty || formSubmission.pendingSignatureRequestIdOpt.nonEmpty
          val isUnMappedForm = !getDocSubmissionResp.formDocTypeMapping.contains(formSubmission.id)
          // This logic depends on Frontend's logic, which is bad, but there is no good/quick way to centralize the logic and
          // reuse it for both Frontend can Backend code
          val isShownOnUI = shouldShowFormSupportingDoc || isUnMappedForm
        !submittedOrHasPendingRequest && isShownOnUI
      }
      formVersionIdsInExistingSubmissions = applicableFormSubmissions.map(
        _.formDataId.parent
      )
      formVersionIdsForDocTypes = getDocRequestResp.docTypeWithRelatedForms.flatMap(_.forms.map(_.formId))
      allFormVersionIds = (formVersionIdsInExistingSubmissions ++ formVersionIdsForDocTypes).distinct
      getFormRespMap <- ZIO
        .foreach(allFormVersionIds) { formVersionId =>
          formService
            .getForm(
              formId = formVersionId.parent,
              versionIdOpt = Some(formVersionId),
              actor = actorId,
              shouldCheckPermission = false
            )
            .map(formVersionId -> _)
        }
        .map(_.toMap)
      inProgressFormSubmissionInfos <- ZIOUtils
        .foreachParN(4)(applicableFormSubmissions) { formSubmission =>
          val docTypeOpt = getDocSubmissionResp.formDocTypeMapping.get(formSubmission.id)
          for {
            gaiaStateOpt <- FDBRecordDatabase.transact(LpFormDataOperations.Production)(
              _.getOpt(formSubmission.formDataId).map(_.map(_.gaiaState))
            )
          } yield getFormSubmissionPendingSignaturesInfo(
            getFormRespOpt = getFormRespMap.get(formSubmission.formDataId.parent),
            gaiaStateOpt = gaiaStateOpt,
            docTypeOpt = docTypeOpt,
            formSubmissionId = formSubmission.id
          )
        }
        .map(_.flatten)
      supportingFormsNotStartedInfos = getDocTypesPendingSignatures(
        getDocRequestResp,
        getDocSubmissionResp,
        getFormRespMap
      )
    } yield SupportingDocPendingSignatureResp(
      inProgressFormSubmissionInfos = inProgressFormSubmissionInfos,
      supportingFormsNotStarted = supportingFormsNotStartedInfos
    )

  private def getFormSubmissionPendingSignaturesInfo(
    getFormRespOpt: Option[GetFormResponse],
    gaiaStateOpt: Option[GaiaState],
    docTypeOpt: Option[String],
    formSubmissionId: FormSubmissionId
  ) = {
    val (isFormCompleted, hasPreppedSignatures) = (for {
      gaiaState <- gaiaStateOpt
      getFormResp <- getFormRespOpt
    } yield {
      val isCompleted = FormValidationUtils.findErrorField(getFormResp.formData.form, gaiaState).isEmpty
      val hasPreppedSignatures = {
        if (isCompleted) {
          FormSignatureUtils
            .getAllSignatureMappingInfo(getFormResp.formData.form, gaiaState.defaultStateMap)
            .nonEmpty
        } else {
          FormSignatureUtils.getAllSignatureMappingInfo(getFormResp.formData.form).nonEmpty
        }
      }
      isCompleted -> hasPreppedSignatures
    }).unzip

    Option.when(hasPreppedSignatures.contains(true)) {
      SupportingFormPendingSignatureInfo(
        formName = getFormRespOpt.fold("Supporting form")(_.formModel.name),
        docTypeOpt = docTypeOpt,
        formSubmissionId = formSubmissionId,
        readyForSignature = isFormCompleted.contains(true)
      )
    }
  }

  private def getDocTypesPendingSignatures(
    getDocRequestResp: GetDocRequestResponse,
    docSubmission: GetDocSubmissionResponse,
    getFormRespMap: Map[FormVersionId, GetFormResponse]
  ): Seq[String] = {
    val formsHasDigitizedSignatures: Set[FormVersionId] =
      getDocRequestResp.docTypeWithRelatedForms
        .flatMap(_.forms.map(_.formId))
        .distinct
        .filter { formVersionId =>
          getFormRespMap.get(formVersionId).exists { getFormResp =>
            FormSignatureUtils
              .getAllSignatureMappingInfo(
                getFormResp.formData.form
              )
              .nonEmpty
          }
        }
        .toSet
    val docTypesAllowSignatures: Set[String] = getDocRequestResp.docTypeWithRelatedForms.flatMap { docTypeAndForms =>
      Option.when(docTypeAndForms.forms.exists(formInfo => formsHasDigitizedSignatures.contains(formInfo.formId))) {
        docTypeAndForms.name
      }
    }.toSet
    val docTypesExistFormSubmissions = docSubmission.formDocTypeMapping.values.toSet
    val docTypesExistUploadedDoc = docSubmission.fileDocTypeMapping.values.toSet
    val providedDocTypes = getDocRequestResp.docRequest.providedItems.toSet
    for {
      docRequestItem <- getDocRequestResp.docRequest.items
      hasDigitizedSignatures = docTypesAllowSignatures.contains(docRequestItem.name)
      isProvided = providedDocTypes.contains(docRequestItem.name)
      alreadyHasFormSubmission = docTypesExistFormSubmissions.contains(docRequestItem.name)
      isApplicable = !docRequestItem.markedAsNa
      alreadyHasUploadedDoc = docTypesExistUploadedDoc.contains(docRequestItem.name)
      if !isProvided && isApplicable && !alreadyHasFormSubmission && !alreadyHasUploadedDoc && hasDigitizedSignatures
    } yield docRequestItem.name
  }

}

object NewSupportingDocService {

  final case class SupportingDocSummary(
    namedDocs: List[RequiredDocInfo],
    otherDocs: List[LpDocument]
  ) {

    // put other docs into an RequiredDocInfo item to be synced in dgraph
    def createSupportingDocItems: List[RequiredDocInfo] = {
      val otherDocsItem = RequiredDocInfo(
        id = "",
        name = DocumentRequestCell.otherDocumentName,
        markedAsNa = true,
        submitted = otherDocs.nonEmpty,
        submittedDocs = otherDocs
      )
      namedDocs :+ otherDocsItem
    }

  }

  case class SupportingFormPendingSignatureInfo(
    formName: String,
    docTypeOpt: Option[String],
    formSubmissionId: FormSubmissionId,
    readyForSignature: Boolean
  )

  case class SupportingDocPendingSignatureResp(
    inProgressFormSubmissionInfos: Seq[SupportingFormPendingSignatureInfo],
    supportingFormsNotStarted: Seq[String]
  )

  object SupportingDocPendingSignatureResp {

    val Empty: SupportingDocPendingSignatureResp = SupportingDocPendingSignatureResp(
      inProgressFormSubmissionInfos = Seq.empty,
      supportingFormsNotStarted = Seq.empty
    )

  }

}
