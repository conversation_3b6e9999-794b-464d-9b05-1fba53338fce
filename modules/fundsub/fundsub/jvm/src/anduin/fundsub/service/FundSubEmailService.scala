// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.service

import java.time.{DayOfWeek, Instant, ZonedDateTime}

import zio.{Task, UIO, ZIO}

import anduin.account.profile.UserProfileService
import anduin.actiontoken.{TokenMetaDataUtils, TokenSubject}
import anduin.customdomain.CustomDomainService
import anduin.dms.service.FileService
import anduin.fdb.record.{DefaultCluster, FDBCluster, FDBRecordDatabase}
import anduin.fundsub.FundSubLoggingService
import anduin.fundsub.auditlog.FundSubAuditLogService.{AddEventEmailParam, AddEventParam}
import anduin.fundsub.auditlog.{AuditLogActorType, AuditLogEventType, FundSubAuditLogService}
import anduin.fundsub.copy.FundSubCopyConfigService
import anduin.fundsub.datalakeingestion.FundSubDataLakeIngestionService
import anduin.fundsub.datalakeingestion.model.{UpdateOrderBasicInfoParams, UserBasicInfo}
import anduin.fundsub.email.digest.log.{DigestEmailLogStoreOperations, DigestEmailLogStoreProvider}
import anduin.fundsub.email.digest.orderactivity.OrderActivityUtils
import anduin.fundsub.email.generate
import anduin.fundsub.email.generate.*
import anduin.fundsub.email.generate.dataextract.DataExtractRequestsReadyForReviewForGpEmailGenerate
import anduin.fundsub.email.generate.demo.{FundSubSimulatorVerificationEmailGenerate, WarningEmailGenerate}
import anduin.fundsub.email.generate.documentreview.*
import anduin.fundsub.email.generate.documentreview.AdditionalDocumentReadyForReviewReportEmailGenerate.NewAdditionalDocReadyForReviewByLp
import anduin.fundsub.email.generate.report.NewLpReportEmailGenerate
import anduin.fundsub.email.generate.report.NewLpReportEmailGenerate.NewLpReportParams
import anduin.fundsub.email.generate.ria.{
  AdvisorInvitationEmailGenerate,
  RevokeAdvisorInvitationEmailGenerate,
  RiaEntityLinkedEmailGenerate
}
import anduin.fundsub.email.generate.sideletter.*
import anduin.fundsub.email.generate.signature.*
import anduin.fundsub.email.generate.signature.FundSubDoneBatchSigningEmailGenerate.{CountersignedLpParam, RecipientType}
import anduin.fundsub.email.generate.supportingdoc.{
  AmlKycDocumentReviewAssignmentEmailGenerate,
  AmlKycDocumentReviewUnassignmentEmailGenerate,
  DocumentHasNoReviewerGenerate
}
import anduin.fundsub.endpoint.subscriptiondoc.review.FundSubSubscriptionDocReviewType
import anduin.fundsub.group.FundSubGroupMemberService
import anduin.fundsub.investorgroup.FundSubInvestorGroupService
import anduin.fundsub.models.signature.FundSubSignatureRequestModels
import anduin.fundsub.models.signature.FundSubSignatureRequestModels.{
  FundSubSignatureEnvelopeType,
  FundSubSignatureRequestBasic
}
import anduin.fundsub.models.{FundSubLpModelStoreOperations, FundSubModelStoreOperations, FundSubSgwModelUtils}
import anduin.fundsub.service.FundSubPermissionService.FundSubRole
import anduin.fundsub.utils.FundSubDataLakeUtils
import anduin.id.ModelIdRegistry
import anduin.id.entity.EntityId
import anduin.id.fundsub.*
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.id.offering.OfferingId
import anduin.id.signature.SignatureRequestId
import anduin.kafka.{KafkaFiber, KafkaService, KafkaSimpleConsumer}
import anduin.link.LinkGeneratorService
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.model.id.email.sending.EmailSystemSpaceId
import anduin.model.id.{FileId, GlobalOfferingIdFactory, TeamId}
import anduin.portaluser.PortalUserService
import anduin.protobuf.actionlogger.event.FundSubEmailType
import anduin.protobuf.activitylog.GeneralActivity
import anduin.protobuf.fundsub.models.lp.FundSubLpModel
import anduin.protobuf.fundsub.notification.FundAdminNotificationPreference
import anduin.protobuf.fundsub.{EmailTemplateMessage, FundSubEvent}
import anduin.protobuf.kafka.ReceivedFromMailgun
import anduin.rebac.RebacStoreOperation
import anduin.service.entity.whitelabel.EntityWhiteLabelService
import anduin.service.{GeneralServiceException, RequestContext}
import anduin.stargazer.service.formcomment.FormCommentCommons.{
  NewCommentAssignmentEmailDataForFundAdmin,
  NewFormCommentEmailDataForFundAdmin,
  NewFormCommentEmailDataForLp
}
import anduin.storageservice.s3.S3Service
import anduin.user.UserService
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.model.services.jwt.ActionTokenClaim
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.api.FileDownloadService
import com.anduin.stargazer.service.email.*
import com.anduin.stargazer.service.email.generate.{GenerateEmail, GenerateSingleEmail}
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.util.date.DateCalculator
import stargazer.model.routing.DynamicAuthPage.{FundSubAdminPage, FundSubLpPage, FundSubPage}

final case class FundSubEmailService(
  fundSubLoggingService: FundSubLoggingService,
  fundSubLpActivityLogService: FundSubLpActivityLogService,
  emailSenderService: EmailSenderService,
  fundSubPermissionUtils: FundSubPermissionService,
  fundSubDataLakeIngestionService: FundSubDataLakeIngestionService,
  fundSubAuditLogService: FundSubAuditLogService,
  kafkaService: KafkaService,
  fundSubInvestorGroupService: FundSubInvestorGroupService,
  userService: UserService,
  fundSubCopyConfigService: FundSubCopyConfigService,
  natsNotificationService: NatsNotificationService
)(
  using val userProfileService: UserProfileService,
  val customDomainService: CustomDomainService,
  val s3Service: S3Service,
  val fundSubEmailUtils: FundSubEmailUtils,
  val linkGeneratorService: LinkGeneratorService,
  val fundSubWhiteLabelService: FundSubWhiteLabelService,
  val entityWhiteLabelService: EntityWhiteLabelService,
  val portalUserService: PortalUserService,
  val fileService: FileService,
  val fileDownloadService: FileDownloadService,
  val fundSubPermissionService: FundSubPermissionService,
  val backendConfig: GondorBackendConfig
) extends KafkaFiber {

  private val trackEmailEventConsumer = KafkaSimpleConsumer[String, ReceivedFromMailgun](
    kafkaService = kafkaService,
    topic = EmailKafkaTopics.emailEventTopic,
    consumerGroupName = backendConfig.fundSubEmailConfig.consumerGroup,
    handler = (_, emailEvent) => {
      val task = for {
        _ <- ZIOUtils.when[Any, Throwable, Unit](
          emailEvent.content
            .contains(OutgoingEmailVariable.FundSubLpIdVariable.value) &&
            emailEvent.content.contains(OutgoingEmailVariable.FundSubEmailTypeVariable.value)
        ) {
          if (emailEvent.webhookType.contains(WebhookType.Bounce.value)) {
            executedBounceTask(emailEvent)
          } else if (emailEvent.webhookType.contains(WebhookType.Open.value)) {
            executeEmailOpenTask(emailEvent)
          } else {
            ZIO.unit
          }
        }
      } yield ()
      task
    }
  )

  private def getFundAdminsWithPermissionToInvestor(
    fundSubLpId: FundSubLpId,
    fundAdmins: Seq[UserId]
  )(
    using FDBCluster
  ): Task[Seq[UserId]] = {
    val fundId = fundSubLpId.parent
    for {
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundId)
      authorizedFundManagersOfInvestor <- FundSubPermissionUtils.getAuthorizedFundManagersOfInvestorR(fundSubLpId)
    } yield fundAdmins.filter(authorizedFundManagersOfInvestor.contains)
  }

  def sendLpEventEmail(
    actor: UserId,
    fundSubLpId: FundSubLpId,
    fundSubEvent: FundSubEvent,
    message: String = ""
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = fundSubLpId.parent
    for {
      fundSubPublicModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(fundSubId)
      }
      fundSubLpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.getFundSubLpModel(fundSubLpId)
      }
      joinedAdmins <- fundSubPermissionService.getJoinedAdmins(fundSubId)
      adminsNotToNotifyWhenFilledForm <-
        ZIO
          .foreach(joinedAdmins) { admin =>
            for {
              preference <- FundAdminNotificationUtils.getFundNotificationPreference(admin, fundSubId)
            } yield {
              Option.unless(preference.notifyOnInvestorFilledFormEvent)(admin)
            }
          }
          .map(_.flatten)
      receivers <- fundSubEvent match {
        case FundSubEvent.lpSubmittedConfirmation =>
          // Ignore collaborator who has fund admin role
          ZIO.attempt((fundSubLpModel.collaborators.diff(joinedAdmins) :+ fundSubLpModel.mainLp).filter(_ != actor))
        case FundSubEvent.lpFilledForm =>
          val joinedAdminsToNotifyWhenFilledForm = joinedAdmins.filterNot(adminsNotToNotifyWhenFilledForm.contains(_))
          getFundAdminsWithPermissionToInvestor(fundSubLpId, joinedAdminsToNotifyWhenFilledForm)
        case _ => ZIO.attempt(Seq.empty)
      }
      redirectedPage = fundSubEvent match {
        case FundSubEvent.lpFilledForm =>
          FundSubAdminPage(fundSubPublicModel.investorEntity, fundSubId)
        case _ => FundSubLpPage(fundSubLpId)
      }
      emailIds <- sendLpEventEmailInternal(
        receivers,
        fundSubEvent,
        fundSubLpModel.mainLp,
        Some(actor),
        fundSubLpId,
        redirectedPage,
        message
      )
    } yield emailIds
  }

  def sendFormCommentNotifEmailForFundAdmin(
    fundSubId: FundSubId,
    emailData: Seq[NewFormCommentEmailDataForFundAdmin]
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    val allLpIds = emailData.flatMap(_.inEmailComments.map(_.fundSubLpId)).distinct
    for {
      authorizedFundManagersOfLps <- fundSubInvestorGroupService.getAuthorizedFundManagers(allLpIds)
      flowTermCollection <- fundSubCopyConfigService.getFlowTermCollection(fundSubId)
      emails <- ZIO
        .foreach(emailData) { emailDataForEachAdmin =>
          val commentsFilteredByPermission = emailDataForEachAdmin.inEmailComments.filter { inEmailComment =>
            authorizedFundManagersOfLps
              .getOrElse(inEmailComment.fundSubLpId, Set.empty)
              .contains(emailDataForEachAdmin.receiverId)
          }

          if (commentsFilteredByPermission.nonEmpty) {
            sendFundSubEmail(
              fundSubId,
              FormCommentNotifEmailForFundAdminGenerate(
                emailDataForEachAdmin.copy(inEmailComments = commentsFilteredByPermission),
                flowTermCollection = flowTermCollection
              )(fundSubEmailUtils)
            ).map(_.internalId)
          } else {
            ZIO.attempt(Seq.empty[EmailSenderService.EmailWithInternalId])
          }
        }
        .map(_.flatten)
    } yield emails
  }

  def sendCommentAssignmentNotifEmailForFundAdmin(
    fundSubId: FundSubId,
    emailData: Seq[NewCommentAssignmentEmailDataForFundAdmin]
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    for {
      flowTermCollection <- fundSubCopyConfigService.getFlowTermCollection(fundSubId)
      emails <- ZIO
        .foreach(emailData) { emailDataForEachAdmin =>
          if (emailDataForEachAdmin.inEmailComments.nonEmpty) {
            sendFundSubEmail(
              fundSubId,
              FormCommentAssignmentNotifEmailForFundAdminGenerate(
                emailDataForEachAdmin,
                flowTermCollection = flowTermCollection
              )(fundSubEmailUtils)
            ).map(_.internalId)
          } else {
            ZIO.attempt(Seq.empty[EmailSenderService.EmailWithInternalId])
          }
        }
        .map(_.flatten)
    } yield emails
  }

  def sendFormCommentNotifEmailForLp(
    emailData: NewFormCommentEmailDataForLp,
    actor: UserId,
    userCustomTemplateOpt: Option[EmailTemplateMessage]
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = emailData.fundSubLpId.parent
    sendFundSubEmail(
      fundSubId,
      FormCommentNotifEmailForLpGenerate(
        emailData,
        emailEvent = FundSubEvent.notifyCommentsToInvestor,
        Some(actor),
        userCustomTemplateOpt
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendFormCommentNotifEmailDigestForLp(
    emailData: NewFormCommentEmailDataForLp
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = emailData.fundSubLpId.parent
    sendFundSubEmail(
      fundSubId,
      FormCommentNotifEmailForLpGenerate(
        emailData,
        emailEvent = FundSubEvent.commentsDigestToInvestor,
        actorOpt = None,
        userCustomTemplateOpt = None
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendInviteAdminEmail(
    fundSubId: FundSubId,
    groupId: TeamId,
    inviter: UserId,
    invitee: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubId,
      InviteAdminEmailGenerate(
        fundSubId,
        groupId,
        inviter,
        invitee
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendInviteCollaboratorEmail(
    inviter: UserId,
    invitee: UserId,
    lpId: FundSubLpId,
    attachedDocs: Seq[FileId] = Seq.empty,
    userCustomTemplateOpt: Option[EmailTemplateMessage] = None,
    enableSSO: Boolean = false
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    sendFundSubEmail(
      fundSubId,
      InviteLpEmailGenerate(
        inviter,
        invitee,
        lpId,
        attachedDocs,
        userCustomTemplateOpt,
        enableSSO = enableSSO
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendInviteLpEmail(
    inviter: UserId,
    invitee: UserId,
    lpId: FundSubLpId,
    attachedDocs: Seq[FileId] = Seq.empty,
    userCustomTemplateOpt: Option[EmailTemplateMessage] = None,
    enableSSO: Boolean = false
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    sendFundSubEmail(
      fundSubId,
      InviteLpEmailGenerate(
        inviter,
        invitee,
        lpId,
        attachedDocs,
        userCustomTemplateOpt,
        enableSSO = enableSSO
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendSubscribeBlankOrderViaInvitationEmail(
    invitee: UserId,
    lpId: FundSubLpId,
    attachedDocs: Seq[FileId] = Seq.empty,
    userCustomTemplateOpt: Option[EmailTemplateMessage] = None
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    sendFundSubEmail(
      fundSubId,
      SubscribeBlankOrderViaInvitationEmailGenerate(
        invitee,
        lpId,
        attachedDocs,
        userCustomTemplateOpt
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendInviteMultipleLpsPerMainLpEmail(
    inviter: UserId,
    invitee: UserId,
    lpIds: Seq[FundSubLpId],
    investAdditionalLinkIdOpt: Option[FundSubSingleUserInvitationLinkId],
    userCustomTemplateOpt: Option[EmailTemplateMessage] = None
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubIdOpt = lpIds.headOption.map(_.parent)
    ZIOUtils
      .traverseOption(fundSubIdOpt) { fundSubId =>
        sendFundSubEmail(
          fundSubId,
          InviteMultipleLpsPerMainLpEmailGenerate(
            inviter,
            invitee,
            lpIds,
            investAdditionalLinkIdOpt,
            userCustomTemplateOpt
          )(fundSubEmailUtils)
        )
          .map(_.internalId.headOption)
      }
      .map(_.flatten)
  }

  def sendInviteMultipleLpsPerCollaboratorLpEmail(
    inviter: UserId,
    invitee: UserId,
    lpIds: Seq[FundSubLpId],
    investAdditionalLinkIdOpt: Option[FundSubSingleUserInvitationLinkId],
    userCustomTemplateOpt: Option[EmailTemplateMessage] = None
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubIdOpt = lpIds.headOption.map(_.parent)
    ZIOUtils
      .traverseOption(fundSubIdOpt) { fundSubId =>
        sendFundSubEmail(
          fundSubId,
          InviteMultipleLpsPerCollaboratorEmailGenerate(
            inviter,
            invitee,
            lpIds,
            investAdditionalLinkIdOpt,
            userCustomTemplateOpt
          )(fundSubEmailUtils)
        )
          .map(_.internalId.headOption)
      }
      .map(_.flatten)
  }

  def sendLpDuplicatedOrderLpEmail(
    fundSubLpId: FundSubLpId,
    originalLpId: FundSubLpId,
    actorUserId: UserId,
    lpUserId: UserId,
    receiverUserId: UserId,
    newFirmName: String
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = fundSubLpId.parent
    sendFundSubEmail(
      fundSubId,
      LpDuplicatedOrderLpEmailGenerate(
        fundSubLpId,
        originalLpId,
        actorUserId,
        lpUserId,
        receiverUserId,
        newFirmName
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  private def sendLpEventEmailInternal(
    receivers: Seq[UserId],
    fundSubEvent: FundSubEvent,
    lpUserId: UserId,
    actor: Option[UserId],
    fundSubLpId: FundSubLpId,
    redirectedPage: FundSubPage,
    message: String = ""
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = fundSubLpId.parent
    for {
      emails <- ZIO
        .foreach(receivers) { receiver =>
          sendFundSubEmail(
            fundSubId,
            LpEventEmailGenerate(
              Seq(receiver),
              fundSubEvent,
              lpUserId,
              actor,
              fundSubLpId,
              redirectedPage,
              message
            )(fundSubEmailUtils)
          ).map(_.internalId)
        }
        .map(_.flatten)
    } yield emails
  }

  def sendNewLpReportEmailUnsafe(
    fundSubId: FundSubId,
    receiverUserId: UserId,
    sendingTimeWithUserZone: ZonedDateTime,
    newLpsFilteredWithPermission: List[NewLpReportParams]
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubId,
      NewLpReportEmailGenerate(
        fundSubId,
        receiverUserId,
        sendingTimeWithUserZone,
        newLpsFilteredWithPermission
      )
    )
      .map(_.internalId.headOption)
  }

  def sendLpSelfRemindSignAgainEmail(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    sendFundSubEmail(
      fundSubId,
      RemindLpSignAgainEmailGenerate(
        lpId,
        actor
      )
    )
      .map(_.internalId.headOption)
  }

  def sendRemindLpCompleteFormEmail(
    fundSubEvent: FundSubEvent,
    actor: UserId,
    receiver: UserId,
    lpUserId: UserId,
    lpId: FundSubLpId,
    receiverNames: Seq[String],
    userCustomTemplateOpt: Option[EmailTemplateMessage] = None,
    attachedDocs: Seq[FileId] = Seq.empty
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    sendFundSubEmail(
      fundSubId,
      RemindLpCompleteFormEmailGenerate(
        fundSubEvent,
        actor,
        receiver,
        lpUserId,
        lpId,
        receiverNames,
        userCustomTemplateOpt,
        attachedDocs
      )(fundSubEmailUtils)
    ).map(_.internalId)
  }

  def sendRemindSupportingDocEmail(
    actor: UserId,
    receiver: UserId,
    lpUserId: UserId,
    lpId: FundSubLpId,
    receiverNames: Seq[String],
    requestedDocNames: Seq[String],
    userCustomTemplateOpt: Option[EmailTemplateMessage],
    attachedDocs: Seq[FileId],
    ccReceivers: Seq[EmailAddress]
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    sendFundSubEmail(
      fundSubId,
      RemindSupportingDocEmailGenerate(
        actor,
        receiver,
        lpUserId,
        lpId,
        receiverNames,
        requestedDocNames,
        userCustomTemplateOpt,
        attachments = attachedDocs,
        additionalCcReceivers = ccReceivers
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendRemoveCollaboratorEmail(
    fundSubLpId: FundSubLpId,
    actor: UserId,
    toRemoveCollaborator: UserId,
    mainLpUserId: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = fundSubLpId.parent
    sendFundSubEmail(
      fundSubId,
      RemoveCollaboratorEmailGenerate(
        fundSubLpId,
        actor,
        toRemoveCollaborator,
        mainLpUserId
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendRemoveFundSubUserEmail(
    fundSubId: FundSubId,
    fundSubEvent: FundSubEvent,
    fundSubLpIdOpt: Option[FundSubLpId],
    removerId: UserId,
    removedUsers: Seq[UserId]
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubId,
      RemoveFundSubUserEmailGenerate(
        fundSubId,
        fundSubEvent,
        fundSubLpIdOpt,
        removerId,
        removedUsers
      )(fundSubEmailUtils)
    ).map(_.internalId)
  }

  def prepareRequestLpChangeEmailGenerate(
    actor: UserId,
    mainLp: UserId,
    receivers: Seq[UserId],
    lpId: FundSubLpId,
    customBodyOpt: Option[String] = None,
    attachedComments: List[AttachedCommentsOnRequestChangeEmail] = List.empty,
    ccReceivers: Seq[EmailAddress] = Seq.empty
  ): RequestLpChangeEmailGenerate = {
    RequestLpChangeEmailGenerate(
      actor,
      mainLp,
      receivers,
      lpId,
      customBodyOpt,
      attachedComments,
      additionalCcReceivers = ccReceivers
    )(fundSubEmailUtils)
  }

  def sendRequestSupportingDocEmail(
    actor: UserId,
    receiver: UserId,
    lpUserId: UserId,
    lpId: FundSubLpId,
    receiverNames: Seq[String],
    requestedDocNames: Seq[String],
    message: String,
    ccRecipients: Seq[EmailAddress] = Seq.empty
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    sendFundSubEmail(
      fundSubId,
      RequestSupportingDocEmailGenerate(
        actor,
        receiver,
        lpUserId,
        lpId,
        receiverNames,
        requestedDocNames,
        message,
        additionalCcReceivers = ccRecipients
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendSendCountersignedEmail(
    fundSubLpId: FundSubLpId,
    actor: UserId,
    userCustomEmailTemplateOpt: Option[EmailTemplateMessage] = None,
    countersignedFiles: List[FileId]
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = fundSubLpId.parent
    sendFundSubEmail(
      fundSubId,
      SendCountersignedEmailGenerate(
        fundSubLpId,
        actor,
        userCustomEmailTemplateOpt,
        countersignedFiles
      )(fundSubEmailUtils)
    ).map(_.internalId)
  }

  def sendUpdateCountersignDocEmail(
    fundSubLpId: FundSubLpId,
    actor: UserId,
    userCustomEmailTemplateOpt: Option[EmailTemplateMessage] = None,
    countersignedFiles: List[FileId]
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = fundSubLpId.parent
    sendFundSubEmail(
      fundSubId,
      UpdateCountersignDocEmailGenerate(
        fundSubLpId,
        actor,
        userCustomEmailTemplateOpt,
        countersignedFiles
      )(fundSubEmailUtils)
    ).map(_.internalId)
  }

  def sendMarkSubscriptionAsCompleteEmail(
    lpId: FundSubLpId,
    actor: UserId,
    userCustomEmailTemplateOpt: Option[EmailTemplateMessage] = None
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    sendFundSubEmail(
      fundSubId,
      MarkSubscriptionAsCompleteEmailGenerate(
        lpId,
        actor,
        userCustomEmailTemplateOpt
      )(fundSubEmailUtils)
    ).map(_.internalId)
  }

  def sendUploadReferenceDocEmail(
    actor: UserId,
    receivers: Seq[UserId],
    lpId: FundSubLpId,
    files: Seq[FileId],
    messageOpt: Option[String],
    ccRecipients: Seq[EmailAddress] = Seq.empty
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    sendFundSubEmail(
      fundSubId,
      UploadReferenceDocEmailGenerate(
        actor,
        receivers,
        lpId,
        files,
        messageOpt,
        additionalCcReceivers = ccRecipients
      )(fundSubEmailUtils)
    ).map(_.internalId)
  }

  def sendVerificationEmail(
    entityId: EntityId,
    fundSubId: FundSubId,
    actor: UserId,
    progressId: FundSubSimulatorProgressId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubId,
      FundSubSimulatorVerificationEmailGenerate(
        entityId,
        fundSubId,
        actor,
        progressId
      )
    )
      .map(_.internalId.headOption)
  }

  def sendWarningEmail(
    email: String
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    emailSenderService.enqueue(WarningEmailGenerate(email), EmailSystemSpaceId.System).map(_.internalId.headOption)
  }

  // LP submit for hard review
  def sendDocumentReadyForReviewEmail(
    entityId: EntityId,
    lpId: FundSubLpId,
    actor: UserId,
    fundAdminIds: Seq[UserId]
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    for {
      fundAdminsWithPermission <- getFundAdminsWithPermissionToInvestor(lpId, fundAdminIds)
      flowTermCollection <- fundSubCopyConfigService.getFlowTermCollection(fundSubId)
      emails <- ZIO
        .foreach(fundAdminsWithPermission) { receiver =>
          sendFundSubEmail(
            fundSubId,
            DocumentReadyForReviewEmailGenerate(
              entityId,
              lpId,
              actor,
              receiver,
              flowTermCollection
            )(fundSubEmailUtils)
          )
            .map(_.internalId.headOption)
        }
        .map(_.flatten)
    } yield emails

  }

  def sendAdditionalDocumentReadyForReviewReportEmail(
    newAdditionalDocReadyForReviewByLps: Seq[NewAdditionalDocReadyForReviewByLp],
    fundSubId: FundSubId,
    receiver: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubId,
      AdditionalDocumentReadyForReviewReportEmailGenerate(
        newAdditionalDocReadyForReviewByLps,
        receiver
      )(fundSubEmailUtils, fundSubId)
    )
      .map(_.internalId.headOption)
  }

  // LP submit for soft review
  def sendUnsignedDocumentReadyForReviewEmail(
    entityId: EntityId,
    lpId: FundSubLpId,
    lpUser: UserId,
    actor: UserId,
    fundAdminIds: Seq[UserId]
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    for {
      fundAdminsWithPermission <- getFundAdminsWithPermissionToInvestor(lpId, fundAdminIds)
      flowTermCollection <- fundSubCopyConfigService.getFlowTermCollection(lpId.parent)
      emails <- ZIO
        .foreach(fundAdminsWithPermission) { receiver =>
          sendFundSubEmail(
            fundSubId,
            UnsignedDocumentReadyForReviewEmailGenerate(
              entityId,
              lpId,
              lpUser,
              actor,
              receiver,
              flowTermCollection
            )(fundSubEmailUtils)
          )
            .map(_.internalId.headOption)
        }
        .map(_.flatten)
    } yield emails
  }

  def sendCancelSoftReviewEmail(
    lpId: FundSubLpId,
    lpUser: UserId,
    actor: UserId,
    fundAdminIds: Seq[UserId]
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    for {
      fundAdminsWithPermission <- getFundAdminsWithPermissionToInvestor(lpId, fundAdminIds)
      emails <- ZIO
        .foreach(fundAdminsWithPermission) { receiver =>
          sendFundSubEmail(
            fundSubId,
            CancelSoftReviewEmailGenerate(
              lpId,
              lpUser,
              actor,
              receiver
            )(fundSubEmailUtils)
          )
            .map(_.internalId.headOption)
        }
        .map(_.flatten)
    } yield emails
  }

  def sendApproveSoftReviewEmail(
    lpId: FundSubLpId,
    actor: UserId,
    receiver: UserId,
    emailTemplateMessageOpt: Option[EmailTemplateMessage]
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    sendFundSubEmail(
      fundSubId,
      ApproveSoftReviewEmailGenerate(
        lpId,
        actor,
        receiver,
        userCustomTemplateOpt = emailTemplateMessageOpt
      )(fundSubEmailUtils)
    ).map(_.internalId)
  }

  def sendDocumentReviewUnassignmentEmail(
    fundSubId: FundSubId,
    actor: UserId,
    receiver: UserId,
    reviewType: Option[FundSubSubscriptionDocReviewType] = None
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    for {
      flowTermCollection <- fundSubCopyConfigService.getFlowTermCollection(fundSubId)
      emailOpt <- sendFundSubEmail(
        fundSubId,
        DocumentReviewUnassignmentEmailGenerate(
          fundSubId,
          actor,
          receiver,
          reviewType,
          flowTermCollection
        )(fundSubEmailUtils)
      )
        .map(_.internalId.headOption)
    } yield emailOpt
  }

  def sendDocumentReviewAssignmentEmail(
    fundSubId: FundSubId,
    assigner: UserId,
    receiver: UserId,
    reviewType: Option[FundSubSubscriptionDocReviewType] = None
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    for {
      flowTermCollection <- fundSubCopyConfigService.getFlowTermCollection(fundSubId)
      emailOpt <- sendFundSubEmail(
        fundSubId,
        DocumentReviewAssignmentEmailGenerate(
          fundSubId,
          assigner,
          receiver,
          reviewType,
          flowTermCollection
        )(fundSubEmailUtils)
      )
        .map(_.internalId.headOption)
    } yield emailOpt

  }

  def sendAmlKycDocumentReviewUnassignmentEmail(
    fundSubId: FundSubId,
    actor: UserId,
    receiver: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubId,
      AmlKycDocumentReviewUnassignmentEmailGenerate(
        fundSubId,
        actor,
        receiver
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendAmlKycDocumentReviewAssignmentEmail(
    fundSubId: FundSubId,
    assigner: UserId,
    receiver: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubId,
      AmlKycDocumentReviewAssignmentEmailGenerate(
        fundSubId,
        assigner,
        receiver
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendCancelSignatureRequestEmail(
    lpId: FundSubLpId,
    fileIds: Seq[FileId],
    receiver: UserId,
    actor: UserId,
    envelopeType: FundSubSignatureRequestModels.FundSubSignatureEnvelopeType,
    requestId: SignatureRequestId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    sendFundSubEmail(
      fundSubId,
      CancelSignatureRequestEmailGenerate(
        lpId,
        fileIds,
        receiver,
        actor,
        envelopeType,
        requestId
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendFundSubDoneSigningRequesterEmail(
    fundSubLpId: FundSubLpId,
    entityId: EntityId,
    fileIds: Seq[FileId],
    requester: UserId,
    actor: UserId,
    envelopeType: FundSubSignatureEnvelopeType,
    signatureRequestId: SignatureRequestId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = fundSubLpId.parent
    sendFundSubEmail(
      fundSubId,
      FundSubDoneSigningRequesterEmailGenerate(
        fundSubLpId,
        entityId,
        fileIds,
        requester,
        actor,
        envelopeType,
        signatureRequestId
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendFundSubDoneBatchSigningEmail(
    fundSubId: FundSubId,
    countersignedLps: Seq[CountersignedLpParam],
    receiverUserIds: Seq[UserId],
    actorId: UserId,
    recipientType: RecipientType
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    if (recipientType == FundSubDoneBatchSigningEmailGenerate.FundAdmin) {
      sendFundSubDoneBatchSigningFundAdminEmail(
        fundSubId,
        countersignedLps,
        receiverUserIds,
        actorId
      )
    } else {
      for {
        flowTermCollection <- fundSubCopyConfigService.getFlowTermCollection(fundSubId)
        emails <- ZIO
          .foreach(receiverUserIds) { receiverUserId =>
            sendFundSubEmail(
              fundSubId,
              FundSubDoneBatchSigningEmailGenerate(
                fundSubId,
                countersignedLps,
                Seq(receiverUserId),
                actorId,
                recipientType,
                flowTermCollection
              )
            ).map(_.internalId)
          }
          .map(_.flatten)
      } yield emails
    }

  }

  private def sendFundSubDoneBatchSigningFundAdminEmail(
    fundSubId: FundSubId,
    countersignedLps: Seq[CountersignedLpParam],
    receiverUserIds: Seq[UserId],
    actorId: UserId
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    for {
      authorizedFundManagersOfLps <- fundSubInvestorGroupService.getAuthorizedFundManagers(
        countersignedLps.map(_.lpId)
      )
      flowTermCollection <- fundSubCopyConfigService.getFlowTermCollection(fundSubId)
      emails <- ZIO
        .foreach(receiverUserIds) { userId =>
          val filteredLpParams = countersignedLps.filter { lpParam =>
            authorizedFundManagersOfLps.getOrElse(lpParam.lpId, Set.empty).contains(userId)
          }
          if (filteredLpParams.nonEmpty) {
            sendFundSubEmail(
              fundSubId,
              FundSubDoneBatchSigningEmailGenerate(
                fundSubId,
                filteredLpParams,
                Seq(userId),
                actorId,
                FundSubDoneBatchSigningEmailGenerate.FundAdmin,
                flowTermCollection
              )
            ).map(_.internalId)
          } else {
            ZIO.attempt(Seq.empty[EmailSenderService.EmailWithInternalId])
          }
        }
        .map(_.flatten)
    } yield emails
  }

  def sendFundSubDoneSigningSignerEmail(
    fundSubLpId: FundSubLpId,
    fileIds: Seq[FileId],
    signer: UserId,
    actor: UserId,
    envelopeType: FundSubSignatureEnvelopeType,
    signatureRequestId: SignatureRequestId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = fundSubLpId.parent
    sendFundSubEmail(
      fundSubId,
      FundSubDoneSigningSignerEmailGenerate(
        fundSubLpId,
        fileIds,
        signer,
        actor,
        envelopeType,
        signatureRequestId
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendFundSubReassignRequesterEmail(
    fundSubLpId: FundSubLpId,
    fileIds: Seq[FileId],
    requester: UserId,
    newSignerName: String,
    newSignerEmail: String,
    reason: String,
    actor: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = fundSubLpId.parent
    sendFundSubEmail(
      fundSubId,
      FundSubReassignRequesterEmailGenerate(
        fundSubLpId,
        fileIds,
        requester,
        newSignerName,
        newSignerEmail,
        reason,
        actor
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendFundSubReassignNewSignerEmail(
    fundSubLpId: FundSubLpId,
    requestId: SignatureRequestId,
    reason: String,
    signer: UserId,
    actor: UserId,
    fileIds: Seq[FileId],
    requester: UserId,
    envelopeType: FundSubSignatureEnvelopeType
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = fundSubLpId.parent
    sendFundSubEmail(
      fundSubId,
      FundSubReassignSignerEmailGenerate(
        fundSubLpId,
        requestId,
        envelopeType,
        reason,
        signer,
        actor,
        fileIds,
        requester
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendFundSubRemindSignatureRequestEmail(
    fundSubLpId: FundSubLpId,
    signer: UserId,
    actor: UserId,
    envelopeType: FundSubSignatureEnvelopeType,
    requestId: SignatureRequestId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = fundSubLpId.parent
    sendFundSubEmail(
      fundSubId,
      FundSubRemindSignatureRequestEmailGenerate(
        fundSubLpId,
        signer,
        actor,
        envelopeType,
        requestId
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendFundSubSendSignatureRequestSignerEmail(
    fundSubLpId: FundSubLpId,
    fileIds: Seq[FileId],
    signer: UserId,
    actor: UserId,
    message: String,
    envelopeType: FundSubSignatureEnvelopeType,
    signatureRequestId: SignatureRequestId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = fundSubLpId.parent
    sendFundSubEmail(
      fundSubId,
      FundSubSendSignatureRequestSignerEmailGenerate(
        fundSubLpId,
        fileIds,
        signer,
        actor,
        message,
        envelopeType,
        signatureRequestId
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendFundSubSendBatchCountersignEmail(
    fundSubId: FundSubId,
    entityId: EntityId,
    signer: UserId,
    actor: UserId,
    message: String,
    packageCount: Int,
    docusignBatchCountersignRequestIdOpt: Option[SignatureRequestId]
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    for {
      flowTermCollection <- fundSubCopyConfigService.getFlowTermCollection(fundSubId)
      emailOpt <- sendFundSubEmail(
        fundSubId,
        FundSubSendBatchCountersignEmailGenerate(
          fundSubId,
          entityId,
          signer,
          actor,
          message,
          packageCount,
          flowTermCollection,
          docusignBatchCountersignRequestIdOpt
        )(fundSubEmailUtils)
      )
        .map(_.internalId.headOption)
    } yield emailOpt
  }

  def sendFundSubRemindBatchCountersignEmail(
    fundSubId: FundSubId,
    entityId: EntityId,
    signer: UserId,
    actorAdminId: UserId,
    packageCount: Int,
    docusignBatchCountersignRequestIdOpt: Option[SignatureRequestId]
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    for {
      flowTermCollection <- fundSubCopyConfigService.getFlowTermCollection(fundSubId)
      emailOpt <- sendFundSubEmail(
        fundSubId,
        FundSubRemindBatchCountersignEmailGenerate(
          fundSubId = fundSubId,
          entityId = entityId,
          signer = signer,
          actorAdminId = actorAdminId,
          packageCount = packageCount,
          flowTermCollection = flowTermCollection,
          docusignBatchCountersignRequestIdOpt = docusignBatchCountersignRequestIdOpt
        )(fundSubEmailUtils)
      )
        .map(_.internalId.headOption)
    } yield emailOpt
  }

  def sendCancelBatchCountersignEmail(
    fundSubId: FundSubId,
    signer: UserId,
    actor: UserId,
    packageCount: Int
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    for {
      flowTermCollection <- fundSubCopyConfigService.getFlowTermCollection(fundSubId)
      emails <- sendFundSubEmail(
        fundSubId,
        FundSubCancelBatchCountersignEmailGenerate(
          fundSubId = fundSubId,
          signer = signer,
          actor = actor,
          packageCount = packageCount,
          flowTermCollection = flowTermCollection
        )(fundSubEmailUtils)
      )
    } yield emails.internalId.headOption
  }

  def sendMarkSubDocRequestCompeteSignerEmail(
    lpId: FundSubLpId,
    signer: UserId,
    requestId: SignatureRequestId,
    envelopeType: FundSubSignatureRequestModels.FundSubSignatureEnvelopeType,
    fileIds: Seq[FileId],
    actor: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    sendFundSubEmail(
      fundSubId,
      MarkSubDocRequestCompeteSignerEmailGenerate(
        lpId,
        signer,
        requestId,
        envelopeType,
        fileIds,
        actor
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendMarkSubDocRequestCompleteRequesterEmail(
    lpId: FundSubLpId,
    requester: UserId,
    requestId: SignatureRequestId,
    envelopeType: FundSubSignatureRequestModels.FundSubSignatureEnvelopeType,
    fileIds: Seq[FileId],
    actor: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    sendFundSubEmail(
      fundSubId,
      MarkSubDocRequestCompleteRequesterEmailGenerate(
        lpId,
        requester,
        requestId,
        envelopeType,
        fileIds,
        actor
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendFormSubmissionSignatureRequestCompleteToRequesterEmail(
    lpId: FundSubLpId,
    actor: UserId,
    requester: UserId,
    fileIds: Seq[FileId],
    signatureRequestId: SignatureRequestId,
    envelopeType: FundSubSignatureEnvelopeType
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    for {
      entityId <- FDBRecordDatabase.transact(
        FundSubModelStoreOperations.Production
      )(_.getFundSubPublicModel(fundSubId).map(_.investorEntity))
      email <- sendFundSubEmail(
        fundSubId,
        FundSubDoneSigningRequesterEmailGenerate(
          fundSubLpId = lpId,
          entityId = entityId,
          fileIds = fileIds,
          requester = requester,
          actor = actor,
          signatureRequestId = signatureRequestId,
          envelopeType = envelopeType
        )(fundSubEmailUtils)
      )
        .map(_.internalId.headOption)
    } yield email
  }

  def sendEmail(
    fundSubId: FundSubId,
    emailGenerate: GenerateSingleEmail
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(fundSubId, emailGenerate).map(_.internalId.headOption)
  }

  override def start(): zio.Task[Unit] = {
    trackEmailEventConsumer.start()
  }

  override def close(): UIO[Unit] = {
    trackEmailEventConsumer.close()
  }

  def trackEmailLinkClicked(
    token: ActionTokenClaim,
    httpContext: Option[RequestContext]
  ): Task[Unit] = {
    ZIOUtils.when(token.offeringId.contains(GlobalOfferingIdFactory.generateId(OfferingId.FundSub))) {
      token.subject.fold[Task[Unit]](ZIO.unit) {
        case TokenSubject.UserIdSubject(userId) =>
          ZIOUtils.traverseOptionUnit(token.mid) { tokenMetaId =>
            for {
              metaData <- TokenMetaDataUtils.getTokenMetaData(tokenMetaId)
              linkType <- FundSubTokenUtils.extractLinkTypeFromMetaData(metaData)
              _ <- linkType match {
                case FundSubTokenUtils.LinkType.ConfirmInvitation =>
                  trackLpOrCollaboratorConfirmInvitation(
                    userId,
                    metaData,
                    httpContext
                  )
              }
            } yield ()
          }
        case _ => ZIO.unit
      }
    }
  }

  def adminsSubscribeToCountersignEmail(fundSubId: FundSubId): Task[List[UserId]] = {
    for {
      admins <- fundSubPermissionService.getJoinedAdmins(fundSubId)
      adminsToNotify <-
        ZIO
          .foreach(admins) { admin =>
            for {
              preference <- FundAdminNotificationUtils.getFundNotificationPreference(admin, fundSubId)
            } yield {
              if (preference.notifyOnFundCountersignEvent) {
                Option(admin)
              } else {
                None
              }
            }
          }
          .map(_.flatten.toList)
    } yield adminsToNotify
  }

  def notifyAdminDocumentsMissingReviewerEmail(
    fundSubId: FundSubId,
    fundSubGroupMemberService: FundSubGroupMemberService,
    investorGroupName: String
  ): Task[Unit] = {
    for {
      fundAdmins <- fundSubGroupMemberService
        .getGroupMembersWithAdminRole(fundSubId, shouldOnlyGetJoinedAdmins = true)
        .map(_.map(_.userId))
      _ <- ZIO.logInfo(
        s"sending DocumentHasNoReviewerGenerate email on fund ${fundSubId.idString}; fund admins: $fundAdmins"
      )
      _ <- ZIO.foreach(fundAdmins) { admin =>
        sendFundSubEmail(
          fundSubId,
          DocumentHasNoReviewerGenerate(
            fundSubId,
            admin,
            investorGroupName
          )(fundSubEmailUtils)
        )
      }
    } yield ()
  }

  def sendFundActivitiesEmail(
    fundId: FundSubId,
    timestampOpt: Option[Instant] = None,
    isDailyEmail: Boolean = true,
    ignoreTimeCheckForTesting: Boolean = false
  ): Task[List[UserId]] = {
    for {
      _ <- ZIO.logInfo(s"Checking ${if (isDailyEmail) "daily" else "weekly"} fund activities for fund $fundId")
      joinedAdmins <- fundSubPermissionService.getJoinedAdmins(fundId)
      affectedAdmins <- ZIO
        .foreach(joinedAdmins) { admin =>
          for {
            preference <- FundAdminNotificationUtils.getFundNotificationPreference(admin, fundId)
            adminOpt <-
              if (
                FundAdminNotificationUtils.subscribeToFundActivitiesEmail(preference) &&
                preference.fundActivitiesEmailFrequency.isFundActivitiesEmailFrequencyDaily == isDailyEmail
              ) {
                sendDailyFundActivityForFundAdmin(
                  admin,
                  fundId,
                  preference,
                  timestampOpt,
                  isDaily = isDailyEmail,
                  ignoreTimeCheckForTesting = ignoreTimeCheckForTesting
                )
              } else {
                ZIO.attempt(None)
              }
          } yield adminOpt
        }
        .map(_.flatten)
    } yield affectedAdmins
  }

  def getAdminsWithVisibility(lpId: FundSubLpId): Task[Seq[UserId]] = {
    for {
      joinedAdmins <- fundSubPermissionService.getJoinedAdmins(lpId.parent)
      adminsWithVisibility <- ZIOUtils
        .foreachParN(parallelism = 4)(joinedAdmins) { admin =>
          for {
            // TODO(side-letter) @tuananhtd: update with correct permission
            canAccessLpSideLetter <- fundSubPermissionService.checkIfUserCanManageInvestor(lpId, admin)
          } yield Option.when(canAccessLpSideLetter)(admin)
        }
        .map(_.flatten)
    } yield adminsWithVisibility.toSeq
  }

  private def sendDailyFundActivityForFundAdmin(
    admin: UserId,
    fundId: FundSubId,
    preference: FundAdminNotificationPreference,
    timestampOpt: Option[Instant],
    isDaily: Boolean,
    ignoreTimeCheckForTesting: Boolean
  ): Task[Option[UserId]] = {
    val now = timestampOpt.getOrElse(DateCalculator.instantNow)
    for {
      adminTimeZone <- userService
        .getUserTimeZone(admin)
        .map(_.getOrElse(DateTimeUtils.losAngelesTimezone))
      zonedDateTime = now.atZone(adminTimeZone)
      localDateTime = zonedDateTime.toLocalDateTime
      lastEmailSentAtOpt <- FDBRecordDatabase.transact(DigestEmailLogStoreProvider.Production) { store =>
        val ops = DigestEmailLogStoreOperations(store)
        ops
          .get(
            fundId,
            admin
          )
          .map { log =>
            if (isDaily) {
              log.lastDailyFundActivitiesSentAt
            } else {
              log.lastWeeklyFundActivitiesSentAt
            }
          }
      }
      lastEmailSentAtLocalDateOpt = lastEmailSentAtOpt.map(_.atZone(adminTimeZone).toLocalDateTime)
      lastEmailSentInSameDate = lastEmailSentAtLocalDateOpt.exists(_.toLocalDate.isEqual(localDateTime.toLocalDate))
      // we ensured no 2 emails is sent at the same day already so the hour can be relaxed to avoid conflicting with deploy time
      timeCheck =
        localDateTime.getHour == 9 || localDateTime.getHour == 10 && (isDaily || localDateTime.getDayOfWeek == DayOfWeek.MONDAY)
      goodTiming =
        ignoreTimeCheckForTesting || (!lastEmailSentInSameDate && timeCheck)
      // getting event from last email but making sure that the period is relatively close to the intended period
      oneDayAgo = now.minusSeconds(24 * 60 * 60)
      oneWeekAgo = now.minusSeconds(7 * 24 * 60 * 60)
      intendedStartPoint = if (isDaily) oneDayAgo else oneWeekAgo
      from =
        if (lastEmailSentAtOpt.exists(_.isBefore(intendedStartPoint.minusSeconds(2 * 60 * 60)))) {
          intendedStartPoint
        } else {
          lastEmailSentAtOpt.getOrElse(intendedStartPoint)
        }
      windowedActivities <- OrderActivityUtils.getActivities(
        fundId,
        from = from,
        to = now
      )
      _ <- ZIO.logInfo(s"Found ${windowedActivities.size} events")
      lpIds <- fundSubInvestorGroupService.getAccessibleLpIds(fundId, admin)
      relatedActivities = windowedActivities.filter { activity =>
        lpIds.contains(activity.lpId) &&
        FundAdminNotificationUtils.isRelatedActivity(
          preference,
          activity.activityType
        )
      }
      shouldSendEmail = goodTiming && relatedActivities.nonEmpty
      _ <- ZIO.when(shouldSendEmail) {
        val dailyOrWeekly = if (isDaily) "daily" else "weekly"
        for {
          _ <- ZIO.logInfo(s"Sending fund $dailyOrWeekly activity email for ${admin.idString} (fund ${fundId.idString})")
          _ <- sendFundSubEmail(
            fundId,
            FundActivityEmailGenerate(
              fundId,
              relatedActivities,
              admin,
              isDaily = isDaily,
              date = localDateTime.toLocalDate
            )(fundSubEmailUtils)
          )
          _ <- FDBRecordDatabase.transact(DigestEmailLogStoreProvider.Production) { store =>
            val ops = DigestEmailLogStoreOperations(store)
            ops.update(
              fundId,
              admin,
              log =>
                if (isDaily) {
                  log.copy(
                    lastDailyFundActivitiesSentAt = Option(now)
                  )
                } else {
                  log.copy(
                    lastWeeklyFundActivitiesSentAt = Option(now)
                  )
                }
            )
          }
        } yield ()
      }
    } yield Option.when(shouldSendEmail)(admin)
  }

  def sendSignerFailedDocusignAuthenticationNotificationToRequester(
    signerId: UserId,
    requestBasic: FundSubSignatureRequestBasic
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    for {
      fundSubId <- ZIOUtils.uniqueSeqToTask(
        requestBasic.envelopeType.lpIds.map(_.parent),
        GeneralServiceException(s"Cannot retrieve fundSubId from request ${requestBasic.requestId}")
      )
      flowTermCollection <- fundSubCopyConfigService.getFlowTermCollection(fundSubId)
      emailOpt <- requestBasic.envelopeType match {
        case singleDocType: FundSubSignatureEnvelopeType.SingleDocTypeEnvelope =>
          ZIOUtils
            .traverseOption(singleDocType.oldSignatureRequestType) { oldSignatureRequestType =>
              sendFundSubEmail(
                fundSubId,
                RecipientFailedDocusignAuthenticationRequesterEmailGenerate(
                  singleDocType.lpId,
                  signerId,
                  requestBasic.requester,
                  oldSignatureRequestType,
                  flowTermCollection
                )(fundSubEmailUtils)
              )
                .map(_.internalId.headOption)
            }
            .map(_.flatten)
        case multiDocType: FundSubSignatureEnvelopeType.LpMultiDocTypeEnvelope =>
          ZIOUtils
            .traverseOption(multiDocType.oldSignatureRequestType) { oldSignatureRequestType =>
              sendFundSubEmail(
                fundSubId,
                RecipientFailedDocusignAuthenticationRequesterEmailGenerate(
                  multiDocType.lpId,
                  signerId,
                  requestBasic.requester,
                  oldSignatureRequestType,
                  flowTermCollection
                )(fundSubEmailUtils)
              )
                .map(_.internalId.headOption)
            }
            .map(_.flatten)
        case _: FundSubSignatureEnvelopeType.GpBatchCountersignEnvelope =>
          // Todo @voxuannguyen2001: low priority item, but should handle this properly
          ZIO.succeed(None)
      }
    } yield emailOpt
  }

  def sendNewDataExtractRequestReadyForReview(
    fundSubId: FundSubId,
    receiverUserId: UserId,
    lpsWithRequests: Seq[FundSubLpId]
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    for {
      emailOpt <- sendFundSubEmail(
        fundSubId,
        DataExtractRequestsReadyForReviewForGpEmailGenerate(
          fundSubId,
          lpsWithRequests,
          receiverUserId
        )
      )
        .map(_.internalId.headOption)
    } yield emailOpt
  }

  def sendAdvisorInvitation(
    fundSubRiaGroupId: FundSubRiaGroupId,
    receiverUserId: UserId,
    actor: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubRiaGroupId.parent,
      AdvisorInvitationEmailGenerate(
        fundSubRiaGroupId = fundSubRiaGroupId,
        receiverUserId = receiverUserId,
        actor = actor
      )
    )
      .map(_.internalId.headOption)
  }

  def sendAdvisorInvitationWithdrawn(
    fundSubRiaGroupId: FundSubRiaGroupId,
    receiverUserId: UserId,
    actor: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = fundSubRiaGroupId.parent
    sendFundSubEmail(
      fundSubId,
      RevokeAdvisorInvitationEmailGenerate(
        fundSubRiaGroupId = fundSubRiaGroupId,
        receiverUserId = receiverUserId,
        actor = actor
      )
    )
      .map(_.internalId.headOption)
  }

  def sendRiaEntityLinkedEmail(
    fundSubRiaGroupId: FundSubRiaGroupId,
    receiverUserIds: Seq[UserId],
    riaEntityName: String,
    actor: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = fundSubRiaGroupId.parent
    sendFundSubEmail(
      fundSubId,
      RiaEntityLinkedEmailGenerate(
        fundSubRiaGroupId = fundSubRiaGroupId,
        riaEntityName = riaEntityName,
        receiverUserIds = receiverUserIds,
        actor = actor
      )
    )
      .map(_.internalId.headOption)
  }

  private def trackLpOrCollaboratorConfirmInvitation(
    userId: UserId,
    metaData: Map[String, String],
    httpContext: Option[RequestContext]
  ) = {
    for {
      lpId <- FundSubTokenUtils.extractLpIdFromMetaData(metaData)
      fundName <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(lpId.parent).map(_.fundName)
      }
      _ <- fundSubLoggingService.logEventEmailLinkClicked(
        userId = userId,
        fundSubLpId = lpId,
        fundName = fundName,
        linkType = FundSubTokenUtils.LinkType.ConfirmInvitation.name,
        httpContext = httpContext
      )
      _ <- fundSubLpActivityLogService.invitationEmailClicked(
        lpId = lpId,
        userId = userId
      )
    } yield ()
  }

  private def executedBounceTask(emailEvent: ReceivedFromMailgun) = {
    getEmailTypeOpt(emailEvent).fold[Task[Unit]] {
      ZIO.fail(new RuntimeException("Unknown fundsub email event"))
    } { emailType =>
      for {
        fundSubLpId <- getFundSubLpId(emailEvent)
        fundSubId = fundSubLpId.parent
        receiverId <- getRecipient(emailEvent)
        _ <- fundSubPermissionUtils
          .validateUserCanAccessLpView(fundSubLpId, receiverId)
          .foldCauseZIO(
            // Fund manager may remove this investor before we handle the bounced email, log error, and skip execution
            ex => ZIO.logErrorCause(s"No role for $receiverId in fund $fundSubLpId", ex),
            _ =>
              for {
                _ <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
                  ops.updateFundSubLpModel(fundSubLpId) { (model: FundSubLpModel) =>
                    model.copy(
                      bouncedEmailUsers = (model.bouncedEmailUsers :+ receiverId).distinct
                    )
                  }
                }
                _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubLpModel(fundSubLpId)(natsNotificationService)
                _ <- LpInfoRecordUtils
                  .updateLpRecordLastUpdatedAt(fundSubLpId)(natsNotificationService)
                fundName <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
                  ops.getFundSubPublicModel(fundSubId).map(_.fundName)
                }
                _ <- fundSubLoggingService.logEventEmailBounce(
                  fundSubLpId = fundSubLpId,
                  userId = receiverId,
                  fundName = fundName,
                  emailType = emailType
                )
                _ <- fundSubLpActivityLogService.invitationEmailBounced(
                  lpId = fundSubLpId,
                  userId = receiverId
                )

                inviter <- getActor(emailEvent)
                inviterRole <- fundSubPermissionService
                  .validateUserCanManageInvestorOrServeOnInvestorSide(fundSubLpId, receiverId)
                bouncedEmailId <- sendFundSubEmail(
                  fundSubId,
                  generate.NotifyEmailBouncedEmailGenerate(
                    fundSubLpId,
                    inviter,
                    inviterRole == FundSubRole.FundManager,
                    receiverId
                  )(fundSubEmailUtils)
                ).map(_.internalId).map(_.flatMap(_.internalIdOpt))
                _ <- fundSubAuditLogService.addEvent(
                  fundSubId = fundSubLpId.parent,
                  params = AddEventParam(
                    actor = None,
                    actorType = AuditLogActorType.System,
                    eventType = AuditLogEventType.INVITATION_EMAIL_BOUNCED,
                    eventEmail = Seq(
                      AddEventEmailParam(
                        fundSubEventType = FundSubEvent.notifyEmailBounced,
                        emailIds = bouncedEmailId
                      )
                    ),
                    activityDetail = GeneralActivity.defaultInstance
                  )
                )
                userInfo <- userProfileService.getUserInfo(receiverId)
                _ <- FundSubDataLakeUtils.sendUpdateParams(
                  fundSubLpId.parent,
                  UpdateOrderBasicInfoParams(
                    lpIdOpt = Option(fundSubLpId),
                    setEmailBouncedUsers = Seq(
                      UserBasicInfo(
                        receiverId,
                        email = userInfo.emailAddressStr,
                        firstName = userInfo.firstName,
                        lastName = userInfo.lastName
                      )
                    )
                  ),
                  fundSubDataLakeIngestionService
                )
              } yield ()
          )
      } yield ()
    }
  }

  private def getActor(emailEvent: ReceivedFromMailgun): Task[UserId] = {
    for {
      actorEmail <- ZIOUtils.optionToTask(
        emailEvent.content.get(OutgoingEmailVariable.ActorVariable.value).flatMap { actorEmailStr =>
          EmailAddress.unapply(actorEmailStr).map(_.address)
        },
        new RuntimeException("Unable to extract actor from webhook event")
      )
      (actorId, _) <- userProfileService.getUserFromEmailAddress(actorEmail)
    } yield actorId
  }

  private def getEmailTypeOpt(emailEvent: ReceivedFromMailgun): Option[FundSubEmailType] = {
    emailEvent.content.get(OutgoingEmailVariable.FundSubEmailTypeVariable.value).flatMap {
      case FundSubEmailType.Invitation.name => Some(FundSubEmailType.Invitation)
      case _                                => None
    }
  }

  private def getFundSubLpId(emailEvent: ReceivedFromMailgun): Task[FundSubLpId] = {
    ZIOUtils.optionToTask(
      emailEvent.content
        .get(OutgoingEmailVariable.FundSubLpIdVariable.value)
        .flatMap(ModelIdRegistry.parser.parseAs[FundSubLpId]),
      new RuntimeException("Unable to extract fundSubLpId from webhook event")
    )
  }

  private def getRecipient(emailEvent: ReceivedFromMailgun): Task[UserId] = {
    for {
      receiverEmail <- ZIOUtils.optionToTask(
        emailEvent.content.get(OutgoingEmailVariable.RecipientVariable.value).flatMap { recipientStr =>
          EmailAddress.unapply(recipientStr).map(_.address)
        },
        new RuntimeException("Unable to extract receiver from webhook event")
      )
      (receiverId, _) <- userProfileService.getUserFromEmailAddress(receiverEmail)
    } yield receiverId
  }

  private def executeEmailOpenTask(emailEvent: ReceivedFromMailgun) = {
    getEmailTypeOpt(emailEvent).fold[Task[Unit]] {
      ZIO.fail(new RuntimeException("Unknown fundsub email event"))
    } { emailType =>
      for {
        fundSubLpId <- getFundSubLpId(emailEvent)
        receiverId <- getRecipient(emailEvent)
        _ <- fundSubPermissionUtils.validateUserCanAccessLpView(fundSubLpId, receiverId)
        fundName <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
          ops.getFundSubPublicModel(fundSubLpId.parent).map(_.fundName)
        }
        _ <- fundSubLoggingService.logEventEmailOpened(
          fundSubLpId = fundSubLpId,
          userId = receiverId,
          fundName = fundName,
          emailType = emailType
        )
      } yield ()
    }
  }

  def sendRequestSideLetterSignatureEmail(
    lpId: FundSubLpId,
    fileIds: Seq[FileId],
    signer: UserId,
    envelopeType: FundSubSignatureEnvelopeType,
    signatureRequestId: SignatureRequestId,
    message: String,
    actor: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    val fundSubId = lpId.parent
    sendFundSubEmail(
      fundSubId,
      FundSubSendSignatureRequestSignerEmailGenerate(
        fundSubLpId = lpId,
        fileIds = fileIds,
        signer = signer,
        actor = actor,
        message = message,
        envelopeType = envelopeType,
        signatureRequestId = signatureRequestId
      )(fundSubEmailUtils)
    )
      .map(_.internalId.headOption)
  }

  def sendGpAddedSideLetterFileEmail(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubId = lpId.parent,
      GpAddedSideLetterFileEmailGenerate(
        lpId,
        actor
      )(fundSubEmailUtils)
    ).map(_.internalId)
  }

  def sendGpCreatedNewSideLetterVersionEmail(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubId = lpId.parent,
      GpCreatedNewSideLetterVersionEmailGenerate(
        lpId,
        actor
      )(fundSubEmailUtils)
    ).map(_.internalId)
  }

  def sendGpMarkedSideLetterAsAgreedEmail(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubId = lpId.parent,
      GpMarkedSideLetterAsAgreedEmailGenerate(
        lpId,
        actor
      )(fundSubEmailUtils)
    ).map(_.internalId.headOption)
  }

  def sendGpMarkedSideLetterAsCompletedEmail(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubId = lpId.parent,
      GpMarkedSideLetterAsCompletedEmailGenerate(
        lpId,
        actor
      )(fundSubEmailUtils)
    ).map(_.internalId.headOption)
  }

  def sendGpSharedFirstSideLetterVersionEmail(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubId = lpId.parent,
      GpSharedFirstSideLetterVersionEmailGenerate(
        lpId,
        actor
      )(fundSubEmailUtils)
    ).map(_.internalId)
  }

  def sendLpAddedSideLetterFileEmail(
    lpId: FundSubLpId,
    receiver: UserId,
    actor: UserId
  ): Task[Seq[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubId = lpId.parent,
      LpAddedSideLetterFileEmailGenerate(
        lpId,
        actor,
        receiver
      )(fundSubEmailUtils)
    ).map(_.internalId)
  }

  def sendLpCreatedSideLetterVersionEmail(
    lpId: FundSubLpId,
    receiver: UserId,
    actor: UserId
  ): Task[Option[EmailSenderService.EmailWithInternalId]] = {
    sendFundSubEmail(
      fundSubId = lpId.parent,
      LpCreatedSideLetterVersionEmailGenerate(
        lpId,
        actor,
        receiver
      )(fundSubEmailUtils)
    ).map(_.internalId.headOption)
  }

  private def sendFundSubEmail(
    fundSubId: FundSubId,
    generateEmail: => GenerateEmail
  ) = {
    val storeEmailConfig = EmailSenderService.StoreEmailConfig(shouldStore = true, parentId = fundSubId)
    for {
      emailProviderModelOpt <- fundSubEmailUtils.getEnabledCustomSmtpServerConfig(fundSubId)
      emailProviderBuilderConfig = emailProviderModelOpt.fold(
        EmailProviderBuilderConfig.defaultBuilderConfig
      ) { config =>
        EmailProviderBuilderConfig.CustomizedSmtpBuilderConfig(config.id)
      }

      res <- emailSenderService.enqueue(
        generateEmail,
        fundSubId,
        emailProviderBuilderConfig,
        storeEmailConfig
      )
    } yield res
  }

}
