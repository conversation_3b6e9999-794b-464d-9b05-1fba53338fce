// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.gondor.server

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration

import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter
import zio.ZIO

import anduin.account.profile.UserProfileService
import anduin.asyncapiv2.AsyncApiService
import anduin.batchaction.BatchActionService
import anduin.dms.service.FileService
import anduin.fundsub.amlkyc.amlcheck.FundSubAmlCheckService
import anduin.fundsub.auditlog.FundSubAuditLogService
import anduin.fundsub.batchaction.FundSubBatchActionService
import anduin.fundsub.comment.FormCommentService
import anduin.fundsub.copy.FundSubCopyConfigService
import anduin.fundsub.dataexport.{FundSubExportService, FundSubFileDownloadService}
import anduin.fundsub.dataextract.service.FundSubSubdocDataExtractService
import anduin.fundsub.emaillog.FundSubEmailLogService
import anduin.fundsub.endpoint.FundSubEndpoints.*
import anduin.fundsub.endpoint.admin.{ComputeProfilePrefillDataForLpParams, LpProfileCheckInfo}
import anduin.fundsub.endpoint.common.GetUserTrackingResponse
import anduin.fundsub.endpoint.contact.*
import anduin.fundsub.endpoint.subscriptiondoc.GetSubscriptionVersionBasicInfoException
import anduin.fundsub.featureswitch.FundSubFeatureSwitchService
import anduin.fundsub.form.FundSubFormService
import anduin.fundsub.fundclose.FundSubCloseService
import anduin.fundsub.multiregion.FundSubMultiRegionService
import anduin.fundsub.participant.FundSubParticipantService
import anduin.fundsub.reviewpackage.ReviewPackageService
import anduin.fundsub.service.*
import anduin.fundsub.signature.{FundSubSignatureJvmUtils, FundSubSignatureService}
import anduin.fundsub.status.FundSubLpStatusHistoryService
import anduin.fundsub.subscriptiondoc.FundSubSubscriptionDocService
import anduin.fundsub.subscriptiondoc.FundSubSubscriptionQueryService.GenerateCleanDocDataStrategy
import anduin.fundsub.user.FundSubUserTrackingService
import anduin.investmententity.service.InvestmentEntityService
import anduin.investmententity.service.subscription.SubscriptionInvestmentEntityLinkIntegrationService
import anduin.tapir.endpoint.EmptyResponse
import anduin.tapir.server.AsyncAuthenticatedEndpointServer
import anduin.tapir.server.EndpointServer.{AsyncTapirServerService, TapirServerService}
import anduin.user.UserService
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.authorization.AuthorizationService
import com.anduin.stargazer.service.fundsub.*
import com.anduin.stargazer.service.fundsub.free.module.{
  FundSubLpInvitationService,
  ManageFundSubAdminM,
  ManageFundSubLpM
}
import com.anduin.stargazer.service.fundsub.generator.FundSubDataGeneratorService
import com.anduin.stargazer.service.fundsub.integration.DataRoomIntegrationAdminService
import com.anduin.stargazer.service.utils.ZIOUtils

final class FundSubEndpointServer(
  protected val backendConfig: GondorBackendConfig,
  dataRoomIntegrationAdminService: DataRoomIntegrationAdminService,
  protected val fundsubAdminService: FundSubAdminService,
  protected val fundsubLpService: FundSubLpService,
  protected val fundSubProtectedLinkService: FundSubProtectedLinkService,
  protected val fundsubLpDashboardService: FundSubLpDashboardService,
  protected val fundsubLpActivityLogService: FundSubLpActivityLogService,
  protected val fundSubContactService: FundSubContactService,
  protected val fundSubPermissionService: FundSubPermissionService,
  protected val fundSubWhiteLabelService: FundSubWhiteLabelService,
  protected val fundSubExportService: FundSubExportService,
  protected val formCommentService: FormCommentService,
  protected val taxFormService: TaxFormService,
  protected val supportingDocService: SupportingDocService,
  protected val fundSubDataGeneratorService: FundSubDataGeneratorService,
  protected val fundSubImportExportService: FundSubImportExportService,
  protected val autoPrefillService: AutoPrefillService,
  protected val investmentEntityService: InvestmentEntityService,
  protected val subscriptionInvestmentEntityLinkService: SubscriptionInvestmentEntityLinkIntegrationService,
  protected val fundSubSignatureService: FundSubSignatureService,
  protected val fundSubCloseService: FundSubCloseService,
  protected val fundSubSubscriptionDocService: FundSubSubscriptionDocService,
  protected val reviewPackageService: ReviewPackageService,
  protected val fundSubParticipantService: FundSubParticipantService,
  protected val supportingDocsHistoryService: SupportingDocsHistoryService,
  protected val fundSubFeatureSwitchService: FundSubFeatureSwitchService,
  protected val fundSubFileDownloadService: FundSubFileDownloadService,
  protected val fundSubLpStatusHistoryService: FundSubLpStatusHistoryService,
  protected val fundSubAuditLogService: FundSubAuditLogService,
  protected val fundSubFormService: FundSubFormService,
  protected val fundSubBatchActionService: FundSubBatchActionService,
  protected val fundSubEnvironmentIntegrationService: FundSubEnvironmentIntegrationService,
  protected val fundSubUserTrackingService: FundSubUserTrackingService,
  protected val fundSubCopyConfigService: FundSubCopyConfigService,
  protected val fundSubSubdocDataExtractService: FundSubSubdocDataExtractService,
  protected val fundSubAmlCheckService: FundSubAmlCheckService,
  protected val fundSubLpAutoPrefillService: FundSubLpAutoPrefillService,
  protected val fundSubMultiRegionService: FundSubMultiRegionService,
  override protected val interpreter: ArmeriaZioServerInterpreter[Any],
  override protected val asyncApiService: AsyncApiService,
  protected val userService: UserService
)(
  using val authorizationService: AuthorizationService,
  val fileService: FileService,
  val fundSubAdmin: ManageFundSubAdminM,
  val lpModule: ManageFundSubLpM,
  val userProfileService: UserProfileService,
  val auditLogService: FundSubAuditLogService,
  val fundSubEmailLogService: FundSubEmailLogService,
  val fundSubLpInvitationService: FundSubLpInvitationService,
  val batchActionService: BatchActionService
) extends AsyncAuthenticatedEndpointServer {

  private lazy val ExportTimeoutSeconds = 300L
  private lazy val ExportUsingDefaultTemplateTimeoutSeconds = 120L
  // timeout for APIs related to investor submission
  private lazy val SubmissionTimeoutSeconds = 120L
  private lazy val RequestCountersignTimeoutSeconds = 90L
  private lazy val SubmitForReviewTimeoutSeconds = 60L

  private lazy val adminServices = List(
    authRouteCatchError(updateFundName) { (params, ctx) =>
      fundsubAdminService.updateFundName(params, ctx.actor.userId)
    },
    authRouteCatchError(updateMarkAsNotApplicableSetting) { (params, ctx) =>
      fundsubAdminService.updateMarkAsNotApplicableSetting(params, ctx.actor.userId)
    },
    authRouteCatchError(updateDisableFundContactInInvestorWorkspaceSetting) { (params, ctx) =>
      fundsubAdminService.updateDisableFundContactInInvestorWorkspaceSetting(
        params.fundSubId,
        params.disableFundContactInInvestorWorkspace,
        ctx.actor.userId
      )
    },
    authRouteCatchError(updateInvestFromAdditionalEntitySetting) { (params, ctx) =>
      fundsubAdminService.updateInvestingFromAdditionalEntitySetting(params, ctx.actor.userId)
    },
    authRouteCatchError(updateCustomLpIdSetting) { (params, ctx) =>
      fundsubAdminService.updateCustomLpIdSetting(params, ctx.actor.userId)
    },
    authRouteCatchError(updateDownloadSubscriptionDocumentSetting) { (params, ctx) =>
      fundsubAdminService.updateDownloadSubscriptionDocumentSetting(params, ctx.actor.userId)
    },
    authRouteCatchError(updateRiaBannerVisibility) { (params, ctx) =>
      fundsubAdminService.updateRiaBannerVisibility(params, ctx.actor.userId)
    },
    authRouteCatchError(updateInactiveLpSetting) { (params, ctx) =>
      fundsubAdminService.updateInactiveLpSetting(params, ctx.actor.userId)
    },
    authRouteCatchError(updateFormCommentFundSettingSwitch) { (params, ctx) =>
      fundsubAdminService.updateFormCommentFundSettingSwitch(params, ctx.actor.userId)
    },
    authRouteCatchError(updateFormCommentInvestorDigestEmailSwitch) { (params, ctx) =>
      fundsubAdminService.updateFormCommentInvestorDigestEmailSwitch(params, ctx.actor.userId)
    },
    authRouteCatchError(updateFormCommentInvestorCanResolveSwitch) { (params, ctx) =>
      fundsubAdminService.updateFormCommentInvestorCanResolveSwitch(params, ctx.actor.userId)
    },
    authRouteCatchError(updateInactiveCommentSetting) { (params, ctx) =>
      fundsubAdminService.updateInactiveCommentSetting(params, ctx.actor.userId)
    },
    authRouteCatchError(updateFormCommentDigestEmailExceptionLps) { (params, ctx) =>
      fundsubAdminService.updateFormCommentDigestEmailExceptionLps(params, ctx.actor.userId)
    },
    authRouteCatchError(getFormCommentDigestEmailExceptionInfo) { (params, ctx) =>
      fundsubAdminService.getFormCommentDigestEmailExceptionInfo(params, ctx.actor.userId)
    },
    authRouteCatchError(updateCustomFundIdSetting) { (params, ctx) =>
      fundsubAdminService.updateCustomFundIdSetting(params, ctx.actor.userId)
    },
    authRouteCatchError(sendNewInvestorReportEmail) { (params, ctx) =>
      fundsubAdminService.sendNewInvestorsReportDemoEmail(params, ctx)
    },
    authRouteCatchError(sendDemoFormCommentsDigestEmail) { (fundSubId, ctx) =>
      fundsubAdminService.sendCommentsDigestDemoEmail(fundSubId, ctx.actor.userId)
    },
    authRouteCatchError(sendDemoFundActivitiesDigestEmail) { (params, ctx) =>
      fundsubAdminService.sendFundActivitiesDemoEmail(params, ctx.actor.userId)
    },
    authRouteCatchError(uploadRefDoc) { (params, ctx) =>
      fundsubAdminService.uploadRefDoc(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(renameRefDoc) { (params, ctx) =>
      fundsubAdminService.renameRefDoc(params, ctx.actor.userId)
    },
    authRouteCatchError(getFundSharedRefDocs) { (params, ctx) =>
      fundsubAdminService.getFundSharedRefDocs(params.fundSubId, ctx.actor.userId)
    },
    authRouteCatchError(saveSubscriptionDocsOrder) { (params, ctx) =>
      fundsubAdminService.saveSubscriptionDocsOrder(params, ctx.actor.userId)
    },
    authRouteCatchError(removeRefDoc) { (params, ctx) =>
      fundsubAdminService.removeRefDoc(params, ctx.actor.userId)
    },
    authRouteCatchError(
      requestCountersignSignature,
      timeout = FiniteDuration(RequestCountersignTimeoutSeconds, TimeUnit.SECONDS)
    ) { (params, ctx) =>
      fundsubAdminService.requestCountersignSignature(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(remindSigningRequestRestrictedFlow) { (params, ctx) =>
      fundsubAdminService.remindSigningRequestRestrictedFlow(params, ctx.actor.userId)
    },
    authRouteCatchError(
      signCountersignRequest,
      timeout = FundSubSignatureJvmUtils.CreateSignatureRequestEndpointTimeoutDuration
    ) { (params, ctx) =>
      fundsubAdminService.signCountersignRequest(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(getLpUnsignedDoc) { (params, ctx) =>
      fundsubAdminService.getLpUnsignedDoc(params, ctx.actor.userId)
    },
    authRouteCatchError(getLpFormAndValue) { (params, ctx) =>
      fundsubAdminService.getLpFormAndValue(params, ctx.actor.userId)
    },
    authRouteCatchError(batchSignCountersignRequest) { (params, ctx) =>
      fundsubAdminService.batchSignCountersignRequest(
        params.batchCountersignItems,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(getLpsBasicInfo) { (params, ctx) =>
      fundsubAdminService.getLpsBasicInfo(params, ctx.actor.userId)
    },
    authRouteCatchError(batchUpdateInvestorDataWithWorkflow) { (params, ctx) =>
      fundsubAdminService.batchUpdateInvestorDataWithWorkflow(params, ctx.actor.userId)
    },
    authRouteCatchError(queryLpDashboard) { (params, ctx) =>
      fundsubLpDashboardService.handleDashboardQuery(params, ctx.actor.userId)
    },
    authRouteCatchError(markLpAsNotNew) { (params, ctx) =>
      fundsubLpDashboardService.markLpAsNotNew(params, ctx.actor.userId)
    },
    authRouteCatchError(getLpDashboardItem) { (params, ctx) =>
      fundsubLpDashboardService.getLpDashboardItem(params, ctx.actor.userId)
    },
    authRouteCatchError(getLpDashboardItemList) { (params, ctx) =>
      fundsubLpDashboardService.getLpDashboardItemList(params, ctx.actor.userId)
    },
    authRouteCatchError(createLpTag) { (params, ctx) =>
      fundsubLpDashboardService.createLpTag(params, ctx.actor.userId)
    },
    authRouteCatchError(createLpTags) { (params, ctx) =>
      fundsubLpDashboardService.createLpTags(params.fundSubId, params.tags, ctx.actor.userId)
    },
    authRouteCatchError(getLpTagsInFund) { (params, ctx) =>
      fundsubLpDashboardService.getAllTagsInFundSub(params, ctx.actor.userId)
    },
    authRouteCatchError(editLpTag) { (params, ctx) =>
      fundsubLpDashboardService.editLpTag(params.tagId, params.newTagName, params.newTagColor, ctx.actor.userId)
    },
    authRouteCatchError(removeLpTag) { (params, ctx) =>
      fundsubLpDashboardService.removeLpTag(params, ctx.actor.userId)
    },
    authRouteCatchError(updateTagsOfLp) { (params, ctx) =>
      fundsubLpDashboardService.updateTagsOfLp(
        params.lpId,
        params.tags,
        ctx.actor.userId
      )
    },
    authRouteCatchError(getSingleLpActivityLog) { (params, ctx) =>
      fundsubLpActivityLogService.getSingleLpActivities(ctx.actor.userId, params)(fundSubPermissionService)
    },
    authRouteCatchError(getLpActivityLog) { (params, ctx) =>
      fundsubLpActivityLogService.getMultiLpActivities(ctx.actor.userId, params)(fundSubPermissionService)
    },
    authRouteCatchError(getNumberOfUnseenActivities) { (params, ctx) =>
      fundsubLpActivityLogService.getNumberOfUnseenActivities(params, ctx.actor.userId)
    },
    authRouteCatchError(markAsSeenLpActivities) { (params, ctx) =>
      fundsubLpActivityLogService.markAsSeenActivities(ctx.actor.userId, params)(fundSubPermissionService)
    },
    authRouteCatchError(getFaActivityLog) { (params, ctx) =>
      fundsubAdminService.getFundActivityLog(params, ctx.actor)
    },
    authRouteCatchError(markFaActivitiesAsSeen) { (params, ctx) =>
      fundsubAdminService.markActivitiesAsSeen(params, ctx.actor.userId)
    },
    authRouteCatchError(getDataRoomIntegrationInfo) { (params, ctx) =>
      fundsubAdminService.getDataRoomIntegrationInfo(params, ctx.actor.userId)
    },
    authRouteCatchError(getFormFilesInfo) { (params, ctx) =>
      fundsubAdminService.getFormFilesInfoForFundAdmin(params, ctx.actor.userId)
    },
    authRouteCatchError(getAdminTestFormData) { (params, ctx) =>
      fundsubAdminService.getFundSubAdminTestFormData(params, ctx.actor.userId)(
        using fundSubAdmin
      )
    },
    authRouteCatchError(saveAdminTestFormData) { (params, ctx) =>
      fundsubAdminService.saveFundSubAdminTestFormData(params, ctx.actor.userId)(
        using fundSubAdmin
      )
    },
    authRouteCatchError(resendBounceInvitation) { (params, ctx) =>
      fundsubAdminService.resendBounceInvitation(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(createProtectedLink) { (params, ctx) =>
      fundSubProtectedLinkService.createProtectedLink(params, ctx)
    },
    authRouteCatchError(editProtectedLink) { (params, ctx) =>
      fundSubProtectedLinkService.editProtectedLink(params, ctx)
    },
    authRouteCatchError(getFundSubEnvironmentStatus) { (params, ctx) =>
      fundsubAdminService.isEnterpriseLoginEnabledForFundSub(params.fundSubId, ctx.actor.userId)
    },
    authRouteCatchError(getEmailTemplates) { (params, ctx) =>
      fundsubLpService.getEmailTemplates(params, ctx.actor.userId)
    },
    authRouteCatchError(updateEmailTemplates) { (params, ctx) =>
      fundsubAdminService.updateEmailTemplates(params, ctx.actor.userId)
    },
    authRouteCatchError(adminRemoveLpCollaborator) { (params, ctx) =>
      fundsubAdminService.adminRemoveLpCollaborator(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(
      requestSupportingDocSignature,
      timeout = FundSubSignatureJvmUtils.CreateSignatureRequestEndpointTimeoutDuration
    ) { (params, ctx) =>
      fundsubAdminService.requestSupportingDocSignature(params, ctx)
    },
    authRouteCatchError(
      signAdditionalSignatureRequest,
      timeout = FundSubSignatureJvmUtils.CreateSignatureRequestEndpointTimeoutDuration
    ) { (params, ctx) =>
      fundsubAdminService.signAdditionalSignatureRequest(params, ctx)
    },
    authRouteCatchError(updateSharedDrLink) { (params, ctx) =>
      fundsubAdminService.updateSharedDataRoomLink(params, ctx.actor.userId)
    },
    authRouteCatchError(getFundAdminNotificationPreference) { (params, ctx) =>
      fundsubAdminService.getFundAdminNotificationPreference(params, ctx.actor.userId)
    },
    authRouteCatchError(updateFundAdminNotificationPreference) { (params, ctx) =>
      fundsubAdminService.updateFundAdminNotificationPreference(params, ctx.actor.userId)
    },
    authRouteCatchError(accessFormCompare) { (params, ctx) =>
      fundsubAdminService.accessFormCompare(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(compareForm) { (params, ctx) =>
      fundsubAdminService.compareForm(params, ctx.actor.userId)
    },
    authRouteCatchError(getCompareResult) { (params, ctx) =>
      fundsubAdminService.getCompareResult(params, ctx.actor.userId)
    },
    authRouteCatchError(checkLpFormValidation) { (params, ctx) =>
      fundsubAdminService.checkLpFormValidation(params, ctx.actor.userId)
    },
    authRouteCatchError(editFormVersionDescription) { (params, ctx) =>
      fundsubAdminService.editFormVersionDescription(params, ctx.actor.userId)
    },
    authRouteCatchError(getTableOfContentLpProgress) { (params, ctx) =>
      fundsubAdminService.getTableOfContentLpProgress(params, ctx.actor.userId)
    },
    authRouteCatchError(createDataRoomForIntegration) { (params, ctx) =>
      dataRoomIntegrationAdminService.createDataRoomForIntegration(params, ctx)
    },
    authRouteCatchError(convertOfflineToNormalOrder) { (params, ctx) =>
      fundsubAdminService.convertOfflineToNormalOrder(
        params.lpIds,
        params.lpEmailTemplate,
        ctx.actor.userId,
        ctx.getClientIP
      )
    },
    authRouteCatchError(convertOfflineToNormalOrderWithWorkflow) { (params, ctx) =>
      fundsubAdminService.convertOfflineToNormalOrderWithWorkflow(
        params.lpIds,
        params.lpEmailTemplate,
        ctx.actor.userId
      )
    },
    authRouteCatchError(approveLpSubscriptionDocument) { (lpId, ctx) =>
      fundsubLpService.approveSubscriptionDocument(lpId, ctx.actor.userId)
    },
    authRouteCatchError(getLpParticipantInfos) { (params, ctx) =>
      fundsubAdminService.getLpParticipantInfos(
        params.fundSubId,
        params.lpFilter,
        ctx.actor.userId
      )
    },
    authRouteCatchError(fundAdminSendCustomEmail) { (params, ctx) =>
      fundsubAdminService.fundAdminSendCustomEmail(
        params.fundSubId,
        params.recipients,
        params.ccRecipients,
        params.subject,
        params.message,
        params.attachments,
        params.sendMeCopy,
        params.primaryButtonCTAOpt,
        ctx.actor.userId
      )
    },
    authRouteCatchError(updateLpCommitment) { (params, ctx) =>
      fundsubAdminService.updateLpCommitment(
        params.fundSubLpId,
        params.investmentFundIdOpt,
        params.expectedCommitment,
        params.acceptedCommitment,
        ctx.actor.userId
      )
    },
    authRouteCatchError(verifyInvestmentEntityIsFromFormData) { (lpId, ctx) =>
      fundsubAdminService.verifyInvestmentEntityIsFromFormData(lpId, ctx.actor.userId)
    },
    authRouteCatchError(checkSeenAutoSelectedCommentsBanner) { (_, ctx) =>
      fundsubAdminService.checkSeenAutoSelectedCommentsBanner(ctx.actor.userId)
    },
    authRouteCatchError(markSeenAutoSelectedCommentsBanner) { (_, ctx) =>
      fundsubAdminService.markSeenAutoSelectedCommentsBanner(ctx.actor.userId)
    },
    authRouteCatchError(checkIfUserCanMakePublicCommentInFund) { (fundId, ctx) =>
      fundsubAdminService.checkIfFundManagerCanMakeShareComment(ctx.actor.userId, fundId)
    },
    authRouteCatchError(getUserTracking) { (_, ctx) =>
      fundSubUserTrackingService.getUserTracking(ctx.actor.userId).map(GetUserTrackingResponse(_))
    },
    authRouteCatchError(updateUserTracking) { (params, ctx) =>
      fundSubUserTrackingService.updateUserTracking(params, ctx.actor.userId)
    },
    authRouteCatchError(getRecentDashboardId) { (fundSubId, ctx) =>
      fundsubAdminService.getRecentDashboardId(fundSubId, ctx.actor.userId)
    },
    authRouteCatchError(setRecentDashboardId) { (params, ctx) =>
      fundsubAdminService.setRecentDashboardId(
        params.dashboardId,
        ctx.actor.userId
      )
    },
    authRouteCatchError(sendNotifyNewCommentsToInvestor) { (params, ctx) =>
      fundsubAdminService.sendNotifyNewCommentsToInvestor(params, ctx.actor.userId)
    },
    authRouteCatchError(getLpStatusActivityHistory) { (params, ctx) =>
      fundSubLpStatusHistoryService.getLpStatusHistory(params.fundSubLpId, ctx.actor.userId)
    },
    authRouteCatchError(getAuditLogData) { (params, ctx) =>
      fundSubAuditLogService.getAuditLogData(params, ctx)
    },
    authRouteCatchError(getEmailBodyContentString) { (params, ctx) =>
      fundSubAuditLogService.getEmailBodyContentString(params, ctx.actor.userId)
    },
    authRouteCatchError(exportAuditLogData) { (params, ctx) =>
      fundSubAuditLogService.exportAuditLogData(params, ctx)
    },
    authRouteCatchError(getFundMemberForFilter) { (params, ctx) =>
      fundSubAuditLogService.getFundMemberForFilter(params, ctx.actor.userId)
    },
    authRouteCatchError(getInvestorForFilter) { (params, ctx) =>
      fundSubAuditLogService.getInvestorForFilter(params, ctx.actor.userId)
    },
    authRouteCatchError(getInvestmentEntityForFilter) { (params, ctx) =>
      fundSubAuditLogService.getInvestmentEntityForFilter(params, ctx.actor.userId)
    },
    authRouteCatchError(getEmailLogData) { (params, ctx) =>
      fundSubEmailLogService.getEmailLogData(params, ctx.actor.userId)
    },
    authRouteCatchError(getLpDocumentsPageData) { (params, ctx) =>
      fundsubAdminService.getLpDocumentsPageData(params.lpId, ctx.actor.userId)
    },
    authRouteCatchError(getInviteLpData) { (fundSubId, ctx) =>
      fundsubAdminService.getInviteLpData(fundSubId, ctx.actor.userId)
    },
    authRouteCatchError(exportComment) { (params, ctx) =>
      fundsubAdminService.exportFundComment(params, ctx.actor.userId)
    },
    authRouteCatchError(getCommentExportTaskStatus) { (params, ctx) =>
      fundsubAdminService.getCommentExportTaskStatus(params, ctx.actor.userId)
    },
    authRouteCatchError(cancelCommentExportTask) { (params, ctx) =>
      fundsubAdminService.cancelCommentExportTask(params, ctx.actor.userId)
    },
    authRouteCatchError(getSmtpConfig) { (fundSubId, ctx) =>
      fundsubAdminService.getSmtpConfig(fundSubId, ctx.actor.userId)
    },
    authRouteCatchError(updateSmtpConfig) { (params, ctx) =>
      fundsubAdminService.updateSmtpConfig(params, ctx.actor.userId)
    },
    authRouteCatchError(sendTestEmailUsingCustomSmtp) { (params, ctx) =>
      fundsubAdminService.sendTestEmailUsingCustomSmtp(params, ctx.actor.userId)
    },
    authRouteCatchError(updateAllowFormEditPostSigning) { (params, ctx) =>
      fundsubAdminService
        .updateAllowFormEditPostSigning(
          params.fundSubId,
          params.isEnabled,
          ctx.actor.userId
        )
        .as(EmptyResponse())
    }
  )

  private lazy val contactServices = List(
    authRouteCatchError(viewedOnboardingModal) { (_, ctx) =>
      fundSubContactService.userViewedOnboardingModal(ctx.actor.userId)
    },
    authRouteCatchError(getContactAndGroup) { (params, ctx) =>
      for {
        fundContactAndGroup <- fundSubContactService.getFundContactAndGroup(params.fundSubId, ctx.actor.userId)
        viewedOnboarding <- fundSubContactService.getUserViewedOnboarding(ctx.actor.userId)
      } yield GetContactAndGroupResponse(
        contactInfo = fundContactAndGroup,
        viewedOnboarding = viewedOnboarding
      )
    },
    authRouteCatchError(createContactGroup) { (params, ctx) =>
      fundSubContactService
        .addContactsToGroup(
          params.fundSubId,
          ctx.actor.userId,
          params.name,
          params.customId,
          params.mainLpEmailOpt,
          params.contacts
        )
        .map(CreateContactGroupResponse.apply)
    },
    authRouteCatchError(editContactGroup) { (params, ctx) =>
      fundSubContactService.editGroup(
        params.groupId,
        params.name,
        params.customId,
        ctx.actor.userId,
        params.contactsToAdd,
        params.contactsToRemove
      )
    },
    authRouteCatchError(deleteContactGroup) { (params, ctx) =>
      fundSubContactService.deleteGroup(params.groupId, ctx.actor.userId)
    },
    authRouteCatchError(addContact) { (params, ctx) =>
      fundSubContactService
        .addLpContact(
          ctx.actor.userId,
          params.fundSubId,
          params.contacts
        )
        .unit
    },
    authRouteCatchError(editContact) { (params, ctx) =>
      fundSubContactService
        .editContact(
          ctx.actor.userId,
          params.fundSubId,
          params.contactId,
          params.contactData
        )
        .unit
    },
    authRouteCatchError(deleteContact) { (params, ctx) =>
      fundSubContactService.deleteContact(
        params.contactId,
        params.fundSubId,
        ctx.actor.userId
      )
    },
    authRouteCatchError(batchDeleteContactAndGroup) { (params, ctx) =>
      fundSubContactService.batchDeleteContactAndGroup(params, ctx.actor.userId)
    },
    authRouteCatchError(listContactsByEmailOrder) { (params, ctx) =>
      fundSubContactService.listContactsByEmailOrder(
        params.fundSubId,
        params.after,
        params.limit,
        ctx.actor.userId
      )
    },
    authRouteCatchError(queryContactsByEmail) { (params, ctx) =>
      fundSubContactService.queryContactsByEmail(
        params.fundSubId,
        params.emailQuery,
        ctx.actor.userId
      )
    },
    authRouteCatchError(queryContactsByName) { (params, ctx) =>
      fundSubContactService.queryContactsByName(
        params.fundSubId,
        params.nameQuery,
        ctx.actor.userId
      )
    },
    authRouteCatchError(getContactsWithSameEmailForGroups) { (params, ctx) =>
      fundSubContactService.getContactsWithSameEmailForGroups(
        params.fundSubId,
        params.groupIds,
        ctx.actor.userId
      )
    },
    authRouteCatchError(queryGroupByName) { (params, ctx) =>
      fundSubContactService.queryGroupByName(
        params.fundSubId,
        params.nameQuery,
        ctx.actor.userId
      )
    },
    authRouteCatchError(getAllGroups) { (params, ctx) =>
      fundSubContactService.getAllGroups(params.fundSubId, ctx.actor.userId)
    },
    authRouteCatchError(getGroupContacts) { (params, ctx) =>
      fundSubContactService.getGroupContacts(
        params.fundSubId,
        params.groupIds,
        ctx.actor.userId
      )
    }
  )

  private lazy val whiteLabelServices = List(
    authRouteCatchError(getWhiteLabel) { (params, ctx) =>
      fundSubWhiteLabelService.getWhiteLabelData(ctx.actor.userId, params.fundSubId)
    },
    authRouteCatchError(updateWhiteLabel) { (params, ctx) =>
      fundSubWhiteLabelService.updateWhiteLabel(params, ctx.actor.userId)
    },
    authRouteCatchError(removeWhiteLabelLogo) { (params, ctx) =>
      fundSubWhiteLabelService.removeWhiteLabelLogo(params, ctx.actor.userId)
    }
  )

  private lazy val copyConfigServices = List(
    authRouteCatchError(getCopyConfigForClient) { (params, ctx) =>
      fundSubCopyConfigService.getCopyConfigForClient(params, ctx.actor.userId)
    }
  )

  private lazy val advanceExportServices = List(
    authRouteCatchError(generateExcel) { (params, ctx) =>
      fundSubExportService.generateExcel(
        params,
        ctx.actor.userId
      )
    },
    authRouteCatchError(loadExportTemplateFromFile) { (params, ctx) =>
      fundSubExportService.loadExportTemplateFromFile(params, ctx.actor.userId)
    },
    authRouteCatchError(generateFundSubExportTemplateId) { (params, ctx) =>
      fundSubExportService.generateFundSubExportTemplateId(params, ctx.actor.userId)
    },
    authRouteCatchError(generateDefaultExportTemplate) { (params, ctx) =>
      fundSubExportService.generateDefaultExportTemplate(params, ctx.actor.userId)
    },
    authRouteCatchError(generateDefaultExportTemplateFile) { (params, ctx) =>
      fundSubExportService.generateDefaultExportTemplateFile(params, ctx.actor.userId)
    },
    authRouteCatchError(getFundSubExportFormsAndTemplates) { (params, ctx) =>
      fundSubExportService.getFundSubExportFormsAndTemplates(params.fundSubId, ctx.actor.userId)
    },
    authRouteCatchError(saveExportTemplateInfo) { (params, ctx) =>
      fundSubExportService.saveExportTemplateInfo(params, ctx.actor.userId)
    },
    authRouteCatchError(getAllTemplateInfosToExport) { (params, ctx) =>
      fundSubExportService.getAllTemplateInfosToExport(params, ctx.actor.userId)
    },
    authRouteCatchError(
      exportLpDataUsingDefaultTemplate,
      timeout = FiniteDuration(ExportUsingDefaultTemplateTimeoutSeconds, TimeUnit.SECONDS)
    ) { (params, ctx) =>
      fundSubExportService.exportLpDataUsingDefaultTemplate(
        params.fundSubId,
        params.lpIds,
        params.fileSuffix,
        ctx.actor.userId
      )
    },
    authRouteCatchError(exportLpDataUsingDefaultPdfTemplate) { (params, ctx) =>
      fundSubExportService.exportLpDataUsingDefaultPdfTemplate(
        params.fundSubId,
        params.lpIds,
        params.fileSuffix,
        ctx.actor.userId
      )
    },
    authRouteCatchError(
      exportLpDataUsingCustomTemplate,
      timeout = FiniteDuration(ExportTimeoutSeconds, TimeUnit.SECONDS)
    ) { (params, ctx) =>
      fundSubExportService.exportLpDataUsingCustomTemplate(
        params.exportTemplateId,
        params.lpIds,
        params.fileSuffix,
        ctx.actor.userId,
        params.forceCsvOutputOpt
      )
    },
    authRouteCatchError(getLpDocuments) { (params, ctx) =>
      fundSubExportService.getLpDocuments(
        params,
        ctx.actor.userId
      )
    },
    authRouteCatchError(downloadLpDocuments) { (params, ctx) =>
      fundSubExportService.getLpDocumentsDownloadUrl(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(generateDefaultImportTemplateFile) { (params, ctx) =>
      fundSubImportExportService.generateDefaultImportTemplateFile(params.fundSubId, ctx.actor.userId)
    }
  )

  // Template import
  private lazy val templateImportServices = List(
    authRouteCatchError(startImportWorkflow) { (params, ctx) =>
      fundSubImportExportService.startImportWorkflow(
        params.fundSubId,
        ctx.actor.userId,
        params.rawData,
        params.mappingIdInfo
      )
    },
    authRouteCatchError(getImportWorkflowStatus) { (params, ctx) =>
      fundSubImportExportService.getImportWorkflowStatus(params.dataImportId, ctx.actor.userId)
    },
    authRouteCatchError(getImportWorkflowItemResult) { (params, ctx) =>
      fundSubImportExportService.getImportItemResult(params.itemId, ctx.actor.userId)
    },
    authRouteCatchError(getFormTemplateMapping) { (params, ctx) =>
      fundSubImportExportService.getFormTemplateMapping(params.fundSubId, ctx.actor.userId)
    },
    authRouteCatchError(getFormAsaImportTemplate) { (params, ctx) =>
      fundSubImportExportService.generateFormSaImportTemplate(
        params,
        ctx.actor.userId
      )
    }
  )

  // Form commenting
  private lazy val formCommentServices = List(
    authRouteCatchError(createComment) { (params, ctx) =>
      formCommentService.createComment(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(addCommentReply) { (params, ctx) =>
      formCommentService.addCommentReply(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(resolveComment) { (params, ctx) =>
      formCommentService.resolveComment(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(assignComment) { (params, ctx) =>
      formCommentService.assignComment(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(unAssignComment) { (params, ctx) =>
      formCommentService.unAssignComment(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(reopenComment) { (params, ctx) =>
      formCommentService.reopenComment(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(updateComment) { (params, ctx) =>
      formCommentService.updateComment(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(updateCommentReply) { (params, ctx) =>
      formCommentService.updateCommentReply(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(deleteComment) { (params, ctx) =>
      formCommentService.deleteComment(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(flagComment) { (params, ctx) =>
      formCommentService.flagComment(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(deleteCommentReply) { (params, ctx) =>
      formCommentService.deleteCommentReply(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(getCommentThread) { (params, ctx) =>
      formCommentService.getCommentThread(params, ctx.actor.userId)
    },
    authRouteCatchError(getFundComments) { (params, ctx) =>
      formCommentService.getFundComments(params, ctx.actor.userId)
    },
    authRouteCatchError(getFundCommentAnchorPoints) { (params, ctx) =>
      formCommentService.getFundCommentAnchorPoints(params, ctx.actor.userId)
    },
    authRouteCatchError(getCommentMentions) { (params, ctx) =>
      formCommentService.getCommentMentions(params, ctx.actor.userId)
    },
    authRouteCatchError(getMentionsInfoInComment) { (params, ctx) =>
      formCommentService.getMentionsInfoInComment(params, ctx.actor.userId)
    },
    authRouteCatchError(getMentionableList) { (params, ctx) =>
      formCommentService.getMentionableList(params, ctx.actor.userId)
    },
    authRouteCatchError(getAssignableList) { (params, ctx) =>
      formCommentService.getAssignableList(params, ctx.actor.userId)
    },
    authRouteCatchError(getAssigneeList) { (params, ctx) =>
      formCommentService.getAssigneeList(params, ctx.actor.userId)
    },
    authRouteCatchError(getCommentAnchorPointAssignee) { (params, ctx) =>
      formCommentService.getCommentAnchorPointAssignee(params, ctx.actor.userId)
    },
    authRouteCatchError(getLpsMetaInfo) { (params, ctx) =>
      formCommentService.getLpsMetaInfo(params, ctx.actor.userId)
    },
    authRouteCatchError(getInboxTabSummary) { (params, ctx) =>
      formCommentService.getInboxTabSummary(params, ctx.actor.userId)
    },
    authRouteCatchError(getSingleLpComments) { (params, ctx) =>
      formCommentService.getLpComments(params, ctx.actor.userId)
    },
    authRouteCatchError(getGaiaFormCommentAllFieldsInfo) { (params, ctx) =>
      formCommentService.getGaiaFormCommentAllFieldsInfoForLp(params, ctx.actor)
    },
    authRouteCatchError(markWholeCommentThreadAsSeen) { (params, ctx) =>
      formCommentService.markWholeCommentThreadAsSeen(params, ctx.actor.userId)
    },
    authRouteCatchError(markFormCommentNotifSpaceAsSeen) { (params, ctx) =>
      formCommentService.markFormCommentNotifSpaceAsSeen(params, ctx.actor)
    },
    authRouteCatchError(getPublicThreadsFromIds) { (params, ctx) =>
      formCommentService.getPublicThreadsFromIds(params, ctx.actor.userId)
    },
    authRouteCatchError(checkIfUserCanMakePublicComment) { (lpId, ctx) =>
      formCommentService.checkIfUserCanMakePublicComment(lpId, ctx.actor.userId)
    },
    authRouteCatchError(getFundCommentNotification) { (params, ctx) =>
      formCommentService.getFundCommentNotification(params.fundId, ctx.actor.userId)
    },
    authRouteCatchError(getDataForCommentFilter) { (params, ctx) =>
      formCommentService.getDataForCommentFilter(params.fundSubId, ctx.actor.userId)
    }
  )

  private lazy val reviewPackageServices = List(
    authRouteCatchError(enableReviewPackage) { (params, ctx) =>
      fundsubAdminService.enableReviewPackage(params, ctx.actor.userId)
    },
    authRouteCatchError(disableReviewPackage) { (params, ctx) =>
      fundsubAdminService.disableReviewPackage(params, ctx.actor.userId)
    },
    authRouteCatchError(updateReviewPackageSetting) { (params, ctx) =>
      reviewPackageService.updateReviewPackageSetting(params, ctx.actor.userId)
    },
    authRouteCatchError(assignReviewers) { (params, ctx) =>
      fundsubAdminService.assignReviewers(params, ctx.actor.userId)
    },
    authRouteCatchError(removeReviewers) { (params, ctx) =>
      fundsubAdminService.removeReviewers(params, ctx.actor.userId)
    },
    authRouteCatchError(checkReviewerPermission) { (lpId, ctx) =>
      fundsubAdminService.checkReviewerPermission(lpId, ctx.actor.userId)
    }
  )

  private lazy val dataGeneratorServices = List(
    authRouteCatchError(
      generateInvestors,
      timeout = FiniteDuration(120, TimeUnit.SECONDS)
    ) { (params, ctx) =>
      fundSubDataGeneratorService.generateInvestors(params, ctx.actor.userId)
    }
  )

  private lazy val fundCloseServices = List(
    authRouteCatchError(getFundSubCloseData) { (fundSubId, ctx) =>
      fundSubCloseService.getFundSubCloseData(fundSubId, ctx.actor.userId)
    },
    authRouteCatchError(createFundSubClose) { (params, ctx) =>
      fundSubCloseService.createFundSubClose(
        params.fundSubId,
        params.name,
        params.customCloseId,
        params.closingDate,
        ctx.actor.userId,
        params.toDefaultClose
      )
    },
    authRouteCatchError(updateFundSubClose) { (params, ctx) =>
      fundSubCloseService.updateFundSubClose(params, ctx.actor.userId).unit
    },
    authRouteCatchError(deleteFundSubClose) { (params, ctx) =>
      fundSubCloseService.deleteFundSubClose(
        params.fundSubCloseId,
        params.fundSubCloseIdToTransfer,
        params.fundSubCloseIdToDefault,
        ctx.actor.userId
      )
    },
    authRouteCatchError(moveLpsToClose) { (params, ctx) =>
      fundSubCloseService.moveLpsToClose(
        params.lpIdsToMove,
        params.fundSubCloseIdToMove,
        ctx.actor.userId
      )
    }
  )

  private lazy val subscriptionDocServices = List(
    authRouteCatchError(getOriginalSubscriptionFormName) { (lpId, ctx) =>
      fundSubSubscriptionDocService.getOriginalSubscriptionFormName(lpId, ctx.actor.userId)
    },
    authRouteCatchError(getOriginalSubscriptionDocuments) { (lpId, ctx) =>
      fundSubSubscriptionDocService.getOriginalSubscriptionDocuments(lpId, ctx.actor.userId)
    },
    authRouteCatchError(getSubscriptionVersionBasicInfo) { (params, ctx) =>
      fundSubSubscriptionDocService.getSubscriptionVersionBasicInfo(params, ctx.actor.userId)
    },
    authRoute(getSubscriptionVersionBasicInfoWithErrorHandler) { (params, ctx) =>
      val task = fundSubSubscriptionDocService.getSubscriptionVersionBasicInfo(params, ctx.actor.userId)
      ZIOUtils.toTaskEither(task) {
        case ex: GetSubscriptionVersionBasicInfoException => ex
        case ex: Exception => GetSubscriptionVersionBasicInfoException.GenericServerError(ex.getMessage)
      }
    },
    authRouteCatchError(getSubscriptionVersionBasicInfoWithoutGeneratingCleanDocs) { (params, ctx) =>
      fundSubSubscriptionDocService.getSubscriptionVersionBasicInfo(
        params.lpId,
        params.versionIndex,
        GenerateCleanDocDataStrategy.ForceNotGenerate,
        ctx.actor.userId
      )
    },
    authRouteCatchError(getAllSubscriptionVersionsBasicInfo) { (params, ctx) =>
      fundSubSubscriptionDocService.getAllSubscriptionVersionBasicInfo(params, ctx.actor.userId)
    },
    authRouteCatchError(getNumberOfSignedVersion) { (lpId, ctx) =>
      fundSubSubscriptionDocService.getNumberOfSignedVersion(lpId, ctx.actor.userId)
    },
    authRouteCatchError(getSubscriptionVersionFormInfo) { (params, ctx) =>
      fundSubSubscriptionDocService.getSubscriptionVersionFormInfo(params, ctx.actor.userId)
    },
    authRouteCatchError(getSubscriptionDocSigningType) { (params, ctx) =>
      fundSubSubscriptionDocService.getSubscriptionDocSigningType(params, ctx.actor.userId)
    },
    authRouteCatchError(saveSubscriptionFormData) { (params, ctx) =>
      fundSubSubscriptionDocService.saveSubscriptionFormData(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(startEditingSubscriptionForm) { (params, ctx) =>
      fundSubSubscriptionDocService.startEditingSubscriptionForm(params, ctx.actor.userId)
    },
    authRouteCatchError(calculateAndUpdateSignatureStatus) { (lpId, ctx) =>
      fundSubSubscriptionDocService.calculateAndUpdateSignatureStatus(lpId, ctx.actor.userId)
    },
    authRouteCatchError(checkIfNeedToSignAgainAfterEditForm) { (lpId, ctx) =>
      fundSubSubscriptionDocService.checkIfNeedToSignAgainAfterEditForm(lpId, ctx.actor.userId)
    },
    authRouteCatchError(
      createSubscriptionDocSignatureRequest,
      timeout = FiniteDuration(SubmissionTimeoutSeconds, TimeUnit.SECONDS)
    ) { (params, ctx) =>
      fundSubSubscriptionDocService.createSubscriptionDocSignatureRequest(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(
      eSignSubscriptionDoc,
      timeout = FiniteDuration(SubmissionTimeoutSeconds, TimeUnit.SECONDS)
    ) { (params, ctx) =>
      fundSubSubscriptionDocService.eSignSubscriptionDoc(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(remindSubscriptionDocSignatureRequest) { (params, ctx) =>
      fundSubSubscriptionDocService.remindSubscriptionSignatureRequest(params, ctx.actor.userId)
    },
    authRouteCatchError(uploadSignedSubscriptionDoc) { (params, ctx) =>
      fundSubSubscriptionDocService.uploadSignedSubscriptionDoc(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(gpUploadSignedSubscriptionDoc) { (params, ctx) =>
      fundSubSubscriptionDocService
        .gpUploadSignedSubscriptionDoc(
          params,
          ctx.actor.userId,
          Some(ctx)
        )
        .unit
    },
    authRouteCatchError(gpUploadExecutedSubscription) { (params, ctx) =>
      fundSubSubscriptionDocService
        .gpUploadExecutedSubscription(
          params,
          ctx.actor.userId,
          Some(ctx)
        )
        .unit
    },
    authRouteCatchError(cancelCompletedSubscriptionSignature) { (lpId, ctx) =>
      fundSubSubscriptionDocService.cancelCompletedSubscriptionSignature(lpId, ctx.actor.userId)
    },
    authRouteCatchError(getToSubmitSubscriptionDocuments) { (params, ctx) =>
      fundSubSubscriptionDocService.getToSubmitSubscriptionDocuments(params, ctx.actor.userId)
    },
    authRouteCatchError(lpManuallySubmitPackage) { (params, ctx) =>
      fundSubSubscriptionDocService.lpManuallySubmitPackage(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(
      submitSubscriptionVersionForReview,
      timeout = FiniteDuration(SubmitForReviewTimeoutSeconds, TimeUnit.SECONDS)
    ) { (params, ctx) =>
      fundSubSubscriptionDocService.submitSubscriptionVersionForReview(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteCatchError(getRequestChangeInfo) { (lpId, ctx) =>
      fundSubSubscriptionDocService.getSubscriptionDocRequestChangesInfo(lpId, ctx.actor.userId)
    }
  )

  private lazy val participantServices = List(
    authRouteCatchError(addMainLpWorkflow) { (params, ctx) =>
      fundsubLpService.addMainLpsWorkflow(
        params.fundSubId,
        params.lpsToInvite,
        params.fundSubCloseIdToAdd,
        params.attachedDocs,
        params.lpOrderType,
        params.investorGroupIdOpt,
        ctx.actor.userId,
        ctx.getClientIP,
        params.importFromFundDataFirm
      )
    },
    authRouteCatchError(addMainLpWorkflowWithoutJointInfo) { (params, ctx) =>
      fundsubLpService.addMainLpsWorkflowWithoutJointInfo(
        params,
        ctx.actor.userId,
        ctx.getClientIP
      )
    },
    authRouteCatchError(removeBatchInvitation) { (fundSubBatchInvitationId, ctx) =>
      fundSubParticipantService.removeBatchInviteInvestor(fundSubBatchInvitationId, ctx.actor.userId)
    },
    authRouteCatchError(stopBatchInvitation) { (fundSubBatchInvitationId, ctx) =>
      fundSubParticipantService.stopBatchInviteInvestor(fundSubBatchInvitationId, ctx.actor.userId)
    },
    authRouteCatchError(getBatchInvitationInfo) { (fundSubBatchInvitationId, ctx) =>
      fundSubParticipantService.getBatchInviteInvestorInfo(fundSubBatchInvitationId, ctx.actor.userId)
    },
    authRouteCatchError(getAllBatchInvitationInfo) { (params, ctx) =>
      fundSubParticipantService.getAllBatchInvitationInfos(params.fundSubId, ctx.actor.userId)
    },
    authRouteCatchError(exportBatchInviteInvestorReport) { (params, ctx) =>
      fundSubParticipantService.exportBatchInviteInvestorReport(
        params.batchInvitationId,
        params.zoneId,
        ctx.actor.userId
      )
    },
    authRouteCatchError(getFundSubPointOfContact) { (params, ctx) =>
      fundSubParticipantService.getFundSubPointOfContact(params, ctx.actor.userId)
    },
    authRouteCatchError(setFundSubPointOfContactWithExternal) { (params, ctx) =>
      fundSubParticipantService.setFundSubPointOfContactWithExternal(params, ctx.actor.userId)
    },
    authRouteCatchError(getFundSubPointOfContactWithExternal) { (params, ctx) =>
      fundSubParticipantService.getFundSubPointOfContactWithExternal(params, ctx.actor.userId)
    },
    authRouteCatchError(getFundFormModel) { (params, ctx) =>
      autoPrefillService.getFundFormModel(params, ctx.actor.userId)
    },
    // Note: Need to return more data for LP profile related flow. Consider merging with
    // computePrefillDataFromPastLp as needed
    authRouteCatchError(computePrefillDataFromPastSubscription) { (params, ctx) =>
      fundSubLpAutoPrefillService.computeLpDataFromPastSubscription(params, ctx.actor.userId)
    },
    authRouteCatchError(computePrefillDataFromPastLp) { (params, ctx) =>
      autoPrefillService
        .computePrefillDataFromPastLp(
          params.pastLpId,
          Left(params.curFundId),
          ctx.actor.userId,
          params.isOverwritten,
          params.matchingInfoOpt
        )
    },
    authRouteCatchError(prefillLpDataFromPastSubscription) { (params, ctx) =>
      fundSubLpAutoPrefillService.prefillLpDataFromPastSubscription(
        params,
        ctx.actor.userId,
        Option(ctx)
      )
    },
    authRouteCatchError(saveProfileFromSubscription) { (params, ctx) =>
      fundSubLpAutoPrefillService.saveProfileFromSubscription(
        params,
        ctx.actor.userId,
        Option(ctx)
      )
    },
    authRouteCatchError(compareSubscriptionAndProfileData) { (params, ctx) =>
      fundSubLpAutoPrefillService.compareSubscriptionAndProfileData(
        params,
        ctx.actor.userId
      )
    },
    authRouteCatchError(compareMainSubscriptionAndLpProfileTemplateData) { (fundSubLpId, ctx) =>
      fundSubLpAutoPrefillService.compareSubscriptionAndProfileTemplateData(
        fundSubLpId,
        ctx.actor.userId
      )
    },
    authRouteCatchError(batchComputeNumPrefillableFieldsForLp) { (params, ctx) =>
      fundSubLpAutoPrefillService.batchComputeNumPrefillableFieldsForLp(
        params,
        ctx.actor.userId
      )
    },
    authRouteCatchError(computeProfilePrefillDataForLp) { (params, ctx) =>
      fundSubLpAutoPrefillService.computeLpDataFromProfile(
        params,
        ctx.actor.userId
      )
    },
    authRouteCatchError(prefillLpDataFromProfile) { (params, ctx) =>
      fundSubLpAutoPrefillService.prefillLpDataFromProfile(
        params,
        ctx.actor.userId,
        Option(ctx)
      )
    },
    authRouteCatchError(computeSupportingFormDataFromProfile) { (params, ctx) =>
      fundSubLpAutoPrefillService.computeSupportingFormDataFromProfile(
        params,
        ctx.actor.userId
      )
    },
    authRouteCatchError(prefillSupportingFormDataFromProfile) { (params, ctx) =>
      fundSubLpAutoPrefillService.prefillSupportingFormDataFromProfile(
        params,
        ctx.actor.userId
      )
    },
    authRouteCatchError(batchComputeNumPrefillableFieldsForSupportingForm) { (params, ctx) =>
      fundSubLpAutoPrefillService.batchComputeNumPrefillableFieldsForSupportingForm(
        params,
        ctx.actor.userId
      )
    },
    authRouteCatchError(clearLpFormValues) { (params, ctx) =>
      fundSubSubscriptionDocService.clearLpFormValues(
        params,
        ctx.actor.userId,
        Option(ctx)
      )
    },
    authRouteCatchError(checkLpAutofillConditions) { (params, ctx) =>
      for {
        associatedLinksRes <- params.investmentEntityLinkResOpt.fold(
          subscriptionInvestmentEntityLinkService.getAssociatedLinks(ctx.actor.userId, params.lpId)
        )(ZIO.succeed(_))
        res <- fundSubLpAutoPrefillService.checkLpAutofillConditions(
          params,
          associatedLinksRes.investmentEntityFullInfo.map(_.investmentEntityId),
          ctx.actor.userId
        )
        hasInvestorAccessProfile <- investmentEntityService.isUserHasInvestmentEntity(
          ctx.actor.userId
        ) // Check user have a profile or not
      } yield res.copy(
        hasValidProfileForAutofill = hasInvestorAccessProfile,
        hasEntityAccessOpt = associatedLinksRes.investmentEntityShortInfo.flatMap(_.hasInvestmentEntityAccess),
        checkProfileInfoOpt = associatedLinksRes.investmentEntityFullInfo.map(ieInfo =>
          LpProfileCheckInfo(
            investmentEntityId = ieInfo.investmentEntityId,
            investmentEntityName = ieInfo.entityName,
            profileId = ieInfo.masterProfileId,
            hasEmptyProfile = ieInfo.isMasterProfileEmpty,
            lastUpdate = ieInfo.lastUpdated
          )
        )
      )
    },
    authRouteCatchError(checkLpSaveDocumentsConditions) { (params, ctx) =>
      fundSubLpAutoPrefillService.checkLpSaveDocumentsConditions(
        params,
        ctx.actor.userId
      )
    }
  )

  private lazy val featureSwitchServices = List(
    authRouteCatchError(exportSwitchUsageMetrics) { (params, ctx) =>
      fundSubFeatureSwitchService.exportSwitchUsageMetrics(params.fundTypes, ctx.actor.userId)
    }
  )

  private lazy val environmentIntegrationServices = List(
    authRouteCatchError(getFundSubEnvironmentBaseUrlWithPrefixPath) { (fundSubId, ctx) =>
      fundSubEnvironmentIntegrationService.getFundSubEnvironmentBaseUrlWithPrefixPath(fundSubId, ctx.actor.userId)
    }
  )

  private lazy val amlCheckServices = List(
    authRouteCatchError(getAmlCheckByLpId) { (lpId, ctx) =>
      fundSubAmlCheckService.getAmlCheckByLpId(
        lpId,
        ctx.actor.userId
      )
    }
  )

  private lazy val multiRegionServices = List(
    authRouteCatchError(getFundSubMultiRegionInfo) { (_, ctx) =>
      fundSubMultiRegionService.getFundSubRegionInfos(ctx)
    }
  )

  lazy val services: List[TapirServerService] = List(
    adminServices,
    contactServices,
    whiteLabelServices,
    copyConfigServices,
    advanceExportServices,
    reviewPackageServices,
    templateImportServices,
    formCommentServices,
    dataGeneratorServices,
    fundCloseServices,
    subscriptionDocServices,
    participantServices,
    featureSwitchServices,
    environmentIntegrationServices,
    amlCheckServices,
    multiRegionServices
  ).flatten

  val asyncServices: List[AsyncTapirServerService] = List(
    asyncRouteCatchError(exportInvestorDashboardData) { (params, ctx) =>
      given lpTagUtilService: FundSubLpTagUtilService = fundSubExportService.fundSubLpTagUtilService
      fundsubAdminService.exportInvestorDashboardData(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    }
  )

}
