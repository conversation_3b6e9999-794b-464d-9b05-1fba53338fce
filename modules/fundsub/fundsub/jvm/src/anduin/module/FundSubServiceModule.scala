// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.module

import com.softwaremill.macwire.wire

import anduin.amlkyc.amlcheck.AmlCheckService
import anduin.announcement.AnnouncementService
import anduin.dashboard.service.DashboardService
import anduin.dataextract.integration.DataExtractIntegrationService
import anduin.dataroom.bot.DataRoomBotService
import anduin.dataroom.integration.service.{DataRoomExternalIntegrationService, DataRoomInternalIntegrationService}
import anduin.docrequest.service.{DocRequestService, DocSubmissionService, FormSubmissionService}
import anduin.environment.*
import anduin.evendim.client.{EvendimAdminClient, EvendimClient}
import anduin.funddata.integration.FundDataIntegrationService
import anduin.fundsub.activitylog.ActivityLogService
import anduin.fundsub.amlkyc.amlcheck.FundSubAmlCheckService
import anduin.fundsub.appnavigation.FundSubAppNavigatorService
import anduin.fundsub.auditlog.FundSubAuditLogService
import anduin.fundsub.batchaction.FundSubBatchActionService
import anduin.fundsub.comment.{
  CommentExportUtils,
  CommentServiceUtils,
  CommentTiDbDataUtils,
  FormCommentEmailService,
  FormCommentService
}
import anduin.fundsub.copy.FundSubCopyConfigService
import anduin.fundsub.customdata.CustomDataService
import anduin.fundsub.dashboard.{FundSubDashboardAdminService, FundSubDashboardDataService, FundSubDashboardService}
import anduin.fundsub.data.service.{FundSubDataService, LpDataService}
import anduin.fundsub.dataexport.selfservice.FundSubSelfServiceExportService
import anduin.fundsub.dataexport.workflow.InvestorDataExportWorkflowService
import anduin.fundsub.dataexport.{FundSubExportService, FundSubFileDownloadService}
import anduin.fundsub.dataextract.service.{
  FundSubDataExtractLogService,
  FundSubDataExtractRequestEmailService,
  FundSubSubdocDataExtractService
}
import anduin.fundsub.datainterface.FundSubDataInterfaceService
import anduin.fundsub.datalakeingestion.FundSubDataLakeIngestionService
import anduin.fundsub.duplicateconfig.FundSubDuplicateConfigService
import anduin.fundsub.email.FundSubDeprecatedSignatureEmailService
import anduin.fundsub.email.digest.newdocreviewready.NewSupportingDocReviewReadyReportService
import anduin.fundsub.email.digest.newdocupload.NewSupportingDocUploadReportService
import anduin.fundsub.email.generate.FundSubEmailUtils
import anduin.fundsub.email.template.FundSubEmailTemplateService
import anduin.fundsub.emaillog.FundSubEmailLogService
import anduin.fundsub.environment.{
  FundSubEnvironmentAuthenticationIntegrationServiceImpl,
  FundSubEnvironmentPolicyAdminService,
  FundSubEnvironmentPolicyMultiRegionClient,
  FundSubEnvironmentPolicyService
}
import anduin.fundsub.featureswitch.FundSubFeatureSwitchService
import anduin.fundsub.flow.FundSubLpFlowTaskHandler
import anduin.fundsub.form.{FormKafkaService, FundSubFormComparisonService, FundSubFormService}
import anduin.fundsub.fundclose.FundSubCloseService
import anduin.fundsub.globaldatabase.{FundSubGlobalDatabaseService, FundSubGlobalDatabaseSyncService}
import anduin.fundsub.group.{FundSubGroupMemberService, FundSubGroupService}
import anduin.fundsub.integplatform.FundSubIntegPlatformInternalService
import anduin.fundsub.integration.funddata.FundSubFundDataIntegrationService
import anduin.fundsub.integration.{
  FundSubExternalIntegrationService,
  FundSubExternalIntegrationServiceImpl,
  FundSubIntegPlatformExternalService,
  FundSubIntegPlatformExternalServiceImpl
}
import anduin.fundsub.investmententity.FundSubInvestmentEntityService
import anduin.fundsub.investorgroup.FundSubInvestorGroupService
import anduin.fundsub.multiregion.FundSubMultiRegionService
import anduin.fundsub.participant.FundSubParticipantService
import anduin.fundsub.report.{FundSubReportService, NewLpReportEmailService}
import anduin.fundsub.reviewpackage.ReviewPackageService
import anduin.fundsub.ria.{FundSubRiaIntegrationService, FundSubRiaService}
import anduin.fundsub.service.*
import anduin.fundsub.signature.batch.FundSubBatchCountersignService
import anduin.fundsub.signature.integration.FundSubSignatureIntegrationService
import anduin.fundsub.signature.oneenvelope.FundSubOneEnvelopeSignatureRequestService
import anduin.fundsub.signature.{FundSubDocusignWebhookEventConsumer, FundSubSignatureService}
import anduin.fundsub.simulator.{FundSubSimulatorOperationService, FundSubSimulatorService, FundSubSimulatorUtilsService}
import anduin.fundsub.status.FundSubLpStatusHistoryService
import anduin.fundsub.storageintegration.FundSubStorageIntegrationService
import anduin.fundsub.subscriptiondoc.review.FundSubSubscriptionDocReviewService
import anduin.fundsub.subscriptiondoc.{
  FundSubSubscriptionCountersignService,
  FundSubSubscriptionDocService,
  FundSubSubscriptionQueryService,
  FundSubSubscriptionSubmitService
}
import anduin.fundsub.uploadeddoc.LpUploadedDocSignatureRequestService
import anduin.fundsub.user.{FundSubUserService, FundSubUserTrackingService}
import anduin.fundsub.validator.FundSubPermissionValidatorService
import anduin.fundsub.view.FundSubViewService
import anduin.fundsub.{FundSubBotUser, FundSubLoggingService}
import anduin.integplatform.service.external.IntegPlatformExternalService
import anduin.investmententity.service.InvestorProfileFundSubIntegrationService
import anduin.review.service.ReviewService
import anduin.ria.integration.RiaExternalIntegrationService
import anduin.signature.integration.UserSignatureService
import com.anduin.stargazer.service.fundsub.*
import com.anduin.stargazer.service.fundsub.free.module.{
  FundSubLpInvitationService,
  ManageFundSubAdminM,
  ManageFundSubLpM
}
import com.anduin.stargazer.service.fundsub.generator.FundSubDataGeneratorService
import com.anduin.stargazer.service.fundsub.integration.*
import com.anduin.stargazer.service.fundsub.operation.{
  FundSubOperationDataExtractService,
  FundSubOperationDataExtractWebhookEventConsumer,
  FundSubOperationInvestorService,
  FundSubOperationService
}
import com.anduin.stargazer.service.fundsub.sideletter.SideLetterService

trait FundSubServiceModule
    extends GaiaServiceModule
    with DynamicFormServiceModule
    with BifrostServiceModule
    with InvestorProfileServiceModule
    with GreylinCoreServiceModule
    with IntegPlatformCoreServiceModule {

  def dataRoomBotService: DataRoomBotService
  def fundDataIntegrationService: FundDataIntegrationService
  def dataExtractIntegrationService: DataExtractIntegrationService
  def dataRoomInternalIntegrationService: DataRoomInternalIntegrationService
  def dataRoomExternalIntegrationService: DataRoomExternalIntegrationService
  def riaExternalIntegrationService: RiaExternalIntegrationService
  def userSignatureService: UserSignatureService
  def investorProfileFundSubIntegrationService: InvestorProfileFundSubIntegrationService
  def integPlatformExternalService: IntegPlatformExternalService

  // Keep at least 1 unwired service to combat zinc bug
  given fundSubBotUser: FundSubBotUser = FundSubBotUser(
    gondorBackendConfig.fundSubBot,
    userProfileService
  )

  given newLpReportEmailService: NewLpReportEmailService = wire[NewLpReportEmailService]
  given supportingDocsHistoryService: SupportingDocsHistoryService = wire[SupportingDocsHistoryService]
  given supportingDocReviewService: SupportingDocReviewService = wire[SupportingDocReviewService]
  given reviewService: ReviewService = wire[ReviewService]

  given fundSubCopyConfigService: FundSubCopyConfigService = wire[FundSubCopyConfigService]

  given fundSubPermissionService: FundSubPermissionService = wire[FundSubPermissionService]

  given fundSubPortalPermissionService: FundSubPortalPermissionService = wire[FundSubPortalPermissionService]

  given fundSubPermissionValidatorService: FundSubPermissionValidatorService = wire[FundSubPermissionValidatorService]

  given fundSubEmailUtils: FundSubEmailUtils = wire[FundSubEmailUtils]

  given fundSubDeprecatedEmailService: FundSubDeprecatedSignatureEmailService =
    wire[FundSubDeprecatedSignatureEmailService]

  given fundSubLpFlowTaskHandler: FundSubLpFlowTaskHandler = wire[FundSubLpFlowTaskHandler]

  given fundSubLoggingService: FundSubLoggingService = wire[FundSubLoggingService]

  given adminModule: ManageFundSubAdminM = wire[ManageFundSubAdminM]

  given lpModule: ManageFundSubLpM = wire[ManageFundSubLpM]

  given fundSubProtectedLinkService: FundSubProtectedLinkService = wire[FundSubProtectedLinkService]

  given fundsubAdminService: FundSubAdminService = wire[FundSubAdminService]

  given fundsubLpService: FundSubLpService = wire[FundSubLpService]

  given fundSubLpDashboardService: FundSubLpDashboardService = wire[FundSubLpDashboardService]

  given fundSubLpActivityLogService: FundSubLpActivityLogService = wire[FundSubLpActivityLogService]

  given fundSubLpTagUtilService: FundSubLpTagUtilService = wire[FundSubLpTagUtilService]

  given fundSubContactService: FundSubContactService = wire[FundSubContactService]

  given fundSubWhiteLabelService: FundSubWhiteLabelService = wire[FundSubWhiteLabelService]

  given commentTiDbDataUtils: CommentTiDbDataUtils = wire[CommentTiDbDataUtils]

  given commentServiceUtils: CommentServiceUtils = wire[CommentServiceUtils]

  given formCommentService: FormCommentService = wire[FormCommentService]

  given commentExportUtils: CommentExportUtils = wire[CommentExportUtils]

  given formCommentEmailService: FormCommentEmailService = wire[FormCommentEmailService]

  given fundSubOperationService: FundSubOperationService = wire[FundSubOperationService]

  given fundSubOperationDataExtractService: FundSubOperationDataExtractService = wire[FundSubOperationDataExtractService]

  given fundSubOperationInvestorService: FundSubOperationInvestorService =
    wire[FundSubOperationInvestorService]

  given fundSubDataGeneratorService: FundSubDataGeneratorService = wire[FundSubDataGeneratorService]

  given fundSubSignatureService: FundSubSignatureService = wire[FundSubSignatureService]

  given lpUploadedDocService: LpUploadedDocSignatureRequestService = wire[LpUploadedDocSignatureRequestService]

  given fundSubBatchCountersignService: FundSubBatchCountersignService = wire[FundSubBatchCountersignService]

  given docRequestService: DocRequestService = wire[DocRequestService]

  given formSubmissionService: FormSubmissionService = wire[FormSubmissionService]

  given docSubmissionService: DocSubmissionService = wire[DocSubmissionService]

  given newSupportingDocUploadReportService: NewSupportingDocUploadReportService =
    wire[NewSupportingDocUploadReportService]

  given newSupportingDocService: NewSupportingDocService = wire[NewSupportingDocService]

  given newSupportingDocReviewReadyReportService: NewSupportingDocReviewReadyReportService =
    wire[NewSupportingDocReviewReadyReportService]

  given newSupportingDocLoggingService: NewSupportingDocLoggingService = wire[NewSupportingDocLoggingService]

  given fundSubFundDataIntegrationService: FundSubFundDataIntegrationService =
    wire[FundSubFundDataIntegrationService]

  given fundSubExternalIntegrationService: FundSubExternalIntegrationService =
    wire[FundSubExternalIntegrationServiceImpl]

  given fundSubIntegPlatformExternalService: FundSubIntegPlatformExternalService =
    wire[FundSubIntegPlatformExternalServiceImpl]

  given fundSubDocusignWebhookEventConsumer: FundSubDocusignWebhookEventConsumer =
    wire[FundSubDocusignWebhookEventConsumer]

  // fundsub simulator
  given fundSubSimulatorService: FundSubSimulatorService = wire[FundSubSimulatorService]
  given fundSubSimulatorUtilsService: FundSubSimulatorUtilsService = wire[FundSubSimulatorUtilsService]
  given fundSubSimulatorOperationService: FundSubSimulatorOperationService = wire[FundSubSimulatorOperationService]

  given fundSubEmailService: FundSubEmailService = wire[FundSubEmailService]
  given fundSubFileDownloadService: FundSubFileDownloadService = wire[FundSubFileDownloadService]

  given fundSubStorageIntegrationService: FundSubStorageIntegrationService =
    wire[FundSubStorageIntegrationService]

  given fundSubCloseService: FundSubCloseService = wire[FundSubCloseService]

  given fundSubSubscriptionQueryService: FundSubSubscriptionQueryService =
    wire[FundSubSubscriptionQueryService]

  given fundSubSubscriptionSubmitService: FundSubSubscriptionSubmitService =
    wire[FundSubSubscriptionSubmitService]

  given fundSubSubscriptionDocReviewService: FundSubSubscriptionDocReviewService =
    wire[FundSubSubscriptionDocReviewService]

  given fundSubSubscriptionCountersignService: FundSubSubscriptionCountersignService =
    wire[FundSubSubscriptionCountersignService]

  given fundSubSubscriptionDocService: FundSubSubscriptionDocService = wire[FundSubSubscriptionDocService]

  given fundSubAuditLogService: FundSubAuditLogService = wire[FundSubAuditLogService]

  given fundSubEmailTemplateService: FundSubEmailTemplateService = wire[FundSubEmailTemplateService]

  given announcementService: AnnouncementService = wire[AnnouncementService]

  given fundSubEmailLogService: FundSubEmailLogService = wire[FundSubEmailLogService]

  given fundSubGroupService: FundSubGroupService = wire[FundSubGroupService]
  given fundSubGroupMemberService: FundSubGroupMemberService = wire[FundSubGroupMemberService]
  given fundSubInvestorGroupService: FundSubInvestorGroupService = wire[FundSubInvestorGroupService]

  given fundSubViewService: FundSubViewService = wire[FundSubViewService]

  given fundSubReportService: FundSubReportService = wire[FundSubReportService]

  given fundSubGlobalDatabaseService: FundSubGlobalDatabaseService = wire[FundSubGlobalDatabaseService]
  given fundSubGlobalDatabaseSyncService: FundSubGlobalDatabaseSyncService = wire[FundSubGlobalDatabaseSyncService]
  given fundSubMultiRegionService: FundSubMultiRegionService = wire[FundSubMultiRegionService]

  given investorDataExportWorkflowService: InvestorDataExportWorkflowService =
    wire[InvestorDataExportWorkflowService]

  given fundSubExportService: FundSubExportService = wire[FundSubExportService]

  given fundSubSelfServiceExportService: FundSubSelfServiceExportService = wire[FundSubSelfServiceExportService]

  given fundSubImportExportService: FundSubImportExportService = wire[FundSubImportExportService]

  given fundSubUserTrackingService: FundSubUserTrackingService = wire[FundSubUserTrackingService]

  given fundSubUserService: FundSubUserService = wire[FundSubUserService]

  given taxFormService: TaxFormService = wire[TaxFormService]

  given supportingDocService: SupportingDocService = wire[SupportingDocService]

  given reviewPackageService: ReviewPackageService = wire[ReviewPackageService]

  given docReviewEmailUtils: DocumentReviewEmailUtils = wire[DocumentReviewEmailUtils]

  given fundSubFormIntegrationService: FundSubFormIntegrationService = wire[FundSubFormIntegrationService]

  given autoPrefillService: AutoPrefillService = wire[AutoPrefillService]

  given fundSubLpAutoPrefillService: FundSubLpAutoPrefillService = wire[FundSubLpAutoPrefillService]

  given fundSubFormService: FundSubFormService = wire[FundSubFormService]

  given fundSubFormComparisonService: FundSubFormComparisonService = wire[FundSubFormComparisonService]

  given fundSubParticipantService: FundSubParticipantService = wire[FundSubParticipantService]

  given fundSubFeatureSwitchService: FundSubFeatureSwitchService = wire[FundSubFeatureSwitchService]

  given fundSubBatchActionService: FundSubBatchActionService = wire[FundSubBatchActionService]

  given fundSubDataExtractLogService: FundSubDataExtractLogService = wire[FundSubDataExtractLogService]

  given fundSubSubdocDataExtractService: FundSubSubdocDataExtractService = wire[FundSubSubdocDataExtractService]

  given fundSubDuplicateConfigService: FundSubDuplicateConfigService = wire[FundSubDuplicateConfigService]

  // activity log
  given fundSubLpStatusHistoryService: FundSubLpStatusHistoryService = wire[FundSubLpStatusHistoryService]

  given activityLogService: ActivityLogService = wire[ActivityLogService]

  given fundSubPublicApiOperationService: FundSubPublicApiOperationService =
    wire[FundSubPublicApiOperationService]

  given fundSubAmlCheckService: FundSubAmlCheckService =
    wire[FundSubAmlCheckService]

  given amlCheckService: AmlCheckService = wire[AmlCheckService]

  given dataRoomIntegrationAdminService: DataRoomIntegrationAdminService =
    wire[DataRoomIntegrationAdminService]

  given fundsubZapierService: FundSubZapierService = wire[FundSubZapierService]

  // New Dashboard
  given fundSubDataLakeIngestionService: FundSubDataLakeIngestionService = wire[FundSubDataLakeIngestionService]
  given fundSubGreylinDataService: FundSubGreylinDataService = wire[FundSubGreylinDataService]

  given evendimClient: EvendimClient = EvendimClient(
    gondorBackendConfig.dgraphConfig,
    jwt,
    evendimHttpClient,
    tracingEnvironment
  )

  given evendimAdminClient: EvendimAdminClient = EvendimAdminClient(
    gondorBackendConfig.dgraphConfig,
    jwt,
    evendimAdminHttpClient,
    tracingEnvironment
  )

  given customDataService: CustomDataService = wire[CustomDataService]
  given dashboardService: DashboardService = wire[DashboardService]

  given fundSubDashboardService: FundSubDashboardService = wire[FundSubDashboardService]
  given fundSubDashboardDataService: FundSubDashboardDataService = wire[FundSubDashboardDataService]
  given fundSubDashboardAdminService: FundSubDashboardAdminService = wire[FundSubDashboardAdminService]

  given formKafkaService: FormKafkaService = wire[FormKafkaService]

  given fundSubInvestmentEntityService: FundSubInvestmentEntityService = wire[FundSubInvestmentEntityService]

  given fundSubEnvironmentIntegrationService: FundSubEnvironmentIntegrationService =
    wire[FundSubEnvironmentIntegrationService]

  given fundSubEnvironmentPolicyMultiRegionClient: FundSubEnvironmentPolicyMultiRegionClient =
    wire[FundSubEnvironmentPolicyMultiRegionClient]

  given fundSubEnvironmentPolicyService: FundSubEnvironmentPolicyService =
    wire[FundSubEnvironmentPolicyService]

  given fundSubEnvironmentAuthenticationIntegrationService: FundSubEnvironmentAuthenticationIntegrationService =
    wire[FundSubEnvironmentAuthenticationIntegrationServiceImpl]

  given fundSubEnvironmentPolicyAdminService: FundSubEnvironmentPolicyAdminService =
    wire[FundSubEnvironmentPolicyAdminService]

  given fundSubDataExtractRequestEmailService: FundSubDataExtractRequestEmailService =
    wire[FundSubDataExtractRequestEmailService]

  given fundSubIntegrationGateService: FundSubIntegrationGateService = wire[FundSubIntegrationGateService]

  given fundSubRiaService: FundSubRiaService = wire[FundSubRiaService]

  given fundSubRiaIntegrationService: FundSubRiaIntegrationService = wire[FundSubRiaIntegrationService]

  given fundSubDataService: FundSubDataService = wire[FundSubDataService]

  given lpDataService: LpDataService = wire[LpDataService]

  given fundSubSignatureIntegrationService: FundSubSignatureIntegrationService = wire[FundSubSignatureIntegrationService]

  given fundSubLpInvitationService: FundSubLpInvitationService = wire[FundSubLpInvitationService]

  given sideLetterService: SideLetterService = wire[SideLetterService]

  given fundSubOneEnvelopeSignatureRequestService: FundSubOneEnvelopeSignatureRequestService =
    wire[FundSubOneEnvelopeSignatureRequestService]

  given fundSubIntegPlatformInternalService: FundSubIntegPlatformInternalService =
    wire[FundSubIntegPlatformInternalService]

  given fundSubOperationDataExtractWebhookEventConsumer: FundSubOperationDataExtractWebhookEventConsumer =
    wire[FundSubOperationDataExtractWebhookEventConsumer]

  given fundSubDataInterfaceService: FundSubDataInterfaceService = wire[FundSubDataInterfaceService]

  given fundSubAppNavigatorService: FundSubAppNavigatorService = wire[FundSubAppNavigatorService]
}
