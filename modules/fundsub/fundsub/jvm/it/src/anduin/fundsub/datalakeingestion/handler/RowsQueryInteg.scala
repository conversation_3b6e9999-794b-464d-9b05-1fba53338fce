// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import java.time.Instant

import fundsub.webhook.WebhookPayload
import zio.ZIO
import zio.test.*

import anduin.dashboard.model.DashboardColumnData
import anduin.fundsub.datalakeingestion.model.{AddOrUpdateFundParams, AddOrderParams, UserBasicInfo}
import anduin.id.TransactionId
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.id.fundsub.{FundSubCloseId, FundSubId}
import anduin.kafka.KafkaAsyncExecutor
import anduin.model.id.{FundSubIdFactory, FundSubLpIdFactory}
import anduin.protobuf.external.squants.CurrencyMessage
import anduin.protobuf.fundsub.LpOrderType
import anduin.testing.{FundSubBaseInteg, RandomUtils}

object RowsQueryInteg extends FundSubBaseInteg {

  private given payloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload] =
    fundSubDataLakeIngestionService.webhookPayloadAsyncExecutor

  private val fundId = FundSubIdFactory.unsafeRandomId(TransactionId.defaultValue.get)

  private val LP_COUNT = 123

  private lazy val lpIds = 0.until(LP_COUNT).map { _ =>
    FundSubLpIdFactory.unsafeRandomId(fundId)
  }

  private lazy val lpInfos = 0.until(LP_COUNT).map { _ =>
    generateUserBasicInfo()
  }

  private def generateUserBasicInfo() = UserBasicInfo(
    id = RandomUtils.generateRandomUserId,
    email = RandomUtils.generateEmailAddress,
    firstName = usFaker.name().firstName(),
    lastName = usFaker.name().lastName()
  )

  private val admin1Info = generateUserBasicInfo()
  private val admin2Info = generateUserBasicInfo()

  override def spec = suite("RowsQueryInteg")(
    test("Add Fund") {
      for {
        _ <- UpdateOrAddFundHandler.execute(
          AddOrUpdateFundParams(
            id = fundId,
            addAdmins = Seq(admin1Info, admin2Info),
            setCurrency = Option(CurrencyMessage.INR),
            setFundName = Option("Rows query test fund")
          )
        )
      } yield {
        assertCompletes
      }
    },
    test("Add Orders") {
      for {
        _ <- ZIO.foreach(lpIds.zipWithIndex) { case (lpId, index) =>
          AddOrderHandler.execute(
            AddOrderParams(
              lpIdOpt = Option(lpId),
              mainLp = lpInfos.lift(index),
              customId = s"custom ID #$index",
              collaborators = Seq.empty,
              investmentEntity = s"My entity #$index",
              commitmentAmounts = Seq.empty,
              closeIdOpt = Option(FundSubCloseId.defaultValue.get),
              lpOrderType = LpOrderType.NormalOrder,
              referenceDocs = Seq.empty,
              createdAt = Some(Instant.now),
              lastActivityAt = Some(Instant.now),
              tags = Seq.empty,
              status = None,
              initialFormProgress = 0.0,
              metadata = Map.empty,
              formFields = Seq.empty,
              advisorGroupIdOpt = Option(FundSubRiaGroupId.defaultValue.get),
              investorGroupIdOpt = Option(FundSubInvestorGroupId.defaultValue.get)
            )
          )
        }
      } yield assertCompletes
    },
    test("Execute rows query with small batch size") {
      for {
        rows <- dashboardService.getAllDashboardRows(
          fundId,
          DashboardColumnData.fixedColumns(Seq.empty).toList,
          batchSize = 7
        )
        _ <- ZIO.logInfo(s"rows size: ${rows.size}")
        rowIds = rows.map(_.rowMetadata.lpId)
      } yield {
        assertTrue(
          rows.size == LP_COUNT,
          rowIds.sortBy(_.idString) == lpIds.sortBy(_.idString)
        )
      }
    },
    test("Execute rows query with default batch size") {
      for {
        rowsSmallBatchSize <- dashboardService.getAllDashboardRows(
          fundId,
          DashboardColumnData.fixedColumns(Seq.empty).toList,
          batchSize = 7
        )
        rowsDefaultBatchSize <- dashboardService.getAllDashboardRows(
          fundId,
          DashboardColumnData.fixedColumns(Seq.empty).toList
        )
      } yield {
        assertTrue(
          rowsDefaultBatchSize.size == LP_COUNT,
          rowsSmallBatchSize.sortBy(_.rowMetadata.lpId.idString) == rowsDefaultBatchSize.sortBy(
            _.rowMetadata.lpId.idString
          )
        )
      }
    },
    test("Execute orders query with small batch size") {
      for {
        orders <- dashboardService.getFundOrders(
          fundId,
          batchSize = 7
        )
        mainLpEmails = orders.map(_.mainLp.emailStr).sorted
      } yield {
        assertTrue(
          mainLpEmails == lpInfos.map(_.email).sorted
        )
      }
    }
  ) @@ TestAspect.sequential

}
