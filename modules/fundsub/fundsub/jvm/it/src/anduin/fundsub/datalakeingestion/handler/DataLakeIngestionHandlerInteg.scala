// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import java.time.{Instant, LocalDate}

import fundsub.webhook.WebhookPayload
import zio.test.*
import zio.{Task, ZIO}

import anduin.dashboard.data
import anduin.dashboard.data.Documents
import anduin.dashboard.query.{CommonQueries, CustomDataQuery}
import anduin.evendim.model.datalake
import anduin.evendim.model.datalake.{CustomDataFilter, StringHashFilter}
import anduin.fundsub.datalakeingestion.model.*
import anduin.fundsub.signature.FundSubSignatureRequestStatusMessage
import anduin.id.TransactionId
import anduin.id.fundsub.FundSubLpFolderTypeId.FolderType
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.id.fundsub.{FundSubCloseId, FundSubId, FundSubLpId}
import anduin.id.review.ReviewConfigId
import anduin.id.signature.SignatureModuleId
import anduin.kafka.KafkaAsyncExecutor
import anduin.model.id.*
import anduin.protobuf.external.squants.CurrencyMessage
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus
import anduin.protobuf.fundsub.models.customdata.item.{DateTimeValue, StringValue}
import anduin.protobuf.fundsub.{FundSubDocType, LpOrderType}
import anduin.testing.{FundSubBaseInteg, RandomUtils}
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.util.date.DateCalculator

object DataLakeIngestionHandlerInteg extends FundSubBaseInteg {

  private given payloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload] =
    fundSubDataLakeIngestionService.webhookPayloadAsyncExecutor

  private val fundId = FundSubIdFactory.unsafeRandomId(TransactionId.defaultValue.get)
  private val lpId = FundSubLpIdFactory.unsafeRandomId(fundId)
  private val closeId1 = FundSubCloseIdFactory.unsafeRandomId(fundId)
  private val closeId2 = FundSubCloseIdFactory.unsafeRandomId(fundId)
  private val closeId3 = FundSubCloseIdFactory.unsafeRandomId(fundId)
  private val tag1 = FundSubLpTagIdFactory.unsafeRandomId(fundId)
  private val tag2 = FundSubLpTagIdFactory.unsafeRandomId(fundId)
  private val tag3 = FundSubLpTagIdFactory.unsafeRandomId(fundId)
  private val tag4 = FundSubLpTagIdFactory.unsafeRandomId(fundId)
  private val advisorGroupId1 = FundSubRiaGroupIdFactory.unsafeRandomId(fundId)
  private val advisorGroupId2 = FundSubRiaGroupIdFactory.unsafeRandomId(fundId)
  private val advisorGroupId3 = FundSubRiaGroupIdFactory.unsafeRandomId(fundId)
  private val investorGroupId1 = FundSubInvestorGroupIdFactory.unsafeRandomId(fundId)
  private val investorGroupId2 = FundSubInvestorGroupIdFactory.unsafeRandomId(fundId)
  private val investorGroupId3 = FundSubInvestorGroupIdFactory.unsafeRandomId(fundId)
  private val fundSubFolderId = FolderId.channelSystemFolderId(fundId)
  private val supportingDocFolderId = FolderId.channelSystemFolderId(FolderType.SupportingDoc(lpId))
  private val referenceDocFolderId = FolderId.channelSystemFolderId(FolderType.ReferenceDoc(lpId))
  private val referenceDoc1 = FileIdFactory.generate(referenceDocFolderId)
  private val referenceDoc2 = FileIdFactory.generate(referenceDocFolderId)
  private val referenceDoc3 = FileIdFactory.generate(referenceDocFolderId)
  private val importTemplateFile = FileIdFactory.generate(fundSubFolderId)
  private val exportTemplateFile = FileIdFactory.generate(fundSubFolderId)
  private val requiredTaxFormFile1 = FileIdFactory.generate(supportingDocFolderId)
  private val requiredTaxFormCertificateFile = FileIdFactory.generate(supportingDocFolderId)
  private val requiredTaxFormFile2 = FileIdFactory.generate(supportingDocFolderId)
  private val requiredDoc3 = FileIdFactory.generate(supportingDocFolderId)
  private val requiredDoc4 = FileIdFactory.generate(supportingDocFolderId)
  private val noteColumnId = CustomDataColumnIdFactory.unsafeRandomId(fundId)
  private val dateColumnId = CustomDataColumnIdFactory.unsafeRandomId(fundId)
  private val signatureRequestId1 = SignatureRequestIdFactory.unsafeRandomId(SignatureModuleId.defaultValue.get)
  private val signatureRequestId2 = SignatureRequestIdFactory.unsafeRandomId(SignatureModuleId.defaultValue.get)
  private val countersignatureRequestId1 = SignatureRequestIdFactory.unsafeRandomId(SignatureModuleId.defaultValue.get)
  private val countersignatureRequestId2 = SignatureRequestIdFactory.unsafeRandomId(SignatureModuleId.defaultValue.get)
  private val countersignatureRequestId3 = SignatureRequestIdFactory.unsafeRandomId(SignatureModuleId.defaultValue.get)
  private val rootTemplateId = DataTemplateIdFactory.unsafeRandomId
  private val templateVersionId1 = DataTemplateVersionIdFactory.unsafeRandomId(rootTemplateId)
  private val templateVersionId2 = DataTemplateVersionIdFactory.unsafeRandomId(rootTemplateId)
  private val reviewFlowId3 = ReviewFlowIdFactory.unsafeRandomId(lpId)
  private val reviewFlowId4 = ReviewFlowIdFactory.unsafeRandomId(lpId)
  private val reviewStepConfigId1 = ReviewStepConfigIdFactory.unsafeRandomId(ReviewConfigId.defaultValue.get)
  private val reviewStepConfigId2 = ReviewStepConfigIdFactory.unsafeRandomId(ReviewConfigId.defaultValue.get)

  private val importTemplate = Template(
    id = templateVersionId1,
    name = "Import Template",
    templateType = TemplateType.TEMPLATE_TYPE_IMPORT,
    lastChangeDescription = "First version",
    spreadsheetFile = Option(
      FileInfo(
        id = importTemplateFile,
        name = "sample_import.xsl",
        uploader = None,
        uploadedAt = None
      )
    ),
    columns = Seq(
      TemplateColumn(
        index = 0,
        title = "First name"
      ),
      TemplateColumn(
        index = 1,
        title = "Last name"
      ),
      TemplateColumn(
        index = 2,
        title = "Email"
      )
    ),
    createdBy = Option(generateUserBasicInfo()),
    createdAt = None,
    lastUpdatedAt = None
  )

  private val exportTemplate = Template(
    id = templateVersionId2,
    name = "Export Template",
    templateType = TemplateType.TEMPLATE_TYPE_EXPORT,
    lastChangeDescription = "Initial commit",
    spreadsheetFile = Option(
      FileInfo(
        id = exportTemplateFile,
        name = "sample_export.xsl",
        uploader = None,
        uploadedAt = None
      )
    ),
    columns = Seq(
      TemplateColumn(
        index = 0,
        title = "Commitment"
      ),
      TemplateColumn(
        index = 1,
        title = "Tax forms"
      ),
      TemplateColumn(
        index = 2,
        title = "Country"
      )
    ),
    createdBy = Option(generateUserBasicInfo()),
    createdAt = None,
    lastUpdatedAt = None
  )

  private def generateUserBasicInfo() = UserBasicInfo(
    id = RandomUtils.generateRandomUserId,
    email = RandomUtils.generateEmailAddress,
    firstName = usFaker.name().firstName(),
    lastName = usFaker.name().lastName()
  )

  private val admin1Info = generateUserBasicInfo()
  private val admin2Info = generateUserBasicInfo()

  private val lp1Info = generateUserBasicInfo()

  private val col1Info = generateUserBasicInfo()
  private val col2Info = generateUserBasicInfo()
  private val col3Info = generateUserBasicInfo()
  private val col4Info = generateUserBasicInfo()

  private val requiredDoc1 = RequiredDocInfo(
    id = "",
    name = "Doc 1",
    markedAsNa = true,
    submitted = false,
    submittedDocs = Seq[LpDocument](
      createLpDocument(
        requiredTaxFormFile1,
        "doc1.pdf",
        FundSubDocType.SupportingForm,
        lpId
      ),
      createLpDocument(
        requiredTaxFormCertificateFile,
        "doc1-2.pdf",
        FundSubDocType.SigningCertificate,
        lpId
      )
    )
  )

  private val requiredDoc2 =
    RequiredDocInfo(
      id = "",
      name = "Doc 2",
      markedAsNa = true,
      submitted = false,
      submittedDocs = Seq[LpDocument](
        createLpDocument(
          requiredTaxFormFile2,
          "doc2.pdf",
          FundSubDocType.SupportingForm,
          lpId
        )
      )
    )

  private def toDataLakeRequiredDoc(requiredDocInfo: RequiredDocInfo): data.RequiredDocInfo = {
    data.RequiredDocInfo(
      id = requiredDocInfo.id,
      name = requiredDocInfo.name,
      markedAsNa = requiredDocInfo.markedAsNa,
      submitted = requiredDocInfo.submitted,
      submittedDocs = requiredDocInfo.submittedDocs.flatMap { submittedDoc =>
        submittedDoc.file.map(_.id).map { fileId =>
          data.Documents.OrderDocument(
            id = submittedDoc.id,
            file = data.Documents.FileInfo(
              id = fileId,
              name = submittedDoc.file.map(_.name).getOrElse(""),
              uploader = None,
              uploadedAt = None
            ),
            docType = submittedDoc.docType,
            lpIdOpt = Option(submittedDoc.lpId)
          )
        }
      }.toList
    )
  }

  private def createDocsContainer(fileNames: List[(String, FundSubDocType)], lpId: FundSubLpId) = {
    LpDocumentsListContainer(
      docs = fileNames.map { case (fileName, docType) =>
        LpDocument(
          id = "",
          file = Option(
            FileInfo(
              id = FileIdFactory.generate(fundSubFolderId),
              name = fileName,
              uploader = None,
              uploadedAt = None
            )
          ),
          docType = docType,
          lpId = lpId
        )
      }
    )
  }

  private def createLpDocument(fileId: FileId, name: String, docType: FundSubDocType, lpId: FundSubLpId) = {
    LpDocument(
      id = "",
      file = Option(
        FileInfo(
          id = fileId,
          name = name,
          uploader = None,
          uploadedAt = None
        )
      ),
      docType = docType,
      lpId = lpId
    )
  }

  private def getRequiredDocs: Task[List[data.RequiredDocInfo]] = {
    val query = datalake.Query
      .getOrder(id = lpId.idString)(
        datalake.Order.supportingDocs()(
          CommonQueries.selectRequiredDoc
        )
      )
      .map(_.getOrElse(List.empty))
    evendimClient.query(query)
  }

  private def sanitizeRequiredDocs(requiredDocs: List[RequiredDocInfo]) = {
    requiredDocs
      .map { doc =>
        sanitizeRequiredDoc(
          toDataLakeRequiredDoc(doc)
        )
      }
      .sortBy(_.name)
  }

  private def sanitizeRequiredDoc(requiredDoc: data.RequiredDocInfo) = {
    requiredDoc.copy(
      id = "",
      submittedDocs = requiredDoc.submittedDocs
        .sortBy(_.file.name)
        .map(_.copy(id = ""))
    )
  }

  private val customDataQuery = datalake.CustomData.customValue()(
    CustomDataQuery.queryCustomValueObj
  )

  override def spec = suite("DataLakeIngestionHandlerInteg")(
    test("Update schema") {
      for {
        _ <- evendimAdminClient.updateSchema()
      } yield assertCompletes
    },
    test("Add Fund") {
      for {
        _ <- UpdateOrAddFundHandler.execute(
          AddOrUpdateFundParams(
            id = fundId,
            addAdmins = Seq(admin1Info, admin2Info),
            setCurrency = Option(CurrencyMessage.INR),
            setFundName = Option("Handler Test Fund")
          )
        )
      } yield {
        assertCompletes
      }
    },
    test("Verify added fund information") {
      for {
        currencyOpt <- evendimClient.query(
          datalake.Query.getFundSubscription(fundId.idString)(
            datalake.FundSubscription.currency
          )
        )
        admins <- evendimClient.query(
          datalake.Query.getFundSubscription(fundId.idString)(
            datalake.FundSubscription.admins()(
              datalake.User.email ~ datalake.User.firstName ~ datalake.User.lastName
            )
          )
        )
        fundName <- evendimClient.query(
          datalake.Query.getFundSubscription(fundId.idString)(
            datalake.FundSubscription.name
          )
        )
      } yield {
        assertTrue(
          currencyOpt == Option(CurrencyMessage.INR.name),
          admins.map(_.toSet) == Option(
            Set(
              (admin1Info.email, admin1Info.firstName, admin1Info.lastName),
              (admin2Info.email, admin2Info.firstName, admin2Info.lastName)
            )
          ),
          fundName.flatten == Option("Handler Test Fund")
        )
      }
    },
    test("Update Fund Name") {
      for {
        _ <- UpdateOrAddFundHandler.execute(
          AddOrUpdateFundParams(
            id = fundId,
            setFundName = Option("Updated name")
          )
        )
        fundName <- evendimClient.query(
          datalake.Query.getFundSubscription(fundId.idString)(
            datalake.FundSubscription.name
          )
        )
      } yield {
        assertTrue(fundName.flatten == Option("Updated name"))
      }
    },
    test("Add Custom Fund ID") {
      for {
        _ <- UpdateOrAddFundHandler.execute(
          AddOrUpdateFundParams(
            id = fundId,
            setCustomFundId = Option("Custom Fund ID")
          )
        )
        customFundId <- evendimClient.query(
          datalake.Query.getFundSubscription(fundId.idString)(
            datalake.FundSubscription.customFundId
          )
        )
      } yield {
        assertTrue(customFundId.flatten == Option("Custom Fund ID"))
      }
    },
    test("Update Custom Fund ID") {
      for {
        _ <- UpdateOrAddFundHandler.execute(
          AddOrUpdateFundParams(
            id = fundId,
            setCustomFundId = Option("New Custom Fund ID")
          )
        )
        newCustomFundId <- evendimClient.query(
          datalake.Query.getFundSubscription(fundId.idString)(
            datalake.FundSubscription.customFundId
          )
        )
      } yield {
        assertTrue(newCustomFundId.flatten == Option("New Custom Fund ID"))
      }
    },
    test("Add closes") {
      for {
        _ <- UpdateOrAddCloseHandler.execute(
          UpdateOrAddCloseParams(
            id = closeId1,
            name = "Close 1",
            customCloseId = "",
            targetClosingDate = Option(
              LocalDate.of(
                2022,
                9,
                22
              )
            )
          ),
          now = None
        )
        _ <- UpdateOrAddCloseHandler.execute(
          UpdateOrAddCloseParams(
            id = closeId2,
            name = "Close 2",
            customCloseId = "",
            targetClosingDate = Option(
              LocalDate.of(
                2023,
                10,
                20
              )
            )
          ),
          now = None
        )
        closes <- evendimClient
          .query(
            datalake.Query.getFundSubscription(fundId.idString)(
              datalake.FundSubscription.closes()(
                datalake.Close.id ~ datalake.Close.name
              )
            )
          )
          .map(_.getOrElse(List.empty))
          .map(_.toSet)
      } yield {
        assertTrue(closes == Set(closeId1.idString -> "Close 1", closeId2.idString -> "Close 2"))
      }
    },
    test("Update closes") {
      for {
        _ <- UpdateOrAddCloseHandler.execute(
          UpdateOrAddCloseParams(
            id = closeId2,
            name = "Close 2 edited",
            customCloseId = "ABC-123",
            targetClosingDate = None
          ),
          now = None
        )
        (name, customCloseId) <- evendimClient
          .query(
            datalake.Query.getClose(id = closeId2.idString)(
              datalake.Close.name ~ datalake.Close.customCloseId
            )
          )
          .map(_.get)
      } yield {
        assertTrue(name == "Close 2 edited", customCloseId == Option("ABC-123"))
      }
    },
    test("Remove close") {
      for {
        _ <- RemoveCloseHandler.execute(
          RemoveCloseParams(closeId1),
          now = None
        )
        closes <- evendimClient
          .query(
            datalake.Query.getFundSubscription(fundId.idString)(
              datalake.FundSubscription.closes()(
                datalake.Close.id ~ datalake.Close.name
              )
            )
          )
          .map(_.getOrElse(List.empty))
          .map(_.toSet)
      } yield {
        assertTrue(closes == Set(closeId2.idString -> "Close 2 edited"))
      }
    },
    test("Add tags") {
      for {
        _ <- UpdateOrAddTagHandler.execute(
          UpdateOrAddTagParams(
            id = tag1,
            name = "Tag 1",
            createdAt = Option(DateCalculator.instantNow),
            creator = Option(
              UserBasicInfo(
                id = userBM2,
                email = userBM2Email,
                firstName = userInfoBM2.firstName,
                lastName = userInfoBM2.lastName
              )
            )
          ),
          now = None
        )
        _ <- UpdateOrAddTagHandler.execute(
          UpdateOrAddTagParams(
            id = tag2,
            name = "Tag 2",
            createdAt = Option(DateCalculator.instantNow),
            creator = Option(
              UserBasicInfo(
                id = userBM2,
                email = userBM2Email,
                firstName = userInfoBM2.firstName,
                lastName = userInfoBM2.lastName
              )
            )
          ),
          now = None
        )
        tags <- evendimClient
          .query(
            datalake.Query.getFundSubscription(fundId.idString)(
              datalake.FundSubscription.tags()(
                datalake.Tag.id ~ datalake.Tag.name
              )
            )
          )
          .map(_.getOrElse(List.empty))
          .map(_.toSet)
      } yield { assertTrue(tags == Set(tag1.idString -> "Tag 1", tag2.idString -> "Tag 2")) }
    },
    test("Update tag") {
      for {
        _ <- UpdateOrAddTagHandler.execute(
          UpdateOrAddTagParams(
            id = tag1,
            name = "Tag 1 edited",
            creator = None,
            createdAt = None
          ),
          now = None
        )
        nameOpt <- evendimClient.query(
          datalake.Query.getTag(id = tag1.idString)(
            datalake.Tag.name
          )
        )
      } yield {
        assertTrue(nameOpt.getOrElse("") == "Tag 1 edited")
      }
    },
    test("Remove tag") {
      for {
        _ <- RemoveTagHandler.execute(
          RemoveTagParams(tag2),
          now = None
        )
        tags <- evendimClient
          .query(
            datalake.Query.getFundSubscription(fundId.idString)(
              datalake.FundSubscription.tags()(
                datalake.Tag.id ~ datalake.Tag.name
              )
            )
          )
          .map(_.getOrElse(List.empty))
          .map(_.toSet)
      } yield {
        assertTrue(tags == Set(tag1.idString -> "Tag 1 edited"))
      }
    },
    test("Add advisor groups") {
      for {
        _ <- UpdateOrAddAdvisorGroupHandler.execute(
          UpdateOrAddAdvisorGroupParams(
            id = advisorGroupId1,
            name = "Advisor group 1"
          ),
          now = None
        )
        _ <- UpdateOrAddAdvisorGroupHandler.execute(
          UpdateOrAddAdvisorGroupParams(
            id = advisorGroupId2,
            name = "Advisor group 2"
          ),
          now = None
        )
        advisorGroups <- evendimClient
          .query(
            datalake.Query.getFundSubscription(fundId.idString)(
              datalake.FundSubscription.advisorEntities()(
                datalake.AdvisorEntity.id ~ datalake.AdvisorEntity.name
              )
            )
          )
          .map(_.getOrElse(List.empty))
          .map(_.toSet)
      } yield {
        assertTrue(
          advisorGroups == Set(
            advisorGroupId1.idString -> "Advisor group 1",
            advisorGroupId2.idString -> "Advisor group 2"
          )
        )
      }
    },
    test("Update advisor groups") {
      for {
        _ <- UpdateOrAddAdvisorGroupHandler.execute(
          UpdateOrAddAdvisorGroupParams(
            id = advisorGroupId2,
            name = "Advisor group 2 edited"
          ),
          now = None
        )
        name <- evendimClient
          .query(datalake.Query.getAdvisorEntity(id = advisorGroupId2.idString)(datalake.AdvisorEntity.name))
          .map(_.get)
      } yield {
        assertTrue(name == "Advisor group 2 edited")
      }
    },
    test("Remove advisor group") {
      for {
        _ <- RemoveAdvisorGroupHandler.execute(
          RemoveAdvisorGroupParams(advisorGroupId1),
          now = None
        )
        advisorGroups <- evendimClient
          .query(
            datalake.Query.getFundSubscription(fundId.idString)(
              datalake.FundSubscription.advisorEntities()(
                datalake.AdvisorEntity.id ~ datalake.AdvisorEntity.name
              )
            )
          )
          .map(_.getOrElse(List.empty))
          .map(_.toSet)
      } yield {
        assertTrue(advisorGroups == Set(advisorGroupId2.idString -> "Advisor group 2 edited"))
      }
    },
    test("Add investor groups") {
      for {
        _ <- UpdateOrAddInvestorGroupHandler.execute(
          UpdateOrAddInvestorGroupParams(
            id = investorGroupId1,
            name = "Investor group 1"
          ),
          now = None
        )
        _ <- UpdateOrAddInvestorGroupHandler.execute(
          UpdateOrAddInvestorGroupParams(
            id = investorGroupId2,
            name = "Investor group 2"
          ),
          now = None
        )
        investorGroups <- evendimClient
          .query(
            datalake.Query.getFundSubscription(fundId.idString)(
              datalake.FundSubscription.investorGroups()(
                datalake.InvestorGroup.id ~ datalake.InvestorGroup.name
              )
            )
          )
          .map(_.getOrElse(List.empty))
          .map(_.toSet)
      } yield {
        assertTrue(
          investorGroups == Set(
            investorGroupId1.idString -> "Investor group 1",
            investorGroupId2.idString -> "Investor group 2"
          )
        )
      }
    },
    test("Update investor groups") {
      for {
        _ <- UpdateOrAddInvestorGroupHandler.execute(
          UpdateOrAddInvestorGroupParams(
            id = investorGroupId2,
            name = "Investor group 2 edited"
          ),
          now = None
        )
        name <- evendimClient
          .query(datalake.Query.getInvestorGroup(id = investorGroupId2.idString)(datalake.InvestorGroup.name))
          .map(_.get)
      } yield {
        assertTrue(name == "Investor group 2 edited")
      }
    },
    test("Remove investor group") {
      for {
        _ <- RemoveInvestorGroupHandler.execute(
          RemoveInvestorGroupParams(investorGroupId1),
          now = None
        )
        investorGroups <- evendimClient
          .query(
            datalake.Query.getFundSubscription(fundId.idString)(
              datalake.FundSubscription.investorGroups()(
                datalake.InvestorGroup.id ~ datalake.InvestorGroup.name
              )
            )
          )
          .map(_.getOrElse(List.empty))
          .map(_.toSet)
      } yield {
        assertTrue(investorGroups == Set(investorGroupId2.idString -> "Investor group 2 edited"))
      }
    },
    test("Add order") {
      for {
        _ <- AddOrderHandler.execute(
          AddOrderParams(
            lpIdOpt = Option(lpId),
            mainLp = Option(lp1Info),
            customId = "",
            collaborators = Seq(col1Info, col2Info),
            investmentEntity = "My entity",
            commitmentAmounts = Seq(
              SubFundCommitmentAmountMessage(
                id = InvestmentFundIdFactory.unsafeRandomId(fundId), // TODO: should generate before this
                expectedCommitment = Some(123000.45f),
                submittedCommitment = None,
                acceptedCommitment = None
              )
            ),
            closeIdOpt = Option(FundSubCloseId.defaultValue.get),
            lpOrderType = LpOrderType.NormalOrder,
            referenceDocs = Seq.empty,
            createdAt = Some(Instant.now),
            lastActivityAt = Some(Instant.now),
            tags = Seq(tag1),
            status = None,
            initialFormProgress = 0.0,
            metadata = Map(
              "field-1" -> "value-1",
              "field-2" -> "value-2"
            ),
            formFields = Seq.empty,
            advisorGroupIdOpt = Option(FundSubRiaGroupId.defaultValue.get),
            investorGroupIdOpt = Option(FundSubInvestorGroupId.defaultValue.get)
          )
        )
      } yield assertCompletes
    },
    test("Verify order information") {
      for {
        values <- evendimClient
          .query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.mainContact()(datalake.User.firstName ~ datalake.User.lastName) ~
                datalake.Order.investmentEntity ~
                datalake.Order.contacts()(datalake.User.email) ~
                datalake.Order.tags()(datalake.Tag.id) ~
                datalake.Order.metadata()(
                  datalake.MetadataField.fieldName
                    .map(_.getOrElse("")) ~ datalake.MetadataField.value.map(_.getOrElse(""))
                )
            )
          )
          .map(_.get)
      } yield {
        val (firstName, lastName, investmentEntity, collaborators, tags, metadataOpt) = values
        val metadataFields = metadataOpt.getOrElse(List.empty).sortBy(_._1)

        assertTrue(
          firstName == lp1Info.firstName,
          lastName == lp1Info.lastName,
          investmentEntity == "My entity",
          collaborators.toSet == Set(col1Info.email, col2Info.email),
          tags.toSet == Set(tag1.idString),
          metadataFields == List(
            "field-1" -> "value-1",
            "field-2" -> "value-2"
          )
        )
      }
    },
    suite("Update basic information")(
      test("Edit investment entity name") {
        for {
          _ <- UpdateOrderBasicInfoHandler.execute(
            UpdateOrderBasicInfoParams(
              lpIdOpt = Option(lpId),
              setInvestmentEntityName = Option("New entity name")
            )
          )
          entityName <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.investmentEntity
            )
          )
        } yield assertTrue(entityName == Option("New entity name"))

      },
      test("Edit orderType") {
        for {
          _ <- UpdateOrderBasicInfoHandler.execute(
            UpdateOrderBasicInfoParams(
              lpIdOpt = Option(lpId),
              setOrderType = Option(LpOrderType.OfflineOrder)
            )
          )
          entityNameOpt <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.investmentEntity
            )
          )
          orderTypeOpt <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.orderType
            )
          )
        } yield {
          assertTrue(
            // double check if existing field got override
            entityNameOpt == Option("New entity name"),
            orderTypeOpt == Option(datalake.OrderType.OFFLINE)
          )
        }
      },
      test("Edit status") {
        for {
          _ <- UpdateOrderBasicInfoHandler.execute(
            UpdateOrderBasicInfoParams(
              lpIdOpt = Option(lpId),
              setLpStatus = Option(LpStatus.LPCompleted)
            )
          )
          orderTypeOpt <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.orderType
            )
          )
          statusOpt <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.status
            )
          )
        } yield {
          assertTrue(
            orderTypeOpt == Option(datalake.OrderType.OFFLINE),
            statusOpt == Option(datalake.OrderStatus.COMPLETED)
          )
        }
      },
      test("Set close") {
        for {
          _ <- UpdateOrAddCloseHandler.execute(
            UpdateOrAddCloseParams(
              id = closeId3,
              name = "Close 3",
              customCloseId = "",
              targetClosingDate = Option(
                LocalDate.of(
                  2023,
                  10,
                  20
                )
              )
            ),
            now = None
          )
          _ <- UpdateOrderBasicInfoHandler.execute(
            UpdateOrderBasicInfoParams(
              lpIdOpt = Option(lpId),
              setClose = Option(closeId3)
            )
          )
          closeNameOpt <- evendimClient
            .query(
              datalake.Query.getOrder(id = lpId.idString)(
                datalake.Order.close()(
                  datalake.Close.name
                )
              )
            )
            .map(_.flatten)
        } yield assertTrue(closeNameOpt == Option("Close 3"))
      },
      suite("Advisor group")(
        test("Set advisor group") {
          for {
            _ <- UpdateOrAddAdvisorGroupHandler.execute(
              UpdateOrAddAdvisorGroupParams(
                id = advisorGroupId3,
                name = "Advisor group 3"
              ),
              now = None
            )
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                setAdvisorGroup = Option(advisorGroupId3)
              )
            )
            advisorGroupNameOpt <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order.advisorEntity()(
                    datalake.AdvisorEntity.name
                  )
                )
              )
              .map(_.flatten)
          } yield assertTrue(advisorGroupNameOpt == Option("Advisor group 3"))
        },
        test("Remove advisor group") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                removeAdvisorGroup = Option(advisorGroupId3)
              )
            )
            advisorGroupNameOpt <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order.advisorEntity()(
                    datalake.AdvisorEntity.name
                  )
                )
              )
              .map(_.flatten)
          } yield assertTrue(advisorGroupNameOpt.isEmpty)
        }
      ),
      suite("Investor group")(
        test("Set investor group") {
          for {
            _ <- UpdateOrAddInvestorGroupHandler.execute(
              UpdateOrAddInvestorGroupParams(
                id = investorGroupId3,
                name = "Investor group 3"
              ),
              now = None
            )
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                setInvestorGroup = Option(investorGroupId3)
              )
            )
            investorGroupNameOpt <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order.investorGroup()(
                    datalake.InvestorGroup.name
                  )
                )
              )
              .map(_.flatten)
          } yield assertTrue(investorGroupNameOpt == Option("Investor group 3"))
        },
        test("Remove investor group") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                removeInvestorGroup = Option(investorGroupId3)
              )
            )
            investorGroupNameOpt <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order.investorGroup()(
                    datalake.InvestorGroup.name
                  )
                )
              )
              .map(_.flatten)
          } yield assertTrue(investorGroupNameOpt.isEmpty)
        }
      ),
      suite("Tags")(
        test("Add tag 3 and tag 4 to fund") {
          for {
            _ <- UpdateOrAddTagHandler.execute(
              UpdateOrAddTagParams(
                id = tag3,
                name = "Tag 3",
                createdAt = Option(DateCalculator.instantNow),
                creator = Option(
                  UserBasicInfo(
                    id = userBM2,
                    email = userBM2Email,
                    firstName = userInfoBM2.firstName,
                    lastName = userInfoBM2.lastName
                  )
                )
              ),
              now = None
            )
            _ <- UpdateOrAddTagHandler.execute(
              UpdateOrAddTagParams(
                id = tag4,
                name = "Tag 4",
                createdAt = Option(DateCalculator.instantNow),
                creator = Option(
                  UserBasicInfo(
                    id = userBM2,
                    email = userBM2Email,
                    firstName = userInfoBM2.firstName,
                    lastName = userInfoBM2.lastName
                  )
                )
              ),
              now = None
            )
            tags <- evendimClient
              .query(
                datalake.Query.getFundSubscription(fundId.idString)(
                  datalake.FundSubscription.tags()(
                    datalake.Tag.id ~ datalake.Tag.name
                  )
                )
              )
              .map(_.getOrElse(List.empty))
              .map(_.toSet)
          } yield {
            assertTrue(
              tags == Set(
                tag1.idString -> "Tag 1 edited",
                tag3.idString -> "Tag 3",
                tag4.idString -> "Tag 4"
              )
            )
          }
        },
        test("Add tag 1 and tag 3 to the order") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                addTags = Seq(tag1, tag3)
              )
            )
            addedTags <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order.tags()(datalake.Tag.name)
                )
              )
              .map(_.getOrElse(List.empty))
          } yield {
            assertTrue(addedTags.toSet == Set("Tag 1 edited", "Tag 3"))
          }
        },
        test("Add and remove tags in the same update") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                addTags = Seq(tag4),
                removeTags = Seq(tag1)
              )
            )
            addedTags <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order.tags()(datalake.Tag.name)
                )
              )
              .map(_.getOrElse(List.empty))
          } yield {
            assertTrue(addedTags.toSet == Set("Tag 3", "Tag 4"))
          }
        }
      ),
      suite("Reference docs")(
        test("Add docs") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                addReferenceFiles = Seq(
                  FileInfo(
                    id = referenceDoc1,
                    name = "File 1",
                    uploader = None,
                    uploadedAt = None
                  ),
                  FileInfo(
                    id = referenceDoc2,
                    name = "File 2",
                    uploader = None,
                    uploadedAt = None
                  )
                )
              )
            )
            addedFiles <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order.referenceDocs()(datalake.File.name)
                )
              )
              .map(_.getOrElse(List.empty))
          } yield {
            assertTrue(addedFiles.toSet == Set("File 1", "File 2"))
          }
        },
        test("Add docs and remove docs in a single update") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                addReferenceFiles = Seq(
                  FileInfo(
                    id = referenceDoc3,
                    name = "File 3",
                    uploader = None,
                    uploadedAt = None
                  )
                ),
                removeReferenceFiles = Seq(referenceDoc2)
              )
            )
            addedFiles <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order.referenceDocs()(datalake.File.name)
                )
              )
              .map(_.getOrElse(List.empty))
          } yield {
            assertTrue(addedFiles.toSet == Set("File 1", "File 3"))
          }
        }
      ),
      suite("Lp Documents")(
        test("Add Filed Form, Lp signed doc, two tax forms and 3 certificates as submitted docs") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                setSubmittedDocsList = Option(
                  createDocsContainer(
                    List(
                      "filled_form.pdf" -> FundSubDocType.FilledForm,
                      "lp_signed_doc.pdf" -> FundSubDocType.LpSignedDoc,
                      "tax_form_1.pdf" -> FundSubDocType.SupportingForm,
                      "tax_form_2.pdf" -> FundSubDocType.SupportingForm,
                      "certificate_1.pdf" -> FundSubDocType.SigningCertificate,
                      "certificate_2.pdf" -> FundSubDocType.SigningCertificate,
                      "certificate_3.pdf" -> FundSubDocType.SigningCertificate
                    ),
                    lpId
                  )
                )
              )
            )
            addedFiles <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order
                    .submittedDocs()(
                      datalake.OrderDocument
                        .file()(datalake.File.name) ~ datalake.OrderDocument.documentType
                    )
                    .map(_.flatMap { case (nameOpt, docTypeOpt) =>
                      nameOpt.flatMap { fileName =>
                        docTypeOpt.map { docType =>
                          fileName -> docType
                        }
                      }
                    })
                )
              )
              .map(_.getOrElse(List.empty))
          } yield {
            assertTrue(
              addedFiles.sortBy(_._1) == List(
                "filled_form.pdf" -> datalake.DocType.FilledForm,
                "lp_signed_doc.pdf" -> datalake.DocType.InvestorSignedDoc,
                "tax_form_1.pdf" -> datalake.DocType.SupportingDoc,
                "tax_form_2.pdf" -> datalake.DocType.SupportingDoc,
                "certificate_1.pdf" -> datalake.DocType.SigningCertificate,
                "certificate_2.pdf" -> datalake.DocType.SigningCertificate,
                "certificate_3.pdf" -> datalake.DocType.SigningCertificate
              ).sortBy(_._1)
            )
          }
        },
        test("Add 2 uploaded countersigned docs and 2 certificates as uploaded countersigned docs") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                setUploadedCountersignedDocsList = Option(
                  createDocsContainer(
                    List(
                      "countersigned_doc_1.pdf" -> FundSubDocType.CounterSignedDoc,
                      "countersigned_doc_2.pdf" -> FundSubDocType.CounterSignedDoc,
                      "certificate_1.pdf" -> FundSubDocType.SigningCertificate,
                      "certificate_2.pdf" -> FundSubDocType.SigningCertificate
                    ),
                    lpId
                  )
                )
              )
            )
            addedFiles <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order
                    .uploadedCountersignedDocs()(
                      datalake.OrderDocument
                        .file()(datalake.File.name) ~ datalake.OrderDocument.documentType
                    )
                    .map(_.flatMap { case (nameOpt, docTypeOpt) =>
                      nameOpt.flatMap { fileName =>
                        docTypeOpt.map { docType =>
                          fileName -> docType
                        }
                      }
                    })
                )
              )
              .map(_.getOrElse(List.empty))
          } yield {
            assertTrue(
              addedFiles.sortBy(_._1) == List(
                "countersigned_doc_1.pdf" -> datalake.DocType.CountersignedDoc,
                "countersigned_doc_2.pdf" -> datalake.DocType.CountersignedDoc,
                "certificate_1.pdf" -> datalake.DocType.SigningCertificate,
                "certificate_2.pdf" -> datalake.DocType.SigningCertificate
              ).sortBy(_._1)
            )
          }
        },
        test("Submit countersigned docs (and clear uploaded countersigned docs)") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                setCountersignedDocsList = Option(
                  createDocsContainer(
                    List(
                      "countersigned_doc_1.pdf" -> FundSubDocType.CounterSignedDoc,
                      "countersigned_doc_2.pdf" -> FundSubDocType.CounterSignedDoc,
                      "certificate_1.pdf" -> FundSubDocType.SigningCertificate,
                      "certificate_2.pdf" -> FundSubDocType.SigningCertificate
                    ),
                    lpId
                  )
                ),
                setUploadedCountersignedDocsList = Option(LpDocumentsListContainer(docs = Seq.empty))
              )
            )
            uploadedCountersignedDocs <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order
                    .uploadedCountersignedDocs()(
                      datalake.OrderDocument
                        .file()(datalake.File.id)
                    )
                )
              )
              .map(_.getOrElse(List.empty))
            addedFiles <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order
                    .countersignedDocs()(
                      datalake.OrderDocument
                        .file()(datalake.File.name) ~ datalake.OrderDocument.documentType
                    )
                    .map(_.flatMap { case (nameOpt, docTypeOpt) =>
                      nameOpt.flatMap { fileName =>
                        docTypeOpt.map { docType =>
                          fileName -> docType
                        }
                      }
                    })
                )
              )
              .map(_.getOrElse(List.empty))
          } yield {
            assertTrue(
              uploadedCountersignedDocs.size == 0,
              addedFiles.sortBy(_._1) == List(
                "countersigned_doc_1.pdf" -> datalake.DocType.CountersignedDoc,
                "countersigned_doc_2.pdf" -> datalake.DocType.CountersignedDoc,
                "certificate_1.pdf" -> datalake.DocType.SigningCertificate,
                "certificate_2.pdf" -> datalake.DocType.SigningCertificate
              ).sortBy(_._1)
            )
          }
        }
      ),
      suite("Countersignature requests")(
        test("Add 2 countersignature requests") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                addCountersignatureRequests = Seq(
                  SignatureRequestBasicInfo(
                    id = countersignatureRequestId1,
                    status = FundSubSignatureRequestStatusMessage.InProgress,
                    creator = Option(
                      UserBasicInfo(
                        id = userBM2,
                        email = userBM2Email,
                        firstName = userInfoBM2.firstName,
                        lastName = userInfoBM2.lastName
                      )
                    ),
                    createdAt = None
                  ),
                  SignatureRequestBasicInfo(
                    id = countersignatureRequestId2,
                    status = FundSubSignatureRequestStatusMessage.InProgress,
                    creator = Option(
                      UserBasicInfo(
                        id = userBM2,
                        email = userBM2Email,
                        firstName = userInfoBM2.firstName,
                        lastName = userInfoBM2.lastName
                      )
                    ),
                    createdAt = None
                  )
                )
              )
            )
            requests <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order.countersignSignatureRequests()(
                    datalake.SignatureRequest.id ~ datalake.SignatureRequest.status
                  )
                )
              )
              .map(_.getOrElse(List.empty))

          } yield {
            assertTrue(
              requests.sortBy(_._1) == List(
                countersignatureRequestId1.idString -> datalake.SignatureRequestStatus.SENT,
                countersignatureRequestId2.idString -> datalake.SignatureRequestStatus.SENT
              ).sortBy(_._1)
            )
          }
        },
        test("Update countersignature requests 1") {
          for {
            _ <- UpdateOrAddOrderSignatureRequestsHandler.execute(
              UpdateOrAddOrderSignatureRequestsParams(
                lpIdOpt = Option(lpId),
                requests = Seq(
                  SignatureRequestBasicInfo(
                    id = countersignatureRequestId1,
                    status = FundSubSignatureRequestStatusMessage.Canceled,
                    creator = None,
                    createdAt = None
                  )
                ),
                requestType = SignatureRequestType.CountersignatureRequest
              )
            )
            requests <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order.countersignSignatureRequests()(
                    datalake.SignatureRequest.id ~ datalake.SignatureRequest.status
                  )
                )
              )
              .map(_.getOrElse(List.empty))

          } yield {
            assertTrue(
              requests.toSet == Set(
                countersignatureRequestId2.idString -> datalake.SignatureRequestStatus.SENT
              )
            )
          }
        },
        test("Remove countersignature request 2 and add countersignature request 3") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                addCountersignatureRequests = Seq(
                  SignatureRequestBasicInfo(
                    id = countersignatureRequestId3,
                    status = FundSubSignatureRequestStatusMessage.Completed,
                    creator = Option(
                      UserBasicInfo(
                        id = userBM2,
                        email = userBM2Email,
                        firstName = userInfoBM2.firstName,
                        lastName = userInfoBM2.lastName
                      )
                    ),
                    createdAt = None
                  )
                ),
                removeCountersignatureRequest = Seq(countersignatureRequestId2)
              )
            )
            requests <- evendimClient
              .query(
                datalake.Query.getOrder(id = lpId.idString)(
                  datalake.Order.countersignSignatureRequests()(
                    datalake.SignatureRequest.id ~ datalake.SignatureRequest.status
                  )
                )
              )
              .map(_.getOrElse(List.empty))

          } yield {
            assertTrue(
              requests == List(
                countersignatureRequestId3.idString -> datalake.SignatureRequestStatus.COMPLETED
              )
            )
          }
        }
      )
    ),
    suite("Add Custom Data")(
      test("User BM2 add new content to note column") {
        for {
          _ <- UpdateOrAddCustomDataHandler.execute(
            UpdateOrAddCustomDataParams(
              lpIdOpt = Option(lpId),
              customDataColumnId = noteColumnId,
              customData = StringValue("Note content"),
              creator = Option(
                UserBasicInfo(
                  id = userBM2,
                  email = userBM2Email,
                  firstName = userInfoBM2.firstName,
                  lastName = userInfoBM2.lastName
                )
              ),
              createdAt = Option(Instant.now()),
              columnName = "Note"
            )
          )
          noteColumnInfo <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.customData()(
                customDataQuery.map(
                  _.flatMap(_.asMessage.sealedValue.stringValue.map(_.value))
                ) ~ datalake.CustomData.dataColumnId
              )
            )
          )
        } yield {
          assertTrue(noteColumnInfo == Option(List(Some("Note content") -> noteColumnId.idString)))
        }
      },
      test("User BM1 add content to date column") {
        for {
          currentInstant <- ZIO.attempt(Instant.now())
          _ <- UpdateOrAddCustomDataHandler.execute(
            UpdateOrAddCustomDataParams(
              lpIdOpt = Option(lpId),
              customDataColumnId = dateColumnId,
              customData = DateTimeValue(Some(currentInstant)),
              creator = Option(
                UserBasicInfo(
                  id = userBM1,
                  email = userBM1Email,
                  firstName = userInfoBM1.firstName,
                  lastName = userInfoBM1.lastName
                )
              ),
              createdAt = Option(Instant.now()),
              columnName = "Date"
            )
          )
          dateColumnInfo <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.customData()(
                customDataQuery.map(
                  _.flatMap(_.asMessage.sealedValue.dateTimeValue.flatMap(_.value))
                ) ~ datalake.CustomData.dataColumnId
              )
            )
          )
        } yield {
          assertTrue(
            dateColumnInfo.map(_.toSet) == Option(
              Set(
                None -> noteColumnId.idString,
                Some(currentInstant) -> dateColumnId.idString
              )
            )
          )
        }
      },
      test("User BM2 update date in date column") {
        for {
          currentInstant <- ZIO.attempt(Instant.now())
          _ <- UpdateOrAddCustomDataHandler.execute(
            UpdateOrAddCustomDataParams(
              lpIdOpt = Option(lpId),
              customDataColumnId = dateColumnId,
              customData = DateTimeValue(Some(currentInstant)),
              creator = Option(
                UserBasicInfo(
                  id = userBM2,
                  email = userBM2Email,
                  firstName = userInfoBM2.firstName,
                  lastName = userInfoBM2.lastName
                )
              ),
              createdAt = Option(Instant.now()),
              columnName = "Date"
            )
          )
          lpDateColumnInfo <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.customData()(
                customDataQuery.map(
                  _.flatMap(_.asMessage.sealedValue.dateTimeValue.flatMap(_.value))
                ) ~ datalake.CustomData.dataColumnId
              )
            )
          )
          customDataFilter = CustomDataFilter(
            dataColumnId = Option(StringHashFilter(eq = Option(dateColumnId.idString)))
          )
          dateLastUpdatedBy <- evendimClient
            .query(
              datalake.Query.getOrder(id = lpId.idString)(
                datalake.Order.customData(filter = Option(customDataFilter))(
                  datalake.CustomData.lastEditedByOpt()(
                    datalake.User.email
                  )
                )
              )
            )
            .map(
              _.getOrElse(List.empty).headOption.flatten
            )
        } yield {
          assertTrue(
            lpDateColumnInfo.map(_.toSet) == Option(
              Set(
                None -> noteColumnId.idString,
                Some(currentInstant) -> dateColumnId.idString
              )
            ),
            dateLastUpdatedBy == Option(userBM2Email)
          )
        }
      }
    ),
    suite("Update collaborators")(
      test("Add & remove collaborators") {
        for {
          _ <- UpdateCollaboratorsHandler.execute(
            UpdateCollaboratorsParams(
              lpIdOpt = Some(lpId),
              add = Seq(col3Info, col4Info),
              remove = Seq(col1Info.id)
            )
          )
          collaboratorsOpt <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.contacts(
              )(datalake.User.email)
            )
          )
          pendingMembersOpt <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.pendingContacts(
              )(datalake.User.email)
            )
          )
        } yield {
          assertTrue(
            collaboratorsOpt.map(_.toSet) == Option(
              Set(
                col2Info.email,
                col3Info.email,
                col4Info.email
              )
            ),
            pendingMembersOpt.map(_.toSet) == Option(
              Set(
                lp1Info.email,
                col2Info.email,
                col3Info.email,
                col4Info.email
              )
            )
          )
        }
      },
      test("Collaborator join") {
        val newFirstName = usFaker.name().firstName()
        val newLastName = usFaker.name().lastName()
        for {
          _ <- LogViewOrderEventHandler.execute(
            LogViewOrderEventParams(
              lpIdOpt = Option(lpId),
              actor = Option(
                col3Info.copy(
                  firstName = newFirstName,
                  lastName = newLastName
                )
              )
            ),
            None
          )
          userInfo <- evendimClient.query(
            datalake.Query.getUser(id = Option(col3Info.id.idString))(
              datalake.User.firstName ~ datalake.User.lastName
            )
          )
          pendingMembers <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.pendingContacts()(
                datalake.User.email
              )
            )
          )
        } yield {
          assertTrue(
            userInfo == Option(newFirstName -> newLastName),
            pendingMembers.map(_.toSet) == Option(Set(lp1Info.email, col2Info.email, col4Info.email))
          )
        }
      },
      test("Promote collaborator 3") {
        for {
          _ <- CollaboratorPromotionHandler.execute(
            PromoteCollaboratorParams(
              lpIdOpt = Option(lpId),
              demotedInvestor = lp1Info.id,
              promotedCollaborator = col3Info.id
            )
          )
          mainLp <- evendimClient.query(
            datalake.Query.getOrder(lpId.idString)(
              datalake.Order.mainContact()(datalake.User.id)
            )
          )
          collaborators <- evendimClient.query(
            datalake.Query.getOrder(lpId.idString)(
              datalake.Order.contacts()(datalake.User.id)
            )
          )
        } yield {
          assertTrue(
            mainLp == Option(col3Info.id.idString),
            collaborators.map(_.sorted) == Option(
              List(
                lp1Info.id.idString,
                col2Info.id.idString,
                col4Info.id.idString
              ).sorted
            )
          )
        }
      },
      test("Update commitment amount") {
        for {
          _ <- UpdateCommitmentAmountHandler.execute(
            UpdateCommitmentAmountParams(
              lpIdOpt = Option(lpId),
              expectedCommitment = Option(100000),
              submittedCommitment = Option(150000),
              acceptedCommitment = Option(200000),
              commitmentAmounts = Seq.empty
            ),
            None
          )
          expectedCommitment <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.estimatedCommitmentAmount
            )
          )

          submittedCommitment <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.submittedCommitmentAmount
            )
          )

          acceptedCommitment <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.acceptedCommitmentAmount
            )
          )
        } yield {
          assertTrue(
            expectedCommitment.flatten == Option(100000),
            submittedCommitment.flatten == Option(150000),
            acceptedCommitment.flatten == Option(200000)
          )
        }
      },
      test("Overwrite commitment amount") {
        for {
          _ <- UpdateCommitmentAmountHandler.execute(
            UpdateCommitmentAmountParams(
              lpIdOpt = Option(lpId),
              expectedCommitment = None,
              submittedCommitment = Option(155000),
              acceptedCommitment = None,
              commitmentAmounts = Seq.empty
            ),
            None
          )
          expectedCommitment <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.estimatedCommitmentAmount
            )
          )

          submittedCommitment <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.submittedCommitmentAmount
            )
          )

          acceptedCommitment <- evendimClient.query(
            datalake.Query.getOrder(id = lpId.idString)(
              datalake.Order.acceptedCommitmentAmount
            )
          )
        } yield {
          assertTrue(
            expectedCommitment.flatten == Option(100000),
            submittedCommitment.flatten == Option(155000),
            acceptedCommitment.flatten == Option(200000)
          )
        }
      },
      suite("Required docs")(
        test("Set initial required docs") {
          val inputDocs = List(requiredDoc1, requiredDoc2)
          for {
            _ <- UpdateRequiredDocsHandler.execute(
              UpdateRequiredDocsParams(
                lpIdOpt = Option(lpId),
                docs = inputDocs
              )
            )
            updatedDocs <- getRequiredDocs.map(_.map(sanitizeRequiredDoc(_)))
          } yield {
            assertTrue(
              updatedDocs.sortBy(_.id) == sanitizeRequiredDocs(inputDocs)
            )
          }
        },
        test("Mark a doc 1 as NA") {
          val inputDocs = List(
            requiredDoc1.copy(markedAsNa = true),
            requiredDoc2
          )
          for {
            _ <- UpdateRequiredDocsHandler.execute(
              UpdateRequiredDocsParams(
                lpIdOpt = Option(lpId),
                docs = inputDocs
              )
            )
            updatedDocs <- getRequiredDocs.map(_.map(sanitizeRequiredDoc(_)))
          } yield {
            assertTrue(updatedDocs.sortBy(_.id) == sanitizeRequiredDocs(inputDocs))
          }
        },
        test("Remove doc 1, update doc 2 and add doc 3") {
          val inputDocs = List(
            requiredDoc2,
            RequiredDocInfo(
              id = "",
              name = "Doc 3",
              markedAsNa = false,
              submitted = false,
              submittedDocs = Seq.empty
            )
          )
          for {
            _ <- UpdateRequiredDocsHandler.execute(
              UpdateRequiredDocsParams(
                lpIdOpt = Option(lpId),
                docs = inputDocs
              )
            )
            updatedDocs <- getRequiredDocs.map(_.map(sanitizeRequiredDoc(_)))
          } yield {
            assertTrue(updatedDocs.sortBy(_.name) == sanitizeRequiredDocs(inputDocs))
          }
        },
        test("Update files of required doc 2 and 3") {
          val inputDocs = List(
            requiredDoc2.copy(
              submittedDocs = List(
                createLpDocument(
                  requiredDoc3,
                  "doc-3.pdf",
                  FundSubDocType.SupportingForm,
                  lpId
                )
              )
            ),
            RequiredDocInfo(
              id = "",
              name = "Doc 3",
              markedAsNa = false,
              submitted = false,
              submittedDocs = Seq(
                createLpDocument(
                  requiredDoc4,
                  "doc-4.pdf",
                  FundSubDocType.SupportingForm,
                  lpId
                )
              )
            )
          )
          for {
            _ <- UpdateRequiredDocsHandler.execute(
              UpdateRequiredDocsParams(
                lpIdOpt = Option(lpId),
                docs = inputDocs
              )
            )
            updatedDocs <- getRequiredDocs.map(_.map(sanitizeRequiredDoc(_)))
          } yield {
            assertTrue(updatedDocs.sortBy(_.name) == sanitizeRequiredDocs(inputDocs))
          }
        }
      ),
      suite("Supporting doc signature request")(
        test("Add initial request") {
          for {
            _ <- UpdateOrAddOrderSignatureRequestsHandler.execute(
              UpdateOrAddOrderSignatureRequestsParams(
                lpIdOpt = Option(lpId),
                requests = List(
                  SignatureRequestBasicInfo(
                    id = signatureRequestId1,
                    status = FundSubSignatureRequestStatusMessage.InProgress,
                    creator = Option(
                      UserBasicInfo(
                        id = userBM2,
                        email = userBM2Email,
                        firstName = userInfoBM2.firstName,
                        lastName = userInfoBM2.lastName
                      )
                    ),
                    createdAt = None
                  )
                ),
                requestType = SignatureRequestType.SupportingDocSignatureRequest
              )
            )
            res <- evendimClient.query(
              datalake.Query.getOrder(id = lpId.idString)(
                datalake.Order.supportingDocSignatureRequests()(
                  datalake.SignatureRequest.id ~ datalake.SignatureRequest.status
                )
              )
            )
          } yield {
            assertTrue(
              res == Option(List(signatureRequestId1.idString -> datalake.SignatureRequestStatus.SENT))
            )
          }
        },
        test("Update request status") {
          for {
            _ <- UpdateOrAddOrderSignatureRequestsHandler.execute(
              UpdateOrAddOrderSignatureRequestsParams(
                lpIdOpt = Option(lpId),
                requests = List(
                  SignatureRequestBasicInfo(
                    id = signatureRequestId1,
                    status = FundSubSignatureRequestStatusMessage.Canceled,
                    creator = None,
                    createdAt = None
                  )
                ),
                requestType = SignatureRequestType.SupportingDocSignatureRequest
              )
            )
            res <- evendimClient.query(
              datalake.Query.getSignatureRequest(id = signatureRequestId1.idString)(
                datalake.SignatureRequest.id ~ datalake.SignatureRequest.status
              )
            )
          } yield {
            assertTrue(
              res == Option(signatureRequestId1.idString -> datalake.SignatureRequestStatus.CANCELLED)
            )
          }
        },
        test("Add second request") {
          for {
            _ <- UpdateOrAddOrderSignatureRequestsHandler.execute(
              UpdateOrAddOrderSignatureRequestsParams(
                lpIdOpt = Option(lpId),
                requests = List(
                  SignatureRequestBasicInfo(
                    id = signatureRequestId2,
                    status = FundSubSignatureRequestStatusMessage.Completed,
                    creator = None,
                    createdAt = None
                  )
                ),
                requestType = SignatureRequestType.SupportingDocSignatureRequest
              )
            )
            res <- evendimClient.query(
              datalake.Query.getOrder(id = lpId.idString)(
                datalake.Order.supportingDocSignatureRequests()(
                  datalake.SignatureRequest.id ~ datalake.SignatureRequest.status
                )
              )
            )
          } yield {
            assertTrue(
              res.map(_.toSet) == Option(
                Set(
                  signatureRequestId2.idString -> datalake.SignatureRequestStatus.COMPLETED
                )
              )
            )
          }
        },
        test("Add an import template") {
          for {
            _ <- UpdateOrAddTemplateHandler.execute(
              UpdateOrAddTemplateParams(
                Option(importTemplate)
              )
            )
            addedTemplateOpt <- evendimClient
              .query(
                datalake.Query
                  .getTemplate(templateVersionId1.idString)(
                    CommonQueries.selectTemplate
                  )
                  .map(_.flatten)
              )
            addedTemplate <- ZIOUtils.optionToTask(addedTemplateOpt, new RuntimeException("Cannot add template"))
          } yield {
            assertTrue(
              addedTemplate.name == importTemplate.name,
              addedTemplate.isExportTemplate == false,
              addedTemplate.lastChangeDescription == importTemplate.lastChangeDescription,
              addedTemplate.columns
                .map { column =>
                  TemplateColumn(
                    index = column.index,
                    title = column.title
                  )
                }
                .sortBy(_.index) == importTemplate.columns.toList,
              addedTemplate.spreadsheetFile.map(_.id) == Option(importTemplateFile),
              addedTemplate.spreadsheetFile.map(_.name) == Option("sample_import.xsl")
            )
          }
        },
        test("Update columns") {
          val newColumns = Seq(
            TemplateColumn(
              index = 0,
              title = "Brown Swiss"
            ),
            TemplateColumn(
              index = 1,
              title = "Simmental"
            ),
            TemplateColumn(
              index = 2,
              title = "Angus"
            )
          )
          for {
            _ <- UpdateOrAddTemplateHandler.execute(
              UpdateOrAddTemplateParams(
                Option(
                  importTemplate.copy(
                    columns = newColumns
                  )
                )
              )
            )
            updatedTemplateOpt <- evendimClient
              .query(
                datalake.Query
                  .getTemplate(templateVersionId1.idString)(
                    CommonQueries.selectTemplate
                  )
                  .map(_.flatten)
              )
            updatedTemplate <- ZIOUtils.optionToTask(updatedTemplateOpt, new RuntimeException("Cannot update template"))
          } yield {
            assertTrue(
              updatedTemplate.columns
                .map { column =>
                  TemplateColumn(
                    index = column.index,
                    title = column.title
                  )
                }
                .sortBy(_.index) == newColumns.toList
            )
          }
        },
        test("Add an export template") {
          for {
            _ <- UpdateOrAddTemplateHandler.execute(
              UpdateOrAddTemplateParams(
                Option(exportTemplate)
              )
            )
            addedTemplateOpt <- evendimClient
              .query(
                datalake.Query
                  .getTemplate(templateVersionId2.idString)(
                    CommonQueries.selectTemplate
                  )
                  .map(_.flatten)
              )
            addedTemplate <- ZIOUtils.optionToTask(addedTemplateOpt, new RuntimeException("Cannot add template"))
          } yield {
            assertTrue(
              addedTemplate.name == exportTemplate.name,
              addedTemplate.isExportTemplate == true,
              addedTemplate.lastChangeDescription == exportTemplate.lastChangeDescription,
              addedTemplate.columns
                .map { column =>
                  TemplateColumn(
                    index = column.index,
                    title = column.title
                  )
                }
                .sortBy(_.index) == exportTemplate.columns.toList,
              addedTemplate.spreadsheetFile.map(_.id) == Option(exportTemplateFile),
              addedTemplate.spreadsheetFile.map(_.name) == Option("sample_export.xsl")
            )
          }
        },
        test("Add templates to fund") {
          for {
            _ <- UpdateOrAddFundHandler.execute(
              AddOrUpdateFundParams(
                id = fundId,
                addTemplates = Seq(templateVersionId1, templateVersionId2)
              )
            )
            templateIds <- evendimClient
              .query(
                datalake.Query.getFundSubscription(fundId.idString)(
                  datalake.FundSubscription.templates()(datalake.Template.id)
                )
              )
              .map(_.getOrElse(List.empty))
          } yield assertTrue(templateIds.sorted == Seq(templateVersionId1, templateVersionId2).map(_.idString).sorted)
        },
        test("Remove template from fund") {
          for {
            _ <- UpdateOrAddFundHandler.execute(
              AddOrUpdateFundParams(
                id = fundId,
                removeTemplates = Seq(templateVersionId2)
              )
            )
            templateIds <- evendimClient
              .query(
                datalake.Query.getFundSubscription(fundId.idString)(
                  datalake.FundSubscription.templates()(datalake.Template.id)
                )
              )
              .map(_.getOrElse(List.empty))
          } yield assertTrue(templateIds == Seq(templateVersionId1.idString))
        }
      ),
      suite("AML/KYC reviews")(
        test("Add AML/KYC review for W-8 and Driving license on Lp 1") {
          for {
            _ <- UpdateOrAddAmlKycReviewHandler.execute(
              UpdateOrAddAmlKycReviewParams(
                lpIdOpt = Option(lpId),
                reviews = Seq(
                  AmlKycReviewInfo(
                    id = "",
                    docType = "W-8",
                    status = AmlKycReviewStatus.AML_KYC_REVIEW_STATUS_PENDING,
                    updatedBy = Option(generateUserBasicInfo()),
                    updatedAt = Option(DateCalculator.instantNow.minusSeconds(2000))
                  ),
                  AmlKycReviewInfo(
                    id = "",
                    docType = "Driving license",
                    status = AmlKycReviewStatus.AML_KYC_REVIEW_STATUS_APPROVED,
                    updatedBy = Option(generateUserBasicInfo()),
                    updatedAt = Option(DateCalculator.instantNow.minusSeconds(3000))
                  )
                )
              )
            )
            reviews <- evendimClient
              .query(
                datalake.Query
                  .getOrder(id = lpId.idString)(
                    datalake.Order.amlKycReviews()(
                      CommonQueries.selectAmlKycReview
                    )
                  )
              )
              .map(_.getOrElse(List.empty))
          } yield {
            val drivingLicenseReviewOpt = reviews.find(_.docType.trim.equalsIgnoreCase("Driving license".trim))
            val status = drivingLicenseReviewOpt.map(_.status).getOrElse(anduin.dashboard.data.ReviewStatus.Pending)
            assertTrue(
              reviews.size == 2,
              status == anduin.dashboard.data.ReviewStatus.Approved
            )
          }
        },
        test("Update Driving licence review") {
          val reviewer = generateUserBasicInfo()
          for {
            _ <- UpdateOrAddAmlKycReviewHandler.execute(
              UpdateOrAddAmlKycReviewParams(
                lpIdOpt = Option(lpId),
                reviews = Seq(
                  AmlKycReviewInfo(
                    id = "",
                    docType = "Driving license",
                    status = AmlKycReviewStatus.AML_KYC_REVIEW_STATUS_REQUESTED_CHANGE,
                    updatedBy = Option(reviewer),
                    updatedAt = Option(DateCalculator.instantNow)
                  )
                )
              )
            )
            reviews <- evendimClient
              .query(
                datalake.Query
                  .getOrder(id = lpId.idString)(
                    datalake.Order.amlKycReviews()(
                      CommonQueries.selectAmlKycReview
                    )
                  )
              )
              .map(_.getOrElse(List.empty))
          } yield {
            val drivingLicenseReviewInfo = reviews.find(_.docType == "Driving license")
            assertTrue(
              drivingLicenseReviewInfo.flatMap(_.updatedBy.map(_.userId)) == Option(reviewer.id),
              drivingLicenseReviewInfo.map(_.status) == Option(anduin.dashboard.data.ReviewStatus.ChangesRequested)
            )
          }
        },
        test("Remove W-8 review") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                removeAmlKycReviews = Seq("W-8")
              )
            )
            reviews <- evendimClient
              .query(
                datalake.Query
                  .getOrder(id = lpId.idString)(
                    datalake.Order.amlKycReviews()(
                      CommonQueries.selectAmlKycReview
                    )
                  )
              )
              .map(_.getOrElse(List.empty))
          } yield {
            assertTrue(
              reviews.size == 1,
              reviews.map(_.docType) == Seq("Driving license")
            )
          }
        },
        test("Add signed review with requested change status") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                setSignedSubscriptionReview = Option(
                  SubscriptionReviewInfo(
                    id = reviewFlowId3,
                    versionIndex = 2,
                    status = ReviewStatus.REVIEW_STATUS_REQUESTED_CHANGE,
                    currentPendingStepId = Option(reviewStepConfigId1)
                  )
                )
              )
            )
            reviewOpt <- evendimClient
              .query(
                datalake.Query
                  .getOrder(id = lpId.idString)(
                    datalake.Order.signedSubscriptionReview()(
                      CommonQueries.selectSubscriptionDocReview
                    )
                  )
                  .map(_.flatten)
              )
              .map(_.flatten)
          } yield {
            assertTrue(
              reviewOpt == Option(
                Documents.SubscriptionDocReview(
                  id = reviewFlowId3,
                  versionIndex = 2,
                  status = anduin.dashboard.data.ReviewStatus.ChangesRequested,
                  currentPendingStepIdOpt = Option(reviewStepConfigId1)
                )
              )
            )
          }
        },
        test("Approved signed subscription doc") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                setSignedSubscriptionReview = Option(
                  SubscriptionReviewInfo(
                    id = reviewFlowId3,
                    versionIndex = 2,
                    status = ReviewStatus.REVIEW_STATUS_STATUS_APPROVED,
                    currentPendingStepId = Option(reviewStepConfigId1)
                  )
                )
              )
            )
            reviewOpt <- evendimClient
              .query(
                datalake.Query
                  .getOrder(id = lpId.idString)(
                    datalake.Order.signedSubscriptionReview()(
                      CommonQueries.selectSubscriptionDocReview
                    )
                  )
                  .map(_.flatten)
              )
              .map(_.flatten)
          } yield {
            assertTrue(
              reviewOpt == Option(
                Documents.SubscriptionDocReview(
                  id = reviewFlowId3,
                  versionIndex = 2,
                  status = anduin.dashboard.data.ReviewStatus.Approved,
                  currentPendingStepIdOpt = Option(reviewStepConfigId1)
                )
              )
            )
          }
        },
        test("Add unsigned review with pending status") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                setUnsignedSubscriptionReview = Option(
                  SubscriptionReviewInfo(
                    id = reviewFlowId4,
                    versionIndex = 4,
                    status = ReviewStatus.REVIEW_STATUS_PENDING,
                    currentPendingStepId = Option(reviewStepConfigId2)
                  )
                )
              )
            )
            reviewOpt <- evendimClient
              .query(
                datalake.Query
                  .getOrder(id = lpId.idString)(
                    datalake.Order.unsignedSubscriptionReview()(
                      CommonQueries.selectSubscriptionDocReview
                    )
                  )
                  .map(_.flatten)
              )
              .map(_.flatten)
          } yield {
            assertTrue(
              reviewOpt == Option(
                Documents.SubscriptionDocReview(
                  id = reviewFlowId4,
                  versionIndex = 4,
                  status = anduin.dashboard.data.ReviewStatus.Pending,
                  currentPendingStepIdOpt = Option(reviewStepConfigId2)
                )
              )
            )
          }
        },
        test("Remove signed review") {
          for {
            _ <- UpdateOrderBasicInfoHandler.execute(
              UpdateOrderBasicInfoParams(
                lpIdOpt = Option(lpId),
                removeSignedSubscriptionReview = Option(reviewFlowId3)
              )
            )
            reviewOpt <- evendimClient
              .query(
                datalake.Query
                  .getOrder(id = lpId.idString)(
                    datalake.Order.signedSubscriptionReview()(
                      CommonQueries.selectSubscriptionDocReview
                    )
                  )
                  .map(_.flatten)
              )
              .map(_.flatten)
            unsignedReviewOpt <- evendimClient
              .query(
                datalake.Query
                  .getOrder(id = lpId.idString)(
                    datalake.Order.unsignedSubscriptionReview()(
                      CommonQueries.selectSubscriptionDocReview
                    )
                  )
                  .map(_.flatten)
              )
              .map(_.flatten)
          } yield {
            assertTrue(
              reviewOpt.isEmpty,
              unsignedReviewOpt.isDefined
            )
          }
        },
        test("Set W-8 and Driving license as provided") {
          for {
            _ <- UpdateProvidedDocsHandler.execute(
              UpdateProvidedDocsParams(
                lpIdOpt = Option(lpId),
                docs = List("W-8", "Driving license")
              )
            )
            docsList <- evendimClient
              .query(
                datalake.Query
                  .getOrder(id = lpId.idString)(
                    datalake.Order.amlKycDocsProvidedOffline
                  )
                  .map(_.map(_.getOrElse(List.empty[String])))
              )
              .map(_.getOrElse(List.empty[String]))
          } yield {
            assertTrue(docsList.toSet == Set("W-8", "Driving license"))
          }
        },
        test("Set W-8 and Passport as provided") {
          for {
            _ <- UpdateProvidedDocsHandler.execute(
              UpdateProvidedDocsParams(
                lpIdOpt = Option(lpId),
                docs = List("W-8", "Passport")
              )
            )
            docsList <- evendimClient
              .query(
                datalake.Query
                  .getOrder(id = lpId.idString)(
                    datalake.Order.amlKycDocsProvidedOffline
                  )
                  .map(_.map(_.getOrElse(List.empty[String])))
              )
              .map(_.getOrElse(List.empty[String]))
          } yield { assertTrue(docsList.toSet == Set("W-8", "Passport")) }
        }
      )
    )
  ) @@ TestAspect.sequential

}
