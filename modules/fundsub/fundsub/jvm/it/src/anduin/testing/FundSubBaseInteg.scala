// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.testing

import anduin.account.policy.AccountPolicyCoreService
import anduin.account.profile.UserProfileService
import anduin.amlkyc.amlcheck.AmlCheckService
import anduin.announcement.AnnouncementService
import anduin.batchaction.BatchActionService
import anduin.contact.service.core.ContactGroupService
import anduin.dashboard.service.DashboardService
import anduin.dms.DmsFeature
import anduin.dms.service.FileService
import anduin.docrequest.service.{DocRequestService, DocSubmissionService, FormSubmissionService}
import anduin.documentcontent.pdf.FillPdfService
import anduin.documentservice.pdf.PdfFileService
import anduin.environment.*
import anduin.esignature.ESignatureService
import anduin.evendim.client.{EvendimAdminClient, EvendimClient}
import anduin.forms.service.*
import anduin.funddata.bot.FundDataBotUser
import anduin.fundsub.FundSubLoggingService
import anduin.fundsub.activitylog.ActivityLogService
import anduin.fundsub.amlkyc.amlcheck.FundSubAmlCheckService
import anduin.fundsub.auditlog.FundSubAuditLogService
import anduin.fundsub.batchaction.FundSubBatchActionService
import anduin.fundsub.comment.*
import anduin.fundsub.copy.FundSubCopyConfigService
import anduin.fundsub.customdata.CustomDataService
import anduin.fundsub.dashboard.{FundSubDashboardAdminService, FundSubDashboardDataService, FundSubDashboardService}
import anduin.fundsub.dataexport.selfservice.FundSubSelfServiceExportService
import anduin.fundsub.dataexport.workflow.InvestorDataExportWorkflowService
import anduin.fundsub.dataexport.{FundSubExportService, FundSubFileDownloadService}
import anduin.fundsub.dataextract.service.{
  FundSubDataExtractLogService,
  FundSubDataExtractRequestEmailService,
  FundSubSubdocDataExtractService
}
import anduin.fundsub.datainterface.FundSubDataInterfaceService
import anduin.fundsub.datalakeingestion.FundSubDataLakeIngestionService
import anduin.fundsub.duplicateconfig.FundSubDuplicateConfigService
import anduin.fundsub.email.digest.newdocreviewready.NewSupportingDocReviewReadyReportService
import anduin.fundsub.email.digest.newdocupload.NewSupportingDocUploadReportService
import anduin.fundsub.email.generate.FundSubEmailUtils
import anduin.fundsub.email.template.FundSubEmailTemplateService
import anduin.fundsub.emaillog.FundSubEmailLogService
import anduin.fundsub.environment.{FundSubEnvironmentPolicyAdminService, FundSubEnvironmentPolicyService}
import anduin.fundsub.featureswitch.FundSubFeatureSwitchService
import anduin.fundsub.flow.FundSubLpFlowTaskHandler
import anduin.fundsub.form.{FormKafkaService, FundSubFormComparisonService, FundSubFormService}
import anduin.fundsub.fundclose.FundSubCloseService
import anduin.fundsub.globaldatabase.{FundSubGlobalDatabaseService, FundSubGlobalDatabaseSyncService}
import anduin.fundsub.group.{FundSubGroupMemberService, FundSubGroupService}
import anduin.fundsub.integration.FundSubExternalIntegrationService
import anduin.fundsub.integration.funddata.FundSubFundDataIntegrationService
import anduin.fundsub.investmententity.FundSubInvestmentEntityService
import anduin.fundsub.investorgroup.FundSubInvestorGroupService
import anduin.fundsub.multiregion.FundSubMultiRegionService
import anduin.fundsub.participant.FundSubParticipantService
import anduin.fundsub.report.{FundSubReportService, NewLpReportEmailService}
import anduin.fundsub.reviewpackage.ReviewPackageService
import anduin.fundsub.ria.{FundSubRiaIntegrationService, FundSubRiaService}
import anduin.fundsub.service.*
import anduin.fundsub.signature.batch.FundSubBatchCountersignService
import anduin.fundsub.signature.integration.FundSubSignatureIntegrationService
import anduin.fundsub.signature.oneenvelope.FundSubOneEnvelopeSignatureRequestService
import anduin.fundsub.signature.{FundSubDocusignWebhookEventConsumer, FundSubSignatureService}
import anduin.fundsub.simulator.{FundSubSimulatorService, FundSubSimulatorUtilsService}
import anduin.fundsub.status.FundSubLpStatusHistoryService
import anduin.fundsub.storageintegration.FundSubStorageIntegrationService
import anduin.fundsub.subscriptiondoc.review.FundSubSubscriptionDocReviewService
import anduin.fundsub.subscriptiondoc.{
  FundSubSubscriptionCountersignService,
  FundSubSubscriptionDocService,
  FundSubSubscriptionQueryService,
  FundSubSubscriptionSubmitService
}
import anduin.fundsub.user.{FundSubUserService, FundSubUserTrackingService}
import anduin.fundsub.validator.FundSubPermissionValidatorService
import anduin.fundsub.view.FundSubViewService
import anduin.greylin.GreylinDataService
import anduin.issuetracker.service.IssueTrackerService
import anduin.jwt.Jwt
import anduin.link.LinkGeneratorService
import anduin.lpprofile.service.LpProfileService
import anduin.portaluser.{ExecutiveAdmin, PortalAdmin, PortalUserService}
import anduin.rebac.MultiStoreRebacStoreManager
import anduin.review.service.ReviewService
import anduin.service.entity.EntityService
import anduin.signature.integration.SignatureIntegrationService
import anduin.signature.request.SignatureRequestService
import anduin.storage.ProtoStorageService
import anduin.storageservice.s3.S3Service
import anduin.team.TeamService
import anduin.telemetry.TelemetryEnvironment
import anduin.temporal.TemporalEnvironment
import anduin.tidb.TiDBEnvironment
import anduin.user.UserService
import com.anduin.stargazer.service.GondorBackendConfig.{AwsS3Config, TemporalConfig}
import com.anduin.stargazer.service.GondorConfig
import com.anduin.stargazer.service.api.{DynamicFormService, FileDownloadService}
import com.anduin.stargazer.service.authorization.AuthorizationService
import com.anduin.stargazer.service.dynamicform.DynamicFormStorageService
import com.anduin.stargazer.service.email.store.EmailStoreService
import com.anduin.stargazer.service.fundsub.*
import com.anduin.stargazer.service.fundsub.free.module.{
  FundSubLpInvitationService,
  ManageFundSubAdminM,
  ManageFundSubLpM
}
import com.anduin.stargazer.service.fundsub.generator.FundSubDataGeneratorService
import com.anduin.stargazer.service.fundsub.integration.*
import com.anduin.stargazer.service.fundsub.operation.{
  FundSubOperationDataExtractService,
  FundSubOperationInvestorService,
  FundSubOperationService
}
import com.anduin.stargazer.service.fundsub.sideletter.SideLetterService
import com.anduin.stargazer.service.nats.NatsNotificationService

abstract class FundSubBaseInteg extends ZIOBaseInteg with GondorCoreIntegUtils {

  given gondorConfig: GondorConfig = FundSubTestModule.gondorConfig

  given s3Config: AwsS3Config = gondorConfig.backendConfig.aws.S3

  given tiDBEnvironment: TiDBEnvironment = FundSubTestModule.tiDBEnvironment

  given temporalConfig: TemporalConfig = FundSubTestModule.temporalConfig

  given temporalEnvironment: TemporalEnvironment = FundSubTestModule.temporalEnvironment

  given entityService: EntityService = FundSubTestModule.entityService

  given userProfileService: UserProfileService = FundSubTestModule.userProfileService

  given authorizationService: AuthorizationService = FundSubTestModule.authorizationService

  given userService: UserService = FundSubTestModule.userService

  given fileDownloadService: FileDownloadService = FundSubTestModule.fileDownloadService

  protected lazy val dmsFeature: DmsFeature = FundSubTestModule.dmsFeature

  given DmsFeature = dmsFeature

  protected given fillPdfService: FillPdfService = FundSubTestModule.fillPdfService

  protected given pdfFileService: PdfFileService = FundSubTestModule.pdfFileService

  protected lazy val multiStoreRebacStoreManager: MultiStoreRebacStoreManager =
    FundSubTestModule.multiStoreRebacStoreManager

  protected given linkGeneratorService: LinkGeneratorService = FundSubTestModule.linkGeneratorService

  lazy val accountPolicyCoreService: AccountPolicyCoreService = FundSubTestModule.accountPolicyCoreService

  protected lazy val emailStoreService: EmailStoreService = FundSubTestModule.emailStoreService

  protected given formTemplateMappingService: FormTemplateMappingService =
    FundSubTestModule.formTemplateMappingService

  protected given lpProfileService: LpProfileService = FundSubTestModule.lpProfileService

  protected given formLockService: FormLockService = FundSubTestModule.formLockService

  protected given dataTemplateService: DataTemplateService = FundSubTestModule.dataTemplateService

  protected lazy val tracingEnvironment: TelemetryEnvironment.Tracing = FundSubTestModule.tracingEnvironment

  protected given issueTrackerService: IssueTrackerService = FundSubTestModule.issueTrackerService

  given fundDataBotUser: FundDataBotUser = FundSubTestModule.fundDataBotUser

  given executiveAdmin: ExecutiveAdmin = FundSubTestModule.executiveAdmin

  given fileService: FileService = FundSubTestModule.fileService

  given portalUserService: PortalUserService = FundSubTestModule.portalUserService

  given formService: FormService = FundSubTestModule.formService

  given dynamicFormService: DynamicFormService = FundSubTestModule.dynamicFormService

  given dynamicFormStorageService: DynamicFormStorageService = FundSubTestModule.dynamicFormStorageService

  given portalAdmin: PortalAdmin = FundSubTestModule.portalAdmin

  given contactGroupService: ContactGroupService = FundSubTestModule.contactGroupService

  given teamService: TeamService = FundSubTestModule.teamService

  given natsNotificationService: NatsNotificationService = FundSubTestModule.natsNotificationService

  given greylinDataService: GreylinDataService = FundSubTestModule.greylinDataService

  given jwt: Jwt = FundSubTestModule.jwt

  given s3Service: S3Service = FundSubTestModule.s3Service

  given signatureIntegrationService: SignatureIntegrationService =
    FundSubTestModule.signatureIntegrationService

  given newLpReportEmailService: NewLpReportEmailService = FundSubTestModule.newLpReportEmailService

  given supportingDocsHistoryService: SupportingDocsHistoryService = FundSubTestModule.supportingDocsHistoryService

  given supportingDocReviewService: SupportingDocReviewService = FundSubTestModule.supportingDocReviewService

  given reviewService: ReviewService = FundSubTestModule.reviewService

  given fundSubCopyConfigService: FundSubCopyConfigService = FundSubTestModule.fundSubCopyConfigService

  given fundSubPermissionService: FundSubPermissionService = FundSubTestModule.fundSubPermissionService

  given fundSubOneEnvelopeSignatureRequestService: FundSubOneEnvelopeSignatureRequestService =
    FundSubTestModule.fundSubOneEnvelopeSignatureRequestService

  given fundSubPortalPermissionService: FundSubPortalPermissionService =
    FundSubTestModule.fundSubPortalPermissionService

  given fundSubPermissionValidatorService: FundSubPermissionValidatorService =
    FundSubTestModule.fundSubPermissionValidatorService

  given fundSubEmailUtils: FundSubEmailUtils = FundSubTestModule.fundSubEmailUtils

  given fundSubLpFlowTaskHandler: FundSubLpFlowTaskHandler = FundSubTestModule.fundSubLpFlowTaskHandler

  given fundSubLoggingService: FundSubLoggingService = FundSubTestModule.fundSubLoggingService

  given adminModule: ManageFundSubAdminM = FundSubTestModule.adminModule

  given lpModule: ManageFundSubLpM = FundSubTestModule.lpModule

  given fundSubProtectedLinkService: FundSubProtectedLinkService = FundSubTestModule.fundSubProtectedLinkService

  given fundsubAdminService: FundSubAdminService = FundSubTestModule.fundsubAdminService

  given fundsubLpService: FundSubLpService = FundSubTestModule.fundsubLpService

  given fundSubLpDashboardService: FundSubLpDashboardService = FundSubTestModule.fundSubLpDashboardService

  given fundSubLpActivityLogService: FundSubLpActivityLogService = FundSubTestModule.fundSubLpActivityLogService

  given fundSubLpTagUtilService: FundSubLpTagUtilService = FundSubTestModule.fundSubLpTagUtilService

  given fundSubContactService: FundSubContactService = FundSubTestModule.fundSubContactService

  given fundSubWhiteLabelService: FundSubWhiteLabelService = FundSubTestModule.fundSubWhiteLabelService

  given commentTiDbDataUtils: CommentTiDbDataUtils = FundSubTestModule.commentTiDbDataUtils

  given commentServiceUtils: CommentServiceUtils = FundSubTestModule.commentServiceUtils

  given formCommentService: FormCommentService = FundSubTestModule.formCommentService

  given commentExportUtils: CommentExportUtils = FundSubTestModule.commentExportUtils

  given formCommentEmailService: FormCommentEmailService = FundSubTestModule.formCommentEmailService

  given fundSubOperationService: FundSubOperationService = FundSubTestModule.fundSubOperationService

  given fundSubOperationDataExtractService: FundSubOperationDataExtractService =
    FundSubTestModule.fundSubOperationDataExtractService

  given fundSubOperationInvestorService: FundSubOperationInvestorService =
    FundSubTestModule.fundSubOperationInvestorService

  given fundSubDataGeneratorService: FundSubDataGeneratorService = FundSubTestModule.fundSubDataGeneratorService

  given fundSubSignatureService: FundSubSignatureService = FundSubTestModule.fundSubSignatureService

  given fundSubBatchCountersignService: FundSubBatchCountersignService =
    FundSubTestModule.fundSubBatchCountersignService

  given docRequestService: DocRequestService = FundSubTestModule.docRequestService

  given formSubmissionService: FormSubmissionService = FundSubTestModule.formSubmissionService

  given docSubmissionService: DocSubmissionService = FundSubTestModule.docSubmissionService

  given newSupportingDocUploadReportService: NewSupportingDocUploadReportService =
    FundSubTestModule.newSupportingDocUploadReportService

  given newSupportingDocService: NewSupportingDocService = FundSubTestModule.newSupportingDocService

  given newSupportingDocReviewReadyReportService: NewSupportingDocReviewReadyReportService =
    FundSubTestModule.newSupportingDocReviewReadyReportService

  given newSupportingDocLoggingService: NewSupportingDocLoggingService =
    FundSubTestModule.newSupportingDocLoggingService

  given fundSubFundDataIntegrationService: FundSubFundDataIntegrationService =
    FundSubTestModule.fundSubFundDataIntegrationService

  given fundSubExternalIntegrationService: FundSubExternalIntegrationService =
    FundSubTestModule.fundSubExternalIntegrationService

  given fundSubDocusignWebhookEventConsumer: FundSubDocusignWebhookEventConsumer =
    FundSubTestModule.fundSubDocusignWebhookEventConsumer

  // fundsub simulator
  given fundSubSimulatorService: FundSubSimulatorService = FundSubTestModule.fundSubSimulatorService

  given fundSubSimulatorUtilsService: FundSubSimulatorUtilsService = FundSubTestModule.fundSubSimulatorUtilsService

  given fundSubEmailService: FundSubEmailService = FundSubTestModule.fundSubEmailService

  given fundSubFileDownloadService: FundSubFileDownloadService = FundSubTestModule.fundSubFileDownloadService

  given fundSubStorageIntegrationService: FundSubStorageIntegrationService =
    FundSubTestModule.fundSubStorageIntegrationService

  given fundSubCloseService: FundSubCloseService = FundSubTestModule.fundSubCloseService

  given fundSubSubscriptionQueryService: FundSubSubscriptionQueryService =
    FundSubTestModule.fundSubSubscriptionQueryService

  given fundSubSubscriptionSubmitService: FundSubSubscriptionSubmitService =
    FundSubTestModule.fundSubSubscriptionSubmitService

  given fundSubSubscriptionDocReviewService: FundSubSubscriptionDocReviewService =
    FundSubTestModule.fundSubSubscriptionDocReviewService

  given fundSubSubscriptionCountersignService: FundSubSubscriptionCountersignService =
    FundSubTestModule.fundSubSubscriptionCountersignService

  given fundSubSubscriptionDocService: FundSubSubscriptionDocService = FundSubTestModule.fundSubSubscriptionDocService

  given fundSubSignatureIntegrationService: FundSubSignatureIntegrationService =
    FundSubTestModule.fundSubSignatureIntegrationService

  given fundSubAuditLogService: FundSubAuditLogService = FundSubTestModule.fundSubAuditLogService

  given fundSubEmailTemplateService: FundSubEmailTemplateService = FundSubTestModule.fundSubEmailTemplateService

  given announcementService: AnnouncementService = FundSubTestModule.announcementService

  given fundSubEmailLogService: FundSubEmailLogService = FundSubTestModule.fundSubEmailLogService

  given fundSubGroupService: FundSubGroupService = FundSubTestModule.fundSubGroupService

  given fundSubGroupMemberService: FundSubGroupMemberService = FundSubTestModule.fundSubGroupMemberService

  given fundSubInvestorGroupService: FundSubInvestorGroupService = FundSubTestModule.fundSubInvestorGroupService

  given fundSubViewService: FundSubViewService = FundSubTestModule.fundSubViewService

  given fundSubReportService: FundSubReportService = FundSubTestModule.fundSubReportService

  given fundSubGlobalDatabaseService: FundSubGlobalDatabaseService = FundSubTestModule.fundSubGlobalDatabaseService

  given fundSubGlobalDatabaseSyncService: FundSubGlobalDatabaseSyncService =
    FundSubTestModule.fundSubGlobalDatabaseSyncService

  given fundSubMultiRegionService: FundSubMultiRegionService = FundSubTestModule.fundSubMultiRegionService

  given investorDataExportWorkflowService: InvestorDataExportWorkflowService =
    FundSubTestModule.investorDataExportWorkflowService

  given fundSubExportService: FundSubExportService = FundSubTestModule.fundSubExportService

  given fundSubSelfServiceExportService: FundSubSelfServiceExportService =
    FundSubTestModule.fundSubSelfServiceExportService

  given fundSubImportExportService: FundSubImportExportService = FundSubTestModule.fundSubImportExportService

  given fundSubUserTrackingService: FundSubUserTrackingService = FundSubTestModule.fundSubUserTrackingService

  given fundSubUserService: FundSubUserService = FundSubTestModule.fundSubUserService

  given taxFormService: TaxFormService = FundSubTestModule.taxFormService

  given supportingDocService: SupportingDocService = FundSubTestModule.supportingDocService

  given reviewPackageService: ReviewPackageService = FundSubTestModule.reviewPackageService

  given docReviewEmailUtils: DocumentReviewEmailUtils = FundSubTestModule.docReviewEmailUtils

  given fundSubFormIntegrationService: FundSubFormIntegrationService = FundSubTestModule.fundSubFormIntegrationService

  given autoPrefillService: AutoPrefillService = FundSubTestModule.autoPrefillService

  given fundSubLpAutoPrefillService: FundSubLpAutoPrefillService = FundSubTestModule.fundSubLpAutoPrefillService

  given fundSubFormService: FundSubFormService = FundSubTestModule.fundSubFormService

  given fundSubFormComparisonService: FundSubFormComparisonService = FundSubTestModule.fundSubFormComparisonService

  given fundSubParticipantService: FundSubParticipantService = FundSubTestModule.fundSubParticipantService

  given fundSubFeatureSwitchService: FundSubFeatureSwitchService = FundSubTestModule.fundSubFeatureSwitchService

  given fundSubBatchActionService: FundSubBatchActionService = FundSubTestModule.fundSubBatchActionService

  given fundSubSubdocDataExtractService: FundSubSubdocDataExtractService =
    FundSubTestModule.fundSubSubdocDataExtractService

  given fundSubDataExtractLogService: FundSubDataExtractLogService = FundSubTestModule.fundSubDataExtractLogService

  given fundSubDuplicateConfigService: FundSubDuplicateConfigService = FundSubTestModule.fundSubDuplicateConfigService

  // activity log
  given fundSubLpStatusHistoryService: FundSubLpStatusHistoryService = FundSubTestModule.fundSubLpStatusHistoryService

  given activityLogService: ActivityLogService = FundSubTestModule.activityLogService

  given fundSubPublicApiOperationService: FundSubPublicApiOperationService =
    FundSubTestModule.fundSubPublicApiOperationService

  given fundSubAmlCheckService: FundSubAmlCheckService = FundSubTestModule.fundSubAmlCheckService

  given amlCheckService: AmlCheckService = FundSubTestModule.amlCheckService

  given dataRoomIntegrationAdminService: DataRoomIntegrationAdminService =
    FundSubTestModule.dataRoomIntegrationAdminService

  given fundsubZapierService: FundSubZapierService = FundSubTestModule.fundsubZapierService

  // New Dashboard
  given fundSubDataLakeIngestionService: FundSubDataLakeIngestionService =
    FundSubTestModule.fundSubDataLakeIngestionService

  given fundSubGreylinDataService: FundSubGreylinDataService = FundSubTestModule.fundSubGreylinDataService

  given evendimClient: EvendimClient = FundSubTestModule.evendimClient

  given evendimAdminClient: EvendimAdminClient = FundSubTestModule.evendimAdminClient

  given customDataService: CustomDataService = FundSubTestModule.customDataService

  given dashboardService: DashboardService = FundSubTestModule.dashboardService

  given fundSubDashboardService: FundSubDashboardService = FundSubTestModule.fundSubDashboardService

  given fundSubDashboardDataService: FundSubDashboardDataService = FundSubTestModule.fundSubDashboardDataService

  given fundSubDashboardAdminService: FundSubDashboardAdminService = FundSubTestModule.fundSubDashboardAdminService

  given formKafkaService: FormKafkaService = FundSubTestModule.formKafkaService

  given fundSubInvestmentEntityService: FundSubInvestmentEntityService =
    FundSubTestModule.fundSubInvestmentEntityService

  given fundSubEnvironmentIntegrationService: FundSubEnvironmentIntegrationService =
    FundSubTestModule.fundSubEnvironmentIntegrationService

  given fundSubEnvironmentPolicyService: FundSubEnvironmentPolicyService =
    FundSubTestModule.fundSubEnvironmentPolicyService

  given fundSubEnvironmentAuthenticationIntegrationService: FundSubEnvironmentAuthenticationIntegrationService =
    FundSubTestModule.fundSubEnvironmentAuthenticationIntegrationService

  given fundSubEnvironmentPolicyAdminService: FundSubEnvironmentPolicyAdminService =
    FundSubTestModule.fundSubEnvironmentPolicyAdminService

  given fundSubDataExtractRequestEmailService: FundSubDataExtractRequestEmailService =
    FundSubTestModule.fundSubDataExtractRequestEmailService

  given formTemplateMappingLockService: FormTemplateMappingLockService =
    FundSubTestModule.formTemplateMappingLockService

  given fundSubLpInvitationService: FundSubLpInvitationService = FundSubTestModule.fundSubLpInvitationService

  lazy val fundSubInvitationActivitiesImpl = FundSubTestModule.fundSubInvitationActivitiesImpl

  lazy val fundSubDataImportActivityImpl = FundSubTestModule.fundSubDataImportActivityImpl

  given eSignatureService: ESignatureService = FundSubTestModule.eSignatureService

  given signatureRequestService: SignatureRequestService = FundSubTestModule.signatureRequestService

  protected lazy val lpDataExportActivities = FundSubTestModule.lpDataExportActivities

  given protoStorageService: ProtoStorageService = FundSubTestModule.protoStorageService

  given sideLetterService: SideLetterService = FundSubTestModule.sideLetterService

  given batchActionService: BatchActionService = FundSubTestModule.batchActionService

  given fundSubRiaService: FundSubRiaService = FundSubTestModule.fundSubRiaService

  given fundSubRiaIntegrationService: FundSubRiaIntegrationService = FundSubTestModule.fundSubRiaIntegrationService

  given fundSubDataInterfaceService: FundSubDataInterfaceService = FundSubTestModule.fundSubDataInterfaceService

}
