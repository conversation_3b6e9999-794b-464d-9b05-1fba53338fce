syntax = "proto3";

import "date_time.proto";
import "email_address.proto";
import "google/protobuf/wrappers.proto";
import "inbox/email.proto";
import "scalapb/scalapb.proto";
import "signature/e_signature.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub"
  single_file: true
  import: "anduin.id.funddata.FundDataFirmId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.fundsub.FundSubLpFormId"
  import: "anduin.id.fundsub.FundSubLpFormVersionId"
  import: "anduin.id.fundsub.FundSubExportTemplateId"
  import: "anduin.id.ria.RiaFundGroupId"
  import: "anduin.id.link.ProtectedLinkId"
  import: "anduin.id.lpprofile.LpProfileId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.FileId"
  import: "anduin.model.id.DynamicFormId"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.id.docrequest.FormSubmissionId"
  import: "java.time.Instant"
  import: "anduin.model.common.emailaddress.EmailAddress"
  import: "anduin.model.id.stage.DataRoomWorkflowId"
};

enum FundSubAdminRole {
  FundSubAdmin = 0; // Fund Manager (or internal admin)
  FundSubCounsel = 1;
  FundSubExternalAdmin = 2; // Fund Admin (from an external Fund admin service provider)
  AnduinSupport = 3;
}

enum FundSubLpRole {
  LpMain = 0;
  LpCollaborator = 1;
}

enum FundSubStatus {
  FundSubNotStarted = 0;
  FundSubInProgress = 1;
  FundSubArchived = 2;
  FundSubCompleted = 4;
}

enum FundSubDocType {
  LpSignedDoc = 0;
  SupportingForm = 1;
  CounterSignedDoc = 2;
  Others = 3;
  SigningCertificate = 4;
  FilledForm = 5;
}

message FundSubFile {
  string file_id = 1 [(scalapb.field).type = "FileId"];
  FundSubDocType doc_type = 2;
}

message FeatureSwitch {
  reserved 1, 2, 5, 8, 11, 12, 15 to 17, 20 to 23, 34, 37, 38, 43, 44, 46, 48, 49, 51, 52, 55, 59, 65, 67, 102;

  bool disable_lp_undo_submission = 3;
  bool disable_lp_supporting_doc = 4;
  bool enabled_custom_lp_id = 6;
  bool form_comment_switch = 7;
  bool disable_submission_instruction = 9;
  bool disable_invest_from_additional_entity = 10;
  bool disabled_mark_as_not_applicable = 13;
  bool enabled_add_additional_tax_forms = 14;
  bool hide_form_files_and_signing_buttons = 18;
  bool show_manual_orders_to_investors = 19;
  bool disable_fund_contact_in_investor_workspace = 24;
  bool enable_form_diff = 25;
  bool enable_lp_manual_submit_subscription = 26;
  bool enable_lp_reuse_previous_signed_version = 27;
  bool disable_investor_tax_form_electronic_signature = 28;
  bool export_investor_dashboard_tags_as_columns = 29;
  bool block_lp_submission_until_finish_supporting = 30;
  bool enable_auto_prefill = 31;
  bool enable_import_data = 32;
  bool enable_fund_permission = 33;
  bool enable_auto_prefill_for_lp = 35;
  bool enable_enforce_lp_submit_review_before_signature = 36;
  bool enable_lp_profile = 39;
  bool allow_multi_step_in_subscription_review = 40;
  bool enable_aml_kyc_commenting = 41;
  bool prevent_lp_upload_supporting_doc_after_countersigned = 42;
  bool enable_supporting_doc_review = 45;
  bool disable_download_subscription_document = 47;
  bool show_orders_via_link_to_invited_lps_only = 50;
  bool enable_aml_kyc_list_after_required_fields_completed = 53;
  bool enable_comment_mentioning = 54;
  bool show_order_metadata_in_detail_view = 56;
  bool enable_import_from_fund_data = 57;
  bool enable_comment_assignment = 58;
  bool disable_ability_of_investor_to_invite_collaborator = 60;
  bool disable_ability_to_countersign = 61;
  bool disable_lp_resolving_comment = 62;
  bool enable_aml_check = 63;
  bool disable_self_service_export = 64;
  bool disable_in_app_announcement = 66;
  bool hide_all_invitation_button_on_gp_side = 68;
  bool enable_ontology_form_matching = 100;
  bool enable_ria = 101;
  bool use_new_gaia_import_order = 103;
  bool enable_one_envelope_experience = 104;
  bool enable_side_letter = 105;
  bool disable_ria_banner = 106;
  bool forcing_fund_managers_to_receive_all_email_notifications = 107;
  bool enable_data_extract_draft_form_log = 108;
  bool show_switch_allow_form_edit_post_signing = 109;
  bool allow_form_edit_post_signing = 110;
}

message DemoInfo {
  string demo_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  optional string demo_fund_data_firm_id_opt = 2 [(scalapb.field).type = "FundDataFirmId"];
  string main_demo_fund_sub_id = 3 [(scalapb.field).type = "FundSubId"]; // This config is for the whole demo
  repeated string additional_demo_fund_sub_ids = 4 [(scalapb.field).type = "FundSubId"]; // This config is for the whole demo
  repeated string additional_demo_lp_ids = 5 [(scalapb.field).type = "FundSubLpId"];
  optional string demo_marketing_data_room_workflow_id_opt = 6 [(scalapb.field).type = "DataRoomWorkflowId"];
  optional string demo_ria_fund_group_id_opt = 7 [(scalapb.field).type = "RiaFundGroupId"];
}

message FundSubSupportingContactInfo {
  string name = 1;
  string email = 2;
  string phone = 3;
}

message FundSubSupportingContacts {
  string orgName = 1;
  repeated FundSubSupportingContactInfo contacts = 2;
}

message FundSubAdminFundContact {
  string user_id = 1 [(scalapb.field).type = "UserId"];
  string phone = 2;
  bool chosen = 3;
}

message FundSubExternalFundContact {
  string name = 1;
  string role = 2;
  string email = 3;
  string phone = 4;
  bool chosen = 5;
}

enum ExportSectionFormatting {
  DefaultFormatting = 0;
  RadioCheckBoxOptionAsColumn = 1;
}

message ExportSectionInfo {
  string id = 1;
  string label = 2;
  ExportSectionFormatting format = 3;
}

message FormExportSettings {
  reserved 1;
  string capital_id = 2;
  repeated ExportSectionInfo exported_sections = 3;
}

message FormReference {
  string name = 1;
  string id_string = 2;
  string group_name = 3;
}

enum FundSubEvent {
  reserved 10, 24, 75, 30, 31;
  inviteAdmin = 0;
  inviteLp = 1;
  inviteLpCollaborator = 2;
  countersigned = 3;
  removeAdmin = 4;
  removeLp = 5;
  revokeAdminInvitation = 6;
  revokeLpInvitation = 7;
  lpSubmitted = 8;
  lpSubmittedConfirmation = 9;
  lpSendSignatureRequest = 11;
  lpCancelSignatureRequest = 12;
  lpRemindSignatureRequest = 13;
  signerDoneSignatureRequest = 14;
  signerDoneSignatureRequestToLp = 15;
  remindLpCompleteForm = 16;
  removeLpCollaborator = 17;
  adminSendCounterSignRequest = 18;
  adminSendBatchCounterSignRequest = 89;
  cancelBatchCountersignRequest = 103;
  adminCancelCounterSignRequest = 19;
  adminRemindCounterSignRequest = 20;
  signerDoneCounterSignRequest = 21;
  signerDoneCounterSignRequestToAdmin = 22;
  requestLpChange = 23;
  requestSupportingDoc = 25;
  lpSubmittedSupportingDoc = 26;
  lpFilledForm = 27;
  notifyEmailBounced = 28;
  uploadReferenceDoc = 29;
  lpDuplicatedOrderParticipantNotification = 32;
  sendSupportingDocSignatureRequest = 33;
  cancelSupportingDocSignatureRequest = 34;
  remindSupportingDocSignatureRequest = 35;
  doneSupportingDocSignatureRequest = 36;
  doneSupportingDocSignatureRequestToAdmin = 37;
  lpRequestReassignActor = 38;
  lpRequestReassignSigner = 39;
  lpRequestReassignRequester = 40;
  documentReviewAssignment = 41;
  documentReviewUnassignment = 42;
  documentReadyForReview = 43;
  unsignedDocumentReadyForReview = 59;
  countersignedDocsUpdated = 44;
  doneTaxFormSignatureRequest = 45;
  sendTaxFormSignatureRequest = 46;
  doneTaxFormSignatureRequestToRequester = 47;
  cancelTaxFormSignatureRequest = 48;
  remindTaxFormSignatureRequest = 49;
  markSubDocRequestCompleteRequester = 50;
  markSubDocRequestCompleteSigner = 51;
  remindSupportingDoc = 52;
  cancelSoftReview = 53;
  approveSoftReview = 54;
  sendFormSubmissionSignatureRequest = 55;
  doneFormSubmissionSignatureRequest = 56;
  remindFormSubmissionSignatureRequest = 57;
  cancelFormSubmissionSignatureRequest = 58;
  customEmailFromFundManager = 60;
  commentsDigestToInvestor = 61;
  commentsDigestToFundManager = 62;
  commentAssignmentsDigestToFundManager = 83;
  newLpDigestToFundManager = 63;
  countersignedDoc = 64;
  uploadedCountersignedDoc = 65;
  distributedDoc = 66;
  notifyCommentsToInvestor = 67;
  newDocumentUploadDigestToFundManager = 68;
  remindLpSignAgain = 69;
  requestChangeOnSupportingDoc = 70;
  cancelSupportingDocChangeRequest = 71;
  documentReadyForReviewDigest = 72;
  amlKycDocumentReviewAssignment = 73;
  amlKycDocumentReviewUnassignment = 74;
  documentHasNoReviewer = 76;
  batchSignCountersignRequestToSignerAndRequester = 77;
  batchSignCountersignRequestToFundAdmin = 78;
  fundActivitiesDigest = 79;
  signerFailedDocusignAuthentication = 80;
  inviteMultipleLpsPerMainLp = 81;
  inviteMultipleLpsPerCollaborator = 82;
  dataExtractRequestReadyForReview = 84;
  subscribeBlankOrderViaInvitation = 85;
  inviteAdvisor = 86;
  revokeAdvisorInvitation = 87;
  markSubscriptionAsComplete = 88;
  riaEntityLinked = 90;
  sendSideLetterSignatureRequest = 91;
  doneSideLetterSignatureRequest = 92;
  remindSideLetterSignatureRequest = 93;
  cancelSideLetterSignatureRequest = 94;
  gpSharedFirstSideLetterVersion = 95;
  gpCreatedNewSideLetterVersion = 96;
  gpAddedSideLetterFile = 97;
  gpMarkedSideLetterAsAgreed = 98;
  gpMarkedSideLetterAsCompleted = 99;
  lpCreatedSideLetterVersion = 100;
  lpAddedSideLetterSignedFile = 101;
  lpAddedSideLetterFile = 102;
  fundSubTestEmail = 104;
}

message EmailTemplateMessage {
  FundSubEvent event = 1;
  string subject = 2;
  EmailModel.Body body = 3;
  EmailModel.Body footer = 4;
  string updated_by = 5 [(scalapb.field).type = "Option[UserId]"];
  InstantMessage updated_at = 6 [(scalapb.field).type = "java.time.Instant"];
  string primaryCTA = 7;
  repeated EmailAddressMessage cc_email_addresses = 8 [(scalapb.field).type = "EmailAddress"];
}

// Fundsub-specific information for its protected link
message FundSubProtectedLinkModel {
  google.protobuf.StringValue link_id = 1 [(scalapb.field).type = "ProtectedLinkId"];
  // Add more protected related data here if needed.
}

message FundSubExportTemplateInfo {
  bool isEnabled = 1;
  repeated string template_ids = 2 [(scalapb.field).type = "FundSubExportTemplateId"];
  bool is_pdf_template_enabled = 3;
}

message InvestorFormSetting {
  bool allow_incomplete_form = 1;
}

enum LpOrderType {
  NormalOrder = 0;
  OfflineOrder = 1;
}

message FundSubSignatureBlockInfo {
  reserved 2;
  string group_name = 1;
  string original_file_id = 3 [(scalapb.field).type = "FileId"];
  anduin.protobuf.signature.PrepFieldModel prep_field = 4;
  bool is_customized = 5;
  bool is_commitment_amount = 6;
}

message RequiredSupportingDocGroupInfo {
  string group_name = 1;
  string group_instruction = 2;
  repeated string doc_names = 3;
}

message LastChangeInfo {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage changed_at = 2 [(scalapb.field).type = "java.time.Instant"];
}

message SaveProfileFromSubscriptionEvent {
  string actor = 1 [(scalapb.field).type = "UserId"];
  string lp_form_version_id = 2 [(scalapb.field).type = "FundSubLpFormVersionId"];
  string profile_id = 3 [(scalapb.field).type = "LpProfileId"];
  InstantMessage occur_at = 4 [(scalapb.field).type = "java.time.Instant"];
  bool is_auto_save = 5;
}

message SaveProfileFromSupportingFormEvent {
  string actor = 1 [(scalapb.field).type = "UserId"];
  string form_submission_id = 2 [(scalapb.field).type = "FormSubmissionId"];
  string profile_id = 3 [(scalapb.field).type = "LpProfileId"];
  InstantMessage occur_at = 4 [(scalapb.field).type = "java.time.Instant"];
  bool is_auto_save = 5;
}

message DismissSaveProfileSuggestionEvent {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occur_at = 2 [(scalapb.field).type = "java.time.Instant"];
}

message SkipOnboardingFlowEvent {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occur_at = 2 [(scalapb.field).type = "java.time.Instant"];
}

message SeenDisclaimerWhenSaveDataToProfileEvent {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occur_at = 2 [(scalapb.field).type = "java.time.Instant"];
}

message UserShouldNotShowSaveDocSuggestion {
  string user = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occur_at = 2 [(scalapb.field).type = "java.time.Instant"];
}

message UserDismissNameMismatchAlertEvent {
  string user = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occur_at = 2 [(scalapb.field).type = "java.time.Instant"];
}

message DynamicFormData {
  reserved 1, 3, 4;
  map<string, string> values = 2;
  string lp_form_id = 5 [(scalapb.field).type = "FundSubLpFormId"];
}

enum FundSubSignatureType {
  RequestedSignature = 0;
  ESignature = 1;
  UploadedSignature = 2;
}

message FormFillData {
  map<string, string> values = 1;
  repeated string viewedSections = 2;
  InstantMessage filledAt = 3 [(scalapb.field).type = "java.time.Instant"];
}

message FundSubAdminInfo {
  FormFillData testFormData = 1;
}

message TaxFormConfig {
  repeated string form_ids = 1 [(scalapb.field).type = "DynamicFormId"];
  string last_updated_by = 2 [(scalapb.field).type = "Option[UserId]"];
  InstantMessage last_updated_at = 3 [(scalapb.field).type = "Instant"];
  repeated string form_version_ids = 4 [(scalapb.field).type = "FormVersionId"];
}

message FormCommentFundSubTrackingData {
  reserved 2;
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  InstantMessage last_updated_at_opt = 3 [(scalapb.field).type = "Instant"];
}

message CustomFundIdSetting {
  bool is_enabled = 1;
  string custom_fund_id = 2;
}

message InvestorsChangedGroupEvent {
  string fund_id = 1 [(scalapb.field).type = "FundSubId"];
  repeated string lp_ids = 2 [(scalapb.field).type = "FundSubLpId"];
  string actor = 3 [(scalapb.field).type = "UserId"];
}
