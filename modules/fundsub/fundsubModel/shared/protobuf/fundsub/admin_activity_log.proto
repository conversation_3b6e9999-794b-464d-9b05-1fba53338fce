syntax = "proto3";

package anduin.protobuf.activitylog.fundsub.admin;

import "anduin/fundsub/comment/topic_data.proto";
import "fundsub/custom_data_type.proto";
import "fundsub/lp_tag.proto";
import "date_time.proto";
import "fundsub/models.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.activitylog.fundsub.admin"
  single_file: true
  import: "java.time.ZonedDateTime"
  import: "anduin.id.fundsub.FundSubBatchInvitationId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.fundsub.CustomDataColumnId"
  import: "anduin.id.issuetracker.IssueId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.TeamId"
  import: "anduin.model.id.FileId"
  import: "anduin.id.fundsub.FundSubCloseId"
  import: "anduin.id.fundsub.FundSubSelfServiceExportTemplateId"
  import: "anduin.id.fundsub.FundSubEmailTemplateId"
  import: "anduin.id.fundsub.FundSubExportTemplateId"
  import: "anduin.id.fundsub.dataextract.FundSubDataExtractRequestId"
  import: "anduin.id.form.DataTemplateVersionId"
};

message AdminInvited {
  string userId = 1 [(scalapb.field).type = "UserId"];
  FundSubAdminRole role = 2;
  string group_name = 3;
}

message AdminJoined {
  string userId = 1 [(scalapb.field).type = "UserId"];
  FundSubAdminRole role = 2;
  string group_name = 3;
}

message AdminRemoved {
  string userId = 1 [(scalapb.field).type = "UserId"];
  FundSubAdminRole role = 2;
  string group_name = 3;
}

message LpInvited {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
  repeated string collaborators = 3 [(scalapb.field).type = "UserId"];
}

message BatchLpInvited {
  string batch_invitation_id = 1 [(scalapb.field).type = "FundSubBatchInvitationId"];
  int32 success_lp = 2;
  int32 failed_lp = 3;
  int32 cancelled_lp = 4;
}

message LpJoined {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message OfflineOrderAdded {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message ConvertOfflineOrderToNormal {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message BatchOfflineOrderAdded {
  string batch_invitation_id = 1 [(scalapb.field).type = "FundSubBatchInvitationId"];
  int32 success_lp = 2;
  int32 failed_lp = 3;
  int32 cancelled_lp = 4;
}

message LpInvestedInAdditionalFund {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string originalLpId = 2 [(scalapb.field).type = "FundSubLpId"];
  string lpId = 3 [(scalapb.field).type = "FundSubLpId"];
}

message LpJoinedViaInvitationLink {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message LpRemoved {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message LpRestored {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message CollaboratorAdded {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message SentEmail {
  repeated string userIds = 1 [(scalapb.field).type = "UserId"];
}

message CollaboratorJoined {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message CollaboratorRemoved {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message CollaboratorPromoted {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message UploadedDocOnBehalf {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
  repeated FundSubFile files = 3;
}

message UploadedExecutedDocument {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
  repeated FundSubFile files = 3;
}

message RemovedUploadedExecutedDocument {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
  repeated FundSubFile files = 3;
}

message SentExecutedDocument {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
  repeated FundSubFile files = 3;
}

// This for counter esign only (not for esign counter sign request)
message SignedExecutedDocument {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
  repeated FundSubFile files = 3;
  repeated string signer_ids = 4 [(scalapb.field).type = "UserId"];
}

message SubmittedSubscriptionPackage {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
  repeated FundSubFile files = 3;
}

message UndidSubscriptionPackage {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message RequestChange {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
  bool refill_form_required = 3;
}

message AdminAccessedSubscription {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message SubscriptionDocumentReviewEnabled {}

message SubscriptionDocumentReviewDisabled {}

message SubscriptionDocumentReviewerAdded {
  string userId = 1 [(scalapb.field).type = "UserId"];
}

message SubscriptionDocumentReviewerRemoved {
  string userId = 1 [(scalapb.field).type = "UserId"];
}

message SubscriptionDocumentMarkedAsReviewed {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message SignedSubscriptionDocumentMarkedAsApproved {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message UnsignedSubscriptionDocumentMarkedAsApproved {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message SubscriptionDocumentReviewSkipped {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message UpdatedExecutedDocument {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
  repeated FundSubFile files = 3;
}

message ManualOrderActivatedByInvestor {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message RemindLpToSignAgain {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message SentReminderToUploadSupportingDoc {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message LpFilledForm {
  string investorUserId = 1 [(scalapb.field).type = "UserId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
}

message NewAdditionalDocumentUploadReport {
  int64 frequency_in_second = 1;
}

message SignatureRequestReassign {
  string old_signer = 1 [(scalapb.field).type = "UserId"];
  string new_signer = 2 [(scalapb.field).type = "UserId"];
}

message NewLpReport {}

message NewFormCommentReport {}

message NewCommentAssignmentReport {}

message NewFormCommentNotificationToInvestor {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string investor_user_id = 2 [(scalapb.field).type = "UserId"];
}

message GroupMembersMoved {
  repeated string group_members = 1 [(scalapb.field).type = "UserId"];
  string dest_group_name = 2;
}

message GroupCreated {
  string group_name = 1;
}

message GroupRenamed {
  string old_name = 1;
  string new_name = 2;
}

message GroupPermissionUpdated {
  string group_name = 1;
}

message GroupVisibilityUpdated {
  string group_name = 1;
}

message GroupDeleted {
  string group_name = 1;
}

message GroupAccessToViewUpdated {
  string view_name = 1;
}

message ViewRenamed {
  string old_name = 1;
  string new_name = 2;
}

message ViewCreated {
  string view_name = 1;
}

message DashboardColumnsUpdated {
  string view_name = 1;
}

message ViewDeleted {
  string view_name = 1;
}

message PrivateViewShared {
  string view_name = 1;
}

message InvestorGroupCreated {
  string group_name = 1;
}

message InvestorGroupRenamed {
  string old_name = 1;
  string new_name = 2;
}

message InvestorGroupDeleted {
  string group_name = 1;
}

message InvestorAssignedToGroup {
  repeated string lp_ids = 1 [(scalapb.field).type = "FundSubLpId"];
  string group_name = 2;
}

message InvestorMovedToAnotherGroup {
  repeated string lp_ids = 1 [(scalapb.field).type = "FundSubLpId"];
  string dest_group_name = 3;
}

message InvestorUnassignedFromGroup {
  repeated string lp_ids = 1 [(scalapb.field).type = "FundSubLpId"];
  string group_name = 2;
}

message InvestorGroupAccessibilityGranted {
  string admin_group_name = 1;
  string investor_group_name = 2;
}

message InvestorGroupAccessibilityWithdrawn {
  string admin_group_name = 1;
  string investor_group_name = 2;
}

// Use message here so we can add more detail in the future
message AmlKycReviewTurnedOff {}
message AmlKycReviewTurnedOn {}

message AmlKycReviewUpdated {
  bool wasDocGroupEnabled = 1;
  bool isDocGroupEnabled = 2;
}

message AmlKycReviewConfigUpdateAction {
  oneof sealed_value {
    AmlKycReviewTurnedOff aml_kyc_review_turned_off = 1;
    AmlKycReviewTurnedOn aml_kyc_review_turned_on = 2;
    AmlKycReviewUpdated aml_kyc_review_updated = 3;
  }
}

message AmlKycReviewConfigUpdated {
  AmlKycReviewConfigUpdateAction action = 1;
}

message SignedSubscriptionDocReviewTurnedOff {}
message SignedSubscriptionDocReviewTurnedOn {}
message SignedSubscriptionDocReviewUpdated {}

message SignedSubscriptionDocReviewConfigUpdateAction {
  oneof sealed_value {
    SignedSubscriptionDocReviewTurnedOff signed_subscription_doc_review_turned_off = 1;
    SignedSubscriptionDocReviewTurnedOn signed_subscription_doc_review_turned_on = 2;
    SignedSubscriptionDocReviewUpdated signed_subscription_doc_review_updated = 3;
  }
}

message SignedSubscriptionDocReviewConfigUpdated {
  SignedSubscriptionDocReviewConfigUpdateAction action = 1;
}

message UnsignedSubscriptionDocReviewTurnedOff {}
message UnsignedSubscriptionDocReviewTurnedOn {}
message UnsignedSubscriptionDocReviewUpdated {}

message UnsignedSubscriptionDocReviewConfigUpdateAction {
  oneof sealed_value {
    UnsignedSubscriptionDocReviewTurnedOff unsigned_subscription_doc_review_turned_off = 1;
    UnsignedSubscriptionDocReviewTurnedOn unsigned_subscription_doc_review_turned_on = 2;
    UnsignedSubscriptionDocReviewUpdated unsigned_subscription_doc_review_updated = 3;
  }
}

message UnsignedSubscriptionDocReviewConfigUpdated {
  UnsignedSubscriptionDocReviewConfigUpdateAction action = 1;
}

message AmendmentDetail {
  string amended_file_id = 1 [(scalapb.field).type = "FileId"];
  string original_subscription_file_id = 2 [(scalapb.field).type = "FileId"];
  string amendment_file_id = 3 [(scalapb.field).type = "FileId"];
  int32 amendment_insert_before_page_index = 4;
  bool is_insert_before_signature_page = 5;
}

message AmendmentAdded {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string investor_user_id = 2 [(scalapb.field).type = "UserId"];
  AmendmentDetail amendment = 3;
}

message AmendmentEdited {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string investor_user_id = 2 [(scalapb.field).type = "UserId"];
  AmendmentDetail amendment = 3;
}

message AmendmentRemoved {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string investor_user_id = 2 [(scalapb.field).type = "UserId"];
}

// deprecated
message UpdateInvestorValueActionAdd {}

// deprecated
message UpdateInvestorValueActionRemove {}

// deprecated
message UpdateInvestorValueAction {
  oneof sealed_value {
    UpdateInvestorValueActionAdd update_investor_value_action_add = 1;
    UpdateInvestorValueActionRemove update_investor_value_action_remove = 2;
  }
}

// deprecated
message UpdateInvestorValueTypeTag {}

// deprecated
message UpdateInvestorValueTypeSingleSelect {
  string custom_column_id = 1 [(scalapb.field).type = "CustomDataColumnId"];
  string column_name = 2;
}

// deprecated
message UpdateInvestorValueTypeMultipleSelect {
  string custom_column_id = 1 [(scalapb.field).type = "CustomDataColumnId"];
  string column_name = 2;
}

// deprecated
message UpdateInvestorValueType {
  oneof sealed_value {
    UpdateInvestorValueTypeTag update_investor_value_type_tag = 1;
    UpdateInvestorValueTypeSingleSelect update_investor_value_type_single_select = 2;
    UpdateInvestorValueTypeMultipleSelect update_investor_value_type_multiple_select = 3;
  }
}

// deprecated
message UpdateInvestorsValues {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string investor_user_id = 2 [(scalapb.field).type = "UserId"];
  UpdateInvestorValueAction update_investor_value_action = 3;
  UpdateInvestorValueType update_investor_value_type = 4;
  repeated string updated_values = 5;
}

message CustomLpIdUpdated {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string current_custom_lp_id = 2;
  string new_custom_lp_id = 3;
}

message CustomFundIdUpdated {
  bool was_enabled = 1;
  bool is_enabled = 2;
  string old_custom_fund_id = 3;
  string new_custom_fund_id = 4;
}

enum CommentAuditLogAction {
  COMMENT_AUDIT_LOG_ACTION_ADD = 0;
  COMMENT_AUDIT_LOG_ACTION_EDIT = 1;
  COMMENT_AUDIT_LOG_ACTION_DELETE_REPLY = 2;
  COMMENT_AUDIT_LOG_ACTION_DELETE_THREAD = 3;
  COMMENT_AUDIT_LOG_ACTION_RESOLVE = 4;
  COMMENT_AUDIT_LOG_ACTION_REOPEN = 5;
}

message CommentActivity {
  string issue_id = 1 [(scalapb.field).type = "IssueId"];
  anduin.protobuf.fundsub.comment.TopicData topic_data = 2;
  bool is_internal = 3;
  string lp_id = 4 [(scalapb.field).type = "FundSubLpId"];
  optional string comment_id = 5;
  CommentAuditLogAction action = 6;
}

enum AnchorPointAuditLogAction {
  ANCHOR_POINT_AUDIT_LOG_ACTION_ASSIGN = 0;
  ANCHOR_POINT_AUDIT_LOG_ACTION_REASSIGN = 1;
  ANCHOR_POINT_AUDIT_LOG_ACTION_UNASSIGN = 2;
}

message AnchorPointActivity {
  anduin.protobuf.fundsub.comment.TopicData topic_data = 1;
  string lp_id = 2 [(scalapb.field).type = "FundSubLpId"];
  string assigned_gp_user_id_opt = 3 [(scalapb.field).type = "Option[UserId]"];
  string assigned_gp_team_id_opt = 4 [(scalapb.field).type = "Option[TeamId]"];
  AnchorPointAuditLogAction action = 5;
}

message CommentExported {
  bool including_internal_comment = 1;
}

message CommentSettingUpdateActivity {
  enum CommentSettingUpdateAction {
    ENABLE_LP_RESOLVING_COMMENT = 0;
    DISABLE_LP_RESOLVING_COMMENT = 1;
  }
  CommentSettingUpdateAction action = 1;
}

message DataExtractRequestInfo {
  reserved 1, 4;
  option (scalapb.message).no_box = true;
  string lp_id = 2 [(scalapb.field).type = "FundSubLpId"];
  string lp_user_id = 3 [(scalapb.field).type = "UserId"];
  string data_extract_request_id = 5 [(scalapb.field).type = "FundSubDataExtractRequestId"];
}

message DataExtractionStarted {
  DataExtractRequestInfo request_info = 1;
  repeated string submitted_files = 2 [(scalapb.field).type = "FileId"];
}

message ExtractedDataReadyForReview {
  repeated DataExtractRequestInfo request_infos = 1;
}

message ExtractedDataMarkedAsComplete {
  DataExtractRequestInfo request_info = 1;
}

message ExtractedDataEdited {
  DataExtractRequestInfo request_info = 1;
}

message CreateClose {
  string fund_sub_close_id = 1 [(scalapb.field).type = "FundSubCloseId"];
  string name = 2;
  string custom_close_id = 3;
  ZonedDateTimeMessage closing_date_zoned = 4 [(scalapb.field).type = "ZonedDateTime"];
  bool to_default_close = 5;
}

message UpdateClose {
  string fund_sub_close_id = 1 [(scalapb.field).type = "FundSubCloseId"];
  string name = 2;
  string custom_close_id = 3;
  ZonedDateTimeMessage closing_date_zoned = 4 [(scalapb.field).type = "ZonedDateTime"];
  bool to_default_close = 5;
}

message DeleteClose {
  string fund_sub_close_id = 1 [(scalapb.field).type = "FundSubCloseId"];
  string fund_sub_close_id_to_transfer = 2 [(scalapb.field).type = "Option[FundSubCloseId]"];
  string fund_sub_close_id_to_default = 3 [(scalapb.field).type = "Option[FundSubCloseId]"];
  string name = 4;
}

message MoveLpToNewClose {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string old_fund_sub_close_id = 2 [(scalapb.field).type = "Option[FundSubCloseId]"];
  string old_fund_sub_close_name = 3;
  string new_fund_sub_close_id = 4 [(scalapb.field).type = "Option[FundSubCloseId]"];
  string new_fund_sub_close_name = 5;
}

message SelfServiceExportTemplateCreated {
  string template_id = 1 [(scalapb.field).type = "FundSubSelfServiceExportTemplateId"];
  string template_name = 2;
}

message SelfServiceExportTemplateRenamed {
  string template_id = 1 [(scalapb.field).type = "FundSubSelfServiceExportTemplateId"];
  string old_template_name = 2;
  string new_template_name = 3;
}

message SelfServiceExportTemplateUpdated {
  string template_id = 1 [(scalapb.field).type = "FundSubSelfServiceExportTemplateId"];
  string template_name = 2;
}

message SelfServiceExportTemplateDeleted {
  string template_id = 1 [(scalapb.field).type = "FundSubSelfServiceExportTemplateId"];
  string template_name = 2;
}
message InvestorDataExported {
  optional string self_service_template_id_opt = 1 [(scalapb.field).type = "FundSubSelfServiceExportTemplateId"];
  optional string advance_export_template_id_opt = 2 [(scalapb.field).type = "FundSubExportTemplateId"];
  string template_name = 3;
  repeated string lp_ids = 4 [(scalapb.field).type = "FundSubLpId"];
  optional string data_template_version_id_opt = 5 [(scalapb.field).type = "DataTemplateVersionId"];
}

message EmailTemplateCreated {
  string template_id = 1 [(scalapb.field).type = "FundSubEmailTemplateId"];
  string template_name = 2;
  FundSubEvent email_event = 3;
}

message EmailTemplateUpdated {
  string template_id = 1 [(scalapb.field).type = "FundSubEmailTemplateId"];
  string template_name = 2;
  FundSubEvent email_event = 3;
}

message EmailTemplateDeleted {
  string template_id = 1 [(scalapb.field).type = "FundSubEmailTemplateId"];
  string template_name = 2;
  FundSubEvent email_event = 3;
}

message EmailTemplateRenamed {
  string template_id = 1 [(scalapb.field).type = "FundSubEmailTemplateId"];
  string old_template_name = 2;
  string new_template_name = 3;
  FundSubEvent email_event = 4;
}

message EmailTemplateSetAsDefault {
  string template_id = 1 [(scalapb.field).type = "FundSubEmailTemplateId"];
  string template_name = 2;
  FundSubEvent email_event = 3;
}

message SignatureDateFormatUpdated {
  string user_id = 1 [(scalapb.field).type = "UserId"];
}

message MarkedSubscriptionAsComplete {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string lp_user_id = 2 [(scalapb.field).type = "UserId"];
}

message AdvisorEntityJoined {
  string entity_name = 1;
  string linker = 2 [(scalapb.field).type = "UserId"];
}

message AdvisorEntityNameUpdated {
  string old_name = 1;
  string new_name = 2;
}

message AdvisorInvited {
  string advisor = 1 [(scalapb.field).type = "UserId"];
  string entity_name = 2;
}

message AdvisorJoined {
  string entity_name = 1;
}

message ResendAdvisorInvitation {
  string advisor = 1 [(scalapb.field).type = "UserId"];
  string entity_name = 2;
}

message RevokeAdvisorInvitation {
  string advisor = 1 [(scalapb.field).type = "UserId"];
  string entity_name = 2;
}

message DisableAdvisorEntityCreateNewSubscription {
  string entity_name = 2;
}

message EnableAdvisorEntityCreateNewSubscription {
  string entity_name = 2;
}

message RiaOrderCreated {
  string entity_name = 1;
  string lp_id = 2 [(scalapb.field).type = "FundSubLpId"];
}

message ConvertToRiaOrder {
  string entity_name = 1;
  string lp_id = 2 [(scalapb.field).type = "FundSubLpId"];
}

message UnlinkRiaOrder {
  string entity_name = 1;
  string lp_id = 2 [(scalapb.field).type = "FundSubLpId"];
}

message SideLetterVersionCreated {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated string files = 3 [(scalapb.field).type = "FileId"];
  int32 version = 2;
}

message SideLetterFilesUploaded {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated string files = 2 [(scalapb.field).type = "FileId"];
  int32 version = 3;
}

message SideLetterFilesRemoved {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated string files = 2 [(scalapb.field).type = "FileId"];
  int32 version = 3;
}

message MarkSideLetterAsAgreed {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated string files = 2 [(scalapb.field).type = "FileId"];
  int32 version = 3;
}

message MarkSideLetterCompleted {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated string files = 2 [(scalapb.field).type = "FileId"];
  int32 version = 3;
}

message CustomColumnCreated {
  string custom_data_column_id = 1 [(scalapb.field).type = "CustomDataColumnId"];
  CustomData column_type = 2;
  string column_name = 3;
}

message CustomColumnDeleted {
  string custom_data_column_id = 1 [(scalapb.field).type = "CustomDataColumnId"];
  CustomData column_type = 2;
  string column_name = 3;
}

message CustomColumnRenamed {
  string custom_data_column_id = 1 [(scalapb.field).type = "CustomDataColumnId"];
  CustomData column_type = 2;
  string old_column_name = 3;
  string new_column_name = 4;
}

message CustomColumnValueUpdated {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string custom_data_column_id = 2 [(scalapb.field).type = "CustomDataColumnId"];
  string column_name = 3;
  CustomData old_data = 4;
  CustomData new_data = 5;
}

message TagListUpdated {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated anduin.protobuf.fundsub.tag.lp.FundSubLpTag added_tags = 2;
  repeated anduin.protobuf.fundsub.tag.lp.FundSubLpTag removed_tags = 3;
}

message AdminUpdateAllowFormEditPostSigning {
  bool is_enabled = 1;
}

message FundAdminActivity {
  oneof sealed_value {
    AdminInvited admin_invited = 1;
    AdminJoined admin_joined = 2;
    AdminRemoved admin_removed = 3;
    LpInvited lp_invited = 4;
    LpJoined lp_joined = 5;
    OfflineOrderAdded offline_order_added = 6;
    LpInvestedInAdditionalFund lp_invested_in_additional_fund = 7;
    LpJoinedViaInvitationLink lp_joined_via_invitation_link = 8;
    LpRemoved lp_removed = 9;
    CollaboratorAdded collaborator_added = 10;
    CollaboratorJoined collaborator_joined = 11;
    CollaboratorRemoved collaborator_removed = 12;
    UploadedDocOnBehalf upload_doc_on_behalf = 13;
    UploadedExecutedDocument upload_executed_document = 15;
    SignedExecutedDocument signed_executed_document = 16;
    SentExecutedDocument sent_executed_document = 17;
    SubmittedSubscriptionPackage submitted_subscription_package = 18;
    UndidSubscriptionPackage undid_subscription_package = 19;
    RequestChange request_change = 20;
    AdminAccessedSubscription admin_accessed_subscription = 21;
    SubscriptionDocumentReviewEnabled subscription_document_review_enabled = 22;
    SubscriptionDocumentReviewDisabled subscription_document_review_disabled = 23;
    SubscriptionDocumentReviewerAdded subscription_document_reviewer_added = 24;
    SubscriptionDocumentReviewerRemoved subscription_document_reviewer_removed = 25;
    SubscriptionDocumentMarkedAsReviewed subscription_document_marked_as_reviewed = 26;

    SubscriptionDocumentReviewSkipped subscription_document_review_skipped = 27;
    RemovedUploadedExecutedDocument removed_uploaded_executed_document = 28;
    UpdatedExecutedDocument updated_executed_document = 29;
    LpRestored lp_restored = 30;
    ManualOrderActivatedByInvestor manual_order_activated_by_investor = 31;
    BatchLpInvited batch_lp_invited = 32;
    BatchOfflineOrderAdded batch_offline_order_added = 33;
    SentEmail sent_email = 34;
    ConvertOfflineOrderToNormal convert_offline_order_to_normal = 35;
    RemindLpToSignAgain remind_lp_to_sign_again = 36;
    SentReminderToUploadSupportingDoc sent_reminder_to_upload_supporting_doc = 37;
    LpFilledForm lp_filled_form = 38;
    NewAdditionalDocumentUploadReport new_additional_document_upload_report = 39;
    NewLpReport new_lp_report = 40;
    NewFormCommentReport new_form_comment_report = 41;

    SignatureRequestReassign signature_request_reassign = 42;

    GroupMembersMoved group_members_moved = 43;
    GroupCreated group_created = 44;
    GroupRenamed group_renamed = 45;
    GroupPermissionUpdated group_permission_updated = 46;
    GroupVisibilityUpdated group_visibility_updated = 47;
    GroupDeleted group_deleted = 48;
    GroupAccessToViewUpdated group_access_to_view_updated = 49;
    ViewRenamed view_renamed = 50;
    ViewCreated view_created = 51;
    DashboardColumnsUpdated dashboard_columns_updated = 52;
    ViewDeleted view_deleted = 53;
    PrivateViewShared private_view_shared = 54;

    InvestorGroupCreated investor_group_created = 55;
    InvestorGroupRenamed investor_group_renamed = 56;
    InvestorGroupDeleted investor_group_deleted = 57;
    InvestorAssignedToGroup investor_assigned_to_group = 58;
    InvestorMovedToAnotherGroup investor_moved_to_another_group = 59;
    InvestorUnassignedFromGroup investor_unassigned_from_group = 60;
    InvestorGroupAccessibilityGranted investor_group_accessibility_granted = 61;
    InvestorGroupAccessibilityWithdrawn investor_group_accessibility_withdrawn = 62;

    AmlKycReviewConfigUpdated aml_kyc_review_config_updated = 63;
    SignedSubscriptionDocReviewConfigUpdated signed_subscription_doc_review_updated = 64;
    UnsignedSubscriptionDocReviewConfigUpdated unsigned_subscription_doc_review_config_updated = 65;

    NewFormCommentNotificationToInvestor new_form_comment_notification_to_investor = 66;

    SignedSubscriptionDocumentMarkedAsApproved signed_subscription_document_marked_as_approved = 67;
    UnsignedSubscriptionDocumentMarkedAsApproved unsigned_subscription_document_marked_as_approved = 68;

    AmendmentAdded amendment_added = 69;
    AmendmentEdited amendment_edited = 70;
    AmendmentRemoved amendment_removed = 71;

    UpdateInvestorsValues update_investors_values = 72;

    CustomLpIdUpdated custom_lp_id_updated = 73;
    CustomFundIdUpdated custom_fund_id_updated = 74;

    CommentActivity comment_activity = 75;
    AnchorPointActivity anchor_point_activity = 76;
    NewCommentAssignmentReport new_comment_assignment_report = 77;
    CommentExported comment_exported = 78;
    CommentSettingUpdateActivity comment_setting_update_activity = 82;

    DataExtractionStarted data_extraction_started = 79;
    ExtractedDataReadyForReview extracted_data_ready_for_review = 80;
    ExtractedDataMarkedAsComplete extracted_data_marked_as_complete = 81;
    ExtractedDataEdited extracted_data_edited = 83;

    CreateClose create_close = 84;
    UpdateClose update_close = 85;
    DeleteClose delete_close = 86;
    MoveLpToNewClose move_lp_to_new_close = 87;

    SelfServiceExportTemplateCreated self_service_export_template_created = 88;
    SelfServiceExportTemplateRenamed self_service_export_template_renamed = 89;
    SelfServiceExportTemplateUpdated self_service_export_template_updated = 90;
    SelfServiceExportTemplateDeleted self_service_export_template_deleted = 91;
    InvestorDataExported investor_data_exported = 92;

    EmailTemplateCreated email_template_created = 93;
    EmailTemplateUpdated email_template_updated = 94;
    EmailTemplateRenamed email_template_renamed = 95;
    EmailTemplateDeleted email_template_deleted = 96;
    EmailTemplateSetAsDefault email_template_set_as_default = 97;

    SignatureDateFormatUpdated signature_date_format_updated = 98;

    MarkedSubscriptionAsComplete marked_subscription_as_complete = 99;

    // Ria
    AdvisorEntityJoined advisor_entity_joined = 100;
    AdvisorEntityNameUpdated advisor_entity_name_updated = 101;
    AdvisorInvited advisor_invited = 102;
    AdvisorJoined advisor_joined = 103;
    RevokeAdvisorInvitation revoke_advisor_invitation = 104;
    ResendAdvisorInvitation resend_advisor_invitation = 105;
    DisableAdvisorEntityCreateNewSubscription disable_advisor_entity_create_new_subscription = 106;
    EnableAdvisorEntityCreateNewSubscription enable_advisor_entity_create_new_subscription = 107;
    RiaOrderCreated ria_order_created = 108;
    ConvertToRiaOrder convert_to_ria_order = 111;
    UnlinkRiaOrder unlink_ria_order = 112;

    CollaboratorPromoted collaborator_promoted = 109;

    // Side Letter
    SideLetterVersionCreated side_letter_version_created = 113;
    SideLetterFilesUploaded side_letter_files_uploaded = 114;
    SideLetterFilesRemoved side_letter_files_removed = 115;
    MarkSideLetterAsAgreed side_letter_marked_as_agreed = 116;
    MarkSideLetterCompleted side_letter_marked_as_completed = 117;

    // Custom data column
    CustomColumnCreated custom_column_created = 118;
    CustomColumnDeleted custom_column_deleted = 119;
    CustomColumnRenamed custom_column_renamed = 120;
    CustomColumnValueUpdated custom_column_value_updated = 121;

    TagListUpdated tag_list_updated = 122;

    AdminUpdateAllowFormEditPostSigning admin_update_allow_form_edit_post_signing = 123;
  }
}
