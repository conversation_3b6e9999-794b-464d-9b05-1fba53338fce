syntax = "proto3";

package anduin.protobuf.fundsub.models.copyconfigdata;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.models.copyconfigdata"
  single_file: true
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
};


message FundSubCopyConfigData {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  map<string, string> copy_config_map = 2;
  string updated_by = 3 [(scalapb.field).type = "Option[UserId]"];
  InstantMessage updated_at = 4 [(scalapb.field).type = "Instant"];
  bool is_disable = 5;
};

message RecordTypeUnion {
  FundSubCopyConfigData _CopyConfigData = 1;
}
