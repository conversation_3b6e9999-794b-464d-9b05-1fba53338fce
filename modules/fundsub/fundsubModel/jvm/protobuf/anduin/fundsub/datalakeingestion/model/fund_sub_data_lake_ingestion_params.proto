syntax = "proto3";

import "anduin/amlcheck/shared_aml_check.proto";
import "date_time.proto";
import "external/squants.proto";
import "fundsub/custom_data_type.proto";
import "fundsub/fund_sub_data_extract.proto";
import "fundsub/models.proto";
import "fundsub/models/signature/fund_sub_signature.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/wrappers.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.fundsub.datalakeingestion.model"
  single_file: true
  import: "anduin.model.codec.ProtoCodecs.jsonValueMapper"
  import: "anduin.fundsub.model.FundSubEnumTypeMappers.given"
  import: "anduin.id.fundsub.FundSubCloseId"
  import: "anduin.id.amlkyc.AmlCheckId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.fundsub.FundSubLpTagId"
  import: "anduin.id.fundsub.CustomDataColumnId"
  import: "anduin.id.fundsub.InvestmentFundId"
  import: "anduin.id.fundsub.dataextract.FundSubDataExtractRequestId"
  import: "anduin.id.fundsub.group.FundSubInvestorGroupId"
  import: "anduin.id.fundsub.ria.FundSubRiaGroupId"
  import: "anduin.id.funddata.FundDataInvestorId"
  import: "anduin.id.form.DataTemplateVersionId"
  import: "anduin.id.review.ReviewFlowId"
  import: "anduin.id.review.ReviewStepConfigId"
  import: "anduin.id.signature.SignatureRequestId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.FileId"
  import: "anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus"
  import: "anduin.protobuf.fundsub.LpOrderType"
  import: "anduin.protobuf.external.squants.CurrencyMessage"
  import: "io.circe.Json"
  import: "java.time.Instant"
  import: "java.time.LocalDate"
  lenses: false
  getters: false
  retain_source_code_info: false
  no_default_values_in_constructor: true
  bytes_type: "scodec.bits.ByteVector"
  preamble: [
    "sealed trait FundSubDataLakeIngestionParamsTrait {",
    "  def lpIdOpt: Option[FundSubLpId] = None",
    "}"
  ];
};

message FundSubDataLakeIngestionParams {
  option (scalapb.message).sealed_oneof_extends = "FundSubDataLakeIngestionParamsTrait";
  reserved 1, 3001;

  oneof sealed_value {
    AddOrUpdateFundParams add_or_update_fund_params = 13;
    AddOrderParams add_order_params = 2;
    LogViewOrderEventParams log_view_order_event_params = 3;
    UpdateCommitmentAmountParams update_commitment_amount_params = 4;
    UpdateOrAddCloseParams update_close_params = 5;
    RemoveCloseParams remove_close_params = 6;
    UpdateOrAddTagParams update_or_add_tag_params = 7;
    RemoveTagParams remove_tag_params = 8;
    UpdateOrAddCustomDataParams update_or_add_note_params = 9;
    UpdateOrderBasicInfoParams update_order_basic_info_params = 10;
    UpdateRequiredDocsParams update_required_docs_params = 11;
    UpdateOrAddOrderSignatureRequestsParams update_or_add_order_signature_request_params = 12;
    RemoveCustomDataColumnParams remove_note_column_params = 14;
    UpdateOrAddTemplateParams update_or_add_template_params = 15;
    PromoteCollaboratorParams promote_investor_params = 16;
    UpdateOrAddAmlKycReviewParams update_or_add_aml_kyc_review_params = 17;
    UpdateFormDataParams update_form_data_params = 2000;
    UpdateCollaboratorsParams update_collaborators_params = 3000;
    UpdateProvidedDocsParams update_provided_docs_params = 18;
    UpdateUserInfoParams update_user_info_params = 19;
    UpdateOrAddSubdocDataExtractRequestParams update_or_add_subdoc_data_extract_request_params = 20;
    UpdateOrAddAmlCheckParams update_or_add_aml_check_request_params = 21;
    RemoveAmlCheckParams remove_aml_check_params = 22;
    UpdateOrAddAdvisorGroupParams update_or_add_advisor_group_params = 23;
    RemoveAdvisorGroupParams remove_advisor_group_params = 24;
    UpdateOrAddInvestorGroupParams update_or_add_investor_group_params = 25;
    RemoveInvestorGroupParams remove_investor_group_params = 26;
  }
}

message UserBasicInfo {
  string id = 1 [(scalapb.field).type = "UserId"];
  string email = 2;
  string first_name = 3;
  string last_name = 4;
}

message CloseInfo {
  string id = 1 [(scalapb.field).type = "FundSubCloseId"];
  string name = 2;
  LocalDateMessage target_closing_date = 3 [(scalapb.field).type = "LocalDate"];
  string customCloseId = 4;
}

message TagInfo {
  string id = 1 [(scalapb.field).type = "FundSubLpTagId"];
  string name = 2;
  UserBasicInfo creator = 3;
  InstantMessage created_at = 4 [(scalapb.field).type = "Instant"];
}

message UpdateUserInfoParams {
  string id = 1 [(scalapb.field).type = "UserId"];
  string first_name = 2;
  string last_name = 3;
  repeated string related_lp_ids = 4 [(scalapb.field).type = "FundSubLpId"];
}

message SubFundMessage {
  string id = 1 [(scalapb.field).type = "anduin.id.fundsub.InvestmentFundId"];
  string custom_id = 4;
  external.squants.CurrencyMessage currency = 2;
  string name = 3;
}

message AddOrUpdateFundParams {
  option (scalapb.message).no_default_values_in_constructor = false;

  string id = 1 [(scalapb.field).type = "FundSubId"];
  repeated UserBasicInfo add_admins = 2;
  repeated string remove_admins = 3 [(scalapb.field).type = "UserId"];
  int32 set_currency = 4 [(scalapb.field).type = "Option[CurrencyMessage]"];
  repeated CloseInfo add_closes = 5;
  repeated string remove_closes = 6 [(scalapb.field).type = "FundSubCloseId"];
  repeated TagInfo add_tags = 7;
  repeated string remove_tags = 8 [(scalapb.field).type = "FundSubLpTagId"];
  google.protobuf.BoolValue set_inactive_notification_enabled = 9;
  google.protobuf.Int32Value set_inactive_notification_duration = 10;
  repeated FileInfo add_reference_docs = 11;
  repeated string remove_reference_docs = 12 [(scalapb.field).type = "FileId"];
  google.protobuf.StringValue set_fund_name = 13;
  repeated string add_templates = 14 [(scalapb.field).type = "DataTemplateVersionId"];
  repeated string remove_templates = 15 [(scalapb.field).type = "DataTemplateVersionId"];
  google.protobuf.StringValue set_custom_fund_id = 16;
  google.protobuf.BoolValue set_supporting_doc_review_enabled = 17;
  repeated SubFundMessage set_sub_funds = 18;
  repeated AdvisorGroupInfo add_advisor_groups = 19;
  repeated string remove_advisor_groups = 20 [(scalapb.field).type = "FundSubRiaGroupId"];
  repeated InvestorGroupInfo add_investor_groups = 21;
  repeated string remove_investor_groups = 22 [(scalapb.field).type = "FundSubInvestorGroupId"];
}

message AddOrderParams {
  reserved 5, 7;

  string lp_id_opt = 1 [
    (scalapb.field).annotations = "override val",
    (scalapb.field).type = "Option[FundSubLpId]"
  ];
  UserBasicInfo main_lp = 2;
  repeated UserBasicInfo collaborators = 3;
  string investment_entity = 4;
  repeated SubFundCommitmentAmountMessage commitment_amounts = 17;
  string close_id_opt = 6 [(scalapb.field).type = "Option[FundSubCloseId]"];
  repeated FileInfo reference_docs = 8;
  LpOrderType lp_order_type = 9;
  InstantMessage created_at = 10 [(scalapb.field).type = "Instant"];
  InstantMessage last_activity_at = 11 [(scalapb.field).type = "Instant"];
  repeated string tags = 12 [(scalapb.field).type = "FundSubLpTagId"];
  string custom_id = 13;
  google.protobuf.Int32Value status = 14 [(scalapb.field).type = "LpStatus"];
  double initial_form_progress = 15;
  map<string, string> metadata = 16;
  repeated FormFieldValue form_fields = 18;
  string advisor_group_id_opt = 19 [(scalapb.field).type = "Option[FundSubRiaGroupId]"];
  string investor_group_id_opt = 20 [(scalapb.field).type = "Option[FundSubInvestorGroupId]"];
}

message LogViewOrderEventParams {
  string lp_id_opt = 1 [
    (scalapb.field).annotations = "override val",
    (scalapb.field).type = "Option[FundSubLpId]"
  ];
  UserBasicInfo actor = 2;
}

message SubFundCommitmentAmountMessage {
  string id = 1 [(scalapb.field).type = "InvestmentFundId"];
  google.protobuf.DoubleValue expected_commitment = 2;
  google.protobuf.DoubleValue submitted_commitment = 3;
  google.protobuf.DoubleValue accepted_commitment = 4;
}

message UpdateCommitmentAmountParams {
  string lp_id_opt = 1 [
    (scalapb.field).annotations = "override val",
    (scalapb.field).type = "Option[FundSubLpId]"
  ];
  google.protobuf.DoubleValue expected_commitment = 2;
  google.protobuf.DoubleValue submitted_commitment = 3;
  google.protobuf.DoubleValue accepted_commitment = 4;
  repeated SubFundCommitmentAmountMessage commitment_amounts = 5;
}

message UpdateOrAddCloseParams {
  string id = 1 [(scalapb.field).type = "FundSubCloseId"];
  string name = 2;
  LocalDateMessage target_closing_date = 3 [(scalapb.field).type = "LocalDate"];
  string customCloseId = 4;
}

message RemoveCloseParams {
  string id = 1 [(scalapb.field).type = "FundSubCloseId"];
}

message UpdateOrAddTagParams {
  string id = 1 [(scalapb.field).type = "FundSubLpTagId"];
  string name = 2;
  UserBasicInfo creator = 3;
  InstantMessage created_at = 4 [(scalapb.field).type = "Instant"];
}

message RemoveTagParams {
  string id = 1 [(scalapb.field).type = "FundSubLpTagId"];
}

message UpdateOrAddCustomDataParams {
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  string custom_data_column_id = 2 [(scalapb.field).type = "CustomDataColumnId"];
  string column_name = 3;
  UserBasicInfo creator = 4;
  InstantMessage created_at = 5 [(scalapb.field).type = "Instant"];
  CustomData custom_data = 6;
}

message RemoveCustomDataColumnParams {
  string custom_data_column_id = 1 [(scalapb.field).type = "CustomDataColumnId"];
}

message UpdateProvidedDocsParams {
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  repeated string docs = 2;
}

message FileInfo {
  string id = 1 [(scalapb.field).type = "FileId"];
  string name = 2;
  UserBasicInfo uploader = 3;
  InstantMessage uploadedAt = 4 [(scalapb.field).type = "Instant"];
}

message LpDocument {
  string id = 3;
  FileInfo file = 1;
  FundSubDocType docType = 2;
  string lp_id = 4 [(scalapb.field).type = "FundSubLpId"];
}

message LpDocumentsListContainer {
  repeated LpDocument docs = 1;
}

message SignatureRequestBasicInfo {
  string id = 1 [(scalapb.field).type = "SignatureRequestId"];
  anduin.fundsub.signature.FundSubSignatureRequestStatusMessage status = 2;
  UserBasicInfo creator = 3;
  InstantMessage createdAt = 4 [(scalapb.field).type = "Instant"];
}

enum SideLetterStatus {
  SIDE_LETTER_STATUS_NOT_STARTED = 0;
  SIDE_LETTER_STATUS_IN_NEGOTIATION = 1;
  SIDE_LETTER_STATUS_AGREED = 2;
  SIDE_LETTER_STATUS_PENDING_SIGNATURE = 3;
  SIDE_LETTER_STATUS_SIGNED = 4;
  SIDE_LETTER_STATUS_COMPLETED = 5;
}

// TODO @tuananhtd: consider to add existing update params for Order's direct field into this
message UpdateOrderBasicInfoParams {
  option (scalapb.message).no_default_values_in_constructor = false;
  reserved 18, 25;

  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  google.protobuf.StringValue set_investment_entity_name = 2;
  google.protobuf.Int32Value set_order_type = 3 [(scalapb.field).type = "LpOrderType"];
  google.protobuf.StringValue set_close = 4 [(scalapb.field).type = "FundSubCloseId"];
  repeated string add_tags = 5 [(scalapb.field).type = "FundSubLpTagId"];
  repeated string remove_tags = 6 [(scalapb.field).type = "FundSubLpTagId"];
  repeated FileInfo add_reference_files = 7;
  repeated string remove_reference_files = 8 [(scalapb.field).type = "FileId"];
  google.protobuf.Int32Value set_lp_status = 9 [(scalapb.field).type = "LpStatus"];
  repeated UserBasicInfo add_seen_by_users = 10;
  LpDocumentsListContainer set_submitted_docs_list = 11;
  LpDocumentsListContainer set_uploaded_countersigned_docs_list = 12;
  LpDocumentsListContainer set_countersigned_docs_list = 13;
  InstantMessage set_last_reinvitation_sent_at = 14 [(scalapb.field).type = "Instant"];
  repeated SignatureRequestBasicInfo add_countersignature_requests = 15;
  repeated string remove_countersignature_request = 16 [(scalapb.field).type = "SignatureRequestId"];
  InstantMessage set_last_reminder_sent_at = 17 [(scalapb.field).type = "Instant"];
  repeated UserBasicInfo set_email_bounced_users = 19;
  repeated string remove_email_bounced_users = 20 [(scalapb.field).type = "UserId"];
  repeated string remove_pending_members = 21 [(scalapb.field).type = "UserId"];
  InstantMessage set_last_activity_at = 22 [(scalapb.field).type = "Instant"];
  google.protobuf.StringValue set_custom_id = 23;
  InstantMessage set_submission_date = 24 [(scalapb.field).type = "Instant"];
  SubscriptionReviewInfo set_unsigned_subscription_review = 26;
  SubscriptionReviewInfo set_signed_subscription_review = 27;
  string remove_unsigned_subscription_review = 28 [(scalapb.field).type = "Option[ReviewFlowId]"];
  string remove_signed_subscription_review = 29 [(scalapb.field).type = "Option[ReviewFlowId]"];
  repeated string remove_aml_kyc_reviews = 30;
  repeated string add_clients = 31 [(scalapb.field).type = "FundDataInvestorId"];
  repeated string remove_clients = 32 [(scalapb.field).type = "FundDataInvestorId"];
  google.protobuf.BoolValue investor_signed_on_paper = 33;
  repeated ClientFormCompareData add_client_compare_data = 34;
  repeated string remove_client_compare_data = 35 [(scalapb.field).type = "FundDataInvestorId"];
  repeated string remove_metadata_fields = 36;
  map<string, string> add_metadata_fields = 37;
  string set_advisor_group = 38 [(scalapb.field).type = "Option[FundSubRiaGroupId]"];
  string remove_advisor_group = 39 [(scalapb.field).type = "Option[FundSubRiaGroupId]"];
  optional SideLetterStatus set_side_letter_status = 40;
  string set_investor_group = 41 [(scalapb.field).type = "Option[FundSubInvestorGroupId]"];
  string remove_investor_group = 42 [(scalapb.field).type = "Option[FundSubInvestorGroupId]"];
}

message ClientFormCompareData {
  string client_id = 1 [(scalapb.field).type = "FundDataInvestorId"];
  int32 num_of_differences = 2;
  InstantMessage compared_at = 3 [(scalapb.field).type = "Instant"];
}

message RequiredDocInfo {
  string id = 5;
  string name = 1;
  bool marked_as_na = 2;
  bool submitted = 3;
  repeated LpDocument submitted_docs = 4;
}

message UpdateRequiredDocsParams {
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  repeated RequiredDocInfo docs = 2;
}

enum SignatureRequestType {
  SupportingDocSignatureRequest = 0;
  CountersignatureRequest = 1;
}

message UpdateOrAddOrderSignatureRequestsParams {
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  repeated SignatureRequestBasicInfo requests = 2;
  SignatureRequestType request_type = 3;
}

message FormFieldValue {
  string namespace = 1;
  string alias = 2;
  google.protobuf.Value json = 3 [(scalapb.field).type = "Json"];
}

message UpdateFormDataParams {
  string lpIdOpt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  float form_filling_progress = 2;
  int32 missing_required_fields = 3;
  int32 missing_recommended_fields = 4;
  repeated FormFieldValue form_fields = 5;
}

message UpdateCollaboratorsParams {
  string lpIdOpt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  repeated UserBasicInfo add = 2;
  repeated string remove = 3 [(scalapb.field).type = "UserId"];
}

enum TemplateType {
  TEMPLATE_TYPE_IMPORT = 0;
  TEMPLATE_TYPE_EXPORT = 1;
}

message TemplateColumn {
  int32 index = 1;
  string title = 2;
}

message Template {
  string id = 1 [(scalapb.field).type = "DataTemplateVersionId"];
  string name = 2;
  TemplateType template_type = 3;
  string last_change_description = 4;
  FileInfo spreadsheet_file = 5;
  repeated TemplateColumn columns = 6;
  UserBasicInfo created_by = 7;
  InstantMessage created_at = 8 [(scalapb.field).type = "Instant"];
  InstantMessage last_updated_at = 9 [(scalapb.field).type = "Instant"];
}

message UpdateOrAddTemplateParams {
  Template template_info = 1;
}

message PromoteCollaboratorParams {
  option (scalapb.message).no_default_values_in_constructor = false;

  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  string demoted_investor = 2 [(scalapb.field).type = "UserId"];
  string promoted_collaborator = 3 [(scalapb.field).type = "UserId"];
}

// TODO @tuananhtd remove AmlKycReviewStatus and use this
enum ReviewStatus {
  REVIEW_STATUS_NOT_STARTED = 0;
  REVIEW_STATUS_PENDING = 1;
  REVIEW_STATUS_REQUESTED_CHANGE = 2;
  REVIEW_STATUS_STATUS_APPROVED = 3;
}

enum AmlKycReviewStatus {
  AML_KYC_REVIEW_STATUS_NOT_STARTED = 0;
  AML_KYC_REVIEW_STATUS_PENDING = 1;
  AML_KYC_REVIEW_STATUS_REQUESTED_CHANGE = 2;
  AML_KYC_REVIEW_STATUS_APPROVED = 3;
}

message AmlKycReviewInfo {
  reserved 1, 4, 5;
  string doc_type = 2;
  AmlKycReviewStatus status = 3;
  UserBasicInfo updated_by = 6;
  InstantMessage updated_at = 7 [(scalapb.field).type = "Instant"];
  string id = 8;
}

message UpdateOrAddAmlKycReviewParams {
  option (scalapb.message).no_default_values_in_constructor = false;
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  repeated AmlKycReviewInfo reviews = 2;
}

message SubscriptionReviewInfo {
  string id = 1 [(scalapb.field).type = "ReviewFlowId"];
  int32 version_index = 2;
  ReviewStatus status = 3;
  string current_pending_step_id = 4 [(scalapb.field).type = "Option[ReviewStepConfigId]"];
}

message DataExtractRequestInfoForDataLakeIngestion {
  reserved 1, 5;
  optional anduin.protobuf.fundsub.LpDataExtractRequestStatusModel status = 2;
  optional int32 missing_required_fields = 3;
  optional int32 missing_recommended_fields = 4;
  string id = 6 [(scalapb.field).type = "FundSubDataExtractRequestId"];
}

message UpdateOrAddSubdocDataExtractRequestParams {
  reserved 3, 4;
  option (scalapb.message).no_default_values_in_constructor = false;
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  DataExtractRequestInfoForDataLakeIngestion new_data_extract_request_info = 2;
  optional string request_to_remove_opt = 5 [(scalapb.field).type = "FundSubDataExtractRequestId"];
}

message AmlCheckInfoForDataLakeIngestion {
  string id = 1 [(scalapb.field).type = "AmlCheckId"];
  AmlCheckStatus status = 2;
}

message UpdateOrAddAmlCheckParams {
  option (scalapb.message).no_default_values_in_constructor = false;
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  repeated AmlCheckInfoForDataLakeIngestion checks = 2;
}

message RemoveAmlCheckParams {
  option (scalapb.message).no_default_values_in_constructor = false;
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  repeated string check_ids = 2 [(scalapb.field).type = "AmlCheckId"];
}

message AdvisorGroupInfo {
  string id = 1 [(scalapb.field).type = "FundSubRiaGroupId"];
  string name = 2;
}

message UpdateOrAddAdvisorGroupParams {
  string id = 1 [(scalapb.field).type = "FundSubRiaGroupId"];
  string name = 2;
}

message RemoveAdvisorGroupParams {
  string id = 1 [(scalapb.field).type = "FundSubRiaGroupId"];
}

message InvestorGroupInfo {
  string id = 1 [(scalapb.field).type = "FundSubInvestorGroupId"];
  string name = 2;
}

message UpdateOrAddInvestorGroupParams {
  string id = 1 [(scalapb.field).type = "FundSubInvestorGroupId"];
  string name = 2;
}

message RemoveInvestorGroupParams {
  string id = 1 [(scalapb.field).type = "FundSubInvestorGroupId"];
}
