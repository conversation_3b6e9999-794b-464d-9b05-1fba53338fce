syntax = "proto3";

package anduin.workflow.fundsub.invitation;

import "anduin/workflow/fundsub/invitation/invitation_status.proto";
import "date_time.proto";
import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "fundsub/models.proto";
import "anduin/workflow/fundsub/common.proto";

option (scalapb.options) = {
  package_name: "anduin.workflow.fundsub.invitation"
  single_file: true
  import: "java.time.Instant"
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.FileId"
  import: "anduin.id.batchaction.BatchActionId"
  import: "anduin.id.batchaction.BatchActionItemId"
  import: "anduin.id.fundsub.FundSubDataImportItemId"
  import: "anduin.id.fundsub.FundSubBatchInvitationId"
  import: "anduin.id.fundsub.FundSubBatchInvitationItemId"
  import: "anduin.id.fundsub.FundSubCloseId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.fundsub.group.FundSubInvestorGroupId"
  import: "anduin.id.fundsub.ria.FundSubRiaGroupId"
  import: "anduin.id.funddata.FundDataFirmId"
  import: "anduin.id.funddata.FundDataInvestmentEntityId"
};

message FundSubBatchInvitationModel {
  reserved 2;
  string fund_sub_batch_invitation_id = 1 [(scalapb.field).type = "FundSubBatchInvitationId"];
  string batch_action_id = 13 [(scalapb.field).type = "BatchActionId"];
  string fund_sub_id = 3 [(scalapb.field).type = "FundSubId"];
  string close_id = 4 [(scalapb.field).type = "Option[FundSubCloseId]"];
  repeated string attached_docs = 5 [(scalapb.field).type = "FileId"];
  LpOrderType order_type = 6;
  string investor_group_id = 11 [(scalapb.field).type = "Option[FundSubInvestorGroupId]"];
  string actor = 7 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 8 [(scalapb.field).type = "Instant"];
  repeated string item_ids = 9 [(scalapb.field).type = "FundSubBatchInvitationItemId"];
  google.protobuf.StringValue workflowId = 10;
  ImportFromFundDataFirm import_from_fund_data_firm = 12;
}

message FundSubBatchInvitationItem {
  reserved 13, 8;
  string batch_invitation_item_id = 1 [(scalapb.field).type = "FundSubBatchInvitationItemId"];
  string batch_action_item_id = 23 [(scalapb.field).type = "BatchActionItemId"];
  UserContact lp_contact = 2;
  repeated UserContact collaborator_contacts = 3;
  string firm_name = 4;
  string custom_id = 5;
  string expected_commitment = 6;
  EmailTemplateMessage lp_email_template = 7;
  FundSubBatchInvitationStatus status = 9;
  google.protobuf.StringValue workflowId = 10;
  repeated string tag_names = 11;
  string prefill_from_lp = 12 [(scalapb.field).type = "Option[FundSubLpId]"];
  string import_item_id = 14 [(scalapb.field).type = "Option[FundSubDataImportItemId]"];
  // closeId of the single invitation. If None, use closeId of the batch invitation instead
  string close_id = 15 [(scalapb.field).type = "Option[FundSubCloseId]"];
  // investorGroupId of the single invitation. If None, use investorGroupId of the batch invitation instead
  string investor_group_id = 16 [(scalapb.field).type = "Option[FundSubInvestorGroupId]"];
  // documents shared with this lp only
  repeated string lp_attached_docs = 17 [(scalapb.field).type = "FileId"];
  // document types to mark as provided
  repeated string doc_types_to_mark_as_provided = 18;
  // supporting document types to share to lp
  map<string, FileList> shared_documents_per_doc_type = 21;
  // fund data investment entity to prefill
  ImportFromFundData import_from_fund_data = 19;
  map<string, string> metadata = 20;
  string advisor_group_id_opt = 22 [(scalapb.field).type = "Option[FundSubRiaGroupId]"];
}

message UserBatchInvitation {
  string userId = 1 [(scalapb.field).type = "UserId"];
  repeated string invitationIds = 2 [(scalapb.field).type = "FundSubBatchInvitationId"];
}

message RecordTypeUnion {
  FundSubBatchInvitationModel _FundSubBatchInvitationModel = 1;
  FundSubBatchInvitationItem _FundSubBatchInvitationItem = 2;
  UserBatchInvitation _UserBatchInvitation = 3;
}

message ImportFromFundData {
  reserved 3;
  string investment_entity_id = 1 [(scalapb.field).type = "FundDataInvestmentEntityId"];
  bool prefill_form_data = 2;
  AutoPrefillDocuments auto_prefill_documents = 4;
}

enum AutoPrefillDocuments {
  DoNotAutoPrefill = 0;
  AutoMarkDocsAsProvided = 1;
  AutoShareDocsWithLp = 2;
}

message ImportFromFundDataFirm {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  EmailTemplateMessage lp_email_template = 2;
  EmailTemplateMessage collaborator_email_template = 3;
}

message FileList {
  repeated string fileIds = 1 [(scalapb.field).type = "FileId"];
}