syntax = "proto3";

package anduin.workflow.fundsub.invitation;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "external/squants.proto";
import "anduin/workflow/fundsub/common.proto";
import "dynamicform/form_data.proto";
import "anduin/form/form_version_id.proto";
import "anduin/forms/model/form_data_source.proto";
import "fundsub/models.proto";
import "fundsub/models/fund_sub_models.proto";
import "fundsub/lp_info.proto";
import "anduin/workflow/fundsub/invitation/invitation_status.proto";
import "anduin/workflow/fundsub/invitation/fund_sub_invitation.proto";
import "anduin/workflow/fundsub/invitation/fundsubWorkflowInvitationModel.proto";
import "batch_action_item_status.proto";
import "fundsub/commitment.proto";

option (scalapb.options) = {
  package_name: "anduin.workflow.fundsub.invitation"
  single_file: true
  no_default_values_in_constructor: true
  import: "anduin.form.id.FormVersionIdTypeMapper.given"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.id.fundsub.FundSubDataImportItemId"
  import: "anduin.id.fundsub.FundSubBatchInvitationId"
  import: "anduin.id.fundsub.FundSubBatchInvitationItemId"
  import: "anduin.id.fundsub.FundSubCloseId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.InvestmentFundId"
  import: "anduin.id.fundsub.FundSubLpFormIdTrait"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.fundsub.ria.FundSubRiaGroupId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.FileId"
  import: "anduin.model.id.FolderId"
  import: "anduin.model.id.TeamId"
  import: "anduin.model.id.email.InternalEmailId"
  import: "anduin.id.fundsub.group.FundSubInvestorGroupId"
  import: "anduin.id.funddata.FundDataInvestmentEntityId"
};

message ResponseUnit {};

message StartSingleInvitationParam {
  string batch_invitation_item_id = 1 [(scalapb.field).type = "FundSubBatchInvitationItemId"];
  google.protobuf.StringValue workflowId = 2;
  string actor_ip_address = 3;
  bool sync_gateway_update = 4;
}

message StartSingleInvitationResponse {
  reserved 8;
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  UserContact lp_contact = 2;
  repeated UserContact collaborator_contacts = 3;
  string firm_name = 4;
  string custom_id = 5;
  string expected_commitment = 6;
  EmailTemplateMessage lp_email_template = 7;
  string close_id = 9 [(scalapb.field).type = "Option[FundSubCloseId]"];
  repeated string attached_docs = 10 [(scalapb.field).type = "FileId"];
  LpOrderType order_type = 11;
  string investor_group_id_opt = 18 [(scalapb.field).type = "Option[FundSubInvestorGroupId]"];
  string actor = 12 [(scalapb.field).type = "UserId"];
  FundSubBatchInvitationStatus status = 13;
  repeated string tag_names = 14;
  string prefill_from_lp = 15 [(scalapb.field).type = "Option[FundSubLpId]"];
  string import_item_id = 16 [(scalapb.field).type = "Option[FundSubDataImportItemId]"];
  string actor_ip_address = 17;
  repeated string doc_types_to_mark_as_provided = 19;
  map<string, FileList> shared_documents_per_doc_type = 22;
  ImportFromFundData import_from_fund_data = 20;
  map<string, string> metadata = 21;
  string advisor_group_id_opt = 23 [(scalapb.field).type = "Option[FundSubRiaGroupId]"];
}

message SetupUserAndTeamParam {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  UserContact lp_contact = 2;
  repeated UserContact collaborator_contacts = 4;
  string advisor_group_id_opt = 5 [(scalapb.field).type = "Option[FundSubRiaGroupId]"];
  string actor = 3 [(scalapb.field).type = "UserId"];
};

message SetupUserAndTeamResponse {
  string lp_user_id = 1 [(scalapb.field).type = "UserId"];
  repeated string collaborator_user_ids = 3 [(scalapb.field).type = "UserId"];
  string lp_team_id = 2 [(scalapb.field).type = "TeamId"];
  string advisor_admin_team_id_opt = 4 [(scalapb.field).type = "Option[TeamId]"];
};

message SetupUserAndTeamCompensateParam {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  repeated string team_ids_from_lp_side = 2 [(scalapb.field).type = "TeamId", (scalapb.field).collection_type = "Set"];
};

message SetUpFolderAndDocumentsParam {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  repeated string team_ids_from_lp_side = 2 [(scalapb.field).type = "TeamId", (scalapb.field).collection_type = "Set"];
  string actor = 3 [(scalapb.field).type = "UserId"];
  repeated string attached_docs = 4 [(scalapb.field).type = "FileId"];
  string investor_group_id_opt = 5 [(scalapb.field).type = "Option[FundSubInvestorGroupId]"];
};

message SetUpFolderAndDocumentsResponse {
  reserved 2;
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated string shared_docs = 3 [(scalapb.field).type = "FileId"];
  repeated string lp_folder_ids = 4 [(scalapb.field).type = "FolderId"];
};

message SetUpFolderAndDocumentsCompensateParam {
  reserved 2;
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated string shared_docs = 3 [(scalapb.field).type = "FileId"];
  string actor = 4 [(scalapb.field).type = "UserId"];
  repeated string lp_folder_ids = 5 [(scalapb.field).type = "FolderId"];
};

message SetUpSignatureModuleParam {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated string team_ids_from_lp_side = 2 [(scalapb.field).type = "TeamId", (scalapb.field).collection_type = "Set"];
  string lp_user_id = 3 [(scalapb.field).type = "UserId"];
  string investor_group_id_opt = 5 [(scalapb.field).type = "Option[FundSubInvestorGroupId]"];
  string actor = 4 [(scalapb.field).type = "UserId"];
};


message SetUpFormParam {
  reserved 5;
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated string team_ids_from_lp_side = 2 [(scalapb.field).type = "TeamId", (scalapb.field).collection_type = "Set"];
  string prefill_from_lp = 3 [(scalapb.field).type = "Option[FundSubLpId]"];
  string actor_id = 4 [(scalapb.field).type = "UserId"];
  string import_item_id = 6 [(scalapb.field).type = "Option[FundSubDataImportItemId]"];
  ImportFromFundData import_from_fund_data = 7;
};

message SetUpFormResponse {
  S3FormChangeInfo s3_form_info = 1;
  string lp_form_id = 2 [(scalapb.field).type = "FundSubLpFormIdTrait"];
  anduin.form.FormVersionIdMessage form_version_id = 3 [(scalapb.field).type = "FormVersionId"];
  anduin.forms.model.FormDataSource form_data_source = 4;
};

message SetUpFormCompensateParam {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated string team_ids_from_lp_side = 2 [(scalapb.field).type = "TeamId", (scalapb.field).collection_type = "Set"];
  string lp_form_id = 3 [(scalapb.field).type = "FundSubLpFormIdTrait"];
};

message SetUpFormCommentingParam {
  reserved 3;
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated string team_ids_from_lp_side = 2 [(scalapb.field).type = "TeamId", (scalapb.field).collection_type = "Set"];
  string actor = 4 [(scalapb.field).type = "UserId"];
};

message SetUpFormCommentingCompensateParam {
  reserved 3;
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated string team_ids_from_lp_side = 2 [(scalapb.field).type = "TeamId", (scalapb.field).collection_type = "Set"];
  string actor = 4 [(scalapb.field).type = "UserId"];
}

message CreateFundSubModelParam {
  reserved 12, 18, 23;
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string lp_user_id = 2 [(scalapb.field).type = "UserId"];
  repeated string collaborator_user_ids = 16 [(scalapb.field).type = "UserId"];
  string lp_team_id = 3 [(scalapb.field).type = "TeamId"];
  repeated string shared_docs = 4 [(scalapb.field).type = "FileId"];
  LpOrderType order_type = 5;
  string firm_name = 6;
  string custom_id = 7;
  S3FormChangeInfo s3_form_info = 8;
  string expected_commitment = 9;
  string lp_form_id = 10 [(scalapb.field).type = "FundSubLpFormIdTrait"];
  anduin.form.FormVersionIdMessage form_version_id = 11 [(scalapb.field).type = "FormVersionId"];
  string close_id = 13 [(scalapb.field).type = "Option[FundSubCloseId]"];
  string investor_group_id = 20 [(scalapb.field).type = "Option[FundSubInvestorGroupId]"];
  string actor = 14 [(scalapb.field).type = "UserId"];
  anduin.protobuf.flow.fundsub.admin.lpdashboard.LpInvitationType invitation_type = 15;
  string prefill_from_lp = 17 [(scalapb.field).type = "Option[FundSubLpId]"];
  string import_item_id = 19 [(scalapb.field).type = "Option[FundSubDataImportItemId]"];
  ImportFromFundData import_from_fund_data = 21;
  map<string, string> metadata = 22;
  string advisor_group_id_opt = 24 [(scalapb.field).type = "Option[FundSubRiaGroupId]"];
};

message CreateFundSubModelResponse {
  repeated anduin.protobuf.fundsub.models.SupportingDocInfo visible_supporting_docs = 1;
  bool has_initial_form_data = 2;
  anduin.forms.model.FormDataSource initial_form_data_source = 3;
  optional string lp_form_id_opt = 4 [(scalapb.field).type = "FundSubLpFormIdTrait"];
  string firm_name = 5;
  anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus lp_status = 6;
  string close_id = 7 [(scalapb.field).type = "Option[FundSubCloseId]"];
  external.squants.MoneyMessage expected_commitment = 8;
  map<string, anduin.protobuf.fundsub.commitment.LpCommitment> commitments = 9 [(scalapb.field).key_type = "InvestmentFundId"];
};

message CreateFundSubModelCompensateParam {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string lp_user_id = 2 [(scalapb.field).type = "UserId"];
  repeated string collaborator_user_ids = 3 [(scalapb.field).type = "UserId"];
  string investor_group_id = 4 [(scalapb.field).type = "Option[FundSubInvestorGroupId]"];
  string actor = 5 [(scalapb.field).type = "UserId"];
};

message AddOrderDataToDataLakeParam {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated string shared_docs = 2 [(scalapb.field).type = "FileId"];
  map<string, string> metadata = 3;
  optional string lp_form_id_opt = 4 [(scalapb.field).type = "FundSubLpFormIdTrait"];
  string advisor_group_id_opt = 6 [(scalapb.field).type = "Option[FundSubRiaGroupId]"];
  string investor_group_id_opt = 7 [(scalapb.field).type = "Option[FundSubInvestorGroupId]"];
  string actor_id = 5 [(scalapb.field).type = "UserId"];
}

message AddOrderDataToDataPipelineParam {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string firm_name = 2;
  anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus lp_status = 3;
  string lp_user_id = 4 [(scalapb.field).type = "UserId"];
  repeated string collaborator_user_ids = 5 [(scalapb.field).type = "UserId"];
  string close_id = 6 [(scalapb.field).type = "Option[FundSubCloseId]"];
  external.squants.MoneyMessage expected_commitment = 7;
  map<string, anduin.protobuf.fundsub.commitment.LpCommitment> commitments = 8 [(scalapb.field).key_type = "InvestmentFundId"];
  map<string, string> metadata = 9;
}

message SetUpLpTagsParam {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  repeated string tag_names = 3;
}

message SetUpProvidedDocumentTypesParam {
  reserved 3;
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  ProvidedDocumentTypes provided_doc_types = 4;
}

message SetUpProvidedDocumentTypesResponse {
  repeated string provided_doc_types = 1;
}

message ProvidedDocumentTypes {
  oneof sealed_value {
    CustomProvidedDocumentTypes custom_provided_document_types = 1;
    AutoMarkImportFromFundData auto_mark_import_from_fund_data = 2;
  }
}

message CustomProvidedDocumentTypes {
  repeated string doc_types = 1;
}

message AutoMarkImportFromFundData {
  string investment_entity_id = 1 [(scalapb.field).type = "FundDataInvestmentEntityId"];
  repeated anduin.protobuf.fundsub.models.SupportingDocInfo visible_supporting_docs = 2;
}

message SetUpShareDocumentsWithLpParam {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  SharedDocumentTypes shared_doc_types = 3;
}

message SetUpShareDocumentsWithLpResponse {
  map<string, FileList> shared_documents_with_lp = 1;
}

message SharedDocumentTypes {
  oneof sealed_value {
    CustomSharedDocumentTypes custom_shared_document_types = 1;
    AutoShareImportFromFundData auto_share_import_from_fund_data = 2;
  }
}

message CustomSharedDocumentTypes {
  map<string, FileList> shared_documents_with_lp = 1;
}

message AutoShareImportFromFundData {
  string investment_entity_id = 1 [(scalapb.field).type = "FundDataInvestmentEntityId"];
  repeated anduin.protobuf.fundsub.models.SupportingDocInfo visible_supporting_docs = 2;
}

message SetUpContactParam {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  anduin.protobuf.flow.fundsub.admin.lpdashboard.LpInvitationType invitation_type = 3;
};

message SetUpContactCompensateParam {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  anduin.protobuf.flow.fundsub.admin.lpdashboard.LpInvitationType invitation_type = 3;
};

message SetUpImportFromFundDataParam {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string investment_entity_id = 2 [(scalapb.field).type = "FundDataInvestmentEntityId"];
  string actor = 3 [(scalapb.field).type = "UserId"];
};

message SetUpImportFromFundDataCompensateParam {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string investment_entity_id = 2 [(scalapb.field).type = "FundDataInvestmentEntityId"];
  string actor = 3 [(scalapb.field).type = "UserId"];
};

message SendEmailParam {
  reserved 4;
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  EmailTemplateMessage lp_email_template = 3;
  anduin.protobuf.flow.fundsub.admin.lpdashboard.LpInvitationType invitation_type = 5;
  string firm_name = 6;
  repeated string skip_invitation_email_contacts = 7;
  repeated string enable_sso_contacts = 8;
};

message NormalInviteEmailResult {
  string lp_email_id = 1 [(scalapb.field).type = "Option[InternalEmailId]"];
  repeated string collaborator_email_ids = 2 [(scalapb.field).type = "InternalEmailId"];
}

message InvestedInAdditionalFundEmailResult {
  reserved 1;
  repeated string lp_team_email_ids = 2 [(scalapb.field).type = "InternalEmailId"];
}

message AddOfflineOrderEmailResult {}

message JoinViaInvitationLink {}

message SendEmailResponse {
  oneof send_email_result {
    NormalInviteEmailResult normal_invite_email_result = 1;
    InvestedInAdditionalFundEmailResult in_additional_fund_email_result = 2;
    AddOfflineOrderEmailResult add_offline_order_email_result = 3;
    JoinViaInvitationLink join_via_invitation_link = 4;
  }
}

message CreateFundSubActivityLogParam {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string lp_user_id = 2 [(scalapb.field).type = "UserId"];
  repeated string collaborator_user_ids = 4 [(scalapb.field).type = "UserId"];
  string actor = 3 [(scalapb.field).type = "UserId"];
  anduin.protobuf.flow.fundsub.admin.lpdashboard.LpInvitationType invitation_type = 5;
  string investment_entity = 6;
  string actor_ip_address = 7;
  SendEmailResponse send_email_result = 8;
  string advisor_group_id_opt = 9 [(scalapb.field).type = "Option[FundSubRiaGroupId]"];
};

message CreateFundSubActivityLogCompensateParam {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
}

message SendAmplitudeAndZapierParam {
  reserved 2;
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  repeated string collaborator_user_ids = 7 [(scalapb.field).type = "UserId"];
  string actor = 3 [(scalapb.field).type = "UserId"];
  UserContact lp_contact = 4;
  repeated UserContact collaborator_contacts = 5;
  string firm_name = 6;
  anduin.protobuf.flow.fundsub.admin.lpdashboard.LpInvitationType invitation_type = 8;
  string prefilled_from_past_lp = 9 [(scalapb.field).type = "Option[FundSubLpId]"];
  bool from_data_import = 10;
  string form_data_source = 11;
};

message CompleteSingleInvitationParam {
  string batch_invitation_item_id = 1 [(scalapb.field).type = "FundSubBatchInvitationItemId"];
  string fund_sub_lp_id = 2 [(scalapb.field).type = "FundSubLpId"];
  string actor_id = 3 [(scalapb.field).type = "UserId"];
  bool sync_gateway_update = 4;
}

message CompleteSingleInvitationResponse {
  FundSubBatchInvitationStatus status = 1;
}

message MarkSingleInvitationFailedParam {
  reserved 2;
  string batch_invitation_item_id = 1 [(scalapb.field).type = "FundSubBatchInvitationItemId"];
  batchaction.BatchActionItemError error = 4;
  bool sync_gateway_update = 3;
}

message MarkSingleInvitationFailedResponse {
  FundSubBatchInvitationStatus status = 1;
}

message CreateBatchInviteInvestorActivityLogParam {
  string batch_invitation_id = 1 [(scalapb.field).type = "FundSubBatchInvitationId"];
}

message SendBatchEmailAndCreateBatchFundSubAuditLogParam {
  string batch_invitation_id = 1 [(scalapb.field).type = "FundSubBatchInvitationId"];
  repeated SingleInvitationResponse single_invitation_responses = 2;
  optional string actor_ip_address = 3;
}

message UpdateSupportingDocsParams {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string actor_id = 2 [(scalapb.field).type = "UserId"];
}