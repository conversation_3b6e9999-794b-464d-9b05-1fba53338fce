// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.dataextract

import java.time.Instant

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterL, ModalL}
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.tracker.laminar.VisibilityTrackerL
import design.anduin.components.well.Well
import design.anduin.components.well.laminar.WellL
import design.anduin.style.CssVar
import design.anduin.style.tw.*
import org.scalajs.dom
import zio.ZIO

import anduin.file.laminar.FileLoaderL
import anduin.forms.data.DataTools.DataToolConfig
import anduin.forms.engine.GaiaEngine.{EngineConfiguration, EngineContext}
import anduin.forms.engine.{GaiaEngine, GaiaState}
import anduin.forms.renderer.MissingFieldBlock
import anduin.forms.utils.FormValidationUtils.{FieldAlert, FieldIDTrait, NoAlert}
import anduin.forms.utils.{FormDataUtils, FormValidationUtils}
import anduin.forms.{FlexibleFormRenderer, FormData, FormRendererSkeleton}
import anduin.frontend.AirStreamUtils
import anduin.fundsub.dataextract.GetDataExtractRequestFormInfoResp.ViewOnlyModeData
import anduin.fundsub.dataextract.PreviewExtractedFormDataModal.{
  RenderHeaderProps,
  SaveExtractedDataEventBusParams,
  SaveExtractedDataProps
}
import anduin.fundsub.endpoint.subscriptiondoc.SubscriptionVersionBasicInfo
import anduin.id.fundsub.FundSubLpId
import anduin.id.fundsub.dataextract.FundSubDataExtractRequestId
import anduin.model.id.FileId
import anduin.service.GeneralServiceException
import anduin.util.FilenameUtils
import com.anduin.stargazer.client.component.viewer.PreviewMultipleDocuments
import com.anduin.stargazer.fundsub.client.FundSubDataExtractClient
import com.anduin.stargazer.fundsub.module.form.formvaluediff.LpDraftVersionFormValueDiff
import stargazer.component.routing.laminar.WithReactRouterL
import stargazer.model.routing.DynamicAuthPage.PreviewFilePage

final case class PreviewExtractedFormDataModal(
  requestId: FundSubDataExtractRequestId,
  formDataOptSignal: Signal[Option[FormData]],
  initialGaiaStateOptSignal: Signal[Option[GaiaState]],
  onCloseFormModal: Observer[Unit],
  submittedDocsSignal: Signal[Seq[FileId]],
  dataToolConfigOpt: Option[DataToolConfig] = None,
  renderHeader: RenderHeaderProps => HtmlElement,
  viewOnlyModeDataOptSignal: Signal[Option[ViewOnlyModeData]] = Val(None),
  initialFormDataLastUpdateAtSignal: Signal[Option[Instant]]
) {

  private val saveExtractedDataToBackendEventBus = new EventBus[SaveExtractedDataEventBusParams]

  private val jumpToFieldEventBus = new EventBus[FieldIDTrait]

  private val fieldAlertVar: Var[FieldAlert] = Var(NoAlert)

  private val fieldAlertSignal: Signal[FieldAlert] = fieldAlertVar.signal

  private val jumpToWarningFieldWellVisibleVar: Var[Boolean] = Var(true)

  private val shouldRenderJumpToWarningFieldButtonSignal = viewOnlyModeDataOptSignal.map(_.isEmpty)

  private val formFillingPercentageVar = Var(0.0f)

  private val mainFormGaiaStateEventBus = new EventBus[GaiaState]

  private val compareModalGaiaStateEventBus = new EventBus[GaiaState]

  private val gaiaStateEvents =
    mainFormGaiaStateEventBus.events.mergeWith(compareModalGaiaStateEventBus.events)

  private val gaiaStateOptSignal = gaiaStateEvents.toWeakSignal

  private val forceSaveFormDataEventBus = new EventBus[SaveExtractedDataProps]

  private val extractedDataLastUpdatedAtVar: Var[Option[Instant]] = Var(None)

  private val isClosingAndSavingFormDataVar: Var[Boolean] = Var(false)
  private val showSessionTimeoutModalVar: Var[Boolean] = Var(false)

  private val RenewLockIntervalMs = 60000 // renew every minute

  private val autoRenewLockEventStream =
    EventStream.periodic(intervalMs = RenewLockIntervalMs).drop(1, resetOnStop = true)

  private val extractedDataLastUpdatedAtSignal =
    extractedDataLastUpdatedAtVar.signal.combineWith(initialFormDataLastUpdateAtSignal).map {
      case (extractedDataLastUpdatedAtOpt, initialextractedDataLastUpdatedAtOpt) =>
        extractedDataLastUpdatedAtOpt.orElse(initialextractedDataLastUpdatedAtOpt)
    }

  private val openFormCompareModalEventBus = new EventBus[(FundSubLpId, Seq[SubscriptionVersionBasicInfo])]
  private val isDisabledOpenFormCompareModalVar = Var(true)
  private val isDisabledOpenFormCompareModalSignal = isDisabledOpenFormCompareModalVar.signal.distinct

  def apply(): HtmlElement = {
    div(
      tw.wPc100.hPc100.flex.flexCol.overflowHidden,
      renderHeader(
        RenderHeaderProps(
          formDataLastUpdateAtSignal = extractedDataLastUpdatedAtSignal,
          onCloseModal = Observer { _ =>
            forceSaveFormDataEventBus.emit(
              SaveExtractedDataProps(
                onSuccess = onCloseFormModal,
                isClosingAfterSaving = true
              )
            )
          },
          isClosingAndSaving = isClosingAndSavingFormDataVar.signal,
          onSaveData = forceSaveFormDataEventBus.writer,
          openFormCompareModalObserver = openFormCompareModalEventBus.writer,
          isDisabledOpenFormCompareModalSignal = isDisabledOpenFormCompareModalSignal
        )
      ),
      child.maybe <-- renderJumpToWarningFieldHeaderOpt,
      div(
        tw.flexFill.overflowAuto.bgGray1.py16,
        div(
          tw.mxAuto.wCustom1,
          styleProp(CssVar.Width.CustomWidth1) := "960px",
          child.maybe <-- renderJumpToWarningFieldWellOpt,
          child <-- renderForm
        )
      ),
      child.maybe <-- showSessionTimeoutModalVar.signal.map {
        Option.when(_) {
          ModalL(
            renderTitle = _ => "Session expired",
            renderContent = _ =>
              div(
                ModalBodyL("Your review session has expired. Please refresh the page to continue."),
                ModalFooterL(
                  div(
                    tw.flex.justifyEnd,
                    ButtonL(
                      style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
                      onClick = Observer(_ => dom.window.location.reload())
                    )("Refresh")
                  )
                )
              ),
            isOpened = Some(Val(true)),
            isClosable = None
          )()
        }
      },
      autoRenewLockEventStream.sample(viewOnlyModeDataOptSignal).filter(_.isEmpty).flatMapSwitch { _ =>
        onRenewLock
      } --> Observer.empty
    )
      .amend(tw.hPc100.wPc100)
  }

  private def renderViewSubmittedDocsModal(
    fileToViewOpt: Option[FileId],
    submittedDocs: Seq[FileId],
    renderTarget: Observer[Unit] => HtmlElement
  ) = {
    ModalL(
      renderTarget = renderTarget,
      renderContent = close =>
        PreviewMultipleDocuments(
          submittedDocs,
          close,
          title = "Submitted documents",
          firstFileToViewOpt = fileToViewOpt
        )(),
      size = ModalL.Size(width = ModalL.Width.Full, height = ModalL.Height.Full)
    )()
  }

  private def renderTableOfContent(tableOfContents: HtmlElement) = {
    div(
      tw.pr32.borderRight.borderGray3,
      width.px := 300,
      tableOfContents,
      child.maybe <-- submittedDocsSignal.map { submittedDocs =>
        Option.when(submittedDocs.nonEmpty) {
          div(
            tw.mt16.pt16.borderTop.borderGray3,
            div(
              tw.fontSemiBold.text17.leading28,
              "Submitted documents"
            ),
            div(
              tw.mt16,
              renderViewSubmittedDocsModal(
                fileToViewOpt = None,
                submittedDocs = submittedDocs,
                renderTarget = open =>
                  ButtonL(
                    style = ButtonL.Style.Ghost(
                      color = ButtonL.Color.Gray9,
                      height = ButtonL.Height.Fix24,
                      icon = Some(Icon.Glyph.Eye)
                    ),
                    onClick = open.contramap(_ => ())
                  )("View all documents")
              )
            ),
            div(
              tw.mt16,
              FileLoaderL(submittedDocs) { renderFileLoaderData =>
                div(
                  tw.spaceY8,
                  children <-- renderFileLoaderData.respOptSignal.map { fileInfoListOpt =>
                    fileInfoListOpt
                      .flatMap(_.fileInfo)
                      .getOrElse(Seq.empty)
                      .map { fileInfo =>
                        TooltipL(
                          renderTarget = WithReactRouterL { router =>
                            a(
                              tw.wPc100.flex.itemsCenter.spaceX4,
                              IconL(
                                name = Val(Icon.File.ByExtension(FilenameUtils.getExtension(fileInfo.name).getOrElse("")))
                              )(),
                              div(
                                tw.truncate.textGray7.cursorPointer.flexFill.text11.hover(tw.underline),
                                fileInfo.name
                              ),
                              href := router
                                .urlFor(PreviewFilePage(fileInfo.itemId))
                                .value,
                              target := "_blank"
                            )
                          },
                          renderContent = _.amend("Open in new tab")
                        )()
                      }
                  }
                )
              }
            )
          )
        }
      }
    )
  }

  private def draftFormCompareModal(
    lpId: FundSubLpId,
    lpVersions: Seq[SubscriptionVersionBasicInfo],
    engine: GaiaEngine,
    draftFormData: FormData,
    draftInitialGaiaState: GaiaState
  ) = {
    ModalL(
      renderContent = close =>
        LpDraftVersionFormValueDiff(
          lpId = lpId,
          lpVersions = lpVersions.reverse,
          engine = engine,
          draftFormData = draftFormData,
          draftInitialGaiaState = draftInitialGaiaState,
          draftLastUpdatedAtSignal = extractedDataLastUpdatedAtSignal,
          backObserver = close,
          draftGaiaStateObserver = compareModalGaiaStateEventBus.writer,
          viewOnlyModeDataOptSignal = viewOnlyModeDataOptSignal
        )(),
      defaultIsOpened = true,
      isClosable = None,
      size = ModalL.Size(ModalL.Width.Full, ModalL.Height.Full)
    )()
  }

  private def renderForm = {
    formDataOptSignal
      .combineWith(initialGaiaStateOptSignal, viewOnlyModeDataOptSignal)
      .distinct
      .map { case (formDataOpt, initialGaiaStateOpt, viewOnlyModeDataOpt) =>
        val isViewOnly = viewOnlyModeDataOpt.nonEmpty
        formDataOpt
          .zip(initialGaiaStateOpt)
          .fold[Node](
            FormRendererSkeleton(
              layout = Option(FormRendererSkeleton.layoutSidebarOnLeft)
            )()
          ) { case (formData, initialGaiaState) =>
            val engine = GaiaEngine.make(
              formData.form,
              EngineConfiguration.default,
              EngineContext.default
            )
            val gaiaStateSignal = gaiaStateOptSignal.map(_.getOrElse(initialGaiaState))
            formFillingPercentageVar.set(FormDataUtils.calculateProgress(formData.form, initialGaiaState.defaultStateMap))
            div(
              saveExtractedDataToBackendEventBus.events.flatMapSwitch { saveParams =>
                onSaveDraftFormData(saveParams, isViewOnly)
              } --> Observer.empty,
              forceSaveFormDataEventBus.events
                .withCurrentValueOf(gaiaStateSignal)
                .map { case (forceSaveFormDataProps, gaiaState) =>
                  SaveExtractedDataEventBusParams(
                    onSuccess = forceSaveFormDataProps.onSuccess,
                    isClosingAfterSaving = forceSaveFormDataProps.isClosingAfterSaving,
                    gaiaState = gaiaState
                  )
                } --> saveExtractedDataToBackendEventBus.writer,
              gaiaStateEvents.map { gaiaState =>
                SaveExtractedDataEventBusParams(
                  onSuccess = Observer.empty,
                  isClosingAfterSaving = false,
                  gaiaState = gaiaState
                )
              } --> saveExtractedDataToBackendEventBus.writer,
              engine.toOption.fold[Node](emptyNode) { engine =>
                // Form body
                div(
                  child <-- openFormCompareModalEventBus.events.withCurrentValueOf(gaiaStateSignal).map {
                    case (lpId, lpVersions, gaiaState) =>
                      draftFormCompareModal(
                        lpId = lpId,
                        lpVersions = lpVersions,
                        engine = engine,
                        draftFormData = formData,
                        draftInitialGaiaState = gaiaState
                      )
                  },
                  FlexibleFormRenderer(
                    formData = formData,
                    dataObserver = mainFormGaiaStateEventBus.writer,
                    engine = engine,
                    initialData = initialGaiaState,
                    stateEventStream = compareModalGaiaStateEventBus.events,
                    initialKeyOpt = None,
                    submitButtonOpt = None,
                    isReadOnly = isViewOnly,
                    showErrorSignal = Val(!isViewOnly),
                    showWarningSignal = Val(!isViewOnly),
                    onFieldAlert = fieldAlertVar.writer,
                    renderTableOfContent = renderTableOfContent,
                    onCloseTableOfContents = Observer.empty,
                    formFillingPercentageSignal = formFillingPercentageVar.signal,
                    dataToolConfigOpt = dataToolConfigOpt,
                    goToFieldEventStream = jumpToFieldEventBus.events
                  )(),
                  onMountUnmountCallback(
                    mount = _ => isDisabledOpenFormCompareModalVar.set(false),
                    unmount = _ => isDisabledOpenFormCompareModalVar.set(true)
                  )
                )
              }
            )
          }
      }
  }

  private def renderFieldAlertWell(
    wellStyle: Well.Style,
    fieldAlert: FieldAlert
  ): HtmlElement = {
    VisibilityTrackerL(
      node = WellL(style = wellStyle)(
        MissingFieldBlock(
          fieldAlert = fieldAlert,
          onJumpToField = jumpToFieldEventBus.writer,
          hasIcon = false
        )()
      ),
      onVisibilityChanged = jumpToWarningFieldWellVisibleVar.writer
    )()
  }

  private def renderJumpToWarningFieldWellOpt = {
    fieldAlertSignal
      .combineWith(shouldRenderJumpToWarningFieldButtonSignal)
      .distinct
      .map { case (fieldAlert, shouldRenderJumpToWarningFieldButton) =>
        Option.when(shouldRenderJumpToWarningFieldButton) {
          div(
            tw.mb16,
            fieldAlert match {
              case FormValidationUtils.FieldError(_) =>
                renderFieldAlertWell(
                  wellStyle = Well.Style.Danger(),
                  fieldAlert
                )
              case FormValidationUtils.FieldWarning(_) =>
                renderFieldAlertWell(
                  wellStyle = Well.Style.Warning(),
                  fieldAlert
                )
              case FormValidationUtils.NoAlert => emptyNode
            }
          )
        }
      }
  }

  private def renderJumpToWarningFieldHeaderOpt = {
    jumpToWarningFieldWellVisibleVar.signal
      .combineWith(fieldAlertSignal, shouldRenderJumpToWarningFieldButtonSignal)
      .distinct
      .map { case (jumpToWarningFieldWellVisible, fieldAlert, shouldRenderJumpToWarningFieldButton) =>
        Option.when(!jumpToWarningFieldWellVisible && shouldRenderJumpToWarningFieldButton) {
          div(
            tw.flex.justifyCenter.py8.px12,
            tw.borderBottom.borderGray3,
            MissingFieldBlock(
              fieldAlert = fieldAlert,
              onJumpToField = jumpToFieldEventBus.writer,
              hasIcon = true
            )()
          )
        }
      }
  }

  private def onSaveDraftFormData(
    eventBusParams: SaveExtractedDataEventBusParams,
    isViewOnly: Boolean
  ): EventStream[Unit] = {
    val task = for {
      _ <- ZIO.succeed(isClosingAndSavingFormDataVar.set(eventBusParams.isClosingAfterSaving))
      _ <- FundSubDataExtractClient
        .saveFormDataOfDataExtract(SaveFormDataOfDataExtractParams(eventBusParams.gaiaState, requestId))
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold(
            _ => {
              Toast.error("Failed to save form data, please try again")
              isClosingAndSavingFormDataVar.set(false)
            },
            resp => {
              isClosingAndSavingFormDataVar.set(false)
              if (resp.isLocked) {
                showSessionTimeoutModalVar.set(true)
              } else {
                formFillingPercentageVar.set(resp.formFillingPercentage)
                extractedDataLastUpdatedAtVar.set(resp.formDataLastEditedAtOpt)
                eventBusParams.onSuccess.onNext(())
              }
            }
          )
        )
    } yield ()

    if (!isViewOnly) {
      AirStreamUtils.taskToStream(task)
    } else {
      EventStream.fromValue(eventBusParams.onSuccess.onNext(()))
    }
  }

  private def onRenewLock: EventStream[Unit] = {
    AirStreamUtils.taskToStream {
      FundSubDataExtractClient.renewDataExtractFormLock(RenewDataExtractFormLockParams(requestId)).map(_ => ())
    }
  }

}

object PreviewExtractedFormDataModal {

  private case class SaveExtractedDataEventBusParams(
    onSuccess: Observer[Unit],
    isClosingAfterSaving: Boolean,
    gaiaState: GaiaState
  )

  case class SaveExtractedDataProps(
    onSuccess: Observer[Unit],
    isClosingAfterSaving: Boolean
  )

  case class RenderHeaderProps(
    formDataLastUpdateAtSignal: Signal[Option[Instant]],
    onCloseModal: Observer[Unit],
    onSaveData: Observer[SaveExtractedDataProps],
    isClosingAndSaving: Signal[Boolean],
    openFormCompareModalObserver: Observer[(FundSubLpId, Seq[SubscriptionVersionBasicInfo])],
    isDisabledOpenFormCompareModalSignal: Signal[Boolean]
  )

}
