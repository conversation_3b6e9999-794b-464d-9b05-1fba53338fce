// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.client

import zio.Task

import anduin.contact.endpoint.*
import anduin.fundsub.endpoint.FundSubEndpoints
import anduin.fundsub.endpoint.admin.*
import anduin.fundsub.endpoint.amlkyc.amlcheck.GetFundSubAmlCheckResponse
import anduin.fundsub.endpoint.auditlog.*
import anduin.fundsub.endpoint.common.{GetUserTrackingResponse, UpdateUserTrackingParams}
import anduin.fundsub.endpoint.contact.{
  GetContactsWithSameEmailForGroupParams,
  ListContactsByEmailOrderParams,
  QueryContactsByEmailParams,
  QueryContactsByNameParams,
  *
}
import anduin.fundsub.endpoint.copy.{GetCopyConfigForClientParams, GetCopyConfigForClientResponse}
import anduin.fundsub.endpoint.dashboard.*
import anduin.fundsub.endpoint.dataexport.*
import anduin.fundsub.endpoint.datagenerator.GenerateInvestorParams
import anduin.fundsub.endpoint.dataimport.*
import anduin.fundsub.endpoint.emaillog.{GetEmailLogDataParams, GetEmailLogDataResponse}
import anduin.fundsub.endpoint.formcomment.*
import anduin.fundsub.endpoint.fundclose.*
import anduin.fundsub.endpoint.lp.{GaiaFormModel as GaiaForm, *}
import anduin.fundsub.endpoint.multiregion.GetFundSubRegionInfosResponse
import anduin.fundsub.endpoint.operation.{ExportFundCommentParams, SendDemoFundActivityEmailParams}
import anduin.fundsub.endpoint.participant.{FundSubBatchInvitationInfo, *}
import anduin.fundsub.endpoint.reusesignature.UpdateAllowFormEditPostSigningParams
import anduin.fundsub.endpoint.reviewpackage.*
import anduin.fundsub.endpoint.signature.batch.*
import anduin.fundsub.endpoint.status.{GetLpStatusActivityHistoryParams, LpStatusHistory}
import anduin.fundsub.endpoint.subscriptiondoc.{GetSubscriptionDocRequestChangesInfoResponse, *}
import anduin.fundsub.endpoint.supportingdoc.*
import anduin.fundsub.endpoint.whitelabel.*
import anduin.fundsub.featureswitch.*
import anduin.id.fundsub.*
import anduin.id.issuetracker.IssueId
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.email.InternalEmailId
import anduin.model.id.{FileId, TemporalWorkflowId}
import anduin.protobuf.fundsub.onboarding.ModuleOnboardingMessage
import anduin.protobuf.fundsub.usertracking.FundSubUserTrackingModel
import anduin.service.{CommonResponse, GeneralServiceException, GeneralServiceResponse}
import anduin.stargazer.service.formcomment.FormCommentCommons.*
import anduin.tapir.client.AuthenticatedEndpointClient
import anduin.tapir.endpoint.{CommonParams, EmptyResponse}

import anduin.email.CustomSmtpServerConfigParams

object FundSubEndpointClient extends AuthenticatedEndpointClient {

  val updateFundName: UpdateFundNameParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateFundName)

  val updateMarkAsNotApplicableSetting: UpdateMarkAsNotApplicableParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateMarkAsNotApplicableSetting)

  val updateDisableFundContactInInvestorWorkspaceSetting
    : UpdateDisableFundContactInInvestorWorkspaceSettingParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateDisableFundContactInInvestorWorkspaceSetting)

  val updateInvestFromAdditionalEntitySetting
    : UpdateInvestingFromAdditionalEntitySettingParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateInvestFromAdditionalEntitySetting)

  val updateCustomLpIdSetting: UpdateCustomLpIdSettingParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateCustomLpIdSetting)

  val updateDownloadSubscriptionDocumentSetting
    : UpdateDownloadSubscriptionDocumentSettingParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateDownloadSubscriptionDocumentSetting)

  val updateRiaBannerVisibility: UpdateRiaBannerVisibilityParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateRiaBannerVisibility)

  val updateInactiveLpSetting: UpdateInactiveLpSettingParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateInactiveLpSetting)

  val updateFormCommentFundSettingSwitch
    : UpdateFormCommentFundSettingSwitchParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateFormCommentFundSettingSwitch)

  val updateFormCommentInvestorDigestEmailSwitch
    : UpdateFormCommentInvestorDigestEmailSwitchParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateFormCommentInvestorDigestEmailSwitch)

  val updateFormCommentInvestorCanResolveSwitch
    : UpdateFormCommentInvestorCanResolveSwitchParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateFormCommentInvestorCanResolveSwitch)

  val updateInactiveCommentSetting: UpdateInactiveCommentSettingParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateInactiveCommentSetting)

  val updateFormCommentDigestEmailExceptionLps
    : UpdateFormCommentDigestEmailExceptionLpsParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateFormCommentDigestEmailExceptionLps)

  val getFormCommentDigestEmailExceptionInfo: GetFormCommentDigestEmailExceptionInfoParams => Task[
    Either[GeneralServiceException, GetFormCommentDigestEmailExceptionInfoResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFormCommentDigestEmailExceptionInfo)

  val updateCustomFundIdSetting: UpdateCustomFundIdSettingParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateCustomFundIdSetting)

  val sendNewInvestorReportEmail: SendNewInvestorsReportDemoEmailParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.sendNewInvestorReportEmail)

  val lpSelfRemindToSignAgain: FundSubLpId => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.lpSelfRemindToSignAgain)

  val sendDemoFormCommentsDigestEmail: FundSubId => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.sendDemoFormCommentsDigestEmail)

  val sendDemoFundActivitiesDigestEmail
    : SendDemoFundActivityEmailParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.sendDemoFundActivitiesDigestEmail)

  val uploadRefDoc: UploadDocumentsParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.uploadRefDoc)

  val renameRefDoc: RenameDocumentsParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.renameRefDoc)

  val removeRefDoc: RemoveDocumentsParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.removeRefDoc)

  val getFundSharedRefDocs
    : GetFundSharedReferenceDocsParams => Task[Either[GeneralServiceException, GetFundSharedReferenceDocsResp]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundSharedRefDocs)

  val saveSubscriptionDocsOrder: SaveSubscriptionDocsOrderParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.saveSubscriptionDocsOrder)

  val requestCountersignSignature
    : RequestCountersignSignatureParams => Task[Either[GeneralServiceException, RequestCountersignSignatureResp]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.requestCountersignSignature)

  val remindSigningRequestRestrictedFlow: RemindSigningRequestParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.remindSigningRequestRestrictedFlow)

  val signCountersignRequest: SignCountersignRequestParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.signCountersignRequest)

  val getLpUnsignedDoc: GetLpUnsignedDocParams => Task[Either[GeneralServiceException, GetLpUnsignedDocResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getLpUnsignedDoc)

  val getLpFormAndValue: GetLpFormAndValueParams => Task[Either[GeneralServiceException, GetLpFormAndValueResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getLpFormAndValue)

  val exportInvestorDashboardData
    : ExportInvestorDashboardDataParams => Task[Either[GeneralServiceException, ExportInvestorDashboardDataResp]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.exportInvestorDashboardData.toSyncEndpoint)

  val getLpsBasicInfo: GetLpsBasicInfoParams => Task[Either[GeneralServiceException, Seq[LpBasicInfo]]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getLpsBasicInfo)

  val batchUpdateInvestorDataWithWorkflow
    : BatchUpdateInvestorDataParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.batchUpdateInvestorDataWithWorkflow)

  val getLpDocuments: GetLpDocumentParams => Task[Either[GeneralServiceException, GetLpDocumentResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getLpDocuments)

  val downloadLpDocuments
    : DownloadLpDocumentsParams => Task[Either[GeneralServiceException, DownloadLpDocumentsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.downloadLpDocuments)

  val queryLpDashboard: LpDashboardQueryParams => Task[Either[GeneralServiceException, LpDashboardQueryResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.queryLpDashboard)

  val markLpAsNotNew: MarkLpAsNotNewParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.markLpAsNotNew)

  val getLpDashboardItem
    : GetLpDashboardItemParams => Task[Either[GeneralServiceException, GetLpDashboardItemResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getLpDashboardItem)

  val getLpDashboardItemList
    : GetLpDashboardItemsParams => Task[Either[GeneralServiceException, GetLpDashboardItemsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getLpDashboardItemList)

  val adminRemoveLpCollaborator: AdminRemoveLpCollaboratorParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.adminRemoveLpCollaborator)

  val accessFormCompare: AccessFormCompareParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.accessFormCompare)

  val compareForm: CompareFormParams => Task[Either[GeneralServiceException, CompareFormResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.compareForm)

  val getCompareResult: GetCompareResultParams => Task[Either[GeneralServiceException, GetCompareResultResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getCompareResult)

  val checkLpFormValidation
    : CheckLpFormValidationParams => Task[Either[GeneralServiceException, CheckLpFormValidationResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.checkLpFormValidation)

  val editFormVersionDescription
    : EditFormVersionDescriptionParams => Task[Either[GeneralServiceException, EditFormVersionDescriptionResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.editFormVersionDescription)

  val getTableOfContentLpProgress
    : GetTableOfContentLpProgressParams => Task[Either[GeneralServiceException, GetTableOfContentLpProgressResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getTableOfContentLpProgress)

  val createDataRoomForIntegration: CreateDataRoomForIntegrationParams => Task[
    Either[GeneralServiceException, CreateDataRoomForIntegrationResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.createDataRoomForIntegration)

  val convertOfflineToNormalOrder
    : ConvertOfflineToNormalOrderParams => Task[Either[GeneralServiceException, ConvertOfflineToNormalOrderResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.convertOfflineToNormalOrder)

  val convertOfflineToNormalOrderWithWorkflow
    : ConvertOfflineToNormalOrderParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.convertOfflineToNormalOrderWithWorkflow)

  val approveLpSubscriptionDocument: FundSubLpId => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.approveLpSubscriptionDocument)

  val createLpTag: CreateLpTagParams => Task[Either[GeneralServiceException, CreateLpTagResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.createLpTag)

  val createLpTags: CreateLpTagsParams => Task[Either[GeneralServiceException, CreateLpTagsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.createLpTags)

  val getLpTagsInFund: GetAllLpTagsInFundParams => Task[Either[GeneralServiceException, GetAllLpTagsInFundResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getLpTagsInFund)

  val editLpTag: EditLpTagParams => Task[Either[GeneralServiceException, EditLpTagResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.editLpTag)

  val removeLpTag: RemoveLpTagParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.removeLpTag)

  val updateTagsOfLp: UpdateTagsOfLpParams => Task[Either[GeneralServiceException, UpdateTagsOfLpResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateTagsOfLp)

  val getSingleLpActivityLog
    : GetSingleLpActivityLogParams => Task[Either[GeneralServiceException, GetSingleLpActivityLogResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getSingleLpActivityLog)

  val getLpActivityLog: GetLpActivityLogParams => Task[Either[GeneralServiceException, GetLpActivityLogResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getLpActivityLog)

  val getNumberOfUnseenActivities
    : GetNumberOfUnSeenActivityParams => Task[Either[GeneralServiceException, GetNumberOfUnSeenActivityResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getNumberOfUnseenActivities)

  val markAsSeenLpActivities
    : MarkActivitiesAsSeenParams => Task[Either[GeneralServiceException, MarkActivitiesAsSeenResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.markAsSeenLpActivities)

  val getFaActivityLog
    : GetFundSubAdminActivityLogParams => Task[Either[GeneralServiceException, GetFundSubAdminActivityLogResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFaActivityLog)

  val markFaActivitiesAsSeen: MarkAsSeenFaActivityLogParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.markFaActivitiesAsSeen)

  val getDataRoomIntegrationInfo
    : GetDataRoomIntegrationInfoParams => Task[Either[GeneralServiceException, GetDataRoomIntegrationInfoResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getDataRoomIntegrationInfo)

  val getFormFilesInfo
    : GetFormFilesForFundAdminParams => Task[Either[GeneralServiceException, GetFormFilesForFundAdminResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFormFilesInfo)

  val getAdminTestFormData
    : GetAdminTestFormDataParams => Task[Either[GeneralServiceException, GetAdminTestFormDataResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getAdminTestFormData)

  val saveAdminTestFormData: SaveAdminTestFormDataParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.saveAdminTestFormData)

  val resendBounceInvitation: ResendBounceInvitationParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.resendBounceInvitation)

  val createProtectedLink
    : CreateProtectedLinkParams => Task[Either[GeneralServiceException, CreateProtectedLinkResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.createProtectedLink)

  val editProtectedLink: EditProtectedLinkParams => Task[Either[GeneralServiceException, EditProtectedLinkResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.editProtectedLink)

  val getFundSubEnvironmentStatus
    : GetFundSubEnvironmentStatusParams => Task[Either[GeneralServiceException, GetFundSubEnvironmentStatusResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundSubEnvironmentStatus)

  val getFundAdminNotificationPreference: GetFundSubAdminNotificationPreferenceParams => Task[
    Either[GeneralServiceException, GetFundSubAdminNotificationPreferenceResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundAdminNotificationPreference)

  val updateFundAdminNotificationPreference
    : UpdateFundSubAdminNotificationPreferenceParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateFundAdminNotificationPreference)

  val getEmailTemplates: GetEmailTemplateParams => Task[Either[GeneralServiceException, GetEmailTemplateResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getEmailTemplates)

  val updateEmailTemplates: UpdateEmailTemplatesParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateEmailTemplates)

  val requestSupportingDocSignature: RequestSupportingDocSignatureParams => Task[
    Either[GeneralServiceException, RequestSupportingDocSignatureResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.requestSupportingDocSignature)

  val signAdditionalSignatureRequest
    : SignAdditionalSignatureRequestParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.signAdditionalSignatureRequest)

  val getLpParticipantInfos
    : GetLpParticipantInfosParams => Task[Either[GeneralServiceException, GetLpParticipantInfosResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getLpParticipantInfos)

  val fundAdminSendCustomEmail
    : FundAdminSendCustomEmailParams => Task[Either[GeneralServiceException, GeneralServiceResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.fundAdminSendCustomEmail)

  val updateLpCommitment: UpdateLpCommitmentParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateLpCommitment)

  val verifyInvestmentEntityIsFromFormData: FundSubLpId => Task[Either[GeneralServiceException, Boolean]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.verifyInvestmentEntityIsFromFormData)

  val checkSeenAutoSelectedCommentsBanner: Unit => Task[Either[GeneralServiceException, Boolean]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.checkSeenAutoSelectedCommentsBanner)

  val markSeenAutoSelectedCommentsBanner: Unit => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.markSeenAutoSelectedCommentsBanner)

  val checkIfUserCanMakePublicCommentInFund: FundSubId => Task[Either[GeneralServiceException, Boolean]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.checkIfUserCanMakePublicCommentInFund)

  val getRecentDashboardId: FundSubId => Task[Either[GeneralServiceException, Option[FundSubDashboardId]]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getRecentDashboardId)

  val setRecentDashboardId: SetRecentDashboardParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.setRecentDashboardId)

  val sendNotifyNewCommentsToInvestor
    : FundAdminSendNotifyNewCommentsToInvestorParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.sendNotifyNewCommentsToInvestor)

  val getLpStatusActivityHistory
    : GetLpStatusActivityHistoryParams => Task[Either[GeneralServiceException, LpStatusHistory]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getLpStatusActivityHistory)

  val getAuditLogData: GetAuditLogDataParams => Task[Either[GeneralServiceException, GetAuditLogDataResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getAuditLogData)

  val getEmailBodyContentString: InternalEmailId => Task[Either[GeneralServiceException, Option[String]]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getEmailBodyContentString)

  val exportAuditLogData
    : ExportAuditLogDataParams => Task[Either[GeneralServiceException, ExportAuditLogDataResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.exportAuditLogData)

  val getFundMemberForFilter: FundSubId => Task[Either[GeneralServiceException, Seq[(UserId, UserInfo)]]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundMemberForFilter)

  val getInvestorForFilter: FundSubLpId => Task[Either[GeneralServiceException, Seq[(UserId, UserInfo)]]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getInvestorForFilter)

  val getInvestmentEntityForFilter: FundSubId => Task[Either[GeneralServiceException, Seq[(FundSubLpId, String)]]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getInvestmentEntityForFilter)

  val getEmailLogData: GetEmailLogDataParams => Task[Either[GeneralServiceException, GetEmailLogDataResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getEmailLogData)

  val addMainLp: AddFundSubMainLpsParams => Task[Either[GeneralServiceException, AddFundSubLpsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.addMainLp)

  val lpInvestInAdditionalEntity
    : LpInvestInAdditionalEntityParams => Task[Either[GeneralServiceException, LpInvestInAdditionalEntityResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.lpInvestInAdditionalEntity)

  val lpInvestInAdditionalEntityFromMultipleLpsEmailLink
    : LpInvestInAdditionalEntityFromMultipleLpsEmailLinkParams => Task[
      Either[GeneralServiceException, LpInvestInAdditionalEntityFromMultipleLpsEmailLinkResp]
    ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.lpInvestInAdditionalEntityFromMultipleLpsEmailLink)

  val getAllFirmNamesOfLp
    : GetAllFirmNamesOfLpParams => Task[Either[GeneralServiceException, GetAllFirmNamesOfLpResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getAllFirmNamesOfLp)

  val resendLpInvitation
    : ResendLpInvitationParams => Task[Either[GeneralServiceException, ResendLpInvitationResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.resendLpInvitation)

  val resendLpInvitationWithWorkflow: ResendLpInvitationParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.resendLpInvitationWithWorkflow)

  val addLpCollaborator: AddFundSubLpCollaboratorsParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.addLpCollaborator)

  val removeLpCollaborator: RemoveLpCollaboratorParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.removeLpCollaborator)

  val promoteCollaboratorToLpRole: PromoteCollaboratorToLpRoleParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.promoteCollaboratorToLpRole)

  val revokeLp: RevokeFundSubLpParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.revokeLp)

  val restoreLp: RestoreFundSubLpParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.restoreLp)

  val remindLpCompleteForm
    : RemindLpCompleteFormParams => Task[Either[GeneralServiceException, RemindLpCompleteFormResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.remindLpCompleteForm)

  val remindLpCompleteFormWithWorkflow: RemindLpCompleteFormParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.remindLpCompleteFormWithWorkflow)

  val requestSupportingDocRestrictedFlow: RequestSupportingDocParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.requestSupportingDocRestrictedFlow)

  val remindSupportingDoc
    : RemindSupportingDocParams => Task[Either[GeneralServiceException, RemindSupportingDocResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.remindSupportingDoc)

  val remindSupportingDocWithWorkflow: RemindSupportingDocParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.remindSupportingDocWithWorkflow)

  val lpGetForm: FundSubGetFormParams => Task[Either[GeneralServiceException, FundSubGetFormResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.lpGetForm)

  val getTaxFormData: GetTaxFormDataParams => Task[Either[GeneralServiceException, GetTaxFormDataResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getTaxFormData)

  val getFormData: GetFormDataParams => Task[Either[GeneralServiceException, GetFormDataResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFormData)

  val submitNewSupportingDoc: SubmitSupportingDocParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.submitNewSupportingDoc)

  val addAdditionalTaxForm
    : AddAdditionalTaxFormParams => Task[Either[GeneralServiceException, AddAdditionalTaxFormResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.addAdditionalTaxForm)

  val removeAdditionalTaxForm: RemoveAdditionalTaxFormParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.removeAdditionalTaxForm)

  val getFundAdditionalTaxForms: GetFundAdditionalTaxFormListsParams => Task[
    Either[GeneralServiceException, GetFundAdditionalTaxFormListResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundAdditionalTaxForms)

  val lpJoin: FundSubLpJoinParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.lpJoin)

  val adminSubmitLpPackage: FundSubLpSubmitParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.adminSubmitLpPackage)

  val saveFormValue: FundSubLpSaveFormParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.saveFormValue)

  val saveTaxFormValue: SaveTaxFormValueParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.saveTaxFormValue)

  val lpSignAndSubmitTaxForm: LpSignAndSubmitTaxFormParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.lpSignAndSubmitTaxForm)

  val requestLpChange: RequestLpChangeParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.requestLpChange)

  val lpViewSection: FundSubLpViewSectionParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.lpViewSection)

  val lpViewPage: FundSubLpViewPageParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.lpViewPage)

  val updateInvestorFormSetting: UpdateInvestorFormSettingParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateInvestorFormSetting)

  val lpGoBackToForm: FundSubLpGoBackToFormParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.lpGoBackToForm)

  val lpGoToSignature: FundSubLpGoToSignatureParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.lpGoToSignature)

  val uploadCounterSignedDocs
    : UploadCounterSignParams => Task[Either[GeneralServiceException, UploadCounterSignResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.uploadCounterSignedDocs)

  val uploadCounterSignedDocsWithWorkflow: UploadCounterSignParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.uploadCounterSignedDocsWithWorkflow)

  val counterESignDocs: CounterEsignParams => Task[Either[GeneralServiceException, CounterEsignResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.counterESignDocs)

  val getBatchCountersignData
    : GetBatchCountersignDataParams => Task[Either[GeneralServiceException, GetBatchCountersignDataResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getBatchCountersignData)

  val getUserPendingCountersignRequests: GetUserPendingCountersignRequestParams => Task[
    Either[GeneralServiceException, GetUserPendingCountersignRequestResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getUserPendingCountersignRequests)

  val removeCounterSignedDocs: RemoveCounterSignParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.removeCounterSignedDocs)

  val distributeCounterSignedDocs
    : DistributeCounterSignParams => Task[Either[GeneralServiceException, DistributeCounterSignResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.distributeCounterSignedDocs)

  val distributeCounterSignedDocsWithWorkflow
    : DistributeCounterSignParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.distributeCounterSignedDocsWithWorkflow)

  val uploadAndDistributeCounterSignedDocsRestrictedFlow
    : UploadAndDistributeCountersignRestrictedFlowParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.uploadAndDistributeCounterSignedDocsRestrictedFlow)

  val markSubscriptionAsComplete
    : MarkSubscriptionAsCompleteParams => Task[Either[GeneralServiceException, EmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.markSubscriptionAsComplete)

  val generateDataForSigningStep: GenerateDataForSigningParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.generateDataForSigningStep)

  val getSubDocSigningType
    : GetSubDocSigningTypeParams => Task[Either[GeneralServiceException, GetSubDocSigningTypeResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getSubDocSigningType)

  val generatePdf: GeneratePdfParams => Task[Either[GeneralServiceException, GeneratePdfResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.generatePdf)

  val generateTaxPdf: GenerateTaxPdfParams => Task[Either[GeneralServiceException, GenerateTaxPdfResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.generateTaxPdf)

  val selectTaxForm: SelectTaxFormParams => Task[Either[GeneralServiceException, SelectTaxFormResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.selectTaxForm)

  val getTaxFormsMapping
    : GetTaxFormsMappingParams => Task[Either[GeneralServiceException, GetTaxFormsMappingResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getTaxFormsMapping)

  val getSignaturePagesDownloadUrl: GetSignaturePagesDownloadUrlParams => Task[
    Either[GeneralServiceException, GetSignaturePagesDownloadUrlResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getSignaturePagesDownloadUrl)

  val requestSubscriptionDocSignature: RequestSubdocSignatureParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.requestSubdocSignature)

  val fundManagerAccessLpView: FundManagerAccessLpSubscriptionParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.fundManagerAccessLpView)

  val activateManualSubscriptionParams
    : ActivateManualSubscriptionParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.activateManualSubscriptionParams)

  val checkSeenLpOnboardGuideTour: Unit => Task[Either[GeneralServiceException, Boolean]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.checkSeenLpOnboardGuideTour)

  val markSeenLpOnboardGuideTour: Unit => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.markSeenLpOnboardGuideTour)

  val verifyInvestmentEntityStandardAliasSetup: FundSubLpId => Task[Either[GeneralServiceException, Boolean]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.verifyInvestmentEntityStandardAliasSetup)

  val getUserModuleOnboarding: ModuleOnboardingMessage => Task[Either[GeneralServiceException, Boolean]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getUserModuleOnboarding)

  val setUserModuleOnboarding: ModuleOnboardingMessage => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.setUserModuleOnboarding)

  val lpESign: LpSelfSignFormParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.lpESign)

  val lpUploadSignedDoc: LpUploadSignedDocParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.lpUploadSignedDoc)

  val signSubDocRequest: SignSubDocRequestParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.signSubDocRequest)

  val cancelSigning: CancelSigningParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.cancelSigning)

  val cancelLpSignatureRequestRestrictedFlow
    : CancelSignatureRequestParamsRestrictedFlow => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.cancelLpSignatureRequestRestrictedFlow)

  val markSubDocRequestAsCompletedRestrictedFlow
    : MarkSubDocRequestAsCompletedParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.markSubDocRequestAsCompletedRestrictedFlow)

  val uploadSupportingDocsOnBehalfRestrictedFlow
    : UploadSupportingDocsOnBehalfParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.uploadSupportingDocsOnBehalfRestrictedFlow)

  val uploadRefDocForLp: UploadReferenceDocsParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.uploadRefDocForLp)

  val removeRefDocForLp: RemoveReferenceDocsParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.removeRefDocForLp)

  val mergeLpDocuments: MergeLpDocumentsParams => Task[Either[GeneralServiceException, MergeLpDocumentsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.mergeLpDocuments)

  val getAllLpNewSupportingDocs
    : GetLpSupportingDocsParams => Task[Either[GeneralServiceException, GetLpSupportingDocsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getAllLpNewSupportingDocs)

  val resubmitNewSupportingDoc: ResubmitSupportingDocParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.resubmitNewSupportingDoc)

  val markNewSupportingDocAsNotApplicable
    : MarkSupportingDocAsNotApplicableParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.markNewSupportingDocAsNotApplicable)

  val unmarkNewSupportingDocAsNotApplicable
    : UnmarkSupportingDocAsNotApplicableParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.unmarkNewSupportingDocAsNotApplicable)

  val removeSupportingDoc: RemoveSupportingDocParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.removeSupportingDoc)

  val uploadAndSubmitSupportingDoc: UploadAndSubmitSupportingDocParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.uploadAndSubmitSupportingDoc)

  val removeOtherSupportingDoc: RemoveOtherSupportingDocParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.removeOtherSupportingDoc)

  val submitOtherTypeSupportingDoc: SubmitOtherTypeSupportingDocParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.submitOtherTypeSupportingDoc)

  val removeUploadedNewSupportingDocFile
    : RemoveUploadedSupportingDocFileParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.removeUploadedNewSupportingDocFile)

  val uploadAndSubmitOtherTypeSupportingDoc
    : UploadAndSubmitOtherTypeSupportingDocParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.uploadAndSubmitOtherTypeSupportingDoc)

  val requestSignatureOnTaxForm: RequestSignatureOnTaxFormParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.requestSignatureOnTaxForm)

  val cancelSignatureRequestOnTaxForm: CancelSignatureOnTaxFormParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.cancelSignatureRequestOnTaxForm)

  val remindToSignTaxForm: RemindSignerOnTaxFormParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.remindToSignTaxForm)

  val signTaxFormSignatureRequest: SignTaxFormSignatureRequestParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.signTaxFormSignatureRequest)

  val getTaxFormSignatureRequestData
    : FundSubSupportingDocId => Task[Either[GeneralServiceException, GetTaxFormSignatureRequestResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getTaxFormSignatureRequestData)

  val updateSupportingDocName: UpdateSupportingDocNameParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateSupportingDocName)

  val updateLpInformation: UpdateLpInformationParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateLpInformation)

  val getAccessToProtectedLink
    : GetAccessToProtectedLinkParams => Task[Either[GeneralServiceException, GetAccessToProtectedLinkResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getAccessToProtectedLink)

  val getFundAdminContact
    : GetFundAdminContactParams => Task[Either[GeneralServiceException, GetFundAdminContactResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundAdminContact)

  val updateFirmName: UpdateFirmNameParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateFirmName)

  val viewedOnboardingModal: Unit => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.viewedOnboardingModal)

  val getContactAndGroup
    : GetContactAndGroupParams => Task[Either[GeneralServiceException, GetContactAndGroupResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getContactAndGroup)

  val createContactGroup
    : CreateContactGroupParams => Task[Either[GeneralServiceException, CreateContactGroupResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.createContactGroup)

  val editContactGroup: EditContactGroupParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.editContactGroup)

  val deleteContactGroup: DeleteContactGroupParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.deleteContactGroup)

  val addContact: AddContactParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.addContact)

  val editContact: EditContactParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.editContact)

  val deleteContact: DeleteContactParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.deleteContact)

  val batchDeleteContactAndGroup: BatchDeleteContactAndGroupParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.batchDeleteContactAndGroup)

  val listContactsByEmailOrder
    : ListContactsByEmailOrderParams => Task[Either[GeneralServiceException, ListContactsByEmailOrderResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.listContactsByEmailOrder)

  val queryContactsByEmail
    : QueryContactsByEmailParams => Task[Either[GeneralServiceException, QueryContactsByEmailResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.queryContactsByEmail)

  val queryContactsByName
    : QueryContactsByNameParams => Task[Either[GeneralServiceException, QueryContactsByNameResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.queryContactsByName)

  val getContactsWithSameEmailForGroups: GetContactsWithSameEmailForGroupParams => Task[
    Either[GeneralServiceException, GetContactsWithSameEmailForGroupResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getContactsWithSameEmailForGroups)

  val queryGroupByName: QueryGroupByNameParams => Task[Either[GeneralServiceException, QueryGroupsByNameResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.queryGroupByName)

  val getAllGroups: GetAllGroupParams => Task[Either[GeneralServiceException, GetAllGroupsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getAllGroups)

  val getGroupContacts: GetGroupContactsParams => Task[Either[GeneralServiceException, GetGroupsContactsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getGroupContacts)

  val getWhiteLabel: GetFundSubWhiteLabelParams => Task[Either[GeneralServiceException, FundSubWhiteLabelData]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getWhiteLabel)

  val updateWhiteLabel
    : UpdateFundSubWhiteLabelParams => Task[Either[GeneralServiceException, UpdateFundSubWhiteLabelResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateWhiteLabel)

  val removeWhiteLabelLogo
    : RemoveFundSubWhiteLabelLogoParams => Task[Either[GeneralServiceException, GeneralServiceResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.removeWhiteLabelLogo)

  val getCopyConfigForClient
    : GetCopyConfigForClientParams => Task[Either[GeneralServiceException, GetCopyConfigForClientResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getCopyConfigForClient)

  val generateExcel: ExportExcelParams => Task[Either[GeneralServiceException, FileId]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.generateExcel)

  val loadExportTemplateFromFile
    : LoadExportTemplateFromFileParams => Task[Either[GeneralServiceException, LoadExportTemplateFromFileResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.loadExportTemplateFromFile)

  val generateFundSubExportTemplateId: GenerateFundSubExportTemplateIdParams => Task[
    Either[GeneralServiceException, GenerateFundSubExportTemplateIdResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.generateFundSubExportTemplateId)

  val generateDefaultExportTemplate: GenerateDefaultExportTemplateParams => Task[
    Either[GeneralServiceException, GenerateDefaultExportTemplateResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.generateDefaultExportTemplate)

  val generateDefaultExportTemplateFile: GenerateDefaultExportTemplateFileParams => Task[
    Either[GeneralServiceException, GenerateDefaultExportTemplateFileResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.generateDefaultExportTemplateFile)

  val getFundSubExportFormsAndTemplates: GetFundSubExportFormsAndTemplatesParams => Task[
    Either[GeneralServiceException, GetFundSubExportFormsAndTemplatesResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundSubExportFormsAndTemplates)

  val saveExportTemplateInfo
    : SaveExportTemplateInfoParams => Task[Either[GeneralServiceException, SaveExportTemplateInfoResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.saveExportTemplateInfo)

  val getAllTemplateInfosToExport
    : GetAllTemplateInfosToExportParams => Task[Either[GeneralServiceException, GetAllTemplateInfosToExportResp]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getAllTemplateInfosToExport)

  val exportLpDataUsingDefaultTemplate
    : ExportLpDataUsingDefaultTemplateParams => Task[Either[GeneralServiceException, FileId]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.exportLpDataUsingDefaultTemplate)

  val exportLpDataUsingDefaultPdfTemplate
    : ExportLpDataUsingDefaultPdfTemplateParams => Task[Either[GeneralServiceException, FileId]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.exportLpDataUsingDefaultPdfTemplate)

  val exportLpDataUsingCustomTemplate
    : ExportLpDataUsingCustomTemplateParams => Task[Either[GeneralServiceException, FileId]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.exportLpDataUsingCustomTemplate)

  val generateDefaultImportTemplateFile: GenerateDefaultImportTemplateFileParams => Task[
    Either[GeneralServiceException, GenerateDefaultImportTemplateFileResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.generateDefaultImportTemplateFile)

  val startImportWorkflow
    : StartImportWorkflowParams => Task[Either[GeneralServiceException, StartImportWorkflowResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.startImportWorkflow)

  val getImportWorkflowStatus
    : GetImportWorkflowStatusParams => Task[Either[GeneralServiceException, GetImportWorkflowStatusResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getImportWorkflowStatus)

  val getImportWorkflowItemResult
    : GetImportItemResultParams => Task[Either[GeneralServiceException, GetImportItemResultResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getImportWorkflowItemResult)

  val getFormTemplateMapping
    : GetFormTemplateMappingParams => Task[Either[GeneralServiceException, GetFormTemplateMappingResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFormTemplateMapping)

  val updateSharedDrLink: UpdateSharedDataRoomLinkParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateSharedDrLink)

  val createComment: CreateCommentParams => Task[Either[GeneralServiceException, Option[IssueId]]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.createComment)

  val addCommentReply: AddCommentReplyParams => Task[Either[GeneralServiceException, AddCommentReplyResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.addCommentReply)

  val resolveComment: ResolveCommentParams => Task[Either[GeneralServiceException, CommonResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.resolveComment)

  val assignComment: AssignCommentParams => Task[Either[GeneralServiceException, AssignCommentResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.assignComment)

  val unAssignComment: UnAssignCommentParams => Task[Either[GeneralServiceException, CommonResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.unAssignComment)

  val reopenComment: ReopenCommentParams => Task[Either[GeneralServiceException, CommonResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.reopenComment)

  val updateComment: UpdateCommentParams => Task[Either[GeneralServiceException, CommonResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateComment)

  val updateCommentReply: UpdateCommentReplyParams => Task[Either[GeneralServiceException, CommonResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateCommentReply)

  val deleteComment: DeleteCommentParams => Task[Either[GeneralServiceException, CommonResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.deleteComment)

  val flagComment: FlagCommentParams => Task[Either[GeneralServiceException, FlagCommentResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.flagComment)

  val deleteCommentReply: DeleteCommentReplyParams => Task[Either[GeneralServiceException, CommonResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.deleteCommentReply)

  val getCommentThread: GetCommentThreadParams => Task[Either[GeneralServiceException, GetCommentThreadResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getCommentThread)

  val getCommentAnchorPointAssignee: GetCommentAnchorPointAssigneeParams => Task[
    Either[GeneralServiceException, GetCommentAnchorPointAssigneeResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getCommentAnchorPointAssignee)

  val getFundComments: GetFundCommentParams => Task[Either[GeneralServiceException, GetFundCommentsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundComments)

  val getFundCommentAnchorPoints
    : GetFundCommentDualThreadsParams => Task[Either[GeneralServiceException, GetFundCommentDualThreadsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundCommentAnchorPoints)

  val getDataForCommentFilter
    : GetDataForCommentFilterParams => Task[Either[GeneralServiceException, GetDataForCommentFilterResp]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getDataForCommentFilter)

  val getLpsMetaInfo: GetLpsMetaInfoParams => Task[Either[GeneralServiceException, GetLpsMetaInfoResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getLpsMetaInfo)

  val getInboxTabSummary
    : GetInboxTabSummaryParams => Task[Either[GeneralServiceException, GetInboxTabSummaryResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getInboxTabSummary)

  val getCommentMentions: GetCommentMentionParams => Task[Either[GeneralServiceException, GetCommentMentionsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getCommentMentions)

  val getMentionsInfoInComment: GetMentionsInfoInCommentThreadParam => Task[
    Either[GeneralServiceException, GetMentionsInfoInCommentThreadResponse]
  ] = toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getMentionsInfoInComment)

  val getMentionableList
    : GetMentionableListParams => Task[Either[GeneralServiceException, GetMentionableListResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getMentionableList)

  val getAssignableList: GetAssignableListParams => Task[Either[GeneralServiceException, GetAssignableListResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getAssignableList)

  val getAssigneeList
    : GetAssigneeFilterListParams => Task[Either[GeneralServiceException, GetAssigneeFilterListResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getAssigneeList)

  val getSingleLpComments: GetLpCommentsParams => Task[Either[GeneralServiceException, GetLpCommentsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getSingleLpComments)

  val getGaiaFormCommentAllFieldsInfo: GetGaiaFormCommentAllFieldsInfoParams => Task[
    Either[GeneralServiceException, GetFormCommentAllFieldsInfoResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getGaiaFormCommentAllFieldsInfo)

  val markWholeCommentThreadAsSeen
    : MarkWholeCommentThreadAsSeenParams => Task[Either[GeneralServiceException, CommonResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.markWholeCommentThreadAsSeen)

  val markFormCommentNotifSpaceAsSeen
    : MarkFormCommentingNotifSpaceAsSeenParams => Task[Either[GeneralServiceException, CommonResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.markFormCommentNotifSpaceAsSeen)

  val getPublicThreadsFromIds: GetPublicThreadsFromIdsParams => Task[
    Either[GeneralServiceException, GetPublicThreadsFromIdsResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getPublicThreadsFromIds)

  val checkIfUserCanMakePublicComment: FundSubLpId => Task[Either[GeneralServiceException, Boolean]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.checkIfUserCanMakePublicComment)

  val getFundCommentNotification
    : GetFundCommentNotificationParams => Task[Either[GeneralServiceException, GetFundCommentNotificationResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundCommentNotification)

  val enableReviewPackage: EnableReviewPackageParams => Task[Either[GeneralServiceException, GeneralServiceResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.enableReviewPackage)

  val disableReviewPackage
    : DisableReviewPackageParams => Task[Either[GeneralServiceException, DisableReviewPackageResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.disableReviewPackage)

  val updateReviewPackageSetting: UpdateReviewPackageSettingParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateReviewPackageSetting)

  val assignReviewers: AssignReviewersParams => Task[Either[GeneralServiceException, GeneralServiceResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.assignReviewers)

  val removeReviewers: RemoveReviewersParams => Task[Either[GeneralServiceException, RemoveReviewersResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.removeReviewers)

  val checkReviewerPermission: FundSubLpId => Task[Either[GeneralServiceException, CheckReviewerPermissionResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.checkReviewerPermission)

  val generateInvestors: GenerateInvestorParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.generateInvestors)

  val getFundSubCloseData: FundSubId => Task[Either[GeneralServiceException, GetFundSubClosesInfoResp]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundSubCloseData)

  val createFundSubClose: CreateFundSubCloseParams => Task[Either[GeneralServiceException, FundSubCloseId]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.createFundSubClose)

  val updateFundSubClose: UpdateFundSubCloseParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateFundSubClose)

  val deleteFundSubClose: DeleteFundSubCloseParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.deleteFundSubClose)

  val moveLpsToClose: MoveLpsToCloseParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.moveLpsToClose)

  val getOriginalSubscriptionFormName: FundSubLpId => Task[Either[GeneralServiceException, String]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getOriginalSubscriptionFormName)

  val getOriginalSubscriptionDocuments: FundSubLpId => Task[Either[GeneralServiceException, Seq[FileId]]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getOriginalSubscriptionDocuments)

  val getSubscriptionVersionBasicInfo: GetSubscriptionVersionBasicInfoParams => Task[
    Either[GeneralServiceException, GetSubscriptionVersionBasicInfoResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getSubscriptionVersionBasicInfo)

  val getSubscriptionVersionBasicInfoWithErrorHandler: GetSubscriptionVersionBasicInfoParams => Task[
    Either[GetSubscriptionVersionBasicInfoException, GetSubscriptionVersionBasicInfoResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getSubscriptionVersionBasicInfoWithErrorHandler)

  val getSubscriptionVersionBasicInfoWithoutGeneratingCleanDocs: GetSubscriptionVersionBasicInfoParams => Task[
    Either[GeneralServiceException, GetSubscriptionVersionBasicInfoResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getSubscriptionVersionBasicInfoWithoutGeneratingCleanDocs)

  val getAllSubscriptionVersionsBasicInfo: GetAllSubscriptionVersionBasicInfoParams => Task[
    Either[GeneralServiceException, GetAllSubscriptionVersionBasicInfoResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getAllSubscriptionVersionsBasicInfo)

  val getNumberOfSignedVersion: FundSubLpId => Task[Either[GeneralServiceException, Int]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getNumberOfSignedVersion)

  val getSubscriptionVersionFormInfo: GetSubscriptionVersionFormInfoParams => Task[
    Either[GeneralServiceException, GetSubscriptionVersionFormInfoResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getSubscriptionVersionFormInfo)

  val getSubscriptionDocSigningType: GetSubscriptionDocSigningTypeParams => Task[
    Either[GeneralServiceException, GetSubscriptionDocSigningTypeResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getSubscriptionDocSigningType)

  val saveSubscriptionFormData
    : SaveSubscriptionFormDataParams => Task[Either[GeneralServiceException, SaveSubscriptionFormDataResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.saveSubscriptionFormData)

  val startEditingSubscriptionForm: StartEditingSubscriptionFormParams => Task[
    Either[GeneralServiceException, StartEditingSubscriptionFormResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.startEditingSubscriptionForm)

  val calculateAndUpdateSignatureStatus: FundSubLpId => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.calculateAndUpdateSignatureStatus)

  val checkIfNeedToSignAgainAfterEditForm: FundSubLpId => Task[Either[GeneralServiceException, Boolean]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.checkIfNeedToSignAgainAfterEditForm)

  val createSubscriptionDocSignatureRequest: CreateSubscriptionDocSignatureRequestParams => Task[
    Either[GeneralServiceException, CreateSubscriptionDocSignatureRequestResp]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.createSubscriptionDocSignatureRequest)

  val eSignSubscriptionDoc: ESignSubscriptionDocParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.eSignSubscriptionDoc)

  val remindSubscriptionDocSignatureRequest
    : RemindSubscriptionDocSignatureRequestParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.remindSubscriptionDocSignatureRequest)

  val uploadSignedSubscriptionDoc: UploadSignedSubscriptionDocParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.uploadSignedSubscriptionDoc)

  val gpUploadSignedSubscriptionDoc
    : GpUploadSignedSubscriptionDocParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.gpUploadSignedSubscriptionDoc)

  val gpUploadExecutedSubscription: GpUploadExecutedSubscriptionParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.gpUploadExecutedSubscription)

  val cancelCompletedSubscriptionSignature: FundSubLpId => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.cancelCompletedSubscriptionSignature)

  val lpManuallySubmitPackage: LpManuallySubmitPackageParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.lpManuallySubmitPackage)

  val submitSubscriptionVersionForReview
    : SubmitSubscriptionVersionForReviewParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.submitSubscriptionVersionForReview)

  val getRequestChangeInfo
    : FundSubLpId => Task[Either[GeneralServiceException, GetSubscriptionDocRequestChangesInfoResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getRequestChangeInfo)

  val getToSubmitSubscriptionDocuments: GetToSubmitSubscriptionDocumentsParams => Task[
    Either[GeneralServiceException, Seq[SubscriptionSubmittedDocument]]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getToSubmitSubscriptionDocuments)

  val addMainLpWorkflow: AddFundSubMainLpsParams => Task[Either[GeneralServiceException, FundSubBatchInvitationId]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.addMainLpWorkflow)

  // add lps workflow, each lp has their own closeId, investorGroup and documents shared with them only
  val addMainLpWorkflowWithoutJointInfo
    : AddFundSubMainLpsWithoutJointInfoParams => Task[Either[GeneralServiceException, FundSubBatchInvitationId]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.addMainLpWorkflowWithoutJointInfo)

  val getBatchInvitationInfo
    : FundSubBatchInvitationId => Task[Either[GeneralServiceException, FundSubBatchInvitationInfo]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getBatchInvitationInfo)

  val getAllBatchInvitationInfo
    : GetAllBatchInvitationInfosParams => Task[Either[GeneralServiceException, GetAllBatchInvitationInfos]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getAllBatchInvitationInfo)

  val removeBatchInvitation: FundSubBatchInvitationId => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.removeBatchInvitation)

  val stopBatchInvitation: FundSubBatchInvitationId => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.stopBatchInvitation)

  val exportBatchInviteInvestorReport
    : ExportBatchInviteInvestorReportParams => Task[Either[GeneralServiceException, FileId]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.exportBatchInviteInvestorReport)

  val getFundSubPointOfContact
    : GetFundSubPointOfContactParams => Task[Either[GeneralServiceException, GetFundSubPointOfContactResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundSubPointOfContact)

  val setFundSubPointOfContactWithExternal
    : SetFundSubPointOfContactWithExternalParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.setFundSubPointOfContactWithExternal)

  val getFundSubPointOfContactWithExternal: GetFundSubPointOfContactWithExternalParams => Task[
    Either[GeneralServiceException, GetFundSubPointOfContactWithExternalResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundSubPointOfContactWithExternal)

  val getAllRelatedOrderIds: FundSubLpId => Task[Either[GeneralServiceException, List[FundSubLpId]]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getAllRelatedOrderIds)

  val getAllSupportingDocsOfOrder
    : FundSubLpId => Task[Either[GeneralServiceException, GetOrderSupportingDocsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getAllSupportingDocsOfOrder)

  val exportSwitchUsageMetrics: ExportSwitchUsageMetricsParams => Task[Either[GeneralServiceException, FileId]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.exportSwitchUsageMetrics)

  val getFundFormModel: FundSubId => Task[Either[GeneralServiceException, Either[DynamicFormInfo, GaiaForm]]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundFormModel)

  val computePrefillDataFromPastLp
    : ComputePrefillDataFromPastLpParams => Task[Either[GeneralServiceException, LpAutoPrefillData]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.computePrefillDataFromPastLp)

  val computePrefillDataFromPastSubscription: ComputePrefillDataFromPastSubscriptionParams => Task[
    Either[GeneralServiceException, ComputeFormPrefillDataResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.computePrefillDataFromPastSubscription)

  val prefillLpDataFromPastSubscription
    : PrefillLpDataFromPastSubscriptionParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.prefillLpDataFromPastSubscription)

  val saveProfileFromSubscription: SaveProfileFromSubscriptionParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.saveProfileFromSubscription)

  val compareSubscriptionAndProfileData: CompareSubscriptionAndProfileDataParams => Task[
    Either[GeneralServiceException, CompareSubscriptionAndProfileDataResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.compareSubscriptionAndProfileData)

  val compareMainSubscriptionAndLpProfileTemplateData: CompareSubscriptionAndProfileTemplateParams => Task[
    Either[GeneralServiceException, CompareSubscriptionAndProfileDataResponse]
  ] = toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.compareMainSubscriptionAndLpProfileTemplateData)

  val batchComputeNumPrefillableFieldsForLp: BatchComputeNumPrefillableFieldsForLpParams => Task[
    Either[GeneralServiceException, BatchComputeNumPrefillableFieldsResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.batchComputeNumPrefillableFieldsForLp)

  val computeProfilePrefillDataForLp: ComputeProfilePrefillDataForLpParams => Task[
    Either[GeneralServiceException, ComputeFormPrefillDataResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.computeProfilePrefillDataForLp)

  val prefillLpDataFromProfile: PrefillLpDataFromProfileParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.prefillLpDataFromProfile)

  val computeSupportingFormDataFromProfile: ComputeSupportingFormDataFromProfileParams => Task[
    Either[GeneralServiceException, ComputeFormPrefillDataResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.computeSupportingFormDataFromProfile)

  val prefillSupportingFormDataFromProfile
    : PrefillSupportingFormDataFromProfileParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.prefillSupportingFormDataFromProfile)

  val batchComputeNumPrefillableFieldsForSupportingForm: BatchComputeNumPrefillableFieldsForSupportingFormParams => Task[
    Either[GeneralServiceException, BatchComputeNumPrefillableFieldsResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.batchComputeNumPrefillableFieldsForSupportingForm)

  val clearLpFormValues: FundSubLpId => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.clearLpFormValues)

  val checkLpAutofillConditions
    : CheckLpAutofillConditionsParams => Task[Either[GeneralServiceException, CheckLpAutofillConditionsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.checkLpAutofillConditions)

  val checkLpSaveDocumentsConditions: CheckLpSaveDocumentsConditionsParams => Task[
    Either[GeneralServiceException, CheckLpSaveDocumentsConditionsResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.checkLpSaveDocumentsConditions)

  val getFormAsaImportTemplate
    : GetFormAsaImportTemplateParams => Task[Either[GeneralServiceException, GetFormAsaImportTemplateResp]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFormAsaImportTemplate)

  val batchSignCountersignRequest
    : BatchSignCountersignRequestParams => Task[Either[GeneralServiceException, BatchSignCountersignRequestResp]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.batchSignCountersignRequest)

  val getLpDocumentsPageData
    : GetLpDocumentsPageParams => Task[Either[GeneralServiceException, GetLpDocumentsPageResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getLpDocumentsPageData)

  val getInviteLpData: FundSubId => Task[Either[GeneralServiceException, GetInviteLpDataResp]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getInviteLpData)

  val appendSkipOnboardingFlowUser: FundSubLpId => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.appendSkipOnboardingFlowUser)

  val getFundSubEnvironmentBaseUrlWithPrefixPath: FundSubId => Task[Either[GeneralServiceException, String]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundSubEnvironmentBaseUrlWithPrefixPath)

  val userDismissNameMismatch: FundSubLpId => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.userDismissNameMismatch)

  val updateListUserShouldNotShowSavingDocSuggestion
    : UpdateListUserShouldNotShowSavingDocSuggestionParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateListUserShouldNotShowSavingDocSuggestion)

  val updateLpAutoSavedFlags: UpdateLpAutoSavedFlagsParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateLpAutoSavedFlags)

  val markUserSeenAutoFillTour: FundSubLpId => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.markUserSeenAutoFillTour)

  val getFilesSharedFromInvestmentEntity: GetFilesSharedFromInvestmentEntityParams => Task[
    Either[GeneralServiceException, GetFilesSharedFromInvestmentEntityResp]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFilesSharedFromInvestmentEntity)

  val getUserTracking: Unit => Task[Either[GeneralServiceException, GetUserTrackingResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getUserTracking)

  val updateUserTracking: UpdateUserTrackingParams => Task[Either[GeneralServiceException, FundSubUserTrackingModel]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateUserTracking)

  val getFundMemberRole: GetFundMemberRoleParams => Task[
    Either[GeneralServiceException, GetFundMemberRoleResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundMemberRole)

  val getLpReferenceDocs: GetLpReferenceDocsParams => Task[Either[GeneralServiceException, GetLpReferenceDocsResp]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getLpReferenceDocs)

  val exportComment: ExportFundCommentParams => Task[Either[GeneralServiceException, TemporalWorkflowId]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.exportComment)

  val cancelCommentExportTask: CancelCommentExportTaskParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.cancelCommentExportTask)

  val getCommentExportTaskStatus
    : FundSubId => Task[Either[GeneralServiceException, GetCommentExportTaskStatusResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getCommentExportTaskStatus)

  val getAmlCheckByLpId: FundSubLpId => Task[Either[GeneralServiceException, GetFundSubAmlCheckResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getAmlCheckByLpId)

  val getFundSubMultiRegionInfo
    : CommonParams.Empty => Task[Either[GeneralServiceException, GetFundSubRegionInfosResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundSubMultiRegionInfo)

  val getFundSubEntityName
    : GetFundSubEntityNameParams => Task[Either[GeneralServiceException, GetFundSubEntityNameResp]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundSubEntityName)

  val getFundSubSignatureConfig
    : FundSubLpId => Task[Either[GeneralServiceException, GetFundSubSignatureConfigResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getFundSubSignatureConfig)

  val getSignatureRequestInfo
    : GetSignatureRequestInfoParams => Task[Either[GeneralServiceException, SignatureRequestInfo]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getSignatureRequestInfo)

  val getSubscriptions: GetSubscriptionsParams => Task[Either[GeneralServiceException, GetSubscriptionsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getSubscriptions)

  val getSmtpConfig: FundSubId => Task[Either[GeneralServiceException, CustomSmtpServerConfigParams]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.getSmtpConfig)

  val updateSmtpConfig: UpdateSmtpConfigParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateSmtpConfig)

  val sendTestEmailUsingCustomSmtp: SendTestEmailUsingCustomSmtpParams => Task[
    Either[GeneralServiceException, Unit]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.sendTestEmailUsingCustomSmtp)

  val updateAllowFormEditPostSigning
    : UpdateAllowFormEditPostSigningParams => Task[Either[GeneralServiceException, EmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubEndpoints.updateAllowFormEditPostSigning)

}
