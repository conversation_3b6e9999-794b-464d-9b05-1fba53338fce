// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.client

import anduin.fundsub.endpoint.integplatform.*
import anduin.service.GeneralServiceException
import anduin.tapir.client.AuthenticatedEndpointClient
import anduin.tapir.endpoint.EmptyResponse
import zio.Task

object FundSubIntegPlatformEndpointClient extends AuthenticatedEndpointClient {

  val lookupIntegrations
    : FundSubLookupIntegrationsParams => Task[Either[GeneralServiceException, FundSubLookupIntegrationsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubIntegPlatformEndpoints.lookupIntegrations.toSyncEndpoint)

  val getLinkableInstances
    : FundSubLinkableInstancesParams => Task[Either[GeneralServiceException, FundSubLinkableInstancesResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubIntegPlatformEndpoints.getLinkableInstances.toSyncEndpoint)

  val linkAppConnector: FundSubLinkAppConnectorParams => Task[Either[GeneralServiceException, EmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubIntegPlatformEndpoints.linkAppConnector.toSyncEndpoint)

  val unlinkAppConnector: FundSubUnlinkAppConnectorParams => Task[Either[GeneralServiceException, EmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundSubIntegPlatformEndpoints.unlinkAppConnector.toSyncEndpoint)

}
