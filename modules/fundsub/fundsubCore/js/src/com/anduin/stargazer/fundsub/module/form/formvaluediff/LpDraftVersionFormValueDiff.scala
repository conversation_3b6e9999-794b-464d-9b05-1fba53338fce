// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.form.formvaluediff

import java.time.format.{DateTimeFormatter, FormatStyle}
import java.time.{Instant, ZoneId}

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.dropdown.laminar.DropdownL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import zio.ZIO

import anduin.forms.engine.{GaiaEngine, GaiaState}
import anduin.forms.{FormData, FormStates}
import anduin.frontend.AirStreamUtils
import anduin.fundsub.dataextract.GetDataExtractRequestFormInfoResp.ViewOnlyModeData
import anduin.fundsub.endpoint.subscriptiondoc.{
  GetSubscriptionVersionFormInfoParams,
  SubscriptionVersionBasicInfo,
  SubscriptionVersionFormInfo
}
import anduin.id.fundsub.FundSubLpId
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

final case class LpDraftVersionFormValueDiff(
  lpId: FundSubLpId,
  lpVersions: Seq[SubscriptionVersionBasicInfo],
  engine: GaiaEngine,
  draftFormData: FormData,
  draftInitialGaiaState: GaiaState,
  draftLastUpdatedAtSignal: Signal[Option[Instant]],
  backObserver: Observer[Unit],
  draftGaiaStateObserver: Observer[GaiaState],
  viewOnlyModeDataOptSignal: Signal[Option[ViewOnlyModeData]]
) {
  private val latestVersionIndexOpt = lpVersions.map(_.versionIndex).maxOption
  private val selectedVersionOptVar = Var(lpVersions.headOption)
  private val selectedVersionOptSignal = selectedVersionOptVar.signal
  private val compareEventBus = new EventBus[Unit]
  private val versionFormInfoOptVar = Var(Option.empty[(SubscriptionVersionBasicInfo, SubscriptionVersionFormInfo)])
  private val versionFormInfoOptSignal = versionFormInfoOptVar.signal
  private val isFetchingVersionFormInfoVar = Var(false)
  private val isFetchingVersionFormInfoSignal = isFetchingVersionFormInfoVar.signal.distinct

  private val draftFormStates = FormStates(
    dataObserver = draftGaiaStateObserver,
    errorObserver = Observer.empty,
    engine = engine,
    initialData = draftInitialGaiaState
  )

  def apply(): HtmlElement = {
    div(
      tw.hPc100.flex.flexCol,
      header,
      child.maybe <-- viewOnlyModeDataOptSignal.map(_.map(renderViewOnlyModeCallout)),
      diffView,
      compareEventBus.events
        .sample(selectedVersionOptSignal)
        .collectSome
        .flatMapSwitch(fetchVersionFormInfo) --> Observer.empty,
      onMountCallback { _ => compareEventBus.emit(()) }
    )
  }

  private def header = {
    div(
      tw.flex.itemsCenter.justifyBetween.px16.py12,
      div(
        tw.wPx48,
        ButtonL(
          style = ButtonL.Style.Full(icon = Some(Icon.Glyph.ArrowLeft)),
          onClick = backObserver.contramap { _ => () }
        )()
      ),
      div(
        tw.flex.itemsCenter.spaceX8,
        div(
          tw.flex.itemsCenter.spaceX16,
          div(tw.heading2, "Comparing"),
          div(
            width.px := 280,
            DropdownL[SubscriptionVersionBasicInfo](
              value = selectedVersionOptSignal,
              valueToString = _.versionIndex.toString,
              onChange = selectedVersionOptVar.writer.contramapSome,
              items = lpVersions.map(DropdownL.Item(_)),
              target = DropdownL.Target(
                placeholder = "Select a version",
                appearance = DropdownL.Appearance.Full(isFullWidth = true),
                renderValue = Some { version => s"Version ${version.versionIndex}" }
              ),
              content = DropdownL.Content[SubscriptionVersionBasicInfo](renderItemBody = Some { item =>
                val versionIndex = item.value.versionIndex
                val latestStr = if (latestVersionIndexOpt.contains(versionIndex)) {
                  " (Latest)"
                } else {
                  ""
                }
                val dateStr = item.value.lastModified.fold("-")(
                  DateTimeFormatter.ofLocalizedDateTime(FormatStyle.MEDIUM).withZone(ZoneId.systemDefault()).format
                )
                div(
                  tw.spaceY4,
                  div(tw.fontSemiBold, s"Version $versionIndex$latestStr"),
                  div(tw.textSmall.textGray7, dateStr)
                )
              }),
              minWidth = 280
            )()
          ),
          div(tw.heading2, "and extracted data")
        ),
        ButtonL(
          style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
          onClick = compareEventBus.writer.contramap { _ => () }
        )("Compare")
      ),
      div(tw.wPx48)
    )

  }

  private def renderViewOnlyModeCallout(viewOnlyModeData: ViewOnlyModeData): HtmlElement = {
    div(
      tw.flex.itemsCenter.justifyCenter.spaceX8.py8.bgWarning3,
      IconL(name = Val(Icon.Glyph.Info))(),
      div(
        tw.flex.itemsCenter,
        "Extracted data is being reviewed by",
        span(tw.fontSemiBold.mx4, viewOnlyModeData.userName),
        s"(${viewOnlyModeData.userEmail}). Editing will be available when ",
        span(tw.fontSemiBold.mx4, viewOnlyModeData.userName),
        " exits the review view."
      )
    )
  }

  private def diffView = {
    div(
      tw.flexFill,
      child <-- isFetchingVersionFormInfoSignal.map(if (_) {
        BlockIndicatorL(isFullHeight = true)()
      } else {
        div(
          tw.hPc100,
          child.maybe <-- versionFormInfoOptSignal.combineWith(viewOnlyModeDataOptSignal).map {
            case (versionOpt, viewOnlyModeDataOpt) =>
              versionOpt.map { case version -> versionFormInfo =>
                EditableFormValueDiffView(
                  versionInfo = version,
                  versionFormInfo = versionFormInfo,
                  draftFormData = draftFormData,
                  draftGaiaStateSignal = draftFormStates.stateSignal,
                  draftLastUpdatedAtSignal = draftLastUpdatedAtSignal,
                  draftEventObserver = draftFormStates.eventObserver,
                  isViewOnly = viewOnlyModeDataOpt.nonEmpty
                )()
              }
          }
        )
      })
    )
  }

  private def fetchVersionFormInfo(version: SubscriptionVersionBasicInfo) = {
    AirStreamUtils.taskToStream {
      val task = for {
        _ <- ZIO.attempt(isFetchingVersionFormInfoVar.set(true))
        respEither <- FundSubEndpointClient.getSubscriptionVersionFormInfo(
          GetSubscriptionVersionFormInfoParams(lpId, version.versionIndex)
        )
        resp <- ZIO.fromEither(respEither)
        formInfo <- ZIOUtils.fromOption(resp.version, RuntimeException("No version form info"))
        _ <- ZIO.attempt {
          versionFormInfoOptVar.set(Some(version -> formInfo))
          isFetchingVersionFormInfoVar.set(false)
        }
      } yield ()
      task.catchAll { error =>
        ZIO.attempt {
          Toast.error(s"Failed to fetch version form info. Error: ${error.getMessage}")
          isFetchingVersionFormInfoVar.set(false)
        }
      }
    }
  }

}
