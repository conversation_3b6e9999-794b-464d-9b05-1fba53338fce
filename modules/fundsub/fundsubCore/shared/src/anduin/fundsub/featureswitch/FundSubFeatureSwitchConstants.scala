// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.featureswitch

object FundSubFeatureSwitchConstants {

  val enableLpUndoSubmission = "Enable investor undo submission"
  val enableLpSupportingDoc = "Enable investor supporting doc section"
  val enabledAddAdditionalTaxForms = "Enable add Supporting Form button"
  val formCommentSwitch = "Enable form commenting"
  val enableSupportingDocReview = "Enable AML/KYC and other documents review"

  val disableInvestorTaxFormElectronicSignature =
    "Disable investor's electronic signature on tax forms (LP Flexible flow only)"

  val enableDisclaimerESignatureView = "Enable disclaimer in e-signature view"
  val enableFinishSigning2FA = "Require 2FA to finish signing"
  val enableElectronicSeal = "Include an electronic seal"

  val enableSubmissionInstruction = "Enable submission instruction"
  val enableInvestFromAdditionalEntity = "Enable investment from an additional entity"
  val enableDownloadSubscriptionDocument = "Enable downloading subscription documents"
  val enableLPMarkAsNotApplicable = "Enable LP Mark a supporting doc as Not Applicable"
  val showFormFilesAndSigningButtons = "Show signing buttons group"
  val showManualOrdersToInvestors = "Show offline subscriptions to investors"
  val showOrdersViaLinkToInvitedLpsOnly = "Show for invited users only"
  val enableFormDiff = "Enable form diff"
  val enableLpManualSubmitSubscription = "Enable LP manually submit subscription documents"
  val enableLpReusePreviousSignedVersion = "Enable LP reuse the previous signed version"
  val exportInvestorDashboardTagsAsColumns = "Export investor dashboard tags as columns"
  val enableAutoPrefill = "Enable GP autofill from past subscriptions"
  val enableImportData = "Enable data import"
  val enableFundPermission = "Enable fund permission"
  val enableAutoPrefillForLp = "Enable LP Autofill from past subscriptions only"
  val enableOntologyFormMatching = "Enable ASA ontology usage in form matching (IA, autofill from past subs)"
  val useNewGaiaImportOrder = "Autofill with new Gaia import order (rule dependencies vs. just appearance order)"

  val blockLpSubmissionUntilFinishSupporting =
    "Only allow LP submission after both sub docs & AML/KYC docs were completed"

  val enableEnforceLpSubmitReviewBeforeSignature = "Require LPs to submit subscription for review before they can sign"

  val enableLpProfile = "Enable Investor Access & LP Autofill from past subscriptions"

  val allowMultiStepInSubscriptionReview = "Allow subscription doc multi-step review"

  val enableAmlKycCommenting = "Enable AML/KYC commenting"

  val enableAmlKycListAfterRequiredFieldsCompleted =
    "Display supporting document list after all required fields are completed"

  val enableCommentMentioning = "Enable comment mentioning"

  val enableCommentAssignment = "Enable comment assignment"

  val enableAbilityOfInvestorToInviteCollaborator = "Enable ability of investor to invite collaborator"

  val showOrderMetadataInDetailView = "Show order metadata in detail view (Public API integrated fund)"

  val enableImportFromFundData = "Enable import investors from Investor Data Management"

  val enableAbilityToCounterSign = "Enable ability to countersign"

  val enableSelfServiceExport = "Enable self-service export template"

  val enableInAppAnnouncement = "Enable in-app announcement"

  val hideAllInvitationButtonOnGpSide = "Hide all invitation buttons on GP side"

  val enableRia = "Enable Advisor Workflow"

  val enableOneEnvelopeExperience = "Enable one envelope signing experience"

  val enableSideLetter = "Enable Side Letter"

  val forcingFundManagersToReceiveAllEmailNotifications = "Forcing fund managers to receive all email notifications"

  val enableDataExtractDraftFormLog = "Enable data extract draft form log (Feature in Development)"

  val showSwitchAllowFormEditPostSigning = """Enable "Allow form edit post signing" (in development)"""
}
