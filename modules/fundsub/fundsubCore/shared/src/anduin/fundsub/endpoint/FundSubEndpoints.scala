// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.endpoint

import sttp.tapir.*

import anduin.asyncapiv2.execution.AsyncApiWorkflowQueue
import anduin.contact.endpoint.*
import anduin.email.CustomSmtpServerConfigParams
import anduin.fundsub.endpoint.admin.*
import anduin.fundsub.endpoint.amlkyc.amlcheck.GetFundSubAmlCheckResponse
import anduin.fundsub.endpoint.auditlog.*
import anduin.fundsub.endpoint.common.{GetUserTrackingResponse, UpdateUserTrackingParams}
import anduin.fundsub.endpoint.contact.{
  GetContactsWithSameEmailForGroupParams,
  ListContactsByEmailOrderParams,
  QueryContactsByEmailParams,
  QueryContactsByNameParams,
  *
}
import anduin.fundsub.endpoint.copy.{GetCopyConfigForClientParams, GetCopyConfigForClientResponse}
import anduin.fundsub.endpoint.dashboard.*
import anduin.fundsub.endpoint.dataexport.*
import anduin.fundsub.endpoint.datagenerator.GenerateInvestorParams
import anduin.fundsub.endpoint.dataimport.*
import anduin.fundsub.endpoint.emaillog.{GetEmailLogDataParams, GetEmailLogDataResponse}
import anduin.fundsub.endpoint.formcomment.*
import anduin.fundsub.endpoint.fundclose.*
import anduin.fundsub.endpoint.lp.*
import anduin.fundsub.endpoint.multiregion.GetFundSubRegionInfosResponse
import anduin.fundsub.endpoint.operation.{ExportFundCommentParams, SendDemoFundActivityEmailParams}
import anduin.fundsub.endpoint.participant.{FundSubBatchInvitationInfo, *}
import anduin.fundsub.endpoint.reusesignature.UpdateAllowFormEditPostSigningParams
import anduin.fundsub.endpoint.reviewpackage.*
import anduin.fundsub.endpoint.signature.*
import anduin.fundsub.endpoint.signature.batch.*
import anduin.fundsub.endpoint.status.{GetLpStatusActivityHistoryParams, LpStatusHistory}
import anduin.fundsub.endpoint.subscriptiondoc.*
import anduin.fundsub.endpoint.supportingdoc.*
import anduin.fundsub.endpoint.whitelabel.*
import anduin.fundsub.featureswitch.*
import anduin.id.fundsub.*
import anduin.id.issuetracker.IssueId
import anduin.model.codec.EitherCodec.given
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.email.InternalEmailId
import anduin.model.id.{FileId, TemporalWorkflowId}
import anduin.protobuf.fundsub.onboarding.ModuleOnboardingMessage
import anduin.protobuf.fundsub.usertracking.FundSubUserTrackingModel
import anduin.service.{CommonResponse, GeneralServiceException, GeneralServiceResponse}
import anduin.stargazer.service.formcomment.FormCommentCommons.*
import anduin.tapir.AsyncEndpoint.AsyncAuthenticatedEndpoint
import anduin.tapir.AuthenticatedEndpoints.BaseAuthenticatedEndpoint
import anduin.tapir.endpoint.{CommonParams, EmptyResponse}
import anduin.tapir.{AsyncEndpoint, AuthenticatedEndpoints}

object FundSubEndpoints extends AuthenticatedEndpoints with AsyncEndpoint {

  private lazy val FundSubPath = "fundsub"

  // Admins
  private lazy val FundSubAdminPath = FundSubPath / "admin"

  @scala.annotation.unused
  private lazy val FundSubAdminBatchInvitationPath = FundSubAdminPath / "batchInvitation"

  lazy val updateFundName: BaseAuthenticatedEndpoint[UpdateFundNameParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateFundNameParams, GeneralServiceException, Unit](
      FundSubAdminPath / "updateFundName"
    )
  }

  lazy val updateMarkAsNotApplicableSetting
    : BaseAuthenticatedEndpoint[UpdateMarkAsNotApplicableParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateMarkAsNotApplicableParams, GeneralServiceException, Unit](
      FundSubAdminPath / "updateUpdateMarkAsNotApplicableSetting"
    )
  }

  lazy val updateDisableFundContactInInvestorWorkspaceSetting: BaseAuthenticatedEndpoint[
    UpdateDisableFundContactInInvestorWorkspaceSettingParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UpdateDisableFundContactInInvestorWorkspaceSettingParams,
      GeneralServiceException,
      Unit
    ](
      FundSubAdminPath / "updateDisableFundContactInInvestorWorkspaceSetting"
    )
  }

  lazy val updateInvestFromAdditionalEntitySetting: BaseAuthenticatedEndpoint[
    UpdateInvestingFromAdditionalEntitySettingParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UpdateInvestingFromAdditionalEntitySettingParams,
      GeneralServiceException,
      Unit
    ](
      FundSubAdminPath / "updateInvestFromAdditionalEntitySetting"
    )
  }

  lazy val updateCustomLpIdSetting
    : BaseAuthenticatedEndpoint[UpdateCustomLpIdSettingParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateCustomLpIdSettingParams, GeneralServiceException, Unit](
      FundSubAdminPath / "updateCustomLpIdSetting"
    )
  }

  lazy val updateDownloadSubscriptionDocumentSetting: BaseAuthenticatedEndpoint[
    UpdateDownloadSubscriptionDocumentSettingParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UpdateDownloadSubscriptionDocumentSettingParams,
      GeneralServiceException,
      Unit
    ](
      FundSubAdminPath / "updateDownloadSubscriptionDocumentSetting"
    )
  }

  lazy val updateRiaBannerVisibility: BaseAuthenticatedEndpoint[
    UpdateRiaBannerVisibilityParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UpdateRiaBannerVisibilityParams,
      GeneralServiceException,
      Unit
    ](
      FundSubAdminPath / "updateRiaBannerVisibility"
    )
  }

  lazy val updateInactiveLpSetting
    : BaseAuthenticatedEndpoint[UpdateInactiveLpSettingParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateInactiveLpSettingParams, GeneralServiceException, Unit](
      FundSubAdminPath / "updateInactiveLpSetting"
    )
  }

  lazy val updateFormCommentFundSettingSwitch: BaseAuthenticatedEndpoint[
    UpdateFormCommentFundSettingSwitchParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UpdateFormCommentFundSettingSwitchParams,
      GeneralServiceException,
      Unit
    ](
      FundSubAdminPath / "updateFormCommentFundSettingSwitch"
    )
  }

  lazy val updateFormCommentInvestorDigestEmailSwitch: BaseAuthenticatedEndpoint[
    UpdateFormCommentInvestorDigestEmailSwitchParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UpdateFormCommentInvestorDigestEmailSwitchParams,
      GeneralServiceException,
      Unit
    ](
      FundSubAdminPath / "updateFormCommentInvestorDigestEmailSwitch"
    )
  }

  lazy val updateFormCommentInvestorCanResolveSwitch: BaseAuthenticatedEndpoint[
    UpdateFormCommentInvestorCanResolveSwitchParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UpdateFormCommentInvestorCanResolveSwitchParams,
      GeneralServiceException,
      Unit
    ](
      FundSubAdminPath / "updateFormCommentInvestorCanResolveSwitch"
    )
  }

  lazy val updateInactiveCommentSetting: BaseAuthenticatedEndpoint[
    UpdateInactiveCommentSettingParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UpdateInactiveCommentSettingParams,
      GeneralServiceException,
      Unit
    ](
      FundSubAdminPath / "updateInactiveCommentSetting"
    )
  }

  lazy val updateFormCommentDigestEmailExceptionLps: BaseAuthenticatedEndpoint[
    UpdateFormCommentDigestEmailExceptionLpsParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UpdateFormCommentDigestEmailExceptionLpsParams,
      GeneralServiceException,
      Unit
    ](
      FundSubAdminPath / "updateFormCommentDigestEmailExceptionLps"
    )
  }

  lazy val getFormCommentDigestEmailExceptionInfo: BaseAuthenticatedEndpoint[
    GetFormCommentDigestEmailExceptionInfoParams,
    GeneralServiceException,
    GetFormCommentDigestEmailExceptionInfoResponse
  ] = {
    authEndpoint[
      GetFormCommentDigestEmailExceptionInfoParams,
      GeneralServiceException,
      GetFormCommentDigestEmailExceptionInfoResponse
    ](
      FundSubAdminPath / "getFormCommentDigestEmailExceptionInfo"
    )
  }

  lazy val updateCustomFundIdSetting
    : BaseAuthenticatedEndpoint[UpdateCustomFundIdSettingParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateCustomFundIdSettingParams, GeneralServiceException, Unit](
      FundSubAdminPath / "updateCustomFundIdSetting"
    )
  }

  lazy val sendNewInvestorReportEmail
    : BaseAuthenticatedEndpoint[SendNewInvestorsReportDemoEmailParams, GeneralServiceException, Unit] = {
    authEndpoint[SendNewInvestorsReportDemoEmailParams, GeneralServiceException, Unit](
      FundSubAdminPath / "sendNewInvestorReportEmail"
    )
  }

  lazy val lpSelfRemindToSignAgain: BaseAuthenticatedEndpoint[FundSubLpId, GeneralServiceException, Unit] = {
    authEndpoint[FundSubLpId, GeneralServiceException, Unit](
      FundSubAdminPath / "lpSelfRemindToSignAgain"
    )
  }

  lazy val sendDemoFormCommentsDigestEmail: BaseAuthenticatedEndpoint[FundSubId, GeneralServiceException, Unit] = {
    authEndpoint[FundSubId, GeneralServiceException, Unit](
      FundSubAdminPath / "sendDemoFormCommentsDigestEmail"
    )
  }

  lazy val sendDemoFundActivitiesDigestEmail
    : BaseAuthenticatedEndpoint[SendDemoFundActivityEmailParams, GeneralServiceException, Unit] = {
    authEndpoint[SendDemoFundActivityEmailParams, GeneralServiceException, Unit](
      FundSubAdminPath / "sendDemoFundActivitiesDigestEmail"
    )
  }

  lazy val uploadRefDoc: BaseAuthenticatedEndpoint[UploadDocumentsParams, GeneralServiceException, Unit] = {
    authEndpoint[UploadDocumentsParams, GeneralServiceException, Unit](
      FundSubAdminPath / "uploadRefDoc"
    )
  }

  lazy val renameRefDoc: BaseAuthenticatedEndpoint[RenameDocumentsParams, GeneralServiceException, Unit] = {
    authEndpoint[RenameDocumentsParams, GeneralServiceException, Unit](
      FundSubAdminPath / "renameRefDoc"
    )
  }

  lazy val getFundSharedRefDocs: BaseAuthenticatedEndpoint[
    GetFundSharedReferenceDocsParams,
    GeneralServiceException,
    GetFundSharedReferenceDocsResp
  ] = authEndpoint[
    GetFundSharedReferenceDocsParams,
    GeneralServiceException,
    GetFundSharedReferenceDocsResp
  ](
    FundSubAdminPath / "getFundSharedRefDocs"
  )

  lazy val removeRefDoc: BaseAuthenticatedEndpoint[RemoveDocumentsParams, GeneralServiceException, Unit] = {
    authEndpoint[RemoveDocumentsParams, GeneralServiceException, Unit](
      FundSubAdminPath / "removeRefDoc"
    )
  }

  lazy val saveSubscriptionDocsOrder
    : BaseAuthenticatedEndpoint[SaveSubscriptionDocsOrderParams, GeneralServiceException, Unit] = {
    authEndpoint[SaveSubscriptionDocsOrderParams, GeneralServiceException, Unit](
      FundSubAdminPath / "saveSubscriptionDocsOrder"
    )
  }

  lazy val requestCountersignSignature: BaseAuthenticatedEndpoint[
    RequestCountersignSignatureParams,
    GeneralServiceException,
    RequestCountersignSignatureResp
  ] = {
    authEndpoint[
      RequestCountersignSignatureParams,
      GeneralServiceException,
      RequestCountersignSignatureResp
    ](
      FundSubAdminPath / "requestCountersignSignature"
    )
  }

  lazy val remindSigningRequestRestrictedFlow
    : BaseAuthenticatedEndpoint[RemindSigningRequestParams, GeneralServiceException, Unit] = {
    authEndpoint[RemindSigningRequestParams, GeneralServiceException, Unit](
      FundSubAdminPath / "remindSigningRequestRestrictedFlow"
    )
  }

  lazy val signCountersignRequest
    : BaseAuthenticatedEndpoint[SignCountersignRequestParams, GeneralServiceException, Unit] = {
    authEndpoint[SignCountersignRequestParams, GeneralServiceException, Unit](
      FundSubAdminPath / "signCountersignRequest"
    )
  }

  lazy val getLpUnsignedDoc: BaseAuthenticatedEndpoint[
    GetLpUnsignedDocParams,
    GeneralServiceException,
    GetLpUnsignedDocResponse
  ] = {
    authEndpoint[
      GetLpUnsignedDocParams,
      GeneralServiceException,
      GetLpUnsignedDocResponse
    ](
      FundSubAdminPath / "getLpUnsignedDoc"
    )
  }

  lazy val batchSignCountersignRequest: BaseAuthenticatedEndpoint[
    BatchSignCountersignRequestParams,
    GeneralServiceException,
    BatchSignCountersignRequestResp
  ] = {
    authEndpoint[
      BatchSignCountersignRequestParams,
      GeneralServiceException,
      BatchSignCountersignRequestResp
    ](
      FundSubAdminPath / "batchSignCountersignRequest"
    )
  }

  lazy val getLpFormAndValue: BaseAuthenticatedEndpoint[
    GetLpFormAndValueParams,
    GeneralServiceException,
    GetLpFormAndValueResponse
  ] = {
    authEndpoint[
      GetLpFormAndValueParams,
      GeneralServiceException,
      GetLpFormAndValueResponse
    ](
      FundSubAdminPath / "getLpFormAndValue"
    )
  }

  lazy val exportInvestorDashboardData: AsyncAuthenticatedEndpoint[
    ExportInvestorDashboardDataParams,
    GeneralServiceException,
    ExportInvestorDashboardDataResp
  ] = {
    asyncEndpoint[
      ExportInvestorDashboardDataParams,
      GeneralServiceException,
      ExportInvestorDashboardDataResp
    ](
      FundSubAdminPath / "exportInvestorDashboardData",
      AsyncApiWorkflowQueue.Heavy
    )
  }

  lazy val getLpsBasicInfo
    : BaseAuthenticatedEndpoint[GetLpsBasicInfoParams, GeneralServiceException, Seq[LpBasicInfo]] = {
    authEndpoint[GetLpsBasicInfoParams, GeneralServiceException, Seq[LpBasicInfo]](
      FundSubAdminPath / "getLpsBasicInfo"
    )
  }

  lazy val batchUpdateInvestorDataWithWorkflow
    : BaseAuthenticatedEndpoint[BatchUpdateInvestorDataParams, GeneralServiceException, Unit] = {
    authEndpoint[BatchUpdateInvestorDataParams, GeneralServiceException, Unit](
      FundSubAdminPath / "batchUpdateInvestorDataWithWorkflow"
    )
  }

  lazy val getLpDocuments
    : BaseAuthenticatedEndpoint[GetLpDocumentParams, GeneralServiceException, GetLpDocumentResponse] = {
    authEndpoint[GetLpDocumentParams, GeneralServiceException, GetLpDocumentResponse](
      FundSubAdminPath / "getLpDocuments"
    )
  }

  lazy val downloadLpDocuments: BaseAuthenticatedEndpoint[
    DownloadLpDocumentsParams,
    GeneralServiceException,
    DownloadLpDocumentsResponse
  ] = {
    authEndpoint[
      DownloadLpDocumentsParams,
      GeneralServiceException,
      DownloadLpDocumentsResponse
    ](
      FundSubAdminPath / "downloadLpDocuments"
    )
  }

  lazy val queryLpDashboard: BaseAuthenticatedEndpoint[
    LpDashboardQueryParams,
    GeneralServiceException,
    LpDashboardQueryResponse
  ] = {
    authEndpoint[
      LpDashboardQueryParams,
      GeneralServiceException,
      LpDashboardQueryResponse
    ](
      FundSubAdminPath / "queryLpDashboard"
    )
  }

  lazy val markLpAsNotNew: BaseAuthenticatedEndpoint[MarkLpAsNotNewParams, GeneralServiceException, Unit] = {
    authEndpoint[MarkLpAsNotNewParams, GeneralServiceException, Unit](
      FundSubAdminPath / "markLpAsNotNew"
    )
  }

  lazy val getLpDashboardItem: BaseAuthenticatedEndpoint[
    GetLpDashboardItemParams,
    GeneralServiceException,
    GetLpDashboardItemResponse
  ] =
    authEndpoint[
      GetLpDashboardItemParams,
      GeneralServiceException,
      GetLpDashboardItemResponse
    ](
      FundSubAdminPath / "getLpDashboardItem"
    )

  lazy val getLpDashboardItemList: BaseAuthenticatedEndpoint[
    GetLpDashboardItemsParams,
    GeneralServiceException,
    GetLpDashboardItemsResponse
  ] =
    authEndpoint[
      GetLpDashboardItemsParams,
      GeneralServiceException,
      GetLpDashboardItemsResponse
    ](
      FundSubAdminPath / "getLpDashboardItemList"
    )

  lazy val adminRemoveLpCollaborator
    : BaseAuthenticatedEndpoint[AdminRemoveLpCollaboratorParams, GeneralServiceException, Unit] =
    authEndpoint[AdminRemoveLpCollaboratorParams, GeneralServiceException, Unit](
      FundSubAdminPath / "adminRemoveLpCollaborator"
    )

  lazy val accessFormCompare: BaseAuthenticatedEndpoint[AccessFormCompareParams, GeneralServiceException, Unit] =
    authEndpoint[AccessFormCompareParams, GeneralServiceException, Unit](FundSubAdminPath / "accessFormCompare")

  lazy val compareForm: BaseAuthenticatedEndpoint[CompareFormParams, GeneralServiceException, CompareFormResponse] =
    authEndpoint[CompareFormParams, GeneralServiceException, CompareFormResponse](FundSubAdminPath / "compareForm")

  lazy val getCompareResult: BaseAuthenticatedEndpoint[
    GetCompareResultParams,
    GeneralServiceException,
    GetCompareResultResponse
  ] =
    authEndpoint[
      GetCompareResultParams,
      GeneralServiceException,
      GetCompareResultResponse
    ](
      FundSubAdminPath / "getCompareResult"
    )

  lazy val checkLpFormValidation: BaseAuthenticatedEndpoint[
    CheckLpFormValidationParams,
    GeneralServiceException,
    CheckLpFormValidationResponse
  ] =
    authEndpoint[
      CheckLpFormValidationParams,
      GeneralServiceException,
      CheckLpFormValidationResponse
    ](
      FundSubAdminPath / "checkLpFormValidation"
    )

  lazy val editFormVersionDescription: BaseAuthenticatedEndpoint[
    EditFormVersionDescriptionParams,
    GeneralServiceException,
    EditFormVersionDescriptionResponse
  ] =
    authEndpoint[
      EditFormVersionDescriptionParams,
      GeneralServiceException,
      EditFormVersionDescriptionResponse
    ](
      FundSubAdminPath / "editFormVersionDescription"
    )

  lazy val getTableOfContentLpProgress: BaseAuthenticatedEndpoint[
    GetTableOfContentLpProgressParams,
    GeneralServiceException,
    GetTableOfContentLpProgressResponse
  ] =
    authEndpoint[
      GetTableOfContentLpProgressParams,
      GeneralServiceException,
      GetTableOfContentLpProgressResponse
    ](
      FundSubAdminPath / "getTableOfContentLpProgress"
    )

  lazy val createDataRoomForIntegration: BaseAuthenticatedEndpoint[
    CreateDataRoomForIntegrationParams,
    GeneralServiceException,
    CreateDataRoomForIntegrationResponse
  ] =
    authEndpoint[
      CreateDataRoomForIntegrationParams,
      GeneralServiceException,
      CreateDataRoomForIntegrationResponse
    ](
      FundSubAdminPath / "createDataRoomForIntegration"
    )

  lazy val convertOfflineToNormalOrder: BaseAuthenticatedEndpoint[
    ConvertOfflineToNormalOrderParams,
    GeneralServiceException,
    ConvertOfflineToNormalOrderResponse
  ] =
    authEndpoint[
      ConvertOfflineToNormalOrderParams,
      GeneralServiceException,
      ConvertOfflineToNormalOrderResponse
    ](
      FundSubAdminPath / "convertOfflineToNormalOrder"
    )

  lazy val convertOfflineToNormalOrderWithWorkflow: BaseAuthenticatedEndpoint[
    ConvertOfflineToNormalOrderParams,
    GeneralServiceException,
    Unit
  ] =
    authEndpoint[ConvertOfflineToNormalOrderParams, GeneralServiceException, Unit](
      FundSubAdminPath / "convertOfflineToNormalOrderWithWorkflow"
    )

  lazy val approveLpSubscriptionDocument: BaseAuthenticatedEndpoint[FundSubLpId, GeneralServiceException, Unit] =
    authEndpoint[FundSubLpId, GeneralServiceException, Unit](
      FundSubAdminPath / "approveLpSubscriptionDocument"
    )

  // tag api
  lazy val createLpTag: BaseAuthenticatedEndpoint[CreateLpTagParams, GeneralServiceException, CreateLpTagResponse] =
    authEndpoint[CreateLpTagParams, GeneralServiceException, CreateLpTagResponse](
      FundSubAdminPath / "createLpTag"
    )

  lazy val createLpTags: BaseAuthenticatedEndpoint[CreateLpTagsParams, GeneralServiceException, CreateLpTagsResponse] =
    authEndpoint[CreateLpTagsParams, GeneralServiceException, CreateLpTagsResponse](
      FundSubAdminPath / "createLpTags"
    )

  lazy val getLpTagsInFund: BaseAuthenticatedEndpoint[
    GetAllLpTagsInFundParams,
    GeneralServiceException,
    GetAllLpTagsInFundResponse
  ] =
    authEndpoint[
      GetAllLpTagsInFundParams,
      GeneralServiceException,
      GetAllLpTagsInFundResponse
    ](
      FundSubAdminPath / "getLpTagsInFund"
    )

  lazy val editLpTag: BaseAuthenticatedEndpoint[EditLpTagParams, GeneralServiceException, EditLpTagResponse] =
    authEndpoint[EditLpTagParams, GeneralServiceException, EditLpTagResponse](
      FundSubAdminPath / "editLpTag"
    )

  lazy val removeLpTag: BaseAuthenticatedEndpoint[RemoveLpTagParams, GeneralServiceException, Unit] =
    authEndpoint[RemoveLpTagParams, GeneralServiceException, Unit](
      FundSubAdminPath / "removeLpTag"
    )

  lazy val updateTagsOfLp
    : BaseAuthenticatedEndpoint[UpdateTagsOfLpParams, GeneralServiceException, UpdateTagsOfLpResponse] =
    authEndpoint[UpdateTagsOfLpParams, GeneralServiceException, UpdateTagsOfLpResponse](
      FundSubAdminPath / "updateTagsOfLp"
    )

  lazy val getSingleLpActivityLog: BaseAuthenticatedEndpoint[
    GetSingleLpActivityLogParams,
    GeneralServiceException,
    GetSingleLpActivityLogResponse
  ] =
    authEndpoint[
      GetSingleLpActivityLogParams,
      GeneralServiceException,
      GetSingleLpActivityLogResponse
    ](
      FundSubAdminPath / "getSingleLpActivityLog"
    )

  lazy val getLpActivityLog: BaseAuthenticatedEndpoint[
    GetLpActivityLogParams,
    GeneralServiceException,
    GetLpActivityLogResponse
  ] =
    authEndpoint[
      GetLpActivityLogParams,
      GeneralServiceException,
      GetLpActivityLogResponse
    ](
      FundSubAdminPath / "getLpActivityLog"
    )

  lazy val getNumberOfUnseenActivities: BaseAuthenticatedEndpoint[
    GetNumberOfUnSeenActivityParams,
    GeneralServiceException,
    GetNumberOfUnSeenActivityResponse
  ] =
    authEndpoint[
      GetNumberOfUnSeenActivityParams,
      GeneralServiceException,
      GetNumberOfUnSeenActivityResponse
    ](
      FundSubAdminPath / "getNumberOfUnseenActivities"
    )

  lazy val markAsSeenLpActivities: BaseAuthenticatedEndpoint[
    MarkActivitiesAsSeenParams,
    GeneralServiceException,
    MarkActivitiesAsSeenResponse
  ] =
    authEndpoint[
      MarkActivitiesAsSeenParams,
      GeneralServiceException,
      MarkActivitiesAsSeenResponse
    ](
      FundSubAdminPath / "markAsSeenActivities"
    )

  lazy val getFaActivityLog: BaseAuthenticatedEndpoint[
    GetFundSubAdminActivityLogParams,
    GeneralServiceException,
    GetFundSubAdminActivityLogResponse
  ] =
    authEndpoint[
      GetFundSubAdminActivityLogParams,
      GeneralServiceException,
      GetFundSubAdminActivityLogResponse
    ](
      FundSubAdminPath / "getFaActivityLog"
    )

  lazy val markFaActivitiesAsSeen
    : BaseAuthenticatedEndpoint[MarkAsSeenFaActivityLogParams, GeneralServiceException, Unit] =
    authEndpoint[MarkAsSeenFaActivityLogParams, GeneralServiceException, Unit](
      FundSubAdminPath / "markFaActivitiesAsSeen"
    )

  lazy val getDataRoomIntegrationInfo: BaseAuthenticatedEndpoint[
    GetDataRoomIntegrationInfoParams,
    GeneralServiceException,
    GetDataRoomIntegrationInfoResponse
  ] =
    authEndpoint[
      GetDataRoomIntegrationInfoParams,
      GeneralServiceException,
      GetDataRoomIntegrationInfoResponse
    ](
      FundSubAdminPath / "getDataRoomIntegrationInfo"
    )

  lazy val getFormFilesInfo: BaseAuthenticatedEndpoint[
    GetFormFilesForFundAdminParams,
    GeneralServiceException,
    GetFormFilesForFundAdminResponse
  ] =
    authEndpoint[
      GetFormFilesForFundAdminParams,
      GeneralServiceException,
      GetFormFilesForFundAdminResponse
    ](
      FundSubAdminPath / "getFormFilesInfo"
    )

  lazy val getAdminTestFormData: BaseAuthenticatedEndpoint[
    GetAdminTestFormDataParams,
    GeneralServiceException,
    GetAdminTestFormDataResponse
  ] =
    authEndpoint[
      GetAdminTestFormDataParams,
      GeneralServiceException,
      GetAdminTestFormDataResponse
    ](
      FundSubAdminPath / "getAdminTestFormData"
    )

  lazy val saveAdminTestFormData
    : BaseAuthenticatedEndpoint[SaveAdminTestFormDataParams, GeneralServiceException, Unit] =
    authEndpoint[SaveAdminTestFormDataParams, GeneralServiceException, Unit](
      FundSubAdminPath / "saveAdminTestFormData"
    )

  lazy val resendBounceInvitation
    : BaseAuthenticatedEndpoint[ResendBounceInvitationParams, GeneralServiceException, Unit] =
    authEndpoint[ResendBounceInvitationParams, GeneralServiceException, Unit](
      FundSubAdminPath / "resendBounceInvitation"
    )

  lazy val createProtectedLink: BaseAuthenticatedEndpoint[
    CreateProtectedLinkParams,
    GeneralServiceException,
    CreateProtectedLinkResponse
  ] =
    authEndpoint[
      CreateProtectedLinkParams,
      GeneralServiceException,
      CreateProtectedLinkResponse
    ](
      FundSubAdminPath / "createProtectedLink"
    )

  lazy val editProtectedLink: BaseAuthenticatedEndpoint[
    EditProtectedLinkParams,
    GeneralServiceException,
    EditProtectedLinkResponse
  ] =
    authEndpoint[
      EditProtectedLinkParams,
      GeneralServiceException,
      EditProtectedLinkResponse
    ](
      FundSubAdminPath / "editProtectedLink"
    )

  lazy val getFundSubEnvironmentStatus: BaseAuthenticatedEndpoint[
    GetFundSubEnvironmentStatusParams,
    GeneralServiceException,
    GetFundSubEnvironmentStatusResponse
  ] =
    authEndpoint[
      GetFundSubEnvironmentStatusParams,
      GeneralServiceException,
      GetFundSubEnvironmentStatusResponse
    ](
      FundSubAdminPath / "getFundSubEnvironmentStatus"
    )

  lazy val getFundAdminNotificationPreference: BaseAuthenticatedEndpoint[
    GetFundSubAdminNotificationPreferenceParams,
    GeneralServiceException,
    GetFundSubAdminNotificationPreferenceResponse
  ] =
    authEndpoint[
      GetFundSubAdminNotificationPreferenceParams,
      GeneralServiceException,
      GetFundSubAdminNotificationPreferenceResponse
    ](
      FundSubAdminPath / "getFundAdminNotificationPreference"
    )

  lazy val updateFundAdminNotificationPreference: BaseAuthenticatedEndpoint[
    UpdateFundSubAdminNotificationPreferenceParams,
    GeneralServiceException,
    Unit
  ] =
    authEndpoint[
      UpdateFundSubAdminNotificationPreferenceParams,
      GeneralServiceException,
      Unit
    ](
      FundSubAdminPath / "updateFundAdminNotificationPreference"
    )

  lazy val getEmailTemplates: BaseAuthenticatedEndpoint[
    GetEmailTemplateParams,
    GeneralServiceException,
    GetEmailTemplateResponse
  ] = {
    authEndpoint[
      GetEmailTemplateParams,
      GeneralServiceException,
      GetEmailTemplateResponse
    ](
      FundSubAdminPath / "getEmailTemplates"
    )
  }

  lazy val updateEmailTemplates
    : BaseAuthenticatedEndpoint[UpdateEmailTemplatesParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateEmailTemplatesParams, GeneralServiceException, Unit](
      FundSubAdminPath / "updateEmailTemplateBodyMessage"
    )
  }

  lazy val requestSupportingDocSignature: BaseAuthenticatedEndpoint[
    RequestSupportingDocSignatureParams,
    GeneralServiceException,
    RequestSupportingDocSignatureResponse
  ] = {
    authEndpoint[
      RequestSupportingDocSignatureParams,
      GeneralServiceException,
      RequestSupportingDocSignatureResponse
    ](
      FundSubAdminPath / "requestSupportingDocSignature"
    )
  }

  lazy val signAdditionalSignatureRequest
    : BaseAuthenticatedEndpoint[SignAdditionalSignatureRequestParams, GeneralServiceException, Unit] = {
    authEndpoint[SignAdditionalSignatureRequestParams, GeneralServiceException, Unit](
      FundSubAdminPath / "signAdditionalSignatureRequest"
    )
  }

  lazy val getLpParticipantInfos: BaseAuthenticatedEndpoint[
    GetLpParticipantInfosParams,
    GeneralServiceException,
    GetLpParticipantInfosResponse
  ] = {
    authEndpoint[
      GetLpParticipantInfosParams,
      GeneralServiceException,
      GetLpParticipantInfosResponse
    ](
      FundSubAdminPath / "getLpParticipantInfos"
    )
  }

  lazy val fundAdminSendCustomEmail: BaseAuthenticatedEndpoint[
    FundAdminSendCustomEmailParams,
    GeneralServiceException,
    GeneralServiceResponse
  ] = {
    authEndpoint[
      FundAdminSendCustomEmailParams,
      GeneralServiceException,
      GeneralServiceResponse
    ](
      FundSubAdminPath / "fundAdminSendCustomEmail"
    )
  }

  lazy val updateLpCommitment: BaseAuthenticatedEndpoint[UpdateLpCommitmentParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateLpCommitmentParams, GeneralServiceException, Unit](
      FundSubAdminPath / "updateLpCommitment"
    )
  }

  lazy val verifyInvestmentEntityIsFromFormData
    : BaseAuthenticatedEndpoint[FundSubLpId, GeneralServiceException, Boolean] = {
    authEndpoint[FundSubLpId, GeneralServiceException, Boolean](
      FundSubAdminPath / "verifyInvestmentEntityIsFromFormData"
    )
  }

  lazy val checkSeenAutoSelectedCommentsBanner: BaseAuthenticatedEndpoint[Unit, GeneralServiceException, Boolean] = {
    authEndpoint[Unit, GeneralServiceException, Boolean](
      FundSubAdminPath / "checkSeenAutoSelectedCommentsBanner"
    )
  }

  lazy val markSeenAutoSelectedCommentsBanner: BaseAuthenticatedEndpoint[Unit, GeneralServiceException, Unit] = {
    authEndpoint[Unit, GeneralServiceException, Unit](
      FundSubAdminPath / "markSeenAutoSelectedCommentsBanner"
    )
  }

  lazy val getRecentDashboardId
    : BaseAuthenticatedEndpoint[FundSubId, GeneralServiceException, Option[FundSubDashboardId]] = {
    authEndpoint[FundSubId, GeneralServiceException, Option[FundSubDashboardId]](
      FundSubAdminPath / "getRecentDashboardId"
    )
  }

  lazy val setRecentDashboardId: BaseAuthenticatedEndpoint[SetRecentDashboardParams, GeneralServiceException, Unit] = {
    authEndpoint[SetRecentDashboardParams, GeneralServiceException, Unit](
      FundSubAdminPath / "setRecentDashboardId"
    )
  }

  lazy val sendNotifyNewCommentsToInvestor: BaseAuthenticatedEndpoint[
    FundAdminSendNotifyNewCommentsToInvestorParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      FundAdminSendNotifyNewCommentsToInvestorParams,
      GeneralServiceException,
      Unit
    ](
      FundSubAdminPath / "sendNotifyNewCommentsToInvestor"
    )
  }

  lazy val getLpStatusActivityHistory: BaseAuthenticatedEndpoint[
    GetLpStatusActivityHistoryParams,
    GeneralServiceException,
    LpStatusHistory
  ] = {
    authEndpoint[
      GetLpStatusActivityHistoryParams,
      GeneralServiceException,
      LpStatusHistory
    ](
      FundSubAdminPath / "getLpStatusActivityHistory"
    )
  }

  // audit log
  lazy val getAuditLogData: BaseAuthenticatedEndpoint[
    GetAuditLogDataParams,
    GeneralServiceException,
    GetAuditLogDataResponse
  ] = {
    authEndpoint[
      GetAuditLogDataParams,
      GeneralServiceException,
      GetAuditLogDataResponse
    ](
      FundSubAdminPath / "getAuditLogData"
    )
  }

  lazy val getEmailBodyContentString
    : BaseAuthenticatedEndpoint[InternalEmailId, GeneralServiceException, Option[String]] = {
    authEndpoint[InternalEmailId, GeneralServiceException, Option[String]](
      FundSubAdminPath / "getEmailBodyContentString"
    )
  }

  lazy val exportAuditLogData: BaseAuthenticatedEndpoint[
    ExportAuditLogDataParams,
    GeneralServiceException,
    ExportAuditLogDataResponse
  ] = {
    authEndpoint[
      ExportAuditLogDataParams,
      GeneralServiceException,
      ExportAuditLogDataResponse
    ](
      FundSubAdminPath / "exportAuditLogData"
    )
  }

  lazy val getFundMemberForFilter
    : BaseAuthenticatedEndpoint[FundSubId, GeneralServiceException, Seq[(UserId, UserInfo)]] = {
    authEndpoint[FundSubId, GeneralServiceException, Seq[(UserId, UserInfo)]](
      FundSubAdminPath / "getFundMemberForFilter"
    )
  }

  lazy val getInvestorForFilter
    : BaseAuthenticatedEndpoint[FundSubLpId, GeneralServiceException, Seq[(UserId, UserInfo)]] = {
    authEndpoint[FundSubLpId, GeneralServiceException, Seq[(UserId, UserInfo)]](
      FundSubAdminPath / "getInvestorForFilter"
    )
  }

  lazy val getInvestmentEntityForFilter
    : BaseAuthenticatedEndpoint[FundSubId, GeneralServiceException, Seq[(FundSubLpId, String)]] = {
    authEndpoint[FundSubId, GeneralServiceException, Seq[(FundSubLpId, String)]](
      FundSubAdminPath / "getInvestmentEntityForFilter"
    )

  }

  lazy val getEmailLogData: BaseAuthenticatedEndpoint[
    GetEmailLogDataParams,
    GeneralServiceException,
    GetEmailLogDataResponse
  ] = {
    authEndpoint[
      GetEmailLogDataParams,
      GeneralServiceException,
      GetEmailLogDataResponse
    ](
      FundSubAdminPath / "getEmailLogData"
    )
  }

  lazy val getInviteLpData: BaseAuthenticatedEndpoint[FundSubId, GeneralServiceException, GetInviteLpDataResp] = {
    authEndpoint[FundSubId, GeneralServiceException, GetInviteLpDataResp](
      FundSubAdminPath / "getInviteLpData"
    )
  }

  lazy val exportComment
    : BaseAuthenticatedEndpoint[ExportFundCommentParams, GeneralServiceException, TemporalWorkflowId] = {
    authEndpoint[ExportFundCommentParams, GeneralServiceException, TemporalWorkflowId](
      FundSubAdminPath / "exportComment"
    )
  }

  lazy val cancelCommentExportTask: BaseAuthenticatedEndpoint[
    CancelCommentExportTaskParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      CancelCommentExportTaskParams,
      GeneralServiceException,
      Unit
    ](
      FundSubAdminPath / "cancelCommentExportTask"
    )
  }

  lazy val getCommentExportTaskStatus: BaseAuthenticatedEndpoint[
    FundSubId,
    GeneralServiceException,
    GetCommentExportTaskStatusResponse
  ] = {
    authEndpoint[
      FundSubId,
      GeneralServiceException,
      GetCommentExportTaskStatusResponse
    ](
      FundSubAdminPath / "getCommentExportTaskStatus"
    )
  }

  lazy val getFundCommentNotification: BaseAuthenticatedEndpoint[
    GetFundCommentNotificationParams,
    GeneralServiceException,
    GetFundCommentNotificationResponse
  ] = {
    authEndpoint[
      GetFundCommentNotificationParams,
      GeneralServiceException,
      GetFundCommentNotificationResponse
    ](
      FundSubAdminPath / "getFundCommentNotification"
    )
  }

  lazy val getDataForCommentFilter: BaseAuthenticatedEndpoint[
    GetDataForCommentFilterParams,
    GeneralServiceException,
    GetDataForCommentFilterResp
  ] = authEndpoint[
    GetDataForCommentFilterParams,
    GeneralServiceException,
    GetDataForCommentFilterResp
  ](
    FundSubAdminPath / "getDataForCommentFilter"
  )

  // Lps
  private lazy val FundSubLpPath = FundSubPath / "lp"
  private lazy val SupportingDocPath = FundSubLpPath / "supportingdoc"
  private lazy val FundSubLpCounterPath = FundSubLpPath / "countersign"

  lazy val addMainLp: BaseAuthenticatedEndpoint[
    AddFundSubMainLpsParams,
    GeneralServiceException,
    AddFundSubLpsResponse
  ] = {
    authEndpoint[
      AddFundSubMainLpsParams,
      GeneralServiceException,
      AddFundSubLpsResponse
    ](
      FundSubLpPath / "addMainLp"
    )
  }

  lazy val lpInvestInAdditionalEntity: BaseAuthenticatedEndpoint[
    LpInvestInAdditionalEntityParams,
    GeneralServiceException,
    LpInvestInAdditionalEntityResponse
  ] = {
    authEndpoint[
      LpInvestInAdditionalEntityParams,
      GeneralServiceException,
      LpInvestInAdditionalEntityResponse
    ](
      FundSubLpPath / "lpInvestInAdditionalEntity"
    )
  }

  lazy val lpInvestInAdditionalEntityFromMultipleLpsEmailLink: BaseAuthenticatedEndpoint[
    LpInvestInAdditionalEntityFromMultipleLpsEmailLinkParams,
    GeneralServiceException,
    LpInvestInAdditionalEntityFromMultipleLpsEmailLinkResp
  ] = {
    authEndpoint[
      LpInvestInAdditionalEntityFromMultipleLpsEmailLinkParams,
      GeneralServiceException,
      LpInvestInAdditionalEntityFromMultipleLpsEmailLinkResp
    ](
      FundSubLpPath / "lpInvestInAdditionalEntityFromMultipleLpsEmailLink"
    )
  }

  lazy val getAllFirmNamesOfLp: BaseAuthenticatedEndpoint[
    GetAllFirmNamesOfLpParams,
    GeneralServiceException,
    GetAllFirmNamesOfLpResponse
  ] = {
    authEndpoint[
      GetAllFirmNamesOfLpParams,
      GeneralServiceException,
      GetAllFirmNamesOfLpResponse
    ](
      FundSubLpPath / "getAllFirmNamesOfLp"
    )
  }

  lazy val resendLpInvitation: BaseAuthenticatedEndpoint[
    ResendLpInvitationParams,
    GeneralServiceException,
    ResendLpInvitationResponse
  ] = {
    authEndpoint[
      ResendLpInvitationParams,
      GeneralServiceException,
      ResendLpInvitationResponse
    ](
      FundSubLpPath / "resendLpInvitation"
    )
  }

  lazy val resendLpInvitationWithWorkflow
    : BaseAuthenticatedEndpoint[ResendLpInvitationParams, GeneralServiceException, Unit] = {
    authEndpoint[ResendLpInvitationParams, GeneralServiceException, Unit](
      FundSubLpPath / "resendLpInvitationWithWorkflow"
    )
  }

  lazy val addLpCollaborator
    : BaseAuthenticatedEndpoint[AddFundSubLpCollaboratorsParams, GeneralServiceException, Unit] = {
    authEndpoint[AddFundSubLpCollaboratorsParams, GeneralServiceException, Unit](
      FundSubLpPath / "addLpCollaborators"
    )
  }

  lazy val removeLpCollaborator
    : BaseAuthenticatedEndpoint[RemoveLpCollaboratorParams, GeneralServiceException, Unit] = {
    authEndpoint[RemoveLpCollaboratorParams, GeneralServiceException, Unit](
      FundSubLpPath / "removeLpCollaborators"
    )
  }

  lazy val promoteCollaboratorToLpRole
    : BaseAuthenticatedEndpoint[PromoteCollaboratorToLpRoleParams, GeneralServiceException, Unit] =
    authEndpoint[PromoteCollaboratorToLpRoleParams, GeneralServiceException, Unit](
      FundSubLpPath / "promoteCollaboratorToLpRole"
    )

  lazy val revokeLp: BaseAuthenticatedEndpoint[RevokeFundSubLpParams, GeneralServiceException, Unit] = {
    authEndpoint[RevokeFundSubLpParams, GeneralServiceException, Unit](
      FundSubLpPath / "revoke"
    )
  }

  lazy val restoreLp: BaseAuthenticatedEndpoint[RestoreFundSubLpParams, GeneralServiceException, Unit] = {
    authEndpoint[RestoreFundSubLpParams, GeneralServiceException, Unit](
      FundSubLpPath / "restore"
    )
  }

  lazy val remindLpCompleteForm: BaseAuthenticatedEndpoint[
    RemindLpCompleteFormParams,
    GeneralServiceException,
    RemindLpCompleteFormResponse
  ] = {
    authEndpoint[
      RemindLpCompleteFormParams,
      GeneralServiceException,
      RemindLpCompleteFormResponse
    ](
      FundSubLpPath / "remindCompleteForm"
    )
  }

  lazy val remindLpCompleteFormWithWorkflow
    : BaseAuthenticatedEndpoint[RemindLpCompleteFormParams, GeneralServiceException, Unit] = {
    authEndpoint[RemindLpCompleteFormParams, GeneralServiceException, Unit](
      FundSubLpPath / "remindLpCompleteFormWithWorkflow"
    )
  }

  lazy val requestSupportingDocRestrictedFlow
    : BaseAuthenticatedEndpoint[RequestSupportingDocParams, GeneralServiceException, Unit] = {
    authEndpoint[RequestSupportingDocParams, GeneralServiceException, Unit](
      FundSubLpPath / "requestSupportingDoc"
    )
  }

  lazy val remindSupportingDoc: BaseAuthenticatedEndpoint[
    RemindSupportingDocParams,
    GeneralServiceException,
    RemindSupportingDocResponse
  ] = {
    authEndpoint[
      RemindSupportingDocParams,
      GeneralServiceException,
      RemindSupportingDocResponse
    ](
      FundSubLpPath / "remindSupportingDoc"
    )
  }

  lazy val remindSupportingDocWithWorkflow
    : BaseAuthenticatedEndpoint[RemindSupportingDocParams, GeneralServiceException, Unit] = {
    authEndpoint[RemindSupportingDocParams, GeneralServiceException, Unit](
      FundSubLpPath / "remindSupportingDocWithWorkflow"
    )
  }

  lazy val lpGetForm: BaseAuthenticatedEndpoint[FundSubGetFormParams, GeneralServiceException, FundSubGetFormResponse] = {
    authEndpoint[FundSubGetFormParams, GeneralServiceException, FundSubGetFormResponse](
      FundSubLpPath / "getForm"
    )
  }

  lazy val getTaxFormData
    : BaseAuthenticatedEndpoint[GetTaxFormDataParams, GeneralServiceException, GetTaxFormDataResponse] = {
    authEndpoint[GetTaxFormDataParams, GeneralServiceException, GetTaxFormDataResponse](
      FundSubAdminPath / "getTaxFormData"
    )
  }

  lazy val getFormData: BaseAuthenticatedEndpoint[GetFormDataParams, GeneralServiceException, GetFormDataResponse] = {
    authEndpoint[GetFormDataParams, GeneralServiceException, GetFormDataResponse](
      FundSubAdminPath / "getFormData"
    )
  }

  lazy val submitNewSupportingDoc: BaseAuthenticatedEndpoint[SubmitSupportingDocParams, GeneralServiceException, Unit] = {
    authEndpoint[SubmitSupportingDocParams, GeneralServiceException, Unit](
      FundSubLpPath / "submitNewSupportingDoc"
    )
  }

  lazy val addAdditionalTaxForm: BaseAuthenticatedEndpoint[
    AddAdditionalTaxFormParams,
    GeneralServiceException,
    AddAdditionalTaxFormResponse
  ] = {
    authEndpoint[
      AddAdditionalTaxFormParams,
      GeneralServiceException,
      AddAdditionalTaxFormResponse
    ](
      FundSubLpPath / "addAdditionalTaxForm"
    )
  }

  lazy val removeAdditionalTaxForm
    : BaseAuthenticatedEndpoint[RemoveAdditionalTaxFormParams, GeneralServiceException, Unit] = {
    authEndpoint[RemoveAdditionalTaxFormParams, GeneralServiceException, Unit](
      FundSubLpPath / "removeAdditionalTaxForm"
    )
  }

  lazy val getFundAdditionalTaxForms: BaseAuthenticatedEndpoint[
    GetFundAdditionalTaxFormListsParams,
    GeneralServiceException,
    GetFundAdditionalTaxFormListResponse
  ] = {
    authEndpoint[
      GetFundAdditionalTaxFormListsParams,
      GeneralServiceException,
      GetFundAdditionalTaxFormListResponse
    ](
      FundSubLpPath / "getFundAdditionalTaxForms"
    )
  }

  lazy val lpJoin: BaseAuthenticatedEndpoint[FundSubLpJoinParams, GeneralServiceException, Unit] = {
    authEndpoint[FundSubLpJoinParams, GeneralServiceException, Unit](
      FundSubLpPath / "join"
    )
  }

  lazy val adminSubmitLpPackage: BaseAuthenticatedEndpoint[FundSubLpSubmitParams, GeneralServiceException, Unit] = {
    authEndpoint[FundSubLpSubmitParams, GeneralServiceException, Unit](
      FundSubLpPath / "adminSubmitLpPackage"
    )
  }

  lazy val saveFormValue: BaseAuthenticatedEndpoint[FundSubLpSaveFormParams, GeneralServiceException, Unit] = {
    authEndpoint[FundSubLpSaveFormParams, GeneralServiceException, Unit](
      FundSubLpPath / "save"
    )
  }

  lazy val saveTaxFormValue: BaseAuthenticatedEndpoint[SaveTaxFormValueParams, GeneralServiceException, Unit] = {
    authEndpoint[SaveTaxFormValueParams, GeneralServiceException, Unit](
      FundSubLpPath / "saveTaxFormValue"
    )
  }

  lazy val lpSignAndSubmitTaxForm
    : BaseAuthenticatedEndpoint[LpSignAndSubmitTaxFormParams, GeneralServiceException, Unit] = {
    authEndpoint[LpSignAndSubmitTaxFormParams, GeneralServiceException, Unit](
      FundSubLpPath / "lpSignAndSubmitTaxForm"
    )
  }

  lazy val requestLpChange: BaseAuthenticatedEndpoint[RequestLpChangeParams, GeneralServiceException, Unit] = {
    authEndpoint[RequestLpChangeParams, GeneralServiceException, Unit](
      FundSubLpPath / "requestLpChange"
    )
  }

  lazy val lpViewSection: BaseAuthenticatedEndpoint[FundSubLpViewSectionParams, GeneralServiceException, Unit] = {
    authEndpoint[FundSubLpViewSectionParams, GeneralServiceException, Unit](
      FundSubLpPath / "view" / "section"
    )
  }

  lazy val lpViewPage: BaseAuthenticatedEndpoint[FundSubLpViewPageParams, GeneralServiceException, Unit] = {
    authEndpoint[FundSubLpViewPageParams, GeneralServiceException, Unit](
      FundSubLpPath / "view" / "page"
    )
  }

  lazy val updateInvestorFormSetting
    : BaseAuthenticatedEndpoint[UpdateInvestorFormSettingParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateInvestorFormSettingParams, GeneralServiceException, Unit](
      FundSubLpPath / "updateInvestorFormSetting"
    )
  }

  lazy val lpGoBackToForm: BaseAuthenticatedEndpoint[FundSubLpGoBackToFormParams, GeneralServiceException, Unit] = {
    authEndpoint[FundSubLpGoBackToFormParams, GeneralServiceException, Unit](
      FundSubLpPath / "goBackToForm"
    )
  }

  lazy val lpGoToSignature: BaseAuthenticatedEndpoint[FundSubLpGoToSignatureParams, GeneralServiceException, Unit] = {
    authEndpoint[FundSubLpGoToSignatureParams, GeneralServiceException, Unit](
      FundSubLpPath / "goToSignature"
    )
  }

  lazy val uploadCounterSignedDocs: BaseAuthenticatedEndpoint[
    UploadCounterSignParams,
    GeneralServiceException,
    UploadCounterSignResponse
  ] = {
    authEndpoint[
      UploadCounterSignParams,
      GeneralServiceException,
      UploadCounterSignResponse
    ](
      FundSubLpCounterPath / "uploadCounterSignedDocs"
    )
  }

  lazy val uploadCounterSignedDocsWithWorkflow
    : BaseAuthenticatedEndpoint[UploadCounterSignParams, GeneralServiceException, Unit] = {
    authEndpoint[UploadCounterSignParams, GeneralServiceException, Unit](
      FundSubLpCounterPath / "uploadWithWorkflow"
    )
  }

  lazy val counterESignDocs
    : BaseAuthenticatedEndpoint[CounterEsignParams, GeneralServiceException, CounterEsignResponse] = {
    authEndpoint[CounterEsignParams, GeneralServiceException, CounterEsignResponse](
      FundSubLpCounterPath / "esign"
    )
  }

  lazy val getBatchCountersignData: BaseAuthenticatedEndpoint[
    GetBatchCountersignDataParams,
    GeneralServiceException,
    GetBatchCountersignDataResponse
  ] =
    authEndpoint[
      GetBatchCountersignDataParams,
      GeneralServiceException,
      GetBatchCountersignDataResponse
    ](
      FundSubLpCounterPath / "getBatchCountersignData"
    )

  lazy val getUserPendingCountersignRequests: BaseAuthenticatedEndpoint[
    GetUserPendingCountersignRequestParams,
    GeneralServiceException,
    GetUserPendingCountersignRequestResponse
  ] =
    authEndpoint[
      GetUserPendingCountersignRequestParams,
      GeneralServiceException,
      GetUserPendingCountersignRequestResponse
    ](
      FundSubLpCounterPath / "getPendingCountersignRequests"
    )

  lazy val removeCounterSignedDocs
    : BaseAuthenticatedEndpoint[RemoveCounterSignParams, GeneralServiceException, Unit] = {
    authEndpoint[RemoveCounterSignParams, GeneralServiceException, Unit](
      FundSubLpCounterPath / "removeCounterSignedDocs"
    )
  }

  lazy val distributeCounterSignedDocs: BaseAuthenticatedEndpoint[
    DistributeCounterSignParams,
    GeneralServiceException,
    DistributeCounterSignResponse
  ] = {
    authEndpoint[
      DistributeCounterSignParams,
      GeneralServiceException,
      DistributeCounterSignResponse
    ](
      FundSubLpCounterPath / "distribute"
    )
  }

  lazy val markSubscriptionAsComplete: BaseAuthenticatedEndpoint[
    MarkSubscriptionAsCompleteParams,
    GeneralServiceException,
    EmptyResponse
  ] = {
    authEndpoint[
      MarkSubscriptionAsCompleteParams,
      GeneralServiceException,
      EmptyResponse
    ](
      FundSubLpPath / "markSubscriptionAsComplete"
    )
  }

  lazy val distributeCounterSignedDocsWithWorkflow
    : BaseAuthenticatedEndpoint[DistributeCounterSignParams, GeneralServiceException, Unit] = {
    authEndpoint[DistributeCounterSignParams, GeneralServiceException, Unit](
      FundSubLpCounterPath / "distributeWithWorkflow"
    )
  }

  lazy val uploadAndDistributeCounterSignedDocsRestrictedFlow: BaseAuthenticatedEndpoint[
    UploadAndDistributeCountersignRestrictedFlowParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UploadAndDistributeCountersignRestrictedFlowParams,
      GeneralServiceException,
      Unit
    ](
      FundSubLpCounterPath / "uploadAndDistributeRestrictedFlow"
    )
  }

  lazy val generateDataForSigningStep
    : BaseAuthenticatedEndpoint[GenerateDataForSigningParams, GeneralServiceException, Unit] = {
    authEndpoint[GenerateDataForSigningParams, GeneralServiceException, Unit](
      FundSubLpPath / "generateDataForSigningStep"
    )
  }

  lazy val getSubDocSigningType: BaseAuthenticatedEndpoint[
    GetSubDocSigningTypeParams,
    GeneralServiceException,
    GetSubDocSigningTypeResponse
  ] = {
    authEndpoint[
      GetSubDocSigningTypeParams,
      GeneralServiceException,
      GetSubDocSigningTypeResponse
    ](
      FundSubLpPath / "getSubDocSigningType"
    )
  }

  lazy val generatePdf: BaseAuthenticatedEndpoint[GeneratePdfParams, GeneralServiceException, GeneratePdfResponse] = {
    authEndpoint[GeneratePdfParams, GeneralServiceException, GeneratePdfResponse](
      FundSubLpPath / "generatePdf"
    )
  }

  lazy val generateTaxPdf
    : BaseAuthenticatedEndpoint[GenerateTaxPdfParams, GeneralServiceException, GenerateTaxPdfResponse] = {
    authEndpoint[GenerateTaxPdfParams, GeneralServiceException, GenerateTaxPdfResponse](
      FundSubLpPath / "generateTaxPdf"
    )
  }

  lazy val selectTaxForm
    : BaseAuthenticatedEndpoint[SelectTaxFormParams, GeneralServiceException, SelectTaxFormResponse] = {
    authEndpoint[SelectTaxFormParams, GeneralServiceException, SelectTaxFormResponse](
      FundSubLpPath / "selectTaxForm"
    )
  }

  lazy val getTaxFormsMapping: BaseAuthenticatedEndpoint[
    GetTaxFormsMappingParams,
    GeneralServiceException,
    GetTaxFormsMappingResponse
  ] = {
    authEndpoint[
      GetTaxFormsMappingParams,
      GeneralServiceException,
      GetTaxFormsMappingResponse
    ](
      FundSubLpPath / "getTaxFormsMapping"
    )
  }

  lazy val getSignaturePagesDownloadUrl: BaseAuthenticatedEndpoint[
    GetSignaturePagesDownloadUrlParams,
    GeneralServiceException,
    GetSignaturePagesDownloadUrlResponse
  ] = {
    authEndpoint[
      GetSignaturePagesDownloadUrlParams,
      GeneralServiceException,
      GetSignaturePagesDownloadUrlResponse
    ](
      FundSubLpPath / "getSignaturePagesDownloadUrl"
    )
  }

  lazy val requestSubdocSignature
    : BaseAuthenticatedEndpoint[RequestSubdocSignatureParams, GeneralServiceException, Unit] = {
    authEndpoint[RequestSubdocSignatureParams, GeneralServiceException, Unit](
      FundSubLpPath / "form" / "requestSubdocSignature"
    )
  }

  lazy val fundManagerAccessLpView
    : BaseAuthenticatedEndpoint[FundManagerAccessLpSubscriptionParams, GeneralServiceException, Unit] = {
    authEndpoint[FundManagerAccessLpSubscriptionParams, GeneralServiceException, Unit](
      FundSubLpPath / "fmAccessSubscription"
    )
  }

  lazy val activateManualSubscriptionParams
    : BaseAuthenticatedEndpoint[ActivateManualSubscriptionParams, GeneralServiceException, Unit] = {
    authEndpoint[ActivateManualSubscriptionParams, GeneralServiceException, Unit](
      FundSubLpPath / "lpActivateManualOrderParams"
    )
  }

  lazy val checkSeenLpOnboardGuideTour: BaseAuthenticatedEndpoint[Unit, GeneralServiceException, Boolean] = {
    authEndpoint[Unit, GeneralServiceException, Boolean](
      FundSubLpPath / "checkSeenLpOnboardGuideTour"
    )
  }

  lazy val markSeenLpOnboardGuideTour: BaseAuthenticatedEndpoint[Unit, GeneralServiceException, Unit] = {
    authEndpoint[Unit, GeneralServiceException, Unit](
      FundSubLpPath / "markSeenLpOnboardGuideTour"
    )
  }

  lazy val verifyInvestmentEntityStandardAliasSetup
    : BaseAuthenticatedEndpoint[FundSubLpId, GeneralServiceException, Boolean] = {
    authEndpoint[FundSubLpId, GeneralServiceException, Boolean](
      FundSubLpPath / "verifyInvestmentEntityStandardAliasSetup"
    )
  }

  lazy val getUserModuleOnboarding
    : BaseAuthenticatedEndpoint[ModuleOnboardingMessage, GeneralServiceException, Boolean] = {
    authEndpoint[ModuleOnboardingMessage, GeneralServiceException, Boolean](
      FundSubLpPath / "getUserModuleOnboarding"
    )
  }

  lazy val setUserModuleOnboarding
    : BaseAuthenticatedEndpoint[ModuleOnboardingMessage, GeneralServiceException, Unit] = {
    authEndpoint[ModuleOnboardingMessage, GeneralServiceException, Unit](
      FundSubLpPath / "setUserModuleOnboarding"
    )
  }

  // self-sign
  lazy val lpESign: BaseAuthenticatedEndpoint[LpSelfSignFormParams, GeneralServiceException, Unit] = {
    authEndpoint[LpSelfSignFormParams, GeneralServiceException, Unit](
      FundSubLpPath / "form" / "sign"
    )
  }

  // upload signed doc
  lazy val lpUploadSignedDoc: BaseAuthenticatedEndpoint[LpUploadSignedDocParams, GeneralServiceException, Unit] = {
    authEndpoint[LpUploadSignedDocParams, GeneralServiceException, Unit](
      FundSubLpPath / "uploadSignedDoc"
    )
  }

  // sign from no login view
  lazy val signSubDocRequest: BaseAuthenticatedEndpoint[SignSubDocRequestParams, GeneralServiceException, Unit] = {
    authEndpoint[SignSubDocRequestParams, GeneralServiceException, Unit](
      FundSubLpPath / "form" / "signSubDocRequest"
    )
  }

  lazy val reassignSignatureRequestRestrictedFlow: BaseAuthenticatedEndpoint[
    ReassignSignatureRequestParamsRestrictedFlow,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      ReassignSignatureRequestParamsRestrictedFlow,
      GeneralServiceException,
      Unit
    ](
      FundSubLpPath / "reassignRequestRestrictedFlow"
    )
  }

  lazy val cancelSigning: BaseAuthenticatedEndpoint[CancelSigningParams, GeneralServiceException, Unit] = {
    authEndpoint[CancelSigningParams, GeneralServiceException, Unit](
      FundSubLpPath / "form" / "cancelSigning"
    )
  }

  lazy val cancelLpSignatureRequestRestrictedFlow: BaseAuthenticatedEndpoint[
    CancelSignatureRequestParamsRestrictedFlow,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      CancelSignatureRequestParamsRestrictedFlow,
      GeneralServiceException,
      Unit
    ](
      FundSubLpPath / "cancelLpSignatureRequestRestrictedFlow"
    )
  }

  lazy val markSubDocRequestAsCompletedRestrictedFlow
    : BaseAuthenticatedEndpoint[MarkSubDocRequestAsCompletedParams, GeneralServiceException, Unit] =
    authEndpoint[MarkSubDocRequestAsCompletedParams, GeneralServiceException, Unit](
      FundSubLpPath / "markSubDocRequestAsCompleted"
    )

  lazy val uploadSupportingDocsOnBehalfRestrictedFlow
    : BaseAuthenticatedEndpoint[UploadSupportingDocsOnBehalfParams, GeneralServiceException, Unit] = {
    authEndpoint[UploadSupportingDocsOnBehalfParams, GeneralServiceException, Unit](
      FundSubLpPath / "uploadSupportingDocsOnBehalfRestrictedFlow"
    )
  }

  lazy val uploadRefDocForLp: BaseAuthenticatedEndpoint[UploadReferenceDocsParams, GeneralServiceException, Unit] = {
    authEndpoint[UploadReferenceDocsParams, GeneralServiceException, Unit](
      FundSubLpPath / "uploadRefDocForLp"
    )
  }

  lazy val removeRefDocForLp: BaseAuthenticatedEndpoint[RemoveReferenceDocsParams, GeneralServiceException, Unit] = {
    authEndpoint[RemoveReferenceDocsParams, GeneralServiceException, Unit](
      FundSubLpPath / "removeRefDocForLp"
    )
  }

  lazy val mergeLpDocuments: BaseAuthenticatedEndpoint[
    MergeLpDocumentsParams,
    GeneralServiceException,
    MergeLpDocumentsResponse
  ] = {
    authEndpoint[
      MergeLpDocumentsParams,
      GeneralServiceException,
      MergeLpDocumentsResponse
    ](
      FundSubLpPath / "mergeLpDocuments"
    )
  }

  // new supporting doc apis
  lazy val getAllLpNewSupportingDocs: BaseAuthenticatedEndpoint[
    GetLpSupportingDocsParams,
    GeneralServiceException,
    GetLpSupportingDocsResponse
  ] = {
    authEndpoint[
      GetLpSupportingDocsParams,
      GeneralServiceException,
      GetLpSupportingDocsResponse
    ](
      SupportingDocPath / "getAllLpNewSupportingDocs"
    )
  }

  lazy val resubmitNewSupportingDoc
    : BaseAuthenticatedEndpoint[ResubmitSupportingDocParams, GeneralServiceException, Unit] = {
    authEndpoint[ResubmitSupportingDocParams, GeneralServiceException, Unit](
      SupportingDocPath / "resubmitNewSupportingDoc"
    )
  }

  lazy val markNewSupportingDocAsNotApplicable
    : BaseAuthenticatedEndpoint[MarkSupportingDocAsNotApplicableParams, GeneralServiceException, Unit] = {
    authEndpoint[MarkSupportingDocAsNotApplicableParams, GeneralServiceException, Unit](
      SupportingDocPath / "markNewSupportingDocAsNotApplicable"
    )
  }

  lazy val unmarkNewSupportingDocAsNotApplicable: BaseAuthenticatedEndpoint[
    UnmarkSupportingDocAsNotApplicableParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UnmarkSupportingDocAsNotApplicableParams,
      GeneralServiceException,
      Unit
    ](
      SupportingDocPath / "unmarkNewSupportingDocAsNotApplicable"
    )
  }

  lazy val removeSupportingDoc: BaseAuthenticatedEndpoint[RemoveSupportingDocParams, GeneralServiceException, Unit] = {
    authEndpoint[RemoveSupportingDocParams, GeneralServiceException, Unit](
      SupportingDocPath / "removeSupportingDoc"
    )
  }

  lazy val uploadAndSubmitSupportingDoc
    : BaseAuthenticatedEndpoint[UploadAndSubmitSupportingDocParams, GeneralServiceException, Unit] = {
    authEndpoint[UploadAndSubmitSupportingDocParams, GeneralServiceException, Unit](
      SupportingDocPath / "uploadAndSubmitSupportingDoc"
    )
  }

  lazy val removeOtherSupportingDoc
    : BaseAuthenticatedEndpoint[RemoveOtherSupportingDocParams, GeneralServiceException, Unit] = {
    authEndpoint[RemoveOtherSupportingDocParams, GeneralServiceException, Unit](
      SupportingDocPath / "removeOtherSupportingDoc"
    )
  }

  lazy val submitOtherTypeSupportingDoc
    : BaseAuthenticatedEndpoint[SubmitOtherTypeSupportingDocParams, GeneralServiceException, Unit] = {
    authEndpoint[SubmitOtherTypeSupportingDocParams, GeneralServiceException, Unit](
      SupportingDocPath / "submitOtherTypeSupportingDoc"
    )
  }

  lazy val removeUploadedNewSupportingDocFile
    : BaseAuthenticatedEndpoint[RemoveUploadedSupportingDocFileParams, GeneralServiceException, Unit] = {
    authEndpoint[RemoveUploadedSupportingDocFileParams, GeneralServiceException, Unit](
      SupportingDocPath / "removeUploadedNewSupportingDocFile"
    )
  }

  lazy val uploadAndSubmitOtherTypeSupportingDoc: BaseAuthenticatedEndpoint[
    UploadAndSubmitOtherTypeSupportingDocParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UploadAndSubmitOtherTypeSupportingDocParams,
      GeneralServiceException,
      Unit
    ](
      SupportingDocPath / "uploadAndSubmitOtherTypeSupportingDoc"
    )
  }

  lazy val getFilesSharedFromInvestmentEntity: BaseAuthenticatedEndpoint[
    GetFilesSharedFromInvestmentEntityParams,
    GeneralServiceException,
    GetFilesSharedFromInvestmentEntityResp
  ] = {
    authEndpoint[
      GetFilesSharedFromInvestmentEntityParams,
      GeneralServiceException,
      GetFilesSharedFromInvestmentEntityResp
    ](
      SupportingDocPath / "getFilesSharedFromInvestmentEntity"
    )
  }

  lazy val requestSignatureOnTaxForm
    : BaseAuthenticatedEndpoint[RequestSignatureOnTaxFormParams, GeneralServiceException, Unit] = {
    authEndpoint[RequestSignatureOnTaxFormParams, GeneralServiceException, Unit](
      SupportingDocPath / "requestSignatureOnTaxForm"
    )
  }

  lazy val cancelSignatureRequestOnTaxForm
    : BaseAuthenticatedEndpoint[CancelSignatureOnTaxFormParams, GeneralServiceException, Unit] = {
    authEndpoint[CancelSignatureOnTaxFormParams, GeneralServiceException, Unit](
      SupportingDocPath / "cancelSignatureRequestOnTaxForm"
    )
  }

  lazy val remindToSignTaxForm
    : BaseAuthenticatedEndpoint[RemindSignerOnTaxFormParams, GeneralServiceException, Unit] = {
    authEndpoint[RemindSignerOnTaxFormParams, GeneralServiceException, Unit](
      SupportingDocPath / "remindToSignTaxForm"
    )
  }

  lazy val signTaxFormSignatureRequest
    : BaseAuthenticatedEndpoint[SignTaxFormSignatureRequestParams, GeneralServiceException, Unit] = {
    authEndpoint[SignTaxFormSignatureRequestParams, GeneralServiceException, Unit](
      SupportingDocPath / "signTaxFormSignatureRequest"
    )
  }

  lazy val getTaxFormSignatureRequestData: BaseAuthenticatedEndpoint[
    FundSubSupportingDocId,
    GeneralServiceException,
    GetTaxFormSignatureRequestResponse
  ] = {
    authEndpoint[
      FundSubSupportingDocId,
      GeneralServiceException,
      GetTaxFormSignatureRequestResponse
    ](
      SupportingDocPath / "getTaxFormSignatureRequestData"
    )
  }

  lazy val updateSupportingDocName
    : BaseAuthenticatedEndpoint[UpdateSupportingDocNameParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateSupportingDocNameParams, GeneralServiceException, Unit](
      SupportingDocPath / "updateSupportingDocName"
    )
  }

  lazy val updateLpInformation: BaseAuthenticatedEndpoint[UpdateLpInformationParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateLpInformationParams, GeneralServiceException, Unit](
      FundSubLpPath / "updateLpInformation"
    )
  }

  lazy val getAccessToProtectedLink: BaseAuthenticatedEndpoint[
    GetAccessToProtectedLinkParams,
    GeneralServiceException,
    GetAccessToProtectedLinkResponse
  ] = {
    authEndpoint[
      GetAccessToProtectedLinkParams,
      GeneralServiceException,
      GetAccessToProtectedLinkResponse
    ](
      FundSubLpPath / "getAccessViaProtectedLink"
    )
  }

  lazy val getFundAdminContact: BaseAuthenticatedEndpoint[
    GetFundAdminContactParams,
    GeneralServiceException,
    GetFundAdminContactResponse
  ] = {
    authEndpoint[
      GetFundAdminContactParams,
      GeneralServiceException,
      GetFundAdminContactResponse
    ](
      FundSubLpPath / "getFundAdminContact"
    )
  }

  lazy val updateFirmName: BaseAuthenticatedEndpoint[UpdateFirmNameParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateFirmNameParams, GeneralServiceException, Unit](
      FundSubLpPath / "updateFirmName"
    )
  }

  // Contact
  private lazy val FundSubContactPath = FundSubPath / "contact"

  lazy val viewedOnboardingModal: BaseAuthenticatedEndpoint[Unit, GeneralServiceException, Unit] = {
    authEndpoint[Unit, GeneralServiceException, Unit](
      FundSubContactPath / "viewedOnboardingModal"
    )
  }

  lazy val getContactAndGroup: BaseAuthenticatedEndpoint[
    GetContactAndGroupParams,
    GeneralServiceException,
    GetContactAndGroupResponse
  ] = {
    authEndpoint[
      GetContactAndGroupParams,
      GeneralServiceException,
      GetContactAndGroupResponse
    ](
      FundSubContactPath / "getContactAndGroup"
    )
  }

  lazy val createContactGroup: BaseAuthenticatedEndpoint[
    CreateContactGroupParams,
    GeneralServiceException,
    CreateContactGroupResponse
  ] = {
    authEndpoint[
      CreateContactGroupParams,
      GeneralServiceException,
      CreateContactGroupResponse
    ](
      FundSubContactPath / "createContactGroup"
    )
  }

  lazy val editContactGroup: BaseAuthenticatedEndpoint[EditContactGroupParams, GeneralServiceException, Unit] = {
    authEndpoint[EditContactGroupParams, GeneralServiceException, Unit](
      FundSubContactPath / "editContactGroup"
    )
  }

  lazy val deleteContactGroup: BaseAuthenticatedEndpoint[DeleteContactGroupParams, GeneralServiceException, Unit] = {
    authEndpoint[DeleteContactGroupParams, GeneralServiceException, Unit](
      FundSubContactPath / "deleteContactGroup"
    )
  }

  lazy val addContact: BaseAuthenticatedEndpoint[AddContactParams, GeneralServiceException, Unit] = {
    authEndpoint[AddContactParams, GeneralServiceException, Unit](
      FundSubContactPath / "addContact"
    )
  }

  lazy val editContact: BaseAuthenticatedEndpoint[EditContactParams, GeneralServiceException, Unit] = {
    authEndpoint[EditContactParams, GeneralServiceException, Unit](
      FundSubContactPath / "editContact"
    )
  }

  lazy val deleteContact: BaseAuthenticatedEndpoint[DeleteContactParams, GeneralServiceException, Unit] = {
    authEndpoint[DeleteContactParams, GeneralServiceException, Unit](
      FundSubContactPath / "deleteContact"
    )
  }

  lazy val batchDeleteContactAndGroup
    : BaseAuthenticatedEndpoint[BatchDeleteContactAndGroupParams, GeneralServiceException, Unit] = {
    authEndpoint[BatchDeleteContactAndGroupParams, GeneralServiceException, Unit](
      FundSubContactPath / "batchDeleteContactAndGroup"
    )
  }

  lazy val listContactsByEmailOrder: BaseAuthenticatedEndpoint[
    ListContactsByEmailOrderParams,
    GeneralServiceException,
    ListContactsByEmailOrderResponse
  ] = {
    authEndpoint[
      ListContactsByEmailOrderParams,
      GeneralServiceException,
      ListContactsByEmailOrderResponse
    ](
      FundSubContactPath / "listContactsByEmailOrder"
    )
  }

  lazy val queryContactsByEmail: BaseAuthenticatedEndpoint[
    QueryContactsByEmailParams,
    GeneralServiceException,
    QueryContactsByEmailResponse
  ] = {
    authEndpoint[
      QueryContactsByEmailParams,
      GeneralServiceException,
      QueryContactsByEmailResponse
    ](
      FundSubContactPath / "queryContactsByEmail"
    )
  }

  lazy val queryContactsByName: BaseAuthenticatedEndpoint[
    QueryContactsByNameParams,
    GeneralServiceException,
    QueryContactsByNameResponse
  ] = {
    authEndpoint[
      QueryContactsByNameParams,
      GeneralServiceException,
      QueryContactsByNameResponse
    ](
      FundSubContactPath / "queryContactsByName"
    )
  }

  lazy val getContactsWithSameEmailForGroups: BaseAuthenticatedEndpoint[
    GetContactsWithSameEmailForGroupParams,
    GeneralServiceException,
    GetContactsWithSameEmailForGroupResponse
  ] =
    authEndpoint[
      GetContactsWithSameEmailForGroupParams,
      GeneralServiceException,
      GetContactsWithSameEmailForGroupResponse
    ](
      FundSubContactPath / "getContactsWithSameEmailForGroups"
    )

  lazy val queryGroupByName: BaseAuthenticatedEndpoint[
    QueryGroupByNameParams,
    GeneralServiceException,
    QueryGroupsByNameResponse
  ] =
    authEndpoint[
      QueryGroupByNameParams,
      GeneralServiceException,
      QueryGroupsByNameResponse
    ](
      FundSubContactPath / "queryGroupByName"
    )

  lazy val getAllGroups: BaseAuthenticatedEndpoint[GetAllGroupParams, GeneralServiceException, GetAllGroupsResponse] =
    authEndpoint[GetAllGroupParams, GeneralServiceException, GetAllGroupsResponse](
      FundSubContactPath / "getAllGroups"
    )

  lazy val getGroupContacts: BaseAuthenticatedEndpoint[
    GetGroupContactsParams,
    GeneralServiceException,
    GetGroupsContactsResponse
  ] =
    authEndpoint[
      GetGroupContactsParams,
      GeneralServiceException,
      GetGroupsContactsResponse
    ](
      FundSubContactPath / "getGroupContacts"
    )

  // White Label
  private lazy val FundSubWhiteLabelPath = FundSubPath / "whitelabel"

  lazy val getWhiteLabel: BaseAuthenticatedEndpoint[
    GetFundSubWhiteLabelParams,
    GeneralServiceException,
    FundSubWhiteLabelData
  ] = {
    authEndpoint[
      GetFundSubWhiteLabelParams,
      GeneralServiceException,
      FundSubWhiteLabelData
    ](
      FundSubWhiteLabelPath / "get"
    )
  }

  lazy val updateWhiteLabel: BaseAuthenticatedEndpoint[
    UpdateFundSubWhiteLabelParams,
    GeneralServiceException,
    UpdateFundSubWhiteLabelResponse
  ] = {
    authEndpoint[
      UpdateFundSubWhiteLabelParams,
      GeneralServiceException,
      UpdateFundSubWhiteLabelResponse
    ](
      FundSubWhiteLabelPath / "update"
    )
  }

  lazy val removeWhiteLabelLogo: BaseAuthenticatedEndpoint[
    RemoveFundSubWhiteLabelLogoParams,
    GeneralServiceException,
    GeneralServiceResponse
  ] = {
    authEndpoint[
      RemoveFundSubWhiteLabelLogoParams,
      GeneralServiceException,
      GeneralServiceResponse
    ](
      FundSubWhiteLabelPath / "removeLogo"
    )
  }

  // Copy Config
  private lazy val FundSubCopyConfigPath = FundSubPath / "copyConfig"

  lazy val getCopyConfigForClient: BaseAuthenticatedEndpoint[
    GetCopyConfigForClientParams,
    GeneralServiceException,
    GetCopyConfigForClientResponse
  ] = {
    authEndpoint[
      GetCopyConfigForClientParams,
      GeneralServiceException,
      GetCopyConfigForClientResponse
    ](
      FundSubCopyConfigPath / "getCopyConfigForClient"
    )
  }

  // Advance Export
  private lazy val FundSubExportPath = FundSubPath / "export"

  lazy val generateExcel: BaseAuthenticatedEndpoint[ExportExcelParams, GeneralServiceException, FileId] = {
    authEndpoint[ExportExcelParams, GeneralServiceException, FileId](
      FundSubExportPath / "exportLpDataOld"
    )
  }

  lazy val loadExportTemplateFromFile: BaseAuthenticatedEndpoint[
    LoadExportTemplateFromFileParams,
    GeneralServiceException,
    LoadExportTemplateFromFileResponse
  ] =
    authEndpoint[
      LoadExportTemplateFromFileParams,
      GeneralServiceException,
      LoadExportTemplateFromFileResponse
    ](
      FundSubExportPath / "loadExportTemplateFromFile"
    )

  lazy val generateFundSubExportTemplateId: BaseAuthenticatedEndpoint[
    GenerateFundSubExportTemplateIdParams,
    GeneralServiceException,
    GenerateFundSubExportTemplateIdResponse
  ] =
    authEndpoint[
      GenerateFundSubExportTemplateIdParams,
      GeneralServiceException,
      GenerateFundSubExportTemplateIdResponse
    ](
      FundSubExportPath / "generateFundSubExportTemplateId"
    )

  lazy val generateDefaultExportTemplate: BaseAuthenticatedEndpoint[
    GenerateDefaultExportTemplateParams,
    GeneralServiceException,
    GenerateDefaultExportTemplateResponse
  ] =
    authEndpoint[
      GenerateDefaultExportTemplateParams,
      GeneralServiceException,
      GenerateDefaultExportTemplateResponse
    ](
      FundSubExportPath / "generateDefaultExportTemplate"
    )

  lazy val generateDefaultExportTemplateFile: BaseAuthenticatedEndpoint[
    GenerateDefaultExportTemplateFileParams,
    GeneralServiceException,
    GenerateDefaultExportTemplateFileResponse
  ] = {
    authEndpoint[
      GenerateDefaultExportTemplateFileParams,
      GeneralServiceException,
      GenerateDefaultExportTemplateFileResponse
    ](
      FundSubExportPath / "generateDefaultExportTemplateFile"
    )
  }

  lazy val getFundSubExportFormsAndTemplates: BaseAuthenticatedEndpoint[
    GetFundSubExportFormsAndTemplatesParams,
    GeneralServiceException,
    GetFundSubExportFormsAndTemplatesResponse
  ] =
    authEndpoint[
      GetFundSubExportFormsAndTemplatesParams,
      GeneralServiceException,
      GetFundSubExportFormsAndTemplatesResponse
    ](
      FundSubExportPath / "getFundSubExportFormsAndTemplates"
    )

  lazy val saveExportTemplateInfo: BaseAuthenticatedEndpoint[
    SaveExportTemplateInfoParams,
    GeneralServiceException,
    SaveExportTemplateInfoResponse
  ] =
    authEndpoint[
      SaveExportTemplateInfoParams,
      GeneralServiceException,
      SaveExportTemplateInfoResponse
    ](
      FundSubExportPath / "saveExportTemplateInfo"
    )

  lazy val getAllTemplateInfosToExport: BaseAuthenticatedEndpoint[
    GetAllTemplateInfosToExportParams,
    GeneralServiceException,
    GetAllTemplateInfosToExportResp
  ] =
    authEndpoint[
      GetAllTemplateInfosToExportParams,
      GeneralServiceException,
      GetAllTemplateInfosToExportResp
    ](
      FundSubExportPath / "getAllTemplateInfosToExport"
    )

  lazy val exportLpDataUsingDefaultTemplate: BaseAuthenticatedEndpoint[
    ExportLpDataUsingDefaultTemplateParams,
    GeneralServiceException,
    FileId
  ] =
    authEndpoint[
      ExportLpDataUsingDefaultTemplateParams,
      GeneralServiceException,
      FileId
    ](
      FundSubExportPath / "exportLpFormDataUsingDefaultTemplate"
    )

  lazy val exportLpDataUsingDefaultPdfTemplate: BaseAuthenticatedEndpoint[
    ExportLpDataUsingDefaultPdfTemplateParams,
    GeneralServiceException,
    FileId
  ] =
    authEndpoint[
      ExportLpDataUsingDefaultPdfTemplateParams,
      GeneralServiceException,
      FileId
    ](
      FundSubExportPath / "exportLpDataUsingDefaultPdfTemplate"
    )

  lazy val exportLpDataUsingCustomTemplate: BaseAuthenticatedEndpoint[
    ExportLpDataUsingCustomTemplateParams,
    GeneralServiceException,
    FileId
  ] =
    authEndpoint[
      ExportLpDataUsingCustomTemplateParams,
      GeneralServiceException,
      FileId
    ](
      FundSubExportPath / "exportLpDataUsingCustomTemplate"
    )

  lazy val generateDefaultImportTemplateFile: BaseAuthenticatedEndpoint[
    GenerateDefaultImportTemplateFileParams,
    GeneralServiceException,
    GenerateDefaultImportTemplateFileResponse
  ] =
    authEndpoint[
      GenerateDefaultImportTemplateFileParams,
      GeneralServiceException,
      GenerateDefaultImportTemplateFileResponse
    ](
      FundSubExportPath / "generateDefaultImportTemplateFile"
    )

  // Template import
  private lazy val FundSubImportPath = FundSubPath / "import"

  lazy val startImportWorkflow: BaseAuthenticatedEndpoint[
    StartImportWorkflowParams,
    GeneralServiceException,
    StartImportWorkflowResponse
  ] =
    authEndpoint[
      StartImportWorkflowParams,
      GeneralServiceException,
      StartImportWorkflowResponse
    ](
      FundSubImportPath / "startImportWorkflow"
    )

  lazy val getImportWorkflowStatus: BaseAuthenticatedEndpoint[
    GetImportWorkflowStatusParams,
    GeneralServiceException,
    GetImportWorkflowStatusResponse
  ] =
    authEndpoint[
      GetImportWorkflowStatusParams,
      GeneralServiceException,
      GetImportWorkflowStatusResponse
    ](
      FundSubImportPath / "getImportWorkflowStatus"
    )

  lazy val getImportWorkflowItemResult: BaseAuthenticatedEndpoint[
    GetImportItemResultParams,
    GeneralServiceException,
    GetImportItemResultResponse
  ] =
    authEndpoint[
      GetImportItemResultParams,
      GeneralServiceException,
      GetImportItemResultResponse
    ](
      FundSubImportPath / "getImportWorkflowItemResult"
    )

  lazy val getFormTemplateMapping: BaseAuthenticatedEndpoint[
    GetFormTemplateMappingParams,
    GeneralServiceException,
    GetFormTemplateMappingResponse
  ] =
    authEndpoint[
      GetFormTemplateMappingParams,
      GeneralServiceException,
      GetFormTemplateMappingResponse
    ](
      FundSubImportPath / "getFormTemplateMapping"
    )

  // Share DR link
  lazy val updateSharedDrLink
    : BaseAuthenticatedEndpoint[UpdateSharedDataRoomLinkParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateSharedDataRoomLinkParams, GeneralServiceException, Unit](
      FundSubAdminPath / "updateSharedDrLink"
    )
  }

  // Form comment
  private lazy val FundSubFormCommentPath = FundSubPath / "formcomment"

  lazy val createComment: BaseAuthenticatedEndpoint[CreateCommentParams, GeneralServiceException, Option[IssueId]] = {
    authEndpoint[CreateCommentParams, GeneralServiceException, Option[IssueId]](
      FundSubFormCommentPath / "createComment"
    )
  }

  lazy val addCommentReply: BaseAuthenticatedEndpoint[
    AddCommentReplyParams,
    GeneralServiceException,
    AddCommentReplyResponse
  ] = {
    authEndpoint[
      AddCommentReplyParams,
      GeneralServiceException,
      AddCommentReplyResponse
    ](
      FundSubFormCommentPath / "addCommentReply"
    )
  }

  lazy val resolveComment: BaseAuthenticatedEndpoint[ResolveCommentParams, GeneralServiceException, CommonResponse] = {
    authEndpoint[ResolveCommentParams, GeneralServiceException, CommonResponse](
      FundSubFormCommentPath / "resolveComment"
    )
  }

  lazy val assignComment
    : BaseAuthenticatedEndpoint[AssignCommentParams, GeneralServiceException, AssignCommentResponse] = {
    authEndpoint[AssignCommentParams, GeneralServiceException, AssignCommentResponse](
      FundSubFormCommentPath / "assignComment"
    )
  }

  lazy val unAssignComment
    : BaseAuthenticatedEndpoint[UnAssignCommentParams, GeneralServiceException, CommonResponse] = {
    authEndpoint[UnAssignCommentParams, GeneralServiceException, CommonResponse](
      FundSubFormCommentPath / "unAssignComment"
    )
  }

  lazy val reopenComment: BaseAuthenticatedEndpoint[ReopenCommentParams, GeneralServiceException, CommonResponse] = {
    authEndpoint[ReopenCommentParams, GeneralServiceException, CommonResponse](
      FundSubFormCommentPath / "reopenComment"
    )
  }

  lazy val updateComment: BaseAuthenticatedEndpoint[UpdateCommentParams, GeneralServiceException, CommonResponse] = {
    authEndpoint[UpdateCommentParams, GeneralServiceException, CommonResponse](
      FundSubFormCommentPath / "updateComment"
    )
  }

  lazy val updateCommentReply
    : BaseAuthenticatedEndpoint[UpdateCommentReplyParams, GeneralServiceException, CommonResponse] = {
    authEndpoint[UpdateCommentReplyParams, GeneralServiceException, CommonResponse](
      FundSubFormCommentPath / "updateCommentReply"
    )
  }

  lazy val deleteComment: BaseAuthenticatedEndpoint[DeleteCommentParams, GeneralServiceException, CommonResponse] = {
    authEndpoint[DeleteCommentParams, GeneralServiceException, CommonResponse](
      FundSubFormCommentPath / "deleteComment"
    )
  }

  lazy val flagComment: BaseAuthenticatedEndpoint[FlagCommentParams, GeneralServiceException, FlagCommentResponse] = {
    authEndpoint[FlagCommentParams, GeneralServiceException, FlagCommentResponse](
      FundSubFormCommentPath / "flagComment"
    )
  }

  lazy val deleteCommentReply
    : BaseAuthenticatedEndpoint[DeleteCommentReplyParams, GeneralServiceException, CommonResponse] = {
    authEndpoint[DeleteCommentReplyParams, GeneralServiceException, CommonResponse](
      FundSubFormCommentPath / "deleteCommentReply"
    )
  }

  lazy val getCommentThread: BaseAuthenticatedEndpoint[
    GetCommentThreadParams,
    GeneralServiceException,
    GetCommentThreadResponse
  ] = {
    authEndpoint[
      GetCommentThreadParams,
      GeneralServiceException,
      GetCommentThreadResponse
    ](
      FundSubFormCommentPath / "getCommentThread"
    )
  }

  lazy val getCommentAnchorPointAssignee: BaseAuthenticatedEndpoint[
    GetCommentAnchorPointAssigneeParams,
    GeneralServiceException,
    GetCommentAnchorPointAssigneeResponse
  ] = {
    authEndpoint[
      GetCommentAnchorPointAssigneeParams,
      GeneralServiceException,
      GetCommentAnchorPointAssigneeResponse
    ](
      FundSubFormCommentPath / "getCommentAnchorPointAssignee"
    )
  }

  lazy val getFundComments: BaseAuthenticatedEndpoint[
    GetFundCommentParams,
    GeneralServiceException,
    GetFundCommentsResponse
  ] = {
    authEndpoint[
      GetFundCommentParams,
      GeneralServiceException,
      GetFundCommentsResponse
    ](
      FundSubFormCommentPath / "getFundComments"
    )
  }

  lazy val getFundCommentAnchorPoints: BaseAuthenticatedEndpoint[
    GetFundCommentDualThreadsParams,
    GeneralServiceException,
    GetFundCommentDualThreadsResponse
  ] = {
    authEndpoint[
      GetFundCommentDualThreadsParams,
      GeneralServiceException,
      GetFundCommentDualThreadsResponse
    ](
      FundSubFormCommentPath / "getFundCommentAnchorPoints"
    )
  }

  lazy val getLpsMetaInfo: BaseAuthenticatedEndpoint[
    GetLpsMetaInfoParams,
    GeneralServiceException,
    GetLpsMetaInfoResponse
  ] = {
    authEndpoint[GetLpsMetaInfoParams, GeneralServiceException, GetLpsMetaInfoResponse](
      FundSubFormCommentPath / "getLpsMetaInfo"
    )
  }

  lazy val getInboxTabSummary: BaseAuthenticatedEndpoint[
    GetInboxTabSummaryParams,
    GeneralServiceException,
    GetInboxTabSummaryResponse
  ] = {
    authEndpoint[
      GetInboxTabSummaryParams,
      GeneralServiceException,
      GetInboxTabSummaryResponse
    ](
      FundSubFormCommentPath / "getInboxTabSummary"
    )
  }

  lazy val getCommentMentions: BaseAuthenticatedEndpoint[
    GetCommentMentionParams,
    GeneralServiceException,
    GetCommentMentionsResponse
  ] = {
    authEndpoint[
      GetCommentMentionParams,
      GeneralServiceException,
      GetCommentMentionsResponse
    ](
      FundSubFormCommentPath / "getCommentMentions"
    )
  }

  lazy val getMentionsInfoInComment: BaseAuthenticatedEndpoint[
    GetMentionsInfoInCommentThreadParam,
    GeneralServiceException,
    GetMentionsInfoInCommentThreadResponse
  ] = {
    authEndpoint[
      GetMentionsInfoInCommentThreadParam,
      GeneralServiceException,
      GetMentionsInfoInCommentThreadResponse
    ](
      FundSubFormCommentPath / "getMentionsInfoInComment"
    )
  }

  lazy val getMentionableList: BaseAuthenticatedEndpoint[
    GetMentionableListParams,
    GeneralServiceException,
    GetMentionableListResponse
  ] = {
    authEndpoint[
      GetMentionableListParams,
      GeneralServiceException,
      GetMentionableListResponse
    ](
      FundSubFormCommentPath / "getMentionableList"
    )
  }

  lazy val getAssignableList: BaseAuthenticatedEndpoint[
    GetAssignableListParams,
    GeneralServiceException,
    GetAssignableListResponse
  ] = {
    authEndpoint[
      GetAssignableListParams,
      GeneralServiceException,
      GetAssignableListResponse
    ](
      FundSubFormCommentPath / "getAssignableList"
    )
  }

  lazy val getAssigneeList: BaseAuthenticatedEndpoint[
    GetAssigneeFilterListParams,
    GeneralServiceException,
    GetAssigneeFilterListResponse
  ] = {
    authEndpoint[
      GetAssigneeFilterListParams,
      GeneralServiceException,
      GetAssigneeFilterListResponse
    ](
      FundSubFormCommentPath / "getAssigneeList"
    )
  }

  lazy val getSingleLpComments: BaseAuthenticatedEndpoint[
    GetLpCommentsParams,
    GeneralServiceException,
    GetLpCommentsResponse
  ] = {
    authEndpoint[GetLpCommentsParams, GeneralServiceException, GetLpCommentsResponse](
      FundSubFormCommentPath / "getSingleLpComments"
    )
  }

  lazy val getGaiaFormCommentAllFieldsInfo: BaseAuthenticatedEndpoint[
    GetGaiaFormCommentAllFieldsInfoParams,
    GeneralServiceException,
    GetFormCommentAllFieldsInfoResponse
  ] = {
    authEndpoint[
      GetGaiaFormCommentAllFieldsInfoParams,
      GeneralServiceException,
      GetFormCommentAllFieldsInfoResponse
    ](
      FundSubFormCommentPath / "getGaiaFormCommentAllFieldsInfo"
    )
  }

  lazy val markWholeCommentThreadAsSeen: BaseAuthenticatedEndpoint[
    MarkWholeCommentThreadAsSeenParams,
    GeneralServiceException,
    CommonResponse
  ] = {
    authEndpoint[
      MarkWholeCommentThreadAsSeenParams,
      GeneralServiceException,
      CommonResponse
    ](
      FundSubFormCommentPath / "markWholeCommentThreadAsSeen"
    )
  }

  lazy val markFormCommentNotifSpaceAsSeen: BaseAuthenticatedEndpoint[
    MarkFormCommentingNotifSpaceAsSeenParams,
    GeneralServiceException,
    CommonResponse
  ] = {
    authEndpoint[
      MarkFormCommentingNotifSpaceAsSeenParams,
      GeneralServiceException,
      CommonResponse
    ](
      FundSubFormCommentPath / "markFormCommentNotifSpaceAsSeen"
    )
  }

  lazy val getPublicThreadsFromIds: BaseAuthenticatedEndpoint[
    GetPublicThreadsFromIdsParams,
    GeneralServiceException,
    GetPublicThreadsFromIdsResponse
  ] = {
    authEndpoint[
      GetPublicThreadsFromIdsParams,
      GeneralServiceException,
      GetPublicThreadsFromIdsResponse
    ](
      FundSubFormCommentPath / "getPublicThreadsFromIds"
    )
  }

  lazy val checkIfUserCanMakePublicComment: BaseAuthenticatedEndpoint[FundSubLpId, GeneralServiceException, Boolean] = {
    authEndpoint[FundSubLpId, GeneralServiceException, Boolean](
      FundSubFormCommentPath / "checkIfUserCanMakePublicComment"
    )
  }

  // Review package
  private lazy val FundSubReviewPackagePath = FundSubPath / "reviewpackage"

  lazy val enableReviewPackage: BaseAuthenticatedEndpoint[
    EnableReviewPackageParams,
    GeneralServiceException,
    GeneralServiceResponse
  ] = {
    authEndpoint[
      EnableReviewPackageParams,
      GeneralServiceException,
      GeneralServiceResponse
    ](
      FundSubReviewPackagePath / "enable"
    )
  }

  lazy val disableReviewPackage: BaseAuthenticatedEndpoint[
    DisableReviewPackageParams,
    GeneralServiceException,
    DisableReviewPackageResponse
  ] = {
    authEndpoint[
      DisableReviewPackageParams,
      GeneralServiceException,
      DisableReviewPackageResponse
    ](
      FundSubReviewPackagePath / "disable"
    )
  }

  lazy val updateReviewPackageSetting
    : BaseAuthenticatedEndpoint[UpdateReviewPackageSettingParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateReviewPackageSettingParams, GeneralServiceException, Unit](
      FundSubReviewPackagePath / "updateReviewPackageSetting"
    )
  }

  lazy val assignReviewers: BaseAuthenticatedEndpoint[
    AssignReviewersParams,
    GeneralServiceException,
    GeneralServiceResponse
  ] = {
    authEndpoint[
      AssignReviewersParams,
      GeneralServiceException,
      GeneralServiceResponse
    ](
      FundSubReviewPackagePath / "assign"
    )
  }

  lazy val removeReviewers: BaseAuthenticatedEndpoint[
    RemoveReviewersParams,
    GeneralServiceException,
    RemoveReviewersResponse
  ] = {
    authEndpoint[
      RemoveReviewersParams,
      GeneralServiceException,
      RemoveReviewersResponse
    ](
      FundSubReviewPackagePath / "remove"
    )
  }

  lazy val checkReviewerPermission
    : BaseAuthenticatedEndpoint[FundSubLpId, GeneralServiceException, CheckReviewerPermissionResponse] = {
    authEndpoint[FundSubLpId, GeneralServiceException, CheckReviewerPermissionResponse](
      FundSubReviewPackagePath / "verify"
    )
  }

  // Data generator
  private lazy val FundSubDataGeneratorPath = FundSubPath / "datagenerator"

  lazy val generateInvestors: BaseAuthenticatedEndpoint[GenerateInvestorParams, GeneralServiceException, Unit] =
    authEndpoint[GenerateInvestorParams, GeneralServiceException, Unit](
      FundSubDataGeneratorPath / "generateInvestors"
    )

  // Fund close
  private lazy val FundSubClosePath = FundSubPath / "close"

  lazy val getFundSubCloseData
    : BaseAuthenticatedEndpoint[FundSubId, GeneralServiceException, GetFundSubClosesInfoResp] = {
    authEndpoint[FundSubId, GeneralServiceException, GetFundSubClosesInfoResp](
      FundSubClosePath / "getFundSubCloseData"
    )
  }

  lazy val createFundSubClose
    : BaseAuthenticatedEndpoint[CreateFundSubCloseParams, GeneralServiceException, FundSubCloseId] = {
    authEndpoint[CreateFundSubCloseParams, GeneralServiceException, FundSubCloseId](
      FundSubClosePath / "createFundSubClose"
    )
  }

  lazy val updateFundSubClose: BaseAuthenticatedEndpoint[UpdateFundSubCloseParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateFundSubCloseParams, GeneralServiceException, Unit](
      FundSubClosePath / "updateFundSubClose"
    )
  }

  lazy val deleteFundSubClose: BaseAuthenticatedEndpoint[DeleteFundSubCloseParams, GeneralServiceException, Unit] = {
    authEndpoint[DeleteFundSubCloseParams, GeneralServiceException, Unit](
      FundSubClosePath / "deleteFundSubClose"
    )
  }

  lazy val moveLpsToClose: BaseAuthenticatedEndpoint[MoveLpsToCloseParams, GeneralServiceException, Unit] = {
    authEndpoint[MoveLpsToCloseParams, GeneralServiceException, Unit](
      FundSubClosePath / "moveLpsToClose"
    )
  }

  private lazy val FundSubSubscriptionDocPath = FundSubPath / "subscription-doc"

  lazy val getOriginalSubscriptionFormName: BaseAuthenticatedEndpoint[
    FundSubLpId,
    GeneralServiceException,
    String
  ] = {
    authEndpoint[FundSubLpId, GeneralServiceException, String](
      FundSubSubscriptionDocPath / "getOriginalSubscriptionFormName"
    )
  }

  lazy val getOriginalSubscriptionDocuments: BaseAuthenticatedEndpoint[
    FundSubLpId,
    GeneralServiceException,
    Seq[FileId]
  ] = {
    authEndpoint[FundSubLpId, GeneralServiceException, Seq[FileId]](
      FundSubSubscriptionDocPath / "getOriginalSubscriptionDocuments"
    )
  }

  lazy val getSubscriptionVersionBasicInfo: BaseAuthenticatedEndpoint[
    GetSubscriptionVersionBasicInfoParams,
    GeneralServiceException,
    GetSubscriptionVersionBasicInfoResponse
  ] = {
    authEndpoint[
      GetSubscriptionVersionBasicInfoParams,
      GeneralServiceException,
      GetSubscriptionVersionBasicInfoResponse
    ](
      FundSubSubscriptionDocPath / "getSubscriptionVersionBasicInfo"
    )
  }

  // Same as getSubscriptionVersionBasicInfo except customized error handler
  lazy val getSubscriptionVersionBasicInfoWithErrorHandler: BaseAuthenticatedEndpoint[
    GetSubscriptionVersionBasicInfoParams,
    GetSubscriptionVersionBasicInfoException,
    GetSubscriptionVersionBasicInfoResponse
  ] = {
    authEndpoint[
      GetSubscriptionVersionBasicInfoParams,
      GetSubscriptionVersionBasicInfoException,
      GetSubscriptionVersionBasicInfoResponse
    ](
      FundSubSubscriptionDocPath / "getSubscriptionVersionBasicInfoWithErrorHandler"
    )
  }

  // Same as getSubscriptionVersionBasicInfo but with customized error handler
  lazy val getSubscriptionVersionBasicInfoWithoutGeneratingCleanDocs: BaseAuthenticatedEndpoint[
    GetSubscriptionVersionBasicInfoParams,
    GeneralServiceException,
    GetSubscriptionVersionBasicInfoResponse
  ] = {
    authEndpoint[
      GetSubscriptionVersionBasicInfoParams,
      GeneralServiceException,
      GetSubscriptionVersionBasicInfoResponse
    ](
      FundSubSubscriptionDocPath / "getSubscriptionVersionBasicInfoWithoutGeneratingCleanDocs"
    )
  }

  lazy val getAllSubscriptionVersionsBasicInfo: BaseAuthenticatedEndpoint[
    GetAllSubscriptionVersionBasicInfoParams,
    GeneralServiceException,
    GetAllSubscriptionVersionBasicInfoResponse
  ] = {
    authEndpoint[
      GetAllSubscriptionVersionBasicInfoParams,
      GeneralServiceException,
      GetAllSubscriptionVersionBasicInfoResponse
    ](
      FundSubSubscriptionDocPath / "getAllSubscriptionVersionsBasicInfo"
    )
  }

  lazy val getNumberOfSignedVersion: BaseAuthenticatedEndpoint[
    FundSubLpId,
    GeneralServiceException,
    Int
  ] = {
    authEndpoint[FundSubLpId, GeneralServiceException, Int](
      FundSubSubscriptionDocPath / "getNumberOfSignedVersion"
    )
  }

  lazy val getSubscriptionVersionFormInfo: BaseAuthenticatedEndpoint[
    GetSubscriptionVersionFormInfoParams,
    GeneralServiceException,
    GetSubscriptionVersionFormInfoResponse
  ] = {
    authEndpoint[
      GetSubscriptionVersionFormInfoParams,
      GeneralServiceException,
      GetSubscriptionVersionFormInfoResponse
    ](
      FundSubSubscriptionDocPath / "getSubscriptionVersionFormInfo"
    )
  }

  lazy val getSubscriptionDocSigningType: BaseAuthenticatedEndpoint[
    GetSubscriptionDocSigningTypeParams,
    GeneralServiceException,
    GetSubscriptionDocSigningTypeResponse
  ] =
    authEndpoint[
      GetSubscriptionDocSigningTypeParams,
      GeneralServiceException,
      GetSubscriptionDocSigningTypeResponse
    ](
      FundSubSubscriptionDocPath / "getSubscriptionDocSigningType"
    )

  lazy val saveSubscriptionFormData: BaseAuthenticatedEndpoint[
    SaveSubscriptionFormDataParams,
    GeneralServiceException,
    SaveSubscriptionFormDataResponse
  ] = {
    authEndpoint[
      SaveSubscriptionFormDataParams,
      GeneralServiceException,
      SaveSubscriptionFormDataResponse
    ](
      FundSubSubscriptionDocPath / "saveSubscriptionFormData"
    )
  }

  lazy val startEditingSubscriptionForm: BaseAuthenticatedEndpoint[
    StartEditingSubscriptionFormParams,
    GeneralServiceException,
    StartEditingSubscriptionFormResponse
  ] = {
    authEndpoint[
      StartEditingSubscriptionFormParams,
      GeneralServiceException,
      StartEditingSubscriptionFormResponse
    ](
      FundSubSubscriptionDocPath / "startEditingSubscriptionForm"
    )
  }

  lazy val calculateAndUpdateSignatureStatus: BaseAuthenticatedEndpoint[FundSubLpId, GeneralServiceException, Unit] =
    authEndpoint[FundSubLpId, GeneralServiceException, Unit](
      FundSubSubscriptionDocPath / "calculateAndUpdateSignatureStatus"
    )

  lazy val checkIfNeedToSignAgainAfterEditForm
    : BaseAuthenticatedEndpoint[FundSubLpId, GeneralServiceException, Boolean] =
    authEndpoint[FundSubLpId, GeneralServiceException, Boolean](
      FundSubSubscriptionDocPath / "checkIfNeedToSignAgainAfterEditForm"
    )

  lazy val createSubscriptionDocSignatureRequest: BaseAuthenticatedEndpoint[
    CreateSubscriptionDocSignatureRequestParams,
    GeneralServiceException,
    CreateSubscriptionDocSignatureRequestResp
  ] =
    authEndpoint[
      CreateSubscriptionDocSignatureRequestParams,
      GeneralServiceException,
      CreateSubscriptionDocSignatureRequestResp
    ](
      FundSubSubscriptionDocPath / "createSubscriptionDocSignatureRequest"
    )

  lazy val eSignSubscriptionDoc: BaseAuthenticatedEndpoint[ESignSubscriptionDocParams, GeneralServiceException, Unit] =
    authEndpoint[ESignSubscriptionDocParams, GeneralServiceException, Unit](
      FundSubSubscriptionDocPath / "eSignSubscriptionDoc"
    )

  lazy val remindSubscriptionDocSignatureRequest: BaseAuthenticatedEndpoint[
    RemindSubscriptionDocSignatureRequestParams,
    GeneralServiceException,
    Unit
  ] =
    authEndpoint[
      RemindSubscriptionDocSignatureRequestParams,
      GeneralServiceException,
      Unit
    ](
      FundSubSubscriptionDocPath / "remindSubscriptionDocSignatureRequest"
    )

  lazy val uploadSignedSubscriptionDoc: BaseAuthenticatedEndpoint[
    UploadSignedSubscriptionDocParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[UploadSignedSubscriptionDocParams, GeneralServiceException, Unit](
      FundSubSubscriptionDocPath / "uploadSignedSubscriptionDoc"
    )
  }

  lazy val gpUploadSignedSubscriptionDoc: BaseAuthenticatedEndpoint[
    GpUploadSignedSubscriptionDocParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[GpUploadSignedSubscriptionDocParams, GeneralServiceException, Unit](
      FundSubSubscriptionDocPath / "gpUploadSignedSubscriptionDoc"
    )
  }

  lazy val gpUploadExecutedSubscription: BaseAuthenticatedEndpoint[
    GpUploadExecutedSubscriptionParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[GpUploadExecutedSubscriptionParams, GeneralServiceException, Unit](
      FundSubSubscriptionDocPath / "gpUploadExecutedSubscription"
    )
  }

  lazy val cancelCompletedSubscriptionSignature: BaseAuthenticatedEndpoint[
    FundSubLpId,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[FundSubLpId, GeneralServiceException, Unit](
      FundSubSubscriptionDocPath / "cancelCompletedSubscriptionSignature"
    )
  }

  lazy val lpManuallySubmitPackage: BaseAuthenticatedEndpoint[
    LpManuallySubmitPackageParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[LpManuallySubmitPackageParams, GeneralServiceException, Unit](
      FundSubSubscriptionDocPath / "lpManuallySubmitPackage"
    )
  }

  lazy val submitSubscriptionVersionForReview: BaseAuthenticatedEndpoint[
    SubmitSubscriptionVersionForReviewParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      SubmitSubscriptionVersionForReviewParams,
      GeneralServiceException,
      Unit
    ](
      FundSubSubscriptionDocPath / "submitSubscriptionVersionForReview"
    )
  }

  lazy val getRequestChangeInfo: BaseAuthenticatedEndpoint[
    FundSubLpId,
    GeneralServiceException,
    GetSubscriptionDocRequestChangesInfoResponse
  ] = {
    authEndpoint[
      FundSubLpId,
      GeneralServiceException,
      GetSubscriptionDocRequestChangesInfoResponse
    ](
      FundSubSubscriptionDocPath / "getRequestChangeInfo"
    )
  }

  lazy val getToSubmitSubscriptionDocuments: BaseAuthenticatedEndpoint[
    GetToSubmitSubscriptionDocumentsParams,
    GeneralServiceException,
    Seq[SubscriptionSubmittedDocument]
  ] = {
    authEndpoint[
      GetToSubmitSubscriptionDocumentsParams,
      GeneralServiceException,
      Seq[SubscriptionSubmittedDocument]
    ](
      FundSubSubscriptionDocPath / "getToSubmitSubscriptionDocuments"
    )
  }

  private lazy val FundSubParticipantPath = FundSubPath / "participant"

  lazy val addMainLpWorkflow: BaseAuthenticatedEndpoint[
    AddFundSubMainLpsParams,
    GeneralServiceException,
    FundSubBatchInvitationId
  ] = {
    authEndpoint[
      AddFundSubMainLpsParams,
      GeneralServiceException,
      FundSubBatchInvitationId
    ](
      FundSubParticipantPath / "addMainLpWorkflow"
    )
  }

  lazy val addMainLpWorkflowWithoutJointInfo: BaseAuthenticatedEndpoint[
    AddFundSubMainLpsWithoutJointInfoParams,
    GeneralServiceException,
    FundSubBatchInvitationId
  ] = {
    authEndpoint[
      AddFundSubMainLpsWithoutJointInfoParams,
      GeneralServiceException,
      FundSubBatchInvitationId
    ](
      FundSubParticipantPath / "addMainLpWorkflowWithoutJointInfo"
    )
  }

  lazy val getBatchInvitationInfo: BaseAuthenticatedEndpoint[
    FundSubBatchInvitationId,
    GeneralServiceException,
    FundSubBatchInvitationInfo
  ] =
    authEndpoint[
      FundSubBatchInvitationId,
      GeneralServiceException,
      FundSubBatchInvitationInfo
    ](
      FundSubParticipantPath / "get"
    )

  lazy val getAllBatchInvitationInfo: BaseAuthenticatedEndpoint[
    GetAllBatchInvitationInfosParams,
    GeneralServiceException,
    GetAllBatchInvitationInfos
  ] =
    authEndpoint[
      GetAllBatchInvitationInfosParams,
      GeneralServiceException,
      GetAllBatchInvitationInfos
    ](
      FundSubParticipantPath / "get-all"
    )

  lazy val removeBatchInvitation: BaseAuthenticatedEndpoint[FundSubBatchInvitationId, GeneralServiceException, Unit] =
    authEndpoint[FundSubBatchInvitationId, GeneralServiceException, Unit](
      FundSubParticipantPath / "remove"
    )

  lazy val stopBatchInvitation: BaseAuthenticatedEndpoint[FundSubBatchInvitationId, GeneralServiceException, Unit] =
    authEndpoint[FundSubBatchInvitationId, GeneralServiceException, Unit](
      FundSubParticipantPath / "stop"
    )

  lazy val exportBatchInviteInvestorReport: BaseAuthenticatedEndpoint[
    ExportBatchInviteInvestorReportParams,
    GeneralServiceException,
    FileId
  ] = {
    authEndpoint[
      ExportBatchInviteInvestorReportParams,
      GeneralServiceException,
      FileId
    ](
      FundSubParticipantPath / "exportBatchInviteInvestorReport"
    )
  }

  lazy val getFundSubPointOfContact: BaseAuthenticatedEndpoint[
    GetFundSubPointOfContactParams,
    GeneralServiceException,
    GetFundSubPointOfContactResponse
  ] = {
    authEndpoint[
      GetFundSubPointOfContactParams,
      GeneralServiceException,
      GetFundSubPointOfContactResponse
    ](
      FundSubParticipantPath / "getFundSubPointOfContact"
    )
  }

  lazy val setFundSubPointOfContactWithExternal: BaseAuthenticatedEndpoint[
    SetFundSubPointOfContactWithExternalParams,
    GeneralServiceException,
    Unit
  ] =
    authEndpoint[
      SetFundSubPointOfContactWithExternalParams,
      GeneralServiceException,
      Unit
    ](
      FundSubParticipantPath / "setFundSubPointOfContactWithExternal"
    )

  lazy val getFundSubPointOfContactWithExternal: BaseAuthenticatedEndpoint[
    GetFundSubPointOfContactWithExternalParams,
    GeneralServiceException,
    GetFundSubPointOfContactWithExternalResponse
  ] = {
    authEndpoint[
      GetFundSubPointOfContactWithExternalParams,
      GeneralServiceException,
      GetFundSubPointOfContactWithExternalResponse
    ](
      FundSubParticipantPath / "getFundSubPointOfContactWithExternal"
    )
  }

  // Supporting doc history

  lazy val getAllRelatedOrderIds: BaseAuthenticatedEndpoint[FundSubLpId, GeneralServiceException, List[FundSubLpId]] = {
    authEndpoint[FundSubLpId, GeneralServiceException, List[FundSubLpId]](
      FundSubLpPath / "getAllRelatedOrderIds"
    )
  }

  lazy val getAllSupportingDocsOfOrder
    : BaseAuthenticatedEndpoint[FundSubLpId, GeneralServiceException, GetOrderSupportingDocsResponse] = {
    authEndpoint[FundSubLpId, GeneralServiceException, GetOrderSupportingDocsResponse](
      FundSubLpPath / "getAllSupportingDocsOfOrder"
    )
  }

  // Feature switch
  private lazy val FeatureSwitchPath = FundSubPath / "switch"

  lazy val exportSwitchUsageMetrics
    : BaseAuthenticatedEndpoint[ExportSwitchUsageMetricsParams, GeneralServiceException, FileId] = {
    authEndpoint[ExportSwitchUsageMetricsParams, GeneralServiceException, FileId](
      FeatureSwitchPath / "exportSwitchUsageMetrics"
    )
  }

  // Environment
  private lazy val EnvironmentPath = FundSubPath / "environment"

  lazy val getFundSubEnvironmentBaseUrlWithPrefixPath: BaseAuthenticatedEndpoint[
    FundSubId,
    GeneralServiceException,
    String
  ] = {
    authEndpoint[FundSubId, GeneralServiceException, String](
      EnvironmentPath / "getFundSubEnvironmentBaseUrlWithPrefixPath"
    )
  }

  // Auto prefill
  lazy val getFundFormModel: BaseAuthenticatedEndpoint[
    FundSubId,
    GeneralServiceException,
    Either[DynamicFormInfo, lp.GaiaFormModel]
  ] = {
    authEndpoint[
      FundSubId,
      GeneralServiceException,
      Either[DynamicFormInfo, lp.GaiaFormModel]
    ](
      FundSubLpPath / "getFundFormModel"
    )
  }

  lazy val computePrefillDataFromPastSubscription: BaseAuthenticatedEndpoint[
    ComputePrefillDataFromPastSubscriptionParams,
    GeneralServiceException,
    ComputeFormPrefillDataResponse
  ] = {
    authEndpoint[
      ComputePrefillDataFromPastSubscriptionParams,
      GeneralServiceException,
      ComputeFormPrefillDataResponse
    ](
      FundSubLpPath / "computePrefillDataFromPastSubscription"
    )
  }

  lazy val computePrefillDataFromPastLp: BaseAuthenticatedEndpoint[
    ComputePrefillDataFromPastLpParams,
    GeneralServiceException,
    LpAutoPrefillData
  ] = {
    authEndpoint[
      ComputePrefillDataFromPastLpParams,
      GeneralServiceException,
      LpAutoPrefillData
    ](
      FundSubLpPath / "computePrefillDataFromPastLp"
    )
  }

  lazy val prefillLpDataFromPastSubscription: BaseAuthenticatedEndpoint[
    PrefillLpDataFromPastSubscriptionParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      PrefillLpDataFromPastSubscriptionParams,
      GeneralServiceException,
      Unit
    ](
      FundSubLpPath / "prefillLpDataFromPastSubscription"
    )
  }

  lazy val saveProfileFromSubscription: BaseAuthenticatedEndpoint[
    SaveProfileFromSubscriptionParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[SaveProfileFromSubscriptionParams, GeneralServiceException, Unit](
      FundSubLpPath / "saveProfileFromSubscription"
    )
  }

  lazy val compareSubscriptionAndProfileData: BaseAuthenticatedEndpoint[
    CompareSubscriptionAndProfileDataParams,
    GeneralServiceException,
    CompareSubscriptionAndProfileDataResponse
  ] = {
    authEndpoint[
      CompareSubscriptionAndProfileDataParams,
      GeneralServiceException,
      CompareSubscriptionAndProfileDataResponse
    ](
      FundSubLpPath / "compareSubscriptionAndProfileData"
    )
  }

  lazy val compareMainSubscriptionAndLpProfileTemplateData: BaseAuthenticatedEndpoint[
    CompareSubscriptionAndProfileTemplateParams,
    GeneralServiceException,
    CompareSubscriptionAndProfileDataResponse
  ] = {
    authEndpoint[
      CompareSubscriptionAndProfileTemplateParams,
      GeneralServiceException,
      CompareSubscriptionAndProfileDataResponse
    ](
      FundSubLpPath / "compareMainSubscriptionAndLpProfileTemplateData"
    )
  }

  lazy val batchComputeNumPrefillableFieldsForLp: BaseAuthenticatedEndpoint[
    BatchComputeNumPrefillableFieldsForLpParams,
    GeneralServiceException,
    BatchComputeNumPrefillableFieldsResponse
  ] = {
    authEndpoint[
      BatchComputeNumPrefillableFieldsForLpParams,
      GeneralServiceException,
      BatchComputeNumPrefillableFieldsResponse
    ](
      FundSubLpPath / "batchComputeNumPrefillableFieldsForLp"
    )
  }

  lazy val computeProfilePrefillDataForLp: BaseAuthenticatedEndpoint[
    ComputeProfilePrefillDataForLpParams,
    GeneralServiceException,
    ComputeFormPrefillDataResponse
  ] = {
    authEndpoint[
      ComputeProfilePrefillDataForLpParams,
      GeneralServiceException,
      ComputeFormPrefillDataResponse
    ](
      FundSubLpPath / "computeProfilePrefillDataForLp"
    )
  }

  lazy val prefillLpDataFromProfile
    : BaseAuthenticatedEndpoint[PrefillLpDataFromProfileParams, GeneralServiceException, Unit] = {
    authEndpoint[PrefillLpDataFromProfileParams, GeneralServiceException, Unit](
      FundSubLpPath / "prefillLpDataFromProfile"
    )
  }

  lazy val computeSupportingFormDataFromProfile: BaseAuthenticatedEndpoint[
    ComputeSupportingFormDataFromProfileParams,
    GeneralServiceException,
    ComputeFormPrefillDataResponse
  ] = {
    authEndpoint[
      ComputeSupportingFormDataFromProfileParams,
      GeneralServiceException,
      ComputeFormPrefillDataResponse
    ](
      FundSubLpPath / "computeSupportingFormDataFromProfile"
    )
  }

  lazy val prefillSupportingFormDataFromProfile: BaseAuthenticatedEndpoint[
    PrefillSupportingFormDataFromProfileParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      PrefillSupportingFormDataFromProfileParams,
      GeneralServiceException,
      Unit
    ](
      FundSubLpPath / "prefillSupportingFormDataFromProfile"
    )
  }

  lazy val batchComputeNumPrefillableFieldsForSupportingForm: BaseAuthenticatedEndpoint[
    BatchComputeNumPrefillableFieldsForSupportingFormParams,
    GeneralServiceException,
    BatchComputeNumPrefillableFieldsResponse
  ] = {
    authEndpoint[
      BatchComputeNumPrefillableFieldsForSupportingFormParams,
      GeneralServiceException,
      BatchComputeNumPrefillableFieldsResponse
    ](
      FundSubLpPath / "batchComputeNumPrefillableFieldsForSupportingForm"
    )
  }

  lazy val clearLpFormValues: BaseAuthenticatedEndpoint[FundSubLpId, GeneralServiceException, Unit] = {
    authEndpoint[FundSubLpId, GeneralServiceException, Unit](
      FundSubLpPath / "clearLpFormValues"
    )
  }

  lazy val checkLpAutofillConditions: BaseAuthenticatedEndpoint[
    CheckLpAutofillConditionsParams,
    GeneralServiceException,
    CheckLpAutofillConditionsResponse
  ] = {
    authEndpoint[
      CheckLpAutofillConditionsParams,
      GeneralServiceException,
      CheckLpAutofillConditionsResponse
    ](
      FundSubLpPath / "checkLpAutofillConditions"
    )
  }

  lazy val checkLpSaveDocumentsConditions: BaseAuthenticatedEndpoint[
    CheckLpSaveDocumentsConditionsParams,
    GeneralServiceException,
    CheckLpSaveDocumentsConditionsResponse
  ] = {
    authEndpoint[
      CheckLpSaveDocumentsConditionsParams,
      GeneralServiceException,
      CheckLpSaveDocumentsConditionsResponse
    ](
      FundSubLpPath / "checkLpSaveDocumentsConditions"
    )
  }

  // NOTE: Underlying this would generate import template using both CSA & ASA. Changing this endpoint name/path
  // however might break things, e.g., some API test scripts -> keep as is for now
  lazy val getFormAsaImportTemplate: BaseAuthenticatedEndpoint[
    GetFormAsaImportTemplateParams,
    GeneralServiceException,
    GetFormAsaImportTemplateResp
  ] = {
    authEndpoint[
      GetFormAsaImportTemplateParams,
      GeneralServiceException,
      GetFormAsaImportTemplateResp
    ](
      FundSubLpPath / "getFormAsaImportTemplate"
    )
  }

  lazy val getLpDocumentsPageData: BaseAuthenticatedEndpoint[
    GetLpDocumentsPageParams,
    GeneralServiceException,
    GetLpDocumentsPageResponse
  ] = {
    authEndpoint[
      GetLpDocumentsPageParams,
      GeneralServiceException,
      GetLpDocumentsPageResponse
    ](
      FundSubLpPath / "getLpDocumentPageData"
    )
  }

  lazy val appendSkipOnboardingFlowUser: BaseAuthenticatedEndpoint[FundSubLpId, GeneralServiceException, Unit] = {
    authEndpoint[FundSubLpId, GeneralServiceException, Unit](
      FundSubLpPath / "appendSkipOnboardingFlowUser"
    )
  }

  lazy val getFundMemberRole: BaseAuthenticatedEndpoint[
    GetFundMemberRoleParams,
    GeneralServiceException,
    GetFundMemberRoleResponse
  ] = {
    authEndpoint[
      GetFundMemberRoleParams,
      GeneralServiceException,
      GetFundMemberRoleResponse
    ](
      FundSubLpPath / "getFundMemberRole"
    )
  }

  lazy val userDismissNameMismatch: BaseAuthenticatedEndpoint[
    FundSubLpId,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      FundSubLpId,
      GeneralServiceException,
      Unit
    ](
      FundSubLpPath / "userDismissNameMismatch"
    )
  }

  lazy val updateListUserShouldNotShowSavingDocSuggestion: BaseAuthenticatedEndpoint[
    UpdateListUserShouldNotShowSavingDocSuggestionParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UpdateListUserShouldNotShowSavingDocSuggestionParams,
      GeneralServiceException,
      Unit
    ](
      FundSubLpPath / "updateListUserShouldNotShowSavingDocSuggestion"
    )
  }

  lazy val getLpReferenceDocs: BaseAuthenticatedEndpoint[
    GetLpReferenceDocsParams,
    GeneralServiceException,
    GetLpReferenceDocsResp
  ] =
    authEndpoint[
      GetLpReferenceDocsParams,
      GeneralServiceException,
      GetLpReferenceDocsResp
    ](FundSubLpPath / "getLpReferenceDocs")

  lazy val updateLpAutoSavedFlags
    : BaseAuthenticatedEndpoint[UpdateLpAutoSavedFlagsParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateLpAutoSavedFlagsParams, GeneralServiceException, Unit](
      FundSubLpPath / "updateLpAutoSavedFlags"
    )
  }

  lazy val markUserSeenAutoFillTour: BaseAuthenticatedEndpoint[FundSubLpId, GeneralServiceException, Unit] = {
    authEndpoint[FundSubLpId, GeneralServiceException, Unit](
      FundSubLpPath / "markUserSeenAutoFillTour"
    )
  }

  // User
  private lazy val FunSubUserPath = FundSubPath / "user"

  lazy val getUserTracking: BaseAuthenticatedEndpoint[Unit, GeneralServiceException, GetUserTrackingResponse] = {
    authEndpoint[Unit, GeneralServiceException, GetUserTrackingResponse](
      FunSubUserPath / "getUserTracking"
    )
  }

  lazy val updateUserTracking: BaseAuthenticatedEndpoint[
    UpdateUserTrackingParams,
    GeneralServiceException,
    FundSubUserTrackingModel
  ] = {
    authEndpoint[
      UpdateUserTrackingParams,
      GeneralServiceException,
      FundSubUserTrackingModel
    ](
      FunSubUserPath / "updateUserTracking"
    )
  }

  lazy val checkIfUserCanMakePublicCommentInFund: BaseAuthenticatedEndpoint[
    FundSubId,
    GeneralServiceException,
    Boolean
  ] = {
    authEndpoint[
      FundSubId,
      GeneralServiceException,
      Boolean
    ](
      FunSubUserPath / "checkIfFundManagerCanMakeShareComment"
    )
  }

  private lazy val AmlCheckPath = FundSubPath / "amlCheck"

  lazy val getAmlCheckByLpId: BaseAuthenticatedEndpoint[
    FundSubLpId,
    GeneralServiceException,
    GetFundSubAmlCheckResponse
  ] = {
    authEndpoint[FundSubLpId, GeneralServiceException, GetFundSubAmlCheckResponse](
      AmlCheckPath / "getAmlCheckByLpId"
    )
  }

  private lazy val MultiRegionPath = FundSubPath / "multi-region"

  lazy val getFundSubMultiRegionInfo: BaseAuthenticatedEndpoint[
    CommonParams.Empty,
    GeneralServiceException,
    GetFundSubRegionInfosResponse
  ] = {
    authEndpoint[
      CommonParams.Empty,
      GeneralServiceException,
      GetFundSubRegionInfosResponse
    ](
      MultiRegionPath / "getFundSubMultiRegionInfo"
    )
  }

  lazy val getFundSubEntityName: BaseAuthenticatedEndpoint[
    GetFundSubEntityNameParams,
    GeneralServiceException,
    GetFundSubEntityNameResp
  ] = authEndpoint[
    GetFundSubEntityNameParams,
    GeneralServiceException,
    GetFundSubEntityNameResp
  ](FundSubLpPath / "getFundSubEntityName")

  lazy val getFundSubSignatureConfig: BaseAuthenticatedEndpoint[
    FundSubLpId,
    GeneralServiceException,
    GetFundSubSignatureConfigResponse
  ] = {
    authEndpoint[
      FundSubLpId,
      GeneralServiceException,
      GetFundSubSignatureConfigResponse
    ](
      FundSubLpPath / "getFundSubSignatureConfig"
    )
  }

  lazy val getSignatureRequestInfo: BaseAuthenticatedEndpoint[
    GetSignatureRequestInfoParams,
    GeneralServiceException,
    SignatureRequestInfo
  ] = {
    authEndpoint[
      GetSignatureRequestInfoParams,
      GeneralServiceException,
      SignatureRequestInfo
    ](
      FundSubLpPath / "getSignatureRequestInfo"
    )
  }

  lazy val getSubscriptions: BaseAuthenticatedEndpoint[
    GetSubscriptionsParams,
    GeneralServiceException,
    GetSubscriptionsResponse
  ] = {
    authEndpoint[
      GetSubscriptionsParams,
      GeneralServiceException,
      GetSubscriptionsResponse
    ](
      FundSubLpPath / "getSubscriptions"
    )
  }

  lazy val getSmtpConfig: BaseAuthenticatedEndpoint[
    FundSubId,
    GeneralServiceException,
    CustomSmtpServerConfigParams
  ] = {
    authEndpoint[
      FundSubId,
      GeneralServiceException,
      CustomSmtpServerConfigParams
    ](
      FundSubAdminPath / "getSmtpConfig"
    )
  }

  lazy val updateSmtpConfig: BaseAuthenticatedEndpoint[
    UpdateSmtpConfigParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UpdateSmtpConfigParams,
      GeneralServiceException,
      Unit
    ](
      FundSubAdminPath / "updateSmtpConfig"
    )
  }

  lazy val sendTestEmailUsingCustomSmtp: BaseAuthenticatedEndpoint[
    SendTestEmailUsingCustomSmtpParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      SendTestEmailUsingCustomSmtpParams,
      GeneralServiceException,
      Unit
    ](
      FundSubAdminPath / "sendTestEmailUsingCustomSmtp"
    )
  }

  lazy val updateAllowFormEditPostSigning: BaseAuthenticatedEndpoint[
    UpdateAllowFormEditPostSigningParams,
    GeneralServiceException,
    EmptyResponse
  ] = authEndpoint[
    UpdateAllowFormEditPostSigningParams,
    GeneralServiceException,
    EmptyResponse
  ](
    FundSubAdminPath / "updateAllowFormEditPostSigning"
  )

}
