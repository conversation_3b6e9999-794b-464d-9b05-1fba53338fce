// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.endpoint.lp

import java.time.Instant
import scala.util.Random

import io.circe.{<PERSON><PERSON>, <PERSON><PERSON>}

import anduin.account.protocol.BifrostCommonProtocol.CaptchaResponse
import anduin.batchaction.BatchActionType
import anduin.circe.generic.semiauto.{deriveCodecWithDefaults, deriveEnumCodec}
import anduin.forms.FormData
import anduin.forms.engine.GaiaState
import anduin.forms.model.FormDataSource
import anduin.forms.ui.types.{SigningGuideline, SigningType}
import anduin.fundsub.endpoint.common.{CreateRequestSignerData, SignData}
import anduin.fundsub.endpoint.contact.ContactData
import anduin.fundsub.endpoint.lp.ImportFromFundData.AutoPrefillDocsOption
import anduin.fundsub.endpoint.signature.DocusignESignatureOptionParams
import anduin.id.form.FormVersionId
import anduin.id.funddata.{FundDataFirmId, FundDataInvestmentEntityId}
import anduin.id.fundsub.*
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.id.link.ProtectedLinkId
import anduin.id.serverless.ServerlessRequestId
import anduin.id.signature.SignatureRequestId
import anduin.model.codec.EitherCodec.given
import anduin.model.codec.MapCodecs.given
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.{DynamicFormId, FileId}
import anduin.protobuf.dynamicform.{DynamicFormSection, DynamicFormSetting}
import anduin.protobuf.email.EmailModel
import anduin.protobuf.external.squants.MoneyMessage
import anduin.protobuf.fundsub.*
import anduin.protobuf.fundsub.models.InactiveCommentSetting
import anduin.protobuf.signature.{DocumentSignatureMessage, DocumentSignatureTemplateMessage}
import anduin.stargazer.util.client.GraphqlClientUtils.InstantCodecs.given

final case class GetEmailTemplateParams(
  fundSubId: FundSubId,
  fundSubEvents: Seq[FundSubEvent],
  lpIdOpt: Option[FundSubLpId]
)

object GetEmailTemplateParams {
  given Codec.AsObject[GetEmailTemplateParams] = deriveCodecWithDefaults
}

final case class GetEmailTemplateResponse(
  templates: Seq[UserEmailTemplate]
)

object GetEmailTemplateResponse {
  given Codec.AsObject[GetEmailTemplateResponse] = deriveCodecWithDefaults
}

final case class UserEmailTemplate(
  fundSubEvent: FundSubEvent,
  subject: String = "",
  body: String = "",
  primaryCTA: String = "",
  lastEditedAt: Option[Instant] = None,
  ccEmailAddresses: Seq[EmailAddress] = Seq.empty
) {

  def toEmailTemplateMessage: EmailTemplateMessage = EmailTemplateMessage(
    event = fundSubEvent,
    subject = subject,
    body = Some(EmailModel.Body(html = Some(body))),
    primaryCTA = primaryCTA,
    updatedAt = lastEditedAt,
    ccEmailAddresses = ccEmailAddresses
  )

  def isInvalid: Boolean = subject.trim.isEmpty || primaryCTA.trim.isEmpty || body.trim.isEmpty
}

object UserEmailTemplate {
  given Codec.AsObject[UserEmailTemplate] = deriveCodecWithDefaults

  def fromEmailTemplateMessage(message: EmailTemplateMessage): UserEmailTemplate = UserEmailTemplate(
    fundSubEvent = message.event,
    subject = message.subject,
    body = message.body.flatMap(_.html).fold[String]("")(identity),
    primaryCTA = message.primaryCTA,
    lastEditedAt = message.updatedAt,
    ccEmailAddresses = message.ccEmailAddresses
  )

}

final case class NameEmailInfo(
  email: String = "",
  firstName: String = "",
  lastName: String = "",
  id: String = "", // Use to identify this name email info
  skipInvitationEmail: Boolean = false,
  enableSSO: Boolean = false
) {

  val fullName: String = s"$firstName $lastName".trim

  def toContactData: ContactData =
    ContactData(
      firstName,
      lastName,
      email,
      Seq.empty
    )

}

object NameEmailInfo {
  given Codec.AsObject[NameEmailInfo] = deriveCodecWithDefaults
}

final case class LpInfoId(value: String) derives CanEqual

object LpInfoId {
  given Codec.AsObject[LpInfoId] = deriveCodecWithDefaults
  private val valueLength = 10
  def generate: LpInfoId = LpInfoId(Random.alphanumeric.take(valueLength).mkString)
}

final case class ImportFromFundData(
  investmentEntityId: FundDataInvestmentEntityId,
  prefillFormData: Boolean,
  autoPrefillDocsOption: AutoPrefillDocsOption = AutoPrefillDocsOption.DoNotAutoPrefill
)

object ImportFromFundData {
  given Codec.AsObject[ImportFromFundData] = deriveCodecWithDefaults

  enum AutoPrefillDocsOption {
    case DoNotAutoPrefill
    case AutoMarkDocsAsProvided
    case AutoShareDocsWithLp
  }

  given Codec[AutoPrefillDocsOption] = deriveEnumCodec
}

final case class ImportFromFundDataFirm(
  firmId: FundDataFirmId,
  lpEmailTemplate: Option[UserEmailTemplate] = None,
  collaboratorEmailTemplate: Option[UserEmailTemplate] = None
)

object ImportFromFundDataFirm {
  given Codec.AsObject[ImportFromFundDataFirm] = deriveCodecWithDefaults
}

final case class FundSubLpInfo(
  lpInfoId: LpInfoId = LpInfoId.generate,
  lpContact: NameEmailInfo,
  collaboratorContacts: Seq[NameEmailInfo] = Seq.empty,
  lpEmailTemplate: Option[UserEmailTemplate] = None,
  firmName: String = "",
  customId: String = "",
  importInvestorId: String = "",
  lpIdToCopyFormDataOpt: Option[FundSubLpId] = None,
  initialFormData: Option[Either[Map[AliasId, Json], GaiaState]] = None,
  expectedCommitment: String = "",
  tagNames: Seq[String] = Seq.empty,
  prefillFromLp: Option[FundSubLpId] = None,
  dataImportItemId: Option[FundSubDataImportItemId] = None,
  lpIdToCopyInvestorGroupOpt: Option[FundSubLpId] = None,
  docTypesToMarkAsProvided: Seq[String] = Seq.empty,
  sharedDocumentsPerDocType: Map[String, List[FileId]] = Map.empty,
  importFromFundData: Option[ImportFromFundData] = None,
  metadata: Map[String, String] = Map.empty
)

object FundSubLpInfo {
  given Codec.AsObject[FundSubLpInfo] = deriveCodecWithDefaults
  given Codec.AsObject[GaiaState] = deriveCodecWithDefaults
}

final case class FundSubLpInvitationInfo(
  lpInfo: FundSubLpInfo,
  closeIdOpt: Option[FundSubCloseId],
  investorGroupIdOpt: Option[FundSubInvestorGroupId],
  lpAttachedDocs: Seq[FileId]
)

object FundSubLpInvitationInfo {
  given Codec.AsObject[FundSubLpInvitationInfo] = deriveCodecWithDefaults
}

final case class AdvisorCreateOrderInfo(
  orderInfoId: LpInfoId = LpInfoId.generate,
  investmentEntityName: String,
  advisorUserId: UserId
)

object AdvisorCreateOrderInfo {
  given Codec.AsObject[AdvisorCreateOrderInfo] = deriveCodecWithDefaults
}

final case class LpSimpleInfo(
  fundSubLpId: FundSubLpId,
  displayName: String,
  mainLpId: UserId,
  collaborators: Seq[UserId]
)

object LpSimpleInfo {
  given Codec.AsObject[LpSimpleInfo] = deriveCodecWithDefaults
}

final case class AddFundSubMainLpsParams(
  fundSubId: FundSubId,
  lpsToInvite: Seq[FundSubLpInfo],
  fundSubCloseIdToAdd: Option[FundSubCloseId] = None,
  attachedDocs: Seq[FileId] = Seq.empty,
  lpOrderType: LpOrderType = LpOrderType.NormalOrder,
  investorGroupIdOpt: Option[FundSubInvestorGroupId] = None,
  importFromFundDataFirm: Option[ImportFromFundDataFirm] = None
) {

  def toParamsWithoutJointInfo: AddFundSubMainLpsWithoutJointInfoParams = {
    AddFundSubMainLpsWithoutJointInfoParams(
      fundSubId,
      lpsToInvite.map { info =>
        FundSubLpInvitationInfo(
          lpInfo = info,
          closeIdOpt = fundSubCloseIdToAdd,
          investorGroupIdOpt = investorGroupIdOpt,
          lpAttachedDocs = Seq.empty
        )
      },
      lpOrderType,
      sharedAttachedDocs = attachedDocs,
      importFromFundDataFirm = importFromFundDataFirm
    )
  }

}

object AddFundSubMainLpsParams {
  given Codec.AsObject[AddFundSubMainLpsParams] = deriveCodecWithDefaults
}

final case class AddFundSubMainLpsWithoutJointInfoParams(
  fundSubId: FundSubId,
  lpsToInvite: Seq[FundSubLpInvitationInfo],
  lpOrderType: LpOrderType,
  sharedAttachedDocs: Seq[FileId],
  importFromFundDataFirm: Option[ImportFromFundDataFirm] = None
)

object AddFundSubMainLpsWithoutJointInfoParams {
  given Codec.AsObject[AddFundSubMainLpsWithoutJointInfoParams] = deriveCodecWithDefaults
}

final case class AddFundSubLpCollaboratorsParams(
  lpId: FundSubLpId,
  collaborators: Seq[NameEmailInfo],
  emailTemplate: Option[UserEmailTemplate] = None,
  enableSSO: Boolean = false
)

object AddFundSubLpCollaboratorsParams {
  given Codec.AsObject[AddFundSubLpCollaboratorsParams] = deriveCodecWithDefaults
}

final case class FundManagerAccessLpSubscriptionParams(
  lpId: FundSubLpId
)

object FundManagerAccessLpSubscriptionParams {
  given Codec.AsObject[FundManagerAccessLpSubscriptionParams] = deriveCodecWithDefaults
}

final case class AddFundSubLpsResponse(
  results: Seq[(LpInfoId, AddFundSubLpStatus)]
) {

  lazy val succeedLps: Seq[(LpInfoId, FundSubLpId)] = results.collect {
    case (lpInfoId, AddFundSubLpStatus.Succeed(lpId)) => lpInfoId -> lpId
  }

  lazy val failedLps: Seq[(LpInfoId, Exception)] = results.collect { case (lpInfoId, AddFundSubLpStatus.Failed(err)) =>
    lpInfoId -> new Exception(err)
  }

  lazy val lpIds: Seq[FundSubLpId] = succeedLps.map(_._2)

}

object AddFundSubLpsResponse {
  given Codec.AsObject[AddFundSubLpsResponse] = deriveCodecWithDefaults
}

sealed trait AddFundSubLpStatus derives CanEqual

object AddFundSubLpStatus {

  given Codec.AsObject[AddFundSubLpStatus] = deriveCodecWithDefaults

  case class Succeed(lpId: FundSubLpId) extends AddFundSubLpStatus

  object Succeed {
    given Codec.AsObject[Succeed] = deriveCodecWithDefaults
  }

  case class Failed(err: String) extends AddFundSubLpStatus

  object Failed {
    given Codec.AsObject[Failed] = deriveCodecWithDefaults
  }

}

final case class RemoveLpCollaboratorParams(
  lpId: FundSubLpId,
  toRemoveCollaborator: UserId,
  notifyRemovedCollaborator: Boolean
)

object RemoveLpCollaboratorParams {
  given Codec.AsObject[RemoveLpCollaboratorParams] = deriveCodecWithDefaults
}

final case class PromoteCollaboratorToLpRoleParams(
  fundSubLpId: FundSubLpId,
  collaboratorId: UserId
)

object PromoteCollaboratorToLpRoleParams {
  given Codec.AsObject[PromoteCollaboratorToLpRoleParams] = deriveCodecWithDefaults
}

final case class RevokeFundSubLpParams(
  lpId: FundSubLpId,
  notifyRemovedLp: Boolean = false
)

object RevokeFundSubLpParams {
  given Codec.AsObject[RevokeFundSubLpParams] = deriveCodecWithDefaults
}

final case class RestoreFundSubLpParams(
  lpId: FundSubLpId
)

object RestoreFundSubLpParams {
  given Codec.AsObject[RestoreFundSubLpParams] = deriveCodecWithDefaults
}

final case class ResendLpInvitationParams(
  lps: List[ResendLpInvitationParams.LpInfo],
  lpEmailTemplate: Option[UserEmailTemplate] = None,
  attachedDocs: Seq[FileId] = Seq.empty
)

object ResendLpInvitationParams {
  given Codec.AsObject[ResendLpInvitationParams] = deriveCodecWithDefaults

  final case class LpInfo(
    lpId: FundSubLpId,
    recipients: List[UserId]
  )

  object LpInfo {
    given Codec.AsObject[LpInfo] = deriveCodecWithDefaults
  }

}

final case class ResendLpInvitationResponse(
  result: Map[FundSubLpId, ResendLpInvitationResponse.ResendLpInvitationStatus]
)

object ResendLpInvitationResponse {
  given Codec.AsObject[ResendLpInvitationResponse] = deriveCodecWithDefaults

  sealed trait ResendLpInvitationStatus derives CanEqual

  object ResendLpInvitationStatus {
    case object Succeed extends ResendLpInvitationStatus

    case object Failed extends ResendLpInvitationStatus

    given Codec.AsObject[ResendLpInvitationStatus] = deriveCodecWithDefaults
    given Codec.AsObject[Succeed.type] = deriveCodecWithDefaults
    given Codec.AsObject[Failed.type] = deriveCodecWithDefaults
  }

}

final case class RemindLpCompleteFormParams(
  lps: List[RemindLpCompleteFormParams.LpInfo],
  remindEmailTemplate: Option[UserEmailTemplate] = None,
  attachedDocs: Seq[FileId] = Seq.empty
)

object RemindLpCompleteFormParams {

  given Codec.AsObject[RemindLpCompleteFormParams] = deriveCodecWithDefaults

  final case class LpInfo(
    lpId: FundSubLpId,
    receivers: List[UserId]
  )

  object LpInfo {
    given Codec.AsObject[LpInfo] = deriveCodecWithDefaults
  }

}

final case class RemindLpCompleteFormResponse(
  result: Map[FundSubLpId, RemindLpCompleteFormResponse.RemindLpCompleteFormStatus]
)

object RemindLpCompleteFormResponse {

  given Codec.AsObject[RemindLpCompleteFormResponse] = deriveCodecWithDefaults

  sealed trait RemindLpCompleteFormStatus derives CanEqual

  object RemindLpCompleteFormStatus {
    case object Succeed extends RemindLpCompleteFormStatus

    case object Failed extends RemindLpCompleteFormStatus

    given Codec.AsObject[RemindLpCompleteFormStatus] = deriveCodecWithDefaults
    given Codec.AsObject[Succeed.type] = deriveCodecWithDefaults
    given Codec.AsObject[Failed.type] = deriveCodecWithDefaults
  }

}

final case class RequestSupportingDocParams(
  lpId: FundSubLpId,
  requestedDocNames: Seq[String],
  message: String
)

object RequestSupportingDocParams {
  given Codec.AsObject[RequestSupportingDocParams] = deriveCodecWithDefaults
}

final case class FundSubLpJoinParams(
  lpId: FundSubLpId
)

object FundSubLpJoinParams {
  given Codec.AsObject[FundSubLpJoinParams] = deriveCodecWithDefaults
}

final case class FundSubGetFormParams(
  lpId: FundSubLpId
)

object FundSubGetFormParams {
  given Codec.AsObject[FundSubGetFormParams] = deriveCodecWithDefaults
}

final case class DynamicFormInfo(
  formName: String,
  section: DynamicFormSection,
  filesWithNames: Seq[(FileId, String)],
  formSetting: Option[DynamicFormSetting],
  embeddedFiles: Map[FileId, String]
)

object DynamicFormInfo {
  given Codec.AsObject[DynamicFormInfo] = deriveCodecWithDefaults
}

final case class GaiaFormModel(
  formName: String,
  formData: FormData,
  formVersionId: FormVersionId
) derives CanEqual

object GaiaFormModel {
  given Codec.AsObject[GaiaFormModel] = deriveCodecWithDefaults
}

final case class FundSubGetFormResponse(
  form: Either[DynamicFormInfo, GaiaFormModel]
)

object FundSubGetFormResponse {
  given Codec.AsObject[FundSubGetFormResponse] = deriveCodecWithDefaults
}

final case class GetTaxFormDataParams(
  supportingDocId: FundSubSupportingDocId
)

object GetTaxFormDataParams {
  given Codec.AsObject[GetTaxFormDataParams] = deriveCodecWithDefaults
}

final case class TaxDynamicFormWithData(
  form: DynamicFormInfo,
  values: Map[String, String]
)

object TaxDynamicFormWithData {
  given Codec.AsObject[TaxDynamicFormWithData] = deriveCodecWithDefaults
}

final case class TaxFormWithData(
  data: Either[TaxDynamicFormWithData, GaiaFormModelWithData]
)

object TaxFormWithData {
  given Codec.AsObject[TaxFormWithData] = deriveCodecWithDefaults
}

final case class GetTaxFormDataResponse(
  taxFormDataOrTaxFormsList: Either[TaxFormWithData, List[TaxFormInfo]]
)

object GetTaxFormDataResponse {
  given Codec.AsObject[GetTaxFormDataResponse] = deriveCodecWithDefaults
}

final case class GetFormDataParams(
  lpId: FundSubLpId
)

object GetFormDataParams {
  given Codec.AsObject[GetFormDataParams] = deriveCodecWithDefaults
}

final case class GetFormDataResponse(
  data: FormStateData
)

object GetFormDataResponse {
  given Codec.AsObject[GetFormDataResponse] = deriveCodecWithDefaults
}

final case class FundSubLpSubmitParams(
  lpId: FundSubLpId,
  signedDocs: Seq[FundSubFile],
  submittedAmounts: Map[InvestmentFundId, Option[MoneyMessage]] = Map.empty
)

object FundSubLpSubmitParams {
  given Codec.AsObject[FundSubLpSubmitParams] = deriveCodecWithDefaults
}

final case class LpSignAndSubmitTaxFormParams(
  supportingDocId: FundSubSupportingDocId,
  signingData: Seq[(FileId, DocumentSignatureMessage)]
)

object LpSignAndSubmitTaxFormParams {
  given Codec.AsObject[LpSignAndSubmitTaxFormParams] = deriveCodecWithDefaults
}

final case class GaiaFormModelWithData(
  form: GaiaFormModel,
  data: GaiaState
)

object GaiaFormModelWithData {
  given Codec.AsObject[GaiaFormModelWithData] = deriveCodecWithDefaults
}

final case class GaiaFormDataWithId(
  formId: FundSubLpFormVersionId,
  data: GaiaState,
  lastImportDataSource: FormDataSource
)

object GaiaFormDataWithId {
  given Codec.AsObject[GaiaFormDataWithId] = deriveCodecWithDefaults
}

final case class FormStateData(
  state: Either[DynamicFormData, GaiaFormDataWithId]
)

object FormStateData {
  given Codec.AsObject[FormStateData] = deriveCodecWithDefaults
}

final case class SaveTaxFormValueParams(
  supportingDocId: FundSubSupportingDocId,
  data: Either[Map[String, String], GaiaState], // Either old or new form data
  formVersionIdOpt: Option[FormVersionId] // Form version that is used to produce the data, used for new form only
)

object SaveTaxFormValueParams {
  given Codec.AsObject[SaveTaxFormValueParams] = deriveCodecWithDefaults
}

final case class FundSubLpSaveFormParams(
  lpId: FundSubLpId,
  data: Either[Map[String, String], GaiaState], // Either old or new form data
  formVersionIdOpt: Option[FormVersionId] // Form version that is used to produce the data, used for new form only
)

object FundSubLpSaveFormParams {
  given Codec.AsObject[FundSubLpSaveFormParams] = deriveCodecWithDefaults
}

final case class RequestLpChangeParams(
  lpId: FundSubLpId,
  messageOpt: Option[String],
  refillFormRequired: Boolean = false,
  sendNotificationEmail: Boolean = true
)

object RequestLpChangeParams {
  given Codec.AsObject[RequestLpChangeParams] = deriveCodecWithDefaults
}

final case class FundSubLpViewSectionParams(
  lpId: FundSubLpId,
  sectionIds: Seq[String]
)

object FundSubLpViewSectionParams {
  given Codec.AsObject[FundSubLpViewSectionParams] = deriveCodecWithDefaults
}

final case class UpdateInvestorFormSettingParams(
  lpId: FundSubLpId,
  investorFormSetting: InvestorFormSetting
)

object UpdateInvestorFormSettingParams {
  given Codec.AsObject[UpdateInvestorFormSettingParams] = deriveCodecWithDefaults
}

final case class FundSubLpViewPageParams(
  lpId: FundSubLpId,
  pageType: FundSubLpViewPageParams.LpViewPageType
)

object FundSubLpViewPageParams {

  given Codec.AsObject[FundSubLpViewPageParams] = deriveCodecWithDefaults

  sealed trait LpViewPageType derives CanEqual

  object LpViewPageType {
    case object FormPage extends LpViewPageType
    case object SubmissionPage extends LpViewPageType
    case object DashboardPage extends LpViewPageType

    given Codec.AsObject[LpViewPageType] = deriveCodecWithDefaults
    given Codec.AsObject[FormPage.type] = deriveCodecWithDefaults
    given Codec.AsObject[SubmissionPage.type] = deriveCodecWithDefaults
    given Codec.AsObject[DashboardPage.type] = deriveCodecWithDefaults
  }

}

final case class FundSubLpGoBackToFormParams(lpId: FundSubLpId)

object FundSubLpGoBackToFormParams {
  given Codec.AsObject[FundSubLpGoBackToFormParams] = deriveCodecWithDefaults
}

final case class FundSubLpGoToSignatureParams(lpId: FundSubLpId)

object FundSubLpGoToSignatureParams {
  given Codec.AsObject[FundSubLpGoToSignatureParams] = deriveCodecWithDefaults
}

final case class UploadCounterSignParams(
  lpIds: Seq[FundSubLpId],
  counterSignedDocs: Seq[FundSubFile],
  acceptedCommitments: Map[InvestmentFundId, Option[MoneyMessage]] = Map.empty
)

object UploadCounterSignParams {
  given Codec.AsObject[UploadCounterSignParams] = deriveCodecWithDefaults
}

final case class UploadCounterSignResponse(
  statuses: Map[FundSubLpId, UploadCounterSignResponse.Status]
)

object UploadCounterSignResponse {

  given Codec.AsObject[UploadCounterSignResponse] = deriveCodecWithDefaults

  sealed trait Status derives CanEqual

  object Status {
    case object Succeed extends Status
    case object Failed extends Status

    given Codec.AsObject[Status] = deriveCodecWithDefaults
    given Codec.AsObject[Succeed.type] = deriveCodecWithDefaults
    given Codec.AsObject[Failed.type] = deriveCodecWithDefaults
  }

}

final case class UploadAndDistributeCountersignRestrictedFlowParams(
  lpId: FundSubLpId,
  counterSignedDocs: Seq[FundSubFile],
  acceptedCommitments: Map[InvestmentFundId, Option[MoneyMessage]] = Map.empty,
  shouldDistributeDoc: Boolean
)

object UploadAndDistributeCountersignRestrictedFlowParams {
  given Codec.AsObject[UploadAndDistributeCountersignRestrictedFlowParams] = deriveCodecWithDefaults
}

final case class CounterEsignParams(
  lpId: FundSubLpId,
  signingData: Seq[(FileId, DocumentSignatureMessage)],
  fileIds: Seq[FileId],
  isBatchCountersigning: Boolean,
  preparedBlocks: Map[FileId, DocumentSignatureTemplateMessage] = Map.empty,
  refDocs: Seq[FileId] = Seq.empty,
  docusignESignatureOptionParamsOpt: Option[DocusignESignatureOptionParams] = None
)

object CounterEsignParams {
  given Codec.AsObject[CounterEsignParams] = deriveCodecWithDefaults
}

final case class CounterEsignResponse(
  requestId: SignatureRequestId,
  signedFiles: Seq[FileId]
)

object CounterEsignResponse {
  given Codec.AsObject[CounterEsignResponse] = deriveCodecWithDefaults
}

final case class RemoveCounterSignParams(
  lpId: FundSubLpId,
  removedDocs: Seq[FundSubFile]
)

object RemoveCounterSignParams {
  given Codec.AsObject[RemoveCounterSignParams] = deriveCodecWithDefaults
}

final case class DistributeCounterSignParams(
  lpIds: Seq[FundSubLpId],
  emailTemplate: Option[UserEmailTemplate] = None
)

object DistributeCounterSignParams {
  given Codec.AsObject[DistributeCounterSignParams] = deriveCodecWithDefaults
}

sealed trait DistributeCounterSignStatus derives CanEqual

object DistributeCounterSignStatus {
  case object Succeed extends DistributeCounterSignStatus

  case object Failed extends DistributeCounterSignStatus

  given Codec.AsObject[DistributeCounterSignStatus] = deriveCodecWithDefaults
  given Codec.AsObject[Succeed.type] = deriveCodecWithDefaults
  given Codec.AsObject[Failed.type] = deriveCodecWithDefaults
}

final case class DistributeCounterSignResponse(
  statuses: Map[FundSubLpId, DistributeCounterSignStatus]
)

object DistributeCounterSignResponse {
  given Codec.AsObject[DistributeCounterSignResponse] = deriveCodecWithDefaults
}

final case class MarkSubscriptionAsCompleteParams(
  lpId: FundSubLpId,
  acceptedAmounts: Map[InvestmentFundId, Option[MoneyMessage]] = Map.empty,
  shouldNotifyLp: Boolean = true,
  emailTemplateOpt: Option[UserEmailTemplate] = None
)

object MarkSubscriptionAsCompleteParams {
  given Codec.AsObject[MarkSubscriptionAsCompleteParams] = deriveCodecWithDefaults
}

final case class RequestSubdocSignatureParams(
  lpId: FundSubLpId,
  signers: Seq[CreateRequestSignerData],
  fileIds: Seq[FileId],
  message: String
)

object RequestSubdocSignatureParams {
  given Codec.AsObject[RequestSubdocSignatureParams] = deriveCodecWithDefaults
}

final case class LpSelfSignFormParams(
  lpId: FundSubLpId,
  signingData: Seq[(FileId, DocumentSignatureMessage)]
)

object LpSelfSignFormParams {
  given Codec.AsObject[LpSelfSignFormParams] = deriveCodecWithDefaults
}

final case class LpUploadSignedDocParams(
  lpId: FundSubLpId,
  files: Seq[FileId]
)

object LpUploadSignedDocParams {
  given Codec.AsObject[LpUploadSignedDocParams] = deriveCodecWithDefaults
}

final case class SignSubDocRequestParams(
  lpId: FundSubLpId,
  lpSignDataSeq: Seq[SignData]
)

object SignSubDocRequestParams {
  given Codec.AsObject[SignSubDocRequestParams] = deriveCodecWithDefaults
}

final case class ReassignSignatureRequestParamsRestrictedFlow(
  lpId: FundSubLpId,
  newRecipientName: String,
  newRecipientEmail: String,
  reason: String
)

object ReassignSignatureRequestParamsRestrictedFlow {
  given Codec.AsObject[ReassignSignatureRequestParamsRestrictedFlow] = deriveCodecWithDefaults
}

final case class CancelSigningParams(
  lpId: FundSubLpId
)

object CancelSigningParams {
  given Codec.AsObject[CancelSigningParams] = deriveCodecWithDefaults
}

final case class CancelSignatureRequestParamsRestrictedFlow(
  lpId: FundSubLpId
)

object CancelSignatureRequestParamsRestrictedFlow {
  given Codec.AsObject[CancelSignatureRequestParamsRestrictedFlow] = deriveCodecWithDefaults
}

final case class MarkSubDocRequestAsCompletedParams(
  lpId: FundSubLpId,
  shouldNotify: Boolean
)

object MarkSubDocRequestAsCompletedParams {
  given Codec.AsObject[MarkSubDocRequestAsCompletedParams] = deriveCodecWithDefaults
}

final case class GenerateDataForSigningParams(
  lpId: FundSubLpId
)

object GenerateDataForSigningParams {
  given Codec.AsObject[GenerateDataForSigningParams] = deriveCodecWithDefaults
}

final case class GetSubDocSigningTypeParams(
  lpId: FundSubLpId
)

object GetSubDocSigningTypeParams {
  given Codec.AsObject[GetSubDocSigningTypeParams] = deriveCodecWithDefaults
}

final case class GetSubDocSigningTypeResponse(
  signingTypes: Set[SigningType],
  signingGuidelineOpt: Option[SigningGuideline]
)

object GetSubDocSigningTypeResponse {
  given Codec.AsObject[GetSubDocSigningTypeResponse] = deriveCodecWithDefaults
}

final case class GeneratePdfParams(
  lpId: FundSubLpId
)

object GeneratePdfParams {
  given Codec.AsObject[GeneratePdfParams] = deriveCodecWithDefaults
}

final case class GeneratePdfResponse(
  files: Seq[FileId]
)

object GeneratePdfResponse {
  given Codec.AsObject[GeneratePdfResponse] = deriveCodecWithDefaults
}

final case class GenerateTaxPdfParams(
  supportingDocId: FundSubSupportingDocId
)

object GenerateTaxPdfParams {
  given Codec.AsObject[GenerateTaxPdfParams] = deriveCodecWithDefaults
}

final case class TaxPdfToSign(
  file: FileId,
  name: String,
  signatureTemplate: DocumentSignatureTemplateMessage
)

object TaxPdfToSign {
  given Codec.AsObject[TaxPdfToSign] = deriveCodecWithDefaults
}

final case class SelectTaxFormParams(
  lpId: FundSubLpId,
  supportingDocId: FundSubSupportingDocId,
  taxFormId: Either[DynamicFormId, FormVersionId]
)

object SelectTaxFormParams {
  given Codec.AsObject[SelectTaxFormParams] = deriveCodecWithDefaults
}

final case class GetTaxFormsMappingParams(
  lpId: FundSubLpId
)

object GetTaxFormsMappingParams {
  given Codec.AsObject[GetTaxFormsMappingParams] = deriveCodecWithDefaults
}

final case class TaxFormInfo(
  name: String,
  formId: Either[DynamicFormId, FormVersionId]
)

object TaxFormInfo {
  given Codec.AsObject[TaxFormInfo] = deriveCodecWithDefaults
}

final case class FormReferenceInfo(
  groupName: String,
  name: String,
  formId: Either[DynamicFormId, FormVersionId]
) derives CanEqual {
  lazy val formIdString: String = formId.fold(_.idString, _.idString)
}

object FormReferenceInfo {
  given Codec.AsObject[FormReferenceInfo] = deriveCodecWithDefaults
}

final case class SupportingDocWithTaxFormInfo(
  name: String,
  relatedTaxForms: List[TaxFormInfo]
)

object SupportingDocWithTaxFormInfo {
  given Codec.AsObject[SupportingDocWithTaxFormInfo] = deriveCodecWithDefaults
}

final case class GetTaxFormsMappingResponse(
  supportingDocs: List[SupportingDocWithTaxFormInfo]
)

object GetTaxFormsMappingResponse {
  given Codec.AsObject[GetTaxFormsMappingResponse] = deriveCodecWithDefaults
}

final case class SelectTaxFormResponse(
  formData: FormStateData
)

object SelectTaxFormResponse {
  given Codec.AsObject[SelectTaxFormResponse] = deriveCodecWithDefaults
}

final case class GenerateTaxPdfResponse(
  files: Seq[TaxPdfToSign]
)

object GenerateTaxPdfResponse {
  given Codec.AsObject[GenerateTaxPdfResponse] = deriveCodecWithDefaults
}

final case class UploadReferenceDocsParams(
  lpId: FundSubLpId,
  files: Seq[FileId],
  messageOpt: Option[String],
  ccRecipients: Seq[EmailAddress] = Seq.empty
)

object UploadReferenceDocsParams {
  given Codec.AsObject[UploadReferenceDocsParams] = deriveCodecWithDefaults
}

final case class RemoveReferenceDocsParams(
  lpId: FundSubLpId,
  fileId: FileId
)

object RemoveReferenceDocsParams {
  given Codec.AsObject[RemoveReferenceDocsParams] = deriveCodecWithDefaults
}

final case class UpdateLpInformationParams(
  fundSubLpId: FundSubLpId,
  note: Option[String] = None, // None will not modify current note
  firmName: Option[String] = None,
  customId: Option[String] = None
)

object UpdateLpInformationParams {
  given Codec.AsObject[UpdateLpInformationParams] = deriveCodecWithDefaults
}

final case class GetAdminTestFormDataParams(fundSubId: FundSubId)

object GetAdminTestFormDataParams {
  given Codec.AsObject[GetAdminTestFormDataParams] = deriveCodecWithDefaults
}

final case class GetAdminTestFormDataResponse(
  form: Either[DynamicFormSection, GaiaFormModel],
  formName: String,
  referenceFiles: Seq[FileId],
  formSetting: Option[DynamicFormSetting],
  embeddedFormFiles: List[(FileId, String)],
  formData: Either[FormFillData, GaiaState],
  formUpdatedAt: Option[Instant]
)

object GetAdminTestFormDataResponse {
  given Codec.AsObject[GetAdminTestFormDataResponse] = deriveCodecWithDefaults
}

final case class SaveAdminTestFormDataParams(
  fundSubId: FundSubId,
  values: Map[String, String],
  viewedSections: List[String]
)

object SaveAdminTestFormDataParams {
  given Codec.AsObject[SaveAdminTestFormDataParams] = deriveCodecWithDefaults
}

final case class GetAccessToProtectedLinkParams(
  fundSubId: FundSubId,
  linkId: ProtectedLinkId
)

object GetAccessToProtectedLinkParams {
  given Codec.AsObject[GetAccessToProtectedLinkParams] = deriveCodecWithDefaults
}

final case class GetAccessToProtectedLinkResponse(
  redirectTarget: GetAccessToProtectedLinkResponse.RedirectTarget,
  accountEnforcementToken: String
)

object GetAccessToProtectedLinkResponse {

  given Codec.AsObject[GetAccessToProtectedLinkResponse] = deriveCodecWithDefaults

  sealed trait RedirectTarget derives CanEqual

  object RedirectTarget {

    given Codec.AsObject[RedirectTarget] = deriveCodecWithDefaults

    final case class FundDashboard(fundSubId: FundSubId) extends RedirectTarget

    object FundDashboard {
      given Codec.AsObject[FundDashboard] = deriveCodecWithDefaults
    }

    final case class LpPage(lpId: FundSubLpId) extends RedirectTarget

    object LpPage {
      given Codec.AsObject[LpPage] = deriveCodecWithDefaults
    }

    final case class FundClosed(fundSubId: FundSubId) extends RedirectTarget

    object FundClosed {
      given Codec.AsObject[FundClosed] = deriveCodecWithDefaults
    }

    case object NoAccess extends RedirectTarget
    given Codec.AsObject[NoAccess.type] = deriveCodecWithDefaults

  }

}

final case class SignupViaProtectedLinkParams(
  linkId: ProtectedLinkId,
  captchaResponse: CaptchaResponse,
  publicPasswordPassedToken: Option[String],
  disclaimerAcceptanceToken: Option[String],
  firstName: String,
  lastName: String,
  email: String,
  redirectUrl: Option[String]
)

object SignupViaProtectedLinkParams {
  given Codec.AsObject[SignupViaProtectedLinkParams] = deriveCodecWithDefaults
}

final case class GetFundAdminContactParams(
  fundSubLpId: FundSubLpId
)

object GetFundAdminContactParams {
  given Codec.AsObject[GetFundAdminContactParams] = deriveCodecWithDefaults
}

final case class GetFundAdminContactResponse(
  fundManagers: Set[UserId]
)

object GetFundAdminContactResponse {
  given Codec.AsObject[GetFundAdminContactResponse] = deriveCodecWithDefaults
}

final case class GetSignaturePagesDownloadUrlParams(
  lpId: FundSubLpId
)

object GetSignaturePagesDownloadUrlParams {
  given Codec.AsObject[GetSignaturePagesDownloadUrlParams] = deriveCodecWithDefaults
}

final case class GetSignaturePagesDownloadUrlResponse(
  url: String
)

object GetSignaturePagesDownloadUrlResponse {
  given Codec.AsObject[GetSignaturePagesDownloadUrlResponse] = deriveCodecWithDefaults
}

final case class LpInvestInAdditionalEntityParams(
  lpId: FundSubLpId,
  firmName: String,
  collaborators: List[NameEmailInfo],
  duplicateFormData: Boolean = false
)

object LpInvestInAdditionalEntityParams {
  given Codec.AsObject[LpInvestInAdditionalEntityParams] = deriveCodecWithDefaults
}

final case class LpInvestInAdditionalEntityResponse(
  newLpId: FundSubLpId
)

object LpInvestInAdditionalEntityResponse {
  given Codec.AsObject[LpInvestInAdditionalEntityResponse] = deriveCodecWithDefaults
}

final case class LpInvestInAdditionalEntityFromMultipleLpsEmailLinkParams(
  linkId: FundSubSingleUserInvitationLinkId
)

object LpInvestInAdditionalEntityFromMultipleLpsEmailLinkParams {
  given Codec.AsObject[LpInvestInAdditionalEntityFromMultipleLpsEmailLinkParams] = deriveCodecWithDefaults
}

final case class LpInvestInAdditionalEntityFromMultipleLpsEmailLinkResp(
  result: LpInvestInAdditionalEntityFromMultipleLpsEmailLinkResp.LpInvestInAdditionalEntityResult
)

object LpInvestInAdditionalEntityFromMultipleLpsEmailLinkResp {
  given Codec.AsObject[LpInvestInAdditionalEntityFromMultipleLpsEmailLinkResp] = deriveCodecWithDefaults

  sealed trait LpInvestInAdditionalEntityResult derives CanEqual

  object LpInvestInAdditionalEntityResult {
    given Codec.AsObject[LpInvestInAdditionalEntityResult] = deriveCodecWithDefaults

    final case class Success(lpId: FundSubLpId) extends LpInvestInAdditionalEntityResult

    object Success {
      given Codec.AsObject[Success] = deriveCodecWithDefaults
    }

    case object RemovedLpsOrNoInvestorRole extends LpInvestInAdditionalEntityResult

    given Codec.AsObject[RemovedLpsOrNoInvestorRole.type] = deriveCodecWithDefaults
  }

}

final case class GetAllFirmNamesOfLpParams(
  lpId: FundSubLpId
)

object GetAllFirmNamesOfLpParams {
  given Codec.AsObject[GetAllFirmNamesOfLpParams] = deriveCodecWithDefaults
}

final case class GetAllFirmNamesOfLpResponse(
  firmNames: List[(FundSubLpId, String)]
)

object GetAllFirmNamesOfLpResponse {
  given Codec.AsObject[GetAllFirmNamesOfLpResponse] = deriveCodecWithDefaults
}

final case class UpdateFirmNameParams(
  fundSubLpId: FundSubLpId,
  firmName: String
)

object UpdateFirmNameParams {
  given Codec.AsObject[UpdateFirmNameParams] = deriveCodecWithDefaults
}

final case class UpdateInactiveLpSettingParams(
  fundSubId: FundSubId,
  enabled: Boolean,
  days: Int
)

object UpdateInactiveLpSettingParams {
  given Codec.AsObject[UpdateInactiveLpSettingParams] = deriveCodecWithDefaults
}

final case class UpdateFormCommentFundSettingSwitchParams(
  fundSubId: FundSubId,
  enableFormComment: Boolean
)

object UpdateFormCommentFundSettingSwitchParams {
  given Codec.AsObject[UpdateFormCommentFundSettingSwitchParams] = deriveCodecWithDefaults
}

final case class UpdateFormCommentInvestorDigestEmailSwitchParams(
  fundSubId: FundSubId,
  enableEmail: Boolean
)

object UpdateFormCommentInvestorDigestEmailSwitchParams {
  given Codec.AsObject[UpdateFormCommentInvestorDigestEmailSwitchParams] = deriveCodecWithDefaults
}

final case class UpdateFormCommentInvestorCanResolveSwitchParams(
  fundSubId: FundSubId,
  investorCanResolve: Boolean
)

object UpdateFormCommentInvestorCanResolveSwitchParams {
  given Codec.AsObject[UpdateFormCommentInvestorCanResolveSwitchParams] = deriveCodecWithDefaults
}

final case class UpdateFormCommentDigestEmailExceptionLpsParams(
  fundSubId: FundSubId,
  addedLps: Seq[FundSubLpId] = Seq.empty,
  removedLps: Seq[FundSubLpId] = Seq.empty
)

object UpdateFormCommentDigestEmailExceptionLpsParams {
  given Codec.AsObject[UpdateFormCommentDigestEmailExceptionLpsParams] = deriveCodecWithDefaults
}

final case class UpdateInactiveCommentSettingParams(
  fundSubId: FundSubId,
  setting: InactiveCommentSetting
)

object UpdateInactiveCommentSettingParams {
  given Codec.AsObject[UpdateInactiveCommentSettingParams] = deriveCodecWithDefaults
}

final case class GetFormCommentDigestEmailExceptionInfoParams(
  fundSubId: FundSubId
)

object GetFormCommentDigestEmailExceptionInfoParams {
  given Codec.AsObject[GetFormCommentDigestEmailExceptionInfoParams] = deriveCodecWithDefaults
}

final case class GetFormCommentDigestEmailExceptionInfoResponse(
  exceptionLps: Seq[LpSimpleInfo]
)

object GetFormCommentDigestEmailExceptionInfoResponse {
  given Codec.AsObject[GetFormCommentDigestEmailExceptionInfoResponse] = deriveCodecWithDefaults
}

final case class UpdateCustomFundIdSettingParams(
  fundSubId: FundSubId,
  isEnabled: Boolean,
  newCustomFundId: String
)

object UpdateCustomFundIdSettingParams {
  given Codec.AsObject[UpdateCustomFundIdSettingParams] = deriveCodecWithDefaults
}

final case class UpdateMarkAsNotApplicableParams(
  fundId: FundSubId,
  disabledMarkAsNotApplicable: Boolean
)

object UpdateMarkAsNotApplicableParams {
  given Codec.AsObject[UpdateMarkAsNotApplicableParams] = deriveCodecWithDefaults
}

final case class UpdateDisableFundContactInInvestorWorkspaceSettingParams(
  fundSubId: FundSubId,
  disableFundContactInInvestorWorkspace: Boolean
)

object UpdateDisableFundContactInInvestorWorkspaceSettingParams {
  given Codec.AsObject[UpdateDisableFundContactInInvestorWorkspaceSettingParams] = deriveCodecWithDefaults
}

final case class GetFundAdditionalTaxFormListsParams(
  lpId: FundSubLpId
)

object GetFundAdditionalTaxFormListsParams {
  given Codec.AsObject[GetFundAdditionalTaxFormListsParams] = deriveCodecWithDefaults
}

final case class GetFundAdditionalTaxFormListResponse(
  forms: List[TaxFormInfo]
)

object GetFundAdditionalTaxFormListResponse {
  given Codec.AsObject[GetFundAdditionalTaxFormListResponse] = deriveCodecWithDefaults
}

final case class AddAdditionalTaxFormParams(
  lpId: FundSubLpId,
  taxFormId: Either[DynamicFormId, FormVersionId]
)

object AddAdditionalTaxFormParams {
  given Codec.AsObject[AddAdditionalTaxFormParams] = deriveCodecWithDefaults
}

final case class AddAdditionalTaxFormResponse(
  supportingDocId: FundSubSupportingDocId
)

object AddAdditionalTaxFormResponse {
  given Codec.AsObject[AddAdditionalTaxFormResponse] = deriveCodecWithDefaults
}

final case class RemoveAdditionalTaxFormParams(
  id: FundSubSupportingDocId
)

object RemoveAdditionalTaxFormParams {
  given Codec.AsObject[RemoveAdditionalTaxFormParams] = deriveCodecWithDefaults
}

final case class UpdateFundNameParams(
  fundId: FundSubId,
  fundName: String
)

object UpdateFundNameParams {
  given Codec.AsObject[UpdateFundNameParams] = deriveCodecWithDefaults
}

final case class CheckReviewerPermissionResponse(
  isReviewer: Boolean,
  lpEmail: String
)

object CheckReviewerPermissionResponse {
  given Codec.AsObject[CheckReviewerPermissionResponse] = deriveCodecWithDefaults
}

final case class ActivateManualSubscriptionParams(
  lpId: FundSubLpId,
  viaDirectLink: Boolean = false
)

object ActivateManualSubscriptionParams {
  given Codec.AsObject[ActivateManualSubscriptionParams] = deriveCodecWithDefaults
}

final case class MergeLpDocumentsParams(
  lps: Seq[(FundSubLpId, Seq[FileId])]
)

object MergeLpDocumentsParams {
  given Codec.AsObject[MergeLpDocumentsParams] = deriveCodecWithDefaults
}

final case class MergeLpDocumentsResponse(
  results: Seq[MergeLpDocumentsResponse.MergeLpDocumentsResult]
)

object MergeLpDocumentsResponse {

  given Codec.AsObject[MergeLpDocumentsResponse] = deriveCodecWithDefaults

  final case class LpDocument(
    lpId: FundSubLpId,
    fileIds: Seq[FileId]
  )

  object LpDocument {
    given Codec.AsObject[LpDocument] = deriveCodecWithDefaults
  }

  sealed trait MergeLpDocumentsResult derives CanEqual {
    def lpId: FundSubLpId
  }

  object MergeLpDocumentsResult {

    given Codec.AsObject[MergeLpDocumentsResult] = deriveCodecWithDefaults

    case class Success(lpId: FundSubLpId, fileIds: Seq[FileId]) extends MergeLpDocumentsResult

    object Success {
      given Codec.AsObject[Success] = deriveCodecWithDefaults
    }

    sealed trait FundSubPdfBoxException derives CanEqual {
      def message: String
    }

    object FundSubPdfBoxException {
      given Codec.AsObject[FundSubPdfBoxException] = deriveCodecWithDefaults
    }

    final case class FundSubPdfBoxPasswordProtectedFilesException(
      override val message: String,
      passwordProtectedFileNames: Seq[String]
    ) extends FundSubPdfBoxException

    final case class FundSubPdfBoxServerException(
      override val message: String
    ) extends FundSubPdfBoxException

    case class Failure(
      lpId: FundSubLpId,
      lpRepresentativeName: String,
      lpEmail: String,
      exception: Option[FundSubPdfBoxException]
    ) extends MergeLpDocumentsResult

    object Failure {
      given Codec.AsObject[Failure] = deriveCodecWithDefaults
    }

    final case class FundSubMergePdfResponse(
      id: ServerlessRequestId,
      message: String,
      exception: Option[FundSubPdfBoxException]
    )

  }

}

final case class GetFundMemberRoleParams(
  lpId: FundSubLpId,
  userId: UserId
)

object GetFundMemberRoleParams {
  given Codec.AsObject[GetFundMemberRoleParams] = deriveCodecWithDefaults
}

sealed trait FundMemberRole derives CanEqual {
  def displayName: String
}

object FundMemberRole {
  given Codec.AsObject[FundMemberRole] = deriveCodecWithDefaults
}

final case class FundAdminRole(
  groupName: String
) extends FundMemberRole {
  override def displayName: String = groupName
}

object FundAdminRole {
  given Codec.AsObject[FundAdminRole] = deriveCodecWithDefaults
}

final case class InvestorRole(
  isMainInvestor: Boolean
) extends FundMemberRole {

  override def displayName: String = if (isMainInvestor) "Investor" else "Collaborator"
}

object InvestorRole {
  given Codec.AsObject[InvestorRole] = deriveCodecWithDefaults
}

final case class GetFundMemberRoleResponse(
  userInfo: UserInfo,
  fundMemberRoles: Seq[FundMemberRole]
)

object GetFundMemberRoleResponse {
  given Codec.AsObject[GetFundMemberRoleResponse] = deriveCodecWithDefaults
}

final case class GetLpReferenceDocsParams(
  lpId: FundSubLpId
)

object GetLpReferenceDocsParams {
  given Codec.AsObject[GetLpReferenceDocsParams] = deriveCodecWithDefaults
}

final case class GetLpReferenceDocsResp(
  sharedRefDocs: Seq[FileId],
  lpAttachedDocs: Seq[FileId]
)

object GetLpReferenceDocsResp {
  given Codec.AsObject[GetLpReferenceDocsResp] = deriveCodecWithDefaults
}

final case class GetFundSubEntityNameParams(
  fundSubId: FundSubId
)

object GetFundSubEntityNameParams {
  given Codec.AsObject[GetFundSubEntityNameParams] = deriveCodecWithDefaults
}

final case class GetFundSubEntityNameResp(
  entityName: String,
  entityAlias: String
)

object GetFundSubEntityNameResp {
  given Codec.AsObject[GetFundSubEntityNameResp] = deriveCodecWithDefaults
}

final case class FundSubBatchInvitationItemInfo(
  lpContact: Option[NameEmailInfo],
  collaboratorContacts: Seq[NameEmailInfo] = Seq.empty,
  firmName: String,
  customId: String = "",
  expectedCommitment: String = "",
  lpEmailTemplate: Option[UserEmailTemplate] = None,
  tagNames: Seq[String] = Seq.empty,
  prefillFromLp: Option[FundSubLpId] = None,
  closeIdOpt: Option[FundSubCloseId] = None,
  investorGroupIdOpt: Option[FundSubInvestorGroupId] = None,
  dataImportItemId: Option[FundSubDataImportItemId] = None,
  lpAttachedDocs: Seq[FileId] = Seq.empty,
  docTypesToMarkAsProvided: Seq[String] = Seq.empty,
  sharedDocumentsPerDocType: Map[String, List[FileId]] = Map.empty,
  importFromFundData: Option[ImportFromFundData] = None,
  metadata: Map[String, String] = Map.empty,
  advisorGroupIdOpt: Option[FundSubRiaGroupId] = None
)

object FundSubBatchInvitationItemInfo {
  given Codec.AsObject[FundSubBatchInvitationItemInfo] = deriveCodecWithDefaults
}

final case class FundSubBatchInvitationInfo(
  fundSubId: FundSubId,
  invitationItems: Seq[FundSubBatchInvitationItemInfo],
  actor: UserId,
  actionType: BatchActionType,
  closeId: Option[FundSubCloseId] = None,
  attachedDocs: Seq[FileId] = Seq.empty,
  orderType: LpOrderType = LpOrderType.NormalOrder,
  investorGroupIdOpt: Option[FundSubInvestorGroupId] = None,
  importFromFundDataFirm: Option[ImportFromFundDataFirm] = None
)

object FundSubBatchInvitationInfo {
  given Codec.AsObject[FundSubBatchInvitationInfo] = deriveCodecWithDefaults
}

final case class SucceededLp(
  fundSubLpId: FundSubLpId
)

object SucceededLp {
  given Codec.AsObject[SucceededLp] = deriveCodecWithDefaults
}
