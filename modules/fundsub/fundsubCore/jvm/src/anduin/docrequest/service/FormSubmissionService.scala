// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.docrequest.service

import io.circe.Json
import zio.{Task, ZIO}

import anduin.dms.DmsFeature.Public
import anduin.fdb.record.{DefaultCluster, FDBCluster, FDBCommonDatabase, FDBOperations, FDBRecordDatabase}
import anduin.dms.service.FileService
import anduin.docrequest.helper.{FormServiceHelper, PublicDataConversionHelper}
import anduin.docrequest.model.{FilledPdfInfo, FormSubmission}
import anduin.docrequest.recordlayer.{
  DocRequestOperations,
  DocRequestPermissionVerificationOperations,
  FormSubmissionServiceOperations
}
import anduin.docrequest.submission.form.FormSubmissionRecord
import anduin.documentcontent.pdf.FillPdfService
import anduin.endpoint.signature.SignatureProviderParams
import anduin.fdb.record.model.{RecordReadTask, RecordTask}
import anduin.forms.Form.FormNamespace
import anduin.forms.FormDataProtoConverter
import anduin.forms.engine.GaiaState
import anduin.forms.service.FormService
import anduin.fundsub.LpFormDataOperations
import anduin.fundsub.utils.FileServiceHelper
import anduin.greylin.{GreylinDataService, modelti, operation}
import anduin.id.docrequest.{DocRequestId, FormSubmissionId}
import anduin.id.form.{FormId, FormVersionId}
import anduin.id.fundsub.{FundSubLpFormVersionId, FundSubLpId}
import anduin.id.signature.SignatureRequestId
import anduin.model.common.user.UserId
import anduin.model.id.{FileId, FolderId}
import anduin.portaluser.ExecutiveAdmin
import anduin.protobuf.fundsub.lpformdata.{FieldValueMap, LpFormData}
import anduin.protobuf.signature.DocumentSignatureMessage
import anduin.service.{AuthenticatedRequestContext, GeneralServiceException}
import anduin.fundsub.signature.integration.FundSubSignatureIntegrationService.FundSubSignerParams
import anduin.fundsub.models.signature.FundSubSignatureRequestModels
import anduin.fundsub.models.signature.FundSubSignatureRequestModels.FundSubSignatureEnvelopeType.SingleDocTypeEnvelope
import anduin.fundsub.models.signature.FundSubSignatureRequestModels.FundSubSignatureRequestBasic
import anduin.fundsub.signature.integration.FundSubSignatureIntegrationService
import anduin.team.TeamServiceOperations
import anduin.team.flow.memberflow.TeamMemberStateStoreOperations
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FormSubmissionService(
  executiveAdmin: ExecutiveAdmin,
  fundSubSignatureIntegrationService: FundSubSignatureIntegrationService,
  greylinDataService: GreylinDataService
)(
  using val fileService: FileService,
  val formService: FormService,
  val fillPdfService: FillPdfService
) extends PublicDataConversionHelper {

  def createFormSubmission(
    docRequestId: DocRequestId,
    formVersionId: FormVersionId,
    name: String,
    initialFormValues: Map[String, Json],
    actor: UserId
  ): Task[FormSubmission] = {
    for {
      request <- FDBRecordDatabase.transact(
        FDBOperations[(DocRequestOperations, TeamMemberStateStoreOperations, TeamServiceOperations)].Production
      ) { case (docRequestOps, teamMemberStateOps, teamServiceOps) =>
        val permissionOps = DocRequestPermissionVerificationOperations(teamMemberStateOps, teamServiceOps)
        for {
          request <- docRequestOps.get(docRequestId)
          _ <- permissionOps.verifySubmitterOrReceiverTeamMembership(request, Option(actor))
        } yield request
      }
      userFormVersionId <- FormServiceHelper.setupLpFormVersion(
        formVersionId,
        initialFormValues,
        request.submitterTeamIds,
        actor,
        formService
      )
      formSubmissionRecord <- transact(
        _.createFormSubmission(
          docRequestId,
          userFormVersionId,
          name,
          Option(actor)
        )
      )
      _ <- ZIOUtils.traverseOption(getLpIdOpt(docRequestId)) { lpId =>
        greylinDataService.runUnit(
          operation.SubscriptionOrderSupportingFormOperations.insert(
            modelti
              .SubscriptionOrderSupportingForm(
                formSubmissionId = formSubmissionRecord.id,
                formDataId = formSubmissionRecord.formDataId,
                formId = formSubmissionRecord.originalFormId,
                subscriptionOrderId = lpId,
                docType = "",
                name = "",
                createdAt = formSubmissionRecord.createdAt,
                creator = formSubmissionRecord.creatorOpt
              )
              .withName(formSubmissionRecord.name)
          )
        )
      }
    } yield toFormSubmission(formSubmissionRecord)
  }

  def getFormSubmission(
    id: FormSubmissionId,
    actorOpt: Option[UserId]
  )(
    using FDBCluster
  ): Task[FormSubmission] = {
    for {
      formSubmissionRecord <- read(
        _.getFormSubmission(id, actorOpt)
      )
      formSubmission <- getFormSubmission(formSubmissionRecord)
    } yield formSubmission
  }

  def getByDocRequestId(
    requestId: DocRequestId,
    actorOpt: Option[UserId]
  ): Task[Seq[FormSubmission]] = {
    for {
      records <- transact(
        _.getByDocRequestId(requestId, actorOpt)
      )
      formSubmissions <- ZIO.foreach(records) { record =>
        getFormSubmission(record)
      }
    } yield formSubmissions
  }

  // TODO: @tuananhtd add Admin verification and check for signature request
  def getByFormId(
    formId: FormId,
    actorOpt: Option[UserId]
  ): Task[Seq[FormSubmission]] = {
    for {
      submissions <- transact(_.getByFormIdIgnorePermissionCheck(formId, actorOpt))
    } yield submissions.map(toFormSubmission(_))
  }

  def selectNewForm(
    id: FormSubmissionId,
    formVersionId: FormVersionId,
    name: String,
    initialFormValues: Map[String, Json],
    actor: UserId
  ): Task[FormSubmission] = {
    for {
      _ <- verifyNoPendingSignatures(id)
      docRequest <- verifySubmitterOrReceiverTeam(id, Option(actor))
      userFormVersionId <- FormServiceHelper.setupLpFormVersion(
        formVersionId,
        initialFormValues,
        docRequest.submitterTeamIds,
        actor,
        formService
      )
      formSubmissionRecord <- transact(
        _.selectNewForm(
          id,
          userFormVersionId = userFormVersionId,
          name,
          Option(actor)
        )
      )
      formSubmission <- getFormSubmission(formSubmissionRecord)
      _ <- greylinDataService.runUnit(
        operation.SubscriptionOrderSupportingFormOperations.update(id)(
          _.copy(
            formDataId = formSubmissionRecord.formDataId,
            formId = formSubmissionRecord.originalFormId
          ).withName(formSubmissionRecord.name)
        )
      )
    } yield formSubmission
  }

  def saveFormData(
    id: FormSubmissionId,
    gaiaState: GaiaState,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"${actor.idString} is filling form on form submission ${id.idString}")
      _ <- ZIOUtils.validate(gaiaState.nonEmpty)(
        GeneralServiceException("Cannot save form empty state data")
      )
      formSubmission <- transact(_.getFormSubmission(id, Option(actor)))
      userFormVersionId = formSubmission.formDataId
      lpFormDataOpt <- FDBRecordDatabase.transact(LpFormDataOperations.Production)(_.getOpt(userFormVersionId))
      patchesList <- ZIO.attempt(FormDataProtoConverter.patchesListToProto(gaiaState.events.toList))
      existingPatches <- ZIO.attempt(lpFormDataOpt.map(_.patchesList).getOrElse(Seq.empty))
      _ <- ZIOUtils.when(patchesList != existingPatches) {
        val values = gaiaState.stateMap.map { case (namespace, state) =>
          FormNamespace.unwrap(namespace) -> FieldValueMap(LpFormData.convertToNewData(state.map { case (k, v) =>
            k -> v.json
          }))
        }
        for {
          _ <- FDBRecordDatabase.transact(
            FDBOperations[LpFormDataOperations].Production
          ) { lpFormDataOps =>
            for {
              _ <- lpFormDataOps.updateOrAdd(
                formId = userFormVersionId,
                defaultData = LpFormData(
                  lpFormId = userFormVersionId,
                  namespaceDataMap = values,
                  patchesList = patchesList
                ),
                updateFunc = LpFormData.updateFuncForDefaultValues(values, Option(patchesList))
              )
            } yield ()
          }
        } yield ()
      }
    } yield ()
  }

  /** Todo @voxuannguyen2001: make this API more efficient. Should not generate the PDFs for each time this API is
    * called. Maybe cache the generated file + signature blocks like how we are dealing with subscription docs
    */
  def generateFilledFiles(
    id: FormSubmissionId,
    actor: UserId,
    httpContextOpt: Option[AuthenticatedRequestContext],
    fontColorOpt: Option[String] = None
  ): Task[Seq[FilledPdfInfo]] = {
    val docRequestId = id.parent
    for {
      _ <- ZIO.logInfo(s"user ${actor.idString} is generating filled pdf for form submission ${id.idString}")
      formSubmission <- transact(_.getFormSubmission(id, Option(actor)))
      docRequest <- FDBRecordDatabase.transact(DocRequestOperations.Production) { ops =>
        ops.get(docRequestId)
      }
      filledPdfInfos <- FormServiceHelper.getFilledPdfInfos(
        formDataId = formSubmission.formDataId,
        folderId = docRequest.sharedFolderId,
        actor = actor,
        httpContext = httpContextOpt,
        fontColorOpt = fontColorOpt
      )
    } yield filledPdfInfos
  }

  def signForm(
    formSubmissionId: FormSubmissionId,
    cleanFileIds: Seq[FileId],
    signers: Seq[FundSubSignerParams],
    actor: UserId,
    httpContextOpt: Option[AuthenticatedRequestContext],
    eSignatureProviderParams: SignatureProviderParams,
    fundSubSignatureDateFormatOpt: Option[String]
  ): Task[FundSubSignatureRequestBasic] = {
    for {
      _ <- ZIO.logInfo(s"user ${actor.idString} is signing form submission ${formSubmissionId.idString}")
      docRequest <- verifySubmitterOrReceiverTeam(formSubmissionId, Option(actor))
      _ <- verifyNoPendingSignatures(formSubmissionId)
      lpId = getLpId(formSubmissionId.parent)
      pendingSignatureRequests <- fundSubSignatureIntegrationService.getFormSubmissionSignatureRequestsMetadata(
        lpId,
        formSubmissionId = formSubmissionId
      )
      _ <- ZIOUtils.validate(pendingSignatureRequests.isEmpty)(
        new RuntimeException(
          s"Form submission contain pending signature request(s): ${pendingSignatureRequests.map(_.requestId)}"
        )
      )
      signatureRequestId <- fundSubSignatureIntegrationService.createSingleDocTypeSignatureRequest(
        singleDocTypeParams = SingleDocTypeEnvelope(
          lpId = lpId,
          docType = FundSubSignatureRequestModels.DocType.SupportingForm,
          formSubmissionIdOpt = Some(formSubmissionId)
        ),
        fileIds = cleanFileIds,
        signers = signers,
        message = "",
        teamIds = docRequest.receiverTeamIds ++ docRequest.submitterTeamIds,
        actor = actor,
        httpContext = httpContextOpt,
        eSignatureProviderParams = eSignatureProviderParams,
        fundSubSignatureDateFormatOpt = fundSubSignatureDateFormatOpt
      )
      requestBasic <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(signatureRequestId)
      _ <- ZIO.when(requestBasic.doneSigning) {
        afterFormSubmissionSignatureRequestCompleted(
          formSubmissionId,
          requestBasic.getSupportingFormPackage(formSubmissionId, lpId),
          actor,
          Some(docRequest.sharedFolderId)
        )
      }
    } yield requestBasic
  }

  def addSignatureRequest(
    id: FormSubmissionId,
    cleanFileIds: Seq[FileId],
    signers: Seq[FundSubSignerParams],
    message: String,
    actor: UserId,
    httpContextOpt: Option[AuthenticatedRequestContext],
    eSignatureProviderParams: SignatureProviderParams,
    refDocs: Seq[FileId],
    fundSubSignatureDateFormatOpt: Option[String]
  ): Task[SignatureRequestId] = {
    for {
      _ <- ZIO.logInfo(s"${actor.idString} is requesting signature from ${signers.map(_.userId)} on form ${id.idString}")
      docRequest <- verifySubmitterOrReceiverTeam(id, Option(actor))
      lpId = getLpId(id.parent)
      pendingSignatureRequests <- fundSubSignatureIntegrationService.getFormSubmissionSignatureRequestsMetadata(
        lpId,
        formSubmissionId = id
      )
      _ <- ZIOUtils.validate(pendingSignatureRequests.isEmpty)(
        new RuntimeException(
          s"Form submission contain pending signature request(s): ${pendingSignatureRequests.map(_.requestId)}"
        )
      )
      signatureRequestId <- fundSubSignatureIntegrationService.createSingleDocTypeSignatureRequest(
        singleDocTypeParams = SingleDocTypeEnvelope(
          lpId = lpId,
          docType = FundSubSignatureRequestModels.DocType.SupportingForm,
          formSubmissionIdOpt = Some(id)
        ),
        fileIds = cleanFileIds,
        signers = signers,
        message = message,
        teamIds = docRequest.receiverTeamIds ++ docRequest.submitterTeamIds,
        actor = actor,
        httpContext = httpContextOpt,
        eSignatureProviderParams = eSignatureProviderParams,
        refDocs = refDocs,
        fundSubSignatureDateFormatOpt = fundSubSignatureDateFormatOpt
      )
    } yield signatureRequestId
  }

  def cancelSignatureRequest(
    id: FormSubmissionId,
    signatureRequestId: SignatureRequestId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"${actor.idString} is cancel signature request on form ${id.idString}")
      _ <- verifySubmitterOrReceiverTeam(id, Option(actor))
      _ <- fundSubSignatureIntegrationService.cancelRequest(
        signatureRequestId,
        actor
      )
    } yield ()
  }

  def afterFormSubmissionSignatureRequestCompleted(
    formSubmissionId: FormSubmissionId,
    formSubmissionEnvelopePackageOpt: Option[FundSubSignatureRequestModels.EnvelopeFilePackage],
    actor: UserId,
    sharedFolderIdOpt: Option[FolderId],
    ctx: Option[AuthenticatedRequestContext] = None
  ): Task[Unit] = {
    val signedFiles = formSubmissionEnvelopePackageOpt.map(_.getSignedFileIds).getOrElse(Seq.empty)
    val certificates = formSubmissionEnvelopePackageOpt.map(_.getCertificateFileIds).getOrElse(Seq.empty)
    for {
      docRequestSharedFolderId <- ZIO
        .fromOption(sharedFolderIdOpt)
        .orElse {
          FDBRecordDatabase.transact(DocRequestOperations.Production) { ops =>
            ops.get(formSubmissionId.parent).map(_.sharedFolderId)
          }
        }
      platformAdmin <- executiveAdmin.userId
      sharedSignedFileIds <- FileServiceHelper
        .shareFilesToFolder(
          fileIds = signedFiles,
          folderId = docRequestSharedFolderId,
          actor = platformAdmin,
          ctx = ctx
        )
        .map(_.values.toSeq)
      sharedCertificateFileIds <- FileServiceHelper
        .shareFilesToFolder(
          fileIds = certificates,
          folderId = docRequestSharedFolderId,
          actor = platformAdmin,
          ctx = ctx
        )
        .map(_.values.toSeq)
      _ <- transact(
        _.completeSignatureRequestIgnorePermissionCheck(
          formSubmissionId,
          sharedSignedFileIds,
          sharedCertificateFileIds,
          Option(actor)
        )
      )
      _ = println(s"formSubmissionEnvelopePackageOpt: $formSubmissionEnvelopePackageOpt")
      _ <- greylinDataService.runUnit(
        for {
          _ <- operation.SubscriptionOrderSupportingFormFileOperations.delete(formSubmissionId)
          _ <- operation.SubscriptionOrderSupportingFormFileOperations.insert(
            (sharedSignedFileIds ++ sharedCertificateFileIds).map { fileId =>
              modelti.SubscriptionOrderSupportingFormFile(
                fileId = fileId,
                formSubmissionId = formSubmissionId
              )
            }.toList
          )
        } yield ()
      )
    } yield ()
  }

  def signingSignatureRequest(
    formSubmissionId: FormSubmissionId,
    signatureRequestId: SignatureRequestId,
    signatures: Map[FileId, DocumentSignatureMessage],
    actor: UserId,
    httpContextOpt: Option[AuthenticatedRequestContext]
  ): Task[FundSubSignatureRequestBasic] = {
    val lpId = getLpId(formSubmissionId.parent)
    for {
      _ <- ZIO.logInfo(s"${actor.idString} signing signature request on form submission ${formSubmissionId.idString}")
      _ <- fundSubSignatureIntegrationService.eSignSignatureRequest(
        lpId,
        signatureRequestId,
        signatures,
        actor,
        httpContextOpt
      )
      requestBasic <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(signatureRequestId)
      _ <- ZIOUtils.when(requestBasic.doneSigning) {
        afterFormSubmissionSignatureRequestCompleted(
          formSubmissionId,
          requestBasic.getSupportingFormPackage(formSubmissionId, lpId),
          actor,
          sharedFolderIdOpt = None
        )
      }
    } yield requestBasic
  }

  def submitFiles(
    id: FormSubmissionId,
    files: Seq[FileId],
    actor: UserId,
    httpContextOpt: Option[AuthenticatedRequestContext],
    isFormGeneratedDocs: Boolean
  ): Task[FormSubmission] = {
    for {
      _ <- ZIO.logInfo(s"${actor.idString} is submitting files on form submission ${id.idString}")
      docRequest <- verifySubmitterOrReceiverTeam(id, Option(actor))
      _ <- verifyNoPendingSignatures(id)
      sharedFiles <- FileServiceHelper
        .shareFilesToFolder(
          files,
          docRequest.sharedFolderId,
          actor,
          httpContextOpt
        )
        .map(_.values.toSeq)

      formSubmissionRecord <- transact(
        _.submitFiles(
          id,
          sharedFiles,
          Option(actor),
          isFormGeneratedDocs = isFormGeneratedDocs
        )
      )
      formSubmission <- getFormSubmission(formSubmissionRecord)
      _ <- greylinDataService.runUnit(
        operation.SubscriptionOrderSupportingFormFileOperations.delete(id)
      )
      _ <- greylinDataService.runUnit(
        operation.SubscriptionOrderSupportingFormFileOperations.insert(
          formSubmissionRecord.submittedFiles.map { fileId =>
            modelti.SubscriptionOrderSupportingFormFile(fileId = fileId, formSubmissionId = id)
          }.toList
        )
      )
    } yield formSubmission
  }

  def resubmit(
    id: FormSubmissionId,
    actorOpt: Option[UserId]
  ): Task[FormSubmission] = {
    for {
      formSubmissionRecord <- transact(_.resubmit(id, actorOpt))
      _ <- verifyNoPendingSignatures(id)
      formSubmission <- getFormSubmission(formSubmissionRecord)
      _ <- greylinDataService.runUnit(
        operation.SubscriptionOrderSupportingFormFileOperations.delete(id)
      )
    } yield formSubmission
  }

  def updateFormDataId(
    id: FormSubmissionId,
    formDataId: FundSubLpFormVersionId,
    actorOpt: Option[UserId]
  ): Task[FormSubmission] = {
    for {
      formSubmissionRecord <- transact(
        _.updateFormDataId(
          id,
          formDataId,
          actorOpt
        )
      )
      formSubmission <- getFormSubmission(formSubmissionRecord)
      _ <- greylinDataService.runUnit(
        operation.SubscriptionOrderSupportingFormOperations.update(id)(_.copy(formDataId = formDataId))
      )
    } yield formSubmission
  }

  def remove(
    id: FormSubmissionId,
    actorOpt: Option[UserId]
  ): Task[Unit] = {
    for {
      _ <- verifyNoPendingSignatures(id)
      _ <- transact(_.remove(id, actorOpt))
      _ <- greylinDataService.runUnit(
        operation.SubscriptionOrderSupportingFormOperations.delete(id)
      )
    } yield ()
  }

  private def getFormSubmission(
    record: FormSubmissionRecord
  )(
    using FDBCluster
  ) = {
    val lpId = getLpId(record.id.parent)
    for {
      requests <- fundSubSignatureIntegrationService.getFormSubmissionSignatureRequestsMetadata(
        lpId = lpId,
        formSubmissionId = record.id
      )
      _ <- ZIOUtils.when(requests.size > 1) {
        ZIO.logWarning(s"Form submission ${record.id.idString} contains multiple pending signature requests")
      }
      pendingSignatureRequestIdOpt = requests.headOption.map(_.requestId)
    } yield {
      toFormSubmission(record, pendingSignatureRequestIdOpt)
    }
  }

  private def verifyNoPendingSignatures(id: FormSubmissionId): Task[Unit] = {
    for {
      signatureRequests <- fundSubSignatureIntegrationService.getFormSubmissionSignatureRequestsMetadata(
        lpId = getLpId(id.parent),
        formSubmissionId = id
      )
      _ <- ZIOUtils.validate(signatureRequests.isEmpty)(
        new RuntimeException(s"Form submission ${id.idString} contains pending signature requests")
      )
    } yield ()
  }

  private def verifySubmitterOrReceiverTeam(id: FormSubmissionId, actorOpt: Option[UserId]) = {
    for {
      request <- FDBRecordDatabase.transact(
        FDBOperations[(DocRequestOperations, TeamMemberStateStoreOperations, TeamServiceOperations)].Production
      ) { case (docRequestOps, teamMemberStateOps, teamServiceOps) =>
        val permissionOps = DocRequestPermissionVerificationOperations(teamMemberStateOps, teamServiceOps)
        for {
          request <- docRequestOps.get(id.parent)
          _ <- permissionOps.verifySubmitterOrReceiverTeamMembership(request, actorOpt)
        } yield request
      }
    } yield request
  }

  private def transact[R](task: FormSubmissionServiceOperations => RecordTask[R]): Task[R] = {
    FDBRecordDatabase.transact(FormSubmissionServiceOperations.Production) { ops =>
      task(ops)
    }
  }

  private def read[R](
    task: FormSubmissionServiceOperations => RecordReadTask[R]
  )(
    using FDBCluster
  ): Task[R] = {
    FDBCommonDatabase().read(FormSubmissionServiceOperations.Production) { ops =>
      task(ops)
    }
  }

  private def getLpId(docRequestId: DocRequestId): FundSubLpId = {
    docRequestId.parent match {
      case lpId: FundSubLpId => lpId
      case _                 => throw new RuntimeException(s"Cannot get lpId from ${docRequestId.idString}")
    }
  }

  private def getLpIdOpt(docRequestId: DocRequestId): Option[FundSubLpId] = {
    docRequestId.parent match {
      case lpId: FundSubLpId => Some(lpId)
      case _                 => None
    }
  }

}
