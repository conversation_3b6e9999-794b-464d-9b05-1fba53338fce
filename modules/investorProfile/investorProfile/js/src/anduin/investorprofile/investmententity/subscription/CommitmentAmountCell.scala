// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.subscription

import anduin.investorprofile.models.SubscriptionModel
import anduin.numeral.Numeral
import anduin.protobuf.external.squants.MoneyMessage
import anduin.util.CurrencyUtils

import com.raquo.laminar.api.L.*

private[investmententity] final case class CommitmentAmountCell(subscription: SubscriptionModel) {

  def apply(): HtmlElement = {
    val committed = subscription.commitmentAmount.isDefined
    val commitmentAmount = subscription.commitmentAmount.getOrElse(MoneyMessage())
    val symbol = CurrencyUtils.getCurrencySymbol(commitmentAmount.unit)
    val value = commitmentAmount.value
    div(
      if (subscription.userAccessible) {
        symbol + " " + (if (committed) Numeral(value).format("0,0.[00]") else "N/A")
      } else {
        ""
      }
    )
  }

}
