//  Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.note

import java.time.LocalDate

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.editor.OnChangeData
import design.anduin.components.editor.laminar.*
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL, ModalL}
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*

import anduin.facades.editor.quill.mod.Sources
import anduin.funddata.endpoint.note.FundDataNote
import anduin.funddata.note.FundDataNoteComponent.{Customization, Mode}
import anduin.utils.DateTimeUtils

private[funddata] final case class FundDataNoteComponent(
  noteOptSignal: Signal[Option[FundDataNote]],
  saveNote: Observer[(content: String, onSuccess: Observer[Unit], onFailed: Observer[Unit])],
  deleteNote: Observer[(onSuccess: Observer[Unit], onFailed: Observer[Unit])],
  customization: Customization = Customization()
) {

  given SelfEqual[Sources] = CanEqual.derived

  private val modeVar = Var[Mode](Mode.View)
  private val isSavingVar = Var[Boolean](false)
  private val isDeletingVar = Var[Boolean](false)

  private val isContentModifiedVar = Var[Boolean](false)
  private val editingContentVar = Var[String]("")

  def apply(): HtmlElement = {
    div(
      tw.wPc100,
      child <-- modeVar.signal.map {
        case Mode.View =>
          div(
            child <-- noteOptSignal.map(
              _.fold(renderEmptyNote)(renderViewNote)
            )
          )
        case Mode.Edit =>
          div(
            tw.maxWPx768,
            child <-- noteOptSignal.map(_.fold("")(_.content)).map { initialContent =>
              renderEditNote(initialContent)
            }
          )
      }
    )
  }

  private def renderEmptyNote = {
    NonIdealStateL(
      icon = img(src := "/web/gondor/images/funddata/magnifier-person.svg"),
      title = div(customization.emptyTitle),
      description = div(customization.emptyDescription),
      action = ButtonL(
        style = ButtonL.Style.Full(color = ButtonL.Color.Primary, icon = Some(Icon.Glyph.Edit)),
        onClick = modeVar.writer.contramap(_ => Mode.Edit)
      )("Add note")
    )().amend(minHeight.px(450))
  }

  private def renderViewNote(note: FundDataNote) = {
    div(
      tw.maxWPx768.spaceY12,
      div(
        tw.flex.itemsEnd.justifyBetween,
        div(
          tw.flex.itemsCenter.spaceX8,
          ButtonL(
            style = ButtonL.Style.Full(icon = Some(Icon.Glyph.Edit)),
            onClick = modeVar.writer.contramap(_ => Mode.Edit)
          )("Edit note"),
          renderDeleteNoteButton
        ),
        div(
          tw.flex.itemsCenter.textGray7.spaceX4,
          IconL(name = Val(Icon.Glyph.Clock))(),
          div(
            tw.text11,
            "Last edited",
            " on ",
            span(
              tw.fontSemiBold,
              if (note.updatedAt.atZone(DateTimeUtils.defaultTimezone).toLocalDate == LocalDate.now()) {
                "today"
              } else {
                DateTimeUtils.formatInstant(note.updatedAt, DateTimeUtils.DefaultDateFormatter)(
                  using DateTimeUtils.defaultTimezone
                )
              }
            ),
            " by ",
            span(tw.fontSemiBold, note.updatedBy.userInfo.fullNameString)
          )
        )
      ),
      div(
        tw.p12.borderAll.borderGray3.border1.rounded2.bgGray1.overflowYAuto,
        maxHeight.px(400),
        EditorPreviewL(content = Val(note.content))()
      )
    )
  }

  private def renderEditNote(initialContent: String) = {
    editingContentVar.set(initialContent)
    isContentModifiedVar.set(false)
    div(
      tw.maxWPx768.spaceY12,
      div(
        tw.flex.itemsCenter.spaceX8,
        renderSaveNoteButton,
        ButtonL(
          isDisabled = isSavingVar.signal,
          onClick = modeVar.writer.contramap(_ => Mode.View)
        )("Cancel")
      ),
      div(
        RichEditorL(
          autoFocus = true,
          initialValue = initialContent,
          placeholder = "Enter your note...",
          onChange = Observer[OnChangeData] { changeData =>
            if (changeData.source == Sources.user) {
              isContentModifiedVar.set(true)
              editingContentVar.set(changeData.value)
            } else {
              editingContentVar.set(changeData.value)
            }
          },
          render = renderProps => {
            div(
              tw.borderAll.border1.borderGray3,
              // Editor content
              renderProps.editorNode.amend(minHeight.px(400)),
              // Toolbar
              div(
                tw.flex.itemsCenter.flexWrap.p4.spaceX2,
                tw.borderBottom.border1.borderGray3,
                TextFormatButton(
                  editorInstance = renderProps.editorInstance,
                  formatType = TextFormatButton.FormatType.Bold
                )(),
                TextFormatButton(
                  editorInstance = renderProps.editorInstance,
                  formatType = TextFormatButton.FormatType.Italic
                )(),
                TextFormatButton(
                  editorInstance = renderProps.editorInstance,
                  formatType = TextFormatButton.FormatType.Underline
                )(),
                TextFormatButton(
                  editorInstance = renderProps.editorInstance,
                  formatType = TextFormatButton.FormatType.StrikeThrough
                )(),
                div(tw.wPx1.hPx24.bgGray3),
                TextAlignButton(
                  editorInstance = renderProps.editorInstance
                )(),
                BulletListButton(
                  editorInstance = renderProps.editorInstance
                )(),
                NumberListButton(
                  editorInstance = renderProps.editorInstance
                )(),
                div(tw.wPx1.hPx24.bgGray3),
                LinkButton(
                  editorInstance = renderProps.editorInstance
                )(),
                div(tw.wPx1.hPx24.bgGray3),
                SpecificFontSize(
                  editorInstance = renderProps.editorInstance
                )()
              )
            )
          }
        )()
      )
    )
  }

  private def renderSaveNoteButton = {
    ButtonL(
      style = ButtonL.Style.Full(color = ButtonL.Color.Primary, isBusy = isSavingVar.signal),
      isDisabled = !isContentModifiedVar.signal,
      onClick = Observer { _ =>
        isSavingVar.set(true)
        saveNote.onNext(
          (
            content = editingContentVar.now(),
            onSuccess = Observer { _ =>
              Toast.success("Note saved successfully")
              isSavingVar.set(false)
              modeVar.set(Mode.View)
            },
            onFailed = Observer { _ =>
              Toast.error("Failed to save note, try again")
              isSavingVar.set(false)
            }
          )
        )
      }
    )("Save")
  }

  private def renderDeleteNoteButton = {
    ModalL(
      renderTitle = _ => customization.deleteModalTitle,
      renderTarget = openModal =>
        ButtonL(
          style = ButtonL.Style.Ghost(color = ButtonL.Color.Danger, icon = Some(Icon.Glyph.Trash)),
          hasChildren = false,
          onClick = openModal.contramap(_ => ())
        )(),
      renderContent = closeModal =>
        div(
          ModalBodyL(customization.deleteModalDescription),
          ModalFooterWCancelL(cancel = closeModal, isCancelDisabled = isDeletingVar.signal)(
            ButtonL(
              style = ButtonL.Style.Full(color = ButtonL.Color.Danger, isBusy = isDeletingVar.signal),
              onClick = Observer { _ =>
                isDeletingVar.set(true)
                deleteNote.onNext(
                  (
                    onSuccess = Observer { _ =>
                      Toast.success("Note deleted successfully")
                      isDeletingVar.set(false)
                      closeModal.onNext(())
                    },
                    onFailed = Observer { _ =>
                      Toast.error("Failed to delete note, try again")
                      isDeletingVar.set(false)
                    }
                  )
                )
              }
            )("Delete")
          )
        )
    )()
  }

}

private[funddata] object FundDataNoteComponent {

  enum Mode derives CanEqual {
    case Edit, View
  }

  final case class Customization(
    emptyTitle: String = "No note",
    emptyDescription: String = "Add a note to provide more information",
    deleteModalTitle: String = "Delete note?",
    deleteModalDescription: String = "Are you sure you want to delete this note? This action can't be undone."
  )

}
