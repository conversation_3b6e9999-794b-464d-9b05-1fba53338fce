// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui.tabs.utils

import anduin.forms.engine.GaiaState
import anduin.forms.{FormData, RestrictedFormRenderer}
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.toast.Toast
import design.anduin.components.well.laminar.WellL
import design.anduin.components.well.Well
import design.anduin.style.tw.*
import zio.ZIO

import anduin.forms.client.FormEndpointClient
import anduin.forms.endpoint.GetFormDataUserTempParams
import anduin.frontend.AirStreamUtils
import anduin.id.form.FormVersionDataId
import anduin.service.GeneralServiceException

private[investmententity] final case class PreviewFormModal(
  name: String,
  formData: FormData,
  formDataIdOrGaiaState: Either[FormVersionDataId, GaiaState],
  close: Observer[Unit]
) {
  private val gaiaStateVar = Var(Option.empty[GaiaState])
  private val isGettingData = Var[Boolean](true)

  def apply(): HtmlElement = {
    div(
      getGaiaState --> Observer.empty,
      tw.flex.flexCol.hPc100,
      header,
      div(
        tw.bgGray1.overflowYAuto.flexFill,
        child <-- gaiaStateVar.signal.map(_.fold(BlockIndicatorL(isFullHeight = true)()) { gaiaState =>
          div(
            tw.maxWPx1024.mxAuto,
            div(
              tw.py20,
              RestrictedFormRenderer
                .build(
                  formData = formData,
                  dataObserver = Observer.empty,
                  initialData = gaiaState,
                  initialKeyOpt = None,
                  enableDataTools = false,
                  isReadOnly = true
                )
                .fold(
                  errorMessage => WellL(style = Well.Style.Warning())(errorMessage),
                  { case (_, restrictedFormRenderer) =>
                    restrictedFormRenderer()
                  }
                )
            )
          )
        })
      )
    )
  }

  private def header = {
    div(
      tw.bgGray0.p12.flex.itemsCenter.shadow2,
      ButtonL(
        style = ButtonL.Style.Minimal(icon = Some(Icon.Glyph.ArrowLeft)),
        onClick = close.contramap(_ => ())
      )(),
      div(
        tw.ml12.pl16.borderGray3.borderLeft.fontSemiBold.text17.leading28,
        s"$name's form preview"
      )
    )
  }

  private def getGaiaState = {
    val task = formDataIdOrGaiaState.fold(
      formDataId =>
        FormEndpointClient
          .getFormDataUserTemp(
            GetFormDataUserTempParams(formDataId)
          )
          .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
          .map(
            _.fold(
              _ => {
                Var.set(
                  isGettingData -> false,
                  gaiaStateVar -> Option.empty[GaiaState]
                )
                Toast.error("Unable to get template info. Try again.")
                close.onNext(())
              },
              res => {
                Var.set(
                  isGettingData -> false,
                  gaiaStateVar -> Some(res)
                )
              }
            )
          ),
      gaiaState =>
        ZIO.succeed(
          Var.set(
            isGettingData -> false,
            gaiaStateVar -> Some(gaiaState)
          )
        )
    )
    AirStreamUtils.taskToStream(task)
  }

}
