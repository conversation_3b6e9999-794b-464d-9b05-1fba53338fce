// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui.tabs.utils

import anduin.forms.diff.{ResolveFormDataConflictV2Engine, ResolveFormDataConflictsV2}
import anduin.forms.utils.FormDataStatsUtils
import anduin.funddata.client.FundDataInvestmentEntityEndpointClient
import com.raquo.laminar.api.L.*
import design.anduin.style.tw.*

import anduin.funddata.endpoint.investmententity.GetInvestmentEntityProfileParams
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui.ReviewStepUI.ManualResolveFormData
import anduin.id.funddata.FundDataInvestmentEntityId
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.toast.Toast
import zio.ZIO

import anduin.forms.client.FormEndpointClient
import anduin.forms.endpoint.GetFormDataUserTempParams
import anduin.id.form.FormVersionDataId

private[tabs] final case class ResolveProfileConflictModal(
  formDataId: FormVersionDataId,
  investmentEntityId: FundDataInvestmentEntityId,
  onDone: Observer[ManualResolveFormData],
  renderTarget: Observer[Unit] => HtmlElement
) {
  private val isLoadingVar = Var[Boolean](false)
  private val resolveProfileDataConflictVar = Var[Option[ResolveFormDataConflictV2Engine.FormAndStates]](None)

  def apply(): HtmlElement = {
    ModalL(
      size = ModalL.Size(width = ModalL.Width.Full, height = ModalL.Height.Full),
      isClosable = None,
      renderContent = onClose => {
        div(
          tw.wPc100.hPc100,
          getProfileForms --> Observer.empty,
          child <-- isLoadingVar.signal.combineWith(resolveProfileDataConflictVar.signal).distinct.map {
            case (isLoading, formAndStatesOpt) =>
              if (isLoading) {
                BlockIndicatorL(title = Val(Some("Loading profiles...")), isFullHeight = true)()
              } else {
                div(
                  tw.wPc100.hPc100,
                  formAndStatesOpt.map { formAndStates =>
                    ResolveFormDataConflictsV2(
                      formData = formAndStates.formData,
                      originalFormState = formAndStates.originalFormState,
                      toUpdateFormState = formAndStates.toUpdateFormState,
                      onCancel = Observer { _ =>
                        resolveProfileDataConflictVar.set(None)
                        onClose.onNext(())
                      },
                      onDone = Observer { resolvedProfileData =>
                        val formCompletion =
                          FormDataStatsUtils.calculateFormCompletion(formAndStates.formData.form, resolvedProfileData)
                        onDone.onNext(
                          ManualResolveFormData(
                            gaiaState = resolvedProfileData,
                            visibleNonEmptyFieldCount = formCompletion.visibleNonEmptyFieldCount,
                            hiddenNonEmptyFieldCount = formCompletion.hiddenNonEmptyFieldCount
                          )
                        )
                        onClose.onNext(())
                      }
                    )()
                  }
                )
              }
          }
        )
      },
      renderTarget = renderTarget
    )()
  }

  private def getProfileForms = {
    val task = for {
      _ <- ZIO.attempt(isLoadingVar.set(true))
      _ <- for {
        ieProfileRespEither <- FundDataInvestmentEntityEndpointClient
          .getInvestmentEntityProfile(
            GetInvestmentEntityProfileParams(investmentEntityId)
          )
          .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        computedProfileRespEither <- FormEndpointClient
          .getFormDataUserTemp(
            GetFormDataUserTempParams(formDataId = formDataId)
          )
          .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
      } yield {
        (ieProfileRespEither, computedProfileRespEither) match {
          case (Right(ieProfileResp), Right(computedProfileResp)) => {
            Var.set(
              isLoadingVar -> false,
              resolveProfileDataConflictVar -> Some(
                ResolveFormDataConflictV2Engine.FormAndStates(
                  formData = ieProfileResp.profileForm,
                  originalFormState = ieProfileResp.profileData,
                  toUpdateFormState = computedProfileResp
                )
              )
            )
          }
          case _ => {
            isLoadingVar.set(false)
            Toast.error("Failed to get investment entity profile")
          }
        }
      }
    } yield ()
    ZIOUtils.toEventStreamUnsafe(task)
  }

}
