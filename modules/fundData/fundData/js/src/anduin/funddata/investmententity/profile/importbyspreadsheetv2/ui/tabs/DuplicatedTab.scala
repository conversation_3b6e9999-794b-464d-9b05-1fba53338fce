// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui.tabs

import anduin.forms.FormData
import anduin.funddata.endpoint.firm.ComputeProfilesDataFromSpreadsheetResp
import anduin.funddata.endpoint.investmententity.FundDataInvestmentEntityBasic
import anduin.funddata.endpoint.investor.FundDataInvestorBasic
import com.raquo.laminar.api.L.*
import design.anduin.style.tw.*
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.ImportProfileBySpreadSheetFlowData
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.ImportProfileBySpreadSheetFlowData.ProfileInfo
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui.ReviewStepUI.ManualResolveFormData
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui.ValidationUtils
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui.tabs.utils.{
  MapProfileToInvestmentEntityModalContent,
  PreviewFormModal,
  ResolveProfileConflictModal
}
import anduin.funddata.widget.CustomIdRenderer
import anduin.id.funddata.{FundDataFirmId, FundDataInvestmentEntityId}
import anduin.scalajs.pluralize.Pluralize
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.menu.laminar.{MenuItemL, MenuL}
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.{PortalPosition, PortalUtils}
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.well.Well
import design.anduin.components.well.laminar.WellL

private[ui] case class DuplicatedTab(
  firmId: FundDataFirmId,
  profileInfos: List[ImportProfileBySpreadSheetFlowData.ProfileInfo],
  searchTermSignal: Signal[String],
  computedProfilesSignal: Signal[Map[String, ComputeProfilesDataFromSpreadsheetResp]],
  isComputeProfilesFinishedSignal: Signal[Boolean],
  mappedProfilesSignal: Signal[Map[String, Option[FundDataInvestmentEntityId]]],
  onMapProfile: Observer[(String, Option[FundDataInvestmentEntityId])],
  hasResolveAllDuplicateProfilesSignal: Signal[Boolean],
  accessibleInvestmentEntities: List[FundDataInvestmentEntityBasic],
  accessibleInvestors: List[FundDataInvestorBasic],
  formData: FormData,
  manuallyResolvedProfilesSignal: Signal[Map[String, ManualResolveFormData]],
  onManuallyResolvedProfile: Observer[(String, ManualResolveFormData)],
  onClearResolvedProfile: Observer[String]
) {

  private val filteredProfilesSignal = searchTermSignal.map { searchTerm =>
    profileInfos
      .filter { record =>
        record.investmentEntityNameOrTrackingId.valueOpt.exists(_.trim.toLowerCase.contains(searchTerm.trim.toLowerCase))
      }
      .sortBy(_.investmentEntityNameOrTrackingId.extractedData.rawValue.toLowerCase)
  }.distinct

  private val hasUnresolvedConflictedProfilesSignal = mappedProfilesSignal
    .combineWith(
      manuallyResolvedProfilesSignal,
      computedProfilesSignal,
      isComputeProfilesFinishedSignal
    )
    .map { case (mappedProfiles, manuallyResolvedProfiles, computedProfiles, isComputeProfilesFinished) =>
      val resolvedConflictedProfileIds = manuallyResolvedProfiles.keySet
      val conflictedProfileIds = mappedProfiles.filter { case (profileId, ieIdOpt) =>
        computedProfiles
          .get(profileId)
          .exists(resp => resp.hasUnresolvedConflict && ieIdOpt.contains(resp.investmentEntityId))
      }.keySet
      !conflictedProfileIds.subsetOf(resolvedConflictedProfileIds) && isComputeProfilesFinished
    }
    .distinct

  def apply(): HtmlElement = {
    div(
      tw.hPc100.flex.flexCol,
      div(
        child <-- hasResolveAllDuplicateProfilesSignal.map {
          if (_) {
            div(
              WellL(style = Well.Style.Success(icon = Some(Icon.Glyph.CheckCircle)))(
                "All issues for profiles with multiple matches have been resolved"
              ),
              child.maybe <-- hasUnresolvedConflictedProfilesSignal.map {
                Option.when(_) {
                  WellL(style = Well.Style.Warning(icon = None))(
                    "Data conflicts were found between the profile in the spreadsheet and your existing database.",
                    " You can resolve them now or after importing."
                  ).amend(tw.mt8)
                }
              }
            )
          } else {
            WellL(style = Well.Style.Warning(icon = None))(
              "These profiles in the spreadsheet match more than 1 existing investment entity in your database. ",
              "Select the investment entity to update, or skip them."
            )
          }
        }
      ),
      renderTable
    )
  }

  private def renderTable: HtmlElement = {
    div(
      child <-- filteredProfilesSignal.map(_.isEmpty).distinct.map {
        if (_) {
          renderEmptySearchState
        } else {
          div(
            div(
              tw.borderGray3.borderBottom.py8.px12.heading5.mt16,
              tw.flex,
              div(width.px(480), "Investment entity profiles being imported"),
              div(tw.flexFill, "Resolution")
            ),
            div(
              maxHeight.px(620),
              tw.overflowYAuto,
              children <-- filteredProfilesSignal.split(_.id) { (_, profile, _) =>
                div(
                  tw.flex.itemsCenter,
                  tw.borderGray3.borderBottom.px12.py8.mt8,
                  height.px(56),
                  renderNameOrCustomIdColumn(profile),
                  renderResolveColumn(profile)
                )
              }
            )
          )
        }
      }
    )
  }

  private def renderNameOrCustomIdColumn(profileInfo: ProfileInfo) = {
    val mappedIeOptSignal = mappedProfilesSignal
      .map(
        _.get(profileInfo.id).flatten.fold(Option.empty[FundDataInvestmentEntityBasic]) { ieId =>
          accessibleInvestmentEntities.find(_.id == ieId)
        }
      )
      .distinct
    div(
      tw.flex.itemsCenter.justifyBetween,
      width.px(480),
      div(
        tw.flex.itemsCenter,
        div(
          tw.bgWarning1.textWarning4.mr8.rounded4.borderAll.borderWarning2,
          tw.wPx24.hPx24.p4,
          IconL(name = Val(Icon.Glyph.UserInfo), size = Icon.Size.Custom(14))()
        ),
        div(
          tw.heading5,
          child <-- mappedIeOptSignal.map { mappedIeOpt =>
            if (mappedIeOpt.exists(ie => profileInfo.investmentEntityNameOrTrackingId.valueOpt.contains(ie.customId))) {
              CustomIdRenderer(customId = profileInfo.investmentEntityNameOrTrackingId.extractedData.rawValue)()
            } else {
              TruncateL(
                target = div(tw.fontSemiBold, profileInfo.investmentEntityNameOrTrackingId.extractedData.rawValue)
              )()
            }
          }
        )
      ),
      div(
        tw.bgPrimary1.textPrimary4.roundedFull.wPx40.hPx40.p12.mr16,
        IconL(name = Val(Icon.Glyph.ArrowRight))()
      )
    )
  }

  private def renderResolveColumn(profileInfo: ProfileInfo) = {
    div(
      tw.flexFill,
      child <-- mappedProfilesSignal.map(_.get(profileInfo.id)).map { mappedIeIdOptOpt =>
        mappedIeIdOptOpt
          .fold(
            renderResolveMethodPopover(profileInfo, mappedIeIdOptOpt)
          ) {
            _.fold(
              div(
                tw.flex.itemsCenter.justifyBetween,
                "Don't import",
                renderResolveMethodPopover(profileInfo, mappedIeIdOptOpt)
              )
            ) { mappedIeId =>
              val investmentEntityOpt = accessibleInvestmentEntities.find(_.id == mappedIeId)
              val computedProfileOptSignal = computedProfilesSignal
                .map(_.get(profileInfo.id).filter { computedProfile =>
                  mappedIeId == computedProfile.investmentEntityId
                })
                .distinct
              val conflictsCountSignal = computedProfileOptSignal.map(_.fold(0)(_.unresolvedConflicts))
              val resolvedProfileOptSignal = manuallyResolvedProfilesSignal.map(_.get(profileInfo.id))
              div(
                tw.flex.itemsCenter.justifyBetween,
                investmentEntityOpt.map { investmentEntity =>
                  div(
                    tw.flex.itemsCenter.spaceX8,
                    div("Import to ", span(tw.fontSemiBold, investmentEntity.name)),
                    Option.when(investmentEntity.customId.trim.nonEmpty) {
                      CustomIdRenderer(investmentEntity.customId.trim)()
                    },
                    child.maybe <-- conflictsCountSignal.combineWith(resolvedProfileOptSignal.map(_.nonEmpty)).map {
                      case (conflictsCount, hasResolved) =>
                        Option.when(conflictsCount > 0 && !hasResolved) {
                          div(
                            tw.textWarning5,
                            Pluralize("conflict", conflictsCount, true),
                            " found"
                          )
                        }
                    }
                  )
                },
                div(
                  tw.flex.itemsCenter.spaceX8,
                  child.maybe <-- computedProfileOptSignal
                    .combineWith(resolvedProfileOptSignal, conflictsCountSignal)
                    .distinct
                    .map { case (computedProfileOpt, resolvedProfileOpt, conflictsCount) =>
                      computedProfileOpt.map { computedProfile =>
                        if (conflictsCount > 0 && resolvedProfileOpt.isEmpty) {
                          div(
                            ResolveProfileConflictModal(
                              formDataId = computedProfile.formVersionDataId,
                              investmentEntityId = computedProfile.investmentEntityId,
                              onDone = Observer { resolvedProfile =>
                                onManuallyResolvedProfile.onNext((profileInfo.id, resolvedProfile))
                              },
                              renderTarget = onOpen =>
                                TooltipL(
                                  renderTarget = ButtonL(
                                    style =
                                      ButtonL.Style.Minimal(height = ButtonL.Height.Fix32, color = ButtonL.Color.Primary),
                                    onClick = onOpen.contramap(_ => ()),
                                    isDisabled = !isComputeProfilesFinishedSignal
                                  )("Resolve conflicts"),
                                  renderContent = _.amend("Please wait until we finish computing all profiles"),
                                  isDisabled = isComputeProfilesFinishedSignal
                                )()
                            )()
                          )
                        } else {
                          div(
                            ModalL(
                              size = ModalL.Size(width = ModalL.Width.Full, height = ModalL.Height.Full),
                              isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false)),
                              renderTarget = open =>
                                TooltipL(
                                  renderTarget = ButtonL(
                                    style =
                                      ButtonL.Style.Minimal(height = ButtonL.Height.Fix32, color = ButtonL.Color.Primary),
                                    onClick = open.contramap(_ => ()),
                                    isDisabled = !isComputeProfilesFinishedSignal
                                  )("Preview profile"),
                                  renderContent = _.amend("Please wait until we finish computing all profiles"),
                                  isDisabled = isComputeProfilesFinishedSignal
                                )(),
                              renderContent = close =>
                                PreviewFormModal(
                                  name = profileInfo.investmentEntityNameOrTrackingId.extractedData.rawValue,
                                  formData = formData,
                                  formDataIdOrGaiaState = resolvedProfileOpt.fold(Left(computedProfile.formVersionDataId)) {
                                    resolvedProfile =>
                                      Right(resolvedProfile.gaiaState)
                                  },
                                  close = close
                                )()
                            )()
                          )
                        }
                      }
                    },
                  renderResolveMethodPopover(profileInfo, mappedIeIdOptOpt)
                )
              )
            }
          }
      }
    )
  }

  private def renderResolveMethodPopover(
    profileInfo: ProfileInfo,
    mappedInvestmentEntityIdOptOpt: Option[Option[FundDataInvestmentEntityId]]
  ) = {
    div(
      PopoverL(
        position = PortalPosition.BottomRight,
        renderTarget = (onClick, isSelected) =>
          if (mappedInvestmentEntityIdOptOpt.isEmpty) {
            renderResolveOptionsButton(onClick, isSelected)
          } else {
            renderChangeResolutionButton(onClick, isSelected)
          },
        renderContent = closePopover =>
          renderResolveMethodOptions(
            profileInfo,
            mappedInvestmentEntityIdOptOpt,
            closePopover
          ).amend(
            tw.flexFill
          )
      )()
    )
  }

  private def renderChangeResolutionButton(onClick: Observer[Unit], isSelected: Signal[Boolean]) = {
    ButtonL(
      style = ButtonL.Style.Minimal(ButtonL.Color.Primary, isSelected = isSelected),
      onClick = onClick.contramap(_ => ())
    )(
      div(
        tw.flex.itemsCenter,
        "Change",
        span(tw.ml8, IconL(name = Val(Icon.Glyph.CaretDown))())
      )
    )
  }

  private def renderResolveOptionsButton(onClick: Observer[Unit], isSelected: Signal[Boolean]) = {
    ButtonL(
      style = ButtonL.Style.Full(isSelected = isSelected),
      onClick = onClick.contramap(_ => ())
    )(
      div(
        tw.flex.itemsCenter.justifyBetween,
        width.px(180),
        "Select a resolution",
        span(tw.ml8, IconL(name = Val(Icon.Glyph.CaretDown))())
      )
    )
  }

  private def renderResolveMethodOptions(
    profileInfo: ProfileInfo,
    mappedInvestmentEntityIdOptOpt: Option[Option[FundDataInvestmentEntityId]],
    closePopover: Observer[Unit]
  ) = {
    val matchedIes = ValidationUtils.matchedInvestmentEntities(profileInfo, accessibleInvestmentEntities)
    val matchedInvestorAndIes = matchedIes.sortBy(_.name.trim).flatMap { ie =>
      accessibleInvestors.find(_.investorId == ie.id.parent).map(_ -> ie)
    }
    MenuL(
      Seq(
        Some(
          ModalL(
            renderTitle = _ => "Map to investment entity",
            isClosable = None,
            renderTarget =
              openModal => MenuItemL(onClick = openModal, icon = Some(Icon.Glyph.UserAccept))("Map to existing entity"),
            renderContent = closeModal =>
              MapProfileToInvestmentEntityModalContent(
                profileInfo = profileInfo,
                mappedInvestmentEntityIdOpt = mappedInvestmentEntityIdOptOpt.flatten,
                matchedInvestmentEntities = matchedInvestorAndIes,
                onMapProfile = Observer { ieId =>
                  onMapProfile.onNext(profileInfo.id -> Some(ieId))
                  onClearResolvedProfile.onNext(profileInfo.id)
                },
                onCancel = closeModal
              )(),
            size = ModalL.Size(width = ModalL.Width.Px720),
            afterUserClose = closePopover
          )()
        ),
        Option.unless(mappedInvestmentEntityIdOptOpt.contains(None)) {
          MenuItemL(
            onClick = Observer { _ =>
              onMapProfile.onNext(profileInfo.id -> None)
              onClearResolvedProfile.onNext(profileInfo.id)
            },
            icon = Some(Icon.Glyph.Cross)
          )("Don't import")
        }
      ).flatten
    )
  }

  private def renderEmptySearchState = {
    NonIdealStateL(
      icon = img(src := "/web/gondor/images/funddata/magnifier-eye.svg"),
      title = div("No results found"),
      description = div("You can rephrase your search to try again")
    )().amend(minHeight.px(300))
  }

}
