// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui.tabs

import anduin.forms.FormData
import anduin.funddata.endpoint.firm.ComputeProfilesDataFromSpreadsheetResp
import com.raquo.laminar.api.L.*
import design.anduin.style.CssVar
import design.anduin.style.tw.*
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.ImportProfileBySpreadSheetFlowData
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui.ReviewStepUI.ManualResolveFormData
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui.tabs.utils.{
  PreviewFormModal,
  ResolveProfileConflictModal
}
import anduin.funddata.widget.CustomIdRenderer
import anduin.id.funddata.FundDataFirmId
import anduin.scalajs.pluralize.Pluralize
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.portal.PortalUtils
import design.anduin.components.portal.laminar.PortalWrapperL
import design.anduin.components.progress.laminar.{CircleIndicatorL, SkeletonL}
import design.anduin.components.progress.Skeleton
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.well.Well
import design.anduin.components.well.laminar.WellL
import design.anduin.table.laminar.{SelectionColumnL, TableL}
import org.scalajs.dom.{HTMLElement, document}

private[ui] case class UpdateTab(
  firmId: FundDataFirmId,
  profileInfos: List[ImportProfileBySpreadSheetFlowData.ProfileInfo],
  searchTermSignal: Signal[String],
  computedProfilesSignal: Signal[Map[String, ComputeProfilesDataFromSpreadsheetResp]],
  isComputeProfilesFinishedSignal: Signal[Boolean],
  selectedProfilesSignal: Signal[List[String]],
  onSelectedProfiles: Observer[List[String]],
  formData: FormData,
  manuallyResolvedProfilesSignal: Signal[Map[String, ManualResolveFormData]],
  onManuallyResolvedProfile: Observer[(String, ManualResolveFormData)]
) {

  private val filteredProfilesSignal = searchTermSignal
    .combineWith(computedProfilesSignal, isComputeProfilesFinishedSignal)
    .map { case (searchTerm, computedProfiles, isComputeProfilesFinished) =>
      val filteredProfileInfos = profileInfos.filter { record =>
        record.investmentEntityNameOrTrackingId.valueOpt.exists(_.trim.toLowerCase.contains(searchTerm.trim.toLowerCase))
      }
      if (isComputeProfilesFinished) {
        filteredProfileInfos.sortBy { profileInfo =>
          val isProfileConflicted = computedProfiles.get(profileInfo.id).exists(_.hasUnresolvedConflict)
          // conflicted profiles are sorted first
          (!isProfileConflicted, profileInfo.investmentEntityNameOrTrackingId.extractedData.rawValue.toLowerCase)
        }
      } else {
        filteredProfileInfos.sortBy(_.investmentEntityNameOrTrackingId.extractedData.rawValue.toLowerCase)
      }
    }
    .distinct

  private val hasUnresolvedConflictedProfilesSignal = selectedProfilesSignal
    .combineWith(
      manuallyResolvedProfilesSignal,
      computedProfilesSignal,
      isComputeProfilesFinishedSignal
    )
    .map { case (selectedProfileIds, manuallyResolvedProfiles, computedProfiles, isComputeProfilesFinished) =>
      val resolvedConflictedProfileIds = manuallyResolvedProfiles.keySet
      val conflictedProfileIds = selectedProfileIds.filter { profileId =>
        computedProfiles.get(profileId).exists(_.hasUnresolvedConflict)
      }.toSet
      !conflictedProfileIds.subsetOf(resolvedConflictedProfileIds) && isComputeProfilesFinished
    }
    .distinct

  def apply(): HtmlElement = {
    div(
      tw.hPc100,
      // force re-render tab when finish computing
      child <-- isComputeProfilesFinishedSignal.combineWith(hasUnresolvedConflictedProfilesSignal).map {
        case (_, hasUnresolvedConflictedProfiles) =>
          div(
            tw.hPc100.flex.flexCol,
            Option.when(hasUnresolvedConflictedProfiles) {
              WellL(style = Well.Style.Warning(icon = None))(
                "Data conflicts were found between the profile in the spreadsheet and your existing database.",
                " You can resolve them now or after importing."
              ).amend(tw.mb16)
            },
            renderTable
          )
      }
    )
  }

  private def checkBoxColumn = {
    SelectionColumnL[ImportProfileBySpreadSheetFlowData.ProfileInfo](
      dataSignal = filteredProfilesSignal.map(_.map { row =>
        SelectionColumnL.Row(data = row)
      }),
      selectedRowsSignal = selectedProfilesSignal.map(_.flatMap(id => profileInfos.find(_.id == id))),
      onRowsSelected = Observer { selectedProfileInfos =>
        onSelectedProfiles.onNext(selectedProfileInfos.map(_.id))
      },
      renderCell = renderProps =>
        div(
          child <-- isComputeProfilesFinishedSignal.splitBoolean(
            whenTrue = _ => renderProps.cell,
            whenFalse = _ =>
              div(
                TooltipL(
                  renderTarget = div(tw.py2.textPrimary5, CircleIndicatorL()()),
                  renderContent = _.amend("Please wait until we finish computing all profiles"),
                  isDisabled = isComputeProfilesFinishedSignal
                )()
              )
          )
        )
    )
  }

  private val ieNameOrTrackingIdColumn = TableL.Column[ImportProfileBySpreadSheetFlowData.ProfileInfo](
    title = "Investment entity name/tracking ID",
    field = "IeNameOrTrackingID",
    renderCell = renderProps => {
      div(
        tw.wPc100,
        renderProps.data.matchedInvestmentEntityInfo.valueOpt.map { matchedIe =>
          if (matchedIe.isMatchByInvestmentEntityName) {
            TruncateL(target = div(tw.fontSemiBold, matchedIe.data.name))()
          } else {
            CustomIdRenderer(customId = matchedIe.data.customId)()
          }
        }
      )
    },
    isSortable = true,
    sortWith = Some(sorter => {
      val a = sorter.a.map(_.investmentEntityNameOrTrackingId.extractedData.rawValue.toLowerCase).getOrElse("")
      val b = sorter.b.map(_.investmentEntityNameOrTrackingId.extractedData.rawValue.toLowerCase).getOrElse("")
      a.compareTo(b).toDouble
    })
  )

  private val profileColumn = TableL.Column[ImportProfileBySpreadSheetFlowData.ProfileInfo](
    title = "Profile",
    field = "profile",
    renderCell = renderProps => {
      val computedProfileOptSignal = computedProfilesSignal
        .map(_.get(renderProps.data.id))
        .distinct
      val hasConflictsSignal = computedProfileOptSignal.map(_.exists(_.hasUnresolvedConflict))
      val resolvedProfileOptSignal = manuallyResolvedProfilesSignal.map(_.get(renderProps.data.id))
      div(
        tw.wPc100.flex.itemsCenter.justifyBetween,
        div(
          tw.spaceY4,
          height.px(44),
          children <-- computedProfileOptSignal.combineWith(hasConflictsSignal, resolvedProfileOptSignal).map {
            case (computedProfileOpt, _, resolvedProfileOpt) =>
              computedProfileOpt
                .fold(
                  Seq(
                    SkeletonL(height = "20px", width = "100px", shape = Skeleton.Shape.Rectangle)(),
                    SkeletonL(height = "20px", width = "120px", shape = Skeleton.Shape.Rectangle)()
                  )
                ) { computedProfile =>
                  val visibleNonEmptyFieldCount =
                    resolvedProfileOpt.fold(computedProfile.visibleNonEmptyFieldCount)(_.visibleNonEmptyFieldCount)
                  val hiddenNonEmptyFieldCount =
                    resolvedProfileOpt.fold(computedProfile.hiddenNonEmptyFieldCount)(_.hiddenNonEmptyFieldCount)
                  Seq(
                    if (visibleNonEmptyFieldCount > 0) {
                      Some(
                        div(
                          Option.when(hiddenNonEmptyFieldCount == 0)(lineHeight.px(44)),
                          span(tw.fontSemiBold, visibleNonEmptyFieldCount),
                          Pluralize(" field", visibleNonEmptyFieldCount),
                          " to be imported"
                        )
                      )
                    } else {
                      Some(
                        div(
                          Option.when(hiddenNonEmptyFieldCount == 0)(lineHeight.px(44)),
                          "No fields to be imported"
                        )
                      )
                    },
                    Option.when(hiddenNonEmptyFieldCount > 0) {
                      TooltipL(
                        renderTarget = div(
                          tw.textGray6.borderBottom.borderDashed,
                          Pluralize(
                            " field",
                            hiddenNonEmptyFieldCount,
                            inclusive = true
                          ),
                          " currently hidden"
                        ),
                        renderContent = _.amend(
                          "These fields are hidden due to the logic associated with them. ",
                          "They will appear once the remaining fields are filled"
                        ),
                        targetWrapper = PortalWrapperL.BlockContent
                      )()
                    }
                  ).flatten
                }
          }
        ),
        child.maybe <-- computedProfileOptSignal
          .combineWith(hasConflictsSignal, resolvedProfileOptSignal)
          .distinct
          .map { case (computedProfileOpt, hasConflicts, resolvedProfileOpt) =>
            computedProfileOpt.map { computedProfile =>
              if (hasConflicts && resolvedProfileOpt.isEmpty) {
                div(
                  ResolveProfileConflictModal(
                    formDataId = computedProfile.formVersionDataId,
                    investmentEntityId = computedProfile.investmentEntityId,
                    onDone = Observer { resolvedProfile =>
                      onManuallyResolvedProfile.onNext((renderProps.data.id, resolvedProfile))
                    },
                    renderTarget = onOpen =>
                      div(
                        TooltipL(
                          renderTarget = ButtonL(
                            style = ButtonL.Style.Ghost(height = ButtonL.Height.Fix32, color = ButtonL.Color.Warning),
                            onClick = onOpen.contramap(_ => ()),
                            isDisabled = !isComputeProfilesFinishedSignal
                          )("Resolve conflicts"),
                          renderContent = _.amend("Please wait until we finish computing all profiles"),
                          isDisabled = isComputeProfilesFinishedSignal
                        )()
                      )
                  )()
                )
              } else {
                div(
                  ModalL(
                    size = ModalL.Size(width = ModalL.Width.Full, height = ModalL.Height.Full),
                    isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false)),
                    renderTarget = open =>
                      TooltipL(
                        renderTarget = ButtonL(
                          style = ButtonL.Style.Full(icon = Some(Icon.Glyph.Eye), height = ButtonL.Height.Fix32),
                          onClick = open.contramap(_ => ()),
                          isDisabled = !isComputeProfilesFinishedSignal
                        )("Preview form").amend(tw.hidden.groupHover(tw.block)),
                        renderContent = _.amend("Please wait until we finish computing all profiles"),
                        isDisabled = isComputeProfilesFinishedSignal
                      )(),
                    renderContent = close =>
                      PreviewFormModal(
                        name = renderProps.data.investmentEntityNameOrTrackingId.extractedData.rawValue,
                        formData = formData,
                        formDataIdOrGaiaState = resolvedProfileOpt
                          .fold(Left(computedProfile.formVersionDataId))(resolvedProfile =>
                            Right(resolvedProfile.gaiaState)
                          ),
                        close = close
                      )()
                  )()
                )
              }
            }
          },
        hasConflictsSignal
          .combineWith(resolvedProfileOptSignal)
          .map { case (hasConflicts, resolvedProfileOpt) =>
            hasConflicts && resolvedProfileOpt.isEmpty
          }
          .distinct --> Observer[Boolean] { hasConflict =>
          val elementOpt = document.getElementById(s"row-${renderProps.data.id}") match {
            case element: HTMLElement => Some(element)
            case _                    => Option.empty
          }
          elementOpt.foreach { e =>
            if (hasConflict) {
              e.style.backgroundColor = CssVar.toVar(CssVar.Color.Warning1)
              e.classList.add(tw.bgOpacity40.css)
              e.classList.add(tw.hover(tw.bgOpacity40).css)
            } else {
              e.style.backgroundColor = ""
              e.classList.remove(tw.bgOpacity40.css)
              e.classList.remove(tw.hover(tw.bgOpacity40).css)
            }
          }
        }
      )
    }
  )

  private def renderTable = {
    TableL[ImportProfileBySpreadSheetFlowData.ProfileInfo](
      dataSignal = filteredProfilesSignal,
      options = TableL.Options(
        indexColumn = Option("id"),
        layout = TableL.Layout.FitColumns,
        selectableMode = TableL.SelectableMode.SelectableByClicking
      ),
      columns = List(
        checkBoxColumn(),
        ieNameOrTrackingIdColumn,
        profileColumn
      ),
      onRowRendered = Observer[TableL.RowRenderedData[ImportProfileBySpreadSheetFlowData.ProfileInfo]](handleRowRendered),
      placeholder = TableL.Placeholder(
        criteria = _.map(_.isEmpty),
        render = _ => renderEmptySearchState
      )
    ).amend(tw.flexFill)
  }

  private def renderEmptySearchState = {
    NonIdealStateL(
      icon = img(src := "/web/gondor/images/funddata/magnifier-eye.svg"),
      title = div("No results found"),
      description = div("You can rephrase your search to try again")
    )().amend(minHeight.px(300))
  }

  private def handleRowRendered(rowRenderedData: TableL.RowRenderedData[ImportProfileBySpreadSheetFlowData.ProfileInfo])
    : Unit = {
    rowRenderedData.rowComponent.getElement().classList.add(tw.group.css)
    rowRenderedData.getData().foreach { data =>
      rowRenderedData.rowComponent.getElement().id = s"row-${data.id}"
    }
  }

}
