// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui

import com.raquo.laminar.api.L.*
import design.anduin.components.badge.Badge
import design.anduin.components.badge.laminar.BadgeL
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.tab.laminar.TabL
import design.anduin.components.tab.Tab
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import zio.ZIO
import anduin.enumeration.StringEnum
import anduin.forms.engine.GaiaState
import anduin.funddata.client.{FundDataInvestmentEntityEndpointClient, FundDataInvestorEndpointClient}
import anduin.funddata.common.flow.FlowStepUI.DefaultLayout
import anduin.funddata.common.importbyspreadsheet.CancelImportBySpreadsheetModal
import anduin.funddata.endpoint.firm.{
  ComputeProfilesDataFromSpreadsheetParams,
  ComputeProfilesDataFromSpreadsheetResp,
  ImportProfilesDataFromSpreadsheetParams
}
import anduin.funddata.endpoint.investmententity.{FundDataInvestmentEntityBasic, GetInvestmentEntitiesBasicParams}
import anduin.funddata.endpoint.investor.{FundDataInvestorBasic, GetInvestorsBasicParams}
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.ImportProfileBySpreadSheetFlowData.ProfileInfo
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.{
  ImportProfileBySpreadSheetFlowData,
  ImportProfileBySpreadSheetFlowStep,
  ImportProfileBySpreadSheetFlowStepUI
}
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui.ReviewStepUI.{
  ManualResolveFormData,
  ProfileLists,
  ProfileTab,
  getTabBadgeColor
}
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui.ValidationUtils.*
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui.tabs.{CannotImportTab, DuplicatedTab, UpdateTab}
import anduin.funddata.widget.filter.SearchTextBox
import anduin.id.funddata.{FundDataFirmId, FundDataInvestmentEntityId}
import anduin.scalajs.pluralize.Pluralize
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils

private[importbyspreadsheetv2] case class ReviewStepUI(
  firmId: FundDataFirmId,
  stepInput: ImportProfileBySpreadSheetFlowData.Mapping,
  initialOutputOpt: Option[ImportProfileBySpreadSheetFlowData.Review],
  onNext: Observer[ImportProfileBySpreadSheetFlowData.Review],
  onBack: Observer[Unit],
  onCancel: Observer[Unit]
) extends ImportProfileBySpreadSheetFlowStepUI[
      ImportProfileBySpreadSheetFlowStep,
      ImportProfileBySpreadSheetFlowData.Mapping,
      ImportProfileBySpreadSheetFlowData.Review
    ] {

  override val currentStep: ImportProfileBySpreadSheetFlowStep = ImportProfileBySpreadSheetFlowStep.ReviewStep

  private val isImportingVar = Var[Boolean](false)
  private val tempSearchVar = Var("")
  private val searchVar = Var("")
  private val curSelectedTabOptVar = Var[Option[ProfileTab]](None)
  private val profileListsVar = Var[Option[ProfileLists]](None)
  private val selectedUpdatingProfilesVar: Var[List[String]] = Var(List.empty) // By id

  private val selectedDuplicatedProfilesVar: Var[Map[String, Option[FundDataInvestmentEntityId]]] =
    Var(Map.empty) // By id to investmentEntityId

  private val manuallyResolvedProfilesVar: Var[Map[String, ManualResolveFormData]] = Var(Map.empty)

  private val hasResolveAllDuplicateProfilesSignal = profileListsVar.signal
    .map(_.map(_.duplicatedProfiles).toList.flatten.length)
    .combineWith(selectedDuplicatedProfilesVar.signal.map(_.size))
    .distinct
    .map { (count, selectedCount) => count == selectedCount }

  private val numImportedProfilesSignal = selectedUpdatingProfilesVar.signal
    .map(_.length)
    .combineWith(selectedDuplicatedProfilesVar.signal.map(_.count(_._2.nonEmpty)))
    .distinct
    .map { case (updatedCount, duplicatedCount) => updatedCount + duplicatedCount }

  private val computedProfilesVar = Var[Map[String, ComputeProfilesDataFromSpreadsheetResp]](Map.empty)

  private val computedProfilesSignal = computedProfilesVar.signal

  private val isComputeProfilesFinishedVar = Var[Boolean](true)

  private val isComputeProfilesFinishedSignal = isComputeProfilesFinishedVar.signal

  private def renderTitleAndSearchBar = {
    div(
      tw.wPc100,
      tw.flex.itemsEnd.justifyBetween,
      div(
        renderTitle("Review profiles").amend(tw.mb4),
        div(
          span(
            tw.fontSemiBold,
            s"${Pluralize(
                "profiles",
                stepInput.numRecords,
                true
              )}"
          ),
          " found in the uploaded spreadsheet."
        )
      ),
      div(
        SearchTextBox(
          typingSearchVar = tempSearchVar,
          onSearchEntered = searchVar.writer
        )().amend(width.px(340))
      )
    )
  }

  private def renderMainContent(profileLists: ProfileLists): HtmlElement = {
    val availableTabs = ProfileTab.values.toList.filter(tab => profileLists.getListByTab(tab).nonEmpty)
    curSelectedTabOptVar.set(availableTabs.headOption)
    div(
      tw.hPc100.flex.flexCol,
      Option.when(availableTabs.length > 1) {
        div(
          tw.pb16,
          TabL(
            activeTabSignal = curSelectedTabOptVar.signal.map(_.map(availableTabs.indexOf)),
            style = Tab.Style.Minimal(),
            panels = availableTabs.map { profileTab =>
              val profiles = profileLists.getListByTab(profileTab)
              Tab.Panel(
                title = div(
                  tw.flex.itemsCenter.spaceX8,
                  div(profileTab.value),
                  div(
                    child <-- hasResolveAllDuplicateProfilesSignal.map { hasResolveAllDuplicates =>
                      BadgeL(
                        color = getTabBadgeColor(profileTab, hasResolveAllDuplicates),
                        theme = Badge.Theme.Light,
                        count = Val(Some(profiles.size))
                      )()
                    }
                  )
                )
              )
            },
            onClick = Observer[Int] { tabIndex =>
              curSelectedTabOptVar.set(availableTabs.lift(tabIndex))
            }
          )().amend(tw.flexNone)
        )
      },
      div(
        tw.flexFill.pb16,
        child.maybe <-- curSelectedTabOptVar.signal.map {
          _.map {
            case ProfileTab.DuplicateProfileTab =>
              DuplicatedTab(
                firmId = firmId,
                profileInfos = profileLists.getListByTab(ProfileTab.DuplicateProfileTab),
                searchTermSignal = searchVar.signal,
                computedProfilesSignal = computedProfilesSignal,
                isComputeProfilesFinishedSignal = isComputeProfilesFinishedSignal,
                mappedProfilesSignal = selectedDuplicatedProfilesVar.signal.distinct,
                onMapProfile = Observer { case (id, ieIdOpt) =>
                  selectedDuplicatedProfilesVar.update(_.updated(id, ieIdOpt))
                },
                hasResolveAllDuplicateProfilesSignal = hasResolveAllDuplicateProfilesSignal,
                accessibleInvestmentEntities = profileLists.accessibleInvestmentEntities,
                accessibleInvestors = profileLists.accessibleInvestors,
                formData = stepInput.formData,
                manuallyResolvedProfilesSignal = manuallyResolvedProfilesVar.signal,
                onManuallyResolvedProfile = Observer { case (id, profile) =>
                  manuallyResolvedProfilesVar.update(_.updated(id, profile))
                },
                onClearResolvedProfile = Observer { id =>
                  manuallyResolvedProfilesVar.update(_.filterNot(_._1 == id))
                }
              )()
            case ProfileTab.CannotImportTab =>
              CannotImportTab(
                firmId = firmId,
                profileInfos = profileLists.getListByTab(ProfileTab.CannotImportTab),
                searchTermSignal = searchVar.signal.distinct
              )()
            case ProfileTab.UpdateProfileTab =>
              UpdateTab(
                firmId = firmId,
                profileInfos = profileLists.getListByTab(ProfileTab.UpdateProfileTab),
                searchTermSignal = searchVar.signal.distinct,
                computedProfilesSignal = computedProfilesSignal,
                isComputeProfilesFinishedSignal = isComputeProfilesFinishedSignal,
                selectedProfilesSignal = selectedUpdatingProfilesVar.signal,
                onSelectedProfiles = selectedUpdatingProfilesVar.writer,
                formData = stepInput.formData,
                manuallyResolvedProfilesSignal = manuallyResolvedProfilesVar.signal,
                onManuallyResolvedProfile = Observer { case (id, profile) =>
                  manuallyResolvedProfilesVar.update(_.updated(id, profile))
                }
              )()
          }
        }
      )
    )
  }

  private def getInvestorBasic = {
    FundDataInvestorEndpointClient
      .getInvestorsBasic(GetInvestorsBasicParams(firmId))
      .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
      .map(
        _.fold(
          _ => {
            Toast.error("Failed to load clients")
            None
          },
          investors => {
            Some(investors)
          }
        )
      )
  }

  private def getInvestmentEntityBasic = {
    FundDataInvestmentEntityEndpointClient
      .getInvestmentEntitiesBasic(GetInvestmentEntitiesBasicParams(firmId, GetInvestmentEntitiesBasicParams.GetBy.All))
      .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
      .map(
        _.fold(
          _ => {
            Toast.error("Failed to load investment entities")
            None
          },
          investmentEntities => {
            Some(investmentEntities)
          }
        )
      )
  }

  private val computeProfilesBatchSize = 5

  private def computeProfilesDataFromSpreadsheet(
    firmId: FundDataFirmId,
    accessibleInvestmentEntities: List[FundDataInvestmentEntityBasic],
    profileInfos: List[ProfileInfo]
  ) = {
    for {
      _ <- ZIO.attempt(isComputeProfilesFinishedVar.set(false))
      profileInfoAndMatchingIes <- ZIO.attempt {
        profileInfos.flatMap { profileInfo =>
          matchedInvestmentEntities(profileInfo, accessibleInvestmentEntities).map(profileInfo -> _)
        }
      }
      _ <- ZIO.foreach(profileInfoAndMatchingIes.sliding(computeProfilesBatchSize, computeProfilesBatchSize).toList) {
        profiles =>
          FundDataInvestmentEntityEndpointClient
            .computeProfilesDataFromSpreadsheet(
              ComputeProfilesDataFromSpreadsheetParams(
                firmId = firmId,
                profiles = profiles.map { case (profileInfo, ie) =>
                  ComputeProfilesDataFromSpreadsheetParams.ProfileInfo(
                    investmentEntityId = ie.id,
                    formTemplateMappingVersionId = stepInput.templateMapping.mappingId,
                    data = profileInfo.fieldValueMap,
                    frontendTrackingId = profileInfo.id
                  )
                }
              )
            )
            .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
            .map(
              _.fold(
                _ => Toast.error("Unable to compute profiles data from spreadsheet. Please try again."),
                computedProfiles => computedProfilesVar.update(_ ++ computedProfiles)
              )
            )
      }
      _ <- ZIO.attempt(isComputeProfilesFinishedVar.set(true))
    } yield ()
  }

  private def importProfilesDataFromSpreadsheet(
    computedProfilesNew: Map[String, ComputeProfilesDataFromSpreadsheetResp]
  ) = {
    val profiles = profileListsVar
      .now()
      .fold(
        List.empty[ImportProfilesDataFromSpreadsheetParams.SingleImportProfile]
      ) { profileLists =>
        val updatingProfiles =
          profileLists.updatingProfiles
            .filter(profile => selectedUpdatingProfilesVar.now().contains(profile.id))
            .flatMap { profile =>
              profile.matchedInvestmentEntityInfo.valueOpt.map(profile -> _.data)
            }
        val duplicatedProfiles = profileLists.duplicatedProfiles.flatMap { profile =>
          selectedDuplicatedProfilesVar.now().get(profile.id).flatten.flatMap { ieId =>
            profileLists.accessibleInvestmentEntities.find(_.id == ieId).map(profile -> _)
          }
        }
        val convertedProfiles = (updatingProfiles ++ duplicatedProfiles).flatMap { case (profile, investmentEntity) =>
          computedProfilesNew
            .get(profile.id)
            .filter(_.investmentEntityId == investmentEntity.id)
            .map { computedProfile =>
              val manualResolvedProfileOpt = manuallyResolvedProfilesVar.now().get(profile.id)
              ImportProfilesDataFromSpreadsheetParams.SingleImportProfile(
                investmentEntityId = computedProfile.investmentEntityId,
                investmentEntityNameOrCustomId = profile.investmentEntityNameOrTrackingId.extractedData.rawValue,
                matchedInvestmentEntityName =
                  investmentEntity.name == profile.investmentEntityNameOrTrackingId.extractedData.rawValue,
                profileFormSource = manualResolvedProfileOpt.fold(
                  ImportProfilesDataFromSpreadsheetParams.ProfileFormSource
                    .FromComputed(computedProfile.formVersionDataId)
                ) { manuallyResolvedProfile =>
                  ImportProfilesDataFromSpreadsheetParams.ProfileFormSource
                    .ManuallyResolved(manuallyResolvedProfile.gaiaState)
                },
                visibleNonEmptyFieldCount =
                  manualResolvedProfileOpt.fold(computedProfile.visibleNonEmptyFieldCount)(_.visibleNonEmptyFieldCount),
                hiddenNonEmptyFieldCount =
                  manualResolvedProfileOpt.fold(computedProfile.hiddenNonEmptyFieldCount)(_.hiddenNonEmptyFieldCount)
              )
            }
        }
        convertedProfiles
      }
    val task = for {
      _ <- ZIO.attempt(isImportingVar.set(true))
      _ <- FundDataInvestmentEntityEndpointClient
        .importProfilesDataFromSpreadsheet(
          ImportProfilesDataFromSpreadsheetParams(
            firmId = firmId,
            profiles = profiles
          )
        )
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold(
            _ => {
              isImportingVar.set(false)
              Toast.error("Unable to import profile data from spreadsheet. Please try again.")
            },
            _ => {
              isImportingVar.set(false)
              onCancel.onNext(())
            }
          )
        )
    } yield ()
    ZIOUtils.runAsync(task)
  }

  private def extractAndValidateData = {
    val task = for {
      accessibleInvestorsOpt <- getInvestorBasic
      accessibleInvestors <- ZIO.getOrFail(accessibleInvestorsOpt)
      accessibleInvestmentEntitiesOpt <- getInvestmentEntityBasic
      accessibleInvestmentEntities <- ZIO.getOrFail(accessibleInvestmentEntitiesOpt)
      data <- extractData(
        stepInput.rawDatas,
        accessibleInvestmentEntities
      )
      commonCriteriaValidatedData <- validateCommonCriteria(data, accessibleInvestmentEntities)
      (validProfiles, invalidProfiles) <- partitionByValid(
        commonCriteriaValidatedData
      )
      _ <- ZIO.attempt {
        val (duplicatedProfiles, updatingProfiles) = validProfiles.partition { profile =>
          isDuplicatedProfile(profile, accessibleInvestmentEntities)
        }
        Var.set(
          profileListsVar ->
            Some(
              ProfileLists(
                invalidProfiles = invalidProfiles.toList,
                updatingProfiles = updatingProfiles.toList,
                duplicatedProfiles = duplicatedProfiles.toList,
                accessibleInvestmentEntities = accessibleInvestmentEntities,
                accessibleInvestors = accessibleInvestors
              )
            ),
          selectedUpdatingProfilesVar -> updatingProfiles.map(_.id).toList
        )
      }
      _ <- computeProfilesDataFromSpreadsheet(firmId, accessibleInvestmentEntities, validProfiles.toList)
    } yield ()
    task.catchAll(_ => ZIO.attempt(Toast.error("Failed to extract and validate data")))
  }

  def apply(): HtmlElement = {
    DefaultLayout(
      body = () =>
        div(
          tw.pt20.px32,
          tw.hPc100.wPc100,
          tw.flex.justifyCenter,
          div(
            tw.wPc100.hPc100.flex.flexCol,
            maxWidth.px(1280),
            renderTitleAndSearchBar.amend(tw.mb16),
            div(
              tw.flexFill,
              child <-- profileListsVar.signal.distinct.map {
                _.fold(
                  BlockIndicatorL(
                    title = Val(Some("Extracting and validating information")),
                    isFullHeight = true
                  )().amend(tw.hPc100)
                ) { profileLists =>
                  renderMainContent(profileLists)
                }
              }
            ),
            onMountCallback(_ => ZIOUtils.runAsync(extractAndValidateData))
          )
        ),
      footer = () =>
        div(
          tw.flex.justifyCenter,
          div(
            tw.wPc100,
            maxWidth.px(1280),
            tw.flex.justifyBetween.itemsCenter,
            renderBackButton(() => "Back"),
            div(
              tw.flex.itemsCenter.justifyEnd,
              child.maybe <-- hasResolveAllDuplicateProfilesSignal.map {
                Option.unless(_) {
                  div(
                    tw.mr8.textWarning5,
                    "Select an action for profiles with multiple matches before importing"
                  )
                }
              },
              CancelImportBySpreadsheetModal(onCancel)().amend(tw.mr8),
              div(
                child <-- numImportedProfilesSignal.combineWith(computedProfilesSignal).distinct.map {
                  case (numImportedProfiles, computedProfiles) =>
                    ButtonL(
                      style = ButtonL.Style.Full(color = ButtonL.Color.Primary, isBusy = isImportingVar.signal),
                      onClick = Observer { _ => importProfilesDataFromSpreadsheet(computedProfiles) },
                      isDisabled = Val(
                        numImportedProfiles == 0
                      ) || !hasResolveAllDuplicateProfilesSignal || !isComputeProfilesFinishedSignal
                    )(
                      s"Import ${Pluralize(
                          "profile",
                          numImportedProfiles,
                          true
                        )}"
                    )
                }
              )
            )
          )
        )
    )()
  }

}

private[importbyspreadsheetv2] object ReviewStepUI {

  enum ProfileTab(val value: String) extends StringEnum {
    case DuplicateProfileTab extends ProfileTab("Multiple matches found")
    case CannotImportTab extends ProfileTab("Skipped profiles")
    case UpdateProfileTab extends ProfileTab("Ready to import")
  }

  def getTabBadgeColor(profileTab: ProfileTab, hasResolveAllDuplicates: Boolean): Badge.Color = {
    profileTab match {
      case ProfileTab.DuplicateProfileTab =>
        if (hasResolveAllDuplicates) {
          Badge.Color.Gray
        } else {
          Badge.Color.Warning
        }
      case ProfileTab.CannotImportTab => Badge.Color.Danger
      case _                          => Badge.Color.Gray
    }
  }

  final case class ProfileLists(
    invalidProfiles: List[ImportProfileBySpreadSheetFlowData.ProfileInfo],
    updatingProfiles: List[ImportProfileBySpreadSheetFlowData.ProfileInfo],
    duplicatedProfiles: List[ImportProfileBySpreadSheetFlowData.ProfileInfo],
    accessibleInvestmentEntities: List[FundDataInvestmentEntityBasic],
    accessibleInvestors: List[FundDataInvestorBasic]
  ) {

    def getListByTab(profileTab: ProfileTab): List[ImportProfileBySpreadSheetFlowData.ProfileInfo] = {
      profileTab match {
        case ProfileTab.DuplicateProfileTab => duplicatedProfiles
        case ProfileTab.CannotImportTab     => invalidProfiles
        case ProfileTab.UpdateProfileTab    => updatingProfiles
      }
    }

  }

  final case class ManualResolveFormData(
    gaiaState: GaiaState,
    visibleNonEmptyFieldCount: Int,
    hiddenNonEmptyFieldCount: Int
  )

}
