// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investmententity.document

import java.time.Instant

import com.raquo.laminar.api.L.*
import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.laminar.InitialAvatarL
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.button.laminar.ButtonL.Height
import design.anduin.components.field.laminar.FieldL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.{ModalFooterWCancelL, ModalL}
import design.anduin.components.progress.laminar.{BlockIndicatorL, CircleIndicatorL}
import design.anduin.components.suggest.laminar.MultiSuggestL
import design.anduin.components.switcher.laminar.SwitcherL
import design.anduin.components.tag.Tag
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.validators.rules.EmailAddressRule
import io.circe.Codec
import io.github.arainko.ducktape.*
import org.scalajs.dom
import org.scalajs.dom.MouseEvent
import zio.ZIO

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.funddata.api.WithEmailTemplate
import anduin.funddata.api.contact.WithInvestmentEntityContactsInfo
import anduin.funddata.client.{FundDataInvestmentEntityEndpointClient, FundDataRequestEndpointClient}
import anduin.funddata.common.email.{EmailTemplateEditor, SelectEmailTemplateDropdown}
import anduin.funddata.endpoint.contact.FundDataContact.{DocumentRequestCommunicationType, InvestmentEntityContact}
import anduin.funddata.endpoint.email.{FundDataEmailTemplate, GetEmailTemplateResponse}
import anduin.funddata.endpoint.investmententity.*
import anduin.funddata.endpoint.request.GetRequestConfigParams
import anduin.funddata.endpoint.tag.FundDataTagItem
import anduin.funddata.investmententity.document.RequestDocumentButton.{DocumentInfo, DocumentRequestItemDescription}
import anduin.funddata.utils.FundDataDocumentUtils
import anduin.id.funddata.{FundDataInvestmentEntityDocumentId, FundDataInvestmentEntityId}
import anduin.id.tag.TagItemId
import anduin.model.id.FileId
import anduin.protobuf.funddata.email.`type`.FundDataEmailType
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils

private[funddata] final case class RequestDocumentButton(
  investmentEntityId: FundDataInvestmentEntityId,
  investmentEntityName: String,
  firmName: String,
  documentsSignal: Signal[List[DocumentInfo]],
  documentTypeItems: List[FundDataTagItem],
  onRefetchDocumentList: Observer[Unit],
  onClose: Observer[Unit],
  renderTarget: RequestDocumentButton.RenderTargetProps => HtmlElement
) {

  private val isModalOpeningVar = Var[Boolean](false)
  private val isRequestCreatingVar = Var[Boolean](false)

  private val requestedRecipientsEmailsVar = Var[Seq[String]](Seq.empty)
  private val isEnabledRecipientsViewDocumentsVar = Var(false)
  private val initialEmailTemplateVar = Var[FundDataEmailTemplate](FundDataEmailTemplate())
  private val emailTemplateVar = Var[FundDataEmailTemplate](FundDataEmailTemplate())
  private val loadingRequestConfigVar: Var[Boolean] = Var(true)

  private val requestedDocumentsVar = Var(
    Map.empty[FundDataInvestmentEntityDocumentId, DocumentRequestItemDescription]
  )

  private val notRequestedDocumentsSignal =
    requestedDocumentsVar.signal.combineWith(documentsSignal).distinct.map { (requestedDocuments, documents) =>
      documents.filter(document => !requestedDocuments.contains(document.documentId))
    } // Including already requested documents

  private val isCanBeRequestedDocumentsExistedSignal = notRequestedDocumentsSignal.map(_.exists(_.requestStatus.isEmpty))

  private val isDataValidSignal =
    requestedRecipientsEmailsVar.signal
      .combineWith(emailTemplateVar.signal, requestedDocumentsVar.signal)
      .distinct
      .map { (requestedRecipientsEmails, emailTemplate, requestedDocuments) =>
        requestedRecipientsEmails.nonEmpty && requestedDocuments.nonEmpty && emailTemplate.primaryCTA.nonEmpty && emailTemplate.subject.nonEmpty
        && requestedRecipientsEmails.forall(_.matches(EmailAddressRule.emailRegex))
      }

  val mapConversion: Conversion[Map[FundDataInvestmentEntityDocumentId, DocumentInfo], Map[
    FundDataInvestmentEntityDocumentId,
    DocumentRequestItemDescription
  ]] = _.view
    .mapValues(document =>
      DocumentRequestItemDescription(
        documentId = document.documentId,
        latestFileId = document.latestFileId,
        documentName = document.documentName,
        documentTypeDescription = document.documentType
          .flatMap(tagId => documentTypeItems.find(_.tagItemId == tagId))
          .fold("")(_.name),
        additionalNote = ""
      )
    )
    .toMap

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL("RequestDocumentButton"),
      renderTarget(
        RequestDocumentButton.RenderTargetProps(
          onOpenRequestDocumentModal =
            Observer[Map[FundDataInvestmentEntityDocumentId, DocumentInfo]] { preSelectedDocuments =>
              Var.set(
                // Open modal
                isModalOpeningVar -> true,
                // Reset Variables
                isRequestCreatingVar -> false,
                requestedRecipientsEmailsVar -> Seq.empty,
                emailTemplateVar -> FundDataEmailTemplate(),
                requestedDocumentsVar -> mapConversion.apply(preSelectedDocuments),
                loadingRequestConfigVar -> true
              )
            }
        )
      ),
      ModalL(
        renderTitle = _ => "Request document update",
        renderContent = _ => renderContent(),
        isOpened = Some(isModalOpeningVar.signal),
        isClosable = None,
        size = ModalL.Size(width = ModalL.Width.Px720)
      )()
    )
  }

  private def onCreateRequest(): Unit = {
    ZIOUtils.runAsync {
      for {
        _ <- ZIO.attempt(isRequestCreatingVar.set(true))
        _ <- FundDataInvestmentEntityEndpointClient
          .createDocumentRequest(
            CreateDocumentRequestParams(
              investmentEntityId = investmentEntityId,
              recipientsEmails = requestedRecipientsEmailsVar.now(),
              requestingDocumentsDescriptions =
                requestedDocumentsVar.now().values.map(_.into[RequestingDocumentDescription].transform()).toSeq,
              sendRequestEmailTemplate = emailTemplateVar.now(),
              isDisabledRecipientsViewDocuments = !isEnabledRecipientsViewDocumentsVar.now()
            )
          )
          .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
          .map {
            _.fold(
              _ => {
                Toast.error(s"Failed to send request")
                isRequestCreatingVar.set(false)
              },
              _ => {
                Toast.success("Request sent successfully")
                isRequestCreatingVar.set(false)
                isModalOpeningVar.set(false)
                onClose.onNext(())
                onRefetchDocumentList.onNext(())
              }
            )
          }
      } yield ()
    }
  }

  private def renderAddRecipientsInputSection(
    contacts: List[InvestmentEntityContact]
  ) = {
    val itemsSignal = requestedRecipientsEmailsVar.signal.distinct
      .map { requestedRecipientsEmails =>
        contacts
          .filter { contact =>
            !requestedRecipientsEmails.contains(contact.contactInfo.email)
          }
          .map(
            MultiSuggestL.Item.apply(_)
          )
      }
    MultiSuggestL[InvestmentEntityContact](
      value = requestedRecipientsEmailsVar.signal,
      selectedTag = MultiSuggestL.SelectedTag(
        label = requestedRecipientEmail =>
          contacts
            .find(_.contactInfo.email == requestedRecipientEmail)
            .map(_.contactInfo.fullName)
            .getOrElse(requestedRecipientEmail),
        color = requestedRecipientEmail =>
          if (requestedRecipientEmail.matches(EmailAddressRule.emailRegex)) { Tag.Light.Gray }
          else { Tag.Light.Danger }
      ),
      items = itemsSignal,
      valueToString = _.contactInfo.email,
      optionSelectKeys = List(
        "Enter",
        "Space",
        ",",
        ";",
        " "
      ),
      onChange = Observer[Seq[String]] { emailStr => requestedRecipientsEmailsVar.set(emailStr.distinct) },
      target = MultiSuggestL.Target(placeholder = "Select recipients"),
      content = MultiSuggestL.Content[InvestmentEntityContact](
        renderItemBody = Some({ props =>
          div(
            tw.flex.itemsCenter,
            InitialAvatarL(
              id = props.value.contactInfo.email,
              initials = Val(props.value.contactInfo.fullName),
              size = InitialAvatar.Size.Px20
            )().amend(tw.flex.flexNone),
            div(
              tw.ml8.heading5.maxWPx256.flex.flexNone,
              TruncateL(target = div(props.value.contactInfo.fullName))()
            ),
            div(
              tw.ml8,
              TagL(label = Val(props.value.contactInfo.email))()
            )
          )
        }),
        filterItemList = Some({ props =>
          props.items.filter(_.value.contactInfo.fullName.toLowerCase.contains(props.keyword.toLowerCase))
        })
      )
    )()
  }

  private def renderSelectDocumentsButtons() = {
    ModalL(
      renderTitle = _ => "Select documents",
      renderContent = onClose =>
        SelectDocumentsToRequestModalContent(
          notRequestedDocumentsSignal,
          documentTypeItems,
          onClose,
          Observer[Map[FundDataInvestmentEntityDocumentId, DocumentInfo]] { justSelectedDocuments =>
            requestedDocumentsVar.update(_ ++ mapConversion(justSelectedDocuments))
          }
        )(),
      isClosable = None,
      size = ModalL.Size(width = ModalL.Width.Px960),
      renderTarget = open =>
        ButtonL(
          style = ButtonL.Style.Full(
            height = Height.Fix24,
            color = ButtonL.Color.Primary
          ),
          isDisabled = isCanBeRequestedDocumentsExistedSignal.map(!_),
          onClick = open.contramap(_ => ())
        )(
          span(
            child <-- requestedDocumentsVar.signal.map { requestedDocuments =>
              if (requestedDocuments.isEmpty) "Select documents" else "Select more"
            }
          )
        )
    )()
  }

  private def renderRemoveDocumentButton(fundDataInvestmentEntityDocumentId: FundDataInvestmentEntityDocumentId) = {
    ButtonL(
      style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.Cross)),
      onClick = Observer[dom.MouseEvent] { _ =>
        requestedDocumentsVar.update(_.removed(fundDataInvestmentEntityDocumentId))
      }
    )()
  }

  private def renderSelectDocumentsInputSection() = {
    div(
      tw.wPc100,
      div(
        tw.my8.textGray7,
        "Select documents you'd like the recipients to update."
      ),
      child <-- loadingRequestConfigVar.signal.map {
        if (_) {
          div(tw.wPc100.flex.justifyAround, CircleIndicatorL()())
        } else {
          div(
            tw.mb12.wPc100.flex.itemsCenter.rounded8.p12.spaceX8.bgGray2,
            SwitcherL(
              isChecked = isEnabledRecipientsViewDocumentsVar.signal,
              onChange = isEnabledRecipientsViewDocumentsVar.writer
            )("Allow recipients to view and download requested documents"),
            TooltipL(
              renderContent = _.amend(
                width.px(300),
                "Turn off this setting to protect document confidentiality. Recipients will only see the file name, document type, and any additional description."
              ),
              renderTarget = IconL(name = Val(Icon.Glyph.Question))()
            )()
          )
        }
      },
      children <-- requestedDocumentsVar.signal.distinct.map(_.values.toSeq).split(_.documentId) {
        case (documentId, _, requestedDocumentSignal) =>
          div(
            tw.flex.itemsStart.wPc100.mb8.p8,
            tw.borderAll.rounded8.borderGray3.border1,
            div(
              tw.flex.itemsStart.flexFill,
              child <-- requestedDocumentSignal.map { requestedDocument =>
                div(
                  tw.wPc50,
                  TruncateL(target = div(requestedDocument.documentName))(),
                  div(
                    tw.mt4.textSmall.textGray7,
                    requestedDocument.documentTypeDescription
                  )
                )
              },
              div(
                tw.wPc50.pl16,
                FieldL(
                  requirement = FieldL.Requirement.Optional
                )(
                  TextBoxL(
                    placeholder = "Enter additional description for recipients",
                    value = requestedDocumentSignal.map(_.additionalNote),
                    onChange = Observer[String] { description =>
                      requestedDocumentsVar.update(oldValue =>
                        oldValue.updatedWith(documentId)(itemOpt => itemOpt.map(_.copy(additionalNote = description)))
                      )
                    },
                    tpe = TextBoxL.Tpe.Area(3),
                    unsafeMod = Seq(minHeight.px(32))
                  )()
                )
              )
            ),
            div(tw.flexNone.ml16, renderRemoveDocumentButton(documentId))
          )
      },
      div(
        tw.flex.itemsCenter,
        renderSelectDocumentsButtons(),
        child.maybe <-- isCanBeRequestedDocumentsExistedSignal.map { existCanBeRequestedDocuments =>
          Option.when(!existCanBeRequestedDocuments) {
            div(tw.textGray7.textSmall.ml8, "You've already selected all documents")
          }
        }
      )
    )
  }

  private def renderContent() = {
    div(
      tw.wPc100,
      WithEmailTemplate(
        firmId = investmentEntityId.parent.parent,
        emailType = FundDataEmailType.NotifyRequestSentToRecipients,
        render = emailData => {
          WithInvestmentEntityContactsInfo(
            investmentEntityId = investmentEntityId,
            render = contactsData => renderCreateRequestFormContent(emailData.isGettingSignal, contactsData)
          )()
        },
        onDidFetch = Observer[GetEmailTemplateResponse] { (getEmailResponse: GetEmailTemplateResponse) =>
          Var.set(
            emailTemplateVar -> getEmailResponse.defaultEmailTemplate.emailContent,
            initialEmailTemplateVar -> getEmailResponse.defaultEmailTemplate.emailContent
          )
        }
      )(),
      ModalFooterWCancelL(
        cancel = Observer[Unit] { _ =>
          onClose.onNext(())
          isModalOpeningVar.set(false)
        },
        cancelLabel = "Cancel"
      )(
        div(
          tw.flex.justifyEnd,
          ButtonL(
            style = ButtonL.Style.Full(color = ButtonL.Color.Primary, isBusy = isRequestCreatingVar.signal),
            onClick = Observer[MouseEvent] { _ =>
              onCreateRequest()
            },
            isDisabled = isDataValidSignal.map(!_)
          )("Send Request")
        )
      )
    )
  }

  private def renderCreateRequestFormContent(
    isGettingEmailTemplateSignal: Signal[Boolean],
    contactsData: WithInvestmentEntityContactsInfo.RenderProps
  ) = {
    div(
      tw.pt20.pb24,
      paddingLeft := "28px",
      paddingRight := "28px",
      child <-- (contactsData.isGettingSignal || isGettingEmailTemplateSignal)
        .splitBoolean(
          whenTrue = _ =>
            BlockIndicatorL(
              title = Signal.fromValue(Some("Loading...")),
              isFullHeight = true
            )().amend(minHeight.px(450)),
          whenFalse = _ =>
            div(
              div(
                tw.flex.itemsCenter,
                div(
                  tw.textGray7.flexFill,
                  "Customize message send to recipients"
                ),
                SelectEmailTemplateDropdown(
                  firmId = investmentEntityId.parent.parent,
                  emailType = FundDataEmailType.NotifyRequestSentToRecipients,
                  renderTarget = (toggle, isOpen) =>
                    div(
                      tw.mb16,
                      ButtonL(
                        style = ButtonL.Style.Full(
                          isSelected = isOpen,
                          height = ButtonL.Height.Fix24
                        ),
                        onClick = toggle.contramap(_ => ())
                      )(
                        div(
                          tw.flex.itemsCenter,
                          "Prefill with template",
                          IconL(name = Val(Icon.Glyph.CaretDown))().amend(tw.ml4)
                        )
                      )
                    ),
                  onSelectTemplate = Observer[FundDataEmailTemplate] { selectedSavedTemplate =>
                    Var.set(
                      initialEmailTemplateVar -> selectedSavedTemplate,
                      emailTemplateVar -> selectedSavedTemplate
                    )
                  }
                )()
              ),
              child <-- contactsData.contactsInfoSignal.map { contactsData =>
                div(
                  onMountCallback { _ =>
                    getRequestConfig()
                    requestedRecipientsEmailsVar.set(
                      contactsData
                        .filter(_.documentRequestCommunicationType == DocumentRequestCommunicationType.Receiver)
                        .map(_.contactInfo.email)
                    )
                  },
                  tw.mb16,
                  child <-- loadingRequestConfigVar.signal.map {
                    if (_) {
                      div(tw.wPc100.flex.justifyAround, CircleIndicatorL()())
                    } else {
                      FieldL(
                        label = Option("Recipients"),
                        requirement = FieldL.Requirement.Required
                      )(
                        renderAddRecipientsInputSection(contactsData)
                      )
                    }
                  }
                )
              },
              child <-- initialEmailTemplateVar.signal.distinct.map { initialEmailTemplate =>
                EmailTemplateEditor(
                  initialEmailTemplate = initialEmailTemplate,
                  emailTemplateVar = emailTemplateVar,
                  customSubjectLabelRendererOpt = Some(() =>
                    div(
                      tw.flex.itemsCenter,
                      "Subject",
                      span(tw.textDanger5.ml4.fontNormal, "*"),
                      TooltipL(
                        renderContent = _.amend("This is also the request name"),
                        renderTarget = span(
                          tw.textGray7,
                          IconL(name = Val(Icon.Glyph.Question), size = Icon.Size.Custom(12))()
                        )
                      )().amend(tw.ml8)
                    )
                  )
                )()
              },
              div(
                tw.mt16,
                FieldL(
                  label = Option("Documents"),
                  requirement = FieldL.Requirement.Required
                )(
                  renderSelectDocumentsInputSection()
                )
              )
            )
        )
    )
  }

  private def getRequestConfig(): Unit = {
    val task = for {
      _ <- FundDataRequestEndpointClient
        .getRequestConfig(GetRequestConfigParams(investmentEntityId.parent.parent))
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold(
            _ => {
              loadingRequestConfigVar.set(false)
            },
            resp => {
              Var.set(
                loadingRequestConfigVar -> false,
                isEnabledRecipientsViewDocumentsVar -> resp.enableDocumentAccess
              )
            }
          )
        )
    } yield ()
    if (loadingRequestConfigVar.now()) ZIOUtils.runAsync(task) // Only prefill config on the first load
  }

}

private[funddata] object RequestDocumentButton {

  given Conversion[FundDataInvestmentEntityDocument, DocumentInfo] = value => {
    value.into[DocumentInfo].transform()
  }

  given Conversion[SingleDocumentInfo, DocumentInfo] = value => {
    value.into[DocumentInfo].transform()
  }

  private def getListConversion[A, B](
    using Conversion[A, B]
  ): Conversion[List[A], List[B]] = value => {
    value.map(summon[Conversion[A, B]])
  }

  private def getMapConversion[K, A, B](
    using Conversion[A, B]
  ): Conversion[
    Map[K, A],
    Map[K, B]
  ] = value => {
    value.view
      .mapValues(summon[Conversion[A, B]])
      .toMap
  }

  private def getSignalConversion[A, B](
    using Conversion[A, B]
  ): Conversion[Signal[A], Signal[B]] = value => {
    value.map(summon[Conversion[A, B]])
  }

  given c1: Conversion[List[FundDataInvestmentEntityDocument], List[DocumentInfo]] =
    getListConversion[FundDataInvestmentEntityDocument, DocumentInfo]

  given c2: Conversion[
    Map[FundDataInvestmentEntityDocumentId, FundDataInvestmentEntityDocument],
    Map[FundDataInvestmentEntityDocumentId, DocumentInfo]
  ] =
    getMapConversion[
      FundDataInvestmentEntityDocumentId,
      FundDataInvestmentEntityDocument,
      DocumentInfo
    ]

  given c3: Conversion[Signal[List[FundDataInvestmentEntityDocument]], Signal[List[DocumentInfo]]] =
    getSignalConversion[List[FundDataInvestmentEntityDocument], List[DocumentInfo]]

  given c4: Conversion[List[SingleDocumentInfo], List[DocumentInfo]] =
    getListConversion[SingleDocumentInfo, DocumentInfo]

  given c5: Conversion[
    Map[FundDataInvestmentEntityDocumentId, SingleDocumentInfo],
    Map[FundDataInvestmentEntityDocumentId, DocumentInfo]
  ] =
    getMapConversion[
      FundDataInvestmentEntityDocumentId,
      SingleDocumentInfo,
      DocumentInfo
    ]

  given c6: Conversion[Signal[List[SingleDocumentInfo]], Signal[List[DocumentInfo]]] =
    getSignalConversion[List[SingleDocumentInfo], List[DocumentInfo]]

  private[document] case class RenderTargetProps(
    onOpenRequestDocumentModal: Observer[Map[FundDataInvestmentEntityDocumentId, DocumentInfo]]
  )

  private[funddata] case class DocumentInfo(
    documentId: FundDataInvestmentEntityDocumentId,
    latestFileId: FileId,
    note: String,
    documentName: String,
    expireAt: Option[Instant],
    documentType: Option[TagItemId],
    requestStatus: Option[RequestedDocumentStatus]
  ) derives CanEqual {

    lazy val isExpiredOrExpiringSoon: Boolean = FundDataDocumentUtils.isExpiredOrExpiringSoon(expireAt, requestStatus)

  }

  object DocumentInfo {
    given Codec.AsObject[DocumentInfo] = deriveCodecWithDefaults
  }

  private[document] final case class DocumentRequestItemDescription(
    documentId: FundDataInvestmentEntityDocumentId,
    latestFileId: FileId,
    documentName: String,
    documentTypeDescription: String,
    additionalNote: String
  )

}
