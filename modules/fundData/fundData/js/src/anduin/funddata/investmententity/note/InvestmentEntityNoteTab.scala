//  Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investmententity.note

import anduin.id.funddata.FundDataInvestmentEntityId
import com.raquo.laminar.api.L.*
import design.anduin.components.progress.laminar.BlockIndicatorL
import zio.ZIO

import anduin.funddata.api.investmententity.note.WithInvestmentEntityNote
import anduin.funddata.client.FundDataInvestmentEntityEndpointClient
import anduin.funddata.endpoint.investmententity.note.{
  DeleteInvestmentEntityNoteParams,
  UpdateInvestmentEntityNoteParams
}
import anduin.funddata.note.FundDataNoteComponent
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils

private[investmententity] final case class InvestmentEntityNoteTab(
  investmentEntityId: FundDataInvestmentEntityId
) {

  def apply(): HtmlElement = {
    WithInvestmentEntityNote(investmentEntityId) { noteData =>
      div(
        child <-- noteData.isGettingSignal.splitBoolean(
          whenTrue = _ =>
            BlockIndicatorL(
              title = Signal.fromValue(Some("Loading note...")),
              isFullHeight = true
            )().amend(minHeight.px(450)),
          whenFalse = _ =>
            FundDataNoteComponent(
              noteOptSignal = noteData.investmentEntityNoteOptSignal,
              saveNote = Observer { case (content, onSuccess, onFailed) =>
                saveNote(
                  content,
                  onSuccess = Observer.combine(noteData.refetch, onSuccess),
                  onFailed = onFailed
                )
              },
              deleteNote = Observer { case (onSuccess, onFailed) =>
                deleteNote(onSuccess = Observer.combine(noteData.refetch, onSuccess), onFailed = onFailed)
              },
              customization = FundDataNoteComponent.Customization(
                emptyTitle = "This investment entity has no note",
                emptyDescription = "Add a note to provide more information about this investment entity",
                deleteModalTitle = "Delete investment entity note?",
                deleteModalDescription =
                  "Are you sure you want to delete this investment entity note? This action can't be undone."
              )
            )()
        )
      )
    }
  }

  private def saveNote(content: String, onSuccess: Observer[Unit], onFailed: Observer[Unit]) = {
    ZIOUtils.runAsync(
      FundDataInvestmentEntityEndpointClient
        .updateInvestmentEntityNote(
          UpdateInvestmentEntityNoteParams(investmentEntityId, content)
        )
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(_.fold(_ => onFailed.onNext(()), _ => onSuccess.onNext(())))
    )
  }

  private def deleteNote(onSuccess: Observer[Unit], onFailed: Observer[Unit]) = {
    ZIOUtils.runAsync(
      FundDataInvestmentEntityEndpointClient
        .deleteInvestmentEntityNote(
          DeleteInvestmentEntityNoteParams(investmentEntityId)
        )
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(_.fold(_ => onFailed.onNext(()), _ => onSuccess.onNext(())))
    )
  }

}
