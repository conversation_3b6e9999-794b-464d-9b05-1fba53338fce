// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.funddata.contact.`export`

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.progress.BarIndicator
import design.anduin.components.progress.laminar.BarIndicatorL
import design.anduin.components.toast.Toast

import anduin.batchaction.BatchActionProvider.RemoveBatchActionFrontendTrackingProps
import anduin.batchaction.endpoint.{BatchActionInfo, BatchActionItemStatusInfo}
import anduin.component.util.JavaScriptUtils
import anduin.frontend.AirStreamUtils
import anduin.funddata.common.widget.ActionWidget
import anduin.funddata.endpoint.contact.ExportContactsBatchItemResp
import anduin.id.batchaction.BatchActionId
import design.anduin.style.tw.*
import japgolly.scalajs.react.callback.Callback
import org.scalajs.dom
import zio.ZIO

private[funddata] case class ExportContactsToSpreadsheetTrackingModal(
  batchActionInfoSignal: Signal[BatchActionInfo],
  removeBatchAction: Observer[RemoveBatchActionFrontendTrackingProps]
) {

  private val isRemovingVar = Var[Boolean](false)

  def apply(): HtmlElement = {
    div(
      child <-- batchActionInfoSignal
        .map { action =>
          action.batchActionId -> action.isCompleted
        }
        .distinct
        .map { case (batchActionId, isCompleted) =>
          if (!isCompleted) {
            renderProgress(batchActionId)
          } else {
            ActionWidget(
              div(
                tw.wPc100,
                child <-- batchActionInfoSignal
                  .map(batchActionInfo => (batchActionInfo.postExecuteStatus, batchActionInfo.batchActionId))
                  .distinct
                  .map { case (status, batchActionId) =>
                    val urlOpt = status.match {
                      case BatchActionItemStatusInfo.Succeeded(_, dataOpt) =>
                        dataOpt.fold(Option.empty[String])(data =>
                          data.as[ExportContactsBatchItemResp].map(_.fileUrl).toOption
                        )
                      case _ => Option.empty[String]
                    }
                    urlOpt.fold(renderFailed(batchActionId)) { urlDownload =>
                      renderCompleted(batchActionId, urlDownload).amend(width.px(460))
                    }
                  }
              )
            )()
          }
        }
    )
  }

  private def renderProgress(batchActionId: BatchActionId) = {
    ActionWidget(
      renderBody = {
        div(
          tw.flex.flexCol.wPc100,
          div(
            tw.fontSemiBold.mb8,
            "Preparing your export"
          ),
          div(
            tw.textPrimary4.mb8,
            child.maybe <-- batchActionInfoSignal.map { batchActionInfo =>
              Option.when(batchActionInfo.totalItemCnt > 0) {
                BarIndicatorL(
                  percent = Val(Some(batchActionInfo.completedItemCnt * 1.0 / batchActionInfo.totalItemCnt)),
                  height = BarIndicator.Height.Medium
                )()
              }
            }
          ),
          div(
            tw.text11,
            "Please don’t close this window until your download starts"
          )
        )
      },
      renderActions = ActionWidget.RenderActions(
        actionProps = List(
          ActionWidget.ActionProps(
            action = "Cancel",
            isBusySignal = isRemovingVar.signal,
            onClick = Observer { _ => onRemoveBatchAction(batchActionId, shouldStopBatchAction = true) }
          )
        )
      )
    )()
  }

  private def renderFailed(batchActionId: BatchActionId) = {
    div(
      tw.flex.itemsCenter,
      IconL(name = Val(Icon.Glyph.Error), size = Icon.Size.Px24)().amend(tw.textDanger4.mr8),
      div(
        tw.fontSemiBold,
        "Failed to export investment entities. Please try again."
      ),
      automaticallyRemoveBatchAction(batchActionId)
    )
  }

  private def renderCompleted(batchActionId: BatchActionId, urlDownload: String) = {
    div(
      tw.flex.itemsCenter.justifyBetween.wPc100,
      AirStreamUtils.taskToStream {
        ZIO.attempt(JavaScriptUtils.downloadSync(urlDownload))
      } --> Observer.empty,
      div(
        tw.flex.itemsCenter,
        div(tw.textSuccess4.mr8, IconL(name = Val(Icon.Glyph.CheckCircle))()),
        div(
          tw.fontSemiBold,
          "Your export started downloading"
        )
      ),
      ButtonL(
        style = ButtonL.Style.Minimal(
          icon = Some(Icon.Glyph.Cross)
        ),
        onClick = Observer { _ =>
          onRemoveBatchAction(batchActionId)
        }
      )(),
      automaticallyRemoveBatchAction(batchActionId)
    )
  }

  private def automaticallyRemoveBatchAction(batchActionId: BatchActionId) = {
    onMountUnmountCallbackWithState[Div, Int](
      mount = _ => {
        dom.window.setTimeout(() => onRemoveBatchAction(batchActionId), 4000)
      },
      unmount = (_, timeOutOpt) => {
        timeOutOpt.foreach(dom.window.clearTimeout)
      }
    )
  }

  private def onRemoveBatchAction(
    batchActionId: BatchActionId,
    shouldStopBatchAction: Boolean = false
  ): Unit = {
    isRemovingVar.set(true)
    removeBatchAction.onNext(
      RemoveBatchActionFrontendTrackingProps(
        batchActionId,
        shouldStopBatchAction = shouldStopBatchAction,
        onSuccess = Callback.empty,
        onFailed = Callback {
          Toast.error("Failed to remove batch action. Please refresh")
          isRemovingVar.set(false)
        }
      )
    )
  }

}
