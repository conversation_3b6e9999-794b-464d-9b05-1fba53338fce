// Copyright (C) 2014-2024 Anduin Transaction Inc.

package anduin.funddata.contact.`export`

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL}
import design.anduin.components.toast.Toast
import zio.ZIO

import anduin.file.FileDownloaderUtils
import anduin.funddata.client.FundDataContactEndpointClient
import anduin.funddata.contact.`export`.ExportContactsModal.Mode
import anduin.funddata.endpoint.contact.{BatchExportContactsParams, ExportContactsParams}
import anduin.id.contact.ContactId
import anduin.id.funddata.FundDataFirmId
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.service.file.BatchDownloadRequest

private[contact] final case class ExportContactsModal(
  firmId: FundDataFirmId,
  exportModeSignal: Signal[ExportContactsModal.Mode],
  onCancel: Observer[Unit],
  onDone: Observer[Unit]
) {

  private val isExportingVar = Var[Boolean](false)
  private val exportContactsEventBus = new EventBus[Unit]

  def apply(): HtmlElement = {
    div(
      ModalBodyL(span(child <-- exportModeSignal.map { mode =>
        s"Are you sure you want to export ${mode match {
            case Mode.Single(contactId) => "this contact"
            case Mode.Batch(contactIds) => "selected contacts"
          }}?"
      })),
      ModalFooterWCancelL(cancel = onCancel, isCancelDisabled = isExportingVar.signal)(
        ButtonL(
          style = ButtonL.Style.Full(
            color = ButtonL.Color.Primary,
            isBusy = isExportingVar.signal
          ),
          onClick = exportContactsEventBus.writer.contramap(_ => ())
        )("Export")
      ),
      exportContactsEventBus.events.sample(exportModeSignal).flatMapSwitch(exportContacts) --> Observer.empty
    )
  }

  private def exportContacts(mode: ExportContactsModal.Mode): EventStream[Unit] = {
    val task = for {
      _ <- ZIO.attempt(isExportingVar.set(true))
      _ <- mode match {
        case Mode.Single(contactId) =>
          FundDataContactEndpointClient
            .exportContacts(
              ExportContactsParams(
                firmId = firmId,
                contactIds = Set(contactId)
              )
            )
            .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
            .map(
              _.fold(
                _ => {
                  isExportingVar.set(false)
                  Toast.error("Failed to export contacts, try again")
                },
                resp => {
                  FileDownloaderUtils.download(
                    BatchDownloadRequest(
                      fileIds = Seq(resp.fileId)
                    )
                  )
                  isExportingVar.set(false)
                  onDone.onNext(())
                }
              )
            )
        case Mode.Batch(contactIds) =>
          FundDataContactEndpointClient
            .batchExportContacts(
              BatchExportContactsParams(
                firmId = firmId,
                contactIds = contactIds
              )
            )
            .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
            .map(
              _.fold(
                _ => {
                  isExportingVar.set(false)
                  Toast.error("Failed to export contacts, try again")
                },
                _ => {
                  isExportingVar.set(false)
                  onDone.onNext(())
                }
              )
            )
      }

    } yield ()
    ZIOUtils.toEventStreamUnsafeDEPRECATED(task)
  }

}

private[contact] object ExportContactsModal {

  sealed trait Mode derives CanEqual {
    def size: Int
  }

  object Mode {

    case class Single(contactId: ContactId) extends Mode {
      override def size: Int = 1
    }

    case class Batch(contactIds: Set[ContactId]) extends Mode {
      override def size: Int = contactIds.size
    }

  }

}
