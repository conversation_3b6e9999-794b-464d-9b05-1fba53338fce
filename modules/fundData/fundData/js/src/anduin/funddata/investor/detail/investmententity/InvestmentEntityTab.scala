//  Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investor.detail.investmententity

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.menu.laminar.{MenuItemL, MenuL}
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.tag.TagColor
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import io.github.arainko.ducktape.*

import anduin.funddata.api.WithInvestmentEntities
import anduin.funddata.common.tag.{TagListProvider, TypeRenderer}
import anduin.funddata.component.tag.TagRenderer
import anduin.funddata.endpoint.investmententity.{FundDataInvestmentEntity, RequestedDocumentStatus}
import anduin.funddata.investmententity.{
  CreateInvestmentEntityModal,
  DeleteInvestmentEntityModal,
  EditInvestmentEntityModal,
  InvestmentEntityDetailPageContent,
  InvestmentEntityDetailPageParams
}
import anduin.funddata.investmententity.assessment.FundDataAssessmentHistoryUtils
import anduin.funddata.investmententity.assessment.FundDataAssessmentHistoryUtils.AssessmentDueDate
import anduin.funddata.investmententity.document.CompactDocumentStatusTags
import anduin.funddata.investmententity.merge.MergeInvestmentEntitiesButton
import anduin.funddata.investmententity.move.MoveInvestmentEntityModal
import anduin.funddata.navigation.BackButton
import anduin.funddata.utils.FundDataDocumentUtils
import anduin.funddata.utils.FundDataDocumentUtils.DocumentExpiry
import anduin.funddata.widget.CustomIdRenderer
import anduin.id.funddata.{FundDataInvestmentEntityId, FundDataInvestorId}
import anduin.scalajs.pluralize.Pluralize
import anduin.tag.v2.standard.AnduinStandardJurisdictionType
import anduin.utils.ScalaUtils
import stargazer.component.routing.laminar.WithReactRouterL
import stargazer.model.routing.DynamicAuthPage.FundData.FundDataInvestmentEntityDetailPage

private[detail] final case class InvestmentEntityTab(
  clientId: FundDataInvestorId
) {

  def apply(): HtmlElement = {
    WithInvestmentEntities(
      investorId = clientId,
      render = investmentEntitiesData =>
        div(
          child <-- investmentEntitiesData.isGettingSignal.splitBoolean(
            whenTrue = _ =>
              BlockIndicatorL(
                title = Val(Some("Loading investment entities...")),
                isFullHeight = true
              )().amend(minHeight.px(400)),
            whenFalse = _ =>
              div(
                child <-- investmentEntitiesData.investmentEntitiesSignal
                  .map(_.isEmpty)
                  .distinct
                  .splitBoolean(
                    whenTrue = _ => renderEmpty(investmentEntitiesData),
                    whenFalse = _ => renderInvestmentEntities(investmentEntitiesData)
                  )
              )
          )
        )
    )()
  }

  private def renderEmpty(investmentEntitiesData: WithInvestmentEntities.InvestmentEntitiesData) = {
    NonIdealStateL(
      icon = img(src := "/web/gondor/images/funddata/magnifier-eye.svg"),
      title = div(tw.textGray8.textBodyLarge.fontSemiBold, "There are no investment entities"),
      description = div("Add an investment entity for this client to keep track of it"),
      action = div(renderCreateEntityButton(investmentEntitiesData))
    )().amend(minHeight.px(400))
  }

  private def renderCreateEntityButton(investmentEntitiesData: WithInvestmentEntities.InvestmentEntitiesData) = {
    ModalL(
      renderTitle = _ => "Create new investment entity",
      isClosable = None,
      renderTarget = openModal =>
        ButtonL(
          style = ButtonL.Style.Full(
            color = ButtonL.Color.Primary,
            icon = Some(Icon.Glyph.Plus)
          ),
          onClick = openModal.contramap(_ => ())
        )("Create entity"),
      renderContent = closeModal =>
        CreateInvestmentEntityModal(
          investorId = clientId,
          closeModal = closeModal,
          onCreated = investmentEntitiesData.refetch.contramap(_ => ())
        )()
    )()
  }

  private def renderEditEntityModal(
    investmentEntity: FundDataInvestmentEntity,
    renderTarget: Observer[Unit] => HtmlElement,
    onEdited: Observer[Unit]
  ) = {
    ModalL(
      renderTitle = _ => "Edit investment entity",
      isClosable = None,
      renderTarget = renderTarget,
      renderContent = closeModal =>
        EditInvestmentEntityModal(
          investmentEntity = investmentEntity
            .into[EditInvestmentEntityModal.InvestmentEntityInfo]
            .transform(Field.renamed(_.riskAssessment, _.riskAssessmentLevel)),
          closeModal = closeModal,
          onEdited = onEdited
        )()
    )()
  }

  private def renderAssignToClient(
    investmentEntityId: FundDataInvestmentEntityId,
    investmentEntitySignal: Signal[FundDataInvestmentEntity],
    onMoved: Observer[Unit]
  ) = {
    val hasPendingConflictOrDocRequestsSignal =
      investmentEntitySignal.map(ie => ie.hasPendingProfileConflict || ie.hasPendingDocumentRequest).distinct
    TooltipL(
      renderTarget = ModalL(
        renderTarget = openModal =>
          div(
            child <-- hasPendingConflictOrDocRequestsSignal.map { isDisabled =>
              MenuItemL(isDisabled = isDisabled, onClick = openModal.contramap(_ => ()))("Assign to another client")
            }
          ),
        renderContent = closeModal =>
          MoveInvestmentEntityModal(
            investmentEntityId = investmentEntityId,
            investmentEntitySignal = investmentEntitySignal,
            closeModal = closeModal,
            onMoved = onMoved
          )(),
        size = ModalL.Size(ModalL.Width.Full, ModalL.Height.Full),
        isClosable = None
      )(),
      renderContent = _.amend("This entity may contain pending document requests or data conflicts"),
      isDisabled = !hasPendingConflictOrDocRequestsSignal
    )()
  }

  private def renderDeleteEntity(
    investmentEntityId: FundDataInvestmentEntityId,
    investmentEntitySignal: Signal[FundDataInvestmentEntity],
    onDeleted: Observer[Unit]
  ) = {
    ModalL(
      renderTitle = _ => "Delete investment entity",
      renderTarget = openModal => MenuItemL(color = MenuItemL.ColorDanger, onClick = openModal)("Delete entity"),
      renderContent = closeModal =>
        DeleteInvestmentEntityModal(
          investmentEntityId = investmentEntityId,
          investmentEntityNameSignal = investmentEntitySignal.map(_.name),
          closeModal = closeModal,
          onDeleted = onDeleted
        )(),
      isClosable = None
    )()
  }

  private def renderInvestmentEntities(investmentEntitiesData: WithInvestmentEntities.InvestmentEntitiesData) = {
    div(
      tw.spaceY16,
      renderHeader(investmentEntitiesData),
      div(
        tw.grid.gridCols1.gapX16.gapY16,
        tw.lg(tw.gridCols2),
        tw.xl(tw.gridCols3),
        children <-- investmentEntitiesData.investmentEntitiesSignal.split(_.investmentEntityId) {
          (investmentEntityId, _, investmentEntitySignal) =>
            renderInvestmentEntityCard(
              investmentEntityId,
              investmentEntitySignal,
              investmentEntitiesData.refetch,
              investmentEntitiesData.refetchSingle
            )
        }
      )
    )
  }

  private def renderHeader(investmentEntitiesData: WithInvestmentEntities.InvestmentEntitiesData) = {
    div(
      tw.flex.itemsCenter.justifyBetween,
      h2(tw.fontSemiBold, "Investment entities"),
      div(
        tw.flex.gap8,
        renderMergeEntities(investmentEntitiesData),
        renderCreateEntityButton(investmentEntitiesData)
      )
    )
  }

  private def renderMergeEntities(investmentEntitiesData: WithInvestmentEntities.InvestmentEntitiesData) = {
    child.maybe <-- investmentEntitiesData.investmentEntitiesSignal
      .map(_.size >= 2)
      .distinct
      .map(
        Option.when(_)(
          MergeInvestmentEntitiesButton(
            firmId = clientId.firmId,
            investmentEntitiesSignal = investmentEntitiesData.investmentEntitiesSignal,
            onDone = investmentEntitiesData.refetch
          )()
        )
      )

  }

  private def renderInvestmentEntityCard(
    investmentEntityId: FundDataInvestmentEntityId,
    investmentEntitySignal: Signal[FundDataInvestmentEntity],
    refetchInvestmentEntities: Observer[Unit],
    refetchInvestmentEntity: Observer[FundDataInvestmentEntityId]
  ): HtmlElement = {
    div(
      tw.borderAll.border1.borderGray3.rounded8.px24.py20.spaceY16,
      height.px(165),
      tw.flex.flexCol.itemsStretch,
      div(
        tw.flexFill.spaceY4,
        renderInvestmentEntityHeader(
          investmentEntityId,
          investmentEntitySignal,
          refetchInvestmentEntities,
          refetchInvestmentEntity
        ),
        renderInvestmentEntityInfo(investmentEntitySignal, refetchInvestmentEntity)
      ),
      div(tw.hPx1.wPc100.bgGray3),
      div(
        tw.flexFill.flex.gap16,
        renderCompliance(investmentEntitySignal),
        renderAssessment(investmentEntitySignal),
        renderFunds(investmentEntitySignal)
      )
    )
  }

  private def renderInvestmentEntityHeader(
    investmentEntityId: FundDataInvestmentEntityId,
    investmentEntitySignal: Signal[FundDataInvestmentEntity],
    refetchInvestmentEntities: Observer[Unit],
    refetchInvestmentEntity: Observer[FundDataInvestmentEntityId]
  ) = {
    div(
      tw.flex.itemsCenter.justifyBetween.spaceX16,
      child <-- investmentEntitySignal.map { investmentEntity =>
        WithReactRouterL { router =>
          val iePage = FundDataInvestmentEntityDetailPage(
            investmentEntityId = investmentEntityId,
            params = InvestmentEntityDetailPageParams.toUrlParams(
              InvestmentEntityDetailPageParams(from = BackButton.FromPage.ClientDetail(clientId))
            )
          )
          TruncateL(
            target = div(
              tw.text15.fontSemiBold,
              ButtonL(
                style = ButtonL.Style.Text(color = ButtonL.Color.Gray9),
                tpe = ButtonL.Tpe.Link(href = router.urlFor(iePage).value)
              )(investmentEntity.name)
            )
          )()
        }.amend(tw.flexFill)
      },
      PopoverL(
        position = PortalPosition.BottomRight,
        renderTarget = (togglePopover, isOpen) =>
          ButtonL(
            style = ButtonL.Style.Minimal(
              icon = Some(Icon.Glyph.EllipsisHorizontal),
              height = ButtonL.Height.Fix24,
              isSelected = isOpen
            ),
            hasChildren = false,
            onClick = togglePopover.contramap(_ => ())
          )(),
        renderContent = closePopover =>
          div(
            child <-- investmentEntitySignal.map { investmentEntity =>
              MenuL(
                Seq(
                  WithReactRouterL { router =>
                    val page = FundDataInvestmentEntityDetailPage(
                      investmentEntityId = investmentEntityId,
                      params = InvestmentEntityDetailPageParams.toUrlParams(
                        InvestmentEntityDetailPageParams(from = BackButton.FromPage.ClientDetail(clientId))
                      )
                    )
                    MenuItemL(url = router.urlFor(page).value)("View entity")
                  },
                  renderEditEntityModal(
                    investmentEntity = investmentEntity,
                    renderTarget = openModal => MenuItemL(onClick = openModal)("Edit entity details"),
                    onEdited = Observer.combine(refetchInvestmentEntity.contramap(_ => investmentEntityId), closePopover)
                  ),
                  renderAssignToClient(
                    investmentEntityId = investmentEntityId,
                    investmentEntitySignal = investmentEntitySignal,
                    onMoved = Observer.combine(refetchInvestmentEntities, closePopover)
                  ),
                  div(tw.my8.mx12.hPx1.bgGray3),
                  renderDeleteEntity(
                    investmentEntityId = investmentEntityId,
                    investmentEntitySignal = investmentEntitySignal,
                    onDeleted = Observer.combine(refetchInvestmentEntities, closePopover)
                  )
                )
              )
            }
          )
      )()
    )
  }

  private def renderInvestmentEntityInfo(
    investmentEntitySignal: Signal[FundDataInvestmentEntity],
    refetchInvestmentEntity: Observer[FundDataInvestmentEntityId]
  ) = {
    val infoNodesSignal = investmentEntitySignal.flatMapSwitch { entity =>
      val customIdNodeOpt = Option.when(entity.customId.nonEmpty)(
        div(CustomIdRenderer(entity.customId)())
      )
      val riskLevelNodeOptSignal = entity.riskAssessmentLevel.fold(Val(Option.empty[HtmlElement])) { riskLevelId =>
        val riskLevelOptSignal =
          TagListProvider.riskLevelAssessmentListOptSignal
            .map(_.flatMap(_.tagItems.find(_.tagItemId == riskLevelId)))
            .distinct
        riskLevelOptSignal.splitOption { case (_, tagItemSignal) => div(TagRenderer(tagItemSignal)()) }
      }
      val investorTypeNodeOpt = entity.investorType.map { investorTypeId =>
        TypeRenderer(
          tagListOptSignal = TagListProvider.investorTypeListOptSignal,
          selectedTypeOptSignal = Val(Option(investorTypeId)),
          otherTypeNameSignal = Val(entity.investorOtherType),
          otherTypeAST = AnduinStandardJurisdictionType.Other
        )()
      }
      riskLevelNodeOptSignal.map { riskLevelNodeOpt =>
        val baseChildren = customIdNodeOpt.toList ++ riskLevelNodeOpt ++ investorTypeNodeOpt
        val truncatedBaseChildren = baseChildren.map(_.amend(maxWidth.px(350 / baseChildren.length)))
        truncatedBaseChildren.zipWithIndex.flatMap { case (node, index) =>
          if (index == 0) List(node) else List(div(tw.wPx1.hPx16.bgGray3.flexNone), node)
        }
      }
    }
    child <-- infoNodesSignal.combineWithFn(investmentEntitySignal) { case (infoNodes, investmentEntity) =>
      if (infoNodes.isEmpty) {
        renderEditEntityModal(
          investmentEntity = investmentEntity,
          renderTarget = openModal =>
            ButtonL(
              style = ButtonL.Style.Text(),
              onClick = openModal.contramap(_ => ())
            )("Add entity details"),
          onEdited = refetchInvestmentEntity.contramap(_ => investmentEntity.investmentEntityId)
        )
      } else {
        div(tw.flex.itemsCenter.gap16, infoNodes)
      }
    }
  }

  private def renderCompliance(investmentEntitySignal: Signal[FundDataInvestmentEntity]) = {
    div(
      tw.flexFill.spaceY8,
      div(
        tw.flex.itemsCenter.text11.textGray7.gap4,
        IconL(name = Val(Icon.Glyph.FileText), size = Icon.Size.Custom(12))(),
        "Compliance:"
      ),
      child <-- investmentEntitySignal.map { entity =>
        val documents = entity.needComplianceActionDocuments
        val documentToExpireTime = documents.flatMap { document =>
          document.expireAt.map { expireDate =>
            FundDataDocumentUtils.calculateDocumentExpiry(expireDate)(FundDataDocumentUtils.ExpiringSoonDaysDefault)
          }
        }
        val countOfExpiredDocument =
          documentToExpireTime.count(documentExpiry => documentExpiry == DocumentExpiry.Expired)
        val countOfExpiringSoonDocument = {
          documentToExpireTime.count(ScalaUtils.isMatch[DocumentExpiry.ExpiringSoon])
        }
        val countOfRequestedDocument =
          documents.count(_.requestStatus.exists(ScalaUtils.isMatch[RequestedDocumentStatus.Requested]))
        val countOfPendingReviewDocument =
          documents.count(_.requestStatus.exists(ScalaUtils.isMatch[RequestedDocumentStatus.PendingReview]))
        val nodes = CompactDocumentStatusTags(
          countOfExpiredDocument,
          countOfExpiringSoonDocument,
          countOfRequestedDocument,
          countOfPendingReviewDocument
        )()
        if (nodes.isEmpty) div("--") else div(tw.flex.spaceX4, nodes)
      }
    )
  }

  private def renderAssessment(investmentEntitySignal: Signal[FundDataInvestmentEntity]) = {
    div(
      tw.flexFill.spaceY8,
      div(
        tw.flex.itemsCenter.text11.textGray7.gap4,
        IconL(name = Val(Icon.Glyph.CheckList), size = Icon.Size.Custom(12))(),
        "Assessment:"
      ),
      child <-- investmentEntitySignal.map { entity =>
        val assessments = entity.overdueAndUpcomingSoonAssessments
        val assessmentToDueDate = assessments.flatMap(_.dueTime.map { dueDate =>
          FundDataAssessmentHistoryUtils.calculateAssessmentDueDate(dueDate)(
            FundDataAssessmentHistoryUtils.DueDateSoonDaysDefault
          )
        })
        val countOfOverdueAssessment = assessmentToDueDate.count(curDueDate => curDueDate == AssessmentDueDate.OverDue)
        val countOfUpcomingSoonAssessment =
          assessmentToDueDate.count(curDueDate => curDueDate == AssessmentDueDate.UpcomingAssessmentSoon)
        if (countOfOverdueAssessment > 0) {
          TagL(label = Val("Overdue"), color = Val(TagColor.Bold.Danger))()
        } else if (countOfUpcomingSoonAssessment > 0) {
          TagL(label = Val("Due soon"), color = Val(TagColor.Bold.Warning))()
        } else {
          div("--")
        }
      }
    )
  }

  private def renderFunds(investmentEntitySignal: Signal[FundDataInvestmentEntity]) = {
    div(
      tw.flexFill.spaceY8,
      div(
        tw.flex.itemsCenter.text11.textGray7.gap4,
        IconL(name = Val(Icon.Glyph.Vault), size = Icon.Size.Custom(12))(),
        "Funds:"
      ),
      child <-- investmentEntitySignal.map { entity =>
        if (entity.funds.isEmpty) {
          div("--")
        } else {
          val subscriptionTab = FundDataInvestmentEntityDetailPage(
            investmentEntityId = entity.investmentEntityId,
            params = InvestmentEntityDetailPageParams.toUrlParams(
              InvestmentEntityDetailPageParams(
                tab = InvestmentEntityDetailPageContent.SubscriptionTab,
                from = BackButton.FromPage.ClientDetail(clientId)
              )
            )
          )
          WithReactRouterL { router =>
            ButtonL(
              style = ButtonL.Style.Text(),
              tpe = ButtonL.Tpe.Link(href = router.urlFor(subscriptionTab).value)
            )(Pluralize("fund", entity.funds.size, true))
          }
        }
      }
    )

  }

}
