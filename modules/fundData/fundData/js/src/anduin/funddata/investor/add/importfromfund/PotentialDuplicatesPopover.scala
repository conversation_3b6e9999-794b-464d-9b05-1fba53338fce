package anduin.funddata.investor.add.importfromfund

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*

import anduin.funddata.algorithm.LevenshsteinTextMatch
import anduin.funddata.endpoint.investor.FundDataInvestorBasic
import anduin.funddata.investor.add.importfromfund.ImportFromFundFlowData.{
  ExistingInvestmentEntity,
  SubscriptionToImport
}
import anduin.scalajs.pluralize.Pluralize
import stargazer.component.routing.laminar.WithReactRouterL
import stargazer.model.routing.DynamicAuthPage.FundData.{FundDataClientDetailPage, FundDataInvestmentEntityDetailPage}

private[importfromfund] final case class PotentialDuplicatesPopover(
  subscription: SubscriptionToImport,
  potentialDuplicateClients: List[FundDataInvestorBasic],
  potentialDuplicateEntities: List[ExistingInvestmentEntity]
) {

  private val textMatch = LevenshsteinTextMatch(subscription.subscriptionName)

  def apply(): HtmlElement = {
    div(
      PopoverL(
        position = PortalPosition.LeftCenter,
        renderTarget = (togglePopover, _) =>
          ButtonL(
            style = ButtonL.Style.Text(),
            onClick = togglePopover.contramap(_ => ())
          )(
            Pluralize(
              "potential duplicate",
              potentialDuplicateClients.size + potentialDuplicateEntities.size,
              true
            )
          ),
        renderContent = _ =>
          div(
            width.px(400),
            tw.p20,
            div(
              tw.text15.fontSemiBold,
              s"We've detected ${potentialDuplicateClients.size + potentialDuplicateEntities.size} potential duplicates"
            ),
            div(
              tw.text11.textGray7.mt4.leading16,
              "There are existing clients and/or investment entities whose names seem to match the subscription you're importing."
            ),
            Option.when(potentialDuplicateClients.nonEmpty)(
              renderDuplicateClients.amend(tw.mt16)
            ),
            Option.when(potentialDuplicateEntities.nonEmpty)(
              renderDuplicateEntities.amend(tw.mt16)
            )
          )
      )()
    )
  }

  private def renderDuplicateClients = {
    div(
      div(tw.fontSemiBold, "Client matches:"),
      div(
        tw.mt8.borderAll.borderGray2.bgGray1.rounded8.p12,
        tw.flex.flexCol.spaceY8,
        potentialDuplicateClients
          .sortBy { client =>
            textMatch.computeMatchedPercentage(client.investorName) * (-1)
          }
          .map { client =>
            div(
              tw.flex.itemsCenter,
              IconL(name = Val(Icon.Glyph.UserSingle))().amend(tw.textGray7),
              TruncateL(
                target = div(tw.flexFill.mx8, client.investorName),
                title = Some(client.investorName)
              )(),
              div(
                width.px(72),
                ReviewAndImportSubscriptionTable
                  .renderSimilarityTag(textMatch.computeMatchedPercentage(client.investorName))
              ),
              WithReactRouterL { router =>
                val clientDetailPage = FundDataClientDetailPage(clientId = client.investorId)
                TooltipL(
                  renderTarget = ButtonL(
                    style = ButtonL.Style.Minimal(icon = Some(Icon.Glyph.Eye), height = ButtonL.Height.Fix24),
                    tpe = ButtonL.Tpe.Link(href = router.urlFor(clientDetailPage).value, target = ButtonL.Target.Blank)
                  )(),
                  renderContent = _.amend("View existing client in a new tab")
                )()
              }
            )
          }
      )
    )
  }

  private def renderDuplicateEntities = {
    div(
      div(tw.fontSemiBold, "Entity matches:"),
      div(
        tw.mt8.borderAll.borderGray2.bgGray1.rounded8.p12,
        tw.flex.flexCol.spaceY8,
        potentialDuplicateEntities
          .sortBy { entity =>
            textMatch.computeMatchedPercentage(entity.name) * (-1)
          }
          .map { entity =>
            div(
              tw.flex.itemsCenter,
              IconL(name = Val(Icon.Glyph.UserInfo))().amend(tw.textGray7),
              TruncateL(target = div(tw.flexFill.mx8, entity.name), title = Some(entity.name))(),
              div(
                width.px(72),
                ReviewAndImportSubscriptionTable.renderSimilarityTag(textMatch.computeMatchedPercentage(entity.name))
              ),
              WithReactRouterL { router =>
                val entityPage = FundDataInvestmentEntityDetailPage(investmentEntityId = entity.id)
                TooltipL(
                  renderTarget = ButtonL(
                    style = ButtonL.Style.Minimal(icon = Some(Icon.Glyph.Eye), height = ButtonL.Height.Fix24),
                    tpe = ButtonL.Tpe.Link(href = router.urlFor(entityPage).value, target = ButtonL.Target.Blank)
                  )(),
                  renderContent = _.amend("View existing entity in a new tab")
                )()
              }
            )
          }
      )
    )
  }

}
