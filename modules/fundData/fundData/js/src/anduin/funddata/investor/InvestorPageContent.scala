// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investor

import com.raquo.laminar.api.L.*
import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.laminar.InitialAvatarL
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.drawer.Drawer
import design.anduin.components.drawer.laminar.DrawerL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.menu.laminar.{MenuItemL, MenuL}
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.portal.laminar.PortalWrapperL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.tag.Tag
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import design.anduin.table.laminar.{SelectionColumnL, TableL}
import org.scalajs.dom

import anduin.funddata.api.{
  WithActiveClientProfileConflicts,
  WithClientGroups,
  WithFundsV2,
  WithInvestors,
  WithInvestorsComplianceSummary
}
import anduin.funddata.common.widget.SelectAllWidget
import anduin.funddata.endpoint.investor.{FundDataInvestor, InvestorQueryParams}
import anduin.funddata.endpoint.investor.InvestorQueryParams.{FilterByAssessment, FilterByDocument, SortBy, SortOrder}
import anduin.funddata.investmententity.CreateInvestmentEntityModal
import anduin.funddata.investmententity.document.CompactDocumentStatusTags
import anduin.funddata.investor.ComplianceButton.ComplianceButtonLayout
import anduin.funddata.investor.InvestorComplianceWidget.ComplianceItem
import anduin.funddata.investor.InvestorPageContent.OpenInvestorDetailDrawer
import anduin.funddata.investor.add.AddInvestorsButton
import anduin.funddata.investor.dashboard.CommitmentCellRenderer
import anduin.funddata.investor.filter.InvestorFilterAndSearch
import anduin.funddata.tag.InvestorTagsRenderer
import anduin.funddata.widget.CustomIdRenderer
import anduin.id.funddata.{FundDataFirmId, FundDataInvestmentEntityId, FundDataInvestorId}
import anduin.scalajs.pluralize.Pluralize
import com.anduin.stargazer.client.localstorage.{SessionStorage, SessionStorageKey}
import com.anduin.stargazer.client.modules.routing.RoutingUtils.Router
import stargazer.component.routing.laminar.WithReactRouterL
import stargazer.model.routing.DynamicAuthPage.FundData.{FundDataClientDetailPage, FundDataInvestmentEntityDetailPage}

private[funddata] final case class InvestorPageContent(
  firmId: FundDataFirmId,
  pageParamsSignal: Signal[InvestorPageParams],
  onSetPageParams: Observer[InvestorPageParams]
) {

  private val openInvestorDetailDrawerVar = Var(Option.empty[OpenInvestorDetailDrawer])

  private val pageParamsUpdateEventBus = new EventBus[InvestorPageParams.ParamsUpdate]

  private val initialSortByVar = Var[Option[SortBy]](None)
  private val initialSortOrderVar = Var[Option[SortOrder]](None)

  private val shouldRefetchOnCloseDrawerVar = Var(false)

  private val isFirstLoadVar = Var[Boolean](true)

  private val selectedInvestorIdsVar = Var[Set[FundDataInvestorId]](Set.empty)
  private val selectInvestorsEventBus = new EventBus[List[FundDataInvestor]]

  private val shouldScrollToFirstRowVar = Var[Boolean](false)

  def apply(): HtmlElement = {
    WithActiveClientProfileConflicts(
      firmId = firmId,
      render = profileConflictsData => {
        div(
          tw.relative.flex.flexCol.flexFill,
          renderInvestorsDashboard,
          renderActiveClientProfileConflicts(profileConflictsData).amend(
            tw.absolute.bottom0.right0.mb32
          )
        )
      }
    )()
  }

  private def renderInvestorsDashboard = {
    WithInvestorsComplianceSummary(
      firmId,
      render = investorComplianceData =>
        WithInvestors(
          firmId,
          queryParamsSignal = pageParamsSignal.map(_.toQueryParams),
          render = investorsData => {
            val totalInvestorsSignal = investorsData.investorDataSignal.map(_.totalInvestors).distinct
            div(
              tw.px32.py24.flex.flexFill,
              child <-- isFirstLoadVar.signal.distinct.combineWith(totalInvestorsSignal).map {
                case (isFirstLoad, totalInvestors) =>
                  if (isFirstLoad) {
                    renderFirstLoad
                  } else if (totalInvestors == 0) {
                    renderNoInvestor(investorsData)
                  } else {
                    div(
                      tw.flex.flexCol.wPc100,
                      renderHeader(investorsData, investorComplianceData.refetch),
                      renderComplianceSummary(investorComplianceData).amend(tw.mt16),
                      renderFilterAndSearch(investorsData, investorComplianceData.refetch)
                        .amend(tw.mt16),
                      child <-- initialSortOrderVar.signal
                        .combineWith(
                          initialSortByVar.signal,
                          investorsData.isGettingSignal
                            .combineWith(shouldScrollToFirstRowVar.signal)
                            .map { case (isGetting, shouldScrollToFirstRow) => isGetting && shouldScrollToFirstRow }
                        )
                        .distinct
                        .map { case (initialSortOrder, initialSortBy, shouldScrollToFirstRowOnGetting) =>
                          if (shouldScrollToFirstRowOnGetting) {
                            BlockIndicatorL(
                              title = Val(Some("Searching for results...")),
                              isFullHeight = true
                            )().amend(tw.mt16.flexFill)
                          } else {
                            renderInvestorDashboard(investorsData, initialSortOrder, initialSortBy)
                              .amend(tw.pt16.flexFill)
                          }
                        },
                      child.maybe <-- investorsData.investorDataSignal
                        .map(_.totalFilteredInvestors > 0)
                        .distinct
                        .map(
                          Option.when(_)(renderPagination(investorsData.investorDataSignal).amend(tw.mt16))
                        ),
                      renderInvestorDetailDrawer(investorsData.refetch, investorComplianceData.refetch)
                    )
                  }
              },
              pageParamsSignal
                .map(_.sortBy)
                .combineWithFn(initialSortByVar.signal) { (sortByParams, initialSortBy) =>
                  initialSortBy.orElse(Some(sortByParams))
                }
                .distinct --> initialSortByVar.writer,
              pageParamsSignal
                .map(_.sortOrder)
                .combineWithFn(initialSortOrderVar.signal) { (sortOrderParam, initialSortOrder) =>
                  initialSortOrder.orElse(Some(sortOrderParam))
                }
                .distinct --> initialSortOrderVar.writer
            )
          },
          onDidQuery = Observer { _ =>
            Var.set(isFirstLoadVar -> false, shouldScrollToFirstRowVar -> false)
          }
        )()
    )()
  }

  private def renderActiveClientProfileConflicts(profileConflictsData: WithActiveClientProfileConflicts.RenderProps) = {
    InvestorProfileConflictWidget(firmId, profileConflictsData)()
  }

  private def renderFirstLoad = {
    div(
      tw.wPc100,
      BlockIndicatorL(
        title = Val(Some("Loading clients..."))
      )().amend(height.px(500))
    )
  }

  private def renderNoInvestor(investorData: WithInvestors.RenderProps) = {
    NonIdealStateL(
      icon = img(src := "/web/gondor/images/funddata/magnifier-person.svg"),
      title = div(
        tw.textGray8.textBodyLarge.fontSemiBold,
        "There are no clients"
      ),
      description = div(
        tw.textGray7.textBody,
        "Add your clients to easily manage them and keep track of their data"
      ),
      action = renderAddClientButton(investorData)
    )().amend(height.px(650))
  }

  private def renderInvestorsSelectedMod(investorsData: WithInvestors.RenderProps): DynamicInserter =
    child.maybe <-- selectedInvestorIdsVar.signal
      .map(_.nonEmpty)
      .distinct
      .map(
        Option.when(_)(
          SelectAllWidget[FundDataInvestorId](
            itemName = "client",
            selectedItemsSignal = selectedInvestorIdsVar.signal,
            allItemsSignal = investorsData.investorDataSignal.map(_.filteredInvestorIds),
            onSelectAll = selectedInvestorIdsVar.writer,
            onDeselectAll = selectedInvestorIdsVar.writer.contramap(_ => Set.empty),
            pageSizeOptSignal = pageParamsSignal.map(_.pageSize).map(Some(_))
          )()
        )
      )

  private def renderBatchActionButtonMod(onReload: Observer[Unit]) =
    BatchButton(
      firmId = firmId,
      investorIdsSignal = selectedInvestorIdsVar.signal.map(_.toList).distinct,
      onReload = onReload,
      clearSelectedInvestorIds = selectedInvestorIdsVar.writer.contramap(_ => Set.empty)
    )()

  private def renderHeader(
    investorData: WithInvestors.RenderProps,
    refetchInvestorsCompliance: Observer[Boolean]
  ) = {
    div(
      tw.flex.itemsCenter.justifyBetween,
      div(tw.heading1.fontSemiBold, "Clients"),
      div(
        tw.flex.itemsCenter.spaceX8,
        renderBatchActionButtonMod(
          onReload = Observer[Unit] { _ =>
            investorData.refetch.onNext(true)
            refetchInvestorsCompliance.onNext(true)
          }
        ),
        renderExportButtonMod,
        renderImportBySpreadsheetButtonMod(investorData),
        renderAddClientButton(investorData)
      )
    )
  }

  private def renderComplianceSummary(investorComplianceData: WithInvestorsComplianceSummary.RenderProps) = {
    div(
      child <-- investorComplianceData.isGettingSignal.map {
        if (_) {
          emptyNode
        } else {
          div(
            child.maybe <-- investorComplianceData.investorsComplianceSummaryOptSignal
              .combineWith(pageParamsSignal)
              .distinct
              .map { (investorsComplianceSummaryOpt, pageParams) =>
                investorsComplianceSummaryOpt.map { investorsComplianceSummary =>
                  div(
                    tw.flex.itemsCenter.wPc100,
                    div(
                      tw.wPc50.pr4,
                      InvestorComplianceWidget[FilterByDocument](
                        currentFilters = pageParams.filterByDocuments,
                        complianceItems = List(
                          ComplianceItem(
                            investorsComplianceSummary.noOfExpiredDocuments,
                            FilterByDocument.Expired,
                            ComplianceButtonLayout.Expired,
                            investorsComplianceSummary.noOfRequestedAndExpired
                          ),
                          ComplianceItem(
                            investorsComplianceSummary.noOfExpiringSoonDocuments,
                            FilterByDocument.ExpiringSoon,
                            ComplianceButtonLayout.Expiring,
                            investorsComplianceSummary.noOfRequestedAndExpiring
                          ),
                          ComplianceItem(
                            investorsComplianceSummary.noOfPendingReview,
                            FilterByDocument.PendingReview,
                            ComplianceButtonLayout.PendingReview
                          )
                        ),
                        layout = InvestorComplianceWidget.ComplianceWidgetLayout.documentCompliance,
                        pageParamsObserver = Observer[List[FilterByDocument]] { filterByDocuments =>
                          pageParamsUpdateEventBus.emit(
                            InvestorPageParams.ParamsUpdate(filterByDocuments = Some(filterByDocuments))
                          )
                        }
                      )()
                    ),
                    div(
                      tw.wPc50.pl4,
                      InvestorComplianceWidget[FilterByAssessment](
                        currentFilters = pageParams.filterByAssessments,
                        complianceItems = List(
                          ComplianceItem(
                            investorsComplianceSummary.noOfOverDueAssessments,
                            FilterByAssessment.Overdue,
                            ComplianceButtonLayout.Overdue
                          ),
                          ComplianceItem(
                            investorsComplianceSummary.noOfUpcomingSoonAssessments,
                            FilterByAssessment.UpcomingSoon,
                            ComplianceButtonLayout.DueSoon
                          )
                        ),
                        layout = InvestorComplianceWidget.ComplianceWidgetLayout.assessmentCompliance,
                        pageParamsObserver = Observer[List[FilterByAssessment]] { filterByAssessments =>
                          pageParamsUpdateEventBus.emit(
                            InvestorPageParams.ParamsUpdate(filterByAssessments = Some(filterByAssessments))
                          )
                        }
                      )()
                    )
                  )
                }
              }
          )
        }
      }
    )
  }

  private def renderImportBySpreadsheetButtonMod(investorData: WithInvestors.RenderProps) =
    ImportSpreadsheetButton(
      firmId = firmId,
      onAddedInvestors = Observer { _ =>
        investorData.refetch.onNext(true)
      }
    )()

  private def renderExportButtonMod =
    ExportButton(
      firmId = firmId,
      investorIdsSignal = selectedInvestorIdsVar.signal.map(_.toList).distinct
    )()

  private def renderAddClientButton(investorData: WithInvestors.RenderProps) = {
    AddInvestorsButton(
      firmId,
      shouldRenderImportClientBySpreadsheetSignal =
        investorData.investorDataSignal.map(investorData => investorData.investors.isEmpty),
      onAddedInvestors = investorData.refetch.contramap(_ => true),
      canImportFromPastSubscriptionSignal = investorData.investorDataSignal.map(_.canImportFromSubscription).distinct
    )()
  }

  private def renderFilterAndSearch(
    investorsData: WithInvestors.RenderProps,
    refetchInvestorsCompliance: Observer[Boolean]
  ) = {
    WithFundsV2(
      firmId = firmId,
      render = fundData =>
        WithClientGroups(
          firmId = firmId,
          render = clientGroupData =>
            div(
              InvestorFilterAndSearch(
                firmId = firmId,
                pageParamsSignal = pageParamsSignal,
                onUpdatePageParams = pageParamsUpdateEventBus.writer,
                fundsSignal = fundData.fundsSignal,
                clientGroupsSignal = clientGroupData.clientGroupsSignal,
                renderInvestorsSelectedMod = renderInvestorsSelectedMod(investorsData)
              )(),
              pageParamsSignal.map(_.toQueryParams).distinct.changes.map(_ => ()) -->
                Observer[Unit] { _ =>
                  investorsData.refetch.onNext(true)
                  refetchInvestorsCompliance.onNext(false)
                },
              EventStream.unit(emitOnce = true).sample(pageParamsSignal) --> Observer[InvestorPageParams] { query =>
                openInvestorDetailDrawerVar.set(
                  query.selectInvestor.map(OpenInvestorDetailDrawer(_))
                )
                SessionStorage.set(SessionStorageKey.FundDataInvestorPageParams, query)
              },
              pageParamsUpdateEventBus.events
                .withCurrentValueOf(pageParamsSignal) --> Observer[(InvestorPageParams.ParamsUpdate, InvestorPageParams)] {
                case (update, params) =>
                  val updatedParams = params.applyUpdate(update)
                  onSetPageParams.onNext(updatedParams)
                  SessionStorage.set(SessionStorageKey.FundDataInvestorPageParams, updatedParams)
              }
            )
        )()
    )()
  }

  private def checkBoxColumn(investorsSignal: Signal[List[FundDataInvestor]]) =
    SelectionColumnL[FundDataInvestor](
      dataSignal = investorsSignal.map(_.map { row =>
        SelectionColumnL.Row(data = row)
      }),
      selectedRowsSignal =
        selectedInvestorIdsVar.signal.combineWithFn(investorsSignal) { case (selectedInvestorIds, investors) =>
          investors.filter(investor => selectedInvestorIds.contains(investor.investorId))
        },
      onRowsSelected = selectInvestorsEventBus.writer
    )()

  private def renderInvestorDashboard(
    investorsData: WithInvestors.RenderProps,
    initialSortOrder: Option[SortOrder],
    initialSortBy: Option[SortBy]
  ) = {
    val investorsSignal = investorsData.investorDataSignal.map(_.investors).distinct
    div(
      selectInvestorsEventBus.events
        .withCurrentValueOf(selectedInvestorIdsVar.signal)
        .withCurrentValueOf(investorsSignal) --> Observer[
        (List[FundDataInvestor], Set[FundDataInvestorId], List[FundDataInvestor])
      ] { case (newSelectedInvestors, curSelectedInvestorIds, investors) =>
        selectedInvestorIdsVar.set(
          curSelectedInvestorIds --
            investors.map(_.investorId).toSet ++
            newSelectedInvestors.map(_.investorId).toSet
        )
      },
      TableL[FundDataInvestor](
        columns = List(
          checkBoxColumn(investorsSignal),
          clientColumn,
          investmentEntityColumn,
          fundColumn,
          renderComplianceColumn,
          totalCommitmentColumn,
          actionColumn(investorsData.refetch)
        ),
        dataSignal = investorsSignal,
        loading = TableL.Loading(
          criteria = investorsData.isGettingSignal,
          render = () =>
            BlockIndicatorL(
              title = Val(Some("Searching for results...")),
              isFullHeight = true
            )()
        ),
        placeholder = TableL.Placeholder(
          criteria = investorsSignal => investorsSignal.map(_.isEmpty),
          render = _ => renderTableEmpty
        ),
        options = TableL.Options(
          layout = TableL.Layout.FitColumns,
          indexColumn = Option("investorId"),
          columnHorizontalAlignment = TableL.HorizontalAlignment.Left,
          columnVerticalAlignment = TableL.VerticalAlignment.Top
        ),
        initialSortColumns = List(
          TableL.SortColumn(
            column = initialSortBy
              .map { case SortBy.Name =>
                clientColumn
              }
              .getOrElse(clientColumn),
            direction = initialSortOrder
              .map {
                case SortOrder.Ascending  => TableL.ColumnSortDirection.Asc
                case SortOrder.Descending => TableL.ColumnSortDirection.Desc
              }
              .getOrElse(TableL.ColumnSortDirection.Asc)
          )
        ),
        onRowClick = Observer[TableL.RowClickData[FundDataInvestor]](_.toggleSelectRow()),
        onRowRendered = Observer[TableL.RowRenderedData[FundDataInvestor]](onRowRendered)
      ).amend(tw.hPc100)
    )
  }

  private def onRowRendered(rowRendererData: TableL.RowRenderedData[FundDataInvestor]): Unit = {
    rowRendererData.rowComponent.getElement().classList.add(tw.group.css)
  }

  private def renderTableEmpty = {
    NonIdealStateL(
      icon = img(src := "/web/gondor/images/funddata/magnifier-eye.svg"),
      title = div(
        tw.textGray8.textBodyLarge.fontSemiBold,
        "No results found"
      ),
      description = div(
        tw.textGray7.textBody,
        "Please rephrase your search to try again"
      )
    )().amend(height.px(500))
  }

  private def openInvestorDetailDrawer(investorId: FundDataInvestorId): Unit = {
    openInvestorDetailDrawerVar.set(Some(OpenInvestorDetailDrawer(investorId)))
    pageParamsUpdateEventBus.writer.onNext(
      InvestorPageParams.ParamsUpdate(selectInvestor = Some(Some(investorId)))
    )
  }

  private def clientColumn = TableL.Column[FundDataInvestor](
    title = "Client",
    field = "clients",
    renderCell = renderProps =>
      div(
        tw.wPc100,
        TooltipL(
          renderTarget = WithReactRouterL { router =>
            val page = FundDataClientDetailPage(clientId = renderProps.data.investorId)
            ButtonL(
              style = ButtonL.Style.Text(),
              tpe = ButtonL.Tpe.Link(href = router.urlFor(page).value),
              onClick = Observer { event =>
                event.stopPropagation()
                // openInvestorDetailDrawer(renderProps.data.investorId)
              }
            )(renderProps.data.name).amend(tw.fontSemiBold.text15)
          },
          renderContent = _.amend(renderProps.data.name),
          targetWrapper = PortalWrapperL.BlockContent
        )(),
        Option.when(renderProps.data.customId.trim.nonEmpty)(
          div(tw.mt4, CustomIdRenderer(renderProps.data.customId.trim)())
        ),
        InvestorTagsRenderer(
          investorId = renderProps.data.investorId,
          investorTags = renderProps.data.tags,
          visibleOnHoverOnly = true
        )().amend(tw.mt8.mb12)
      ),
    isSortable = true,
    sortWith = Some(sorter =>
      sorter.a.zip(sorter.b).fold[Double](0) { case (a, b) =>
        a.name.toLowerCase.compareTo(b.name.toLowerCase).toDouble
      }
    ),
    onColumnClick = pageParamsUpdateEventBus.writer.contramap { _ =>
      InvestorPageParams.ParamsUpdate(sortBy = Some(InvestorQueryParams.SortBy.Name))
    }
  )

  private def investmentEntityColumn = TableL.Column[FundDataInvestor](
    title = "Investment entities",
    field = "investment entity",
    renderCell = renderProps => {
      val investmentEntities = renderProps.data.investmentEntities.sortBy(_._2.trim.toLowerCase)
      val (investmentEntitiesInCell, investmentEntitiesInPopover) = {
        if (investmentEntities.length > 4) {
          investmentEntities.take(3) -> investmentEntities.drop(3)
        } else {
          investmentEntities -> List.empty
        }
      }
      WithReactRouterL { router =>
        div(
          tw.wPc100.spaceY4,
          Option.when(investmentEntities.isEmpty)(div("--")),
          investmentEntitiesInCell.map { investmentEntity =>
            renderInvestmentEntityLink(router, investmentEntity)
          },
          Option.when(investmentEntitiesInPopover.nonEmpty) {
            PopoverL(
              position = PortalPosition.TopLeft,
              renderTarget = (toggle, _) =>
                ButtonL(
                  style = ButtonL.Style.Text(),
                  onClick = Observer[dom.MouseEvent] { event =>
                    event.stopPropagation()
                    toggle.onNext(())
                  }
                )(
                  "+" + Pluralize(
                    "other",
                    investmentEntitiesInPopover.size,
                    inclusive = true
                  )
                ),
              renderContent = _ => renderInvestmentEntityPopover(router, investmentEntitiesInPopover)
            )()
          },
          renderCreateInvestmentEntityButton(router, renderProps.data).amend(
            tw.invisible.groupHover(tw.visible)
          )
        )
      }.amend(tw.wPc100)
    }
  )

  private def renderInvestmentEntityPopover(
    router: Router,
    investmentEntities: List[(FundDataInvestmentEntityId, String)]
  ) = {
    MenuL(
      investmentEntities.map { item =>
        MenuItemL(
          icon = Some(Icon.Glyph.UserInfo),
          url = router.urlFor(FundDataInvestmentEntityDetailPage(item._1)).value
        )(item._2)
      }
    )
  }

  private def renderCreateInvestmentEntityButton(router: Router, investor: FundDataInvestor) = {
    ModalL(
      renderTitle = _ => "Create new investment entity",
      isClosable = None,
      renderTarget = openModal =>
        div(
          tw.flex.itemsCenter,
          div(tw.textPrimary5.mr4, IconL(name = Val(Icon.Glyph.PlusCircle))()),
          ButtonL(
            style = ButtonL.Style.Text(),
            onClick = Observer { e =>
              e.stopPropagation()
              openModal.onNext(())
            }
          )("Create entity")
        ),
      renderContent = closeModal =>
        CreateInvestmentEntityModal(
          investor.investorId,
          closeModal,
          onCreated = Observer { investmentEntityId =>
            router.set(FundDataInvestmentEntityDetailPage(investmentEntityId)).runNow()
          }
        )()
    )()
  }

  private def renderInvestmentEntityLink(
    router: Router,
    investmentEntity: (FundDataInvestmentEntityId, String)
  ) = {
    TruncateL(
      target = div(
        ButtonL(
          style = ButtonL.Style.Text(color = ButtonL.Color.Primary),
          tpe = ButtonL.Tpe.Link(href = router.urlFor(FundDataInvestmentEntityDetailPage(investmentEntity._1)).value),
          onClick = Observer(_.stopPropagation())
        )(investmentEntity._2)
      )
    )()
  }

  private def fundColumn = TableL.Column[FundDataInvestor](
    title = "Funds",
    field = "fund",
    renderCell = renderProps => {
      val funds = renderProps.data.funds.sortBy(_.trim.toLowerCase)
      val (fundInCell, fundInPopover) = {
        if (funds.length > 4) {
          funds.take(3) -> funds.drop(3)
        } else {
          funds -> List.empty
        }
      }
      div(
        tw.flex.flexCol.spaceY4.wPc100,
        Option.when(funds.isEmpty)(div("--")),
        fundInCell.map(div(tw.truncate, _)),
        Option.when(fundInPopover.nonEmpty) {
          PopoverL(
            position = PortalPosition.TopLeft,
            renderTarget = (toggle, _) =>
              ButtonL(
                style = ButtonL.Style.Text(),
                onClick = Observer[dom.MouseEvent] { event =>
                  event.stopPropagation()
                  toggle.onNext(())
                }
              )(
                "+" + Pluralize(
                  "other",
                  fundInPopover.size,
                  inclusive = true
                )
              ),
            renderContent = _ => renderFundPopover(fundInPopover)
          )()
        }
      )
    }
  )

  private def renderFundPopover(fundSubscriptions: List[String]) = {
    div(
      tw.px8.py4,
      minWidth := "280px",
      fundSubscriptions.zipWithIndex.map { case (fundSubscription, index) =>
        div(
          (index > 0).cls(tw.mt8),
          tw.flex.itemsCenter,
          InitialAvatarL(
            id = fundSubscription,
            initials = Signal.fromValue(fundSubscription.take(1)),
            kind = InitialAvatar.Kind.Organization,
            size = InitialAvatar.Size.Px24
          )(),
          div(tw.ml8, fundSubscription)
        )
      }
    )
  }

  private def renderComplianceColumn = TableL.Column[FundDataInvestor](
    title = "Compliance statuses",
    field = "compliance",
    renderCell = renderProps => {
      val documentSummary = renderProps.data.documentSummary
      val documentStatusTags = CompactDocumentStatusTags(
        documentSummary.noOfExpiredDocuments,
        documentSummary.noOfExpiringSoonDocuments,
        documentSummary.noOfRequested,
        documentSummary.noOfPendingReview
      )()
      val assessmentSummary = renderProps.data.assessmentSummary
      val assessmentStatusTags = Seq(
        Option.when(assessmentSummary.noOfOverDueAssessments > 0) {
          div(
            tw.wFit,
            TagL(
              label = Val(s"${assessmentSummary.noOfOverDueAssessments} overdue"),
              color = Val(Tag.Bold.Danger)
            )()
          )
        },
        Option.when(assessmentSummary.noOfUpcomingSoonAssessments > 0) {
          div(
            tw.wFit,
            TagL(
              label = Val(s"${assessmentSummary.noOfUpcomingSoonAssessments} upcoming"),
              color = Val(Tag.Bold.Warning)
            )()
          )
        }
      ).flatten

      if (documentStatusTags.isEmpty && assessmentStatusTags.isEmpty) {
        div("--")
      } else {
        div(
          tw.spaceY12,
          Option.when(documentStatusTags.nonEmpty)(
            div(
              div(
                tw.textSmall.fontMedium.textGray7,
                "Documents"
              ),
              div(
                tw.flex.spaceX4.mt4,
                documentStatusTags
              )
            )
          ),
          Option.when(assessmentStatusTags.nonEmpty)(
            div(
              div(
                tw.textSmall.fontMedium.textGray7,
                "Risk assessments"
              ),
              div(
                tw.flex.spaceX4.mt4,
                assessmentStatusTags
              )
            )
          )
        )
      }
    }
  )

  private def totalCommitmentColumn = TableL.Column[FundDataInvestor](
    title = "Total commitment",
    field = "total-commitment",
    renderCell = renderProps =>
      CommitmentCellRenderer(
        investorId = renderProps.data.investorId,
        totalCommitment = renderProps.data.totalCommitment
      )()
  )

  private def actionColumn(refetchInvestors: Observer[Boolean]) = TableL.Column[FundDataInvestor](
    title = "",
    field = "action",
    renderCell = renderProps => renderActionCell(renderProps.data, refetchInvestors.contramap(_ => true)),
    width = Option(100)
  )

  private def renderActionCell(investor: FundDataInvestor, refetchInvestors: Observer[Unit]) = {
    div(
      tw.flex.justifyBetween,
      TooltipL(
        renderTarget = WithReactRouterL { router =>
          val page = FundDataClientDetailPage(clientId = investor.investorId)
          ButtonL(
            style = ButtonL.Style.Minimal(
              icon = Some(Icon.Glyph.Eye),
              height = ButtonL.Height.Fix32
            ),
            tpe = ButtonL.Tpe.Link(href = router.urlFor(page).value),
            onClick = Observer { event =>
              event.stopPropagation()
              // openInvestorDetailDrawer(investor.investorId)
            }
          )()
        },
        renderContent = _.amend("View details")
      )().amend(tw.mr8),
      PopoverL(
        position = PortalPosition.BottomRight,
        renderTarget = (toggle, isActive) =>
          ButtonL(
            style = ButtonL.Style.Minimal(
              icon = Some(Icon.Glyph.EllipsisHorizontal),
              height = ButtonL.Height.Fix32,
              isSelected = isActive
            ),
            onClick = Observer[dom.MouseEvent] { event =>
              event.stopPropagation()
              toggle.onNext(())
            }
          )(),
        renderContent = closePopover =>
          InvestorActionMenu(
            investorInfo = InvestorActionMenu.InvestorInfo(
              investorId = investor.investorId,
              name = investor.name,
              customId = investor.customId
            ),
            onEditedInvestor = Observer.combine(closePopover),
            onRemovedInvestor = Observer.combine(closePopover, refetchInvestors)
          )()
      )()
    )
  }

  private def renderPagination(investorsData: Signal[WithInvestors.InvestorData]) = {
    InvestorPagination(
      currentPageInvestorsSignal = pageParamsSignal.map(_.pageSize),
      totalInvestorsSignal = investorsData.map(_.totalFilteredInvestors),
      pageSizeSignal = pageParamsSignal.map(_.pageSize),
      currentPageSignal = pageParamsSignal.map(_.currentPage),
      onMoveToPage = Observer { page =>
        pageParamsUpdateEventBus.emit(InvestorPageParams.ParamsUpdate(currentPage = Some(page)))
        shouldScrollToFirstRowVar.set(true)
      },
      onChangePageSize =
        pageParamsUpdateEventBus.writer.contramap(pageSize => InvestorPageParams.ParamsUpdate(pageSize = Some(pageSize)))
    )()
  }

  private def renderInvestorDetailDrawer(refetchData: Observer[Boolean], refetchComplianceSummmary: Observer[Boolean]) = {
    div(
      child.maybe <-- openInvestorDetailDrawerVar.signal.map {
        _.map { openInvestorDetailDrawer =>
          DrawerL(
            width = Drawer.Width.Custom(600),
            defaultIsOpened = true,
            renderContent = closeDrawer =>
              InvestorDetailDrawer(
                investorId = openInvestorDetailDrawer.investorId,
                closeDrawer = closeDrawer,
                refetchDataOnCloseDrawer = Observer { _ =>
                  shouldRefetchOnCloseDrawerVar.set(true)
                }
              )(),
            afterUserClose = Observer[Unit] { _ =>
              openInvestorDetailDrawer.toggleRow()
              openInvestorDetailDrawerVar.set(None)
              pageParamsUpdateEventBus.writer.onNext(InvestorPageParams.ParamsUpdate(selectInvestor = Some(None)))
              if (shouldRefetchOnCloseDrawerVar.now()) {
                shouldRefetchOnCloseDrawerVar.set(false)
                refetchData.onNext(true)
                refetchComplianceSummmary.onNext(true)
              }
            }
          )()
        }
      }
    )
  }

}

private[funddata] object InvestorPageContent {

  final case class OpenInvestorDetailDrawer(
    investorId: FundDataInvestorId,
    toggleRow: () => Unit = () => ()
  )

}
