package anduin.funddata.investor.add.importfromfund

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.pagination.laminar.PaginationL
import design.anduin.components.radio.RadioL
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import design.anduin.table.laminar.TableL

import anduin.funddata.algorithm.{LevenshsteinTextMatch, SubstringTextMatch}
import anduin.funddata.endpoint.investor.FundDataInvestorBasic
import anduin.funddata.widget.filter.SearchTextBox
import anduin.id.funddata.FundDataInvestorId
import stargazer.component.routing.laminar.WithReactRouterL
import stargazer.model.routing.DynamicAuthPage.FundData.FundDataClientDetailPage

private[importfromfund] final case class SelectClientModalContent(
  clients: List[FundDataInvestorBasic],
  onSelectedClient: Observer[FundDataInvestorBasic],
  onCancel: Observer[Unit],
  similarityConfig: SelectClientModalContent.SimilarityConfig,
  renderDescriptionOpt: Option[() => HtmlElement] = None
) {

  private val paginationSize = 50
  private val currentPageVar = Var[Int](1)

  private val typingSearchVar = Var[String]("")
  private val enteredSearchVar = Var[String]("")

  private val selectedClientOptVar = Var[Option[FundDataInvestorBasic]](None)

  private val filteredClientsSignal = enteredSearchVar.signal.distinct.map { searchText =>
    val textSearcher = SubstringTextMatch(searchText.trim)
    clients.filter { client =>
      searchText.isEmpty || textSearcher.isMatched(Seq(client.investorName, client.customId))
    }
  }

  private val similarityMap: Map[FundDataInvestorId, Double] = {
    val textMatch = LevenshsteinTextMatch(similarityConfig.baseText)
    clients
      .map(client => client.investorId -> textMatch.computeMatchedPercentage(client.investorName))
      .toMap
  }

  def apply(): HtmlElement = {
    div(
      div(
        tw.pt8,
        paddingLeft.px(28),
        paddingRight.px(28),
        renderDescriptionOpt.map(_().amend(tw.mb12)),
        renderSearch.amend(tw.mb16),
        renderClientTable
      ),
      renderFooter
    )
  }

  private def renderSearch = {
    SearchTextBox(
      typingSearchVar,
      onSearchEntered = Observer { searchText =>
        Var.set(
          enteredSearchVar -> searchText,
          currentPageVar -> 1
        )
      },
      size = TextBoxL.Size.Px40,
      placeholder = "Search and press Enter..."
    )()
  }

  private def renderClientTable = {
    val shouldShowSimilarityColumn = similarityMap.exists(_._2 >= similarityConfig.threshold)
    TableL[FundDataInvestorBasic](
      columns = List(
        Some(selectColumn),
        Some(clientNameColumn),
        Some(clientIdColumn),
        Option.when(shouldShowSimilarityColumn)(similarityColumn),
        Some(actionColumn)
      ).flatten,
      dataSignal =
        filteredClientsSignal.combineWithFn(currentPageVar.signal.distinct) { case (filteredClients, currentPage) =>
          filteredClients.slice(paginationSize * (currentPage - 1), paginationSize * currentPage)
        },
      placeholder = TableL.Placeholder(
        criteria = clientsSignal => clientsSignal.map(_.isEmpty),
        render = _ => renderTableEmpty
      ),
      options = TableL.Options(
        layout = TableL.Layout.FitColumns,
        selectableMode = TableL.SelectableMode.Clickable
      ),
      onRowClick = Observer { rowData =>
        rowData.getData().foreach(client => selectedClientOptVar.set(Some(client)))
      },
      onRowRendered = Observer[TableL.RowRenderedData[FundDataInvestorBasic]](onRowRendered),
      initialSortColumns = List(
        TableL.SortColumn(
          column = if (shouldShowSimilarityColumn) similarityColumn else clientNameColumn,
          direction = TableL.ColumnSortDirection.Asc
        )
      )
    ).amend(height.px(420))
  }

  private def renderTableEmpty = {
    NonIdealStateL(
      icon = img(src := "/web/gondor/images/funddata/magnifier-eye.svg"),
      title = div(if (clients.isEmpty) "There is no clients" else "No results found"),
      description = div(if (clients.isEmpty) "" else "You can rephrase your search to try again")
    )()
  }

  private def onRowRendered(rowRendererData: TableL.RowRenderedData[FundDataInvestorBasic]): Unit = {
    rowRendererData.rowComponent.getElement().classList.add(tw.group.css)
  }

  private def selectColumn = TableL.Column[FundDataInvestorBasic](
    title = "",
    field = "select",
    renderCell = renderProps =>
      div(
        RadioL(
          isChecked = selectedClientOptVar.signal.map(_.contains(renderProps.data)),
          onChange = Observer { _ =>
            selectedClientOptVar.set(Some(renderProps.data))
          }
        )()
      ),
    width = Some(40)
  )

  private def clientNameColumn = TableL.Column[FundDataInvestorBasic](
    title = "Client",
    field = "client-name",
    renderCell = renderProps => div(fontWeight := "500", renderProps.data.investorName),
    isSortable = true,
    sortWith = Some(sorter =>
      sorter.a.zip(sorter.b).fold[Double](0) { case (a, b) =>
        a.investorName.toLowerCase.compareTo(b.investorName.toLowerCase).toDouble
      }
    )
  )

  private def clientIdColumn = TableL.Column[FundDataInvestorBasic](
    title = "Client ID",
    field = "client-id",
    renderCell = renderProps => SelectClientModalContent.renderCustomId(renderProps.data.customId),
    isSortable = true,
    sortWith = Some(sorter =>
      sorter.a.zip(sorter.b).fold[Double](0) { case (a, b) =>
        a.customId.toLowerCase.compareTo(b.customId.toLowerCase).toDouble
      }
    ),
    width = Some(160)
  )

  private def similarityColumn = TableL.Column[FundDataInvestorBasic](
    title = "Similarity",
    field = "similarity",
    renderCell = renderProps =>
      div(
        similarityMap.get(renderProps.data.investorId).filter(_ >= similarityConfig.threshold).map { similarity =>
          TooltipL(
            renderTarget = ReviewAndImportSubscriptionTable.renderSimilarityTag(similarity),
            renderContent = _.amend("Percentage of similarity between this client name and the one being imported")
          )()
        }
      ),
    isSortable = true,
    sortWith = Some(sorter =>
      sorter.a.zip(sorter.b).fold[Double](0) { case (a, b) =>
        similarityMap.getOrElse(a.investorId, 0.0).compareTo(similarityMap.getOrElse(b.investorId, 0.0)).toDouble * (-1)
      }
    ),
    width = Some(124)
  )

  private def actionColumn = TableL.Column[FundDataInvestorBasic](
    title = "",
    field = "action",
    renderCell = renderProps => {
      WithReactRouterL { router =>
        val clientDetailPage = FundDataClientDetailPage(clientId = renderProps.data.investorId)
        TooltipL(
          renderTarget = ButtonL(
            style = ButtonL.Style.Minimal(height = ButtonL.Height.Fix24, icon = Some(Icon.Glyph.Eye)),
            tpe = ButtonL.Tpe.Link(target = ButtonL.Target.Blank, href = router.urlFor(clientDetailPage).value)
          )().amend(tw.invisible.groupHover(tw.visible)),
          renderContent = _.amend("View existing client in a new tab")
        )()
      }
    },
    width = Some(48)
  )

  private def renderFooter = {
    div(
      tw.borderTop.borderGray3.py20.flex.itemsCenter.justifyBetween,
      paddingLeft.px(28),
      paddingRight.px(28),
      child <-- filteredClientsSignal.map(_.size).distinct.map { filteredClientsSize =>
        if (filteredClientsSize > 0) {
          PaginationL(
            totalPage = Val((filteredClientsSize - 1) / paginationSize + 1),
            currentPage = currentPageVar.signal,
            onJumpToPage = currentPageVar.writer
          )()
        } else {
          div()
        }
      },
      div(
        tw.flex.itemsCenter,
        ButtonL(
          onClick = onCancel.contramap(_ => ())
        )("Cancel").amend(tw.mr8),
        ButtonL(
          style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
          isDisabled = selectedClientOptVar.signal.map(_.isEmpty),
          onClick = Observer { _ =>
            selectedClientOptVar.now().foreach(onSelectedClient.onNext)
          }
        )("Add to client")
      )
    )
  }

}

private[importfromfund] object SelectClientModalContent {

  final case class SimilarityConfig(
    baseText: String,
    threshold: Double
  )

  def renderCustomId(customId: String): HtmlElement = {
    if (customId.trim.nonEmpty) {
      div(
        tw.px4.py2.wFit,
        tw.bgGray0.borderAll.borderGray3.rounded4.textGray7.fontMono.leading16,
        fontSize.px(12),
        customId
      )
    } else {
      div("--")
    }
  }

}
