// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.fund.overview

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.field.laminar.FieldL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterL}
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import org.scalajs.dom
import zio.ZIO
import anduin.funddata.api.WithFundNameAndIDs
import anduin.funddata.endpoint.fund.{FundDataFund, UpdateFundParams}
import anduin.funddata.client.FundDataFundEndpointClient
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils

import com.raquo.laminar.api.L.*

private[overview] case class EditFundModalContent(
  oldFundData: FundDataFund,
  onCloseModal: Observer[Unit],
  onEditedFund: Observer[Unit]
) {

  private val fundNameVar = Var(oldFundData.name)
  private val fundCustomIdVar = Var(oldFundData.customId)
  private val descriptionVar = Var(oldFundData.description)
  private val descriptionValidationVar = Var[FieldL.Validation](FieldL.Validation.None)

  private def isDisableSubmitEditSignal =
    fundNameVar.signal
      .combineWith(
        fundCustomIdVar.signal,
        descriptionVar.signal,
        descriptionValidationVar.signal
      )
      .map { case (fundName, fundCustomId, description, descriptionValidation) =>
        (fundName == oldFundData.name && fundCustomId == oldFundData.customId && description == oldFundData.description) ||
        fundName.isEmpty || descriptionValidation != FieldL.Validation.None
      }
      .distinct

  private val editFundEventBus = new EventBus[UpdateFundParams]
  private val isEditingFundVar = Var[Boolean](false)

  private def onEditFund(
    editFundParams: UpdateFundParams
  ) = {
    val task = for {
      _ <- ZIO.attempt(isEditingFundVar.set(true))
      _ <- FundDataFundEndpointClient
        .updateFund(editFundParams)
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold[Unit](
            _ => {
              isEditingFundVar.set(false)
              Toast.error("Failed to edit fund, please try again")
            },
            _ => {
              Toast.success("Fund edited successfully")
              onCloseModal.onNext(())
              onEditedFund.onNext(())
            }
          )
        )
    } yield ()

    ZIOUtils.toEventStreamUnsafeDEPRECATED(task)
  }

  def apply(): HtmlElement = {
    WithFundNameAndIDs(
      oldFundData.fundId.parent,
      render = fundsData => {
        div(
          child <-- fundsData.isGettingSignal.map(
            if (_) {
              BlockIndicatorL()()
            } else {
              div(
                editFundEventBus.events.flatMapSwitch(onEditFund) --> Observer.empty,
                renderModalBody(fundsData),
                renderModalFooter
              )
            }
          )
        )
      }
    )()
  }

  private def renderModalBody(fundsData: WithFundNameAndIDs.FundsData) = {
    val fundNameValidationSignal = fundsData.fundsSignal
      .map(_.filter(_.fundId != oldFundData.fundId).map(_.name).filter(_.nonEmpty))
      .combineWith(fundNameVar.signal)
      .distinct
      .map { case (existingFundNames, name) =>
        if (existingFundNames.contains(name)) {
          FieldL.Validation.Warning("This fund name already exists")
        } else {
          FieldL.Validation.None
        }
      }

    val fundCustomIdValidationSignal = fundsData.fundsSignal
      .map(_.filter(_.fundId != oldFundData.fundId).map(_.customId).filter(_.nonEmpty))
      .combineWith(fundCustomIdVar.signal)
      .distinct
      .map { case (existingCustomIds, id) =>
        if (existingCustomIds.contains(id)) {
          FieldL.Validation.Warning("This tracking ID already exists")
        } else {
          FieldL.Validation.None
        }
      }

    ModalBodyL(
      div(
        tw.mb16,
        FieldL(
          label = Option("Name"),
          requirement = FieldL.Requirement.Required,
          validation = fundNameValidationSignal
        )(
          TextBoxL(
            isAutoFocus = true,
            placeholder = "Acme fund",
            value = fundNameVar.signal,
            onChange = fundNameVar.writer,
            status = fundNameValidationSignal.map {
              case FieldL.Validation.None => TextBoxL.Status.None
              case _                      => TextBoxL.Status.Warning
            }
          )()
        )
      ),
      div(
        tw.mb16,
        width.px := 280,
        FieldL(
          label = Option("Tracking ID"),
          requirement = FieldL.Requirement.Optional,
          validation = fundCustomIdValidationSignal
        )(
          TextBoxL(
            placeholder = "Unique ID",
            value = fundCustomIdVar.signal,
            onChange = fundCustomIdVar.writer,
            status = fundCustomIdValidationSignal.map {
              case FieldL.Validation.None => TextBoxL.Status.None
              case _                      => TextBoxL.Status.Warning
            }
          )()
        )
      ),
      div(
        FieldL(
          label = Option("Description"),
          requirement = FieldL.Requirement.Optional
        )(
          TextBoxL(
            placeholder = "Description (maximum 250 characters)",
            value = descriptionVar.signal,
            onChange = Observer[String] { description =>
              descriptionVar.set(description)
              if (description.length > 250) {
                descriptionValidationVar.set(FieldL.Validation.Invalid("Number of characters exceeded"))
              } else {
                descriptionValidationVar.set(FieldL.Validation.None)
              }
            },
            tpe = TextBoxL.Tpe.Area(4),
            unsafeMod = Seq(minHeight.px(132)),
            status = descriptionValidationVar.signal.map {
              case FieldL.Validation.None => TextBoxL.Status.None
              case _                      => TextBoxL.Status.Invalid
            }
          )()
        ),
        div(
          tw.mt4.textSmall,
          child <-- descriptionVar.signal.combineWith(descriptionValidationVar.signal).map {
            case (description, validation) =>
              div(
                validation match {
                  case FieldL.Validation.None => tw.textGray7
                  case _                      => tw.textDanger5
                },
                s"${description.length}/250 characters"
              )
          }
        )
      )
    )
  }

  private def renderModalFooter = {
    ModalFooterL(
      div(
        tw.flex.justifyEnd,
        ButtonL(onClick = onCloseModal.contramap(_ => ()), isDisabled = isEditingFundVar.signal)("Cancel")
          .amend(tw.mr8),
        ButtonL(
          style = ButtonL.Style.Full(color = ButtonL.Color.Primary, isBusy = isEditingFundVar.signal),
          isDisabled = isDisableSubmitEditSignal,
          onClick = Observer[dom.MouseEvent] { _ =>
            val fundName = fundNameVar.now().trim
            val fundCustomId = fundCustomIdVar.now().trim
            val description = descriptionVar.now()
            if (fundName.nonEmpty) {
              editFundEventBus.writer.onNext(
                UpdateFundParams(
                  oldFundData.fundId,
                  Option.when(fundName != oldFundData.name)(fundName),
                  Option.when(fundCustomId != oldFundData.customId)(fundCustomId),
                  Option.when(description != oldFundData.description)(description)
                )
              )
            }
          }
        )("Save")
      )
    )
  }

}
