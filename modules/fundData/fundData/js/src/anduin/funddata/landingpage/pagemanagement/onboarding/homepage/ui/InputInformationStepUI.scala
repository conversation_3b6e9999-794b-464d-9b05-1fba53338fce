// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.landingpage.pagemanagement.onboarding.homepage.ui

import com.raquo.laminar.api.L.*
import design.anduin.components.field.laminar.FieldL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL}
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.style.tw.*

import anduin.funddata.constant.Constant
import anduin.funddata.landingpage.pagemanagement.onboarding.homepage.{
  OnboardingHomePageFlowData,
  OnboardingHomePageFlowStep,
  OnboardingHomePageFlowStepUI
}
import anduin.id.funddata.FundDataFirmId

private[homepage] case class InputInformationStepUI(
  firmId: FundDataFirmId,
  stepInput: OnboardingHomePageFlowData.Input,
  initialOutputOpt: Option[OnboardingHomePageFlowData.InputInformation],
  onNext: Observer[OnboardingHomePageFlowData.InputInformation],
  onBack: Observer[Unit],
  onCancel: Observer[Unit]
) extends OnboardingHomePageFlowStepUI[
      OnboardingHomePageFlowStep.InputInformation,
      OnboardingHomePageFlowData.Input,
      OnboardingHomePageFlowData.InputInformation
    ] {

  override def currentStep: OnboardingHomePageFlowStep.InputInformation = OnboardingHomePageFlowStep.InputInformation()

  private val nameSignal = stepOutputVar.signal.map(_.map(_.name).getOrElse(""))

  private val onChangeName = Observer[String] { name =>
    stepOutputVar.writer.onNext(
      Some(OnboardingHomePageFlowData.InputInformation(name.take(Constant.LandingPage.PageNameMaxLength)))
    )
  }

  private val disableActionSignal = nameSignal
    .map { rawName =>
      val name = rawName.trim
      val emptyName = name.isEmpty
      emptyName
    }

  private def renderName = {
    div(
      div(tw.fontSemiBold.mb4, "Homepage name"),
      div(tw.text11.mb4, "The name must be unique and will be shown in the app and browser tab"),
      FieldL()(
        TextBoxL(
          value = nameSignal,
          onChange = onChangeName,
          isAutoFocus = true,
          placeholder = "E.g. Biotech Fund I"
        )()
      )
    )
  }

  def apply(): HtmlElement = {
    div(
      width.px(480),
      ModalBodyL(
        div(
          div(renderName)
        )
      ),
      ModalFooterWCancelL(
        cancel = onCancel
      )(
        renderNextButton(isDisabled = disableActionSignal, child = () => "Next")
      )
    )
  }

}
