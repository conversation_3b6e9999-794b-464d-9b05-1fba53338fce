// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.module

import com.softwaremill.macwire.wire

import anduin.annotation.service.AnnotationDocumentService
import anduin.encryption.StreamingEncryptionService
import anduin.environment.FundDataFirmEnvironmentAuthenticationIntegrationService
import anduin.evendim.client.EvendimClient
import anduin.forms.service.{FormDataUserTempService, FormMappingService, FormService, FormTemplateMappingService}
import anduin.funddata.appnavigation.FundDataAppNavigatorService
import anduin.funddata.auditlog.FundDataAuditLogService
import anduin.funddata.clientgroup.FundDataClientGroupService
import anduin.funddata.communication.mapping.FundLegalEntityCommunicationMappingService
import anduin.funddata.communication.{
  FundDataCommunicationService,
  FundDataFirmCommunicationService,
  FundDataFundLegalEntityCommunicationService
}
import anduin.funddata.contact.note.FundDataContactNoteService
import anduin.funddata.contact.{FundDataContactExportService, FundDataContactService}
import anduin.funddata.dataroom.FundDataDataRoomService
import anduin.funddata.document.split.FundDataDocumentSplitService
import anduin.funddata.email.FundDataEmailService
import anduin.funddata.email.notification.{
  FundDataAssessmentDueDateNotificationService,
  FundDataDocumentExpirationNotificationService
}
import anduin.funddata.environment.{
  FundDataEnvironmentPolicyAdminService,
  FundDataEnvironmentPolicyService,
  FundDataFirmEnvironmentAuthenticationIntegrationServiceImpl
}
import anduin.funddata.event.{FundDataEventExportService, FundDataEventService}
import anduin.funddata.firm.*
import anduin.funddata.firm.config.{FundDataExpirationConfigService, FundDataLlmConfigService}
import anduin.funddata.firm.feature.FundDataFeatureSwitchService
import anduin.funddata.fund.fundfamily.FundFamilyService
import anduin.funddata.fund.fundlegalentity.FundLegalEntityService
import anduin.funddata.fund.fundsub.FundDataFundSubService
import anduin.funddata.fund.subscription.FundDataFundSubscriptionService
import anduin.funddata.fund.transaction.FundTransactionService
import anduin.funddata.fund.transaction.document.FundTransactionDocumentService
import anduin.funddata.fund.transaction.sync.FundTransactionSyncService
import anduin.funddata.fund.{FundDataFundGroupService, FundDataFundService}
import anduin.funddata.group.{FundDataGroupMemberService, FundDataGroupRoleService, FundDataGroupService}
import anduin.funddata.guest.FundDataGuestService
import anduin.funddata.importbyspreadsheet.{FundDataImportBySpreadsheetService, FundDataImportInputValidateService}
import anduin.funddata.integplatform.{
  FundDataIntegPlatformExternalService,
  FundDataIntegPlatformExternalServiceImpl,
  FundDataIntegPlatformInternalService
}
import anduin.funddata.integration.{
  FundDataIntegrationGateService,
  FundDataIntegrationService,
  FundDataIntegrationServiceImpl
}
import anduin.funddata.investmententity.FundDataInvestmentEntityService
import anduin.funddata.investmententity.assessment.FundDataInvestmentEntityAssessmentService
import anduin.funddata.investmententity.contact.FundDataInvestmentEntityContactService
import anduin.funddata.investmententity.document.{
  FundDataInvestmentEntityDocumentRequestService,
  FundDataInvestmentEntityDocumentService,
  FundDataInvestmentEntityDocumentTextractService
}
import anduin.funddata.investmententity.note.FundDataInvestmentEntityNoteService
import anduin.funddata.investmententity.profile.{
  FundDataInvestmentEntityProfileHistoryService,
  FundDataInvestmentEntityProfileService,
  FundDataProfileConflictService
}
import anduin.funddata.investor.FundDataInvestorService
import anduin.funddata.landingpage.*
import anduin.funddata.landingpage.document.FundDataPortalLpDocumentPageService
import anduin.funddata.permission.{FundDataFolderPermissionService, FundDataPermissionService}
import anduin.funddata.portal.FundDataPortalService
import anduin.funddata.portal.document.FundDataPortalDocumentService
import anduin.funddata.portal.document.distribution.flow.DocDistributionFlowExecution
import anduin.funddata.portal.document.distribution.{
  DocDistributionFileService,
  DocDistributionNotificationService,
  DocDistributionPermissionService,
  FundDataPortalDocDistributionService
}
import anduin.funddata.portal.file.InvestorPortalFileService
import anduin.funddata.portal.flow.InvestorPortalFlowExecution
import anduin.funddata.portal.instance.{FundDataPortalInstanceInvestmentEntityService, FundDataPortalInstanceService}
import anduin.funddata.request.{FundDataRequestConfigService, FundDataRequestService}
import anduin.funddata.role.FundDataRoleService
import anduin.funddata.simulator.{
  FundDataLandingPageSimulatorService,
  FundDataPortalSimulatorService,
  FundDataSimulatorService,
  FundDataSimulatorServiceImpl
}
import anduin.funddata.sync.{FundDataDataLakeSyncService, FundDataFundSubSyncService, FundDataGreylinSyncService}
import anduin.funddata.tag.FundDataTagService
import anduin.funddata.webhook.FundDataWebhookService
import anduin.funddata.whitelabel.FundDataWhiteLabelService
import anduin.fundsub.datalakeingestion.FundSubDataLakeIngestionService
import anduin.fundsub.form.FundSubFormService
import anduin.fundsub.group.{FundSubGroupMemberService, FundSubGroupService}
import anduin.fundsub.integration.FundSubExternalIntegrationService
import anduin.greylin.GreylinDataService
import anduin.integplatform.service.external.IntegPlatformExternalService
import anduin.investmententity.service.InvestmentEntitySimulatorService
import anduin.ontology.service.FormSaProfileMappingService
import anduin.sa.service.{CommonSaProfileMappingService, MappingDestinationService, SaProfileService}
import anduin.signature.integration.SignatureIntegrationService

trait FundDataServiceModule extends GondorCoreServiceModule with BifrostServiceModule {

  def formService: FormService
  def formTemplateMappingService: FormTemplateMappingService
  def formMappingService: FormMappingService
  def greylinDataService: GreylinDataService
  def fundSubExternalIntegrationService: FundSubExternalIntegrationService
  def signatureIntegrationService: SignatureIntegrationService
  def fundSubFormService: FundSubFormService
  def fundSubGroupService: FundSubGroupService
  def fundSubGroupMemberService: FundSubGroupMemberService
  def fundSubDataLakeIngestionService: FundSubDataLakeIngestionService
  def investmentEntitySimulatorService: InvestmentEntitySimulatorService
  def evendimClient: EvendimClient
  def integPlatformExternalService: IntegPlatformExternalService
  def saProfileService: SaProfileService
  def commonSaProfileMappingService: CommonSaProfileMappingService
  def mappingDestinationService: MappingDestinationService
  def annotationDocumentService: AnnotationDocumentService
  def formSaProfileMappingService: FormSaProfileMappingService
  def formDataUserTempService: FormDataUserTempService

  // Keep at least 1 unwired service to combat zinc bug

  given fundDataPermissionService: FundDataPermissionService = wire[FundDataPermissionService]
  given fundDataFolderPermissionService: FundDataFolderPermissionService = wire[FundDataFolderPermissionService]
  given fundDataEventService: FundDataEventService = wire[FundDataEventService]
  given fundDataEventExportService: FundDataEventExportService = wire[FundDataEventExportService]
  given fundDataFirmService: FundDataFirmService = wire[FundDataFirmService]
  given fundDataFirmSaService: FundDataFirmSaService = wire[FundDataFirmSaService]
  given fundDataFirmEnvironmentService: FundDataFirmEnvironmentService = wire[FundDataFirmEnvironmentService]
  given fundDataFirmProfileService: FundDataFirmProfileService = wire[FundDataFirmProfileService]

  given fundDataFirmProfileImportExportService: FundDataFirmProfileImportExportService =
    wire[FundDataFirmProfileImportExportService]

  given fundDataFirmOrganizationService: FundDataFirmOrganizationService = wire[FundDataFirmOrganizationService]
  given fundDataFeatureSwitchService: FundDataFeatureSwitchService = wire[FundDataFeatureSwitchService]
  given fundDataExpirationConfigService: FundDataExpirationConfigService = wire[FundDataExpirationConfigService]
  given fundDataGroupRoleService: FundDataGroupRoleService = wire[FundDataGroupRoleService]
  given fundDataGroupService: FundDataGroupService = wire[FundDataGroupService]
  given fundDataGroupMemberService: FundDataGroupMemberService = wire[FundDataGroupMemberService]
  given fundDataImportBySpreadsheetService: FundDataImportBySpreadsheetService = wire[FundDataImportBySpreadsheetService]
  given fundDataImportInputValidateService: FundDataImportInputValidateService = wire[FundDataImportInputValidateService]
  given fundDataFundService: FundDataFundService = wire[FundDataFundService]
  given fundDataDataRoomService: FundDataDataRoomService = wire[FundDataDataRoomService]

  given fundDataRoleService: FundDataRoleService = wire[FundDataRoleService]

  given fundDataClientGroupService: FundDataClientGroupService = wire[FundDataClientGroupService]

  given fundDataFundGroupService: FundDataFundGroupService = wire[FundDataFundGroupService]
  given fundDataFundSubscriptionService: FundDataFundSubscriptionService = wire[FundDataFundSubscriptionService]

  given fundDataWhiteLabelService: FundDataWhiteLabelService = wire[FundDataWhiteLabelService]
  given fundDataEmailService: FundDataEmailService = wire[FundDataEmailService]
  given encryptionService: StreamingEncryptionService = wire[StreamingEncryptionService]

  given fundDataDocumentExpirationNotificationService: FundDataDocumentExpirationNotificationService =
    wire[FundDataDocumentExpirationNotificationService]

  given fundDataAssessmentDueDateNotificationService: FundDataAssessmentDueDateNotificationService =
    wire[FundDataAssessmentDueDateNotificationService]

  given fundDataTagService: FundDataTagService = wire[FundDataTagService]

  given fundDataInvestorService: FundDataInvestorService = wire[FundDataInvestorService]

  given fundDataProfileConflictService: FundDataProfileConflictService =
    wire[FundDataProfileConflictService]

  given fundDataRequestService: FundDataRequestService = wire[FundDataRequestService]

  given fundDataRequestConfigService: FundDataRequestConfigService = wire[FundDataRequestConfigService]

  given fundDataInvestmentEntityService: FundDataInvestmentEntityService =
    wire[FundDataInvestmentEntityService]

  given fundDataInvestmentEntityDocumentService: FundDataInvestmentEntityDocumentService =
    wire[FundDataInvestmentEntityDocumentService]

  given fundDataInvestmentEntityDocumentTextractService: FundDataInvestmentEntityDocumentTextractService =
    wire[FundDataInvestmentEntityDocumentTextractService]

  given fundDataInvestmentEntityContactService: FundDataInvestmentEntityContactService =
    wire[FundDataInvestmentEntityContactService]

  given fundDataInvestmentEntityProfileService: FundDataInvestmentEntityProfileService =
    wire[FundDataInvestmentEntityProfileService]

  given fundDataInvestmentEntityProfileHistoryService: FundDataInvestmentEntityProfileHistoryService =
    wire[FundDataInvestmentEntityProfileHistoryService]

  given fundDataInvestmentEntityAssessmentService: FundDataInvestmentEntityAssessmentService =
    wire[FundDataInvestmentEntityAssessmentService]

  given fundDataInvestmentEntityNoteService: FundDataInvestmentEntityNoteService =
    wire[FundDataInvestmentEntityNoteService]

  given fundDataAuditLogService: FundDataAuditLogService = wire[FundDataAuditLogService]
  given fundDataGreylinSyncService: FundDataGreylinSyncService = wire[FundDataGreylinSyncService]
  given fundDataFundSubSyncService: FundDataFundSubSyncService = wire[FundDataFundSubSyncService]
  given fundDataDataLakeSyncService: FundDataDataLakeSyncService = wire[FundDataDataLakeSyncService]

  given fundDataIntegrationService: FundDataIntegrationService = wire[FundDataIntegrationServiceImpl]

  given fundDataInvestmentEntityDocumentRequestService: FundDataInvestmentEntityDocumentRequestService =
    wire[FundDataInvestmentEntityDocumentRequestService]

  given fundDataLandingPageSimulatorService: FundDataLandingPageSimulatorService =
    wire[FundDataLandingPageSimulatorService]

  given fundDataPortalSimulatorService: FundDataPortalSimulatorService =
    wire[FundDataPortalSimulatorService]

  given fundDataSimulatorService: FundDataSimulatorService =
    wire[FundDataSimulatorServiceImpl]

  given fundDataWebhookService: FundDataWebhookService = wire[FundDataWebhookService]

  given fundDataPageValidationService: FundDataPageValidationService = wire[FundDataPageValidationService]

  given fundDataLandingPageService: FundDataLandingPageService = wire[FundDataLandingPageService]

  given fundDataPortalHomePageService: FundDataPortalHomePageService = wire[FundDataPortalHomePageService]

  given fundDataOpportunityPageService: FundDataOpportunityPageService = wire[FundDataOpportunityPageService]

  given fundDataVehiclePageService: FundDataVehiclePageService = wire[FundDataVehiclePageService]

  given fundDataPortalLpDocumentPageService: FundDataPortalLpDocumentPageService =
    wire[FundDataPortalLpDocumentPageService]

  given fundDataWebContentService: FundDataWebContentService = wire[FundDataWebContentService]

  given fundDataLandingPageConfigService: FundDataLandingPageConfigService = wire[FundDataLandingPageConfigService]

  given fundDataGuestService: FundDataGuestService = wire[FundDataGuestService]

  given fundDataLandingPageLinkService: FundDataLandingPageLinkService = wire[FundDataLandingPageLinkService]

  given fundDataWidgetDataService: FundDataWidgetDataService = wire[FundDataWidgetDataService]

  given fundDataFirmEnvironmentAuthenticationIntegrationService
    : FundDataFirmEnvironmentAuthenticationIntegrationService =
    wire[FundDataFirmEnvironmentAuthenticationIntegrationServiceImpl]

  given fundLegalEntityService: FundLegalEntityService = wire[FundLegalEntityService]

  given fundTransactionSyncService: FundTransactionSyncService = wire[FundTransactionSyncService]

  given fundFamilyService: FundFamilyService = wire[FundFamilyService]

  given fundTransactionService: FundTransactionService = wire[FundTransactionService]

  given fundTransactionDocumentService: FundTransactionDocumentService = wire[FundTransactionDocumentService]

  given fundDataFundSubService: FundDataFundSubService = wire[FundDataFundSubService]

  given fundDataContactService: FundDataContactService = wire[FundDataContactService]

  given fundDataContactExportService: FundDataContactExportService = wire[FundDataContactExportService]

  given fundDataContactNoteService: FundDataContactNoteService = wire[FundDataContactNoteService]

  given fundDataCommunicationService: FundDataCommunicationService = wire[FundDataCommunicationService]

  given fundDataFirmCommunicationService: FundDataFirmCommunicationService = wire[FundDataFirmCommunicationService]

  given fundDataFundLegalEntityCommunicationService: FundDataFundLegalEntityCommunicationService =
    wire[FundDataFundLegalEntityCommunicationService]

  given fundDataInvestmentEntityCommunicationService: FundLegalEntityCommunicationMappingService =
    wire[FundLegalEntityCommunicationMappingService]

  given investorPortalFlowExecution: InvestorPortalFlowExecution = wire[InvestorPortalFlowExecution]

  given fundDataPortalService: FundDataPortalService = wire[FundDataPortalService]

  given fundDataPortalInstanceService: FundDataPortalInstanceService = wire[FundDataPortalInstanceService]

  given fundDataPortalInstanceInvestmentEntityService: FundDataPortalInstanceInvestmentEntityService =
    wire[FundDataPortalInstanceInvestmentEntityService]

  given fundDataPortalDocDistributionService: FundDataPortalDocDistributionService =
    wire[FundDataPortalDocDistributionService]

  given fundDataPortalDocumentService: FundDataPortalDocumentService = wire[FundDataPortalDocumentService]

  given investorPortalFileService: InvestorPortalFileService = wire[InvestorPortalFileService]

  given docDistributionFileService: DocDistributionFileService = wire[DocDistributionFileService]

  given docDistributionPermissionService: DocDistributionPermissionService = wire[DocDistributionPermissionService]

  given docDistributionNotificationService: DocDistributionNotificationService = wire[DocDistributionNotificationService]

  given docDistributionFlowExecution: DocDistributionFlowExecution = wire[DocDistributionFlowExecution]

  given fundDataDocumentSplitService: FundDataDocumentSplitService = wire[FundDataDocumentSplitService]

  given fundDataLlmConfigService: FundDataLlmConfigService = wire[FundDataLlmConfigService]

  given fundDataIntegrationGateService: FundDataIntegrationGateService = wire[FundDataIntegrationGateService]

  given fundDataIntegPlatformInternalService: FundDataIntegPlatformInternalService =
    wire[FundDataIntegPlatformInternalService]

  given fundDataIntegPlatformExternalService: FundDataIntegPlatformExternalService =
    wire[FundDataIntegPlatformExternalServiceImpl]

  given fundDataEnvironmentPolicyService: FundDataEnvironmentPolicyService = wire[FundDataEnvironmentPolicyService]

  given fundDataEnvironmentPolicyAdminService: FundDataEnvironmentPolicyAdminService =
    wire[FundDataEnvironmentPolicyAdminService]

  given fundDataAppNavigatorService: FundDataAppNavigatorService = wire[FundDataAppNavigatorService]

}
