// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.email

import java.time.Instant

import io.github.arainko.ducktape.*
import scalapb.UnknownFieldSet
import zio.prelude.Validation
import zio.{Task, UIO, ZIO}

import anduin.account.profile.UserProfileService
import anduin.dms.service.FileService
import anduin.email.CustomSmtpServerConfig
import anduin.email.smtp.{CustomEmailAddressModel, CustomSmtpServerConfigModel}
import anduin.encryption.StreamingEncryptionService
import anduin.fdb.record.FDBRecordDatabase
import anduin.fdb.record.model.RecordIO
import anduin.funddata.email.generate.*
import anduin.funddata.email.generate.core.{FundDataDefaultEmailTemplate, FundDataEmailGenerate}
import anduin.funddata.email.generate.guest.{NotifyGuestEmailGenerate, RemoveGuestEmailGenerate}
import anduin.funddata.endpoint.email.*
import anduin.funddata.endpoint.firm.{FirmSmtpConfigResponse, UpdateFirmSmtpConfigParams}
import anduin.funddata.error.FundDataError.FundDataValidationError
import anduin.funddata.event.FundDataEventService
import anduin.funddata.group.FundDataGroupMemberService
import anduin.funddata.permission.FundDataPermissionService
import anduin.funddata.sync.FundDataFundSubSyncConfigStoreOperations
import anduin.funddata.validation.FundDataValidationUtils
import anduin.funddata.whitelabel.FundDataWhiteLabelService
import anduin.greylin.GreylinDataService
import anduin.id.funddata.FundDataFirmId
import anduin.id.fundsub.FundSubId
import anduin.kafka.{KafkaFiber, KafkaService, KafkaSimpleConsumer}
import anduin.link.LinkGeneratorService
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.protobuf.funddata.email.template.{
  FundDataEmailTemplateModel,
  FundDataEmailSavedTemplate as FundDataEmailSavedTemplateGeneratedMessage,
  FundDataEmailTemplate as FundDataEmailTemplateGeneratedMessage
}
import anduin.protobuf.funddata.email.{FundDataEmailConfigModel, FundDataProfileConflictNotificationConfig}
import anduin.protobuf.funddata.event.*
import anduin.taskrequest.TaskRequestService
import anduin.utils.{DateTimeUtils, UserProfileUtils}
import com.anduin.stargazer.external.base64.Base64
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.email.utils.EmailContentUtils
import com.anduin.stargazer.service.email.{EmailProviderBuilderConfig, EmailSenderService}
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FundDataEmailService(
  backendConfig: GondorBackendConfig,
  emailSenderService: EmailSenderService,
  kafkaService: KafkaService,
  fundDataEventService: FundDataEventService,
  fundDataGroupMemberService: FundDataGroupMemberService,
  encryptionService: StreamingEncryptionService
)(
  using val linkGeneratorService: LinkGeneratorService,
  val taskRequestService: TaskRequestService,
  val fundDataPermissionService: FundDataPermissionService,
  val userProfileService: UserProfileService,
  val fileService: FileService,
  val fundDataWhiteLabelService: FundDataWhiteLabelService,
  val greylinDataService: GreylinDataService
) extends KafkaFiber {

  private val userProfileUtils = UserProfileUtils()

  def getEmailTemplate(params: GetEmailTemplateParams, actor: UserId): Task[GetEmailTemplateResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor gets email template of type ${params.emailType} of ${params.firmId}")
      _ <- fundDataPermissionService.validateUserHasRole(params.firmId, actor)
      emailTemplateModel <- FDBRecordDatabase.transact(FundDataEmailTemplateStoreOperations.Production) {
        _.getOpt(params.firmId, params.emailType)
          .map(_.getOrElse(FundDataEmailTemplateModel(firmId = params.firmId, emailType = params.emailType)))
      }
      savedTemplates <- ZIOUtils.foreachParN(4)(emailTemplateModel.savedTemplates) { savedTemplate =>
        for {
          updatedByOpt <- ZIOUtils.traverseOption(savedTemplate.updatedByOpt) { updatedBy =>
            userProfileUtils.getBasicUserInfo(updatedBy)
          }
          template <- userProfileUtils.withBasicUserInfo(savedTemplate.createdBy) { createdBy =>
            savedTemplate
              .into[FundDataSavedEmailTemplate]
              .transform(
                Field.const(
                  _.emailContent,
                  sanitizeEmailTemplate(
                    savedTemplate.emailContent
                      .map(_.into[FundDataEmailTemplate].transform())
                      .getOrElse(FundDataEmailTemplate())
                  )
                ),
                Field.const(_.updatedByOpt, updatedByOpt),
                Field.const(_.createdByOpt, Option(createdBy)),
                Field.const(_.isDefault, emailTemplateModel.defaultTemplateIdOpt.contains(savedTemplate.id))
              )
          }
        } yield template
      }
    } yield GetEmailTemplateResponse(
      savedTemplates = savedTemplates,
      anduinTemplate = sanitizeEmailTemplate(
        FundDataDefaultEmailTemplate.getEmailTemplate(params.emailType)
      )
    )
  }

  def deleteEmailTemplatesAndConfigsWhenDeleteFirm(firmId: FundDataFirmId, actor: UserId): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor delete email templates and configs for  $firmId")
      _ <- FDBRecordDatabase.transact(FundDataEmailStoreOperations.Production)(_.deleteEmailConfig(firmId))
      _ <- FDBRecordDatabase.transact(FundDataEmailTemplateStoreOperations.Production)(_.delete(firmId))
    } yield ()
  }

  private[funddata] def getEmailConfigUnsafe(firmId: FundDataFirmId): Task[Option[FundDataEmailConfigModel]] = {
    for {
      config <- FDBRecordDatabase.transact(FundDataEmailStoreOperations.Production)(_.getEmailConfig(firmId))
    } yield config
  }

  private[funddata] def updateProfileConflictEmailConfigUnsafe(firmId: FundDataFirmId, recipients: Seq[UserId])
    : Task[Unit] = {
    for {
      _ <- FDBRecordDatabase.transact(FundDataEmailStoreOperations.Production) { ops =>
        for {
          configOpt <- ops.getEmailConfig(firmId)
          newConfig <- RecordIO.attempt(
            configOpt
              .getOrElse(
                FundDataEmailConfigModel(firmId)
              )
              .copy(profileConflictNotificationConfig =
                Some(FundDataProfileConflictNotificationConfig(recipients = recipients))
              )
          )
          _ <- ops.upsertEmailConfig(newConfig)
        } yield ()
      }
    } yield ()
  }

  def upsertEmailTemplate(
    params: UpsertEmailTemplateParams,
    actor: UserId
  ): Task[Unit] = {
    val templateId = params.templateId.trim
    val templateName = params.templateName.trim
    val emailContent = sanitizeEmailTemplate(
      params.emailTemplate
    ).into[FundDataEmailTemplateGeneratedMessage]
      .transform(Field.const(_.unknownFields, UnknownFieldSet.empty))
    for {
      _ <- ZIO.logInfo(s"$actor update email template type ${params.emailType} of ${params.firmId}")
      _ <- Validation
        .validate(
          FundDataValidationUtils.nonEmpty("templateName", templateName),
          FundDataValidationUtils.nonEmpty("templateId", templateId)
        )
        .toZIO
        .mapError(FundDataValidationError(_))
      _ <- fundDataPermissionService.validateUserHasAdminRole(params.firmId, actor)
      _ <- FDBRecordDatabase.transact(FundDataEmailTemplateStoreOperations.Production)(ops =>
        for {
          emailTemplateModel <- ops
            .getOpt(params.firmId, params.emailType)
            .map(_.getOrElse(FundDataEmailTemplateModel(firmId = params.firmId, emailType = params.emailType)))

          existedTemplateOpt = emailTemplateModel.savedTemplates.find(_.id == templateId)

          upsertedEmailTemplate = existedTemplateOpt
            .map(
              _.copy(
                emailContent = Some(emailContent),
                updatedByOpt = Some(actor),
                updatedAt = Some(Instant.now()),
                templateName = templateName
              )
            )
            .getOrElse(
              FundDataEmailSavedTemplateGeneratedMessage(
                id = templateId,
                emailContent = Some(emailContent),
                createdBy = actor,
                createdAt = Some(Instant.now()),
                templateName = templateName
              )
            )

          upsertedTemplateModel = emailTemplateModel.copy(
            savedTemplates = (emailTemplateModel.savedTemplates
              .map(savedTemplate => savedTemplate.id -> savedTemplate)
              .toMap + (upsertedEmailTemplate.id -> upsertedEmailTemplate)).values.toSeq,
            defaultTemplateIdOpt = if (params.isSetAsDefault) {
              Some(params.templateId)
            } else {
              if (emailTemplateModel.defaultTemplateIdOpt.contains(params.templateId)) {
                None
              } else emailTemplateModel.defaultTemplateIdOpt
            }
          )
          _ <- ops.upsert(upsertedTemplateModel)
        } yield ()
      )
    } yield ()
  }

  def removeEmailTemplate(params: RemoveEmailTemplateParams, actor: UserId): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"$actor remove email template ${params.removedTemplateId} type ${params.emailType} of ${params.firmId}"
      )
      _ <- fundDataPermissionService.validateUserHasAdminRole(params.firmId, actor)
      _ <- FDBRecordDatabase.transact(FundDataEmailTemplateStoreOperations.Production)(ops =>
        for {
          emailTemplateModelOpt <- ops.getOpt(params.firmId, params.emailType)
          emailTemplateModel <- RecordIO.fromOption(
            emailTemplateModelOpt,
            FundDataValidationError(s"${params.firmId} does not have saved email templates of types ${params.emailType}")
          )
          _ <- RecordIO.validate(params.removedTemplateId.nonEmpty)(
            FundDataValidationError("removedTemplateId should not be empty")
          )
          _ <- RecordIO.validate(emailTemplateModel.savedTemplates.map(_.id).contains(params.removedTemplateId))(
            FundDataValidationError("removedTemplateId should not exist")
          )
          _ <- ops.upsert(
            emailTemplateModel.copy(
              savedTemplates = emailTemplateModel.savedTemplates.filterNot(_.id == params.removedTemplateId),
              defaultTemplateIdOpt = if (emailTemplateModel.defaultTemplateIdOpt.contains(params.removedTemplateId)) {
                None
              } else {
                emailTemplateModel.defaultTemplateIdOpt
              }
            )
          )
        } yield ()
      )
    } yield ()
  }

  def useAnduinDefaultEmailTemplate(params: UseAnduinDefaultEmailTemplateParams, actor: UserId): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"$actor switches to use Anduin default email template for type ${params.emailType} in ${params.firmId}"
      )
      _ <- fundDataPermissionService.validateUserHasAdminRole(params.firmId, actor)
      _ <- FDBRecordDatabase.transact(FundDataEmailTemplateStoreOperations.Production)(ops =>
        for {
          emailTemplateModelOpt <- ops.getOpt(params.firmId, params.emailType)
          emailTemplateModel <- RecordIO.fromOption(
            emailTemplateModelOpt,
            FundDataValidationError(s"${params.firmId} does not have saved email templates of type ${params.emailType}")
          )
          _ <- ops.upsert(
            emailTemplateModel.copy(
              defaultTemplateIdOpt = None
            )
          )
        } yield ()
      )
    } yield ()
  }

  private def sanitizeEmailTemplate(
    emailTemplate: FundDataEmailTemplate
  ): FundDataEmailTemplate =
    FundDataEmailTemplate(
      subject = EmailContentUtils.sanitizeSubject(emailTemplate.subject),
      title = EmailContentUtils.sanitizeBody(emailTemplate.title),
      body = EmailContentUtils.sanitizeBody(emailTemplate.body),
      footer = EmailContentUtils.sanitizeBody(emailTemplate.footer),
      primaryCTA = EmailContentUtils.htmlEscape(emailTemplate.primaryCTA)
    )

  /** Email sender
    */

  private val processFundDataEventConsumer = KafkaSimpleConsumer[FundDataFirmId, FundDataEvent](
    kafkaService = kafkaService,
    topic = fundDataEventService.fundDataEventTopic,
    consumerGroupName = s"${fundDataEventService.fundDataEventTopic.name}-email-service-consumer",
    handler = fundDataEventHandler
  )

  private def fundDataEventHandler(
    firmId: FundDataFirmId,
    event: FundDataEvent
  ): Task[Unit] = {
    event.detail match {
      case inviteMember: InviteMemberEvent =>
        sendEmail(
          InviteMemberEmailGenerate(
            firmId,
            invitee = inviteMember.userId,
            inviter = event.actor
          )
        )
      case remindMember: RemindMemberInvitationEvent =>
        sendEmail(
          RemindMemberEmailGenerate(
            inviter = event.actor,
            invitee = remindMember.userId,
            groupId = remindMember.groupId,
            zoneId = remindMember.zoneId.getOrElse(DateTimeUtils.defaultTimezone),
            userCustomTemplateOpt = remindMember.emailTemplate.map(
              _.to[FundDataEmailTemplate]
            )
          )
        )
      case removeMember: RemoveMemberEvent =>
        for {
          _ <- ZIOUtils.when(removeMember.shouldNotify) {
            sendEmail(
              if (removeMember.userId == event.actor) {
                LeaveFundDataEmailGenerate(
                  firmId = firmId,
                  actor = event.actor
                )
              } else {
                RemoveMemberEmailGenerate(
                  firmId = firmId,
                  actor = event.actor,
                  removedMember = removeMember.userId
                )
              }
            )
          }
          // send emails when admin in auto sync config is removed
          configOpt <- FDBRecordDatabase.transact(FundDataFundSubSyncConfigStoreOperations.Production)(
            _.getConfigOpt(firmId)
          )
          shouldSendDowngradeAdminEmail = configOpt.exists(config =>
            config.isEnabled && config.fundSubConfigs.exists(_.actor == removeMember.userId)
          )
          _ <- ZIO.when(shouldSendDowngradeAdminEmail) {
            sendAutoSyncDowngradeAdmin(
              firmId = firmId,
              downgradedAdmin = removeMember.userId,
              downgradedFrom = Right(firmId)
            )
          }
        } yield ()
      case requestSentEvent: RequestSentEvent =>
        ZIOUtils
          .foreachParN(4)(requestSentEvent.recipients) { recipient =>
            sendEmail(
              RequestSentEmailGenerate(
                firmId = firmId,
                requester = event.actor,
                recipientOpt = Some(recipient),
                investmentEntityIdOpt = Some(requestSentEvent.investmentEntityId),
                taskRequestIdOpt = Some(requestSentEvent.requestId),
                requestSentEmailType = RequestSentEmailGenerate.EmailType.RequestSentToRecipient,
                userCustomTemplateOpt = requestSentEvent.emailTemplate.map(_.into[FundDataEmailTemplate].transform())
              )
            )
          }
          .unit
      case requestRemindEvent: RequestRemindEvent =>
        ZIOUtils
          .foreachParN(4)(requestRemindEvent.recipients) { recipient =>
            sendEmail(
              RequestSentEmailGenerate(
                firmId = firmId,
                requester = event.actor,
                recipientOpt = Some(recipient),
                investmentEntityIdOpt = Some(requestRemindEvent.investmentEntityId),
                taskRequestIdOpt = Some(requestRemindEvent.requestId),
                requestSentEmailType = RequestSentEmailGenerate.EmailType.RequestRemindedToRecipient,
                userCustomTemplateOpt = requestRemindEvent.emailTemplate.map(_.into[FundDataEmailTemplate].transform())
              )
            )
          }
          .unit
      case requestSubmittedEvent: RequestSubmittedEvent =>
        sendEmail(
          RequestSubmittedEmailGenerate(
            firmId = firmId,
            actor = event.actor,
            recipients = requestSubmittedEvent.recipients,
            taskRequestId = requestSubmittedEvent.requestId,
            investmentEntityId = requestSubmittedEvent.investmentEntityId,
            isAllMarkedAsNotApplicable = requestSubmittedEvent.isAllMarkedAsNotApplicable
          )
        )
      case requestCanceledEvent: RequestCanceledEvent =>
        ZIOUtils.when(requestCanceledEvent.isNotify) {
          sendEmail(
            RequestCanceledEmailGenerate(
              firmId = firmId,
              actor = event.actor,
              recipients = requestCanceledEvent.recipients,
              investmentEntityId = requestCanceledEvent.investmentEntityId,
              message = requestCanceledEvent.message
            )
          )
        }
      case requestAddCollaboratorsEvent: RequestAddCollaboratorsEvent =>
        ZIOUtils
          .foreachParN(4)(requestAddCollaboratorsEvent.collaborators) { collaborator =>
            sendEmail(
              RequestSentEmailGenerate(
                firmId = firmId,
                requester = event.actor,
                recipientOpt = Some(collaborator),
                investmentEntityIdOpt = Some(requestAddCollaboratorsEvent.investmentEntityId),
                taskRequestIdOpt = Some(requestAddCollaboratorsEvent.requestId),
                requestSentEmailType = RequestSentEmailGenerate.EmailType.RequestSentToCollaborator,
                userCustomTemplateOpt = None
              )
            )
          }
          .unit
      case requestRemindCollaboratorEvent: RequestRemindCollaboratorEvent =>
        sendEmail(
          RequestSentEmailGenerate(
            firmId = firmId,
            requester = event.actor,
            recipientOpt = Some(requestRemindCollaboratorEvent.collaborator),
            investmentEntityIdOpt = Some(requestRemindCollaboratorEvent.investmentEntityId),
            taskRequestIdOpt = Some(requestRemindCollaboratorEvent.requestId),
            requestSentEmailType = RequestSentEmailGenerate.EmailType.RequestRemindedToCollaborator,
            userCustomTemplateOpt = None
          )
        )
      case requestRemoveCollaboratorEvent: RequestRemoveCollaboratorEvent =>
        ZIOUtils.when(requestRemoveCollaboratorEvent.isNotify) {
          sendEmail(
            RequestRemoveCollaboratorEmailGenerate(
              firmId = firmId,
              actor = event.actor,
              recipient = requestRemoveCollaboratorEvent.collaborator,
              investmentEntityId = requestRemoveCollaboratorEvent.investmentEntityId
            )
          )
        }
      case removeGuestEvent: RemoveGuestEvent =>
        ZIOUtils.when(removeGuestEvent.notifyEmail) {
          sendEmail(
            RemoveGuestEmailGenerate(
              firmId = firmId,
              actor = event.actor,
              removedGuest = removeGuestEvent.userId
            )
          )
        }
      case inviteGuestEvent: InviteGuestEvent =>
        ZIOUtils.traverseOptionUnit(inviteGuestEvent.emailTemplate) { emailTemplate =>
          sendEmail(
            NotifyGuestEmailGenerate(
              firmId = firmId,
              actor = event.actor,
              recipientOpt = Some(inviteGuestEvent.userId),
              destinationPage = inviteGuestEvent.destinationPage,
              userCustomTemplateOpt = Some(emailTemplate.to[FundDataEmailTemplate])
            )
          )
        }
      case notifyGuestEvent: NotifyGuestEvent =>
        ZIOUtils.traverseOptionUnit(notifyGuestEvent.emailTemplate) { emailTemplate =>
          sendEmail(
            NotifyGuestEmailGenerate(
              firmId = firmId,
              actor = event.actor,
              recipientOpt = Some(notifyGuestEvent.userId),
              destinationPage = notifyGuestEvent.destinationPage,
              userCustomTemplateOpt = Some(emailTemplate.to[FundDataEmailTemplate])
            )
          )
        }
      case _ => ZIO.unit
    }
  }

  def sendAutoSyncDowngradeAdmin(
    firmId: FundDataFirmId,
    downgradedAdmin: UserId,
    downgradedFrom: Either[FundSubId, FundDataFirmId]
  ): Task[Unit] = {
    for {
      firmMembers <- fundDataGroupMemberService.getMembers(firmId)
      _ <- ZIO.foreachDiscard(firmMembers.filterNot(_.userId == downgradedAdmin)) { member =>
        sendEmail(
          AutoSyncDowngradeAdminEmailGenerate(
            firmId = firmId,
            downgradedAdmin = downgradedAdmin,
            downgradedFrom = downgradedFrom,
            recipient = member.userId
          )
        )
      }
    } yield ()
  }

  private[funddata] def sendEmail(
    emailGenerate: FundDataEmailGenerate
  ): Task[Unit] = {
    val firmId = emailGenerate.firmId
    val emailType = emailGenerate.emailType
    for {
      shouldSendEmail <- getShouldSendEmail(emailGenerate)
      _ <- ZIO.when(shouldSendEmail)(
        for {
          emailConfigOpt <- FDBRecordDatabase.transact(FundDataEmailStoreOperations.Production)(
            _.getEmailConfig(emailGenerate.firmId)
          )
          emailProviderBuilderConfig <- emailConfigOpt
            .flatMap(_.customSmtpServerConfig)
            .filter(_ => emailConfigOpt.exists(_.isSmtpServerEnabled))
            .fold(
              ZIO.succeed(EmailProviderBuilderConfig.defaultBuilderConfig)
            ) { smtpConfig =>
              for {
                decryptedPassword <- encryptionService
                  .decrypt(Base64.toByteArray(smtpConfig.encryptedPassword))
                  .map(new String(_))
                smtpBuilderConfig = EmailProviderBuilderConfig.DeprecatedSmtpBuilderConfig(
                  from = smtpConfig.from.map(email => EmailAddress(name = Some(email.name), address = email.address)),
                  host = smtpConfig.host,
                  port = smtpConfig.port,
                  username = smtpConfig.userName,
                  password = decryptedPassword,
                  tls = smtpConfig.tls
                )
              } yield smtpBuilderConfig
            }
          _ <- emailSenderService.enqueue(
            emailGenerate,
            firmId,
            emailProviderBuilderConfig = emailProviderBuilderConfig,
            storeEmailConfig = EmailSenderService.StoreEmailConfig(shouldStore = true, parentId = emailGenerate.firmId)
          )
          _ <- ZIO.logInfo(s"Sent email $emailType for $firmId")
        } yield ()
      )
    } yield ()
  }

  private def getShouldSendEmail(
    emailGenerate: FundDataEmailGenerate
  ): Task[Boolean] = {
    val firmId = emailGenerate.firmId
    val emailType = emailGenerate.emailType
    for {
      shouldSendEmailByConfig <- FDBRecordDatabase.transact(FundDataEmailStoreOperations.Production)(
        _.getEmailConfig(emailGenerate.firmId).map(
          _.fold(true)(!_.disabledEmailTypes.contains(emailType))
        )
      )
      _ <- ZIOUtils.unless(shouldSendEmailByConfig)(ZIO.logInfo(s"Skip send email $emailType for $firmId by config"))
    } yield shouldSendEmailByConfig
  }

  override def start(): Task[Unit] = {
    processFundDataEventConsumer.start()
  }

  override def close(): UIO[Unit] = {
    processFundDataEventConsumer.close()
  }

  def updateFirmSmtpConfig(params: UpdateFirmSmtpConfigParams, actor: UserId): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Update SMTP config for firm ${params.firmId}")
      _ <-
        if (params.smtpConfig.edited) {
          val config = params.smtpConfig
          for {
            encryptedPasswordBinary <- encryptionService.encrypt(params.smtpConfig.config.rawPassword.getBytes)
            configModel = CustomSmtpServerConfigModel(
              from = Some(
                CustomEmailAddressModel(
                  name = config.config.from.name,
                  address = config.config.from.address
                )
              ),
              host = config.config.host,
              port = config.config.port,
              encryptedPassword = Base64.toBase64(encryptedPasswordBinary),
              userName = config.config.userName,
              tls = config.config.tls
            )
            _ <- FDBRecordDatabase.transact(FundDataEmailStoreOperations.Production) { ops =>
              ops.updateEmailConfig(params.firmId) { config =>
                config.copy(
                  customSmtpServerConfig = Some(configModel),
                  isSmtpServerEnabled = params.smtpConfig.enabled
                )
              }
            }
          } yield ()
        } else {
          FDBRecordDatabase.transact(FundDataEmailStoreOperations.Production) { ops =>
            ops.updateEmailConfig(params.firmId) { config =>
              config.copy(isSmtpServerEnabled = params.smtpConfig.enabled)
            }
          }
        }
    } yield ()
  }

  def getFirmSmtpConfig(firmId: FundDataFirmId, actor: UserId): Task[FirmSmtpConfigResponse] = {
    for {
      _ <- ZIO.logInfo(s"Get SMTP config for firm $firmId")
      config <- FDBRecordDatabase.transact(FundDataEmailStoreOperations.Production) { ops =>
        ops.getEmailConfig(firmId)
      }
    } yield FirmSmtpConfigResponse(
      isEnabled = config.exists(_.isSmtpServerEnabled),
      smtpConfig = config
        .flatMap(_.customSmtpServerConfig)
        .map { configModel =>
          CustomSmtpServerConfig.from(configModel)
        }
    )
  }

}
