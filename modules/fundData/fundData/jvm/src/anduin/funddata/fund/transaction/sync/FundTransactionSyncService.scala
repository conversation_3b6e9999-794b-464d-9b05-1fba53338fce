// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.funddata.fund.transaction.sync

import squants.market.Money
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.fdb.record.FDBRecordDatabase
import anduin.funddata.bot.FundDataBotUser
import anduin.funddata.endpoint.UpdateProperty
import anduin.funddata.endpoint.fund.transaction.document.CreateFundTransactionDocumentsParams.FundTransactionDocumentInfo
import anduin.funddata.endpoint.fund.transaction.{CreateFundTransactionsParams, UpdateFundTransactionParams}
import anduin.funddata.fund.FundDataFundService
import anduin.funddata.fund.fundlegalentity.{FundLegalEntityOperations, FundLegalEntityService}
import anduin.funddata.fund.subscription.FundDataFundSubscriptionStoreOperations
import anduin.funddata.fund.transaction.document.FundTransactionDocumentService
import anduin.funddata.fund.transaction.sync.FundTransactionSyncService.{
  SyncFundTransactionOperation,
  SyncFundTransactionOperationCreate,
  SyncFundTransactionOperationDelete,
  SyncFundTransactionOperationUpdate
}
import anduin.funddata.fund.transaction.{FundTransactionModel, FundTransactionService, FundTransactionStoreOperations}
import anduin.funddata.investmententity.document.FundDataInvestmentEntityDocumentService
import anduin.fundsub.utils.FundSubLpUtils
import anduin.greylin.core.cdc.TypedEvent
import anduin.greylin.core.event.GreylinEventListener
import anduin.greylin.{GreylinDataService, modelti}
import anduin.greylin.modelti.fundsub.SubscriptionOrderSubFundInvestment
import anduin.greylin.modelti.{SubscriptionOrder, SubscriptionOrderStatus}
import anduin.greylin.operation.fundsub.{FundSubscriptionSubFundOperations, SubscriptionOrderSubFundInvestmentOperations}
import anduin.greylin.operation.{
  SubscriptionOrderOperations,
  SubscriptionOrderSubscriptionDocumentOperations,
  SubscriptionOrderSupportingDocumentOperations,
  SubscriptionOrderSupportingFormFileOperations,
  SubscriptionOrderSupportingFormOperations
}
import anduin.id.funddata.fund.FundLegalEntityId
import anduin.id.fundsub.{FundSubLpId, InvestmentFundId}
import anduin.kafka.KafkaService
import anduin.model.id.FileId
import anduin.portaluser.ExecutiveAdmin
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FundTransactionSyncService(
  fundDataFundService: FundDataFundService,
  greylinDataService: GreylinDataService,
  fundDataInvestmentEntityDocumentService: FundDataInvestmentEntityDocumentService,
  fundTransactionService: FundTransactionService,
  fundTransactionDocumentService: FundTransactionDocumentService,
  executiveAdmin: ExecutiveAdmin,
  fundDataBotUser: FundDataBotUser,
  userProfileService: UserProfileService,
  kafkaService: KafkaService
) extends GreylinEventListener {

  override val groupName: String = "fund-data-transaction-sync-service-consumer"

  override val inputs: List[INPUT] = List(
    registerInput(modelti.SubscriptionOrder -> handleSubscriptionOrderEvent),
    registerInput(modelti.SubscriptionOrderSupportingDocument -> handleSubscriptionOrderSupportingDocumentEvent)
  )

  private def detectSupportingDocumentType(
    supportingDoc: modelti.SubscriptionOrderSupportingDocument,
    supportingDocumentTypes: List[anduin.funddata.investmententity.document.FundDataInvestmentEntityDocumentService.DocumentType]
  ): Task[FundTransactionDocumentInfo] = {
    fundDataInvestmentEntityDocumentService
      .autoDetectDocumentTypes(
        documentTypes = supportingDocumentTypes,
        fileIdOpt = Some(supportingDoc.fileId),
        otherTexts = Seq(supportingDoc.docType)
      )
      .map(_.headOption)
      .map { tagItemOpt =>
        FundTransactionDocumentInfo(
          fileId = supportingDoc.fileId,
          documentTypeOpt = tagItemOpt.map(_.tagItemId)
        )
      }
  }

  private def processAndAddSupportingDocument(
    supportingDoc: modelti.SubscriptionOrderSupportingDocument,
    transaction: FundTransactionModel,
    supportingDocumentTypes: List[anduin.funddata.investmententity.document.FundDataInvestmentEntityDocumentService.DocumentType],
    actor: anduin.id.user.UserId
  ): Task[Unit] = {
    for {
      documentInfo <- detectSupportingDocumentType(supportingDoc, supportingDocumentTypes)
      _ <- fundTransactionDocumentService.addOrReplaceFundTransactionDocument(
        transactionId = transaction.id,
        toAddDocument = documentInfo.fileId,
        documentTypeOpt = documentInfo.documentTypeOpt,
        actor = actor
      )
    } yield ()
  }

  private def handleSubscriptionOrderSupportingDocumentEvent(
    event: TypedEvent[modelti.SubscriptionOrderSupportingDocument]
  ): Task[Unit] = {
    event match {
      case TypedEvent.InsertEvent(data) =>
        for {
          _ <- ZIO.logInfo(s"Handling subscription order supporting document event $data")

          actor <- executiveAdmin.userId
          linkedOrderTransactions <- FDBRecordDatabase.transact(FundTransactionStoreOperations.Production)(
            _.getByLinkedFundSubOrder(data.subscriptionOrderId)
          )
          _ <- ZIO.foreach(linkedOrderTransactions) { transaction =>
            for {
              supportingDocumentTypes <- fundDataInvestmentEntityDocumentService.getNonSubscriptionDocumentTypes(
                transaction.id.parent.parent
              )
              _ <- processAndAddSupportingDocument(data, transaction, supportingDocumentTypes, actor)
            } yield ()
          }
        } yield ()

      case _ => ZIO.unit
    }
  }

  private def handleSubscriptionOrderEvent(event: TypedEvent[SubscriptionOrder]): Task[Unit] = {
    event match {
      case TypedEvent.InsertEvent(data) => syncOrderToTransactionsUnsafe(data)
      case TypedEvent.UpdateEvent(oldData, newData) =>
        for {
          _ <- ZIO.logInfo(s"Handling fund sub order update event ${newData.id}")
          _ <-
            if (
              newData.status == SubscriptionOrderStatus.Removed &&
              oldData.status != newData.status
            ) {
              for {
                linkedOrderTransactions <- FDBRecordDatabase.transact(FundTransactionStoreOperations.Production)(
                  _.getByLinkedFundSubOrder(newData.id)
                )
                _ <- ZIO.foreach(linkedOrderTransactions.map(_.id))(
                  fundTransactionService.markTransactionImportedFromFundSubAsDeletedUnsafe
                )
              } yield ()
            } else {
              syncOrderToTransactionsUnsafe(newData)
            }
        } yield ()
      case TypedEvent.DeleteEvent(_) => ZIO.unit
    }
  }

  def syncInvestmentFundToTransactionsUnsafe(
    investmentFundId: InvestmentFundId,
    fundLegalEntityId: FundLegalEntityId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Sync orders of investment fund $investmentFundId to fund legal entity $fundLegalEntityId")

      _ <- FundLegalEntityService.validateFundLegalEntityLinkedWithInvestmentFund(fundLegalEntityId, investmentFundId)

      orders <- greylinDataService
        .run(SubscriptionOrderOperations.getByFundId(investmentFundId.parent))

      ordersToSync <- ZIO
        .foreach(orders) { order =>
          FundTransactionSyncService.checkIfOrderCanBeSynced(order).map(Option.when(_)(order))
        }
        .map(_.flatten)

      _ <- syncOrdersToTransactionsUnsafe(
        fundLegalEntityId = fundLegalEntityId,
        investmentFundId = investmentFundId,
        orders = ordersToSync
      )

    } yield ()
  }

  private def updateOrderRelatedTransactions(
    order: SubscriptionOrder
  ): Task[Unit] = {
    for {
      linkedTransactions <- FDBRecordDatabase.transact(FundTransactionStoreOperations.Production)(
        _.getByLinkedFundSubOrder(order.id)
      )

      linkedFundLegalEntities <- FDBRecordDatabase.transact(FundLegalEntityOperations.Production)(
        _.gets(linkedTransactions.map(_.id.parent).distinct)
      )

      _ <- ZIO.foreach(linkedFundLegalEntities) { linkedFundLegalEntity =>
        ZIO.foreach(linkedFundLegalEntity.connectedInvestmentFundIds) { investmentFundId =>
          syncOrdersToTransactionsUnsafe(
            fundLegalEntityId = linkedFundLegalEntity.id,
            investmentFundId = investmentFundId,
            orders = List(order)
          )
        }
      }
    } yield ()
  }

  def syncOrderToTransactionsUnsafe(
    order: SubscriptionOrder
  ): Task[Unit] = {
    for {
      canSync <- FundTransactionSyncService.checkIfOrderCanBeSynced(order)
      _ <- ZIO.when(canSync) {
        for {
          _ <- ZIO.logInfo(s"Sync order $order to transactions")

          _ <- updateOrderRelatedTransactions(order)

          orderInvestmentFunds <- greylinDataService
            .run(
              SubscriptionOrderSubFundInvestmentOperations.Default.getBySubscriptionOrder(order.id)
            )
            .map(_.filter(_.commitmentAmount.nonEmpty))
          _ <- ZIO.foreach(orderInvestmentFunds) { orderInvestmentFund =>
            for {
              linkedFundLegalEntities <- FDBRecordDatabase.transact(FundLegalEntityOperations.Production)(
                _.getByConnectedInvestmentFund(orderInvestmentFund.subFundId)
              )
              _ <- ZIO.foreach(linkedFundLegalEntities) { fundLegalEntity =>
                syncOrdersToTransactionsUnsafe(
                  fundLegalEntityId = fundLegalEntity.id,
                  investmentFundId = orderInvestmentFund.subFundId,
                  orders = List(order)
                )
              }
            } yield ()
          }
        } yield ()
      }
    } yield ()
  }

  def syncOrdersToTransactionsUnsafe(
    fundLegalEntityId: FundLegalEntityId,
    investmentFundId: InvestmentFundId,
    orders: List[SubscriptionOrder]
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Sync orders $orders to fund legal entity $fundLegalEntityId")

      _ <- syncOrderInvestmentsToTransactionsUnsafe(
        fundLegalEntityId = fundLegalEntityId,
        investmentFundId = investmentFundId,
        orders = orders
      )
      _ <- syncOrderDocumentsToTransactionsUnsafe(
        fundLegalEntityId = fundLegalEntityId,
        investmentFundId = investmentFundId,
        orders = orders
      )

    } yield ()
  }

  private def syncOrderInvestmentsToTransactionsUnsafe(
    fundLegalEntityId: FundLegalEntityId,
    investmentFundId: InvestmentFundId,
    orders: List[SubscriptionOrder]
  ): Task[Unit] = {
    for {
      // TODO: Need to make sure subfund update is existed in database before this run
      investmentFundModelOpt <- greylinDataService
        .run(FundSubscriptionSubFundOperations.Default.get(List(investmentFundId)))
        .map(_.headOption)

      investmentFundModel <- ZIOUtils.optionToTask(
        investmentFundModelOpt,
        new RuntimeException(s"Investment fund $investmentFundId not found")
      )

      ordersToSync <- ZIO
        .foreach(orders) { order =>
          FundTransactionSyncService.checkIfOrderCanBeSynced(order).map(Option.when(_)(order))
        }
        .map(_.flatten)

      _ <- ZIO.when(ordersToSync.nonEmpty) {
        for {
          _ <- ZIO.logInfo(
            s"Sync orders ${orders.map(_.id)} of investment fund $investmentFundId to fund legal entity $fundLegalEntityId"
          )

          _ <- FundSubLpUtils.validateLpListNonEmptyAndBelongToTheSameFund(ordersToSync.map(_.id))

          _ <- FundLegalEntityService.validateFundLegalEntityLinkedWithInvestmentFund(
            fundLegalEntityId,
            investmentFundId
          )

          orderInvestmentEntityMap <- ZIOUtils
            .foreachParN(4)(ordersToSync) { order =>
              FDBRecordDatabase.transact(FundDataFundSubscriptionStoreOperations.Production) { ops =>
                for {
                  subscriptions <- ops.getSubscriptions(fundLegalEntityId.parent, order.id)
                  orderToInvestmentEntityMap = subscriptions.flatMap { sub =>
                    sub.linkedFundSubOrderId.zip(sub.linkedInvestmentEntityId)
                  }
                } yield orderToInvestmentEntityMap
              }
            }
            .map(_.flatten.toMap)

          syncFundTransactionOperations <- ZIO
            .foreach(ordersToSync) { order =>
              for {
                existingTransactionOfOrderOpt <- FDBRecordDatabase
                  .transact(FundTransactionStoreOperations.Production)(
                    _.getByOrderAndInvestmentFund(order.id, investmentFundId)
                  )

                actualInvestmentFundOfOrderOpt <- greylinDataService
                  .run(
                    SubscriptionOrderSubFundInvestmentOperations.Default
                      .getBySubFund(investmentFundId, List(order.id))
                  )
                  .map(_.find(_.commitmentAmount.nonEmpty))

                syncFundTransactionOperationOpt: Option[SyncFundTransactionOperation] =
                  if (existingTransactionOfOrderOpt.nonEmpty && actualInvestmentFundOfOrderOpt.nonEmpty) {
                    existingTransactionOfOrderOpt.zip(actualInvestmentFundOfOrderOpt).map {
                      case (transaction, investmentFund) =>
                        SyncFundTransactionOperationUpdate(
                          order = order,
                          oldTransaction = transaction,
                          newInvestmentFund = investmentFund
                        )
                    }
                  } else if (existingTransactionOfOrderOpt.isEmpty && actualInvestmentFundOfOrderOpt.nonEmpty) {
                    actualInvestmentFundOfOrderOpt.map(SyncFundTransactionOperationCreate(order, _))
                  } else if (existingTransactionOfOrderOpt.nonEmpty && actualInvestmentFundOfOrderOpt.isEmpty) {
                    existingTransactionOfOrderOpt.map(SyncFundTransactionOperationDelete(order, _))
                  } else {
                    None
                  }

              } yield syncFundTransactionOperationOpt
            }
            .map(_.flatten)

          toCreateTransactions = syncFundTransactionOperations.collect {
            case SyncFundTransactionOperationCreate(order, newInvestmentFund) => (order, newInvestmentFund)
          }
          toUpdateTransactions = syncFundTransactionOperations.collect {
            case SyncFundTransactionOperationUpdate(order, oldTransaction, newInvestmentFund) =>
              (order, oldTransaction, newInvestmentFund)
          }
          toDeleteTransactions = syncFundTransactionOperations.collect {
            case SyncFundTransactionOperationDelete(order, toDeleteTransactions) => (order, toDeleteTransactions)
          }

          userInfoMap <- userProfileService.batchGetUserInfos(
            (toCreateTransactions.map(_._1.mainUserId) ++ toUpdateTransactions.map(_._1.mainUserId)).toSet
          )

          transactionNameMap = (toCreateTransactions.map(_._1) ++ toUpdateTransactions.map(_._1)).flatMap { order =>
            if (order.investmentEntityName.trim.isEmpty) {
              userInfoMap.get(order.mainUserId).map(userInfo => order.id -> userInfo.fullNameString)
            } else {
              Some(order.id -> order.investmentEntityName)
            }
          }.toMap

          actor <- fundDataBotUser.userId

          // create transactions
          _ <- ZIO.foreach(toCreateTransactions) { case (order, investmentFund) =>
            fundTransactionService.createSingleFundTransactionUnsafe(
              fundLegalEntityId = fundLegalEntityId,
              info = CreateFundTransactionsParams.FundTransactionInfo(
                name = transactionNameMap.getOrElse(order.id, ""),
                linkedInvestmentEntityOpt = orderInvestmentEntityMap.get(order.id),
                estimatedAmountOpt = investmentFund.expectedCommitmentAmount.map(Money(_, investmentFundModel.currency)),
                acceptedAmountOpt = investmentFund.acceptedCommitmentAmount.map(Money(_, investmentFundModel.currency)),
                capitalAmountOpt = investmentFund.commitmentAmount.map(Money(_, investmentFundModel.currency)),
                documents = List.empty,
                source = CreateFundTransactionsParams.FundTransactionSource.ImportFromOrder(
                  orderId = order.id,
                  investmentFundId = investmentFund.subFundId,
                  isDeleted = false
                )
              ),
              actor = actor
            )
          }

          // update transactions
          _ <- ZIO.foreach(toUpdateTransactions) { case (order, oldTransaction, newInvestmentFund) =>
            fundTransactionService.updateFundTransactionUnsafe(
              params = UpdateFundTransactionParams(
                fundTransactionId = oldTransaction.id,
                nameChange = UpdateProperty(Option(transactionNameMap.getOrElse(order.id, ""))),
                expectedAmountsChange = UpdateProperty(
                  newInvestmentFund.expectedCommitmentAmount.map { amount =>
                    List(Money(amount, investmentFundModel.currency))
                  }
                ),
                acceptedAmountsChange = UpdateProperty(
                  newInvestmentFund.acceptedCommitmentAmount.map { amount =>
                    List(Money(amount, investmentFundModel.currency))
                  }
                ),
                capitalAmountsChange = UpdateProperty(
                  newInvestmentFund.commitmentAmount.map { amount =>
                    List(Money(amount, investmentFundModel.currency))
                  }
                )
              )
            )
          }

          // delete transactions
          _ <- ZIO.foreach(toDeleteTransactions.map(_._2.id))(fundTransactionService.deleteTransactionUnsafe)

        } yield ()

      }
    } yield ()
  }

  private def syncOrderDocumentsToTransactionsUnsafe(
    fundLegalEntityId: FundLegalEntityId,
    investmentFundId: InvestmentFundId,
    orders: List[SubscriptionOrder]
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Sync documents of order ${orders.map(_.id)} to fund legal entity $fundLegalEntityId")

      ordersToSync <- ZIO
        .foreach(orders) { order =>
          FundTransactionSyncService.checkIfOrderCanBeSynced(order).map(Option.when(_)(order))
        }
        .map(_.flatten)

      _ <- ZIO.when(ordersToSync.nonEmpty) {
        for {
          subscriptionDocumentTypeOpt <- fundDataInvestmentEntityDocumentService.getSubscriptionDocumentTypeOpt(
            fundLegalEntityId.parent
          )
          supportingDocumentTypes <- fundDataInvestmentEntityDocumentService.getNonSubscriptionDocumentTypes(
            fundLegalEntityId.parent
          )

          orderSubscriptionDocumentWithMatchedDocumentTypeMap <- subscriptionDocumentTypeOpt.fold(
            ZIO.succeed(Map.empty[FundSubLpId, List[FundTransactionDocumentInfo]])
          ) { subscriptionDocumentType =>
            greylinDataService
              .run(SubscriptionOrderSubscriptionDocumentOperations.getByOrderIds(ordersToSync.map(_.id)))
              .map(
                _.groupBy(_.subscriptionOrderId).view
                  .mapValues(
                    _.map(_.fileId).map(FundTransactionDocumentInfo(_, Option(subscriptionDocumentType.tagItemId)))
                  )
                  .toMap
              )
          }

          orderSupportingDocMap <- greylinDataService
            .run(SubscriptionOrderSupportingDocumentOperations.getByOrderIds(ordersToSync.map(_.id)))
            .map(_.groupBy(_.subscriptionOrderId))

          orderSupportingDocWithMatchedDocumentType <- ZIO.foreach(orderSupportingDocMap) {
            case (orderId, supportingDocs) =>
              ZIO
                .foreach(supportingDocs) { supportingDoc =>
                  detectSupportingDocumentType(supportingDoc, supportingDocumentTypes)
                }
                .map(orderId -> _)
          }

          orderSupportingFormMap <- greylinDataService
            .run(SubscriptionOrderSupportingFormOperations.gets(ordersToSync.map(_.id)))
            .map(_.groupBy(_.subscriptionOrderId))

          orderFormSubmissionMap <- greylinDataService
            .run(
              SubscriptionOrderSupportingFormFileOperations.gets(
                orderSupportingFormMap.values.flatten.toList.map(_.formSubmissionId)
              )
            )
            .map(_.groupMap(_.formSubmissionId)(_.fileId))

          orderSupportingFormWithMatchedDocumentType <- ZIO.foreach(orderSupportingFormMap) {
            case (orderId, supportingForms) =>
              ZIO
                .foreach(supportingForms) { supportingForm =>
                  fundDataInvestmentEntityDocumentService
                    .autoDetectDocumentTypes(
                      documentTypes = supportingDocumentTypes,
                      fileIdOpt = None,
                      otherTexts = Seq(supportingForm.docType)
                    )
                    .map(_.headOption)
                    .map { tagItemOpt =>
                      orderFormSubmissionMap
                        .getOrElse(supportingForm.formSubmissionId, List[FileId]())
                        .map { fileId =>
                          FundTransactionDocumentInfo(
                            fileId = fileId,
                            documentTypeOpt = tagItemOpt.map(_.tagItemId)
                          )
                        }
                    }
                }
                .map(orderId -> _.flatten)
          }

          actor <- executiveAdmin.userId

          orderDocumentMap = {
            val allOrderIds = orderSubscriptionDocumentWithMatchedDocumentTypeMap.keySet ++
              orderSupportingDocWithMatchedDocumentType.keySet ++
              orderSupportingFormWithMatchedDocumentType.keySet

            allOrderIds.map { orderId =>
              val subscriptionDocs = orderSubscriptionDocumentWithMatchedDocumentTypeMap.getOrElse(orderId, List.empty)
              val supportingDocs = orderSupportingDocWithMatchedDocumentType.getOrElse(orderId, List.empty)
              val formDocs = orderSupportingFormWithMatchedDocumentType.getOrElse(orderId, List.empty)

              orderId -> (subscriptionDocs ++ supportingDocs ++ formDocs)
            }.toMap
          }

          _ = println(s"allOrderIds: $orderDocumentMap")

          _ <- ZIO.foreach(ordersToSync) { order =>
            for {
              transactionsImportedFromOrder <- FDBRecordDatabase.transact(FundTransactionStoreOperations.Production)(
                _.getByOrderAndInvestmentFund(order.id, investmentFundId)
              )
              _ <- ZIO.foreach(transactionsImportedFromOrder) { transaction =>
                ZIO.foreach(orderDocumentMap.get(order.id).toSeq.flatten) { document =>
                  fundTransactionDocumentService.addOrReplaceFundTransactionDocument(
                    transactionId = transaction.id,
                    toAddDocument = document.fileId,
                    documentTypeOpt = document.documentTypeOpt,
                    actor = actor
                  )
                }
              }
            } yield ()
          }
        } yield ()
      }
    } yield ()
  }

}

object FundTransactionSyncService {

  sealed trait SyncFundTransactionOperation

  final case class SyncFundTransactionOperationCreate(
    order: SubscriptionOrder,
    newInvestmentFund: SubscriptionOrderSubFundInvestment
  ) extends SyncFundTransactionOperation

  final case class SyncFundTransactionOperationUpdate(
    order: SubscriptionOrder,
    oldTransaction: FundTransactionModel,
    newInvestmentFund: SubscriptionOrderSubFundInvestment
  ) extends SyncFundTransactionOperation

  final case class SyncFundTransactionOperationDelete(
    order: SubscriptionOrder,
    toDeleteTransactions: FundTransactionModel
  ) extends SyncFundTransactionOperation

  def checkIfOrderCanBeSynced(
    order: SubscriptionOrder
  ): Task[Boolean] = {
    for {
      canSync <- ZIO.succeed(order.status >= SubscriptionOrderStatus.Submitted)
    } yield canSync
  }

}
