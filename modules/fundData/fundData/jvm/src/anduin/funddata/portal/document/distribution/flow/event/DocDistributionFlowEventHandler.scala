//  Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.portal.document.distribution.flow.event

import io.github.arainko.ducktape.*

import anduin.funddata.portal.document.distribution.DocDistributionDocument
import anduin.funddata.portal.document.distribution.flow.activity.DocDistributionFlowActivity
import anduin.funddata.portal.document.distribution.flow.error.DocDistributionFlowError
import anduin.funddata.portal.document.distribution.flow.event.DocDistributionFlowEventHandler.*
import anduin.funddata.portal.document.distribution.flow.protocols.*
import anduin.funddata.portal.document.distribution.flow.state.*
import anduin.funddata.portal.document.distribution.flow.workflow.StartDocDistributionWorkflow
import anduin.funddata.portal.document.distribution.flow.{DocDistributionFlowSpec, DocDistributionFlowUtils}
import anduin.funddata.portal.file.InvestorPortalFileUtils
import anduin.funddata.portal.flow.InvestorPortalFlowChildWorkflow
import anduin.funddata.portal.flow.event.CreateStagingSubFolderEvent
import anduin.id.flow.TFlowId
import anduin.id.investorportal.InvestorPortalId
import anduin.model.common.user.UserId
import anduin.model.id.{FolderId, TFlowEventIdFactory}
import anduin.workflow.TemporalWorkflowService.{newActivityStub, newChildWorkflowStub}
import anduin.workflow.flow.FlowEffect
import anduin.workflow.flow.event.TemporalFlowEventHandler

trait DocDistributionFlowEventHandler
    extends TemporalFlowEventHandler[DocDistributionFlowSpec]
    with DocDistributionFlowSpec {

  private given InitialTransition = initialTransition { case e: CreateDocDistributionEvent =>
    EventHandlerResult.init {
      DocDistributionCreatedState(
        workflowId = e.eventId.parent,
        name = e.name,
        method = e.method,
        draftProgress = DocDistributionDraftProgress.Created,
        creator = e.actor,
        createdAt = e.timestamp,
        lastUpdatedAt = e.timestamp
      )
    } { createdState => createDocDistribution(createdState) }
  }

  private given Transition[DocDistributionCreatedState] = transition { createdState =>
    {
      case e: CreateDocDistributionFolderEvent =>
        val investorPortalId = DocDistributionFlowUtils.getInvestorPortalId(createdState.workflowId)
        val folderId = InvestorPortalFileUtils
          .stagingFolderId(investorPortalId)
          .addSuffix(e.folderSuffix)
        EventHandlerResult.transit {
          createdState.copy(
            stagingFolderId = folderId,
            lastUpdatedAt = e.timestamp
          )
        } { (_, updatedState) =>
          createUploadFolder(updatedState, investorPortalId, folderId, e.actor)
        }
      case e: UpdateDocDistributionEvent =>
        EventHandlerResult.transit {
          createdState.copy(
            draftProgress = getUpdatedField(e.updateProgress, createdState.draftProgress, e.newProgress),
            documents = getUpdatedField(e.updateDocuments, createdState.documents, e.newDocuments),
            target = getUpdatedField(e.updateTarget, createdState.target, e.newTarget),
            emailTemplate = getUpdatedField(e.updateEmailTemplate, createdState.emailTemplate, e.newEmailTemplate),
            settings = getUpdatedField(e.updateSettings, createdState.settings, e.newSettings),
            lastUpdatedAt = e.timestamp,
            documentSplitDataOpt = getUpdatedField(
              e.updateDocumentSplitData,
              createdState.documentSplitDataOpt,
              e.newDocumentSplitDataOpt
            )
          )
        } { (_, updatedState) =>
          updateDocDistribution(updatedState, e.actor)
        }
      case e: StartDocDistributionEvent =>
        EventHandlerResult.transit {
          createdState
            .into[DocDistributionDistributingState]
            .transform(
              Field.const(_.lastUpdatedAt, e.timestamp)
            )
        } { (_, distributingState) =>
          startDocDistribution(distributingState, e.actor)
        }
      case e: ScheduleDocDistributionEvent =>
        EventHandlerResult.transit {
          createdState
            .into[DocDistributionScheduledState]
            .transform(
              Field.const(_.lastUpdatedAt, e.timestamp)
            )
        } { (_, scheduledState) =>
          scheduleDocDistribution(scheduledState, e.actor)
        }
      case e: DeleteDocDistributionEvent =>
        EventHandlerResult.transit(createdState) { (_, _) =>
          deleteDocDistribution(
            createdState.workflowId,
            createdState.stagingFolderId,
            createdState.documents,
            e.actor
          )
        }
      case e: RenameDocDistributionEvent =>
        EventHandlerResult.transit(createdState) { (_, _) =>
          renameDocDistribution(
            createdState.workflowId,
            e.newName,
            e.actor
          )
        }
    }
  }

  private given Transition[DocDistributionScheduledState] = transition { scheduledState =>
    {
      case e: StartDocDistributionEvent =>
        EventHandlerResult.transit {
          scheduledState
            .into[DocDistributionDistributingState]
            .transform(
              Field.const(_.lastUpdatedAt, e.timestamp)
            )
        } { (_, distributingState) =>
          startDocDistribution(distributingState, e.actor)
        }
      case e: RescheduleDocDistributionEvent =>
        EventHandlerResult.transit {
          scheduledState.copy(
            settings = scheduledState.settings.map(_.copy(scheduledAt = Some(e.scheduleAt))),
            lastUpdatedAt = e.timestamp
          )
        } { (_, updatedState) =>
          scheduleDocDistribution(updatedState, e.actor)
        }
      case e: UnscheduleDocDistributionEvent =>
        EventHandlerResult.transit {
          scheduledState
            .into[DocDistributionCreatedState]
            .transform(
              Field.const(_.lastUpdatedAt, e.timestamp),
              Field.const(_.draftProgress, DocDistributionDraftProgress.ReviewedSettings)
            )
        } { (_, createdState) =>
          unscheduleDocDistribution(createdState, e.actor)
        }
      case e: DeleteDocDistributionEvent =>
        EventHandlerResult.transit {
          scheduledState
            .into[DocDistributionCreatedState]
            .transform(
              Field.const(_.lastUpdatedAt, e.timestamp),
              Field.const(_.draftProgress, DocDistributionDraftProgress.ReviewedSettings)
            )
        } { (_, createdState) =>
          deleteScheduledDocDistribution(createdState, e.actor)
        }
      case e: RenameDocDistributionEvent =>
        EventHandlerResult.transit(scheduledState) { (_, _) =>
          renameDocDistribution(
            scheduledState.workflowId,
            e.newName,
            e.actor
          )
        }
    }
  }

  private given Transition[DocDistributionDistributingState] = transition { distributingState =>
    {
      case e: CompleteDocDistributionEvent =>
        EventHandlerResult.transit {
          distributingState
            .into[DocDistributionCompletedState]
            .transform(
              Field.const(_.lastUpdatedAt, e.timestamp),
              Field.const(_.completedAt, e.timestamp)
            )
        } { (_, completedState) =>
          completeDocDistribution(completedState, e.actor)
        }
      case e: FailDocDistributionEvent =>
        EventHandlerResult.transit(
          distributingState
            .into[DocDistributionFailedState]
            .transform(
              Field.const(_.lastUpdatedAt, e.timestamp),
              Field.const(_.errorMessage, e.errorMessage)
            )
        ) { (_, failedState) =>
          failDocDistribution(failedState, e.actor)
        }
    }
  }

  private given Transition[DocDistributionCompletedState] = transition { completedState =>
    { case e: RenameDocDistributionEvent =>
      EventHandlerResult.transit(completedState) { (_, _) =>
        renameDocDistribution(
          completedState.workflowId,
          e.newName,
          e.actor
        )
      }
    }
  }

  private given Transition[DocDistributionFailedState] = transition { failedState =>
    {
      case e: ResolveDocDistributionEvent =>
        EventHandlerResult.transit(
          failedState
            .into[DocDistributionCreatedState]
            .transform(
              Field.const(_.lastUpdatedAt, e.timestamp),
              Field.const(_.draftProgress, DocDistributionDraftProgress.ReviewedSettings)
            )
        ) { (_, createdState) =>
          resolveDocDistribution(createdState, e.actor)
        }
      case e: DeleteDocDistributionEvent =>
        EventHandlerResult.transit(failedState) { (_, _) =>
          deleteDocDistribution(
            failedState.workflowId,
            failedState.stagingFolderId,
            failedState.documents,
            e.actor
          )
        }
    }
  }

  override val eventHandler: EventHandler = EventHandler.derived

}

object DocDistributionFlowEventHandler {

  private def getUpdatedField[A](shouldUpdate: Boolean, oldField: A, newField: A) = {
    if (shouldUpdate) newField else oldField
  }

  private def createDocDistribution(createdState: DocDistributionCreatedState)
    : FlowEffect[Either[DocDistributionFlowError, EmptyEventResult]] = {
    val effect = for {
      _ <- FlowEffect.attempt(
        scribe.info(s"User ${createdState.creator} is creating doc distribution ${createdState.name}")
      )
      flowActivity = newActivityStub[DocDistributionFlowActivity]
      _ <- FlowEffect.executeActivityEither(flowActivity.createDocDistribution(createdState))
    } yield EmptyEventResult()
    effect.eitherSome[DocDistributionFlowError]
  }

  private def createUploadFolder(
    updatedState: DocDistributionCreatedState,
    investorPortalId: InvestorPortalId,
    folderId: FolderId,
    actor: UserId
  ): FlowEffect[Either[DocDistributionFlowError, CreateDocDistributionFolderResult]] = {
    val effect = for {
      _ <- FlowEffect.attempt(
        scribe.info(s"User $actor is creating upload folder for doc distribution")
      )
      eventId <- FlowEffect.sideEffect(() =>
        TFlowEventIdFactory.unsafeRandomId(TFlowId.investorPortalFlowId(investorPortalId))
      )
      _ <- InvestorPortalFlowChildWorkflow
        .executeEventEffect(
          CreateStagingSubFolderEvent(
            eventId = eventId,
            folderSuffix = folderId.suffix,
            folderName = updatedState.name,
            actor = actor
          )
        )
        .flatMap(FlowEffect.fromEither)
      flowActivity = newActivityStub[DocDistributionFlowActivity]
      _ <- FlowEffect.executeActivity(flowActivity.updateDocDistribution(updatedState))
    } yield CreateDocDistributionFolderResult(folderId)
    effect.eitherSome[DocDistributionFlowError]
  }

  private def updateDocDistribution(updatedState: DocDistributionCreatedState, actor: UserId)
    : FlowEffect[Either[DocDistributionFlowError, EmptyEventResult]] = {
    val effect = for {
      _ <- FlowEffect.attempt(
        scribe.info(s"User ${actor.idString} is updating doc distribution")
      )
      flowActivity = newActivityStub[DocDistributionFlowActivity]
      _ <- FlowEffect.executeActivityEither(flowActivity.updateDocDistribution(updatedState))
    } yield EmptyEventResult()
    effect.eitherSome[DocDistributionFlowError]
  }

  private def startDocDistribution(
    distributionState: DocDistributionDistributingState,
    actor: UserId
  ): FlowEffect[Either[DocDistributionFlowError, EmptyEventResult]] = {
    for {
      _ <- FlowEffect.attempt(
        scribe.info(s"User ${actor.idString} is starting doc distribution")
      )
      childWorkflow = newChildWorkflowStub[StartDocDistributionWorkflow]
      res <- FlowEffect
        .executeChildWorkflow(
          childWorkflow.run(
            StartDocDistributionInput(
              state = distributionState,
              actor = actor
            )
          )
        )
        .map(_.map(_ => EmptyEventResult()))
    } yield res
  }

  private def scheduleDocDistribution(scheduledState: DocDistributionScheduledState, actor: UserId)
    : FlowEffect[Either[DocDistributionFlowError, EmptyEventResult]] = {
    val flowActivity = newActivityStub[DocDistributionFlowActivity]
    FlowEffect.executeActivity(flowActivity.scheduleDocDistribution(ScheduleDocDistributionInput(scheduledState, actor)))
  }

  private def unscheduleDocDistribution(createdState: DocDistributionCreatedState, actor: UserId)
    : FlowEffect[Either[DocDistributionFlowError, EmptyEventResult]] = {
    val flowActivity = newActivityStub[DocDistributionFlowActivity]
    FlowEffect.executeActivity(flowActivity.unscheduleDocDistribution(UnscheduleDocDistributionInput(createdState, actor)))
  }

  private def deleteScheduledDocDistribution(createdState: DocDistributionCreatedState, actor: UserId)
    : FlowEffect[Either[DocDistributionFlowError, EmptyEventResult]] = {
    val effect = for {
      _ <- unscheduleDocDistribution(createdState, actor).flatMap(FlowEffect.fromEither)
      _ <- deleteDocDistribution(
        createdState.workflowId,
        createdState.stagingFolderId,
        createdState.documents,
        actor
      ).flatMap(FlowEffect.fromEither)
    } yield EmptyEventResult()
    effect.eitherSome[DocDistributionFlowError]
  }

  private def renameDocDistribution(
    workflowId: TFlowId,
    newName: String,
    actor: UserId
  ): FlowEffect[Either[DocDistributionFlowError, EmptyEventResult]] = {
    val effect = for {
      _ <- FlowEffect.attempt(
        scribe.info(s"User ${actor.idString} is renaming doc distribution $workflowId")
      )
      flowActivity = newActivityStub[DocDistributionFlowActivity]
      _ <- FlowEffect.executeActivityEither(
        flowActivity.renameDocDistribution(
          RenameDocDistributionInput(
            workflowId,
            newName,
            actor
          )
        )
      )
    } yield EmptyEventResult()
    effect.eitherSome[DocDistributionFlowError]
  }

  private def deleteDocDistribution(
    workflowId: TFlowId,
    stagingFolderId: FolderId,
    documents: Seq[DocDistributionDocument],
    actor: UserId
  ): FlowEffect[Either[DocDistributionFlowError, EmptyEventResult]] = {
    val effect = for {
      _ <- FlowEffect.attempt(
        scribe.info(s"User ${actor.idString} is deleting doc distribution ${workflowId}")
      )
      flowActivity = newActivityStub[DocDistributionFlowActivity]
      _ <- FlowEffect.executeActivityEither(
        flowActivity.deleteDocDistribution(
          DeleteDocDistributionInput(
            workflowId,
            stagingFolderId,
            documents,
            actor
          )
        )
      )
    } yield EmptyEventResult()
    effect.eitherSome[DocDistributionFlowError]
  }

  private def completeDocDistribution(
    completedState: DocDistributionCompletedState,
    actor: UserId
  ): FlowEffect[Either[DocDistributionFlowError, EmptyEventResult]] = {
    val effect = for {
      _ <- FlowEffect.attempt(
        scribe.info(s"User ${actor.idString} is completing doc distribution")
      )
      flowActivity = newActivityStub[DocDistributionFlowActivity]
      _ <- FlowEffect.executeActivityEither(
        flowActivity.completeDocDistribution(CompleteDocDistributionInput(completedState, actor))
      )
    } yield EmptyEventResult()
    effect.eitherSome[DocDistributionFlowError]
  }

  private def failDocDistribution(failedState: DocDistributionFailedState, actor: UserId)
    : FlowEffect[Either[DocDistributionFlowError, EmptyEventResult]] = {
    val effect = for {
      _ <- FlowEffect.attempt(
        scribe.info(s"User ${actor.idString} is failing doc distribution")
      )
      flowActivity = newActivityStub[DocDistributionFlowActivity]
      _ <- FlowEffect.executeActivityEither(
        flowActivity.failDocDistribution(FailDocDistributionInput(failedState, actor))
      )
    } yield EmptyEventResult()
    effect.eitherSome[DocDistributionFlowError]
  }

  private def resolveDocDistribution(createdState: DocDistributionCreatedState, actor: UserId)
    : FlowEffect[Either[DocDistributionFlowError, EmptyEventResult]] = {
    val effect = for {
      _ <- FlowEffect.attempt(
        scribe.info(s"User ${actor.idString} is resolving doc distribution")
      )
      flowActivity = newActivityStub[DocDistributionFlowActivity]
      _ <- FlowEffect.executeActivityEither(
        flowActivity.resolveDocDistribution(ResolveDocDistributionInput(createdState, actor))
      )
    } yield EmptyEventResult()
    effect.eitherSome[DocDistributionFlowError]
  }

}
