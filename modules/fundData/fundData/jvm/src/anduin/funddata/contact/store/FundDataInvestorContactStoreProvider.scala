// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.contact.store

import com.apple.foundationdb.record.RecordMetaDataBuilder
import com.apple.foundationdb.record.metadata.expressions.KeyExpression.FanType
import com.apple.foundationdb.record.metadata.{Index, IndexTypes, Key}

import anduin.fdb.record.model.FDBTupleConverter
import anduin.fdb.record.model.common.RadixIdTupleConverter
import anduin.fdb.record.{FDBRecordEnum, FDBRecordKeySpace, FDBRecordStoreProvider, FDBStoreProviderCompanion}
import anduin.funddata.contact.store.FundDataInvestorContactStoreProvider.investorIdExpression
import anduin.id.contact.ContactId
import anduin.id.funddata.FundDataInvestorId
import anduin.protobuf.funddata.investor.contact.{ContactProto, FundDataInvestorContactModel}

final case class FundDataInvestorContactStoreProvider(
  override protected val keySpace: FDBRecordKeySpace
) extends FDBRecordStoreProvider[FDBRecordEnum.FundDataInvestorContact.type](
      FDBRecordEnum.FundDataInvestorContact,
      ContactProto
    ) {

  override protected def recordBuilderFn(builder: RecordMetaDataBuilder): Unit = {
    builder
      .getRecordType(FundDataInvestorContactModel.scalaDescriptor.name)
      .setPrimaryKey(investorIdExpression)
  }

  protected def indexes: Seq[IndexMappingWithVersion] = Seq(
    FundDataInvestorContactStoreProvider.Indexes.investorByContactIndex -> 1
  )

}

object FundDataInvestorContactStoreProvider extends FDBStoreProviderCompanion[FDBRecordEnum.FundDataInvestorContact.type] {

  private val investorIdExpression = Key.Expressions.field("investor_id")

  given investorIdTupleConverter: FDBTupleConverter[FundDataInvestorId] = {
    RadixIdTupleConverter.instance
  }

  lazy val contactIdTupleConverter: FDBTupleConverter[ContactId] = RadixIdTupleConverter.instance[ContactId]

  given mapping: Mapping[FundDataInvestorId, FundDataInvestorContactModel] = mappingInstance

  object Indexes {

    lazy val investorByContactIndex: IndexMapping[FundDataInvestorContactModel] = mappingIndexInstance(
      new Index(
        "investor_by_contact_value_index",
        Key.Expressions.field("contacts", FanType.FanOut).nest("contact_id"),
        IndexTypes.VALUE
      )
    )

  }

}
