// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.funddata.contact

import io.circe.Json
import io.circe.syntax.EncoderOps
import zio.{Task, ZIO}

import anduin.batchaction.endpoint.{BatchActionInfo, BatchActionItemStatusInfo}
import anduin.batchaction.*
import anduin.dms.service.FileService
import anduin.funddata.endpoint.contact.{ExportContactsBatchItem, ExportContactsBatchItemResp, BatchExportContactsResp}
import anduin.model.common.user.UserId
import anduin.service.GeneralServiceException
import anduin.workflow.{ActivityQueue, TemporalWorkflowService, WorkflowQueue}
import com.anduin.stargazer.service.api.FileDownloadService
import com.anduin.stargazer.service.utils.ZIOUtils

class FundDataExportContactsToSpreadsheetWorkflowImpl extends BatchActionWorkflowImpl {
  override def activityQueue: ActivityQueue = ActivityQueue.FundDataExportContactsToSpreadsheet
}

object FundDataExportContactsToSpreadsheetWorkflowImpl
    extends BatchActionWorkflowImplCompanion[FundDataExportContactsToSpreadsheetWorkflowImpl] {
  override def workflowQueue: WorkflowQueue = WorkflowQueue.FundDataExportContactsToSpreadsheet
}

class FundDataExportContactsToSpreadsheetWorkflowItemImpl extends BatchActionItemWorkflowImpl {
  override def activityQueue: ActivityQueue = ActivityQueue.FundDataExportContactsToSpreadsheet
}

object FundDataExportContactsToSpreadsheetWorkflowItemImpl
    extends BatchActionItemWorkflowImplCompanion[FundDataExportContactsToSpreadsheetWorkflowItemImpl] {
  override def workflowQueue: WorkflowQueue = WorkflowQueue.FundDataExportContactsToSpreadsheet
}

case class FundDataExportContactsToSpreadsheetActivitiesImpl(
  fundDataContactExportService: FundDataContactExportService,
  fileService: FileService,
  fileDownloadService: FileDownloadService,
  override val batchActionService: BatchActionService
)(
  using override val temporalWorkflowService: TemporalWorkflowService
) extends BatchActionActivitiesImpl {

  override def processItem(data: Json, actor: UserId): Task[Option[Json]] = {
    for {
      batchItem <- ZIOUtils.fromOption(
        data.as[ExportContactsBatchItem].toOption,
        GeneralServiceException(s"Failed to decode $data")
      )
      resp <- fundDataContactExportService.exportContactsBatch(batchItem, actor)
    } yield Option(resp.asJson)
  }

  override def processPostExecute(data: BatchActionInfo): Task[Option[Json]] = {
    for {
      batchItemResps <- ZIO.attempt {
        data.items.flatMap { batchActionItemInfo =>
          batchActionItemInfo.status match {
            case BatchActionItemStatusInfo.Succeeded(_, dataOpt) =>
              dataOpt.fold(Option.empty[ExportContactsBatchItemResp])(inputData =>
                inputData.as[ExportContactsBatchItemResp].toOption
              )
            case _ => Option.empty[ExportContactsBatchItemResp]
          }
        }
      }

    } yield batchItemResps.headOption.map { batchItemResp =>
      BatchExportContactsResp(
        fileUrl = batchItemResp.fileUrl
      ).asJson
    }

  }

}
