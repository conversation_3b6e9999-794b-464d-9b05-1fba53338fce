//  Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.contact.note

import java.time.Instant

import io.github.arainko.ducktape.*
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.funddata.contact.FundDataContactOperations
import anduin.funddata.endpoint.contact.note.{DeleteContactNoteParams, GetContactNoteParams, UpdateContactNoteParams}
import anduin.funddata.endpoint.note.FundDataNote
import anduin.funddata.event.FundDataEventService
import anduin.funddata.idconfig.FundDataIdConfigStoreOperations
import anduin.funddata.note.{FundDataNoteModel, FundDataNoteStoreOperations, FundDataNoteUtils}
import anduin.funddata.permission.FundDataPermissionService
import anduin.id.contact.ContactId
import anduin.id.funddata.FundDataFirmId
import anduin.id.funddata.note.FundDataNoteId
import anduin.model.common.user.UserId
import anduin.protobuf.funddata.event.{DeleteContactNoteEvent, FundDataEvent, UpdateContactNoteEvent}
import anduin.utils.UserProfileUtils
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FundDataContactNoteService(
  fundDataPermissionService: FundDataPermissionService,
  fundDataEventService: FundDataEventService
)(
  using userProfileService: UserProfileService
) {

  private val userProfileUtils = UserProfileUtils()

  def getContactNote(
    params: GetContactNoteParams,
    actor: UserId
  ): Task[Option[FundDataNote]] = {
    val firmId = params.firmId
    for {
      _ <- ZIO.logInfo(s"$actor gets note for ${params.contactId}")

      noteOpt <- FDBRecordDatabase.transact(
        FDBOperations[(FundDataIdConfigStoreOperations, FundDataNoteStoreOperations)].Production
      ) { case (idConfigOps, noteOps) =>
        for {
          noteGroupId <- idConfigOps.get(firmId).map(_.contactNoteGroupId)
          notes <- noteOps.getByObject(noteGroupId, params.contactId)
        } yield notes.headOption
      }
      noteUpdatedByOpt <- ZIOUtils.traverseOption(noteOpt) { note =>
        userProfileUtils.getBasicUserInfo(note.updatedBy)
      }
    } yield noteOpt.zip(noteUpdatedByOpt).map { case (note, updatedBy) =>
      note.into[FundDataNote].transform(Field.const(_.updatedBy, updatedBy))
    }
  }

  private[anduin] def getContactsNote(
    firmId: FundDataFirmId,
    contactIds: Set[ContactId]
  ): Task[Map[ContactId, Option[FundDataNoteModel]]] = {
    FDBRecordDatabase.transact(
      FDBOperations[(FundDataIdConfigStoreOperations, FundDataNoteStoreOperations)].Production
    ) { case (idConfigOps, noteOps) =>
      for {
        noteGroupId <- idConfigOps.get(firmId).map(_.contactNoteGroupId)
        noteMap <- RecordIO
          .parTraverseN(4)(contactIds) { contactId =>
            noteOps.getByObject(noteGroupId, contactId).map { notes =>
              contactId -> notes.headOption
            }
          }
          .map(_.toMap)
      } yield noteMap
    }
  }

  def updateContactNote(
    params: UpdateContactNoteParams,
    actor: UserId
  ): Task[FundDataNoteId] = {
    val firmId = params.firmId
    for {
      _ <- ZIO.logInfo(s"$actor updates note for ${params.contactId}")

      sanitizedNoteContent = FundDataNoteUtils.sanitizeNoteContent(params.content)
      (curNoteOpt, updatedNote) <- FDBRecordDatabase.transact(
        FDBOperations[(FundDataIdConfigStoreOperations, FundDataNoteStoreOperations)].Production
      ) { case (idConfigOps, noteOps) =>
        for {
          noteGroupId <- idConfigOps.get(firmId).map(_.contactNoteGroupId)
          noteOpt <- noteOps.getByObject(noteGroupId, params.contactId).map(_.headOption)
          updatedNote <- noteOpt match {
            case Some(note) =>
              noteOps.update(note.id)(
                _.copy(
                  content = sanitizedNoteContent,
                  updatedAt = Instant.now(),
                  updatedBy = actor
                )
              )
            case None =>
              noteOps.create(
                groupId = noteGroupId,
                objectId = params.contactId,
                content = sanitizedNoteContent,
                actor = actor
              )
          }
        } yield noteOpt -> updatedNote
      }

      _ <- for {
        contactCustomId <- FundDataContactOperations.transact(_.getContactCustomIdUnsafe(firmId, params.contactId))
        _ <- fundDataEventService.push(
          FundDataEvent(
            firmId = firmId,
            actor = actor,
            createdAt = Some(Instant.now),
            detail = UpdateContactNoteEvent(
              contactId = params.contactId,
              contactCustomId = Option.when(contactCustomId.nonEmpty)(contactCustomId),
              oldNote = curNoteOpt.fold("")(_.content),
              newNote = updatedNote.content
            )
          )
        )
      } yield ()
    } yield updatedNote.id
  }

  def deleteContactNote(
    params: DeleteContactNoteParams,
    actor: UserId
  ): Task[Unit] = {
    val firmId = params.firmId
    for {
      _ <- ZIO.logInfo(s"$actor deletes note for ${params.contactId}")

      deletedNoteCnt <- FDBRecordDatabase.transact(
        FDBOperations[(FundDataIdConfigStoreOperations, FundDataNoteStoreOperations)].Production
      ) { case (idConfigOps, noteOps) =>
        for {
          noteGroupId <- idConfigOps.get(firmId).map(_.contactNoteGroupId)
          deletedNoteCnt <- noteOps.deleteByObject(noteGroupId, params.contactId)
        } yield deletedNoteCnt
      }

      _ <- ZIOUtils.when(deletedNoteCnt > 0)(
        for {
          contactCustomId <- FundDataContactOperations.transact(_.getContactCustomIdUnsafe(firmId, params.contactId))
          _ <- fundDataEventService.push(
            FundDataEvent(
              firmId = firmId,
              actor = actor,
              createdAt = Some(Instant.now),
              detail = DeleteContactNoteEvent(
                contactId = params.contactId,
                contactCustomId = Option.when(contactCustomId.nonEmpty)(contactCustomId)
              )
            )
          )
        } yield ()
      )
    } yield ()
  }

}
