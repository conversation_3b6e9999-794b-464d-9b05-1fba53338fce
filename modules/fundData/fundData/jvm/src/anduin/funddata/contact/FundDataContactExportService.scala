// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.funddata.contact

import java.time.Instant

import io.circe.syntax.EncoderOps
import org.apache.poi.ss.usermodel.IndexedColors
import org.apache.poi.xssf.usermodel.{XSSFFont, XSSFRichTextString}
import sttp.model.MediaType
import zio.temporal.workflow.ZWorkflowStub
import zio.{Task, ZIO}

import anduin.batchaction.{BatchActionFrontendTracking, BatchActionService, BatchActionType}
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.documentcontent.spreadsheet.FillSheetData.{FillSheetCell, FillSheetRow}
import anduin.documentcontent.spreadsheet.{FillSheetData, SpreadsheetUtils}
import anduin.fdb.record.{DefaultCluster, FDBRecordDatabase}
import anduin.funddata.communication.{FundDataFirmCommunicationService, FundDataFundLegalEntityCommunicationService}
import anduin.funddata.contact.note.FundDataContactNoteService
import anduin.funddata.endpoint.contact.*
import anduin.funddata.firm.FundDataFirmStoreOperations
import anduin.funddata.fund.fundlegalentity.FundLegalEntityOperations
import anduin.funddata.investmententity.InvestmentEntityStoreOperations
import anduin.funddata.investor.FundDataInvestorStoreOperations
import anduin.funddata.note.FundDataNoteUtils
import anduin.funddata.permission.FundDataPermissionService
import anduin.id.batchaction.BatchActionId
import anduin.model.common.user.UserId
import anduin.storageservice.common.FileContentOrigin
import anduin.temporal.TemporalEnvironment
import anduin.user.UserService
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.service.api.FileDownloadService
import com.anduin.stargazer.service.file.BatchDownloadRequest

final case class FundDataContactExportService(
  fundDataPermissionService: FundDataPermissionService,
  fundDataContactService: FundDataContactService,
  fundDataFirmCommunicationService: FundDataFirmCommunicationService,
  fundDataFundLegalEntityCommunicationService: FundDataFundLegalEntityCommunicationService,
  fileService: FileService,
  fundDataContactNoteService: FundDataContactNoteService,
  batchActionService: BatchActionService,
  temporalEnvironment: TemporalEnvironment,
  fileDownloadService: FileDownloadService,
  userService: UserService
) {

  private def generateSpreedSheetHeaderText(headerText: String, hasAsterisk: Boolean = false): XSSFRichTextString = {
    val headerFont = {
      val font = XSSFFont()
      font.setBold(true)
      font
    }

    val headerRichTextString = XSSFRichTextString()
    headerRichTextString.append(headerText, headerFont)

    if (hasAsterisk) {
      val asteriskFont = {
        val font = XSSFFont()
        font.setColor(IndexedColors.RED.getIndex)
        font
      }

      headerRichTextString.append(" ")
      headerRichTextString.append("*", asteriskFont)
    }

    headerRichTextString
  }

  def exportContacts(
    params: ExportContactsParams,
    actor: UserId
  ): Task[ExportContactsResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor export ${params.contactIds.size} contacts data")
      _ <- fundDataPermissionService.validateUserHasRole(params.firmId, actor)

      contacts <- fundDataContactService
        .getFirmContactsInfo(
          params = GetFirmContactsInfoParams(
            firmId = params.firmId,
            filterByContacts = Some(params.contactIds)
          ),
          actor = actor
        )
        .map(_.data)

      refinedContacts <- ZIO.foreach(contacts) { contact =>
        for {
          contactRelations <- fundDataContactService.getContactRelation(
            params = GetContactRelationParams(
              firmId = params.firmId,
              contactId = contact.contactId
            ),
            actor = actor
          )

          contactMatrix <- fundDataContactService
            .getContactMatrix(
              params = GetContactMatrixParams(
                firmId = params.firmId,
                contactId = contact.contactId
              ),
              actor = actor
            )
            .map(_.contactMatrixValue)

        } yield (
          contact = contact,
          contactInvestmentEntityPermission = contactRelations.investmentEntityRelations,
          contactClientRelations = (contactRelations.contactRelationInfos.investors
            .map {
              _.investorId -> None
            } ++
            contactRelations.contactRelationInfos.investmentEntities.map { relation =>
              relation.investmentEntityId.parent -> Some(relation.investmentEntityId)
            })
            .groupMap(_._1)(_._2)
            .flatMap { case (investorId, investmentEntityIds) =>
              investmentEntityIds.distinct match {
                case None :: Nil =>
                  Seq(
                    (
                      clientId = investorId,
                      investmentEntityId = None
                    )
                  )
                case investmentEntityIds =>
                  investmentEntityIds.flatten.map { investmentEntityIdOpt =>
                    (
                      clientId = investorId,
                      investmentEntityId = Option(investmentEntityIdOpt)
                    )
                  }
              }

            }
            .toSeq,
          contactMatrix = contactMatrix
        )
      }

      clientNameMap <- FundDataInvestorStoreOperations
        .transact(
          _.getInvestors(refinedContacts.flatMap(_.contactClientRelations.map(_.clientId)).distinct)
        )
        .map(_.map { investor =>
          investor.investorId -> investor.name
        }.toMap)

      investmentEntityNameMap <- InvestmentEntityStoreOperations
        .transact(
          _.gets(refinedContacts.flatMap(_.contactClientRelations.flatMap(_.investmentEntityId)).distinct)
        )
        .map(_.map { investmentEntity =>
          investmentEntity.investmentEntityId -> investmentEntity.name
        }.toMap)

      fundLegalEntityNameMap <- FundLegalEntityOperations
        .transact(_.gets(refinedContacts.flatMap(_.contactMatrix.map(_.contactMatrixKey.fundLegalEntityId)).distinct))
        .map(_.map { fundLegalEntity =>
          fundLegalEntity.id -> fundLegalEntity.name
        }.toMap)

      contactsNoteMap <- fundDataContactNoteService.getContactsNote(
        firmId = params.firmId,
        contactIds = params.contactIds
      )

      contactInformationSheet = FillSheetData(
        sheetName = Some("1. Contact information"),
        startCol = 0,
        startRow = 0,
        rows = FillSheetRow(
          cells = List(
            FillSheetCell(generateSpreedSheetHeaderText("Email")),
            FillSheetCell(generateSpreedSheetHeaderText("First name")),
            FillSheetCell(generateSpreedSheetHeaderText("Last name")),
            FillSheetCell(generateSpreedSheetHeaderText("Custom ID")),
            FillSheetCell(generateSpreedSheetHeaderText("Company")),
            FillSheetCell(generateSpreedSheetHeaderText("Title")),
            FillSheetCell(generateSpreedSheetHeaderText("Phone")),
            FillSheetCell(generateSpreedSheetHeaderText("Number and street")),
            FillSheetCell(generateSpreedSheetHeaderText("City")),
            FillSheetCell(generateSpreedSheetHeaderText("State")),
            FillSheetCell(generateSpreedSheetHeaderText("Country")),
            FillSheetCell(generateSpreedSheetHeaderText("Zip code")),
            FillSheetCell(generateSpreedSheetHeaderText("Note"))
          )
        ) +: refinedContacts.map { case (contact, _, _, _) =>
          FillSheetRow(
            cells = List(
              FillSheetCell(contact.contactInfo.email),
              FillSheetCell(contact.contactInfo.firstName),
              FillSheetCell(contact.contactInfo.lastName),
              FillSheetCell(contact.contactInfo.customId),
              FillSheetCell(contact.contactInfo.company),
              FillSheetCell(contact.contactInfo.title),
              FillSheetCell(contact.contactInfo.phone),
              FillSheetCell(contact.contactInfo.numberAndStreet),
              FillSheetCell(contact.contactInfo.city),
              FillSheetCell(contact.contactInfo.state),
              FillSheetCell(contact.contactInfo.country),
              FillSheetCell(contact.contactInfo.zipCode),
              FillSheetCell(
                contactsNoteMap.getOrElse(contact.contactId, None).fold("") { note =>
                  FundDataNoteUtils.getTextFromNoteContent(note.content)
                }
              )
            )
          )
        }
      )

      contactRelationshipsSheet = FillSheetData(
        sheetName = Some("2. Contact Relationships"),
        startCol = 0,
        startRow = 0,
        rows = FillSheetRow(
          cells = List(
            FillSheetCell(generateSpreedSheetHeaderText("Email")),
            FillSheetCell(generateSpreedSheetHeaderText("Client")),
            FillSheetCell(generateSpreedSheetHeaderText("Investment Entity"))
          )
        ) +: refinedContacts.flatMap { case (contact, _, contactClientRelations, _) =>
          contactClientRelations.map { relation =>
            FillSheetRow(
              cells = List(
                FillSheetCell(contact.contactInfo.email),
                FillSheetCell(clientNameMap.getOrElse(relation.clientId, "")),
                FillSheetCell(relation.investmentEntityId.flatMap(investmentEntityNameMap.get).getOrElse(""))
              )
            )
          }
        }
      )

      ieLevelCommunicationSettingSheet = FillSheetData(
        sheetName = Some("3.1. IE-Level Communication Settings"),
        startCol = 0,
        startRow = 0,
        rows = FillSheetRow(
          cells = List(
            FillSheetCell(generateSpreedSheetHeaderText("Email")),
            FillSheetCell(generateSpreedSheetHeaderText("Investment Entity")),
            FillSheetCell(generateSpreedSheetHeaderText("Invitation emails")),
            FillSheetCell(generateSpreedSheetHeaderText("Document requests"))
          )
        ) +: refinedContacts.flatMap { case (contact, contactInvestmentEntityPermission, _, _) =>
          contactInvestmentEntityPermission.map { permission =>
            FillSheetRow(
              cells = List(
                FillSheetCell(contact.contactInfo.email),
                FillSheetCell(investmentEntityNameMap.getOrElse(permission.investmentEntityId, "")),
                FillSheetCell(permission.fundSubCommunicationType match {
                  case FundDataContact.FundSubCommunicationType.MainInvestor => "Yes"
                  case FundDataContact.FundSubCommunicationType.Empty        => "No"
                }),
                FillSheetCell(permission.documentRequestCommunicationType match {
                  case FundDataContact.DocumentRequestCommunicationType.NonReceiver => "No"
                  case FundDataContact.DocumentRequestCommunicationType.Receiver    => "Yes"
                })
              )
            )
          }
        }
      )

      firmCommunicationTypes <- fundDataFirmCommunicationService
        .getFirmCommunicationsUnsafe(params.firmId)
        .map(_.sortBy(_.name))

      fleCommunicationTypeMap <- ZIO
        .foreach(
          refinedContacts.flatMap(_.contactMatrix.map(_.contactMatrixKey.fundLegalEntityId)).distinct
        ) { fundLegalEntityId =>
          fundDataFundLegalEntityCommunicationService
            .getFundLegalEntityCommunicationsUnsafe(fundLegalEntityId)
            .map(fundLegalEntityId -> _.map(_.communicationTypeId).toSet)
        }
        .map(_.toMap)

      fundCommunicationMatricesSheet = FillSheetData(
        sheetName = Some("3.2. Fund Communication Matrices"),
        startCol = 0,
        startRow = 0,
        rows = FillSheetRow(
          cells = List(
            FillSheetCell(generateSpreedSheetHeaderText("Email")),
            FillSheetCell(generateSpreedSheetHeaderText("Investment Entity")),
            FillSheetCell(generateSpreedSheetHeaderText("Fund Legal Entity"))
          ) ++ firmCommunicationTypes.map { communicationType =>
            FillSheetCell(generateSpreedSheetHeaderText(communicationType.name))
          }
        ) +: refinedContacts.flatMap { case (contact, _, _, contactMatrices) =>
          contactMatrices
            .groupMap { matrix =>
              (
                investmentEntityId = matrix.contactMatrixKey.investmentEntityId,
                fundLegalEntityId = matrix.contactMatrixKey.fundLegalEntityId
              )
            } { matrix =>
              matrix.contactMatrixKey.communicationTypeId -> matrix.communicationRole
            }
            .view
            .mapValues(_.toMap)
            .toSeq
            .map { case ((investmentEntityId, fundLegalEntityId), communicationRoleMap) =>
              val fleCommunicationTypeIds = fleCommunicationTypeMap.getOrElse(fundLegalEntityId, Set.empty)
              FillSheetRow(
                cells = List(
                  FillSheetCell(contact.contactInfo.email),
                  FillSheetCell(investmentEntityNameMap.getOrElse(investmentEntityId, "")),
                  FillSheetCell(fundLegalEntityNameMap.getOrElse(fundLegalEntityId, ""))
                ) ++ firmCommunicationTypes.map { communicationType =>
                  FillSheetCell(
                    if (fleCommunicationTypeIds.contains(communicationType.communicationTypeId)) {
                      communicationRoleMap
                        .get(communicationType.communicationTypeId)
                        .fold(
                          "Unassigned"
                        ) {
                          case FundDataContact.CommunicationRole.Primary   => "Primary"
                          case FundDataContact.CommunicationRole.Secondary => "Secondary"
                        }
                    } else {
                      "N/A"
                    }
                  )
                }
              )
            }
        }
      )

      workbookStream <- SpreadsheetUtils.createAndFillSpreadsheet(
        Seq(
          contactInformationSheet,
          contactRelationshipsSheet,
          ieLevelCommunicationSettingSheet,
          fundCommunicationMatricesSheet
        )
      )

      firmName <- FDBRecordDatabase.transact(FundDataFirmStoreOperations.Production)(_.get(params.firmId)).map(_.name)

      userZoneId <- userService.getUserTimeZone(actor).map(_.getOrElse(DateTimeUtils.defaultTimezone))
      formattedDateTime = DateTimeUtils.formatInstant(
        Instant.now,
        DateTimeUtils.MonthDayFormatter3
      )(
        using userZoneId
      )

      workbookName = s"$firmName - Contact Data Export - $formattedDateTime.xlsx"

      folderId <- fileService.createUserTemporaryFolderIfNeeded(actor)

      generatedFileId <- fileService.uploadFile(
        parentFolderId = folderId,
        fileName = workbookName,
        content = FileContentOrigin.FromSource(
          workbookStream,
          MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        ),
        uploader = actor
      )

    } yield ExportContactsResponse(generatedFileId)
  }

  def exportContactsBatch(
    params: ExportContactsBatchItem,
    actor: UserId
  ): Task[ExportContactsBatchItemResp] = {
    for {
      _ <- ZIO.logInfo(s"$actor export ${params.contactIds.size} contacts data for batch")
      _ <- fundDataPermissionService.validateUserHasRole(params.firmId, actor)

      exportedFileId <- exportContacts(
        ExportContactsParams(
          firmId = params.firmId,
          contactIds = params.contactIds
        ),
        actor
      ).map(_.fileId)

      fileUrl <- fileDownloadService
        .getBatchDownloadData(
          actor = actor,
          request = BatchDownloadRequest(
            "",
            Seq(),
            Seq(exportedFileId),
            DmsTrackingActivityType.Download
          ),
          httpContext = None
        )
        .map(_.url)

    } yield ExportContactsBatchItemResp(fileUrl)
  }

  def batchExportContacts(
    params: BatchExportContactsParams,
    actor: UserId
  ): Task[BatchActionId] = {
    for {
      _ <- ZIO.logInfo(s"$actor export ${params.contactIds.size} contacts data to spreadsheet")
      _ <- fundDataPermissionService.validateUserHasRole(params.firmId, actor)

      // Create a single batch item with all contacts
      batchItem = ExportContactsBatchItem(
        firmId = params.firmId,
        contactIds = params.contactIds
      )

      batchActionId <- batchActionService.startBatchActionInternal(
        params.firmId,
        actor,
        actionType = BatchActionType.FundDataExportContactsToSpreadsheet,
        batchActionItemsData = List(batchItem.asJson),
        frontendTracking = BatchActionFrontendTracking.ACTOR_TRACKING,
        startWorkflow = workflowParams => {
          FundDataExportContactsToSpreadsheetWorkflowImpl.instance
            .getWorkflowStub()
            .provideEnvironment(temporalEnvironment.workflowClient)
            .flatMap(workflowStub => ZWorkflowStub.start(workflowStub.execute(workflowParams)))
        }
      )
    } yield batchActionId
  }

}
