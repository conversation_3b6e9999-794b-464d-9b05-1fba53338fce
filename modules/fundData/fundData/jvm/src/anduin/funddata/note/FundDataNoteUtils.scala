//  Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.note

import org.owasp.html.{HtmlPolicyBuilder, Sanitizers}
import org.apache.commons.text.StringEscapeUtils

object FundDataNoteUtils {

  def sanitizeNoteContent(content: String): String = {
    noteContentSanitizer.sanitize(content)
  }

  def getTextFromNoteContent(content: String): String =
    unescapeHtml(noteContentTextExtractor.sanitize(content))

  private val noteContentTextExtractor = new HtmlPolicyBuilder().toFactory

  private val noteContentSanitizer = Sanitizers.FORMATTING
    .and(Sanitizers.BLOCKS)
    .and(Sanitizers.STYLES)
    .and(
      new HtmlPolicyBuilder()
        .allowStandardUrlProtocols()
        .allowElements("a", "li")
        .allowAttributes("href", "target", "rel")
        .onElements("a")
        .allowAttributes("class")
        .globally()
        .allowAttributes("data-list")
        .onElements("li")
        .toFactory()
    )

  private def unescapeHtml(text: String): String = {
    StringEscapeUtils.unescapeHtml4(text)
  }

}
