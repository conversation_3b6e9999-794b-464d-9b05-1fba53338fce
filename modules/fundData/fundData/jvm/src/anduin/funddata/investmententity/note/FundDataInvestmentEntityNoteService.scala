//  Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investmententity.note

import java.time.Instant

import io.github.arainko.ducktape.*
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.funddata.endpoint.investmententity.note.*
import anduin.funddata.endpoint.note.FundDataNote
import anduin.funddata.event.FundDataEventService
import anduin.funddata.idconfig.FundDataIdConfigStoreOperations
import anduin.funddata.investmententity.InvestmentEntityStoreOperations
import anduin.funddata.note.{FundDataNoteModel, FundDataNoteStoreOperations, FundDataNoteUtils}
import anduin.funddata.permission.FundDataPermissionService
import anduin.id.funddata.{FundDataFirmId, FundDataInvestmentEntityId}
import anduin.id.funddata.note.FundDataNoteId
import anduin.model.common.user.UserId
import anduin.protobuf.funddata.event.{DeleteInvestmentEntityNoteEvent, FundDataEvent, UpdateInvestmentEntityNoteEvent}
import anduin.utils.UserProfileUtils
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FundDataInvestmentEntityNoteService(
  fundDataPermissionService: FundDataPermissionService,
  fundDataEventService: FundDataEventService
)(
  using userProfileService: UserProfileService
) {

  private val userProfileUtils = UserProfileUtils()

  def getInvestmentEntityNote(
    params: GetInvestmentEntityNoteParams,
    actor: UserId
  ): Task[Option[FundDataNote]] = {
    val firmId = params.investmentEntityId.firmId
    for {
      _ <- ZIO.logInfo(s"$actor gets note for ${params.investmentEntityId}")

      noteOpt <- FDBRecordDatabase.transact(
        FDBOperations[(FundDataIdConfigStoreOperations, FundDataNoteStoreOperations)].Production
      ) { case (idConfigOps, noteOps) =>
        for {
          noteGroupId <- idConfigOps.get(firmId).map(_.investmentEntityNoteGroupId)
          notes <- noteOps.getByObject(noteGroupId, params.investmentEntityId)
        } yield notes.headOption
      }
      noteUpdatedByOpt <- ZIOUtils.traverseOption(noteOpt) { note =>
        userProfileUtils.getBasicUserInfo(note.updatedBy)
      }
    } yield noteOpt.zip(noteUpdatedByOpt).map { case (note, updatedBy) =>
      note.into[FundDataNote].transform(Field.const(_.updatedBy, updatedBy))
    }
  }

  private[anduin] def getInvestmentEntitiesNote(
    firmId: FundDataFirmId,
    investmentEntityIds: Set[FundDataInvestmentEntityId]
  ): Task[Map[FundDataInvestmentEntityId, Option[FundDataNoteModel]]] = {
    FDBRecordDatabase.transact(
      FDBOperations[(FundDataIdConfigStoreOperations, FundDataNoteStoreOperations)].Production
    ) { case (idConfigOps, noteOps) =>
      for {
        noteGroupId <- idConfigOps.get(firmId).map(_.investmentEntityNoteGroupId)
        noteMap <- RecordIO
          .parTraverseN(4)(investmentEntityIds) { investmentEntityId =>
            noteOps.getByObject(noteGroupId, investmentEntityId).map { notes =>
              investmentEntityId -> notes.headOption
            }
          }
          .map(_.toMap)
      } yield noteMap
    }
  }

  def updateInvestmentEntityNote(
    params: UpdateInvestmentEntityNoteParams,
    actor: UserId,
    updateWhen: UpdateInvestmentEntityNoteEvent.UpdateWhen = UpdateInvestmentEntityNoteEvent.UpdateWhen.Manual
  ): Task[FundDataNoteId] = {
    val firmId = params.investmentEntityId.firmId
    for {
      _ <- ZIO.logInfo(s"$actor updates note for ${params.investmentEntityId}")

      sanitizedNoteContent = FundDataNoteUtils.sanitizeNoteContent(params.content)
      (curNoteOpt, updatedNote) <- FDBRecordDatabase.transact(
        FDBOperations[(FundDataIdConfigStoreOperations, FundDataNoteStoreOperations)].Production
      ) { case (idConfigOps, noteOps) =>
        for {
          noteGroupId <- idConfigOps.get(firmId).map(_.investmentEntityNoteGroupId)
          noteOpt <- noteOps.getByObject(noteGroupId, params.investmentEntityId).map(_.headOption)
          updatedNote <- noteOpt match {
            case Some(note) =>
              noteOps.update(note.id)(
                _.copy(
                  content = sanitizedNoteContent,
                  updatedAt = Instant.now(),
                  updatedBy = actor
                )
              )
            case None =>
              noteOps.create(
                groupId = noteGroupId,
                objectId = params.investmentEntityId,
                content = sanitizedNoteContent,
                actor = actor
              )
          }
        } yield noteOpt -> updatedNote
      }

      _ <- for {
        investmentEntityCustomIdOpt <- InvestmentEntityStoreOperations.transact(_.getCustomId(params.investmentEntityId))
        _ <- fundDataEventService.push(
          FundDataEvent(
            firmId = firmId,
            actor = actor,
            createdAt = Some(Instant.now),
            detail = UpdateInvestmentEntityNoteEvent(
              investmentEntityId = params.investmentEntityId,
              investmentEntityCustomId = investmentEntityCustomIdOpt,
              oldNote = curNoteOpt.fold("")(_.content),
              newNote = updatedNote.content,
              updateWhen = updateWhen
            )
          )
        )
      } yield ()
    } yield updatedNote.id
  }

  def deleteInvestmentEntityNote(
    params: DeleteInvestmentEntityNoteParams,
    actor: UserId,
    deleteWhen: DeleteInvestmentEntityNoteEvent.DeleteWhen = DeleteInvestmentEntityNoteEvent.DeleteWhen.Manual
  ): Task[Unit] = {
    val firmId = params.investmentEntityId.firmId
    for {
      _ <- ZIO.logInfo(s"$actor deletes note for ${params.investmentEntityId}")

      deletedNoteCnt <- FDBRecordDatabase.transact(
        FDBOperations[(FundDataIdConfigStoreOperations, FundDataNoteStoreOperations)].Production
      ) { case (idConfigOps, noteOps) =>
        for {
          noteGroupId <- idConfigOps.get(firmId).map(_.investmentEntityNoteGroupId)
          deletedNoteCnt <- noteOps.deleteByObject(noteGroupId, params.investmentEntityId)
        } yield deletedNoteCnt
      }

      _ <- ZIOUtils.when(deletedNoteCnt > 0)(
        for {
          investmentEntityCustomIdOpt <- InvestmentEntityStoreOperations.transact(
            _.getCustomId(params.investmentEntityId)
          )
          _ <- fundDataEventService.push(
            FundDataEvent(
              firmId = firmId,
              actor = actor,
              createdAt = Some(Instant.now),
              detail = DeleteInvestmentEntityNoteEvent(
                investmentEntityId = params.investmentEntityId,
                investmentEntityCustomId = investmentEntityCustomIdOpt,
                deleteWhen = deleteWhen
              )
            )
          )
        } yield ()
      )
    } yield ()
  }

}
