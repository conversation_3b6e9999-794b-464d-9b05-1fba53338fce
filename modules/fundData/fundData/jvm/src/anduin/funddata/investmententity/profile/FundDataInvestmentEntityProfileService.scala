// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investmententity.profile

import java.time.Instant
import java.time.format.DateTimeFormatter

import io.circe.syntax.*
import sttp.model.MediaType
import zio.prelude.Validation
import zio.temporal.workflow.ZWorkflowStub
import zio.{Task, ZIO}

import anduin.batchaction.{
  BatchActionFrontendTracking,
  BatchActionItemStatusSucceeded,
  BatchActionService,
  BatchActionType
}
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.documentcontent.csv.CsvUtils
import anduin.fdb.record.{DefaultCluster, FDBRecordDatabase}
import anduin.forms.engine.GaiaEngine.{EngineConfiguration, EngineContext}
import anduin.forms.engine.{GaiaEngine, GaiaState}
import anduin.forms.logic.extractor.LogicExtractor
import anduin.forms.model.mapping.FormTemplateMappingModels.{FormMappingType, MappingItem}
import anduin.forms.rules.FormRule
import anduin.forms.service.{FormDataUserTempService, FormMappingService, FormService}
import anduin.forms.storage.{
  FormTemplateMappingStoreOperations,
  FormVersionDataStoreOperations,
  FormVersionDataStoreProvider
}
import anduin.forms.transfer.DataTransferEngine
import anduin.forms.utils.*
import anduin.forms.{Form, FormData}
import anduin.funddata.endpoint.firm.*
import anduin.funddata.endpoint.firm.ComputeProfilesDataFromSpreadsheetParams.ProfileInfo
import anduin.funddata.endpoint.firm.FundDataFormToFormTemplateMapping.MappingType
import anduin.funddata.endpoint.firm.ImportProfilesDataFromSpreadsheetParams.{ImportProfileInfo, ProfileFormSource}
import anduin.funddata.endpoint.investmententity.*
import anduin.funddata.endpoint.investmententity.FundDataInvestmentEntityProfile.ProfileUpdateInfo
import anduin.funddata.endpoint.investmententity.MergeInvestmentEntitiesParams.ProfileConflictResolveMethod
import anduin.funddata.error.FundDataError.{FundDataAuthorizationError, FundDataValidationError}
import anduin.funddata.event.FundDataEventService
import anduin.funddata.firm.{
  FundDataFirmProfileImportExportService,
  FundDataFirmProfileService,
  FundDataFirmProfileStoreOperations,
  FundDataFirmStoreOperations
}
import anduin.funddata.fund.subscription.{FundDataFundSubscriptionStoreOperations, FundDataFundSubscriptionStoreProvider}
import anduin.funddata.investmententity.InvestmentEntityStoreOperations
import anduin.funddata.investmententity.profile.FundDataInvestmentEntityProfileService.{
  ComputedProfileDataAppendWithSubscriptionData,
  ComputedSubscriptionDataAppendWithProfileData,
  DataTransferStrategy
}
import anduin.funddata.investmententity.profile.history.{
  InvestmentEntityProfileHistoryStoreOperation,
  InvestmentEntityProfileHistoryStoreProvider
}
import anduin.funddata.investor.FundDataInvestorStoreOperations
import anduin.funddata.permission.FundDataPermissionService
import anduin.funddata.tag.FundDataTagService
import anduin.funddata.validation.FundDataValidationUtils
import anduin.fundsub.form.FundSubFormService
import anduin.fundsub.integration.FundSubExternalIntegrationService
import anduin.fundsub.models.FundSubModelStoreOperations
import anduin.greylin.GreylinDataService
import anduin.greylin.operation.FundSubscriptionOperations
import anduin.id.batchaction.BatchActionId
import anduin.id.form.{FormTemplateMappingVersionId, FormVersionDataId, FormVersionId}
import anduin.id.funddata.{FundDataFirmId, FundDataInvestmentEntityId, FundDataInvestorId, FundDataProfileConflictId}
import anduin.id.fundsub.{FundSubAdminRestrictedId, FundSubId, FundSubLpId}
import anduin.model.common.user.UserId
import anduin.model.document.DocumentStorageId
import anduin.ontology.service.FormSaProfileMappingService
import anduin.portaluser.ExecutiveAdmin
import anduin.protobuf.funddata.event.*
import anduin.protobuf.funddata.investmententity.profile.*
import anduin.protobuf.funddata.investmententity.profile.conflict.{
  FundDataProfileConflictSourceAutoSyncFromFund,
  FundDataProfileConflictSourceImportFromFund,
  FundDataProfileConflictSourceImportFromSpreadsheet
}
import anduin.service.GeneralServiceException
import anduin.storageservice.common.FileContentOrigin
import anduin.temporal.TemporalEnvironment
import anduin.user.UserService
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FundDataInvestmentEntityProfileService(
  fundDataPermissionService: FundDataPermissionService,
  fundDataFirmProfileService: FundDataFirmProfileService,
  batchActionService: BatchActionService,
  formService: FormService,
  formSaProfileMappingService: FormSaProfileMappingService,
  fundDataEventService: FundDataEventService,
  temporalEnvironment: TemporalEnvironment,
  fundDataTagService: FundDataTagService,
  fundSubFormService: FundSubFormService,
  formMappingService: FormMappingService,
  executiveAdmin: ExecutiveAdmin,
  greylinDataService: GreylinDataService,
  fundSubExternalIntegrationService: FundSubExternalIntegrationService,
  fundDataInvestmentEntityProfileHistoryService: FundDataInvestmentEntityProfileHistoryService,
  profileImportExportService: FundDataFirmProfileImportExportService,
  fundDataProfileConflictService: FundDataProfileConflictService,
  formDataUserTempService: FormDataUserTempService
)(
  using val fileService: FileService,
  userService: UserService
) {

  def setupInvestmentEntityProfile(
    investmentEntityId: FundDataInvestmentEntityId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor set up investment entity profile for $investmentEntityId")
      (profileFormId, profileForm) <- getFirmProfileTemplate(investmentEntityId.parent.parent, actor)
      initialProfileFormData <- ZIO.attempt(
        GaiaEngine
          .make(
            profileForm.form,
            EngineConfiguration.default,
            EngineContext.default
          )
          .flatMap(_.replay(List.empty).map(_._1))
      )
      profile <- FDBRecordDatabase.transact(
        InvestmentEntityProfileStoreProvider.Production,
        FormVersionDataStoreProvider.Production
      ) { (profileStore, dataStore) =>
        val ops = InvestmentEntityProfileStoreOperations(profileStore, dataStore)
        ops.createProfile(
          investmentEntityId,
          profileFormId,
          initialProfileFormData,
          actor
        )
      }
      _ <- fundDataEventService.push(
        FundDataEvent(
          firmId = investmentEntityId.parent.parent,
          actor = actor,
          createdAt = Some(Instant.now),
          detail = CreateInvestmentEntityProfileEvent(
            investmentEntityId = investmentEntityId,
            profileFormId = profile.profileFormId,
            profileFormDataId = profile.profileFormDataId
          )
        )
      )
    } yield ()
  }

  def getInvestmentEntityProfile(
    investmentEntityId: FundDataInvestmentEntityId,
    actor: UserId
  ): Task[FundDataInvestmentEntityProfile] = {
    val firmId = investmentEntityId.parent.parent
    for {
      _ <- ZIO.logInfo(s"$actor get investment entity profile of $investmentEntityId")
      _ <- fundDataPermissionService.validateUserCanViewProfile(investmentEntityId, actor)
      (profileModel, profileData, canImportDataFromSubscription, canClearData, lastProfileUpdatedAt) <-
        FDBRecordDatabase.transactC(
          InvestmentEntityProfileStoreProvider.Production,
          FormVersionDataStoreProvider.Production,
          FundDataFundSubscriptionStoreProvider.Production,
          InvestmentEntityProfileHistoryStoreProvider.Production
        ) { (_, profileStore, dataStore, subscriptionStore, historyStore) =>
          val ops = InvestmentEntityProfileStoreOperations(profileStore, dataStore)
          val historyOps = InvestmentEntityProfileHistoryStoreOperation(historyStore)
          for {
            profileModel <- ops.getProfile(investmentEntityId)
            profileData <- ops.dataOps.get(profileModel.profileFormDataId)
            linkedSubscriptions <- FundDataFundSubscriptionStoreOperations(subscriptionStore).getSubscriptions(
              investmentEntityId
            )
            (lastProfileHistoryEvents, _) <- historyOps.getEvents(profileModel.profileHistoryId, limit = 1)
          } yield {
            val canImportDataFromSubscription = linkedSubscriptions.exists(_.linkedFundSubOrderId.nonEmpty)
            val canClearData = !InvestmentEntityProfileModelUtils.isProfileEmpty(profileModel.profileUpdate)
            val lastProfileUpdatedAt = lastProfileHistoryEvents.headOption.flatMap(_.timestamp)
            (profileModel, profileData, canImportDataFromSubscription, canClearData, lastProfileUpdatedAt)
          }
        }
      profileForm <- formService
        .getForm(
          profileModel.profileFormId.parent,
          Option(profileModel.profileFormId),
          actor,
          shouldCheckPermission = false
        )
        .map(_.formData)
      firmProfile <- FDBRecordDatabase.transact(FundDataFirmProfileStoreOperations.Production)(
        _.get(firmId)
      )
    } yield FundDataInvestmentEntityProfile(
      profileFormId = profileModel.profileFormId,
      profileForm = profileForm,
      profileData = profileData,
      profileUpdate = FundDataInvestmentEntityProfileService.toProfileUpdateInfo(profileModel.profileUpdate),
      canImportDataFromSubscription = canImportDataFromSubscription,
      canClearData = canClearData,
      viewProfileSummaryTable = firmProfile.enableViewProfileSummaryTable,
      lastProfileUpdatedAt = lastProfileUpdatedAt
    )
  }

  def getExportProfileData(
    firmId: FundDataFirmId,
    investorIds: List[FundDataInvestorId],
    templateVersionId: FormTemplateMappingVersionId,
    actor: UserId
  ): Task[DocumentStorageId] = {
    for {
      ieModels <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.get(investorIds).map(_.values.flatten.toList)
      )
      ieIdToModelMap = ieModels.map(ieModel => ieModel.investmentEntityId -> ieModel).toMap
      investorIdToInvestorModelMap <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
        _.getInvestors(investorIds).map(_.map(investorModel => investorModel.investorId -> investorModel).toMap)
      )
      exportedData <- profileImportExportService.exportProfileData(
        firmId = firmId,
        investmentEntityIds = ieModels.map(_.investmentEntityId),
        templateMappingVersionIdOpt = Some(templateVersionId),
        actor = actor,
        exportFormat = FundDataFirmProfileImportExportService.ExportFormat.ValueLabel
      )
      startCol = exportedData.startCol
      templateFieldColMap = exportedData.templateFieldColMap

      rawData = exportedData.investmentEntityProfileData.map { case (investmentEntityId, keyValueMap) =>
        val investorId = investmentEntityId.parent
        val predefinedCells = List(
          investorIdToInvestorModelMap.get(investorId).map(_.name).getOrElse(""),
          investorIdToInvestorModelMap.get(investorId).map(_.customId).getOrElse(""),
          ieIdToModelMap.get(investmentEntityId).map(_.name).getOrElse(""),
          ieIdToModelMap.get(investmentEntityId).map(_.customId).getOrElse("")
        )
        val paddingCells = List.fill(startCol)("")
        val templateCells = for {
          maxCol <- templateFieldColMap.keySet.maxOption.toList
          colIndex <- Range.inclusive(startCol, maxCol).toList
        } yield templateFieldColMap
          .get(colIndex)
          .fold("")(
            keyValueMap.getOrElse(_, "")
          )
        predefinedCells ++ paddingCells ++ templateCells
      }

      resultSource <- CsvUtils.createCsvTask(rawData)
      folderId <- fileService.createUserTemporaryFolderIfNeeded(actor = actor)
      userZoneId <- userService.getUserTimeZone(actor).map(_.getOrElse(DateTimeUtils.defaultTimezone))
      formattedDateTime = DateTimeUtils.formatInstant(
        Instant.now,
        DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss")
      )(
        using userZoneId
      )

      fileId <- fileService.uploadFile(
        folderId,
        s"export_profile_data_$formattedDateTime.csv",
        FileContentOrigin.FromSource(
          resultSource,
          MediaType.TextCsv
        ),
        actor
      )

      documentStorageId <- fileService.getFileStorageId(
        actor = actor,
        fileId = fileId,
        purpose = DmsTrackingActivityType.Internal,
        httpContextOpt = None
      )
    } yield documentStorageId
  }

  private def getFirmProfileTemplate(firmId: FundDataFirmId, actor: UserId) = {
    for {
      templateId <- FDBRecordDatabase.transact(FundDataFirmStoreOperations.Production)(
        _.get(firmId).map(_.profileTemplateId)
      )
      form <- formService
        .getForm(
          templateId.parent,
          Option(templateId),
          actor,
          shouldCheckPermission = false
        )
        .map(_.formData)
    } yield (templateId, form)
  }

  def saveInvestmentEntityProfileDataIncremental(
    investmentEntityId: FundDataInvestmentEntityId,
    profileFormId: FormVersionId,
    profileData: GaiaState,
    isFinishedEdit: Boolean,
    actor: UserId
  ): Task[Unit] = {
    val firmId = investmentEntityId.parent.parent
    for {
      _ <- ZIO.logInfo(s"$actor save investment entity profile data of $investmentEntityId")
      _ <- fundDataPermissionService.validateUserCanEditProfile(investmentEntityId, actor)

      (curProfileModel, _) <- FDBRecordDatabase.transact(
        InvestmentEntityProfileStoreProvider.Production,
        FormVersionDataStoreProvider.Production
      ) { (profileStore, dataStore) =>
        val ops = InvestmentEntityProfileStoreOperations(profileStore, dataStore)
        for {
          curProfileModel <- ops.getProfile(investmentEntityId)
          curProfileData <- ops.dataOps.get(curProfileModel.profileFormDataId)
        } yield curProfileModel -> curProfileData
      }
      _ <- ZIOUtils.validate(curProfileModel.profileFormId == profileFormId)(
        GeneralServiceException(
          s"Form version $profileFormId to save doesn't match with profile version ${curProfileModel.profileFormId}"
        )
      )
      _ <- updateProfileData(
        investmentEntityId,
        profileData,
        profileUpdate = ProfileUpdateManualEdit(actor, at = Some(Instant.now)),
        actor
      )

      investmentEntityModel <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.get(investmentEntityId)
      )
      _ <- ZIOUtils.when(isFinishedEdit)(
        fundDataEventService.push(
          FundDataEvent(
            firmId = firmId,
            actor = actor,
            createdAt = Some(Instant.now),
            detail = FinishManualEditInvestmentEntityProfile(
              investmentEntityId = investmentEntityId,
              customId = investmentEntityModel.customId
            )
          )
        )
      )
    } yield ()
  }

  def computeProfilesDataFromSpreadsheet(params: ComputeProfilesDataFromSpreadsheetParams, actor: UserId)
    : Task[Map[String, ComputeProfilesDataFromSpreadsheetResp]] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is starting import workflow for ${params.firmId}")
      _ <- fundDataPermissionService.validateUserHasRole(params.firmId, actor)
      _ <- ZIOUtils.validate(params.profiles.forall(_.investmentEntityId.firmId == params.firmId)) {
        FundDataValidationError(s"All profile should be in firm ${params.firmId}")
      }
      _ <- FundDataValidationUtils.convertError(
        FundDataValidationUtils.maxSize("profiles", params.profiles.toList, 5)
      )

      computedProfiles <- ZIOUtils.foreachParN(4)(params.profiles) { profile =>
        for {
          computedProfile <- computeProfileDataFromSpreadsheet(profile, actor)
        } yield profile.frontendTrackingId -> computedProfile
      }
    } yield computedProfiles.toMap
  }

  private[funddata] def computeProfileDataFromSpreadsheet(profileInfo: ProfileInfo, actor: UserId) = {
    for {
      _ <- ZIO.logInfo(s"$actor compute profile from spreadsheet data for ${profileInfo.investmentEntityId}")
      (formVersionId, profileData) <- FDBRecordDatabase.transact(
        InvestmentEntityProfileStoreProvider.Production,
        FormVersionDataStoreProvider.Production
      ) { (profileStore, dataStore) =>
        val ops = InvestmentEntityProfileStoreOperations(profileStore, dataStore)
        for {
          profileModel <- ops.getProfile(profileInfo.investmentEntityId)
          profileData <- ops.dataOps.get(profileModel.profileFormDataId)
        } yield (profileModel.profileFormId, profileData)
      }
      profileFormData <- formService
        .getForm(
          formVersionId.parent,
          Some(formVersionId),
          actor,
          shouldCheckPermission = false
        )
        .map(_.formData)
      profileForm = profileFormData.form
      importedProfileData <- profileImportExportService.importProfileData(
        firmId = profileInfo.investmentEntityId.parent.parent,
        profileForm = profileForm,
        profileDataOpt = Some(profileData),
        templateMappingVersionIdOpt = Some(profileInfo.formTemplateMappingVersionId),
        importData = profileInfo.data,
        executeMode = DataTransferEngine.ExecuteMode.Default
      )
      formVersionDataId <- formDataUserTempService.createUserTempFormDataInternal(
        owner = actor,
        formVersionId = formVersionId,
        formData = importedProfileData
      )
      coverage <- ZIO.attempt(FormDataStatsUtils.calculateFormCoverage(profileForm, importedProfileData))
      completionStatus <- ZIO.attempt(FormDataStatsUtils.calculateFormCompletion(profileForm, importedProfileData))
      unresolvedConflicts <- ZIO.attemptBlocking {
        FormDataComparison(
          form = profileFormData,
          computedFormState = importedProfileData,
          previousFormState = profileData
        ).conflictedMatchings.length
      }
    } yield ComputeProfilesDataFromSpreadsheetResp(
      investmentEntityId = profileInfo.investmentEntityId,
      frontendTrackingId = profileInfo.frontendTrackingId,
      formCoverage = coverage,
      visibleNonEmptyFieldCount = completionStatus.visibleNonEmptyFieldCount,
      hiddenNonEmptyFieldCount = completionStatus.hiddenNonEmptyFieldCount,
      formVersionDataId = formVersionDataId,
      unresolvedConflicts = unresolvedConflicts
    )
  }

  def getComputedProfileDataFromSpreadsheet(params: GetComputedProfileDataFromSpreadsheetParams, actor: UserId)
    : Task[GaiaState] = {
    for {
      _ <- ZIO.logInfo(s"$actor get preview profile form data")
      _ <- batchActionService.validateAccessValidator(params.batchActionItemId.parent, actor)
      batchItemModel <- batchActionService.getBatchActionItemModelUnsafe(params.batchActionItemId)
      computeRespOpt <- batchItemModel.status match {
        case succeeded: BatchActionItemStatusSucceeded =>
          ZIO.succeed(succeeded.data.flatMap(_.as[ComputeProfilesDataFromSpreadsheetResp].toOption))
        case _ =>
          ZIO.fail(FundDataValidationError(s"Batch action item ${params.batchActionItemId} is not in succeeded status"))
      }
      computeResp <- ZIOUtils.fromOption(
        computeRespOpt,
        FundDataValidationError(s"Batch action item ${params.batchActionItemId} is not a profile computing item")
      )
      formData <- FDBRecordDatabase.transact(FormVersionDataStoreOperations.Production) {
        _.get(computeResp.formVersionDataId)
      }
    } yield formData
  }

  def importProfilesDataFromSpreadsheet(params: ImportProfilesDataFromSpreadsheetParams, actor: UserId)
    : Task[BatchActionId] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is starting import workflow for ${params.firmId}")
      _ <- fundDataPermissionService.validateUserHasRole(params.firmId, actor)
      _ <- ZIOUtils.validate(params.profiles.forall(_.investmentEntityId.parent.parent == params.firmId)) {
        FundDataValidationError(s"All profile should be in firm ${params.firmId}")
      }
      importProfileInfos <- ZIOUtils.foreachParN(8)(params.profiles) { profile =>
        for {
          (formDataVersionId, isManuallyResolved) <- profile.profileFormSource match {
            case ProfileFormSource.FromComputed(formDataId) =>
              ZIO.succeed(formDataId -> false)
            case ProfileFormSource.ManuallyResolved(gaiaState) =>
              // save gaiaState to a user temporary storage
              for {
                profileFormId <- FDBRecordDatabase.transact(
                  InvestmentEntityProfileStoreProvider.Production,
                  FormVersionDataStoreProvider.Production
                ) { (profileStore, dataStore) =>
                  InvestmentEntityProfileStoreOperations(profileStore, dataStore)
                    .getProfile(profile.investmentEntityId)
                    .map(_.profileFormId)
                }
                formDataId <- formDataUserTempService.createUserTempFormDataInternal(
                  owner = actor,
                  formVersionId = profileFormId,
                  formData = gaiaState
                )
              } yield formDataId -> true
          }
        } yield ImportProfileInfo(
          investmentEntityId = profile.investmentEntityId,
          investmentEntityNameOrCustomId = profile.investmentEntityNameOrCustomId,
          matchedInvestmentEntityName = profile.matchedInvestmentEntityName,
          formVersionDataId = formDataVersionId,
          visibleNonEmptyFieldCount = profile.visibleNonEmptyFieldCount,
          hiddenNonEmptyFieldCount = profile.hiddenNonEmptyFieldCount,
          isManuallyResolved = isManuallyResolved
        )
      }
      batchActionId <- batchActionService.startBatchActionInternal(
        params.firmId,
        actor,
        actionType = BatchActionType.FundDataImportProfilesFromSpreadsheet,
        batchActionItemsData = importProfileInfos.map(_.asJson).toList,
        frontendTracking = BatchActionFrontendTracking.ACTOR_TRACKING,
        startWorkflow = workflowParams => {
          FundDataImportProfilesFromSpreadsheetWorkflowImpl.instance
            .getWorkflowStub()
            .provideEnvironment(temporalEnvironment.workflowClient)
            .flatMap(workflowStub => ZWorkflowStub.start(workflowStub.execute(workflowParams)))
        }
      )

    } yield batchActionId
  }

  private[funddata] def importProfileDataFromSpreadsheet(
    investmentEntityId: FundDataInvestmentEntityId,
    formVersionDataId: FormVersionDataId,
    isManuallyResolved: Boolean,
    actor: UserId
  ): Task[ImportProfilesDataFromSpreadsheetParams.SingleImportProfileResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor import profile data from spreadsheet by $investmentEntityId")
      _ <- fundDataPermissionService.validateUserHasRole(investmentEntityId.parent.parent, actor)
      toBeUpdatedProfileData <- FDBRecordDatabase.transact(FormVersionDataStoreOperations.Production) {
        _.get(formVersionDataId)
      }
      hasUnresolvedConflicts <-
        if (isManuallyResolved) {
          ZIO.succeed(false)
        } else {
          for {
            (formVersionId, profileData) <- FDBRecordDatabase.transact(
              InvestmentEntityProfileStoreProvider.Production,
              FormVersionDataStoreProvider.Production
            ) { (profileStore, dataStore) =>
              val ops = InvestmentEntityProfileStoreOperations(profileStore, dataStore)
              for {
                profileModel <- ops.getProfile(investmentEntityId)
                profileData <- ops.dataOps.get(profileModel.profileFormDataId)
              } yield (profileModel.profileFormId, profileData)
            }
            profileFormData <- formService
              .getForm(
                formVersionId.parent,
                Some(formVersionId),
                actor,
                shouldCheckPermission = false
              )
              .map(_.formData)
            _ = profileFormData.form
            hasUnresolvedConflicts = FormDataComparison(
              form = profileFormData,
              computedFormState = toBeUpdatedProfileData,
              previousFormState = profileData
            ).conflictedMatchings.nonEmpty
          } yield hasUnresolvedConflicts
        }
      profileConflictIdOpt <-
        if (hasUnresolvedConflicts) {
          fundDataProfileConflictService
            .createProfileConflictUnsafe(
              investmentEntityId = investmentEntityId,
              subscriptionId = None,
              actor = actor,
              importSource = FundDataProfileConflictSourceImportFromSpreadsheet(formVersionDataId)
            )
            .map(Some(_))
        } else {
          updateProfileData(
            investmentEntityId,
            profileData = toBeUpdatedProfileData,
            profileUpdate = ProfileUpdateImportFromSpreadsheet(
              actor,
              at = Some(Instant.now)
            ),
            actor
          ).as(Option.empty[FundDataProfileConflictId])
        }
      _ <- formDataUserTempService.deleteUserTempFormDataInternal(
        owner = actor,
        formDataId = formVersionDataId,
        keepFormDataForInternalUsage = hasUnresolvedConflicts
      )
    } yield ImportProfilesDataFromSpreadsheetParams.SingleImportProfileResponse(
      profileConflictIdOpt = profileConflictIdOpt
    )
  }

  private[funddata] def computeProfileDataAppendWithSubscriptionDataInternal(
    investmentEntityId: FundDataInvestmentEntityId,
    fundSubOrderId: FundSubLpId,
    actor: UserId
  ): Task[ComputedProfileDataAppendWithSubscriptionData] = {
    val firmId = investmentEntityId.parent.parent
    for {
      (profileFormId, profileData) <- FDBRecordDatabase.transact(
        InvestmentEntityProfileStoreProvider.Production,
        FormVersionDataStoreProvider.Production
      ) { (profileStore, dataStore) =>
        val ops = InvestmentEntityProfileStoreOperations(profileStore, dataStore)
        for {
          profileModel <- ops.getProfile(investmentEntityId)
          profileData <- ops.dataOps.get(profileModel.profileFormDataId)
        } yield (profileModel.profileFormId, profileData)
      }
      subscriptionData <- fundSubFormService.getFormData(fundSubOrderId)
      lpGaiaFormData <- ZIOUtils.optionToTask(
        subscriptionData.state.toOption,
        GeneralServiceException("Only support compute subscription data append with profile data for new form")
      )
      templateMappingOpt <- fundDataFirmProfileService.getActiveFormToFormTemplateMapping(
        firmId,
        formVersionId = lpGaiaFormData.formId.parent,
        mappingType = MappingType.FormToProfile,
        actor
      )
      transferStrategy <- templateMappingOpt
        .flatMap(_.latestMappingVersionOpt)
        .fold(
          ZIO
            .logInfo(
              s"Compute profile data $investmentEntityId from subscription $fundSubOrderId using ASA mapping"
            )
            .as(DataTransferStrategy.ASA)
        ) { templateMappingVersion =>
          ZIO
            .logInfo(
              s"Compute profile data $investmentEntityId from subscription $fundSubOrderId using template mapping ${templateMappingVersion.id}"
            )
            .as(DataTransferStrategy.TemplateMapping(templateMappingVersion.id))
        }
      (_, _, computedData) <- transferFormToFormData(
        sourceFormVersionId = lpGaiaFormData.formId.parent,
        targetFormVersionId = profileFormId,
        transferStrategy = transferStrategy,
        mappingType = FormMappingType.Import,
        sourceData = lpGaiaFormData.data,
        initialTargetDataOpt = Some(profileData),
        actor = actor
      )
    } yield ComputedProfileDataAppendWithSubscriptionData(
      fundSubOrderId,
      profileFormId,
      profileData,
      convertedProfileData = computedData
    )
  }

  def computeProfileFormAndDataFromConflict(
    params: ComputeProfileDataFromConflictParams,
    actor: UserId
  ): Task[ComputeProfileDataFromConflictResp] = {
    val investmentEntityId = params.investmentEntityId
    for {
      _ <- ZIO.logInfo(s"$actor compute profile data of $investmentEntityId from conflict ${params.conflictId}")
      _ <- fundDataPermissionService.validateUserCanViewProfile(params.investmentEntityId, actor)
      profileConflictModel <- FDBRecordDatabase.transact(FundDataProfileConflictStoreOperations.Production)(
        _.getConflict(params.conflictId)
      )
      (formVersionId, profileData) <- FDBRecordDatabase.transact(
        InvestmentEntityProfileStoreProvider.Production,
        FormVersionDataStoreProvider.Production
      ) { (profileStore, dataStore) =>
        val ops = InvestmentEntityProfileStoreOperations(profileStore, dataStore)
        for {
          profileModel <- ops.getProfile(investmentEntityId)
          profileData <- ops.dataOps.get(profileModel.profileFormDataId)
        } yield (profileModel.profileFormId, profileData)
      }
      resp <- profileConflictModel.source match {
        case s: FundDataProfileConflictSourceImportFromSpreadsheet =>
          for {
            toBeUpdatedProfileData <- FDBRecordDatabase.transact(FormVersionDataStoreOperations.Production) {
              _.get(s.formVersionDataId)
            }
            profileFormData <- formService
              .getForm(
                formVersionId.parent,
                Some(formVersionId),
                actor,
                shouldCheckPermission = false
              )
              .map(_.formData)
          } yield ComputeProfileDataFromConflictResp(
            profileFormData,
            profileData,
            toBeUpdatedProfileData,
            formVersionId
          )
        case s: FundDataProfileConflictSourceAutoSyncFromFund =>
          for {
            resp <- computeProfileFormAndDataFromLinkedSubscriptionOrder(
              params = ComputeProfileDataFromLinkedSubscriptionOrderParams(
                params.investmentEntityId,
                s.subscriptionId
              ),
              actor = actor
            )
          } yield ComputeProfileDataFromConflictResp(
            resp.profileForm,
            resp.profileData,
            resp.convertedProfileData,
            formVersionId
          )
        case s: FundDataProfileConflictSourceImportFromFund =>
          for {
            resp <- computeProfileFormAndDataFromLinkedSubscriptionOrder(
              params = ComputeProfileDataFromLinkedSubscriptionOrderParams(
                params.investmentEntityId,
                s.subscriptionId
              ),
              actor = actor
            )
          } yield ComputeProfileDataFromConflictResp(
            resp.profileForm,
            resp.profileData,
            resp.convertedProfileData,
            formVersionId
          )
        case _ => ZIO.fail(FundDataValidationError(s"Invalid conflict ${params.conflictId}"))
      }
    } yield resp
  }

  def computeProfileFormAndDataFromLinkedSubscriptionOrder(
    params: ComputeProfileDataFromLinkedSubscriptionOrderParams,
    actor: UserId
  ): Task[ComputeProfileDataFromLinkedSubscriptionOrderResponse] = {
    val investmentEntityId = params.investmentEntityId
    val linkedSubscriptionId = params.linkedSubscriptionId
    val firmId = params.investmentEntityId.parent.parent
    for {
      _ <- ZIO.logInfo(s"$actor compute profile data of $investmentEntityId from linked order $linkedSubscriptionId")
      _ <- fundDataPermissionService.validateUserCanViewProfile(params.investmentEntityId, actor)
      _ <- ZIOUtils.validate(linkedSubscriptionId.parent.parent == firmId) {
        FundDataValidationError(s"Linked subscription and investment entity should both in firm $firmId")
      }

      subscriptionModel <- FDBRecordDatabase.transact(FundDataFundSubscriptionStoreOperations.Production)(
        _.getSubscription(linkedSubscriptionId)
      )
      linkedFundSubOrderId <- ZIOUtils.fromOption(
        subscriptionModel.linkedFundSubOrderId,
        GeneralServiceException(s"Subscription $linkedSubscriptionId does not link to any fund sub order")
      )
      _ <- ZIOUtils.validate(subscriptionModel.linkedInvestmentEntityId.contains(investmentEntityId))(
        GeneralServiceException(
          s"Subscription $linkedSubscriptionId does not link to investment entity $investmentEntityId"
        )
      )
      _ <- ZIOUtils.validate(
        fundSubExternalIntegrationService.checkIfUserCanManageInvestor(linkedFundSubOrderId, actor)
      )(
        FundDataAuthorizationError(s"User $actor doesn't have access to subscription order $linkedFundSubOrderId")
      )

      computedProfileData <- computeProfileDataAppendWithSubscriptionDataInternal(
        investmentEntityId,
        linkedFundSubOrderId,
        actor
      )

      profileForm <- formService
        .getForm(
          computedProfileData.profileFormId.parent,
          Option(computedProfileData.profileFormId),
          actor,
          shouldCheckPermission = false
        )
        .map(_.formData)
    } yield ComputeProfileDataFromLinkedSubscriptionOrderResponse(
      profileForm = profileForm,
      profileData = computedProfileData.profileData,
      convertedProfileData = computedProfileData.convertedProfileData
    )
  }

  def importProfileDataFromLinkedSubscriptionOrder(
    params: ImportProfileDataFromLinkedSubscriptionOrderParams,
    actor: UserId
  ): Task[Unit] = {
    val investmentEntityId = params.investmentEntityId
    val linkedSubscriptionId = params.linkedSubscriptionId
    val firmId = investmentEntityId.parent.parent
    for {
      _ <- ZIO.logInfo(s"$actor import profile data of $investmentEntityId from linked order $linkedSubscriptionId")
      _ <- fundDataPermissionService.validateUserCanEditProfile(params.investmentEntityId, actor)
      _ <- ZIOUtils.validate(linkedSubscriptionId.parent.parent == firmId) {
        FundDataValidationError(s"Linked subscription and investment entity should both in firm $firmId")
      }
      subscriptionModel <- FDBRecordDatabase.transact(FundDataFundSubscriptionStoreOperations.Production)(
        _.getSubscription(
          linkedSubscriptionId
        )
      )
      linkedFundSubOrderId <- ZIOUtils.fromOption(
        subscriptionModel.linkedFundSubOrderId,
        GeneralServiceException(s"Subscription $linkedSubscriptionId does not link to any fund sub order")
      )
      fundName <- greylinDataService
        .run(FundSubscriptionOperations.get(linkedFundSubOrderId.parent))
        .map(_.name)
      _ <- updateProfileData(
        investmentEntityId,
        profileData = params.resolvedProfileData,
        profileUpdate = ProfileUpdateImportSubscription(
          actor,
          at = Some(Instant.now),
          linkedSubscriptionId,
          linkedFundSubOrderId,
          fundName,
          params.importMethod match {
            case ImportProfileDataFromLinkedSubscriptionOrderParams.ManualImport =>
              ProfileUpdateImportSubscriptionMethod.MANUAL_IMPORT
            case ImportProfileDataFromLinkedSubscriptionOrderParams.AutoSync =>
              ProfileUpdateImportSubscriptionMethod.AUTO_SYNC
            case ImportProfileDataFromLinkedSubscriptionOrderParams.ManualSync =>
              ProfileUpdateImportSubscriptionMethod.MANUAL_SYNC
          }
        ),
        actor
      )
    } yield ()
  }

  def updateProfileDataFromPublicAPI(
    investmentEntityId: FundDataInvestmentEntityId,
    templateIdOpt: Option[FormTemplateMappingVersionId],
    data: Map[String, String],
    actor: UserId
  ): Task[GaiaState] = {
    val firmId = investmentEntityId.parent.parent
    for {
      (profileFormId, profileFormData) <- FDBRecordDatabase.transact(
        InvestmentEntityProfileStoreProvider.Production,
        FormVersionDataStoreProvider.Production
      ) { (profileStore, dataStore) =>
        val ops = InvestmentEntityProfileStoreOperations(profileStore, dataStore)
        for {
          profileModel <- ops.getProfile(investmentEntityId)
          profileFormData <- ops.dataOps.get(profileModel.profileFormDataId)
        } yield profileModel.profileFormId -> profileFormData
      }
      profileForm <- formService
        .getForm(
          profileFormId.parent,
          Option(profileFormId),
          actor,
          shouldCheckPermission = false
        )
        .map(_.formData.form)
      importedProfileData <- profileImportExportService.importProfileData(
        firmId = firmId,
        profileForm = profileForm,
        profileDataOpt = Some(profileFormData),
        templateMappingVersionIdOpt = templateIdOpt,
        importData = data,
        executeMode = DataTransferEngine.ExecuteMode.AutoClearUnsetOutput
      )
      _ <- updateProfileData(
        investmentEntityId,
        profileData = importedProfileData,
        profileUpdate = ProfileUpdatePublicAPI(actor, at = Some(Instant.now)),
        actor
      )
    } yield importedProfileData
  }

  private[investmententity] def mergeProfileWhenMergingInvestmentEntity(
    srcInvestmentEntityId: FundDataInvestmentEntityId,
    destInvestmentEntityId: FundDataInvestmentEntityId,
    profileConflictResolveMethod: MergeInvestmentEntitiesParams.ProfileConflictResolveMethod,
    actor: UserId
  ) = {
    for {
      resolvedGaiaState <- profileConflictResolveMethod match {
        case ProfileConflictResolveMethod.Overwrite | ProfileConflictResolveMethod.Ignore =>
          for {
            srcProfile <- getInvestmentEntityProfile(srcInvestmentEntityId, actor)
            srcData = srcProfile.profileData.defaultStateMap
              .filter { case (k, v) =>
                !v.isHidden && srcProfile.profileForm.form.defaultSchema.visibleBySetting.contains(
                  k
                ) && !v.hasEmptyValueIncludingRepeatable
              }
              .view
              .mapValues(_.getValue)
              .toMap
            srcConvertedData = Seq(Form.DefaultNamespace -> srcData.map { case (k, v) =>
              k -> v
            })
            destProfile <- getInvestmentEntityProfile(destInvestmentEntityId, actor)
            convertedFormState <- formMappingService
              .applyDataMapping(
                srcProfile.profileForm.form,
                srcConvertedData,
                Some(destProfile.profileData)
              )
              .map(_.gaiaState)
            formDataComparison = FormDataComparison(
              form = destProfile.profileForm,
              computedFormState = convertedFormState,
              previousFormState = destProfile.profileData
            )
          } yield profileConflictResolveMethod match {
            case ProfileConflictResolveMethod.Overwrite => formDataComparison.computedFormState
            case ProfileConflictResolveMethod.Ignore    => formDataComparison.computedStateIgnoreConflicts
            case _                                      => GaiaState.empty
          }
        case ProfileConflictResolveMethod.ManualResolve(gaiaState) => ZIO.succeed(gaiaState)
      }
      sourceInvestmentEntity <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.get(srcInvestmentEntityId)
      )
      _ <- updateProfileData(
        investmentEntityId = destInvestmentEntityId,
        profileData = resolvedGaiaState,
        profileUpdate = ProfileUpdateMergeInvestmentEntities(
          actor = actor,
          at = Some(Instant.now),
          srcInvestmentEntityId = srcInvestmentEntityId,
          srcInvestmentEntityName = sourceInvestmentEntity.name
        ),
        actor = actor
      )
    } yield ()
  }

  def clearProfileData(
    investmentEntityId: FundDataInvestmentEntityId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor clear profile data of investment entity $investmentEntityId")
      _ <- fundDataPermissionService.validateUserCanEditProfile(investmentEntityId, actor)

      profileFormId <- FDBRecordDatabase.transact(
        InvestmentEntityProfileStoreProvider.Production,
        FormVersionDataStoreProvider.Production
      ) { (profileStore, dataStore) =>
        InvestmentEntityProfileStoreOperations(profileStore, dataStore)
          .getProfile(investmentEntityId)
          .map(_.profileFormId)
      }

      profileForm <- formService
        .getForm(
          profileFormId.parent,
          Option(profileFormId),
          actor,
          shouldCheckPermission = false
        )
        .map(_.formData)

      emptyProfileFormData <- ZIOUtils.fromOption(
        GaiaEngine
          .make(
            profileForm.form,
            EngineConfiguration.default,
            EngineContext.default
          )
          .flatMap(_.replay(List.empty).map(_._1))
          .toOption,
        GeneralServiceException(s"Failed to setup empty profile data for form $profileFormId of $investmentEntityId")
      )
      _ <- updateProfileData(
        investmentEntityId,
        profileData = emptyProfileFormData,
        profileUpdate = ProfileUpdateClear(actor, at = Some(Instant.now)),
        actor
      )
    } yield ()
  }

  def getInvestmentEntitiesProfileTemplate(
    firmId: FundDataFirmId,
    actor: UserId
  ): Task[GetInvestmentEntitiesProfileTemplateResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor get investment entities' profile template of $firmId")
      _ <- fundDataPermissionService.validateAnduinAdmin(actor)

      entitiesProfileModels <- FDBRecordDatabase.transact(
        InvestmentEntityProfileStoreProvider.Production,
        FormVersionDataStoreProvider.Production
      ) { (profileStore, dataStore) =>
        InvestmentEntityProfileStoreOperations(profileStore, dataStore).get(firmId)
      }
      entityNames <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.get(firmId).map(_.map(model => model.investmentEntityId -> model.name).toMap)
      )
    } yield GetInvestmentEntitiesProfileTemplateResponse(
      investmentEntities = entitiesProfileModels.map { profileModel =>
        FundDataInvestmentEntityProfileTemplate(
          investmentEntityId = profileModel.investmentEntityId,
          investmentEntityName = entityNames.getOrElse(profileModel.investmentEntityId, ""),
          profileFormId = profileModel.profileFormId
        )
      }
    )
  }

  def compareProfileDataWithFormVersion(
    params: CompareProfileDataWithFormVersionParams,
    actor: UserId
  ): Task[CompareProfileDataWithFormVersionResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"$actor compare profile data with new form version for investment entities ${params.investmentEntityIds}"
      )
      _ <- fundDataPermissionService.validateAnduinAdmin(actor)
      _ <- ZIOUtils.uniqueSeqToTask(
        params.investmentEntityIds.map(_.parent.parent),
        FundDataValidationError("All profiles should be in the same firm")
      )
      _ <- Validation
        .validate(
          FundDataValidationUtils.maxSize("investment_entities", params.investmentEntityIds, maxSize = 5),
          FundDataValidationUtils.minSize("investment_entities", params.investmentEntityIds)
        )
        .toZIO
        .mapError(FundDataValidationError(_))

      targetedFormVersion <- formService
        .getForm(
          params.formVersionId.parent,
          Some(params.formVersionId),
          actor,
          shouldCheckPermission = false
        )
        .map(_.formData)
      diffResults <- ZIOUtils.foreachParN(4)(params.investmentEntityIds) { investmentEntityId =>
        val task = for {
          (profileData, computedProfileData) <- computeProfileDataApplyNewFormVersion(
            investmentEntityId,
            targetedFormVersion.form
          )
          diffResult = FormValueAnonymizedDiff.diffForm(
            profileData.defaultStateMap,
            computedProfileData.defaultStateMap
          )
        } yield investmentEntityId -> Right[String, List[FormValueAnonymizedDiff.FieldDiffResult]](diffResult)
        task.catchAll { err =>
          ZIO.succeed(investmentEntityId -> Left[String, List[FormValueAnonymizedDiff.FieldDiffResult]](err.getMessage))
        }
      }
    } yield CompareProfileDataWithFormVersionResponse(diffResults.toMap)
  }

  def updateProfileDataToFormVersion(
    params: UpdateProfileDataToFormVersionParams,
    actor: UserId
  ): Task[UpdateProfileDataToFormVersionResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"$actor update to data with new form version for investment entities ${params.investmentEntityIds}"
      )
      _ <- fundDataPermissionService.validateAnduinAdmin(actor)
      _ <- ZIOUtils.uniqueSeqToTask(
        params.investmentEntityIds.map(_.parent.parent),
        FundDataValidationError("All profiles should be in the same firm")
      )
      _ <- Validation
        .validate(
          FundDataValidationUtils.maxSize("investment_entities", params.investmentEntityIds, maxSize = 5),
          FundDataValidationUtils.minSize("investment_entities", params.investmentEntityIds)
        )
        .toZIO
        .mapError(FundDataValidationError(_))

      targetedFormVersion <- formService
        .getForm(
          params.formVersionId.parent,
          Some(params.formVersionId),
          actor,
          shouldCheckPermission = false
        )
        .map(_.formData)
      updateResults <- ZIO.foreach(params.investmentEntityIds) { investmentEntityId =>
        val task = for {
          (_, computedProfileData) <- computeProfileDataApplyNewFormVersion(
            investmentEntityId,
            targetedFormVersion.form
          )
          (curProfileModel, updatedProfileModel) <- FDBRecordDatabase.transact(
            InvestmentEntityProfileStoreProvider.Production,
            FormVersionDataStoreProvider.Production
          ) { (profileStore, dataStore) =>
            InvestmentEntityProfileStoreOperations(profileStore, dataStore).updateProfileDataToFormVersion(
              investmentEntityId,
              params.formVersionId,
              computedProfileData,
              actor
            )
          }
          investmentEntityModel <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
            _.get(investmentEntityId)
          )
          _ <- fundDataEventService.push(
            FundDataEvent(
              firmId = investmentEntityId.parent.parent,
              actor = actor,
              createdAt = Some(Instant.now),
              detail = UpdateInvestmentEntityProfileEvent(
                investmentEntityId = investmentEntityId,
                newProfileFormId = updatedProfileModel.profileFormId,
                oldProfileFormId = curProfileModel.profileFormId,
                newProfileFormDataId = updatedProfileModel.profileFormDataId,
                oldProfileFormDataId = curProfileModel.profileFormDataId,
                profileUpdate = ProfileUpdateUpgradeFormVersion(actor, Some(Instant.now)),
                customId = investmentEntityModel.customId
              )
            )
          )
        } yield investmentEntityId -> Right[String, Unit](())
        task.catchAll { err =>
          ZIO.succeed(investmentEntityId -> Left[String, Unit](err.getMessage))
        }
      }
    } yield UpdateProfileDataToFormVersionResponse(updateResults.toMap)
  }

  private def computeProfileDataApplyNewFormVersion(
    investmentEntityId: FundDataInvestmentEntityId,
    newFormVersion: Form
  ) = {
    for {
      profileData <- FDBRecordDatabase.transact(
        InvestmentEntityProfileStoreProvider.Production,
        FormVersionDataStoreProvider.Production
      ) { (profileStore, dataStore) =>
        val ops = InvestmentEntityProfileStoreOperations(profileStore, dataStore)
        for {
          profileModel <- ops.getProfile(investmentEntityId)
          profileData <- ops.dataOps.get(profileModel.profileFormDataId)
        } yield profileData
      }
      computedProfileDataOpt = for {
        engine <- GaiaEngine
          .make(
            newFormVersion,
            EngineConfiguration.default,
            EngineContext.default
          )
          .toOption
        result <- engine.replay(profileData.events.toList).toOption
      } yield result._1
      computedProfileData <- ZIOUtils.optionToTask(
        computedProfileDataOpt,
        GeneralServiceException(s"Failed to import events of $investmentEntityId to new form")
      )
    } yield profileData -> computedProfileData
  }

  def deleteProfileWhenDeleteInvestmentEntity(
    investmentEntityId: FundDataInvestmentEntityId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor delete profile when delete investment entity $investmentEntityId")
      _ <- FDBRecordDatabase.transact(
        InvestmentEntityProfileStoreProvider.Production,
        FormVersionDataStoreProvider.Production
      ) { (profileStore, dataStore) =>
        InvestmentEntityProfileStoreOperations(profileStore, dataStore).delete(investmentEntityId)
      }
      _ <- fundDataEventService.push(
        FundDataEvent(
          firmId = investmentEntityId.parent.parent,
          actor = actor,
          createdAt = Some(Instant.now),
          detail = DeleteInvestmentEntityProfileEvent(investmentEntityId)
        )
      )
    } yield ()
  }

  private def updateProfileData(
    investmentEntityId: FundDataInvestmentEntityId,
    profileData: GaiaState,
    profileUpdate: ProfileUpdate,
    actor: UserId
  ): Task[Boolean] = {
    val firmId = investmentEntityId.parent.parent
    for {
      (currProfileData, updatedProfileModel) <- FDBRecordDatabase.transact(
        InvestmentEntityProfileStoreProvider.Production,
        FormVersionDataStoreProvider.Production
      ) { (profileStore, dataStore) =>
        val ops = InvestmentEntityProfileStoreOperations(profileStore, dataStore)
        for {
          currProfileModel <- ops.getProfile(investmentEntityId)
          currProfileData <- ops.dataOps.get(currProfileModel.profileFormDataId)
          updatedProfileModel <- ops.updateProfileData(
            investmentEntityId,
            profileData,
            profileUpdate
          )
        } yield (currProfileData, updatedProfileModel)
      }
      events <- fundDataInvestmentEntityProfileHistoryService.addEventHistoryFromFormChange(
        actor = actor,
        investmentEntityId = investmentEntityId,
        formVersionId = updatedProfileModel.profileFormId,
        profileUpdate = profileUpdate,
        prevProfileState = currProfileData,
        updatedProfileState = profileData
      )
      hasFieldChange = events.nonEmpty
      investmentEntityModel <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.get(investmentEntityId)
      )
      _ <- fundDataEventService.push(
        FundDataEvent(
          firmId = firmId,
          actor = actor,
          createdAt = Some(Instant.now),
          detail = UpdateInvestmentEntityProfileEvent(
            investmentEntityId = investmentEntityId,
            newProfileFormId = updatedProfileModel.profileFormId,
            oldProfileFormId = updatedProfileModel.profileFormId,
            newProfileFormDataId = updatedProfileModel.profileFormDataId,
            oldProfileFormDataId = updatedProfileModel.profileFormDataId,
            profileUpdate = profileUpdate,
            customId = investmentEntityModel.customId,
            hasFieldChange = hasFieldChange
          )
        )
      )
    } yield hasFieldChange
  }

  private[funddata] def computeSubscriptionDataAppendWithProfileDataInternal(
    lpId: FundSubLpId | FundSubId,
    investmentEntityId: FundDataInvestmentEntityId,
    actor: UserId,
    useSubscriptionDataAsInitialData: Boolean
  ): Task[ComputedSubscriptionDataAppendWithProfileData] = {
    val firmId = investmentEntityId.parent.parent
    for {
      (profileModel, profileData, lastProfileUpdatedAt) <- FDBRecordDatabase.transact(
        InvestmentEntityProfileStoreProvider.Production,
        FormVersionDataStoreProvider.Production,
        InvestmentEntityProfileHistoryStoreProvider.Production
      ) { (profileStore, dataStore, profileHistoryStore) =>
        val ops = InvestmentEntityProfileStoreOperations(profileStore, dataStore)
        val profileHistoryOps = InvestmentEntityProfileHistoryStoreOperation(profileHistoryStore)
        for {
          profileModel <- ops.getProfile(investmentEntityId)
          profileData <- ops.dataOps.get(profileModel.profileFormDataId)
          (lastProfileHistoryEvents, _) <- profileHistoryOps.getEvents(profileModel.profileHistoryId, limit = 1)
        } yield (profileModel, profileData, lastProfileHistoryEvents.headOption.flatMap(_.timestamp))
      }
      (lpFormVersionId, lpFormDataOpt) <- lpId match {
        case lpId: FundSubLpId =>
          for {
            subscriptionData <- fundSubFormService.getFormData(lpId)
            lpGaiaFormData <- ZIOUtils.optionToTask(
              subscriptionData.state.toOption,
              GeneralServiceException("Only support compute subscription data append with profile data for new form")
            )
          } yield lpGaiaFormData.formId.parent -> Some(lpGaiaFormData.data)
        case fundSubId: FundSubId =>
          for {
            fundResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
              ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundSubId))
            }
            lpFormVersionId <- ZIOUtils.fromOption(
              fundResModel.formVersions.headOption.map(_.id),
              GeneralServiceException(s"Fund sub $fundSubId must has no form version setup")
            )
          } yield lpFormVersionId -> Option.empty[GaiaState]
      }
      templateMappingOpt <- fundDataFirmProfileService.getActiveFormToFormTemplateMapping(
        firmId,
        formVersionId = lpFormVersionId,
        mappingType = MappingType.ProfileToForm,
        actor
      )
      mappings <- templateMappingOpt
        .flatMap(_.latestMappingVersionOpt.map(_.id))
        .fold(
          ZIO.succeed(Set.empty[String])
        ) { mappingId =>
          for {
            (mappingType, mappingItems) <- FDBRecordDatabase.transact(FormTemplateMappingStoreOperations.Production) {
              ops =>
                for {
                  mappingType <- ops.modelOps.get(mappingId.parent).map(_.mappingType)
                  mappingItems <- ops.contentOps.get(mappingId).map(_.mappingItems)
                } yield (mappingType, mappingItems)
            }
          } yield {
            val defaultNamespace = mappingType match {
              case FormMappingType.Import => Form.ImportNamespace
              case FormMappingType.Export => Form.DefaultNamespace
            }
            val results = LogicExtractor.batchExtract(
              rules = mappingItems.zipWithIndex.map { case (item, index) =>
                FormRule(
                  value = item.logic,
                  name = s"mapping_item_$index",
                  defaultNamespace = defaultNamespace
                )
              },
              libs = Seq.empty,
              fieldSchemaInfos = Seq.empty
            )
            results.flatMap(_.map(_.outputs.keys.map(_.key).toSet).getOrElse(Seq.empty)).toSet
          }

        }
      transferStrategy <- templateMappingOpt
        .flatMap(_.latestMappingVersionOpt)
        .fold(
          ZIO
            .logInfo(
              s"Compute subscription data $lpId from profile data $investmentEntityId using ASA mapping"
            )
            .as(DataTransferStrategy.ASA)
        ) { templateMappingVersion =>
          ZIO
            .logInfo(
              s"Compute subscription data $lpId from profile data $investmentEntityId using template mapping ${templateMappingVersion.id}"
            )
            .as(DataTransferStrategy.TemplateMapping(templateMappingVersion.id))
        }
      (_, lpForm, computedData) <- transferFormToFormData(
        sourceFormVersionId = profileModel.profileFormId,
        targetFormVersionId = lpFormVersionId,
        transferStrategy = transferStrategy,
        mappingType = FormMappingType.Export,
        sourceData = profileData,
        initialTargetDataOpt = Option.when(useSubscriptionDataAsInitialData)(lpFormDataOpt).flatten,
        actor = actor
      )
    } yield ComputedSubscriptionDataAppendWithProfileData(
      subscriptionForm = lpForm,
      subscriptionStateOpt = lpFormDataOpt,
      computedState = computedData,
      lastProfileUpdatedAt = lastProfileUpdatedAt,
      isProfileEmpty = InvestmentEntityProfileModelUtils.isProfileEmpty(profileModel.profileUpdate),
      mappingFields = mappings
    )
  }

  private def transferFormToFormData(
    sourceFormVersionId: FormVersionId,
    targetFormVersionId: FormVersionId,
    transferStrategy: DataTransferStrategy,
    mappingType: FormMappingType,
    sourceData: GaiaState,
    initialTargetDataOpt: Option[GaiaState],
    actor: UserId
  ): Task[(FormData, FormData, GaiaState)] = {
    for {
      sourceForm <- formService
        .getForm(
          sourceFormVersionId.parent,
          Option(sourceFormVersionId),
          actor,
          shouldCheckPermission = false
        )
        .map(_.formData)
      targetForm <- formService
        .getForm(
          targetFormVersionId.parent,
          Option(targetFormVersionId),
          actor,
          shouldCheckPermission = false
        )
        .map(_.formData)
      (mappingItems, srcAsaMapping, targetAsaMapping, srcSapMapping, targetSapMapping) <- transferStrategy match {
        case DataTransferStrategy.ASA =>
          for {
            srcAsaMapping <- formService.getFormStandardAliasMapping(
              sourceFormVersionId,
              checkValidFormFieldsAndOptions = false
            )
            targetAsaMapping <- formService.getFormStandardAliasMapping(
              targetFormVersionId,
              checkValidFormFieldsAndOptions = false
            )
            srcSapMapping <- formSaProfileMappingService.queryFormVersionSaMapping(actor, sourceFormVersionId)
            targetSapMapping <- formSaProfileMappingService.queryFormVersionSaMapping(actor, targetFormVersionId)
          } yield (Seq.empty[MappingItem], srcAsaMapping, targetAsaMapping, srcSapMapping, targetSapMapping)
        case DataTransferStrategy.TemplateMapping(versionId) =>
          for {
            mappingItems <- FDBRecordDatabase.transact(FormTemplateMappingStoreOperations.Production)(
              _.contentOps.get(versionId).map(_.mappingItems)
            )
          } yield (
            mappingItems,
            Map.empty[String, String],
            Map.empty[String, String],
            Map.empty[String, String],
            Map.empty[String, String]
          )
      }
      result = DataTransferEngine.transferFormToForm(
        sourceForm = sourceForm.form,
        targetForm = targetForm.form,
        mappingItems = mappingItems,
        mappingType = mappingType,
        sourceData = sourceData,
        targetInitialStateOpt = initialTargetDataOpt,
        sourceAsaMapping = srcAsaMapping,
        targetAsaMapping = targetAsaMapping,
        sourceSapMapping = srcSapMapping,
        targetSapMapping = targetSapMapping,
        inputMode = DataTransferEngine.InputMode.IncludeAll,
        executeMode = DataTransferEngine.ExecuteMode.IgnoreEmptyOutput
      )
      formState <- result.fold(
        error =>
          ZIO.fail(
            GeneralServiceException(
              s"Transfer from $sourceFormVersionId to $targetFormVersionId with strategy $transferStrategy failed with $error"
            )
          ),
        result => ZIO.succeed(result.transferredData)
      )
    } yield (sourceForm, targetForm, formState)
  }

}

object FundDataInvestmentEntityProfileService {

  private[funddata] sealed trait DataTransferStrategy derives CanEqual

  object DataTransferStrategy {
    case object ASA extends DataTransferStrategy
    case class TemplateMapping(version: FormTemplateMappingVersionId) extends DataTransferStrategy
  }

  private[funddata] final case class ComputedProfileDataAppendWithSubscriptionData(
    subscriptionOrderId: FundSubLpId,
    profileFormId: FormVersionId,
    profileData: GaiaState,
    convertedProfileData: GaiaState
  )

  private[funddata] final case class ComputedSubscriptionDataAppendWithProfileData(
    subscriptionForm: FormData,
    subscriptionStateOpt: Option[GaiaState],
    computedState: GaiaState,
    lastProfileUpdatedAt: Option[Instant],
    isProfileEmpty: Boolean,
    mappingFields: Set[String]
  )

  private[funddata] final case class SubscriptionDataWithProfileData(
    subscriptionForm: FormData,
    subscriptionState: GaiaState,
    profileStateOpt: Option[GaiaState], // None if there is no profile exist
    lastProfileUpdatedAt: Option[Instant]
  )

  given toProfileUpdateInfo: Conversion[ProfileUpdate, ProfileUpdateInfo] = {
    case p: ProfileUpdateInitial               => ProfileUpdateInfo.Initial(p.at)
    case p: ProfileUpdateManualEdit            => ProfileUpdateInfo.ManualEdit(p.at)
    case p: ProfileUpdateImportSubscription    => ProfileUpdateInfo.ImportSubscription(p.subscriptionId, p.at)
    case p: ProfileUpdateImportFromSpreadsheet => ProfileUpdateInfo.ImportFromSpreadsheet(p.at)
    case p: ProfileUpdateClear                 => ProfileUpdateInfo.Clear(p.at)
    case p: ProfileUpdatePublicAPI             => ProfileUpdateInfo.UpdatePublicAPI(p.at)
    case p: ProfileUpdateEmpty                 => ProfileUpdateInfo.Empty(p.reason, p.at)
    case p: ProfileUpdateMergeInvestmentEntities =>
      ProfileUpdateInfo.MergeInvestmentEntities(p.srcInvestmentEntityId, p.at)
    case p: ProfileUpdateMoveInvestmentEntity => ProfileUpdateInfo.MoveInvestmentEntity(p.srcInvestmentEntityId, p.at)
    case _                                    => ProfileUpdateInfo.Empty("", at = None)
  }

}
