// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investmententity

import anduin.account.profile.UserProfileService
import anduin.batchaction.{BatchActionFrontendTracking, BatchActionService, BatchActionType}
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.documentcontent.csv.CsvUtils
import anduin.documentcontent.spreadsheet.FillSheetData.{FillSheetCell, FillSheetRow}
import anduin.documentcontent.spreadsheet.{FillSheetData, FillSpreadsheet, SpreadsheetUtils}
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{DefaultCluster, FDBOperations, FDBRecordDatabase}
import anduin.forms.service.{FormService, FormTemplateMappingService}
import anduin.forms.storage.FormVersionDataStoreProvider
import anduin.funddata.constant.Constant
import anduin.funddata.contact.FundDataContactRelationUtils
import anduin.funddata.endpoint.investmententity.*
import anduin.funddata.endpoint.investmententity.FundDataInvestmentEntity.FundNameAndAccessible
import anduin.funddata.endpoint.investmententity.ImportInvestmentEntitiesBySpreadsheetParams.ImportOption.*
import anduin.funddata.endpoint.investmententity.ImportInvestmentEntitiesBySpreadsheetParams.InvestmentEntityInfo
import anduin.funddata.endpoint.investmententity.MergeInvestmentEntitiesParams.ProfileConflictResolveMethod
import anduin.funddata.error.FundDataError.FundDataValidationError
import anduin.funddata.event.FundDataEventService
import anduin.funddata.firm.{FundDataFirmProfileStoreOperations, FundDataFirmStoreOperations}
import anduin.funddata.fund.subscription.{FundDataFundSubscriptionService, FundDataFundSubscriptionStoreOperations}
import anduin.funddata.fund.transaction.FundTransactionUtils
import anduin.funddata.fund.{FundDataFundQueryUtils, FundDataFundV2StoreOperations}
import anduin.funddata.group.FundDataGroupService
import anduin.funddata.investmententity.FundDataInvestmentEntityService.CheckDuplicatedCustomIdParams
import anduin.funddata.investmententity.assessment.FundDataInvestmentEntityAssessmentService
import anduin.funddata.investmententity.contact.FundDataInvestmentEntityContactService
import anduin.funddata.investmententity.document.{
  FundDataInvestmentEntityDocumentRequestService,
  FundDataInvestmentEntityDocumentService
}
import anduin.funddata.investmententity.profile.{
  FundDataInvestmentEntityProfileService,
  FundDataProfileConflictService,
  InvestmentEntityProfileStoreOperations,
  InvestmentEntityProfileStoreProvider
}
import anduin.funddata.investor.FundDataInvestorStoreOperations
import anduin.funddata.permission.FundDataRebacModel.ClientPermission
import anduin.funddata.permission.FundDataRebacModel.InvestmentEntityPermission.CanEdit
import anduin.funddata.permission.{FundDataPermissionService, FundDataRebacModel}
import anduin.funddata.portal.FundDataPortalService
import anduin.funddata.request.FundDataRequestService
import anduin.funddata.tag.FundDataTagService.{GenericTag, InvestorType, Jurisdiction, TagType}
import anduin.funddata.tag.{FundDataTagService, FundDataTagUtils}
import anduin.funddata.utils.AssessmentTagUtils.compareRiskLevelItemByColor
import anduin.funddata.validation.FundDataValidationUtils
import anduin.greylin.GreylinDataService
import anduin.greylin.operation.FundSubscriptionOperations
import anduin.id.batchaction.BatchActionId
import anduin.id.funddata.{FundDataFirmId, FundDataInvestmentEntityId, FundDataInvestorId}
import anduin.id.tag.TagItemId
import anduin.kafka.{KafkaFiber, KafkaService, KafkaSimpleConsumer}
import anduin.model.common.user.UserId
import anduin.model.document.DocumentStorageId
import anduin.model.id.FundDataInvestmentEntityIdFactory
import anduin.model.notichannel.FundDataNotificationChannels
import anduin.protobuf.funddata.event.*
import anduin.protobuf.funddata.investmententity.FundDataInvestmentEntityModel
import anduin.protobuf.funddata.investmententity.profile.*
import anduin.service.GeneralServiceException
import anduin.storageservice.common.FileContentOrigin
import anduin.tapir.endpoint.UpdateOptionOperator
import anduin.temporal.TemporalEnvironment
import anduin.user.UserService
import anduin.utils.{DateTimeUtils, SquantsUtils}
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils
import io.circe.syntax.*
import io.github.arainko.ducktape.*
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import sttp.model.MediaType
import zio.implicits.*
import zio.prelude.Validation
import zio.temporal.workflow.ZWorkflowStub
import zio.{Task, ZIO}
import java.time.Instant
import scala.annotation.unused

import anduin.funddata.endpoint.UpdateProperty
import anduin.funddata.endpoint.investmententity.note.{
  DeleteInvestmentEntityNoteParams,
  UpdateInvestmentEntityNoteParams
}
import anduin.funddata.fund.investment.FundInvestmentStoreOperations
import anduin.funddata.investmententity.note.FundDataInvestmentEntityNoteService
import anduin.funddata.note.FundDataNoteUtils
import anduin.utils.endpoint.DashboardPaginationData

final case class FundDataInvestmentEntityService(
  fundDataPermissionService: FundDataPermissionService,
  fundDataEventService: FundDataEventService,
  fundDataFundSubscriptionService: FundDataFundSubscriptionService,
  fundDataInvestmentEntityAssessmentService: FundDataInvestmentEntityAssessmentService,
  fundDataInvestmentEntityDocumentService: FundDataInvestmentEntityDocumentService,
  fundDataInvestmentEntityDocumentRequestService: FundDataInvestmentEntityDocumentRequestService,
  fundDataInvestmentEntityProfileService: FundDataInvestmentEntityProfileService,
  fundDataInvestmentEntityContactService: FundDataInvestmentEntityContactService,
  fundDataInvestmentEntityNoteService: FundDataInvestmentEntityNoteService,
  fundDataGroupService: FundDataGroupService,
  batchActionService: BatchActionService,
  temporalEnvironment: TemporalEnvironment,
  fundDataTagService: FundDataTagService,
  fundDataRequestService: FundDataRequestService,
  fundDataProfileConflictService: FundDataProfileConflictService,
  natsNotificationService: NatsNotificationService,
  kafkaService: KafkaService,
  formService: FormService,
  formTemplateMappingService: FormTemplateMappingService,
  fundDataPortalService: FundDataPortalService
)(
  using greylinDataService: GreylinDataService,
  userProfileService: UserProfileService,
  userService: UserService,
  fileService: FileService
) extends KafkaFiber {

  def checkDuplicatedIEData(
    params: CheckDuplicatedIEDataParams,
    actor: UserId
  ): Task[CheckDuplicatedIEDataResponse] = {
    val firmId = params.firmId
    for {
      _ <- ZIO.logInfo(s"$actor check duplicated investment entity data for $firmId")
      _ <- fundDataPermissionService.validateUserHasRole(firmId, actor)

      ieModels <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.get(firmId)
      )
      existingNames = ieModels.map(_.name).filter(_.nonEmpty).toSet
      existingCustomIds = ieModels.map(_.customId).filter(_.nonEmpty).toSet
    } yield CheckDuplicatedIEDataResponse(
      nameDuplicated = params.names.map(name => name -> existingNames.contains(name)).toMap,
      customIdDuplicated = params.customIds.map(id => id -> existingCustomIds.contains(id)).toMap
    )
  }

  def createInvestmentEntity(
    rawParams: CreateInvestmentEntityParams,
    actor: UserId
  ): Task[FundDataInvestmentEntityId] = {
    val params = rawParams.trim
    for {
      _ <- ZIO.logInfo(s"$actor creates investment entity for ${params.investorId}")
      _ <- fundDataPermissionService.validateUserCanCreateInvestmentEntity(params.investorId, actor)

      newInvestmentEntityId <- createInvestmentEntityUnsafe(
        params.investorId,
        params.name,
        params.customId,
        params.jurisdictionType,
        params.jurisdictionOtherType,
        params.investorType,
        params.investorOtherType,
        params.riskLevelOpt,
        noteOpt = None,
        actor = actor,
        source = ManuallyCreateInvestmentEntity()
      )
//      _ <- fundDataPortalService.addInvestmentEntities(Seq(newInvestmentEntityId), actor)
    } yield newInvestmentEntityId
  }

  def createInvestmentEntityUnsafe(
    investorId: FundDataInvestorId,
    name: String,
    customId: String,
    jurisdictionType: Option[TagItemId],
    jurisdictionOtherType: String,
    investorType: Option[TagItemId],
    investorOtherType: String,
    riskLevelOpt: Option[TagItemId],
    noteOpt: Option[String] = None,
    actor: UserId,
    source: CreateInvestmentEntitySource
  ): Task[FundDataInvestmentEntityId] = {
    val firmId = investorId.parent
    for {
      _ <- validateInvestorExist(investorId)
      _ <- checkDuplicatedCustomIds(investorId.parent, List(CheckDuplicatedCustomIdParams(customId)))
      _ <- Validation
        .validate(
          FundDataValidationUtils.nonEmpty("name", name),
          FundDataValidationUtils.maxLength("name", name, Constant.InvestmentEntity.maxNameLength),
          FundDataValidationUtils.when(customId.nonEmpty)(FundDataValidationUtils.isValidCustomId("customId", customId)),
          FundDataValidationUtils.maxLength(
            "jurisdiction",
            jurisdictionOtherType,
            Constant.InvestmentEntity.maxJurisdictionLength
          ),
          FundDataValidationUtils.maxLength("type", investorOtherType, Constant.InvestmentEntity.maxTypeLength)
        )
        .toZIO
        .mapError(FundDataValidationError(_))
      _ <- ZIOUtils.traverseOption(jurisdictionType) { jurisdictionType =>
        fundDataTagService.validateTagItemExists(investorId.parent, List(jurisdictionType))(
          using FundDataTagService.Jurisdiction
        )
      }
      _ <- ZIOUtils.traverseOption(investorType) { investorType =>
        fundDataTagService.validateTagItemExists(investorId.parent, List(investorType))(
          using FundDataTagService.InvestorType
        )
      }
      _ <- ZIOUtils.traverseOption(riskLevelOpt) { riskLevel =>
        fundDataTagService.validateTagItemExists(investorId.parent, List(riskLevel))(
          using FundDataTagService.RiskLevelAssessment
        )
      }

      newInvestmentEntityId <- ZIO.attempt(FundDataInvestmentEntityIdFactory.unsafeRandomId(investorId))

      _ <- fundDataGroupService.getAllGroupIdsInternal(investorId.parent)
      _ <- fundDataInvestmentEntityProfileService.setupInvestmentEntityProfile(
        investmentEntityId = newInvestmentEntityId,
        actor
      )
      _ <- fundDataTagService.createObjectTagsRecord(
        firmId,
        newInvestmentEntityId,
        jurisdictionType.toList
      )(
        using Jurisdiction
      )
      _ <- fundDataTagService.createObjectTagsRecord(
        firmId,
        newInvestmentEntityId,
        investorType.toList
      )(
        using InvestorType
      )
      _ <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.create(
          FundDataInvestmentEntityModel(
            investmentEntityId = newInvestmentEntityId,
            name,
            customId,
            createdAt = Some(Instant.now),
            createdBy = actor,
            jurisdictionOtherType = jurisdictionOtherType,
            investorOtherType = investorOtherType
          )
        )
      )
      _ <- ZIO.when(riskLevelOpt.nonEmpty) {
        for {
          userInfo <- userProfileService.getUserInfo(actor)
          newAssessmentId <- fundDataInvestmentEntityAssessmentService.createAssessment(
            rawParams = CreateAssessmentParams(
              investmentEntityId = newInvestmentEntityId,
              dueTime = Some(Instant.now()),
              reviewers = List(userInfo.getDisplayName),
              note = ""
            ),
            actor = actor
          )
          _ <- fundDataInvestmentEntityAssessmentService.markAssessmentAsReviewed(
            params = MarkAssessmentAsReviewedParams(
              assessmentId = newAssessmentId,
              dueTime = Some(Instant.now()),
              riskLevel = riskLevelOpt,
              reviewers = List(userInfo.getDisplayName),
              note = ""
            ),
            actor = actor
          )
        } yield ()
      }
      _ <- ZIOUtils.traverseOption(noteOpt) { note =>
        fundDataInvestmentEntityNoteService.updateInvestmentEntityNote(
          UpdateInvestmentEntityNoteParams(
            investmentEntityId = newInvestmentEntityId,
            content = note
          ),
          actor,
          updateWhen = UpdateInvestmentEntityNoteEvent.UpdateWhen.CreateInvestmentEntity
        )
      }
      _ <- fundDataPermissionService.updatePermissionWhenAddInvestmentEntity(newInvestmentEntityId)
      _ <- natsNotificationService.publish(investorId, FundDataNotificationChannels.fundDataInvestorDashboard(investorId))
      _ <- fundDataEventService.push(
        FundDataEvent(
          firmId = firmId,
          actor = actor,
          createdAt = Some(Instant.now),
          detail = CreateInvestmentEntityEvent(
            investmentEntityId = newInvestmentEntityId,
            name,
            customId,
            jurisdictionType,
            jurisdictionOtherType,
            investorType,
            investorOtherType,
            source
          )
        )
      )
    } yield newInvestmentEntityId
  }

  def importInvestmentEntitiesBySpreadsheet(params: ImportInvestmentEntitiesBySpreadsheetParams, actor: UserId)
    : Task[BatchActionId] = {
    for {
      _ <- ZIO.logInfo(s"$actor import investment entities by spreadsheet for $params.firmId")
      firmId = params.firmId
      importedInvestmentEntities = params.investmentEntities
      _ <- fundDataPermissionService.validateUserHasRole(firmId, actor)
      _ <- FundDataValidationUtils
        .minSize("investmentEntities", importedInvestmentEntities)
        .toZIO
        .mapError(FundDataValidationError(_))
      _ <- ZIOUtils.validate(params.investmentEntities.forall(_.investorId.parent == firmId)) {
        FundDataValidationError(s"All investment entities should be in the same firm $firmId")
      }
      _ <- fundDataPermissionService.validateHasPermissionOnMultipleClients(
        actor,
        ClientPermission.CanEdit,
        importedInvestmentEntities.map(_.investorId).distinct
      )
      _ <- fundDataPermissionService.validateHasPermissionOnMultipleInvestmentEntities(
        actor,
        CanEdit,
        importedInvestmentEntities.map(_.importOption).collect {
          case ImportToExistingInvestmentEntity(investmentEntityId) =>
            investmentEntityId
        }
      )
      _ <- ZIOUtils.foreachParN(4)(importedInvestmentEntities) { importedInvestmentEntity =>
        importedInvestmentEntity.importOption match {
          case ImportToExistingInvestmentEntity(
                investmentEntityId
              ) =>
            for {
              _ <- ZIOUtils.validate(investmentEntityId.parent == importedInvestmentEntity.investorId)(
                FundDataValidationError(
                  s"Investment entity $investmentEntityId doesn't belong to investor ${importedInvestmentEntity.investorId}"
                )
              )
            } yield ()
          case _ => ZIO.unit
        }
      }

      batchActionId <- batchActionService.startBatchActionInternal(
        params.firmId,
        actor,
        actionType = BatchActionType.FundDataImportInvestmentEntitiesBySpreadsheet,
        batchActionItemsData = params.investmentEntities.map(_.asJson),
        frontendTracking = BatchActionFrontendTracking.ACTOR_TRACKING,
        startWorkflow = workflowParams => {
          FundDataImportInvestmentEntitiesBySpreadsheetWorkflowImpl.instance
            .getWorkflowStub()
            .provideEnvironment(temporalEnvironment.workflowClient)
            .flatMap(workflowStub => ZWorkflowStub.start(workflowStub.execute(workflowParams)))
        }
      )

    } yield batchActionId
  }

  private[investmententity] def importInvestmentEntityBySpreadsheet(
    investmentEntityInfo: InvestmentEntityInfo,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor import investment entity by spreadsheet for ${investmentEntityInfo.investorId}")
      _ <- investmentEntityInfo.importOption match {
        case ImportNew =>
          createInvestmentEntityUnsafe(
            investorId = investmentEntityInfo.investorId,
            name = investmentEntityInfo.nameOpt.getOrElse(""),
            customId = investmentEntityInfo.customIdOpt.getOrElse(""),
            jurisdictionType = investmentEntityInfo.jurisdictionTypeOpt.flatMap(_.value),
            jurisdictionOtherType = "",
            investorType = investmentEntityInfo.investorTypeOpt.flatMap(_.value),
            investorOtherType = "",
            riskLevelOpt = investmentEntityInfo.riskLevelOpt.flatMap(_.value),
            noteOpt = investmentEntityInfo.noteOpt,
            actor = actor,
            source = ImportInvestmentEntityFromSpreadSheet()
          )
        case ImportToExistingInvestmentEntity(
              investmentEntityId
            ) =>
          editInvestmentEntityInternal(
            investmentEntityId = investmentEntityId,
            name = investmentEntityInfo.nameOpt,
            customId = investmentEntityInfo.customIdOpt,
            jurisdictionType = UpdateOptionOperator(investmentEntityInfo.jurisdictionTypeOpt),
            investorType = UpdateOptionOperator(investmentEntityInfo.investorTypeOpt),
            riskLevel = investmentEntityInfo.riskLevelOpt.flatMap(_.value),
            noteUpdate = UpdateProperty(valueOpt = investmentEntityInfo.noteOpt),
            actor = actor
          )
      }
    } yield ()
  }

  def getDownloadSkippedInvestmentEntitiesReport(
    params: GetDownloadSkippedInvestmentEntitiesReportParams,
    actor: UserId
  ) // TODO: refactor to common function in next PR
    : Task[GetDownloadSkippedInvestmentEntitiesReportResp] = {
    for {
      _ <- ZIO.logInfo(
        s"$actor get download skipped investment entities when import investment entities for ${params.firmId}"
      )
      _ <- fundDataPermissionService.validateUserHasRole(params.firmId, actor)
      _ <- FundDataValidationUtils
        .minSize("investment entities", params.investmentEntityInfos)
        .toZIO
        .mapError(FundDataValidationError(_))
      firmModel <- FDBRecordDatabase.transact(FundDataFirmStoreOperations.Production)(_.get(params.firmId))

      userZoneId <- userService.getUserTimeZone(actor).map(_.getOrElse(DateTimeUtils.defaultTimezone))
      formattedDateTime = DateTimeUtils.formatInstant(
        Instant.now,
        DateTimeUtils.DefaultDateFormatter
      )(
        using userZoneId
      )

      rawData = List(
        List(
          "Client name/tracking ID",
          "Entity name",
          "Entity tracking ID",
          "Entity jurisdiction",
          "Entity type",
          "Risk level",
          "Errors"
        )
      ) ++ params.investmentEntityInfos.map { skippedInvestmentEntityInfo =>
        val skippedReasons =
          skippedInvestmentEntityInfo.skippedClientNameOrTrackingIdColReasons ++ skippedInvestmentEntityInfo.skippedInvestmentEntityNameColReasons ++
            skippedInvestmentEntityInfo.skippedCustomIdColReasons ++ skippedInvestmentEntityInfo.skippedJurisdictionColReasons ++
            skippedInvestmentEntityInfo.skippedInvestorTypeColReasons ++ skippedInvestmentEntityInfo.skippedRiskLevelColReasons
        List(
          skippedInvestmentEntityInfo.clientNameOrTrackingId,
          skippedInvestmentEntityInfo.investmentEntityName,
          skippedInvestmentEntityInfo.customId,
          skippedInvestmentEntityInfo.jurisdiction,
          skippedInvestmentEntityInfo.investorType,
          skippedInvestmentEntityInfo.riskLevel,
          skippedReasons.map(_.reason).mkString(" ; ")
        )
      }

      fileName = firmModel.name + "_investment entity data import errors_" + formattedDateTime + ".xlsx"

      fillSpreadsheetData = FillSheetData(
        startCol = 0,
        startRow = 0,
        rows = rawData.map { row =>
          FillSheetRow(row.map(valueStr => FillSheetCell(valueStr)))
        }
      )

      result <- ZIO
        .attempt {
          new XSSFWorkbook()
        }
        .bracket { workbook =>
          val sheet = workbook.createSheet("Import_errors")
          for {
            _ <- ZIO.attempt {
              FillSpreadsheet.fillSheet(sheet, fillSpreadsheetData)
              FillSpreadsheet.setBoldRow(
                workbook = workbook,
                sheet = sheet,
                rowIndex = 1
              )
            }
            resultSource <- SpreadsheetUtils.workbookToStream(workbook)
          } yield resultSource
        } { workbook =>
          ZIO.succeed {
            workbook.close()
          }
        }

      userFolderId <- fileService.createUserTemporaryFolderIfNeeded(actor)

      generatedFileId <- fileService.uploadFile(
        userFolderId,
        fileName,
        FileContentOrigin.FromSource(
          result,
          MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        ),
        actor
      )
    } yield GetDownloadSkippedInvestmentEntitiesReportResp(fileId = generatedFileId)
  }

  def editInvestmentEntity(
    rawParams: EditInvestmentEntityParams,
    actor: UserId
  ): Task[Unit] = {
    val params = rawParams.trim
    for {
      _ <- ZIO.logInfo(s"$actor edit investment entity ${params.investmentEntityId}")
      _ <- fundDataPermissionService.validateUserCanEditInvestmentEntity(params.investmentEntityId, actor)
      _ <- editInvestmentEntityInternal(
        params.investmentEntityId,
        params.name,
        params.customId,
        params.jurisdictionType,
        params.jurisdictionOtherType,
        params.investorType,
        params.investorOtherType,
        params.riskLevel,
        actor = actor
      )
    } yield ()
  }

  def editInvestmentEntityInternal(
    investmentEntityId: FundDataInvestmentEntityId,
    name: Option[String] = None,
    customId: Option[String] = None,
    jurisdictionType: UpdateOptionOperator[TagItemId] = UpdateOptionOperator(newValueOpt = None),
    jurisdictionOtherType: Option[String] = None,
    investorType: UpdateOptionOperator[TagItemId] = UpdateOptionOperator(newValueOpt = None),
    investorOtherType: Option[String] = None,
    riskLevel: Option[TagItemId] = None,
    noteUpdate: UpdateProperty[String] = UpdateProperty.unchanged,
    actor: UserId
  ): Task[Unit] = {
    val firmId = investmentEntityId.parent.parent
    for {
      _ <- ZIOUtils.traverseOption(name)(name =>
        Validation
          .validate(
            FundDataValidationUtils.nonEmpty("name", name),
            FundDataValidationUtils.maxLength("name", name)
          )
          .toZIO
          .mapError(GeneralServiceException(_))
      )
      _ <- ZIOUtils.traverseOption(customId)(customId =>
        for {
          _ <- FundDataValidationUtils.convertError(
            FundDataValidationUtils.when(customId.nonEmpty)(FundDataValidationUtils.isValidCustomId("customId", customId))
          )
          _ <- checkDuplicatedCustomIds(firmId, List(CheckDuplicatedCustomIdParams(customId, Some(investmentEntityId))))
        } yield ()
      )

      (curIE, newIE) <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production) { ops =>
        for {
          curIE <- ops.get(investmentEntityId)
          newIE = curIE.copy(
            name = name.getOrElse(curIE.name),
            customId = customId.getOrElse(curIE.customId),
            jurisdictionOtherType = jurisdictionOtherType.getOrElse(curIE.jurisdictionOtherType),
            investorOtherType = investorOtherType.getOrElse(curIE.investorOtherType)
          )
          _ <- ops.update(newIE)
        } yield curIE -> newIE
      }

      (curJurisdiction, newJurisdiction) <- updateTagInvestmentEntity(
        firmId,
        investmentEntityId,
        jurisdictionType,
        Jurisdiction
      )

      (curInvestorType, newInvestorType) <- updateTagInvestmentEntity(
        firmId,
        investmentEntityId,
        investorType,
        InvestorType
      )

      ieRiskTags <- getInvestmentEntitiesWithRiskTagsUnsafe(firmId, List(investmentEntityId))
      oldTagItemId = ieRiskTags.get(investmentEntityId)
      _ <- ZIO.when(riskLevel.nonEmpty && oldTagItemId != riskLevel) {
        for {
          userInfo <- userProfileService.getUserInfo(actor)
          newAssessmentId <- fundDataInvestmentEntityAssessmentService.createAssessment(
            rawParams = CreateAssessmentParams(
              investmentEntityId = investmentEntityId,
              dueTime = Some(Instant.now()),
              reviewers = List(userInfo.getDisplayName),
              note = ""
            ),
            actor = actor
          )
          _ <- fundDataInvestmentEntityAssessmentService.markAssessmentAsReviewed(
            params = MarkAssessmentAsReviewedParams(
              assessmentId = newAssessmentId,
              dueTime = Some(Instant.now()),
              riskLevel = riskLevel,
              reviewers = List(userInfo.getDisplayName),
              note = ""
            ),
            actor = actor
          )
        } yield ()
      }

      _ <- ZIOUtils.traverseOption(noteUpdate.valueOpt) { note =>
        fundDataInvestmentEntityNoteService.updateInvestmentEntityNote(
          params = UpdateInvestmentEntityNoteParams(investmentEntityId = investmentEntityId, content = note),
          actor = actor
        )
      }

      _ <- natsNotificationService.publish(
        investmentEntityId.parent,
        FundDataNotificationChannels.fundDataInvestorDashboard(investmentEntityId.parent)
      )
      _ <- fundDataEventService.push(
        FundDataEvent(
          firmId = firmId,
          actor = actor,
          createdAt = Some(Instant.now),
          detail = EditInvestmentEntityEvent(
            investmentEntityId = investmentEntityId,
            oldName = curIE.name,
            name = newIE.name,
            oldCustomId = curIE.customId,
            customId = newIE.customId,
            oldJurisdictionType = curJurisdiction,
            jurisdictionType = newJurisdiction,
            oldJurisdictionOtherType = curIE.jurisdictionOtherType,
            jurisdictionOtherType = newIE.jurisdictionOtherType,
            oldInvestorType = curInvestorType,
            investorType = newInvestorType,
            oldInvestorOtherType = curIE.investorOtherType,
            investorOtherType = newIE.investorOtherType,
            oldRiskLevel = oldTagItemId,
            newRiskLevel = riskLevel
          )
        )
      )
    } yield ()
  }

  private def updateTagInvestmentEntity(
    firmId: FundDataFirmId,
    investmentEntityId: FundDataInvestmentEntityId,
    updateOperator: UpdateOptionOperator[TagItemId],
    tagType: TagType
  ) = {
    updateOperator.getValueOpt.fold(
      for {
        tag <- fundDataTagService.getTagsByObject(firmId, investmentEntityId)(
          using tagType
        )
      } yield tag.headOption -> tag.headOption
    )(newTag =>
      for {
        (oldTags, newTags) <- fundDataTagService
          .updateTagsAssignedToObject(
            firmId,
            investmentEntityId,
            newTag.toList
          )(
            using tagType
          )
          .map(resp => resp.oldTags -> resp.newTags)
      } yield oldTags.headOption -> newTags.headOption
    )
  }

  def getInvestmentEntitiesBasic(
    params: GetInvestmentEntitiesBasicParams,
    actor: UserId,
    filterByPermission: Boolean = true
  ): Task[List[FundDataInvestmentEntityBasic]] = {
    val firmId = params.firmId
    for {
      _ <- ZIO.logInfo(s"$actor get investment entities basic of $firmId")
      investmentEntities <- FDBRecordDatabase.transact(
        FDBOperations[(InvestmentEntityStoreOperations, FundInvestmentStoreOperations)].Production
      ) { (investmentEntityOps, fundInvestmentOps) =>
        params.getBy match {
          case GetInvestmentEntitiesBasicParams.GetBy.All =>
            investmentEntityOps.get(params.firmId)
          case GetInvestmentEntitiesBasicParams.GetBy.Investors(investorIds) =>
            RecordIO.parTraverseN(4)(investorIds.distinct)(investmentEntityOps.get).map(_.flatten)
          case GetInvestmentEntitiesBasicParams.GetBy.InvestmentEntities(investmentEntityIds) =>
            RecordIO.parTraverseN(4)(investmentEntityIds.distinct)(investmentEntityOps.getOpt(_)).map(_.flatten)
          case GetInvestmentEntitiesBasicParams.GetBy.FundLegalEntities(fundLegalEntityIds) =>
            for {
              investmentEntityIds <- RecordIO
                .parTraverseN(4)(fundLegalEntityIds)(fundInvestmentOps.getByFundLegalEntity)
                .map(_.flatten.map(_.investmentEntityId).distinct)
              investmentEntities <- RecordIO
                .parTraverseN(4)(investmentEntityIds)(investmentEntityOps.getOpt(_))
                .map(_.flatten)
            } yield investmentEntities
        }
      }

      accessibleInvestmentEntityIds <- fundDataPermissionService
        .filterInvestmentEntitiesWithPermission(
          investmentEntityIds = investmentEntities.map(_.investmentEntityId),
          permissionName = FundDataRebacModel.InvestmentEntityPermission.CanView,
          actor = actor
        )
        .map(_.toSet)

      investmentEntitiesToReturn =
        if (filterByPermission) {
          investmentEntities.filter { investmentEntity =>
            accessibleInvestmentEntityIds.contains(investmentEntity.investmentEntityId)
          }
        } else {
          investmentEntities
        }

      investmentEntityIdsToReturn = investmentEntitiesToReturn.map(_.investmentEntityId)
      jurisdictionMap <- fundDataTagService
        .getTagsByObjectsSameTagType(firmId, investmentEntityIdsToReturn)(
          using Jurisdiction
        )
      investorTypeMap <- fundDataTagService
        .getTagsByObjectsSameTagType(firmId, investmentEntityIdsToReturn)(
          using InvestorType
        )
      riskMap <- getInvestmentEntitiesWithRiskTagsUnsafe(firmId, investmentEntityIdsToReturn)
    } yield investmentEntitiesToReturn.map { investmentEntity =>
      val canViewByCurrentActor = accessibleInvestmentEntityIds.contains(investmentEntity.investmentEntityId)
      val ieBasic = FundDataInvestmentEntityBasic(
        id = investmentEntity.investmentEntityId,
        name = investmentEntity.name,
        customId = investmentEntity.customId,
        jurisdictionType = jurisdictionMap.get(investmentEntity.investmentEntityId).flatMap(_.headOption),
        investorType = investorTypeMap.get(investmentEntity.investmentEntityId).flatMap(_.headOption),
        riskAssessmentLevel = riskMap.get(investmentEntity.investmentEntityId),
        canViewByCurrentActor = canViewByCurrentActor
      )
      if (canViewByCurrentActor) {
        ieBasic
      } else {
        // mask out all information
        ieBasic.copy(
          name = "",
          customId = "",
          jurisdictionType = None,
          investorType = None,
          riskAssessmentLevel = None
        )
      }
    }
  }

  private[funddata] def getInvestmentEntitiesBasicUnsafe(
    firmId: FundDataFirmId,
    investmentEntityIds: Set[FundDataInvestmentEntityId],
    actor: UserId
  ): Task[Map[FundDataInvestmentEntityId, FundDataInvestmentEntityBasic]] = {
    for {
      accessibleInvestmentEntityIds <- fundDataPermissionService
        .filterInvestmentEntitiesWithPermission(
          investmentEntityIds = investmentEntityIds.toList,
          permissionName = FundDataRebacModel.InvestmentEntityPermission.CanView,
          actor = actor
        )
        .map(_.toSet)
      investmentEntities <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production) { ops =>
        RecordIO.parTraverseN(4)(investmentEntityIds.toSeq)(ops.getOpt(_)).map(_.flatten)
      }
      jurisdictionMap <- fundDataTagService
        .getTagsByObjectsSameTagType(firmId, investmentEntityIds.toList)(
          using Jurisdiction
        )
      investorTypeMap <- fundDataTagService
        .getTagsByObjectsSameTagType(firmId, investmentEntityIds.toList)(
          using InvestorType
        )
      riskMap <- getInvestmentEntitiesWithRiskTagsUnsafe(firmId, investmentEntityIds.toList)
    } yield investmentEntities.map { investmentEntity =>
      val canViewByCurrentActor = accessibleInvestmentEntityIds.contains(investmentEntity.investmentEntityId)
      investmentEntity.investmentEntityId -> FundDataInvestmentEntityBasic(
        id = investmentEntity.investmentEntityId,
        name = investmentEntity.name,
        customId = investmentEntity.customId,
        jurisdictionType = jurisdictionMap.get(investmentEntity.investmentEntityId).flatMap(_.headOption),
        investorType = investorTypeMap.get(investmentEntity.investmentEntityId).flatMap(_.headOption),
        riskAssessmentLevel = riskMap.get(investmentEntity.investmentEntityId),
        canViewByCurrentActor = canViewByCurrentActor
      )
    }.toMap
  }

  def getInvestmentEntitiesWithTypesUnsafe(
    firmId: FundDataFirmId,
    investmentEntityIds: List[FundDataInvestmentEntityId]
  ): Task[Map[FundDataInvestmentEntityId, FundDataIEAndTags]] = {
    for {
      ieTypesTags <- fundDataTagService.getTagsByObjectsSameTagType(
        firmId,
        investmentEntityIds
      )(
        using InvestorType
      )
    } yield investmentEntityIds.map { ieId =>
      ieId -> FundDataIEAndTags(
        investmentEntityId = ieId,
        investorType = ieTypesTags.get(ieId).flatMap(_.headOption)
      )
    }.toMap
  }

  def getInvestmentEntitiesWithRiskTagsUnsafe(
    firmId: FundDataFirmId,
    investmentEntityIds: List[FundDataInvestmentEntityId]
  ): Task[Map[FundDataInvestmentEntityId, TagItemId]] = {
    for {
      ieToAssessments <- fundDataInvestmentEntityAssessmentService.getAssessmentsByIEsUnsafe(firmId, investmentEntityIds)
      tagList <- fundDataTagService.getFirmTagListInternal(firmId)(
        using FundDataTagService.RiskLevelAssessment
      )
      tagItemMap = tagList.tagItems.map { tagItem => tagItem.tagItemId -> tagItem }
      ieToAssessmentMap = ieToAssessments.map { case (ieId, assessments) =>
        val sortedAssessments = assessments
          .filter(_.status == FundDataInvestmentEntityAssessmentStatus.Completed)
          .map(assessment => (assessment.dueTime, assessment.riskLevel))
          .collect { case (Some(dueTime), Some(riskLevel)) => (dueTime, riskLevel) }
          .sortBy(_._1)
          .reverse
        val mostRecentDueTimeOpt = sortedAssessments.headOption.map(_._1)
        val latestRisks = mostRecentDueTimeOpt.fold(List.empty) { mostRecent =>
          sortedAssessments.filter(_._1 == mostRecent).map(_._2)
        }
        val tagItems = latestRisks.flatMap(tagItemMap.toMap.get)
        ieId -> tagItems
          .sortBy(tagItem => compareRiskLevelItemByColor(tagItem.color))
          .reverse
          .map(_.tagItemId)
          .headOption
      }
    } yield ieToAssessmentMap.flatMap { case (ieId, riskOpt) => riskOpt.map(ieId -> _) }.toMap
  }

  def getInvestmentEntitiesWithJurisdictionTagsUnsafe(
    firmId: FundDataFirmId,
    investmentEntityIds: List[FundDataInvestmentEntityId]
  ): Task[Map[FundDataInvestmentEntityId, FundDataIEAndTags]] = {
    for {
      jurisdictionTypeMap <- fundDataTagService.getTagsByObjectsSameTagType(
        firmId,
        investmentEntityIds
      )(
        using Jurisdiction
      )
    } yield investmentEntityIds.map { ieId =>
      ieId -> FundDataIEAndTags(
        investmentEntityId = ieId,
        jurisdictionType = jurisdictionTypeMap.get(ieId).flatMap(_.headOption)
      )
    }.toMap
  }

  def exportInvestmentEntitiesToSpreadsheet(params: ExportInvestmentEntitiesToSpreadsheetParams, actor: UserId)
    : Task[BatchActionId] = {
    for {
      _ <- ZIO.logInfo(s"$actor export investment entities data to spreadsheet for ${params.firmId}")
      _ <- fundDataPermissionService.validateUserHasRole(params.firmId, actor)
      _ <- ZIOUtils.validate(params.investorIds.forall(_.parent == params.firmId)) {
        FundDataValidationError(s"All investors should be in firm ${params.firmId}")
      }
      _ <- FundDataValidationUtils
        .minSize("investors", params.investorIds)
        .toZIO
        .mapError(FundDataValidationError(_))

      firmModel <- FDBRecordDatabase.transact(FundDataFirmStoreOperations.Production)(_.get(params.firmId))
      investors = params.investorIds.sliding(100, 100).toList
      batchItems = investors.map { investorIds =>
        ExportInvestmentEntitiesBatchItem(
          firmId = params.firmId,
          templateVersionIds = params.templateVersionIds,
          investorIds = investorIds
        )
      }
      userZoneId <- userService.getUserTimeZone(actor).map(_.getOrElse(DateTimeUtils.defaultTimezone))
      formattedDateTime = DateTimeUtils.formatInstant(
        Instant.now,
        DateTimeUtils.DefaultDateFormatter
      )(
        using userZoneId
      )

      fileName = firmModel.name + "- Investor data export - " + formattedDateTime + ".xlsx"
      postExecuteParams = ExportInvestmentEntitiesToSpreadsheetPostExecuteParams(
        firmId = params.firmId,
        fileName = fileName
      )
      batchActionId <- batchActionService.startBatchActionInternal(
        params.firmId,
        actor,
        actionType = BatchActionType.FundDataExportInvestmentEntitiesToSpreadsheet,
        batchActionItemsData = batchItems.map(_.asJson),
        postExecuteDataOpt = Some(postExecuteParams.asJson),
        frontendTracking = BatchActionFrontendTracking.ACTOR_TRACKING,
        startWorkflow = workflowParams => {
          FundDataExportInvestmentEntitiesToSpreadsheetWorkflowImpl.instance
            .getWorkflowStub()
            .provideEnvironment(temporalEnvironment.workflowClient)
            .flatMap(workflowStub => ZWorkflowStub.start(workflowStub.execute(workflowParams)))
        }
      )
    } yield batchActionId
  }

  private def exportInvestorsAndIEsPage(firmId: FundDataFirmId, investorIds: List[FundDataInvestorId], actor: UserId)
    : Task[DocumentStorageId] = {
    for {
      investorsToIEModelsMap <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.get(investorIds)
      )
      investorModelsMap <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
        _.getInvestors(investorIds).map(_.map(investorModel => investorModel.investorId -> investorModel).toMap)
      )

      ieModels = investorsToIEModelsMap.values.flatten.toList
      investorWithoutIEs = investorIds.filterNot(investorId =>
        ieModels.map(_.investmentEntityId.parent).toSet.contains(investorId)
      )

      ieToFundSubscriptions <- ZIOUtils.foreachParN(4)(ieModels) { ieModel =>
        for {
          fundSubscriptions <- fundDataFundSubscriptionService.getSubscriptionsByInvestmentEntity(
            ieModel.investmentEntityId,
            actor
          )
        } yield ieModel.investmentEntityId -> fundSubscriptions
      }

      linkedFundSubIds <- ZIO.succeed(
        ieToFundSubscriptions.flatMap(
          _._2.flatMap(_.getOnlineSubscriptionValueOpt(_.linkedFundSubOrderId)).map(_.parent).distinct
        )
      )
      linkedFundSubsInfo <- greylinDataService.run(
        FundSubscriptionOperations
          .get(linkedFundSubIds)
          .map(_.map(fund => fund.id -> fund).toMap)
      )

      genericTagMap <- fundDataTagService.getTagsByObjectsSameTagType(firmId, investorIds)(
        using GenericTag
      )

      investmentEntityIds = ieModels.map(_.investmentEntityId)

      jurisdictionTypeMap <- fundDataTagService.getTagsByObjectsSameTagType(firmId, investmentEntityIds)(
        using Jurisdiction
      )

      investorTypeMap <- fundDataTagService.getTagsByObjectsSameTagType(firmId, investmentEntityIds)(
        using InvestorType
      )

      riskLevelMap <- getInvestmentEntitiesWithRiskTagsUnsafe(firmId, ieModels.map(_.investmentEntityId))

      allTagItemIds = genericTagMap.values.toList.flatten ++ jurisdictionTypeMap.values.toList.flatten ++
        investorTypeMap.values.toList.flatten ++ riskLevelMap.values.toList
      allTagItems <- fundDataTagService.getTagItemsUnsafe(allTagItemIds.distinct)
      tagItemIdToTagItemMap = allTagItems.map(tagItem => tagItem.tagItemId -> tagItem).toMap

      investmentEntityNoteMap <- fundDataInvestmentEntityNoteService.getInvestmentEntitiesNote(
        firmId,
        investmentEntityIds.toSet
      )

      ieRawData <- ZIOUtils.foreachParN(4)(ieModels) { ieModel =>
        for {
          clientNameOpt <- ZIO.succeed(investorModelsMap.get(ieModel.investmentEntityId.parent).map(_.name))
          clientTrackingIdOpt <- ZIO.succeed(investorModelsMap.get(ieModel.investmentEntityId.parent).map(_.customId))
          genericTagItemIds <- ZIO.succeed(genericTagMap.getOrElse(ieModel.investmentEntityId.parent, Seq.empty).toList)
          clientTagItems = genericTagItemIds.flatMap(tagItemIdToTagItemMap.get)

          jurisdictionTagItemIdOpt = jurisdictionTypeMap.get(ieModel.investmentEntityId).flatMap(_.headOption)
          jurisdictionTagItem = jurisdictionTagItemIdOpt.fold("")(jurisdictionTagItemId =>
            tagItemIdToTagItemMap.get(jurisdictionTagItemId).map(_.name).getOrElse("")
          )
          investorTagItemIdOpt = investorTypeMap.get(ieModel.investmentEntityId).flatMap(_.headOption)
          investorTagItem = investorTagItemIdOpt.fold("")(tagItemId =>
            tagItemIdToTagItemMap.get(tagItemId).map(_.name).getOrElse("")
          )
          riskTagItemIdOpt = riskLevelMap.get(ieModel.investmentEntityId)
          riskTagItem = riskTagItemIdOpt.fold("")(riskTagItemId =>
            tagItemIdToTagItemMap.get(riskTagItemId).map(_.name).getOrElse("")
          )
          subscriptions = ieToFundSubscriptions.toMap
            .getOrElse(ieModel.investmentEntityId, List.empty)
            .distinctBy(_.fundInfo.fundId)
          fundNames = subscriptions.flatMap(subscription =>
            subscription.foldSubscriptionTypeValue(
              onlineFn = online => linkedFundSubsInfo.get(online.linkedFundSubOrderId.parent).map(_.name),
              offlineFn = _ => Some(subscription.fundInfo.name)
            )
          )
          commitmentMoneyStr = SquantsUtils.formatMoneysWithAggregate(
            SquantsUtils.aggregateMoneys(subscriptions.flatMap(_.subscriptionTypeInfo.currentCommitments))
          )
          noteStr = investmentEntityNoteMap.getOrElse(ieModel.investmentEntityId, None).fold("") { note =>
            FundDataNoteUtils.getTextFromNoteContent(note.content)
          }
        } yield List(
          clientNameOpt.getOrElse(""),
          clientTrackingIdOpt.getOrElse(""),
          clientTagItems.map(_.name).mkString(" ; "),
          ieModel.name,
          ieModel.customId,
          investorTagItem,
          jurisdictionTagItem,
          riskTagItem,
          fundNames.mkString(" ; "),
          commitmentMoneyStr,
          noteStr
        )
      }

      investorWithoutIERawData <- ZIOUtils.foreachParN(4)(investorWithoutIEs) { investorId =>
        for {
          clientNameOpt <- ZIO.succeed(investorModelsMap.get(investorId).map(_.name))
          clientTrackingIdOpt <- ZIO.succeed(investorModelsMap.get(investorId).map(_.customId))
          genericTagItemIds <- ZIO.succeed(genericTagMap.getOrElse(investorId, Seq.empty).toList)
          clientTagItems = genericTagItemIds.flatMap(tagItemIdToTagItemMap.get)
        } yield List(
          clientNameOpt.getOrElse(""),
          clientTrackingIdOpt.getOrElse(""),
          clientTagItems.map(_.name).mkString(" ; "),
          "",
          "",
          "",
          "",
          "",
          "",
          ""
        )
      }

      rawData = ieRawData ++ investorWithoutIERawData

      resultSource <- CsvUtils.createCsvTask(rawData)
      folderId <- fileService.createUserTemporaryFolderIfNeeded(actor = actor)
      userZoneId <- userService.getUserTimeZone(actor).map(_.getOrElse(DateTimeUtils.defaultTimezone))
      formattedDateTime = DateTimeUtils.formatInstant(
        Instant.now,
        DateTimeUtils.DefaultDateFormatter
      )(
        using userZoneId
      )

      fileId <- fileService.uploadFile(
        folderId,
        s"export_investment_entity_data_$formattedDateTime.csv",
        FileContentOrigin.FromSource(
          resultSource,
          MediaType.TextCsv
        ),
        actor
      )

      documentStorageId <- fileService.getFileStorageId(
        actor = actor,
        fileId = fileId,
        purpose = DmsTrackingActivityType.Internal,
        httpContextOpt = None
      )
    } yield documentStorageId
  }

  def exportInvestmentEntities(
    params: ExportInvestmentEntitiesBatchItem,
    actor: UserId
  ): Task[ExportInvestmentEntitiesBatchItemResp] = {
    for {
      _ <- ZIO.logInfo(s"$actor export ${params.investorIds.size} clients data")
      _ <- fundDataPermissionService.validateUserHasRole(params.firmId, actor)
      investmentEntityFileStorageId <- exportInvestorsAndIEsPage(params.firmId, params.investorIds, actor)
      profileFileWithTemplateVersionIds <- ZIOUtils.foreachParN(4)(params.templateVersionIds) { templateVersionId =>
        for {
          profileFileStorageId <- fundDataInvestmentEntityProfileService.getExportProfileData(
            params.firmId,
            params.investorIds,
            templateVersionId,
            actor
          )
        } yield templateVersionId -> profileFileStorageId
      }
    } yield ExportInvestmentEntitiesBatchItemResp(
      investmentEntityFileStorageId = investmentEntityFileStorageId,
      profileFileWithTemplateVersionIds = profileFileWithTemplateVersionIds
    )
  }

  def getInvestmentEntities(
    investorId: FundDataInvestorId,
    actor: UserId
  ): Task[GetInvestmentEntitiesResponse] = {
    val firmId = investorId.parent
    for {
      _ <- ZIO.logInfo(s"$actor get investment entities of $investorId")
      _ <- fundDataPermissionService.validateUserCanViewClient(investorId, actor)

      ieModels <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.get(investorId)
      )
      ies <- getInvestmentEntitiesInternal(firmId, ieModels, actor)
    } yield GetInvestmentEntitiesResponse(
      data = ies.map(ie => ie.investmentEntityId -> ie),
      filterKeys = ies.map(_.investmentEntityId).toSet,
      pagination = DashboardPaginationData(offset = 0, limit = -1, filter = ies.size, total = ies.size)
    )
  }

  private def getInvestmentEntitiesInternal(
    firmId: FundDataFirmId,
    ieModels: List[FundDataInvestmentEntityModel],
    actor: UserId
  ) = {
    for {
      jurisdictionsMap <- fundDataTagService.getTagsByObjectsSameTagType(
        firmId,
        ieModels.map(_.investmentEntityId)
      )(
        using Jurisdiction
      )
      investorTypesMap <- fundDataTagService.getTagsByObjectsSameTagType(
        firmId,
        ieModels.map(_.investmentEntityId)
      )(
        using InvestorType
      )
      fundIdsToFunds <- FDBRecordDatabase.transact(FundDataFundV2StoreOperations.Production)(
        _.getFunds(firmId).map(_.map(fund => fund.fundId -> fund))
      )
      ies <- ZIOUtils.foreachParN(10)(ieModels) { ieModel =>
        for {
          fundSubscriptionsModels <- FDBRecordDatabase.transact(FundDataFundSubscriptionStoreOperations.Production)(
            _.getSubscriptions(ieModel.investmentEntityId)
          )
          linkedSubscriptionOrderIds = fundSubscriptionsModels.flatMap(_.linkedFundSubOrderId)
          linkedFundSubIds = linkedSubscriptionOrderIds.map(_.parent).toSet
          canAccessFundSubMap <- FundDataFundQueryUtils.canActorAccessFundSubs(linkedFundSubIds.toList, actor)
          linkedFundIds = fundSubscriptionsModels.map(_.subscriptionId.parent).toSet
          linkedFundSubsInfo <- greylinDataService.run(
            FundSubscriptionOperations
              .get(linkedFundSubIds.toList)
              .map(_.map(fund => fund.id -> fund.name).toMap)
          )
          funds = linkedFundIds.toList.flatMap { fundId =>
            val fundOpt = fundIdsToFunds.toMap.get(fundId)
            fundOpt.map { fund =>
              FundNameAndAccessible(
                fundId = fundId,
                name = if (fund.name.isEmpty) {
                  fund.linkedFundSubId.fold("")(fundSubId => linkedFundSubsInfo.getOrElse(fundSubId, ""))
                } else {
                  fund.name
                },
                canAccessFund =
                  fund.linkedFundSubId.fold(false)(fundSubId => canAccessFundSubMap.getOrElse(fundSubId, false))
              )
            }
          }
          needComplianceActionDocuments <- fundDataInvestmentEntityDocumentService
            .getNeedComplianceActionDocumentsUnsafe(ieModel.investmentEntityId)
          overdueAndUpcomingSoonAssessments <- fundDataInvestmentEntityAssessmentService
            .getOverDueAndUpcomingDueDateSoonAssessmentsUnsafe(
              ieModel.investmentEntityId
            )
          hasPendingProfileConflict <- fundDataProfileConflictService
            .getProfileConflictModelsByInvestmentEntityInternal(ieModel.investmentEntityId)
            .map(_.nonEmpty)
          latestAssessmentTagMap <- getInvestmentEntitiesWithRiskTagsUnsafe(firmId, List(ieModel.investmentEntityId))
          hasFinishedDocumentRequest <- fundDataInvestmentEntityDocumentRequestService.checkHasFinishedDocumentRequests(
            ieModel.investmentEntityId
          )
        } yield ieModel
          .into[FundDataInvestmentEntity]
          .transform(
            Field.const(_.funds, funds),
            Field.const(_.needComplianceActionDocuments, needComplianceActionDocuments),
            Field.const(_.overdueAndUpcomingSoonAssessments, overdueAndUpcomingSoonAssessments),
            Field.computed(
              _.jurisdictionType,
              ieModel => jurisdictionsMap.get(ieModel.investmentEntityId).flatMap(_.headOption)
            ),
            Field.computed(
              _.investorType,
              ieModel => investorTypesMap.get(ieModel.investmentEntityId).flatMap(_.headOption)
            ),
            Field.const(
              _.riskAssessmentLevel,
              latestAssessmentTagMap.get(ieModel.investmentEntityId)
            ),
            Field.const(_.hasPendingProfileConflict, hasPendingProfileConflict),
            Field.const(_.hasFinishedDocumentRequest, hasFinishedDocumentRequest)
          )
      }
    } yield ies
  }

  def getInvestmentEntity(
    params: GetInvestmentEntityParams,
    actor: UserId
  ): Task[FundDataInvestmentEntity] = {
    for {
      _ <- ZIO.logInfo(s"$actor get investment entity ${params.investmentEntityId}")

      ieModel <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.get(params.investmentEntityId)
      )
      ies <- getInvestmentEntitiesInternal(params.investmentEntityId.firmId, List(ieModel), actor)
      ie <- ZIOUtils.fromOption(
        ies.headOption,
        FundDataValidationError(s"investment entity does not exist")
      )
    } yield ie
  }

  def getInvestorAndInvestmentEntityForSelection(
    firmId: FundDataFirmId,
    actor: UserId
  ): Task[List[FundDataInvestorAndInvestmentEntityForSelection]] = {
    for {
      _ <- ZIO.logInfo(s"$actor get investors and investment entities for selection")
      _ <- fundDataPermissionService.validateUserHasRole(firmId, actor)

      investorModels <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
        _.getInvestors(firmId)
      )

      // filter accessible investors
      accessibleInvestorIds <- fundDataPermissionService
        .filterInvestorsWithPermission(
          investorIds = investorModels.map(_.investorId),
          permissionName = FundDataRebacModel.ClientPermission.CanView,
          actor = actor
        )
        .map(_.toSet)
      accessibleInvestorModels = investorModels.filter(investor => accessibleInvestorIds.contains(investor.investorId))

      investmentEntityModels <- FDBRecordDatabase
        .transact(InvestmentEntityStoreOperations.Production) { ops =>
          RecordIO.parTraverseN(4)(accessibleInvestorModels) { investor =>
            ops.get(investor.investorId).map(investor.investorId -> _)
          }
        }
        .map(_.toMap)
      riskAssessmentMap <- getInvestmentEntitiesWithRiskTagsUnsafe(
        firmId,
        investmentEntityModels.values.flatMap(_.map(_.investmentEntityId)).toList
      )

      investorTags <- fundDataTagService.getTagsByObjectsSameTagType(
        firmId,
        objectIds = accessibleInvestorModels.map(_.investorId)
      )(
        using FundDataTagService.GenericTag
      )
      investmentEntityInvestorTypes <- fundDataTagService.getTagsByObjectsSameTagType(
        firmId,
        objectIds = investmentEntityModels.toList.flatMap(_._2).map(_.investmentEntityId)
      )(
        using FundDataTagService.InvestorType
      )
      investmentEntityJurisdictionTypes <- fundDataTagService.getTagsByObjectsSameTagType(
        firmId,
        objectIds = investmentEntityModels.toList.flatMap(_._2).map(_.investmentEntityId)
      )(
        using FundDataTagService.Jurisdiction
      )

      fundIdsByFundSub <- FDBRecordDatabase.transact(FundDataFundV2StoreOperations.Production) { ops =>
        for {
          linkedFunds <- ops.getFunds(firmId)
          fundsByFundSubs = linkedFunds
            .map(linkedFund => linkedFund.linkedFundSubId -> linkedFund.fundId)
            .collect { case (Some(fundSubId), fundId) =>
              fundSubId -> fundId
            }
            .groupMap(_._1)(_._2)
        } yield fundsByFundSubs.view.mapValues(_.distinct).toMap
      }
      subscriptionModels <- FDBRecordDatabase.transact(FundDataFundSubscriptionStoreOperations.Production)(
        _.getSubscriptions(firmId)
      )
      fundsByInvestmentEntityId <- ZIO.attempt(
        subscriptionModels.groupBy(_.linkedInvestmentEntityId).collect {
          case (Some(investmentEntityId), subscriptions) =>
            val fundSubIds = subscriptions.flatMap(_.linkedFundSubOrderId).map(_.parent).distinct
            val fundIds = fundSubIds.flatMap(fundIdsByFundSub.get).flatten.distinct
            investmentEntityId -> fundIds
        }
      )
    } yield accessibleInvestorModels.map { investorModel =>
      val investorId = investorModel.investorId
      FundDataInvestorAndInvestmentEntityForSelection(
        investor = investorModel
          .into[FundDataInvestorAndInvestmentEntityForSelection.InvestorInfo]
          .transform(
            Field.const(_.tagIds, investorTags.getOrElse(investorId, Seq.empty).toList)
          ),
        investmentEntities = investmentEntityModels
          .getOrElse(investorId, List.empty)
          .map { investmentEntityModel =>
            val investmentEntityId = investmentEntityModel.investmentEntityId
            investmentEntityModel
              .into[FundDataInvestorAndInvestmentEntityForSelection.InvestmentEntityInfo]
              .transform(
                Field.const(_.investorType, investmentEntityInvestorTypes.get(investmentEntityId).flatMap(_.headOption)),
                Field.const(
                  _.jurisdictionType,
                  investmentEntityJurisdictionTypes.get(investmentEntityId).flatMap(_.headOption)
                ),
                Field.const(
                  _.riskAssessmentLevel,
                  riskAssessmentMap.get(investmentEntityId)
                ),
                Field.const(_.fundIds, fundsByInvestmentEntityId.getOrElse(investmentEntityId, List.empty))
              )
          }
      )
    }
  }

  private[funddata] def getAccessibleInvestmentEntitiesBasicUnsafe(
    firmId: FundDataFirmId,
    investmentEntityIds: List[FundDataInvestmentEntityId],
    userId: UserId
  ) = {
    val investorIds = investmentEntityIds.map(_.parent).distinct
    for {
      accessibleInvestorIds <- fundDataPermissionService
        .filterInvestorsWithPermission(
          investorIds = investorIds,
          permissionName = FundDataRebacModel.ClientPermission.CanView,
          actor = userId
        )
      accessibleInvestmentEntityIds = investmentEntityIds.filter(investmentEntityId =>
        accessibleInvestorIds.contains(investmentEntityId.parent)
      )
      ieBasics <- getInvestmentEntitiesBasicUnsafe(
        firmId = firmId,
        investmentEntityIds = accessibleInvestmentEntityIds.toSet,
        actor = userId
      )
    } yield ieBasics
  }

  private def getInvestmentEntityDetailUnsafe(
    investmentEntityId: FundDataInvestmentEntityId
  ) = {
    val firmId = investmentEntityId.parent.parent
    for {
      ieModel <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.get(investmentEntityId)
      )
      investorModel <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
        _.getInvestor(ieModel.investmentEntityId.parent)
      )
      jurisdictionOpt <- fundDataTagService
        .getTagsByObject(
          firmId,
          investmentEntityId
        )(
          using Jurisdiction
        )
        .map(_.headOption)

      investorTypeOpt <- fundDataTagService
        .getTagsByObject(
          firmId,
          investmentEntityId
        )(
          using InvestorType
        )
        .map(_.headOption)
      latestAssessmentTagMap <- getInvestmentEntitiesWithRiskTagsUnsafe(firmId, List(investmentEntityId))
    } yield ieModel
      .into[FundDataInvestmentEntityDetail]
      .transform(
        Field.const(_.clientName, investorModel.name),
        Field.const(_.jurisdictionType, jurisdictionOpt),
        Field.const(_.investorType, investorTypeOpt),
        Field.const(_.riskAssessment, latestAssessmentTagMap.get(investmentEntityId))
      )
  }

  def getInvestmentEntityDetail(
    investmentEntityId: FundDataInvestmentEntityId,
    actor: UserId
  ): Task[FundDataInvestmentEntityDetail] = {
    for {
      _ <- ZIO.logInfo(s"$actor get investment entity detail $investmentEntityId")
      _ <- fundDataPermissionService.validateUserCanViewInvestmentEntity(investmentEntityId, actor)
      ieDetail <- getInvestmentEntityDetailUnsafe(investmentEntityId)
    } yield ieDetail
  }

  def deleteInvestmentEntitiesWhenDeleteInvestor(
    investorId: FundDataInvestorId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor delete investment entities when delete investor $investorId")
      investmentEntityModels <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.delete(investorId)
      )
      _ <- ZIO.foreachDiscard(investmentEntityModels) { ieModel =>
        deleteInvestmentEntityRelatedData(ieModel.investmentEntityId, actor)
      }
      _ <- fundDataTagService.deleteObjectTags(investmentEntityModels.map(_.investmentEntityId))
      _ <- fundDataEventService.push(
        FundDataEvent(
          firmId = investorId.parent,
          actor = actor,
          createdAt = Some(Instant.now),
          detail = DeleteInvestmentEntityEvent(
            investmentEntityModels.map(ieModel =>
              InvestmentEntityIdWithName(ieModel.investmentEntityId, ieModel.name, ieModel.customId)
            ),
            actionType = FundDataInvestmentEntityDeleteInvestor()
          )
        )
      )
    } yield ()
  }

  def deleteInvestmentEntity(
    investmentEntityId: FundDataInvestmentEntityId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor delete investment entity $investmentEntityId")
      _ <- fundDataPermissionService.validateUserCanDeleteInvestmentEntity(investmentEntityId, actor)
      _ <- deleteInvestmentEntityUnsafe(
        investmentEntityId = investmentEntityId,
        actor = actor,
        actionType = FundDataInvestmentEntityDeleteInvestmentEntity()
      )
    } yield ()
  }

  private def deleteInvestmentEntityUnsafe(
    investmentEntityId: FundDataInvestmentEntityId,
    actor: UserId,
    actionType: DeleteInvestmentEntityActionType
  ) = {
    for {
      ieModel <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.delete(investmentEntityId)
      )
      _ <- deleteInvestmentEntityRelatedData(
        investmentEntityId,
        actor
      )
      _ <- fundDataTagService.deleteObjectTags(List(investmentEntityId))
      _ <- natsNotificationService.publish(
        investmentEntityId.parent,
        FundDataNotificationChannels.fundDataInvestorDashboard(investmentEntityId.parent)
      )
      _ <- fundDataEventService.push(
        FundDataEvent(
          firmId = investmentEntityId.parent.parent,
          actor = actor,
          createdAt = Some(Instant.now),
          detail = DeleteInvestmentEntityEvent(
            Seq(InvestmentEntityIdWithName(investmentEntityId, ieModel.name, ieModel.customId)),
            actionType = actionType
          )
        )
      )
    } yield ()
  }

  private def deleteInvestmentEntityRelatedData(
    investmentEntityId: FundDataInvestmentEntityId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- fundDataPortalService.removeInvestmentEntities(Set(investmentEntityId), actor)
      _ <- fundDataInvestmentEntityProfileService.deleteProfileWhenDeleteInvestmentEntity(
        investmentEntityId,
        actor
      )
      _ <- fundDataFundSubscriptionService.unlinkSubscriptionsWhenDeleteInvestmentEntity(
        investmentEntityId,
        actor
      )
      _ <- FundTransactionUtils.deleteInvestmentAndUnlinkTransactionsWhenDeleteInvestmentEntity(
        investmentEntityId
      )
      _ <- fundDataRequestService.deleteRequestsWhenDeleteInvestmentEntity(
        investmentEntityId,
        actor
      )
      _ <- fundDataInvestmentEntityDocumentService.deleteDocumentsWhenDeleteInvestmentEntity(
        investmentEntityId,
        actor
      )
      _ <- fundDataInvestmentEntityContactService.deleteContactsWhenDeleteInvestmentEntity(
        investmentEntityId,
        actor
      )
      _ <- fundDataInvestmentEntityAssessmentService.deleteAssessmentsWhenDeleteInvestmentEntity(
        investmentEntityId,
        actor
      )
      _ <- fundDataProfileConflictService.deleteProfileConflictsWhenDeleteInvestmentEntity(
        investmentEntityId,
        actor
      )
      _ <- FundDataContactRelationUtils.deleteContactRelationsWhenDeleteInvestmentEntity(
        investmentEntityId,
        actor
      )
      _ <- fundDataInvestmentEntityNoteService.deleteInvestmentEntityNote(
        params = DeleteInvestmentEntityNoteParams(investmentEntityId),
        actor = actor,
        deleteWhen = DeleteInvestmentEntityNoteEvent.DeleteWhen.DeleteInvestmentEntity
      )
      _ <- fundDataPermissionService.updatePermissionWhenDeleteInvestmentEntity(investmentEntityId)
    } yield ()
  }

  private def validateInvestorExist(investorId: FundDataInvestorId) = {
    for {
      investorModelOpt <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
        _.getInvestorOpt(investorId)
      )
      _ <- Validation
        .fromPredicateWith("investor must be valid")(investorModelOpt)(_.nonEmpty)
        .toZIO
        .mapError(FundDataValidationError(_))
    } yield ()
  }

  def getInvestmentEntityIdsByCustomIdInternal(firmId: FundDataFirmId, customId: String)
    : Task[List[FundDataInvestmentEntityId]] = {
    FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
      _.getByCustomId(firmId, customId).map(_.map(_.investmentEntityId))
    )
  }

  def verifyInvestmentEntityExisted(firmId: FundDataFirmId, investmentEntityId: FundDataInvestmentEntityId)
    : Task[Boolean] = {
    FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
      _.getOpt(investmentEntityId).map(_.exists(_.investmentEntityId.parent.parent == firmId))
    )
  }

  private def checkDuplicatedCustomIds(
    firmId: FundDataFirmId,
    customIds: List[CheckDuplicatedCustomIdParams]
  ): Task[Unit] = {
    val customIdsNonEmpty = customIds.filter(_.customId.trim.nonEmpty)
    for {
      _ <- ZIOUtils.failWhen(customIdsNonEmpty.distinctBy(_.customId).length != customIdsNonEmpty.length)(
        FundDataValidationError("duplicated tracking IDs")
      )

      duplicatedCustomIds <- FDBRecordDatabase
        .transact(InvestmentEntityStoreOperations.Production) { ops =>
          RecordIO.parTraverseN(4)(customIdsNonEmpty) { case CheckDuplicatedCustomIdParams(customId, ieIdOpt) =>
            for {
              isDuplicated <- ops
                .getByCustomId(firmId, customId)
                .map(_.map(_.investmentEntityId))
                .map(_.filterNot(ieIdOpt.contains(_)))
                .map(_.nonEmpty)
            } yield customId -> isDuplicated
          }
        }
      _ <- ZIOUtils.foreachParN(4)(duplicatedCustomIds) { case (customId, isDuplicated) =>
        ZIOUtils.failWhen(isDuplicated)(
          FundDataValidationError(s"tracking ID '$customId' is already existed")
        )
      }
    } yield ()
  }

  def mergeInvestmentEntities(params: MergeInvestmentEntitiesParams, actor: UserId): Task[MergeInvestmentEntitiesResp] = {
    val srcFirmId = params.srcInvestmentEntityId.parent.parent
    val destFirmId = params.destInvestmentEntityId.parent.parent
    for {
      _ <- ZIOUtils.validate(srcFirmId == destFirmId) {
        FundDataValidationError(s"Source firm $srcFirmId is different from destination firm $destFirmId")
      }
      _ <- fundDataPermissionService.validateUserCanDeleteInvestmentEntity(params.srcInvestmentEntityId, actor)
      _ <- fundDataPermissionService.validateUserCanEditInvestmentEntity(params.destInvestmentEntityId, actor)
      _ <- fundDataProfileConflictService.validateInvestmentEntitiesHaveNoProfileConflict(
        List(params.srcInvestmentEntityId, params.destInvestmentEntityId)
      )
      _ <- fundDataInvestmentEntityDocumentService.fundDataInvestmentEntityDocumentRequestService
        .validateInvestmentEntitiesHaveNoUnfinishedDocumentRequest(
          List(params.srcInvestmentEntityId, params.destInvestmentEntityId)
        )

      destInvestmentEntityModel <- getInvestmentEntityDetail(params.destInvestmentEntityId, actor)
      _ <- mergeInvestmentEntitiesUnsafe(
        srcInvestmentEntityId = params.srcInvestmentEntityId,
        destInvestmentEntityId = params.destInvestmentEntityId,
        investorType = destInvestmentEntityModel.investorType,
        investorOtherType = destInvestmentEntityModel.investorOtherType,
        jurisdictionType = destInvestmentEntityModel.jurisdictionType,
        jurisdictionOtherType = destInvestmentEntityModel.jurisdictionOtherType,
        profileConflictResolveMethod = params.profileConflictResolveMethod,
        customId = destInvestmentEntityModel.customId,
        shouldMoveRiskAssessments = false,
        actor = actor
      )
    } yield MergeInvestmentEntitiesResp()
  }

  private def mergeInvestmentEntitiesUnsafe(
    srcInvestmentEntityId: FundDataInvestmentEntityId,
    destInvestmentEntityId: FundDataInvestmentEntityId,
    customId: String,
    investorType: Option[TagItemId],
    investorOtherType: String,
    jurisdictionType: Option[TagItemId],
    jurisdictionOtherType: String,
    profileConflictResolveMethod: ProfileConflictResolveMethod,
    shouldMoveRiskAssessments: Boolean,
    actor: UserId
  ) = {
    for {
      // handle assessments
      _ <- ZIOUtils.when(shouldMoveRiskAssessments) {
        fundDataInvestmentEntityAssessmentService.copyAssessmentsFromInvestmentEntityUnsafe(
          srcInvestmentEntityId,
          destInvestmentEntityId,
          actor
        )
      }

      // handle contacts
      _ <- fundDataInvestmentEntityContactService.copyContactsFromInvestmentEntityUnsafe(
        srcInvestmentEntityId,
        destInvestmentEntityId,
        actor
      )

      // handle documents
      _ <- fundDataInvestmentEntityDocumentService.copyDocumentsFromInvestmentEntityUnsafe(
        srcInvestmentEntityId,
        destInvestmentEntityId,
        actor
      )

      // handle profile
      _ <- fundDataInvestmentEntityProfileService.mergeProfileWhenMergingInvestmentEntity(
        srcInvestmentEntityId,
        destInvestmentEntityId,
        profileConflictResolveMethod,
        actor
      )

      // handle subscriptions
      _ <- fundDataFundSubscriptionService.transferLinkSubscriptionsFromInvestmentEntityUnsafe(
        srcInvestmentEntityId,
        destInvestmentEntityId,
        actor
      )

      // delete src entity
      _ <- deleteInvestmentEntityUnsafe(
        investmentEntityId = srcInvestmentEntityId,
        actor = actor,
        actionType = FundDataInvestmentEntityMergeInvestmentEntities()
      )

      // handle metadata
      _ <- editInvestmentEntityInternal(
        investmentEntityId = destInvestmentEntityId,
        customId = Some(customId),
        investorOtherType = Some(investorOtherType),
        jurisdictionOtherType = Some(jurisdictionOtherType),
        investorType = UpdateOptionOperator(Some(investorType)),
        jurisdictionType = UpdateOptionOperator(Some(jurisdictionType)),
        actor = actor
      )
    } yield ()
  }

  def moveInvestmentEntity(params: MoveInvestmentEntityParams, actor: UserId): Task[MoveInvestmentEntityResp] = {
    val srcFirmId = params.srcInvestmentEntityId.parent.parent
    val destFirmId = params.destInvestorId.parent
    for {
      _ <- ZIOUtils.validate(srcFirmId == destFirmId) {
        FundDataValidationError(s"Source firm $srcFirmId is different from destination firm $destFirmId")
      }
      _ <- ZIOUtils.validate(params.srcInvestmentEntityId.parent != params.destInvestorId) {
        FundDataValidationError(
          s"Investment entity ${params.srcInvestmentEntityId} is already in investor ${params.destInvestorId}"
        )
      }
      _ <- fundDataPermissionService.validateUserCanCreateInvestmentEntity(params.destInvestorId, actor)
      _ <- fundDataPermissionService.validateUserCanDeleteInvestmentEntity(params.srcInvestmentEntityId, actor)
      _ <- fundDataProfileConflictService.validateInvestmentEntitiesHaveNoProfileConflict(
        List(params.srcInvestmentEntityId)
      )
      _ <- fundDataInvestmentEntityDocumentService.fundDataInvestmentEntityDocumentRequestService
        .validateInvestmentEntitiesHaveNoUnfinishedDocumentRequest(List(params.srcInvestmentEntityId))
      srcInvestmentEntityModel <- getInvestmentEntityDetail(params.srcInvestmentEntityId, actor)
      destInvestmentEntityId <- createInvestmentEntityUnsafe(
        investorId = params.destInvestorId,
        name = srcInvestmentEntityModel.name,
        customId = "",
        jurisdictionType = srcInvestmentEntityModel.jurisdictionType,
        jurisdictionOtherType = srcInvestmentEntityModel.jurisdictionOtherType,
        investorType = srcInvestmentEntityModel.investorType,
        investorOtherType = srcInvestmentEntityModel.investorOtherType,
        riskLevelOpt = srcInvestmentEntityModel.riskAssessment,
        actor = actor,
        source = MoveInvestmentEntity(params.srcInvestmentEntityId)
      )
      _ <- mergeInvestmentEntitiesUnsafe(
        srcInvestmentEntityId = params.srcInvestmentEntityId,
        destInvestmentEntityId = destInvestmentEntityId,
        investorType = srcInvestmentEntityModel.investorType,
        investorOtherType = srcInvestmentEntityModel.investorOtherType,
        jurisdictionType = srcInvestmentEntityModel.jurisdictionType,
        jurisdictionOtherType = srcInvestmentEntityModel.jurisdictionOtherType,
        profileConflictResolveMethod = MergeInvestmentEntitiesParams.ProfileConflictResolveMethod.Overwrite,
        customId = srcInvestmentEntityModel.customId,
        shouldMoveRiskAssessments = true,
        actor = actor
      )
    } yield MoveInvestmentEntityResp(destInvestmentEntityId)
  }

  /** Subscribe to fund data event
    */

  private val processFundDataEventConsumer = KafkaSimpleConsumer[FundDataFirmId, FundDataEvent](
    kafkaService = kafkaService,
    topic = fundDataEventService.fundDataEventTopic,
    consumerGroupName = s"${fundDataEventService.fundDataEventTopic.name}-investment-entity-service-consumer",
    handler = fundDataEventHandler
  )

  private def fundDataEventHandler(
    @unused firmId: FundDataFirmId,
    event: FundDataEvent
  ): Task[Unit] = {
    event.detail match {
      case updateProfile: UpdateInvestmentEntityProfileEvent =>
        for {
          shouldUpdateInvestorAndJurisdictionType <- ZIO.attempt(
            updateProfile.profileUpdate match {
              case _: ProfileUpdateManualEdit | _: ProfileUpdateUpgradeFormVersion | _: ProfileUpdateInitial |
                  _: ProfileUpdateClear | _: ProfileUpdateEmpty | _: ProfileUpdateMergeInvestmentEntities |
                  _: ProfileUpdateMoveInvestmentEntity | ProfileUpdate.Empty =>
                false
              case _: ProfileUpdateImportSubscription | _: ProfileUpdatePublicAPI |
                  _: ProfileUpdateImportFromSpreadsheet =>
                true
            }
          )
          _ <- ZIOUtils.when(shouldUpdateInvestorAndJurisdictionType)(
            updateInvestorAndJurisdictionTypeWhenProfileUpdate(
              updateProfile.investmentEntityId,
              event.actor
            )
          )
        } yield ()
      case finishManualEditProfile: FinishManualEditInvestmentEntityProfile =>
        updateInvestorAndJurisdictionTypeWhenProfileUpdate(
          finishManualEditProfile.investmentEntityId,
          event.actor
        )
      case _ => ZIO.unit
    }
  }

  private def updateInvestorAndJurisdictionTypeWhenProfileUpdate(
    investmentEntityId: FundDataInvestmentEntityId,
    actor: UserId
  ): Task[Unit] = {
    val firmId = investmentEntityId.parent.parent
    for {
      firmProfileModel <- FDBRecordDatabase.transact(FundDataFirmProfileStoreOperations.Production)(
        _.get(firmId)
      )
      curInvestorTypeOpt <- fundDataTagService.getTagsByObject(firmId, investmentEntityId)(
        using InvestorType
      )
      curJurisdictionTypeOpt <- fundDataTagService.getTagsByObject(firmId, investmentEntityId)(
        using Jurisdiction
      )
      shouldUpdateInvestorType = curInvestorTypeOpt.isEmpty && firmProfileModel.investorTypeFieldMapping.nonEmpty
      shouldUpdateJurisdictionType =
        curJurisdictionTypeOpt.isEmpty && firmProfileModel.jurisdictionTypeFieldMapping.nonEmpty
      _ <- ZIOUtils.when(shouldUpdateInvestorType || shouldUpdateJurisdictionType) {
        for {
          (profileFormId, profileData) <- FDBRecordDatabase.transact(
            InvestmentEntityProfileStoreProvider.Production,
            FormVersionDataStoreProvider.Production
          ) { (profileStore, dataStore) =>
            val ops = InvestmentEntityProfileStoreOperations(profileStore, dataStore)
            for {
              profileModel <- ops.getProfile(investmentEntityId)
              profileData <- ops.dataOps.get(profileModel.profileFormDataId)
            } yield profileModel.profileFormId -> profileData
          }
          profileForm <- formService
            .getForm(
              formId = profileFormId.parent,
              versionIdOpt = Some(profileFormId),
              actor,
              shouldCheckPermission = false
            )
            .map(_.formData)
          _ <- ZIOUtils.when(shouldUpdateInvestorType) {
            for {
              investorTypeList <- fundDataTagService.getFirmTagListInternal(firmId)(
                using InvestorType
              )
              toUpdateInvestorTypeOpt = FundDataTagUtils.computeTagFromProfile(
                profileForm,
                profileData,
                firmProfileModel.investorTypeFieldMapping.toList,
                investorTypeList
              )
              _ <- ZIOUtils.traverseOptionUnit(toUpdateInvestorTypeOpt) { toUpdateInvestorType =>
                editInvestmentEntityInternal(
                  investmentEntityId,
                  investorType = UpdateOptionOperator(newValueOpt = Some(Some(toUpdateInvestorType.tagItemId))),
                  actor = actor
                )
              }
            } yield ()
          }
          _ <- ZIOUtils.when(shouldUpdateJurisdictionType)(
            for {
              jurisdictionTypeList <- fundDataTagService.getFirmTagListInternal(firmId)(
                using Jurisdiction
              )
              toUpdateJurisdictionTypeOpt = FundDataTagUtils.computeTagFromProfile(
                profileForm,
                profileData,
                firmProfileModel.jurisdictionTypeFieldMapping.toList,
                jurisdictionTypeList
              )
              _ <- ZIOUtils.traverseOptionUnit(toUpdateJurisdictionTypeOpt) { toUpdateJurisdictionType =>
                editInvestmentEntityInternal(
                  investmentEntityId,
                  jurisdictionType = UpdateOptionOperator(newValueOpt = Some(Some(toUpdateJurisdictionType.tagItemId))),
                  actor = actor
                )
              }
            } yield ()
          )

        } yield ()
      }
    } yield ()
  }

  override def start(): Task[Unit] = {
    processFundDataEventConsumer.start()
  }

  override def close(): Task[Unit] = {
    processFundDataEventConsumer.close()
  }

}

object FundDataInvestmentEntityService {

  final case class CheckDuplicatedCustomIdParams(
    customId: String,
    ignoreInvestmentEntityIdOpt: Option[FundDataInvestmentEntityId] = None
  )

  def verifyInvestmentEntitiesExisted(firmId: FundDataFirmId, investmentEntityIds: List[FundDataInvestmentEntityId])
    : Task[Boolean] = {
    FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production) { ops =>
      RecordIO
        .parTraverseN(8)(investmentEntityIds) { investmentEntityId =>
          ops.getOpt(investmentEntityId).map(_.exists(_.investmentEntityId.parent.parent == firmId))
        }
        .map(_.forall(identity))
    }
  }

}
