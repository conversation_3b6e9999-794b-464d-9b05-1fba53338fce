// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.firm

import org.apache.poi.ss.usermodel.{IndexedColors, VerticalAlignment}
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import sttp.model.MediaType
import zio.{Task, ZIO}

import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.documentcontent.spreadsheet.{FillSheetData, FillSpreadsheet, SpreadsheetUtils}
import anduin.documentcontent.spreadsheet.FillSheetData.{FillSheetCell, FillSheetRow}
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{DefaultCluster, FDBOperations, FDBRecordDatabase}
import anduin.forms.diff.Utils
import anduin.forms.diff.Utils.ExtractedInputableField
import anduin.forms.{Form, FormData}
import anduin.forms.engine.GaiaEngine.{EngineConfiguration, EngineContext}
import anduin.forms.engine.{GaiaEngine, GaiaState}
import anduin.forms.model.Schema
import anduin.forms.model.template.DataTemplateModels.SpreadsheetTemplateType
import anduin.forms.service.FormService
import anduin.forms.storage.{
  DataTemplateVersionStoreOperations,
  FormTemplateMappingStoreOperations,
  FormVersionDataStoreProvider
}
import anduin.forms.transfer.DataTransferEngine
import anduin.forms.transfer.DataTransferEngine.ExecuteMode
import anduin.forms.ui.{Widget, WidgetType}
import anduin.forms.util.TextUtils
import anduin.forms.utils.{FormDataUtils, FormTemplateMappingUtils}
import anduin.funddata.endpoint.firm.profile.*
import anduin.funddata.error.FundDataError.FundDataValidationError
import anduin.funddata.firm.FundDataFirmProfileImportExportService.*
import anduin.funddata.investmententity.FundDataInvestmentEntityExportHelper
import anduin.funddata.investmententity.profile.{
  InvestmentEntityProfileStoreOperations,
  InvestmentEntityProfileStoreProvider
}
import anduin.funddata.permission.{FundDataPermissionService, FundDataRebacModel}
import anduin.id.form.{FormTemplateMappingVersionId, FormVersionId}
import anduin.id.funddata.{FundDataFirmId, FundDataInvestmentEntityId}
import anduin.model.common.user.UserId
import anduin.model.id.FileId
import anduin.portaluser.ExecutiveAdmin
import anduin.protobuf.funddata.investmententity.profile.FundDataInvestmentEntityProfileModel
import anduin.service.GeneralServiceException
import anduin.storageservice.common.FileContentOrigin
import anduin.utils.StringUtils
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FundDataFirmProfileImportExportService(
  fundDataPermissionService: FundDataPermissionService,
  formService: FormService,
  executiveAdmin: ExecutiveAdmin
)(
  using val fileService: FileService
) {

  private val EmptyCell = FillSheetCell("")
  private val FieldLabelMaxLength = 300
  private val OptionLabelMaxLength = 80

  private[funddata] def importProfileData(
    firmId: FundDataFirmId,
    profileForm: Form,
    profileDataOpt: Option[GaiaState],
    templateMappingVersionIdOpt: Option[FormTemplateMappingVersionId],
    importData: Map[String, String],
    executeMode: ExecuteMode
  ): Task[GaiaState] = {
    for {
      firmProfileModel <- FDBRecordDatabase.transact(FundDataFirmProfileStoreOperations.Production)(
        _.get(firmId)
      )
      templateMappingId <- verifyTemplateMapping(
        templateMappingIdOpt = templateMappingVersionIdOpt,
        availableCustomTemplateIds = firmProfileModel.importMappingIds,
        allowDefaultTemplate = firmProfileModel.enableDefaultImportTemplate
      )
      importedProfileData <-
        if (templateMappingId == FormTemplateMappingVersionId.defaultValue.get) {
          importProfileDataUsingDefaultTemplateMapping(
            profileForm,
            profileDataOpt,
            importData
          )
        } else {
          importProfileDataUsingCustomTemplateMapping(
            profileForm,
            profileDataOpt,
            templateMappingVersionId = templateMappingId,
            importData = importData,
            executeMode = executeMode
          )
        }
    } yield importedProfileData
  }

  private def verifyTemplateMapping(
    templateMappingIdOpt: Option[FormTemplateMappingVersionId],
    availableCustomTemplateIds: Seq[FormTemplateMappingVersionId],
    allowDefaultTemplate: Boolean
  ): Task[FormTemplateMappingVersionId] = {
    for {
      availableTemplateIds <-
        if (allowDefaultTemplate) {
          ZIO.attempt(availableCustomTemplateIds.appended(FormTemplateMappingVersionId.defaultValue.get).distinct)
        } else {
          ZIO.attempt(availableCustomTemplateIds.distinct)
        }
      templateMappingId <- templateMappingIdOpt.fold(
        availableTemplateIds.toList match {
          case availableTemplateId :: Nil => ZIO.succeed(availableTemplateId)
          case _ =>
            ZIO.fail(FundDataValidationError(s"select one of templates: ${availableTemplateIds.mkString(", ")}"))
        }
      ) { templateId =>
        if (availableTemplateIds.contains(templateId)) {
          ZIO.succeed(templateId)
        } else {
          ZIO.fail(
            FundDataValidationError(
              s"template ID does not exist; select one of templates: ${availableTemplateIds.mkString(", ")}"
            )
          )
        }
      }
    } yield templateMappingId
  }

  private def importProfileDataUsingDefaultTemplateMapping(
    profileForm: Form,
    profileDataOpt: Option[GaiaState],
    importData: Map[String, String]
  ): Task[GaiaState] = {
    for {
      engine <- ZIOUtils.optionToTask(
        GaiaEngine
          .make(
            profileForm,
            EngineConfiguration.default,
            EngineContext.default
          )
          .toOption,
        GeneralServiceException(s"Failed to initialize form engine")
      )
      importJsonData <- ZIO.attempt(FormDataUtils.rawDataToJsonValue(profileForm, importData))
      result <- ZIO.attempt(
        engine.importData(Seq(Form.DefaultNamespace -> importJsonData), initialStateOpt = profileDataOpt)
      )
      importedData <- ZIOUtils.eitherToTask(
        result.fold(err => Left(GeneralServiceException(s"Cannot import data err = $err")), res => Right(res.state))
      )
    } yield importedData
  }

  private def importProfileDataUsingCustomTemplateMapping(
    profileForm: Form,
    profileDataOpt: Option[GaiaState],
    templateMappingVersionId: FormTemplateMappingVersionId,
    importData: Map[String, String],
    executeMode: ExecuteMode
  ): Task[GaiaState] = {
    for {
      (templateType, mappingItems) <- FDBRecordDatabase.transact(
        FDBOperations[(FormTemplateMappingStoreOperations, DataTemplateVersionStoreOperations)].Production
      ) { case (mappingOps, templateOps) =>
        for {
          dataTemplateVersionId <- mappingOps.versionOps.get(templateMappingVersionId).map(_.templateVersionIdUnsafe)
          mappingItems <- mappingOps.contentOps.get(templateMappingVersionId).map(_.mappingItems)
          templateType <- templateOps.get(dataTemplateVersionId).map(_.template)
        } yield templateType -> mappingItems
      }
      result <- ZIO.attemptBlocking {
        DataTransferEngine.importTemplateData(
          form = profileForm,
          template = templateType,
          mappingItems = mappingItems,
          sourceData = importData,
          formInitialStateOpt = profileDataOpt,
          executeMode = executeMode
        )
      }
      importedData <- ZIOUtils.eitherToTask(
        result.fold(
          err => Left(GeneralServiceException(s"Cannot import data err = $err")),
          res => Right(res.transferredData)
        )
      )
    } yield importedData
  }

  def setEnableDefaultImportTemplate(
    params: SetEnableProfileDefaultImportTemplateParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"User $actor set enable profile default import template for ${params.firmId} to ${params.isEnabled}"
      )
      _ <- FDBRecordDatabase.transact(FundDataFirmProfileStoreOperations.Production)(
        _.update(params.firmId)(
          _.copy(enableDefaultImportTemplate = params.isEnabled)
        )
      )
    } yield ()
  }

  def getDefaultImportTemplateFile(
    params: GetFirmProfileDefaultImportTemplateFileParams,
    actor: UserId
  ): Task[GetFirmProfileDefaultImportTemplateFileResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor gets default import template for ${params.firmId}")
      fileId <- generateDefaultImportTemplateFile(params.firmId, actor)
    } yield GetFirmProfileDefaultImportTemplateFileResponse(fileId)
  }

  // TODO: @poor future enhancements
  // 1. Consider combine this method with FormDataService.innerGenerateDefaultImportTemplateFile
  // 2. Consider cache the default import template file since it's fixed per form version,
  //    instead of generate a new one everytime
  private[firm] def generateDefaultImportTemplateFile(
    firmId: FundDataFirmId,
    actor: UserId
  ): Task[FileId] = {
    for {
      firmModel <- FDBRecordDatabase.transact(FundDataFirmStoreOperations.Production)(_.get(firmId))
      extractedFields <- extractProfileFields(firmModel.profileTemplateId, actor)
      sheetData = computeDefaultImportTemplateSheetData(extractedFields)
      templateByteStream <- ZIO
        .acquireReleaseWith(
          ZIO.attempt(new XSSFWorkbook())
        ) { workbook =>
          ZIO.succeed(workbook.close())
        } { workbook =>
          val sheet = workbook.createSheet("Data to Import")
          for {
            _ <- ZIO.attempt {
              FillSpreadsheet.fillSheet(sheet, sheetData)
            }
            resultSource <- SpreadsheetUtils.workbookToStream(workbook)
          } yield resultSource
        }
      folderId <- fileService.createUserTemporaryFolderIfNeeded(actor)
      generatedFileId <- fileService.uploadFile(
        folderId,
        s"IDM_${firmModel.name}_Default profile import template.xlsx",
        FileContentOrigin.FromSource(
          templateByteStream,
          MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        ),
        actor
      )
    } yield generatedFileId
  }

  private def computeDefaultImportTemplateSheetData(
    extractedFields: List[ExtractedInputableField]
  ): FillSheetData = {
    val sectionRow = computeDefaultSectionRow(
      extractedFields,
      prefixCells = List(
        FillSheetCell(
          "Section",
          setBold = true,
          setVerticalAlignment = Some(VerticalAlignment.TOP)
        ),
        EmptyCell
      )
    )
    val questionRow = computeDefaultQuestionRow(
      extractedFields,
      prefixCells = List(FillSheetCell("Question", setVerticalAlignment = Some(VerticalAlignment.TOP)), EmptyCell)
    )
    val widgetRow = FillSheetRow(
      cells = List(FillSheetCell("Widget Type"), EmptyCell) ++
        extractedFields.map(field => FillSheetCell(field.widget.widgetType.entryName))
    )
    val optionRow = FillSheetRow(
      cells = List(
        FillSheetCell("Options", setVerticalAlignment = Some(VerticalAlignment.TOP)),
        FillSheetCell(
          "Only key in the Value to import (inside the << >> symbols). If empty, it's open text.",
          setItalic = true,
          setWrapText = true,
          setVerticalAlignment = Some(VerticalAlignment.TOP)
        )
      ) ++
        extractedFields.map(field =>
          FillSheetCell(
            field.fieldOptionWithLabels
              .map { case (optionValue, optionLabel) =>
                s"<<$optionValue>> ${getTextFromHtml(optionLabel, maxLength = OptionLabelMaxLength)}"
              }
              .mkString("\n")
          )
        )
    )
    val fieldAlias = FillSheetRow(
      cells = List(
        FillSheetCell("Header row", setBold = true, setColor = Some(IndexedColors.RED)),
        FillSheetCell(
          "Investment Entity name or tracking ID",
          setBold = true,
          setWrapText = true,
          setVerticalAlignment = Some(VerticalAlignment.TOP)
        )
      ) ++
        extractedFields.map(field =>
          FillSheetCell(
            field.fieldKey,
            setColor = Some(IndexedColors.GREY_50_PERCENT)
          )
        )
    )

    FillSheetData(
      startRow = 0,
      startCol = 0,
      rows = List(
        sectionRow,
        questionRow,
        widgetRow,
        optionRow,
        fieldAlias
      ),
      freezeCol = 2,
      freezeRow = 2,
      defaultColumnWidth = Some(24)
    )
  }

  private def computeDefaultSectionRow(
    extractedFields: List[ExtractedInputableField],
    prefixCells: List[FillSheetCell] = List.empty
  ): FillSheetRow = {
    val mergedSectionCells = extractedFields
      .map(_.pageTitleOpt.getOrElse(""))
      .scanLeft("" -> 1) { case ((previousSection, count), section) =>
        section -> (if (section.nonEmpty && section == previousSection) count + 1 else 1)
      }
      .drop(1)
      .appended("" -> 1)
      .sliding(2)
      .collect {
        case section :: Nil                                                => section
        case firstSection :: secondSection :: Nil if secondSection._2 == 1 => firstSection
      }
      .map { case (sectionLabel, count) =>
        FillSheetCell(
          sectionLabel,
          colSpan = count,
          setBold = true,
          setWrapText = true,
          setVerticalAlignment = Some(VerticalAlignment.TOP)
        )
      }
    FillSheetRow(cells = prefixCells ++ mergedSectionCells)
  }

  private def computeDefaultQuestionRow(
    extractedFields: List[ExtractedInputableField],
    prefixCells: List[FillSheetCell] = List.empty
  ): FillSheetRow = {
    FillSheetRow(
      cells = prefixCells ++
        extractedFields.map { field =>
          val parts = List(
            field.groupLabelOpt.map(getTextFromHtml(_) + " -"),
            Option(getTextFromHtml(field.fieldLabel).trim.stripSuffix("*").trim.stripSuffix(":")),
            field.repeatableIndexOpt.map(index => (index + 1).toString)
          )
          FillSheetCell(
            parts.flatten.mkString(" ") + ":",
            setWrapText = true,
            setVerticalAlignment = Some(VerticalAlignment.TOP)
          )
        }
    )
  }

  def exportProfileData(
    firmId: FundDataFirmId,
    investmentEntityIds: List[FundDataInvestmentEntityId],
    templateMappingVersionIdOpt: Option[FormTemplateMappingVersionId],
    actor: UserId,
    exportFormat: ExportFormat = ExportFormat.ValueRaw
  ): Task[ExportProfileDataResp] = {
    for {
      _ <- fundDataPermissionService.validateHasPermissionOnMultipleInvestmentEntities(
        actor,
        FundDataRebacModel.InvestmentEntityPermission.CanViewProfile,
        investmentEntityIds
      )
      firmProfileModel <- FDBRecordDatabase.transact(FundDataFirmProfileStoreOperations.Production)(
        _.get(firmId)
      )
      templateMappingId <- verifyTemplateMapping(
        templateMappingIdOpt = templateMappingVersionIdOpt,
        availableCustomTemplateIds = firmProfileModel.exportMappingIds,
        allowDefaultTemplate = firmProfileModel.enableDefaultExportTemplate
      )
      profiles <- FDBRecordDatabase.transact(
        InvestmentEntityProfileStoreProvider.Production,
        FormVersionDataStoreProvider.Production
      ) { (profileStore, dataStore) =>
        val ops = InvestmentEntityProfileStoreOperations(profileStore, dataStore)
        RecordIO.parTraverseN(4)(investmentEntityIds) { investmentEntityId =>
          for {
            profileModel <- ops.getProfile(investmentEntityId)
            profileData <- ops.dataOps.get(profileModel.profileFormDataId)
          } yield profileModel -> profileData
        }
      }
      profileForms <-
        ZIOUtils
          .foreachParN(4)(profiles.map(_._1.profileFormId).distinct) { profileFormId =>
            formService
              .getForm(
                formId = profileFormId.parent,
                versionIdOpt = Some(profileFormId),
                shouldCheckPermission = false,
                actor = actor
              )
              .map(resp => profileFormId -> resp.formData)
          }
          .map(_.toMap)
      exportedProfileData <-
        if (templateMappingId == FormTemplateMappingVersionId.defaultValue.get) {
          exportProfileDataUsingDefaultTemplateMapping(profiles, profileForms, exportFormat)
        } else {
          exportProfileDataUsingCustomTemplateMapping(
            profiles,
            profileForms,
            templateMappingVersionId = templateMappingId
          )
        }
    } yield exportedProfileData
  }

  private def exportProfileDataUsingDefaultTemplateMapping(
    profiles: List[(FundDataInvestmentEntityProfileModel, GaiaState)],
    profileForms: Map[FormVersionId, FormData],
    exportFormat: ExportFormat
  ): Task[ExportProfileDataResp] = {
    for {
      extractedFields <- ZIO.attempt {
        profileForms.headOption.fold(List.empty[ExtractedInputableField]) { case (_, profileForm) =>
          extractFields(
            profileForm.form.defaultFormSchema,
            profileForm.form.defaultUiSchema
          )
        }
      }
      exportedData <- ZIOUtils.foreachParN(4)(profiles) { case (profileModel, profileData) =>
        for {
          profileForm <- ZIOUtils.fromOption(
            profileForms.get(profileModel.profileFormId),
            GeneralServiceException(s"Profile form ${profileModel.profileFormId} not found")
          )
          exportedData <- ZIO.attempt(FormDataUtils.jsonValueToRawData(profileForm.form, profileData))
          refinedExportedData = exportFormat match {
            case ExportFormat.ValueRaw =>
              exportedData
            case ExportFormat.ValueLabel =>
              extractedFields.map { field =>
                val fieldValueRaw = exportedData.getOrElse(field.fieldKey, "")
                val fieldValueLabel = field.widget.widgetType match {
                  case WidgetType.Radio | WidgetType.Dropdown =>
                    field.fieldOptionWithLabels
                      .find(_._1 == fieldValueRaw)
                      .fold("")(option => getTextFromHtml(option._2, OptionLabelMaxLength))
                  case WidgetType.MultipleCheckbox | WidgetType.MultipleSuggest =>
                    fieldValueRaw
                      .split(FormDataUtils.valuesSeparator)
                      .map { valueRaw =>
                        field.fieldOptionWithLabels
                          .find(_._1 == valueRaw)
                          .fold("")(option => getTextFromHtml(option._2, OptionLabelMaxLength))
                      }
                      .mkString(FormDataUtils.valuesSeparator)
                  case _ => fieldValueRaw
                }
                field.fieldKey -> fieldValueLabel
              }.toMap
          }
        } yield profileModel.investmentEntityId -> refinedExportedData
      }
      templateFieldColMap = extractedFields.zipWithIndex.map { case (field, index) =>
        (index + 1) -> field.fieldKey
      }.toMap
    } yield ExportProfileDataResp(
      investmentEntityProfileData = exportedData,
      templateMappingVersionId = FormTemplateMappingVersionId.defaultValue.get,
      templateFieldColMap = templateFieldColMap,
      startCol = 1
    )
  }

  private def exportProfileDataUsingCustomTemplateMapping(
    profiles: List[(FundDataInvestmentEntityProfileModel, GaiaState)],
    profileForms: Map[FormVersionId, FormData],
    templateMappingVersionId: FormTemplateMappingVersionId
  ): Task[ExportProfileDataResp] = {
    for {
      (templateType, mappingItems) <- FDBRecordDatabase.transact(
        FDBOperations[(FormTemplateMappingStoreOperations, DataTemplateVersionStoreOperations)].Production
      ) { case (mappingOps, templateOps) =>
        for {
          dataTemplateVersionId <- mappingOps.versionOps.get(templateMappingVersionId).map(_.templateVersionIdUnsafe)
          mappingItems <- mappingOps.contentOps.get(templateMappingVersionId).map(_.mappingItems)
          templateType <- templateOps.get(dataTemplateVersionId).map(_.template)
        } yield templateType -> mappingItems
      }
      exportedData <- ZIOUtils.foreachParN(4)(profiles) { case (profileModel, profileData) =>
        for {
          profileForm <- ZIOUtils.fromOption(
            profileForms.get(profileModel.profileFormId),
            GeneralServiceException(s"Profile form ${profileModel.profileFormId} not found")
          )
          result <- ZIO.attemptBlocking {
            DataTransferEngine.exportTemplateData(
              form = profileForm.form,
              template = templateType,
              mappingItems = mappingItems,
              sourceData = profileData
            )
          }
          exportedState <- ZIOUtils.eitherToTask(
            result.fold(
              err => Left(GeneralServiceException(s"Cannot export data err = $err")),
              res => Right(res._1)
            )
          )
          exportedData = {
            val toExportFields = templateType match {
              case spreadsheet: SpreadsheetTemplateType =>
                spreadsheet.fields.map { field => field.name -> field.refinedName }
            }
            toExportFields.map { case (fieldName, fieldRefinedName) =>
              val valueOpt = exportedState
                .get(fieldRefinedName)
                .map(field => FormTemplateMappingUtils.fieldJsonToString(field.getValue))
              fieldName -> valueOpt.getOrElse("")
            }
          }
        } yield profileModel.investmentEntityId -> exportedData.toMap
      }
      startCol = templateType match {
        case spreadsheet: SpreadsheetTemplateType => spreadsheet.startColOpt.fold(0)(_.max(1) - 1)
      }
      executiveAdminId <- executiveAdmin.userId
      templateFieldColMap <- templateType match {
        case spreadsheet: SpreadsheetTemplateType =>
          FundDataInvestmentEntityExportHelper.getExportTemplateFields(spreadsheet, actor = executiveAdminId)
      }
    } yield ExportProfileDataResp(
      investmentEntityProfileData = exportedData,
      templateMappingVersionId = templateMappingVersionId,
      templateFieldColMap = templateFieldColMap,
      startCol = startCol
    )
  }

  def setEnableDefaultExportTemplate(
    params: SetEnableProfileDefaultExportTemplateParams,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"User $actor set enable profile default export template for ${params.firmId} to ${params.isEnabled}"
      )
      _ <- FDBRecordDatabase.transact(FundDataFirmProfileStoreOperations.Production)(
        _.update(params.firmId)(
          _.copy(enableDefaultExportTemplate = params.isEnabled)
        )
      )
    } yield ()
  }

  def getDefaultExportTemplateFile(
    params: GetFirmProfileDefaultExportTemplateFileParams,
    actor: UserId
  ): Task[GetFirmProfileDefaultExportTemplateFileResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor gets default export template for ${params.firmId}")
      fileId <- generateDefaultExportTemplateFile(params.firmId, actor)
    } yield GetFirmProfileDefaultExportTemplateFileResponse(fileId)
  }

  // TODO: @poor future enhancements
  // 1. Consider move this method to FormDataService
  // 2. Consider cache the default export template file since it's fixed per form version,
  //    instead of generate a new one everytime
  private[funddata] def generateDefaultExportTemplateFile(
    firmId: FundDataFirmId,
    actor: UserId
  ): Task[FileId] = {
    for {
      firmModel <- FDBRecordDatabase.transact(FundDataFirmStoreOperations.Production)(_.get(firmId))
      extractedFields <- extractProfileFields(firmModel.profileTemplateId, actor)
      sheetData = computeDefaultExportTemplateSheetData(extractedFields)
      templateByteStream <- ZIO
        .acquireReleaseWith(
          ZIO.attempt(new XSSFWorkbook())
        ) { workbook =>
          ZIO.succeed(workbook.close())
        } { workbook =>
          val sheet = workbook.createSheet("Profile data")
          for {
            _ <- ZIO.attempt {
              FillSpreadsheet.fillSheet(sheet, sheetData)
            }
            resultSource <- SpreadsheetUtils.workbookToStream(workbook)
          } yield resultSource
        }
      folderId <- fileService.createUserTemporaryFolderIfNeeded(actor)
      generatedFileId <- fileService.uploadFile(
        folderId,
        s"IDM_${firmModel.name}_Default profile export template.xlsx",
        FileContentOrigin.FromSource(
          templateByteStream,
          MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        ),
        actor
      )
    } yield generatedFileId
  }

  private def computeDefaultExportTemplateSheetData(
    extractedFields: List[ExtractedInputableField]
  ): FillSheetData = {
    val sectionRow = computeDefaultSectionRow(
      extractedFields,
      prefixCells = List(
        FillSheetCell(
          "Section",
          setBold = true,
          setVerticalAlignment = Some(VerticalAlignment.TOP)
        )
      )
    )
    val questionRow = computeDefaultQuestionRow(
      extractedFields,
      prefixCells = List(FillSheetCell("Question", setVerticalAlignment = Some(VerticalAlignment.TOP)))
    )
    val fieldAlias = FillSheetRow(
      cells = EmptyCell +:
        extractedFields.map(field =>
          FillSheetCell(
            field.fieldKey,
            setColor = Some(IndexedColors.GREY_50_PERCENT)
          )
        )
    )

    FillSheetData(
      startRow = 0,
      startCol = 0,
      rows = List(
        sectionRow,
        questionRow,
        fieldAlias
      ),
      freezeCol = 1,
      freezeRow = 3,
      defaultColumnWidth = Some(24)
    )
  }

  def extractProfileFields(profileFormId: FormVersionId, actor: UserId): Task[List[ExtractedInputableField]] = {
    for {
      profileForm <- formService
        .getForm(
          formId = profileFormId.parent,
          versionIdOpt = Some(profileFormId),
          actor,
          shouldCheckPermission = false
        )
        .map(_.formData)
      extractedFields = extractFields(
        profileForm.form.defaultFormSchema,
        profileForm.form.defaultUiSchema
      )
    } yield extractedFields
  }

  private[firm] def extractFields(
    schemaObj: Schema.obj,
    uiMap: Map[String, Widget]
  ): List[ExtractedInputableField] = {
    Utils.extractInputableFields(schemaObj, uiMap)
  }

  private def getTextFromHtml(strHtml: String, maxLength: Int = FieldLabelMaxLength): String = {
    StringUtils.truncateWithoutWordCutOff(TextUtils.getTextFromHtml(strHtml), maxLength)
  }

}

object FundDataFirmProfileImportExportService {

  private[anduin] enum ExportFormat {
    case ValueRaw
    case ValueLabel
  }

  private[firm] final case class ExportProfileDataResp(
    investmentEntityProfileData: List[(FundDataInvestmentEntityId, Map[String, String])],
    templateMappingVersionId: FormTemplateMappingVersionId,
    templateFieldColMap: Map[Int, String],
    startCol: Int
  )

}
