//  Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.api.guest

import com.raquo.laminar.api.L.*
import design.anduin.components.toast.Toast
import zio.ZIO

import anduin.funddata.client.FundDataLandingPageEndpointClient
import anduin.funddata.endpoint.landingpage.{BasicLandingPageInfo, GetAccessiblePagesParams}
import anduin.id.funddata.FundDataFirmId
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils

private[funddata] final case class WithAccessiblePage(
  firmId: FundDataFirmId,
  shouldGetSystemPages: Boolean = false,
  render: WithAccessiblePage.RenderProps => HtmlElement
) {

  private val isGettingVar = Var[Boolean](true)
  private val landingPagesVar = Var[List[BasicLandingPageInfo]](List.empty)
  private val getEventBus = new EventBus[Boolean]

  def apply(): HtmlElement = {
    render(
      WithAccessiblePage.RenderProps(
        isGettingVar.signal.distinct,
        landingPagesVar.signal.distinct,
        getEventBus.writer
      )
    ).amend(
      getAccessiblePage(true) --> Observer.empty,
      getEventBus.events.flatMapSwitch { getAccessiblePage } --> Observer.empty
    )
  }

  private def getAccessiblePage(resetGetting: Boolean): EventStream[Unit] = {
    val task = for {
      _ <- ZIO.when(resetGetting)(ZIO.attempt(isGettingVar.set(true)))
      _ <- FundDataLandingPageEndpointClient
        .getAccessiblePages(GetAccessiblePagesParams(firmId, shouldGetSystemPages))
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold(
            _ => {
              Var.set(
                landingPagesVar -> List.empty,
                isGettingVar -> false
              )
              Toast.error("Failed to load accessible pages, try again")
            },
            resp => {
              Var.set(
                landingPagesVar -> resp.pages,
                isGettingVar -> false
              )
            }
          )
        )
    } yield ()

    ZIOUtils.toEventStreamUnsafe(task)
  }

}

private[funddata] object WithAccessiblePage {

  final case class RenderProps(
    isGettingSignal: Signal[Boolean],
    landingPagesSignal: Signal[List[BasicLandingPageInfo]],
    refetch: Observer[Boolean]
  )

}
