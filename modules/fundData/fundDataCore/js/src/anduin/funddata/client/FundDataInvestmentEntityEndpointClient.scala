// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.client

import zio.Task

import anduin.forms.engine.GaiaState
import anduin.funddata.endpoint.firm.{
  ComputeProfilesDataFromSpreadsheetDeprecatedParams,
  ComputeProfilesDataFromSpreadsheetParams,
  ComputeProfilesDataFromSpreadsheetResp,
  GetComputedProfileDataFromSpreadsheetParams,
  ImportProfilesDataFromSpreadsheetParams
}
import anduin.funddata.endpoint.investmententity.*
import anduin.funddata.endpoint.investmententity.note.*
import anduin.funddata.endpoint.note.FundDataNote
import anduin.funddata.endpoint.investmententity.profilehistory.{GetProfileHistoryParams, GetProfileHistoryResponse}
import anduin.funddata.endpoint.request.UpdateRequestReviewParams
import anduin.id.batchaction.BatchActionId
import anduin.id.funddata.*
import anduin.id.funddata.note.FundDataNoteId
import anduin.service.GeneralServiceException
import anduin.tapir.client.{AsyncEndpointClient, AuthenticatedEndpointClient}

object FundDataInvestmentEntityEndpointClient extends AuthenticatedEndpointClient, AsyncEndpointClient {

  /*
    Investment Entity
   */

  val checkDuplicatedIEData
    : CheckDuplicatedIEDataParams => Task[Either[GeneralServiceException, CheckDuplicatedIEDataResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.checkDuplicatedIEData)

  val createInvestmentEntity
    : CreateInvestmentEntityParams => Task[Either[GeneralServiceException, FundDataInvestmentEntityId]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.createInvestmentEntity)

  val editInvestmentEntity: EditInvestmentEntityParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.editInvestmentEntity)

  val deleteInvestmentEntity: DeleteInvestmentEntityParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.deleteInvestmentEntity.toSyncEndpoint)

  val getInvestmentEntities
    : GetInvestmentEntitiesParams => Task[Either[GeneralServiceException, GetInvestmentEntitiesResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getInvestmentEntities)

  val getInvestmentEntity
    : GetInvestmentEntityParams => Task[Either[GeneralServiceException, FundDataInvestmentEntity]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getInvestmentEntity)

  val getInvestorAndInvestmentEntityForSelection: GetInvestorAndInvestmentEntityForSelectionParams => Task[
    Either[GeneralServiceException, List[FundDataInvestorAndInvestmentEntityForSelection]]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getInvestorAndInvestmentEntityForSelection)

  val getInvestmentEntityDetail
    : GetInvestmentEntityDetailParams => Task[Either[GeneralServiceException, FundDataInvestmentEntityDetail]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getInvestmentEntityDetail)

  val getInvestmentEntitiesBasic
    : GetInvestmentEntitiesBasicParams => Task[Either[GeneralServiceException, List[FundDataInvestmentEntityBasic]]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getInvestmentEntitiesBasic)

  val importInvestmentEntitiesBySpreadsheet
    : ImportInvestmentEntitiesBySpreadsheetParams => Task[Either[GeneralServiceException, BatchActionId]] = {
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.importInvestmentEntitiesBySpreadsheet)
  }

  val getDownloadSkippedInvestmentEntitiesReport: GetDownloadSkippedInvestmentEntitiesReportParams => Task[
    Either[GeneralServiceException, GetDownloadSkippedInvestmentEntitiesReportResp]
  ] = {
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getDownloadSkippedInvestmentEntitiesReport)
  }

  val moveInvestmentEntity
    : MoveInvestmentEntityParams => Task[Either[GeneralServiceException, MoveInvestmentEntityResp]] = {
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.moveInvestmentEntity)
  }

  val mergeInvestmentEntities
    : MergeInvestmentEntitiesParams => Task[Either[GeneralServiceException, MergeInvestmentEntitiesResp]] = {
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.mergeInvestmentEntities)
  }

  val exportInvestmentEntitiesToSpreadsheet: ExportInvestmentEntitiesToSpreadsheetParams => Task[
    Either[GeneralServiceException, BatchActionId]
  ] = toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.exportInvestmentEntitiesToSpreadsheet)

  /*
      Investment Entity Contacts
      DO NOT USE, WILL BE DELETED
   */

  val getInvestmentEntityContacts: GetInvestmentEntityContactsParams => Task[
    Either[GeneralServiceException, List[FundDataInvestmentEntityContact]]
  ] = toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getInvestmentEntityContacts)

  val getContactsByInvestmentEntities: GetContactsByInvestmentEntitiesParams => Task[
    Either[GeneralServiceException, Map[FundDataInvestmentEntityId, List[FundDataInvestmentEntityContact]]]
  ] = toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getContactsByInvestmentEntities)

  val createInvestmentEntityContacts: CreateInvestmentEntityContactParams => Task[
    Either[GeneralServiceException, List[FundDataInvestmentEntityContactId]]
  ] = toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.createContactsToInvestmentEntity)

  val updateInvestmentEntityContact: UpdateInvestmentEntityContactParams => Task[
    Either[GeneralServiceException, Unit]
  ] = toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.updateContactToInvestmentEntity)

  val deleteInvestmentEntityContacts: DeleteInvestmentEntityContactParams => Task[
    Either[GeneralServiceException, Unit]
  ] = toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.deleteContactsToInvestmentEntity)

  val importContactsBySpreadsheet: ImportContactsBySpreadsheetParams => Task[
    Either[GeneralServiceException, BatchActionId]
  ] = toClientThrowDecodeAndSecurityFailures(
    FundDataInvestmentEntityEndpoints.importContactsBySpreadsheet
  )

  /*
    Investment Entity Profile
   */

  val getInvestmentEntityProfile: GetInvestmentEntityProfileParams => Task[
    Either[GeneralServiceException, FundDataInvestmentEntityProfile]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getInvestmentEntityProfile)

  val saveInvestmentEntityProfileDataIncremental: SaveInvestmentEntityProfileDataIncrementalParams => Task[
    Either[GeneralServiceException, Unit]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.saveInvestmentEntityProfileDataIncremental)

  val computeProfilesDataFromSpreadsheet: ComputeProfilesDataFromSpreadsheetParams => Task[
    Either[GeneralServiceException, Map[String, ComputeProfilesDataFromSpreadsheetResp]]
  ] = toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.computeProfilesDataFromSpreadsheet)

  val computeProfilesDataFromSpreadsheetDeprecated: ComputeProfilesDataFromSpreadsheetDeprecatedParams => Task[
    Either[GeneralServiceException, BatchActionId]
  ] = toClientThrowDecodeAndSecurityFailures(
    FundDataInvestmentEntityEndpoints.computeProfilesDataFromSpreadsheetDeprecated
  )

  val getComputedProfileDataFromSpreadsheet
    : GetComputedProfileDataFromSpreadsheetParams => Task[Either[GeneralServiceException, GaiaState]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getComputedProfileDataFromSpreadsheet)

  val computeProfileDataFromLinkedSubscriptionOrder: ComputeProfileDataFromLinkedSubscriptionOrderParams => Task[
    Either[GeneralServiceException, ComputeProfileDataFromLinkedSubscriptionOrderResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(
      FundDataInvestmentEntityEndpoints.computeProfileDataFromLinkedSubscriptionOrder
    )

  val computeProfileDataFromConflict: ComputeProfileDataFromConflictParams => Task[
    Either[GeneralServiceException, ComputeProfileDataFromConflictResp]
  ] =
    toClientThrowDecodeAndSecurityFailures(
      FundDataInvestmentEntityEndpoints.computeProfileDataFromConflict
    )

  val importProfilesDataFromSpreadsheet: ImportProfilesDataFromSpreadsheetParams => Task[
    Either[GeneralServiceException, BatchActionId]
  ] = toClientThrowDecodeAndSecurityFailures(
    FundDataInvestmentEntityEndpoints.importProfilesDataFromSpreadsheet
  )

  val importProfileDataFromLinkedSubscriptionOrder: ImportProfileDataFromLinkedSubscriptionOrderParams => Task[
    Either[GeneralServiceException, Unit]
  ] =
    toClientThrowDecodeAndSecurityFailures(
      FundDataInvestmentEntityEndpoints.importProfileDataFromLinkedSubscriptionOrder
    )

  val clearProfileData: ClearProfileDataParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.clearProfileData)

  val getInvestmentEntitiesProfileTemplate: GetInvestmentEntitiesProfileTemplateParams => Task[
    Either[GeneralServiceException, GetInvestmentEntitiesProfileTemplateResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getInvestmentEntitiesProfileTemplate)

  val compareProfileDataWithFormVersion: CompareProfileDataWithFormVersionParams => Task[
    Either[GeneralServiceException, CompareProfileDataWithFormVersionResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.compareProfileDataWithFormVersion)

  val updateProfileDataToFormVersion: UpdateProfileDataToFormVersionParams => Task[
    Either[GeneralServiceException, UpdateProfileDataToFormVersionResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.updateProfileDataToFormVersion)

  /*
    Investment Entity Document
   */

  val addDocumentsManually
    : AddDocumentsManuallyParams => Task[Either[GeneralServiceException, AddDocumentsManuallyResp]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.addDocumentsManually)

  val getInvestmentEntityDocuments: GetInvestmentEntityDocumentsParams => Task[
    Either[GeneralServiceException, List[FundDataInvestmentEntityDocument]]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getInvestmentEntityDocuments)

  val getInvestmentEntityDocumentHistory: GetInvestmentEntityDocumentHistoryParams => Task[
    Either[GeneralServiceException, GetInvestmentEntityDocumentHistoryResp]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getInvestmentEntityDocumentHistory)

  val editInvestmentEntityDocumentDetail: EditInvestmentEntityDocumentDetailParams => Task[
    Either[GeneralServiceException, Unit]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.editInvestmentEntityDocumentDetail)

  val replaceInvestmentEntityDocument: ReplaceInvestmentEntityDocumentParams => Task[
    Either[GeneralServiceException, ReplaceInvestmentEntityDocumentResp]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.replaceInvestmentEntityDocument)

  val renameInvestmentEntityDocument: RenameInvestmentEntityDocumentParams => Task[
    Either[GeneralServiceException, Unit]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.renameInvestmentEntityDocument)

  val getPendingExtractDateDocuments: GetPendingExtractDateDocumentsParams => Task[
    Either[GeneralServiceException, GetPendingExtractDateDocumentsResp]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getPendingExtractDateDocuments)

  val deleteInvestmentEntityDocuments: DeleteInvestmentEntityDocumentsParams => Task[
    Either[GeneralServiceException, Unit]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.deleteInvestmentEntityDocuments)

  val importDocumentsBySpreadsheet
    : BatchImportDocumentsBySpreadsheetParams => Task[Either[GeneralServiceException, BatchActionId]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.importDocumentsBySpreadsheet)

  val sendDocumentExpirationWebhookViaPortal: SendDocumentExpirationWebhookViaPortalParams => Task[
    Either[GeneralServiceException, SendDocumentExpirationWebhookViaPortalResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.sendDocumentExpirationWebhookViaPortal)

  val getDocumentsForInvestors
    : GetDocumentsForInvestorsParams => Task[Either[GeneralServiceException, GetDocumentsForInvestorsResp]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getDocumentsForInvestors)

  val getInvestorDocumentsDownloadUrl: GetInvestorDocumentsDownloadUrlParams => Task[
    Either[GeneralServiceException, GetInvestorDocumentsDownloadUrlResp]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getInvestorDocumentsDownloadUrl)

  /*
    Investment Entity Assessment
   */
  val createAssessment
    : CreateAssessmentParams => Task[Either[GeneralServiceException, FundDataInvestmentEntityAssessmentId]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.createAssessment)

  val getAssessments
    : GetAssessmentsParams => Task[Either[GeneralServiceException, List[FundDataInvestmentEntityAssessment]]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getAssessments)

  val editAssessment: EditAssessmentParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.editAssessment)

  val markAssessmentAsReviewed: MarkAssessmentAsReviewedParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.markAssessmentAsReviewed)

  val deleteAssessments: DeleteAssessmentsParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.deleteAssessments)

  val importRiskAssessmentsBySpreadsheet
    : ImportRiskAssessmentsBySpreadsheetParams => Task[Either[GeneralServiceException, BatchActionId]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.importRiskAssessmentsBySpreadsheet)

  /*
  Investment Entity Document Request
   */

  val createDocumentRequest
    : CreateDocumentRequestParams => Task[Either[GeneralServiceException, CreateDocumentRequestResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.createDocumentRequest)

  val createBatchDocumentRequest
    : CreateBatchDocumentRequestParams => Task[Either[GeneralServiceException, CreateBatchDocumentRequestResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.createBatchDocumentRequest)

  val finishDocumentRequestReview: UpdateRequestReviewParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.finishDocumentRequestReview)

  val getInvestmentEntityProfileHistory
    : GetProfileHistoryParams => Task[Either[GeneralServiceException, GetProfileHistoryResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityEndpoints.getInvestmentEntityProfileHistory)

  /** Note
    */
  val getInvestmentEntityNote
    : GetInvestmentEntityNoteParams => Task[Either[GeneralServiceException, Option[FundDataNote]]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityNoteEndpoints.getInvestmentEntityNote)

  val updateInvestmentEntityNote
    : UpdateInvestmentEntityNoteParams => Task[Either[GeneralServiceException, FundDataNoteId]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityNoteEndpoints.updateInvestmentEntityNote)

  val deleteInvestmentEntityNote: DeleteInvestmentEntityNoteParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundDataInvestmentEntityNoteEndpoints.deleteInvestmentEntityNote)

}
