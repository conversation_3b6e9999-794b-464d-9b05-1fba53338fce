// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.client

import zio.Task

import anduin.funddata.endpoint.integplatform.*
import anduin.service.GeneralServiceException
import anduin.tapir.client.AuthenticatedEndpointClient
import anduin.tapir.endpoint.EmptyResponse

object FundDataIntegPlatformEndpointClient extends AuthenticatedEndpointClient {

  val lookupIntegrations
    : FundDataLookupIntegrationsParams => Task[Either[GeneralServiceException, FundDataLookupIntegrationsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataIntegPlatformEndpoints.lookupIntegrations.toSyncEndpoint)

  val getLinkableInstances
    : FundDataLinkableInstancesParams => Task[Either[GeneralServiceException, FundDataLinkableInstancesResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataIntegPlatformEndpoints.getLinkableInstances.toSyncEndpoint)

  val linkAppConnector: FundDataLinkAppConnectorParams => Task[Either[GeneralServiceException, EmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataIntegPlatformEndpoints.linkAppConnector.toSyncEndpoint)

  val unlinkAppConnector: FundDataUnlinkAppConnectorParams => Task[Either[GeneralServiceException, EmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataIntegPlatformEndpoints.unlinkAppConnector.toSyncEndpoint)

}
