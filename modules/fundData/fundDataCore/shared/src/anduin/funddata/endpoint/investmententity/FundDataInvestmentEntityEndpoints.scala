// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.endpoint.investmententity

import sttp.tapir.*

import anduin.asyncapiv2.execution.AsyncApiWorkflowQueue
import anduin.funddata.endpoint.firm.{
  ComputeProfilesDataFromSpreadsheetDeprecatedParams,
  ComputeProfilesDataFromSpreadsheetParams,
  ComputeProfilesDataFromSpreadsheetResp,
  GetComputedProfileDataFromSpreadsheetParams,
  ImportProfilesDataFromSpreadsheetParams
}
import anduin.id.batchaction.BatchActionId
import anduin.id.funddata.*
import anduin.service.GeneralServiceException
import anduin.tapir.{AsyncEndpoint, AuthenticatedEndpoints}
import anduin.tapir.AuthenticatedEndpoints.BaseAuthenticatedEndpoint
import anduin.model.codec.MapCodecs.given
import anduin.forms.engine.GaiaState
import anduin.funddata.endpoint.investmententity.profilehistory.{GetProfileHistoryParams, GetProfileHistoryResponse}
import anduin.funddata.endpoint.request.UpdateRequestReviewParams
import anduin.tapir.AsyncEndpoint.AsyncAuthenticatedEndpoint

object FundDataInvestmentEntityEndpoints extends AuthenticatedEndpoints, AsyncEndpoint {

  private lazy val Path = "funddata" / "investmententity"

  lazy val checkDuplicatedIEData: BaseAuthenticatedEndpoint[
    CheckDuplicatedIEDataParams,
    GeneralServiceException,
    CheckDuplicatedIEDataResponse
  ] = {
    authEndpoint[
      CheckDuplicatedIEDataParams,
      GeneralServiceException,
      CheckDuplicatedIEDataResponse
    ](
      Path / "checkDuplicatedIEData"
    )
  }

  lazy val exportInvestmentEntitiesToSpreadsheet: BaseAuthenticatedEndpoint[
    ExportInvestmentEntitiesToSpreadsheetParams,
    GeneralServiceException,
    BatchActionId
  ] = {
    authEndpoint[
      ExportInvestmentEntitiesToSpreadsheetParams,
      GeneralServiceException,
      BatchActionId
    ](Path / "exportInvestmentEntitiesToSpreadsheet")
  }

  lazy val createInvestmentEntity: BaseAuthenticatedEndpoint[
    CreateInvestmentEntityParams,
    GeneralServiceException,
    FundDataInvestmentEntityId
  ] = {
    authEndpoint[
      CreateInvestmentEntityParams,
      GeneralServiceException,
      FundDataInvestmentEntityId
    ](
      Path / "createInvestmentEntity"
    )
  }

  lazy val editInvestmentEntity
    : BaseAuthenticatedEndpoint[EditInvestmentEntityParams, GeneralServiceException, Unit] = {
    authEndpoint[EditInvestmentEntityParams, GeneralServiceException, Unit](
      Path / "editInvestmentEntity"
    )
  }

  lazy val deleteInvestmentEntity
    : AsyncAuthenticatedEndpoint[DeleteInvestmentEntityParams, GeneralServiceException, Unit] = {
    asyncEndpoint[DeleteInvestmentEntityParams, GeneralServiceException, Unit](
      Path / "deleteInvestmentEntity",
      AsyncApiWorkflowQueue.Heavy
    )
  }

  lazy val getInvestmentEntities: BaseAuthenticatedEndpoint[
    GetInvestmentEntitiesParams,
    GeneralServiceException,
    GetInvestmentEntitiesResponse
  ] = {
    authEndpoint[
      GetInvestmentEntitiesParams,
      GeneralServiceException,
      GetInvestmentEntitiesResponse
    ](
      Path / "getInvestmentEntities"
    )
  }

  lazy val getInvestmentEntity: BaseAuthenticatedEndpoint[
    GetInvestmentEntityParams,
    GeneralServiceException,
    FundDataInvestmentEntity
  ] = {
    authEndpoint[
      GetInvestmentEntityParams,
      GeneralServiceException,
      FundDataInvestmentEntity
    ](
      Path / "getInvestmentEntity"
    )
  }

  lazy val getInvestorAndInvestmentEntityForSelection: BaseAuthenticatedEndpoint[
    GetInvestorAndInvestmentEntityForSelectionParams,
    GeneralServiceException,
    List[FundDataInvestorAndInvestmentEntityForSelection]
  ] = {
    authEndpoint[
      GetInvestorAndInvestmentEntityForSelectionParams,
      GeneralServiceException,
      List[FundDataInvestorAndInvestmentEntityForSelection]
    ](
      Path / "getInvestorAndInvestmentEntityForSelection"
    )
  }

  lazy val getInvestmentEntitiesBasic: BaseAuthenticatedEndpoint[
    GetInvestmentEntitiesBasicParams,
    GeneralServiceException,
    List[FundDataInvestmentEntityBasic]
  ] = {
    authEndpoint[
      GetInvestmentEntitiesBasicParams,
      GeneralServiceException,
      List[FundDataInvestmentEntityBasic]
    ](
      Path / "getInvestmentEntitiesBasic"
    )
  }

  lazy val getInvestmentEntityDetail: BaseAuthenticatedEndpoint[
    GetInvestmentEntityDetailParams,
    GeneralServiceException,
    FundDataInvestmentEntityDetail
  ] = {
    authEndpoint[
      GetInvestmentEntityDetailParams,
      GeneralServiceException,
      FundDataInvestmentEntityDetail
    ](
      Path / "getInvestmentEntityDetail"
    )
  }

  lazy val importInvestmentEntitiesBySpreadsheet: BaseAuthenticatedEndpoint[
    ImportInvestmentEntitiesBySpreadsheetParams,
    GeneralServiceException,
    BatchActionId
  ] = {
    authEndpoint[
      ImportInvestmentEntitiesBySpreadsheetParams,
      GeneralServiceException,
      BatchActionId
    ](
      Path / "importInvestmentEntitiesBySpreadsheet"
    )
  }

  lazy val getDownloadSkippedInvestmentEntitiesReport: BaseAuthenticatedEndpoint[
    GetDownloadSkippedInvestmentEntitiesReportParams,
    GeneralServiceException,
    GetDownloadSkippedInvestmentEntitiesReportResp
  ] = {
    authEndpoint[
      GetDownloadSkippedInvestmentEntitiesReportParams,
      GeneralServiceException,
      GetDownloadSkippedInvestmentEntitiesReportResp
    ](
      Path / "getDownloadSkippedInvestmentEntitiesReport"
    )
  }

  lazy val moveInvestmentEntity: BaseAuthenticatedEndpoint[
    MoveInvestmentEntityParams,
    GeneralServiceException,
    MoveInvestmentEntityResp
  ] = {
    authEndpoint[
      MoveInvestmentEntityParams,
      GeneralServiceException,
      MoveInvestmentEntityResp
    ](
      Path / "moveInvestmentEntity"
    )
  }

  lazy val mergeInvestmentEntities: BaseAuthenticatedEndpoint[
    MergeInvestmentEntitiesParams,
    GeneralServiceException,
    MergeInvestmentEntitiesResp
  ] = {
    authEndpoint[
      MergeInvestmentEntitiesParams,
      GeneralServiceException,
      MergeInvestmentEntitiesResp
    ](
      Path / "mergeInvestmentEntities"
    )
  }

  private lazy val PathContact = Path / "contact"

  lazy val createContactsToInvestmentEntity: BaseAuthenticatedEndpoint[
    CreateInvestmentEntityContactParams,
    GeneralServiceException,
    List[
      FundDataInvestmentEntityContactId
    ]
  ] = {
    authEndpoint[
      CreateInvestmentEntityContactParams,
      GeneralServiceException,
      List[FundDataInvestmentEntityContactId]
    ](
      PathContact / "createInvestmentEntityContact"
    )
  }

  lazy val updateContactToInvestmentEntity
    : BaseAuthenticatedEndpoint[UpdateInvestmentEntityContactParams, GeneralServiceException, Unit] = {
    authEndpoint[UpdateInvestmentEntityContactParams, GeneralServiceException, Unit](
      PathContact / "updateInvestmentEntityContact"
    )
  }

  lazy val getInvestmentEntityContacts: BaseAuthenticatedEndpoint[
    GetInvestmentEntityContactsParams,
    GeneralServiceException,
    List[
      FundDataInvestmentEntityContact
    ]
  ] = {
    authEndpoint[
      GetInvestmentEntityContactsParams,
      GeneralServiceException,
      List[FundDataInvestmentEntityContact]
    ](
      PathContact / "getInvestmentEntityContact"
    )
  }

  lazy val deleteContactsToInvestmentEntity
    : BaseAuthenticatedEndpoint[DeleteInvestmentEntityContactParams, GeneralServiceException, Unit] = {
    authEndpoint[DeleteInvestmentEntityContactParams, GeneralServiceException, Unit](
      PathContact / "deleteInvestmentEntityContact"
    )
  }

  lazy val getContactsByInvestmentEntities: BaseAuthenticatedEndpoint[
    GetContactsByInvestmentEntitiesParams,
    GeneralServiceException,
    Map[FundDataInvestmentEntityId, List[FundDataInvestmentEntityContact]]
  ] = {
    authEndpoint[
      GetContactsByInvestmentEntitiesParams,
      GeneralServiceException,
      Map[FundDataInvestmentEntityId, List[FundDataInvestmentEntityContact]]
    ](
      PathContact / "getContactsByInvestmentEntities"
    )
  }

  lazy val importContactsBySpreadsheet: BaseAuthenticatedEndpoint[
    ImportContactsBySpreadsheetParams,
    GeneralServiceException,
    BatchActionId
  ] = {
    authEndpoint[
      ImportContactsBySpreadsheetParams,
      GeneralServiceException,
      BatchActionId
    ](
      Path / "importContactsBySpreadsheet"
    )
  }

  /** Profile
    */
  private lazy val PathProfile = Path / "profile"

  lazy val getInvestmentEntityProfile: BaseAuthenticatedEndpoint[
    GetInvestmentEntityProfileParams,
    GeneralServiceException,
    FundDataInvestmentEntityProfile
  ] =
    authEndpoint[
      GetInvestmentEntityProfileParams,
      GeneralServiceException,
      FundDataInvestmentEntityProfile
    ](
      PathProfile / "getInvestmentEntityProfile"
    )

  lazy val saveInvestmentEntityProfileDataIncremental: BaseAuthenticatedEndpoint[
    SaveInvestmentEntityProfileDataIncrementalParams,
    GeneralServiceException,
    Unit
  ] =
    authEndpoint[
      SaveInvestmentEntityProfileDataIncrementalParams,
      GeneralServiceException,
      Unit
    ](
      PathProfile / "saveInvestmentEntityProfileDataIncremental"
    )

  lazy val computeProfilesDataFromSpreadsheet: BaseAuthenticatedEndpoint[
    ComputeProfilesDataFromSpreadsheetParams,
    GeneralServiceException,
    Map[String, ComputeProfilesDataFromSpreadsheetResp]
  ] =
    authEndpoint[
      ComputeProfilesDataFromSpreadsheetParams,
      GeneralServiceException,
      Map[String, ComputeProfilesDataFromSpreadsheetResp]
    ](
      PathProfile / "computeProfilesDataFromSpreadsheet"
    )

  lazy val computeProfilesDataFromSpreadsheetDeprecated: BaseAuthenticatedEndpoint[
    ComputeProfilesDataFromSpreadsheetDeprecatedParams,
    GeneralServiceException,
    BatchActionId
  ] =
    authEndpoint[
      ComputeProfilesDataFromSpreadsheetDeprecatedParams,
      GeneralServiceException,
      BatchActionId
    ](
      PathProfile / "computeProfilesDataFromSpreadsheetDeprecated"
    )

  lazy val importProfilesDataFromSpreadsheet: BaseAuthenticatedEndpoint[
    ImportProfilesDataFromSpreadsheetParams,
    GeneralServiceException,
    BatchActionId
  ] =
    authEndpoint[
      ImportProfilesDataFromSpreadsheetParams,
      GeneralServiceException,
      BatchActionId
    ](
      PathProfile / "importProfilesDataFromSpreadsheet"
    )

  lazy val getComputedProfileDataFromSpreadsheet: BaseAuthenticatedEndpoint[
    GetComputedProfileDataFromSpreadsheetParams,
    GeneralServiceException,
    GaiaState
  ] =
    authEndpoint[
      GetComputedProfileDataFromSpreadsheetParams,
      GeneralServiceException,
      GaiaState
    ](
      PathProfile / "getComputedProfileDataFromSpreadsheet"
    )

  lazy val computeProfileDataFromLinkedSubscriptionOrder: BaseAuthenticatedEndpoint[
    ComputeProfileDataFromLinkedSubscriptionOrderParams,
    GeneralServiceException,
    ComputeProfileDataFromLinkedSubscriptionOrderResponse
  ] =
    authEndpoint[
      ComputeProfileDataFromLinkedSubscriptionOrderParams,
      GeneralServiceException,
      ComputeProfileDataFromLinkedSubscriptionOrderResponse
    ](
      PathProfile / "computeProfileDataFromLinkedSubscriptionOrder"
    )

  lazy val computeProfileDataFromConflict: BaseAuthenticatedEndpoint[
    ComputeProfileDataFromConflictParams,
    GeneralServiceException,
    ComputeProfileDataFromConflictResp
  ] =
    authEndpoint[
      ComputeProfileDataFromConflictParams,
      GeneralServiceException,
      ComputeProfileDataFromConflictResp
    ](
      PathProfile / "computeProfileDataFromConflict"
    )

  lazy val importProfileDataFromLinkedSubscriptionOrder: BaseAuthenticatedEndpoint[
    ImportProfileDataFromLinkedSubscriptionOrderParams,
    GeneralServiceException,
    Unit
  ] =
    authEndpoint[
      ImportProfileDataFromLinkedSubscriptionOrderParams,
      GeneralServiceException,
      Unit
    ](
      PathProfile / "importProfileDataFromLinkedSubscriptionOrder"
    )

  lazy val clearProfileData: BaseAuthenticatedEndpoint[ClearProfileDataParams, GeneralServiceException, Unit] =
    authEndpoint[ClearProfileDataParams, GeneralServiceException, Unit](
      PathProfile / "clearProfileData"
    )

  lazy val getInvestmentEntitiesProfileTemplate: BaseAuthenticatedEndpoint[
    GetInvestmentEntitiesProfileTemplateParams,
    GeneralServiceException,
    GetInvestmentEntitiesProfileTemplateResponse
  ] =
    authEndpoint[
      GetInvestmentEntitiesProfileTemplateParams,
      GeneralServiceException,
      GetInvestmentEntitiesProfileTemplateResponse
    ](
      PathProfile / "getInvestmentEntitiesProfileTemplate"
    )

  lazy val compareProfileDataWithFormVersion: BaseAuthenticatedEndpoint[
    CompareProfileDataWithFormVersionParams,
    GeneralServiceException,
    CompareProfileDataWithFormVersionResponse
  ] =
    authEndpoint[
      CompareProfileDataWithFormVersionParams,
      GeneralServiceException,
      CompareProfileDataWithFormVersionResponse
    ](
      PathProfile / "compareProfileDataWithFormVersion"
    )

  lazy val updateProfileDataToFormVersion: BaseAuthenticatedEndpoint[
    UpdateProfileDataToFormVersionParams,
    GeneralServiceException,
    UpdateProfileDataToFormVersionResponse
  ] =
    authEndpoint[
      UpdateProfileDataToFormVersionParams,
      GeneralServiceException,
      UpdateProfileDataToFormVersionResponse
    ](
      PathProfile / "updateProfileDataToFormVersion"
    )

  /** Document
    */
  private lazy val PathDocument = Path / "document"

  lazy val addDocumentsManually: BaseAuthenticatedEndpoint[
    AddDocumentsManuallyParams,
    GeneralServiceException,
    AddDocumentsManuallyResp
  ] =
    authEndpoint[
      AddDocumentsManuallyParams,
      GeneralServiceException,
      AddDocumentsManuallyResp
    ](
      PathDocument / "addDocumentsManually"
    )

  lazy val getInvestmentEntityDocuments: BaseAuthenticatedEndpoint[
    GetInvestmentEntityDocumentsParams,
    GeneralServiceException,
    List[
      FundDataInvestmentEntityDocument
    ]
  ] = {
    authEndpoint[
      GetInvestmentEntityDocumentsParams,
      GeneralServiceException,
      List[FundDataInvestmentEntityDocument]
    ](
      PathDocument / "getInvestmentEntityDocuments"
    )
  }

  lazy val getInvestmentEntityDocumentHistory: BaseAuthenticatedEndpoint[
    GetInvestmentEntityDocumentHistoryParams,
    GeneralServiceException,
    GetInvestmentEntityDocumentHistoryResp
  ] = {
    authEndpoint[
      GetInvestmentEntityDocumentHistoryParams,
      GeneralServiceException,
      GetInvestmentEntityDocumentHistoryResp
    ](
      PathDocument / "getInvestmentEntityDocumentHistory"
    )
  }

  lazy val editInvestmentEntityDocumentDetail: BaseAuthenticatedEndpoint[
    EditInvestmentEntityDocumentDetailParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      EditInvestmentEntityDocumentDetailParams,
      GeneralServiceException,
      Unit
    ](
      PathDocument / "editInvestmentEntityDocumentDetail"
    )
  }

  lazy val replaceInvestmentEntityDocument: BaseAuthenticatedEndpoint[
    ReplaceInvestmentEntityDocumentParams,
    GeneralServiceException,
    ReplaceInvestmentEntityDocumentResp
  ] = {
    authEndpoint[
      ReplaceInvestmentEntityDocumentParams,
      GeneralServiceException,
      ReplaceInvestmentEntityDocumentResp
    ](
      PathDocument / "replaceInvestmentEntityDocument"
    )
  }

  lazy val renameInvestmentEntityDocument
    : BaseAuthenticatedEndpoint[RenameInvestmentEntityDocumentParams, GeneralServiceException, Unit] = {
    authEndpoint[RenameInvestmentEntityDocumentParams, GeneralServiceException, Unit](
      PathDocument / "renameInvestmentEntityDocument"
    )
  }

  lazy val getPendingExtractDateDocuments: BaseAuthenticatedEndpoint[
    GetPendingExtractDateDocumentsParams,
    GeneralServiceException,
    GetPendingExtractDateDocumentsResp
  ] = {
    authEndpoint[
      GetPendingExtractDateDocumentsParams,
      GeneralServiceException,
      GetPendingExtractDateDocumentsResp
    ](
      PathDocument / "getPendingExtractDateDocuments"
    )
  }

  lazy val deleteInvestmentEntityDocuments
    : BaseAuthenticatedEndpoint[DeleteInvestmentEntityDocumentsParams, GeneralServiceException, Unit] = {
    authEndpoint[DeleteInvestmentEntityDocumentsParams, GeneralServiceException, Unit](
      PathDocument / "deleteInvestmentEntityDocuments"
    )
  }

  lazy val importDocumentsBySpreadsheet: BaseAuthenticatedEndpoint[
    BatchImportDocumentsBySpreadsheetParams,
    GeneralServiceException,
    BatchActionId
  ] =
    authEndpoint[
      BatchImportDocumentsBySpreadsheetParams,
      GeneralServiceException,
      BatchActionId
    ](PathDocument / "investmentEntityDocumentImportSpreadsheet")

  lazy val sendDocumentExpirationWebhookViaPortal: BaseAuthenticatedEndpoint[
    SendDocumentExpirationWebhookViaPortalParams,
    GeneralServiceException,
    SendDocumentExpirationWebhookViaPortalResponse
  ] =
    authEndpoint[
      SendDocumentExpirationWebhookViaPortalParams,
      GeneralServiceException,
      SendDocumentExpirationWebhookViaPortalResponse
    ](PathDocument / "sendDocumentExpirationWebhookViaPortal")

  lazy val getDocumentsForInvestors: BaseAuthenticatedEndpoint[
    GetDocumentsForInvestorsParams,
    GeneralServiceException,
    GetDocumentsForInvestorsResp
  ] = authEndpoint[
    GetDocumentsForInvestorsParams,
    GeneralServiceException,
    GetDocumentsForInvestorsResp
  ](PathDocument / "getDocumentsForInvestors")

  lazy val getInvestorDocumentsDownloadUrl: BaseAuthenticatedEndpoint[
    GetInvestorDocumentsDownloadUrlParams,
    GeneralServiceException,
    GetInvestorDocumentsDownloadUrlResp
  ] = authEndpoint[
    GetInvestorDocumentsDownloadUrlParams,
    GeneralServiceException,
    GetInvestorDocumentsDownloadUrlResp
  ](PathDocument / "getInvestorDocumentsDownloadUrl")

  private lazy val PathAssessment = Path / "assessment"

  lazy val createAssessment: BaseAuthenticatedEndpoint[
    CreateAssessmentParams,
    GeneralServiceException,
    FundDataInvestmentEntityAssessmentId
  ] =
    authEndpoint[
      CreateAssessmentParams,
      GeneralServiceException,
      FundDataInvestmentEntityAssessmentId
    ](
      PathAssessment / "createAssessment"
    )

  lazy val importRiskAssessmentsBySpreadsheet: BaseAuthenticatedEndpoint[
    ImportRiskAssessmentsBySpreadsheetParams,
    GeneralServiceException,
    BatchActionId
  ] = authEndpoint[
    ImportRiskAssessmentsBySpreadsheetParams,
    GeneralServiceException,
    BatchActionId
  ](
    PathAssessment / "importRiskAssessmentsBySpreadsheet"
  )

  lazy val getAssessments: BaseAuthenticatedEndpoint[
    GetAssessmentsParams,
    GeneralServiceException,
    List[
      FundDataInvestmentEntityAssessment
    ]
  ] =
    authEndpoint[
      GetAssessmentsParams,
      GeneralServiceException,
      List[FundDataInvestmentEntityAssessment]
    ](
      PathAssessment / "getAssessments"
    )

  lazy val editAssessment: BaseAuthenticatedEndpoint[EditAssessmentParams, GeneralServiceException, Unit] =
    authEndpoint[EditAssessmentParams, GeneralServiceException, Unit](
      PathAssessment / "editAssessment"
    )

  lazy val markAssessmentAsReviewed
    : BaseAuthenticatedEndpoint[MarkAssessmentAsReviewedParams, GeneralServiceException, Unit] =
    authEndpoint[MarkAssessmentAsReviewedParams, GeneralServiceException, Unit](
      PathAssessment / "markAssessmentAsReviewed"
    )

  lazy val deleteAssessments: BaseAuthenticatedEndpoint[DeleteAssessmentsParams, GeneralServiceException, Unit] =
    authEndpoint[DeleteAssessmentsParams, GeneralServiceException, Unit](
      PathAssessment / "deleteAssessments"
    )

  private lazy val PathDocumentRequest = Path / "request" / "document"

  lazy val createDocumentRequest: BaseAuthenticatedEndpoint[
    CreateDocumentRequestParams,
    GeneralServiceException,
    CreateDocumentRequestResponse
  ] =
    authEndpoint[
      CreateDocumentRequestParams,
      GeneralServiceException,
      CreateDocumentRequestResponse
    ](
      PathDocumentRequest / "createDocumentRequest"
    )

  lazy val createBatchDocumentRequest: BaseAuthenticatedEndpoint[
    CreateBatchDocumentRequestParams,
    GeneralServiceException,
    CreateBatchDocumentRequestResponse
  ] = authEndpoint[
    CreateBatchDocumentRequestParams,
    GeneralServiceException,
    CreateBatchDocumentRequestResponse
  ](
    PathDocumentRequest / "createBatchDocumentRequest"
  )

  lazy val finishDocumentRequestReview: BaseAuthenticatedEndpoint[
    UpdateRequestReviewParams,
    GeneralServiceException,
    Unit
  ] = {
    authEndpoint[
      UpdateRequestReviewParams,
      GeneralServiceException,
      Unit
    ](
      PathDocumentRequest / "finishDocumentRequestReview"
    )
  }

  lazy val getInvestmentEntityProfileHistory: BaseAuthenticatedEndpoint[
    GetProfileHistoryParams,
    GeneralServiceException,
    GetProfileHistoryResponse
  ] = {
    authEndpoint[
      GetProfileHistoryParams,
      GeneralServiceException,
      GetProfileHistoryResponse
    ](
      PathDocumentRequest / "getInvestmentEntityProfileHistory"
    )
  }

}
