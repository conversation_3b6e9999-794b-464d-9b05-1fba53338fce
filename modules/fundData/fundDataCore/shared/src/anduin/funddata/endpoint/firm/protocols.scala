// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.endpoint.firm

import anduin.circe.generic.semiauto.CirceCodec
import java.time.Instant

import io.circe.{Codec, Decoder, Encoder}

import anduin.circe.generic.semiauto.{deriveCodecWithDefaults, deriveStringEnumCodec}
import anduin.email.{CustomSmtpServerConfig, CustomSmtpServerConfigParams}
import anduin.enumeration.{StringEnum, StringEnumCompanion}
import anduin.forms.{ExtractedPdf, FormData}
import anduin.forms.engine.GaiaState
import anduin.funddata.endpoint.firm.ComputeProfilesDataFromSpreadsheetParams.ProfileInfo
import anduin.funddata.endpoint.firm.DocumentType.CustomDocumentType
import anduin.funddata.endpoint.firm.GetAllUnlinkedOrganizationsResp.OrganizationInfo
import anduin.funddata.endpoint.firm.GetFirmProfileExportTemplateMappingResp.ExportTemplateMappingVersion
import anduin.funddata.endpoint.firm.GetFirmsByUserIdParams.FirmBasicInfo
import anduin.funddata.endpoint.firm.GetFirmsParams.FilterByEnvironment
import anduin.funddata.endpoint.firm.GetFundSubsByEntityIdResp.EntityFundSubInfo
import anduin.funddata.endpoint.firm.ImportProfilesDataFromSpreadsheetParams.SingleImportProfile
import anduin.funddata.endpoint.firm.RiskLevel.CustomRiskLevel
import anduin.funddata.endpoint.permission.{
  FundDataAnduinAdminValidationParams,
  FundDataFirmValidationParams,
  FundDataInvestmentEntitiesWithinFirmValidationParams
}
import anduin.funddata.endpoint.tag.FundDataTagList
import anduin.id.annotation.AnnotationDocumentVersionId
import anduin.id.batchaction.BatchActionItemId
import anduin.id.contact.ContactId
import anduin.id.entity.EntityId
import anduin.id.environment.EnvironmentId
import anduin.id.form.*
import anduin.id.funddata.*
import anduin.id.fundsub.FundSubId
import anduin.id.sa.{MappingDestinationId, SaProfileId}
import anduin.id.tag.{TagItemId, TagListId}
import anduin.model.codec.MapCodecs.given
import anduin.model.common.user.UserId
import anduin.model.id.FileId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.sa.endpoints.SaProfileFullInfo
import anduin.sa.model.saprofilemapping.mappingdestinationmessage.MappingDestinationMessage
import anduin.sa.model.saprofilemapping.saprofilemappingmessage.SaProfileMappingMessage
import anduin.tag.v2.AddTagItemParams
import io.circe.Json

import anduin.forms.model.annotation.AnnotationDocumentModels.AnnotationDocumentData
import anduin.forms.utils.PdfFieldConversionUtils
import anduin.model.codec.ProtoCodecs.given

final case class FundDataFirm(
  firmId: FundDataFirmId,
  name: String,
  genericTagListId: TagListId,
  riskLevelAssessmentListId: TagListId,
  contactTypeListId: TagListId,
  jurisdictionListId: TagListId,
  documentTypeListId: TagListId, // For onboarding documents, eg: Tax docs, AML-KYC docs.
  investorTypeListId: TagListId,
  opportunityPageTypeListId: TagListId,
  portalDocumentTypeListId: TagListId, // For post-close documents (Portal Documents), eg: Capital Call
  createdAt: Option[Instant],
  createdBy: UserId,
  formName: String,
  formVersionName: String,
  demoInfo: Option[FundDataDemoInfo],
  firmType: FirmType,
  environmentIdOpt: Option[EnvironmentId],
  linkedSaProfileIdOpt: Option[SaProfileId]
) {

  lazy val firmTagListIds: List[TagListId] = List(
    genericTagListId,
    riskLevelAssessmentListId,
    contactTypeListId,
    jurisdictionListId,
    documentTypeListId,
    investorTypeListId,
    opportunityPageTypeListId,
    portalDocumentTypeListId
  )

}

object FundDataFirm {
  given Codec.AsObject[FundDataFirm] = deriveCodecWithDefaults
}

final case class FundDataFirmInfoForLandingPage(
  firmId: FundDataFirmId,
  name: String,
  environmentIdOpt: Option[EnvironmentId],
  opportunityPageTypes: FundDataTagList,
  portalDocumentTypes: FundDataTagList
)

object FundDataFirmInfoForLandingPage {
  given Codec.AsObject[FundDataFirmInfoForLandingPage] = deriveCodecWithDefaults
}

enum FirmType(val value: String, val description: String) extends StringEnum {
  def name: String = value
  case Internal extends FirmType("Internal", "For internal testing, training, troubleshooting, etc")

  case External
      extends FirmType(
        "External",
        "For external testing and validation, not included in billings or analytics"
      )

  case Production extends FirmType("Production", "Live fund of customers, included in billings and analytics")

}

object FirmType extends StringEnumCompanion[FirmType] {
  given Codec[FirmType] = deriveStringEnumCodec
}

final case class FundDataFirmDefaultSetting(
  defaultSettingId: FundDataFirmDefaultSettingId,
  profileTemplateId: FormVersionId,
  riskLevelAssessmentListId: TagListId,
  contactTypeListId: TagListId,
  jurisdictionListId: TagListId,
  documentTypeListId: TagListId,
  investorTypeListId: TagListId,
  opportunityPageTypeListId: TagListId,
  portalDocumentTypeListId: TagListId
)

object FundDataFirmDefaultSetting {
  given Codec.AsObject[FundDataFirmDefaultSetting] = deriveCodecWithDefaults
}

final case class CreateDefaultSettingParams(
  profileTemplateId: FormVersionId,
  riskLevels: List[AddTagItemParams],
  contactTypes: List[AddTagItemParams],
  jurisdiction: List[AddTagItemParams],
  documentTypes: List[AddTagItemParams],
  investorTypes: List[AddTagItemParams],
  opportunityPageTypes: List[AddTagItemParams],
  portalDocumentTypes: List[AddTagItemParams]
) extends FundDataAnduinAdminValidationParams

object CreateDefaultSettingParams {
  given Codec.AsObject[CreateDefaultSettingParams] = deriveCodecWithDefaults
}

final case class GetDefaultSettingsParams() extends FundDataAnduinAdminValidationParams

object GetDefaultSettingsParams {
  given Codec.AsObject[GetDefaultSettingsParams] = deriveCodecWithDefaults
}

final case class SetDefaultSettingProfileTemplateParams(
  defaultSettingId: FundDataFirmDefaultSettingId,
  profileTemplateId: FormVersionId
) extends FundDataAnduinAdminValidationParams

object SetDefaultSettingProfileTemplateParams {
  given Codec.AsObject[SetDefaultSettingProfileTemplateParams] = deriveCodecWithDefaults
}

final case class GetDefaultSettingProfileTemplateParams(
  defaultSettingId: FundDataFirmDefaultSettingId
) extends FundDataAnduinAdminValidationParams

object GetDefaultSettingProfileTemplateParams {
  given Codec.AsObject[GetDefaultSettingProfileTemplateParams] = deriveCodecWithDefaults
}

final case class CreateFirmParams(
  name: String,
  profileTemplateId: FormVersionId,
  riskLevels: List[AddTagItemParams],
  contactTypes: List[AddTagItemParams],
  jurisdiction: List[AddTagItemParams],
  documentTypes: List[AddTagItemParams],
  investorTypes: List[AddTagItemParams],
  opportunityPageTypes: List[AddTagItemParams],
  portalDocumentTypes: List[AddTagItemParams],
  firmType: FirmType
) extends FundDataAnduinAdminValidationParams

object CreateFirmParams {
  given Codec.AsObject[CreateFirmParams] = deriveCodecWithDefaults
}

final case class UpdateFirmTypeParams(
  firmId: FundDataFirmId,
  firmType: FirmType
) extends FundDataAnduinAdminValidationParams

object UpdateFirmTypeParams {
  given Codec.AsObject[UpdateFirmTypeParams] = deriveCodecWithDefaults
}

final case class DeleteFirmParams(
  firmId: FundDataFirmId
) extends FundDataAnduinAdminValidationParams

object DeleteFirmParams {
  given Codec.AsObject[DeleteFirmParams] = deriveCodecWithDefaults
}

final case class RenameFirmParams(
  firmId: FundDataFirmId,
  name: String
) extends FundDataFirmValidationParams
    with FundDataAnduinAdminValidationParams

object RenameFirmParams {
  given Codec.AsObject[RenameFirmParams] = deriveCodecWithDefaults
}

final case class GetFirmsParams(
  filterByEnvironment: Option[FilterByEnvironment] = None
) extends FundDataAnduinAdminValidationParams

object GetFirmsParams {
  given Codec.AsObject[GetFirmsParams] = deriveCodecWithDefaults

  final case class FilterByEnvironment(environmentId: EnvironmentId)

  object FilterByEnvironment {
    given Codec.AsObject[FilterByEnvironment] = deriveCodecWithDefaults
  }

}

final case class GetFirmsByUserIdParams()

object GetFirmsByUserIdParams {

  final case class FirmBasicInfo(
    firmId: FundDataFirmId,
    name: String,
    firmType: FirmType,
    logoUrl: Option[String]
  )

  object FirmBasicInfo {
    given Codec.AsObject[FirmBasicInfo] = deriveCodecWithDefaults
  }

  given Codec.AsObject[GetFirmsByUserIdParams] = deriveCodecWithDefaults
}

final case class GetFirmsByUserIdResp(
  firms: List[FirmBasicInfo]
)

object GetFirmsByUserIdResp {
  given Codec.AsObject[GetFirmsByUserIdResp] = deriveCodecWithDefaults
}

final case class QueryFirmInfoWithAdminAccessParams(
  firmId: FundDataFirmId
) extends FundDataFirmValidationParams
    with FundDataAnduinAdminValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class GetFirmParams(
  firmId: FundDataFirmId
) extends FundDataFirmValidationParams
    with FundDataAnduinAdminValidationParams

object GetFirmParams {
  given Codec.AsObject[GetFirmParams] = deriveCodecWithDefaults
}

final case class GetFirmInfoForLandingPageParams(
  firmId: FundDataFirmId
) extends FundDataFirmValidationParams

object GetFirmInfoForLandingPageParams {
  given Codec.AsObject[GetFirmInfoForLandingPageParams] = deriveCodecWithDefaults
}

/** Firm - SA profile * */
final case class LinkSaProfileToFirmParams(
  firmId: FundDataFirmId,
  saProfileIdOpt: Option[SaProfileId]
) extends FundDataFirmValidationParams
    with FundDataAnduinAdminValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class AnnotationDocPdfInfo(
  pdfDocVersionId: AnnotationDocumentVersionId,
  pdfFileId: FileId,
  pdfData: AnnotationDocumentData
) {

  lazy val extractedPdf: ExtractedPdf = ExtractedPdf(
    name = pdfData.fileName,
    fields = PdfFieldConversionUtils.pdfObjectsToExtractedFields(pdfData.pdfObjects)
  )

}

object AnnotationDocPdfInfo {
  given Codec.AsObject[AnnotationDocPdfInfo] = deriveCodecWithDefaults
}

final case class FundSubWithFormAndPdfInfo(
  fundSubId: FundSubId,
  fundSubName: String,
  fundSubFormVersionId: FormVersionId,
  fundSubPdfs: List[AnnotationDocPdfInfo]
) derives CirceCodec.WithDefaultsAndTypeName

final case class QueryFirmAccessibleFundWithFormAndPdfInfoResponse(
  firmId: FundDataFirmId,
  accessibleFundSubs: List[FundSubWithFormAndPdfInfo]
) derives CirceCodec.WithDefaultsAndTypeName

final case class QueryFirmProfileFormWithSaMappingResponse(
  firmId: FundDataFirmId,
  profileFormData: FormData,
  profileSaMapping: Map[String, String]
) derives CirceCodec.WithDefaultsAndTypeName

final case class QueryFirmSaProfileWithRelatedMappingInfoResponse(
  firmId: FundDataFirmId,
  saProfileInfoOpt: Option[SaProfileFullInfo],
  saProfileMappings: List[SaProfileMappingMessage],
  // Info for related MappingDestinations from saProfileMapping as well as SaProfile's source for each SA
  relatedMappingDestination: List[MappingDestinationMessage],
  mappingDestinationPdfInfoMap: Map[MappingDestinationId, AnnotationDocPdfInfo]
) derives CirceCodec.WithDefaultsAndTypeName

/** Profile
  */
final case class FundDataFirmProfileSetting(
  profileInfoOpt: Option[FundDataProfileTemplate],
  investorTypeFieldMapping: List[String],
  jurisdictionFieldMapping: List[String],
  importTemplateMappingsInfo: List[ProfileTemplateMapping],
  exportTemplateMappingsInfo: List[ProfileTemplateMapping],
  enableViewProfileSummaryTable: Boolean,
  enableDefaultImportTemplate: Boolean,
  enableDefaultExportTemplate: Boolean
)

object FundDataFirmProfileSetting {
  given Codec.AsObject[FundDataFirmProfileSetting] = deriveCodecWithDefaults
}

final case class GetFirmProfileExportTemplateMappingParams(
  firmId: FundDataFirmId
) extends FundDataFirmValidationParams

object GetFirmProfileExportTemplateMappingParams {
  given Codec.AsObject[GetFirmProfileExportTemplateMappingParams] = deriveCodecWithDefaults
}

final case class GetFirmProfileExportTemplateMappingResp(
  templateMappingVersions: List[ExportTemplateMappingVersion]
)

object GetFirmProfileExportTemplateMappingResp {

  given Codec.AsObject[GetFirmProfileExportTemplateMappingResp] = deriveCodecWithDefaults

  final case class ExportTemplateMappingVersion(
    mappingId: FormTemplateMappingVersionId,
    templateName: String
  )

  given Codec.AsObject[ExportTemplateMappingVersion] = deriveCodecWithDefaults

}

final case class SetFirmProfileTemplateParams(
  firmId: FundDataFirmId,
  templateId: FormVersionId
) extends FundDataAnduinAdminValidationParams

object SetFirmProfileTemplateParams {
  given Codec.AsObject[SetFirmProfileTemplateParams] = deriveCodecWithDefaults
}

final case class FundDataProfileTemplate(
  formVersionId: FormVersionId,
  formName: String,
  versionName: String,
  versionNumber: Int
) {
  val nameWithNumber: String = s"$versionNumber. $versionName"
}

object FundDataProfileTemplate {
  given Codec.AsObject[FundDataProfileTemplate] = deriveCodecWithDefaults
}

final case class FundDataProfileImportMappingTemplate(
  mappingId: FormTemplateMappingVersionId,
  templateName: String,
  templateFileId: FileId
)

object FundDataProfileImportMappingTemplate {
  given Codec.AsObject[FundDataProfileImportMappingTemplate] = deriveCodecWithDefaults
}

final case class GetFirmProfileImportMappingTemplatesParams(
  firmId: FundDataFirmId
) extends FundDataFirmValidationParams

object GetFirmProfileImportMappingTemplatesParams {
  given Codec.AsObject[GetFirmProfileImportMappingTemplatesParams] = deriveCodecWithDefaults
}

final case class GetFirmProfileImportMappingTemplatesResp(
  mappings: Seq[FundDataProfileImportMappingTemplate],
  formData: FormData
)

object GetFirmProfileImportMappingTemplatesResp {
  given Codec.AsObject[GetFirmProfileImportMappingTemplatesResp] = deriveCodecWithDefaults
}

final case class GetFirmProfileSettingParams(firmId: FundDataFirmId) extends FundDataAnduinAdminValidationParams

object GetFirmProfileSettingParams {
  given Codec.AsObject[GetFirmProfileSettingParams] = deriveCodecWithDefaults
}

final case class GetFirmProfileFormParams(firmId: FundDataFirmId) extends FundDataAnduinAdminValidationParams

object GetFirmProfileFormParams {
  given Codec.AsObject[GetFirmProfileFormParams] = deriveCodecWithDefaults
}

final case class GetFormTemplateMappingsAndVersionsParams(
  formId: FormId,
  templateType: TemplateMappingType
) extends FundDataAnduinAdminValidationParams

object GetFormTemplateMappingsAndVersionsParams {
  given Codec.AsObject[GetFormTemplateMappingsAndVersionsParams] = deriveCodecWithDefaults
}

final case class ProfileTemplateMapping(
  profileFormVersion: FundDataFormVersion,
  templateMapping: TemplateMapping,
  templateMappingVersion: TemplateMappingVersion
)

object ProfileTemplateMapping {
  given Codec.AsObject[ProfileTemplateMapping] = deriveCodecWithDefaults
}

final case class UpdateFirmProfileTemplateMappingsParams(
  firmId: FundDataFirmId,
  templateMappingIds: List[FormTemplateMappingVersionId],
  templateMappingType: TemplateMappingType
) extends FundDataAnduinAdminValidationParams

object UpdateFirmProfileTemplateMappingsParams {
  given Codec.AsObject[UpdateFirmProfileTemplateMappingsParams] = deriveCodecWithDefaults
}

final case class FormTemplateMappingAndVersions(
  formVersion: FundDataFormVersion,
  templateMapping: TemplateMapping,
  templateMappingVersions: List[TemplateMappingVersion]
)

object FormTemplateMappingAndVersions {
  given Codec.AsObject[FormTemplateMappingAndVersions] = deriveCodecWithDefaults
}

final case class TemplateMapping(
  id: FormTemplateMappingId,
  name: String
)

object TemplateMapping {
  given Codec.AsObject[TemplateMapping] = deriveCodecWithDefaults
}

final case class TemplateMappingVersion(
  id: FormTemplateMappingVersionId,
  name: String,
  versionIndex: Int
) {
  lazy val nameWithNumber: String = s"${versionIndex + 1}. $name"
}

object TemplateMappingVersion {
  given Codec.AsObject[TemplateMappingVersion] = deriveCodecWithDefaults
}

sealed trait TemplateMappingType derives CanEqual {
  def name: String
}

object TemplateMappingType {
  given Codec.AsObject[TemplateMappingType] = deriveCodecWithDefaults

  case object Import extends TemplateMappingType {
    override def name: String = "import"
  }

  case object Export extends TemplateMappingType {
    override def name: String = "export"
  }

  given Codec.AsObject[Import.type] = deriveCodecWithDefaults
  given Codec.AsObject[Export.type] = deriveCodecWithDefaults
}

final case class UpdateEnableViewProfileSummaryTableParams(
  firmId: FundDataFirmId,
  enableViewProfileSummaryTable: Boolean
) extends FundDataAnduinAdminValidationParams

object UpdateEnableViewProfileSummaryTableParams {
  given Codec.AsObject[UpdateEnableViewProfileSummaryTableParams] = deriveCodecWithDefaults
}

final case class GetFormToFormTemplateMappingConfigParams(
  firmId: FundDataFirmId
) extends FundDataAnduinAdminValidationParams

object GetFormToFormTemplateMappingConfigParams {
  given Codec.AsObject[GetFormToFormTemplateMappingConfigParams] = deriveCodecWithDefaults
}

final case class GetFormToFormTemplateMappingConfigResponse(
  profileInfoOpt: Option[FundDataProfileTemplate],
  formConfigs: List[GetFormToFormTemplateMappingConfigResponse.FormToFormTemplateMappingConfig]
)

object GetFormToFormTemplateMappingConfigResponse {

  given Codec.AsObject[GetFormToFormTemplateMappingConfigResponse] = deriveCodecWithDefaults

  final case class FormToFormTemplateMappingConfig(
    form: FundDataForm,
    funds: List[FundInfo],
    templateMappings: List[FundDataFormToFormTemplateMapping]
  )

  object FormToFormTemplateMappingConfig {
    given Codec.AsObject[FormToFormTemplateMappingConfig] = deriveCodecWithDefaults
  }

  final case class FundInfo(
    fundId: FundDataFundId,
    fundName: String,
    formVersion: FundDataFormVersion
  )

  object FundInfo {
    given Codec.AsObject[FundInfo] = deriveCodecWithDefaults
  }

}

final case class ComputeProfilesDataFromSpreadsheetParams(
  firmId: FundDataFirmId,
  profiles: Seq[ProfileInfo]
) extends FundDataFirmValidationParams

object ComputeProfilesDataFromSpreadsheetParams {
  given Codec.AsObject[ComputeProfilesDataFromSpreadsheetParams] = deriveCodecWithDefaults

  final case class ProfileInfo(
    investmentEntityId: FundDataInvestmentEntityId,
    formTemplateMappingVersionId: FormTemplateMappingVersionId,
    data: Map[String, String],
    frontendTrackingId: String
  )

  object ProfileInfo {
    given Codec.AsObject[ProfileInfo] = deriveCodecWithDefaults
  }

}

final case class ComputeProfilesDataFromSpreadsheetDeprecatedParams(
  firmId: FundDataFirmId,
  profiles: Seq[ComputeProfilesDataFromSpreadsheetDeprecatedParams.ProfileInfo]
) extends FundDataFirmValidationParams

object ComputeProfilesDataFromSpreadsheetDeprecatedParams {
  given Codec.AsObject[ComputeProfilesDataFromSpreadsheetDeprecatedParams] = deriveCodecWithDefaults

  final case class ProfileInfo(
    investmentEntityId: FundDataInvestmentEntityId,
    formTemplateMappingVersionId: FormTemplateMappingVersionId,
    data: Map[String, String]
  )

  object ProfileInfo {
    given Codec.AsObject[ProfileInfo] = deriveCodecWithDefaults
  }

}

final case class ImportProfilesDataFromSpreadsheetParams(
  firmId: FundDataFirmId,
  profiles: Seq[SingleImportProfile]
) extends FundDataInvestmentEntitiesWithinFirmValidationParams {

  override def investmentEntityIds: List[FundDataInvestmentEntityId] =
    profiles.map(_.investmentEntityId).distinct.toList

}

object ImportProfilesDataFromSpreadsheetParams {
  given Codec.AsObject[ImportProfilesDataFromSpreadsheetParams] = deriveCodecWithDefaults

  final case class SingleImportProfile(
    investmentEntityId: FundDataInvestmentEntityId,
    investmentEntityNameOrCustomId: String,
    matchedInvestmentEntityName: Boolean,
    profileFormSource: ProfileFormSource,
    visibleNonEmptyFieldCount: Int,
    hiddenNonEmptyFieldCount: Int
  )

  object SingleImportProfile {
    given Codec.AsObject[SingleImportProfile] = deriveCodecWithDefaults
  }

  sealed trait ProfileFormSource derives CanEqual

  object ProfileFormSource {
    final case class FromComputed(formDataId: FormVersionDataId) extends ProfileFormSource
    final case class ManuallyResolved(gaiaState: GaiaState) extends ProfileFormSource
    given Codec.AsObject[ProfileFormSource] = deriveCodecWithDefaults
    given Codec.AsObject[FromComputed] = deriveCodecWithDefaults
    given Codec.AsObject[ManuallyResolved] = deriveCodecWithDefaults
  }

  final case class ImportProfileInfo(
    investmentEntityId: FundDataInvestmentEntityId,
    investmentEntityNameOrCustomId: String,
    matchedInvestmentEntityName: Boolean,
    formVersionDataId: FormVersionDataId,
    visibleNonEmptyFieldCount: Int,
    hiddenNonEmptyFieldCount: Int,
    isManuallyResolved: Boolean
  )

  object ImportProfileInfo {
    given Codec.AsObject[ImportProfileInfo] = deriveCodecWithDefaults
  }

  final case class SingleImportProfileResponse(
    profileConflictIdOpt: Option[FundDataProfileConflictId]
  )

  object SingleImportProfileResponse {
    given Codec.AsObject[SingleImportProfileResponse] = deriveCodecWithDefaults
  }

}

final case class GetComputedProfileDataFromSpreadsheetParams(batchActionItemId: BatchActionItemId)

object GetComputedProfileDataFromSpreadsheetParams {
  given Codec.AsObject[GetComputedProfileDataFromSpreadsheetParams] = deriveCodecWithDefaults
}

final case class ComputeProfilesDataFromSpreadsheetResp(
  investmentEntityId: FundDataInvestmentEntityId,
  frontendTrackingId: String,
  formCoverage: Float,
  visibleNonEmptyFieldCount: Int,
  hiddenNonEmptyFieldCount: Int,
  formVersionDataId: FormVersionDataId,
  unresolvedConflicts: Int
) {
  lazy val hasUnresolvedConflict: Boolean = unresolvedConflicts > 0
}

object ComputeProfilesDataFromSpreadsheetResp {
  given Codec.AsObject[ComputeProfilesDataFromSpreadsheetResp] = deriveCodecWithDefaults
}

final case class FundDataForm(
  formId: FormId,
  name: String
)

object FundDataForm {
  given Codec.AsObject[FundDataForm] = deriveCodecWithDefaults
}

final case class FundDataFormVersion(
  formVersionId: FormVersionId,
  name: String,
  versionNumber: Int,
  createdAt: Option[Instant]
) {
  val nameWithNumber: String = s"$versionNumber. $name"
}

object FundDataFormVersion {
  given Codec.AsObject[FundDataFormVersion] = deriveCodecWithDefaults
}

final case class GetAllFormsParams() extends FundDataAnduinAdminValidationParams

object GetAllFormsParams {
  given Codec.AsObject[GetAllFormsParams] = deriveCodecWithDefaults
}

final case class GetAllFormVersionsParams(formId: FormId) extends FundDataAnduinAdminValidationParams

object GetAllFormVersionsParams {
  given Codec.AsObject[GetAllFormVersionsParams] = deriveCodecWithDefaults
}

final case class UpdateFirmProfileDataMappingParams(
  firmId: FundDataFirmId,
  investorTypeFieldMapping: List[String],
  jurisdictionFieldMapping: List[String]
) extends FundDataAnduinAdminValidationParams

object UpdateFirmProfileDataMappingParams {
  given Codec.AsObject[UpdateFirmProfileDataMappingParams] = deriveCodecWithDefaults
}

final case class CheckFirmProfileDataMappingParams(
  firmId: FundDataFirmId,
  investorTypeFieldMapping: List[String],
  jurisdictionFieldMapping: List[String]
) extends FundDataAnduinAdminValidationParams

object CheckFirmProfileDataMappingParams {
  given Codec.AsObject[CheckFirmProfileDataMappingParams] = deriveCodecWithDefaults
}

final case class CheckFirmProfileDataMappingResponse(
  investorTypeFieldResult: Option[CheckFirmProfileDataMappingResponse.DataMappingToTagResult],
  jurisdictionFieldResult: Option[CheckFirmProfileDataMappingResponse.DataMappingToTagResult]
)

object CheckFirmProfileDataMappingResponse {
  given Codec.AsObject[CheckFirmProfileDataMappingResponse] = deriveCodecWithDefaults

  final case class DataMappingToTagResult(
    optionMappingResult: List[DataMappingOptionToTagResult]
  )

  object DataMappingToTagResult {
    given Codec.AsObject[DataMappingToTagResult] = deriveCodecWithDefaults
  }

  final case class DataMappingOptionToTagResult(
    fieldId: String,
    optionValue: String,
    optionFormattedValue: String,
    mappedTag: Option[String]
  )

  object DataMappingOptionToTagResult {
    given Codec.AsObject[DataMappingOptionToTagResult] = deriveCodecWithDefaults
  }

}

sealed trait VisibleFundType derives CanEqual {
  val name: String
}

object VisibleFundType {
  given Codec.AsObject[VisibleFundType] = deriveCodecWithDefaults

  case object Internal extends VisibleFundType {
    val name = "Internal"
  }

  given Codec.AsObject[Internal.type] = deriveCodecWithDefaults

  case object External extends VisibleFundType {
    val name = "External"
  }

  given Codec.AsObject[External.type] = deriveCodecWithDefaults

  case object Production extends VisibleFundType {
    val name = "Production"
  }

  given Codec.AsObject[Production.type] = deriveCodecWithDefaults

  def getAll(): Seq[VisibleFundType] = {
    Seq(
      Production,
      Internal,
      External
    )
  }

}

final case class OrganizationData(
  firmOrganizationId: FundDataFirmOrganizationId,
  entityId: EntityId,
  name: String,
  numOfFunds: Int,
  visibleFundTypes: Seq[VisibleFundType] = Seq(VisibleFundType.Production),
  numOfDataRooms: Int
)

object OrganizationData {
  given Codec.AsObject[OrganizationData] = deriveCodecWithDefaults
}

final case class GetFundSubsByEntityIdParams(
  firmId: FundDataFirmId,
  entityId: EntityId
) extends FundDataAnduinAdminValidationParams

object GetFundSubsByEntityIdParams {
  given Codec.AsObject[GetFundSubsByEntityIdParams] = deriveCodecWithDefaults
}

final case class GetFundSubsByEntityIdResp(
  entityFundSubInfos: List[EntityFundSubInfo]
)

object GetFundSubsByEntityIdResp {
  given Codec.AsObject[GetFundSubsByEntityIdResp] = deriveCodecWithDefaults

  final case class EntityFundSubInfo(
    fundSubId: FundSubId,
    name: String,
    noOfInvestors: Int,
    fundTypeOpt: Option[VisibleFundType],
    status: FundSubStatus,
    lastActivityAt: Option[Instant]
  )

  given Codec.AsObject[EntityFundSubInfo] = deriveCodecWithDefaults

}

enum FundSubStatus(val value: String) extends StringEnum {
  case Active extends FundSubStatus("Active")
  case InActive extends FundSubStatus("In-active")
}

object FundSubStatus extends StringEnumCompanion[FundSubStatus] {
  given Codec[FundSubStatus] = deriveStringEnumCodec
}

final case class GetDataRoomsByEntityIdParams(
  firmId: FundDataFirmId,
  entityId: EntityId
) extends FundDataAnduinAdminValidationParams

object GetDataRoomsByEntityIdParams {
  given Codec.AsObject[GetDataRoomsByEntityIdParams] = deriveCodecWithDefaults
}

final case class GetDataRoomsByEntityIdResp(
  entityDataRoomInfos: List[GetDataRoomsByEntityIdResp.EntityDataRoomInfo]
)

object GetDataRoomsByEntityIdResp {
  given Codec.AsObject[GetDataRoomsByEntityIdResp] = deriveCodecWithDefaults

  final case class EntityDataRoomInfo(
    dataRoomWorkflowId: DataRoomWorkflowId,
    name: String,
    isLinkActive: Boolean,
    isArchived: Boolean
  )

  object EntityDataRoomInfo {
    given Codec.AsObject[EntityDataRoomInfo] = deriveCodecWithDefaults
  }

}

final case class GetFirmOrganizationsParams(
  firmId: FundDataFirmId
) extends FundDataAnduinAdminValidationParams

object GetFirmOrganizationsParams {
  given Codec.AsObject[GetFirmOrganizationsParams] = deriveCodecWithDefaults
}

final case class LinkFirmOrganizationsParams(
  firmId: FundDataFirmId,
  organizationIds: List[EntityId],
  visibleFundTypes: Seq[VisibleFundType]
) extends FundDataAnduinAdminValidationParams

object LinkFirmOrganizationsParams {
  given Codec.AsObject[LinkFirmOrganizationsParams] = deriveCodecWithDefaults
}

final case class UnlinkFirmOrganizationParams(
  firmOrganizationId: FundDataFirmOrganizationId
) extends FundDataAnduinAdminValidationParams

object UnlinkFirmOrganizationParams {
  given Codec.AsObject[UnlinkFirmOrganizationParams] = deriveCodecWithDefaults
}

final case class GetFirmOrganizationsResp(
  organizations: Seq[OrganizationData]
)

object GetFirmOrganizationsResp {
  given Codec.AsObject[GetFirmOrganizationsResp] = deriveCodecWithDefaults
}

final case class GetAllUnlinkedOrganizationsParams(
  firmId: FundDataFirmId
) extends FundDataAnduinAdminValidationParams

object GetAllUnlinkedOrganizationsParams {
  given Codec.AsObject[GetAllUnlinkedOrganizationsParams] = deriveCodecWithDefaults
}

final case class GetAllUnlinkedOrganizationsResp(
  organizations: Seq[OrganizationInfo]
)

object GetAllUnlinkedOrganizationsResp {

  final case class OrganizationInfo(
    entityId: EntityId,
    name: String,
    funds: List[FundSubInfo],
    dataRoomCount: Int,
    firmInfos: List[FirmInfo]
  ) derives CanEqual

  object OrganizationInfo {
    given Codec.AsObject[OrganizationInfo] = deriveCodecWithDefaults
  }

  final case class FundSubInfo(
    fundSubId: FundSubId,
    fundType: Option[VisibleFundType]
  )

  object FundSubInfo {
    given Codec.AsObject[FundSubInfo] = deriveCodecWithDefaults
  }

  final case class FirmInfo(
    fundDataFirmId: FundDataFirmId,
    name: String
  )

  object FirmInfo {
    given Codec.AsObject[FirmInfo] = deriveCodecWithDefaults
  }

  given Codec.AsObject[GetAllUnlinkedOrganizationsResp] = deriveCodecWithDefaults

}

final case class UpdateVisibleFundTypeFirmOrganizationParams(
  firmOrganizationId: FundDataFirmOrganizationId,
  visibleFundTypes: Seq[VisibleFundType]
) extends FundDataAnduinAdminValidationParams

object UpdateVisibleFundTypeFirmOrganizationParams {
  given Codec.AsObject[UpdateVisibleFundTypeFirmOrganizationParams] = deriveCodecWithDefaults
}

final case class GetFirmFeatureSwitchParams(
  firmId: FundDataFirmId
) extends FundDataAnduinAdminValidationParams
    with FundDataFirmValidationParams

object GetFirmFeatureSwitchParams {
  given Codec.AsObject[GetFirmFeatureSwitchParams] = deriveCodecWithDefaults
}

final case class GetFirmFeatureSwitchResp(
  featureSwitch: FirmFeatureSwitch
)

object GetFirmFeatureSwitchResp {
  given Codec.AsObject[GetFirmFeatureSwitchResp] = deriveCodecWithDefaults
}

final case class FirmFeatureSwitch(
  firmId: FundDataFirmId,
  enableExtractSigningDateFromTaxForm: Boolean,
  enableExtractExpirationDateFromProofOfId: Boolean,
  enableAutoCalculateExpirationDate: Boolean,
  enableLandingPage: Boolean,
  enablePortal: Boolean,
  enableCsaManagement: Boolean
)

object FirmFeatureSwitch {
  given Codec.AsObject[FirmFeatureSwitch] = deriveCodecWithDefaults
}

final case class UpdateFirmFeatureSwitchParams(
  firmId: FundDataFirmId,
  featureSwitchChanges: FirmFeatureSwitchChanges
) extends FundDataAnduinAdminValidationParams
    with FundDataFirmValidationParams

object UpdateFirmFeatureSwitchParams {
  given Codec.AsObject[UpdateFirmFeatureSwitchParams] = deriveCodecWithDefaults
}

final case class FirmFeatureSwitchChanges(
  enableExtractSigningDateFromTaxFormOpt: Option[Boolean] = None,
  enableExtractExpirationDateFromProofOfIdOpt: Option[Boolean] = None,
  enableAutoCalculateExpirationDateOpt: Option[Boolean] = None,
  enableLandingPageOpt: Option[Boolean] = None,
  enablePortalOpt: Option[Boolean] = None,
  enableCsaManagementOpt: Option[Boolean] = None
)

object FirmFeatureSwitchChanges {
  given Codec.AsObject[FirmFeatureSwitchChanges] = deriveCodecWithDefaults
}

final case class GetExpirationDateConfigParams(
  firmId: FundDataFirmId
) extends FundDataAnduinAdminValidationParams
    with FundDataFirmValidationParams

object GetExpirationDateConfigParams {
  given Codec.AsObject[GetExpirationDateConfigParams] = deriveCodecWithDefaults
}

final case class SingleExpirationDateConfigWithId(
  configId: FundDataExpirationDateConfigId,
  config: SingleExpirationDateConfig
)

object SingleExpirationDateConfigWithId {
  given Codec.AsObject[SingleExpirationDateConfigWithId] = deriveCodecWithDefaults
}

enum RiskLevel {
  case CustomRiskLevel(riskLevelOpt: Option[TagItemId])
  case AllRiskLevel
  case NoRiskLevel
}

object RiskLevel {
  given Codec.AsObject[RiskLevel] = deriveCodecWithDefaults
}

enum DocumentType {
  case CustomDocumentType(docTypeOpt: Option[TagItemId])
  case AllDocumentType
  case NoDocumentType
}

object DocumentType {
  given Codec.AsObject[DocumentType] = deriveCodecWithDefaults
}

enum DateType {
  case SigningDate
  case UploadedDate
  case SpecificDate(valueOpt: Option[Instant])
}

object DateType {
  given Codec.AsObject[DateType] = deriveCodecWithDefaults
}

final case class GetExpirationDateConfigResp(
  configs: List[SingleExpirationDateConfigWithId],
  isEnabled: Boolean
)

object GetExpirationDateConfigResp {
  given Codec.AsObject[GetExpirationDateConfigResp] = deriveCodecWithDefaults
}

final case class SingleExpirationDateConfig(
  targetDocumentType: DocumentType = CustomDocumentType(None),
  targetRiskLevel: RiskLevel = CustomRiskLevel(None),
  validityMonths: Int = 0,
  targetDate: Option[DateType] = None,
  lastDayOfYear: Boolean = false
) derives CanEqual

object SingleExpirationDateConfig {
  val MaxValidityMonths: Int = 120
  given Codec.AsObject[SingleExpirationDateConfig] = deriveCodecWithDefaults
}

final case class UpdateExpirationDateConfigParams(
  firmId: FundDataFirmId,
  toAddConfigs: List[SingleExpirationDateConfig],
  updatedExistingConfigs: List[SingleExpirationDateConfigWithId]
) extends FundDataAnduinAdminValidationParams
    with FundDataFirmValidationParams

object UpdateExpirationDateConfigParams {
  given Codec.AsObject[UpdateExpirationDateConfigParams] = deriveCodecWithDefaults
}

final case class UpdateExpirationDateConfigResp()

object UpdateExpirationDateConfigResp {
  given Codec.AsObject[UpdateExpirationDateConfigResp] = deriveCodecWithDefaults
}

final case class FundDataDemoInfo(
  demoFundSubId: FundSubId // the FS that create this FDM demo
)

object FundDataDemoInfo {
  given Codec.AsObject[FundDataDemoInfo] = deriveCodecWithDefaults
}

final case class RecalculateDocumentExpirationDateWithConfigParams(
  configId: FundDataExpirationDateConfigId
) extends FundDataAnduinAdminValidationParams

object RecalculateDocumentExpirationDateWithConfigParams {
  given Codec.AsObject[RecalculateDocumentExpirationDateWithConfigParams] = deriveCodecWithDefaults
}

final case class FundDataFirmWebhookConfig(
  firmId: FundDataFirmId,
  isEnabled: Boolean,
  isEnabledUrlVerificationRequest: Boolean,
  supportedEventTypes: List[String]
)

object FundDataFirmWebhookConfig {
  given Codec.AsObject[FundDataFirmWebhookConfig] = deriveCodecWithDefaults
}

final case class GetFirmWebhookConfigParams(
  firmId: FundDataFirmId
) extends FundDataAnduinAdminValidationParams

object GetFirmWebhookConfigParams {
  given Codec.AsObject[GetFirmWebhookConfigParams] = deriveCodecWithDefaults
}

final case class UpdateFirmWebhookConfigParams(
  firmId: FundDataFirmId,
  isEnabledWebhookChange: Option[Boolean] = None,
  isEnabledWebhookUrlVerificationRequestChange: Option[Boolean] = None
) extends FundDataAnduinAdminValidationParams

object UpdateFirmWebhookConfigParams {
  given Codec.AsObject[UpdateFirmWebhookConfigParams] = deriveCodecWithDefaults
}

final case class CreateFirmContactParams(
  firmId: FundDataFirmId,
  contacts: List[CreateFirmContactParams.CreateContactInfo]
)

object CreateFirmContactParams {

  given Codec.AsObject[CreateFirmContactParams] = deriveCodecWithDefaults

  final case class CreateContactInfo(
    firstName: String,
    lastName: String,
    email: String,
    role: String,
    phone: String
  ) {

    def trim: CreateContactInfo = copy(
      firstName = firstName.trim,
      lastName = lastName.trim,
      email = email.trim,
      role = role.trim,
      phone = phone.trim
    )

  }

  object CreateContactInfo {
    given Codec.AsObject[CreateContactInfo] = deriveCodecWithDefaults
  }

}

final case class CreateFirmContactResp(
  contactMap: Map[String, ContactId]
)

object CreateFirmContactResp {
  given Codec.AsObject[CreateFirmContactResp] = deriveCodecWithDefaults
}

final case class UpdateFirmSmtpConfigParams(
  firmId: FundDataFirmId,
  smtpConfig: CustomSmtpServerConfigParams
) extends FundDataAnduinAdminValidationParams

object UpdateFirmSmtpConfigParams {
  given Codec.AsObject[UpdateFirmSmtpConfigParams] = deriveCodecWithDefaults
}

final case class FirmSmtpConfigResponse(
  isEnabled: Boolean,
  smtpConfig: Option[CustomSmtpServerConfig]
)

object FirmSmtpConfigResponse {
  given Codec.AsObject[FirmSmtpConfigResponse] = deriveCodecWithDefaults
}

final case class GetFirmSmtpConfigParams(
  firmId: FundDataFirmId
) extends FundDataAnduinAdminValidationParams

object GetFirmSmtpConfigParams {
  given Codec.AsObject[GetFirmSmtpConfigParams] = deriveCodecWithDefaults
}

final case class GetFirmLlmConfigParams(
  firmId: FundDataFirmId
) extends FundDataAnduinAdminValidationParams
    with FundDataFirmValidationParams derives CirceCodec.WithDefaults

final case class GetFirmLlmConfigResp(
  firmLlmConfig: FirmLlmConfig
) derives CirceCodec.WithDefaults

final case class FirmLlmConfig(
  firmId: FundDataFirmId,
  rawDocumentExtractEntityInfoSchema: Json
) derives CirceCodec.WithDefaults

final case class UpdateFirmLlmConfigParams(
  firmId: FundDataFirmId,
  rawDocumentExtractEntityInfoSchemaChange: Option[Json]
) extends FundDataAnduinAdminValidationParams derives CirceCodec.WithDefaults

final case class UpdateFirmLlmConfigResp() derives CirceCodec.WithDefaults
