// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.core.model

import scala.reflect.TypeTest

import io.circe.Json

//import io.circe.Json
import io.getquill.MappedEncoding
import squants.market.{Currency, USD, defaultMoneyContext}

import anduin.id.ModelIdRegistry
import anduin.id.dataroom.DataRoomGroupId
import anduin.id.docrequest.{DocRequestId, FormSubmissionId}
import anduin.id.entity.EntityId
import anduin.id.environment.EnvironmentId
import anduin.id.form.{DataTemplateVersionId, FormId, FormVersionDataId, FormVersionId}
import anduin.id.funddata.{
  FundDataFirmId,
  FundDataFundId,
  FundDataFundSubscriptionId,
  FundDataGroupRoleId,
  FundDataInvestmentEntityAssessmentId,
  FundDataInvestmentEntityContactId,
  FundDataInvestmentEntityDocumentId,
  FundDataInvestmentEntityId,
  FundDataInvestorId
}
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.fundsub.{
  FundSubCloseId,
  FundSubId,
  FundSubLpFormVersionId,
  FundSubLpId,
  FundSubLpTagId,
  InvestmentFundId
}
import anduin.id.issuetracker.{CommentAssignmentId, IssueId}
import anduin.id.notification.NotificationId
import anduin.id.role.fundsub.FundManagerRoleId
import anduin.id.signature.{SignatureModuleId, SignatureRequestId}
import anduin.id.tag.{TagItemId, TagListId}
import anduin.model.common.user.UserId
import anduin.model.id.{FileId, TeamId}
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.radix.RadixId

object Codec {

  given currencyEncode: MappedEncoding[Currency, String] = MappedEncoding(_.code)

  given currencyDecode: MappedEncoding[String, Currency] = MappedEncoding(
    Currency(_)(
      using defaultMoneyContext
    ).toOption.getOrElse(USD)
  )

  given fundSubIdEncode: MappedEncoding[FundSubId, String] = radixIdEncode
  given fundSubIdDecode: MappedEncoding[String, FundSubId] = radixIdDecode

  given investmentFundIdEncode: MappedEncoding[InvestmentFundId, String] = radixIdEncode
  given investmentFundIdDecode: MappedEncoding[String, InvestmentFundId] = radixIdDecode

  given fundSubLpIdEncode: MappedEncoding[FundSubLpId, String] = radixIdEncode
  given fundSubLpIdDecode: MappedEncoding[String, FundSubLpId] = radixIdDecode

  given fundSubLpFormVersionIdEncode: MappedEncoding[FundSubLpFormVersionId, String] = radixIdEncode
  given fundSubLpFormVersionIdDecode: MappedEncoding[String, FundSubLpFormVersionId] = radixIdDecode

  given teamIdEncode: MappedEncoding[TeamId, String] = radixIdEncode
  given teamIdDecode: MappedEncoding[String, TeamId] = radixIdDecode

  given fundManagerRoleIdEncode: MappedEncoding[FundManagerRoleId, String] = radixIdEncode
  given fundManagerRoleIdDecode: MappedEncoding[String, FundManagerRoleId] = radixIdDecode

  given userIdEncode: MappedEncoding[UserId, String] = MappedEncoding(UserId.userIdMapper.toBase)
  given userIdDecode: MappedEncoding[String, UserId] = MappedEncoding(UserId.userIdMapper.toCustom)

  given fundSubInvestorGroupIdEncode: MappedEncoding[FundSubInvestorGroupId, String] = radixIdEncode
  given fundSubInvestorGroupIdDecode: MappedEncoding[String, FundSubInvestorGroupId] = radixIdDecode

  given dataRoomWorkflowIdEncode: MappedEncoding[DataRoomWorkflowId, String] = radixIdEncode
  given dataRoomWorkflowIdDecode: MappedEncoding[String, DataRoomWorkflowId] = radixIdDecode

  given dataRoomGroupIdEncode: MappedEncoding[DataRoomGroupId, String] = radixIdEncode
  given dataRoomGroupIdDecode: MappedEncoding[String, DataRoomGroupId] = radixIdDecode

  given entityIdEncode: MappedEncoding[EntityId, String] = radixIdEncode
  given entityIdDecode: MappedEncoding[String, EntityId] = radixIdDecode

  given fileIdEncode: MappedEncoding[FileId, String] = radixIdEncode
  given fileIdDecode: MappedEncoding[String, FileId] = radixIdDecode

  given fundDataFirmIdEncode: MappedEncoding[FundDataFirmId, String] = radixIdEncode
  given fundDataFirmIdDecode: MappedEncoding[String, FundDataFirmId] = radixIdDecode

  given fundDataGroupRoleIdEncode: MappedEncoding[FundDataGroupRoleId, String] = radixIdEncode
  given fundDataGroupRoleIdDecode: MappedEncoding[String, FundDataGroupRoleId] = radixIdDecode

  given fundDataFundIdEncode: MappedEncoding[FundDataFundId, String] = radixIdEncode
  given fundDataFundIdDecode: MappedEncoding[String, FundDataFundId] = radixIdDecode

  given fundDataFundSubscriptionIdEncode: MappedEncoding[FundDataFundSubscriptionId, String] = radixIdEncode
  given fundDataFundSubscriptionIdDecode: MappedEncoding[String, FundDataFundSubscriptionId] = radixIdDecode

  given fundDataInvestorIdEncode: MappedEncoding[FundDataInvestorId, String] = radixIdEncode
  given fundDataInvestorIdDecode: MappedEncoding[String, FundDataInvestorId] = radixIdDecode

  given fundDataInvestmentEntityIdEncode: MappedEncoding[FundDataInvestmentEntityId, String] = radixIdEncode
  given fundDataInvestmentEntityIdDecode: MappedEncoding[String, FundDataInvestmentEntityId] = radixIdDecode

  given fundDataInvestmentEntityDocumentIdEncode: MappedEncoding[FundDataInvestmentEntityDocumentId, String] =
    radixIdEncode

  given fundDataInvestmentEntityDocumentIdDecode: MappedEncoding[String, FundDataInvestmentEntityDocumentId] =
    radixIdDecode

  given fundDataInvestmentEntityContactIdEncode: MappedEncoding[FundDataInvestmentEntityContactId, String] =
    radixIdEncode

  given fundDataInvestmentEntityContactIdDecode: MappedEncoding[String, FundDataInvestmentEntityContactId] =
    radixIdDecode

  given fundDataInvestmentEntityAssessmentIdEncode: MappedEncoding[FundDataInvestmentEntityAssessmentId, String] =
    radixIdEncode

  given fundDataInvestmentEntityAssessmentIdDecode: MappedEncoding[String, FundDataInvestmentEntityAssessmentId] =
    radixIdDecode

  given tagListIdEncode: MappedEncoding[TagListId, String] =
    radixIdEncode

  given tagListIdDecode: MappedEncoding[String, TagListId] =
    radixIdDecode

  given tagItemIdEncode: MappedEncoding[TagItemId, String] =
    radixIdEncode

  given tagItemIdDecode: MappedEncoding[String, TagItemId] =
    radixIdDecode

  given formSubmissionIdEncode: MappedEncoding[FormSubmissionId, String] = radixIdEncode
  given formSubmissionIdDecode: MappedEncoding[String, FormSubmissionId] = radixIdDecode

  given docRequestIdEncode: MappedEncoding[DocRequestId, String] = radixIdEncode
  given docRequestIdDecode: MappedEncoding[String, DocRequestId] = radixIdDecode

  given formVersionIdEncode: MappedEncoding[FormVersionId, String] = radixIdEncode
  given formVersionIdDecode: MappedEncoding[String, FormVersionId] = radixIdDecode

  given formVersionDataIdEncode: MappedEncoding[FormVersionDataId, String] = radixIdEncode

  given formVersionDataIdDecode: MappedEncoding[String, FormVersionDataId] = radixIdDecode

  given formIdEncode: MappedEncoding[FormId, String] = radixIdEncode
  given formIdDecode: MappedEncoding[String, FormId] = radixIdDecode

  given signatureRequestIdEncode: MappedEncoding[SignatureRequestId, String] = radixIdEncode
  given signatureRequestIdDecode: MappedEncoding[String, SignatureRequestId] = radixIdDecode

  given signatureModuleIdEncode: MappedEncoding[SignatureModuleId, String] = radixIdEncode
  given signatureModuleIdDecode: MappedEncoding[String, SignatureModuleId] = radixIdDecode

  given environmentIdEncode: MappedEncoding[EnvironmentId, String] = radixIdEncode
  given environmentIdDecode: MappedEncoding[String, EnvironmentId] = radixIdDecode

  given commentAssignmentIdEncode: MappedEncoding[CommentAssignmentId, String] = radixIdEncode
  given commentAssignmentIdDecode: MappedEncoding[String, CommentAssignmentId] = radixIdDecode

  given commentThreadIdEncode: MappedEncoding[IssueId, String] = radixIdEncode
  given commentThreadIdDecode: MappedEncoding[String, IssueId] = radixIdDecode

  given notificationIdEncode: MappedEncoding[NotificationId, String] = radixIdEncode
  given notificationIdDecode: MappedEncoding[String, NotificationId] = radixIdDecode

  given fundSubCloseIdEncode: MappedEncoding[FundSubCloseId, String] = radixIdEncode
  given fundSubCloseIdDecode: MappedEncoding[String, FundSubCloseId] = radixIdDecode

  given fundSubLpTagIdEncode: MappedEncoding[FundSubLpTagId, String] = radixIdEncode
  given fundSubLpTagIdDecode: MappedEncoding[String, FundSubLpTagId] = radixIdDecode

  given templateVersionIdEncode: MappedEncoding[DataTemplateVersionId, String] = radixIdEncode
  given templateVersionIdDecode: MappedEncoding[String, DataTemplateVersionId] = radixIdDecode

  given jsonEncoder: MappedEncoding[Json, String] = MappedEncoding(_.noSpaces)

  given jsonDecoder: MappedEncoding[String, Json] = MappedEncoding(
    io.circe.parser.parse(_).getOrElse(Json.Null)
  )

  private def radixIdEncode[A <: RadixId]: MappedEncoding[A, String] = MappedEncoding(_.idString)

  private def radixIdDecode[A <: RadixId](
    using TypeTest[RadixId, A]
  ): MappedEncoding[String, A] = MappedEncoding { id =>
    ModelIdRegistry.parser
      .parseAs[A](id)
      .getOrElse(
        throw new RuntimeException(s"Failed to decode radix id $id")
      )
  }

}
