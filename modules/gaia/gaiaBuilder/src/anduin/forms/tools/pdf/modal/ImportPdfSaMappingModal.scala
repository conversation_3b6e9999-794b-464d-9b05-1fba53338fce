// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.tools.pdf.modal

import anduin.frontend.AirStreamUtils
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterL}
import design.anduin.components.progress.BarIndicator
import design.anduin.components.progress.laminar.BarIndicatorL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*

import anduin.forms.client.DocumentSaProfileMappingEndpointClient
import anduin.sa.SaMappingUtils
import com.raquo.laminar.api.L.*
import design.anduin.components.well.Well
import design.anduin.components.well.laminar.WellL
import zio.ZIO

import anduin.id.annotation.AnnotationDocumentVersionId
import anduin.id.sa.SaProfileId
import anduin.sa.endpoints.ImportDocumentSaProfileMappingParams

final case class ImportPdfSaMappingModal(
  saProfileId: SaProfileId,
  documentVersionId: AnnotationDocumentVersionId,
  onDoneImportSaMapping: Observer[Map[String, String]],
  close: Observer[Unit]
) {

  private val saMappingTextVar = Var("")

  private val saMappingSignal = saMappingTextVar.signal.map(SaMappingUtils.saMappingFromText).distinct

  private val numInvalidAndTotalLinesSignal = saMappingTextVar.signal.distinct.map { saMappingText =>
    if (saMappingText.trim.isEmpty) {
      0 -> 0
    } else {
      val numLines = saMappingText.split("\n").length
      val numInvalidLines = numLines - SaMappingUtils.saMappingFromText(saMappingText).size
      numInvalidLines -> numLines
    }
  }

  private val importDocumentSaMappingEventBus = new EventBus[Unit]
  private val progressImportVar = Var[Option[ProcessBarUIInfo]](None)

  def apply(): Node = div(
    ModalBodyL(
      div(
        saMappingTextBox,
        child.maybe <-- numInvalidAndTotalLinesSignal.map { (numInvalidLines, numLines) =>
          Option.when(numInvalidLines > 0) {
            div(
              tw.mt12,
              WellL(style = Well.Style.Warning())(
                div(
                  span(
                    tw.fontBold,
                    s"$numInvalidLines/$numLines lines don't follow the syntax '<field>${SaMappingUtils.ConnectorSymbol}<csa>'" +
                      s" or have unsupported characters (i.e., not alphanumeric nor '_'). These lines will be ignored"
                  )
                )
              )
            )
          }
        }
      )
    ),
    ModalFooterL(footer)
  )

  private def saMappingTextBox: Node = div(
    div(tw.fontSemiBold.mb4, "PDF Field CSA Mapping"),
    textArea(
      tw.borderGray3.borderAll.wPc100.p8.fontMono.leading16,
      value <-- saMappingTextVar.signal,
      rows := 20,
      inContext(thisNode => onBlur.mapTo(thisNode.ref.value) --> saMappingTextVar.writer)
    )
  )

  private def footer: Node = div(
    tw.flexCol.itemsCenter,
    importDocumentSaMappingEventBus.events
      .withCurrentValueOf(saMappingSignal)
      .flatMapSwitch(saMapping => handleImport(saMapping)) --> Observer.empty,
    div(
      tw.flex.itemsCenter,
      child.maybe <-- progressImportVar.signal.map(_.map(showProgress)),
      div(
        tw.flex.flexNone.mlAuto,
        ButtonL(
          isDisabled = progressImportVar.signal.map(_.nonEmpty),
          onClick = close.contramap(_ => ())
        )("Cancel"),
        div(
          tw.ml8,
          ButtonL(
            style = ButtonL.Style.Full(color = ButtonL.Color.Primary, isBusy = progressImportVar.signal.map(_.nonEmpty)),
            onClick = importDocumentSaMappingEventBus.writer.contramap(_ => ()),
            isDisabled = saMappingSignal.map(_.isEmpty)
          )("Import")
        )
      )
    )
  )

  private case class ProcessBarUIInfo(
    percentage: Double,
    title: String
  )

  private def showProgress(progressInfo: ProcessBarUIInfo): Node = {
    div(
      tw.flex.flexCol.itemsCenter.flexFill,
      div(tw.textGray7, progressInfo.title),
      div(
        tw.textPrimary3.wPc80.mt4,
        BarIndicatorL(
          percent = Val(Some(progressInfo.percentage)),
          height = BarIndicator.Height.Large
        )()
      )
    )
  }

  private def handleImport(saMapping: Map[String, String]): EventStream[Unit] = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        _ <- ZIO.attempt(progressImportVar.set(Some(ProcessBarUIInfo(0.5, "Importing CSA mapping..."))))
        eitherRes <- DocumentSaProfileMappingEndpointClient.importDocumentSaProfileMapping(
          ImportDocumentSaProfileMappingParams(saProfileId, documentVersionId, saMapping)
        )
      } yield eitherRes.fold(
        err => {
          Toast.error(s"Unable to import CSA mapping. Error: ${err.getMessage}")
          progressImportVar.set(None)
        },
        res => {
          Toast.info(s"Import ${res.importedMapping.size}/${saMapping.size} valid CSA mapping")
          onDoneImportSaMapping.onNext(res.importedMapping)
          close.onNext(())
        }
      )
    }
  }

}
