// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.builder.rule

import scala.util.Try
import scala.language.adhocExtensions

import sjsonnet.Expr.{IfElse, Import}

import anduin.forms.{Form, NamespaceKey}
import anduin.forms.builder.rule.extractor.AliasCollector
import anduin.forms.rules.RuleCtx.AnduinPath
import anduin.forms.logic.CachedResolverExtensions.*

import sjsonnet.*

private[forms] final case class FakeEvaluator(
  resolver: CachedResolver,
  valScope: ValScope,
  aliasCollector: AliasCollector
) extends Evaluator(
      resolver,
      Map.empty,
      AnduinPath(""),
      Settings.default
    ) {

  override def visitImport(
    e: Import
  ): Val = {
    val (p, str) = importer.resolveAndReadOrFail(e.value, e.pos, binaryData = false)
    cachedImports.getOrElseUpdate(
      p, {
        val doc = resolver.safeParse(p, str) match {
          case Right((expr, _)) => expr
          case Left(err)        => throw err.asSeenFrom(this)
        }
        visitExpr(doc)(
          using valScope
        )
      }
    )
  }

  private def forceLazy(v: Val): Val = {
    v match {
      case arr: Val.Arr => new Val.Arr(arr.pos, arr.asStrictArray.map(forceLazy))
      case obj: Val.Obj =>
        obj.foreachElement(sort = false, emptyMaterializeFileScopePos) { case (_, v) =>
          v.force
          ()
        }

        obj
      case other: Val => other.force
    }
  }

  override def visitExpr(
    e: Expr
  )(
    using scope: ValScope
  ): Val = {
    val result =
      try {
        val v = super.visitExpr(e)
        forceLazy(v)
      } catch {
        case _: Throwable => null // scalafix:ok DisableSyntax.null
      }
    checkInput(e, result)
    result
  }

  override def visitIfElse(
    e: IfElse
  )(
    using scope: ValScope
  ): Val = {
    val thenExpr = visitExpr(e.`then`)
    val elseExpr = e.`else` match {
      // scalafix:off DisableSyntax.null
      case null => Val.Null(e.pos)
      // scalafix:on
      case v: Expr => visitExpr(v)
    }
    visitExpr(e.cond) match {
      case Val.True(_)  => thenExpr
      case Val.False(_) => elseExpr
      case _            => Val.Null(e.pos)
    }
  }

  private def checkInput(expr: Expr, v: Val): Unit = {
    if (expr != null && v != null) { // scalafix:ok DisableSyntax.null
      Try(v.asObj).foreach { obj =>
        if (obj.allKeyNames.contains(FakeState.repeatableControlledKey)) {
          parseName(expr).foreach { id =>
            aliasCollector.addRepeatableInputKey(NamespaceKey(Form.DefaultNamespace, id))
          }
        }
      }
    }
  }

  private def parseName(expr: Expr): Option[String] = {
    expr match {
      case Expr.ValidId(_, name, _) => Some(name)
      case Expr.Id(_, name)         => Some(name)
      case Expr.Select(_, _, name)  => Some(name)
      case _                        => None
    }
  }

}
