package anduin.forms.builder.toc

import scala.reflect.Selectable.reflectiveSelectable

import com.raquo.airstream.core.Observer
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.collapse.Collapse
import design.anduin.components.collapse.laminar.CollapseL
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.editor.laminar.EditorPreviewL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL, ModalL}
import design.anduin.components.monaco.MonacoDiffEditor
import design.anduin.components.monaco.languages.MonacoLanguage
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.tag.Tag
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import io.circe.syntax.*
import io.circe.{ACursor, Json}
import org.scalajs.dom
import zio.prelude.These

import anduin.blueprint.*
import anduin.forms.builder.checklist.ChecklistUtils
import anduin.forms.builder.checklist.section.BlueprintMetadataMappingNonExistentSectionView
import anduin.forms.builder.modal.AddSectionPopover
import anduin.forms.builder.modal.ImportBlueprintMetadataModal.*
import anduin.forms.builder.signaldata.FormBuilderDataCommand
import anduin.forms.builder.states.BuilderMode
import anduin.forms.builder.toc.BuilderTOCBlueprintMetadataSection.*
import anduin.forms.builder.utils.BuilderUtils
import anduin.forms.model.Schema
import anduin.forms.model.Schema.obj.Field
import anduin.forms.monaco.MonacoSettings
import anduin.forms.ui.types.{MultipleOptionType, SignatureBlockType, SingleOptionType}
import anduin.forms.ui.{UIKey, UIOptions, Widget, WidgetType}
import anduin.forms.version.BlueprintRef
import anduin.forms.version.FormVersionMetadataModel.WidgetSource
import anduin.forms.widget.WidgetIcon
import anduin.forms.{Form, FormSchema}
import anduin.scalajs.pluralize.Pluralize
import anduin.utils.DateTimeUtils
import anduin.utils.StringUtils.{MatchResult, MatchType}

private[toc] final case class BuilderTOCBlueprintMetadataSection(
  formSignal: Signal[Form],
  keysInFormRulesSignal: Signal[Set[String]],
  blueprintRefOptSignal: Signal[Option[BlueprintRef]],
  blueprintMetadataSignal: Signal[BlueprintMetadata],
  selectedKeySignal: Signal[String],
  formBuilderDataCommandObserver: Observer[FormBuilderDataCommand],
  onSelectKey: Observer[String],
  bpMetadataSearchTextSignal: Signal[String],
  onChangeBpMetadataSearchText: Observer[String],
  editModeSignal: Signal[BuilderMode]
) {

  private val matchingType = MatchType.PartialMatch

  private def createMetadataSignal[T](
    metadataSignal: Signal[Seq[T]],
    keyExtractor: T => String,
    labelExtractor: T => String
  ): Signal[Seq[(T, Option[MatchResult], Option[MatchResult])]] =
    metadataSignal.combineWith(bpMetadataSearchTextSignal).distinct.map { (items, searchText) =>
      if (searchText.isEmpty) {
        items.map(item => (item, None, None))
      } else {
        items
          .flatMap { item =>
            val keyMatchResult = matchingType.computeMatchScore(keyExtractor(item), searchText)
            val labelMatchResult = matchingType.computeMatchScore(labelExtractor(item), searchText)
            if (keyMatchResult.isDefined || labelMatchResult.isDefined) {
              Some(
                (
                  item,
                  keyMatchResult,
                  labelMatchResult
                )
              )
            } else {
              None
            }
          }
          .sortBy {
            case (_, Some(keyMatch), Some(labelMatch)) =>
              (-Math.max(keyMatch.score.matchScore, labelMatch.score.matchScore), keyMatch.score.baseLength)
            case (_, Some(keyMatch), None) =>
              (-keyMatch.score.matchScore, keyMatch.score.baseLength)
            case (_, None, Some(labelMatch)) =>
              (-labelMatch.score.matchScore, labelMatch.score.baseLength)
            case _ =>
              (0.0, Int.MaxValue)
          }
      }
    }

  private def createMetadataSubSignal[T](
    extract: BlueprintMetadata => Seq[T],
    keyExtractor: T => String,
    labelExtractor: T => String
  ): Signal[Seq[(T, Option[MatchResult], Option[MatchResult])]] = {
    createMetadataSignal(
      blueprintMetadataSignal.map(extract),
      keyExtractor,
      labelExtractor
    )
  }

  private val gatingQuestionsSignal: Signal[Seq[(GatingQuestion, Option[MatchResult], Option[MatchResult])]] =
    createMetadataSubSignal(
      _.gatingQuestions,
      _.key,
      question => BuilderUtils.getTextFromHtml(question.label)
    )

  private val paragraphsSignal: Signal[Seq[(ParagraphMessage, Option[MatchResult], Option[MatchResult])]] =
    createMetadataSubSignal(
      _.paragraphs,
      _.key,
      pr => BuilderUtils.getTextFromHtml(pr.label)
    )

  private val fileGroupsSignal: Signal[Seq[(FileGroup, Option[MatchResult], Option[MatchResult])]] =
    createMetadataSubSignal(
      _.fileGroups,
      _.key,
      _.name
    )

  private val signaturesSignal: Signal[Seq[(BlueprintSignature, Option[MatchResult], Option[MatchResult])]] =
    createMetadataSubSignal(
      _.signatures,
      _.key,
      _.signer
    )

  private val isEmptySearchResultSignal =
    fileGroupsSignal.map(_.isEmpty) && paragraphsSignal.map(_.isEmpty) &&
      gatingQuestionsSignal.map(_.isEmpty) && signaturesSignal.map(_.isEmpty)

  private val importItemVar = Var[Option[(ItemType, String)]](None)
  private val importEventBus = new EventBus[Unit]

  private val mappedBlueprintMetadataFieldsSignal: Signal[(Map[String, Schema], Seq[FormFieldData])] =
    formSignal.map(_.defaultSchema).map { formSchema =>
      val allFields = BuilderUtils.getAllFields(formSchema.schema)
      val formFieldData =
        ChecklistUtils.getWidgetsWithBlueprintMetadataMapping(formSchema).flatMap { case (alias, widget) =>
          allFields.get(alias).map(fieldSchema => FormFieldData(Field(alias, fieldSchema), alias, widget))
        }
      (allFields, formFieldData)
    }

  def apply(): HtmlElement = {
    div(
      idAttr := "BuilderTOCBlueprintMetadataSection",
      tw.flexFill.flex.flexCol,
      div(
        tw.spaceY12.p8.borderBottom.borderGray3,
        TextBoxL(
          value = bpMetadataSearchTextSignal.distinct,
          onChange = onChangeBpMetadataSearchText,
          icon = Some(Icon.Glyph.Search),
          size = TextBoxL.Size.Px32
        )()
      ),
      div(
        tw.flexFill.overflowYAuto,
        child.maybe <-- isEmptySearchResultSignal.map {
          Option.when(_) {
            div(
              div(
                tw.hPc100.wPc100.flex.flexCol.itemsCenter.justifyCenter,
                tw.spaceY12,
                tw.textGray5.selectNone,
                tw.my8,
                "No item found"
              )
            )
          }
        },
        div(
          tw.itemsCenter.textGray7,
          child.maybe <-- gatingQuestionsSignal.map(_.nonEmpty).map {
            Option.when(_) {
              renderMetadataSection(
                "Gating questions",
                gatingQuestionsSignal,
                _.label,
                _.key,
                _.id,
                ItemType.GatingQuestion
              )
            }
          },
          child.maybe <-- paragraphsSignal.map(_.nonEmpty).map {
            Option.when(_) {
              renderMetadataSection(
                "Wells",
                paragraphsSignal,
                _.label,
                _.key,
                _.id,
                ItemType.ParagraphMessage
              )
            }
          },
          child.maybe <-- fileGroupsSignal.map(_.nonEmpty).map {
            Option.when(_) {
              renderMetadataSection(
                "File groups",
                fileGroupsSignal,
                _.name,
                _.key,
                _.id,
                ItemType.FileGroup
              )
            }
          },
          child.maybe <-- signaturesSignal.map(_.nonEmpty).map {
            Option.when(_) {
              renderMetadataSection(
                "Signatures",
                signaturesSignal,
                _.signer,
                _.key,
                _.id,
                ItemType.BlueprintSignature
              )
            }
          }
        ),
        importEventBus.events
          .withCurrentValueOf(
            importItemVar.signal,
            formSignal,
            blueprintMetadataSignal,
            keysInFormRulesSignal,
            selectedKeySignal
          )
          .collect { case (Some(importItem), form, metadata, allKeysInRules, selectedKey) =>
            (importItem, form, metadata, allKeysInRules, selectedKey)
          }
          --> formBuilderDataCommandObserver
            .contramap[((ItemType, String), Form, BlueprintMetadata, Set[String], String)] {
              case ((itemType, id), form, metadata, allKeysInRules, selectedKey) =>
                val selectedItems2FormData = convertBlueprintMetadataToFormData(List((itemType, id)), metadata)
                widgetTypeByAlias(selectedKey, form)
                  .flatMap(AddSectionPopover.filterObjType)
                  .fold {
                    FormBuilderDataCommand.addSibling(
                      rawFields = selectedItems2FormData.map(_._1),
                      rawWidgetMap = selectedItems2FormData.flatMap(_._2).toMap,
                      prevKeyOpt = Option(selectedKey).filter(_.nonEmpty).orElse(None),
                      widgetSource = WidgetSource.BLUEPRINT_IMPORT,
                      allKeysInRules = allKeysInRules
                    )
                  } { _ =>
                    FormBuilderDataCommand.addChild(
                      rawFields = selectedItems2FormData.map(_._1),
                      rawWidgetMap = selectedItems2FormData.flatMap(_._2).toMap,
                      parent = selectedKey,
                      widgetSource = WidgetSource.BLUEPRINT_IMPORT,
                      allKeysInRules = allKeysInRules
                    )
                  }
            }
      )
    )
  }

  private def renderMetadataSection[T <: ParagraphMessage | GatingQuestion | FileGroup | BlueprintSignature](
    title: String,
    itemsSignal: Signal[Seq[(T, Option[MatchResult], Option[MatchResult])]],
    getLabel: T => String,
    getKey: T => String,
    idExtractor: T => String,
    itemType: ItemType
  ): HtmlElement = {
    val formMappingsSignal: Signal[Seq[
      (
        T,
        Option[MatchResult],
        Option[MatchResult],
        Seq[FormFieldData],
        FormFieldData,
        Map[String, Schema],
        BlueprintMetadata
      )
    ]] =
      itemsSignal
        .combineWith(
          mappedBlueprintMetadataFieldsSignal,
          blueprintMetadataSignal
        )
        .map { case (items, (allFields, formFieldsData), metadata) =>
          items.map { case (item, keyMatch, labelMatch) =>
            val relatedFields = formFieldsData.filter { formFieldData =>
              formFieldData.widget.uiOptions
                .get(UIKey.blueprintMetadataMapping)
                .exists(metadataMapping => metadataMapping == getKey(item))
            }

            val selectedItems = List(itemType -> idExtractor(item))
            val extractedData = convertBlueprintMetadataToFormData(selectedItems, metadata).headOption
            val blueprintFormFieldData = createBlueprintFormFieldData(extractedData, getKey(item))

            (item, keyMatch, labelMatch, relatedFields, blueprintFormFieldData, allFields, metadata)
          }
        }

    div(
      tw.flex.flexFill.itemsCenter.wPc100.borderBottom.borderGray3.py12,
      CollapseL(
        defaultIsOpened = true,
        renderTarget = render => {
          div(
            tw.flex.itemsCenter.selectNone,
            button(
              tw.hPx32.justifyBetween,
              tw.flex.flexFill.itemsCenter.mx8,
              onClick.mapTo(()) --> render.onToggle,
              span(
                tw.textGray8.text13.fontSemiBold,
                s"$title"
              ),
              div(
                tw.flex.itemsCenter,
                span(tw.p4, text <-- itemsSignal.map(_.size)),
                div(
                  tw.p4,
                  IconL(render.currentStatus match {
                    case Collapse.Status.Open  => Val(Icon.Glyph.ChevronDown)
                    case Collapse.Status.Close => Val(Icon.Glyph.ChevronRight)
                  })()
                )
              )
            )
          )
        },
        direction = Collapse.Direction.TopToBottom
      )(
        div(
          tw.flex.flexCol.ml8,
          children <-- formMappingsSignal.split { case (item, _, _, _, _, _, _) => idExtractor(item) } {
            case (_, _, itemSignal) =>
              div(
                tw.flex.spaceX8.py2.mt8,
                child.maybe <-- itemSignal.map { case (item, _, _, _, _, _, _) =>
                  item match {
                    case q: GatingQuestion     => Some(getFormUIInputTypeIconElement(q.inputType))
                    case p: ParagraphMessage   => Some(getParagraphMessageIconElement(p.style))
                    case _: FileGroup          => Some(IconL(Val(Icon.Glyph.MultiSelect), size = Icon.Size.Px16)())
                    case _: BlueprintSignature => Some(IconL(Val(Icon.Glyph.Sign), size = Icon.Size.Px16)())
                    case _                     => None
                  }
                },
                div(
                  tw.flexCol.wPc90,
                  child <-- itemSignal.map { case (item, _, labelMatch, _, _, _, _) =>
                    item match {
                      case _: GatingQuestion | _: ParagraphMessage =>
                        ModalL(
                          size = ModalL.Size(width = ModalL.Width.Px960),
                          renderTarget = openModal =>
                            div(
                              tw.textGray8.cursorPointer,
                              TooltipL(
                                renderContent = _.amend("Preview", maxWidth.fitContent),
                                renderTarget = renderMetadataTitle(item, getLabel, labelMatch).amend(tw.lineClamp3)
                              )(),
                              onClick --> openModal.contramap(_ => ())
                            ),
                          renderContent = closeModal =>
                            div(
                              ModalBodyL(
                                div(
                                  tw.flex.p8.borderAll.borderGray3,
                                  EditorPreviewL(
                                    content = itemSignal.map((item, _, _, _, _, _, _) => getLabel(item))
                                  )().amend(height := "auto")
                                )
                              ),
                              ModalFooterWCancelL(cancel = closeModal, cancelLabel = "Done")()
                            ),
                          renderTitle = _ => "Preview"
                        )()
                      case sign: BlueprintSignature => renderSignatureMapping(sign, item, getLabel, labelMatch)
                      case _ =>
                        TruncateL(
                          lineClamp = Some(3),
                          target = renderMetadataTitle(item, getLabel, labelMatch),
                          title = Some(BuilderUtils.getTextFromHtml(getLabel(item)))
                        )()
                    }
                  },
                  div(
                    children <-- itemSignal.map { case (item, keyMatch, _, _, _, _, _) =>
                      highlightTextWithSegments(
                        getKey(item),
                        keyMatch.map(_.segments).getOrElse(Nil)
                      )
                    }
                  ),
                  child.maybe <-- itemSignal.map {
                    case (item: GatingQuestion, _, _, _, _, _, _)   => renderAfterField(item)
                    case (item: ParagraphMessage, _, _, _, _, _, _) => renderAfterField(item)
                    case _                                          => None
                  },
                  child.maybe <-- itemSignal.map { case (item, _, _, _, _, _, _) =>
                    item match {
                      case q: GatingQuestion =>
                        Some(renderPopover(q.options.size, q.options, opt => (opt.label, opt.key), "option"))
                      case f: FileGroup if f.fileItems.nonEmpty =>
                        Some(renderPopover(f.fileItems.size, f.fileItems, item => (item.name, item.key), "file"))
                      case _ => None
                    }
                  },
                  children <-- itemSignal.distinct.map {
                    case (item, _, _, formMappingFields, blueprintField, _, metadata) =>
                      val blueprintFormFieldSchema = createFormSchemaFromFormFieldData(blueprintField)
                      val formFieldDiffs = formMappingFields.map { formField =>
                        val formFieldSchema = createFormSchemaFromFormFieldData(formField)

                        val isMismatched = item match {
                          case _: ParagraphMessage =>
                            !BlueprintMetadataMappingNonExistentSectionView.isMatchingParagraphMapping(
                              formField.widget,
                              metadata.paragraphs
                            )
                          case _: BlueprintSignature =>
                            !BlueprintMetadataMappingNonExistentSectionView.isMatchingSignatureMapping(
                              formField.widget,
                              metadata.signatures
                            )
                          case _: FileGroup =>
                            !BlueprintMetadataMappingNonExistentSectionView.isMatchingFileGroupMapping(
                              formField.widget,
                              metadata.fileGroups
                            )
                          case _: GatingQuestion =>
                            !BlueprintMetadataMappingNonExistentSectionView.isMatchingGatingQuestionMapping(
                              formField.widget,
                              metadata.gatingQuestions
                            )
                        }
                        (formField, formFieldSchema, isMismatched)
                      }
                      val totalDiffs = formFieldDiffs.count { case (_, _, hasDiff) => hasDiff }

                      Seq(
                        if (formMappingFields.nonEmpty) {
                          div(
                            tw.flexCol,
                            DividerL()(),
                            div("Form mappings:"),
                            formFieldDiffs.sortBy { case (formField, _, _) => formField.alias }.map {
                              (formField, formFieldSchema, hasDiff) =>
                                div(
                                  tw.flex,
                                  div(tw.textPrimary5, IconL(name = Val(WidgetIcon(formField.widget.widgetType)))()),
                                  TruncateL(
                                    target = div(
                                      tw.flexFill.ml4.cursorPointer.textPrimary5,
                                      formField.alias,
                                      onClick --> Observer { _ => onSelectKey.onNext(formField.alias) }
                                    )
                                  )(),
                                  if (hasDiff) {
                                    div(
                                      PopoverL(
                                        renderTarget = (open, _) =>
                                          TagL(
                                            label = Val("View diff"),
                                            color = Val(Tag.Light.Warning),
                                            target = Tag.Target.Button(onClick = open)
                                          )(),
                                        renderContent = _ =>
                                          div(
                                            tw.p8,
                                            MonacoDiffEditor(
                                              workerPaths = MonacoSettings.monacoEditorWorkerPath,
                                              language = MonacoLanguage.Json,
                                              appearance = MonacoSettings.getDiffEditorAppearance(),
                                              onAppearanceChanged = Observer { config =>
                                                MonacoSettings.setDiffEditorAppearance(config)
                                              },
                                              originalModel = MonacoDiffEditor.ContentModel(
                                                blueprintFormFieldSchema.asJson.toString,
                                                isReadOnly = true
                                              ),
                                              modifiedModel = MonacoDiffEditor.ContentModel(
                                                formFieldSchema.asJson.toString,
                                                isReadOnly = true
                                              ),
                                              fontSize = 12
                                            ).element.amend(height.px(400), width.px(920))
                                          ),
                                        position = PortalPosition.RightTop
                                      )()
                                    )
                                  } else { emptyNode }
                                )
                            }
                          )
                        } else { emptyNode },
                        div(
                          tw.flex.itemsCenter.mt4,
                          ButtonL(
                            style = ButtonL.Style.Ghost(height = ButtonL.Height.Fix24),
                            onClick = Observer { _ =>
                              importItemVar.set(Some(itemType -> idExtractor(item)))
                              importEventBus.emit(())
                            },
                            isDisabled = editModeSignal.map(_ != BuilderMode.EditFull)
                          )("Import"),
                          if (totalDiffs > 0) {
                            ButtonL(
                              style = ButtonL.Style.Ghost(height = ButtonL.Height.Fix24, color = ButtonL.Color.Warning),
                              onClick = formBuilderDataCommandObserver.contramap[dom.MouseEvent] { _ =>
                                val fieldsToUpdate = formFieldDiffs.collect {
                                  case (formField, formFieldSchema, diff) if diff =>
                                    formField.alias -> updateFormFieldData(
                                      formField,
                                      formFieldSchema,
                                      blueprintField,
                                      blueprintFormFieldSchema,
                                      itemType
                                    )
                                }.toMap

                                for {
                                  _ <- FormBuilderDataCommand.updateSchemaWidget(fieldsToUpdate)
                                } yield ()
                              },
                              isDisabled = editModeSignal.map(_ != BuilderMode.EditFull)
                            )(s"Sync ${Pluralize("form fields", totalDiffs, true)}").amend(tw.ml4)
                          } else { emptyNode }
                        )
                      )
                  }
                )
              )
          }
        )
      ).amend(tw.wPc100)
    )
  }

  private def renderAfterField(item: { def afterFieldOpt: Option[AfterFieldInfo] }): Option[HtmlElement] = {
    item.afterFieldOpt.map { afterField =>
      div(
        "After PDF field: ",
        span(afterField.fieldName)
      )
    }
  }

  private def renderPopover[T](
    itemCount: Int,
    items: Seq[T],
    getContent: T => (String, String),
    itemName: String
  ): HtmlElement = {
    PopoverL(
      renderTarget = (open, _) =>
        span(
          tw.cursorPointer.hover(tw.underline),
          Pluralize(itemName, itemCount, true),
          onClick --> open.contramap(_ => ())
        ),
      renderContent = _ =>
        div(
          tw.p8.overflowYAuto,
          maxWidth.px(400),
          minWidth.px(200),
          items.map { item =>
            val (label, key) = getContent(item)
            div(
              tw.py4,
              EditorPreviewL(content = Val(label))(),
              div(tw.text11.textGray7.leading16, key)
            )
          }
        )
    )()
  }

  private def renderMetadataTitle[T <: ParagraphMessage | GatingQuestion | FileGroup | BlueprintSignature](
    item: T,
    getLabel: T => String,
    labelMatch: Option[MatchResult]
  ): HtmlElement = {
    div(
      tw.hover(tw.opacity10),
      highlightTextWithSegments(
        BuilderUtils.getTextFromHtml(getLabel(item)),
        labelMatch.map(_.segments).getOrElse(Nil)
      )
    )
  }

  private def renderSignatureMapping[T <: ParagraphMessage | GatingQuestion | FileGroup | BlueprintSignature](
    signature: BlueprintSignature,
    item: T,
    getLabel: T => String,
    labelMatch: Option[MatchResult]
  ): HtmlElement = {
    PopoverL(
      renderTarget = (open, _) =>
        renderMetadataTitle(item, getLabel, labelMatch)
          .amend(onClick --> open.contramap(_ => ()), tw.cursorPointer),
      renderContent = _ =>
        div(
          tw.px4.overflowYAuto,
          maxWidth.px(600),
          minWidth.px(480),
          signature.mappings.map { mapping =>
            div(
              tw.flex.itemsCenter.py4,
              div(
                tw.flex.flexFill.itemsCenter.mb4,
                span(mapping.mapping),
                if (!mapping.isOptional) { span(tw.ml2.textDanger4, "*") }
                else { emptyNode }
              ),
              div(
                tw.text11.leading16.textGray7.mb4,
                if (mapping.signatureType == SignatureBlockType.Date) {
                  s"Date (${mapping.format.getOrElse(DateTimeUtils.SignatureDefaultDateFormat).toLowerCase})"
                } else {
                  mapping.getExtendedSignatureTypeName
                }
              )
            )
          },
          signature.mappings
            .map(_.customTypeConfig.map(_.description))
            .filter(_.nonEmpty)
            .map(_.map(description => div(tw.textGray7.text11.leading16.mb4, s"Description: $description"))),
          signature.mappings
            .map(_.tooltip.filter(_.nonEmpty))
            .map(_.map(tooltip => div(tw.textGray7.text11.leading16.mb4, s"Tooltip: $tooltip"))),
          signature.mappings
            .map(_.customTypeConfig.exists(_.isCustomized))
            .map(if (_) {
              div(tw.textGray7.text11.leading16, "Customized")
            } else { emptyNode })
        )
    )()
  }

  private def updateFormFieldData(
    formField: FormFieldData,
    formFieldSchema: FormSchema,
    blueprintField: FormFieldData,
    blueprintSchema: FormSchema,
    itemType: ItemType
  ): These[Schema, Widget] = {
    val currentWidget = formFieldSchema.uiSchema.getOrElse(formField.alias, Widget.default)
    val currentSchema = formFieldSchema.formSchemaMap.getOrElse(formField.alias, Schema.`null`())

    val blueprintWidget = blueprintSchema.uiSchema.getOrElse(blueprintField.alias, Widget.default)
    val blueprintSchemaField = blueprintSchema.formSchemaMap.getOrElse(blueprintField.alias, Schema.`null`())

    val (updatedSchemaOpt, updatedWidgetOpt) = itemType match {
      case ItemType.ParagraphMessage =>
        updateParagraphData(currentSchema, currentWidget, blueprintWidget)

      case ItemType.FileGroup =>
        updateFileGroupData(currentSchema, currentWidget, blueprintSchemaField, blueprintWidget)

      case ItemType.GatingQuestion =>
        updateGatingQuestionData(currentSchema, currentWidget, blueprintSchemaField, blueprintWidget)

      case ItemType.BlueprintSignature =>
        updateSignatureData(currentSchema, currentWidget, blueprintWidget)
    }

    val finalSchema = updatedSchemaOpt.getOrElse(currentSchema)
    val finalWidget = updatedWidgetOpt.getOrElse(currentWidget)
    val schemaChanged = finalSchema != currentSchema
    val widgetChanged = finalWidget != currentWidget

    (schemaChanged, widgetChanged) match {
      case (true, true)   => These.Both(finalSchema, finalWidget)
      case (true, false)  => These.Left(finalSchema)
      case (false, true)  => These.Right(finalWidget)
      case (false, false) => These.Right(currentWidget)
    }
  }

  private def updateParagraphData(
    currentSchema: Schema,
    currentWidget: Widget,
    blueprintWidget: Widget
  ): (Option[Schema], Option[Widget]) = {
    // keep the current title because it does not affect to significant UI nor logic rule
    val updatedSchema = Some(currentSchema)

    val uiOptions = blueprintWidget.uiOptions
    val updatedOptions = currentWidget.uiOptions
      .updatedOpt(UIKey.formattedText)(uiOptions.get(UIKey.formattedText))
      .updatedOpt(UIKey.wellType)(uiOptions.get(UIKey.wellType))
      .updatedOpt(UIKey.blueprintMetadataMapping)(uiOptions.get(UIKey.blueprintMetadataMapping))
      .updatedOpt(UIKey.marginBottom)(uiOptions.get(UIKey.marginBottom))

    val updatedWidget = if (!updatedOptions.equals(currentWidget.uiOptions)) {
      Some(Widget(WidgetType.Paragraph, updatedOptions))
    } else {
      None
    }

    (updatedSchema, updatedWidget)
  }

  private def updateFileGroupData(
    currentSchema: Schema,
    currentWidget: Widget,
    blueprintSchema: Schema,
    blueprintWidget: Widget
  ): (Option[Schema], Option[Widget]) = {

    val updatedSupportingFileGroupOpt = {
      for {
        blueprintOpt <- blueprintWidget.uiOptions.get(UIKey.supportingFileGroup)
        currentOpt <- currentWidget.uiOptions.get(UIKey.supportingFileGroup)
      } yield {
        // Build a map of blueprint files by blueprintMetadataMapping
        val blueprintFileByMapping =
          blueprintOpt.files.values.map(f => f.blueprintMetadataMapping -> f).toMap

        // Process current files, keeping first occurrence for each blueprintMetadataMapping
        val currentFilesGroupedByMapping =
          currentOpt.files.toList.groupBy { case (_, file) => file.blueprintMetadataMapping }

        val updatedFilesFromCurrent = currentFilesGroupedByMapping.flatMap { case (mapping, files) =>
          blueprintFileByMapping.get(mapping).flatMap { blueprintFile =>
            files.headOption.map { case (filename, currentFile) =>
              filename -> currentFile.copy(
                description = blueprintFile.description,
                helpText = blueprintFile.helpText
              )
            }
          }
        }

        // Add any blueprint files not present in current
        val missingBlueprintFiles = blueprintFileByMapping.collect {
          case (mapping, blueprintFile) if !currentFilesGroupedByMapping.contains(mapping) =>
            blueprintFile.blueprintMetadataMapping -> blueprintFile
        }

        currentOpt.copy(
          description = blueprintOpt.description,
          helpText = blueprintOpt.helpText,
          files = updatedFilesFromCurrent ++ missingBlueprintFiles
        )
      }
    }

    val keyOptionMapping =
      updatedSupportingFileGroupOpt
        .map(fg => fg.files.keySet.filter(_.nonEmpty).map(Json.fromString).toSeq)
        .getOrElse(Seq.empty)

    val updatedSchema = currentSchema match {
      case s: Schema.array =>
        blueprintSchema match {
          case b: Schema.array =>
            b.componentType match {
              case be: Schema.`enum` =>
                Some(
                  s.copy(
                    title = s.title, // still keep the current title
                    componentType = s.componentType match {
                      case se: Schema.`enum` => se.copy(values = keyOptionMapping)
                      case _                 => be
                    }
                  )
                )
              case _ => None
            }
          case _ => None
        }
      case _ => None
    }

    val updatedOptions = currentWidget.uiOptions
      .updatedOpt(UIKey.supportingFileGroup)(updatedSupportingFileGroupOpt)
      .updatedOpt(UIKey.blueprintMetadataMapping)(blueprintWidget.uiOptions.get(UIKey.blueprintMetadataMapping))

    val updatedWidget = Some(Widget(WidgetType.FileGroup, updatedOptions))

    (updatedSchema, updatedWidget)
  }

  private def updateGatingQuestionData(
    currentSchema: Schema,
    currentWidget: Widget,
    blueprintSchema: Schema,
    blueprintWidget: Widget
  ): (Option[Schema], Option[Widget]) = {

    def getUpdatedSchema(
      currentSchema: Schema,
      blueprintSchema: Schema,
      valuesFromUIOptions: Seq[Json]
    ): Option[Schema] = {
      blueprintSchema match {
        case b: Schema.`enum` =>
          Some(
            Schema.`enum`(
              componentType = Schema.string(title = b.title.orElse(currentSchema.title)),
              values = valuesFromUIOptions
            )
          )
        case b: Schema.array =>
          b.componentType match {
            case _: Schema.`enum` =>
              Some(
                Schema.array(
                  title = b.title.orElse(currentSchema.title),
                  componentType = Schema.`enum`(
                    componentType = Schema.string(),
                    values = valuesFromUIOptions
                  )
                )
              )
            case _ =>
              None
          }
        case _ =>
          None
      }
    }

    def getUpdatedUIOptions(currentWidget: Widget, blueprintWidget: Widget): (UIOptions, Seq[Json]) = {
      val uiOptions = blueprintWidget.uiOptions
      val blueprintMultiOpts = uiOptions.get[MultipleOptionType](UIKey.multipleOption)
      blueprintMultiOpts match {
        case Some(blueprintOpts) =>
          val blueprintOptionsByMapping = blueprintOpts.options.values.map { opt =>
            opt.blueprintMetadataMapping -> opt
          }.toMap

          val currentMultiOpts = currentWidget.uiOptions.get[MultipleOptionType](UIKey.multipleOption)
          val updatedOptions = currentMultiOpts match {
            case Some(curr) =>
              // Update existing keys
              val updatedFromCurrent = curr.options.flatMap { case (key, currentOpt) =>
                blueprintOptionsByMapping.get(currentOpt.blueprintMetadataMapping).map { blueprintOpt =>
                  key -> currentOpt.copy(
                    formattedText = blueprintOpt.formattedText,
                    blueprintMetadataMapping = blueprintOpt.blueprintMetadataMapping
                  )
                }
              }

              // Add new options from blueprint that don't exist in current
              val missingOptions = blueprintOptionsByMapping.flatMap { case (mapping, blueprintOpt) =>
                val existing = updatedFromCurrent.values.exists(_.blueprintMetadataMapping == mapping)
                if (!existing) {
                  Some(
                    mapping -> SingleOptionType(
                      formattedText = blueprintOpt.formattedText,
                      blueprintMetadataMapping = blueprintOpt.blueprintMetadataMapping
                    )
                  )
                } else None
              }

              updatedFromCurrent ++ missingOptions

            case None =>
              // No current options, take blueprint options as is
              blueprintOpts.options.map { case (key, blueprintOpt) =>
                key -> blueprintOpt
              }
          }

          val keyOptionMapping = updatedOptions.keySet
            .filter(_.nonEmpty)
            .map(Json.fromString)
            .toSeq
          val updatedMultiOpts = MultipleOptionType(updatedOptions)

          val updatedUIOptions = currentWidget.uiOptions
            .updatedOpt(UIKey.formattedText)(uiOptions.get(UIKey.formattedText))
            .updatedOpt(UIKey.multipleOption)(Some(updatedMultiOpts))
            .updatedOpt(UIKey.blueprintMetadataMapping)(uiOptions.get(UIKey.blueprintMetadataMapping))
            .updatedOpt(UIKey.marginBottom)(uiOptions.get(UIKey.marginBottom))
            .updatedOpt(UIKey.fixedLayout)(uiOptions.get(UIKey.fixedLayout))

          (updatedUIOptions, keyOptionMapping)

        case None =>
          (currentWidget.uiOptions, Seq.empty) // No update if blueprint has no multipleOption
      }
    }

    val (updatedUIOptions, keyOptionMapping) = getUpdatedUIOptions(currentWidget, blueprintWidget)
    val updatedSchema = getUpdatedSchema(currentSchema, blueprintSchema, keyOptionMapping)

    val updatedWidget =
      if (!updatedUIOptions.equals(currentWidget.uiOptions) || blueprintWidget.widgetType != currentWidget.widgetType) {
        Some(Widget(blueprintWidget.widgetType, updatedUIOptions))
      } else {
        None
      }

    (updatedSchema, updatedWidget)
  }

  private def updateSignatureData(
    currentSchema: Schema,
    currentWidget: Widget,
    blueprintWidget: Widget
  ): (Option[Schema], Option[Widget]) = {
    // keep the current title because it does not affect to significant UI nor logic rule
    val updatedSchema = Some(currentSchema)

    val uiOptions = blueprintWidget.uiOptions
    val updatedOptions = currentWidget.uiOptions
      .updatedOpt(UIKey.signatureSigner)(uiOptions.get(UIKey.signatureSigner))
      .updatedOpt(UIKey.signatureType)(uiOptions.get(UIKey.signatureType))
      .updatedOpt(UIKey.signature)(uiOptions.get(UIKey.signature))
      .updatedOpt(UIKey.uniqueSignerEmail)(uiOptions.get(UIKey.uniqueSignerEmail))
      .updatedOpt(UIKey.blueprintMetadataMapping)(uiOptions.get(UIKey.blueprintMetadataMapping))

    val updatedWidget = if (!updatedOptions.equals(currentWidget.uiOptions)) {
      Some(Widget(WidgetType.Signature, updatedOptions))
    } else {
      None
    }

    (updatedSchema, updatedWidget)
  }

}

object BuilderTOCBlueprintMetadataSection {

  private def getFormUIInputTypeIconElement(item: FormUIInputType): HtmlElement = {
    val icon = item match {
      case FormUIInputType.Radio            => Icon.Glyph.Radio
      case FormUIInputType.MultipleSuggest  => Icon.Glyph.CheckList
      case FormUIInputType.MultipleCheckbox => Icon.Glyph.Checkbox
      case FormUIInputType.Dropdown         => Icon.Glyph.Expand
    }
    div(IconL(name = Val(icon), size = Icon.Size.Px16)())
  }

  private def getParagraphMessageIconElement(style: ParagraphStyle): HtmlElement = {
    val icon = IconL(Val(Icon.Glyph.Paragraph), size = Icon.Size.Px16)()
    style match {
      case ParagraphStyle.Blue   => div(icon, tw.textPrimary4)
      case ParagraphStyle.Orange => div(icon, tw.textWarning4)
      case _                     => div(icon)
    }
  }

  private[builder] case class FormFieldData(field: Field, alias: String, widget: Widget)

  private[builder] def createFormSchemaFromFormFieldData(formFieldData: FormFieldData): FormSchema = {
    val newSchema = Schema.obj(formFieldData.field)
    val widgets = Map(formFieldData.alias -> formFieldData.widget)

    FormSchema(newSchema, widgets)
  }

  private val uiKeyToCompare = List(
    "ui:formattedText",
    "ui:multipleOption",
    "formattedText",
    "ui:widget",
    "ui:wellType",
    "ui:uiType",
    "ui:supportingFileGroup",
    "ui:signatureSigner",
    "ui:signatureType",
    "ui:signature",
    "ui:uniqueSignerEmail",
    "ui:fixedLayout"
  )

  private val schemaKeyToCompare = List(
    "type",
    "title",
    "enum"
  )

  private[toc] def countFormSchemaDifferences(jsonA: Json, jsonB: Json): Int = {
    val schemaDiffs = compareSchema(jsonA.hcursor.downField("schema"), jsonB.hcursor.downField("schema"))
    val uiSchemaDiffs = compareUiSchema(jsonA.hcursor.downField("uiSchema"), jsonB.hcursor.downField("uiSchema"))
    schemaDiffs + uiSchemaDiffs
  }

  private def compareSchema(cursorA: ACursor, cursorB: ACursor): Int = {
    val propertiesA = cursorA.downField("properties").as[Map[String, Json]].getOrElse(Map.empty)
    val propertiesB = cursorB.downField("properties").as[Map[String, Json]].getOrElse(Map.empty)

    val schemaDiffs = schemaKeyToCompare.count { field =>
      cursorA.get[Json](field).toOption != cursorB.get[Json](field).toOption
    }

    val propertyDiffs = propertiesA.keySet.union(propertiesB.keySet).count { key =>
      val propA = propertiesA.get(key)
      val propB = propertiesB.get(key)

      schemaKeyToCompare.exists { field =>
        propA.flatMap(_.hcursor.get[Json](field).toOption) != propB.flatMap(_.hcursor.get[Json](field).toOption)
      }
    }

    schemaDiffs + propertyDiffs
  }

  private def compareUiSchema(cursorA: ACursor, cursorB: ACursor): Int = {
    val uiSchemaDiffs = cursorA.keys.toSeq.flatten.concat(cursorB.keys.toSeq.flatten).count { key =>
      val uiSchemaA = cursorA.downField(key)
      val uiSchemaB = cursorB.downField(key)

      uiKeyToCompare.exists { field =>
        uiSchemaA.get[Json](field).toOption != uiSchemaB.get[Json](field).toOption
      }
    }
    uiSchemaDiffs
  }

}
