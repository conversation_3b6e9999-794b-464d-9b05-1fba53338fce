// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.builder.signaldata

import cats.syntax.either.*
import io.circe.parser.decode
import zio.prelude.fx.ZPure
import zio.prelude.{NonEmptyList, These}
import anduin.forms.*
import anduin.forms.Form.GaiaLogicVersion
import anduin.forms.builder.logic.RelatedLogicItems.SingleLogicItem
import anduin.forms.builder.logic.generation.{LogicSupportInjections, RuleBatchGenerator}
import anduin.forms.builder.modal.PdfFormCutSharedModal.CutPdfOutput
import anduin.forms.builder.operator.SchemaOperator
import anduin.forms.builder.rule.{LogicRefactor, RuleUtils}
import anduin.forms.builder.signaldata.FormBuilderDataCommand.FormBuilderZData
import anduin.forms.builder.signaldata.FormBuilderDataCommandUtils.generateValidKey
import anduin.forms.builder.signaldata.FormBuilderDataHelpers.{FieldAddedEvent, ModifiedPastedKey}
import anduin.forms.builder.signaldata.FormRuleExtractResult.RuleExtractedResult
import anduin.forms.builder.states.FormBuilderSelection
import anduin.forms.builder.utils.BuilderUtils
import anduin.forms.endpoint.ImportFromAnnotationDocumentResponse
import anduin.forms.logic.extractor.ExtractorResult
import anduin.forms.model.Schema
import anduin.forms.model.Schema.obj.Field
import anduin.forms.rules.{FormLibrary, FormRule}
import anduin.forms.tools.pdf.modal.AnnotationModal.PdfMappingUpdateLog
import anduin.forms.ui.types.{FileId, MultipleOptionType, SignatureBlockType, SignatureMapping}
import anduin.forms.ui.{UIKey, Widget, WidgetType}
import anduin.forms.utils.FormDataConverters
import anduin.forms.version.FormVersionMetadataModel.{
  IgnoredMismatchAsa,
  PdfCutInfo,
  PdfFieldInfo,
  WidgetSource,
  WidgetSourceSummary
}
import anduin.forms.version.*
import anduin.id.annotation.AnnotationDocumentVersionId
import anduin.model.common.FieldAlias
import anduin.model.id.FileId as SysFileId
import com.anduin.stargazer.client.localstorage.LocalStorageKey
import anduin.id.cue.CueModuleVersionId

sealed trait FormBuilderDataCommand derives CanEqual {
  type R
  def underlying: FormBuilderZData[R]
}

object FormBuilderDataCommand {

  given [A] => Conversion[FormBuilderZData[A], FormBuilderDataCommand] = formBuilderZData =>
    new FormBuilderDataCommand {
      override type R = A
      override def underlying: FormBuilderZData[R] = formBuilderZData
    }

  type FormBuilderDataProps = (
    FormData,
    Map[String, String],
    FormVersionMetadataModel,
    FormVersionSystemMetadataModel,
    FormBuilderSelection
  )

  type FormBuilderZData[A] =
    ZPure[
      FormBuilderDataCommandType,
      FormBuilderDataProps,
      FormBuilderDataProps,
      FormBuilderDataHelpers,
      String,
      A
    ]

  def getUploadedPdf: FormBuilderZData[Map[FileId, ExtractedPdf]] = {
    ZPure
      .get[FormBuilderDataProps]
      .map(_._1.uploadedPdf)
      .log(FormBuilderDataCommandType.Inspect)
  }

  def getUploadedDocx: FormBuilderZData[Map[FileId, Seq[String]]] = {
    ZPure
      .get[FormBuilderDataProps]
      .map(_._1.uploadedDocx)
      .log(FormBuilderDataCommandType.Inspect)
  }

  def getEmbeddedPdf: FormBuilderZData[Map[String, FileId]] = {
    ZPure
      .get[FormBuilderDataProps]
      .map(_._1.embeddedPdf)
      .log(FormBuilderDataCommandType.Inspect)
  }

  def getFormSchema: FormBuilderZData[FormSchema] = {
    ZPure
      .get[FormBuilderDataProps]
      .map(_._1.form.defaultSchema)
      .log(FormBuilderDataCommandType.Inspect)
  }

  def getForm: FormBuilderZData[Form] = {
    ZPure
      .get[FormBuilderDataProps]
      .map(_._1.form)
      .log(FormBuilderDataCommandType.Inspect)
  }

  def getFormStandardAliasMapping: FormBuilderZData[Map[String, String]] = {
    ZPure
      .get[FormBuilderDataProps]
      .map(_._2)
      .log(FormBuilderDataCommandType.Inspect)
  }

  def getFormMetadata: FormBuilderZData[FormVersionMetadataModel] = {
    ZPure
      .get[FormBuilderDataProps]
      .map(_._3)
      .log(FormBuilderDataCommandType.Inspect)
  }

  def getFormSystemMetadata: FormBuilderZData[FormVersionSystemMetadataModel] = {
    ZPure
      .get[FormBuilderDataProps]
      .map(_._4)
      .log(FormBuilderDataCommandType.Inspect)
  }

  def getFormRuleWarnings: FormBuilderZData[Seq[FormRuleWarning]] = {
    ZPure
      .get[FormBuilderDataProps]
      .map(_._3.ruleWarnings)
      .log(FormBuilderDataCommandType.Inspect)
  }

  def getFormRules: FormBuilderZData[List[FormRule]] = {
    ZPure
      .get[FormBuilderDataProps]
      .map(_._1.form.rules)
      .log(FormBuilderDataCommandType.Inspect)
  }

  def getFormLibs: FormBuilderZData[List[FormLibrary]] = {
    ZPure
      .get[FormBuilderDataProps]
      .map(_._1.form.libs)
      .log(FormBuilderDataCommandType.Inspect)
  }

  def resetData(
    formData: FormData,
    formStandardAliasMapping: Map[String, String],
    formMetadata: (FormVersionMetadataModel, FormVersionSystemMetadataModel),
    selection: FormBuilderSelection
  ): FormBuilderZData[Unit] =
    ZPure
      .set[FormBuilderDataProps]((formData, formStandardAliasMapping, formMetadata._1, formMetadata._2, selection))
      .log(FormBuilderDataCommandType.Reset)

  def setFormSchema(newFormSchema: FormSchema): FormBuilderZData[FormSchema] = {
    ZPure
      .modify[FormBuilderDataProps, FormBuilderDataProps, FormSchema] {
        case (oldFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection) =>
          val oldForm = oldFormData.form
          val newForm = oldForm.copy(
            namespaceFormSchemaMap = oldForm.namespaceFormSchemaMap + (oldForm.defaultNamespace -> newFormSchema)
          )
          val newFormData = oldFormData.copy(form = newForm)
          oldForm.defaultSchema -> (newFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection)
      }
      .log(FormBuilderDataCommandType.Update)
  }

  def setLogicVersion(version: GaiaLogicVersion): FormBuilderZData[Unit] = {
    ZPure
      .modify[FormBuilderDataProps, FormBuilderDataProps, Unit] {
        case (oldFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection) =>
          val oldForm = oldFormData.form
          val newForm = oldForm.copy(
            gaiaLogicVersion = version
          )
          val newFormData = oldFormData.copy(form = newForm)
          () -> (newFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection)
      }
      .log(FormBuilderDataCommandType.Update)
  }

  def setFormRules(formRules: List[FormRule]): FormBuilderZData[List[FormRule]] = {
    ZPure
      .modify[FormBuilderDataProps, FormBuilderDataProps, List[FormRule]] {
        case (oldFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection) =>
          val oldForm = oldFormData.form
          val newForm = oldForm.copy(
            rules = formRules
          )
          val newFormData = oldFormData.copy(form = newForm)
          oldForm.rules -> (newFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection)
      }
      .log(FormBuilderDataCommandType.Update)
  }

  def setFormLibs(formLibs: List[FormLibrary]): FormBuilderZData[List[FormLibrary]] = {
    ZPure
      .modify[FormBuilderDataProps, FormBuilderDataProps, List[FormLibrary]] {
        case (oldFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection) =>
          val oldForm = oldFormData.form
          val newForm = oldForm.copy(
            libs = formLibs
          )
          val newFormData = oldFormData.copy(form = newForm)
          oldForm.libs -> (newFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection)
      }
      .log(FormBuilderDataCommandType.Update)
  }

  def updateFormSchema(
    updateFn: FormSchema => FormSchema
  ): FormBuilderZData[FormSchema] = {
    ZPure
      .modify[FormBuilderDataProps, FormBuilderDataProps, FormSchema] {
        case (oldFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection) =>
          val oldForm = oldFormData.form
          val newForm = oldForm.copy(
            namespaceFormSchemaMap =
              oldForm.namespaceFormSchemaMap + (oldForm.defaultNamespace -> updateFn(oldForm.defaultSchema))
          )
          val newFormData = oldFormData.copy(form = newForm)
          oldForm.defaultSchema -> (newFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection)
      }
      .log(FormBuilderDataCommandType.Update)
  }

  def updateFormData(
    updateFn: FormData => FormData
  ): FormBuilderZData[Unit] = {
    ZPure
      .modify[FormBuilderDataProps, FormBuilderDataProps, Unit] {
        case (oldFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection) =>
          val newFormData = updateFn(oldFormData)
          () -> (newFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection)
      }
      .log(FormBuilderDataCommandType.Update)
  }

  def updateFormStandardAliasMapping(
    updateFn: Map[String, String] => Map[String, String]
  ): FormBuilderZData[Map[String, String]] = {
    ZPure
      .modify[FormBuilderDataProps, FormBuilderDataProps, Map[String, String]] {
        case (formData, oldFormStandardAliasMapping, formMetadata, formSystemMetadata, selection) =>
          val newFormStandardAliasMapping = updateFn(oldFormStandardAliasMapping)
          oldFormStandardAliasMapping -> (
            formData,
            newFormStandardAliasMapping,
            formMetadata,
            formSystemMetadata,
            selection
          )
      }
      .log(FormBuilderDataCommandType.Update)
  }

  def updateFormMetadata(
    updateFn: FormVersionMetadataModel => FormVersionMetadataModel
  ): FormBuilderZData[FormVersionMetadataModel] = {
    ZPure
      .modify[FormBuilderDataProps, FormBuilderDataProps, FormVersionMetadataModel] {
        case (formData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection) =>
          val newMetadata = updateFn(formMetadata)
          formMetadata -> (formData, formStandardAliasMapping, newMetadata, formSystemMetadata, selection)
      }
      .log(FormBuilderDataCommandType.Update)
  }

  def updateFormSystemMetadata(
    updateFn: FormVersionSystemMetadataModel => FormVersionSystemMetadataModel
  ): FormBuilderZData[FormVersionSystemMetadataModel] = {
    ZPure
      .modify[
        FormBuilderDataProps,
        FormBuilderDataProps,
        FormVersionSystemMetadataModel
      ] { case (formData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection) =>
        val newSystemMetadata = updateFn(formSystemMetadata)
        formSystemMetadata -> (formData, formStandardAliasMapping, formMetadata, newSystemMetadata, selection)
      }
      .log(FormBuilderDataCommandType.Update)
  }

  def updateRejectedGeneratedRules(
    updateFn: Seq[GeneratedRule] => Seq[GeneratedRule]
  ): FormBuilderZData[Seq[GeneratedRule]] = {
    updateFormMetadata { metaData =>
      metaData.copy(
        rejectedGeneratedRules = updateFn(metaData.rejectedGeneratedRules)
      )
    }
      .map(_.rejectedGeneratedRules)
      .log(FormBuilderDataCommandType.Update)
  }

  def updateRuleWarnings(
    updateFn: Seq[FormRuleWarning] => Seq[FormRuleWarning]
  ): FormBuilderZData[Seq[FormRuleWarning]] = {
    updateFormMetadata { metaData =>
      metaData.copy(
        ruleWarnings = updateFn(metaData.ruleWarnings)
      )
    }
      .map(_.ruleWarnings)
      .log(FormBuilderDataCommandType.Update)
  }

  def updateIgnoredInvalidRequireSetups(
    updateFn: Set[String] => Set[String]
  ): FormBuilderZData[Set[String]] = {
    updateFormMetadata { metaData =>
      metaData.copy(
        ignoredInvalidRequireSetups = updateFn(metaData.ignoredInvalidRequireSetups)
      )
    }.map(_.ignoredInvalidRequireSetups)
  }

  def updateRejectedAsaSuggestions(
    updateFn: Seq[AsaRejectedSuggestion] => Seq[AsaRejectedSuggestion]
  ): FormBuilderZData[Seq[AsaRejectedSuggestion]] = {
    updateFormMetadata { metaData =>
      metaData.copy(
        rejectedAsaSuggestions = updateFn(metaData.rejectedAsaSuggestions)
      )
    }.map(_.rejectedAsaSuggestions)
  }

  def updateIgnoredInvalidPdfNonInputMappingFields(
    updateFn: Set[String] => Set[String]
  ): FormBuilderZData[Set[String]] = {
    updateFormMetadata { metadata =>
      metadata.copy(ignoredInvalidPdfNonInputMappingFields = updateFn(metadata.ignoredInvalidPdfNonInputMappingFields))
    }.map(_.ignoredInvalidPdfNonInputMappingFields)
  }

  def updateIgnoredUnmappedPdfNonInputFields(
    updateFn: Set[PdfFieldInfo] => Set[PdfFieldInfo]
  ): FormBuilderZData[Set[PdfFieldInfo]] = {
    updateFormMetadata { metadata =>
      metadata.copy(ignoredUnmappedPdfNonInputFields = updateFn(metadata.ignoredUnmappedPdfNonInputFields))
    }.map(_.ignoredUnmappedPdfNonInputFields)
  }

  def updateCueMappingId(
    updateFn: Option[CueModuleVersionId] => Option[CueModuleVersionId]
  ): FormBuilderZData[Option[CueModuleVersionId]] = {
    updateFormSystemMetadata { metadata =>
      metadata.copy(cueMappingId = updateFn(metadata.cueMappingId))
    }.map(_.cueMappingId)
  }

  def updateWidgetSources(
    updateFn: Map[String, WidgetSource] => Map[String, WidgetSource]
  ): FormBuilderZData[Unit] = {
    for {
      form <- getForm
      _ <- ZPure
        .modify[FormBuilderDataProps, FormBuilderDataProps, Unit] {
          case (formData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection) =>
            val widgetSources = updateFn(formMetadata.widgetSources)
            val sourceCount = widgetSources.toSeq
              .groupBy(_._2)
              .view
              .map { case (source, values) => source.index -> values.size }
              .filterNot(_._2 == 0)
              .toMap
            val totalCount = form.defaultSchema.formSchemaSeq.size
            val newMetadata = formMetadata.copy(
              widgetSources = widgetSources,
              widgetSourceSummary = Some(WidgetSourceSummary(sourceCount, totalCount))
            )
            () -> (formData, formStandardAliasMapping, newMetadata, formSystemMetadata, selection)
        }
        .log(FormBuilderDataCommandType.Update)
    } yield ()
  }

  private def updateIgnoredMismatchAsaData(
    updateFn: Set[IgnoredMismatchAsa] => Set[IgnoredMismatchAsa]
  ): FormBuilderZData[Set[IgnoredMismatchAsa]] = {
    updateFormMetadata { metaData =>
      metaData.copy(
        ignoredMismatchAsa = updateFn(metaData.ignoredMismatchAsa)
      )
    }.map(_.ignoredMismatchAsa)
  }

  private def updatePdfCuts(updateFn: Map[String, PdfCutInfo] => Map[String, PdfCutInfo]) = {
    updateFormMetadata { metadata => metadata.copy(pdfCuts = updateFn(metadata.pdfCuts)) }.map(_.pdfCuts)
  }

  def updateUploadedPdf(
    updateFn: Map[FileId, ExtractedPdf] => Map[FileId, ExtractedPdf]
  ): FormBuilderZData[Map[FileId, ExtractedPdf]] = {
    ZPure
      .modify[FormBuilderDataProps, FormBuilderDataProps, Map[FileId, ExtractedPdf]] {
        case (oldFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection) =>
          val newFormData = oldFormData.copy(
            uploadedPdf = updateFn(oldFormData.uploadedPdf)
          )
          oldFormData.uploadedPdf -> (newFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection)
      }
      .log(FormBuilderDataCommandType.Update)
  }

  def updateEmbeddedPdf(
    updateFn: Map[String, FileId] => Map[String, FileId]
  ): FormBuilderZData[Map[String, FileId]] = {
    ZPure
      .modify[FormBuilderDataProps, FormBuilderDataProps, Map[String, FileId]] {
        case (oldFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection) =>
          val newFormData = oldFormData.copy(
            embeddedPdf = updateFn(oldFormData.embeddedPdf)
          )
          oldFormData.embeddedPdf -> (newFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection)
      }
      .log(FormBuilderDataCommandType.Update)
  }

  def updateFormRules(
    updateFn: List[FormRule] => List[FormRule]
  ): FormBuilderZData[List[FormRule]] = {
    ZPure
      .modify[FormBuilderDataProps, FormBuilderDataProps, List[FormRule]] {
        case (oldFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection) =>
          val oldForm = oldFormData.form
          val newForm = oldForm.copy(
            rules = updateFn(oldForm.rules)
          )
          val newFormData = oldFormData.copy(form = newForm)
          oldForm.rules -> (newFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection)
      }
      .log(FormBuilderDataCommandType.Update)
  }

  def updateFormWidgetMap(
    updateFn: Map[String, Widget] => Map[String, Widget]
  ): FormBuilderZData[Map[String, Widget]] = {
    updateFormSchema { formSchema =>
      val widgetMap = formSchema.uiSchema
      val newWidgetMap = updateFn(widgetMap)
      formSchema.copy(uiSchema = newWidgetMap)
    }.map(_.uiSchema)
  }

  def updateFormLibs(
    updateFn: List[FormLibrary] => List[FormLibrary]
  ): FormBuilderZData[List[FormLibrary]] = {
    ZPure
      .modify[FormBuilderDataProps, FormBuilderDataProps, List[FormLibrary]] {
        case (oldFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection) =>
          val oldForm = oldFormData.form
          val newForm = oldForm.copy(
            libs = updateFn(oldForm.libs)
          )
          val newFormData = oldFormData.copy(form = newForm)
          oldForm.libs -> (newFormData, formStandardAliasMapping, formMetadata, formSystemMetadata, selection)
      }
      .log(FormBuilderDataCommandType.Update)
  }

  def updateSelection(
    updateFn: FormBuilderSelection => FormBuilderSelection
  ): FormBuilderZData[FormBuilderSelection] = {
    ZPure
      .modify[FormBuilderDataProps, FormBuilderDataProps, FormBuilderSelection] {
        case (formData, formStandardAliasMapping, formMetadata, formSystemMetadata, oldSelection) =>
          val newSelection = updateFn(oldSelection)
          oldSelection -> (formData, formStandardAliasMapping, formMetadata, formSystemMetadata, newSelection)
      }
      .log(FormBuilderDataCommandType.Inspect)
  }

  private def updateAnnotationMapping(
    updateFn: Map[SysFileId, AnnotationDocumentVersionId] => Map[SysFileId, AnnotationDocumentVersionId]
  ): FormBuilderZData[Map[SysFileId, AnnotationDocumentVersionId]] = {
    updateFormSystemMetadata { systemMetadata =>
      systemMetadata.copy(
        annotationMapping = updateFn(systemMetadata.annotationMapping)
      )
    }.map(_.annotationMapping)
  }

  private def getUnusedUploadedPdfs(removedKeys: Set[String]) = {
    for {
      uploadedPdfs <- getUploadedPdf
      formSchema <- getFormSchema
      usedUploadedPdfs = formSchema.uiSchema
        .removedAll(removedKeys)
        .values
        .flatMap { widget =>
          for {
            _ <- Option.when(widget.widgetType == WidgetType.File)(())
            fileName <- widget.uiOptions.get(UIKey.fileName)
          } yield fileName
        }
        .toSet
    } yield uploadedPdfs.values.map(_.name).toSet -- usedUploadedPdfs
  }

  private def getUnusedEmbeddedPdfs(removedKeys: Set[String]) = {
    for {
      embeddedPdfs <- getEmbeddedPdf
      formSchema <- getFormSchema
      usedEmbeddedPdfs = formSchema.uiSchema
        .removedAll(removedKeys)
        .values
        .flatMap { widget =>
          for {
            _ <- Option.when(widget.widgetType == WidgetType.Pdf)(())
            fileName <- widget.uiOptions.get(UIKey.embeddedPdf)
          } yield fileName
        }
        .toSet
    } yield embeddedPdfs.keySet -- usedEmbeddedPdfs
  }

  private def removeFileWidgets(keys: Set[String]) = {
    for {
      unusedUploadedPdfs <- getUnusedUploadedPdfs(keys)
      removedUploadedPdfKeys <- getUploadedPdf
        .map(_.filter { case _ -> extractedPdf => unusedUploadedPdfs.contains(extractedPdf.name) })
        .map(_.keySet)
      _ <- updateUploadedPdf(_ -- removedUploadedPdfKeys)
      _ <- updateAnnotationMapping(_ -- removedUploadedPdfKeys.flatMap(FormDataConverters.fileIdTypeToFileId))
      _ <- updatePdfCuts(_.filterNot { case _ -> pdfCutInfo => unusedUploadedPdfs.contains(pdfCutInfo.fileName) })
    } yield ()
  }

  private def removeEmbeddedPdfWidgets(keys: Set[String]) = {
    for {
      unusedEmbeddedPdfs <- getUnusedEmbeddedPdfs(keys)
      _ <- updateEmbeddedPdf(_ -- unusedEmbeddedPdfs)
      _ <- updatePdfCuts(_ -- unusedEmbeddedPdfs)
    } yield ()
  }

  def removeEmbeddedPdfWidget(key: String): FormBuilderZData[Unit] = {
    removeEmbeddedPdfWidgets(Set(key))
  }

  def getAllKeys: FormBuilderZData[Set[String]] = {
    for {
      formSchema <- getFormSchema
    } yield FormBuilderDataCommandUtils.findAllKeysSet(formSchema)
  }

  def modifyKeys(
    rawFields: Seq[Field],
    rawWidgetMap: Map[String, Widget],
    allKeysInRules: Set[String]
  ): FormBuilderZData[ModifiedPastedKey] = {
    for {
      existingKeys <- getAllKeys
      allFieldKeys <- delay {
        rawFields.view
          .flatMap(FormBuilderDataCommandUtils.findAllKeysSet)
          .toSet ++ rawWidgetMap.keySet
      }
      keysModifiers = FormBuilderDataCommandUtils.getKeysModifier(existingKeys ++ allKeysInRules, allFieldKeys)
      newKeys = allFieldKeys.map(key => keysModifiers.getOrElse(key, key))
      modifiedFields = rawFields
        .map(FormBuilderDataCommandUtils.modifyKey(_, keyModifier = key => keysModifiers.getOrElse(key, key)))
      dataWithLogEvents = rawWidgetMap.view.map { case (key, widget) =>
        val data = FormBuilderDataCommandUtils.modifyKeyWidget(
          key,
          widget,
          key => keysModifiers.getOrElse(key, key)
        )
        (data._1, data._2) -> data._3
      }.toSeq
      modifiedWidgetMap = dataWithLogEvents.view.map(_._1).toMap
      logEvents = dataWithLogEvents.flatMap(_._2)
      _ <- ZPure.serviceWith[FormBuilderDataHelpers] { helpers =>
        helpers.onKeyModified.onNext(logEvents)
      }
    } yield ModifiedPastedKey(
      modifiedFields,
      modifiedWidgetMap,
      keysModifiers,
      newKeys
    )
  }

  def addChild(
    rawFields: Seq[Field],
    rawWidgetMap: Map[String, Widget],
    rawRules: Seq[SingleLogicItem] = Seq.empty,
    parent: String,
    widgetSource: WidgetSource,
    allKeysInRules: Set[String]
  ): FormBuilderZData[Unit] = {
    for {
      modifiedResult <- modifyKeys(
        rawFields,
        rawWidgetMap,
        allKeysInRules
      )
      _ <- FormBuilderDataCommand.updateFormSchema { formSchema =>
        val newSchema = SchemaOperator.addChild(
          formSchema.schema,
          modifiedResult.fields,
          Some(parent)
        )
        val newUiSchema = formSchema.uiSchema ++ modifiedResult.widgetMap
        FormSchema(newSchema, newUiSchema)
      }
      _ <- onFieldsAdded(
        modifiedResult,
        rawWidgetMap,
        rawRules,
        widgetSource
      )
    } yield ()
  }

  def addSibling(
    rawFields: Seq[Field],
    rawWidgetMap: Map[String, Widget],
    rawRules: Seq[SingleLogicItem] = Seq.empty,
    prevKeyOpt: Option[String],
    widgetSource: WidgetSource,
    allKeysInRules: Set[String]
  ): FormBuilderZData[Unit] = {
    for {
      modifiedResult <- modifyKeys(
        rawFields,
        rawWidgetMap,
        allKeysInRules
      )
      _ <- FormBuilderDataCommand.updateFormSchema { formSchema =>
        val obj = formSchema.schema
        val newSchema = prevKeyOpt.fold(obj.copy(fields = obj.fields ++ modifiedResult.fields)) { prevKey =>
          SchemaOperator.addSibling(
            obj,
            modifiedResult.fields,
            prevKey
          )
        }
        val newUiSchema = formSchema.uiSchema ++ modifiedResult.widgetMap
        FormSchema(newSchema, newUiSchema)
      }
      _ <- onFieldsAdded(
        modifiedResult,
        rawWidgetMap,
        rawRules,
        widgetSource
      )
    } yield ()
  }

  private def onFieldsAdded(
    modifiedResult: ModifiedPastedKey,
    rawWidgetMap: Map[String, Widget],
    rawRules: Seq[SingleLogicItem],
    widgetSource: WidgetSource
  ): FormBuilderZData[Unit] = {
    for {
      form <- getForm
      ruleWarnings <- getFormRuleWarnings
      (newRules, warningItems) <- delay {
        RuleUtils.appendPastedRules(
          form,
          rawRules,
          modifiedResult.keyMap,
          rawWidgetMap.keys.toSeq,
          ruleWarnings
        )
      }
      _ <- updateFormRules(_ => newRules)
      _ <- updateRuleWarnings(ruleWarnings => warningItems ++ ruleWarnings)
      _ <- updateWidgetSources { widgetSources =>
        widgetSources ++ modifiedResult.newKeys.map(_ -> widgetSource)
      }
      _ <- ZPure.serviceWith[FormBuilderDataHelpers] { helpers =>
        helpers.onFieldAdded.onNext(FieldAddedEvent(modifiedResult.fields.map(_.name), warningItems.nonEmpty))
      }
    } yield ()
  }

  private def handleMissingKeysOfLogicSupportPage(
    missingKeys: Set[String],
    logicSupportSchema: Field,
    logicSupportWidgets: Map[String, Widget]
  ): FormBuilderZData[Unit] = {
    for {
      _ <- FormBuilderDataCommand.updateFormSchema { formSchema =>
        missingKeys
          .foldLeft(formSchema) { case (prevFormSchema, key) =>
            val addedSchemaOpt = for {
              keySchema <- logicSupportSchema.schemaMap.get(key)
              keyWidget <- logicSupportWidgets.get(key)
              isInputWidget = WidgetType.inputWidgets.contains(keyWidget.widgetType)

              res = (keySchema, keyWidget) if isInputWidget
            } yield res

            addedSchemaOpt.fold(prevFormSchema) { case (keySchema, keyWidget) =>
              val newSchema = SchemaOperator.addChild(
                prevFormSchema.schema,
                Seq(Field(key, keySchema)),
                Some(LogicSupportInjections.LogicSupportPageKey)
              )
              val newUiSchema = prevFormSchema.uiSchema + (key -> keyWidget)

              FormSchema(newSchema, newUiSchema)
            }
          }
      }
    } yield ()
  }

  // If gr library doesn't exist, add it.
  def makeSureGenerateRuleFormLibraryExists(): FormBuilderZData[Unit] = {
    for {
      _ <- FormBuilderDataCommand.updateFormLibs(libs => {
        if (!libs.exists(_.name == RuleBatchGenerator.GenerateRuleLibName)) {
          libs :+ RuleBatchGenerator.getGenerateRuleFormLibrary
        } else {
          libs
        }
      })
    } yield ()
  }

  def injectLogicSupport(): FormBuilderZData[Unit] = {
    for {
      _ <- makeSureGenerateRuleFormLibraryExists()
      _ <- FormBuilderDataCommand.setLogicVersion(GaiaLogicVersion.V2)

      logicSupportSchema <- ZPure.fromEither(
        decode[Field](LogicSupportInjections.FormSchema).left.map(_.getMessage)
      )
      logicSupportWidgets <- ZPure.fromEither(
        decode[Map[String, Widget]](LogicSupportInjections.FormUISchema).left.map(_.getMessage)
      )

      existingKeys <- FormBuilderDataCommand.getAllKeys
      allAddedKeys <- FormBuilderDataCommand.delay {
        FormBuilderDataCommandUtils.findAllKeysSet(logicSupportSchema)
      }
      conflictKeys = allAddedKeys.intersect(existingKeys)
      isLogicSupportPageFullyAdded = conflictKeys.equals(allAddedKeys)
      _ <-
        if (conflictKeys.isEmpty) {
          FormBuilderDataCommand.updateFormSchema { formSchema =>
            val newSchema = SchemaOperator.addChild(
              formSchema.schema,
              Seq(logicSupportSchema)
            )
            val newUiSchema = formSchema.uiSchema ++ logicSupportWidgets

            FormSchema(newSchema, newUiSchema)
          }
        } else if (!isLogicSupportPageFullyAdded) {
          val missingKeys = allAddedKeys.removedAll(conflictKeys)

          handleMissingKeysOfLogicSupportPage(
            missingKeys,
            logicSupportSchema,
            logicSupportWidgets
          )
        } else {
          ZPure.unit[FormBuilderDataProps]
        }
    } yield ()
  }

  def renameKey(
    oldKey: String,
    newKey: String,
    extractedRules: List[RuleExtractedResult]
  ): FormBuilderZData[Unit] = {
    for {
      formSchema <- getFormSchema
      newSchema = SchemaOperator.renameKey(
        formSchema.schema,
        oldKey,
        newKey
      )
      keysModifier = Map(oldKey -> newKey)
      dataWithLogEvents = formSchema.uiSchema.view.map { case (key, widget) =>
        val data = FormBuilderDataCommandUtils.modifyKeyWidget(
          key,
          widget,
          key => keysModifier.getOrElse(key, key)
        )
        (data._1, data._2) -> data._3
      }.toSeq
      newUiSchema = dataWithLogEvents.view.map(_._1).toMap
      logEvents = dataWithLogEvents.flatMap(_._2)
      _ <- ZPure.serviceWith[FormBuilderDataHelpers] { helpers =>
        helpers.onKeyModified.onNext(logEvents)
      }
      _ <- setFormSchema(FormSchema(newSchema, newUiSchema))
      _ <- updateFormStandardAliasMapping(_ - oldKey)
      _ <- updateWidgetSources { widgetSources =>
        widgetSources.get(oldKey).fold(widgetSources) { source =>
          widgetSources - oldKey + (newKey -> source)
        }
      }
      _ <- renameFieldInRelatedRules(
        oldKey,
        newKey,
        extractedRules
      )
      _ <- ZPure.serviceWith[FormBuilderDataHelpers] { helpers =>
        helpers.onFieldRename.onNext(newKey)
      }
    } yield ()
  }

  private def renameFieldInRelatedRules(
    oldKey: String,
    newKey: String,
    extractedRules: List[RuleExtractedResult]
  ): FormBuilderZData[Unit] = {
    for {
      updatedRelatedRules <- delay {
        RuleUtils.getRelatedRules(Set(oldKey), extractedRules).map { case (rule, extractorResult) =>
          val result = LogicRefactor.renameAliasesInLogic(
            rule.value,
            extractorResult,
            renameMap = Map(oldKey -> newKey)
          )
          rule.copy(value = result.content) -> result
        }
      }
      // 1. Update rules content
      _ <- ZPure.when(updatedRelatedRules.nonEmpty) {
        updateFormRules { curRules =>
          val updatedRuleMap = updatedRelatedRules.map { case (rule, _) =>
            rule.name -> rule
          }.toMap
          curRules.map { rule =>
            updatedRuleMap.getOrElse(rule.name, rule)
          }
        }
      }
      // 2. Update rules warning
      _ <- ZPure.when(updatedRelatedRules.exists(!_._2.isComplete)) {
        updateRuleWarnings { curRuleWarnings =>
          val updatedWarningRules = updatedRelatedRules.flatMap { case (rule, extractorResult) =>
            Option.when(!extractorResult.isComplete)(rule.name)
          }.toSet
          curRuleWarnings.map { ruleWarning =>
            if (updatedWarningRules.contains(ruleWarning.ruleName)) {
              ruleWarning.addKeyModifiers(oldKey -> newKey)
            } else {
              ruleWarning
            }
          }
        }
      }
    } yield ()
  }

  def reTitle(
    key: String,
    newTitle: String
  ): FormBuilderZData[Unit] = {
    for {
      _ <- updateFormSchema { formSchema =>
        val obj = formSchema.schema
        val newSchema = SchemaOperator
          .find(obj, key)
          .fold(obj)(schema =>
            SchemaOperator.modify(
              obj,
              key,
              schema.withTitle(newTitle)
            )
          )
        formSchema.copy(schema = newSchema)
      }
    } yield ()
  }

  def remove(
    key: String
  ): FormBuilderZData[Unit] = {
    for {
      formSchema <- getFormSchema
      keys = SchemaOperator
        .find(formSchema.schema, key)
        .fold(
          Set.empty[String]
        ) { schema =>
          FormBuilderDataCommandUtils.findAllKeysSet(schema)
        } + key
      newSchema = SchemaOperator.removeKey(formSchema.schema, key)
      keysModifier = keys.view.map(_ -> "").toMap
      dataWithLogEvents = formSchema.uiSchema.view
        .filterKeys(!keys.contains(_))
        .map { case (key, widget) =>
          val data = FormBuilderDataCommandUtils.modifyKeyWidget(
            key,
            widget,
            key => keysModifier.getOrElse(key, key)
          )
          (data._1, data._2) -> data._3
        }
        .toSeq
      newUiSchema = dataWithLogEvents.view.map(_._1).toMap
      logEvents = dataWithLogEvents.flatMap(_._2)
      _ <- ZPure.serviceWith[FormBuilderDataHelpers] { helpers =>
        helpers.onKeyModified.onNext(logEvents)
      }
      newFormSchema = FormSchema(newSchema, newUiSchema)
      _ <- setFormSchema(newFormSchema)
      _ <- updateFormStandardAliasMapping(_.removedAll(keys))
      _ <- removeFileWidgets(keys)
      _ <- removeEmbeddedPdfWidgets(keys)
      _ <- updateWidgetSources { widgetSources =>
        widgetSources -- keys
      }
      _ <- ZPure.serviceWith[FormBuilderDataHelpers] { helpers =>
        val newKeyOpt =
          FormBuilderDataCommandUtils.takeWhile(formSchema.schema, (key: String) => !keys.contains(key)).lastOption
        val newKey = newKeyOpt.orElse(newFormSchema.schema.fields.headOption.map(_.name)).getOrElse("")
        helpers.onFieldRemoved.onNext(newKey)
      }
    } yield ()
  }

  def move(
    key: String,
    parentOpt: Option[String],
    prevOpt: Option[String]
  ): FormBuilderZData[Unit] = {
    for {
      _ <- updateFormSchema { formSchema =>
        val newSchema = SchemaOperator.move(
          formSchema.schema,
          key,
          parentOpt,
          prevOpt
        )
        formSchema.copy(schema = newSchema)
      }
    } yield ()
  }

  def copy(key: String): FormBuilderZData[Unit] = {
    for {
      formSchema <- getFormSchema
      copyData = {
        for {
          schemaToCopy <- SchemaOperator.find(formSchema.schema, key)
          allKeys = FormBuilderDataCommandUtils.findAllKeysSet(schemaToCopy) + key
          uiSchemaToCopy = formSchema.uiSchema.view
            .filterKeys(allKeys.contains)
            .toMap
        } yield Field(key, schemaToCopy) -> uiSchemaToCopy
      }
      _ <- ZPure.serviceWith[FormBuilderDataHelpers] { helpers =>
        copyData.foreach { case (fieldToCopy, uiSchemaToCopy) =>
          helpers.setLocalStorage(LocalStorageKey.BuilderCopyFieldKey, fieldToCopy)
          helpers.setLocalStorage(LocalStorageKey.BuilderCopyWidgetKey, uiSchemaToCopy)
          helpers.setLocalStorage(LocalStorageKey.BuilderCopyRule, Seq.empty[Seq[SingleLogicItem]])
        }
      }
    } yield ()
  }

  def copyItemAndRules(key: String, extractedRules: Seq[(Int, FormRule, ExtractorResult)])
    : FormBuilderZData[Seq[String]] = {
    for {
      _ <- copy(key)
      formSchema <- getFormSchema
      formMetadata <- getFormMetadata
      copiedKeys = formSchema.schema.schemaSeq.find(_._1 == key).map(_._2.schemaMap.keySet).getOrElse(Set.empty) + key
      copiedNamespaceKeys = copiedKeys.map(key => NamespaceKey(Form.DefaultNamespace, key))
      // Copy all rules which one of its inputs or outputs is inside the copied widgets
      relevantItems = extractedRules.collect {
        case (_, rule, extractResult)
            if (extractResult.inputKeys ++ extractResult.outputKeys).intersect(copiedNamespaceKeys).nonEmpty =>
          SingleLogicItem(extractResult, rule)
      }
      // Warned rule shouldn't be included in copy content
      // Because it's not been fixed properly yet
      (copiedItems, warnedItems) = relevantItems.partition { item =>
        formMetadata.ruleWarnings.find(_.ruleName == item.rule.name).forall(_.resolved)
      }
      _ <- ZPure.serviceWith[FormBuilderDataHelpers] { helpers =>
        helpers.setLocalStorage(LocalStorageKey.BuilderCopyRule, copiedItems)
      }
    } yield warnedItems.map(_.rule.name)
  }

  def pasteAsChild(
    key: String,
    allKeysInRules: Set[String]
  ): FormBuilderZData[Unit] = {
    for {
      pasteData <- ZPure.serviceWith[FormBuilderDataHelpers] { helpers =>
        for {
          fieldToPaste <- helpers.getLocalStore[Field](LocalStorageKey.BuilderCopyFieldKey)
          uiSchemaToPaste <- helpers.getLocalStore[Map[String, Widget]](LocalStorageKey.BuilderCopyWidgetKey)
          ruleToPaste <- helpers.getLocalStore[Seq[SingleLogicItem]](LocalStorageKey.BuilderCopyRule)
        } yield (fieldToPaste, uiSchemaToPaste, ruleToPaste)
      }
      addedKeys <- pasteData.fold[FormBuilderZData[Unit]](
        ZPure.unit
      ) { case (fieldToPaste, uiSchemaToPaste, ruleToPaste) =>
        addChild(
          Seq(fieldToPaste),
          uiSchemaToPaste,
          ruleToPaste,
          key,
          widgetSource = WidgetSource.COPY,
          allKeysInRules = allKeysInRules
        )
      }
    } yield addedKeys
  }

  def pasteAsSibling(
    key: String,
    allKeysInRules: Set[String]
  ): FormBuilderZData[Unit] = {
    for {
      pasteData <- ZPure.serviceWith[FormBuilderDataHelpers] { helpers =>
        for {
          fieldToPaste <- helpers.getLocalStore[Field](LocalStorageKey.BuilderCopyFieldKey)
          uiSchemaToPaste <- helpers.getLocalStore[Map[String, Widget]](LocalStorageKey.BuilderCopyWidgetKey)
          ruleToPaste <- helpers.getLocalStore[Seq[SingleLogicItem]](LocalStorageKey.BuilderCopyRule)
        } yield (fieldToPaste, uiSchemaToPaste, ruleToPaste)
      }
      addedKeys <- pasteData.fold[FormBuilderZData[Unit]](
        ZPure.unit
      ) { case (fieldToPaste, uiSchemaToPaste, ruleToPaste) =>
        addSibling(
          Seq(fieldToPaste),
          uiSchemaToPaste,
          ruleToPaste,
          Some(key),
          widgetSource = WidgetSource.COPY,
          allKeysInRules = allKeysInRules
        )
      }
    } yield addedKeys
  }

  def updateSchemaWidget(
    data: Map[String, These[Schema, Widget]]
  ): FormBuilderZData[Unit] = {
    for {
      _ <- ZPure.forEach(data.toSeq) { case (key, schemaWidget) =>
        updateFormSchema { formSchema =>
          schemaWidget match {
            case These.Left(schema) =>
              formSchema.copy(
                schema = SchemaOperator.modify(
                  formSchema.schema,
                  key,
                  schema
                )
              )
            case These.Right(widget) =>
              formSchema.copy(
                uiSchema = formSchema.uiSchema.updated(key, widget)
              )
            case These.Both(schema, widget) =>
              formSchema.copy(
                schema = SchemaOperator.modify(
                  formSchema.schema,
                  key,
                  schema
                ),
                uiSchema = formSchema.uiSchema.updated(key, widget)
              )
          }
        }
      }
    } yield ()
  }

  def uploadToFileWidget(
    key: String,
    newFileId: FileId,
    newExtractedPdf: ExtractedPdf,
    newAnnotationVersionIdOpt: Option[AnnotationDocumentVersionId]
  ): FormBuilderZData[Unit] = {
    for {
      unusedUploadedPdfs <- getUnusedUploadedPdfs(Set(key))
      removedUploadedPdfKeys <- getUploadedPdf.map(_.filterNot { case _ -> extractedPdf =>
        !unusedUploadedPdfs.contains(extractedPdf.name) &&
        extractedPdf.name.toLowerCase != newExtractedPdf.name.toLowerCase
      }.keySet)
      _ <- updateUploadedPdf(_ -- removedUploadedPdfKeys + (newFileId -> newExtractedPdf))
      _ <- updateAnnotationMapping { mapping =>
        val newSysFileDataOpt = for {
          newSysFile <- FormDataConverters.fileIdTypeToFileId(newFileId)
          newAnnotationVersionId <- newAnnotationVersionIdOpt
        } yield (newSysFile, newAnnotationVersionId)

        mapping --
          removedUploadedPdfKeys.flatMap(FormDataConverters.fileIdTypeToFileId) ++
          newSysFileDataOpt
      }
      _ <- updateFormWidgetMap(_.map { case widgetKey -> widget =>
        if (
          widgetKey == key || widget.widgetType == WidgetType.File && widget.uiOptions
            .get(UIKey.fileName)
            .exists(_.toLowerCase == newExtractedPdf.name.toLowerCase)
        ) {
          widgetKey -> widget.copy(uiOptions = widget.uiOptions.updated(UIKey.fileName)(newExtractedPdf.name))
        } else {
          widgetKey -> widget
        }
      })
      _ <- updatePdfCuts(
        _.view
          .mapValues { pdfCutInfo =>
            if (
              unusedUploadedPdfs.contains(pdfCutInfo.fileName) ||
              pdfCutInfo.fileName.toLowerCase == newExtractedPdf.name.toLowerCase
            ) {
              pdfCutInfo.copy(fileName = newExtractedPdf.name, needReview = true)
            } else {
              pdfCutInfo
            }
          }
          .toMap
      )
    } yield ()
  }

  def uploadToEmbeddedPdfWidget(
    key: String,
    newFileId: FileId,
    newFileName: String
  ): FormBuilderZData[Unit] = {
    for {
      unusedEmbeddedPdfs <- getUnusedEmbeddedPdfs(Set(key))
      _ <- updateEmbeddedPdf(_.filter { case fileName -> _ =>
        !unusedEmbeddedPdfs.contains(fileName) && fileName.toLowerCase != newFileName.toLowerCase
      } + (newFileName -> newFileId))
      _ <- updateFormWidgetMap(_.map { case widgetKey -> widget =>
        if (
          widgetKey == key || widget.widgetType == WidgetType.Pdf && widget.uiOptions
            .get(UIKey.embeddedPdf)
            .exists(_.toLowerCase == newFileName.toLowerCase)
        ) {
          widgetKey -> widget.copy(uiOptions = widget.uiOptions.updated(UIKey.embeddedPdf)(newFileName))
        } else {
          widgetKey -> widget
        }
      })
      _ <- updatePdfCuts(_.filter { case fileName -> _ =>
        !unusedEmbeddedPdfs.contains(fileName) && fileName.toLowerCase != newFileName.toLowerCase
      })
    } yield ()
  }

  def cutEmbeddedPdfWidgetFromUploadedPdf(
    key: String,
    newFileId: FileId,
    newFileName: String,
    pdfCutInfo: PdfCutInfo
  ): FormBuilderZData[Unit] = {
    for {
      _ <- uploadToEmbeddedPdfWidget(
        key,
        newFileId,
        newFileName
      )
      _ <- updatePdfCuts(_ + (newFileName -> pdfCutInfo))
    } yield ()
  }

  def selectEmbeddedPdfWidgetFromEmbeddedPdf(key: String, fileName: String): FormBuilderZData[Unit] = {
    for {
      unusedEmbeddedPdfs <- getUnusedEmbeddedPdfs(Set(key))
      _ <- updateEmbeddedPdf(_ -- unusedEmbeddedPdfs)
      _ <- updateFormWidgetMap(
        _.updatedWith(key)(_.map { widget =>
          widget.copy(uiOptions = widget.uiOptions.updated(UIKey.embeddedPdf)(fileName))
        })
      )
      _ <- updatePdfCuts(_ -- unusedEmbeddedPdfs)
    } yield ()
  }

  def generateKeyFromTitle(
    oldKey: String,
    title: String,
    allKeysInRules: Set[String]
  ): FormBuilderZData[Option[String]] = {
    for {
      aliasOpt <- delay {
        FieldAlias(title).normalizedAlias.toOption.map(_.alias)
      }
      keyOpt <- aliasOpt.fold[FormBuilderZData[Option[String]]](
        ZPure.succeed(None)
      ) { alias =>
        for {
          allKeys <- getAllKeys.map(_ - oldKey)
          newKey = generateValidKey(alias, allKeys ++ allKeysInRules)
        } yield Some(newKey)
      }
    } yield keyOpt
  }

  def updatePdfMapping(
    key: String,
    updateFn: (Seq[Either[SignatureMapping, String]], WidgetType) => Seq[Either[SignatureMapping, String]]
  ): FormBuilderZData[Unit] = {
    updateFormSchema { formSchema =>
      val uiSchema = formSchema.uiSchema
      val dataOpt = uiSchema.get(key).map { widget =>
        val primaryMappings = widget.uiOptions
          .get(UIKey.pdfMapping)
          .map(_.map(_.asRight[SignatureMapping]))
          .getOrElse(Seq.empty)
        val signatureMappings = widget.uiOptions
          .get(UIKey.signature)
          .map(_.map(_.asLeft[String]))
          .getOrElse(Seq.empty)
        (primaryMappings ++ signatureMappings) -> widget.widgetType
      }
      dataOpt.fold(formSchema) { case (mappings, widgetType) =>
        val nextMappings = updateFn(mappings, widgetType)
        val nextUISchema = uiSchema.updatedWith(key)(_.map { widget =>
          val (signatureMappings, primaryMappings) = nextMappings.partitionMap(identity)
          val nextUIOptions = widget.uiOptions
            .updated(UIKey.pdfMapping)(primaryMappings)
            .updated(UIKey.signature)(signatureMappings)
          widget.copy(uiOptions = nextUIOptions)
        })
        formSchema.copy(uiSchema = nextUISchema)
      }
    }.unit
  }

  def innerUpdateUploadedPdf(
    oldFileId: FileId,
    newFileId: FileId,
    oldExtractedPdf: ExtractedPdf,
    newExtractedPdf: ExtractedPdf,
    newAnnotationVersionIdOpt: Option[AnnotationDocumentVersionId]
  ) = {
    for {
      _ <- updateUploadedPdf(_ - oldFileId + (newFileId -> newExtractedPdf))
      _ <- updateAnnotationMapping { mapping =>
        val oldSysFileOpt = FormDataConverters.fileIdTypeToFileId(oldFileId)
        val newSysFileDataOpt = for {
          oldSysFile <- oldSysFileOpt
          newSysFile <- FormDataConverters.fileIdTypeToFileId(newFileId)
          newLinkedAnnotationId <- newAnnotationVersionIdOpt.orElse(mapping.get(oldSysFile))
        } yield (newSysFile, newLinkedAnnotationId)

        mapping -- oldSysFileOpt ++ newSysFileDataOpt
      }
      _ <- updateFormWidgetMap(
        _.view
          .mapValues { widget =>
            if (
              widget.widgetType == WidgetType.File && widget.uiOptions
                .get(UIKey.fileName)
                .contains(oldExtractedPdf.name)
            ) {
              widget.copy(uiOptions = widget.uiOptions.updated(UIKey.fileName)(newExtractedPdf.name))
            } else {
              widget
            }
          }
          .toMap
      )
    } yield ()
  }

  def renameUploadedPdf(
    oldFileId: FileId,
    newFileId: FileId,
    newExtractedPdf: ExtractedPdf
  ): FormBuilderZData[Unit] = {
    for {
      uploadedPdfs <- getUploadedPdf
      _ <- ZPure.foreachDiscard(uploadedPdfs.get(oldFileId)) { oldExtractedPdf =>
        for {
          _ <- innerUpdateUploadedPdf(oldFileId, newFileId, oldExtractedPdf, newExtractedPdf, None)
          _ <- updatePdfCuts(
            _.view
              .mapValues { pdfCutInfo =>
                if (pdfCutInfo.fileName == oldExtractedPdf.name) {
                  pdfCutInfo.copy(fileName = newExtractedPdf.name)
                } else {
                  pdfCutInfo
                }
              }
              .toMap
          )
        } yield ()
      }
    } yield ()
  }

  def renameEmbeddedPdf(oldFileId: FileId, newFileId: FileId, newFileName: String): FormBuilderZData[Unit] = {
    for {
      embeddedPdfs <- getEmbeddedPdf
      _ <- ZPure.foreachDiscard(embeddedPdfs.find(_._2 == oldFileId)) { case oldFileName -> _ =>
        for {
          _ <- updateEmbeddedPdf(_ - oldFileName + (newFileName -> newFileId))
          _ <- updateFormWidgetMap(
            _.view
              .mapValues { widget =>
                if (widget.widgetType == WidgetType.Pdf && widget.uiOptions.get(UIKey.embeddedPdf).contains(oldFileName)) {
                  widget.copy(uiOptions = widget.uiOptions.updated(UIKey.embeddedPdf)(newFileName))
                } else {
                  widget
                }
              }
              .toMap
          )
          _ <- updatePdfCuts { pdfCuts =>
            pdfCuts.get(oldFileName).fold(pdfCuts) { pdfCutInfo => pdfCuts - oldFileName + (newFileName -> pdfCutInfo) }
          }
        } yield ()
      }
    } yield ()
  }

  def replaceUploadedPdf(
    oldFileId: FileId,
    newFileId: FileId,
    newExtractedPdf: ExtractedPdf,
    newAnnotationVersionIdOpt: Option[AnnotationDocumentVersionId],
    updateMappingLogsOpt: Option[NonEmptyList[PdfMappingUpdateLog]]
  ): FormBuilderZData[Unit] = {
    for {
      uploadedPdfs <- getUploadedPdf
      _ <- ZPure.foreachDiscard(uploadedPdfs.get(oldFileId)) { oldExtractedPdf =>
        val oldFileName = oldExtractedPdf.name
        for {
          _ <- innerUpdateUploadedPdf(
            oldFileId,
            newFileId,
            oldExtractedPdf,
            newExtractedPdf,
            newAnnotationVersionIdOpt
          )
          _ <- ZPure.foreachDiscard(updateMappingLogsOpt) { updateMappingLogs =>
            for {
              mappingMapDataSet <- ZPure.succeed {
                BuilderUtils.getMappingDataMap(uploadedPdfs - oldFileId, Map.empty).keySet
              }
              _ <- updateFormWidgetMap { uiSchemaMap =>
                updateMappingLogs.foldLeft(uiSchemaMap) { case (curMap, updateMappingLog) =>
                  updateMappingLog match {
                    case PdfMappingUpdateLog.PrimaryMapping(fieldName, oldMapping, newPdfMapping) =>
                      curMap.updatedWith(fieldName) { oldWidgetOpt =>
                        for {
                          oldWidget <- oldWidgetOpt
                          pdfMappings <- oldWidget.uiOptions.get(UIKey.pdfMapping)
                          newMappings = (if (mappingMapDataSet.contains(oldMapping)) {
                                           pdfMappings
                                         } else {
                                           pdfMappings.filterNot(_ == oldMapping)
                                         }) :+ newPdfMapping
                        } yield oldWidget.copy(
                          uiOptions = oldWidget.uiOptions.updated(UIKey.pdfMapping)(
                            newMappings.distinct
                          )
                        )
                      }
                    case PdfMappingUpdateLog.SecondaryMapping(fieldName, option, oldMapping, newPdfMapping) =>
                      curMap.updatedWith(fieldName) { oldWidgetOpt =>
                        for {
                          oldWidget <- oldWidgetOpt
                          optionMap <- oldWidget.uiOptions.get(UIKey.multipleOption).map(_.options)
                          newOptionMap = optionMap.updatedWith(option)(_.map { singleOptionTpe =>
                            val newMappings = (if (mappingMapDataSet.contains(oldMapping)) {
                                                 singleOptionTpe.pdfMappings
                                               } else {
                                                 singleOptionTpe.pdfMappings.filterNot(_ == oldMapping)
                                               }) :+ newPdfMapping
                            singleOptionTpe.copy(pdfMappings = newMappings.distinct)
                          })
                        } yield oldWidget.copy(
                          uiOptions = oldWidget.uiOptions.updated(UIKey.multipleOption)(MultipleOptionType(newOptionMap))
                        )
                      }
                    case PdfMappingUpdateLog.SignatureMapping(fieldName, oldMapping, newPdfMapping) =>
                      curMap.updatedWith(fieldName) { oldWidgetOpt =>
                        for {
                          oldWidget <- oldWidgetOpt
                          signatureMappings <- oldWidget.uiOptions.get(UIKey.signature)
                          newSignatureMappings = (if (mappingMapDataSet.contains(oldMapping)) {
                                                    signatureMappings
                                                  } else {
                                                    signatureMappings.filterNot(_.mapping == oldMapping)
                                                  }) :+ SignatureMapping(newPdfMapping, SignatureBlockType.Signature)
                        } yield oldWidget.copy(
                          uiOptions = oldWidget.uiOptions.updated(UIKey.signature)(
                            newSignatureMappings.distinctBy(_.mapping)
                          )
                        )
                      }
                    case _: PdfMappingUpdateLog.IgnoredMapping => curMap
                  }
                }
              }
              _ <- updateIgnoredMismatchAsaData { ignoredMismatchAsaData =>
                val newIgnoredData = updateMappingLogs.collect { case PdfMappingUpdateLog.IgnoredMapping(fieldName) =>
                  IgnoredMismatchAsa(
                    newExtractedPdf.name,
                    fieldName
                  )
                }.toSet

                ignoredMismatchAsaData
                  .filterNot(_.fileName == oldFileName) ++ newIgnoredData
              }
            } yield ()
          }
          _ <- updatePdfCuts(
            _.view
              .mapValues { pdfCutInfo =>
                if (pdfCutInfo.fileName == oldFileName) {
                  pdfCutInfo.copy(fileName = newExtractedPdf.name, needReview = true)
                } else {
                  pdfCutInfo
                }
              }
              .toMap
          )
        } yield ()
      }
    } yield ()
  }

  def replaceEmbeddedPdf(
    oldFileId: FileId,
    newFileId: FileId,
    newFileName: String,
    pdfCutInfoOpt: Option[PdfCutInfo] = None
  ): FormBuilderZData[Unit] = {
    for {
      embeddedPdfs <- getEmbeddedPdf
      _ <- ZPure.foreachDiscard(embeddedPdfs.find(_._2 == oldFileId)) { case oldFileName -> _ =>
        for {
          _ <- updateEmbeddedPdf(_ - oldFileName + (newFileName -> newFileId))
          _ <- updateFormWidgetMap(
            _.view
              .mapValues { widget =>
                if (widget.widgetType == WidgetType.Pdf && widget.uiOptions.get(UIKey.embeddedPdf).contains(oldFileName)) {
                  widget.copy(uiOptions = widget.uiOptions.updated(UIKey.embeddedPdf)(newFileName))
                } else {
                  widget
                }
              }
              .toMap
          )
          _ <- pdfCutInfoOpt.fold(updatePdfCuts(_ - oldFileName)) { pdfCutInfo =>
            updatePdfCuts(_ + (newFileName -> pdfCutInfo))
          }
        } yield ()
      }
    } yield ()
  }

  def removeUploadedPdf(fileId: FileId): FormBuilderZData[Unit] = {
    for {
      uploadedPdfs <- getUploadedPdf
      _ <- ZPure.foreachDiscard(uploadedPdfs.get(fileId)) { extractedPdf =>
        for {
          _ <- updateUploadedPdf(_ - fileId)
          _ <- updateAnnotationMapping(_ -- FormDataConverters.fileIdTypeToFileId(fileId))
          _ <- updateFormWidgetMap(
            _.view
              .mapValues { widget =>
                if (
                  widget.widgetType == WidgetType.File && widget.uiOptions
                    .get(UIKey.fileName)
                    .contains(extractedPdf.name)
                ) {
                  widget.copy(uiOptions = widget.uiOptions.removed(UIKey.fileName))
                } else {
                  widget
                }
              }
              .toMap
          )
          _ <- updatePdfCuts(_.filter(_._2.fileName != extractedPdf.name))
        } yield ()
      }
    } yield ()
  }

  def removeEmbeddedPdf(fileId: FileId): FormBuilderZData[Unit] = {
    for {
      embeddedPdfs <- getEmbeddedPdf
      _ <- ZPure.foreachDiscard(embeddedPdfs.find(_._2 == fileId)) { case fileName -> _ =>
        for {
          _ <- updateEmbeddedPdf(_ - fileName)
          _ <- updateFormWidgetMap(
            _.view
              .mapValues { widget =>
                if (widget.widgetType == WidgetType.Pdf && widget.uiOptions.get(UIKey.embeddedPdf).contains(fileName)) {
                  widget.copy(uiOptions = widget.uiOptions.removed(UIKey.embeddedPdf))
                } else {
                  widget
                }
              }
              .toMap
          )
          _ <- updatePdfCuts(_ - fileName)
        } yield ()
      }
    } yield ()
  }

  def keepEmbeddedPdfsAsCurrent(fileNames: Set[String]): FormBuilderZData[Unit] = {
    updatePdfCuts(_.map { case fileName -> pdfCutInfo =>
      if (fileNames.contains(fileName)) {
        fileName -> pdfCutInfo.copy(needReview = false)
      } else {
        fileName -> pdfCutInfo
      }
    }).unit
  }

  def recutEmbeddedPdf(
    oldFileName: String,
    newFileId: FileId,
    newFileName: String,
    newPdfCutInfo: PdfCutInfo
  ): FormBuilderZData[Unit] = {
    for {
      _ <- updateEmbeddedPdf(_ - oldFileName + (newFileName -> newFileId))
      _ <- updateFormWidgetMap(
        _.view
          .mapValues { widget =>
            if (widget.widgetType == WidgetType.Pdf && widget.uiOptions.get(UIKey.embeddedPdf).contains(oldFileName)) {
              widget.copy(uiOptions = widget.uiOptions.updated(UIKey.embeddedPdf)(newFileName))
            } else {
              widget
            }
          }
          .toMap
      )
      _ <- updatePdfCuts(_ - oldFileName + (newFileName -> newPdfCutInfo))
    } yield ()
  }

  def recutMultipleEmbeddedPdfs(recutMap: Map[String, CutPdfOutput]): FormBuilderZData[Unit] = {
    ZPure.foreachDiscard(recutMap) { case oldFileName -> CutPdfOutput(newFileId, newFileName, newPdfCutInfo) =>
      recutEmbeddedPdf(
        oldFileName,
        newFileId,
        newFileName,
        newPdfCutInfo
      )
    }
  }

  def importFromAnnotation(
    resp: ImportFromAnnotationDocumentResponse,
    allKeysInRules: Set[String],
    getBuilderCommand: (Seq[Field], Map[String, Widget]) => FormBuilderZData[Unit]
  ): FormBuilderZData[Unit] = {
    for {
      keySet <- ZPure.succeed {
        resp.keySchemas.map(_._1).toSet ++ resp.keySchemas.flatMap { (_, schema) =>
          FormBuilderDataCommandUtils.findAllKeysSet(schema)
        }.toSet ++ resp.widgetMap.keySet ++ resp.cutInfoMap.keySet
      }
      existingKeys <- FormBuilderDataCommand.getAllKeys
      keyModifierMap = FormBuilderDataCommandUtils.getKeysModifier(existingKeys ++ allKeysInRules, keySet)
      sanitizedKeySchemas = resp.keySchemas.view.map { (key, schema) =>
        val sanitizedKay = keyModifierMap.getOrElse(key, key)
        val sanitizedSchema =
          FormBuilderDataCommandUtils.modifyKey(schema, key => keyModifierMap.getOrElse(key, key))
        Schema.obj.Field(sanitizedKay, sanitizedSchema)
      }.toSeq
      dataWithLogEvents = resp.widgetMap.map { case (key, widget) =>
        val data = FormBuilderDataCommandUtils.modifyKeyWidget(
          key,
          widget,
          key => keyModifierMap.getOrElse(key, key)
        )
        (data._1, data._2) -> data._3
      }.toSeq
      sanitizedWidgetMap = dataWithLogEvents.view.map(_._1).toMap
      sanitizedCutInfoMap = resp.cutInfoMap.map { (key, cutInfo) =>
        keyModifierMap.getOrElse(key, key) -> cutInfo
      }
      _ <- getBuilderCommand(sanitizedKeySchemas, sanitizedWidgetMap)
      _ <- ZPure.foreachDiscard(sanitizedCutInfoMap) { case (key, (sysFileId, fileName, pdfCutInfo)) =>
        FormBuilderDataCommand.cutEmbeddedPdfWidgetFromUploadedPdf(
          key,
          FormDataConverters.fileIdToForm(sysFileId),
          fileName,
          pdfCutInfo
        )
      }
    } yield ()
  }

  def updateBlueprintRef(updateFn: Option[BlueprintRef] => Option[BlueprintRef])
    : FormBuilderZData[Option[BlueprintRef]] = {
    updateFormMetadata { metadata =>
      metadata.copy(blueprintRef = updateFn(metadata.blueprintRef))
    }
      .map(_.blueprintRef)
      .log(FormBuilderDataCommandType.Update)
  }

  def updateIgnoredMismatchedBlueprintMetadata(
    updateFn: Set[String] => Set[String]
  ): FormBuilderZData[Set[String]] = {
    updateFormMetadata { metaData =>
      metaData.copy(
        ignoredMismatchedBlueprintMetadata = updateFn(metaData.ignoredMismatchedBlueprintMetadata)
      )
    }.map(_.ignoredMismatchedBlueprintMetadata)
  }

  def updateIgnoredNonImportedBlueprintMetadata(
    updateFn: Set[String] => Set[String]
  ): FormBuilderZData[Set[String]] = {
    updateFormMetadata { metaData =>
      metaData.copy(
        ignoredNonImportedBlueprintMetadata = updateFn(metaData.ignoredNonImportedBlueprintMetadata)
      )
    }.map(_.ignoredNonImportedBlueprintMetadata)
  }

  // delay the calculation until the form builder data handle it
  def delay[A](value: => A): FormBuilderZData[A] = {
    ZPure
      .suspend[
        FormBuilderDataCommandType,
        FormBuilderDataProps,
        FormBuilderDataProps,
        FormBuilderDataHelpers,
        String,
        A
      ](
        ZPure.succeed(value)
      )
  }

  def unit: FormBuilderZData[Unit] = {
    delay(())
  }

}
