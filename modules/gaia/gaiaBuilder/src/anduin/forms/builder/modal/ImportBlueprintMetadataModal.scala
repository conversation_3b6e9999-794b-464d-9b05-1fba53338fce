package anduin.forms.builder.modal

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.checkbox.CheckboxL
import design.anduin.components.collapse.Collapse
import design.anduin.components.collapse.laminar.CollapseL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL}
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.style.tw.*
import io.circe.Json
import io.circe.syntax.*
import io.laminext.syntax.core.*

import anduin.blueprint.*
import anduin.component.laminar.SearchBoxL
import anduin.forms.Form
import anduin.forms.builder.modal.AddSectionPopover.Radio2OptionsType
import anduin.forms.builder.modal.ImportBlueprintMetadataModal.{
  ItemType,
  convertBlueprintMetadataToFormData,
  highlightTextWithSegments,
  widgetTypeByAlias
}
import anduin.forms.builder.signaldata.FormBuilderDataCommand
import anduin.forms.builder.toc.BuilderTOCBlueprintMetadataSection.FormFieldData
import anduin.forms.builder.utils.BuilderUtils
import anduin.forms.model.Schema
import anduin.forms.model.Schema.obj.Field
import anduin.forms.ui.types.*
import anduin.forms.ui.types.SupportingFileGroupType.SupportingFileType
import anduin.forms.ui.{UIKey, UIOptions, Widget, WidgetType}
import anduin.forms.version.BlueprintRef
import anduin.forms.version.FormVersionMetadataModel.WidgetSource
import anduin.scalajs.pluralize.Pluralize
import anduin.utils.StringUtils.*

case class ImportBlueprintMetadataModal(
  formSignal: Signal[Form],
  blueprintRefOptSignal: Signal[Option[BlueprintRef]],
  blueprintMetadataSignal: Signal[BlueprintMetadata],
  keysInFormRulesSignal: Signal[Set[String]],
  selectedKeySignal: Signal[String],
  formBuilderDataCommandObserver: Observer[FormBuilderDataCommand],
  onClose: Observer[Unit],
  lastRoot: Option[String]
) {

  private val importEventBus = new EventBus[Unit]
  private val selectEventBus = new EventBus[(ItemType, String)]

  private val isEmptyMetadataSignal = blueprintMetadataSignal.map { metadata =>
    metadata.fileGroups.isEmpty &&
    metadata.paragraphs.isEmpty &&
    metadata.gatingQuestions.isEmpty &&
    metadata.signatures.isEmpty
  }

  private val selectedItemsVar = Var(List.empty[(ItemType, String)])

  private val totalItemCountSignal = selectedItemsVar.signal.map(_.size).distinct

  private val hasNoSelectedItemsSignal = selectedItemsVar.signal.map(_.isEmpty).distinct

  private val searchTextVar = Var("")

  private val matchingType = MatchType.PartialMatch

  private def createMetadataSignal[T](
    metadataSignal: Signal[Seq[T]],
    keyExtractor: T => String,
    labelExtractor: T => String
  ): Signal[Seq[(T, Option[MatchResult], Option[MatchResult])]] =
    metadataSignal.combineWith(searchTextVar.signal).distinct.map { (items, searchText) =>
      if (searchText.isEmpty) {
        items.map(item => (item, None, None))
      } else {
        items
          .flatMap { item =>
            val keyMatchResult = matchingType.computeMatchScore(keyExtractor(item), searchText)
            val labelMatchResult = matchingType.computeMatchScore(labelExtractor(item), searchText)
            if (keyMatchResult.isDefined || labelMatchResult.isDefined) {
              Some(
                (
                  item,
                  keyMatchResult,
                  labelMatchResult
                )
              )
            } else {
              None
            }
          }
          .sortBy {
            case (_, Some(keyMatch), Some(labelMatch)) =>
              (-Math.max(keyMatch.score.matchScore, labelMatch.score.matchScore), keyMatch.score.baseLength)
            case (_, Some(keyMatch), None) =>
              (-keyMatch.score.matchScore, keyMatch.score.baseLength)
            case (_, None, Some(labelMatch)) =>
              (-labelMatch.score.matchScore, labelMatch.score.baseLength)
            case _ =>
              (0.0, Int.MaxValue)
          }
      }
    }

  private val fileGroupsSignal: Signal[Seq[(FileGroup, Option[MatchResult], Option[MatchResult])]] =
    createMetadataSignal(
      blueprintMetadataSignal.map(_.fileGroups),
      keyExtractor = _.key,
      labelExtractor = _.name
    )

  private val paragraphsSignal: Signal[Seq[(ParagraphMessage, Option[MatchResult], Option[MatchResult])]] =
    createMetadataSignal(
      blueprintMetadataSignal.map(_.paragraphs),
      keyExtractor = _.key,
      labelExtractor = pr => BuilderUtils.getTextFromHtml(pr.label)
    )

  private val gatingQuestionsSignal: Signal[Seq[(GatingQuestion, Option[MatchResult], Option[MatchResult])]] =
    createMetadataSignal(
      blueprintMetadataSignal.map(_.gatingQuestions),
      keyExtractor = _.key,
      labelExtractor = question => BuilderUtils.getTextFromHtml(question.label)
    )

  private val signaturesSignal: Signal[Seq[(BlueprintSignature, Option[MatchResult], Option[MatchResult])]] =
    createMetadataSignal(
      blueprintMetadataSignal.map(_.signatures),
      keyExtractor = _.key,
      labelExtractor = _.signer
    )

  private val isEmptySearchResultSignal =
    fileGroupsSignal.map(_.isEmpty) &&
      paragraphsSignal.map(_.isEmpty) &&
      gatingQuestionsSignal.map(_.isEmpty) &&
      signaturesSignal.map(_.isEmpty)

  private val fileGroupCheckAllVar = Var(false)
  private val fileGroupIsIndeterminateVar = Var(false)
  private val paragraphCheckAllVar = Var(false)
  private val paragraphIsIndeterminateVar = Var(false)
  private val gatingQuestionCheckAllVar = Var(false)
  private val gatingQuestionIsIndeterminateVar = Var(false)
  private val signatureCheckAllVar = Var(false)
  private val signatureIsIndeterminateVar = Var(false)

  def apply(): HtmlElement = {
    div(
      ModalBodyL(
        div(
          tw.spaceY12,
          SearchBoxL(onSearch = searchTextVar.writer)(),
          div(
            tw.overflowYAuto,
            tw.borderAll.borderGray3.bgGray1,
            height.px := 512,
            child.maybe <-- isEmptyMetadataSignal.map { isEmpty =>
              Option.when(isEmpty) {
                NonIdealStateL(
                  icon = div(
                    tw.textGray5,
                    IconL(name = Val(Icon.Glyph.Error), size = Icon.Size.Px32)()
                  ),
                  title = "Metadata must be added to the blueprint before importing!"
                )()
              }
            },
            child.maybe <-- isEmptySearchResultSignal.map { isEmpty =>
              Option.when(isEmpty) {
                div(
                  tw.hPc100.wPc100.flex.flexCol.itemsCenter.justifyCenter,
                  tw.spaceY12,
                  tw.textGray5.selectNone,
                  "No item found"
                )
              }
            },
            child.maybe <-- fileGroupsSignal.map(_.nonEmpty).map { ne =>
              Option.when(ne) {
                renderMetadataSection(
                  ItemType.FileGroup,
                  "File Group",
                  fileGroupsSignal,
                  _.name,
                  _.key,
                  _.id,
                  fileGroupCheckAllVar.signal,
                  fileGroupIsIndeterminateVar.signal
                )
              }
            },
            child.maybe <-- paragraphsSignal.map(_.nonEmpty).map { ne =>
              Option.when(ne) {
                renderMetadataSection(
                  ItemType.ParagraphMessage,
                  "Blue/Orange well",
                  paragraphsSignal,
                  _.label,
                  _.key,
                  _.id,
                  paragraphCheckAllVar.signal,
                  paragraphIsIndeterminateVar.signal
                )
              }
            },
            child.maybe <-- gatingQuestionsSignal.map(_.nonEmpty).map { ne =>
              Option.when(ne) {
                renderMetadataSection(
                  ItemType.GatingQuestion,
                  "Gating question",
                  gatingQuestionsSignal,
                  _.label,
                  _.key,
                  _.id,
                  gatingQuestionCheckAllVar.signal,
                  gatingQuestionIsIndeterminateVar.signal
                )
              }
            },
            child.maybe <-- signaturesSignal.map(_.nonEmpty).map { ne =>
              Option.when(ne) {
                renderMetadataSection(
                  ItemType.BlueprintSignature,
                  "Signature",
                  signaturesSignal,
                  _.signer,
                  _.key,
                  _.id,
                  signatureCheckAllVar.signal,
                  signatureIsIndeterminateVar.signal
                )
              }
            }
          )
        )
      ),
      ModalFooterWCancelL(
        cancel = Observer { _ =>
          onClose.onNext(())
        }
      )(
        ButtonL(
          style = ButtonL.Style.Full(
            color = ButtonL.Color.Primary
          ),
          isDisabled = hasNoSelectedItemsSignal,
          onClick = importEventBus.writer.contramap(_ => ())
        )(
          div(
            span("Import"),
            child.maybe <-- totalItemCountSignal.map(num =>
              Option.when(num > 0)(
                span(" ", Pluralize("item", num, inclusive = true))
              )
            )
          )
        )
      ),
      selectEventBus.events --> Observer[(ItemType, String)] { case (itemType, id) =>
        selectedItemsVar.update { current =>
          val existingIndex = current.indexWhere { case (t, i) => t == itemType && i == id }
          if (existingIndex >= 0)
            current.patch(existingIndex, Nil, 1)
          else
            current :+ (itemType, id)
        }
      },
      importEventBus.events
        .withCurrentValueOf(
          formSignal,
          blueprintMetadataSignal,
          selectedItemsVar.signal,
          keysInFormRulesSignal,
          selectedKeySignal
        )
        .distinct --> formBuilderDataCommandObserver
        .contramap[(Form, BlueprintMetadata, List[(ItemType, String)], Set[String], String)] {
          (form, metadata, selectedItems, allKeysInRules, selectedKey) =>
            val selectedItems2FormData = convertBlueprintMetadataToFormData(selectedItems, metadata)
            onClose.onNext(())
            widgetTypeByAlias(selectedKey, form)
              .flatMap(AddSectionPopover.filterObjType)
              .fold {
                FormBuilderDataCommand.addSibling(
                  rawFields = selectedItems2FormData.map(_._1),
                  rawWidgetMap = selectedItems2FormData.flatMap(_._2).toMap,
                  prevKeyOpt = Option(selectedKey).filter(_.nonEmpty).orElse(lastRoot),
                  widgetSource = WidgetSource.BLUEPRINT_IMPORT,
                  allKeysInRules = allKeysInRules
                )
              } { _ =>
                FormBuilderDataCommand.addChild(
                  rawFields = selectedItems2FormData.map(_._1),
                  rawWidgetMap = selectedItems2FormData.flatMap(_._2).toMap,
                  parent = selectedKey,
                  widgetSource = WidgetSource.BLUEPRINT_IMPORT,
                  allKeysInRules = allKeysInRules
                )
              }
        },
      computeCheckAllState(
        fileGroupsSignal,
        selectedItemsVar.signal,
        ItemType.FileGroup,
        _.id,
        fileGroupCheckAllVar,
        fileGroupIsIndeterminateVar
      ),
      computeCheckAllState(
        paragraphsSignal,
        selectedItemsVar.signal,
        ItemType.ParagraphMessage,
        _.id,
        paragraphCheckAllVar,
        paragraphIsIndeterminateVar
      ),
      computeCheckAllState(
        gatingQuestionsSignal,
        selectedItemsVar.signal,
        ItemType.GatingQuestion,
        _.id,
        gatingQuestionCheckAllVar,
        gatingQuestionIsIndeterminateVar
      ),
      computeCheckAllState(
        signaturesSignal,
        selectedItemsVar.signal,
        ItemType.BlueprintSignature,
        _.id,
        signatureCheckAllVar,
        signatureIsIndeterminateVar
      )
    )
  }

  private def computeCheckAllState[T](
    itemsSignal: Signal[Seq[(T, Option[MatchResult], Option[MatchResult])]],
    selectedItemsSignal: Signal[List[(ItemType, String)]],
    itemType: ItemType,
    idExtractor: T => String,
    checkAllVar: Var[Boolean],
    isIndeterminateVar: Var[Boolean]
  ) = {
    itemsSignal
      .combineWith(selectedItemsSignal)
      .distinct --> Observer[(Seq[(T, Option[MatchResult], Option[MatchResult])], List[(ItemType, String)])] {
      (items, selectedItems) =>
        val itemIds = items.map { case (item, _, _) => idExtractor(item) }
        val allChecked = itemIds.forall(id => selectedItems.exists(item => item._1 == itemType && item._2 == id))
        val anyChecked = itemIds.exists(id => selectedItems.exists(item => item._1 == itemType && item._2 == id))
        checkAllVar.update(_ => allChecked)
        isIndeterminateVar.update(_ => anyChecked && !allChecked)
    }
  }

  private def renderMetadataSection[T](
    itemType: ItemType,
    title: String,
    itemsSignal: Signal[Seq[(T, Option[MatchResult], Option[MatchResult])]],
    getLabel: T => String,
    getKey: T => String,
    idExtractor: T => String,
    checkAllSignal: Signal[Boolean],
    isIndeterminateSignal: Signal[Boolean]
  ): HtmlElement = {
    val checkAllEventBus = new EventBus[Boolean]

    div(
      CollapseL(
        defaultIsOpened = true,
        renderTarget = render => {
          div(
            tw.flex.itemsCenter.selectNone,
            button(
              tw.hPx32.fontSemiBold,
              tw.flex.itemsCenter.ml4,
              onClick.mapTo(()) --> render.onToggle,
              div(
                tw.mr12,
                IconL(render.currentStatus match {
                  case Collapse.Status.Open  => Val(Icon.Glyph.ChevronDown)
                  case Collapse.Status.Close => Val(Icon.Glyph.ChevronRight)
                })()
              ),
              div(
                tw.mr4,
                IconL(Val(itemType.icon), size = Icon.Size.Px16)()
              ),
              s"$title"
            ),
            CheckboxL(
              isChecked = checkAllSignal,
              isIndeterminate = isIndeterminateSignal,
              onChange = checkAllEventBus.writer
            )().amend(tw.ml12)
          )
        },
        direction = Collapse.Direction.TopToBottom
      )(
        div(
          tw.pl16.pr12,
          children <-- itemsSignal.split { case (item, _, _) => idExtractor(item) } { case (id, _, itemSignal) =>
            val isCheckedSignal = selectedItemsVar.signal.map(_.exists(item => item._1 == itemType && item._2 == id))

            div(
              tw.flex.itemsCenter.cursorPointer.spaceX8.py2.selectNone.rounded4,
              isCheckedSignal.cls(tw.bgPrimary2.bgOpacity40),
              tw.hover(tw.bgPrimary1),
              div(
                minHeight.px := 36,
                tw.cursorPointer.selectNone
              ),
              CheckboxL(
                isChecked = isCheckedSignal,
                onChange = Observer.empty
              )(),
              div(
                tw.flexFill,
                TruncateL(
                  target = div(
                    tw.flexGrow,
                    children <-- itemSignal.map { case (item, _, labelMatch) =>
                      highlightTextWithSegments(
                        BuilderUtils.getTextFromHtml(getLabel(item)),
                        labelMatch.map(_.segments).getOrElse(Nil)
                      )
                    }
                  )
                )(),
                div(
                  tw.text11.leading16,
                  children <-- itemSignal.map { case (item, keyMatch, _) =>
                    highlightTextWithSegments(
                      getKey(item),
                      keyMatch.map(_.segments).getOrElse(Nil)
                    )
                  }
                )
              ),
              inContext(
                _.events(onClick.preventDefault.stopPropagation)
                  .mapTo((itemType, id)) --> Observer[(ItemType, String)] { (itemType, itemId) =>
                  selectEventBus.emit((itemType, itemId))
                }
              )
            )
          }
        )
      ),
      checkAllEventBus.events
        .withCurrentValueOf(itemsSignal)
        --> Observer[(Boolean, Seq[(T, Option[MatchResult], Option[MatchResult])])] { (isChecked, items) =>
          val itemIds = items.map { case (item, _, _) => idExtractor(item) }
          selectedItemsVar.update { current =>
            if (isChecked) {
              current ++ itemIds.map(id => (itemType, id)).filterNot(current.contains)
            } else {
              current.filterNot(item => item._1 == itemType && itemIds.contains(item._2))
            }
          }
        }
    )
  }

}

object ImportBlueprintMetadataModal {

  enum ItemType(val icon: Icon.Name) {
    case ParagraphMessage extends ItemType(icon = Icon.Glyph.Paragraph)
    case GatingQuestion extends ItemType(icon = Icon.Glyph.Questionnaire)
    case FileGroup extends ItemType(icon = Icon.Glyph.MultiSelect)
    case BlueprintSignature extends ItemType(icon = Icon.Glyph.Sign)
  }

  private[builder] def convertBlueprintMetadataToFormData(
    selectedItems: List[(ItemType, String)],
    metadata: BlueprintMetadata
  ): Seq[(Schema.obj.Field, Map[String, Widget])] = {
    selectedItems.flatMap {
      case (ItemType.ParagraphMessage, id) =>
        metadata.paragraphs.find(_.id == id).map(createParagraphData)
      case (ItemType.FileGroup, id) =>
        metadata.fileGroups.find(_.id == id).map(createFileGroupData)
      case (ItemType.GatingQuestion, id) =>
        metadata.gatingQuestions.find(_.id == id).map(createGatingQuestionData)
      case (ItemType.BlueprintSignature, id) =>
        metadata.signatures.find(_.id == id).map(createSignatureData)
      case _ => None
    }
  }

  private[builder] def createParagraphData(p: ParagraphMessage): (Schema.obj.Field, Map[String, Widget]) = {
    val uiType =
      if (p.style == ParagraphStyle.Blue) WellType.Info
      else if (p.style == ParagraphStyle.Orange) WellType.Warning
      else WellType.Default
    val field = Schema.obj.Field(p.key, Schema.`null`(title = Some(p.key.replace("_", " "))))
    val widget = Widget(
      WidgetType.Paragraph,
      UIOptions.empty
        .updated(UIKey.formattedText)(p.label)
        .updated(UIKey.wellType)(uiType)
        .updated(UIKey.blueprintMetadataMapping)(p.key)
        .updated(UIKey.marginBottom)(MarginBottomType.Px12)
    )
    (field, Map(p.key -> widget))
  }

  private[builder] def createFileGroupData(fg: FileGroup): (Schema.obj.Field, Map[String, Widget]) = {
    val field = Schema.obj.Field(
      fg.key,
      Schema.array(
        componentType = Schema.`enum`(
          Schema.string(),
          fg.fileItems.map(fi => Json.fromString(fi.key))
        ),
        title = Some(fg.key.replace("_", " "))
      )
    )
    val supportingFileGroup = SupportingFileGroupType(
      description = fg.name,
      helpText = fg.helpText,
      files = fg.fileItems.map(fi => fi.key -> SupportingFileType(fi.name, fi.helpText, fi.key)).toMap
    )
    val widget = Widget(
      WidgetType.FileGroup,
      UIOptions.fromMap(
        Map(UIKey.supportingFileGroup -> supportingFileGroup.asJson, UIKey.blueprintMetadataMapping -> fg.key.asJson)
      )
    )
    (field, Map(fg.key -> widget))
  }

  private[builder] def createGatingQuestionData(gq: GatingQuestion): (Schema.obj.Field, Map[String, Widget]) = {
    val widgetType = gq.inputType match {
      case FormUIInputType.Radio            => WidgetType.Radio
      case FormUIInputType.MultipleCheckbox => WidgetType.MultipleCheckbox
      case FormUIInputType.Dropdown         => WidgetType.Dropdown
      case FormUIInputType.MultipleSuggest  => WidgetType.MultipleSuggest
    }

    val title = Option(BuilderUtils.getTextFromHtml(gq.label))
      .filter(_.nonEmpty)
      .orElse(Option(gq.key.replace("_", " ")))

    val field = Schema.obj.Field(
      gq.key,
      if (gq.inputType == FormUIInputType.Radio || gq.inputType == FormUIInputType.Dropdown)
        Schema.`enum`(
          Schema.string(title = title),
          gq.options.map(opt => Json.fromString(opt.key))
        )
      else {
        Schema.array(
          componentType = Schema.`enum`(
            Schema.string(),
            gq.options.map(opt => Json.fromString(opt.key))
          ),
          title = title
        )
      }
    )

    val multipleOption = MultipleOptionType(
      options = gq.options
        .map(opt => opt.key -> SingleOptionType(formattedText = opt.label, blueprintMetadataMapping = opt.key))
        .toMap
    )

    val shouldUseInlineLayout = widgetType == WidgetType.Radio &&
      gq.options.size == 2 &&
      Radio2OptionsType.allTypes.exists { optionType =>
        gq.options.forall { opt =>
          optionType.regex.matches(opt.key) ||
          optionType.asaRegex.matches(opt.key) ||
          optionType.blueprintRegex.matches(opt.key) ||
          optionType.labels.contains(BuilderUtils.getTextFromHtml(opt.label).toLowerCase)
        }
      }
    val uiOptions = UIOptions
      .fromMap(
        Map(
          UIKey.formattedText -> gq.label.asJson,
          UIKey.multipleOption -> multipleOption.asJson,
          UIKey.blueprintMetadataMapping -> gq.key.asJson,
          UIKey.required -> Json.fromString("This field is required")
        )
      )
      .updated(UIKey.marginBottom)(MarginBottomType.Px12)

    val widget = Widget(
      widgetType,
      if (shouldUseInlineLayout) uiOptions.updated(UIKey.fixedLayout)(FixedLayoutType.Inline) else uiOptions
    )

    (field, Map(gq.key -> widget))
  }

  private[builder] def createSignatureData(sig: BlueprintSignature): (Schema.obj.Field, Map[String, Widget]) = {
    val field = Schema.obj.Field(sig.key, Schema.`null`(title = Some(getSignatureTitle(sig))))
    val widget = Widget(
      WidgetType.Signature,
      UIOptions.fromMap(
        Map(
          UIKey.signatureSigner -> sig.signer.asJson,
          UIKey.signatureType -> SignatureType.circeEncoder(sig.signatureType),
          UIKey.signature -> sig.mappings.asJson,
          UIKey.uniqueSignerEmail -> sig.uniqueSignerEmail.asJson,
          UIKey.blueprintMetadataMapping -> sig.key.asJson
        )
      )
    )
    (field, Map(sig.key -> widget))
  }

  private[builder] def getSignatureTitle(sig: BlueprintSignature): String = {
    val sigType = sig.signatureType match {
      case SignatureType.LpSignature => "LP"
      case SignatureType.GpSignature => "GP"
    }

    s"$sigType - ${sig.signer}"
  }

  private[builder] def widgetTypeByAlias(alias: String, form: Form): Option[WidgetType] = {
    form.defaultUiSchema.get(alias).map(_.widgetType)
  }

  private[builder] def highlightTextWithSegments(text: String, segments: Seq[MatchSegment]): Seq[HtmlElement] = {
    case class TextAccumulator(currentIndex: Int, elements: Seq[HtmlElement])

    val accumulated =
      segments.sortBy(_.start).foldLeft(TextAccumulator(0, Seq.empty)) {
        case (TextAccumulator(currentIndex, elements), segment) =>
          val prefix = Option
            .when(segment.start > currentIndex)(
              span(text.substring(currentIndex, segment.start))
            )
            .toSeq

          val highlighted = span(tw.fontSemiBold, text.substring(segment.start, segment.end))

          TextAccumulator(segment.end, elements ++ prefix :+ highlighted)
      }

    val suffix = Option
      .when(accumulated.currentIndex < text.length)(
        span(text.substring(accumulated.currentIndex))
      )
      .toSeq

    accumulated.elements ++ suffix
  }

  private[builder] def createBlueprintFormFieldData(
    extractedData: Option[(Field, Map[String, Widget])],
    bpMetadataMapping: String
  ) = {
    extractedData match {
      case Some((field, widgetMap)) =>
        FormFieldData(
          field,
          bpMetadataMapping,
          widgetMap.getOrElse(bpMetadataMapping, Widget.default)
        )
      case None =>
        FormFieldData(
          Field(bpMetadataMapping, Schema.string()),
          bpMetadataMapping,
          Widget.default
        )
    }
  }

}
