package anduin.forms.builder.checklist.section

import com.raquo.airstream.core.Observer
import com.raquo.laminar.api.L
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.monaco.MonacoDiffEditor
import design.anduin.components.monaco.languages.MonacoLanguage
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.tag.Tag
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.style.tw.*
import io.circe.syntax.*

import anduin.blueprint.*
import anduin.forms.builder.checklist.section.BlueprintMetadataMappingNonExistentSectionView.InvalidBlueprintMappingFields
import anduin.forms.builder.checklist.{
  BlueprintMetadataMismatchedMappingSection,
  BlueprintMetadataNonExistentMappingSection,
  BlueprintMetadataNonImportedSection,
  ChecklistSectionView,
  IgnorableItems
}
import anduin.forms.builder.toc.BuilderTOCBlueprintMetadataSection.{FormFieldData, createFormSchemaFromFormFieldData}
import anduin.forms.builder.toc.BuilderTOCHeader.HeaderMode
import anduin.forms.monaco.MonacoSettings
import anduin.forms.ui.types.SupportingFileGroupType.SupportingFileType
import anduin.forms.ui.types.{MultipleOptionType, SingleOptionType, SupportingFileGroupType, WellType}
import anduin.forms.ui.{UIKey, Widget, WidgetType}
import anduin.forms.version.BlueprintRef
import anduin.forms.widget.WidgetIcon
import anduin.forms.builder.checklist.section.BlueprintMetadataMappingNonExistentSectionView.MismatchedField
import anduin.forms.builder.signaldata.FormBuilderDataCommand

case class BlueprintMetadataMappingNonExistentSectionView(
  override protected val isExpandedSignal: Signal[Boolean],
  override protected val toggleExpandedObserver: Observer[Unit],
  onSelectKey: Observer[String],
  invalidBlueprintMetadataMappingFieldsSignal: Signal[InvalidBlueprintMappingFields]
) extends ChecklistSectionView(
      section = BlueprintMetadataNonExistentMappingSection,
      isExpandedSignal = isExpandedSignal,
      issueCountSignal = invalidBlueprintMetadataMappingFieldsSignal.map(_.nonExistentFields.size),
      toggleExpandedObserver = toggleExpandedObserver
    ) {

  override def renderSectionBody(): HtmlElement = {
    div(
      tw.flex.flexCol.mt8,
      div(
        tw.spaceY8,
        div(
          children <-- invalidBlueprintMetadataMappingFieldsSignal.map(_.nonExistentFields).split(identity) {
            case (field, _, _) =>
              ButtonL(
                style = ButtonL.Style.Minimal(isFullWidth = true, icon = Some(WidgetIcon(field._2.widgetType))),
                onClick = Observer { _ =>
                  onSelectKey.onNext(field._1)
                }
              )(
                div(
                  width := "calc(100% - 24px)",
                  tw.text11.fontNormal.leading16.textLeft,
                  tw.flex.justifyBetween.itemsCenter.group,
                  TruncateL(
                    target = div(field._1)
                  )()
                )
              )
          }
        )
      )
    )
  }

}

case class BlueprintMetadataMappingMismatchedSectionView(
  override protected val isExpandedSignal: Signal[Boolean],
  override protected val toggleExpandedObserver: Observer[Unit],
  onSelectKey: Observer[String],
  invalidBlueprintMetadataMappingFieldsSignal: Signal[InvalidBlueprintMappingFields],
  isEditModeSignal: Signal[Boolean],
  formBuilderDataCommandObserver: Observer[FormBuilderDataCommand],
  ignoredMismatchedBlueprintMetadataFieldsSignal: Signal[Set[String]]
) extends ChecklistSectionView(
      section = BlueprintMetadataMismatchedMappingSection,
      isExpandedSignal = isExpandedSignal,
      issueCountSignal = invalidBlueprintMetadataMappingFieldsSignal
        .map(_.mismatchedFields)
        .combineWith(ignoredMismatchedBlueprintMetadataFieldsSignal)
        .map { case (mismatchedFields, ignoreKeys) =>
          mismatchedFields.count(field => !ignoreKeys.contains(field.alias))
        },
      toggleExpandedObserver = toggleExpandedObserver
    ) {

  private def mismatchedItemSetup(misMatchedField: MismatchedField, actionButton: Node): HtmlElement = {
    lazy val blueprintFormFieldSchema = createFormSchemaFromFormFieldData(misMatchedField.blueprintFormField)
    lazy val formFieldSchema = createFormSchemaFromFormFieldData(misMatchedField.formField)
    div(
      tw.flex.itemsCenter,
      ButtonL(
        style = ButtonL.Style.Minimal(isFullWidth = true, icon = Some(WidgetIcon(misMatchedField.widget.widgetType))),
        onClick = Observer { _ =>
          onSelectKey.onNext(misMatchedField.alias)
        }
      )(
        div(
          width := "calc(100% - 24px)",
          tw.text11.fontNormal.leading16.textLeft,
          tw.flex.justifyBetween.itemsCenter.group,
          TruncateL(
            target = div(misMatchedField.alias)
          )(),
          div(
            tw.flexNone.flex.itemsCenter.invisible.ml4.groupHover(tw.visible),
            actionButton
          )
        )
      ).amend(tw.flexFill),
      div(
        PopoverL(
          renderTarget = (open, _) =>
            TagL(
              label = Val("View diff"),
              color = Val(Tag.Light.Warning),
              target = Tag.Target.Button(onClick = open)
            )(),
          renderContent = _ =>
            div(
              tw.p8,
              MonacoDiffEditor(
                workerPaths = MonacoSettings.monacoEditorWorkerPath,
                language = MonacoLanguage.Json,
                appearance = MonacoSettings.getDiffEditorAppearance(),
                onAppearanceChanged = Observer { config =>
                  MonacoSettings.setDiffEditorAppearance(config)
                },
                originalModel = MonacoDiffEditor.ContentModel(
                  blueprintFormFieldSchema.asJson.toString,
                  isReadOnly = true
                ),
                modifiedModel = MonacoDiffEditor.ContentModel(
                  formFieldSchema.asJson.toString,
                  isReadOnly = true
                ),
                fontSize = 12
              ).element.amend(height.px(400), width.px(920))
            ),
          position = PortalPosition.RightTop
        )()
      )
    )
  }

  override def renderSectionBody(): HtmlElement = {
    IgnorableItems[String, MismatchedField](
      itemsSignal = invalidBlueprintMetadataMappingFieldsSignal.map(_.mismatchedFields),
      ignoredSetSignal = ignoredMismatchedBlueprintMetadataFieldsSignal,
      getKey = _.alias,
      renderItem = mismatchedItemSetup,
      isEditModeSignal = isEditModeSignal,
      ignoreObserver = formBuilderDataCommandObserver.contramap { keys =>
        FormBuilderDataCommand.updateIgnoredMismatchedBlueprintMetadata(_ ++ keys)
      },
      unignoreObserver = formBuilderDataCommandObserver.contramap { keys =>
        FormBuilderDataCommand.updateIgnoredMismatchedBlueprintMetadata(_ -- keys)
      }
    )()
  }

}

case class BlueprintMetadataNonImportedSectionView(
  override protected val isExpandedSignal: Signal[Boolean],
  override protected val toggleExpandedObserver: Observer[Unit],
  invalidBlueprintMetadataMappingFieldsSignal: Signal[InvalidBlueprintMappingFields],
  onHeaderModeChange: Observer[HeaderMode],
  blueprintRefOptSignal: Signal[Option[BlueprintRef]],
  onClose: Observer[Unit],
  onChangeBpMetadataSearchText: Observer[String],
  isEditModeSignal: Signal[Boolean],
  formBuilderDataCommandObserver: Observer[FormBuilderDataCommand],
  ignoredNonImportedBlueprintMetadataFieldsSignal: Signal[Set[String]]
) extends ChecklistSectionView(
      section = BlueprintMetadataNonImportedSection,
      isExpandedSignal = isExpandedSignal,
      issueCountSignal = invalidBlueprintMetadataMappingFieldsSignal
        .map(_.nonImportedMetadata)
        .combineWith(ignoredNonImportedBlueprintMetadataFieldsSignal)
        .map { case (nonImportedFields, ignoreKeys) =>
          nonImportedFields.count((alias, _) => !ignoreKeys.contains(alias))
        },
      toggleExpandedObserver = toggleExpandedObserver
    ) {

  private def nonImportedItemSetup(field: (String, WidgetType), actionButton: Node): HtmlElement = {
    ButtonL(
      style = ButtonL.Style.Minimal(isFullWidth = true, icon = Some(WidgetIcon(field._2))),
      onClick = Observer { _ =>
        onClose.onNext(())
        onHeaderModeChange.onNext(HeaderMode.BlueprintMetadataMode)
        onChangeBpMetadataSearchText.onNext(field._1)
      },
      isDisabled = blueprintRefOptSignal.map(_.isEmpty)
    )(
      div(
        width := "calc(100% - 24px)",
        tw.text11.fontNormal.leading16.textLeft,
        tw.flex.justifyBetween.itemsCenter.group,
        TruncateL(
          target = div(field._1)
        )().amend(tw.ml8),
        div(
          tw.flexNone.flex.itemsCenter.invisible.ml4.groupHover(tw.visible),
          actionButton
        )
      )
    )
  }

  override def renderSectionBody(): HtmlElement = {
    IgnorableItems[String, (String, WidgetType)](
      itemsSignal = invalidBlueprintMetadataMappingFieldsSignal.map(_.nonImportedMetadata),
      ignoredSetSignal = ignoredNonImportedBlueprintMetadataFieldsSignal,
      getKey = _._1,
      renderItem = nonImportedItemSetup,
      isEditModeSignal = isEditModeSignal,
      ignoreObserver = formBuilderDataCommandObserver.contramap { keys =>
        FormBuilderDataCommand.updateIgnoredNonImportedBlueprintMetadata(_ ++ keys)
      },
      unignoreObserver = formBuilderDataCommandObserver.contramap { keys =>
        FormBuilderDataCommand.updateIgnoredNonImportedBlueprintMetadata(_ -- keys)
      }
    )()
  }

}

object BlueprintMetadataMappingNonExistentSectionView {

  case class InvalidBlueprintMappingFields(
    nonExistentFields: Seq[(String, Widget)],
    mismatchedFields: Seq[MismatchedField],
    nonImportedMetadata: Seq[(String, WidgetType)]
  )

  case class MismatchedField(
    alias: String,
    widget: Widget,
    formField: FormFieldData,
    blueprintFormField: FormFieldData
  )

  private[checklist] def isNonExistParagraphMapping(
    widget: Widget,
    paragraphMessages: Seq[ParagraphMessage]
  ): Boolean = {
    val blueprintMapping = widget.uiOptions.getOrElse(UIKey.blueprintMetadataMapping, "")
    paragraphMessages.find(_.key == blueprintMapping) match {
      case None => true
      case _    => false
    }
  }

  private[checklist] def isNonExistSignatureMapping(
    widget: Widget,
    signatures: Seq[BlueprintSignature]
  ): Boolean = {
    val blueprintMapping = widget.uiOptions.getOrElse(UIKey.blueprintMetadataMapping, "")
    signatures.find(_.key == blueprintMapping) match {
      case None => true
      case _    => false
    }
  }

  private[checklist] def isNonExistFileGroupMapping(
    widget: Widget,
    fileGroups: Seq[FileGroup]
  ): Boolean = {
    val blueprintMapping = widget.uiOptions.getOrElse(UIKey.blueprintMetadataMapping, "")
    fileGroups.find(_.key == blueprintMapping) match {
      case None => true
      case Some(fileGroup) =>
        val blueprintFileItemsKey = fileGroup.fileItems.map(_.key)
        val formFieldFileItemMapping = widget.uiOptions
          .get(UIKey.supportingFileGroup)
          .map(_.files.values.map(_.blueprintMetadataMapping).toSeq.filter(_.nonEmpty))
          .getOrElse(Seq.empty)

        formFieldFileItemMapping.exists(!blueprintFileItemsKey.contains(_))
    }
  }

  private[checklist] def isNonExistGatingQuestionMapping(
    widget: Widget,
    gatingQuestion: Seq[GatingQuestion]
  ): Boolean = {
    val blueprintMapping = widget.uiOptions.getOrElse(UIKey.blueprintMetadataMapping, "")
    gatingQuestion.find(_.key == blueprintMapping) match {
      case None => true
      case Some(question) =>
        val blueprintOptionItemsKey = question.options.map(_.key)
        val formFieldOptionItemMapping = widget.uiOptions
          .get(UIKey.multipleOption)
          .map(_.options.values.map(_.blueprintMetadataMapping).toSeq.filter(_.nonEmpty))
          .getOrElse(Seq.empty)

        formFieldOptionItemMapping.exists(!blueprintOptionItemsKey.contains(_))
    }
  }

  private[builder] def isMatchingParagraphMapping(
    widget: Widget,
    paragraphMessages: Seq[ParagraphMessage]
  ): Boolean = {
    val blueprintMapping = widget.uiOptions.getOrElse(UIKey.blueprintMetadataMapping, "")
    paragraphMessages.find(_.key == blueprintMapping).exists { message =>
      val formFieldFormattedText = widget.uiOptions.getOrElse(UIKey.formattedText, "")
      val blueprintFormattedText = message.label

      val expectedUIType = message.style match {
        case ParagraphStyle.Blue   => WellType.Info
        case ParagraphStyle.Orange => WellType.Warning
        case _                     => WellType.Default
      }
      val formFieldUITypeOpt = widget.uiOptions.get(UIKey.wellType)

      formFieldFormattedText == blueprintFormattedText &&
      formFieldUITypeOpt.contains(expectedUIType)
    }
  }

  private[builder] def isMatchingSignatureMapping(
    widget: Widget,
    signatures: Seq[BlueprintSignature]
  ): Boolean = {
    val blueprintMapping = widget.uiOptions.getOrElse(UIKey.blueprintMetadataMapping, "")
    signatures.find(_.key == blueprintMapping).exists { signature =>
      widget.uiOptions.get(UIKey.signatureSigner).contains(signature.signer) &&
      widget.uiOptions.get(UIKey.signatureType).contains(signature.signatureType) &&
      widget.uiOptions.get(UIKey.signature).contains(signature.mappings) &&
      widget.uiOptions.get(UIKey.uniqueSignerEmail).contains(signature.uniqueSignerEmail)
    }
  }

  private[builder] def isMatchingFileGroupMapping(
    widget: Widget,
    fileGroups: Seq[FileGroup]
  ): Boolean = {
    val blueprintMapping = widget.uiOptions.getOrElse(UIKey.blueprintMetadataMapping, "")
    fileGroups.find(_.key == blueprintMapping).exists { fileGroup =>
      val blueprintFileGroup = SupportingFileGroupType(
        description = fileGroup.name,
        helpText = fileGroup.helpText,
        files = fileGroup.fileItems.map(fi => fi.key -> SupportingFileType(fi.name, fi.helpText, fi.key)).toMap
      )

      val formFieldFileGroupOpt = widget.uiOptions.get(UIKey.supportingFileGroup).collect {
        case fileGroup: SupportingFileGroupType => fileGroup
      }
      val formFieldGroupNameOpt = formFieldFileGroupOpt.map(_.description)

      formFieldGroupNameOpt.contains(fileGroup.name) && formFieldFileGroupOpt.exists { fileGroup =>
        if (fileGroup.files.size != blueprintFileGroup.files.size) {
          false
        } else {
          blueprintFileGroup.files.values.forall { blueprintOpt =>
            fileGroup.files.values.find(
              _.blueprintMetadataMapping == blueprintOpt.blueprintMetadataMapping
            ) match {
              case Some(formFieldOpt) =>
                formFieldOpt.description == blueprintOpt.description && formFieldOpt.helpText == blueprintOpt.helpText
              case None =>
                false
            }
          }
        }
      }
    }
  }

  private[builder] def isMatchingGatingQuestionMapping(
    widget: Widget,
    gatingQuestions: Seq[GatingQuestion]
  ): Boolean = {
    val blueprintMapping = widget.uiOptions.getOrElse(UIKey.blueprintMetadataMapping, "")
    gatingQuestions.find(_.key == blueprintMapping).exists { question =>
      val formFieldFormattedText = widget.uiOptions.getOrElse(UIKey.formattedText, "")
      val blueprintFormattedText = question.label

      val formFieldOptionOpt = widget.uiOptions.get(UIKey.multipleOption).collect {
        case multipleOption: MultipleOptionType => multipleOption
      }

      val blueprintOption = MultipleOptionType(
        options = question.options
          .map(opt => opt.key -> SingleOptionType(formattedText = opt.label, blueprintMetadataMapping = opt.key))
          .toMap
      )

      // Compare formatted text and options
      formFieldFormattedText == blueprintFormattedText && formFieldOptionOpt.exists { formFieldOption =>
        if (formFieldOption.options.size != blueprintOption.options.size) {
          false
        } else {
          blueprintOption.options.values.forall { blueprintOpt =>
            formFieldOption.options.values.find(
              _.blueprintMetadataMapping == blueprintOpt.blueprintMetadataMapping
            ) match {
              case Some(formFieldOpt) =>
                formFieldOpt.formattedText == blueprintOpt.formattedText
              case None =>
                false
            }
          }
        }
      }
    }
  }

}
