// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.builder.checklist

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*

import anduin.blueprint.{BlueprintMetadata, FormUIInputType}
import anduin.forms.builder.checklist.FormBuilderChecklistModal.*
import anduin.forms.builder.checklist.section.*
import anduin.forms.builder.checklist.section.BlueprintMetadataMappingNonExistentSectionView.InvalidBlueprintMappingFields
import anduin.forms.builder.navigation.SelectKeyEventData
import anduin.forms.builder.signaldata.FormBuilderDataCommand
import anduin.forms.builder.toc.BuilderTOCBlueprintMetadataSection.FormFieldData
import anduin.forms.builder.toc.BuilderTOCHeader.HeaderMode
import anduin.forms.builder.utils.BuilderUtils
import anduin.forms.logic.extractor.ExtractorResult
import anduin.forms.model.Schema.obj.Field
import anduin.forms.model.annotation.AnnotationDocumentModels.AnnotationDocumentData
import anduin.forms.rules.FormRule
import anduin.forms.ui.types.FileId
import anduin.forms.ui.{UIKey, Widget, WidgetType}
import anduin.forms.utils.StandardAliasUtils
import anduin.forms.version.FormVersionMetadataModel.PdfFieldInfo
import anduin.forms.version.FormVersionModel.FormType
import anduin.forms.version.{BlueprintRef, FormRuleWarning}
import anduin.forms.{ExtractedPdf, Form, FormSchema}
import anduin.gaia.engine.utils.GaiaRuntimeLog
import anduin.id.form.FormId
import anduin.forms.builder.checklist.section.BlueprintMetadataMappingNonExistentSectionView.MismatchedField
import anduin.forms.builder.modal.ImportBlueprintMetadataModal

final case class FormBuilderChecklistModal(
  formIdOptSignal: Signal[Option[FormId]],
  ignoredInvalidRequireSetupsSignal: Signal[Set[String]],
  ignoredInvalidPdfNonInputMappingFieldsSignal: Signal[Set[String]],
  ignoredUnmappedPdfNonInputFieldsSignal: Signal[Set[PdfFieldInfo]],
  formRuleExtractResultSignal: Signal[List[(Int, FormRule, Either[String, ExtractorResult])]],
  formSignal: Signal[Form],
  formTypeSignal: Signal[FormType],
  formStandardAliasMappingSignal: Signal[Map[String, String]],
  extractedPdfsSignal: Signal[Map[FileId, ExtractedPdf]],
  uploadedAnnotationDataSignal: Signal[Map[FileId, AnnotationDocumentData]],
  embeddedPdfsSignal: Signal[Seq[String]],
  isEditModeSignal: Signal[Boolean],
  formRuleWarningsSignal: Signal[Seq[FormRuleWarning]],
  selectedKeySignal: Signal[String],
  close: Observer[Unit],
  onRuleChanged: Observer[(FormRule, Int, String)],
  formBuilderDataCommandObserver: Observer[FormBuilderDataCommand],
  onSelectKey: Observer[String],
  onSelectKeyWithPDF: Observer[SelectKeyEventData],
  onSwitchToDocumentTab: Observer[Unit],
  sideViewerOpenObserver: Observer[Unit],
  expandedSectionsSignal: Signal[Set[ChecklistSection]],
  toggleChecklistSectionObserver: Observer[ChecklistSection],
  expandedModalSignal: Signal[Boolean],
  expandedModalObserver: Observer[Boolean],
  gaiaRuntimeLogSignal: Signal[List[GaiaRuntimeLog]],
  blueprintMetadataSignal: Signal[BlueprintMetadata],
  onHeaderModeChange: Observer[HeaderMode],
  blueprintRefOptSignal: Signal[Option[BlueprintRef]],
  onChangeBpMetadataSearchText: Observer[String],
  ignoredMismatchedBlueprintMetadataFieldsSignal: Signal[Set[String]],
  ignoredNonImportedBlueprintMetadataFieldsSignal: Signal[Set[String]]
) {

  private val formSchemaSignal = formSignal.map(_.defaultSchema)

  private val extractedPdfsValueSignal = extractedPdfsSignal.map(_.values.toSeq)

  private val fundSetupAsaMappingsSignal = formStandardAliasMappingSignal.map(_.filter { case (_, value) =>
    StandardAliasUtils.StandardAliasesForFundSubSetup.contains(value)
  })

  private val invalidRulesSignal =
    formSchemaSignal.combineWith(formRuleExtractResultSignal).mapN(ChecklistUtils.getInvalidRules)

  private val initializingErrorSignal = gaiaRuntimeLogSignal
    .combineWith(formRuleExtractResultSignal)
    .mapN(ChecklistUtils.getInitializingErrorRules)

  private val allInvalidRulesSignal =
    invalidRulesSignal.combineWith(initializingErrorSignal).distinct.map { case (invalidRules, initialiseErrorRule) =>
      val listSyntaxErrors: Seq[(FormRule, Int, RuleErrorType)] =
        invalidRules.map((rule, idx) => (rule, idx, RuleErrorType.InvalidSyntax))
      val listInitialiseErrors: Seq[(FormRule, Int, RuleErrorType)] =
        initialiseErrorRule.map((rule, idx) => (rule, idx, RuleErrorType.InitializingError))

      listSyntaxErrors ++ listInitialiseErrors
    }

  private val logicOutputPropertyConflictsSignal =
    formRuleExtractResultSignal.map(ChecklistUtils.getLogicOutputPropertyConflicts)

  private val logicOutputConflictsSignal = formRuleExtractResultSignal.map(ChecklistUtils.getLogicOutputConflicts)

  private val invalidPdfMappingFieldsSignal = formSchemaSignal
    .combineWith(extractedPdfsSignal, uploadedAnnotationDataSignal)
    .map(ChecklistUtils.getInvalidPdfMappingFields(_, _, _, ChecklistUtils.InputAndSignatureWidgets))

  private val missingFileFieldsSignal = formSchemaSignal
    .combineWith(extractedPdfsValueSignal, embeddedPdfsSignal)
    .mapN(ChecklistUtils.getMissingFileFields)

  private val unmappedPdfFieldsSignal = formSchemaSignal
    .combineWith(extractedPdfsSignal.map(_.toSeq))
    .map(ChecklistUtils.getUnmappedPdfFields(_, _, ChecklistUtils.InputAndSignatureWidgets))

  private val unmappedFormFieldsSignal =
    formSchemaSignal.combineWith(formTypeSignal).map { case (formSchema, formType) =>
      formType match {
        case FormType.Validation => ChecklistUtils.getUnmappedInputFields(formSchema)
        case _                   => Seq.empty
      }
    }

  private val oneFormFieldMappedToManyPdfFieldsSignal =
    formSchemaSignal.combineWith(formTypeSignal).map { case (formSchema, formType) =>
      formType match {
        case FormType.Validation => ChecklistUtils.getOneFormFieldMappedToManyPdfFields(formSchema)
        case _                   => Seq.empty
      }
    }

  private val manyFormFieldMappedToOnePdfFieldsSignal =
    formSchemaSignal.combineWith(extractedPdfsSignal, formTypeSignal).map { case (formSchema, pdfs, formType) =>
      formType match {
        case FormType.Validation => ChecklistUtils.getManyFormFieldMappedToOnePdfField(formSchema, pdfs.toSeq)
        case _                   => Seq.empty
      }
    }

  private val duplicateFileNamesSignal =
    extractedPdfsValueSignal.combineWith(embeddedPdfsSignal).mapN(ChecklistUtils.getDuplicateFileNames)

  private val invalidPdfFieldsSignal = extractedPdfsSignal.map(ChecklistUtils.getInvalidPdfFields)

  private val invalidRepeatableFieldsSignal =
    formSchemaSignal.combineWith(extractedPdfsValueSignal).mapN(ChecklistUtils.getInvalidRepeatableFields)

  private val unmatchedPdfMappingTypeFieldsSignal =
    formSchemaSignal.combineWith(extractedPdfsValueSignal).mapN(ChecklistUtils.getUnmatchedPdfMappingTypeFields.apply)

  // TODO: @hoangdinh to refactor not using flowticks
  // private val cyclicFieldBelows = ChecklistUtils.getCyclicFieldBelows(formSchema)

  private val invalidPageFieldsSignal = formSchemaSignal.map(ChecklistUtils.getInvalidPageFields)

  private val duplicatedFsAsaMappingsSignal =
    formSchemaSignal.combineWith(fundSetupAsaMappingsSignal).map(ChecklistUtils.getDuplicatedAsaFields)

  private val emptySignerSignaturesSignal = formSchemaSignal.map(ChecklistUtils.getEmptySignerSignatures)

  private val invalidPdfNonInputMappingFieldsSignal =
    formSchemaSignal.combineWith(extractedPdfsSignal, uploadedAnnotationDataSignal).map {
      ChecklistUtils.getInvalidPdfMappingFields(_, _, _, ChecklistUtils.NonInputWidgetsWithPdfMappings)
    }

  private val unmappedPdfNonInputFieldsSignal = formSchemaSignal
    .combineWith(extractedPdfsSignal.map(_.toSeq))
    .map(
      ChecklistUtils.getUnmappedPdfFields(
        _,
        _,
        ChecklistUtils.NonInputWidgetsWithPdfMappings
      )
    )

  private val invalidCustomisedSignaturesSignal =
    formSchemaSignal.combineWith(extractedPdfsValueSignal).mapN(ChecklistUtils.checkUniqueCustomisedSignatures)

  private val incorrectRequireFieldsSignal = formSignal
    .map(_.defaultSchema)
    .map(ChecklistUtils.getIncorrectRequireFieldSetups)

  private lazy val invalidBlueprintMetadataMappingFieldsSignal: Signal[InvalidBlueprintMappingFields] =
    formSchemaSignal.combineWith(blueprintMetadataSignal).map { case (formSchema, blueprintMetadata) =>
      val aliasesWidget = ChecklistUtils.getWidgetsWithBlueprintMetadataMapping(formSchema)
      val allFields = BuilderUtils.getAllFields(formSchema.schema)
      val formFieldData =
        aliasesWidget.flatMap { case (alias, widget) =>
          allFields.get(alias).map(fieldSchema => FormFieldData(Field(alias, fieldSchema), alias, widget))
        }

      val (mismatchedFields, nonExistentFields) =
        formFieldData.foldLeft((Vector.empty[MismatchedField], Vector.empty[(String, Widget)])) {
          case ((mismatchedAcc, nonExistentAcc), formField) =>
            val alias = formField.alias
            val widget = formField.widget
            val metadataMapping = widget.uiOptions.getOrElse(UIKey.blueprintMetadataMapping, "")

            def appendMismatched(
              blueprintFormField: FormFieldData
            ): (Vector[MismatchedField], Vector[(String, Widget)]) = {
              val mismatch = MismatchedField(alias, widget, formField, blueprintFormField)
              (mismatchedAcc :+ mismatch, nonExistentAcc)
            }

            widget.widgetType match {
              case WidgetType.Paragraph =>
                if (
                  BlueprintMetadataMappingNonExistentSectionView.isNonExistParagraphMapping(
                    widget,
                    blueprintMetadata.paragraphs
                  )
                ) {
                  (mismatchedAcc, nonExistentAcc :+ (alias -> widget))
                } else if (
                  !BlueprintMetadataMappingNonExistentSectionView.isMatchingParagraphMapping(
                    widget,
                    blueprintMetadata.paragraphs
                  )
                ) {
                  val blueprintFieldOpt = blueprintMetadata.paragraphs
                    .find(_.key == metadataMapping)
                    .map(ImportBlueprintMetadataModal.createParagraphData)
                  appendMismatched(
                    ImportBlueprintMetadataModal.createBlueprintFormFieldData(blueprintFieldOpt, metadataMapping)
                  )
                } else {
                  (mismatchedAcc, nonExistentAcc)
                }

              case WidgetType.Signature =>
                if (
                  BlueprintMetadataMappingNonExistentSectionView.isNonExistSignatureMapping(
                    widget,
                    blueprintMetadata.signatures
                  )
                ) {
                  (mismatchedAcc, nonExistentAcc :+ (alias -> widget))
                } else if (
                  !BlueprintMetadataMappingNonExistentSectionView.isMatchingSignatureMapping(
                    widget,
                    blueprintMetadata.signatures
                  )
                ) {
                  val blueprintFieldOpt = blueprintMetadata.signatures
                    .find(_.key == metadataMapping)
                    .map(ImportBlueprintMetadataModal.createSignatureData)
                  appendMismatched(
                    ImportBlueprintMetadataModal.createBlueprintFormFieldData(blueprintFieldOpt, metadataMapping)
                  )
                } else {
                  (mismatchedAcc, nonExistentAcc)
                }

              case WidgetType.FileGroup =>
                if (
                  BlueprintMetadataMappingNonExistentSectionView.isNonExistFileGroupMapping(
                    widget,
                    blueprintMetadata.fileGroups
                  )
                ) {
                  (mismatchedAcc, nonExistentAcc :+ (alias -> widget))
                } else if (
                  !BlueprintMetadataMappingNonExistentSectionView.isMatchingFileGroupMapping(
                    widget,
                    blueprintMetadata.fileGroups
                  )
                ) {
                  val blueprintFieldOpt = blueprintMetadata.fileGroups
                    .find(_.key == metadataMapping)
                    .map(ImportBlueprintMetadataModal.createFileGroupData)
                  appendMismatched(
                    ImportBlueprintMetadataModal.createBlueprintFormFieldData(blueprintFieldOpt, metadataMapping)
                  )
                } else {
                  (mismatchedAcc, nonExistentAcc)
                }

              case WidgetType.Radio | WidgetType.Dropdown | WidgetType.MultipleSuggest | WidgetType.MultipleCheckbox =>
                if (
                  BlueprintMetadataMappingNonExistentSectionView.isNonExistGatingQuestionMapping(
                    widget,
                    blueprintMetadata.gatingQuestions
                  )
                ) {
                  (mismatchedAcc, nonExistentAcc :+ (alias -> widget))
                } else if (
                  !BlueprintMetadataMappingNonExistentSectionView.isMatchingGatingQuestionMapping(
                    widget,
                    blueprintMetadata.gatingQuestions
                  )
                ) {
                  val blueprintFieldOpt = blueprintMetadata.gatingQuestions
                    .find(_.key == metadataMapping)
                    .map(ImportBlueprintMetadataModal.createGatingQuestionData)
                  appendMismatched(
                    ImportBlueprintMetadataModal.createBlueprintFormFieldData(blueprintFieldOpt, metadataMapping)
                  )
                } else {
                  (mismatchedAcc, nonExistentAcc)
                }

              case _ =>
                (mismatchedAcc, nonExistentAcc)
            }
        }

      val mappedKeys: Set[String] =
        aliasesWidget.flatMap { case (_, widget) =>
          widget.uiOptions.get(UIKey.blueprintMetadataMapping)
        }.toSet

      val nonImportedMetadata: Seq[(String, WidgetType)] =
        (blueprintMetadata.paragraphs.map(p => (p.key, WidgetType.Paragraph)) ++
          blueprintMetadata.signatures.map(s => (s.key, WidgetType.Signature)) ++
          blueprintMetadata.fileGroups.map(f => (f.key, WidgetType.FileGroup)) ++
          blueprintMetadata.gatingQuestions
            .map { q =>
              val widgetType = q.inputType match {
                case FormUIInputType.Dropdown         => WidgetType.Dropdown
                case FormUIInputType.MultipleSuggest  => WidgetType.MultipleSuggest
                case FormUIInputType.MultipleCheckbox => WidgetType.MultipleCheckbox
                case FormUIInputType.Radio            => WidgetType.Radio
              }
              (q.key, widgetType)
            })
          .filterNot { case (key, _) => mappedKeys.contains(key) }

      InvalidBlueprintMappingFields(
        nonExistentFields,
        mismatchedFields,
        nonImportedMetadata
      )
    }

  def apply(): Node = {
    div(
      tw.hPc100,
      div(
        tw.flex.flexCol.hPc100.justifyBetween,
        width <-- expandedModalSignal.map {
          if (_) { "720px" }
          else { "360px" }
        },
        header,
        body,
        footer
      )
    )
  }

  private def header: Node = {
    div(
      tw.px20.py16.borderBottom.borderGray3.flexNone.flex.justifyBetween,
      div(tw.text20.leading32.fontSemiBold, "Validation"),
      div(
        tw.flex,
        child <-- expandedModalSignal.map { expandedModal =>
          val expandButtonIcon = if (expandedModal) Icon.Glyph.ChevronDoubleLeft else Icon.Glyph.ChevronDoubleRight
          div(
            tw.mr4,
            TooltipL(
              renderTarget = ButtonL(
                style = ButtonL.Style.Minimal(icon = Some(expandButtonIcon)),
                onClick = expandedModalObserver.contramap(_ => !expandedModal)
              )(),
              renderContent = _.amend(if (expandedModal) "Collapse" else "Expand"),
              position = PortalPosition.BottomCenter
            )()
          )
        },
        ButtonL(
          style = ButtonL.Style.Minimal(icon = Some(Icon.Glyph.Cross)),
          onClick = close.contramap(_ => ())
        )()
      )
    )
  }

  private def body: HtmlElement = {
    val allInvalidRequireFieldSetupResolvedSignal = incorrectRequireFieldsSignal
      .combineWith(ignoredInvalidRequireSetupsSignal)
      .map { case (fields, ignoredItems) =>
        fields
          .map(_._1)
          .forall(ignoredItems.contains)
      }
      .distinct

    val allInvalidPdfNonInputMappingFieldsResolvedSignal = ignoredInvalidPdfNonInputMappingFieldsSignal
      .combineWith(invalidPdfNonInputMappingFieldsSignal)
      .mapN { (ignoredSet, invalidPdfNonInputMappings) =>
        invalidPdfNonInputMappings
          .map(_._1)
          .forall(ignoredSet.contains)
      }
      .distinct

    val allUnmappedPdfNonInputFieldsResolvedSignal = ignoredUnmappedPdfNonInputFieldsSignal
      .combineWith(unmappedPdfNonInputFieldsSignal)
      .mapN { (ignoredSet, unmappedPdfNonInputFields) =>
        unmappedPdfNonInputFields
          .map { case (field, fileName, _) => PdfFieldInfo(fileName, field.key) }
          .forall(ignoredSet.contains)
      }
      .distinct

    val hasNoInvalidRulesSignal =
      allInvalidRulesSignal.map(_.isEmpty).distinct

    val hasNoFormRuleWarningsSignal = formRuleWarningsSignal.map(_.forall(_.resolved)).distinct
    val hasNoInvalidPdfMappingFieldsSignal = invalidPdfMappingFieldsSignal.map(_.isEmpty).distinct
    val hasNoLogicConflictsSignal =
      (logicOutputPropertyConflictsSignal.map(_.forall(!_.warning))
        && logicOutputConflictsSignal.map(_.isEmpty)).distinct
    val hasNoMissingFileFieldsSignal = missingFileFieldsSignal.map(_.isEmpty).distinct
    val hasNoUnmappedPdfFieldsSignal = unmappedPdfFieldsSignal.map(_.isEmpty).distinct
    val hasNoDuplicateFileNamesSignal = duplicateFileNamesSignal.map(_.isEmpty).distinct
    val hasNoInvalidPdfFieldsSignal = invalidPdfFieldsSignal.map(_.map(_._2.alertCount).sum == 0).distinct
    val hasNoInvalidRepeatableFieldsSignal = invalidRepeatableFieldsSignal.map(_.isEmpty).distinct
    val hasNoUnmatchedPdfMappingTypeFieldsSignal = unmatchedPdfMappingTypeFieldsSignal.map(_.isEmpty).distinct
    val hasNoInvalidPageFieldsSignal = invalidPageFieldsSignal.map(_.isEmpty).distinct
    val hasNoDuplicatedFsAsaMappingsSignal = duplicatedFsAsaMappingsSignal.map(_.isEmpty).distinct
    val hasNoEmptySignerSignaturesSignal = emptySignerSignaturesSignal.map(_.isEmpty).distinct
    val hasNoInvalidCustomisedSignaturesSignal = invalidCustomisedSignaturesSignal.map(_.isEmpty).distinct
    val hasNoUnmappedFormFieldsSignal = unmappedFormFieldsSignal.map(_.isEmpty).distinct
    val hasNoOneFormFieldMappedToManyPdfFieldsSignal = oneFormFieldMappedToManyPdfFieldsSignal.map(_.isEmpty).distinct
    val hasNoManyFormFieldMappedToOnePdfFieldsSignal = manyFormFieldMappedToOnePdfFieldsSignal.map(_.isEmpty).distinct
    val hasNonExistentFieldsBlueprintMappingSignal =
      invalidBlueprintMetadataMappingFieldsSignal.map(_.nonExistentFields.isEmpty).distinct

    val hasMismatchedFieldsBlueprintMappingSignal = invalidBlueprintMetadataMappingFieldsSignal
      .map(_.mismatchedFields)
      .combineWith(ignoredMismatchedBlueprintMetadataFieldsSignal)
      .map { case (fields, ignoredItems) =>
        fields.map(_.alias).forall(ignoredItems.contains)
      }
      .distinct
    val hasNonImportedBlueprintMetadataSignal = invalidBlueprintMetadataMappingFieldsSignal
      .map(_.nonImportedMetadata)
      .combineWith(ignoredNonImportedBlueprintMetadataFieldsSignal)
      .map { case (fields, ignoredItems) =>
        fields.map(_._1).forall(ignoredItems.contains)
      }
      .distinct

    val sectionStatusSignals: Seq[Signal[(ChecklistSection, Boolean)]] = Seq(
      hasNoInvalidRulesSignal.map(InvalidRuleSection -> _),
      hasNoLogicConflictsSignal.map(ConflictRuleSection -> _),
      hasNoInvalidPdfMappingFieldsSignal.map(InvalidPdfMappingSection -> _),
      hasNoDuplicateFileNamesSignal.map(DuplicateFileNameSection -> _),
      hasNoMissingFileFieldsSignal.map(MissingFileSection -> _),
      hasNoUnmappedPdfFieldsSignal.map(UnmappedPdfFieldSection -> _),
      hasNoInvalidPdfFieldsSignal.map(InvalidPdfFieldsSection -> _),
      hasNoInvalidRepeatableFieldsSignal.map(InvalidRepeatableSection -> _),
      hasNoUnmatchedPdfMappingTypeFieldsSignal.map(UnmatchedPdfMappingTypeSection -> _),
      allInvalidRequireFieldSetupResolvedSignal.map(IncorrectRequireFieldSection -> _),
      hasNoInvalidPageFieldsSignal.map(InvalidPageFieldsSection -> _),
      hasNoDuplicatedFsAsaMappingsSignal.map(DuplicatedFundSetupAsaSection -> _),
      hasNoEmptySignerSignaturesSignal.map(EmptySignerSignatureSection -> _),
      hasNoFormRuleWarningsSignal.map(TransferredRulesWarningSection -> _),
      allInvalidPdfNonInputMappingFieldsResolvedSignal.map(InvalidPdfNonInputMappingsSection -> _),
      allUnmappedPdfNonInputFieldsResolvedSignal.map(UnmappedPdfNonInputFieldsSection -> _),
      hasNoInvalidCustomisedSignaturesSignal.map(InvalidCustomisedSignatureSection -> _),
      hasNoUnmappedFormFieldsSignal.map(FormFieldUnmappedSection -> _),
      hasNoOneFormFieldMappedToManyPdfFieldsSignal.map(OneToManyFormToPdfFieldMapping -> _),
      hasNoManyFormFieldMappedToOnePdfFieldsSignal.map(ManyToOneFormToPdfFieldMapping -> _),
      hasNonExistentFieldsBlueprintMappingSignal.map(BlueprintMetadataNonExistentMappingSection -> _),
      hasMismatchedFieldsBlueprintMappingSignal.map(BlueprintMetadataMismatchedMappingSection -> _),
      hasNonImportedBlueprintMetadataSignal.map(BlueprintMetadataNonImportedSection -> _)
    )

    val allSectionStatusesSignal = Signal.combineSeq(sectionStatusSignals)

    val allPassedSignal = allSectionStatusesSignal.map(_.forall(_._2)).distinct

    val sectionsSignal = formTypeSignal
      .combineWith(allSectionStatusesSignal)
      .map { case (formType, sectionStatuses) =>
        val allSections = if (formType == FormType.Validation) {
          DefaultSectionOrder ++ SectionForValidationModeOnly
        } else {
          DefaultSectionOrder
        }
        val (validSections, invalidSections) =
          allSections.partition(sectionType => sectionStatuses.find(_._1 == sectionType).exists(_._2))
        val (errorSections, warningSections) =
          invalidSections.partition(_.isError)

        errorSections ++ warningSections ++ validSections
      }
      .distinct

    div(
      tw.flex.flexCol.flexFill.overflowYAuto,
      child.maybe <-- allPassedSignal.map(Option.when(_) {
        div(
          tw.flex.flexCol.itemsCenter.py32.bgSuccess1,
          tw.borderBottom.borderGray3,
          div(tw.textSuccess4, IconL(Val(Icon.Glyph.CheckCircle), Icon.Size.Custom(48))()),
          div(tw.text15.mt16, "All good!")
        )
      }),
      children <-- sectionsSignal.split(identity) { case (_, sectionType, _) =>
        renderSection(sectionType)
      }
    )
  }

  private def renderSection(sectionType: ChecklistSection) = {
    val isExpandedSignal = expandedSectionsSignal.map(_.contains(sectionType)).distinct
    val toggleExpandedObserver = Observer[Unit] { _ =>
      toggleChecklistSectionObserver.onNext(sectionType)
    }

    sectionType match {
      case InvalidRuleSection =>
        InvalidRuleSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          invalidRulesSignal = allInvalidRulesSignal,
          formSignal = formSignal,
          isEditModeSignal = isEditModeSignal,
          onRuleChanged = onRuleChanged
        )()
      case ConflictRuleSection =>
        ConflictRuleSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          formSignal = formSignal,
          outputPropertyConflictsSignal = logicOutputPropertyConflictsSignal,
          outputConflictsSignal = logicOutputConflictsSignal,
          isEditModeSignal = isEditModeSignal,
          onRuleChanged = onRuleChanged,
          onSelectKey = onSelectKey
        )()
      case InvalidPdfMappingSection =>
        InvalidPdfMappingSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          invalidPdfMappingFieldsSignal = invalidPdfMappingFieldsSignal,
          onSelectKey = onSelectKey
        )()
      case DuplicateFileNameSection =>
        DuplicateFileNameSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          duplicateFileNamesSignal = duplicateFileNamesSignal,
          onSwitchToDocumentTab = onSwitchToDocumentTab
        )()
      case MissingFileSection =>
        MissingFileSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          missingFileFieldsSignal = missingFileFieldsSignal,
          onSelectKey = onSelectKey
        )()
      case UnmappedPdfFieldSection =>
        UnmappedPdfFieldSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          unmappedPdfFieldsSignal = unmappedPdfFieldsSignal,
          extractedPdfsSignal = extractedPdfsValueSignal,
          onSelectKeyWithPDF = onSelectKeyWithPDF,
          sideViewerOpenObserver = sideViewerOpenObserver
        )()
      case InvalidPdfFieldsSection =>
        InvalidPdfFieldsSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          invalidPdfFieldsSignal = invalidPdfFieldsSignal,
          extractedPdfsSignal = extractedPdfsSignal,
          onSelectKeyWithPDF = onSelectKeyWithPDF,
          sideViewerOpenObserver = sideViewerOpenObserver
        )()
      case InvalidRepeatableSection =>
        InvalidRepeatableSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          invalidRepeatableFieldsSignal = invalidRepeatableFieldsSignal,
          onSelectKey = onSelectKey
        )()
      case UnmatchedPdfMappingTypeSection =>
        UnmatchedPdfMappingTypeSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          unmatchedPdfMappingTypeFieldsSignal = unmatchedPdfMappingTypeFieldsSignal,
          onSelectKey = onSelectKey
        )()
      case IncorrectRequireFieldSection =>
        IncorrectRequireFieldSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          incorrectRequireFieldsSignal = incorrectRequireFieldsSignal,
          ignoredInvalidRequireSetupsSignal = ignoredInvalidRequireSetupsSignal,
          isEditModeSignal = isEditModeSignal,
          onSelectKey = onSelectKey,
          formBuilderDataCommandObserver = formBuilderDataCommandObserver
        )()
      case InvalidPageFieldsSection =>
        InvalidPageFieldsSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          invalidPageFieldsSignal = invalidPageFieldsSignal,
          onSelectKey = onSelectKey
        )()
      case DuplicatedFundSetupAsaSection =>
        DuplicatedFundSetupAsaSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          duplicatedFsAsaMappingsSignal = duplicatedFsAsaMappingsSignal,
          onSelectKey = onSelectKey
        )()
      case EmptySignerSignatureSection =>
        EmptySignerSignatureSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          emptySignerSignaturesSignal = emptySignerSignaturesSignal,
          onSelectKey = onSelectKey
        )()
      case TransferredRulesWarningSection =>
        TransferredRulesWarningSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          formSignal = formSignal,
          formRuleWarningsSignal = formRuleWarningsSignal,
          isEditModeSignal = isEditModeSignal,
          formRuleExtractResultSignal = formRuleExtractResultSignal,
          selectedKeySignal = selectedKeySignal,
          formBuilderDataCommandObserver = formBuilderDataCommandObserver,
          onSelectKey = onSelectKey
        )()
      case InvalidPdfNonInputMappingsSection =>
        InvalidPdfNonInputMappingsSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          invalidPdfNonInputMappingFieldsSignal = invalidPdfNonInputMappingFieldsSignal,
          ignoredInvalidPdfNonInputMappingFieldsSignal = ignoredInvalidPdfNonInputMappingFieldsSignal,
          isEditModeSignal = isEditModeSignal,
          onSelectKey = onSelectKey,
          formBuilderDataCommandObserver = formBuilderDataCommandObserver
        )()
      case UnmappedPdfNonInputFieldsSection =>
        UnmappedPdfNonInputFieldsSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          unmappedPdfNonInputFieldsSignal = unmappedPdfNonInputFieldsSignal,
          ignoredUnmappedPdfNonInputFieldsSignal = ignoredUnmappedPdfNonInputFieldsSignal,
          isEditModeSignal = isEditModeSignal,
          onSelectKey = onSelectKey,
          formBuilderDataCommandObserver = formBuilderDataCommandObserver,
          onSelectKeyWithPDF = onSelectKeyWithPDF,
          sideViewerOpenObserver = sideViewerOpenObserver
        )()
      case InvalidCustomisedSignatureSection =>
        InvalidCustomisedSignatureSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          invalidCustomisedSignaturesSignal = invalidCustomisedSignaturesSignal,
          onSelectKey = onSelectKey
        )()
      case ManyToOneFormToPdfFieldMapping =>
        ManyFormFieldMappedToOnePdfFieldsSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          invalidFields = manyFormFieldMappedToOnePdfFieldsSignal,
          onSelectKeyWithPDF = onSelectKeyWithPDF
        )()
      case OneToManyFormToPdfFieldMapping =>
        OneFormFieldMappedToManyPdfFieldsSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          invalidFields = oneFormFieldMappedToManyPdfFieldsSignal,
          onSelectKey = onSelectKey
        )()
      case FormFieldUnmappedSection =>
        UnmappedFormFieldsSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          unmappedFormFields = unmappedFormFieldsSignal,
          onSelectKey = onSelectKey
        )()
      case BlueprintMetadataNonExistentMappingSection =>
        BlueprintMetadataMappingNonExistentSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          invalidBlueprintMetadataMappingFieldsSignal = invalidBlueprintMetadataMappingFieldsSignal,
          onSelectKey = onSelectKey
        )()
      case BlueprintMetadataMismatchedMappingSection =>
        BlueprintMetadataMappingMismatchedSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          invalidBlueprintMetadataMappingFieldsSignal = invalidBlueprintMetadataMappingFieldsSignal,
          onSelectKey = onSelectKey,
          isEditModeSignal = isEditModeSignal,
          formBuilderDataCommandObserver = formBuilderDataCommandObserver,
          ignoredMismatchedBlueprintMetadataFieldsSignal = ignoredMismatchedBlueprintMetadataFieldsSignal
        )()
      case BlueprintMetadataNonImportedSection =>
        BlueprintMetadataNonImportedSectionView(
          isExpandedSignal = isExpandedSignal,
          toggleExpandedObserver = toggleExpandedObserver,
          invalidBlueprintMetadataMappingFieldsSignal = invalidBlueprintMetadataMappingFieldsSignal,
          onHeaderModeChange = onHeaderModeChange,
          blueprintRefOptSignal = blueprintRefOptSignal,
          onClose = close,
          onChangeBpMetadataSearchText = onChangeBpMetadataSearchText,
          isEditModeSignal = isEditModeSignal,
          formBuilderDataCommandObserver = formBuilderDataCommandObserver,
          ignoredNonImportedBlueprintMetadataFieldsSignal = ignoredNonImportedBlueprintMetadataFieldsSignal
        )()
    }
  }

  private val footer: Node = div(
    tw.px20.py16.flexNone.flex.justifyEnd.borderTop.borderGray3,
    ButtonL(onClick = close.contramap(_ => ()))("Done")
  )

}

object FormBuilderChecklistModal {

  private val DefaultSectionOrder =
    Seq(
      InvalidRuleSection,
      ConflictRuleSection,
      InvalidPdfMappingSection,
      DuplicateFileNameSection,
      MissingFileSection,
      UnmappedPdfFieldSection,
      InvalidPdfFieldsSection,
      InvalidRepeatableSection,
      UnmatchedPdfMappingTypeSection,
      IncorrectRequireFieldSection,
      InvalidPageFieldsSection,
      DuplicatedFundSetupAsaSection,
      EmptySignerSignatureSection,
      TransferredRulesWarningSection,
      InvalidPdfNonInputMappingsSection,
      UnmappedPdfNonInputFieldsSection,
      InvalidCustomisedSignatureSection,
      BlueprintMetadataNonExistentMappingSection,
      BlueprintMetadataMismatchedMappingSection,
      BlueprintMetadataNonImportedSection
    )

  private val SectionForValidationModeOnly = Seq(
    ManyToOneFormToPdfFieldMapping,
    OneToManyFormToPdfFieldMapping,
    FormFieldUnmappedSection
  )

  enum RuleErrorType { case InvalidSyntax, InitializingError }
}
