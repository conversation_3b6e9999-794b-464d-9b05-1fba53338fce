syntax = "proto3";

package anduin.forms.storage.data.user;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.form.FormVersionDataId"
};

message FormDataUserTempModel {
  option (scalapb.message).annotations = "private[storage]";

  string owner = 1 [(scalapb.field).type = "UserId"];
  string form_data_id = 2 [(scalapb.field).type = "FormVersionDataId"];
  InstantMessage created_at = 3 [(scalapb.field).type = "java.time.Instant", (scalapb.field).no_box = true];
  InstantMessage last_accessed_at = 4 [(scalapb.field).type = "java.time.Instant", (scalapb.field).no_box = true];
}

message RecordTypeUnion {
  FormDataUserTempModel _FormDataUserTempModel = 1;
}
