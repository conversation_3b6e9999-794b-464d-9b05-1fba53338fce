// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.utils.components

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.portal.laminar.PortalWrapperL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.viewer.pdfium.ViewerP
import design.anduin.style.tw.*
import zio.ZIO

import anduin.file.FileDownloaderUtils
import anduin.forms.ui.types.FileId
import anduin.forms.utils.FormDataConverters
import anduin.forms.utils.components.FileViewerButton.{FileState, TargetType}
import anduin.frontend.AirStreamUtils
import anduin.util.FilenameUtils
import com.anduin.stargazer.client.services.file.FileJsClient
import com.anduin.stargazer.service.FileServiceEndpoints.GetPdfUrlParams
import com.anduin.stargazer.service.file.BatchDownloadRequest

final case class FileViewerButton(
  fileId: FileId,
  targetRenderer: FileViewerButton.TargetType
) {

  private val fileStateVar = Var[FileState](FileState.Loading)

  private def loadFile: EventStream[FileState] =
    AirStreamUtils.taskToStreamDEPRECATED(
      for {
        _ <- ZIO.attempt(fileStateVar.set(FileState.Loading))
        fid <- ZIO.attempt(FormDataConverters.fileIdTypeToFileIdThrowError(fileId))
        state <- FileJsClient
          .getPdfUrlCache(GetPdfUrlParams(fid, None))
          .map(
            _.fold(
              _ => FileState.Error,
              resp => FileState.Loaded(resp.url)
            )
          )
      } yield state
    )

  def apply(): HtmlElement = {
    val fileName = targetRenderer match {
      case TargetType.StringValue(name)       => name
      case TargetType.CustomRenderer(name, _) => name
    }
    val extension = FilenameUtils.getExtension(fileName).getOrElse("pdf")
    ModalL(
      renderTarget = renderViewButton(_, extension),
      renderContent = closeModal => {
        div(
          tw.hPc100.overflowHidden.relative,
          tw.flex.flexCol,
          // Header
          div(
            tw.flex.itemsCenter.px16.wPc100,
            tw.shadow1.z1,
            height := "56px",
            // Close button
            div(
              tw.mr16,
              ButtonL(
                style = ButtonL.Style.Full(icon = Option(Icon.Glyph.ArrowLeft)),
                onClick = Observer { _ =>
                  closeModal.onNext(())
                }
              )()
            ),
            // Border
            div(
              tw.mr12.wPx1.hPx24.bgGray3
            ),
            // File icon
            div(
              tw.mr8,
              IconL(name = Val(Icon.File.ByExtension(extension)), size = Icon.Size.Px32)()
            ),
            // File name
            TruncateL(
              target = {
                div(
                  tw.flexFill.mr8,
                  tw.textGray8.text15.leading20.fontSemiBold,
                  fileName
                )
              }
            )(),
            // Download button
            div(
              tw.mlAuto,
              TooltipL(
                renderTarget = {
                  ButtonL(
                    style = ButtonL.Style.Full(icon = Option(Icon.Glyph.Download)),
                    onClick = Observer { _ =>
                      FileDownloaderUtils.download(
                        BatchDownloadRequest(
                          zipNameOnMultipleFiles = "Generated document",
                          fileIds = Seq(fileId).flatMap(FormDataConverters.fileIdTypeToFileId)
                        )
                      )
                    }
                  )()
                },
                renderContent = _.amend("Download"),
                position = PortalPosition.BottomRight
              )()
            )
          ),
          // Content
          div(
            tw.flexFill.bgGray1,
            tw.borderTop.border1.borderGray3,
            loadFile --> fileStateVar.writer,
            child <-- fileStateVar.signal.map {
              case FileState.Error       => ErrorView
              case FileState.Loading     => LoadingView
              case FileState.Loaded(url) => ViewerP(url = url)()
            }
          )
        )
      },
      size = ModalL.Size(width = ModalL.Width.Full, height = ModalL.Height.Full),
      // Reset state after modal is closed to avoid using expired url next time when modal is opened
      afterUserClose = Observer { _ => fileStateVar.set(FileState.Loading) }
    )()
  }

  private def renderViewButton(open: Observer[Unit], fileExtension: String) = {
    targetRenderer match {
      case TargetType.StringValue(name) =>
        TooltipL(
          renderTarget = div(
            tw.flex.flexFill.itemsCenter.cursorPointer,
            onClick.mapTo(()) --> open,
            div(
              tw.flexNone.mr4,
              IconL(name = Val(Icon.File.ByExtension(fileExtension)))()
            ),
            div(
              tw.flexFill.truncate.hover(tw.textPrimary4),
              name
            )
          ),
          renderContent = _.amend(name),
          targetWrapper = PortalWrapperL.BlockContent
        )()
      case TargetType.CustomRenderer(_, renderer) =>
        renderer(open)
    }
  }

  private lazy val ErrorView =
    div(tw.textDanger4.flex.itemsCenter.justifyCenter, "File not found")

  private lazy val LoadingView = BlockIndicatorL(isFullHeight = true)()

}

object FileViewerButton {

  private sealed trait FileState derives CanEqual

  private object FileState {
    case object Error extends FileState
    case object Loading extends FileState
    case class Loaded(url: String) extends FileState
  }

  enum TargetType {
    case StringValue(name: String)
    case CustomRenderer(name: String, renderer: Observer[Unit] => HtmlElement)
  }

}
