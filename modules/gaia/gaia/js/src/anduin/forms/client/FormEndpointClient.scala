// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.client

import zio.Task

import anduin.forms.endpoint.*
import anduin.forms.engine.GaiaState
import anduin.service.GeneralServiceException
import anduin.tapir.client.AuthenticatedEndpointClient
import anduin.tapir.endpoint.EmptyResponse

object FormEndpointClient extends AuthenticatedEndpointClient {

  val loadPdfFields: LoadFieldsRequest => Task[Either[GeneralServiceException, LoadFieldsResponse]] =
    toClientThrowDecodeAndSecurityFailures(PdfToolEndpoints.loadPdfFields)

  val savePdfFields: SaveFieldsRequest => Task[Either[GeneralServiceException, SaveFieldsResponse]] =
    toClientThrowDecodeAndSecurityFailures(PdfToolEndpoints.savePdfFields)

  val getTextInAreas: GetTextInAreasRequest => Task[Either[GeneralServiceException, GetTextInAreasResponse]] =
    toClientThrowDecodeAndSecurityFailures(PdfToolEndpoints.getTextInAreas)

  val getPdfFileContentHash
    : GetPdfFileContentHashRequest => Task[Either[GeneralServiceException, GetPdfFileContentHashResponse]] =
    toClientThrowDecodeAndSecurityFailures(PdfToolEndpoints.getPdfFileContentHash)

  val processFile: ProcessFileRequest => Task[Either[GeneralServiceException, ProcessFileResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.processFile)

  val processAnnotationDocument
    : ProcessAnnotationDocumentParams => Task[Either[GeneralServiceException, ProcessAnnotationDocumentResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.processAnnotationDocument)

  val extractText: ExtractTextRequest => Task[Either[GeneralServiceException, ExtractTextResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.extractText)

  val createForm: CreateFormParams => Task[Either[GeneralServiceException, CreateFormResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.createForm)

  val startEditForm: StartEditFormParams => Task[Either[GeneralServiceException, StartEditFormResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.startEditForm)

  val getFormLock: GetFormLockParams => Task[Either[GeneralServiceException, GetFormLockResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getFormLock)

  val renewFormLock: RenewFormLockParams => Task[Either[GeneralServiceException, RenewFormLockResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.renewFormLock)

  val releaseFormLock: ReleaseFormLockParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.releaseFormLock)

  val editForm: EditFormParams => Task[Either[GeneralServiceException, EditFormResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.editForm)

  val duplicateForm: DuplicateFormParams => Task[Either[GeneralServiceException, DuplicateFormResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.duplicateForm)

  val saveForm: SaveFormParams => Task[Either[GeneralServiceException, SaveFormResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.saveForm)

  val saveFormMetadata: SaveFormMetadataParams => Task[Either[GeneralServiceException, SaveFormMetadataResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.saveFormMetadata)

  val saveFormSystemMetadata
    : SaveFormMetadataParams => Task[Either[GeneralServiceException, SaveFormMetadataResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.saveFormMetadata)

  val getForm: GetFormParams => Task[Either[GeneralServiceException, GetFormResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getForm)

  val getAllForms: Unit => Task[Either[GeneralServiceException, GetAllFormModelsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getAllForms)

  val exportFormMatching
    : ExportFormMatchingParams => Task[Either[GeneralServiceException, ExportFormMatchingResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.exportFormMatching)

  val getAllFormsAnalytics: Unit => Task[Either[GeneralServiceException, GetAllFormsAnalyticsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getAllFormsAnalytics)

  val getAllVersions: GetAllVersionParams => Task[Either[GeneralServiceException, GetAllVersionResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getAllVersions)

  val createFormVersion: CreateFormVersionParams => Task[Either[GeneralServiceException, CreateFormVersionResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.createFormVersion)

  val editVersion: EditVersionParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.editVersion)

  val getFormActivities: GetFormActivitiesParams => Task[Either[GeneralServiceException, GetFormActivitiesResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getFormActivities)

  val diffForm: DiffFormParams => Task[Either[GeneralServiceException, DiffFormResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.diffForm)

  val diffFormFile: DiffFormFileParams => Task[Either[GeneralServiceException, DiffFormFileResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.diffFormFile)

  val generatePdf: GeneratePdfParams => Task[Either[GeneralServiceException, GeneratePdfResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.generatePdf)

  val getFileInfoToCut: GetFileInfoToCutParams => Task[Either[GeneralServiceException, GetFileInfoToCutResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getFileInfoToCut)

  val cutFile: CutFileParams => Task[Either[GeneralServiceException, CutFileResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.cutFile)

  val getFormFileMetadata
    : GetFormFileMetadataParams => Task[Either[GeneralServiceException, GetFormFileMetadataResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getFormFileMetadata)

  val addFormStandardAliasMapping: AddFormStandardAliasMappingParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.addFormStandardAliasMapping)

  val removeFormStandardAliasMapping
    : RemoveFormStandardAliasMappingParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.removeFormStandardAliasMapping)

  val importFormStandardAliasMapping: AddFormStandardAliasMappingParams => Task[
    Either[GeneralServiceException, ImportFormStandardAliasMappingResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.importFormStandardAliasMapping)

  val getFormStandardAliasMapping
    : GetFormStandardAliasMappingParams => Task[Either[GeneralServiceException, Map[String, String]]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getFormStandardAliasMapping)

  val getAllStandardAliases: Unit => Task[Either[GeneralServiceException, List[StandardAlias]]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getAllStandardAliases)

  val exportAllPdfMappingsToCsv
    : ExportAllPdfMappingsToCsvParams => Task[Either[GeneralServiceException, ExportAllPdfMappingsToCsvResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.exportAllPdfMappingsToCsv)

  val importSpreadsheetFile
    : ImportSpreadsheetFileParams => Task[Either[GeneralServiceException, ImportDataFileResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.importSpreadsheetFile)

  val importCsvFile: ImportCSVFileParams => Task[Either[GeneralServiceException, ImportDataFileResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.importCsvFile)

  val getDefaultImportTemplate: GenerateDefaultImportTemplateParams => Task[
    Either[GeneralServiceException, GenerateDefaultImportTemplateResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getDefaultImportTemplate)

  val getNamingRefineTemplate: GetNamingRefineTemplateParams => Task[
    Either[GeneralServiceException, GetNamingRefineTemplateResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getNamingRefineTemplate)

  val createFormFolder: CreateFormFolderParams => Task[Either[GeneralServiceException, CreateFormFolderResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.createFormFolder)

  val updateFormFolder: UpdateFormFolderParams => Task[Either[GeneralServiceException, UpdateFormFolderResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.updateFormFolder)

  val deleteFormFolder: DeleteFormFolderParams => Task[Either[GeneralServiceException, DeleteFormFolderResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.deleteFormFolder)

  val archiveForm: ArchiveFormParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.archiveForm)

  val unarchiveForm: UnarchiveFormParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.unarchiveForm)

  val getFormFolderContent
    : GetFormFolderContentParams => Task[Either[GeneralServiceException, GetFormFolderContentResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getFormFolderContent)

  val getAllFormFolderModels: Unit => Task[Either[GeneralServiceException, GetAllFormFolderModelsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getAllFormFolderModels)

  val getFormFolder: GetFormFolderParams => Task[Either[GeneralServiceException, GetFormFolderResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getFormFolder)

  val getComponentLibraryVersion: Unit => Task[Either[GeneralServiceException, GetComponentLibraryVersionResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getComponentLibraryVersion)

  val setComponentLibraryVersion
    : SetComponentLibraryVersionParams => Task[Either[GeneralServiceException, SetComponentLibraryVersionResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.setComponentLibraryVersion)

  val setFormVersionTag: SetFormVersionTagParams => Task[Either[GeneralServiceException, SetFormVersionTagResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.setFormVersionTag)

  val getAllLibraryComponents: Unit => Task[Either[GeneralServiceException, GetAllLibraryComponentsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getAllLibraryComponents)

  val getAllInvestorAccessAsaValues
    : Unit => Task[Either[GeneralServiceException, GetAllInvestorAccessAsaValuesResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getAllInvestorAccessAsaValues)

  val copyFormFiles: CopyFormFilesParams => Task[Either[GeneralServiceException, CopyFormFilesResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.copyFormFiles)

  val getAllTemplates: Unit => Task[Either[GeneralServiceException, GetAllTemplateResponse]] =
    toClientThrowDecodeAndSecurityFailures(TemplateEndpoints.getAllTemplates)

  val createTemplate: CreateTemplateParams => Task[Either[GeneralServiceException, CreateTemplateResponse]] =
    toClientThrowDecodeAndSecurityFailures(TemplateEndpoints.createTemplate)

  val updateTemplateName
    : UpdateTemplateNameParams => Task[Either[GeneralServiceException, UpdateTemplateNameResponse]] =
    toClientThrowDecodeAndSecurityFailures(TemplateEndpoints.updateTemplateName)

  val getTemplate: GetTemplateParams => Task[Either[GeneralServiceException, GetTemplateResponse]] =
    toClientThrowDecodeAndSecurityFailures(TemplateEndpoints.getTemplate)

  val getTemplateVersions
    : GetTemplateVersionsParams => Task[Either[GeneralServiceException, GetTemplateVersionsResponse]] =
    toClientThrowDecodeAndSecurityFailures(TemplateEndpoints.getTemplateVersions)

  val createTemplateVersion
    : CreateTemplateVersionParams => Task[Either[GeneralServiceException, CreateTemplateVersionResponse]] =
    toClientThrowDecodeAndSecurityFailures(TemplateEndpoints.createTemplateVersion)

  val updateTemplateVersionDescription: UpdateTemplateVersionDescriptionParams => Task[
    Either[GeneralServiceException, UpdateTemplateVersionDescriptionResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(TemplateEndpoints.updateTemplateVersionDescription)

  val previewPdfFields: PreviewAnnotationParams => Task[Either[GeneralServiceException, PreviewAnnotationResponse]] =
    toClientThrowDecodeAndSecurityFailures(PdfToolEndpoints.previewPdfFields)

  val exportFormAsaMappingTemplate
    : ExportFormAsaMappingTemplateParams => Task[Either[GeneralServiceException, ExportFormAsaMappingTemplateResp]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.exportFormAsaMappingTemplate)

  val processFormAsaMappingTemplate
    : ProcessFormAsaMappingTemplateParams => Task[Either[GeneralServiceException, ProcessFormAsaMappingTemplateResp]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.processFormAsaMappingTemplate)

  val editFormAssociatedLinks
    : EditFormAssociatedLinksParams => Task[Either[GeneralServiceException, EditFormAssociatedLinksResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.editFormAssociatedLinks)

  val getFormToolConfigs
    : GetFormToolConfigsParams => Task[Either[GeneralServiceException, GetFormToolConfigsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getFormToolConfigs)

  val addFormToolConfig: AddFormToolConfigParams => Task[Either[GeneralServiceException, AddFormToolConfigsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.addFormToolConfig)

  val updateFormToolConfig
    : UpdateFormToolConfigParams => Task[Either[GeneralServiceException, UpdateFormToolConfigsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.updateFormToolConfig)

  val generateSignatures
    : GenerateSignaturesParams => Task[Either[GeneralServiceException, GenerateSignaturesResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.generateSignatures)

  val getFilePageSizes: GetFilePageSizesParams => Task[Either[GeneralServiceException, GetFilePageSizesResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getFilePageSizes)

  val importFromAnnotationDocument: ImportFromAnnotationDocumentParams => Task[
    Either[GeneralServiceException, ImportFromAnnotationDocumentResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.importFromAnnotationDocument)

  val updateBlueprintRef: UpdateBlueprintRefParams => Task[
    Either[GeneralServiceException, UpdateBlueprintRefResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.updateBlueprintRef)

  val removeBlueprintRef: RemoveBlueprintRefParams => Task[
    Either[GeneralServiceException, RemoveBlueprintRefResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.removeBlueprintRef)

  val diffFormBlueprint: DiffFormBlueprintParams => Task[Either[GeneralServiceException, DiffFormBlueprintResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.diffFormBlueprint)

  val getFormCueModule: GetFormCueModuleParams => Task[Either[GeneralServiceException, GetFormCueModuleResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getFormCueModule)

  val generateFormModule: GenerateFormCueModuleParams => Task[Either[GeneralServiceException, EmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.generateFormModule)

  val extractDataLayerDefaultValues: ExtractDataLayerDefaultValuesParams => Task[
    Either[GeneralServiceException, ExtractDataLayerDefaultValuesResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.extractDataLayerDefaultValues)

  val verifyLinkedCueVersion: VerifyLinkedCueVersionParams => Task[Either[GeneralServiceException, EmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.verifyLinkedCueVersion)

  val getFormDataUserTemp: GetFormDataUserTempParams => Task[Either[GeneralServiceException, GaiaState]] =
    toClientThrowDecodeAndSecurityFailures(FormEndpoints.getFormDataUserTemp)

}
