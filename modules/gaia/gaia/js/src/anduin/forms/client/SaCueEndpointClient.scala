// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.client

import zio.Task

import anduin.sa.endpoints.*
import anduin.service.GeneralServiceException
import anduin.tapir.client.AuthenticatedEndpointClient
import anduin.tapir.endpoint.EmptyResponse

object SaCueEndpointClient extends AuthenticatedEndpointClient {

  val createSaDataTemplateCueModule: CreateSaDataTemplateCueModuleParams => Task[
    Either[GeneralServiceException, CreateSaDataTemplateCueModuleResponse]
  ] = toClientThrowDecodeAndSecurityFailures(SaCueEndpoints.createSaDataTemplateCueModule)

  val getSaDataTemplateCueModule: GetSaDataTemplateCueModuleParams => Task[
    Either[GeneralServiceException, GetSaDataTemplateCueModuleResponse]
  ] = toClientThrowDecodeAndSecurityFailures(SaCueEndpoints.getSaDataTemplateCueModule)

  val generateSaDataTemplateCueModule
    : GenerateSaDataTemplateCueModuleParams => Task[Either[GeneralServiceException, EmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(SaCueEndpoints.generateSaDataTemplateCueModule)

}
