// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.sa.endpoints

import anduin.circe.generic.semiauto.CirceCodec
import anduin.cue.model.CueModuleSharedModels.CueModule
import anduin.id.annotation.AnnotationDocumentVersionId
import anduin.id.form.FormVersionId
import anduin.id.sa.{SaDataTemplateId, SaProfileId}
import anduin.model.codec.MapCodecs.given
import anduin.tapir.endpoint.EmptyEndpointValidationParams

final case class AddDocumentSaProfileMappingParams(
  saProfileId: SaProfileId,
  documentVersionId: AnnotationDocumentVersionId,
  mappingToAdd: Map[String, String],
  checkForValidFieldName: Boolean = true
) derives CirceCodec.WithDefaults

final case class ImportDocumentSaProfileMappingParams(
  saProfileId: SaProfileId,
  documentVersionId: AnnotationDocumentVersionId,
  mappingToImport: Map[String, String]
) derives CirceCodec.WithDefaults

final case class ImportDocumentSaProfileMappingResponse(
  importedMapping: Map[String, String]
) derives CirceCodec.WithDefaults

final case class TransferSingleSaProfileMappingParams(
  saProfileId: SaProfileId,
  srcDocumentVersionId: AnnotationDocumentVersionId,
  targetDocumentVersionId: AnnotationDocumentVersionId
) derives CirceCodec.WithDefaults

final case class TransferAllSaProfileMappingsParams(
  srcDocumentVersionId: AnnotationDocumentVersionId,
  targetDocumentVersionId: AnnotationDocumentVersionId
) derives CirceCodec.WithDefaults

final case class RemoveDocumentSaProfileMappingParams(
  saProfileId: SaProfileId,
  documentVersionId: AnnotationDocumentVersionId,
  mappedFieldsToRemove: Set[String]
) derives CirceCodec.WithDefaults

final case class DeleteDocumentSaProfileMappingPermanentlyParams(
  saProfileId: SaProfileId,
  documentVersionId: AnnotationDocumentVersionId
) derives CirceCodec.WithDefaults

final case class GetDocumentSaProfileMappingParams(
  saProfileId: SaProfileId,
  documentVersionId: AnnotationDocumentVersionId
) derives CirceCodec.WithDefaults

final case class GetDocumentSaProfileMappingResponse(
  saProfileId: SaProfileId,
  documentVersionId: AnnotationDocumentVersionId,
  fieldToSaMappingOpt: Option[Map[String, String]]
) derives CirceCodec.WithDefaults

final case class QuerySaProfileMappingByDocumentVersionIdParams(
  documentVersionId: AnnotationDocumentVersionId
) derives CirceCodec.WithDefaults

final case class QuerySaProfileMappingByDocumentVersionIdResponse(
  documentVersionId: AnnotationDocumentVersionId,
  saProfileMappingList: List[SingleSaProfileMappingResponse]
) derives CirceCodec.WithDefaults

object QuerySaProfileMappingByDocumentVersionIdResponse {

  lazy val Empty: QuerySaProfileMappingByDocumentVersionIdResponse = QuerySaProfileMappingByDocumentVersionIdResponse(
    AnnotationDocumentVersionId.defaultValue.get,
    List.empty
  )

}

final case class CreateSaDataTemplateCueModuleParams(
  saDataTemplateId: SaDataTemplateId
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults

final case class CreateSaDataTemplateCueModuleResponse(
  cueModule: CueModule
) derives CirceCodec.WithDefaults

final case class GetSaDataTemplateCueModuleParams(
  saDataTemplateId: SaDataTemplateId
) extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults

final case class GetSaDataTemplateCueModuleResponse(
  cueModuleOpt: Option[CueModule]
) derives CirceCodec.WithDefaults

final case class QueryFormVersionSaMappingInfoParams(
  formVersionId: FormVersionId
) derives CirceCodec.WithDefaults

final case class QueryFormVersionSaMappingInfoResponse(
  saProfileBasicInfoOpt: Option[SaProfileBasicInfo],
  fieldToSaMapping: Map[String, String]
) derives CirceCodec.WithDefaults

final case class GenerateSaDataTemplateCueModuleParams(saDataTemplateId: SaDataTemplateId)
    extends EmptyEndpointValidationParams derives CirceCodec.WithDefaults
