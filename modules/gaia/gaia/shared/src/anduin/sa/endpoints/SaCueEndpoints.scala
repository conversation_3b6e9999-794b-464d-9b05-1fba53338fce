package anduin.sa.endpoints

import sttp.tapir.given

import anduin.service.GeneralServiceException
import anduin.tapir.AuthenticatedEndpoints
import anduin.tapir.AuthenticatedEndpoints.BaseAuthenticatedEndpoint
import anduin.tapir.endpoint.EmptyResponse

object SaCueEndpoints extends AuthenticatedEndpoints {
  private val SaCuePath = "saCue"

  val createSaDataTemplateCueModule: BaseAuthenticatedEndpoint[
    CreateSaDataTemplateCueModuleParams,
    GeneralServiceException,
    CreateSaDataTemplateCueModuleResponse
  ] = {
    authEndpoint[
      CreateSaDataTemplateCueModuleParams,
      GeneralServiceException,
      CreateSaDataTemplateCueModuleResponse
    ](SaCuePath / "createSaDataTemplateCueModule")
  }

  val getSaDataTemplateCueModule: BaseAuthenticatedEndpoint[
    GetSaDataTemplateCueModuleParams,
    GeneralServiceException,
    GetSaDataTemplateCueModuleResponse
  ] = {
    authEndpoint[
      GetSaDataTemplateCueModuleParams,
      GeneralServiceException,
      GetSaDataTemplateCueModuleResponse
    ](SaCuePath / "getSaDataTemplateCueModule")
  }

  val generateSaDataTemplateCueModule: BaseAuthenticatedEndpoint[
    GenerateSaDataTemplateCueModuleParams,
    GeneralServiceException,
    EmptyResponse
  ] = {
    authEndpoint[
      GenerateSaDataTemplateCueModuleParams,
      GeneralServiceException,
      EmptyResponse
    ](SaCuePath / "generateSaDataTemplateCueModule")
  }

}
