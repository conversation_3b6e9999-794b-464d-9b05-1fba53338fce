// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.endpoint

import anduin.service.GeneralServiceException
import anduin.tapir.AuthenticatedEndpoints
import anduin.tapir.AuthenticatedEndpoints.BaseAuthenticatedEndpoint
import anduin.tapir.endpoint.EmptyResponse
import sttp.tapir.*

import anduin.forms.engine.GaiaState

object FormEndpoints extends AuthenticatedEndpoints {

  private lazy val FormPath = "form"

  lazy val processFile: BaseAuthenticatedEndpoint[ProcessFileRequest, GeneralServiceException, ProcessFileResponse] =
    authEndpoint[ProcessFileRequest, GeneralServiceException, ProcessFileResponse](FormPath / "processFile")

  lazy val processAnnotationDocument: BaseAuthenticatedEndpoint[
    ProcessAnnotationDocumentParams,
    GeneralServiceException,
    ProcessAnnotationDocumentResponse
  ] =
    authEndpoint[
      ProcessAnnotationDocumentParams,
      GeneralServiceException,
      ProcessAnnotationDocumentResponse
    ](FormPath / "processAnnotationDocument")

  lazy val extractText: BaseAuthenticatedEndpoint[ExtractTextRequest, GeneralServiceException, ExtractTextResponse] =
    authEndpoint[ExtractTextRequest, GeneralServiceException, ExtractTextResponse](FormPath / "extractText")

  lazy val createForm: BaseAuthenticatedEndpoint[CreateFormParams, GeneralServiceException, CreateFormResponse] =
    authEndpoint[CreateFormParams, GeneralServiceException, CreateFormResponse](FormPath / "createForm")

  lazy val startEditForm
    : BaseAuthenticatedEndpoint[StartEditFormParams, GeneralServiceException, StartEditFormResponse] =
    authEndpoint[StartEditFormParams, GeneralServiceException, StartEditFormResponse](FormPath / "startEditForm")

  lazy val getFormLock: BaseAuthenticatedEndpoint[GetFormLockParams, GeneralServiceException, GetFormLockResponse] =
    authEndpoint[GetFormLockParams, GeneralServiceException, GetFormLockResponse](
      FormPath / "getFormLock"
    )

  lazy val renewFormLock
    : BaseAuthenticatedEndpoint[RenewFormLockParams, GeneralServiceException, RenewFormLockResponse] =
    authEndpoint[RenewFormLockParams, GeneralServiceException, RenewFormLockResponse](
      FormPath / "renewFormLock"
    )

  lazy val releaseFormLock: BaseAuthenticatedEndpoint[ReleaseFormLockParams, GeneralServiceException, Unit] =
    authEndpoint[ReleaseFormLockParams, GeneralServiceException, Unit](
      FormPath / "releaseFormLock"
    )

  lazy val editForm: BaseAuthenticatedEndpoint[EditFormParams, GeneralServiceException, EditFormResponse] =
    authEndpoint[EditFormParams, GeneralServiceException, EditFormResponse](FormPath / "editForm")

  lazy val duplicateForm
    : BaseAuthenticatedEndpoint[DuplicateFormParams, GeneralServiceException, DuplicateFormResponse] =
    authEndpoint[DuplicateFormParams, GeneralServiceException, DuplicateFormResponse](FormPath / "duplicateForm")

  lazy val saveForm: BaseAuthenticatedEndpoint[SaveFormParams, GeneralServiceException, SaveFormResponse] =
    authEndpoint[SaveFormParams, GeneralServiceException, SaveFormResponse](FormPath / "saveForm")

  lazy val saveFormMetadata: BaseAuthenticatedEndpoint[
    SaveFormMetadataParams,
    GeneralServiceException,
    SaveFormMetadataResponse
  ] =
    authEndpoint[
      SaveFormMetadataParams,
      GeneralServiceException,
      SaveFormMetadataResponse
    ](
      FormPath / "saveFormMetadata"
    )

  lazy val getForm: BaseAuthenticatedEndpoint[GetFormParams, GeneralServiceException, GetFormResponse] =
    authEndpoint[GetFormParams, GeneralServiceException, GetFormResponse](FormPath / "getForm")

  lazy val getAllForms: BaseAuthenticatedEndpoint[Unit, GeneralServiceException, GetAllFormModelsResponse] =
    authEndpoint[Unit, GeneralServiceException, GetAllFormModelsResponse](FormPath / "getAllForms")

  lazy val exportFormMatching: BaseAuthenticatedEndpoint[
    ExportFormMatchingParams,
    GeneralServiceException,
    ExportFormMatchingResponse
  ] =
    authEndpoint[
      ExportFormMatchingParams,
      GeneralServiceException,
      ExportFormMatchingResponse
    ](FormPath / "exportFormMatching")

  lazy val getAllFormsAnalytics
    : BaseAuthenticatedEndpoint[Unit, GeneralServiceException, GetAllFormsAnalyticsResponse] =
    authEndpoint[Unit, GeneralServiceException, GetAllFormsAnalyticsResponse](FormPath / "getAllFormsAnalytics")

  lazy val getAllVersions
    : BaseAuthenticatedEndpoint[GetAllVersionParams, GeneralServiceException, GetAllVersionResponse] =
    authEndpoint[GetAllVersionParams, GeneralServiceException, GetAllVersionResponse](FormPath / "getAllVersions")

  lazy val createFormVersion: BaseAuthenticatedEndpoint[
    CreateFormVersionParams,
    GeneralServiceException,
    CreateFormVersionResponse
  ] =
    authEndpoint[
      CreateFormVersionParams,
      GeneralServiceException,
      CreateFormVersionResponse
    ](
      FormPath / "createFormVersion"
    )

  lazy val editVersion: BaseAuthenticatedEndpoint[EditVersionParams, GeneralServiceException, Unit] =
    authEndpoint[EditVersionParams, GeneralServiceException, Unit](FormPath / "editVersion")

  lazy val getFormActivities: BaseAuthenticatedEndpoint[
    GetFormActivitiesParams,
    GeneralServiceException,
    GetFormActivitiesResponse
  ] =
    authEndpoint[
      GetFormActivitiesParams,
      GeneralServiceException,
      GetFormActivitiesResponse
    ](
      FormPath / "getFormActivities"
    )

  lazy val diffForm: BaseAuthenticatedEndpoint[DiffFormParams, GeneralServiceException, DiffFormResponse] =
    authEndpoint[DiffFormParams, GeneralServiceException, DiffFormResponse](
      FormPath / "diffForm"
    )

  lazy val diffFormFile: BaseAuthenticatedEndpoint[DiffFormFileParams, GeneralServiceException, DiffFormFileResponse] =
    authEndpoint[DiffFormFileParams, GeneralServiceException, DiffFormFileResponse](
      FormPath / "diffFormFile"
    )

  lazy val generatePdf: BaseAuthenticatedEndpoint[GeneratePdfParams, GeneralServiceException, GeneratePdfResponse] =
    authEndpoint[GeneratePdfParams, GeneralServiceException, GeneratePdfResponse](
      FormPath / "generatePdf"
    )

  lazy val getFileInfoToCut: BaseAuthenticatedEndpoint[
    GetFileInfoToCutParams,
    GeneralServiceException,
    GetFileInfoToCutResponse
  ] =
    authEndpoint[
      GetFileInfoToCutParams,
      GeneralServiceException,
      GetFileInfoToCutResponse
    ](
      FormPath / "getFileInfoToCut"
    )

  lazy val cutFile: BaseAuthenticatedEndpoint[CutFileParams, GeneralServiceException, CutFileResponse] =
    authEndpoint[CutFileParams, GeneralServiceException, CutFileResponse](
      FormPath / "cutFile"
    )

  lazy val getFormFileMetadata: BaseAuthenticatedEndpoint[
    GetFormFileMetadataParams,
    GeneralServiceException,
    GetFormFileMetadataResponse
  ] =
    authEndpoint[
      GetFormFileMetadataParams,
      GeneralServiceException,
      GetFormFileMetadataResponse
    ](
      FormPath / "getFormFileMetadata"
    )

  lazy val addFormStandardAliasMapping
    : BaseAuthenticatedEndpoint[AddFormStandardAliasMappingParams, GeneralServiceException, Unit] =
    authEndpoint[AddFormStandardAliasMappingParams, GeneralServiceException, Unit](
      FormPath / "addFormStandardAliasMapping"
    )

  lazy val removeFormStandardAliasMapping
    : BaseAuthenticatedEndpoint[RemoveFormStandardAliasMappingParams, GeneralServiceException, Unit] =
    authEndpoint[RemoveFormStandardAliasMappingParams, GeneralServiceException, Unit](
      FormPath / "removeFormStandardAliasMapping"
    )

  lazy val importFormStandardAliasMapping: BaseAuthenticatedEndpoint[
    AddFormStandardAliasMappingParams,
    GeneralServiceException,
    ImportFormStandardAliasMappingResponse
  ] =
    authEndpoint[
      AddFormStandardAliasMappingParams,
      GeneralServiceException,
      ImportFormStandardAliasMappingResponse
    ](
      FormPath / "importFormStandardAliasMapping"
    )

  lazy val getFormStandardAliasMapping: BaseAuthenticatedEndpoint[
    GetFormStandardAliasMappingParams,
    GeneralServiceException,
    Map[String, String]
  ] =
    authEndpoint[
      GetFormStandardAliasMappingParams,
      GeneralServiceException,
      Map[String, String]
    ](
      FormPath / "getFormStandardAliasMapping"
    )

  lazy val getAllStandardAliases: BaseAuthenticatedEndpoint[Unit, GeneralServiceException, List[StandardAlias]] =
    authEndpoint[Unit, GeneralServiceException, List[StandardAlias]](
      FormPath / "getAllStandardAliases"
    )

  lazy val exportAllPdfMappingsToCsv: BaseAuthenticatedEndpoint[
    ExportAllPdfMappingsToCsvParams,
    GeneralServiceException,
    ExportAllPdfMappingsToCsvResponse
  ] =
    authEndpoint[
      ExportAllPdfMappingsToCsvParams,
      GeneralServiceException,
      ExportAllPdfMappingsToCsvResponse
    ](
      FormPath / "exportAllPdfMappingsToCsv"
    )

  lazy val importSpreadsheetFile: BaseAuthenticatedEndpoint[
    ImportSpreadsheetFileParams,
    GeneralServiceException,
    ImportDataFileResponse
  ] =
    authEndpoint[
      ImportSpreadsheetFileParams,
      GeneralServiceException,
      ImportDataFileResponse
    ](
      FormPath / "importSpreadsheetFile"
    )

  lazy val importCsvFile
    : BaseAuthenticatedEndpoint[ImportCSVFileParams, GeneralServiceException, ImportDataFileResponse] =
    authEndpoint[ImportCSVFileParams, GeneralServiceException, ImportDataFileResponse](
      FormPath / "importCsvFile"
    )

  lazy val getDefaultImportTemplate: BaseAuthenticatedEndpoint[
    GenerateDefaultImportTemplateParams,
    GeneralServiceException,
    GenerateDefaultImportTemplateResponse
  ] =
    authEndpoint[
      GenerateDefaultImportTemplateParams,
      GeneralServiceException,
      GenerateDefaultImportTemplateResponse
    ](
      FormPath / "getDefaultImportTemplate"
    )

  lazy val getNamingRefineTemplate: BaseAuthenticatedEndpoint[
    GetNamingRefineTemplateParams,
    GeneralServiceException,
    GetNamingRefineTemplateResponse
  ] =
    authEndpoint[
      GetNamingRefineTemplateParams,
      GeneralServiceException,
      GetNamingRefineTemplateResponse
    ](
      FormPath / "getNamingRefineTemplate"
    )

  lazy val createFormFolder: BaseAuthenticatedEndpoint[
    CreateFormFolderParams,
    GeneralServiceException,
    CreateFormFolderResponse
  ] =
    authEndpoint[
      CreateFormFolderParams,
      GeneralServiceException,
      CreateFormFolderResponse
    ](
      FormPath / "createFormFolder"
    )

  lazy val updateFormFolder: BaseAuthenticatedEndpoint[
    UpdateFormFolderParams,
    GeneralServiceException,
    UpdateFormFolderResponse
  ] =
    authEndpoint[
      UpdateFormFolderParams,
      GeneralServiceException,
      UpdateFormFolderResponse
    ](
      FormPath / "updateFormFolder"
    )

  lazy val deleteFormFolder: BaseAuthenticatedEndpoint[
    DeleteFormFolderParams,
    GeneralServiceException,
    DeleteFormFolderResponse
  ] =
    authEndpoint[
      DeleteFormFolderParams,
      GeneralServiceException,
      DeleteFormFolderResponse
    ](
      FormPath / "deleteFormFolder"
    )

  lazy val archiveForm: BaseAuthenticatedEndpoint[ArchiveFormParams, GeneralServiceException, Unit] =
    authEndpoint[ArchiveFormParams, GeneralServiceException, Unit](
      FormPath / "archiveForm"
    )

  lazy val unarchiveForm: BaseAuthenticatedEndpoint[UnarchiveFormParams, GeneralServiceException, Unit] =
    authEndpoint[UnarchiveFormParams, GeneralServiceException, Unit](
      FormPath / "unarchiveForm"
    )

  lazy val getFormFolderContent: BaseAuthenticatedEndpoint[
    GetFormFolderContentParams,
    GeneralServiceException,
    GetFormFolderContentResponse
  ] =
    authEndpoint[
      GetFormFolderContentParams,
      GeneralServiceException,
      GetFormFolderContentResponse
    ](
      FormPath / "getFormFolderContent"
    )

  lazy val getAllFormFolderModels
    : BaseAuthenticatedEndpoint[Unit, GeneralServiceException, GetAllFormFolderModelsResponse] =
    authEndpoint[Unit, GeneralServiceException, GetAllFormFolderModelsResponse](
      FormPath / "getAllFormFolders"
    )

  lazy val getFormFolder
    : BaseAuthenticatedEndpoint[GetFormFolderParams, GeneralServiceException, GetFormFolderResponse] =
    authEndpoint[GetFormFolderParams, GeneralServiceException, GetFormFolderResponse](
      FormPath / "getFormFolder"
    )

  lazy val getComponentLibraryVersion
    : BaseAuthenticatedEndpoint[Unit, GeneralServiceException, GetComponentLibraryVersionResponse] =
    authEndpoint[Unit, GeneralServiceException, GetComponentLibraryVersionResponse](
      FormPath / "getComponentLibraryVersion"
    )

  lazy val setComponentLibraryVersion: BaseAuthenticatedEndpoint[
    SetComponentLibraryVersionParams,
    GeneralServiceException,
    SetComponentLibraryVersionResponse
  ] =
    authEndpoint[
      SetComponentLibraryVersionParams,
      GeneralServiceException,
      SetComponentLibraryVersionResponse
    ](
      FormPath / "setComponentLibraryVersion"
    )

  lazy val setFormVersionTag: BaseAuthenticatedEndpoint[
    SetFormVersionTagParams,
    GeneralServiceException,
    SetFormVersionTagResponse
  ] =
    authEndpoint[
      SetFormVersionTagParams,
      GeneralServiceException,
      SetFormVersionTagResponse
    ](
      FormPath / "setFormVersionTag"
    )

  lazy val getAllLibraryComponents
    : BaseAuthenticatedEndpoint[Unit, GeneralServiceException, GetAllLibraryComponentsResponse] =
    authEndpoint[Unit, GeneralServiceException, GetAllLibraryComponentsResponse](
      FormPath / "getAllLibraryComponents"
    )

  lazy val getAllInvestorAccessAsaValues
    : BaseAuthenticatedEndpoint[Unit, GeneralServiceException, GetAllInvestorAccessAsaValuesResponse] =
    authEndpoint[Unit, GeneralServiceException, GetAllInvestorAccessAsaValuesResponse](
      FormPath / "getAllInvestorAccessAsaValues"
    )

  lazy val copyFormFiles
    : BaseAuthenticatedEndpoint[CopyFormFilesParams, GeneralServiceException, CopyFormFilesResponse] =
    authEndpoint[CopyFormFilesParams, GeneralServiceException, CopyFormFilesResponse](
      FormPath / "copyFormFiles"
    )

  lazy val exportFormAsaMappingTemplate: BaseAuthenticatedEndpoint[
    ExportFormAsaMappingTemplateParams,
    GeneralServiceException,
    ExportFormAsaMappingTemplateResp
  ] =
    authEndpoint[
      ExportFormAsaMappingTemplateParams,
      GeneralServiceException,
      ExportFormAsaMappingTemplateResp
    ](
      FormPath / "exportFormAsaMappingTemplate"
    )

  lazy val processFormAsaMappingTemplate: BaseAuthenticatedEndpoint[
    ProcessFormAsaMappingTemplateParams,
    GeneralServiceException,
    ProcessFormAsaMappingTemplateResp
  ] =
    authEndpoint[
      ProcessFormAsaMappingTemplateParams,
      GeneralServiceException,
      ProcessFormAsaMappingTemplateResp
    ](
      FormPath / "processFormAsaMappingTemplate"
    )

  lazy val editFormAssociatedLinks: BaseAuthenticatedEndpoint[
    EditFormAssociatedLinksParams,
    GeneralServiceException,
    EditFormAssociatedLinksResponse
  ] =
    authEndpoint[
      EditFormAssociatedLinksParams,
      GeneralServiceException,
      EditFormAssociatedLinksResponse
    ](
      FormPath / "editFormAssociatedLinks"
    )

  lazy val getFormToolConfigs: BaseAuthenticatedEndpoint[
    GetFormToolConfigsParams,
    GeneralServiceException,
    GetFormToolConfigsResponse
  ] =
    authEndpoint[
      GetFormToolConfigsParams,
      GeneralServiceException,
      GetFormToolConfigsResponse
    ](
      FormPath / "getFormToolConfigs"
    )

  lazy val addFormToolConfig: BaseAuthenticatedEndpoint[
    AddFormToolConfigParams,
    GeneralServiceException,
    AddFormToolConfigsResponse
  ] =
    authEndpoint[
      AddFormToolConfigParams,
      GeneralServiceException,
      AddFormToolConfigsResponse
    ](
      FormPath / "addFormToolConfig"
    )

  lazy val updateFormToolConfig: BaseAuthenticatedEndpoint[
    UpdateFormToolConfigParams,
    GeneralServiceException,
    UpdateFormToolConfigsResponse
  ] =
    authEndpoint[
      UpdateFormToolConfigParams,
      GeneralServiceException,
      UpdateFormToolConfigsResponse
    ](
      FormPath / "updateFormToolConfig"
    )

  lazy val generateSignatures: BaseAuthenticatedEndpoint[
    GenerateSignaturesParams,
    GeneralServiceException,
    GenerateSignaturesResponse
  ] = authEndpoint[
    GenerateSignaturesParams,
    GeneralServiceException,
    GenerateSignaturesResponse
  ](FormPath / "generateSignatures")

  lazy val getFilePageSizes: BaseAuthenticatedEndpoint[
    GetFilePageSizesParams,
    GeneralServiceException,
    GetFilePageSizesResponse
  ] = authEndpoint[
    GetFilePageSizesParams,
    GeneralServiceException,
    GetFilePageSizesResponse
  ](FormPath / "getFilePageSizes")

  lazy val importFromAnnotationDocument: BaseAuthenticatedEndpoint[
    ImportFromAnnotationDocumentParams,
    GeneralServiceException,
    ImportFromAnnotationDocumentResponse
  ] = authEndpoint[
    ImportFromAnnotationDocumentParams,
    GeneralServiceException,
    ImportFromAnnotationDocumentResponse
  ](FormPath / "importFromAnnotationDocument")

  lazy val updateBlueprintRef: BaseAuthenticatedEndpoint[
    UpdateBlueprintRefParams,
    GeneralServiceException,
    UpdateBlueprintRefResponse
  ] = authEndpoint[
    UpdateBlueprintRefParams,
    GeneralServiceException,
    UpdateBlueprintRefResponse
  ](FormPath / "updateBlueprintRef")

  lazy val removeBlueprintRef: BaseAuthenticatedEndpoint[
    RemoveBlueprintRefParams,
    GeneralServiceException,
    RemoveBlueprintRefResponse
  ] = authEndpoint[
    RemoveBlueprintRefParams,
    GeneralServiceException,
    RemoveBlueprintRefResponse
  ](FormPath / "removeBlueprintRef")

  lazy val diffFormBlueprint: BaseAuthenticatedEndpoint[
    DiffFormBlueprintParams,
    GeneralServiceException,
    DiffFormBlueprintResponse
  ] = authEndpoint[
    DiffFormBlueprintParams,
    GeneralServiceException,
    DiffFormBlueprintResponse
  ](FormPath / "diffFormBlueprint")

  lazy val getFormCueModule: BaseAuthenticatedEndpoint[
    GetFormCueModuleParams,
    GeneralServiceException,
    GetFormCueModuleResponse
  ] = authEndpoint[
    GetFormCueModuleParams,
    GeneralServiceException,
    GetFormCueModuleResponse
  ](FormPath / "getFormCueModule")

  lazy val generateFormModule: BaseAuthenticatedEndpoint[
    GenerateFormCueModuleParams,
    GeneralServiceException,
    EmptyResponse
  ] = authEndpoint[GenerateFormCueModuleParams, GeneralServiceException, EmptyResponse](FormPath / "generateFormModule")

  lazy val extractDataLayerDefaultValues: BaseAuthenticatedEndpoint[
    ExtractDataLayerDefaultValuesParams,
    GeneralServiceException,
    ExtractDataLayerDefaultValuesResponse
  ] = authEndpoint[
    ExtractDataLayerDefaultValuesParams,
    GeneralServiceException,
    ExtractDataLayerDefaultValuesResponse
  ](FormPath / "extractDataLayerDefaultValues")

  lazy val verifyLinkedCueVersion: BaseAuthenticatedEndpoint[
    VerifyLinkedCueVersionParams,
    GeneralServiceException,
    EmptyResponse
  ] = authEndpoint[
    VerifyLinkedCueVersionParams,
    GeneralServiceException,
    EmptyResponse
  ](FormPath / "verifyLinkedCueVersion")

  lazy val getFormDataUserTemp: BaseAuthenticatedEndpoint[
    GetFormDataUserTempParams,
    GeneralServiceException,
    GaiaState
  ] = authEndpoint[
    GetFormDataUserTempParams,
    GeneralServiceException,
    GaiaState
  ](FormPath / "getFormDataUserTemp")

}
