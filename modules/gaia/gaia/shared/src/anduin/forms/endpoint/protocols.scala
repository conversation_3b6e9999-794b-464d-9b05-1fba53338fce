// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.endpoint

import java.time.Instant

import io.circe.{<PERSON><PERSON>, <PERSON><PERSON>}
import squants.information.Information
import zio.Chunk
import anduin.circe.generic.semiauto.CirceCodec

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.formmatching.FormMatchingMode
import anduin.forms.Form.FormNamespace
import anduin.forms.`import`.FormImportData
import anduin.forms.annotation.{Area2D, HashType, PdfObjectId, PdfObjects}
import anduin.forms.engine.GaiaState
import anduin.forms.logic.extractor.ExtractorResult
import anduin.forms.model.FormModel.FundEngagementType
import anduin.forms.model.activity.FormActivityModel
import anduin.forms.model.annotation.AnnotationDocumentModels.AnnotationDocumentData
import anduin.forms.model.formfolder.FormFolderModel
import anduin.forms.model.formmapping.{FormInfo, FormMappingUseCase, SameAliasMappingNamespacePair}
import anduin.forms.model.formmeta.{FormForFile, FormForInvestorProfile}
import anduin.forms.model.template.DataTemplateModels.{DataTemplateModel, DataTemplateType, DataTemplateVersionModel}
import anduin.forms.model.toolconfig.{FormToolConfigModel, FormToolConfigProperty}
import anduin.forms.model.{AssociatedLink, FormModel, Schema}
import anduin.forms.rules.FormRule
import anduin.forms.ui.{Widget, WidgetType}
import anduin.forms.utils.PdfAnnotationToolUtils.SelectedTextArea
import anduin.forms.version.*
import anduin.forms.version.FormVersionMetadataModel.{PdfCutInfo, WidgetSourceSummary}
import anduin.forms.version.FormVersionModel.FormType
import anduin.forms.{ExtractedPdf, Form, FormData, FormFieldState}
import anduin.id.annotation.AnnotationDocumentVersionId
import anduin.id.blueprint.{BlueprintId, BlueprintVersionId}
import anduin.id.form.*
import anduin.id.fundsub.FundSubFormIdTrait
import anduin.model.codec.EitherCodec.given
import anduin.model.codec.MapCodecs.given
import anduin.model.codec.ProtoCodecs.given
import anduin.model.codec.SquantCodec.given
import anduin.model.common.FieldAlias
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.{AssetSessionId, FileId}
import anduin.protobuf.signature.{DocumentSignatureMessage, SignatureMessage}
import anduin.cue.model.CueModuleSharedModels.CueModule
import anduin.id.cue.CueModuleVersionId
import anduin.cue.model.datalayer.DataLayerDefaultValue

final case class ProcessFileRequest(
  fileIds: Seq[FileId]
)

object ProcessFileRequest {
  given Codec.AsObject[ProcessFileRequest] = deriveCodecWithDefaults
}

final case class ProcessFileResponse(
  files: Map[FileId, ExtractedPdf]
)

object ProcessFileResponse {
  given Codec.AsObject[ProcessFileResponse] = deriveCodecWithDefaults
}

final case class ExtractTextRequest(
  fileIds: Seq[FileId]
)

object ExtractTextRequest {
  given Codec.AsObject[ExtractTextRequest] = deriveCodecWithDefaults
}

final case class ExtractTextResponse(
  texts: Map[FileId, Seq[String]]
)

object ExtractTextResponse {
  given Codec.AsObject[ExtractTextResponse] = deriveCodecWithDefaults
}

final case class CreateFormParams(
  formName: String,
  tags: Set[String],
  owners: Seq[EmailAddress],
  parentFolderIdOpt: Option[FormFolderId] = None,
  fundEngagementTypeOpt: Option[FundEngagementType] = None,
  numFundraiseOpt: Option[Int] = None
)

object CreateFormParams {
  given Codec.AsObject[CreateFormParams] = deriveCodecWithDefaults
}

final case class CreateFormResponse(
  formVersionId: FormVersionId,
  model: FormModel
) {
  val formId: FormId = formVersionId.parent
}

object CreateFormResponse {
  given Codec.AsObject[CreateFormResponse] = deriveCodecWithDefaults
}

final case class CreateFormFolderParams(
  formFolderName: String,
  parentFolderId: FormFolderId,
  tags: Set[String]
)

object CreateFormFolderParams {
  given Codec.AsObject[CreateFormFolderParams] = deriveCodecWithDefaults
}

final case class CreateFormFolderResponse(
  model: FormFolderModel
)

object CreateFormFolderResponse {
  given Codec.AsObject[CreateFormFolderResponse] = deriveCodecWithDefaults
}

final case class EditFormParams(
  formId: FormId,
  formName: String,
  tags: Set[String],
  owners: Seq[EmailAddress],
  parentFolderIdOpt: Option[FormFolderId] = None,
  fundEngagementTypeOpt: Option[FundEngagementType] = None,
  numFundraiseOpt: Option[Int] = None
)

object EditFormParams {
  given Codec.AsObject[EditFormParams] = deriveCodecWithDefaults
}

final case class EditFormResponse(
  formModel: FormModel
)

object EditFormResponse {
  given Codec.AsObject[EditFormResponse] = deriveCodecWithDefaults
}

final case class DuplicateFormParams(
  sourceFormVersionId: FormVersionId,
  formName: String,
  tags: Set[String],
  owners: Seq[EmailAddress],
  sourceUrl: String,
  parentFolderIdOpt: Option[FormFolderId] = None,
  fundEngagementTypeOpt: Option[FundEngagementType] = None,
  numFundraiseOpt: Option[Int] = None
)

object DuplicateFormParams {
  given Codec.AsObject[DuplicateFormParams] = deriveCodecWithDefaults
}

final case class DuplicateFormResponse(
  model: FormModel
)

object DuplicateFormResponse {
  given Codec.AsObject[DuplicateFormResponse] = deriveCodecWithDefaults
}

final case class SaveFormParams(
  formId: FormId,
  sessionId: AssetSessionId,
  formData: FormData,
  parentVersionIdOpt: Option[FormVersionId],
  name: String,
  metadataOpt: Option[(FormVersionMetadataModel, FormVersionSystemMetadataModel)],
  formTypeOpt: Option[FormType]
)

object SaveFormParams {
  given Codec.AsObject[SaveFormParams] = deriveCodecWithDefaults
}

final case class SaveFormMetadataParams(
  formVersionId: FormVersionId,
  sessionId: AssetSessionId,
  metadata: (FormVersionMetadataModel, FormVersionSystemMetadataModel)
)

object SaveFormMetadataParams {
  given Codec.AsObject[SaveFormMetadataParams] = deriveCodecWithDefaults
}

final case class SaveFormResult(
  draftVersion: FormVersionModel,
  lastSavedBy: Option[EmailAddress],
  lastSavedAt: Option[Instant],
  checkData: FormCheckUpToDateData
)

object SaveFormResult {
  given Codec.AsObject[SaveFormResult] = deriveCodecWithDefaults
}

sealed trait GetLockResult derives CanEqual

object GetLockResult {
  given Codec.AsObject[GetLockResult] = deriveCodecWithDefaults

  final case class Granted(expiredAt: Instant) extends GetLockResult

  object Granted {
    given Codec.AsObject[Granted] = deriveCodecWithDefaults
  }

  final case class AssetLocked(lockedUser: UserId, lockedSession: Option[AssetSessionId], lockedUserInfo: UserInfo)
      extends GetLockResult

  object AssetLocked {
    given Codec.AsObject[AssetLocked] = deriveCodecWithDefaults
  }

  case object AssetOutdated extends GetLockResult

  given Codec.AsObject[AssetOutdated.type] = deriveCodecWithDefaults

}

sealed trait RenewLockResult derives CanEqual

object RenewLockResult {
  given Codec.AsObject[RenewLockResult] = deriveCodecWithDefaults

  final case class Granted(expiredAt: Instant) extends RenewLockResult

  object Granted {
    given Codec.AsObject[Granted] = deriveCodecWithDefaults
  }

  final case class AssetLocked(lockedUser: UserId, lockedSession: Option[AssetSessionId], lockedUserInfo: UserInfo)
      extends RenewLockResult

  object AssetLocked {
    given Codec.AsObject[AssetLocked] = deriveCodecWithDefaults
  }

  case object NoLockFound extends RenewLockResult

  given Codec.AsObject[NoLockFound.type] = deriveCodecWithDefaults

}

final case class FormCheckUpToDateData(
  latestVersionIdOpt: Option[FormVersionId] = None,
  latestDraftCreatedOpt: Option[Instant] = None
) derives CanEqual

object FormCheckUpToDateData {
  given Codec.AsObject[FormCheckUpToDateData] = deriveCodecWithDefaults
}

final case class StartEditFormParams(
  formId: FormId,
  checkData: FormCheckUpToDateData,
  sessionID: AssetSessionId
)

object StartEditFormParams {
  given Codec.AsObject[StartEditFormParams] = deriveCodecWithDefaults
}

final case class StartEditFormResponse(
  lockResult: GetLockResult
)

object StartEditFormResponse {
  given Codec.AsObject[StartEditFormResponse] = deriveCodecWithDefaults
}

final case class GetFormLockParams(
  formId: FormId,
  sessionID: AssetSessionId
)

object GetFormLockParams {
  given Codec.AsObject[GetFormLockParams] = deriveCodecWithDefaults
}

final case class GetFormLockResponse(
  lockResult: GetLockResult
)

object GetFormLockResponse {
  given Codec.AsObject[GetFormLockResponse] = deriveCodecWithDefaults
}

final case class RenewFormLockParams(
  formId: FormId,
  sessionID: AssetSessionId
)

object RenewFormLockParams {
  given Codec.AsObject[RenewFormLockParams] = deriveCodecWithDefaults
}

final case class RenewFormLockResponse(
  lockResult: RenewLockResult
)

object RenewFormLockResponse {
  given Codec.AsObject[RenewFormLockResponse] = deriveCodecWithDefaults
}

final case class ReleaseFormLockParams(
  formId: FormId,
  sessionID: AssetSessionId
)

object ReleaseFormLockParams {
  given Codec.AsObject[ReleaseFormLockParams] = deriveCodecWithDefaults
}

sealed trait EditFormDataError derives CanEqual

object EditFormDataError {
  given Codec.AsObject[EditFormDataError] = deriveCodecWithDefaults

  case object NoLockFound extends EditFormDataError
  given Codec.AsObject[NoLockFound.type] = deriveCodecWithDefaults

}

final case class SaveFormResponse(
  result: Either[EditFormDataError, SaveFormResult]
)

object SaveFormResponse {
  given Codec.AsObject[SaveFormResponse] = deriveCodecWithDefaults
}

final case class SaveFormMetadataResponse(
  result: Either[EditFormDataError, Unit]
)

object SaveFormMetadataResponse {
  given Codec.AsObject[SaveFormMetadataResponse] = deriveCodecWithDefaults
}

final case class GetFormParams(
  formId: FormId,
  versionIdOpt: Option[FormVersionId]
)

object GetFormParams {
  given Codec.AsObject[GetFormParams] = deriveCodecWithDefaults
}

final case class GetFormResponse(
  formModel: FormModel,
  formData: FormData,
  versionOpt: Option[FormVersionModel],
  editingUserOpt: Option[UserId],
  checkData: FormCheckUpToDateData,
  metadata: FormVersionMetadataModel,
  systemMetadata: FormVersionSystemMetadataModel,
  draftVersionId: FormVersionId,
  userInfoMap: Map[UserId, UserInfo]
) derives CanEqual

object GetFormResponse {
  given Codec.AsObject[GetFormResponse] = deriveCodecWithDefaults
}

final case class GetFormCueModuleParams(
  formId: FormId
) derives CanEqual,
      CirceCodec.WithDefaults

final case class GetFormCueModuleResponse(
  cueModuleOpt: Option[CueModule]
) derives CanEqual,
      CirceCodec.WithDefaults

final case class GenerateFormCueModuleParams(
  formId: FormId
) derives CanEqual,
      CirceCodec.WithDefaults

final case class GetFormFolderParams(
  formFolderId: FormFolderId
)

final case class ExtractDataLayerDefaultValuesParams(
  input: Either[CueModuleVersionId, (FormId, Option[FormVersionId])]
) derives CanEqual,
      CirceCodec.WithDefaults

final case class ExtractDataLayerDefaultValuesResponse(
  defaultValue: DataLayerDefaultValue
) derives CanEqual,
      CirceCodec.WithDefaults

object GetFormFolderParams {
  given Codec.AsObject[GetFormFolderParams] = deriveCodecWithDefaults
}

final case class GetFormFolderResponse(
  formFolderModel: FormFolderModel
)

object GetFormFolderResponse {
  given Codec.AsObject[GetFormFolderResponse] = deriveCodecWithDefaults
}

final case class GetAllFormModelsResponse(
  forms: List[FormModel]
)

object GetAllFormModelsResponse {
  given Codec.AsObject[GetAllFormModelsResponse] = deriveCodecWithDefaults
}

final case class GetAllFormsAnalyticsResponse(
  forms: Seq[(FormModel, Option[WidgetSourceSummary])]
)

object GetAllFormsAnalyticsResponse {
  given Codec.AsObject[GetAllFormsAnalyticsResponse] = deriveCodecWithDefaults
}

final case class GetAllVersionParams(
  formId: FormId
)

object GetAllVersionParams {
  given Codec.AsObject[GetAllVersionParams] = deriveCodecWithDefaults
}

final case class GetAllVersionResponse(
  formModel: FormModel,
  versions: Seq[FormVersionModel]
)

object GetAllVersionResponse {
  given Codec.AsObject[GetAllVersionResponse] = deriveCodecWithDefaults
}

final case class CreateFormVersionParams(
  formId: FormId,
  sessionId: AssetSessionId,
  description: String,
  versionNote: String
)

object CreateFormVersionParams {
  given Codec.AsObject[CreateFormVersionParams] = deriveCodecWithDefaults
}

final case class CreateFormVersionResult(
  versionId: FormVersionId,
  versionNumber: Int,
  checkData: FormCheckUpToDateData
)

object CreateFormVersionResult {
  given Codec.AsObject[CreateFormVersionResult] = deriveCodecWithDefaults
}

final case class CreateFormVersionResponse(
  result: Either[EditFormDataError, CreateFormVersionResult]
)

object CreateFormVersionResponse {
  given Codec.AsObject[CreateFormVersionResponse] = deriveCodecWithDefaults
}

final case class EditVersionParams(
  formId: FormId,
  formVersionIdOpt: Option[FormVersionId],
  name: String,
  note: String
)

object EditVersionParams {
  given Codec.AsObject[EditVersionParams] = deriveCodecWithDefaults
}

final case class GetFormActivitiesParams(
  formId: FormId
)

object GetFormActivitiesParams {
  given Codec.AsObject[GetFormActivitiesParams] = deriveCodecWithDefaults
}

final case class GetFormActivitiesResponse(
  formActivities: List[FormActivityModel],
  userMap: Map[UserId, EmailAddress]
)

object GetFormActivitiesResponse {
  given Codec.AsObject[GetFormActivitiesResponse] = deriveCodecWithDefaults
}

final case class DiffFormParams(
  formId: FormId,
  firstVersionOpt: Option[FormVersionModel],
  secondVersionOpt: Option[FormVersionModel]
)

object DiffFormParams {
  given Codec.AsObject[DiffFormParams] = deriveCodecWithDefaults
}

final case class DiffFormResponse(
  firstVersionFormData: FormData,
  secondVersionFormData: FormData
)

object DiffFormResponse {
  given Codec.AsObject[DiffFormResponse] = deriveCodecWithDefaults
}

final case class DiffFormFileParams(
  formId: FormId,
  firstVersionIdOpt: Option[FormVersionId],
  secondVersionIdOpt: Option[FormVersionId]
)

object DiffFormFileParams {
  given Codec.AsObject[DiffFormFileParams] = deriveCodecWithDefaults
}

final case class DiffFormFileResponse(
  firstVersionData: Map[FileId, (String, AnnotationDocumentData)],
  secondVersionData: Map[FileId, (String, AnnotationDocumentData)]
)

object DiffFormFileResponse {
  given Codec.AsObject[DiffFormFileResponse] = deriveCodecWithDefaults
}

final case class GeneratePdfParams(
  formId: FormId,
  formData: FormData,
  formStates: Map[String, FormFieldState]
)

object GeneratePdfParams {
  given Codec.AsObject[GeneratePdfParams] = deriveCodecWithDefaults
}

final case class GeneratePdfResponse(
  files: Seq[(String, FileId)]
)

object GeneratePdfResponse {
  given Codec.AsObject[GeneratePdfResponse] = deriveCodecWithDefaults
}

final case class CompareFormParams(
  oldVersion: FormVersionModel,
  newVersion: FormVersionModel
)

object CompareFormParams {
  given Codec.AsObject[CompareFormParams] = deriveCodecWithDefaults
}

final case class CompareFormResponse(
  oldFormData: FormData,
  newFormData: FormData
)

object CompareFormResponse {
  given Codec.AsObject[CompareFormResponse] = deriveCodecWithDefaults
}

final case class GetFormFileMetadataParams(
  fileIds: Seq[FileId]
)

object GetFormFileMetadataParams {
  given Codec.AsObject[GetFormFileMetadataParams] = deriveCodecWithDefaults
}

final case class GetFormFileMetadataResponse(
  fileMetadata: Map[FileId, FileMetadata]
)

object GetFormFileMetadataResponse {
  given Codec.AsObject[GetFormFileMetadataResponse] = deriveCodecWithDefaults
}

final case class FileMetadata(
  createdBy: String,
  createdAt: Option[Instant],
  fileSize: Information
)

object FileMetadata {
  given Codec.AsObject[FileMetadata] = deriveCodecWithDefaults
}

final case class GetFileInfoToCutParams(
  fileId: FileId,
  formId: FormId
)

object GetFileInfoToCutParams {
  given Codec.AsObject[GetFileInfoToCutParams] = deriveCodecWithDefaults
}

final case class GetFileInfoToCutResponse(
  url: String,
  pageCount: Int
)

object GetFileInfoToCutResponse {
  given Codec.AsObject[GetFileInfoToCutResponse] = deriveCodecWithDefaults
}

final case class CutFileParams(
  fileId: FileId,
  formId: FormId,
  start: Int,
  end: Int,
  fileName: String
)

object CutFileParams {
  given Codec.AsObject[CutFileParams] = deriveCodecWithDefaults
}

final case class CutFileResponse(
  fileId: FileId
)

object CutFileResponse {
  given Codec.AsObject[CutFileResponse] = deriveCodecWithDefaults
}

final case class CreateFormMappingParams(
  name: String,
  mappingDefaultNamespace: FormNamespace = Form.DefaultNamespace,
  formInfoList: List[FormInfo] = List.empty,
  useCaseType: FormMappingUseCase = FormMappingUseCase.Empty,
  sameAliasMappingNamespacePairs: List[SameAliasMappingNamespacePair] = List.empty,
  additionalMappingAliases: Map[FieldAlias, Schema] = Map.empty,
  mappingRules: List[FormRule] = List.empty
)

object CreateFormMappingParams {
  given Codec.AsObject[CreateFormMappingParams] = deriveCodecWithDefaults
}

final case class RenameFormMappingParams(
  formMappingId: FormMappingId,
  newName: String
)

object RenameFormMappingParams {
  given Codec.AsObject[RenameFormMappingParams] = deriveCodecWithDefaults
}

final case class ImportFormIntoMappingParams(
  formMappingId: FormMappingId,
  formVersionId: FormVersionId,
  namespaceMap: Map[String, String]
)

object ImportFormIntoMappingParams {
  given Codec.AsObject[ImportFormIntoMappingParams] = deriveCodecWithDefaults
}

final case class RemoveFormsFromMappingParams(
  formMappingId: FormMappingId,
  removedFormVersionIds: Set[FormVersionId]
)

object RemoveFormsFromMappingParams {
  given Codec.AsObject[RemoveFormsFromMappingParams] = deriveCodecWithDefaults
}

final case class UpdateSameAliasMappingNamespacePairsParams(
  formMappingId: FormMappingId,
  pairsToAdd: List[SameAliasMappingNamespacePair],
  pairsToRemove: Set[SameAliasMappingNamespacePair]
)

object UpdateSameAliasMappingNamespacePairsParams {
  given Codec.AsObject[UpdateSameAliasMappingNamespacePairsParams] = deriveCodecWithDefaults
}

final case class UpdateMappingAdditionalAliasesParams(
  formMappingId: FormMappingId,
  aliasesToAdd: Map[FieldAlias, Schema],
  aliasesToRemove: Set[FieldAlias]
)

object UpdateMappingAdditionalAliasesParams {
  given Codec.AsObject[UpdateMappingAdditionalAliasesParams] = deriveCodecWithDefaults
}

final case class UpdateMappingRulesParams(
  formMappingId: FormMappingId,
  rulesToAdd: Seq[FormRule],
  rulesToRemove: Set[FormRule]
)

object UpdateMappingRulesParams {
  given Codec.AsObject[UpdateMappingRulesParams] = deriveCodecWithDefaults
}

final case class UpdateMappingUseCaseParams(
  formMappingId: FormMappingId,
  newMappingUseCase: FormMappingUseCase
)

object UpdateMappingUseCaseParams {
  given Codec.AsObject[UpdateMappingUseCaseParams] = deriveCodecWithDefaults
}

final case class CreateFormMetaForFileParams(
  formVersionId: FormVersionId,
  formMeta: FormForFile
)

object CreateFormMetaForFileParams {
  given Codec.AsObject[CreateFormMetaForFileParams] = deriveCodecWithDefaults
}

final case class CreateFormMetaForInvestorProfileParams(
  formVersionId: FormVersionId,
  formMeta: FormForInvestorProfile
)

object CreateFormMetaForInvestorProfileParams {
  given Codec.AsObject[CreateFormMetaForInvestorProfileParams] = deriveCodecWithDefaults
}

final case class ApplyMappingDataResponse(
  gaiaState: GaiaState
) {

  lazy val namespaceValueMap: Map[FormNamespace, Map[String, Json]] = gaiaState.stateMap.map {
    case (namespace, singleState) =>
      namespace -> singleState.flatMap { case (k, v) =>
        v.validate().map(k -> _.getValue)
      }
  }

}

object ApplyMappingDataResponse {
  given Codec.AsObject[ApplyMappingDataResponse] = deriveCodecWithDefaults
}

final case class GetAllFormIdPublicParams(
  sharedSecret: String
)

object GetAllFormIdPublicParams {
  given Codec.AsObject[GetAllFormIdPublicParams] = deriveCodecWithDefaults
}

final case class GetAllFormIdPublicResponse(
  forms: Seq[FormId]
)

object GetAllFormIdPublicResponse {
  given Codec.AsObject[GetAllFormIdPublicResponse] = deriveCodecWithDefaults
}

final case class LoadFieldsRequest(
  fileId: FileId,
  needStrippedPdf: Boolean = false
)

object LoadFieldsRequest {
  given Codec.AsObject[LoadFieldsRequest] = deriveCodecWithDefaults
}

final case class LoadFieldsResponse(
  name: String,
  strippedFileId: FileId,
  totalPages: Int,
  pdfObjects: PdfObjects
)

object LoadFieldsResponse {
  given Codec.AsObject[LoadFieldsResponse] = deriveCodecWithDefaults
}

final case class SaveFieldsRequest(
  fileId: FileId,
  pdfObjects: PdfObjects
)

object SaveFieldsRequest {
  given Codec.AsObject[SaveFieldsRequest] = deriveCodecWithDefaults
}

final case class SaveFieldsResponse(fileId: FileId)

object SaveFieldsResponse {
  given Codec.AsObject[SaveFieldsResponse] = deriveCodecWithDefaults
}

final case class GetTextInAreasRequest(fileId: FileId, pageAreas: Seq[(Int, Area2D)])

object GetTextInAreasRequest {
  given Codec.AsObject[GetTextInAreasRequest] = deriveCodecWithDefaults
}

final case class GetTextInAreasResponse(
  content: String
)

object GetTextInAreasResponse {
  given Codec.AsObject[GetTextInAreasResponse] = deriveCodecWithDefaults
}

final case class GetPdfFileContentHashRequest(fileId: FileId)

object GetPdfFileContentHashRequest {
  given Codec.AsObject[GetPdfFileContentHashRequest] = deriveCodecWithDefaults
}

final case class GetPdfFileContentHashResponse(
  hash: String,
  hashType: HashType
)

object GetPdfFileContentHashResponse {
  given Codec.AsObject[GetPdfFileContentHashResponse] = deriveCodecWithDefaults
}

final case class AddFormStandardAliasMappingParams(
  id: FormVersionId,
  aliasMappingToAdd: Map[String, String]
)

object AddFormStandardAliasMappingParams {
  given Codec.AsObject[AddFormStandardAliasMappingParams] = deriveCodecWithDefaults
}

final case class ImportFormStandardAliasMappingResponse(
  addedMapping: Map[String, String]
)

object ImportFormStandardAliasMappingResponse {
  given Codec.AsObject[ImportFormStandardAliasMappingResponse] = deriveCodecWithDefaults
}

final case class RemoveFormStandardAliasMappingParams(
  id: FormVersionId,
  aliasesToRemove: Set[String]
)

object RemoveFormStandardAliasMappingParams {
  given Codec.AsObject[RemoveFormStandardAliasMappingParams] = deriveCodecWithDefaults
}

final case class GetFormStandardAliasMappingParams(
  id: FundSubFormIdTrait,
  checkExistingFormAlias: Boolean
)

object GetFormStandardAliasMappingParams {
  given Codec.AsObject[GetFormStandardAliasMappingParams] = deriveCodecWithDefaults
}

final case class GetFormStandardAliasMappingResponse(
  aliasMapping: Map[String, String],
  lastUpdatedAt: Instant
)

object GetFormStandardAliasMappingResponse {
  given Codec.AsObject[GetFormStandardAliasMappingResponse] = deriveCodecWithDefaults
}

final case class StandardAlias(
  alias: String,
  forFundSubWorkflowSetup: Boolean,
  forFieldValue: Boolean = false,
  translation: String = "",
  jsonType: String = "",
  widgetType: String = "",
  section: String = "",
  description: String = "",
  parentAlias: String = ""
)

object StandardAlias {
  given Codec.AsObject[StandardAlias] = deriveCodecWithDefaults
}

final case class MatchedValue(
  alias: String,
  value: String
) derives CanEqual {

  val text = s"$alias${MatchedValueCompanion.AliasValueSeparator}$value"
}

object MatchedValue {
  given Codec.AsObject[MatchedValue] = deriveCodecWithDefaults
}

case object MatchedValueCompanion {
  val AliasValueSeparator = "#"

  def fromText(input: String): Option[MatchedValue] = {
    input.split(AliasValueSeparator).toList match {
      case head :: tail =>
        if (tail.nonEmpty) {
          Option(MatchedValue(head, tail.mkString(AliasValueSeparator)))
        } else {
          None
        }
      case _ => None
    }
  }

}

final case class ExportAllPdfMappingsToCsvParams(
  formId: FormId,
  formName: String,
  formData: FormData
)

object ExportAllPdfMappingsToCsvParams {
  given Codec.AsObject[ExportAllPdfMappingsToCsvParams] = deriveCodecWithDefaults
}

final case class ExportAllPdfMappingsToCsvResponse(
  fileId: FileId
)

object ExportAllPdfMappingsToCsvResponse {
  given Codec.AsObject[ExportAllPdfMappingsToCsvResponse] = deriveCodecWithDefaults
}

final case class ImportSpreadsheetFileParams(
  formId: FormId,
  formVersionIdOpt: Option[FormVersionId],
  fileId: FileId,
  aliasRowIndex: Int
)

object ImportSpreadsheetFileParams {
  given Codec.AsObject[ImportSpreadsheetFileParams] = deriveCodecWithDefaults
}

final case class ImportCSVFileParams(
  formId: FormId,
  formVersionIdOpt: Option[FormVersionId],
  fileId: FileId,
  aliasRowIndex: Int,
  separator: Char = ',',
  stringDelimiter: Char = '"'
)

object ImportCSVFileParams {
  given Codec.AsObject[ImportCSVFileParams] = deriveCodecWithDefaults
}

final case class ImportDataFileResponse(
  data: Seq[FormImportData]
)

object ImportDataFileResponse {
  given Codec.AsObject[ImportDataFileResponse] = deriveCodecWithDefaults
}

final case class GenerateDefaultImportTemplateParams(
  formId: FormId,
  formVersionIdOpt: Option[FormVersionId]
)

object GenerateDefaultImportTemplateParams {
  given Codec.AsObject[GenerateDefaultImportTemplateParams] = deriveCodecWithDefaults
}

final case class GenerateDefaultImportTemplateResponse(
  fileId: FileId
)

object GenerateDefaultImportTemplateResponse {
  given Codec.AsObject[GenerateDefaultImportTemplateResponse] = deriveCodecWithDefaults
}

final case class GetAllTemplateResponse(templates: Seq[DataTemplateModel])

object GetAllTemplateResponse {
  given Codec.AsObject[GetAllTemplateResponse] = deriveCodecWithDefaults
}

final case class GetNamingRefineTemplateParams(
  formId: FormId,
  formVersionIdOpt: Option[FormVersionId],
  includeCsaMappingInfo: Boolean = false
)

object GetNamingRefineTemplateParams {
  given Codec.AsObject[GetNamingRefineTemplateParams] = deriveCodecWithDefaults
}

final case class GetNamingRefineTemplateResponse(
  fileId: FileId
)

object GetNamingRefineTemplateResponse {
  given Codec.AsObject[GetNamingRefineTemplateResponse] = deriveCodecWithDefaults
}

final case class CreateTemplateParams(name: String)

object CreateTemplateParams {
  given Codec.AsObject[CreateTemplateParams] = deriveCodecWithDefaults
}

final case class CreateTemplateResponse(templateId: DataTemplateId)

object CreateTemplateResponse {
  given Codec.AsObject[CreateTemplateResponse] = deriveCodecWithDefaults
}

final case class UpdateTemplateNameParams(templateId: DataTemplateId, name: String)

object UpdateTemplateNameParams {
  given Codec.AsObject[UpdateTemplateNameParams] = deriveCodecWithDefaults
}

final case class UpdateTemplateNameResponse(templateModel: DataTemplateModel)

object UpdateTemplateNameResponse {
  given Codec.AsObject[UpdateTemplateNameResponse] = deriveCodecWithDefaults
}

final case class GetTemplateParams(templateId: DataTemplateId)

object GetTemplateParams {
  given Codec.AsObject[GetTemplateParams] = deriveCodecWithDefaults
}

final case class GetTemplateResponse(templateModel: DataTemplateModel)

object GetTemplateResponse {
  given Codec.AsObject[GetTemplateResponse] = deriveCodecWithDefaults
}

final case class GetTemplateVersionsParams(templateId: DataTemplateId)

object GetTemplateVersionsParams {
  given Codec.AsObject[GetTemplateVersionsParams] = deriveCodecWithDefaults
}

final case class GetTemplateVersionsResponse(versions: Seq[DataTemplateVersionModel], templateModel: DataTemplateModel)

object GetTemplateVersionsResponse {
  given Codec.AsObject[GetTemplateVersionsResponse] = deriveCodecWithDefaults
}

final case class CreateTemplateVersionParams(
  templateId: DataTemplateId,
  templateType: DataTemplateType,
  description: String
)

object CreateTemplateVersionParams {
  given Codec.AsObject[CreateTemplateVersionParams] = deriveCodecWithDefaults
}

final case class CreateTemplateVersionResponse(versionModel: DataTemplateVersionModel)

object CreateTemplateVersionResponse {
  given Codec.AsObject[CreateTemplateVersionResponse] = deriveCodecWithDefaults
}

final case class UpdateTemplateVersionDescriptionParams(
  versionId: DataTemplateVersionId,
  description: String
)

object UpdateTemplateVersionDescriptionParams {
  given Codec.AsObject[UpdateTemplateVersionDescriptionParams] = deriveCodecWithDefaults
}

final case class UpdateTemplateVersionDescriptionResponse(version: DataTemplateVersionModel)

object UpdateTemplateVersionDescriptionResponse {
  given Codec.AsObject[UpdateTemplateVersionDescriptionResponse] = deriveCodecWithDefaults
}

final case class UpdateFormFolderInfoParams(
  formFolderId: FormFolderId,
  name: String,
  tags: Set[String]
)

object UpdateFormFolderInfoParams {
  given Codec.AsObject[UpdateFormFolderInfoParams] = deriveCodecWithDefaults
}

final case class GetFormFolderContentParams(formFolderId: FormFolderId)

object GetFormFolderContentParams {
  given Codec.AsObject[GetFormFolderContentParams] = deriveCodecWithDefaults
}

final case class GetFormFolderContentResponse(
  formFolderModel: FormFolderModel,
  formFolderSeq: Seq[FormFolderModel],
  formSeq: Seq[FormModel]
)

object GetFormFolderContentResponse {
  given Codec.AsObject[GetFormFolderContentResponse] = deriveCodecWithDefaults
}

final case class GetChildrenFormModelParams(parentFolderId: FormFolderId)

object GetChildrenFormModelParams {
  given Codec.AsObject[GetChildrenFormModelParams] = deriveCodecWithDefaults
}

final case class GetChildrenFormModelResponse(formSeq: Seq[FormModel])

object GetChildrenFormModelResponse {
  given Codec.AsObject[GetChildrenFormModelResponse] = deriveCodecWithDefaults
}

final case class UpdateFormFolderParams(
  formFolderId: FormFolderId,
  formFolderName: String,
  formFolderTags: Set[String],
  parentFolderId: FormFolderId
)

object UpdateFormFolderParams {
  given Codec.AsObject[UpdateFormFolderParams] = deriveCodecWithDefaults
}

final case class UpdateFormFolderResponse(
  model: FormFolderModel
)

object UpdateFormFolderResponse {
  given Codec.AsObject[UpdateFormFolderResponse] = deriveCodecWithDefaults
}

final case class DeleteFormFolderParams(formFolderId: FormFolderId)

object DeleteFormFolderParams {
  given Codec.AsObject[DeleteFormFolderParams] = deriveCodecWithDefaults
}

final case class DeleteFormFolderResponse(
  subFolders: Seq[FormFolderModel],
  subForms: Seq[FormModel]
)

object DeleteFormFolderResponse {
  given Codec.AsObject[DeleteFormFolderResponse] = deriveCodecWithDefaults
}

final case class ArchiveFormParams(formId: FormId)

object ArchiveFormParams {
  given Codec.AsObject[ArchiveFormParams] = deriveCodecWithDefaults
}

final case class UnarchiveFormParams(formId: FormId)

object UnarchiveFormParams {
  given Codec.AsObject[UnarchiveFormParams] = deriveCodecWithDefaults
}

final case class GetAllFormFolderModelsResponse(formFolderSeq: Seq[FormFolderModel])

object GetAllFormFolderModelsResponse {
  given Codec.AsObject[GetAllFormFolderModelsResponse] = deriveCodecWithDefaults
}

final case class GetComponentLibraryVersionResponse(
  versionOpt: Option[FormVersionModel]
)

object GetComponentLibraryVersionResponse {
  given Codec.AsObject[GetComponentLibraryVersionResponse] = deriveCodecWithDefaults
}

final case class SetComponentLibraryVersionParams(
  formVersionId: FormVersionId,
  enable: Boolean
)

object SetComponentLibraryVersionParams {
  given Codec.AsObject[SetComponentLibraryVersionParams] = deriveCodecWithDefaults
}

final case class SetComponentLibraryVersionResponse(
  version: FormVersionModel
)

object SetComponentLibraryVersionResponse {
  given Codec.AsObject[SetComponentLibraryVersionResponse] = deriveCodecWithDefaults
}

final case class GetInvestorAccessVersionResponse(
  versions: List[FormVersionModel]
)

object GetInvestorAccessVersionResponse {
  given codec: Codec.AsObject[GetInvestorAccessVersionResponse] = deriveCodecWithDefaults
}

final case class SetInvestorAccessVersionParams(
  formVersionId: FormVersionId,
  enable: Boolean
)

object SetInvestorAccessVersionParams {
  given codec: Codec.AsObject[SetInvestorAccessVersionParams] = deriveCodecWithDefaults
}

final case class SetInvestorAccessVersionResponse(
  version: FormVersionModel
)

object SetInvestorAccessVersionResponse {
  given codec: Codec.AsObject[SetInvestorAccessVersionResponse] = deriveCodecWithDefaults
}

final case class GetAllInvestorAccessAsaValuesResponse(
  mappings: Set[String]
)

object GetAllInvestorAccessAsaValuesResponse {
  given codec: Codec.AsObject[GetAllInvestorAccessAsaValuesResponse] = deriveCodecWithDefaults
}

final case class SetFormVersionTagParams(
  formVersionId: FormVersionId,
  tags: Seq[FormVersionTagModel]
)

object SetFormVersionTagParams {
  given Codec.AsObject[SetFormVersionTagParams] = deriveCodecWithDefaults
}

final case class SetFormVersionTagResponse(
  version: FormVersionModel
)

object SetFormVersionTagResponse {
  given Codec.AsObject[SetFormVersionTagResponse] = deriveCodecWithDefaults
}

final case class LibraryComponent(
  name: String,
  schema: Schema,
  widgets: Map[String, Widget],
  relatedRules: Seq[(FormRule, ExtractorResult)]
) {
  lazy val widgetTypeOpt: Option[WidgetType] = widgets.get(name).map(_.widgetType)
}

object LibraryComponent {
  given Codec.AsObject[LibraryComponent] = deriveCodecWithDefaults
}

final case class LibraryComponentPage(
  pageTitle: String,
  components: Seq[LibraryComponent]
)

object LibraryComponentPage {
  given Codec.AsObject[LibraryComponentPage] = deriveCodecWithDefaults
}

final case class GetAllLibraryComponentsResponse(
  pages: Seq[LibraryComponentPage]
)

object GetAllLibraryComponentsResponse {
  given Codec.AsObject[GetAllLibraryComponentsResponse] = deriveCodecWithDefaults
}

final case class GetAllTextParams(
  fileId: FileId
)

object GetAllTextParams {
  given Codec.AsObject[GetAllTextParams] = deriveCodecWithDefaults
}

final case class GetAllTextResponse(
  textContent: String
)

object GetAllTextResponse {
  given Codec.AsObject[GetAllTextResponse] = deriveCodecWithDefaults
}

final case class GetSelectedTextParams(
  fileId: FileId,
  selectedTextAreas: Seq[SelectedTextArea]
)

object GetSelectedTextParams {
  given Codec.AsObject[GetSelectedTextParams] = deriveCodecWithDefaults
}

final case class GetSelectedTextResponse(
  textContent: String
)

object GetSelectedTextResponse {
  given Codec.AsObject[GetSelectedTextResponse] = deriveCodecWithDefaults
}

final case class CopyFormFilesParams(
  formId: FormId,
  fileIdsAndNames: Seq[(FileId, String)]
)

object CopyFormFilesParams {
  given Codec.AsObject[CopyFormFilesParams] = deriveCodecWithDefaults
}

final case class CopyFormFilesResponse(
  newFileIdsAndNames: Seq[(FileId, String)]
)

object CopyFormFilesResponse {
  given Codec.AsObject[CopyFormFilesResponse] = deriveCodecWithDefaults
}

final case class PreviewAnnotationParams(
  fileId: FileId,
  pdfObjects: PdfObjects,
  withData: Boolean
)

object PreviewAnnotationParams {
  given Codec.AsObject[PreviewAnnotationParams] = deriveCodecWithDefaults
}

final case class PreviewAnnotationResponse(fileId: FileId)

object PreviewAnnotationResponse {
  given Codec.AsObject[PreviewAnnotationResponse] = deriveCodecWithDefaults
}

final case class ExportFormAsaMappingTemplateParams(
  aliasList: List[(String, Boolean)],
  formId: FormId,
  formVersionId: FormVersionId,
  formName: String
)

object ExportFormAsaMappingTemplateParams {
  given Codec.AsObject[ExportFormAsaMappingTemplateParams] = deriveCodecWithDefaults
}

final case class ExportFormAsaMappingTemplateResp(
  fileId: FileId
)

object ExportFormAsaMappingTemplateResp {
  given Codec.AsObject[ExportFormAsaMappingTemplateResp] = deriveCodecWithDefaults
}

final case class ProcessFormAsaMappingTemplateParams(
  fileId: FileId,
  formId: FormId,
  formVersionId: FormVersionId
)

object ProcessFormAsaMappingTemplateParams {
  given Codec.AsObject[ProcessFormAsaMappingTemplateParams] = deriveCodecWithDefaults
}

final case class ProcessFormAsaMappingTemplateResp(
  validMap: List[ImportAliasSaPair],
  invalidMap: List[InvalidAliasSaGroup]
)

object ProcessFormAsaMappingTemplateResp {
  given Codec.AsObject[ProcessFormAsaMappingTemplateResp] = deriveCodecWithDefaults
}

final case class ImportAliasSaPair(
  alias: String,
  sa: String
)

object ImportAliasSaPair {
  given Codec.AsObject[ImportAliasSaPair] = deriveCodecWithDefaults
}

final case class InvalidAliasSaGroup(
  errorMessage: String,
  invalidElemList: List[String]
)

object InvalidAliasSaGroup {
  given Codec.AsObject[InvalidAliasSaGroup] = deriveCodecWithDefaults
}

/** @param jsonValue
  *   the final Json accumulated from the source form to prefill
  * @param srcAliases
  *   fields' aliases of the source form that are used to prefill
  */
final case class ConvertibleFieldsWhenPrefill(jsonValue: Json, srcAliases: Set[String])

object ConvertibleFieldsWhenPrefill {
  given Codec.AsObject[ConvertibleFieldsWhenPrefill] = deriveCodecWithDefaults
}

final case class EditFormAssociatedLinksParams(formId: FormId, newAssociatedLinks: Map[String, AssociatedLink])

object EditFormAssociatedLinksParams {
  given Codec.AsObject[EditFormAssociatedLinksParams] = deriveCodecWithDefaults
}

final case class EditFormAssociatedLinksResponse(formModel: FormModel)

object EditFormAssociatedLinksResponse {
  given Codec.AsObject[EditFormAssociatedLinksResponse] = deriveCodecWithDefaults
}

final case class GetFormToolConfigsParams(
  id: Option[FormToolConfigId] = None,
  property: Option[FormToolConfigProperty] = None,
  active: Option[Boolean] = None
)

object GetFormToolConfigsParams {
  given Codec.AsObject[GetFormToolConfigsParams] = deriveCodecWithDefaults
}

final case class GetFormToolConfigsResponse(
  configs: Seq[FormToolConfigModel]
)

object GetFormToolConfigsResponse {
  given Codec.AsObject[GetFormToolConfigsResponse] = deriveCodecWithDefaults
}

final case class FormToolConfigParams(
  name: String,
  description: String,
  active: Boolean,
  property: FormToolConfigProperty
)

object FormToolConfigParams {
  given Codec.AsObject[FormToolConfigParams] = deriveCodecWithDefaults
}

final case class AddFormToolConfigParams(
  data: FormToolConfigParams
)

object AddFormToolConfigParams {
  given Codec.AsObject[AddFormToolConfigParams] = deriveCodecWithDefaults
}

final case class AddFormToolConfigsResponse(
  config: FormToolConfigModel
)

object AddFormToolConfigsResponse {
  given Codec.AsObject[AddFormToolConfigsResponse] = deriveCodecWithDefaults
}

final case class UpdateFormToolConfigParams(
  id: FormToolConfigId,
  data: FormToolConfigParams
)

object UpdateFormToolConfigParams {
  given Codec.AsObject[UpdateFormToolConfigParams] = deriveCodecWithDefaults
}

final case class UpdateFormToolConfigsResponse(
  config: FormToolConfigModel
)

object UpdateFormToolConfigsResponse {
  given Codec.AsObject[UpdateFormToolConfigsResponse] = deriveCodecWithDefaults
}

final case class ProcessAnnotationDocumentParams(
  formId: FormId,
  annotationDocumentVersionId: AnnotationDocumentVersionId,
  fileNameOverride: Option[String] = None
)

object ProcessAnnotationDocumentParams {
  given Codec.AsObject[ProcessAnnotationDocumentParams] = deriveCodecWithDefaults
}

final case class ProcessAnnotationDocumentResponse(
  files: Map[FileId, ExtractedPdf]
)

object ProcessAnnotationDocumentResponse {
  given Codec.AsObject[ProcessAnnotationDocumentResponse] = deriveCodecWithDefaults
}

final case class GenerateSignaturesParams(
  resourceId: FormId | BlueprintId,
  fileId: FileId,
  documentSignature: DocumentSignatureMessage,
  signatureModel: SignatureMessage
)

object GenerateSignaturesParams {
  given Codec.AsObject[GenerateSignaturesParams] = deriveCodecWithDefaults
}

final case class GenerateSignaturesResponse(fileId: FileId)

object GenerateSignaturesResponse {
  given Codec.AsObject[GenerateSignaturesResponse] = deriveCodecWithDefaults
}

final case class GetFilePageSizesParams(
  resourceId: FormId | BlueprintId,
  fileId: FileId
)

object GetFilePageSizesParams {
  given Codec.AsObject[GetFilePageSizesParams] = deriveCodecWithDefaults
}

final case class GetFilePageSizesResponse(
  pageSizeMap: Map[Int, PageWidthHeight]
)

object GetFilePageSizesResponse {
  given Codec.AsObject[GetFilePageSizesResponse] = deriveCodecWithDefaults
}

final case class PageWidthHeight(
  width: Double,
  height: Double
)

object PageWidthHeight {
  given Codec.AsObject[PageWidthHeight] = deriveCodecWithDefaults
}

final case class ExportFormMatchingParams(
  srcFormVersionId: FormVersionId,
  destFormVersionId: FormVersionId,
  matchingMode: FormMatchingMode,
  useServerless: Boolean = true
)

object ExportFormMatchingParams {
  given Codec.AsObject[ExportFormMatchingParams] = deriveCodecWithDefaults
}

final case class ExportFormMatchingResponse(
  reusabilityScore: Double,
  matchingScore: Double,
  exportedFileId: FileId
)

object ExportFormMatchingResponse {
  given Codec.AsObject[ExportFormMatchingResponse] = deriveCodecWithDefaults
}

final case class ImportFromAnnotationDocumentParams(
  formId: FormId,
  importData: List[(AnnotationDocumentVersionId, List[PdfObjectId])]
)

object ImportFromAnnotationDocumentParams {
  given Codec.AsObject[ImportFromAnnotationDocumentParams] = deriveCodecWithDefaults
}

final case class ImportFromAnnotationDocumentResponse(
  keySchemas: Chunk[(String, Schema)],
  widgetMap: Map[String, Widget],
  cutInfoMap: Map[String, (FileId, String, PdfCutInfo)]
)

object ImportFromAnnotationDocumentResponse {
  given Codec.AsObject[ImportFromAnnotationDocumentResponse] = deriveCodecWithDefaults

  val Empty: ImportFromAnnotationDocumentResponse =
    ImportFromAnnotationDocumentResponse(Chunk.empty, Map.empty, Map.empty)

}

final case class UpdateBlueprintRefParams(
  blueprintId: BlueprintId,
  blueprintVersionId: BlueprintVersionId,
  versionIndex: Int,
  blueprintName: String,
  formId: FormId,
  formVersionIdOpt: Option[FormVersionId]
)

object UpdateBlueprintRefParams {
  given Codec.AsObject[UpdateBlueprintRefParams] = deriveCodecWithDefaults
}

final case class UpdateBlueprintRefResponse(
  blueprintRefOpt: Option[BlueprintRef]
)

object UpdateBlueprintRefResponse {
  given Codec.AsObject[UpdateBlueprintRefResponse] = deriveCodecWithDefaults
}

final case class RemoveBlueprintRefParams(
  formId: FormId,
  formVersionIdOpt: Option[FormVersionId]
)

object RemoveBlueprintRefParams {
  given Codec.AsObject[RemoveBlueprintRefParams] = deriveCodecWithDefaults
}

final case class RemoveBlueprintRefResponse(
  blueprintRefOpt: Option[BlueprintRef]
)

object RemoveBlueprintRefResponse {
  given Codec.AsObject[RemoveBlueprintRefResponse] = deriveCodecWithDefaults
}

final case class DiffFormBlueprintParams(
  formId: FormId,
  firstVersionIdOpt: Option[FormVersionId],
  secondVersionIdOpt: Option[FormVersionId]
)

object DiffFormBlueprintParams {
  given Codec.AsObject[DiffFormBlueprintParams] = deriveCodecWithDefaults
}

final case class DiffFormBlueprintResponse(
  firstVersionBlueprintRefOpt: Option[BlueprintRef],
  secondVersionBlueprintRefOpt: Option[BlueprintRef]
)

object DiffFormBlueprintResponse {
  given Codec.AsObject[DiffFormBlueprintResponse] = deriveCodecWithDefaults
}

final case class VerifyLinkedCueVersionParams(
  formId: FormId,
  linkedCueVersionId: CueModuleVersionId
) derives CanEqual,
      CirceCodec.WithDefaults

final case class GetFormDataUserTempParams(
  formDataId: FormVersionDataId
) derives CirceCodec.WithDefaults
