// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.service

import scala.util.Random

import zio.ZIO
import zio.implicits.*

import anduin.forms.endpoint.AddFormStandardAliasMappingParams
import anduin.forms.Form
import anduin.id.form.FormVersionId
import anduin.id.role.portal.PortalSectionId
import anduin.autofill.ComputeFormMatchingMode
import anduin.model.common.user.UserId
import anduin.portaluser.PortalUserModel
import anduin.portaluser.PortalUserProtocols.{AddPortalUserParams, PortalRole, SetUserRoleParams}
import anduin.service.AuthenticatedRequestContext
import anduin.testing.{GaiaBaseInteg, GaiaIntegUtils, GondorCoreIntegUtils}
import zio.test.*

import anduin.id.sa.SaProfileId
import anduin.ontology.parser.AsaAnnotationParser
import anduin.sa.endpoints.AddSaProfileParams
import anduin.sa.model.SaMessage

object FormMatchingServiceInteg extends GaiaBaseInteg with GaiaIntegUtils with GondorCoreIntegUtils {

  private val expectedMatchingAliasHeuristic = Map(
    "investorname" -> Set("investorname"),
    "investoraddress1" -> Set("investoraddress"),
    "maxnumboardmember" -> Set("maximumboardmembercount"), // This one is matched by ASA mapping
    "repeat:emailaddress:0" -> Set("repeat1:emailaddress:0"),
    "repeat:emailaddress:1" -> Set("repeat1:emailaddress:1"),
    "repeat:emailaddress:2" -> Set("repeat1:emailaddress:2"),
    "repeat:phone:0" -> Set("repeat1:phone:0"),
    "repeat:phone:1" -> Set("repeat1:phone:1"),
    "repeat:phone:2" -> Set("repeat1:phone:2")
  )

  private val expectedMatchingAliasAsa = Map(
    "maxnumboardmember" -> Set("maximumboardmembercount"),
    "investorname" -> Set("investorname")
  )

  private val expectedMatchingValueAsa = Map(
    "investortype#usperson" -> Set("individualinvestortype#usresident"),
    "investortype#scorp" -> Set("entityinvestortype#scorporation"),
    "investortype#ccorp" -> Set("entityinvestortype#ccorporation")
  )

  private var formPortalAdmin: UserId = scala.compiletime.uninitialized // scalafix:ok
  private val formPortalAdminEmail: String = s"integ.admin1+${Random.alphanumeric.take(6).mkString}@anduintransact.com"

  private val formPortalAdminUserInfo = emptyUserInfo.copy(
    emailAddressStr = formPortalAdminEmail,
    firstName = usFaker.name().firstName(),
    lastName = usFaker.name().lastName()
  )

  // scalafix:off DisableSyntax.var
  private var srcFormVersionId: FormVersionId = scala.compiletime.uninitialized
  private var destFormVersionId: FormVersionId = scala.compiletime.uninitialized
  private var testSaProfileId: SaProfileId = scala.compiletime.uninitialized
  // scalafix:on

  override def spec = suite("FormMatchingServiceInteg")(
    createTestUsers,
    prepareDataSuite,
    integSuite
  ) @@ TestAspect.sequential

  private def createTestUsers = test("Create test users") {
    for {
      _ <- createTestUser(formPortalAdminUserInfo).map(formPortalAdmin = _)
      _ <- portalUserService
        .addPortalUser(
          params = AddPortalUserParams(formPortalAdminEmail, None),
          ctx = AuthenticatedRequestContext.defaultInstance
        )
        .unit
        .onErrorHandleWith(_ => ZIO.unit)
      _ <- portalUserService.setUserRole(
        params = SetUserRoleParams(
          userId = formPortalAdmin,
          roles = Seq(
            PortalRole(PortalSectionId.Form, PortalUserModel.Relation.Admin),
            PortalRole(PortalSectionId.AsaOntology, PortalUserModel.Relation.Admin),
            PortalRole(PortalSectionId.AsaManagement, PortalUserModel.Relation.Admin)
          ),
          isSuperAdmin = false
        ),
        ctx = AuthenticatedRequestContext.defaultInstance
      )
    } yield assertCompletes
  }

  private def prepareDataSuite = suite("FormMatchingServiceInteg - Prepare data")(
    test("Create test SA profile") {
      for {
        profileId <- saProfileService.addSaProfile(
          formPortalAdmin,
          AddSaProfileParams(
            profileName = FormServiceTestUtils.testSaProfileInfo.profileName,
            // Create manually -> no MappingDestinationId
            saList = FormServiceTestUtils.testSaProfileInfo.aliasList.map(alias => SaMessage(alias = alias))
          )
        )
      } yield {
        testSaProfileId = profileId
        assertCompletes
      }
    },
    test("Create source form") {
      for {
        formVersionId <- createGaiaForm(
          formPortalAdmin,
          formService,
          fileService,
          inputForm = FormServiceTestUtils.testForm1
        )
          .map(_._2)
        // Add standard alias mapping
        _ <- formService.addFormStandardAliasMapping(
          AddFormStandardAliasMappingParams(
            formVersionId,
            FormServiceTestUtils.testForm1AsaMapping
          ),
          formPortalAdmin
        )
        // Add SA profile mapping
        _ <- formSaProfileMappingService.addFormSaProfileMapping(
          formPortalAdmin,
          testSaProfileId,
          formVersionId,
          FormServiceTestUtils.testForm1SaProfileMapping
        )
      } yield {
        srcFormVersionId = formVersionId
        assertCompletes
      }
    },
    test("Create destination form") {
      for {
        formVersionId <- createGaiaForm(
          formPortalAdmin,
          formService,
          fileService,
          inputForm = FormServiceTestUtils.testForm2
        )
          .map(_._2)
        // Add standard alias mapping
        _ <- formService.addFormStandardAliasMapping(
          AddFormStandardAliasMappingParams(
            formVersionId,
            FormServiceTestUtils.testForm2AsaMapping
          ),
          formPortalAdmin
        )
        // Add SA profile mapping
        _ <- formSaProfileMappingService.addFormSaProfileMapping(
          formPortalAdmin,
          testSaProfileId,
          formVersionId,
          FormServiceTestUtils.testForm2SaProfileMapping
        )
      } yield {
        destFormVersionId = formVersionId
        assertCompletes
      }
    }
  )

  private def integSuite = suite("FormMatchingServiceInteg - Testing functions")(
    test("Test loading ASA to ontology conversion library") {
      ZIO.attempt {
        val asaOntologyMap = formMatchingService.getAsaToOntologyConversionMap
        val validAsaCount = asaOntologyMap.keySet.filter(_.startsWith("asa_")).size
        val validOntologyCount = asaOntologyMap.values.flatMap(AsaAnnotationParser.parse(_).toOption).size

        assertTrue(
          validAsaCount == 721,
          validOntologyCount == 721
        )
      }
    },
    test("Test compute form matching using standard ASA") {
      for {
        res <- formMatchingService.getComputeFormMatching(
          formPortalAdmin,
          srcFormVersionId,
          destFormVersionId,
          matchingMode = ComputeFormMatchingMode.UseOldAsaOnly
        )
      } yield {
        val matchingAliasMap = res.matchingAliasMap.map { case (k, v) =>
          k -> v.items.map(_.alias)
        }
        val matchingValueMap = res.matchingValueMap.map { case (k, v) =>
          k -> v.items
        }
        assertTrue(matchingAliasMap == expectedMatchingAliasAsa)
        assertTrue(matchingValueMap == expectedMatchingValueAsa)
      }
    },
    test("Test compute form matching using heuristic (serverless)") {
      for {
        res <- formMatchingService.getComputeFormMatching(
          formPortalAdmin,
          srcFormVersionId,
          destFormVersionId,
          matchingMode = ComputeFormMatchingMode.AliasAsaHeuristic,
          useServerless = true
        )
      } yield {
        val matchingAliasMap = res.matchingAliasMap.map { case (k, v) =>
          k -> v.items.map(_.alias)
        }
        assertTrue(matchingAliasMap == expectedMatchingAliasHeuristic)
      }
    },
    test("Clean up") {
      for {
        _ <- saProfileService.deleteSaProfilePermanently(formPortalAdmin, testSaProfileId).catchAllCause(_ => ZIO.unit)
      } yield {
        assertCompletes
      }
    }
  )

}
