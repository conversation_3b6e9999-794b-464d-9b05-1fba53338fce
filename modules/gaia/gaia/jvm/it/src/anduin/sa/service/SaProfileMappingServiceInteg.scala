// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.sa.service

import scala.util.Random

import zio.ZIO
import zio.test.*

import anduin.annotation.AssetSessionIdUtils
import anduin.annotation.AssetSessionIdUtils.AssetSessionType
import anduin.annotation.service.AnnotationDocumentService
import anduin.fdb.record.FDBRecordDatabase
import anduin.forms.FormData
import anduin.forms.endpoint.annotation.CommitAnnotationDocumentVersionParams
import anduin.forms.service.{FormLockServiceIntegUtils, FormServiceTestUtils}
import anduin.forms.storage.FormModelStoreOperations
import anduin.id.annotation.{AnnotationDocumentId, AnnotationDocumentVersionId}
import anduin.id.form.{FormId, FormVersionId}
import anduin.id.sa.{MappingDestinationId, SaProfileId}
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.model.id.AssetSessionId
import anduin.sa.model.saprofilemapping.saprofilemappingmessage.SaProfileMappingMessage
import anduin.testing.{GaiaBaseInteg, GondorCoreIntegUtils}
import anduin.sa.service.CommonSaIntegUtils.*

object SaProfileMappingServiceInteg extends GaiaBaseInteg with GondorCoreIntegUtils {

  given AnnotationDocumentService.KeySpace = AnnotationDocumentService.KeySpace.Test

  // scalafix:off DisableSyntax.var
  private var adminUserId: UserId = scala.compiletime.uninitialized
  private var docId1: AnnotationDocumentId = scala.compiletime.uninitialized
  private var docId2: AnnotationDocumentId = scala.compiletime.uninitialized
  private var doc1VersionId1: AnnotationDocumentVersionId = scala.compiletime.uninitialized
  private var doc1DraftId: AnnotationDocumentVersionId = scala.compiletime.uninitialized
  private var doc2DraftId: AnnotationDocumentVersionId = scala.compiletime.uninitialized
  private var testFormId1: FormId = scala.compiletime.uninitialized
  private var testForm1DraftVersionId: FormVersionId = scala.compiletime.uninitialized
  private var testForm1VersionId1: FormVersionId = scala.compiletime.uninitialized
  private var doc1Version1DestinationId: MappingDestinationId = scala.compiletime.uninitialized
  private var doc1DraftDestinationId: MappingDestinationId = scala.compiletime.uninitialized
  private var doc2DraftIdDestinationId: MappingDestinationId = scala.compiletime.uninitialized
  private var testForm1DraftDestinationId: MappingDestinationId = scala.compiletime.uninitialized
  private var testForm1Version1DestinationId: MappingDestinationId = scala.compiletime.uninitialized

  private var nbSaProfileId: SaProfileId = scala.compiletime.uninitialized
  private var bainSaProfileId: SaProfileId = scala.compiletime.uninitialized
  private var kkrSaProfileId: SaProfileId = scala.compiletime.uninitialized
  // scalafix:on

  private val doc1Field1: String = "doc1_field1"
  private val doc1Field2: String = "doc1_field2"
  private val doc1Field3: String = "doc1_field3"
  private val doc2Field1: String = "doc2_field1"
  private val doc2Field2: String = "doc2_field2"

  private val userEmail = s"integ.user+${Random.alphanumeric.take(6).mkString}@anduintransact.com"

  private val userInfo = emptyUserInfo.copy(
    emailAddressStr = userEmail,
    firstName = usFaker.name().firstName(),
    lastName = usFaker.name().lastName()
  )

  private val formSession = AssetSessionId.FormBuilder("testsession1")

  private val formOwners = Seq(
    EmailAddress(Some(userInfo.fullNameString), userEmail)
  )

  private def ignoreProfileMappingLastUpdatedInfo(input: SaProfileMappingMessage) =
    input.copy(lastUpdatedByOpt = None, lastUpdatedAtOpt = None)

  override def spec = suite("SaProfile Mapping Services Integ")(
    setupSuite,
    documentSaProfileMappingSuite,
    formSaProfileMappingSuite,
    commonSaProfileMappingTest,
    cleanTest
  ) @@ TestAspect.sequential

  private def setupSuite = suite("SaProfile Mapping Services - Set up test context")(
    test("Create test users") {
      for {
        _ <- createTestUser(userInfo).map(adminUserId = _)
        _ <- setupFormAsaUserAdmin(adminUserId, userEmail)(portalUserService)
      } yield assertCompletes
    },
    test("Initialize some SaProfiles") {
      for {
        addedSaProfileIds <- addSampleSaProfiles(adminUserId)(saProfileService)
        queryAddedProfileRes <- saProfileService.getSaProfileByIds(adminUserId, addedSaProfileIds)
      } yield {
        nbSaProfileId = addedSaProfileIds.head
        bainSaProfileId = addedSaProfileIds(1)
        kkrSaProfileId = addedSaProfileIds(2)

        assertTrue(
          queryAddedProfileRes.map(_.name).toSet == Set(naaSaProfileName, bnnSaProfileName, kxrSaProfileName)
        )
      }
    }
  )

  private def documentSaProfileMappingSuite = suite("Document SaProfile mapping service tests")(
    test("Initialize documents") {
      for {
        createDocListRes <- addSamplePdfs(adminUserId = adminUserId, numDoc = 2, baseName = "Document")(
          fileService,
          annotationDocumentService
        )
      } yield {
        docId1 = createDocListRes.head.model.id
        docId2 = createDocListRes.last.model.id
        doc1DraftId = createDocListRes.head.model.draftVersionId
        doc2DraftId = createDocListRes.last.model.draftVersionId

        assertTrue(
          createDocListRes.head.model.name == "Document1.pdf",
          createDocListRes.last.model.name == "Document2.pdf"
        )
      }
    },
    test("Basic Add/Remove Document SaProfile mapping functions") {
      for {
        _ <- documentSaProfileMappingService.addDocumentSaProfileMapping(
          adminUserId,
          nbSaProfileId,
          doc1DraftId,
          Map(
            doc1Field1 -> someAliases.head,
            doc1Field2 -> someAliases(1),
            doc1Field3 -> someAliases(2)
          ),
          checkForValidFieldName = false
        )
        destinationId1 <- mappingDestinationService
          .queryMappingDestinationByResourceId(doc1DraftId.idString)
          .map(_.head.id)
        _ <- documentSaProfileMappingService.addDocumentSaProfileMapping(
          adminUserId,
          bainSaProfileId,
          doc2DraftId,
          Map(doc2Field1 -> someAliases.head, doc2Field2 -> someAliases(1)),
          checkForValidFieldName = false
        )
        destinationId2 <- mappingDestinationService
          .queryMappingDestinationByResourceId(doc2DraftId.idString)
          .map(_.head.id)
        doc1SaProfileMappingMsgList <- documentSaProfileMappingService
          .querySaProfileMappingByDocumentId(adminUserId, docId1)
        doc1DraftSaProfileMappingMsgList <- documentSaProfileMappingService
          .querySaProfileMappingByDocumentVersionId(adminUserId, doc1DraftId)
        doc2SaProfileMappingMsgList <- documentSaProfileMappingService
          .querySaProfileMappingByDocumentId(adminUserId, docId2)
        doc2DraftSaProfileMappingMsgList <- documentSaProfileMappingService
          .querySaProfileMappingByDocumentVersionId(adminUserId, doc2DraftId)
        _ <- documentSaProfileMappingService.removeDocumentSaProfileMapping(
          adminUserId,
          nbSaProfileId,
          doc1DraftId,
          Set(doc1Field3)
        )
        doc1DraftSaProfileMappingAfterRemovingMsg <- documentSaProfileMappingService
          .getDocumentSaProfileMapping(adminUserId, nbSaProfileId, doc1DraftId)
          .map(_.get)
      } yield {
        doc1DraftDestinationId = destinationId1
        doc2DraftIdDestinationId = destinationId2

        assertTrue(
          ignoreProfileMappingLastUpdatedInfo(doc1DraftSaProfileMappingAfterRemovingMsg) == SaProfileMappingMessage(
            nbSaProfileId,
            doc1DraftDestinationId,
            Map(doc1Field1 -> someAliases.head, doc1Field2 -> someAliases(1))
          ),
          doc2DraftSaProfileMappingMsgList.map(ignoreProfileMappingLastUpdatedInfo) == List(
            SaProfileMappingMessage(
              bainSaProfileId,
              doc2DraftIdDestinationId,
              Map(doc2Field1 -> someAliases.head, doc2Field2 -> someAliases(1))
            )
          ),
          doc1DraftSaProfileMappingMsgList.map(ignoreProfileMappingLastUpdatedInfo) == List(
            SaProfileMappingMessage(
              nbSaProfileId,
              doc1DraftDestinationId,
              Map(
                doc1Field1 -> someAliases.head,
                doc1Field2 -> someAliases(1),
                doc1Field3 -> someAliases(2)
              )
            )
          ),
          doc2SaProfileMappingMsgList == doc2DraftSaProfileMappingMsgList,
          doc1SaProfileMappingMsgList == doc1DraftSaProfileMappingMsgList
        )
      }
    },
    test("Transfer Document SaProfile mapping") {
      for {
        createVersionResp <- annotationDocumentService.commitAnnotationDocumentVersion(
          CommitAnnotationDocumentVersionParams(
            documentId = docId1,
            sessionId = AssetSessionIdUtils.generateAssetSessionId(AssetSessionType.Annotation),
            versionName = "Document 1 - version 1",
            description = ""
          ),
          actor = adminUserId
        )
        docVersionId = createVersionResp.version.id
        doc1DraftSaProfileMappingMsgList <- documentSaProfileMappingService
          .querySaProfileMappingByDocumentVersionId(adminUserId, doc1DraftId)
        _ <- documentSaProfileMappingService.transferAllSaProfileMappings(
          adminUserId,
          doc1DraftId,
          docVersionId
        )
        doc1Version1SaProfileMappingMsgList <- documentSaProfileMappingService
          .querySaProfileMappingByDocumentVersionId(adminUserId, docVersionId)
        doc1SaProfileMappingMsgList <- documentSaProfileMappingService
          .querySaProfileMappingByDocumentId(adminUserId, docId1)
        destinationId3 <- mappingDestinationService
          .queryMappingDestinationByResourceId(docVersionId.idString)
          .map(_.head.id)
      } yield {
        doc1VersionId1 = docVersionId
        doc1Version1DestinationId = destinationId3

        assertTrue(
          doc1Version1SaProfileMappingMsgList.map(ignoreProfileMappingLastUpdatedInfo) == List(
            SaProfileMappingMessage(
              nbSaProfileId,
              doc1Version1DestinationId,
              Map(doc1Field1 -> someAliases.head, doc1Field2 -> someAliases(1))
            )
          ),
          doc1SaProfileMappingMsgList.toSet == (doc1Version1SaProfileMappingMsgList ++ doc1DraftSaProfileMappingMsgList).toSet
        )
      }
    }
  )

  private def formSaProfileMappingSuite = suite("Form SaProfile mapping service tests")(
    test("Initialize form") {
      for {
        formRes <- formService.createForm(
          "testForm1",
          Set.empty,
          formOwners,
          adminUserId
        )
        formId = formRes.formId
        _ <- FormLockServiceIntegUtils.forceGetLock(
          formId,
          adminUserId,
          formSession
        )(formLockService)
        draftVersionId <- formService
          .getAllVersions(formId, adminUserId)
          .map(_.versions.headOption.map(_.formVersionId).get)
        _ <- formService.saveFormData(
          formId = formId,
          formData = FormData(FormServiceTestUtils.testForm1),
          versionMetadataOpt = None,
          parentVersionId = Some(draftVersionId),
          name = "versionDraft",
          actor = adminUserId,
          sessionId = formSession
        )
        createVersionRes <- formService.createFormVersion(
          formId,
          "version1",
          "",
          adminUserId,
          formSession
        )
        formVersionRes <- formService.getForm(
          formId,
          Option(createVersionRes.result.toOption.get.versionId),
          adminUserId
        )
      } yield {
        testFormId1 = formId
        testForm1DraftVersionId = draftVersionId
        testForm1VersionId1 = formVersionRes.formModel.latestVersionId

        assertTrue(
          testForm1VersionId1 == createVersionRes.result.toOption.get.versionId,
          formVersionRes.formModel.name == "testForm1",
          formVersionRes.versionOpt.get.name == "version1",
          formVersionRes.formData == FormData(FormServiceTestUtils.testForm1)
        )
      }
    },
    test("Form SaProfile mapping functions") {
      for {
        _ <- formSaProfileMappingService.addFormSaProfileMapping(
          adminUserId,
          nbSaProfileId,
          testForm1DraftVersionId,
          Map(
            "isusperson" -> someAliases(3)
          )
        )
        destinationId1 <- mappingDestinationService
          .queryMappingDestinationByResourceId(testForm1DraftVersionId.idString)
          .map(_.head.id)
        _ <- formSaProfileMappingService.addFormSaProfileMapping(
          adminUserId,
          nbSaProfileId,
          testForm1VersionId1,
          Map(
            "investorname" -> someAliases.head,
            "bankname" -> someAliases(1),
            "bankaccount" -> someAliases(2)
          )
        )
        destinationId2 <- mappingDestinationService
          .queryMappingDestinationByResourceId(testForm1VersionId1.idString)
          .map(_.head.id)
        form1SaProfileMappingMsgList <- formSaProfileMappingService
          .querySaProfileMappingByFormId(adminUserId, testFormId1)
        form1DraftSaProfileMappingMsgList <- formSaProfileMappingService
          .querySaProfileMappingByFormVersionId(adminUserId, testForm1DraftVersionId)
        form1Version1SaProfileMappingMsgList <- formSaProfileMappingService
          .querySaProfileMappingByFormVersionId(adminUserId, testForm1VersionId1)
        _ <- formSaProfileMappingService.removeFormSaProfileMapping(
          adminUserId,
          nbSaProfileId,
          testForm1VersionId1,
          Set("bankaccount")
        )
        form1Version1NbSaProfileMappingMsg <- formSaProfileMappingService
          .getFormSaProfileMapping(adminUserId, nbSaProfileId, testForm1VersionId1)
          .map(_.get)
      } yield {
        testForm1DraftDestinationId = destinationId1
        testForm1Version1DestinationId = destinationId2

        assertTrue(
          ignoreProfileMappingLastUpdatedInfo(form1Version1NbSaProfileMappingMsg) == SaProfileMappingMessage(
            nbSaProfileId,
            testForm1Version1DestinationId,
            Map("investorname" -> someAliases.head, "bankname" -> someAliases(1))
          ),
          form1Version1SaProfileMappingMsgList.map(ignoreProfileMappingLastUpdatedInfo) == List(
            SaProfileMappingMessage(
              nbSaProfileId,
              testForm1Version1DestinationId,
              Map(
                "investorname" -> someAliases.head,
                "bankname" -> someAliases(1),
                "bankaccount" -> someAliases(2)
              )
            )
          ),
          form1DraftSaProfileMappingMsgList.map(ignoreProfileMappingLastUpdatedInfo) == List(
            SaProfileMappingMessage(
              nbSaProfileId,
              testForm1DraftDestinationId,
              Map("isusperson" -> someAliases(3))
            )
          ),
          form1SaProfileMappingMsgList.toSet == (form1Version1SaProfileMappingMsgList ++ form1DraftSaProfileMappingMsgList).toSet
        )
      }
    }
  )

  private def commonSaProfileMappingTest = test("Remaining common SaProfile mapping service functions") {
    for {
      nbSaProfileUseRes <- commonSaProfileMappingService.querySaProfileMappingByProfileId(adminUserId, nbSaProfileId)
      bainSaProfileUseRes <- commonSaProfileMappingService.querySaProfileMappingByProfileId(adminUserId, bainSaProfileId)
      saInvestorNameUseRes <- commonSaProfileMappingService.querySaProfileMappingBySingleSaAlias(
        adminUserId,
        someAliases.head
      )
    } yield {
      assertTrue(
        nbSaProfileUseRes.forall(_.saProfileId == nbSaProfileId),
        nbSaProfileUseRes.map(_.destinationId).toSet.size == 4,
        bainSaProfileUseRes.forall(_.saProfileId == bainSaProfileId),
        bainSaProfileUseRes.map(_.destinationId).toSet.size == 1,
        saInvestorNameUseRes.forall(_.fieldToSaMap.values.toSet.contains(someAliases.head)),
        saInvestorNameUseRes.map(_.destinationId).toSet.size == 4
      )
    }
  }

  private def cleanTest = test("Some clean up") {
    for {
      // Clean up Form
      _ <- formLockService.releaseLock(
        testFormId1,
        adminUserId,
        formSession
      )
      _ <- FDBRecordDatabase.transact(FormModelStoreOperations.Production) { ops =>
        ops.delete(testFormId1)
      }
      // Clean up FormMapping records
      _ <- commonSaProfileMappingService.scanToUpdateSaProfileMapping(
        adminUserId,
        updateFunc = msgList =>
          ZIO
            .foreach(msgList) { case (key, _) =>
              commonSaProfileMappingService
                .deleteSaProfileMappingPermanently(adminUserId, key.saProfileId, key.mappingDestinationId)
            }
            .unit
      )
      // Clean up test SaProfiles
      _ <- ZIO
        .foreach(List(nbSaProfileId, bainSaProfileId, kkrSaProfileId)) { profileId =>
          saProfileService.deleteSaProfilePermanently(adminUserId, profileId)
        }
        .unit
    } yield assertCompletes
  }

}
