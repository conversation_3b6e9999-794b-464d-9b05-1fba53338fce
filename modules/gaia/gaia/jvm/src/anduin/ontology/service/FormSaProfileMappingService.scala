// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.ontology.service

import zio.{Task, ZIO}

import anduin.cue.model.CueJsonCommonDataTypes
import anduin.id.form.{FormId, FormVersionId}
import anduin.model.common.user.UserId
import anduin.forms.service.FormService
import anduin.fdb.record.DefaultCluster
import anduin.forms.endpoint.MatchedValue
import anduin.forms.utils.FormDataUtils
import com.anduin.stargazer.service.GondorConfig
import com.anduin.stargazer.service.utils.ZIOUtils
import anduin.id.sa.{MappingDestinationId, SaProfileId}
import anduin.cue.model.datalayer.Common.StandardAliasInfoForCueGeneration
import anduin.sa.SaCommonUtils
import anduin.sa.endpoints.{SaProfileFullInfo, SingleSaProfileMappingResponse}
import anduin.sa.model.FormResourceDetails
import anduin.sa.model.saprofilemapping.saprofilemappingmessage.SaProfileMappingMessage
import anduin.sa.service.{CommonSaProfileMappingService, MappingDestinationService, SaProfileService}

final case class FormSaProfileMappingService(
  gondorConfig: GondorConfig,
  commonSaProfileMappingService: CommonSaProfileMappingService,
  mappingAuthorizationService: MappingAuthorizationService,
  mappingDestinationService: MappingDestinationService,
  saProfileService: SaProfileService,
  formService: FormService
)(
  using val documentSaProfileMappingService: DocumentSaProfileMappingService
) {

  private val parallelism = 4

  private def getExistingDestinationIdOpt(formVersionId: FormVersionId) = {
    mappingDestinationService
      .queryMappingDestinationByResourceId(formVersionId.idString)
      .map(_.headOption.map(_.id))
  }

  private def createMappingDestinationIfNotExisted(
    actorId: UserId,
    formVersionId: FormVersionId
  ): Task[MappingDestinationId] = {
    for {
      existingDestinationIdOpt <- getExistingDestinationIdOpt(formVersionId)
      destinationId <- existingDestinationIdOpt.fold(
        for {
          getFormRes <- formService
            .getForm(
              formVersionId.parent,
              Option(formVersionId),
              actorId,
              shouldCheckPermission = false
            )
          createdDestinationId <- mappingDestinationService.addMappingDestination(
            actorId = actorId,
            resourceId = formVersionId.idString,
            resourceDescription =
              List(Option(getFormRes.formModel.name), getFormRes.versionOpt.map(_.name)).flatten.mkString("_"),
            resourceDetails = FormResourceDetails(
              formVersionId = formVersionId,
              formName = getFormRes.formModel.name,
              formVersionName = getFormRes.versionOpt.map(_.name).getOrElse("")
            )
          )
        } yield createdDestinationId
      )(ZIO.succeed(_))
    } yield destinationId
  }

  def addFormSaProfileMapping(
    actorId: UserId,
    saProfileId: SaProfileId,
    formVersionId: FormVersionId,
    mappingToAdd: Map[String, String]
  ): Task[Unit] = {
    ZIOUtils.when(mappingToAdd.nonEmpty)(
      for {
        _ <- mappingAuthorizationService.validateFormPermission(formVersionId.parent, actorId)
        destinationId <- createMappingDestinationIfNotExisted(actorId, formVersionId)
        _ <- commonSaProfileMappingService.addSaProfileMapping(actorId, saProfileId, destinationId, mappingToAdd)
      } yield ()
    )
  }

  def transferSingleSaProfileMapping(
    actorId: UserId,
    saProfileId: SaProfileId,
    srcFormVersionId: FormVersionId,
    targetFormVersionId: FormVersionId
  ): Task[Unit] = {
    for {
      _ <- mappingAuthorizationService.validateFormPermission(srcFormVersionId.parent, actorId)
      _ <- mappingAuthorizationService.validateFormPermission(targetFormVersionId.parent, actorId)
      srcDestinationId <- createMappingDestinationIfNotExisted(actorId, srcFormVersionId)
      targetDestinationId <- createMappingDestinationIfNotExisted(actorId, targetFormVersionId)
      _ <- commonSaProfileMappingService.transferSingleSaProfileMapping(
        actorId,
        saProfileId,
        srcDestinationId,
        targetDestinationId
      )
    } yield ()
  }

  def transferAllSaProfileMappings(
    actorId: UserId,
    srcFormVersionId: FormVersionId,
    targetFormVersionId: FormVersionId
  ): Task[Unit] = {
    for {
      _ <- mappingAuthorizationService.validateFormPermission(srcFormVersionId.parent, actorId)
      _ <- mappingAuthorizationService.validateFormPermission(targetFormVersionId.parent, actorId)
      srcDestinationId <- createMappingDestinationIfNotExisted(actorId, srcFormVersionId)
      targetDestinationId <- createMappingDestinationIfNotExisted(actorId, targetFormVersionId)
      _ <- commonSaProfileMappingService.transferAllSaProfileMappings(actorId, srcDestinationId, targetDestinationId)
    } yield ()
  }

  def removeFormSaProfileMapping(
    actorId: UserId,
    saProfileId: SaProfileId,
    formVersionId: FormVersionId,
    mappedFieldsToRemove: Set[String]
  ): Task[Unit] = {
    ZIOUtils.when(mappedFieldsToRemove.nonEmpty)(
      for {
        _ <- mappingAuthorizationService.validateFormPermission(formVersionId.parent, actorId)
        existingDestinationIdOpt <- getExistingDestinationIdOpt(formVersionId)
        _ <- existingDestinationIdOpt.fold(ZIO.unit) { destinationId =>
          commonSaProfileMappingService.removeSaProfileMapping(actorId, saProfileId, destinationId, mappedFieldsToRemove)
        }
      } yield ()
    )
  }

  def deleteFormSaProfileMappingPermanently(
    actorId: UserId,
    saProfileId: SaProfileId,
    formVersionId: FormVersionId
  ): Task[Unit] = {
    for {
      _ <- mappingAuthorizationService.validateFormPermission(formVersionId.parent, actorId)
      existingDestinationIdOpt <- getExistingDestinationIdOpt(formVersionId)
      _ <- existingDestinationIdOpt.fold(ZIO.unit) { destinationId =>
        commonSaProfileMappingService.deleteSaProfileMappingPermanently(actorId, saProfileId, destinationId)
      }
    } yield ()
  }

  def getFormSaProfileMapping(
    actorId: UserId,
    saProfileId: SaProfileId,
    formVersionId: FormVersionId,
    ignorePermissionCheck: Boolean = true
  ): Task[Option[SaProfileMappingMessage]] = {
    for {
      _ <- ZIO.whenDiscard(!ignorePermissionCheck)(
        mappingAuthorizationService.validateFormPermission(formVersionId.parent, actorId)
      )
      existingDestinationIdOpt <- getExistingDestinationIdOpt(formVersionId)
      res <- existingDestinationIdOpt.fold(ZIO.succeed(Option.empty[SaProfileMappingMessage])) { destinationId =>
        commonSaProfileMappingService.getSaProfileMapping(saProfileId, destinationId)
      }
    } yield res
  }

  def querySaProfileMappingByFormVersionId(
    actorId: UserId,
    formVersionId: FormVersionId,
    ignorePermissionCheck: Boolean = true
  ): Task[List[SaProfileMappingMessage]] = {
    for {
      _ <- ZIO.whenDiscard(!ignorePermissionCheck)(
        mappingAuthorizationService.validateFormPermission(formVersionId.parent, actorId)
      )
      existingDestinationIdOpt <- getExistingDestinationIdOpt(formVersionId)
      res <- existingDestinationIdOpt.fold(ZIO.succeed(List.empty[SaProfileMappingMessage])) { destinationId =>
        commonSaProfileMappingService.querySaProfileMappingByDestinationId(destinationId)
      }
    } yield res
  }

  // Just a convenient function with the assumption that each form version is only mapped to 1 SaProfile
  def queryFormVersionSaMapping(
    actorId: UserId,
    formVersionId: FormVersionId,
    useSaLocalValueIfExisted: Boolean = false,
    ignorePermissionCheck: Boolean = true
  ): Task[Map[String, String]] = {
    queryFormVersionSaMappingInfoOpt(
      actorId,
      formVersionId,
      useSaLocalValueIfExisted,
      ignorePermissionCheck
    ).map(
      _.map(_.fieldToSaMap).getOrElse(Map.empty)
    )
  }

  def queryFormVersionSaMappingInfoOpt(
    actorId: UserId,
    formVersionId: FormVersionId,
    // - This param is to support cases where users wanna use locally unique CSA option value, e.g., `yes`, `no`
    // when import/export instead of the full meaning, globally unique CSA alias, e.g., `yes_questionA`, `no_questionA`.
    // - Handling mechanism: The locally unique option value will be combined with its parent alias to become globally unique alias
    // to be used as normal CSA while the original local option value is also stored to be used when needed (e.g., in import/export flows)
    useSaLocalValueIfExisted: Boolean = false,
    ignorePermissionCheck: Boolean = true
  ): Task[Option[SingleSaProfileMappingResponse]] = {
    for {
      directFormSaMappingResOpt <- querySaProfileMappingByFormVersionId(actorId, formVersionId, ignorePermissionCheck)
        .map(_.headOption)
      formSaMappingFromPdfMappingOpt <- queryFormVersionSaMappingViaPdfs(
        actorId,
        formVersionId,
        directFormSaMappingResOpt.map(_.saProfileId)
      )
    } yield formSaMappingFromPdfMappingOpt.map { mappingInfo =>
      // directFormSaMapping would have higher priority
      val combinedFieldToSaMap =
        mappingInfo.fieldToSaMap ++ directFormSaMappingResOpt.map(_.fieldToSaMap).getOrElse(Map.empty)
      val allSaMap = mappingInfo.saProfileInfo.saList.map(saMsg => saMsg.alias -> saMsg).toMap
      val fieldToSaMap = if (useSaLocalValueIfExisted) {
        combinedFieldToSaMap.view
          .mapValues(saAlias => allSaMap.get(saAlias).flatMap(_.localOptionValueOpt).getOrElse(saAlias))
          .toMap
      } else {
        combinedFieldToSaMap
      }
      val lastUpdatedInfoOpt = List(
        mappingInfo.lastUpdatedByOpt.zip(mappingInfo.lastUpdatedAtOpt),
        directFormSaMappingResOpt.flatMap(msg => msg.lastUpdatedByOpt.zip(msg.lastUpdatedAtOpt))
      ).flatten.maxByOption(_._2.toEpochMilli)

      mappingInfo.copy(
        fieldToSaMap = fieldToSaMap,
        lastUpdatedByOpt = lastUpdatedInfoOpt.map(_._1),
        lastUpdatedAtOpt = lastUpdatedInfoOpt.map(_._2)
      )
    }
  }

  def getFormSaListForCueGeneration(
    actorId: UserId,
    formVersionId: FormVersionId,
    ignorePermissionCheck: Boolean = true
  ): Task[List[StandardAliasInfoForCueGeneration]] = {
    for {
      directFormSaMappings <- querySaProfileMappingByFormVersionId(actorId, formVersionId, ignorePermissionCheck)
      linkedSaProfileIdOpt <- directFormSaMappings.headOption.fold(
        // No direct Form SA mapping -> try getting via Form's PDFs
        for {
          formVerSystemMetaRes <- formService
            .getFormModelsUnsafe(formId = formVersionId.parent, versionIdOpt = Option(formVersionId))
            .map(_._4)
          allAnnotationDocVerIds = formVerSystemMetaRes.annotationMapping.values.toList
          pdfsSaMappingRes <- ZIOUtils.foreachParN(parallelism)(allAnnotationDocVerIds)(
            documentSaProfileMappingService.querySaProfileMappingByDocumentVersionId(actorId, _)
          )
        } yield pdfsSaMappingRes.flatten.headOption.map(_.saProfileId)
      )(formSaMapping => ZIO.succeed(Some(formSaMapping.saProfileId)))
      res <- saProfileService
        .getSaProfileByIds(
          actorId = actorId,
          ids = linkedSaProfileIdOpt.toList,
          ignorePermissionCheck = ignorePermissionCheck
        )
        .map(
          _.headOption
            .map { saProfileMsg =>
              val saList = SaCommonUtils.consolidateCheckboxRadioOptionsFromSaList(saProfileMsg.saList.toList)
              saList.map { sa =>
                StandardAliasInfoForCueGeneration(
                  alias = sa.alias,
                  label = sa.description,
                  cueTypeId = sa.saTypeId,
                  cueSchemaJson =
                    CueJsonCommonDataTypes.extractJsonOfStaticSchemaPart(sa.saTypeId, sa.saTypeJsonSchemaOpt),
                  isRepeatable = sa.isRepeatable
                )
              }
            }
            .getOrElse(List.empty)
        )
    } yield res
  }

  private def queryFormVersionSaMappingViaPdfs(
    actorId: UserId,
    formVersionId: FormVersionId,
    expectedProfileIdOpt: Option[SaProfileId]
  ): Task[Option[SingleSaProfileMappingResponse]] = {
    for {
      formResp <- formService.getForm(
        formId = formVersionId.parent,
        versionIdOpt = Option(formVersionId),
        actor = actorId,
        shouldCheckPermission = false
      )
      allAnnotationDocVerIds = formResp.systemMetadata.annotationMapping.values.toList
      pdfSaMappingsRes <- ZIOUtils.foreachParN(parallelism)(allAnnotationDocVerIds)(
        documentSaProfileMappingService.querySaProfileMappingByDocumentVersionId(actorId, _)
      )
      pdfSaMappingList = pdfSaMappingsRes.flatten
      pdfSaMappingWithProfileIdOpt = expectedProfileIdOpt
        .map { expectedSaProfileId =>
          expectedSaProfileId -> pdfSaMappingList.filter(_.saProfileId == expectedSaProfileId)
        }
        .orElse {
          // Chose the profile with most mapped PDFs
          pdfSaMappingList.groupBy(_.saProfileId).toList.maxByOption(_._2.size)
        }
      pdfMappingInfoOpt <- pdfSaMappingWithProfileIdOpt.fold(
        ZIO.succeed(Option.empty[SingleSaProfileMappingResponse])
      ) { case (profileId, mappingMsgList) =>
        val lastUpdatedInfoOpt = mappingMsgList
          .flatMap(msg => msg.lastUpdatedByOpt.zip(msg.lastUpdatedAtOpt))
          .maxByOption(_._2.toEpochMilli)

        saProfileService
          .getSaProfileByIds(actorId, List(profileId))
          .map(
            _.headOption.map { profileMsg =>
              SingleSaProfileMappingResponse(
                saProfileInfo = SaProfileFullInfo.fromSaProfileMsg(profileMsg),
                fieldToSaMap = mappingMsgList.map(_.fieldToSaMap).reduceOption(_ ++ _).getOrElse(Map.empty),
                lastUpdatedByOpt = lastUpdatedInfoOpt.map(_._1),
                lastUpdatedAtOpt = lastUpdatedInfoOpt.map(_._2)
              )
            }
          )
      }
    } yield pdfMappingInfoOpt.map { pdfMappingInfo =>
      // NOTE: Currently getAllPdfMappings
      // - Only returns single mapping for option values (though they could have multiple PDF mappings)
      // - Doesn't include repeatable mappings
      // Kinda out of the scope for this PR but when getAllPdfMappings is refactored, consider revising this function as well
      val allPdfMappings = FormDataUtils.getAllPdfMappings(formResp.formData)
      val saMappingFromAllPdfs = pdfMappingInfo.fieldToSaMap

      val formFieldSaMapping = allPdfMappings.flatMap { pdfMapping =>
        val fieldSaMappingSeq =
          pdfMapping.selfMappings.flatMap(saMappingFromAllPdfs.get).headOption.map(pdfMapping.fieldName -> _).toSeq
        val optionsSaMappingSeq = pdfMapping.optionMappings.toSeq.flatMap { case (optionValue, optionPdfMapping) =>
          saMappingFromAllPdfs
            .get(optionPdfMapping)
            .map(
              MatchedValue(pdfMapping.fieldName, optionValue).text -> _
            )
        }
        fieldSaMappingSeq ++ optionsSaMappingSeq
      }.toMap
      pdfMappingInfo.copy(fieldToSaMap = formFieldSaMapping)
    }
  }

  def querySaProfileMappingByFormId(
    actorId: UserId,
    formId: FormId,
    ignorePermissionCheck: Boolean = true
  ): Task[List[SaProfileMappingMessage]] = {
    for {
      _ <- ZIO.whenDiscard(!ignorePermissionCheck)(
        mappingAuthorizationService.validateFormPermission(formId, actorId)
      )
      destinationIds <- mappingDestinationService
        .queryMappingDestinationByResourceIdPrefix(formId.idString)
        .map(_.map(_.id))
      res <- ZIO.foreach(destinationIds)(commonSaProfileMappingService.querySaProfileMappingByDestinationId)
    } yield res.flatten
  }

}
