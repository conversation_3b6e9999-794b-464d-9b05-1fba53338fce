//  Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.storage

import com.apple.foundationdb.record.RecordMetaDataBuilder
import com.apple.foundationdb.record.metadata.{Index, Key}

import anduin.fdb.record.model.common.{RadixIdTupleConverter, UserIdTupleConverter}
import anduin.fdb.record.model.FDBTupleConverter
import anduin.fdb.record.{FDBRecordEnum, FDBRecordKeySpace, FDBRecordStoreProvider, FDBStoreProviderCompanion}
import anduin.forms.storage.data.user.{FormDataUserTempModel, FormDataUserTempProto}
import anduin.id.form.FormVersionDataId
import anduin.model.common.user.UserId

final case class FormDataUserTempStoreProvider(
  override protected val keySpace: FDBRecordKeySpace
) extends FDBRecordStoreProvider[FDBRecordEnum.FormDataUserTemp.type](
      FDBRecordEnum.FormDataUserTemp,
      FormDataUserTempProto
    ) {

  override protected def recordBuilderFn(builder: RecordMetaDataBuilder): Unit = {
    builder
      .getRecordType(FormDataUserTempModel.scalaDescriptor.name)
      .setPrimaryKey(FormDataUserTempStoreProvider.primaryKeyExpression)
  }

  override protected def indexes: Seq[IndexMappingWithVersion] = Seq(
    FormDataUserTempStoreProvider.primaryIndexMapping -> 1,
    FormDataUserTempStoreProvider.lastAccessedAtIndexMapping -> 2
  )

}

object FormDataUserTempStoreProvider extends FDBStoreProviderCompanion[FDBRecordEnum.FormDataUserTemp.type] {

  private val primaryKeyExpression = Key.Expressions.concat(
    Key.Expressions.field("owner"),
    Key.Expressions.field("form_data_id")
  )

  private val lastAccessedAtExpression = Key.Expressions
    .field("last_accessed_at")
    .nest(
      Key.Expressions.concat(
        scalarFieldNotNull("seconds"),
        scalarFieldNotNull("nanos")
      )
    )

  final case class FormDataUserTempPrimaryKey(
    owner: UserId,
    formDataId: FormVersionDataId
  )

  val primaryIndexMapping: IndexMapping[FormDataUserTempModel] =
    mappingIndexInstance(
      new Index("primary", primaryKeyExpression)
    )

  val lastAccessedAtIndexMapping: IndexMapping[FormDataUserTempModel] = {
    mappingIndexInstance(
      new Index(
        "last_accessed_at",
        lastAccessedAtExpression
      )
    )
  }

  private given formDataUserTempPrimaryKeyTupleConverter: FDBTupleConverter[FormDataUserTempPrimaryKey] = {
    UserIdTupleConverter.userIdTupleConverter
      .concat(RadixIdTupleConverter.instance[FormVersionDataId])
      .biMap { case (owner, formDataId) =>
        FormDataUserTempPrimaryKey(owner, formDataId)
      } { case FormDataUserTempPrimaryKey(owner, formDataId) =>
        (owner, formDataId)
      }
  }

  given formDataUserTempMapping: Mapping[FormDataUserTempPrimaryKey, FormDataUserTempModel] = mappingInstance

}
