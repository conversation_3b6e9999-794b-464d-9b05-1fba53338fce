//  Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.storage

import java.time.Instant

import anduin.fdb.record.FDBOperations
import anduin.fdb.record.model.{RecordIO, RecordTask}
import anduin.forms.storage.FormDataUserTempStoreProvider.{*, given}
import anduin.forms.storage.data.user.FormDataUserTempModel
import anduin.id.form.FormVersionDataId
import anduin.model.common.user.UserId

private[forms] final case class FormDataUserTempStoreOperations(store: Store) {

  def create(
    owner: UserId,
    formDataId: FormVersionDataId
  ): RecordTask[Unit] = {
    for {
      now <- RecordIO.succeed(Instant.now)
      _ <- store.create(
        FormDataUserTempModel(
          owner = owner,
          formDataId = formDataId,
          createdAt = now,
          lastAccessedAt = now
        )
      )
    } yield ()
  }

  def get(
    owner: UserId,
    formDataId: FormVersionDataId
  ): RecordTask[FormDataUserTempModel] = {
    store.get(
      FormDataUserTempStoreProvider.FormDataUserTempPrimaryKey(owner = owner, formDataId = formDataId)
    )
  }

  def getOpt(
    owner: UserId,
    formDataId: FormVersionDataId
  ): RecordTask[Option[FormDataUserTempModel]] = {
    store.getOpt(
      FormDataUserTempStoreProvider.FormDataUserTempPrimaryKey(owner = owner, formDataId = formDataId)
    )
  }

  def update(
    owner: UserId,
    formDataId: FormVersionDataId
  )(
    fn: FormDataUserTempModel => FormDataUserTempModel
  ): RecordTask[FormDataUserTempModel] = {
    store.update(FormDataUserTempStoreProvider.FormDataUserTempPrimaryKey(owner = owner, formDataId = formDataId), fn)
  }

  def delete(
    owner: UserId,
    formDataId: FormVersionDataId
  ): RecordTask[Boolean] = {
    store.delete(
      FormDataUserTempStoreProvider.FormDataUserTempPrimaryKey(owner = owner, formDataId = formDataId)
    )
  }

}

private[forms] object FormDataUserTempStoreOperations
    extends FDBOperations.Single[RecordEnum, FormDataUserTempStoreOperations](FormDataUserTempStoreProvider)
