// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.service

import java.time.Instant
import scala.io.Source

import sttp.model.MediaType
import com.github.tototoshi.csv.{CSVReader, DefaultCSVFormat}
import zio.temporal.workflow.ZWorkflowStub
import zio.{Task, ZIO}

import anduin.dms.DmsFeature.Public
import anduin.forms.utils.FormMappingUtils
import anduin.id.form.FormVersionId
import anduin.model.common.user.UserId
import anduin.model.id.{ComputeFormMatchingIdFactory, TemporalWorkflowId}
import anduin.autofill.storage.ComputeFormMatchingResultStoreOperations
import anduin.fdb.record.{FDBRecordDatabase, ReadOnlyCluster}
import anduin.portaluser.PortalUserService
import com.anduin.stargazer.service.GondorBackendConfig
import anduin.autofill.*
import anduin.autofill.ComputeFormMatchingResult.ComputeFormMatchingStatus
import anduin.autofill.workflow.impl.ComputeFormMatchingWorkflowImpl
import anduin.dms.service.FileService
import anduin.documentcontent.csv.CsvUtils
import anduin.forms.util.FormFieldMatchingUtils.*
import anduin.serverless.common.ServerlessModels.S3Access
import anduin.formmatching.FormMatchingMode
import anduin.id.role.portal.PortalSectionId
import anduin.serverless.common.ServerlessModels.formmatching.FormMatchingUtilsRequest.ComputeFormMatchingRequest
import anduin.serverless.functions.FormMatchingUtilsServerless
import anduin.serverless.utils.ServerlessUtils
import anduin.temporal.TemporalEnvironment
import anduin.forms.endpoint.{ExportFormMatchingParams, ExportFormMatchingResponse}
import anduin.ontology.service.FormSaProfileMappingService
import anduin.storageservice.common.FileContentOrigin

final case class FormMatchingService(
  protected val backendConfig: GondorBackendConfig,
  formService: FormService,
  fileService: FileService,
  portalUserService: PortalUserService,
  formMatchingUtilsServerless: FormMatchingUtilsServerless,
  formSaProfileMappingService: FormSaProfileMappingService,
  temporalEnvironment: TemporalEnvironment
) {
  private val AsaOntologyConversionLibraryPath = "/formmatching/ASA_To_Ontology_Conversion_Library.csv"

  private val csvFormat = new DefaultCSVFormat {
    override val delimiter: Char = ';'
    override val quoteChar: Char = '"'
  }

  private val ConvertAsaToOntologyMap = {
    val csvData = CSVReader
      .open(
        Source.fromInputStream(getClass.getResourceAsStream(AsaOntologyConversionLibraryPath))
      )(
        using csvFormat
      )
      .all()
    csvData.flatMap {
      case asa :: ontologyAnnotation :: Nil => Option(asa -> ontologyAnnotation)
      case _                                => None
    }.toMap
  }

  val formS3Access: S3Access = ServerlessUtils.getS3Access(backendConfig.aws.S3.formStorageBucket)

  def getAsaToOntologyConversionMap: Map[String, String] = ConvertAsaToOntologyMap

  private def getFormMatchingInfo(
    actorId: UserId,
    formVersionId: FormVersionId,
    formFullAsaMappings: Map[String, String] // Including fields/options named with ASA instead of being mapped to ASA
  ): Task[FormMatchingInfo] = {
    for {
      form <- formService
        .getForm(
          formVersionId.parent,
          Option(formVersionId),
          actorId,
          shouldCheckPermission = false
        )
        .map(_.formData.form)
    } yield FormMatchingInfo(
      formId = Right(formVersionId),
      fields = fieldMatchingInfoOfGaiaForm(form),
      asaMapping = formFullAsaMappings,
      repeatableMap = FormMappingUtils.getRepeatableMap(form)
    )
  }

  private def computeFormMatchingWithoutOntology(
    params: ComputeFormMatchingParams,
    useOldAsaOnly: Boolean = false
  ): Task[ComputeFormMatchingResult] = {
    for {
      srcFormMatchingInfo <- getFormMatchingInfo(params.actorId, params.srcFormVersionId, params.srcFormAsaMapping)
      destFormMatchingInfo <- getFormMatchingInfo(params.actorId, params.destFormVersionId, params.destFormAsaMapping)
    } yield {
      if (srcFormMatchingInfo.fields.nonEmpty && destFormMatchingInfo.fields.nonEmpty) {
        val asaMatchingAliases = FormMappingUtils.getAsaMatchingAliases(
          srcFormMatchingInfo.asaMapping,
          destFormMatchingInfo.asaMapping,
          srcFormMatchingInfo.repeatableMap,
          destFormMatchingInfo.repeatableMap
        )
        val (reusabilityScore, matchingScore, matchingAlias) = if (useOldAsaOnly) {
          val matchingAliases = asaMatchingAliases.view.mapValues(_.map(_ -> 1.0)).toMap
          val reusabilityScore = matchingAliases.size.toDouble / srcFormMatchingInfo.fields.size
          val matchingScore = matchingAliases.values.flatten.toSet.size.toDouble / destFormMatchingInfo.fields.size
          (reusabilityScore, matchingScore, matchingAliases)
        } else {
          val matchingRes = computeFieldMatchingScore(
            fromFormFields = srcFormMatchingInfo.fields,
            toFormFields = destFormMatchingInfo.fields,
            fromFormAsaMapping = srcFormMatchingInfo.asaMapping,
            toFormAsaMapping = destFormMatchingInfo.asaMapping,
            knownMatching = asaMatchingAliases
          )
          (matchingRes.reusabilityScore, matchingRes.matchingScore, matchingRes.matchingAlias)
        }
        val matchingValues =
          FormMappingUtils.getAsaMatchingValues(srcFormMatchingInfo.asaMapping, destFormMatchingInfo.asaMapping)
        ComputeFormMatchingResult(
          id = params.computeFormMatchingId,
          reusabilityScore = reusabilityScore,
          matchingScore = matchingScore,
          matchingAliasMap = matchingAlias.map { case (k, v) =>
            k -> MatchedAliasSet(
              v.map { case (alias, score) => MatchedAlias(alias, score) }
            )
          },
          matchingValueMap = matchingValues.map { case (k, v) =>
            k.text -> StringSet(v.map(_.text))
          },
          status = ComputeFormMatchingResult.ComputeFormMatchingStatus.COMPLETED,
          lastUpdatedAt = Option(Instant.now)
        )
      } else {
        ComputeFormMatchingResult(
          id = params.computeFormMatchingId,
          status = ComputeFormMatchingResult.ComputeFormMatchingStatus.COMPLETED,
          lastUpdatedAt = Option(Instant.now)
        )
      }
    }
  }

  // TODO @trancuong81 Get list of "term synonyms" here
  private def computeFormMatchingWithOntology(
    params: ComputeFormMatchingParams
  ): Task[ComputeFormMatchingResult] = {
    val srcFormVersionId = params.srcFormVersionId
    val destFormVersionId = params.destFormVersionId
    for {
      srcForm <- formService
        .getForm(
          srcFormVersionId.parent,
          Option(srcFormVersionId),
          params.actorId,
          shouldCheckPermission = false
        )
        .map(_.formData.form)
      destForm <- formService
        .getForm(
          destFormVersionId.parent,
          Option(destFormVersionId),
          params.actorId,
          shouldCheckPermission = false
        )
        .map(_.formData.form)
    } yield {
      val srcFields = FormMappingUtils.traverseExpandRepeatables(srcForm)
      val destFields = FormMappingUtils.traverseExpandRepeatables(destForm)

      if (srcFields.nonEmpty && destFields.nonEmpty) {
        val matchingAliases =
          getOntologyAsaMatchingAliases(params.srcFormAsaMapping, params.destFormAsaMapping, srcForm, destForm)
        val reusabilityScore = matchingAliases.size.toDouble / srcFields.size
        val matchingScore = matchingAliases.values.flatten.toSet.size.toDouble / destFields.size
        val matchingValues =
          getOntologyAsaMatchingValues(params.srcFormAsaMapping, params.destFormAsaMapping)

        ComputeFormMatchingResult(
          id = params.computeFormMatchingId,
          reusabilityScore = reusabilityScore,
          matchingScore = matchingScore,
          matchingAliasMap = matchingAliases.map { case (k, v) =>
            k -> MatchedAliasSet(
              v.map { case (alias, score) => MatchedAlias(alias, score) }
            )
          },
          // NOTE: ATM, no confidence score for matching value map -> revise as needed
          matchingValueMap = matchingValues.map { case (k, v) =>
            k.text -> StringSet(v.map(_._1.text))
          },
          status = ComputeFormMatchingResult.ComputeFormMatchingStatus.COMPLETED,
          lastUpdatedAt = Option(Instant.now)
        )
      } else {
        ComputeFormMatchingResult(
          id = params.computeFormMatchingId,
          status = ComputeFormMatchingResult.ComputeFormMatchingStatus.COMPLETED,
          lastUpdatedAt = Option(Instant.now)
        )
      }
    }
  }

  private def computeFormMatchingServerless(
    params: ComputeFormMatchingParams
  ): Task[ComputeFormMatchingResult] = {
    for {
      srcFormKey <- formService.getFormVersionStorageKeyUnsafe(params.srcFormVersionId)
      destFormKey <- formService.getFormVersionStorageKeyUnsafe(params.destFormVersionId)
      res <- formMatchingUtilsServerless.computeFormMatching(
        ComputeFormMatchingRequest(
          s3Access = formS3Access,
          srcFormKey = srcFormKey,
          destFormKey = destFormKey,
          srcFormAsaMapping = params.srcFormAsaMapping,
          destFormAsaMapping = params.destFormAsaMapping,
          matchingMode = params.matchingMode match {
            case ComputeFormMatchingMode.UseOldAsaOnly      => FormMatchingMode.UseOldAsaOnly
            case ComputeFormMatchingMode.UseAsaOntologyOnly => FormMatchingMode.UseAsaOntologyOnly
            case ComputeFormMatchingMode.AliasAsaHeuristic  => FormMatchingMode.AliasAsaHeuristic
            case _                                          => FormMatchingMode.AutoSelect
          }
        )
      )
    } yield ComputeFormMatchingResult(
      id = params.computeFormMatchingId,
      reusabilityScore = res.reusabilityScore,
      matchingScore = res.matchingScore,
      matchingAliasMap = res.matchingAliasMap.map { case (k, v) =>
        k -> MatchedAliasSet(
          v.map { case (alias, score) => MatchedAlias(alias, score) }
        )
      },
      matchingValueMap = res.matchingValueMap.map { case (k, v) =>
        k -> StringSet(v)
      },
      status = ComputeFormMatchingResult.ComputeFormMatchingStatus.COMPLETED,
      lastUpdatedAt = Option(Instant.now)
    )
  }

  def computeFormMatchingTask(
    params: ComputeFormMatchingParams
  ): Task[ComputeFormMatchingResult] = {
    val formMatchingResultId = params.computeFormMatchingId
    for {
      _ <- FDBRecordDatabase.transact(ComputeFormMatchingResultStoreOperations.Production) { ops =>
        ops.update(
          formMatchingResultId,
          _.copy(
            status = ComputeFormMatchingResult.ComputeFormMatchingStatus.IN_PROGRESS,
            lastUpdatedAt = Option(Instant.now)
          )
        )
      }
      res <-
        if (params.useServerless) {
          computeFormMatchingServerless(params)
        } else {
          params.matchingMode match {
            case ComputeFormMatchingMode.UseOldAsaOnly =>
              computeFormMatchingWithoutOntology(params, useOldAsaOnly = true)
            case ComputeFormMatchingMode.UseAsaOntologyOnly | ComputeFormMatchingMode.AutoSelect =>
              computeFormMatchingWithOntology(params)
            case _ =>
              computeFormMatchingWithoutOntology(params)
          }
        }
      _ <- FDBRecordDatabase.transact(ComputeFormMatchingResultStoreOperations.Production) { ops =>
        ops.update(
          formMatchingResultId,
          _ => res
        )
      }
    } yield res
  }

  private def getComputeFormMatchingParams(
    actorId: UserId,
    srcFormVersionId: FormVersionId,
    destFormVersionId: FormVersionId,
    matchingMode: ComputeFormMatchingMode,
    useServerless: Boolean
  ) = {
    val getAsaMappingInfoTask = matchingMode match {
      case ComputeFormMatchingMode.AliasAsaHeuristic | ComputeFormMatchingMode.UseOldAsaOnly =>
        for {
          srcAsaMapRes <- formService.getFullFormAsaMapping(actorId, srcFormVersionId)
          destAsaMapRes <- formService.getFullFormAsaMapping(actorId, destFormVersionId)
          srcSaProfileMapResOpt <- formSaProfileMappingService.queryFormVersionSaMappingInfoOpt(
            actorId,
            srcFormVersionId
          )
          destSaProfileMapResOpt <- formSaProfileMappingService.queryFormVersionSaMappingInfoOpt(
            actorId,
            destFormVersionId
          )
        } yield {
          val srcSaProfileMapping = srcSaProfileMapResOpt.map(_.fieldToSaMap).getOrElse(Map.empty)
          val destSaProfileMapping = destSaProfileMapResOpt.map(_.fieldToSaMap).getOrElse(Map.empty)

          // Combine info from SA profile mapping & ASA mapping
          if (srcSaProfileMapping.nonEmpty && destSaProfileMapping.nonEmpty) {
            // SaProfile mapping should have higher priority since it's customer specific vs. the generic ASA
            // (i.e., if a field is mapped to both SaProfile & ASA, the SaProfile mapping will overwrite
            val srcMapping = srcAsaMapRes.aliasMapping ++ srcSaProfileMapping
            val destMapping = destAsaMapRes.aliasMapping ++ destSaProfileMapping
            val srcLastUpdatedAt =
              List(Some(srcAsaMapRes.lastUpdatedAt), srcSaProfileMapResOpt.flatMap(_.lastUpdatedAtOpt)).flatten
                .maxBy(_.toEpochMilli)
            val destLastUpdatedAt =
              List(Some(destAsaMapRes.lastUpdatedAt), destSaProfileMapResOpt.flatMap(_.lastUpdatedAtOpt)).flatten
                .maxBy(_.toEpochMilli)

            (
              srcMapping,
              srcLastUpdatedAt,
              destMapping,
              destLastUpdatedAt,
              matchingMode
            )
          } else {
            (
              srcAsaMapRes.aliasMapping,
              srcAsaMapRes.lastUpdatedAt,
              destAsaMapRes.aliasMapping,
              destAsaMapRes.lastUpdatedAt,
              matchingMode
            )
          }
        }
      case ComputeFormMatchingMode.UseAsaOntologyOnly =>
        for {
          srcOntologyAsaMapRes <- formService.formAsaMappingService.exportFormVersionMappings(
            srcFormVersionId.parent,
            Option(srcFormVersionId),
            actorId
          )
          destOntologyAsaMapRes <- formService.formAsaMappingService.exportFormVersionMappings(
            destFormVersionId.parent,
            Option(destFormVersionId),
            actorId
          )
        } yield (
          srcOntologyAsaMapRes.aliasMapping,
          srcOntologyAsaMapRes.lastUpdatedAt,
          destOntologyAsaMapRes.aliasMapping,
          destOntologyAsaMapRes.lastUpdatedAt,
          matchingMode
        )
      case ComputeFormMatchingMode.AutoSelect =>
        // TODO @trancuong81 Consider whether/how we wanna integrate SA profile mapping info for this mode (it's not actually used yet)
        for {
          srcAsaMapRes <- formService.getFullFormAsaMapping(actorId, srcFormVersionId)
          destAsaMapRes <- formService.getFullFormAsaMapping(actorId, destFormVersionId)
          srcOntologyAsaMapRes <- formService.formAsaMappingService.exportFormVersionMappings(
            srcFormVersionId.parent,
            Option(srcFormVersionId),
            actorId
          )
          destOntologyAsaMapRes <- formService.formAsaMappingService.exportFormVersionMappings(
            destFormVersionId.parent,
            Option(destFormVersionId),
            actorId
          )
        } yield {
          if (srcOntologyAsaMapRes.aliasMapping.isEmpty && destOntologyAsaMapRes.aliasMapping.isEmpty) {
            (
              srcAsaMapRes.aliasMapping,
              srcAsaMapRes.lastUpdatedAt,
              destAsaMapRes.aliasMapping,
              destAsaMapRes.lastUpdatedAt,
              ComputeFormMatchingMode.AliasAsaHeuristic
            )
          } else if (srcAsaMapRes.aliasMapping.isEmpty && destAsaMapRes.aliasMapping.isEmpty) {
            (
              srcOntologyAsaMapRes.aliasMapping,
              srcOntologyAsaMapRes.lastUpdatedAt,
              destOntologyAsaMapRes.aliasMapping,
              destOntologyAsaMapRes.lastUpdatedAt,
              ComputeFormMatchingMode.UseAsaOntologyOnly
            )
          } else {
            val srcAsaMap = srcOntologyAsaMapRes.aliasMapping ++
              srcAsaMapRes.aliasMapping.flatMap { case (k, v) => ConvertAsaToOntologyMap.get(v).map(k -> _) }
            val destAsaMap = destOntologyAsaMapRes.aliasMapping ++
              destAsaMapRes.aliasMapping.flatMap { case (k, v) => ConvertAsaToOntologyMap.get(v).map(k -> _) }
            val srcAsaLastUpdatedAt =
              if (srcOntologyAsaMapRes.lastUpdatedAt.toEpochMilli > srcAsaMapRes.lastUpdatedAt.toEpochMilli) {
                srcOntologyAsaMapRes.lastUpdatedAt
              } else {
                srcAsaMapRes.lastUpdatedAt
              }
            val destAsaLastUpdatedAt =
              if (destOntologyAsaMapRes.lastUpdatedAt.toEpochMilli > destAsaMapRes.lastUpdatedAt.toEpochMilli) {
                destOntologyAsaMapRes.lastUpdatedAt
              } else {
                destAsaMapRes.lastUpdatedAt
              }
            (srcAsaMap, srcAsaLastUpdatedAt, destAsaMap, destAsaLastUpdatedAt, matchingMode)
          }
        }
      case ComputeFormMatchingMode.Unrecognized(_) =>
        ZIO.succeed(
          (
            Map.empty[String, String],
            Instant.EPOCH,
            Map.empty[String, String],
            Instant.EPOCH,
            ComputeFormMatchingMode.AliasAsaHeuristic
          )
        )
    }

    for {
      (srcAsaMap, srcAsaLastUpdated, destAsaMap, destAsaLastUpdated, newMatchingMode) <- getAsaMappingInfoTask
    } yield {
      val computeFormMatchingId = ComputeFormMatchingIdFactory.generate(
        srcFormVersionId,
        srcAsaLastUpdated,
        destFormVersionId,
        destAsaLastUpdated,
        matchingModeOpt = Option.when(newMatchingMode != ComputeFormMatchingMode.AliasAsaHeuristic)(newMatchingMode.name)
      )
      ComputeFormMatchingParams(
        actorId,
        computeFormMatchingId,
        srcFormVersionId,
        destFormVersionId,
        srcAsaMap,
        Option(srcAsaLastUpdated),
        destAsaMap,
        Option(destAsaLastUpdated),
        newMatchingMode,
        useServerless
      )
    }
  }

  def getComputeFormMatching(
    actorId: UserId,
    srcFormVersionId: FormVersionId,
    destFormVersionId: FormVersionId,
    matchingMode: ComputeFormMatchingMode,
    runAsync: Boolean = false,
    useServerless: Boolean = false
  ): Task[ComputeFormMatchingResult] = {
    for {
      computeParams <- getComputeFormMatchingParams(
        actorId,
        srcFormVersionId,
        destFormVersionId,
        matchingMode,
        useServerless
      )
      computeFormMatchingId = computeParams.computeFormMatchingId
      curFormMatchingRes <- FDBRecordDatabase.transact(ComputeFormMatchingResultStoreOperations.Production)(
        _.getOrAdd(computeFormMatchingId)
      )
      res <-
        if (runAsync) {
          curFormMatchingRes.status match {
            case ComputeFormMatchingStatus.COMPLETED | ComputeFormMatchingStatus.IN_PROGRESS =>
              ZIO.succeed(curFormMatchingRes)
            case ComputeFormMatchingStatus.NOT_STARTED | ComputeFormMatchingStatus.FAILED |
                ComputeFormMatchingStatus.Unrecognized(_) =>
              for {
                workflowStub <- ComputeFormMatchingWorkflowImpl.instance
                  .getWorkflowStub(TemporalWorkflowId.unsafeFromSuffix(computeFormMatchingId.idString))
                  .provideEnvironment(temporalEnvironment.workflowClient)
                _ <- ZWorkflowStub.start(workflowStub.run(computeParams))
              } yield curFormMatchingRes.copy(
                status = ComputeFormMatchingStatus.IN_PROGRESS,
                lastUpdatedAt = Option(Instant.now)
              )
          }
        } else {
          curFormMatchingRes.status match {
            case ComputeFormMatchingStatus.COMPLETED =>
              ZIO.succeed(curFormMatchingRes)
            case _ => computeFormMatchingTask(computeParams)
          }
        }
    } yield res
  }

  def exportFormMatching(
    actorId: UserId,
    params: ExportFormMatchingParams
  ): Task[ExportFormMatchingResponse] = {
    val exportFileName =
      s"exported_matchings_src_${params.srcFormVersionId.idString}" +
        s"_dest_${params.destFormVersionId.idString}_${params.matchingMode.toString()}"

    for {
      _ <- portalUserService.validateReadPermission(actorId, PortalSectionId.Form)
      matchingRes <- getComputeFormMatching(
        actorId = actorId,
        srcFormVersionId = params.srcFormVersionId,
        destFormVersionId = params.destFormVersionId,
        matchingMode = params.matchingMode match {
          case FormMatchingMode.UseOldAsaOnly      => ComputeFormMatchingMode.UseOldAsaOnly
          case FormMatchingMode.UseAsaOntologyOnly => ComputeFormMatchingMode.UseAsaOntologyOnly
          case FormMatchingMode.AliasAsaHeuristic  => ComputeFormMatchingMode.AliasAsaHeuristic
          case _                                   => ComputeFormMatchingMode.AutoSelect
        },
        useServerless = params.useServerless
      )
      headerRow = List(
        f"Source alias (reusability score ${matchingRes.reusabilityScore * 100}%.1f)",
        f"Target aliases separated by new line (matching score ${matchingRes.matchingScore * 100}%.1f)"
      )
      matchingAliasList = matchingRes.matchingAliasMap.map { case (k, v) =>
        List(k, v.items.map(item => f"${item.alias}#${item.similarity}%.1f").mkString("\n"))
      }.toList
      matchingValueList = matchingRes.matchingValueMap.map { case (k, v) =>
        List(k, v.items.map(item => s"$item#2.0").mkString("\n"))
      }.toList
      csvData <- CsvUtils.createCsvTask(headerRow +: (matchingAliasList ++ matchingValueList))
      userFolderId <- fileService.createUserTemporaryFolderIfNeeded(actorId)
      exportedFileId <- fileService.uploadFile(
        userFolderId,
        exportFileName + ".csv",
        FileContentOrigin.FromSource(
          csvData,
          MediaType("text", "csv")
        ),
        actorId
      )
    } yield ExportFormMatchingResponse(
      reusabilityScore = matchingRes.reusabilityScore,
      matchingScore = matchingRes.matchingScore,
      exportedFileId = exportedFileId
    )
  }

}
