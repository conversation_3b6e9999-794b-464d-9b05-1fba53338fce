package anduin.forms.service

import java.time.Instant

import sttp.model.MediaType
import zio.{NonEmptyChunk, Task, ZIO, ZLayer}

import anduin.account.profile.UserProfileService
import anduin.annotation.service.AnnotationDocumentService
import anduin.blueprint.service.BlueprintFileService
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.dms.tracking.DmsTrackingActivityType.Internal
import anduin.documentcontent.pdf.{FillPdfService, PdfBoxHelper}
import anduin.documentcontent.spreadsheet.SpreadsheetUtils
import anduin.documentservice.pdf.PdfFileService
import anduin.fdb.record.model.{RecordIO, RecordReadIO}
import anduin.fdb.record.{FDBCluster, FDBCommonDatabase, FDBOperations, FDBRecordDatabase}
import anduin.forms.analytics.FormMappingAnalyticService
import anduin.forms.annotation.PdfObjectId
import anduin.forms.endpoint.*
import anduin.forms.model.FormModel.FundEngagementType
import anduin.forms.model.activity.{FormActivityModel, FormCreated, FormStatusUpdated}
import anduin.forms.model.annotation.AnnotationDocumentModels.AnnotationDocumentData
import anduin.forms.model.standardaliasmapping.*
import anduin.forms.model.utils.SystemTag
import anduin.forms.model.{AssociatedLink, FormFieldMappingTarget, FormModel, Schema}
import anduin.forms.storage.*
import anduin.forms.ui.{UIKey, WidgetType}
import anduin.forms.util.AnnotationImporter.ConversionResult
import anduin.forms.util.{AnnotationImporter, FormSaUtils, FormImportUtils, FormUtils}
import anduin.forms.utils.*
import anduin.forms.version.FormVersionMetadataModel.{PdfCutInfo, WidgetSource}
import anduin.forms.version.FormVersionModel.FormType
import anduin.forms.version.{BlueprintRef, FormVersionMetadataModel, FormVersionModel, FormVersionSystemMetadataModel}
import anduin.forms.{ExtractedPdf, Form, FormData, FormFieldState}
import anduin.id.annotation.AnnotationDocumentVersionId
import anduin.id.blueprint.BlueprintId
import anduin.id.form.*
import anduin.id.fundsub.FundSubFormIdTrait
import anduin.id.role.portal.PortalSectionId
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.model.id.*
import anduin.ontology.service.{AsaService, FormAsaMappingService}
import anduin.portaluser.{ExecutiveAdmin, PortalUserService}
import anduin.protobuf.flow.file.{FileFolderPermission, FileFolderPermissionMap}
import anduin.protobuf.ontology.{AsaRelationType, ForFieldValueFlag, ForFsWorkflowSetupFlag}
import anduin.protobuf.signature.{DocumentSignatureMessage, SignatureMessage}
import anduin.serverless.common.ServerlessModels.muPdf.{
  MergePdfFileParam,
  MergePdfRequest,
  PageRange,
  PdfPageCountRequest
}
import anduin.serverless.functions.MuPDFServerless
import anduin.serverless.utils.ServerlessUtils
import anduin.service.{AuthenticatedRequestContext, GeneralServiceException}
import anduin.signature.integration.SignatureIntegrationService
import anduin.storageservice.common.FileContentOrigin
import anduin.storageservice.s3.S3Service
import anduin.util.FilenameUtils
import anduin.utils.stream.ZStreamIOUtils
import com.anduin.stargazer.endpoints.PermissionChanges
import com.anduin.stargazer.service.GondorConfig
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.util.date.DateCalculator

final case class FormService(
  gondorConfig: GondorConfig,
  fileService: FileService,
  userProfileService: UserProfileService,
  portalUserService: PortalUserService,
  executiveAdmin: ExecutiveAdmin,
  formLockService: FormLockService,
  formStorageService: FormStorageService,
  asaService: AsaService,
  pdfAnnotationService: PdfAnnotationService,
  annotationDocumentService: AnnotationDocumentService,
  muPdfServerless: MuPDFServerless,
  formAsaMappingService: FormAsaMappingService,
  formIntegrationService: FormIntegrationService,
  s3Service: S3Service,
  signatureIntegrationService: SignatureIntegrationService,
  blueprintService: BlueprintFileService
)(
  using val pdfFileService: PdfFileService,
  val fillPdfService: FillPdfService
) {

  given FileService = fileService

  private val Separator: String = ", "

  // No need version id for draft, this will be changed later once we separate this from the main form model
  private val draftMetadataId = FormVersionMetadataId.defaultValue.get
  private val draftSystemMetadataId = FormVersionSystemMetadataId.defaultValue.get

  def processFile(
    fileIds: Seq[FileId],
    actor: UserId,
    parallelism: Int = 8,
    needOriginalFile: Boolean = true
  ): Task[ProcessFileResponse] = {
    val fileStr = fileIds.map(_.idString).mkString(Separator)
    for {
      _ <- ZIO.logInfo(s"${actor.id} is processing file $fileStr")
      files <- ZIO
        .foreachPar(fileIds) { fileId =>
          for {
            fileName <- fileService.getFileName(actor)(fileId)
            originalSysFileIdOpt <-
              if (needOriginalFile) {
                for {
                  originalStorageId <- fileService.getFileStorageId(
                    actor,
                    fileId,
                    DmsTrackingActivityType.Internal,
                    None
                  )
                  originalFileId <- fileService.uploadFile(
                    fileId.parent,
                    FilenameUtils.getPdfFilename(s"${FilenameUtils.getName(fileName)}"),
                    FileContentOrigin.FromStorageId(originalStorageId, gondorConfig.backendConfig.aws.S3.bucket),
                    actor
                  )
                } yield Some(originalFileId)
              } else {
                ZIO.none
              }
            fields <- ZIO.scoped {
              for {
                fileSource <- fileService.getFileSource(actor, fileId)
                fields <- FormUtils.extractFormFromPdf(fileSource)
              } yield fields
            }
            _ <- pdfAnnotationService.removeHelperAnnotations(fileId, actor)
          } yield {
            (
              fileId,
              ExtractedPdf(
                fileName,
                fields,
                originalSysFileIdOpt.map(FormDataConverters.fileIdToForm)
              )
            )
          }
        }
        .withParallelism(parallelism)
    } yield ProcessFileResponse(files.toMap)
  }

  def processAnnotationDocument(
    formId: FormId,
    annotationDocumentVersionId: AnnotationDocumentVersionId,
    fileNameOverride: Option[String] = None,
    actor: UserId
  ): Task[ProcessAnnotationDocumentResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"${actor.id} is processing file from annotation document ${annotationDocumentVersionId.idString}"
      )
      folderId <- ZIO.attempt(FolderId.channelSystemFolderId(formId))
      (annotatedFileId, annotationDocumentData, annotationDocumentModel) <- annotationDocumentService
        .generateAnnotatedDocument(
          Some(folderId),
          annotationDocumentVersionId,
          actor
        )(
          using AnnotationDocumentService.KeySpace.Production
        )
        .map(_.fileId)
        .zipPar {
          annotationDocumentService
            .getAnnotationData(
              annotationDocumentVersionId.parent,
              Some(annotationDocumentVersionId),
              actor
            )(
              using AnnotationDocumentService.KeySpace.Production
            )
        }
        .zipPar {
          annotationDocumentService
            .getAnnotationDocumentModel(
              annotationDocumentVersionId.parent,
              Some(annotationDocumentVersionId),
              actor
            )(
              using AnnotationDocumentService.KeySpace.Production
            )
            .map(_._1)
        }
      extractedPdf <- ZIO.attempt {
        val extractedFields = PdfFieldConversionUtils.pdfObjectsToExtractedFields(annotationDocumentData.pdfObjects)
        ExtractedPdf(
          FilenameUtils.getPdfFilename(fileNameOverride.getOrElse(annotationDocumentModel.name)),
          extractedFields,
          None
        )
      }
    } yield ProcessAnnotationDocumentResponse(Map(annotatedFileId -> extractedPdf))
  }

  def extractText(
    fileIds: Seq[FileId],
    actor: UserId
  ): Task[ExtractTextResponse] = {
    ZIO
      .foreachPar(fileIds) { fileId =>
        ZIO.scoped {
          for {
            fileSource <- fileService.getFileSource(actor, fileId)
            texts <- FormUtils.extractTextFromDocx(fileSource)
          } yield fileId -> texts
        }
      }
      .map(texts => ExtractTextResponse(texts.toMap))
  }

  def createForm(
    name: String,
    tags: Set[String],
    owners: Seq[EmailAddress],
    actor: UserId,
    formIdOpt: Option[FormId] = None,
    parentFolderIdOpt: Option[FormFolderId] = None,
    fundEngagementTypeOpt: Option[FundEngagementType] = None,
    numFundraiseOpt: Option[Int] = None,
    systemTags: Set[SystemTag] = Set.empty
  ): Task[CreateFormResponse] = {
    val formId = formIdOpt.getOrElse(FormIdFactory.unsafeRandomId)
    val parentFolderId = parentFolderIdOpt.getOrElse(FormFolderId(FormFolderId.RootFolderId))
    val formVersionId = FormVersionIdFactory.unsafeRandomId(formId)

    for {
      _ <- ZIO.logInfo(s"${actor.id} is creating new form: $name")
      _ <- portalUserService.validateWritePermission(actor, PortalSectionId.Form)
      storageUniqueKey <- formStorageService.uploadInitialDraft(
        formId,
        actor,
        FormData()
      )
      _ <- fileService.createSystemFolderForChannel(
        channel = formId,
        folderName = formId.idString,
        creator = actor,
        permissionOpts = FileFolderPermissionMap()
      )
      author <- userProfileService.getEmailAddress(actor)
      instantNow <- ZIO.attempt(DateCalculator.instantNow)
      formModel <- FDBRecordDatabase.transact(
        FDBOperations[
          (
            ((FormModelStoreOperations, FormVersionStoreOperations), AssetLockStoreOperations),
            FormActivityStoreOperations
          )
        ].Production
      ) { case (((formStoreOps, versionStoreOps), lockOps), activityOps) =>
        for {
          _ <- formStoreOps.create(
            FormModel(
              formId = formId,
              name = name,
              createdAt = Some(instantNow),
              owners = owners,
              tags = tags,
              lastEditedAt = Some(instantNow),
              lastEditor = Some(actor),
              latestVersionId = formVersionId,
              storageUniqueKey = storageUniqueKey,
              parentFolderId = parentFolderId,
              fundEngagementType = fundEngagementTypeOpt,
              numFundraise = numFundraiseOpt.orElse(Some(1)),
              systemTags = systemTags.map { systemTag =>
                anduin.utils.SystemTag.fromValue(systemTag.value)
              }
            )
          )
          _ <- versionStoreOps.createVersion(
            FormVersionModel(
              formVersionId = formVersionId,
              formId = formId,
              name = "Form created",
              createdAt = Some(instantNow),
              author = Some(author),
              parentVersionId = None,
              formType = FormType.Default
            )
          )
          _ <- lockOps.create(assetId = formId)
          _ <- activityOps.create(
            FormActivityModel(
              formActivityId = FormActivityIdFactory.unsafeRandomId(formId),
              formId = formId,
              actor = actor,
              at = Some(instantNow),
              data = FormCreated()
            )
          )
          formModel <- formStoreOps.get(formId)
        } yield formModel
      }
      _ <- ZIO.logInfo(s"Form ${formId.idString} created")
    } yield CreateFormResponse(formVersionId, formModel)
  }

  def startEditForm(
    params: StartEditFormParams,
    actor: UserId
  )(
    using FDBCluster
  ): Task[StartEditFormResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is starting editing form ${params.formId}")
      _ <- validateFormPermission(params.formId, actor)
      formCheckUpToDateData <- getFormCheckUpToDateData(params.formId)

      lockResult <-
        if (formCheckUpToDateData != params.checkData) {
          ZIO.succeed(GetLockResult.AssetOutdated)
        } else {
          formLockService.getLock(
            params.formId,
            actor,
            params.sessionID
          )
        }
      _ <-
        lockResult match {
          case GetLockResult.Granted(_) =>
            ZIO.logInfo(s"${actor.id} is successful to request a lock for editing form ${params.formId}")
          case GetLockResult.AssetLocked(lockedUser, session, _) =>
            ZIO.logInfo(
              s"${actor.id} in session ${params.sessionID.idStr}" +
                s" failed to request a lock for editing form ${params.formId} as ${lockedUser.id} in session ${session.getOrElse(" ")} is editing"
            )
          case GetLockResult.AssetOutdated =>
            ZIO.logInfo(s"form ${params.formId} is outdated, actor ${actor.idString} need to reload the page")
        }
    } yield StartEditFormResponse(
      lockResult = lockResult
    )
  }

  def editForm(
    formId: FormId,
    name: String,
    tags: Set[String],
    owners: Seq[EmailAddress],
    actor: UserId,
    parentFolderIdOpt: Option[FormFolderId] = None,
    fundEngagementTypeOpt: Option[FundEngagementType] = None,
    numFundraiseOpt: Option[Int] = None
  )(
    using FDBCluster
  ): Task[EditFormResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is editing form ${formId.idString}")
      _ <- validateFormPermission(formId, actor)
      updatedModel <- FDBRecordDatabase.transact(FormModelStoreOperations.Production)(
        _.update(formId) { model =>
          model.copy(
            name = name,
            tags = tags,
            owners = owners,
            lastEditedAt = Some(DateCalculator.instantNow),
            lastEditor = Some(actor),
            parentFolderId = parentFolderIdOpt.getOrElse(model.parentFolderId),
            fundEngagementType = fundEngagementTypeOpt.orElse(model.fundEngagementType),
            numFundraise = numFundraiseOpt.orElse(model.numFundraise)
          )
        }
      )
      _ <- ZIO.logInfo(s"Form ${formId.idString} edited")
    } yield EditFormResponse(updatedModel)
  }

  def duplicateForm(
    sourceFormVersionId: FormVersionId,
    name: String,
    tags: Set[String],
    owners: Seq[EmailAddress],
    actor: UserId,
    sourceUrl: String,
    parentFolderIdOpt: Option[FormFolderId] = None,
    fundEngagementTypeOpt: Option[FundEngagementType] = None,
    numFundraiseOpt: Option[Int] = None
  )(
    using FDBCluster
  ): Task[DuplicateFormResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is duplicating from form ${sourceFormVersionId.idString}")
      sourceFormResp <- getForm(
        formId = sourceFormVersionId.parent,
        versionIdOpt = Some(sourceFormVersionId),
        actor = actor
      )
      firstVersionId <- createForm(
        name,
        tags,
        owners,
        actor,
        parentFolderIdOpt = parentFolderIdOpt,
        fundEngagementTypeOpt = fundEngagementTypeOpt,
        numFundraiseOpt = numFundraiseOpt
      ).map(_.formVersionId)
      formId = firstVersionId.parent
      (newFormData, newAnnotationMapping) <- transferFileForNewForm(
        formId = formId,
        formData = sourceFormResp.formData,
        actor = actor,
        annotationMapping = sourceFormResp.systemMetadata.annotationMapping
      )
      allKeys <- ZIO.attempt(FormDataUtils.findAllKeys(newFormData.form))
      _ <- saveFormDataInternal(
        formId = formId,
        formData = newFormData,
        metadataOpt = Some(
          (
            sourceFormResp.metadata.copy(widgetSources = allKeys.map(_ -> WidgetSource.COPY).toMap),
            sourceFormResp.systemMetadata.copy(
              annotationMapping = newAnnotationMapping,
              cueMappingId = None
            )
          )
        ),
        parentVersionIdOpt = Some(firstVersionId),
        name = name,
        actor = actor,
        shouldCheckForTransferOntologyAsaMapping = false,
        formTypeOpt = sourceFormResp.versionOpt.map(_.formType)
      )
      sourceFormVersionNumber = sourceFormResp.versionOpt.map(_.versionNumber).getOrElse(1)
      duplicatedFormVersionId <- createFormVersionInternal(
        formId = formId,
        name = s"Duplicated_version $sourceFormVersionNumber",
        note = s"Duplicated from: ${sourceFormResp.formModel.name} - Version $sourceFormVersionNumber - $sourceUrl",
        actor = actor,
        formVersionIdOpt = None,
        sourceFormVersionId = sourceFormVersionId,
        shouldTransferOntologyAsaMapping = false
      ).map(_.versionId)
      // Duplicate ASA mapping from source
      srcAsaMapping <- getFormStandardAliasMapping(sourceFormVersionId, checkValidFormFieldsAndOptions = false)
      _ <- ZIOUtils.when(srcAsaMapping.nonEmpty)(
        addFormStandardAliasMapping(
          AddFormStandardAliasMappingParams(duplicatedFormVersionId, srcAsaMapping),
          actor
        )
      )
      formModel <- FDBCommonDatabase().read(FormModelStoreOperations.Production)(_.get(formId))
    } yield DuplicateFormResponse(formModel)
  }

  private[anduin] def transferFileForNewForm(
    formId: FormId,
    formData: FormData,
    actor: UserId,
    annotationMapping: Map[FileId, AnnotationDocumentVersionId]
  ) = {
    for {
      allFileIds <- ZIO.attempt(
        formData.uploadedPdf.keySet ++
          formData.uploadedPdf.flatMap(_._2.originalFileIdOpt) ++
          formData.embeddedPdf.values
      )
      allFiles <- ZIO.attempt(
        allFileIds.flatMap(FormDataConverters.fileIdTypeToFileId)
      )
      folderId <- ZIO.attempt(FolderId.channelSystemFolderId(formId))
      newFileIdMap <- ZIO
        .foreach(allFiles) { fileId =>
          fileService
            .copyFileInSameFeature(
              actor = actor,
              originFileId = fileId,
              folderId = folderId,
              ctx = None
            )
            .map { r => FormDataConverters.fileIdToForm(fileId) -> FormDataConverters.fileIdToForm(r) }
        }
        .map(_.toMap)
    } yield {
      val replacePdfForm = formData.replaceUploadedPdf(newFileIdMap)
      // TODO: @hiepbui to update this logic inside replaceUploadedPdf of the gaia repository
      val newFormData = replacePdfForm.copy(
        uploadedPdf = replacePdfForm.uploadedPdf.view.mapValues { pdf =>
          pdf.copy(
            originalFileIdOpt = pdf.originalFileIdOpt.map(id => newFileIdMap.getOrElse(id, id))
          )
        }.toMap
      )
      val newAnnotationMapping = newFileIdMap
        .flatMap { (oldFormFileId, newFormFileId) =>
          for {
            oldSysFileId <- FormDataConverters.fileIdTypeToFileId(oldFormFileId)
            newSysFileId <- FormDataConverters.fileIdTypeToFileId(newFormFileId)
          } yield oldSysFileId -> newSysFileId
        }
        .foldLeft(annotationMapping) { case (curMapping, oldSysFileId -> newSysFileId) =>
          val annotationVersionIdOpt = curMapping.get(oldSysFileId)
          curMapping - oldSysFileId ++ annotationVersionIdOpt.map(newSysFileId -> _)
        }

      newFormData -> newAnnotationMapping
    }
  }

  def saveFormData(
    formId: FormId,
    formData: FormData,
    versionMetadataOpt: Option[(FormVersionMetadataModel, FormVersionSystemMetadataModel)],
    parentVersionId: Option[FormVersionId],
    name: String,
    actor: UserId,
    sessionId: AssetSessionId,
    formTypeOpt: Option[FormType] = None
  )(
    using FDBCluster
  ): Task[SaveFormResponse] = {
    val fileStr = formData.uploadedPdf.keys.toSeq.map(_.id).sorted.mkString(Separator)
    for {
      _ <- ZIO.logInfo(s"${actor.id} is saving form data for form ${formId.idString} ($fileStr)")
      _ <- validateFormPermission(formId, actor)
      result <- validateEditFormLockTask(
        formId = formId,
        actor = actor,
        sessionId = sessionId,
        task = saveFormDataInternal(
          formId,
          formData,
          versionMetadataOpt,
          parentVersionId,
          name,
          actor,
          shouldCheckForTransferOntologyAsaMapping = true,
          formTypeOpt = formTypeOpt
        ),
        errorMsg = s"Invalid lock, cannot save form data for form ${formId.idString}"
      )
    } yield {
      SaveFormResponse(result)
    }
  }

  def saveFormMetadata(
    formVersionId: FormVersionId,
    metadata: (FormVersionMetadataModel, FormVersionSystemMetadataModel),
    actor: UserId,
    sessionId: AssetSessionId
  )(
    using FDBCluster
  ): Task[SaveFormMetadataResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is saving form metadata for form ${formVersionId.parent.idString}")
      _ <- validateFormPermission(formVersionId.parent, actor)
      result <- validateEditFormLockTask(
        formId = formVersionId.parent,
        actor = actor,
        sessionId = sessionId,
        task = FDBRecordDatabase.transact(FormVersionStoreProvider.Production) { formVersionStore =>
          FormVersionStoreOperations(formVersionStore)
            .updateMetadata(formVersionId)(_ => metadata)
            .unit
        },
        errorMsg = s"Invalid lock, cannot save form metadata for form ${formVersionId.parent.idString}"
      )
    } yield {
      SaveFormMetadataResponse(result)
    }
  }

  private def saveFormDataInternal(
    formId: FormId,
    formData: FormData,
    metadataOpt: Option[(FormVersionMetadataModel, FormVersionSystemMetadataModel)],
    parentVersionIdOpt: Option[FormVersionId],
    name: String,
    actor: UserId,
    shouldCheckForTransferOntologyAsaMapping: Boolean,
    formTypeOpt: Option[FormType]
  )(
    using FDBCluster
  ) = {
    for {
      formModel <- FDBCommonDatabase().read(FormModelStoreOperations.Production)(_.get(formId))
      _ <- formStorageService.uploadDraft(
        formModel,
        actor,
        // Always turn off this flag for new forms
        FormDataUtils.sanitizeFormFiles(formData.turnOffTriggerRuleByProperty)
      )
      author <- userProfileService.getEmailAddress(actor)
      draftVersion <- ZIO.attempt {
        FormVersionModel(
          parentVersionId = parentVersionIdOpt,
          name = name,
          author = Some(author),
          createdAt = Some(DateCalculator.instantNow),
          formType = formTypeOpt.orElse(formModel.draftVersion.map(_.formType)).getOrElse(FormType.Default)
        )
      }
      newDraftMetadataOpt = metadataOpt.map(_._1.copy(id = draftMetadataId))
      newDraftSystemMetadataOpt = metadataOpt.map(_._2.copy(id = draftSystemMetadataId))
      oldDraft = formModel.draftVersion
      updatedModel <- FDBRecordDatabase.transact(FormModelStoreOperations.Production) { ops =>
        for {
          _ <- ops.update(formId) {
            _.copy(
              draftVersion = Some(draftVersion),
              lastEditedAt = Some(DateCalculator.instantNow),
              lastEditor = Some(actor),
              draftMetadata = newDraftMetadataOpt.orElse(formModel.draftMetadata),
              draftSystemMetadata = newDraftSystemMetadataOpt.orElse(formModel.draftSystemMetadata)
            )
          }
          updatedModel <- ops.get(formId)
        } yield updatedModel
      }
      _ <- ZIO.when(shouldCheckForTransferOntologyAsaMapping)(
        transferOntologyAsaMappingOnSaveDraftIfNecessary(
          formId = formId,
          newParentVersionIdOpt = parentVersionIdOpt,
          currentDraftModelOpt = formModel.draftVersion,
          latestFormVersionId = formModel.latestVersionId,
          actor = actor
        )
      )
      checkData <- getFormCheckUpToDateData(formId, Some(updatedModel))
      _ <- ZIO.logInfo(s"Form data saved for form ${formId.idString}")
    } yield SaveFormResult(
      draftVersion,
      oldDraft.flatMap(_.author),
      oldDraft.flatMap(_.createdAt),
      checkData
    )
  }

  def editVersion(
    formId: FormId,
    formVersionIdOpt: Option[FormVersionId], // draft version if this is None
    name: String,
    note: String,
    actor: UserId
  )(
    using FDBCluster
  ): Task[Unit] = {
    val versionStr = formVersionIdOpt.fold("draft version")(id => s"version ${id.value.value}")
    for {
      _ <- ZIO.logInfo(s"${actor.id} is editing version $versionStr of form ${formId.idString}")
      _ <- validateFormPermission(formId, actor)
      _ <- formVersionIdOpt.fold {
        FDBRecordDatabase.transact(FormModelStoreOperations.Production) { ops =>
          ops
            .update(formId) { formModel =>
              formModel.copy(
                draftVersion = formModel.draftVersion.map(_.copy(name = name, note = note))
              )
            }
            .unit
        }
      } { formVersionId =>
        FDBRecordDatabase.transact(FormVersionStoreOperations.Production) { ops =>
          ops.updateVersion(formVersionId)(_.copy(name = name, note = note)).unit
        }
      }
      _ <- ZIO.logInfo(s"${versionStr.capitalize} of form ${formId.idString} edited")
    } yield ()
  }

  def getDraftVersionId(
    formId: FormId
  )(
    using FDBCluster
  ): Task[FormVersionId] = {
    FDBCommonDatabase().read(FormModelStoreOperations.Production)(_.getDraftVersionId(formId))
  }

  def createFormVersion(
    formId: FormId,
    name: String,
    note: String,
    actor: UserId,
    sessionId: AssetSessionId,
    formVersionIdOpt: Option[FormVersionId] = None
  )(
    using FDBCluster
  ): Task[CreateFormVersionResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is creating a version for form ${formId.idString}")
      _ <- validateFormPermission(formId, actor)
      result <- validateEditFormLockTask(
        formId = formId,
        actor = actor,
        sessionId = sessionId,
        task = for {
          draftVersionId <- getDraftVersionId(formId)
          result <- createFormVersionInternal(
            formId,
            name,
            note,
            actor,
            formVersionIdOpt,
            sourceFormVersionId = draftVersionId,
            shouldTransferOntologyAsaMapping = true
          )
        } yield result,
        errorMsg = s"Invalid lock, cannot create form version for form ${formId.idString}"
      )
    } yield CreateFormVersionResponse(result)
  }

  private def getValidAsaMapping(curMapping: Map[String, String], form: Form): Map[String, String] = {
    val formFields = FormDataUtils
      .traverseAndExtract(
        form,
        (_, _) => true,
        unwrapArray = true
      )
      .map(_._1)
      .toSet
    val formFieldMap = formFields.map(field => field.name -> field).toMap

    curMapping.view.filterKeys { k =>
      MatchedValueCompanion
        .fromText(k)
        .fold(
          // Field alias
          formFieldMap.keySet.contains(k)
        ) { optionValue =>
          // Option value
          formFieldMap.get(optionValue.alias).exists { field =>
            val validOptions = field.tpe match {
              case Schema.array(_ @Schema.`enum`(_, values), _, _, _) => values.flatMap(_.asString)
              case Schema.`enum`(_, values)                           => values.flatMap(_.asString)
              case _                                                  => Seq.empty
            }
            validOptions.toSet.contains(optionValue.value)
          }
        }
    }.toMap
  }

  private def createFormVersionInternal(
    formId: FormId,
    name: String,
    note: String,
    actor: UserId,
    formVersionIdOpt: Option[FormVersionId],
    // TODO: @hiepbui to revise this
    sourceFormVersionId: FormVersionId,
    shouldTransferOntologyAsaMapping: Boolean
  )(
    using FDBCluster
  ) = {
    val formVersionId = formVersionIdOpt.getOrElse(FormVersionIdFactory.unsafeRandomId(formId))
    for {
      author <- userProfileService.getEmailAddress(actor)
      formModel <- FDBCommonDatabase().read(FormModelStoreOperations.Production)(_.get(formId))
      draftVersion <- ZIOUtils.optionToTask(
        formModel.draftVersion,
        GeneralServiceException(s"No draft version for form ${formId.idString}")
      )
      storageUniqueKey <- formStorageService.commit(formModel, formVersionId)
      instantNow <- ZIO.attempt(DateCalculator.instantNow)
      // 1. Save form & form version
      _ <- FDBRecordDatabase.transact(
        FDBOperations[(FormModelStoreOperations, FormVersionStoreOperations)].Production
      ) { case (formStore, formVersionStore) =>
        for {
          _ <- formStore.update(formId) {
            _.copy(
              draftVersion = None,
              lastEditedAt = Some(instantNow),
              lastEditor = Some(actor),
              latestVersionId = formVersionId,
              versionCount = formModel.versionCount + 1
            )
          }
          _ <- formVersionStore.createVersion(
            FormVersionModel(
              formVersionId = formVersionId,
              formId = formId,
              name = name,
              createdAt = Some(instantNow),
              author = Some(author),
              parentVersionId = draftVersion.parentVersionId,
              note = note,
              versionNumber = formModel.versionCount + 1,
              storageUniqueKey = storageUniqueKey,
              formType = formModel.draftVersion.fold(FormType.Default)(_.formType)
            )
          )
          _ <- formVersionStore.updateMetadata(formVersionId) { (oldMetadata, oldSystemMetadata) =>
            (
              formModel.draftMetadata
                .map(_.copy(id = FormVersionMetadataId(formVersionId)))
                .getOrElse(oldMetadata),
              formModel.draftSystemMetadata
                .map(_.copy(id = FormVersionSystemMetadataId(formVersionId)))
                .getOrElse(oldSystemMetadata)
            )
          }
        } yield ()
      }
      parentDraftVersionOpt = draftVersion.parentVersionId.orElse(
        FormVersionId.getNonDefaultId(formModel.latestVersionId)
      )
      // Also save the standard alias mapping from draft
      // 2. Save ASA mapping
      draftStandardAliasMapping <- parentDraftVersionOpt
        .map(getFormStandardAliasMapping(_, checkValidFormFieldsAndOptions = false))
        .getOrElse(ZIO.attempt(Map.empty[String, String]))
      formData <- formStorageService.getFormData(formModel, Option(draftVersion))
      validMapping = getValidAsaMapping(draftStandardAliasMapping, formData.form)
      _ <- ZIOUtils.when(validMapping.nonEmpty)(
        addFormStandardAliasMapping(
          AddFormStandardAliasMappingParams(formVersionId, validMapping),
          actor
        )
      )
      _ <- ZIO.when(shouldTransferOntologyAsaMapping)(
        transferOntologyAsaMappingsOnCommit(formData, sourceFormVersionId, formVersionId, actor)
      )
      _ <- formIntegrationService.transferConfigFromDraftVersionUnsafe(
        formVersionId,
        formData.form,
        actor
      )
      checkData <- getFormCheckUpToDateData(formId, Some(formModel))
      _ <- ZIO.logInfo(s"Version ${formVersionId.idString} created for form ${formId.idString}")
    } yield {
      CreateFormVersionResult(
        formVersionId,
        formModel.versionCount + 1,
        checkData
      )
    }
  }

  // Transfer mappings from the based version to draft when the draft points to other based version
  private def transferOntologyAsaMappingOnSaveDraftIfNecessary(
    formId: FormId,
    newParentVersionIdOpt: Option[FormVersionId],
    currentDraftModelOpt: Option[FormVersionModel],
    latestFormVersionId: FormVersionId,
    actor: UserId
  )(
    using FDBCluster
  ) = {
    newParentVersionIdOpt
      .filter { newParentVersionId =>
        // Either
        // - parent version changes
        // - current draft is empty (indicating it based on the latest form version) and new parent version id isn't
        //   the latest one
        currentDraftModelOpt.fold(
          latestFormVersionId != newParentVersionId
        ) { currentDraft =>
          !currentDraft.parentVersionId.contains(newParentVersionId)
        }
      }
      .fold[Task[Unit]](
        ZIO.unit
      ) { newParentVersionId =>
        for {
          draftVersionId <- getDraftVersionId(formId)
          _ <- formAsaMappingService.transferAsaMappings(
            sourceVersionId = newParentVersionId,
            targetVersionId = draftVersionId,
            filterAvailableIdsOpt = None,
            actor = actor,
            clearTargetExistingMappings = true
          )
        } yield ()
      }
  }

  private def transferOntologyAsaMappingsOnCommit(
    formData: FormData,
    sourceVersionId: FormVersionId,
    targetVersionId: FormVersionId,
    actor: UserId
  ) = {
    for {
      allTargets <- ZIO.attempt {
        FormFieldMappingTarget.getAllFormFieldTargets(formData).map(_.idString)
      }
      _ <- formAsaMappingService.transferAsaMappings(
        sourceVersionId = sourceVersionId,
        targetVersionId = targetVersionId,
        filterAvailableIdsOpt = Some(allTargets.toSet),
        actor = actor,
        clearTargetExistingMappings = false
      )
    } yield ()
  }

  private def sanitizeFormData(formData: FormData): FormData = {
    val sanitizedForm = formData.form.copy(
      namespaceFormSchemaMap = formData.form.namespaceFormSchemaMap.map { case (formNamespace, formSchema) =>
        val allSchemaKeySet = formSchema.formSchemaMap.keySet
        val sanitizedFormSchema = formSchema.copy(
          uiSchema = formSchema.uiSchema.filter { case (key, _) => allSchemaKeySet.contains(key) }
        )
        formNamespace -> sanitizedFormSchema
      }
    )
    formData.copy(form = sanitizedForm)
  }

  def getFormModelsUnsafe(
    formId: FormId,
    versionIdOpt: Option[FormVersionId]
  )(
    using FDBCluster
  ): Task[
    (
      FormModel,
      Option[FormVersionModel],
      FormVersionMetadataModel,
      FormVersionSystemMetadataModel,
      Boolean,
      FormVersionId
    )
  ] = {
    for {
      (formModel, formVersionOpt, draftVersionId, refinedVersionIdOpt) <- FDBCommonDatabase()
        .read(
          FDBOperations[(FormModelStoreOperations, FormVersionStoreOperations)].Production
        ) { case (formOps, versionOps) =>
          for {
            formModel <- formOps.get(formId)
            draftVersionId <- formOps.getDraftVersionId(formId)
            // refinedVersionIdOpt =
            //   if versionIdOpt.nonEmpty                => versionIdOpt
            //   else if formModel.draftVersion.nonEmpty => None
            //   else                                    => Some(formModel.latestVersionId)
            refinedVersionIdOpt = versionIdOpt.orElse(
              Option.when(formModel.draftVersion.isEmpty)(formModel.latestVersionId)
            )
            formVersionOpt <- RecordReadIO.traverseOption(refinedVersionIdOpt)(versionOps.getVersionOpt)
          } yield (formModel, formVersionOpt, draftVersionId, refinedVersionIdOpt)
        }
      isDraft = refinedVersionIdOpt.isEmpty
      (versionMetadata, versionSystemMetadata) <- refinedVersionIdOpt.fold[
        Task[(FormVersionMetadataModel, FormVersionSystemMetadataModel)]
      ](
        ZIO.succeed(
          (
            formModel.draftMetadata.getOrElse(FormVersionMetadataModel(id = draftMetadataId)),
            formModel.draftSystemMetadata.getOrElse(FormVersionSystemMetadataModel(id = draftSystemMetadataId))
          )
        )
      ) { formVersionId =>
        FDBRecordDatabase.transact(FormVersionStoreOperations.Production)(_.getOrCreateMetadataIfNeeded(formVersionId))
      }
    } yield (formModel, formVersionOpt, versionMetadata, versionSystemMetadata, isDraft, draftVersionId)
  }

  /** @param versionIdOpt
    *   return branch data if no version is specified
    */
  def getForm(
    formId: FormId,
    versionIdOpt: Option[FormVersionId],
    actor: UserId,
    // TODO: @flashmt to remove this & support proper check for fundsub lp
    shouldCheckPermission: Boolean = true
  )(
    using FDBCluster
  ): Task[GetFormResponse] = {
    val versionStr = versionIdOpt.map(id => s"version $id").getOrElse("latest version")
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting $versionStr of form ${formId.idString}")
      _ <- ZIOUtils.when(shouldCheckPermission) {
        validateFormPermission(formId, actor)
      }
      (formModel, formVersionOpt, versionMetadata, versionSystemMetadata, isDraft, draftVersionId) <-
        getFormModelsUnsafe(
          formId,
          versionIdOpt
        )
      formData <- formStorageService
        .getFormData(
          formModel,
          formVersionOpt
        )
        .map(sanitizeFormData)
      editingUserOpt <- formLockService.getLockedUser(formId)
      checkData <- getFormCheckUpToDateData(formId, Some(formModel))
      migratedVersionMetadata = migrateVersionMetadata(versionMetadata, formData)
      migratedFormModel =
        if (isDraft) {
          formModel.copy(draftMetadata = Some(migratedVersionMetadata))
        } else {
          formModel
        }
      userIds = migratedFormModel.associatedLinks.values.flatMap(_.createdBy).toSet ++ Set(actor)
      userInfoMap <- userProfileService.batchGetUserInfos(userIds, Some(actor))
    } yield {
      GetFormResponse(
        formModel = migratedFormModel,
        formData = formData,
        versionOpt = formVersionOpt,
        editingUserOpt = editingUserOpt,
        checkData = checkData,
        metadata = migratedVersionMetadata,
        systemMetadata = versionSystemMetadata,
        draftVersionId = draftVersionId,
        userInfoMap = userInfoMap
      )
    }
  }

  // Migrate PdfCutInfo from FormData to FormVersionMetadataModel when getting a form
  private def migrateVersionMetadata(versionMetadata: FormVersionMetadataModel, formData: FormData) = {
    if (!versionMetadata.isPdfCutsMigrated && versionMetadata.pdfCuts.isEmpty) {
      val uploadedPdf = formData.uploadedPdf
      val rawPdfCuts = formData.form.defaultUiSchema.values.flatMap { widget =>
        for {
          _ <- Option.when(widget.widgetType == WidgetType.Pdf)(())
          embeddedPdfFileName <- widget.uiOptions.get(UIKey.embeddedPdf)
          pdfCutInfo <- widget.uiOptions.get(UIKey.pdfCutInfo)
          uploadedPdfFileId = pdfCutInfo.reviewingFileId.getOrElse(pdfCutInfo.fileId)
          uploadedPdfFileName <- uploadedPdf.get(uploadedPdfFileId).map(_.name)
          needReview = pdfCutInfo.reviewingFileId.nonEmpty
        } yield embeddedPdfFileName -> PdfCutInfo(
          uploadedPdfFileName,
          pdfCutInfo.start,
          pdfCutInfo.end,
          needReview
        )
      }
      val pdfCuts = rawPdfCuts.groupMap(_._1)(_._2).flatMap { case embeddedPdfFileName -> pdfCutInfoList =>
        // In case there are multiple pdfCutInfo for the same embedded file, prioritize one with needReview = true
        pdfCutInfoList.find(_.needReview).orElse(pdfCutInfoList.headOption).map(embeddedPdfFileName -> _)
      }
      versionMetadata.copy(pdfCuts = pdfCuts, isPdfCutsMigrated = true)
    } else {
      versionMetadata
    }
  }

  def getFormDataUnsafe(
    formVersionId: FormVersionId
  )(
    using FDBCluster
  ): Task[FormData] = {
    for {
      (formModel, formVersionModel) <- FDBCommonDatabase().read(
        FDBOperations[(FormModelStoreOperations, FormVersionStoreOperations)].Production
      ) { case (formOps, formVersionOps) =>
        for {
          formModel <- formOps.get(formVersionId.parent)
          formVersionModel <- formVersionOps.getVersion(formVersionId)
        } yield formModel -> formVersionModel
      }
      formData <- formStorageService.getFormData(formModel, Some(formVersionModel))
    } yield formData
  }

  // For internal usage, please verify permission before calling this
  def getFormNames(
    formIds: Seq[FormId]
  )(
    using FDBCluster
  ): Task[Seq[(FormId, String)]] = {
    for {
      models <- FDBCommonDatabase().read(FormModelStoreOperations.Production) { ops =>
        RecordReadIO.parTraverseN(64)(formIds)(ops.get)
      }
    } yield models.map(formModel => formModel.formId -> formModel.name)
  }

  // For internal usage, please verify permission before calling this
  def updatePermission(
    versionId: FormVersionId,
    grantedTeams: Set[TeamId] = Set.empty,
    grantedUsers: Set[UserId] = Set.empty,
    revokedTeams: Set[TeamId] = Set.empty,
    revokedUsers: Set[UserId] = Set.empty
  )(
    using FDBCluster
  ): Task[FormData] = {
    val grantedMsg = s"teams: ${grantedTeams.mkString(Separator)}, users: ${grantedUsers.mkString(Separator)}"
    for {
      _ <- ZIO.logInfo(s"Granting permission of form $versionId to $grantedMsg")
      admin <- executiveAdmin.userId
      getFormResp <- getForm(
        formId = versionId.parent,
        versionIdOpt = Some(versionId),
        actor = admin,
        shouldCheckPermission = false
      )
      formData = getFormResp.formData

      // Grant all files' permission within the form to the granted teams & users,
      // and revoke permission from removed teams & users
      allFileIds <- ZIO.foreach(formData.allFileIds)(FormDataConverters.fileIdTypeToFileIdTask)
      _ <- ZIO.foreach(allFileIds) { fileId =>
        fileService.modifyFilePermissions(
          actor = admin,
          fileId = fileId,
          changes = PermissionChanges(
            permissionMap = FileFolderPermissionMap(
              userPermissions = grantedUsers.map(_ -> FileFolderPermission.Read).toMap,
              teamPermissions = grantedTeams.map(_ -> FileFolderPermission.Read).toMap
            ),
            revokedUsers = revokedUsers,
            revokedTeams = revokedTeams
          )
        )
      }
    } yield formData
  }

  def getAllForms(
    actor: UserId
  )(
    using FDBCluster
  ): Task[GetAllFormModelsResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting all forms")
      hasManagePermission <- portalUserService.hasManagePermission(actor, PortalSectionId.Form)
      hasWritePermission <- portalUserService.hasWritePermission(actor, PortalSectionId.Form)
      _ <- ZIOUtils.failUnless(hasManagePermission || hasWritePermission) {
        GeneralServiceException(s"${actor.id} has no permission to get all forms")
      }
      forms <- getAllFormsInternal
      filteredForms <-
        if (hasManagePermission) {
          ZIO.attempt(forms)
        } else {
          for {
            userInfo <- userProfileService.getUserInfo(actor)
          } yield {
            forms.filter(
              _.owners.map(_.address).contains(userInfo.emailAddressStr)
            )
          }
        }
      // Clean unused draft metadata to make the response smaller
    } yield GetAllFormModelsResponse(filteredForms.map(_.copy(draftMetadata = None)))
  }

  def getAllFormsInternal(
    using FDBCluster
  ): Task[List[FormModel]] =
    FDBCommonDatabase().read(FormModelStoreOperations.Production) { ops =>
      ops.getAll.map(_.map(_._2))
    }

  def getAllFormsAnalytics(
    actor: UserId
  )(
    using FDBCluster
  ): Task[GetAllFormsAnalyticsResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting all forms analytics")
      allForms <- getAllForms(actor).map(_.forms)
      result <- ZIO.foreach(allForms.grouped(16).toSeq) { formModels =>
        FDBCommonDatabase().read(FormVersionStoreOperations.Production) { ops =>
          RecordReadIO.parTraverseN(formModels.size)(formModels) { formModel =>
            for {
              lastVersionModel <- ops.getVersion(formModel.latestVersionId)
              // If this is the first version the we will use draft version instead
              isLatestVersionEmpty = lastVersionModel.versionNumber == 0
              summaryOpt <-
                if (isLatestVersionEmpty) {
                  RecordReadIO.succeed(formModel.draftMetadata.flatMap(_.widgetSourceSummary))
                } else {
                  ops.getMetadataOpt(formModel.latestVersionId).map(_.flatMap(_.widgetSourceSummary))
                }
            } yield (formModel, summaryOpt)
          }
        }
      }
    } yield GetAllFormsAnalyticsResponse(result.flatten)
  }

  def getAllFormIdWithSharedSecret(
    sharedSecret: String
  )(
    using FDBCluster
  ): Task[Seq[FormId]] = {
    for {
      _ <- ZIOUtils.validate(sharedSecret == gondorConfig.backendConfig.formConfig.sharedSecret)(
        new RuntimeException("Shared secret mismatch")
      )
      forms <- FDBCommonDatabase().read(FormModelStoreOperations.Production)(_.getAll)
    } yield forms.map(_._1)
  }

  def getAllVersions(
    formId: FormId,
    actor: UserId
  )(
    using FDBCluster
  ): Task[GetAllVersionResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting all versions of form ${formId.idString}")
      _ <- validateFormPermission(formId, actor)
      (model, versions) <- getAllVersionsInternal(formId)
    } yield GetAllVersionResponse(model, versions)
  }

  def getAllVersionsInternal(
    formId: FormId
  )(
    using FDBCluster
  ): Task[(FormModel, List[FormVersionModel])] =
    FDBCommonDatabase().read(
      FDBOperations[(FormModelStoreOperations, FormVersionStoreOperations)].Production
    ) { case (modelOps, versionOps) =>
      for {
        model <- modelOps.get(formId)
        versions <- versionOps.getVersionsByFormId(formId)
      } yield model -> versions
    }

  def getFormActivities(
    formId: FormId,
    actor: UserId
  )(
    using FDBCluster
  ): Task[GetFormActivitiesResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is getting activities of form ${formId.idString}")
      _ <- validateFormPermission(formId, actor)
      formActivities <- FDBCommonDatabase().read(FormActivityStoreOperations.Production)(_.getByFormId(formId))
      userMap <- {
        val actors = formActivities.map(_.actor).toSet
        userProfileService
          .batchGetUserInfos(actors)
          .map { userInfoMap =>
            userInfoMap.view.mapValues { userInfo =>
              EmailAddress(Some(userInfo.fullNameString), userInfo.emailAddressStr)
            }.toMap
          }
      }
    } yield GetFormActivitiesResponse(formActivities, userMap)
  }

  def diffForm(
    formId: FormId,
    firstVersionOpt: Option[FormVersionModel],
    secondVersionOpt: Option[FormVersionModel],
    actor: UserId
  )(
    using FDBCluster
  ): Task[DiffFormResponse] = {
    val firstVersionId = firstVersionOpt.fold("draft version")(_.formVersionId.idString)
    val secondVersionId = secondVersionOpt.fold("draft version")(_.formVersionId.idString)
    for {
      _ <- ZIO.logInfo(s"${actor.id} is comparing $firstVersionId and $secondVersionId")
      _ <- validateFormPermission(formId, actor)
      formModel <- FDBCommonDatabase().read(FormModelStoreOperations.Production)(_.get(formId))
      firstFormData <- formStorageService.getFormData(
        formModel,
        firstVersionOpt
      )
      secondFormData <- formStorageService.getFormData(
        formModel,
        secondVersionOpt
      )
    } yield DiffFormResponse(firstFormData, secondFormData)
  }

  def diffFormFile(
    formId: FormId,
    firstVersionIdOpt: Option[FormVersionId],
    secondVersionIdOpt: Option[FormVersionId],
    actor: UserId
  )(
    using FDBCluster
  ): Task[DiffFormFileResponse] = {
    val firstVersionIdStr = firstVersionIdOpt.fold("draft version")(_.idString)
    val secondVersionIdStr = secondVersionIdOpt.fold("draft version")(_.idString)
    for {
      _ <- ZIO.logInfo(s"${actor.id} is comparing $firstVersionIdStr's files and $secondVersionIdStr's files'")
      _ <- validateFormPermission(formId, actor)
      (formModel, firstVersionData, secondVersionData) <- FDBCommonDatabase().read(
        FDBOperations[(FormModelStoreOperations, FormVersionStoreOperations)].Production
      ) { (modelOp, versionOp) =>
        for {
          model <- modelOp.get(formId)
          draftVersionData = model.draftVersion -> model.draftSystemMetadata
          firstVersionData <- firstVersionIdOpt
            .map { firstVersion =>
              for {
                firstVersionOpt <- versionOp.getVersionOpt(firstVersion)
                firstVersionSystemMetaOpt <- versionOp.getSystemMetadataOpt(firstVersion)
              } yield firstVersionOpt -> firstVersionSystemMetaOpt
            }
            .getOrElse(RecordReadIO.succeed(draftVersionData))
          secondVersionData <- secondVersionIdOpt
            .map { secondVersion =>
              for {
                secondVersionOpt <- versionOp.getVersionOpt(secondVersion)
                secondVersionSystemMetaOpt <- versionOp.getSystemMetadataOpt(secondVersion)
              } yield secondVersionOpt -> secondVersionSystemMetaOpt
            }
            .getOrElse(RecordReadIO.succeed(draftVersionData))
        } yield (model, firstVersionData, secondVersionData)
      }
      (firstFormData, secondFormData) <- formStorageService
        .getFormData(
          formModel,
          firstVersionData._1
        )
        .zipPar {
          formStorageService.getFormData(
            formModel,
            secondVersionData._1
          )
        }
      (firstVersionFileDataMap, secondVersionFileDataMap) <- ZIO.attempt {
        val firstVersionFileDataMap = firstFormData.uploadedPdf.flatMap { (fileId, uploadedPdf) =>
          FormDataConverters.fileIdTypeToFileId(fileId).map { sysFileId =>
            val idData = firstVersionData._2
              .flatMap(_.annotationMapping.get(sysFileId))
              .getOrElse[FileId | AnnotationDocumentVersionId] {
                uploadedPdf.originalFileIdOpt
                  .flatMap(FormDataConverters.fileIdTypeToFileId)
                  .getOrElse(sysFileId)
              }
            sysFileId -> (uploadedPdf.name, idData)
          }
        }
        val secondVersionFileDataMap = secondFormData.uploadedPdf.flatMap { (fileId, uploadedPdf) =>
          FormDataConverters.fileIdTypeToFileId(fileId).map { sysFileId =>
            val idData = secondVersionData._2
              .flatMap(_.annotationMapping.get(sysFileId))
              .getOrElse[FileId | AnnotationDocumentVersionId] {
                uploadedPdf.originalFileIdOpt
                  .flatMap(FormDataConverters.fileIdTypeToFileId)
                  .getOrElse(sysFileId)
              }
            sysFileId -> (uploadedPdf.name, idData)
          }
        }
        firstVersionFileDataMap -> secondVersionFileDataMap
      }
      annotationDataMap <- NonEmptyChunk
        .fromIterableOption {
          (firstVersionFileDataMap.values.map(_._2) ++ secondVersionFileDataMap.values.map(_._2)).toSet
        }
        .fold(ZIO.succeed(Map.empty[FileId | AnnotationDocumentVersionId, AnnotationDocumentData])) { dataChunk =>
          given AnnotationDocumentService.KeySpace = AnnotationDocumentService.KeySpace.Production
          ZIO
            .foreachPar(dataChunk) {
              case fileId: FileId =>
                annotationDocumentService
                  .extractDocumentData(fileId, actor)
                  .map[(FileId | AnnotationDocumentVersionId, AnnotationDocumentData)] { resp =>
                    fileId -> resp.annotationData
                  }
              case annotationVersionId: AnnotationDocumentVersionId =>
                annotationDocumentService
                  .getAnnotationData(annotationVersionId.parent, Some(annotationVersionId), actor)
                  .map[(FileId | AnnotationDocumentVersionId, AnnotationDocumentData)](annotationVersionId -> _)
            }
            .withParallelism(4)
            .map(_.toMap)
        }
    } yield {
      val firstVersionFileAnnotationMap = firstVersionFileDataMap.flatMap { (fileId, data) =>
        annotationDataMap.get(data._2).map(annotation => fileId -> (data._1, annotation))
      }
      val secondVersionFileAnnotationMap = secondVersionFileDataMap.flatMap { (fileId, data) =>
        annotationDataMap.get(data._2).map(annotation => fileId -> (data._1, annotation))
      }
      DiffFormFileResponse(firstVersionFileAnnotationMap, secondVersionFileAnnotationMap)
    }
  }

  def generatePdf(
    formId: FormId,
    formData: FormData,
    formStates: Map[String, FormFieldState],
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[GeneratePdfResponse] = {
    val folderId = FolderId.channelSystemFolderId(formId)
    val uploadedPdf = formData.uploadedPdf.flatMap { case (id, pdf) =>
      FormDataConverters.fileIdTypeToFileId(id).map(_ -> pdf)
    }
    for {
      fillFormValuesResult <- FormUtils
        .fillFormValuesAndGenerateFile(
          formData.form,
          uploadedPdf,
          formStates,
          actor,
          folderId,
          httpContext
        )
      fileMap <- fillFormValuesResult.fold(
        exception => ZIO.fail(GeneralServiceException(exception.getMessage)),
        fileMap => ZIO.attempt(fileMap)
      )
      files <- ZIO.foreach(fileMap.values.toSeq) { fileId => fileService.getFileName(actor)(fileId).map(_ -> fileId) }
    } yield GeneratePdfResponse(files)
  }

  def getFileInfoToCut(
    fileId: FileId,
    formId: FormId,
    actor: UserId
  )(
    using FDBCluster
  ): Task[GetFileInfoToCutResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is getting info for file $fileId")
      _ <- validateFormPermission(formId, actor)
      _ <- validateFormFile(
        formId,
        fileId,
        actor
      )
      url <- pdfFileService.getPdfUrlOfFile(
        actor,
        fileId,
        None,
        DmsTrackingActivityType.Internal,
        None
      )
      pdfStorageId <- fileService.getFileStorageId(
        actor,
        fileId,
        DmsTrackingActivityType.Internal,
        httpContextOpt = None
      )
      pageCount <- muPdfServerless
        .pdfPageCount(
          PdfPageCountRequest(
            s3Access = ServerlessUtils.getS3Access(),
            pdfStorageId = pdfStorageId.id,
            passwordOpt = None
          )
        )
        .map(_.pageCount)
    } yield GetFileInfoToCutResponse(url, pageCount)
  }

  def cutFile(
    fileId: FileId,
    formId: FormId,
    start: Int,
    end: Int,
    fileName: String,
    actor: UserId
  )(
    using FDBCluster
  ): Task[CutFileResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor is cutting file $fileId in form $formId")
      _ <- validateFormPermission(formId, actor)
      _ <- validateFormFile(
        formId,
        fileId,
        actor
      )
      folderId <- ZIO.attempt(FolderId.channelSystemFolderId(formId))
      outputStorageId = fileService.s3Service.generateRandomStorageId(Some(folderId), fileName)
      inputFileStorageId <- fileService.getFileStorageId(actor, fileId, DmsTrackingActivityType.Internal, None)
      mergePdfResp <- muPdfServerless.mergePdf(
        MergePdfRequest(
          s3Access = ServerlessUtils.getS3Access(fileService.s3Service.s3Config.bucket),
          inputPdfs = Seq(
            MergePdfFileParam(
              inputFileStorageId.id,
              Seq(PageRange.MultiplePages(start, Some(end)))
            )
          ),
          outputStorageId = outputStorageId.id,
          flattenAcroForm = Some(false)
        )
      )
      _ <- ZIO.foreach(mergePdfResp.exception) { exception =>
        ZIO.fail(GeneralServiceException(s"Exception when cutting PDF: ${exception.message}"))
      }
      outputFileId <- fileService.uploadFile(
        folderId,
        fileName,
        FileContentOrigin.FromStorageId(outputStorageId, fileService.s3Service.s3Config.bucket),
        actor
      )
    } yield CutFileResponse(outputFileId)
  }

  def getFormFileMetadata(fileIds: Seq[FileId], actor: UserId): Task[GetFormFileMetadataResponse] = {
    for {
      _ <- portalUserService.validateReadPermission(actor, PortalSectionId.Form)
      metadata <- ZIO.foreachPar(fileIds) { fileId =>
        for {
          createdAt <- fileService.getFileCreatedAt(actor)(fileId)
          createdBy <- fileService.getFileCreatedBy(actor)(fileId)
          fileSize <- fileService.getFileSize(fileId)
          userInfo <- userProfileService.getUserInfo(createdBy)
        } yield fileId -> FileMetadata(userInfo.fullNameString, createdAt, fileSize)
      }
    } yield GetFormFileMetadataResponse(metadata.toMap)
  }

  private def validateEditFormLockTask[A](
    formId: FormId,
    actor: UserId,
    sessionId: AssetSessionId,
    task: => Task[A],
    errorMsg: String
  ): Task[Either[EditFormDataError, A]] = {
    for {
      isValidLock <- formLockService.validateLock(
        formId,
        actor,
        sessionId
      )
      result <-
        if (isValidLock) {
          task.map(Right.apply)
        } else {
          ZIO.logInfo(errorMsg).as(Left(EditFormDataError.NoLockFound))
        }
    } yield result
  }

  private[service] def getFormCheckUpToDateData(
    formId: FormId,
    formModelOpt: Option[FormModel] = None
  )(
    using FDBCluster
  ) = {
    for {
      formModel <- formModelOpt.fold(
        FDBCommonDatabase().read(FormModelStoreOperations.Production)(_.get(formId))
      )(model => ZIO.attempt(model))
    } yield FormCheckUpToDateData(
      latestVersionIdOpt = Some(formModel.latestVersionId),
      latestDraftCreatedOpt = formModel.draftVersion.flatMap(_.createdAt)
    )
  }

  def validateFormPermission(
    formId: FormId,
    userId: UserId
  )(
    using FDBCluster
  ): Task[Unit] = {
    for {
      hasFullFormPermission <- portalUserService.hasManagePermission(userId, PortalSectionId.Form)
      _ <- ZIOUtils.unless(hasFullFormPermission) {
        for {
          _ <- portalUserService.validateWritePermission(userId, PortalSectionId.Form)
          formModel <- FDBCommonDatabase().read(FormModelStoreOperations.Production)(_.get(formId))
          userInfo <- userProfileService.getUserInfo(userId)
          _ <- ZIOUtils.failUnless(formModel.owners.map(_.address).contains(userInfo.emailAddressStr)) {
            GeneralServiceException(s"${userId.id} is not an owner of ${formId.idString}")
          }
        } yield ()
      }
    } yield ()
  }

  /** * Form standard alias mapping functions
    */
  private def updateStandardAliasMapping(
    id: FormVersionId,
    activitiesToAdd: Seq[StandardAliasMappingActivity],
    mappingToAdd: Map[String, String] = Map.empty,
    mappingToRemove: Set[String] = Set.empty
  ): Task[Unit] = {
    FDBRecordDatabase.transact(
      FDBOperations[(StandardAliasMappingStoreOperations, FormVersionStoreOperations)].Production
    ) { case (standardAliasMappingStoreOps, formVersionStoreOps) =>
      for {
        standardAliasMapping <- standardAliasMappingStoreOps.updateOrAdd(
          id = id,
          defaultData = StandardAliasMapping(
            id,
            mappingToAdd,
            activitiesToAdd
          ),
          updateFunc = prevMsg => {
            val newMapping =
              prevMsg.toStandardAliasMap.view.filterKeys(!mappingToRemove.contains(_)).toMap ++ mappingToAdd
            val newActivities = prevMsg.activities ++ activitiesToAdd
            prevMsg.copy(
              toStandardAliasMap = newMapping,
              activities = newActivities
            )
          }
        )
        _ <- formVersionStoreOps.updateFormVersionAsaCount(id)(_ => standardAliasMapping.toStandardAliasMap.size)
      } yield ()
    }
  }

  def addFormStandardAliasMapping(
    params: AddFormStandardAliasMappingParams,
    actorId: UserId
  )(
    using FDBCluster
  ): Task[Unit] = {
    for {
      _ <- validateFormPermission(params.id.parent, actorId)
      _ <- ZIOUtils.when(params.aliasMappingToAdd.nonEmpty)(
        updateStandardAliasMapping(
          id = params.id,
          mappingToAdd = params.aliasMappingToAdd,
          activitiesToAdd = Seq(
            AddStandardAliasMappingActivity(
              actorId,
              Option(Instant.now),
              params.aliasMappingToAdd
            )
          )
        )
      )
    } yield ()
  }

  def removeFormStandardAliasMapping(
    params: RemoveFormStandardAliasMappingParams,
    actorId: UserId
  )(
    using FDBCluster
  ): Task[Unit] = {
    for {
      _ <- validateFormPermission(params.id.parent, actorId)
      _ <- ZIOUtils.when(params.aliasesToRemove.nonEmpty)(
        updateStandardAliasMapping(
          id = params.id,
          mappingToRemove = params.aliasesToRemove,
          activitiesToAdd = Seq(
            RemoveStandardAliasMappingActivity(
              actorId,
              Option(Instant.now),
              params.aliasesToRemove.toSeq
            )
          )
        )
      )
    } yield ()
  }

  def importFormStandardAliasMapping(
    params: AddFormStandardAliasMappingParams,
    actorId: UserId
  )(
    using FDBCluster
  ): Task[ImportFormStandardAliasMappingResponse] = {
    for {
      getFormRes <- getForm(
        params.id.parent,
        Some(params.id),
        actorId
      )
      validMapping = getValidAsaMapping(params.aliasMappingToAdd, getFormRes.formData.form)
      curMapping <- getFormStandardAliasMapping(params.id, checkValidFormFieldsAndOptions = false)
      _ <- removeFormStandardAliasMapping(
        RemoveFormStandardAliasMappingParams(params.id, curMapping.keySet),
        actorId
      )
      _ <- addFormStandardAliasMapping(params.copy(aliasMappingToAdd = validMapping), actorId)
    } yield ImportFormStandardAliasMappingResponse(validMapping)
  }

  // Including fields/options named with ASA as well
  def getFullFormAsaMapping(
    actorId: UserId,
    formVersionId: FormVersionId
  )(
    using FDBCluster
  ): Task[GetFormStandardAliasMappingResponse] = {
    for {
      allASAs <- getAllStandardAliases
      form <- getForm(
        formVersionId.parent,
        Option(formVersionId),
        actorId,
        shouldCheckPermission = false
      ).map(_.formData.form)
      formMappingRes <- getFormStandardAliasMappingResponse(formVersionId, checkExistingFormAlias = false)
    } yield {
      val fullAsaMapping = StandardAliasUtils.getAsaAnnotatedAndMappedValues(
        form = form,
        formAsaMappings = formMappingRes.aliasMapping,
        allStandardMappings = allASAs
      )
      formMappingRes.copy(aliasMapping = fullAsaMapping)
    }
  }

  def getFormStandardAliasMappingResponse(
    id: FundSubFormIdTrait,
    checkExistingFormAlias: Boolean = true
  )(
    using FDBCluster
  ): Task[GetFormStandardAliasMappingResponse] = {
    for {
      aliasMappingRes <- FDBCommonDatabase().read(StandardAliasMappingStoreOperations.Production)(_.getOpt(id))
      aliasMapping = aliasMappingRes.map(_.toStandardAliasMap).getOrElse(Map.empty)
      lastUpdatedAt = aliasMappingRes
        .flatMap(
          _.activities.lastOption.flatMap {
            case add: AddStandardAliasMappingActivity       => add.occurredAt
            case remove: RemoveStandardAliasMappingActivity => remove.occurredAt
            case StandardAliasMappingActivity.Empty         => None
          }
        )
        .getOrElse(Instant.EPOCH)
      validMapping <- id match {
        case formVersionId: FormVersionId =>
          if (checkExistingFormAlias) {
            for {
              formData <- getFormDataUnsafe(formVersionId)
            } yield getValidAsaMapping(aliasMapping, formData.form)
          } else {
            ZIO.attempt(aliasMapping)
          }
        case _ => ZIO.attempt(aliasMapping)
      }
    } yield GetFormStandardAliasMappingResponse(validMapping, lastUpdatedAt)
  }

  def getFormStandardAliasMapping(
    id: FundSubFormIdTrait,
    checkValidFormFieldsAndOptions: Boolean = true
  )(
    using FDBCluster
  ): Task[Map[String, String]] = {
    getFormStandardAliasMappingResponse(id, checkValidFormFieldsAndOptions).map(_.aliasMapping)
  }

  def getAllStandardAliases: Task[List[StandardAlias]] = {
    asaService.getAllAsaListUnsafe
      .map(
        _.map { res =>
          StandardAlias(
            alias = res.id.idString,
            forFundSubWorkflowSetup = res.flags.contains(ForFsWorkflowSetupFlag()),
            forFieldValue = res.flags.contains(ForFieldValueFlag()),
            translation = res.shortDescription,
            jsonType = res.jsonType.name,
            widgetType = res.widgetType.name,
            section = res.section,
            description = res.longDescription,
            parentAlias = res.relations
              .find(_.relationType == AsaRelationType.PARENT)
              .map(_.relatedAsaId.idString)
              .getOrElse("")
              .toLowerCase // TODO: remove this `toLowerCase` when the parentAlias is properly set up
          )
        }
      )
      .catchAllCause { ex =>
        // In case of error, just log error & return some default standard aliases for FundSub setup
        ZIO.logWarningCause("Error[getAllStandardAliases]", ex).as {
          StandardAliasUtils.StandardAliasesForFundSubSetup.map { alias =>
            StandardAlias(alias, forFundSubWorkflowSetup = true)
          }
        }
      }
  }

  def checkAsaAvailability(
    formVersionId: FormVersionId
  )(
    using FDBCluster
  ): Task[Boolean] = {
    for {
      asaMapping <- getFormStandardAliasMapping(formVersionId, checkValidFormFieldsAndOptions = false)
    } yield {
      asaMapping.values.exists { asa =>
        !FormSaUtils.shouldHideAsa(asa)
      }
    }
  }

  def archiveForm(
    formId: FormId,
    actor: UserId
  )(
    using FDBCluster
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.id} is archiving form ${formId.idString}")
      _ <- validateFormPermission(formId, actor)
      instantNow <- ZIO.attempt(DateCalculator.instantNow)
      _ <- FDBRecordDatabase.transact(
        FDBOperations[
          (FormModelStoreOperations, FormActivityStoreOperations)
        ].Production
      ) { case (formOps, formActivityOps) =>
        for {
          formModel <- formOps.get(formId)
          _ <- RecordIO.when(!formModel.trackingStatus.isArchived) {
            formOps.update(formId) {
              _.copy(
                parentFolderId = FormFolderId(FormFolderId.ArchiveFolderId),
                trackingStatus = FormModel.FormTrackingStatus.Archived,
                lastEditedAt = Some(instantNow),
                lastEditor = Some(actor)
              )
            }
          }
          _ <- formActivityOps.create(
            FormActivityModel(
              formActivityId = FormActivityIdFactory.unsafeRandomId(formId),
              formId = formId,
              actor = actor,
              at = Some(instantNow),
              data = FormStatusUpdated(FormModel.FormTrackingStatus.Archived)
            )
          )
        } yield ()
      }
      _ <- ZIO.logInfo(s"Form $formId is archived")
    } yield ()
  }

  def unarchiveForm(
    formId: FormId,
    actor: UserId
  )(
    using FDBCluster
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.id} is un-archiving form ${formId.idString}")
      _ <- validateFormPermission(formId, actor)
      instantNow <- ZIO.attempt(DateCalculator.instantNow)
      _ <- FDBRecordDatabase.transact(
        FDBOperations[
          (FormModelStoreOperations, FormActivityStoreOperations)
        ].Production
      ) { case (formOps, formActivityOps) =>
        for {
          formModel <- formOps.get(formId)
          _ <- RecordIO.when(formModel.trackingStatus.isArchived) {
            formOps.update(formId) {
              _.copy(
                parentFolderId = FormFolderId(FormFolderId.RootFolderId),
                trackingStatus = FormModel.FormTrackingStatus.Created,
                lastEditedAt = Some(instantNow),
                lastEditor = Some(actor)
              )
            }
          }
          _ <- formActivityOps.create(
            FormActivityModel(
              formActivityId = FormActivityIdFactory.unsafeRandomId(formId),
              formId = formId,
              actor = actor,
              at = Some(instantNow),
              data = FormStatusUpdated(FormModel.FormTrackingStatus.Created)
            )
          )
        } yield ()
      }
      _ <- ZIO.logInfo(s"Form $formId is unarchived")
    } yield ()
  }

  def getComponentLibraryVersion(
    actor: UserId
  )(
    using FDBCluster
  ): Task[GetComponentLibraryVersionResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.id} getting component library form version")
      hasReadPermission <- portalUserService.hasReadPermission(actor, PortalSectionId.Form)
      _ <- ZIOUtils.validate(hasReadPermission)(
        GeneralServiceException(s"${actor.id} doesn't have read form permission")
      )
      versionOpt <- FDBCommonDatabase().read(FormVersionStoreOperations.Production) { ops =>
        // Only support at most one form version at the moment
        ops.getFormVersionsForComponentLibrary.map(_.headOption)
      }
    } yield GetComponentLibraryVersionResponse(versionOpt)
  }

  def setFormVersionTag(
    params: SetFormVersionTagParams,
    actor: UserId
  ): Task[SetFormVersionTagResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.id} is setting form version's tags")
      hasManagePermission <- portalUserService.hasManagePermission(actor, PortalSectionId.Form)
      _ <- ZIOUtils.validate(hasManagePermission)(
        GeneralServiceException(s"${actor.id} doesn't have manage form permission")
      )
      model <- FDBRecordDatabase.transact(FormVersionStoreOperations.Production) { ops =>
        ops.updateFormVersionTag(params.formVersionId)(_ => params.tags)
      }
    } yield SetFormVersionTagResponse(model)
  }

  def setComponentLibraryVersion(
    params: SetComponentLibraryVersionParams,
    actor: UserId
  ): Task[SetComponentLibraryVersionResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.id} getting component library form version")
      hasManagePermission <- portalUserService.hasManagePermission(actor, PortalSectionId.Form)
      _ <- ZIOUtils.validate(hasManagePermission)(
        GeneralServiceException(s"${actor.id} doesn't have manage form permission")
      )
      model <- FDBRecordDatabase.transact(FormVersionStoreOperations.Production) { ops =>
        for {
          _ <- ops.setFormComponentLibrary(params.formVersionId, params.enable)
          model <- ops.getVersion(params.formVersionId)
        } yield model
      }
    } yield SetComponentLibraryVersionResponse(model)
  }

  def getAllLibraryComponents(
    actor: UserId
  )(
    using FDBCluster
  ): Task[GetAllLibraryComponentsResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.id} getting all library components")
      hasReadPermission <- portalUserService.hasReadPermission(actor, PortalSectionId.Form)
      _ <- ZIOUtils.validate(hasReadPermission)(
        GeneralServiceException(s"${actor.id} doesn't have read form permission")
      )
      formVersionIds <- FDBCommonDatabase().read(FormVersionStoreOperations.Production)(
        _.getFormVersionsForComponentLibrary.map(_.map(_.formVersionId))
      )
      allComponents <- ZIO.foreach(formVersionIds) { formVersionId =>
        getForm(
          formId = formVersionId.parent,
          versionIdOpt = Some(formVersionId),
          actor = actor,
          shouldCheckPermission = false
        ).map(resp => FormComponentUtils.extractLibraryComponents(resp.formData.form))
      }
    } yield GetAllLibraryComponentsResponse(allComponents.flatten)
  }

  def getFormVersionStorageKeyUnsafe(
    versionId: FormVersionId
  )(
    using FDBCluster
  ): Task[String] = {
    for {
      version <- FDBCommonDatabase().read(FormVersionStoreOperations.Production)(_.getVersion(versionId))
    } yield formStorageService.getVersionKey(versionId, version.storageUniqueKey)
  }

  def getFormDraftVersionStorageKeyUnsafe(
    formId: FormId
  )(
    using FDBCluster
  ): Task[String] = {
    for {
      formModel <- FDBCommonDatabase().read(FormModelStoreOperations.Production)(_.get(formId))
    } yield formStorageService.getDraftKeyUnsafe(formModel)
  }

  def getInvestorAccessFormVersions(
    actor: UserId
  )(
    using FDBCluster
  ): Task[GetInvestorAccessVersionResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.id} getting Investor Access form version")
      hasFormReadPermission <- portalUserService.hasReadPermission(actor, PortalSectionId.Form)
      hasFundSubPermission <- portalUserService.hasReadPermission(actor, PortalSectionId.FundSub)
      _ <- ZIOUtils.validate(hasFormReadPermission || hasFundSubPermission)(
        GeneralServiceException(s"${actor.id} doesn't have read form permission")
      )
      versions <- FDBCommonDatabase().read(FormVersionStoreOperations.Production)(_.getFormVersionsForInvestorAccess)
    } yield GetInvestorAccessVersionResponse(versions)
  }

  def setInvestorAccessFormVersion(
    params: SetInvestorAccessVersionParams,
    actor: UserId
  ): Task[SetInvestorAccessVersionResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.id} updating Investor Access form version")
      hasFundSubWritePermission <- portalUserService.hasWritePermission(actor, PortalSectionId.FundSub)
      _ <- ZIOUtils.validate(hasFundSubWritePermission)(
        GeneralServiceException(s"${actor.id} doesn't have manage form permission")
      )
      model <- FDBRecordDatabase.transact(FormVersionStoreOperations.Production) { ops =>
        for {
          _ <- ops.setFormInvestorAccess(params.formVersionId, params.enable)
          model <- ops.getVersion(params.formVersionId)
        } yield model
      }
    } yield SetInvestorAccessVersionResponse(model)
  }

  def getAllInvestorAccessAsaValues(
    actor: UserId
  )(
    using FDBCluster
  ): Task[GetAllInvestorAccessAsaValuesResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.id} getting all Investor Access forms")
      hasReadPermission <- portalUserService.hasReadPermission(actor, PortalSectionId.Form)
      _ <- ZIOUtils.validate(hasReadPermission)(
        GeneralServiceException(s"${actor.id} doesn't have read form permission")
      )
      formVersionIds <- getInvestorAccessFormVersions(actor).map(_.versions.map(_.formVersionId))
      mappings <- ZIO.foreach(formVersionIds) { formVersionId =>
        for {
          formResp <- getForm(
            formVersionId.parent,
            Some(formVersionId),
            actor
          )
          form = formResp.formData.form
          formStandardAliasMappings <- getFormStandardAliasMapping(
            id = formVersionId,
            checkValidFormFieldsAndOptions = false
          )
          allStandardMappings <- getAllStandardAliases
        } yield StandardAliasUtils.getAsaAnnotatedAndMappedValues(
          form = form,
          formAsaMappings = formStandardAliasMappings,
          allStandardMappings = allStandardMappings
        )

      }
      res = mappings.flatMap(_.values).toSet
    } yield GetAllInvestorAccessAsaValuesResponse(res)
  }

  def copyFiles(
    params: CopyFormFilesParams,
    actor: UserId
  )(
    using FDBCluster
  ): Task[CopyFormFilesResponse] = {
    val fileIdsStr = params.fileIdsAndNames.map(_._1.idString).mkString(", ")
    for {
      _ <- ZIO.logInfo(s"User ${actor.id} is copying files $fileIdsStr in form ${params.formId.idString}")
      _ <- validateFormPermission(params.formId, actor)
      formFolderId <- ZIO.attempt(FolderId.channelSystemFolderId(params.formId))
      newFileIdsAndNames <- ZIO.foreach(params.fileIdsAndNames) { case (fileId, fileName) =>
        for {
          _ <- validateFormFile(
            params.formId,
            fileId,
            actor
          )
          // Always create a new file instead of renaming existing file
          newFileId <- fileService.copyFileInSameFeature(
            actor = actor,
            originFileId = fileId,
            folderId = formFolderId,
            ctx = None,
            fileNameOpt = Some(fileName)
          )
          newFileName <- fileService.getFileName(actor)(newFileId)
        } yield newFileId -> newFileName
      }
    } yield CopyFormFilesResponse(newFileIdsAndNames)
  }

  private def validateFormFile(
    formId: FormId,
    fileId: FileId,
    actor: UserId
  )(
    using FDBCluster
  ) = {
    ZIOUtils.unless(FolderId.channelSystemFolderId(formId).isAncestorOf(fileId)) {
      for {
        formResp <- getForm(
          formId = formId,
          versionIdOpt = None,
          actor = actor,
          shouldCheckPermission = false
        )
        _ <- ZIOUtils.validate {
          formResp.formData.allFileIds.contains(FormDataConverters.fileIdToForm(fileId))
        }(
          GeneralServiceException(s"File ${fileId.idString} doesn't belong to form ${formId.idString}")
        )
      } yield ()
    }
  }

  def exportFormAsaMappingTemplate(
    params: ExportFormAsaMappingTemplateParams,
    actor: UserId
  )(
    using FDBCluster
  ): Task[ExportFormAsaMappingTemplateResp] = {
    val formMappingAnalyticService = FormMappingAnalyticService(this)
    for {
      _ <- validateFormPermission(params.formId, actor)
      formAnalyticDataOpt <- formMappingAnalyticService.getFormAnalyticData(actor, params.formVersionId)
      formAnalyticData <- ZIOUtils.optionToTask(
        formAnalyticDataOpt,
        GeneralServiceException(s"Failed to get analytic data of form ${params.formVersionId.idString}")
      )
      formAsaAnalytic = formAnalyticData.asaAnalyticInfos
      fileContent <- FormImportUtils.generateAsaMappingTemplateFile(
        fileDataToFill = formAsaAnalytic
      )
      folderId <- fileService.createUserTemporaryFolderIfNeeded(actor)
      fileId <- fileService.uploadFile(
        folderId,
        params.formName + "_asa_mapping_template.xlsx",
        fileContent,
        actor
      )
    } yield ExportFormAsaMappingTemplateResp(fileId)
  }

  def processFormAsaMappingTemplate(
    params: ProcessFormAsaMappingTemplateParams,
    userId: UserId
  )(
    using FDBCluster
  ): Task[ProcessFormAsaMappingTemplateResp] = {
    val formMappingAnalyticService = FormMappingAnalyticService(this)
    for {
      _ <- validateFormPermission(params.formId, userId)
      spreadsheetData <- fileService.useFileInputStream(
        userId,
        params.fileId,
        None
      ) { inputStream =>
        SpreadsheetUtils.useWorkbookInputStream(inputStream) { workbook =>
          {
            ZIO.attemptBlocking {
              FormImportUtils.getAsaMapFromTemplateWorkbook(workbook)
            }
          }
        }
      }
      formAnalyticDataOpt <- formMappingAnalyticService.getFormAnalyticData(userId, params.formVersionId)
      formAnalyticData <- ZIOUtils.optionToTask(
        formAnalyticDataOpt,
        GeneralServiceException(s"Failed to get analytic data of form ${params.formVersionId.idString}")
      )
      formAsaAnalytic = formAnalyticData.asaAnalyticInfos
      allStandardAliases <- getAllStandardAliases
      (validAsaMap, invalidAsaMap) = FormImportUtils.validateFormAsaMappingTemplate(
        spreadsheetData,
        formAsaAnalytic,
        allStandardAliases
      )
    } yield ProcessFormAsaMappingTemplateResp(validAsaMap, invalidAsaMap)
  }

  def editFormAssociatedLinks(
    formId: FormId,
    newAssociatedLinks: Map[String, AssociatedLink],
    actor: UserId
  )(
    using FDBCluster
  ): Task[EditFormAssociatedLinksResponse] = {
    for {
      _ <- ZIO.logInfo(s"${actor.id} is updating form ${formId.idString} associated links")
      _ <- validateFormPermission(formId, actor)
      updatedModel <- FDBRecordDatabase.transact(FormModelStoreOperations.Production)(
        _.update(formId)(
          _.copy(
            lastEditedAt = Some(Instant.now()),
            lastEditor = Some(actor),
            associatedLinks = newAssociatedLinks
          )
        )
      )
      _ <- ZIO.logInfo(s"Form ${formId.idString} edited")
    } yield EditFormAssociatedLinksResponse(updatedModel)
  }

  def generateSignatures(
    resourceId: FormId | BlueprintId,
    fileId: FileId,
    documentSignature: DocumentSignatureMessage,
    signatureModel: SignatureMessage,
    actor: UserId
  )(
    using FDBCluster
  ): Task[GenerateSignaturesResponse] = {
    val folderId = resourceId match {
      case formId: FormId           => FolderId.channelSystemFolderId(formId)
      case blueprintId: BlueprintId => FolderId.channelSystemFolderId(blueprintId)
    }

    for {
      _ <- ZIO.logInfo(resourceId match {
        case formId: FormId =>
          s"${actor.id} is generating signatures for file ${fileId.idString} in form ${formId.idString}"
        case blueprintId: BlueprintId =>
          s"${actor.id} is generating signatures for file ${fileId.idString} in blueprint ${blueprintId.idString}"
      })
      _ <- resourceId match {
        case formId: FormId           => validateFormPermission(formId, actor)
        case blueprintId: BlueprintId => blueprintService.validateBlueprintPermission(blueprintId, actor)
      }
      fileName <- fileService.getFileName(actor)(fileId)
      pdfStorageId <- fileService.getFileStorageId(
        actor,
        fileId,
        DmsTrackingActivityType.Internal,
        None
      )
      outputPdfStorageId = s3Service.generateRandomStorageId(
        Some(folderId),
        FilenameUtils.appendSuffix(fileName, "_filled")
      )
      fillPdfExceptionOpt <- fillPdfService.fillPdfFile(
        pdfStorageId = pdfStorageId,
        fields = Map.empty,
        outputStorageId = outputPdfStorageId
      )
      _ <- ZIO.foreach(fillPdfExceptionOpt) { fillPdfException =>
        ZIO.fail(GeneralServiceException(fillPdfException.getMessage))
      }
      signedFileId <- ZIO.scoped[Any] {
        for {
          documentStream <- s3Service.downloadFromS3Scoped(s3Service.s3Config.bucket, outputPdfStorageId.id)
          document <- PdfBoxHelper.mkDocumentFromStream(documentStream, None)
          _ <- signatureIntegrationService.addSignaturesOnPdf(document, documentSignature, signatureModel)
          signedDocumentStream <- ZStreamIOUtils.fromOutputStreamWriterTask(document.save)
          signedFileId <- fileService.uploadFile(
            folderId,
            FilenameUtils.appendSuffix(fileName, "_signed"),
            FileContentOrigin.FromSource(signedDocumentStream, MediaType.ApplicationPdf),
            actor
          )
        } yield signedFileId
      }
    } yield GenerateSignaturesResponse(signedFileId)
  }

  def getFilePageSizes(
    resourceId: FormId | BlueprintId,
    fileId: FileId,
    actor: UserId
  )(
    using FDBCluster
  ): Task[GetFilePageSizesResponse] = {
    for {
      _ <- ZIO.logInfo(resourceId match {
        case formId: FormId =>
          s"${actor.id} is getting page sizes for file ${fileId.idString} in form ${formId.idString}"
        case blueprintId: BlueprintId =>
          s"${actor.id} is getting page sizes for file ${fileId.idString} in blueprint ${blueprintId.idString}"
      })
      _ <- resourceId match {
        case formId: FormId           => validateFormPermission(formId, actor)
        case blueprintId: BlueprintId => blueprintService.validateBlueprintPermission(blueprintId, actor)
      }
      pageMediaBoxMap <- pdfFileService.getPageMediaBoxes(fileId, actor, Internal, None)
      pageSizeMap = pageMediaBoxMap.view.mapValues { pageSize =>
        PageWidthHeight(pageSize.width, pageSize.height)
      }.toMap
    } yield GetFilePageSizesResponse(pageSizeMap)
  }

  def importFromAnnotationDocument(
    formId: FormId,
    importData: List[(AnnotationDocumentVersionId, List[PdfObjectId])],
    ctx: AuthenticatedRequestContext
  )(
    using FDBCluster
  ): Task[ImportFromAnnotationDocumentResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"${ctx.actor.userId.id} is importing data from annotation versions ${importData.map(_._1).mkString("[", ", ", "]")}"
      )
      _ <- validateFormPermission(formId, ctx.actor.userId)
      resp <- ZIO.scoped {
        AnnotationImporter
          .getAnnotationImportHelper(
            formId,
            annotationDocumentService,
            fileService,
            ctx.actor.userId
          )
          .flatMap { annotationImportHelperService =>
            ZIO.provideLayer {
              ZLayer.succeed(fileService) ++
                ZLayer.succeed(ctx) ++
                ZLayer.succeed(muPdfServerless) ++
                ZLayer.succeed(annotationImportHelperService)
            } {
              ZIO.foldLeft {
                importData.flatMap { (versionId, pdfObjectIds) =>
                  pdfObjectIds.map(versionId -> _)
                }
              }(
                ImportFromAnnotationDocumentResponse.Empty
              ) { case (resp, (versionId, pdfObjectId)) =>
                for {
                  result <- AnnotationImporter.convertObjectIdToFormData(versionId, pdfObjectId)
                  nextResp = result match {
                    case ConversionResult.SchemaWidgetData(keySchemaSeq, widgetMap, cutFileMap) =>
                      resp.copy(
                        keySchemas = (resp.keySchemas ++ keySchemaSeq.iterator).distinctBy(_._1),
                        widgetMap = resp.widgetMap ++ widgetMap,
                        cutInfoMap = resp.cutInfoMap ++ cutFileMap
                      )
                    case _ => resp
                  }
                } yield nextResp
              }
            }
          }
      }
    } yield resp
  }

  def updateBlueprintRef(
    params: UpdateBlueprintRefParams,
    ctx: AuthenticatedRequestContext
  )(
    using FDBCluster
  ): Task[UpdateBlueprintRefResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"${ctx.actor.userId.id} is updating blueprint reference for version ${params.formVersionIdOpt.fold("draft version")(_.idString)} in form  ${params.formId.idString}"
      )
      _ <- validateFormPermission(params.formId, ctx.actor.userId)
      _ <- FDBCommonDatabase().read(FormModelStoreOperations.Production)(_.get(params.formId))
      blueprintRefOpt <- FDBRecordDatabase.transact(
        FDBOperations[(FormModelStoreOperations, FormVersionStoreOperations)].Production
      ) { (modelOps, versionOps) =>
        val newBlueprintRefOpt = Some(
          BlueprintRef(
            id = params.blueprintId,
            versionId = params.blueprintVersionId,
            versionIndex = params.versionIndex,
            name = params.blueprintName,
            createdAt = Some(DateCalculator.instantNow),
            createdBy = ctx.actor.userId,
            lastUpdatedAt = Some(DateCalculator.instantNow),
            lastUpdatedBy = ctx.actor.userId
          )
        )

        for {
          _ <- params.formVersionIdOpt match {
            case Some(versionId) =>
              versionOps.updateMetadata(versionId) { (oldMetadata, oldSystemMetadata) =>
                (
                  oldMetadata.copy(blueprintRef = newBlueprintRefOpt),
                  oldSystemMetadata
                )
              }
            case None =>
              modelOps.update(params.formId) { formModel =>
                formModel.copy(
                  draftMetadata = formModel.draftMetadata.map(
                    _.copy(blueprintRef = newBlueprintRefOpt)
                  )
                )

              }
          }
        } yield newBlueprintRefOpt
      }
    } yield UpdateBlueprintRefResponse(blueprintRefOpt = blueprintRefOpt)
  }

  def removeBlueprintRef(
    params: RemoveBlueprintRefParams,
    ctx: AuthenticatedRequestContext
  )(
    using FDBCluster
  ): Task[RemoveBlueprintRefResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"${ctx.actor.userId.id} is removing blueprint reference for version ${params.formVersionIdOpt.fold("draft version")(_.idString)} in form  ${params.formId.idString}"
      )
      _ <- validateFormPermission(params.formId, ctx.actor.userId)
      _ <- FDBCommonDatabase().read(FormModelStoreOperations.Production)(_.get(params.formId))
      _ <- FDBRecordDatabase.transact(
        FDBOperations[(FormModelStoreOperations, FormVersionStoreOperations)].Production
      ) { (modelOps, versionOps) =>
        for {
          _ <- params.formVersionIdOpt match {
            case Some(versionId) =>
              versionOps.updateMetadata(versionId) { (oldMetadata, oldSystemMetadata) =>
                (
                  oldMetadata.copy(blueprintRef = None),
                  oldSystemMetadata
                )
              }
            case None =>
              modelOps.update(params.formId) { formModel =>
                formModel.copy(
                  draftMetadata = formModel.draftMetadata.map(
                    _.copy(blueprintRef = None)
                  )
                )

              }
          }
        } yield ()
      }
    } yield RemoveBlueprintRefResponse(blueprintRefOpt = None)
  }

  def diffFormBlueprint(
    formId: FormId,
    firstVersionIdOpt: Option[FormVersionId],
    secondVersionIdOpt: Option[FormVersionId],
    actor: UserId
  )(
    using FDBCluster
  ): Task[DiffFormBlueprintResponse] = {
    val firstVersionId = firstVersionIdOpt.fold("draft version")(_.idString)
    val secondVersionId = secondVersionIdOpt.fold("draft version")(_.idString)
    for {
      _ <- ZIO.logInfo(s"${actor.id} is comparing $firstVersionId and $secondVersionId")
      _ <- validateFormPermission(formId, actor)
      _ <- FDBCommonDatabase().read(FormModelStoreOperations.Production)(_.get(formId))
      (_, _, firstVersionMetadata, _, _, _) <-
        getFormModelsUnsafe(
          formId,
          firstVersionIdOpt
        )
      (_, _, secondVersionMetadata, _, _, _) <-
        getFormModelsUnsafe(
          formId,
          secondVersionIdOpt
        )
    } yield DiffFormBlueprintResponse(firstVersionMetadata.blueprintRef, secondVersionMetadata.blueprintRef)
  }

}
