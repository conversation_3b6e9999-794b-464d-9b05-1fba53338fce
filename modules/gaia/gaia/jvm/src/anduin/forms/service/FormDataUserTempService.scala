//  Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.service

import java.time.Instant

import zio.{Task, ZIO}

import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.forms.endpoint.GetFormDataUserTempParams
import anduin.forms.engine.GaiaState
import anduin.forms.storage.{FormDataUserTempStoreOperations, FormVersionDataStoreOperations}
import anduin.id.form.{FormVersionDataId, FormVersionId}
import anduin.model.common.user.UserId
import anduin.service.GeneralServiceException
import com.anduin.stargazer.service.utils.ZIOUtils

/** Service for managing temporary form data for users. Only owner can access the data. Form data will be removed after
  * 30 days of inactivity.
  */
final case class FormDataUserTempService() {

  def createUserTempFormDataInternal(
    owner: UserId,
    formVersionId: FormVersionId,
    formData: GaiaState
  ): Task[FormVersionDataId] = {
    for {
      _ <- ZIO.logInfo(s"Create temporary form data for user $owner")
      formDataId <- FDBRecordDatabase.transact(
        FDBOperations[(FormDataUserTempStoreOperations, FormVersionDataStoreOperations)].Production
      ) { case (formDataUserTempOps, formDataOps) =>
        for {
          formDataId <- formDataOps.create(
            formVersionId = formVersionId,
            creator = owner,
            data = formData
          )
          _ <- formDataUserTempOps.create(
            owner = owner,
            formDataId = formDataId
          )
        } yield formDataId
      }
    } yield formDataId
  }

  def deleteUserTempFormDataInternal(
    owner: UserId,
    formDataId: FormVersionDataId,
    keepFormDataForInternalUsage: Boolean = false
  ): Task[Boolean] = {
    FDBRecordDatabase.transact(
      FDBOperations[(FormDataUserTempStoreOperations, FormVersionDataStoreOperations)].Production
    ) { case (formDataUserTempOps, formDataOps) =>
      for {
        _ <- RecordIO.unless(keepFormDataForInternalUsage)(
          formDataOps.delete(formDataId)
        )
        deleted <- formDataUserTempOps.delete(
          owner = owner,
          formDataId = formDataId
        )
      } yield deleted
    }
  }

  def getUserTempFormData(
    params: GetFormDataUserTempParams,
    actor: UserId
  ): Task[GaiaState] = {
    for {
      _ <- ZIO.logInfo(s"$actor get user temporary form data ${params.formDataId}")
      _ <- ZIOUtils.validate(
        FormDataUserTempStoreOperations.transact(_.getOpt(actor, params.formDataId)).map(_.nonEmpty)
      )(GeneralServiceException(s"$actor does not have access to temporary form data ${params.formDataId}"))

      gaiaState <- FDBRecordDatabase.transact(
        FDBOperations[(FormDataUserTempStoreOperations, FormVersionDataStoreOperations)].Production
      ) { case (formDataUserTempOps, formDataOps) =>
        for {
          _ <- formDataUserTempOps.update(actor, params.formDataId)(_.copy(lastAccessedAt = Instant.now))
          gaiaState <- formDataOps.get(params.formDataId)
        } yield gaiaState
      }
    } yield gaiaState
  }

}
