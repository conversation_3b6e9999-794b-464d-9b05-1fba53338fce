// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.service

import io.circe.Json
import io.circe.syntax.*

import anduin.forms.Form.DefaultNamespace
import anduin.forms.model.Schema
import anduin.forms.rules.FormRule
import anduin.forms.ui.types.{MultipleOptionType, SingleOptionType}
import anduin.forms.ui.{UIKey, UIOptions, Widget, WidgetType}
import anduin.forms.{Form, FormSchema}

object FormServiceTestUtils {

  def defaultApply(
    defaultFormSchema: Schema.obj,
    defaultUiSchema: Map[String, Widget],
    rules: List[FormRule]
  ): Form = {
    Form(
      namespaceFormSchemaMap = Map(DefaultNamespace -> FormSchema(defaultFormSchema, defaultUiSchema)),
      rules = rules,
      defaultNamespace = DefaultNamespace,
      triggerRuleByProperty = false
    )
  }

  val testForm1 = FormServiceTestUtils.defaultApply(
    defaultFormSchema = Schema.obj(
      Schema.obj.Field("investorname", Schema.string()),
      Schema.obj.Field("investoraddress1", Schema.string()),
      Schema.obj.Field("maxnumboardmember", Schema.integer()),
      Schema.obj
        .Field(
          "repeat",
          Schema.array(
            Schema.obj(
              Schema.obj.Field("emailaddress", Schema.string()),
              Schema.obj.Field("phone", Schema.string())
            )
          )
        ),
      Schema.obj
        .Field(
          "investortype",
          Schema.array(
            Schema.`enum`(
              Schema.string(),
              Seq(
                Json.fromString("usperson"),
                Json.fromString("scorp"),
                Json.fromString("ccorp")
              )
            )
          )
        )
    ),
    defaultUiSchema = Map(
      "investorname" -> Widget(WidgetType.TextBox, UIOptions.fromMap(Map(UIKey.formattedText -> "Investor name".asJson))),
      "investoraddress1" -> Widget(
        WidgetType.TextArea,
        UIOptions.fromMap(Map(UIKey.formattedText -> "Investor address".asJson))
      ),
      "maxnumboardmember" -> Widget(
        WidgetType.Integer,
        UIOptions.fromMap(Map(UIKey.formattedText -> "Maximum number of BM".asJson))
      ),
      "repeat" -> Widget(WidgetType.Repeatable, UIOptions.fromMap(Map(UIKey.maxLength -> Json.fromInt(3)))),
      "emailaddress" -> Widget(WidgetType.Email, UIOptions.fromMap(Map(UIKey.formattedText -> "Email".asJson))),
      "phone" -> Widget(WidgetType.Email, UIOptions.fromMap(Map(UIKey.formattedText -> "Phone".asJson))),
      "investortype" -> Widget(
        WidgetType.MultipleCheckbox,
        UIOptions.fromMap(
          Map(
            UIKey.multipleOption -> MultipleOptionType(
              Map(
                "usperson" -> SingleOptionType(Seq("mapping1"), "US person"),
                "scorp" -> SingleOptionType(Seq("mapping2"), "S-Corp"),
                "ccorp" -> SingleOptionType(Seq("mapping3"), "C-Corp")
              )
            ).asJson
          )
        )
      )
    ),
    rules = List.empty
  )

  val testSaProfileInfo = (profileName = "Test SA profile", aliasList = List("investor_name", "bank_account_name"))

  val testForm1SaProfileMapping = Map("investorname" -> "investor_name")

  val testForm1AsaMapping = Map(
    "maxnumboardmember" -> "max_bm_count_fs_setup",
    "investortype#usperson" -> "investortype_usperson",
    "investortype#scorp" -> "investortype_scorp",
    "investortype#ccorp" -> "investortype_ccorp"
  )

  val testForm1OntologyMapping = Map(
    "maxnumboardmember" -> "asa:max_board_member_count#int:investor#concept:general_info#general_info:document",
    "investortype#usperson" -> "asa:att_(investor, att_(individual, is_us_resident)#bool)#bool:investor#concept:general_info#general_info:document",
    "investortype#scorp" -> "asa:att_(investor, att_(entity, is_scorp)#bool)#bool:investor#concept:general_info#general_info:document",
    "investortype#ccorp" -> "asa:att_(investor,att_(entity, is_ccorp)#bool)#bool:investor#concept:general_info#general_info:document"
  )

  val testForm2 = FormServiceTestUtils.defaultApply(
    defaultFormSchema = Schema.obj(
      Schema.obj.Field("investorname", Schema.string()),
      Schema.obj.Field("investoraddress", Schema.string()),
      Schema.obj.Field("investorfulladdress", Schema.string()),
      Schema.obj.Field("maximumboardmembercount", Schema.integer()),
      Schema.obj
        .Field(
          "repeat1",
          Schema.array(
            Schema.obj(
              Schema.obj.Field("emailaddress", Schema.string()),
              Schema.obj.Field("phone", Schema.string())
            )
          )
        ),
      Schema.obj
        .Field(
          "individualinvestortype",
          Schema.array(
            Schema.`enum`(
              Schema.string(),
              Seq(Json.fromString("usresident"), Json.fromString("nonusresident"))
            )
          )
        ),
      Schema.obj
        .Field(
          "entityinvestortype",
          Schema.array(
            Schema.`enum`(
              Schema.string(),
              Seq(Json.fromString("scorporation"), Json.fromString("ccorporation"))
            )
          )
        )
    ),
    defaultUiSchema = Map(
      "investorname" -> Widget(WidgetType.TextBox, UIOptions.fromMap(Map(UIKey.formattedText -> "Investor name".asJson))),
      "investoraddress" -> Widget(
        WidgetType.TextArea,
        UIOptions.fromMap(Map(UIKey.formattedText -> "Investor address".asJson))
      ),
      "investorfulladdress" -> Widget(
        WidgetType.TextArea,
        UIOptions.fromMap(Map(UIKey.formattedText -> "Investor full address".asJson))
      ),
      "maximumboardmembercount" -> Widget(
        WidgetType.Integer,
        UIOptions.fromMap(Map(UIKey.formattedText -> "Maximum number of BM".asJson))
      ),
      "repeat1" -> Widget(WidgetType.Repeatable, UIOptions.fromMap(Map(UIKey.maxLength -> Json.fromInt(3)))),
      "emailaddress" -> Widget(WidgetType.Email, UIOptions.fromMap(Map(UIKey.formattedText -> "Email".asJson))),
      "phone" -> Widget(WidgetType.Email, UIOptions.fromMap(Map(UIKey.formattedText -> "Phone".asJson))),
      "individualinvestortype" -> Widget(
        WidgetType.MultipleCheckbox,
        UIOptions.fromMap(
          Map(
            UIKey.multipleOption -> MultipleOptionType(
              Map(
                "usresident" -> SingleOptionType(Seq("mapping1"), "US resident"),
                "nonusresident" -> SingleOptionType(Seq("mapping2"), "Non-US resident")
              )
            ).asJson
          )
        )
      ),
      "entityinvestortype" -> Widget(
        WidgetType.MultipleCheckbox,
        UIOptions.fromMap(
          Map(
            UIKey.multipleOption -> MultipleOptionType(
              Map(
                "scorporation" -> SingleOptionType(Seq("mapping1"), "S-Corp"),
                "ccorporation" -> SingleOptionType(Seq("mapping2"), "C-Corp")
              )
            ).asJson
          )
        )
      )
    ),
    rules = List(
      FormRule(
        value = """function(investoraddress)
                  |  atd.add("investorfulladdress", "value", investoraddress.value)
                  |""".stripMargin,
        name = "rule1",
        defaultNamespace = Form.DefaultNamespace
      )
    )
  )

  val testForm2SaProfileMapping = Map("investorname" -> "investor_name")

  val testForm2AsaMapping = Map(
    "maximumboardmembercount" -> "max_bm_count_fs_setup",
    "individualinvestortype#usresident" -> "investortype_usperson",
    "entityinvestortype#scorporation" -> "investortype_scorp",
    "entityinvestortype#ccorporation" -> "investortype_ccorp"
  )

  val testForm2OntologyMapping = Map(
    "maximumboardmembercount" -> "asa:max_board_member_count#int:investor#concept:general_info#general_info:document",
    "individualinvestortype#usresident" -> "asa:att_(investor, att_(individual, is_us_resident)#bool)#bool:investor#concept:general_info#general_info:document",
    "entityinvestortype#scorporation" -> "asa:att_(investor, att_(entity, is_scorp)#bool)#bool:investor#concept:general_info#general_info:document",
    "entityinvestortype#ccorporation" -> "asa:att_(investor,att_(entity, is_ccorp)#bool)#bool:investor#concept:general_info#general_info:document"
  )

}
