// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.gondor.server

import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter

import anduin.cue.service.CueModuleService.CueModuleServiceKeySpace
import anduin.digitization.common.DigitizationServiceKeyspace
import anduin.portaluser.PortalUserService
import anduin.sa.endpoints.SaCueEndpoints.{
  createSaDataTemplateCueModule,
  generateSaDataTemplateCueModule,
  getSaDataTemplateCueModule
}
import anduin.sa.service.SaCueService
import anduin.sa.service.permission.SaEndpointValidatorServer
import anduin.tapir.endpoint.EmptyResponse
import anduin.tapir.server.AuthenticatedValidationEndpointServer
import anduin.tapir.server.EndpointServer.TapirServerService
import com.anduin.stargazer.service.authorization.AuthorizationService

final class SaCueServer(
  protected val authorizationService: AuthorizationService,
  saCueService: SaCueService,
  override protected val interpreter: ArmeriaZioServerInterpreter[Any]
)(
  using val portalUserService: PortalUserService
) extends AuthenticatedValidationEndpointServer
    with SaEndpointValidatorServer {

  private given DigitizationServiceKeyspace.KeySpace = DigitizationServiceKeyspace.KeySpace.Production
  private given CueModuleServiceKeySpace = CueModuleServiceKeySpace.Production

  private val createSaDataTemplateCueModuleService =
    validateRouteCatchError(createSaDataTemplateCueModule, saManagementWriteValidator) { (params, ctx) =>
      saCueService.createSaDataTemplateCueModule(
        params.saDataTemplateId,
        ctx.actor.userId,
        ignorePermissionCheck = true
      )
    }

  private val getSaDataTemplateCueModuleService =
    validateRouteCatchError(getSaDataTemplateCueModule, saManagementReadValidator) { (params, ctx) =>
      saCueService.getSaDataTemplateCueModule(
        params.saDataTemplateId,
        ctx.actor.userId,
        ignorePermissionCheck = true
      )
    }

  private val generateSaDataTemplateCueModuleService = validateRouteCatchError(
    generateSaDataTemplateCueModule,
    saManagementWriteValidator
  ) { (params, ctx) =>
    saCueService
      .generateSaDataTemplateCueModule(
        params.saDataTemplateId,
        ctx.actor.userId,
        ignorePermissionCheck = true
      )
      .as(EmptyResponse())
  }

  val services: List[TapirServerService] = List(
    createSaDataTemplateCueModuleService,
    getSaDataTemplateCueModuleService,
    generateSaDataTemplateCueModuleService
  )

}
