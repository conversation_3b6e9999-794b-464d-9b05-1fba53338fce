// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.gondor.server

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration

import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter

import anduin.fdb.record.DefaultCluster
import anduin.forms.endpoint.FormEndpoints.*
import anduin.forms.endpoint.integration.FormIntegrationEndpoints.*
import anduin.forms.endpoint.{GetFormLockResponse, RenewFormLockResponse}
import anduin.forms.service.*
import anduin.tapir.server.AuthenticatedEndpointServer
import anduin.tapir.server.EndpointServer.TapirServerService
import com.anduin.stargazer.apps.stargazer.StargazerSettings
import com.anduin.stargazer.service.authorization.AuthorizationService
import anduin.cue.service.CueModuleService
import anduin.tapir.endpoint.EmptyResponse

final class FormServer(
  protected val authorizationService: AuthorizationService,
  formService: FormService,
  formMatchingService: FormMatchingService,
  formFolderService: FormFolderService,
  formDataService: FormDataService,
  formLockService: FormLockService,
  formToolConfigService: FormToolConfigService,
  formIntegrationService: FormIntegrationService,
  dataLayerService: DataLayerService,
  formDataUserTempService: FormDataUserTempService,
  override protected val interpreter: ArmeriaZioServerInterpreter[Any]
) extends AuthenticatedEndpointServer {

  private given CueModuleService.CueModuleServiceKeySpace = CueModuleService.CueModuleServiceKeySpace.Production

  private val ExportFormMatchingTimeoutSeconds = 120L

  private val pdfAnnotationServerlessTimeout =
    StargazerSettings.gondorConfig.backendConfig.serverlessConfig.serverlessFunctions.pdfAnnotationRunner.executionTimeout

  val services: List[TapirServerService] = List(
    authRouteCatchError(extractText) { (params, context) =>
      formService.extractText(params.fileIds, context.actor.userId)
    },
    authRouteCatchError(processFile) { (params, context) =>
      formService.processFile(params.fileIds, context.actor.userId)
    },
    authRouteCatchError(processAnnotationDocument, timeout = defaultTimeout + pdfAnnotationServerlessTimeout) {
      (params, context) =>
        formService.processAnnotationDocument(
          params.formId,
          params.annotationDocumentVersionId,
          params.fileNameOverride,
          context.actor.userId
        )
    },
    authRouteCatchError(createForm) { (params, context) =>
      formService.createForm(
        params.formName,
        params.tags,
        params.owners,
        context.actor.userId,
        parentFolderIdOpt = params.parentFolderIdOpt,
        fundEngagementTypeOpt = params.fundEngagementTypeOpt,
        numFundraiseOpt = params.numFundraiseOpt
      )
    },
    authRouteCatchError(startEditForm) { (params, context) =>
      formService.startEditForm(
        params,
        context.actor.userId
      )
    },
    authRouteCatchError(getFormLock) { (params, context) =>
      formLockService
        .getLock(
          params.formId,
          context.actor.userId,
          params.sessionID
        )
        .map(GetFormLockResponse.apply)
    },
    authRouteCatchError(renewFormLock) { (params, context) =>
      formLockService
        .renewLock(
          params.formId,
          context.actor.userId,
          params.sessionID
        )
        .map(RenewFormLockResponse.apply)
    },
    authRouteCatchError(releaseFormLock) { (params, context) =>
      formLockService
        .releaseLock(
          params.formId,
          context.actor.userId,
          params.sessionID
        )
        .map(_ => ())
    },
    authRouteCatchError(editForm) { (params, context) =>
      formService.editForm(
        params.formId,
        params.formName,
        params.tags,
        params.owners,
        context.actor.userId,
        params.parentFolderIdOpt,
        params.fundEngagementTypeOpt,
        params.numFundraiseOpt
      )
    },
    authRouteCatchError(duplicateForm) { (params, context) =>
      formService.duplicateForm(
        params.sourceFormVersionId,
        params.formName,
        params.tags,
        params.owners,
        context.actor.userId,
        params.sourceUrl,
        params.parentFolderIdOpt,
        params.fundEngagementTypeOpt,
        params.numFundraiseOpt
      )
    },
    authRouteCatchError(saveForm) { (params, context) =>
      formService.saveFormData(
        params.formId,
        formData = params.formData,
        versionMetadataOpt = params.metadataOpt,
        parentVersionId = params.parentVersionIdOpt,
        name = params.name,
        actor = context.actor.userId,
        sessionId = params.sessionId,
        formTypeOpt = params.formTypeOpt
      )
    },
    authRouteCatchError(saveFormMetadata) { (params, context) =>
      formService.saveFormMetadata(
        params.formVersionId,
        params.metadata,
        actor = context.actor.userId,
        sessionId = params.sessionId
      )
    },
    authRouteCatchError(editVersion) { (params, context) =>
      formService.editVersion(
        params.formId,
        params.formVersionIdOpt,
        params.name,
        params.note,
        context.actor.userId
      )
    },
    authRouteCatchError(getForm) { (params, context) =>
      formService.getForm(
        params.formId,
        params.versionIdOpt,
        context.actor.userId
      )
    },
    authRouteCatchError(getAllForms) { (_, context) =>
      formService.getAllForms(context.actor.userId)
    },
    authRouteCatchError(
      exportFormMatching,
      timeout = FiniteDuration(ExportFormMatchingTimeoutSeconds, TimeUnit.SECONDS)
    ) { (params, context) =>
      formMatchingService.exportFormMatching(context.actor.userId, params)
    },
    authRouteCatchError(getAllFormsAnalytics) { (_, context) =>
      formService.getAllFormsAnalytics(context.actor.userId)
    },
    authRouteCatchError(getAllVersions) { (params, context) =>
      formService.getAllVersions(params.formId, context.actor.userId)
    },
    authRouteCatchError(createFormVersion) { (params, context) =>
      formService.createFormVersion(
        params.formId,
        params.description,
        params.versionNote,
        context.actor.userId,
        params.sessionId
      )
    },
    authRouteCatchError(getFormActivities) { (params, context) =>
      formService.getFormActivities(params.formId, context.actor.userId)
    },
    authRouteCatchError(diffForm) { (params, context) =>
      formService.diffForm(
        params.formId,
        params.firstVersionOpt,
        params.secondVersionOpt,
        context.actor.userId
      )
    },
    authRouteCatchError(diffFormFile, timeout = defaultTimeout + pdfAnnotationServerlessTimeout) { (params, context) =>
      formService.diffFormFile(
        params.formId,
        params.firstVersionIdOpt,
        params.secondVersionIdOpt,
        context.actor.userId
      )
    },
    authRouteCatchError(generatePdf) { (params, context) =>
      formService.generatePdf(
        params.formId,
        params.formData,
        params.formStates,
        context.actor.userId,
        Some(context)
      )
    },
    authRouteCatchError(getFileInfoToCut) { (params, context) =>
      formService.getFileInfoToCut(
        params.fileId,
        params.formId,
        context.actor.userId
      )
    },
    authRouteCatchError(cutFile) { (params, context) =>
      formService.cutFile(
        params.fileId,
        params.formId,
        params.start,
        params.end,
        params.fileName,
        context.actor.userId
      )
    },
    authRouteCatchError(getFormFileMetadata) { (params, context) =>
      formService.getFormFileMetadata(params.fileIds, context.actor.userId)
    },
    authRouteCatchError(addFormStandardAliasMapping) { (params, context) =>
      formService.addFormStandardAliasMapping(params, context.actor.userId)
    },
    authRouteCatchError(removeFormStandardAliasMapping) { (params, context) =>
      formService.removeFormStandardAliasMapping(params, context.actor.userId)
    },
    authRouteCatchError(importFormStandardAliasMapping) { (params, context) =>
      formService.importFormStandardAliasMapping(params, context.actor.userId)
    },
    authRouteCatchError(getFormStandardAliasMapping) { (params, _) =>
      formService.getFormStandardAliasMapping(params.id, params.checkExistingFormAlias)
    },
    authRouteCatchError(getAllStandardAliases) { (_, _) =>
      formService.getAllStandardAliases
    },
    authRouteCatchError(exportAllPdfMappingsToCsv) { (params, context) =>
      formDataService.exportAllPdfMappingsToCsv(params, context.actor.userId)
    },
    authRouteCatchError(importSpreadsheetFile) { (params, ctx) =>
      formDataService.importSpreadsheetFile(params, ctx.actor.userId)
    },
    authRouteCatchError(importCsvFile) { (params, ctx) =>
      formDataService.importCsvFile(params, ctx.actor.userId)
    },
    authRouteCatchError(getDefaultImportTemplate) { (params, ctx) =>
      formDataService.generateDefaultImportTemplateFile(params, ctx.actor.userId)
    },
    authRouteCatchError(getNamingRefineTemplate) { (params, ctx) =>
      formDataService.generateNamingRefineTemplate(params, ctx.actor.userId)
    },
    authRouteCatchError(createFormFolder) { (params, ctx) =>
      formFolderService.createFormFolder(
        params.formFolderName,
        params.tags,
        ctx.actor.userId,
        parentFolderId = Some(params.parentFolderId)
      )
    },
    authRouteCatchError(updateFormFolder) { (params, ctx) =>
      formFolderService.updateFormFolderInfo(
        params.formFolderId,
        params.formFolderName,
        params.formFolderTags,
        params.parentFolderId,
        ctx.actor.userId
      )
    },
    authRouteCatchError(deleteFormFolder) { (params, ctx) =>
      formFolderService.deleteFormFolder(params.formFolderId, ctx.actor.userId)
    },
    authRouteCatchError(archiveForm) { (params, ctx) =>
      formService.archiveForm(params.formId, ctx.actor.userId)
    },
    authRouteCatchError(unarchiveForm) { (params, ctx) =>
      formService.unarchiveForm(params.formId, ctx.actor.userId)
    },
    authRouteCatchError(getFormFolderContent) { (params, ctx) =>
      formFolderService.getFormFolderContent(params.formFolderId, ctx.actor.userId)
    },
    authRouteCatchError(getAllFormFolderModels) { (_, ctx) =>
      formFolderService.getAllFormFolderModels(ctx.actor.userId)
    },
    authRouteCatchError(getFormFolder) { (params, ctx) =>
      formFolderService.getFormFolder(params.formFolderId, ctx.actor.userId)
    },
    authRouteCatchError(getComponentLibraryVersion) { (_, ctx) =>
      formService.getComponentLibraryVersion(ctx.actor.userId)
    },
    authRouteCatchError(setComponentLibraryVersion) { (params, ctx) =>
      formService.setComponentLibraryVersion(params, ctx.actor.userId)
    },
    authRouteCatchError(setFormVersionTag) { (params, ctx) =>
      formService.setFormVersionTag(params, ctx.actor.userId)
    },
    authRouteCatchError(getAllLibraryComponents) { (_, ctx) =>
      formService.getAllLibraryComponents(ctx.actor.userId)
    },
    authRouteCatchError(getAllInvestorAccessAsaValues) { (_, ctx) =>
      formService.getAllInvestorAccessAsaValues(ctx.actor.userId)
    },
    authRouteCatchError(copyFormFiles) { (params, ctx) =>
      formService.copyFiles(params, ctx.actor.userId)
    },
    authRouteCatchError(exportFormAsaMappingTemplate) { (params, ctx) =>
      formService.exportFormAsaMappingTemplate(params, ctx.actor.userId)
    },
    authRouteCatchError(processFormAsaMappingTemplate) { (params, ctx) =>
      formService.processFormAsaMappingTemplate(params, ctx.actor.userId)
    },
    authRouteCatchError(editFormAssociatedLinks) { (params, ctx) =>
      formService.editFormAssociatedLinks(
        params.formId,
        params.newAssociatedLinks,
        ctx.actor.userId
      )
    },
    authRouteCatchError(getFormToolConfigs) { (params, ctx) =>
      formToolConfigService.getFormToolConfigs(params, ctx.actor.userId)
    },
    authRouteCatchError(addFormToolConfig) { (params, ctx) =>
      formToolConfigService.addFormToolConfig(params, ctx.actor.userId)
    },
    authRouteCatchError(updateFormToolConfig) { (params, ctx) =>
      formToolConfigService.updateFormToolConfig(params, ctx.actor.userId)
    },
    // Form integration
    authRouteCatchError(getFormVersionIntegrationConfig) { (params, ctx) =>
      formIntegrationService.getFormVersionIntegrationConfig(params, ctx.actor.userId)
    },
    authRouteCatchError(getAllVersionsIntegrationConfig) { (params, ctx) =>
      formIntegrationService.getAllFormVersionIntegrationConfigs(params, ctx.actor.userId)
    },
    authRouteCatchError(updateFormVersionIntegrationConfig) { (params, ctx) =>
      formIntegrationService.updateFormVersionIntegrationConfig(params, ctx.actor.userId)
    },
    authRouteCatchError(generateSignatures) { (params, ctx) =>
      formService.generateSignatures(
        params.resourceId,
        params.fileId,
        params.documentSignature,
        params.signatureModel,
        ctx.actor.userId
      )
    },
    authRouteCatchError(getFilePageSizes) { (params, ctx) =>
      formService.getFilePageSizes(
        params.resourceId,
        params.fileId,
        ctx.actor.userId
      )
    },
    authRouteCatchError(importFromAnnotationDocument) { (params, ctx) =>
      formService.importFromAnnotationDocument(
        params.formId,
        params.importData,
        ctx
      )
    },
    authRouteCatchError(updateBlueprintRef) { (params, ctx) =>
      formService.updateBlueprintRef(params, ctx)
    },
    authRouteCatchError(removeBlueprintRef) { (params, ctx) =>
      formService.removeBlueprintRef(params, ctx)
    },
    authRouteCatchError(diffFormBlueprint) { (params, context) =>
      formService.diffFormBlueprint(
        params.formId,
        params.firstVersionIdOpt,
        params.secondVersionIdOpt,
        context.actor.userId
      )
    },
    authRouteCatchError(getFormCueModule) { (params, context) =>
      dataLayerService.getFormCueModule(
        params.formId,
        context.actor.userId
      )
    },
    authRouteCatchError(generateFormModule) { (params, context) =>
      dataLayerService
        .generateFormModule(
          params.formId,
          context.actor.userId
        )
        .as(EmptyResponse())
    },
    authRouteCatchError(extractDataLayerDefaultValues) { (params, context) =>
      dataLayerService.extractDataLayerDefaultValues(params.input, context.actor.userId)
    },
    authRouteCatchError(verifyLinkedCueVersion) { (params, context) =>
      dataLayerService
        .verifyLinkedCueVersion(params.formId, params.linkedCueVersionId, context.actor.userId)
        .as(EmptyResponse())
    },
    authRouteCatchError(getFormDataUserTemp) { (params, ctx) =>
      formDataUserTempService.getUserTempFormData(params, ctx.actor.userId)
    }
  )

}
