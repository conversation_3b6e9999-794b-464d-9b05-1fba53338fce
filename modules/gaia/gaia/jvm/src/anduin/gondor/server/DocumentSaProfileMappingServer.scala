// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.gondor.server

import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter

import anduin.sa.endpoints.{
  GetDocumentSaProfileMappingResponse,
  QuerySaProfileMappingByDocumentVersionIdResponse,
  SaProfileFullInfo,
  SingleSaProfileMappingResponse
}
import anduin.sa.endpoints.DocumentSaProfileMappingEndpoint.*
import anduin.ontology.service.DocumentSaProfileMappingService
import anduin.tapir.server.AuthenticatedEndpointServer
import anduin.tapir.server.EndpointServer.TapirServerService
import com.anduin.stargazer.service.authorization.AuthorizationService
import anduin.sa.service.SaProfileService
import anduin.annotation.service.AnnotationDocumentService.KeySpace as AnnotationDocumentServiceKeySpace

final class DocumentSaProfileMappingServer(
  protected val authorizationService: AuthorizationService,
  documentSaProfileMappingService: DocumentSaProfileMappingService,
  saProfileService: SaProfileService,
  override protected val interpreter: ArmeriaZioServerInterpreter[Any]
) extends AuthenticatedEndpointServer {

  private given AnnotationDocumentServiceKeySpace = AnnotationDocumentServiceKeySpace.Production

  val services: List[TapirServerService] = List(
    authRouteCatchError(addDocumentSaProfileMapping) { (params, context) =>
      documentSaProfileMappingService.addDocumentSaProfileMapping(
        actorId = context.actor.userId,
        saProfileId = params.saProfileId,
        documentVersionId = params.documentVersionId,
        mappingToAdd = params.mappingToAdd,
        checkForValidFieldName = params.checkForValidFieldName
      )
    },
    authRouteCatchError(importDocumentSaProfileMapping) { (params, context) =>
      documentSaProfileMappingService.importDocumentSaProfileMapping(
        actorId = context.actor.userId,
        params = params
      )
    },
    authRouteCatchError(transferSingleSaProfileMapping) { (params, context) =>
      documentSaProfileMappingService.transferSingleSaProfileMapping(
        actorId = context.actor.userId,
        saProfileId = params.saProfileId,
        srcDocumentVersionId = params.srcDocumentVersionId,
        targetDocumentVersionId = params.targetDocumentVersionId
      )
    },
    authRouteCatchError(transferAllSaProfileMappings) { (params, context) =>
      documentSaProfileMappingService.transferAllSaProfileMappings(
        actorId = context.actor.userId,
        srcDocumentVersionId = params.srcDocumentVersionId,
        targetDocumentVersionId = params.targetDocumentVersionId
      )
    },
    authRouteCatchError(removeDocumentSaProfileMapping) { (params, context) =>
      documentSaProfileMappingService.removeDocumentSaProfileMapping(
        actorId = context.actor.userId,
        saProfileId = params.saProfileId,
        documentVersionId = params.documentVersionId,
        mappedFieldsToRemove = params.mappedFieldsToRemove
      )
    },
    authRouteCatchError(deleteDocumentSaProfileMappingPermanently) { (params, context) =>
      documentSaProfileMappingService.deleteDocumentSaProfileMappingPermanently(
        actorId = context.actor.userId,
        saProfileId = params.saProfileId,
        documentVersionId = params.documentVersionId
      )
    },
    authRouteCatchError(getDocumentSaProfileMapping) { (params, context) =>
      documentSaProfileMappingService
        .getDocumentSaProfileMapping(
          actorId = context.actor.userId,
          saProfileId = params.saProfileId,
          documentVersionId = params.documentVersionId,
          ignorePermissionCheck = false
        )
        .map(saProfileMsgOpt =>
          GetDocumentSaProfileMappingResponse(
            params.saProfileId,
            params.documentVersionId,
            saProfileMsgOpt.map(_.fieldToSaMap)
          )
        )
    },
    authRouteCatchError(querySaProfileMappingByDocumentVersionId) { (params, context) =>
      for {
        saProfileMappingMsgList <- documentSaProfileMappingService.querySaProfileMappingByDocumentVersionId(
          actorId = context.actor.userId,
          documentVersionId = params.documentVersionId,
          ignorePermissionCheck = false
        )
        saProfileMsgList <- saProfileService.getSaProfileByIds(
          actorId = context.actor.userId,
          ids = saProfileMappingMsgList.map(_.saProfileId)
        )
      } yield {
        val fullFieldMappingMap = saProfileMappingMsgList.map(msg => msg.saProfileId -> msg).toMap

        QuerySaProfileMappingByDocumentVersionIdResponse(
          params.documentVersionId,
          saProfileMsgList.map { saProfileMsg =>
            val mappingMsgOpt = fullFieldMappingMap.get(saProfileMsg.id)
            SingleSaProfileMappingResponse(
              saProfileInfo = SaProfileFullInfo.fromSaProfileMsg(saProfileMsg),
              fieldToSaMap = mappingMsgOpt.map(_.fieldToSaMap).getOrElse(Map.empty),
              lastUpdatedByOpt = mappingMsgOpt.flatMap(_.lastUpdatedByOpt),
              lastUpdatedAtOpt = mappingMsgOpt.flatMap(_.lastUpdatedAtOpt)
            )
          }
        )
      }
    }
  )

}
