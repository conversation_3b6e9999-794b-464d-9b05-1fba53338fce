// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.sa.service

import zio.{Task, ZIO}

import anduin.cue.service.CueModuleService
import anduin.cue.service.CueModuleService.CueModuleServiceKeySpace
import anduin.digitization.common.DigitizationServiceKeyspace
import anduin.digitization.service.DigitizationFileService
import anduin.id.digitization.DigitizationFolderId
import anduin.id.sa.SaDataTemplateId
import anduin.model.common.user.UserId
import anduin.sa.endpoints.{CreateSaDataTemplateCueModuleResponse, GetSaDataTemplateCueModuleResponse}
import anduin.sa.service.CommonServiceUtils.PortalPermissionLevel
import anduin.serverless.common.ServerlessModels.formModule.CueGeneratorInput.GenerateSaTemplateModule

final case class SaCueService(
  saDataTemplateService: SaDataTemplateService,
  digitizationFileService: DigitizationFileService,
  cueModuleService: CueModuleService
) {

  def createSaDataTemplateCueModule(
    saDataTemplateId: SaDataTemplateId,
    actor: UserId,
    ignorePermissionCheck: Boolean = false
  )(
    using DigitizationServiceKeyspace.KeySpace
  ): Task[CreateSaDataTemplateCueModuleResponse] = {
    val task = for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is creating cue module for SA data template ${saDataTemplateId.idString}"
      )
      template <- saDataTemplateService.getSaDataTemplateUnsafe(saDataTemplateId)
      resp <- digitizationFileService.createCueModuleFile(
        parentId = saDataTemplateId,
        name = s"SA data template: ${template.name}",
        actor = actor,
        parentFolderIdOpt = Some(DigitizationFolderId(DigitizationFolderId.RootFolderId))
      )
    } yield CreateSaDataTemplateCueModuleResponse(resp.cueModule)
    saDataTemplateService.checkPermission(
      task,
      actor,
      "createSaDataTemplateCueModule",
      ignorePermissionCheck = ignorePermissionCheck
    )
  }

  def getSaDataTemplateCueModule(
    saDataTemplateId: SaDataTemplateId,
    actor: UserId,
    ignorePermissionCheck: Boolean = false
  )(
    using CueModuleServiceKeySpace
  ): Task[GetSaDataTemplateCueModuleResponse] = {
    val task = for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is getting cue module for SA data template ${saDataTemplateId.idString}")
      cueModuleOpt <- cueModuleService.getCueModuleOptUnsafe(saDataTemplateId.cueModuleId)
    } yield GetSaDataTemplateCueModuleResponse(cueModuleOpt)
    saDataTemplateService.checkPermission(
      task,
      actor,
      "getSaDataTemplateCueModule",
      permissionLevel = PortalPermissionLevel.Read,
      ignorePermissionCheck = ignorePermissionCheck
    )
  }

  def generateSaDataTemplateCueModule(
    saDataTemplateId: SaDataTemplateId,
    actor: UserId,
    ignorePermissionCheck: Boolean = false
  )(
    using CueModuleServiceKeySpace
  ): Task[Unit] = {
    val task = for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is generating cue module for SA data template ${saDataTemplateId.idString}"
      )
      _ <- cueModuleService.generateCueModule(
        cueModuleId = saDataTemplateId.cueModuleId,
        cueGeneratorInput = GenerateSaTemplateModule(),
        actor = actor
      )
    } yield ()
    saDataTemplateService.checkPermission(
      task,
      actor,
      "generateSaDataTemplateCueModule",
      ignorePermissionCheck = ignorePermissionCheck
    )
  }

}
