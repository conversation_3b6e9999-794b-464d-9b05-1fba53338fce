// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.cue.utils

import anduin.id.cue.CueModuleId
import anduin.serverless.common.ServerlessModels.formModule.CueGeneratorInput.GenerateTableMappingModule.TableModule

object CueGeneratorUtils {

  def toDataExtractTableModule(
    cueModuleId: CueModuleId,
    versionTagOpt: Option[String]
  )(
    using keyspace: CueModuleZotUtils.CueModuleZotKeySpace
  ): TableModule = {
    TableModule(
      moduleInfo = CueModuleZotUtils.toZotModule(cueModuleId, versionTagOpt),
      tableSchemaPackage = ":data_extract_table_schema",
      valueSchemaPackage = ":data_extract_table_value_schema",
      valuesTransformerPackage = ":data_extract_table_values_transformer",
      valuesWithValidationTransformerPackage = ":data_extract_table_values_with_validation_transformer"
    )
  }

  def toSaTemplateTableModule(
    cueModuleId: CueModuleId,
    versionTagOpt: Option[String]
  )(
    using keyspace: CueModuleZotUtils.CueModuleZotKeySpace
  ): TableModule = {
    TableModule(
      moduleInfo = CueModuleZotUtils.toZotModule(cueModuleId, versionTagOpt),
      tableSchemaPackage = ":sa_template_table_schema",
      valueSchemaPackage = ":sa_template_table_value_schema",
      valuesTransformerPackage = ":sa_template_table_values_transformer",
      valuesWithValidationTransformerPackage = ":sa_template_table_values_with_validation_transformer"
    )
  }

}
