// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.cue.service

import io.circe.{Decoder, parser}
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.cue.endpoint.*
import anduin.cue.model.CueModuleSharedModels.{CueFileChange, CueModule, CueModuleCheckUpToDateData, CueModuleVersion}
import anduin.cue.model.commands.CueCommands
import anduin.cue.model.commands.CueCommands.{Command, CommandsTrait, CueCommand}
import anduin.cue.model.{CueModuleMessage, CueModuleModelConverter}
import anduin.cue.service.CueModuleService.CueModuleServiceKeySpace
import anduin.cue.storage.{CueModuleModelStoreOperations, CueModuleVersionStoreOperations}
import anduin.cue.utils.CueModuleZotUtils
import anduin.cue.utils.CueModuleZotUtils.CueModuleZotKeySpace
import anduin.cue.version.CueModuleVersionMessage
import anduin.dms.upload.{FileUploadHandler, FileUploadService}
import anduin.fdb.record.FDBOperations.FDBKeySpaceEnum
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.forms.endpoint.GetLockResult
import anduin.forms.service.AssetLockService
import anduin.forms.storage.AssetLockStoreOperations
import anduin.id.cue.{CueModuleId, CueModuleVersionId, IdWithCueModule}
import anduin.id.role.portal.PortalSectionId
import anduin.model.common.user.UserId
import anduin.model.id.CueModuleVersionIdFactory
import anduin.portaluser.PortalUserService
import anduin.serverless.common.ServerlessModels.formModule.{CueGeneratorInput, DataFile, DeleteFile, OverrideFile}
import anduin.serverless.functions.FormModuleServerless
import anduin.serverless.utils.ServerlessUtils
import anduin.service.GeneralServiceException
import anduin.storageservice.s3.S3Service
import anduin.storageservice.zot.ZotService
import anduin.storageservice.zot.models.ModuleInfo
import anduin.util.FilenameUtils
import com.anduin.stargazer.service.file.FileTypeFilter
import com.anduin.stargazer.service.utils.ZIOUtils
import anduin.fdb.record.model.RecordIO

final case class CueModuleService(
  portalUserService: PortalUserService,
  zotService: ZotService,
  formModuleServerless: FormModuleServerless,
  fileUploadService: FileUploadService,
  s3Service: S3Service,
  assetLockService: AssetLockService,
  userProfileService: UserProfileService
) {

  val uploadCueFilesHandler: FileUploadHandler = FileUploadHandler[UploadCueFilesParams, UploadCueFilesResponse](
    createTask = createInput =>
      for {
        _ <- ZIO.logInfo(
          s"User ${createInput.actor} is creating cue file upload for ${createInput.files.size} files"
        )
        _ <- portalUserService.validateWritePermission(createInput.actor, PortalSectionId.Form)
        _ <- fileUploadService.createDirectUploadUnsafe(
          batchUploadId = createInput.batchUploadId,
          apiName = createInput.params.apiName,
          paramsOpt = Some(createInput.params.toJsonString),
          files = createInput.files,
          emptyFolders = Seq.empty,
          actor = createInput.actor,
          allowEmptyFileContent = true
        )
      } yield createInput.batchUploadId,
    completeTask = completeInput =>
      fileUploadService
        .completeDirectUpload(
          completeInput.batchUploadId,
          completeInput.actor,
          shouldDeleteObjects = false
        )
        .as(UploadCueFilesResponse(completeInput.files.map { file => file.storageId -> file.bucket })),
    fileTypeFilter = FileTypeFilter.Whitelist(Set(FilenameUtils.Extension.Cue, FilenameUtils.Extension.Json))
  )

  def createCueModule(
    parentId: IdWithCueModule,
    name: String,
    actor: UserId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[CueModule] = {
    given FDBKeySpaceEnum = keySpace.fdbKeySpace
    given CueModuleZotKeySpace = keySpace.cueModuleZotKeySpace
    val cueModuleId = parentId.cueModuleId
    val zotModule = CueModuleZotUtils.toZotModule(cueModuleId, None)
    val zotDraft = CueModuleZotUtils.toZotModule(cueModuleId, Some(ModuleInfo.LatestTag))
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is creating new cue module from parent resource: ${parentId.idString}")
      _ <- portalUserService.validateWritePermission(userId = actor, sectionId = PortalSectionId.Form)
      _ <- zotService
        .getModuleVersion(zotDraft)
        .flatMap { _ =>
          ZIO.logInfo(s"Zot module ${zotModule.canonicalName} exists. Skip creating zot module.")
        }
        .catchAllCause { _ =>
          formModuleServerless.createModule(module = CueModuleZotUtils.toZotModule(cueModuleId, None)).mapError {
            error => GeneralServiceException(error.getMessage)
          }
        }
      cueModuleDraftId <- ZIO.attempt(CueModuleVersionIdFactory.unsafeRandomId(cueModuleId))
      cueModuleMessage <- FDBRecordDatabase.transact(
        FDBOperations[
          (CueModuleModelStoreOperations, CueModuleVersionStoreOperations, AssetLockStoreOperations)
        ].getProviderCached
      ) { case (modelOps, versionOps, lockOps) =>
        for {
          cueModule <- modelOps.create(
            model = CueModuleMessage(id = cueModuleId, name = name, draftVersionId = cueModuleDraftId),
            actor = actor
          )
          _ <- versionOps.create(
            version = CueModuleVersionMessage(
              id = cueModuleDraftId,
              name = CueModuleVersion.draftName,
              versionIndex = CueModuleVersion.draftVersionIndex,
              zotVersionTag = ModuleInfo.LatestTag
            ),
            actor = actor
          )
          _ <- lockOps.create(cueModuleId)
        } yield cueModule
      }
    } yield CueModuleModelConverter.cueModuleProtoToModel(cueModuleMessage)
  }

  def getCueModule(
    cueModuleId: CueModuleId,
    versionIdOpt: Option[CueModuleVersionId],
    actor: UserId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[GetCueModuleResponse] = {
    given FDBKeySpaceEnum = keySpace.fdbKeySpace
    given CueModuleZotKeySpace = keySpace.cueModuleZotKeySpace
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is getting cue module ${cueModuleId.idString}")
      _ <- portalUserService.validateReadPermission(userId = actor, sectionId = PortalSectionId.Form)
      _ <- ZIO.foreachDiscard(versionIdOpt) { versionId =>
        ZIOUtils.validate(versionId.parent == cueModuleId)(
          GeneralServiceException(s"Version ${versionId.idString} does not belong to cue module ${cueModuleId.idString}")
        )
      }
      (cueModuleMessage, versionMessage) <- FDBRecordDatabase.read(
        FDBOperations[(CueModuleModelStoreOperations, CueModuleVersionStoreOperations)].getProviderCached
      ) { case modelOps -> versionOps =>
        for {
          model <- modelOps.get(cueModuleId)
          versionId = versionIdOpt.getOrElse(model.draftVersionId)
          version <- versionOps.get(versionId)
        } yield model -> version
      }
      zotModuleData <- zotService.getModuleVersion(
        CueModuleZotUtils.toZotModule(cueModuleId, Some(versionMessage.zotVersionTag))
      )
      checkData <- getCheckUpToDateData(cueModuleId)
      allVersions <- getAllVersionsUnsafe(cueModuleId)
      userIds = (Seq(
        cueModuleMessage.createdBy,
        cueModuleMessage.lastUpdatedBy,
        Some(actor)
      ).flatten ++ allVersions
        .flatMap { version => Seq(version.createdBy, version.lastUpdatedBy).flatten }).toSet
      userInfoMap <- userProfileService.batchGetUserInfos(userIds, Some(actor))
    } yield GetCueModuleResponse(
      cueModule = CueModuleModelConverter.cueModuleProtoToModel(cueModuleMessage),
      version = CueModuleModelConverter.cueModuleVersionProtoToModel(versionMessage),
      cueModuleData = CueModuleZotUtils.toCueModuleData(zotModuleData),
      checkData = checkData,
      allVersions = allVersions,
      userInfoMap = userInfoMap
    )
  }

  def getZotModule(
    cueModuleId: CueModuleId,
    versionIdOpt: Option[CueModuleVersionId],
    actor: UserId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[ModuleInfo] = {
    given FDBKeySpaceEnum = keySpace.fdbKeySpace
    given CueModuleZotKeySpace = keySpace.cueModuleZotKeySpace
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is getting zot module ${cueModuleId.idString}")
      _ <- portalUserService.validateReadPermission(userId = actor, sectionId = PortalSectionId.Form)
      _ <- ZIO.foreachDiscard(versionIdOpt) { versionId =>
        ZIOUtils.validate(versionId.parent == cueModuleId)(
          GeneralServiceException(s"Version ${versionId.idString} does not belong to cue module ${cueModuleId.idString}")
        )
      }
      versionMessage <- FDBRecordDatabase.read(
        FDBOperations[(CueModuleModelStoreOperations, CueModuleVersionStoreOperations)].getProviderCached
      ) { case modelOps -> versionOps =>
        for {
          model <- modelOps.get(cueModuleId)
          versionId = versionIdOpt.getOrElse(model.draftVersionId)
          version <- versionOps.get(versionId)
        } yield version
      }
    } yield CueModuleZotUtils.toZotModule(cueModuleId, Some(versionMessage.zotVersionTag))
  }

  def getAllCueModuleVersions(
    cueModuleId: CueModuleId,
    actor: UserId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[GetAllCueModuleVersionsResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is getting all versions for cue module ${cueModuleId.idString}")
      _ <- portalUserService.validateReadPermission(userId = actor, sectionId = PortalSectionId.Form)
      allVersions <- getAllVersionsUnsafe(cueModuleId)
    } yield GetAllCueModuleVersionsResponse(allVersions)
  }

  def getCueModuleOptUnsafe(
    cueModuleId: CueModuleId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[Option[CueModule]] = {
    given FDBKeySpaceEnum = keySpace.fdbKeySpace
    FDBRecordDatabase
      .read(CueModuleModelStoreOperations.getProviderCached)(_.getOpt(cueModuleId))
      .map(_.map(CueModuleModelConverter.cueModuleProtoToModel))
  }

  def getCueModuleVersionUnsafe(
    cueModuleVersionId: CueModuleVersionId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[CueModuleVersion] = {
    given FDBKeySpaceEnum = keySpace.fdbKeySpace
    FDBRecordDatabase
      .read(CueModuleVersionStoreOperations.getProviderCached)(_.get(cueModuleVersionId))
      .map(CueModuleModelConverter.cueModuleVersionProtoToModel)
  }

  def getCueFilePreSignedLink(
    cueModuleId: CueModuleId,
    zotVersionTag: String,
    ref: String,
    actor: UserId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[GetCueFilePreSignedLinkResponse] = {
    given CueModuleZotKeySpace = keySpace.cueModuleZotKeySpace
    for {
      _ <- ZIO.logInfo(
        s"User ${actor.idString} is getting pre-signed link for cue module ${cueModuleId.idString}, " +
          s"zot version tag $zotVersionTag, ref $ref"
      )
      _ <- portalUserService.validateReadPermission(userId = actor, sectionId = PortalSectionId.Form)
      preSignedLink <- zotService.getPreSignedFile(
        module = CueModuleZotUtils.toZotModule(cueModuleId, Some(zotVersionTag)),
        ref = ref
      )
    } yield GetCueFilePreSignedLinkResponse(preSignedLink)
  }

  def editCueModule(
    cueModuleId: CueModuleId,
    newName: String,
    actor: UserId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[EditCueModuleResponse] = {
    given FDBKeySpaceEnum = keySpace.fdbKeySpace
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is editing cue module ${cueModuleId.idString}")
      _ <- portalUserService.validateWritePermission(userId = actor, sectionId = PortalSectionId.Form)
      updatedCueModuleMessage <- FDBRecordDatabase.transact(CueModuleModelStoreOperations.getProviderCached)(
        _.update(cueModuleId, actor)(_.copy(name = newName))
      )
    } yield EditCueModuleResponse(CueModuleModelConverter.cueModuleProtoToModel(updatedCueModuleMessage))
  }

  def saveDraft(
    cueModuleId: CueModuleId,
    fileChanges: Seq[CueFileChange],
    checkData: CueModuleCheckUpToDateData,
    actor: UserId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[SaveDraftResponse] = {
    given FDBKeySpaceEnum = keySpace.fdbKeySpace
    given CueModuleZotKeySpace = keySpace.cueModuleZotKeySpace
    val zotModule = CueModuleZotUtils.toZotModule(cueModuleId, Some(ModuleInfo.LatestTag))
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is saving draft for cue module ${cueModuleId.idString}")
      _ <- portalUserService.validateWritePermission(userId = actor, sectionId = PortalSectionId.Form)
      _ <- validateLock(cueModuleId, checkData, actor)
      _ <- ZIO.foreachDiscard(fileChanges) { fileChange =>
        ZIOUtils.validate(FilenameUtils.isCueOrJsonFile(fileChange.fileName))(
          GeneralServiceException(s"File ${fileChange.fileName} is not a cue or json file")
        )
      }
      ops = fileChanges.map {
        case CueFileChange.AddOrReplace(fileName, storageId, bucket) =>
          OverrideFile(
            path = fileName,
            source = DataFile(storageId = storageId.id, s3Access = ServerlessUtils.getS3Access(bucket))
          )
        case CueFileChange.Delete(fileName) => DeleteFile(fileName)
      }
      _ <- formModuleServerless.updateModuleDraft(module = zotModule, ops = ops.toList).mapError { error =>
        GeneralServiceException(error.getMessage)
      }
      (cueModuleMessage, draftMessage) <- FDBRecordDatabase.transact(
        FDBOperations[(CueModuleModelStoreOperations, CueModuleVersionStoreOperations)].getProviderCached
      ) { case modelOps -> versionOps =>
        for {
          model <- modelOps.update(cueModuleId, actor)(identity)
          draft <- versionOps.update(model.draftVersionId, actor)(identity)
        } yield model -> draft
      }
      zotModuleData <- zotService.getModuleVersion(zotModule)
      tempStorageIds = fileChanges.collect {
        case CueFileChange.AddOrReplace(_, storageId, s3Service.s3Config.tempUploadBucket) => storageId.id
      }
      _ <- s3Service
        .deleteObjects(s3Service.s3Config.tempUploadBucket, tempStorageIds)
        .catchAllCause { error =>
          ZIO.logWarningCause(
            s"Cannot delete temp upload objects, storageIds = $tempStorageIds",
            error
          )
        }
      checkData <- getCheckUpToDateData(cueModuleId)
    } yield SaveDraftResponse(
      cueModule = CueModuleModelConverter.cueModuleProtoToModel(cueModuleMessage),
      draft = CueModuleModelConverter.cueModuleVersionProtoToModel(draftMessage),
      cueModuleData = CueModuleZotUtils.toCueModuleData(zotModuleData),
      checkData = checkData
    )
  }

  def startEditing(
    cueModuleId: CueModuleId,
    checkData: CueModuleCheckUpToDateData,
    actor: UserId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[StartEditingResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is starting editing cue module ${cueModuleId.idString}")
      _ <- portalUserService.validateWritePermission(userId = actor, sectionId = PortalSectionId.Form)
      getLockResult <- getLock(cueModuleId, checkData, actor)
    } yield StartEditingResponse(getLockResult)
  }

  def renewLock(cueModuleId: CueModuleId, actor: UserId): Task[RenewLockResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is renewing lock for cue module ${cueModuleId.idString}")
      _ <- portalUserService.validateWritePermission(userId = actor, sectionId = PortalSectionId.Form)
      renewLockResult <- assetLockService.renewLock(cueModuleId, actor)
    } yield RenewLockResponse(renewLockResult)
  }

  def finishEditing(cueModuleId: CueModuleId, actor: UserId): Task[FinishEditingResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is finishing editing cue module ${cueModuleId.idString}")
      _ <- portalUserService.validateWritePermission(userId = actor, sectionId = PortalSectionId.Form)
      releaseLockResult <- assetLockService.releaseLock(cueModuleId, actor)
    } yield FinishEditingResponse(releaseLockResult)
  }

  def saveAsNewVersion(
    cueModuleId: CueModuleId,
    name: String,
    description: String,
    checkData: CueModuleCheckUpToDateData,
    actor: UserId
  )(
    using CueModuleServiceKeySpace
  ): Task[SaveAsNewVersionResponse] = {
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is saving new version for cue module ${cueModuleId.idString}")
      _ <- portalUserService.validateWritePermission(userId = actor, sectionId = PortalSectionId.Form)
      _ <- validateLock(cueModuleId, checkData, actor)
      (cueModule, draft, newVersion) <- commitNewVersion(cueModuleId, name, description, actor)
      latestCheckData <- getCheckUpToDateData(cueModuleId)
    } yield SaveAsNewVersionResponse(cueModule, draft, newVersion, latestCheckData)
  }

  private def commitNewVersion(
    cueModuleId: CueModuleId,
    name: String,
    description: String,
    actor: UserId
  )(
    using keySpace: CueModuleServiceKeySpace
  ) = {
    given FDBKeySpaceEnum = keySpace.fdbKeySpace
    given CueModuleZotKeySpace = keySpace.cueModuleZotKeySpace
    for {
      newVersionId <- ZIO.attempt(CueModuleVersionIdFactory.unsafeRandomId(cueModuleId))
      (cueModuleMessage, draftMessage) <- FDBRecordDatabase.read(
        FDBOperations[(CueModuleModelStoreOperations, CueModuleVersionStoreOperations)].getProviderCached
      ) { case modelOps -> versionOps =>
        for {
          model <- modelOps.get(cueModuleId)
          draft <- versionOps.get(model.draftVersionId)
        } yield model -> draft
      }
      newVersionIndex = cueModuleMessage.versionCount
      allZotVersions <- zotService.listModuleVersions(CueModuleZotUtils.toZotModule(cueModuleId, None))
      newZotVersionTag = CueModuleZotUtils.getNewZotVersionTag(allZotVersions)
      _ <- formModuleServerless
        .modulePublish(CueModuleZotUtils.toZotModule(cueModuleId, Some(newZotVersionTag)))
        .mapError { error => GeneralServiceException(error.getMessage) }
      (updatedCueModuleMessage, updatedDraftMessage, newVersionMessage) <- FDBRecordDatabase.transact(
        FDBOperations[(CueModuleModelStoreOperations, CueModuleVersionStoreOperations)].getProviderCached
      ) { case modelOps -> versionOps =>
        for {
          newVersion <- versionOps.create(
            CueModuleVersionMessage(
              id = newVersionId,
              name = name,
              description = description,
              versionIndex = newVersionIndex,
              parentVersionIdOpt = draftMessage.parentVersionIdOpt,
              zotVersionTag = newZotVersionTag
            ),
            actor
          )
          updatedDraft <- versionOps.update(draftMessage.id, actor)(_.copy(parentVersionIdOpt = Some(newVersionId)))
          updatedModel <- modelOps.update(cueModuleId, actor) { model =>
            model.copy(versionCount = model.versionCount + 1)
          }
        } yield (updatedModel, updatedDraft, newVersion)
      }
    } yield (
      model = CueModuleModelConverter.cueModuleProtoToModel(updatedCueModuleMessage),
      draft = CueModuleModelConverter.cueModuleVersionProtoToModel(updatedDraftMessage),
      newVersion = CueModuleModelConverter.cueModuleVersionProtoToModel(newVersionMessage)
    )
  }

  def restoreVersion(
    versionId: CueModuleVersionId,
    checkData: CueModuleCheckUpToDateData,
    actor: UserId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[RestoreVersionResponse] = {
    given FDBKeySpaceEnum = keySpace.fdbKeySpace
    given CueModuleZotKeySpace = keySpace.cueModuleZotKeySpace
    val cueModuleId = versionId.parent
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is restoring cue module version ${versionId.idString}")
      _ <- portalUserService.validateWritePermission(userId = actor, sectionId = PortalSectionId.Form)
      _ <- validateLock(cueModuleId, checkData, actor)
      (cueModuleMessage, versionMessage) <- FDBRecordDatabase.read(
        FDBOperations[(CueModuleModelStoreOperations, CueModuleVersionStoreOperations)].getProviderCached
      ) { case modelOps -> versionOps =>
        for {
          model <- modelOps.get(cueModuleId)
          version <- versionOps.get(versionId)
        } yield model -> version
      }
      _ <- ZIOUtils.validate(versionMessage.versionIndex != CueModuleVersion.draftVersionIndex)(
        GeneralServiceException("Cannot restore draft version")
      )
      _ <- formModuleServerless
        .moduleCheckout(
          CueModuleZotUtils.toZotModule(cueModuleId = cueModuleId, versionTagOpt = Some(versionMessage.zotVersionTag))
        )
        .mapError { error => GeneralServiceException(error.getMessage) }
      (updatedCueModuleMessage, updatedDraftMessage) <- FDBRecordDatabase.transact(
        FDBOperations[(CueModuleModelStoreOperations, CueModuleVersionStoreOperations)].getProviderCached
      ) { case modelOps -> versionOps =>
        for {
          updatedDraft <- versionOps.update(cueModuleMessage.draftVersionId, actor)(
            _.copy(parentVersionIdOpt = Some(versionId))
          )
          updatedModel <- modelOps.update(cueModuleId, actor)(identity)
        } yield updatedModel -> updatedDraft
      }
      zotModuleData <- zotService.getModuleVersion(
        CueModuleZotUtils.toZotModule(cueModuleId = cueModuleId, versionTagOpt = Some(ModuleInfo.LatestTag))
      )
      latestCheckData <- getCheckUpToDateData(cueModuleId)
    } yield RestoreVersionResponse(
      cueModule = CueModuleModelConverter.cueModuleProtoToModel(updatedCueModuleMessage),
      draft = CueModuleModelConverter.cueModuleVersionProtoToModel(updatedDraftMessage),
      cueModuleData = CueModuleZotUtils.toCueModuleData(zotModuleData),
      checkData = latestCheckData
    )
  }

  def getAllVersionsUnsafe(
    cueModuleId: CueModuleId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[Seq[CueModuleVersion]] = {
    given FDBKeySpaceEnum = keySpace.fdbKeySpace
    FDBRecordDatabase
      .read(CueModuleVersionStoreOperations.getProviderCached)(
        _.getAllByCueModuleId(cueModuleId)
      )
      .map(_.map(CueModuleModelConverter.cueModuleVersionProtoToModel))
  }

  def getLatestVersionUnsafe(
    cueModuleId: CueModuleId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[Option[CueModuleVersion]] = {
    getAllVersionsUnsafe(cueModuleId).map(_.maxByOption(_.versionIndex))
  }

  def editVersion(
    versionId: CueModuleVersionId,
    newName: String,
    newDescription: String,
    actor: UserId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[EditVersionResponse] = {
    given FDBKeySpaceEnum = keySpace.fdbKeySpace
    val cueModuleId = versionId.parent
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is editing cue module version ${versionId.idString}")
      _ <- portalUserService.validateWritePermission(userId = actor, sectionId = PortalSectionId.Form)
      version <- FDBRecordDatabase.transact(CueModuleVersionStoreOperations.getProviderCached)(_.get(versionId))
      _ <- ZIOUtils.validate(version.versionIndex != CueModuleVersion.draftVersionIndex)(
        GeneralServiceException("Cannot edit draft version")
      )
      (updatedCueModuleMessage, updatedVersionMessage) <- FDBRecordDatabase.transact(
        FDBOperations[(CueModuleModelStoreOperations, CueModuleVersionStoreOperations)].getProviderCached
      ) { case modelOps -> versionOps =>
        for {
          updatedVersion <- versionOps.update(versionId, actor)(_.copy(name = newName, description = newDescription))
          updatedModel <- modelOps.update(cueModuleId, actor)(identity)
        } yield updatedModel -> updatedVersion
      }
    } yield EditVersionResponse(
      cueModule = CueModuleModelConverter.cueModuleProtoToModel(updatedCueModuleMessage),
      version = CueModuleModelConverter.cueModuleVersionProtoToModel(updatedVersionMessage)
    )
  }

  private def getCheckUpToDateData(
    cueModuleId: CueModuleId
  )(
    using keySpace: CueModuleServiceKeySpace
  ) = {
    given FDBKeySpaceEnum = keySpace.fdbKeySpace
    FDBRecordDatabase.read(
      FDBOperations[(CueModuleModelStoreOperations, CueModuleVersionStoreOperations)].getProviderCached
    ) { case modelOps -> versionOps =>
      for {
        cueModule <- modelOps.get(cueModuleId)
        draft <- versionOps.get(cueModule.draftVersionId)
      } yield CueModuleCheckUpToDateData(latestDraftUpdatedOpt = draft.lastUpdatedAt)
    }
  }

  private def getLock(
    cueModuleId: CueModuleId,
    checkData: CueModuleCheckUpToDateData,
    actor: UserId
  )(
    using CueModuleServiceKeySpace
  ) = {
    for {
      latestCheckData <- getCheckUpToDateData(cueModuleId)
      lockResult <-
        if (checkData == latestCheckData) {
          assetLockService.getLock(cueModuleId, actor)
        } else {
          ZIO.succeed(GetLockResult.AssetOutdated)
        }
    } yield lockResult
  }

  private def validateLock(
    cueModuleId: CueModuleId,
    checkData: CueModuleCheckUpToDateData,
    actor: UserId
  )(
    using CueModuleServiceKeySpace
  ) = {
    for {
      lockResult <- getLock(cueModuleId, checkData, actor)
      _ <- lockResult match {
        case _: GetLockResult.Granted => ZIO.unit
        case locked: GetLockResult.AssetLocked =>
          ZIO.fail(GeneralServiceException(s"Cue module is locked by user ${locked.lockedUserInfo.getDisplayName}"))
        case GetLockResult.AssetOutdated => ZIO.fail(GeneralServiceException(s"Cue module is outdated"))
      }
    } yield ()
  }

  private def execCueCommandInternal(
    cueModuleId: CueModuleId,
    zotVersionTag: String,
    command: CueCommand,
    inputJsonStringOpt: Option[String]
  )(
    using keySpace: CueModuleServiceKeySpace
  ) = {
    given CueModuleZotKeySpace = keySpace.cueModuleZotKeySpace

    val module = CueModuleZotUtils.toZotModule(cueModuleId, Some(zotVersionTag))
    for {
      result <- formModuleServerless
        .execCueCommand(
          module = module,
          command = command,
          inputJsonStringOpt = inputJsonStringOpt
        )
        .mapError { error => GeneralServiceException(error.getMessage) }
    } yield result
  }

  def execCueCommandUnsafe(
    cueModuleVersionId: CueModuleVersionId,
    command: CueCommand,
    inputJsonStringOpt: Option[String] = None
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[Either[String, String]] = {
    given FDBKeySpaceEnum = keySpace.fdbKeySpace
    for {
      versionMessage <- FDBRecordDatabase.read(CueModuleVersionStoreOperations.getProviderCached) { versionOps =>
        versionOps.get(cueModuleVersionId)
      }
      result <- execCueCommandInternal(
        cueModuleId = cueModuleVersionId.parent,
        zotVersionTag = versionMessage.zotVersionTag,
        command = command,
        inputJsonStringOpt = inputJsonStringOpt
      )
    } yield result
  }

  def execCueCommand(
    input: Either[(CueModuleId, String), CueModuleVersionId],
    command: CueCommand,
    inputJsonStringOpt: Option[String],
    actor: UserId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[ExecCueCommandResponse] = {
    portalUserService.validateReadPermission(userId = actor, sectionId = PortalSectionId.Form) *> {
      (input match {
        case Left((cueModuleId, zotVersionTag)) =>
          ZIO.logInfo(s"User ${actor.idString} is executing cue command on module ${cueModuleId.idString}") *>
            execCueCommandInternal(
              cueModuleId = cueModuleId,
              zotVersionTag = zotVersionTag,
              command = command,
              inputJsonStringOpt = inputJsonStringOpt
            )
        case Right(cueModuleVersionId) =>
          ZIO.logInfo(
            s"User ${actor.idString} is executing cue command on module version ${cueModuleVersionId.idString}"
          ) *>
            execCueCommandUnsafe(
              cueModuleVersionId = cueModuleVersionId,
              command = command,
              inputJsonStringOpt = inputJsonStringOpt
            )
      }).map(result => ExecCueCommandResponse(result))
    }
  }

  def generateCueModule(
    cueModuleId: CueModuleId,
    cueGeneratorInput: CueGeneratorInput,
    actor: UserId
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[Unit] = {
    given FDBKeySpaceEnum = keySpace.fdbKeySpace
    given CueModuleZotKeySpace = keySpace.cueModuleZotKeySpace
    for {
      _ <- ZIO.logInfo(s"User ${actor.idString} is generating into the draft of cue module ${cueModuleId.idString}")
      _ <- portalUserService.validateWritePermission(userId = actor, sectionId = PortalSectionId.Form)
      checkData <- getCheckUpToDateData(cueModuleId)
      _ <- ZIO.acquireReleaseWith(validateLock(cueModuleId, checkData, actor)) { _ =>
        assetLockService
          .releaseLock(cueModuleId, actor)
          .catchAllCause(
            ZIO.logErrorCause(s"Failed to release lock for module ${cueModuleId.idString} by user ${actor.idString}", _)
          )
      } { _ =>
        for {
          _ <- formModuleServerless
            .generateCueModule(
              module = CueModuleZotUtils.toZotModule(cueModuleId = cueModuleId, versionTagOpt = None),
              cueGeneratorInput = cueGeneratorInput
            )
            .mapError { error => GeneralServiceException(error.getMessage) }
          _ <- FDBRecordDatabase.transact(
            FDBOperations[(CueModuleModelStoreOperations, CueModuleVersionStoreOperations)].getProviderCached
          ) { case modelOps -> versionOps =>
            for {
              model <- modelOps.update(cueModuleId, actor)(identity)
              _ <- versionOps.update(model.draftVersionId, actor)(identity)
            } yield ()
          }
        } yield ()
      }
    } yield ()
  }

  def getModuleCommandsUnsafe[T <: CommandsTrait: Decoder](
    cueModuleVersionId: CueModuleVersionId,
    commandPackages: Seq[String] = CueCommands.defaultCommandPackages
  )(
    using CueModuleServiceKeySpace
  ): Task[T] = {
    for {
      execResults <- execCueCommandUnsafe(
        cueModuleVersionId = cueModuleVersionId,
        command = CueCommand(command = Command.Export, packages = commandPackages)
      )
      results <- ZIO.fromEither(execResults).mapError { error =>
        GeneralServiceException(s"Failed to export commands. Error: $error")
      }
      commands <- ZIO.fromEither(parser.decode[T](results)).mapError { error =>
        GeneralServiceException(s"Failed to decode commands. Error: ${error.getMessage}")
      }
    } yield commands
  }

}

object CueModuleService {

  enum CueModuleServiceKeySpace(val fdbKeySpace: FDBKeySpaceEnum, val cueModuleZotKeySpace: CueModuleZotKeySpace) {
    case Production extends CueModuleServiceKeySpace(FDBKeySpaceEnum.Production, CueModuleZotKeySpace.Production)
    case Test extends CueModuleServiceKeySpace(FDBKeySpaceEnum.Test, CueModuleZotKeySpace.Test)
  }

}
