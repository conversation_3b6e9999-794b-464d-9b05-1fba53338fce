// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.cue.service

import java.time.Duration

import io.circe.{Decoder, parser}
import zio.cache.{Cache, Lookup}
import zio.{Task, ZIO}

import anduin.cue.model.commands.CueCommands
import anduin.cue.model.commands.CueCommands.TableMappingCommands
import anduin.cue.model.table.TableCommonSchema.SoftValidationValueSchema
import anduin.cue.service.CueModuleService.CueModuleServiceKeySpace
import anduin.id.cue.CueModuleVersionId
import anduin.service.GeneralServiceException
import com.anduin.stargazer.service.utils.ZIOUtils

final case class CueTableMappingService(
  cueModuleService: CueModuleService
) {

  private val commandsCache = ZIOUtils.unsafeRun(
    Cache.make(
      capacity = 128,
      timeToLive = Duration.ofMinutes(5),
      lookup = Lookup { (key: (CueModuleVersionId, Seq[String], CueModuleServiceKeySpace)) =>
        given CueModuleServiceKeySpace = key._3
        getTableMappingCommandsInternal(key._1, key._2)
      }
    )
  )

  def getTableMappingCommandsUnsafe(
    cueModuleVersionId: CueModuleVersionId,
    commandPackages: Seq[String] = CueCommands.defaultCommandPackages
  )(
    using keySpace: CueModuleServiceKeySpace
  ): Task[TableMappingCommands] = {
    for {
      cueModuleVersion <- cueModuleService.getCueModuleVersionUnsafe(cueModuleVersionId)
      commands <-
        if (cueModuleVersion.isDraft) {
          getTableMappingCommandsInternal(cueModuleVersionId, commandPackages)
        } else {
          commandsCache.get((cueModuleVersionId, commandPackages, keySpace))
        }
    } yield commands
  }

  def runTableMappingUnsafe(
    sourceTableModuleVersionId: CueModuleVersionId,
    targetMappingId: String,
    sourceTableData: SoftValidationValueSchema
  )(
    using CueModuleServiceKeySpace
  ): Task[SoftValidationValueSchema] = {
    for {
      commands <- getTableMappingCommandsUnsafe(sourceTableModuleVersionId)
      mappingCommand <- ZIO.fromEither(
        commands.mappings
          .get(targetMappingId)
          .toRight(
            GeneralServiceException(
              s"Mapping $targetMappingId not found in module version ${sourceTableModuleVersionId.idString}."
            )
          )
      )
      resultsEither <- cueModuleService.execCueCommandUnsafe(
        cueModuleVersionId = sourceTableModuleVersionId,
        command = mappingCommand.transformTableData,
        inputJsonStringOpt = Some(sourceTableData.json.noSpaces)
      )
      results <- ZIO.fromEither(resultsEither).mapError { error =>
        GeneralServiceException(s"Failed to run table mapping. Error: $error")
      }
      targetTableData <- ZIO.fromEither(parser.decode[SoftValidationValueSchema](results)).mapError { error =>
        GeneralServiceException(
          s"Failed to parse SoftValidationValueSchema. Error: ${error.getMessage}"
        )
      }
    } yield targetTableData
  }

  def runCustomMappingUnsafe[T: Decoder](
    sourceTableModuleVersionId: CueModuleVersionId,
    targetMappingId: String,
    sourceTableData: SoftValidationValueSchema
  )(
    using CueModuleServiceKeySpace
  ): Task[T] = {
    for {
      commands <- getTableMappingCommandsUnsafe(sourceTableModuleVersionId)
      mappingCommand <- ZIO.fromOption(commands.mappings.get(targetMappingId)).mapError { _ =>
        GeneralServiceException(
          s"Mapping $targetMappingId not found in module version ${sourceTableModuleVersionId.idString}."
        )
      }
      customMappingCommand <- ZIO.fromOption(mappingCommand.transformCustomData).mapError { _ =>
        GeneralServiceException(
          s"Custom mapping command not defined for mapping $targetMappingId in module version ${sourceTableModuleVersionId.idString}."
        )
      }
      resultsEither <- cueModuleService.execCueCommandUnsafe(
        cueModuleVersionId = sourceTableModuleVersionId,
        command = customMappingCommand,
        inputJsonStringOpt = Some(sourceTableData.json.noSpaces)
      )
      results <- ZIO.fromEither(resultsEither).mapError { error =>
        GeneralServiceException(s"Failed to run custom mapping. Error: $error")
      }
      targetData <- ZIO.fromEither(parser.decode[T](results)).mapError { error =>
        GeneralServiceException(s"Failed to parse output. Error: ${error.getMessage}")
      }
    } yield targetData
  }

  private def getTableMappingCommandsInternal(
    cueModuleVersionId: CueModuleVersionId,
    commandPackages: Seq[String] = CueCommands.defaultCommandPackages
  )(
    using keySpace: CueModuleServiceKeySpace
  ) = {
    cueModuleService.getModuleCommandsUnsafe[TableMappingCommands](cueModuleVersionId, commandPackages)
  }

}
