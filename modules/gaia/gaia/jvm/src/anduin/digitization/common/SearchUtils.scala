// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.digitization.common

import anduin.digitization.endpoint.DigitizationFilterFileType
import anduin.digitization.model.digitizationfile.DigitizationFileModel
import anduin.digitization.model.digitizationfile.DigitizationFileModel.DigitizationResource
import anduin.digitization.model.digitizationfolder.DigitizationFolderModel
import anduin.digitization.model.search.DigitizationSearchEntry
import anduin.digitization.storage.DigitizationSearchStoreOperations
import anduin.digitization.storage.DigitizationSearchStoreProvider.fileSearchMapping
import anduin.fdb.record.model.{RecordIO, RecordTask}

object SearchUtils {

  def updateFolderSearchEntry(
    model: DigitizationFolderModel,
    searchOps: DigitizationSearchStoreOperations,
    currentEntry: Option[DigitizationSearchEntry] = None
  ): RecordTask[Unit] = {
    val newName = model.name.toLowerCase
    val newTags = model.tags.map(_.toLowerCase)
    val newOwners = model.owners.map(_.idString).toSet

    lazy val upsertFunc = searchOps.upsert(
      DigitizationSearchEntry(
        id = model.folderId.idString,
        name = newName,
        tags = newTags,
        owners = newOwners,
        fileType = DigitizationFilterFileType.DigitizationFilterFolderType.value,
        lastEditedAt = model.lastEditedAt,
        createdAt = model.createdAt
      )
    )

    currentEntry.fold[RecordTask[Unit]](upsertFunc) { entry =>
      if (newName != entry.name || newTags != entry.tags || newOwners != entry.owners) {
        upsertFunc
      } else {
        RecordIO.unit
      }
    }
  }

  def updateFileSearchEntry(
    model: DigitizationFileModel,
    searchOps: DigitizationSearchStoreOperations,
    currentEntry: Option[DigitizationSearchEntry] = None
  ): RecordTask[Unit] = {
    val newName = model.name.toLowerCase
    val newTags = model.tags.map(_.toLowerCase)
    val newOwners = model.owners.map(_.idString).toSet
    val newFileType = model.digitizationResource match {
      case DigitizationResource.Empty => ""
      case DigitizationResource.FormResource(_) =>
        DigitizationFilterFileType.DigitizationFilterFormType.value
      case DigitizationResource.AnnotationDocumentResource(_) =>
        DigitizationFilterFileType.DigitizationFilterAnnotationDocumentType.value
      case DigitizationResource.DataTemplateResource(_) =>
        DigitizationFilterFileType.DigitizationFilterTemplateType.value
      case DigitizationResource.BlueprintResource(_) =>
        DigitizationFilterFileType.DigitizationFilterBlueprintType.value
      case DigitizationResource.CueModuleResource(_) => DigitizationFilterFileType.DigitizationFilterCueModuleType.value
    }

    lazy val upsertFunc = searchOps.upsert(
      DigitizationSearchEntry(
        id = model.fileId.idString,
        name = newName,
        tags = newTags,
        owners = newOwners,
        fileType = newFileType,
        lastEditedAt = model.lastEditedAt,
        createdAt = model.createdAt
      )
    )

    currentEntry.fold[RecordTask[Unit]](upsertFunc) { entry =>
      if (newName != entry.name || newTags != entry.tags || newOwners != entry.owners) {
        upsertFunc
      } else {
        RecordIO.unit
      }
    }
  }

}
